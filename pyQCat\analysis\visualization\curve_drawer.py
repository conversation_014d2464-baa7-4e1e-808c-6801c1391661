# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/05/24
# __author:       <PERSON><PERSON><PERSON> <PERSON>

from typing import Optional, Sequence, Union

import matplotlib.pyplot as plt
import numpy as np
from matplotlib.axes import Axes

from ...errors import AnalysisDrawerError
from .base_drawer import BaseCurveDrawer, get_ax


class CurveDrawer(BaseCurveDrawer):
    """Curve drawer for MatplotLib backend."""

    def initialize_canvas(self):
        """Initialize the drawing canvas."""
        # Create axis if empty
        if not self.options.axis:
            figure, axis = get_ax(self.options.subplots)
            self._figure = figure
            self._figure.set_size_inches(*self.options.figsize)
        else:
            axis = self.options.axis

        p_row, p_column = self.options.subplots
        p_subplots = p_row * p_column
        if p_subplots > 1:
            # Add inset axis. User may provide a single axis object via the analysis option,
            # while this analysis tries to draw its result in multiple canvases,
            # especially when the analysis consists of multiple curves.
            # Inset axis is experimental implementation of matplotlib 3.0 so maybe unstable API.
            # This draws inset axes with shared x and y axis.
            # inset_ax_h = 1 / p_row  # 1/2
            # inset_ax_w = 1 / p_column  # 1
            axis = axis.flatten()
            for i in range(p_row):
                for j in range(p_column):
                    # this axis locates at left, write y-label
                    label_index = i * p_column + j
                    sub_ax = axis[label_index]
                    sub_ax.ticklabel_format(useOffset=False, style="plain")
                    if self.options.ylabel:
                        label = self.options.ylabel
                        if isinstance(label, list):
                            # Y label can be given as a list for each sub axis
                            label = (
                                label[label_index] if label_index < len(label) else ""
                            )
                            if label == "" or label is None:
                                sub_ax.remove()
                                continue
                        sub_ax.set_ylabel(label, **self.options.axis_label)

                    # this axis locates at bottom, write x-label
                    if self.options.xlabel:
                        label = self.options.xlabel
                        if isinstance(label, list):
                            # X label can be given as a list for each sub axis
                            label = label[label_index]
                        sub_ax.set_xlabel(label, **self.options.axis_label)
                    if self.options.sub_title:
                        title = self.options.sub_title
                        if isinstance(title, list):
                            title = title[label_index]
                        sub_ax.set_title(title, **self.options.axis_title)
                    # Set label size for outer axes where labels are drawn
                    sub_ax.tick_params(
                        axis="both",
                        which="major",
                        labelsize=self.options.tick_label_size,
                    )
                    sub_ax.grid()

            # Remove original axis frames
        else:
            axis.set_xlabel(self.options.xlabel, **self.options.axis_label)

            axis.set_ylabel(self.options.ylabel[0], **self.options.axis_label)

            axis.set_title(self.options.title, **self.options.axis_title)

            axis.tick_params(
                axis="both", which="major", labelsize=self.options.tick_label_size
            )
            axis.grid()
            axis = np.array([axis])
        self._axis = axis

    def format_canvas(self):
        """Final cleanup for the canvas appearance."""

        all_axes = self._axis

        # Add data labels if there are multiple labels registered per sub_ax.
        # Add title
        for i, sub_ax in enumerate(all_axes):
            _, labels = sub_ax.get_legend_handles_labels()
            # title = sub_ax.get_title()
            if labels:
                sub_ax.legend(
                    loc=self.options.legend_loc, prop=self.options.axis_legend
                )
            # if not title and self.options.title:
            #     title = self.options.title
            #     if isinstance(title, list):
            #         title = title[i]
            #         sub_ax.set_title(title, **self.options.axis_title)
            #     else:
            #         if i == 0:
            #             sub_ax.set_title(title, **self.options.axis_title)

        self._figure.suptitle(self.options.title, **self.options.sup_title)

        # Format x and y axis
        for ax_type in ("x", "y"):
            # Get axis formatter from drawing options
            if ax_type == "x":
                lim = self.options.xlim
            else:
                lim = self.options.ylim
            # Compute data range from auto scale
            if lim:
                # Format axis number notation
                # Auto-scale all axes to the first sub axis
                if ax_type == "x":
                    all_axes[0].get_shared_x_axes().join(*all_axes)
                    all_axes[0].set_xlim(lim)
                else:
                    all_axes[0].get_shared_y_axes().join(*all_axes)
                    all_axes[0].set_ylim(lim)
        # plt.tight_layout()
        # t = Thread(target=self.figure.tight_layout)
        # t.start()
        # self.figure.tight_layout()

    def _get_axis(self, index: Optional[int] = None) -> Axes:
        """A helper method to get inset axis.
        Args:
            index: Index of inset axis. If nothing is provided, it returns the entire axis.
        Returns:
            Corresponding axis object.
        Raises:
            CurveDrawerError: When axis index is specified but no inset axis is found.
        """
        if index is not None:
            try:
                return self._axis[index]
            except IndexError as ex:
                raise AnalysisDrawerError(
                    f"Canvas index {index} is out of range. "
                    f"Only {len(self._axis)} subplots are initialized."
                ) from ex
        else:
            return self._axis[0]

    def draw_raw_data(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        ax_index: Optional[int] = None,
        **options,
    ):
        """Draw raw data.

        Args:
            x_data: X values.
            y_data: Y values.
            ax_index: Index of canvas if multiple inset axis exist.
            options: Valid options for the drawer backend API.
        """
        if self.options.logarithm:
            self._get_axis(ax_index).set_yscale("log")
            y_data = abs(y_data)

        if self.options["raw_data_format"] == "scatter":
            draw_ops = {"s": 60, "marker": "o", "alpha": 0.8, "label": "raw_data"}
            if not ax_index % 2:
                draw_ops["color"] = self.options.default_colors[0]
            else:
                draw_ops["color"] = self.options.default_colors[1]
            draw_ops.update(**options)
            check_draw_ops(draw_ops)
            self._get_axis(ax_index).ticklabel_format(
                style="sci", axis="y", scilimits=(-3, 3)
            )
            self._get_axis(ax_index).scatter(x_data, y_data, **draw_ops)

        if self.options["raw_data_format"] == "plot":
            draw_ops = {
                "markersize": 6,
                "marker": self.options.marker,
                "alpha": 0.8,
                "label": "raw_data",
                "linewidth": 0.8,
            }
            if not ax_index % 2:
                draw_ops["color"] = self.options.default_colors[0]
            else:
                draw_ops["color"] = self.options.default_colors[1]
            draw_ops.update(**options)
            check_draw_ops(draw_ops)
            self._get_axis(ax_index).ticklabel_format(
                style="sci", axis="y", scilimits=(-3, 3)
            )
            self._get_axis(ax_index).plot(x_data, y_data, **draw_ops)

    def draw_dynamic_data(
        self,
        x_data: np.ndarray,
        y_data: np.ndarray,
        z_data: np.ndarray,
        ax_index: Optional[int] = None,
        **options,
    ):
        """Draw raw data.

        Args:
            x_data: X values.
            y_data: Y values.
            z_data: Z values.
            ax_index: Index of canvas if multiple inset axis exist.
            options: Valid options for the drawer backend API.
        """
        draw_ops = {
            "s": 60,
            "marker": "o",
            "alpha": 0.8,
        }
        cm = plt.cm.get_cmap("RdYlBu")
        draw_ops.update(**options)

        row_length = y_data.shape[0]

        # # calculate min or max maybe error, solve bug 2022-12-08
        # v_min = np.min(z_data)
        # v_max = np.max(z_data)

        sc0 = None
        for i in range(row_length):
            y = y_data[i]
            x = np.ones_like(y) * x_data[i]
            z = z_data[i]
            sc0 = self._get_axis(ax_index).scatter(x, y, c=z, cmap=cm, **draw_ops)
            # sc0 = self._get_axis(ax_index).scatter(x, y, c=z, vmin=v_min, vmax=v_max, cmap=cm, **draw_ops)
        plt.colorbar(sc0, ax=self._get_axis(ax_index))

    def draw_filter_data(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        ax_index: Optional[int] = None,
        **options,
    ):
        """Draw smooth data

        Args:
            x_data: X values.
            y_data: Y values.
            ax_index: Index of canvas if multiple inset axis exist.
            options: Valid options for the drawer backend API.
        """
        draw_ops = {
            "color": "green",
            "alpha": 0.8,
            "linewidth": 1.5,
            "label": "smooth_data",
        }
        draw_ops.update(**options)
        self._get_axis(ax_index).plot(x_data, y_data, **draw_ops)

    def draw_fit_line(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        ax_index: Optional[int] = None,
        label: str = "fit_data",
        **options,
    ):
        """Draw fit line.

        Args:
            x_data: X values.
            y_data: Fit Y values.
            ax_index: Index of canvas if multiple inset axis exist.
            options: Valid options for the drawer backend API.
        """
        draw_ops = {"alpha": 0.8, "linewidth": 2.5}
        if label:
            draw_ops["label"] = label
        if not ax_index % 2:
            draw_ops["color"] = self.options.default_colors[0]
        else:
            draw_ops["color"] = self.options.default_colors[1]
        draw_ops.update(**options)
        self._get_axis(ax_index).plot(x_data, y_data, **draw_ops)

    def draw_text(self, ax_index: Optional[int] = None, **options):
        """
        Add text to the axes.
        Add the text *s* to the axes at location *x*, *y* in data coordinates.

        Args:
            ax_index: Index of canvas if multiple inset axis exist.
        """
        draw_ops = {
            "ha": "center",
            "va": "top",
            "rotation": 1,
            "fontdict": {
                "fontsize": 8,
                "color": "blue",
                "family": "monospace",
                "weight": "normal",
            },
        }
        draw_ops.update(**options)

        if isinstance(self.options.text_pos, list):
            text_pos = self.options.text_pos
            text_rp = self.options.text_rp
        else:
            text_pos = self.options.text_pos.get(ax_index)
            text_rp = self.options.text_rp.get(ax_index)

        for pos, t in zip(text_pos, text_rp):
            self._get_axis(ax_index).text(pos[0], pos[1], t, **draw_ops)

    def draw_color_map(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        z_data: Sequence[float],
        ax_index: Optional[int] = None,
        clear_base: int = 0,
        **options,
    ):
        """
        using norm to map colormaps onto data in non-linear ways
        Args:
            x_data:
            y_data:
            z_data:
            ax_index: Index of canvas if multiple inset axis exist.
            clear_base: Debase by row or column, 0: no action, 1: base row, 2: base col
        """
        if clear_base == 1:
            base = z_data[-1]
            for i in range(len(z_data)):
                z_data[i] -= base
        elif clear_base == 2:
            z_data = z_data.T
            base = z_data[0]
            for i in range(len(z_data)):
                z_data[i] -= base
            z_data = z_data.T

        mesh_x, mesh_y = np.meshgrid(x_data, y_data)
        map_z = np.reshape(z_data, (len(y_data), len(x_data)), order="F")
        if mesh_x.size != map_z.size:
            raise AnalysisDrawerError(
                f"Matrix dimension mismatch, {(len(x_data), len(y_data))} != {len(z_data)}"
            )
        draw_ops = {
            "cmap": plt.cm.get_cmap("viridis"),
            "shading": "nearest",
        }
        draw_ops.update(**options)
        axis = self._get_axis(ax_index)
        mesh = axis.pcolormesh(mesh_x, mesh_y, map_z, **draw_ops)
        self.figure.colorbar(mesh, ax=axis, extend="both")

    def draw_vlines(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        y_min: Union[Sequence[float], float] = 0,
        ax_index: Optional[int] = None,
        **options,
    ):
        """Draw vline.

        Args:
            x_data: X values.
            y_data: Y values.
            y_min: Respective beginning and end of each line. If scalars are
                   provided, all lines will have same length.
            ax_index: Index of canvas if multiple inset axis exist.
            options: Valid options for the drawer backend API.
        """
        draw_ops = {"alpha": 0.8, "linewidth": 2.5, "label": "vline_data"}
        draw_ops.update(**options)
        self._get_axis(ax_index).vlines(x_data, y_min, y_data, **draw_ops)

    def draw_bar_data(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        ax_index: Optional[int] = None,
        **options,
    ):
        """Draw smooth data

        Args:
            x_data: X values.
            y_data: Y values.
            ax_index: Index of canvas if multiple inset axis exist.
            options: Valid options for the drawer backend API.

        """
        draw_ops = {"width": 0.25, "color": "#009527"}
        draw_ops.update(**options)
        self._get_axis(ax_index).bar(x_data, y_data, **draw_ops)

    def draw_xy_point(self, x_data, y_data, ax_index, **options):
        draw_ops = {"s": 60, "marker": "o", "alpha": 0.8, "label": "raw_data"}
        draw_ops.update(options)
        sc = self._get_axis(ax_index).scatter(x_data, y_data, **draw_ops)
        self.figure.colorbar(sc, ax=self._get_axis(ax_index))

    def draw_hist_raw_data(
        self, y_data: Sequence[float], ax_index: Optional[int] = None, **options
    ):
        """Draw hist raw data.

        Args:
            y_data: Y values.
            ax_index: Index of canvas if multiple inset axis exist.
            options: Valid options for the drawer backend API.
        """
        draw_ops = {"bins": 50, "density": True}

        draw_ops.update(**options)

        self._get_axis(ax_index).hist(y_data, **draw_ops)

    def draw_scatter_point(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        ax_index: Optional[int] = None,
        **options,
    ):
        self._get_axis(ax_index).scatter(x_data, y_data, **options)

    def draw_plot_point(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        ax_index: Optional[int] = None,
        **options,
    ):
        self._get_axis(ax_index).plot(x_data, y_data, **options)

    def draw_semilogx_point(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        ax_index: Optional[int] = None,
        **options,
    ):
        self._get_axis(ax_index).semilogx(x_data, y_data, **options)

    def draw_axv_line(self, x: float, ax_index: Optional[int] = None, **options):
        self._get_axis(ax_index).axvline(x=x, **options)

    def draw_axh_line(self, y: float, ax_index: Optional[int] = None, **options):
        self._get_axis(ax_index).axhline(y=y, **options)

    def draw_fill_between(
        self,
        x_data: Sequence[float],
        down_data: Sequence[float],
        up_data: Sequence[float],
        ax_index: Optional[int] = None,
        **options,
    ):
        self._get_axis(ax_index).fill_between(x_data, down_data, up_data, **options)


def check_draw_ops(draw_ops: dict):
    c = draw_ops.pop("c", None)
    if c:
        draw_ops["color"] = c
