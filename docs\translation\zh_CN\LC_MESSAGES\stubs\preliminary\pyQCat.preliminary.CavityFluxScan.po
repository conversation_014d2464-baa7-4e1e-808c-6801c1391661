# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:2
msgid "pyQCat.preliminary.CavityFluxScan"
msgstr ""

#: of pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan:1
msgid ""
"Bases: "
":py:class:`~pyQCat.preliminary.preliminary_models.PreliminaryExperiment`"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
#: of pyQCat.preliminary.preliminary_models.PreliminaryExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.preliminary.CavityFluxScan.__init__>`\\ "
"\\(\\[qubit\\, coupler\\]\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.preliminary.CavityFluxScan.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.preliminary.CavityFluxScan.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.preliminary.CavityFluxScan.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ":py:obj:`run <pyQCat.preliminary.CavityFluxScan.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ""
":py:obj:`select_dc_source "
"<pyQCat.preliminary.CavityFluxScan.select_dc_source>`\\ "
"\\(\\[dc\\_source\\, qaio\\_ip\\, qaio\\_type\\]\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ""
":py:obj:`select_mic_source "
"<pyQCat.preliminary.CavityFluxScan.select_mic_source>`\\ "
"\\(mic\\_source\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ""
":py:obj:`select_net_analyzer "
"<pyQCat.preliminary.CavityFluxScan.select_net_analyzer>`\\ "
"\\(net\\_analyzer\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.preliminary.CavityFluxScan.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.preliminary.CavityFluxScan.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.preliminary.CavityFluxScan.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.preliminary.CavityFluxScan.set_run_options>`\\ \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid ""
":py:obj:`set_sub_analysis_options "
"<pyQCat.preliminary.CavityFluxScan.set_sub_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:33:<autosummary>:1
msgid "Set subclass analysis options."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.CavityFluxScan.rst:35
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.preliminary.CavityFluxScan.analysis>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.preliminary.CavityFluxScan.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`coupler <pyQCat.preliminary.CavityFluxScan.coupler>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.preliminary.CavityFluxScan.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`qaio <pyQCat.preliminary.CavityFluxScan.qaio>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`qubit <pyQCat.preliminary.CavityFluxScan.qubit>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`run_options <pyQCat.preliminary.CavityFluxScan.run_options>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`sub_analysis_options "
"<pyQCat.preliminary.CavityFluxScan.sub_analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:1
msgid "Default Experiment Options."
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:13
msgid "Default experiment options:"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:4
msgid ""
"dc_source (str): dc source type, default is `qaio` base_qubit "
"(BaseQubit): Experiment to adjust the DC of the target base qubit. "
"dc_channel (int): Scan dc will set dc channel. other_dc_dict (Dict): May "
"be set constant dc to other dc channels. dc_list (List, np.ndarray): Scan"
" dc range. freq_list (List, np.ndarray): Set scan net analyzer scan "
"frequency range. dynamic_plot (bool): If True stands for animated drawing"
" name (str): experiment name, default is class name. fine_scan (bool): "
"Determine whether the experiment sweeps multiple valleys or sweeps one "
"valley."
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_analysis_options
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_sub_analysis_options
msgid "Return type"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_analysis_options:15
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_experiment_options:15
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_sub_analysis_options:11
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_analysis_options:1
msgid "Default Analysis Options"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_analysis_options:13
msgid "Default analysis options:"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_analysis_options:4
msgid ""
"quality_bounds (list): Fit quality assessment threshold. data_key (str): "
"The key name of the analysis data. x_label (str): Label for scanned data."
" diff_threshold (float): The cavity frequency adjustment range is less"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_analysis_options:8
msgid "than 0.1 MHz, and it is considered to be non-modulated."
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_analysis_options:9
msgid ""
"fit_q (bool): Whether to perform q-value fitting. ATT (float): "
"Attenuation value. tackle_type (str): experiment tackle type. sample "
"(str): chip sample name."
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_sub_analysis_options:1
msgid "Default Sub analysis Options"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_sub_analysis_options:9
msgid "Default sub analysis options:"
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._default_sub_analysis_options:4
msgid ""
"quality_bounds (list): Fit quality assessment threshold. data_key (str): "
"The key name of the analysis data. x_label (str): Label for scanned data."
" p0: fit initial value. cavity_count (int): Number of cavities."
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._check_options:1
msgid "Check Options."
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._save_analysis_plot:1
msgid "Save CurveAnalysis plot figure."
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._result_reduction:1
msgid "Process Net Analyzer Scan Once result data."
msgstr ""

#: of
#: pyQCat.preliminary.library.cavity_flux_scan.CavityFluxScan._run_analysis:1
msgid "Create and Run TunableAnalysis."
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.preliminary.preliminary_models.QubitPreliminary`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.preliminary.CavityFluxScan.__init__>`\\ \\(\\[config\\,"
#~ " network\\_analyzer\\, dc\\_source\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`run <pyQCat.preliminary.CavityFluxScan.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`select_dc_source "
#~ "<pyQCat.preliminary.CavityFluxScan.select_dc_source>`\\ "
#~ "\\(\\[dc\\_source\\, qaio\\_ip\\, qaio\\_type\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`select_mic_source "
#~ "<pyQCat.preliminary.CavityFluxScan.select_mic_source>`\\ "
#~ "\\(mic\\_source\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`select_net_analyzer "
#~ "<pyQCat.preliminary.CavityFluxScan.select_net_analyzer>`\\ "
#~ "\\(net\\_analyzer\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.preliminary.CavityFluxScan.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_dc <pyQCat.preliminary.CavityFluxScan.set_dc>`\\"
#~ " \\(dc\\_dict\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.preliminary.CavityFluxScan.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_sub_analysis_options "
#~ "<pyQCat.preliminary.CavityFluxScan.set_sub_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`store_set "
#~ "<pyQCat.preliminary.CavityFluxScan.store_set>`\\ \\(root\\[\\,"
#~ " name\\, describe\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`analysis <pyQCat.preliminary.CavityFluxScan.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.preliminary.CavityFluxScan.analysis_options>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`config <pyQCat.preliminary.CavityFluxScan.config>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`config_path <pyQCat.preliminary.CavityFluxScan.config_path>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`dc_source <pyQCat.preliminary.CavityFluxScan.dc_source>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.preliminary.CavityFluxScan.experiment_options>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`file <pyQCat.preliminary.CavityFluxScan.file>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`mic_source <pyQCat.preliminary.CavityFluxScan.mic_source>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`net_analyzer "
#~ "<pyQCat.preliminary.CavityFluxScan.net_analyzer>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`qaio <pyQCat.preliminary.CavityFluxScan.qaio>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`sub_analysis_options "
#~ "<pyQCat.preliminary.CavityFluxScan.sub_analysis_options>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.preliminary.preliminary_models.QubitPreliminary`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.preliminary.CavityFluxScan.__init__>`\\"
#~ " \\(\\[config\\, network\\_analyzer\\, "
#~ "dc\\_source\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`run <pyQCat.preliminary.CavityFluxScan.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid "Start cavity flux scan qubit_test."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`select_dc_source "
#~ "<pyQCat.preliminary.CavityFluxScan.select_dc_source>`\\ "
#~ "\\(\\[dc\\_source\\, qaio\\_ip\\, qaio\\_type\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`select_mic_source "
#~ "<pyQCat.preliminary.CavityFluxScan.select_mic_source>`\\ "
#~ "\\(mic\\_source\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`select_net_analyzer "
#~ "<pyQCat.preliminary.CavityFluxScan.select_net_analyzer>`\\ "
#~ "\\(net\\_analyzer\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.preliminary.CavityFluxScan.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid "Set analysis options."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_dc <pyQCat.preliminary.CavityFluxScan.set_dc>`\\ "
#~ "\\(dc\\_dict\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.preliminary.CavityFluxScan.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid "Set experiment options."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_sub_analysis_options "
#~ "<pyQCat.preliminary.CavityFluxScan.set_sub_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`store_set <pyQCat.preliminary.CavityFluxScan.store_set>`\\"
#~ " \\(root\\[\\, name\\, describe\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.preliminary.CavityFluxScan.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.preliminary.CavityFluxScan.analysis_options>`\\"
#~ msgstr ""

#~ msgid ":obj:`config <pyQCat.preliminary.CavityFluxScan.config>`\\"
#~ msgstr ""

#~ msgid ":obj:`config_path <pyQCat.preliminary.CavityFluxScan.config_path>`\\"
#~ msgstr ""

#~ msgid ":obj:`dc_source <pyQCat.preliminary.CavityFluxScan.dc_source>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.preliminary.CavityFluxScan.experiment_options>`\\"
#~ msgstr ""

#~ msgid ":obj:`file <pyQCat.preliminary.CavityFluxScan.file>`\\"
#~ msgstr ""

#~ msgid ":obj:`mic_source <pyQCat.preliminary.CavityFluxScan.mic_source>`\\"
#~ msgstr ""

#~ msgid ":obj:`net_analyzer <pyQCat.preliminary.CavityFluxScan.net_analyzer>`\\"
#~ msgstr ""

#~ msgid ":obj:`qaio <pyQCat.preliminary.CavityFluxScan.qaio>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`sub_analysis_options "
#~ "<pyQCat.preliminary.CavityFluxScan.sub_analysis_options>`\\"
#~ msgstr ""

#~ msgid "Experiment Options:"
#~ msgstr ""

#~ msgid ""
#~ "q_bits (List[int]): Test qubit number, "
#~ "normal `[ 0 ]`. c_bits (List[int]): "
#~ "Test coupler number, normal `[ 0 "
#~ "]`. dc_channel (int): Scan dc will "
#~ "set dc channel. add_dc_channels (List[int]):"
#~ " May be set constant dc to "
#~ "other dc channels. dc_list (List, "
#~ "np.ndarray): Scan dc range. freq_list "
#~ "(List, np.ndarray): Set scan net "
#~ "analyzer scan frequency range. net_IFBW "
#~ ": The IF bandwidth of network "
#~ "analyzer. net_power (float): The power "
#~ "of network analyzer. dynamic_plot (bool): "
#~ "If True stands for animated drawing "
#~ "fit_q (bool): If True, stands Q "
#~ "fitting. ATT : Plot mark ATT "
#~ "value. sample (str, BaseQubit): str or"
#~ " BaseQubit object. name (str): Optional."
#~ " Experiment name."
#~ msgstr ""

#~ msgid "Default Sub Once Analysis Options"
#~ msgstr ""

#~ msgid "Set metadata."
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.structures.MetaData`"
#~ msgstr ""

#~ msgid "Parameters"
#~ msgstr ""

#~ msgid "Scan dc range."
#~ msgstr ""

