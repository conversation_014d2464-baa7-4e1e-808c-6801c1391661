# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.analysis.visualization.rst:2
msgid "pyQCat.analysis.visualization package"
msgstr ""

#: ../../source/api/pyQCat.analysis.visualization.rst:5
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.analysis.visualization.rst:8
msgid "pyQCat.analysis.visualization.base\\_drawer module"
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.get_ax:1
msgid "Return a matplotlib axes that can be used in a child thread."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.get_ax:3
msgid ""
"Analysis/plotting is done in a separate thread (so it doesn't block the "
"main thread), but matplotlib doesn't support GUI mode in a child thread. "
"This function creates a separate Figure and attaches a non-GUI SVG canvas"
" to it."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.get_ax
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.get_ax:8
msgid "A matplotlib axes that can be used in a child thread."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.figure
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.options
#: pyQCat.analysis.visualization.base_drawer.get_ax
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:1
msgid "Bases: :py:class:`~abc.ABC`"
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:1
msgid "Abstract class for the serializable Qiskit Experiments curve drawer."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:3
msgid ""
"A curve drawer may be implemented by different drawing backends such as "
"matplotlib or plotly. Sub-classes that wrap these backends by subclassing"
" `BaseCurveDrawer` must implement the following abstract methods."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:7
msgid "initialize_canvas"
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:9
msgid ""
"This method should implement a protocol to initialize a drawing canvas "
"with user input ``axis`` object. Note that curve analysis drawer supports"
" visualization of experiment results in multiple canvases tiled into N "
"(row) x M (column) inset grids, which is specified in the option "
"``subplots``. By default, this is N=1, M=1 and thus no inset grid will be"
" initialized. The data points to draw might be provided with a canvas "
"number defined in :attr:`SeriesDef.canvas` which defaults to ``None``, "
"i.e. no-inset grids."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:17
msgid ""
"This method should first check the drawing options for the axis object "
"and initialize the axis only when it is not provided by the options. Once"
" axis is initialized, this is set to the instance member ``self._axis``."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:21
msgid "format_canvas"
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:23
msgid ""
"This method should implement a protocol to format the appearance of "
"canvas. Typically, it updates axis and tick labels. Note that the axis SI"
" unit may be specified in the drawing options. In this case, axis numbers"
" should be auto-scaled with the unit prefix."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:28
msgid "draw_raw_data"
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:30
msgid ""
"This method is called after data processing is completed. This method "
"draws raw experiment data points on the canvas."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:33
msgid "draw_fit_line"
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:35
msgid ""
"This method is called after fitting is completed and when there is valid "
"fit outcome. This method is called with the interpolated x and y values."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:38
msgid "draw_text"
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:40
msgid ""
"This method is called after fitting is completed and when there is valid "
"fit outcome. This method is called with the interpolated x and a pair of "
"y values that represent the upper and lower bound within certain "
"confidence interval. This might be called multiple times with different "
"interval sizes."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:45
msgid "draw_fit_report"
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer:47
msgid ""
"This method is called after fitting is completed and when there is valid "
"fit outcome. This method is called with the list of analysis results and "
"the reduced chi-squared values. The fit report should be generated to "
"show this information on the canvas."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.options:1
msgid "Return the drawing options."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:52
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.options:3
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:11
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.figure:1
msgid "Return figure object handler to be saved in the database."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.figure:3
msgid ""
"In the MatplotLib the ``Figure`` and ``Axes`` are different object. User "
"can pass a part of the figure (i.e. multi-axes) to the drawer option "
"``axis``. For example, a user wants to combine two different experiment "
"results in the same figure, one can call ``pyplot.subplots`` with two "
"rows and pass one of the generated two axes to each experiment drawer. "
"Once all the experiments complete, the user will obtain the single figure"
" collecting all experimental results."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.figure:10
msgid ""
"Note that this method returns the entire figure object, rather than a "
"single axis. Thus, the experiment data saved in the database might have a"
" figure collecting all child axes drawings."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.figure:14
msgid ":py:class:`~matplotlib.figure.Figure`"
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:1
msgid "Return default draw options."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:50
msgid "Draw Options:"
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:4
msgid ""
"axis (Any): Arbitrary object that can be used as a drawing canvas. "
"subplots (Tuple[int, int]): Number of rows and columns when the "
"experimental"
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:6
msgid "result is drawn in the multiple windows."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:7
msgid "xlabel (Union[str, List[str]]): X-axis label string of the output figure."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:8
msgid ""
"If there are multiple columns in the canvas, this could be a list of "
"labels."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:9
msgid "ylabel (Union[str, List[str]]): Y-axis label string of the output figure."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:10
msgid "If there are multiple rows in the canvas, this could be a list of labels."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:11
msgid "xlim (Tuple[float, float]): Min and max value of the horizontal axis."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:12
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:14
msgid ""
"If not provided, it is automatically scaled based on the input data "
"points."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:13
msgid "ylim (Tuple[float, float]): Min and max value of the vertical axis."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:22
msgid "xval_unit (str): SI unit of x values. No prefix is needed here."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:16
msgid ""
"For example, when the x values represent time, this option will be just "
"\"s\" rather than \"ms\". In the output figure, the prefix is "
"automatically selected based on the maximum value in this axis. If your x"
" values are in [1e-3, 1e-4], they are displayed as [1 ms, 10 ms]. This "
"option is likely provided by the analysis class rather than end-users. "
"However, users can still override if they need different unit notation. "
"By default, this option is set to ``None``, and no scaling is applied. If"
" nothing is provided, the axis numbers will be displayed in the "
"scientific notation."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:24
msgid ""
"yval_unit (str): Unit of y values. See ``xval_unit`` for details. figsize"
" (Tuple[int, int]): A tuple of two numbers representing the size of"
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:26
msgid ""
"the output figure (width, height). Note that this is applicable only when"
" ``axis`` object is not provided. If any canvas object is provided, the "
"figure size associated with the axis is preferentially applied."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:31
msgid ""
"legend_loc (str): Vertical and horizontal location of the curve legend "
"window in"
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:30
msgid ""
"a single string separated by a space. This defaults to ``center right``. "
"Vertical position can be ``upper``, ``center``, ``lower``. Horizontal "
"position can be ``right``, ``center``, ``left``."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:33
msgid ""
"tick_label_size (int): Size of text representing the axis tick numbers. "
"axis_label_size (int): Size of text representing the axis label. "
"fit_report_rpos (Tuple[int, int]): A tuple of numbers showing the "
"location of"
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:36
msgid ""
"the fit report window. These numbers are horizontal and vertical position"
" of the top left corner of the window in the relative coordinate on the "
"output figure, i.e. ``[0, 1]``. The fit report window shows the selected "
"fit parameters and the reduced chi-squared value."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:41
msgid ""
"fit_report_text_size (int): Size of text in the fit report window. "
"plot_sigma (List[Tuple[float, float]]): A list of two number tuples"
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:43
msgid ""
"showing the configuration to write confidence intervals for the fit "
"curve. The first argument is the relative sigma (n_sigma), and the second"
" argument is the transparency of the interval plot in ``[0, 1]``. "
"Multiple n_sigma intervals can be drawn for the single curve."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer._default_options:47
msgid ""
"text_pos (List[Tuple]): The position of the annotations to place on the "
"figure. text_rp (List[str]): The contents of the annotations to place on "
"the figure. text_key (List[str]): Indicates the data key to annotate."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.set_options:1
msgid "Set the drawing options. :param fields: The fields to update the options"
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.initialize_canvas:1
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.initialize_canvas:1
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.initialize_canvas:1
msgid "Initialize the drawing canvas."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.format_canvas:1
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.format_canvas:1
msgid "Final cleanup for the canvas appearance."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_raw_data:1
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_raw_data:1
msgid "Draw raw data."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_filter_data
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_fit_line
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_raw_data
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_filter_data
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_fit_line
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_raw_data
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_text
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.draw_matrix
msgid "Parameters"
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_filter_data:4
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_fit_line:4
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_raw_data:4
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_filter_data:4
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_fit_line:4
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_raw_data:4
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines:4
msgid "X values."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_filter_data:6
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_raw_data:6
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_filter_data:6
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_raw_data:6
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines:6
msgid "Y values."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_filter_data:8
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_fit_line:8
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_raw_data:8
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_filter_data:8
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_fit_line:8
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_raw_data:8
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_text:5
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines:11
msgid "Index of canvas if multiple inset axis exist."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_filter_data:9
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_fit_line:9
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_raw_data:9
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_filter_data:9
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_fit_line:9
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_raw_data:9
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines:12
msgid "Valid options for the drawer backend API."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_filter_data:1
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_filter_data:1
msgid "Draw smooth data"
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_fit_line:1
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_fit_line:1
msgid "Draw fit line."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_fit_line:6
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_fit_line:6
msgid "Fit Y values."
msgstr ""

#: of pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_text:1
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_text:1
msgid ""
"Add text to the axes. Add the text *s* to the axes at location *x*, *y* "
"in data coordinates."
msgstr ""

#: of
#: pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer.draw_color_map:1
msgid ""
"using norm to map colormaps onto data in non-linear ways :type x_data: "
":py:class:`~typing.Sequence`\\[:py:class:`float`] :param x_data: :type "
"y_data: :py:class:`~typing.Sequence`\\[:py:class:`float`] :param y_data: "
":type z_data: :py:class:`~typing.Sequence`\\[:py:class:`float`] :param "
"z_data:"
msgstr ""

#: ../../source/api/pyQCat.analysis.visualization.rst:16
msgid "pyQCat.analysis.visualization.curve\\_drawer module"
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer:1
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer:1
msgid ""
"Bases: "
":py:class:`~pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer`"
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer:1
#: pyQCat.analysis.visualization:15:<autosummary>:1
msgid "Curve drawer for MatplotLib backend."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis:1
msgid ""
"A helper method to get inset axis. :type index: "
":py:data:`~typing.Optional`\\[:py:class:`int`] :param index: Index of "
"inset axis. If nothing is provided, it returns the entire axis."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis:5
msgid ":py:class:`~matplotlib.axes._axes.Axes`"
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis:6
msgid "Corresponding axis object."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.draw_matrix
msgid "Raises"
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis:8
msgid "When axis index is specified but no inset axis is found."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_color_map:1
msgid ""
"using norm to map colormaps onto data in non-linear ways :type x_data: "
":py:class:`~typing.Sequence`\\[:py:class:`float`] :param x_data: :type "
"y_data: :py:class:`~typing.Sequence`\\[:py:class:`float`] :param y_data: "
":type z_data: :py:class:`~typing.Sequence`\\[:py:class:`float`] :param "
"z_data: :type ax_index: :py:data:`~typing.Optional`\\[:py:class:`int`] "
":param ax_index: Index of canvas if multiple inset axis exist."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines:1
msgid "Draw vline."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines:8
msgid ""
"Respective beginning and end of each line. If scalars are provided, all "
"lines will have same length."
msgstr ""

#: ../../source/api/pyQCat.analysis.visualization.rst:24
msgid "pyQCat.analysis.visualization.scatter\\_drawer module"
msgstr ""

#: ../../source/api/pyQCat.analysis.visualization.rst:32
msgid "pyQCat.analysis.visualization.style module"
msgstr ""

#: of pyQCat.analysis.visualization.style:1
msgid "Configurable stylesheet."
msgstr ""

#: of pyQCat.analysis.visualization.style.PlotterStyle:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.analysis.visualization.style.PlotterStyle:1
#: pyQCat.analysis.visualization:15:<autosummary>:1
msgid "A stylesheet for curve analysis figure."
msgstr ""

#: ../../source/api/pyQCat.analysis.visualization.rst:40
msgid "pyQCat.analysis.visualization.tomography\\_drawer module"
msgstr ""

#: of pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer:1
#: pyQCat.analysis.visualization:15:<autosummary>:1
msgid "Tomography drawer for MatplotLib backend."
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:1
msgid "Create the Analysis options, and set some fields."
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:3
msgid "**Analysis Options**:"
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:5
msgid "**labels (List)** - Subplot title, default is ``[real, image]``."
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:7
msgid "**figsize (Tuple)** - Canvas size, defaultn is ``(14, 7)``"
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:9
msgid "**sup_title (str)** - Top-level canvas title, ``QPT`` or ``QST``"
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:12
msgid "Tomography drawer options."
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.format_canvas:1
msgid "Add a title to the canvas."
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.draw_matrix:1
msgid "Draw matrix."
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.draw_matrix:4
msgid "goal maxtrix."
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.draw_matrix:7
msgid "wrong dimension"
msgstr ""

#: ../../source/api/pyQCat.analysis.visualization.rst:48
msgid "Module contents"
msgstr ""

#: of pyQCat.analysis.visualization:3
msgid "Analysis Visualization  (:mod:`pyQCat.analysis.visualization`)"
msgstr ""

#: of pyQCat.analysis.visualization:5
msgid "Analysis submodule, visualization modules."
msgstr ""

#: of pyQCat.analysis.visualization:8
msgid "Base Classes"
msgstr ""

#: of pyQCat.analysis.visualization:15:<autosummary>:1
msgid ""
":py:obj:`PlotterStyle <pyQCat.analysis.visualization.PlotterStyle>`\\ "
"\\(\\[figsize\\, legend\\_loc\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.visualization:15:<autosummary>:1
msgid ":py:obj:`CurveDrawer <pyQCat.analysis.visualization.CurveDrawer>`\\ \\(\\)"
msgstr ""

#: of pyQCat.analysis.visualization:15:<autosummary>:1
msgid ""
":py:obj:`TomographyDrawer "
"<pyQCat.analysis.visualization.TomographyDrawer>`\\ \\(\\)"
msgstr ""

#~ msgid "Bases: :py:class:`~abc.ABC`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`PlotterStyle "
#~ "<pyQCat.analysis.visualization.PlotterStyle>`\\ \\(\\[figsize\\,"
#~ " legend\\_loc\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CurveDrawer "
#~ "<pyQCat.analysis.visualization.CurveDrawer>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`TomographyDrawer "
#~ "<pyQCat.analysis.visualization.TomographyDrawer>`\\ \\(\\)"
#~ msgstr ""

#~ msgid "Bases: :class:`abc.ABC`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer`"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`PlotterStyle "
#~ "<pyQCat.analysis.visualization.PlotterStyle>`\\ \\(\\[figsize\\,"
#~ " legend\\_loc\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`CurveDrawer <pyQCat.analysis.visualization.CurveDrawer>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`TomographyDrawer "
#~ "<pyQCat.analysis.visualization.TomographyDrawer>`\\ \\(\\)"
#~ msgstr ""

