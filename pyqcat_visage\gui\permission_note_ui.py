# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'permission_note_ui.ui'
##
## Created by: Qt User Interface Compiler version 6.8.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QAction, QBrush, QColor, QConicalGradient,
    QCursor, QFont, QFontDatabase, QGradient,
    QIcon, QImage, QKeySequence, QLinearGradient,
    QPainter, QPalette, QPixmap, QRadialGradient,
    QTransform)
from PySide6.QtWidgets import (QApplication, QGridLayout, QGroupBox, QHBoxLayout,
    QHeaderView, QLabel, QMainWindow, QPushButton,
    QSizePolicy, QSpacerItem, QSpinBox, QStatusBar,
    QToolBar, QVBoxLayout, QWidget)

from .widgets.combox_custom.combox_search import SearchComboBox
from .widgets.permissions.table_view_perms_note import QTableViewPermsNoteWidget
from .widgets.permissions.tree_view_perms_note import QTreeViewPermsNoteWidget

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1068, 704)
        # self.actionGroupManage = QAction(MainWindow)
        # self.actionGroupManage.setObjectName(u"actionGroupManage")
        # self.actionUserManage = QAction(MainWindow)
        # self.actionUserManage.setObjectName(u"actionUserManage")
        self.actionRefresh = QAction(MainWindow)
        self.actionRefresh.setObjectName(u"actionRefresh")
        self.actionPlatformMannage = QAction(MainWindow)
        self.actionPlatformMannage.setObjectName(u"actionPlatformMannage")

        # self.actionPlatformGroup = QAction(MainWindow)
        # self.actionPlatformGroup.setObjectName(u"actionPlatformGroup")
        # self.actionPlatformUser = QAction(MainWindow)
        # self.actionPlatformUser.setObjectName(u"actionPlatformUser")
        #
        # self.actionNormalGroup = QAction(MainWindow)
        # self.actionNormalGroup.setObjectName(u"actionNormalGroup")
        # self.actionNormalUser = QAction(MainWindow)
        # self.actionNormalUser.setObjectName(u"actionNormalUser")

        self.actionPermission = QAction(MainWindow)
        self.actionPermission.setObjectName(u"actionPermission")

        # self.actionPlatformPermission = QAction(MainWindow)
        # self.actionPlatformPermission.setObjectName(u"actionPlatformPermission")
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.horizontalLayout_6 = QHBoxLayout(self.centralwidget)
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.widget_main = QWidget(self.centralwidget)
        self.widget_main.setObjectName(u"widget_main")
        self.verticalLayout_2 = QVBoxLayout(self.widget_main)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.widget_2 = QWidget(self.widget_main)
        self.widget_2.setObjectName(u"widget_2")
        self.horizontalLayout_4 = QHBoxLayout(self.widget_2)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout_4.setContentsMargins(-1, 0, -1, 0)
        self.widget_11 = QWidget(self.widget_2)
        self.widget_11.setObjectName(u"widget_11")
        self.gridLayout_2 = QGridLayout(self.widget_11)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout_2.setContentsMargins(-1, 0, -1, 0)
        self.widget_3 = QWidget(self.widget_11)
        self.widget_3.setObjectName(u"widget_3")
        self.horizontalLayout = QHBoxLayout(self.widget_3)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.label = QLabel(self.widget_3)
        self.label.setObjectName(u"label")

        self.horizontalLayout.addWidget(self.label)

        self.horizontalSpacer = QSpacerItem(77, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer)

        self.UserBox = SearchComboBox(self.widget_3)
        self.UserBox.setObjectName(u"UserBox")

        self.horizontalLayout.addWidget(self.UserBox)

        self.horizontalSpacer_2 = QSpacerItem(77, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_2)

        self.horizontalLayout.setStretch(0, 2)
        self.horizontalLayout.setStretch(1, 2)
        self.horizontalLayout.setStretch(2, 7)
        self.horizontalLayout.setStretch(3, 2)

        self.gridLayout_2.addWidget(self.widget_3, 0, 0, 1, 1)

        self.widget_4 = QWidget(self.widget_11)
        self.widget_4.setObjectName(u"widget_4")
        self.horizontalLayout_2 = QHBoxLayout(self.widget_4)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.label_2 = QLabel(self.widget_4)
        self.label_2.setObjectName(u"label_2")

        self.horizontalLayout_2.addWidget(self.label_2)

        self.horizontalSpacer_3 = QSpacerItem(77, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_3)

        self.GroupBox = SearchComboBox(self.widget_4)
        self.GroupBox.setObjectName(u"GroupBox")

        self.horizontalLayout_2.addWidget(self.GroupBox)

        self.horizontalSpacer_4 = QSpacerItem(77, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_4)

        self.horizontalLayout_2.setStretch(0, 2)
        self.horizontalLayout_2.setStretch(1, 2)
        self.horizontalLayout_2.setStretch(2, 7)
        self.horizontalLayout_2.setStretch(3, 2)

        self.gridLayout_2.addWidget(self.widget_4, 1, 0, 1, 1)

        self.widget_5 = QWidget(self.widget_11)
        self.widget_5.setObjectName(u"widget_5")
        self.horizontalLayout_3 = QHBoxLayout(self.widget_5)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_3.setContentsMargins(-1, -1, -1, 9)
        self.label_3 = QLabel(self.widget_5)
        self.label_3.setObjectName(u"label_3")

        self.horizontalLayout_3.addWidget(self.label_3)

        self.horizontalSpacer_5 = QSpacerItem(77, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_5)

        self.PermissionBox = SearchComboBox(self.widget_5)
        self.PermissionBox.setObjectName(u"PermissionBox")

        self.horizontalLayout_3.addWidget(self.PermissionBox)

        self.horizontalSpacer_6 = QSpacerItem(77, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_6)

        self.horizontalLayout_3.setStretch(0, 2)
        self.horizontalLayout_3.setStretch(1, 2)
        self.horizontalLayout_3.setStretch(2, 7)
        self.horizontalLayout_3.setStretch(3, 2)

        self.gridLayout_2.addWidget(self.widget_5, 2, 0, 1, 1)


        self.horizontalLayout_4.addWidget(self.widget_11)

        self.widget_10 = QWidget(self.widget_2)
        self.widget_10.setObjectName(u"widget_10")
        self.verticalLayout_3 = QVBoxLayout(self.widget_10)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalSpacer = QSpacerItem(20, 18, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_3.addItem(self.verticalSpacer)

        self.Query = QPushButton(self.widget_10)
        self.Query.setObjectName(u"Query")

        self.verticalLayout_3.addWidget(self.Query)

        self.verticalSpacer_2 = QSpacerItem(20, 18, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_3.addItem(self.verticalSpacer_2)


        self.horizontalLayout_4.addWidget(self.widget_10)

        self.horizontalLayout_4.setStretch(0, 4)
        self.horizontalLayout_4.setStretch(1, 1)

        self.verticalLayout_2.addWidget(self.widget_2)

        self.groupBox = QGroupBox(self.widget_main)
        self.groupBox.setObjectName(u"groupBox")
        self.verticalLayout = QVBoxLayout(self.groupBox)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.widget_6 = QWidget(self.groupBox)
        self.widget_6.setObjectName(u"widget_6")
        self.horizontalLayout_5 = QHBoxLayout(self.widget_6)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.horizontalSpacer_7 = QSpacerItem(49, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer_7)

        self.label_4 = QLabel(self.widget_6)
        self.label_4.setObjectName(u"label_4")

        self.horizontalLayout_5.addWidget(self.label_4)

        self.PageBox = QSpinBox(self.widget_6)
        self.PageBox.setObjectName(u"PageBox")
        self.PageBox.setMinimum(1)

        self.horizontalLayout_5.addWidget(self.PageBox)

        self.horizontalSpacer_8 = QSpacerItem(257, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer_8)

        self.label_5 = QLabel(self.widget_6)
        self.label_5.setObjectName(u"label_5")

        self.horizontalLayout_5.addWidget(self.label_5)

        self.VolumeBox = QSpinBox(self.widget_6)
        self.VolumeBox.setObjectName(u"VolumeBox")
        self.VolumeBox.setMinimum(10)
        self.VolumeBox.setMaximum(100)
        self.VolumeBox.setValue(20)

        self.horizontalLayout_5.addWidget(self.VolumeBox)

        self.horizontalSpacer_9 = QSpacerItem(49, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer_9)

        self.horizontalLayout_5.setStretch(0, 1)
        self.horizontalLayout_5.setStretch(1, 1)
        self.horizontalLayout_5.setStretch(2, 2)
        self.horizontalLayout_5.setStretch(3, 5)
        self.horizontalLayout_5.setStretch(4, 1)
        self.horizontalLayout_5.setStretch(5, 2)
        self.horizontalLayout_5.setStretch(6, 1)

        self.verticalLayout.addWidget(self.widget_6)

        self.listView = QTableViewPermsNoteWidget(self.groupBox)
        self.listView.setObjectName(u"listView")

        self.verticalLayout.addWidget(self.listView)


        self.verticalLayout_2.addWidget(self.groupBox)

        self.verticalLayout_2.setStretch(0, 1)
        self.verticalLayout_2.setStretch(1, 4)
        self.groupBox.raise_()
        self.widget_2.raise_()

        self.horizontalLayout_6.addWidget(self.widget_main)

        self.widget = QWidget(self.centralwidget)
        self.widget.setObjectName(u"widget")
        self.gridLayout = QGridLayout(self.widget)
        self.gridLayout.setObjectName(u"gridLayout")
        self.treeView = QTreeViewPermsNoteWidget(self.widget)
        self.treeView.setObjectName(u"treeView")

        self.gridLayout.addWidget(self.treeView, 0, 0, 1, 1)


        self.horizontalLayout_6.addWidget(self.widget)

        self.horizontalLayout_6.setStretch(0, 7)
        self.horizontalLayout_6.setStretch(1, 3)
        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)
        self.toolBar = QToolBar(MainWindow)
        self.toolBar.setObjectName(u"toolBar")
        MainWindow.addToolBar(Qt.ToolBarArea.TopToolBarArea, self.toolBar)

        # self.toolBar.addAction(self.actionGroupManage)
        # self.toolBar.addAction(self.actionUserManage)
        self.toolBar.addAction(self.actionPlatformMannage)
        # self.toolBar.addAction(self.actionPlatformPermission)
        # self.toolBar.addAction(self.actionPlatformGroup)
        # self.toolBar.addAction(self.actionPlatformUser)
        # self.toolBar.addAction(self.actionNormalGroup)
        # self.toolBar.addAction(self.actionNormalUser)
        self.toolBar.addAction(self.actionPermission)
        self.toolBar.addSeparator()
        self.toolBar.addAction(self.actionRefresh)

        self.retranslateUi(MainWindow)
        # self.actionGroupManage.triggered.connect(MainWindow.group_manage)
        # self.actionUserManage.triggered.connect(MainWindow.user_manage)
        self.actionRefresh.triggered.connect(MainWindow.refresh)
        self.Query.clicked.connect(MainWindow.query_perms_note)
        self.VolumeBox.valueChanged.connect(MainWindow.change_volume)
        self.PageBox.valueChanged.connect(MainWindow.change_page)
        self.actionPlatformMannage.triggered.connect(MainWindow.platform_manage)
        # self.actionPlatformGroup.triggered.connect(MainWindow.platform_group)
        # self.actionPlatformUser.triggered.connect(MainWindow.platform_user)
        # self.actionNormalGroup.triggered.connect(MainWindow.normal_group)
        # self.actionNormalUser.triggered.connect(MainWindow.normal_user)
        self.actionPermission.triggered.connect(MainWindow.permission_manage)
        # self.actionPlatformPermission.triggered.connect(MainWindow.platform_permission)

        self.GroupBox.currentIndexChanged.connect(MainWindow.change_perm_type)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"Permissions", None))
        # self.actionGroupManage.setText(QCoreApplication.translate("MainWindow", u"Group Manage", None))
#if QT_CONFIG(tooltip)
        # self.actionGroupManage.setToolTip(QCoreApplication.translate("MainWindow", u"Group Manage", None))
#endif // QT_CONFIG(tooltip)
        # self.actionUserManage.setText(QCoreApplication.translate("MainWindow", u"UserManage", None))
#if QT_CONFIG(tooltip)
        # self.actionUserManage.setToolTip(QCoreApplication.translate("MainWindow", u"UserManage", None))
#endif // QT_CONFIG(tooltip)
        self.actionRefresh.setText(QCoreApplication.translate("MainWindow", u"Refresh", None))
        self.actionPlatformMannage.setText(QCoreApplication.translate("MainWindow", u"Platform", None))
        # self.actionPlatformGroup.setText(QCoreApplication.translate("MainWindow", u"Platform Group", None))
        # self.actionPlatformUser.setText(QCoreApplication.translate("MainWindow", u"Platform User", None))
        # self.actionNormalGroup.setText(QCoreApplication.translate("MainWindow", u"Normal Group", None))
        # self.actionNormalUser.setText(QCoreApplication.translate("MainWindow", u"Normal User", None))
        self.actionPermission.setText(QCoreApplication.translate("MainWindow", u"Permission Manage", None))
        # self.actionPlatformPermission.setText(QCoreApplication.translate("MainWindow", u"PlatformPermission", None))
        self.label.setText(QCoreApplication.translate("MainWindow", u"target_name", None))
        self.label_2.setText(QCoreApplication.translate("MainWindow", u"perm_type", None))
        self.label_3.setText(QCoreApplication.translate("MainWindow", u"permission", None))
        self.Query.setText(QCoreApplication.translate("MainWindow", u"query", None))
        self.groupBox.setTitle(QCoreApplication.translate("MainWindow", u"PermissionNote", None))
        self.label_4.setText(QCoreApplication.translate("MainWindow", u"Page", None))
        self.label_5.setText(QCoreApplication.translate("MainWindow", u"Volume", None))
        self.toolBar.setWindowTitle(QCoreApplication.translate("MainWindow", u"toolBar", None))
    # retranslateUi

