# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/12/31
# __author:       xw

import numpy as np

from ....analysis.library import DistortionPhaseAnalysis
from ....parameters import options_wrapper
from ....pulse.pulse_function import half_pi_pulse, zero_pulse
from ....pulse.pulse_lib import Constant
from ....structures import Options
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


@options_wrapper
class DistortionAssistPhase(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
        """
        options = super()._default_experiment_options()

        options.set_validator("delay", float)
        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("phase_list", list)
        options.set_validator("separation", float)

        options.delay = 10
        options.z_amp = 0.13
        options.phase_list = np.linspace(0, 2 * np.pi, 15).tolist()
        options.separation = 200

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """"""
        options = super()._default_analysis_options()
        options.set_validator("quality_bounds", list)
        options.set_validator("filter", dict)

        options.data_key = ["P1"]
        options.filter = {"window_length": 5, "polyorder": 3}
        options.quality_bounds = [0.95, 0.9, 0.8]
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.exp_qubit = None
        options.drag_qubit = None

        options.support_context = [StandardContext.QC.value]

        return options

    def _check_options(self):
        super()._check_options()
        if self.discriminator is None:
            data_type = "amp_phase"
        else:
            data_type = "I_Q"

        self.set_experiment_options(data_type=data_type)

        self.set_run_options(
            x_data=self.experiment_options.phase_list,
            analysis_class=DistortionPhaseAnalysis,
            exp_qubit=self.qubit,
            drag_qubit=self.qubit,
        )

    @staticmethod
    def set_xy_pulses(self):
        phase_list = self.experiment_options.phase_list
        pulses = DistortionAssistPhase.get_xy_pulses(self, phase_list)
        self.play_pulse("XY", self.run_options.drag_qubit, pulses)

    def get_xy_pulses(self, phase_list):
        xy_pulse_list = []
        # delay = self.experiment_options.delay
        separation = self.experiment_options.separation
        for phase in phase_list:
            front_pulse = half_pi_pulse(self.run_options.drag_qubit)
            step_pulse = Constant(separation, 0, "XY")
            rear_pulse = half_pi_pulse(self.run_options.drag_qubit)
            rear_pulse.phase = phase
            xy_pulse = front_pulse() + step_pulse() + rear_pulse()
            xy_pulse_list.append(xy_pulse)
        return xy_pulse_list

    @staticmethod
    def set_z_pulses(self):
        delay = self.experiment_options.delay
        separation = self.experiment_options.separation
        phase_list = self.experiment_options.phase_list
        z_amp = self.experiment_options.z_amp
        z_pulses = DistortionAssistPhase.get_z_pulses(
            self.run_options.drag_qubit, phase_list, z_amp, delay, separation
        )
        self.play_pulse("Z", self.run_options.exp_qubit, z_pulses)

    @staticmethod
    def get_z_pulses(qubit, phase_list, z_amp, delay, separation):
        z_pulse_list = []
        rear_width = qubit.XYwave.time + qubit.XYwave.offset * 2
        for _ in phase_list:
            delay_pulse = zero_pulse(qubit, "Z")
            step_pulse = Constant(delay, z_amp)
            mid_pulse = Constant(round(separation - delay, 4), 0)
            rear_pulse = Constant(rear_width, 0)
            z_pulse = delay_pulse() + step_pulse() + mid_pulse() + rear_pulse()
            z_pulse_list.append(z_pulse)
        return z_pulse_list
