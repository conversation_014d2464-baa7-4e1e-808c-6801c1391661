# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/19
# __author:       <PERSON><PERSON><PERSON>

"""
Task and data collection transfer service.
"""

from __future__ import annotations

import asyncio
import os
import threading
import traceback

import zmq
from zmq.asyncio import Context, Poller, Socket

from .data_service import DispatcherManager
from .log import Logger, LogLevel, logger
from .message import start_transfer_socket
from .share import Share<PERSON>rea
from .state import LinkRequireCode, TransferTaskStatusEnum
from .util import (
    build_ipc_file,
    decode_msg,
    encode_msg,
    generate_unique_identity
)

share_lock = threading.Lock()


class Transfer:
    def __init__(self, settings, process_name: str):
        """Initializes the instance of the class.

        Args:
            settings (QDict): The QDict object that contains configuration
                parameters for the instance.

        Attributes:
            _settings (QDict): The settings object passed during initialization.
            _data_service (DataService | None): An optional data service object that
                send monster program and receive acq data. It is initialized to None
                and should be set by subclasses or by other methods of the class.

        Notes:
            This method also calls the `_initialize` method to perform any additional
            initialization tasks that may be required by the subclass.
        """
        self._process_name = process_name
        self._settings = settings
        self._share = ShareArea(process_name, settings)
        self._data_service: DispatcherManager | None = None
        self._transfer_threads = {}
        self._link_socket = None
        self._pub_socket = None
        self._poller = Poller()
        self._initialize()

        self._data_service_task = None
        self._link_map = {
            LinkRequireCode.LINK: self._create_transfer_sock_thread,
            LinkRequireCode.SET: self._set_dispatcher,
            LinkRequireCode.CLOSE: self._close,
            LinkRequireCode.STOP: self._stop,
            LinkRequireCode.QUERY_PROGRAM: self._query_program,
        }

    def __repr__(self) -> str:
        return f"Transfer Process {os.getpid()}"

    def _initialize(self):
        """
        Instantiate DataService object.
        """

        link_url = build_ipc_file(self._settings.link_url + self._process_name)
        link_id = self._settings.link_id

        ctx = Context.instance()
        self._link_socket: Socket = ctx.socket(zmq.ROUTER)
        self._link_socket.setsockopt(zmq.IDENTITY, generate_unique_identity(link_id))
        self._link_socket.bind(link_url)
        self._poller.register(self._link_socket, zmq.POLLIN)
        
        pub_url = build_ipc_file(self._settings.pub_url + self._process_name)
        self._pub_socket = Context.instance().socket(zmq.PUB)
        self._pub_socket.bind(pub_url)

    async def _monitor_data_client(self):
        logger.log(LogLevel.SYSTEM, "Transfer-monitor client start...")
        while self._share.close is False:
            res_ = dict(await self._poller.poll(1000))
            if self._link_socket in res_:
                client, code, *message = await self._link_socket.recv_multipart()
                if code in self._link_map:
                    result = await self._link_map[code](*message)
                    if result:
                        msg = [client]
                        msg.extend(result)
                        await self._link_socket.send_multipart(msg)
        logger.log(LogLevel.SYSTEM, "Transfer-monitor client end...")

    async def listening_time_out(self):
        """
        Asynchronous system resource listener for the transfer.

        This coroutine will poll for tasks in the listening area. If the task status
        is FINISH, the task resources will be cleared and memory will be released.
        """
        logger.log(LogLevel.SYSTEM, "Transfer-listening_time_out start...")
        while self._share.close is False:
            # logger.info("monitor task time out")
            for task in self._share.transfer_task_collection.values():
                if task.check_time_out():
                    logger.warning(f"Overdue waiting, ready to send {task}")
                    await self._pub_socket.send_string(str(task))
            await asyncio.sleep(2)
        logger.log(LogLevel.SYSTEM, "Transfer-listening_time_out end...")

    async def _close(self):
        logger.log(LogLevel.SYSTEM, f"{self} will close ...")
        self._share.close = True
        self._share.clear_ipc_file()

    async def _stop(self):
        share_lock.acquire()
        self._share.stop = True
        if self._data_service:
            self._data_service._stop_task()
        self._share.clear_resource()
        self._share.stop = False
        share_lock.release()

    async def _query_program(self, buffer: bytes):
        try:
            task_id = decode_msg(buffer)
            response = self._data_service.web_client.query_program(task_id)
            return [response["data"]["program"]]
        except Exception as e:
            logger.error(f"query {task_id} error | {e}")
            return [LinkRequireCode.QUERY_PROGRAM]

    async def _create_transfer_sock_thread(self, name: bytes):
        if name and name not in self._transfer_threads:
            token = decode_msg(name)
            self._transfer_threads[name] = start_transfer_socket(
                self._settings.transfer_url,
                self._settings.transfer_id,
                token,
                self._share,
            )
            self._share.transfer_addr_list.append(token)
        for name in list(self._transfer_threads.keys()):
            if self._transfer_threads[name].running is False:
                self._transfer_threads.pop(name)
        return [name]

    async def _set_dispatcher(self, setting: bytes):
        try:
            setting_data = decode_msg(setting, parse_json=True)

            # update dispatcher addr
            if self._data_service is None or self._data_service._dispatcher_url != setting_data["dispatcher_url"]:
                if self._data_service and self._data_service_task is not None:
                    while not self._data_service_task.done():
                        self._share.stop = True
                        self._data_service.close()
                        await asyncio.sleep(1)

                self._share.clear_resource()
                self._data_service = DispatcherManager(
                    setting_data["dispatcher_url"],
                    self._settings.dispatcher_id,
                    setting_data["token"],
                    setting_data["web_url"],
                    self._share,
                )
                self._share.stop = False
                self._data_service_task = asyncio.create_task(self._data_service.run())

            # update web token
            if self._data_service:
                self._data_service.web_client.token = setting_data["token"]
                self._data_service.web_client.url = setting_data["web_url"]

            # update log path
            log_path = setting_data.get("log_path")
            if log_path:
                log_obj = Logger(log_path=setting_data.get("log_path"), print_terminal=False)
                log_obj.set_log_level()
                log_obj.save_log()
                logger.log(
                    LogLevel.SYSTEM,
                    f"Result logger successfully. Data transfer log will save at: {log_obj.log_path}",
                )
            result = {"status": str(TransferTaskStatusEnum.TACKLE_SUC)}
        except Exception:
            error_msg = traceback.format_exc()
            logger.error(f"set dispatcher error | {error_msg}")
            result = {"status": str(TransferTaskStatusEnum.TACKLE_FAIL), "reason": str(error_msg)}
        return [encode_msg(result)]

    async def run(self):
        """
        Asynchronously runs the transfer's tasks.

        This method orchestrates the execution of three main asynchronous tasks:
        1. Monster program handling (`self._monitor_program()`): Handles monster
            task program.
        2. Dispatcher result handling (`self._monitor_result()`): Handles dispatcher
            message, include task_uuid、acq_data、state ...
        3. Monster memory resource handing(`self._monitor_task()`): Monitor memory
            resources and clean them up in a timely manner.

        All tasks are executed concurrently using `asyncio.gather()`, which waits for
        all tasks to complete successfully. If any task raises an exception, the exception
        is propagated and `asyncio.gather()` cancels the remaining tasks.

        In the case where the entire dispatcher operation is cancelled (e.g., by an
        external signal or another task), an `asyncio.CancelledError` is caught and a
        warning log is issued, indicating that the dispatcher was cancelled.

        Returns:
            This method is an asynchronous function and does not return a value
            directly. Instead, it completes its execution once all tasks are handled or
            the dispatcher is cancelled.

        Raises:
            Exceptions raised by `self._monitor_program()`, `self._monitor_result()`, or
            `self._monitor_resource()` are propagated and can be caught by the caller if
            desired. However, `asyncio.CancelledError` is specifically caught and logged
            as a warning, indicating dispatcher cancellation.
        """
        try:
            logger.log(LogLevel.SYSTEM, f"{self} start ...")
            await asyncio.gather(
                self._monitor_data_client(),
                self.listening_time_out()
            )
        except Exception:
            error_msg = traceback.format_exc()
            logger.error(f"{self} crash. {error_msg}")
        finally:
            if self._data_service_task and not self._data_service_task.done():
                await self._data_service_task
            logger.log(LogLevel.SYSTEM, f"{self} close ...")
