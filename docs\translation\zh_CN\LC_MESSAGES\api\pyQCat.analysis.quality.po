# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.analysis.quality.rst:2
msgid "pyQCat.analysis.quality package"
msgstr ""

#: ../../source/api/pyQCat.analysis.quality.rst:5
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.analysis.quality.rst:8
msgid "pyQCat.analysis.quality.goodness\\_of\\_fit module"
msgstr ""

#: of pyQCat.analysis.quality.goodness_of_fit.GoodnessofFit:1
#: pyQCat.analysis.quality.single_shot_quality.SingleShotQuality:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: ../../source/api/pyQCat.analysis.quality.rst:16
msgid "pyQCat.analysis.quality.outlier\\_error module"
msgstr ""

#: ../../source/api/pyQCat.analysis.quality.rst:24
msgid "pyQCat.analysis.quality.single\\_shot\\_quality module"
msgstr ""

#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality:1
#: pyQCat.analysis.quality:12:<autosummary>:1
msgid "SingleShot analysis quality evaluation standard."
msgstr ""

#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality.quality:1
msgid "Quality value."
msgstr ""

#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality.evaluate:1
msgid "Calculate quality value."
msgstr ""

#: ../../source/api/pyQCat.analysis.quality.rst:32
msgid "Module contents"
msgstr ""

#: of pyQCat.analysis.quality:3
msgid "Analysis Quality  (:mod:`pyQCat.analysis.quality`)"
msgstr ""

#: of pyQCat.analysis.quality:6
msgid "Base Classes"
msgstr ""

#: of pyQCat.analysis.quality:12:<autosummary>:1
msgid ""
":py:obj:`GoodnessofFit <pyQCat.analysis.quality.GoodnessofFit>`\\ "
"\\(perfect\\, normal\\, abnormal\\)"
msgstr ""

#: of pyQCat.analysis.quality:12:<autosummary>:1
msgid ""
":py:obj:`SingleShotQuality <pyQCat.analysis.quality.SingleShotQuality>`\\"
" \\(k\\_recommend\\, F0\\, F1\\, ...\\)"
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`GoodnessofFit "
#~ "<pyQCat.analysis.quality.GoodnessofFit>`\\ \\(perfect\\, "
#~ "normal\\, abnormal\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`SingleShotQuality "
#~ "<pyQCat.analysis.quality.SingleShotQuality>`\\ "
#~ "\\(k\\_recommend\\, F0\\, F1\\, ...\\)"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`GoodnessofFit <pyQCat.analysis.quality.GoodnessofFit>`\\"
#~ " \\(perfect\\, normal\\, abnormal\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`SingleShotQuality "
#~ "<pyQCat.analysis.quality.SingleShotQuality>`\\ "
#~ "\\(k\\_recommend\\, F0\\, F1\\, ...\\)"
#~ msgstr ""

