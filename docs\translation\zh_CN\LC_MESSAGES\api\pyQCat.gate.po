# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.gate.rst:2
msgid "pyQCat.gate package"
msgstr ""

#: ../../source/api/pyQCat.gate.rst:5
msgid "Module contents"
msgstr ""

#: of pyQCat.gate:3
msgid "Common Gate (:mod:`pyQCat.gate`)"
msgstr ""

#: of pyQCat.gate:5
msgid "Common gate."
msgstr ""

#: of pyQCat.gate:8
msgid "Base Classes"
msgstr ""

#: of pyQCat.gate:20:<autosummary>:1
msgid ":py:obj:`Xgate <pyQCat.gate.Xgate>`\\ \\(\\)"
msgstr ""

#: of pyQCat.gate:20:<autosummary>:1
msgid ":py:obj:`Ygate <pyQCat.gate.Ygate>`\\ \\(\\)"
msgstr ""

#: of pyQCat.gate:20:<autosummary>:1
msgid ":py:obj:`RXgate <pyQCat.gate.RXgate>`\\ \\(theta\\)"
msgstr ""

#: of pyQCat.gate:20:<autosummary>:1
msgid ":py:obj:`RYgate <pyQCat.gate.RYgate>`\\ \\(theta\\)"
msgstr ""

#: of pyQCat.gate:20:<autosummary>:1
msgid ":py:obj:`Igate <pyQCat.gate.Igate>`\\ \\(\\)"
msgstr ""

#: of pyQCat.gate:20:<autosummary>:1
msgid ""
":py:obj:`CPhaseGate <pyQCat.gate.CPhaseGate>`\\ "
"\\(\\[accumulation\\_phase\\]\\)"
msgstr ""

#: of pyQCat.gate:20:<autosummary>:1
msgid ":py:obj:`GateCollection <pyQCat.gate.GateCollection>`\\ \\(\\)"
msgstr ""

#: of pyQCat.gate:20:<autosummary>:1
msgid ":py:obj:`GateBucket <pyQCat.gate.GateBucket>`\\ \\(\\)"
msgstr ""

#: of pyQCat.gate.notable_gate.CPhaseGate:1
#: pyQCat.gate.notable_gate.GateBucket:1
#: pyQCat.gate.notable_gate.GateCollection:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.gate.notable_gate.Igate:1 pyQCat.gate.notable_gate.RXgate:1
#: pyQCat.gate.notable_gate.RYgate:1 pyQCat.gate.notable_gate.Rphi_gate:1
#: pyQCat.gate.notable_gate.Xgate:1 pyQCat.gate.notable_gate.Ygate:1
msgid "Bases: :py:class:`~pyQCat.gate.notable_gate.SingleQgate`"
msgstr ""

#: of pyQCat.gate.notable_gate.Igate.to_pulse:1
#: pyQCat.gate.notable_gate.Rphi_gate.to_pulse:1
#: pyQCat.gate.notable_gate.Xgate.to_pulse:1
#: pyQCat.gate.notable_gate.Ygate.to_pulse:1
msgid "Convert quantum logic gate to pulse Object."
msgstr ""

#: of pyQCat.gate.notable_gate.Igate.to_pulse
#: pyQCat.gate.notable_gate.RXgate.to_pulse
#: pyQCat.gate.notable_gate.RYgate.to_pulse
#: pyQCat.gate.notable_gate.Rphi_gate.to_pulse
#: pyQCat.gate.notable_gate.Xgate.to_pulse
#: pyQCat.gate.notable_gate.Ygate.to_pulse
msgid "Return type"
msgstr ""

#: of pyQCat.gate.notable_gate.Igate.to_pulse:4
#: pyQCat.gate.notable_gate.RXgate.to_pulse:4
#: pyQCat.gate.notable_gate.RYgate.to_pulse:4
#: pyQCat.gate.notable_gate.Rphi_gate.to_pulse:4
#: pyQCat.gate.notable_gate.Xgate.to_pulse:4
#: pyQCat.gate.notable_gate.Ygate.to_pulse:4
msgid ":py:class:`~pyQCat.pulse.base_pulse.PulseComponent`"
msgstr ""

#: of pyQCat.gate.notable_gate.RXgate.to_pulse:1
#: pyQCat.gate.notable_gate.RYgate.to_pulse:1
msgid "todo Optmized"
msgstr ""

#~ msgid ":py:obj:`Xgate <pyQCat.gate.Xgate>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`Ygate <pyQCat.gate.Ygate>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`RXgate <pyQCat.gate.RXgate>`\\ \\(theta\\)"
#~ msgstr ""

#~ msgid ":py:obj:`RYgate <pyQCat.gate.RYgate>`\\ \\(theta\\)"
#~ msgstr ""

#~ msgid ":py:obj:`Igate <pyQCat.gate.Igate>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`Rphi_gate <pyQCat.gate.Rphi_gate>`\\ "
#~ "\\(phase\\[\\, theta\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`Cphase_gate <pyQCat.gate.Cphase_gate>`\\ \\(qc\\, qt\\)"
#~ msgstr ""

#~ msgid ":py:obj:`GateCollection <pyQCat.gate.GateCollection>`\\ \\(\\)"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.gate.notable_gate.SingleQgate`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ":obj:`Xgate <pyQCat.gate.Xgate>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`Ygate <pyQCat.gate.Ygate>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`RXgate <pyQCat.gate.RXgate>`\\ \\(theta\\)"
#~ msgstr ""

#~ msgid ":obj:`RYgate <pyQCat.gate.RYgate>`\\ \\(theta\\)"
#~ msgstr ""

#~ msgid ":obj:`Igate <pyQCat.gate.Igate>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`Rphi_gate <pyQCat.gate.Rphi_gate>`\\ \\(phase\\[\\, theta\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`Cphase_gate <pyQCat.gate.Cphase_gate>`\\ \\(qc\\, qt\\)"
#~ msgstr ""

#~ msgid ":obj:`GateCollection <pyQCat.gate.GateCollection>`\\ \\(\\)"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.gate.notable_gate.SingleQgate`"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

