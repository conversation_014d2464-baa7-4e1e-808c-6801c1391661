# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/10/19
# __author:       SSFang

"""
Xpi detection composite experiment.
"""

from copy import deepcopy

import numpy as np

from ....log import pyqlog
from ....structures import MetaData, Options
from ....tools import qarange
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import RabiScanAmp, CouplerRabiScanAmp, RabiScanAmpF12


class XpiDetection(CompositeExperiment):
    """Xpi Detection, RabiScanAmp Composite Experiment."""

    _sub_experiment_class = RabiScanAmp

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            expect_value (float): The expect Xpi or Xpi2 value.
            scope (float): The Xpi or Xpi2 value accept floating scope,
                           default 0.1, if expect_value 0.7,
                           that target range [ 0.6, 0.8].
            max_loops (int): The max execute child experiment times.

            name (str): Indicates which type of pulse is selected
                                  to perform the experimentOnly 'Xpi' and
                                  'Xpi/2' supported.
            amps (list, np.ndarray): The list of XY pulse amplitudes that
                                     will be scanned in the experiment.
            drive_power (float): The driver power of qubit.
        """
        options = super()._default_experiment_options()

        options.set_validator("expect_value", (0, 1, 2))
        options.set_validator("scope", float)
        options.set_validator("max_loops", int)

        options.set_validator("name", ["Xpi", "Xpi/2"])
        options.set_validator("amps", list, limit_null=True)
        options.set_validator("drive_power", (-40, -10, 1))

        options.expect_value = 0.7
        options.scope = 0.1
        options.max_loops = 5

        # RabiScanAmp experiment options
        options.amps = qarange(0, 1, 0.02)
        options.drive_power = None
        options.name = "Xpi"

        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        return options

    def _check_options(self):
        super()._check_options()
        drive_power = self.experiment_options.drive_power

        if drive_power is None:
            if self.child_experiment.is_coupler_exp is True:
                drive_power = self.coupler.drive_power
            else:
                drive_power = self.qubit.drive_power
            drive_power = drive_power or -15.0

        self.set_experiment_options(drive_power=drive_power)

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.draw_meta = {}
        return metadata

    # Shq 2024/04/29
    # for async mode.
    async def _sync_composite_run(self):
        expect_value = self.experiment_options.expect_value
        scope = self.experiment_options.scope
        max_loops = self.experiment_options.max_loops

        amps = self.experiment_options.amps
        drive_power = self.experiment_options.drive_power
        name = self.experiment_options.name

        pyqlog.log("EXP", f"The expect_value is {expect_value}.")
        pyqlog.log("EXP", f"The max test time is {max_loops}.")

        count = 0
        rabi_exp = None
        result_name = ""
        while count < max_loops:
            rabi_exp = deepcopy(self.child_experiment)
            rabi_exp.set_parent_file(
                self, f"count={count}-drive_power={drive_power}", count
            )

            rabi_exp.set_experiment_options(
                amps=amps, drive_power=drive_power, name=name
            )
            self._check_simulator_data(rabi_exp, count)
            await rabi_exp.run_experiment()

            self._experiments.append(rabi_exp)

            if not result_name:
                result_name = rabi_exp.analysis_options.result_name

            if name == "Xpi":
                exp_value = rabi_exp.analysis.results.Xpi.value
            else:
                exp_value = rabi_exp.analysis.results.Xpi2.value

            if expect_value - scope <= exp_value <= expect_value + scope:
                pyqlog.log(
                    "RESULT", f"Success, {name} {exp_value}, drive_power {drive_power}"
                )
                break
            else:
                update_step = 10 * np.log10((expect_value / exp_value) ** 2)
                drive_power -= update_step
                drive_power = round(drive_power, 1)
                if drive_power > -10 or drive_power < -40:
                    pyqlog.error(
                        f"Failed, update {result_name} drive_power {drive_power}, "
                        f"value not in range [-40, -10]"
                    )
                    break

            count += 1

        if rabi_exp is not None and hasattr(rabi_exp, "analysis"):
            self._analysis = rabi_exp.analysis
            self._save_curve_analysis_plot()


class CouplerXpiDetection(XpiDetection):
    _sub_experiment_class = CouplerRabiScanAmp


class F12XpiDetection(XpiDetection):
    _sub_experiment_class = RabiScanAmpF12
