<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>777</width>
    <height>713</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Platform</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item row="0" column="0">
     <widget class="QWidget" name="widget_main" native="true">
      <layout class="QVBoxLayout" name="verticalLayout_2" stretch="1,8">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QWidget" name="widget_2" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="4,1">
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QWidget" name="widget_11" native="true">
            <layout class="QGridLayout" name="gridLayout_2">
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QWidget" name="widget_3" native="true">
               <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,2,7,2">
                <item>
                 <widget class="QLabel" name="label">
                  <property name="text">
                   <string>权限类型</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>77</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="SearchComboBox" name="TypeBox"/>
                </item>
                <item>
                 <spacer name="horizontalSpacer_2">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>77</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_10" native="true">
            <layout class="QVBoxLayout" name="verticalLayout_3">
             <item>
              <spacer name="verticalSpacer">
               <property name="orientation">
                <enum>Qt::Orientation::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>18</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="Query">
               <property name="text">
                <string>query</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_2">
               <property name="orientation">
                <enum>Qt::Orientation::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>18</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox">
         <property name="title">
          <string>PermissionList</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout">
          <item>
           <widget class="QWidget" name="widget_6" native="true">
            <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="1,1,2,5,1,2,1">
             <item>
              <spacer name="horizontalSpacer_7">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>49</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="label_4">
               <property name="text">
                <string>Page</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QSpinBox" name="PageBox">
               <property name="minimum">
                <number>1</number>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_8">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>257</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="label_5">
               <property name="text">
                <string>Volume</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QSpinBox" name="VolumeBox">
               <property name="minimum">
                <number>10</number>
               </property>
               <property name="maximum">
                <number>100</number>
               </property>
               <property name="value">
                <number>20</number>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_9">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>49</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QTableViewPermsNoteWidget" name="listView"/>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
      <zorder>groupBox</zorder>
      <zorder>widget_2</zorder>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionCreatePlatform"/>
   <addaction name="separator"/>
  </widget>
  <action name="actionCreatePlatform">
   <property name="text">
    <string>CreatePlatform</string>
   </property>
   <property name="toolTip">
    <string>CreatePlatform</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SearchComboBox</class>
   <extends>QComboBox</extends>
   <header>.widgets.combox_custom.combox_search</header>
  </customwidget>
  <customwidget>
   <class>QTableViewPermsNoteWidget</class>
   <extends>QListView</extends>
   <header>.widgets.permissions.table_view_perms_note</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>actionCreatePlatform</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>create_platform()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>414</x>
     <y>314</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>Query</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>query_info()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>678</x>
     <y>105</y>
    </hint>
    <hint type="destinationlabel">
     <x>388</x>
     <y>326</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>PageBox</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>change_page()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>195</x>
     <y>209</y>
    </hint>
    <hint type="destinationlabel">
     <x>388</x>
     <y>356</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>VolumeBox</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>change_volume()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>629</x>
     <y>209</y>
    </hint>
    <hint type="destinationlabel">
     <x>388</x>
     <y>356</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>query_info()</slot>
  <slot>create_platform()</slot>
  <slot>change_volume()</slot>
  <slot>change_page()</slot>
  <slot>platform_permission()</slot>
 </slots>
</ui>
