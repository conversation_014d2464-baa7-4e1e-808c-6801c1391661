# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.cosine_fit_guess.rst:2
msgid "pyQCat.analysis.algorithms.cosine\\_fit\\_guess"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:1
msgid "Calculate the initial value of the cosine function fit"
msgstr "计算余弦函数拟合初始值"

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:3
msgid "The cosine function formula is as follows:"
msgstr "余弦函数模型如下："

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:5
msgid ""
"f_{\\rm est} = \\frac{1}{2\\pi {\\rm max}\\left| y \\right|}\n"
"    {\\rm max} \\left| \\frac{dy}{dx} \\right|"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:10
msgid "Get `amp`, `freq`, `phase`, `baseline` in cosine formula via `np.fft.fft`"
msgstr "从 `np.fft.fft` 中获取 `amp`, `freq`, `phase`, `baseline` 的值。"

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:14
msgid ""
"Assuming that the output signal has `N` sampling points, the result after"
" `fft` transformation is `N` complex numbers, each complex number "
"corresponds to a frequency (the frequency corresponding to the `n <= N/2`"
" point is `( n-1)/N*Fs` ), the modulus value of this complex number "
"represents the amplitude characteristic of this frequency. The "
"relationship between this amplitude feature and the amplitude of the "
"original signal is: If the amplitude of the original signal is `A`, then "
"the modulo value of each point (except the first DC component point) of "
"the `fft` result is `A` `N/2` times; while the modulus value of the first"
" point is `N` times the amplitude of the DC component."
msgstr ""
"假设输出信号有N个采样点，fft变换后的结果是N个复数，每个复数对应一个频率"
"（n <= N/2点对应的频率为(n-1)/N*Fs )，这个复数的模值代表这个频率的幅度特性。"
" 该幅值特征与原始信号幅值的关系为：若原始信号幅值为A，则fft结果各点（除第一个"
"直流分量点外）的模值为A N/2 倍； 而第一个点的模值是直流分量幅度的N倍。"

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:22
msgid ""
"The `N` complex points that are left after the first point is removed "
"from the `N-1` points are conjugate symmetrical about their center, so "
"actually only the spectrum of the first half of the points needs to be "
"taken, because the conjugate symmetry The modulus value (amplitude) of "
"the two points is the same."
msgstr ""
"从N-1个点中去掉第一个点后剩下的N个复点关于它们的中心是共轭对称的，所以实际"
"上只需要取前半部分点，因为共轭对称的模值两点的（幅度）相同。"

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:27
msgid ""
"According to the above steps, the algorithm is roughly implemented as "
"follows:"
msgstr "根据上面的介绍，算法大致实现如下："

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:29
msgid ""
"First determine the signal number domain for subsequent determination of "
"the actual signal oscillation frequency"
msgstr "先确定信号数域，用于后续确定实际信号振荡频率"

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:30
msgid "Fourier transform to obtain the first half of the calculation results"
msgstr "傅里叶变换得到前半部分的计算结果"

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:31
msgid ""
"Select the frequency point corresponding to the maximum value of the "
"amplitude feature `index`"
msgstr "选择幅值特征 `index` 最大值对应的频点"

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:32
msgid ""
"Get the main signal amplitude by the maximum amplitude characteristic / "
"(N / 2)"
msgstr "通过最大幅度特性/(N/2)得到主信号幅度"

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:33
msgid ""
"Calculate the frequency corresponding to the main signal through the "
"maximum frequency point and the number domain"
msgstr "通过最大频点和数域计算主信号对应的频率"

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:34
msgid "Calculate the signal phase from the real and imaginary parts"
msgstr "从实部和虚部计算信号相位"

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:35
msgid ""
"DC signal (`baseline`) can be calculated directly from the modulo value "
"of the first point"
msgstr "直流信号（基线）可以直接从第一个点的模值计算"

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:45
msgid "x data"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:47
msgid "y data"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:49
msgid ":py:data:`~typing.Tuple`"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:50
msgid "Cosine function fit initial value"
msgstr "余弦信号初始值"

