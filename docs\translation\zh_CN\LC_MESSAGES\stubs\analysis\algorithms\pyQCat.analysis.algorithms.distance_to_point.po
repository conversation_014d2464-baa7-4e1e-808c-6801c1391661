# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 18:05+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.distance_to_point.rst:2
msgid "pyQCat.analysis.algorithms.distance\\_to\\_point"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point:1
msgid ""
"Calculate the index distance required for peak finding based on the "
"X-axis numerical distance"
msgstr "根据X轴数值距离计算寻峰所需的索引距离"

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point:3
msgid ""
"For example, in the bit spectrum scanning, we assume that the peak "
"interval between the 1 energy level and the 2 energy level is 100MHz. If "
"this is the limit modulation of the distance that needs to be added for "
"peak searching, you cannot directly set the distance to 100, because it "
"is a dimensionless value. , the distance is judged according to the index"
" of the output signal, so it is necessary to map 100MHz into the index "
"distance, which needs to be calculated by the following formula:"
msgstr ""
"如我们在比特能谱扫描中，假设1能级和2能级的峰间隔为100MHz，如果这是寻峰需要加"
"距离的限制调制，不可以直接设置distance为100，因为它是无量纲的数值，根据输出信"
"号的索引来进行距离判别，因此需要将100MHz映射成索引距离，这是后就需要通过下面的"
"公式计算。"

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point:10
msgid ""
"n_{distance} = \\frac{x_{distance}}{\\frac{x_{right} - x_{left}}{gaps}} ="
" \\frac{x_{distance} * gaps}{x_{right} - x_{left}}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point:14
msgid "The minimum interval between peaks (based on the actual input semaphore)."
msgstr "峰值最小间隔(以实际输入信号量为基准)"

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point:17
msgid "input signal."
msgstr "输入信号"

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point:20
msgid "Minimum interval between peaks (based on the input signal index)"
msgstr "峰值最小间隔(以输入信号索引为基准)"

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point
msgid "Return type"
msgstr ""

#~ msgid "Distance convert to point."
#~ msgstr ""

#~ msgid "Parameters"
#~ msgstr ""

#~ msgid "Peak minimum spacing."
#~ msgstr ""

#~ msgid "The scanned frequency range."
#~ msgstr ""

#~ msgid "Returns"
#~ msgstr ""

#~ msgid "float value."
#~ msgstr ""

#~ msgid ""
#~ "n_{distance} =\n"
#~ "\n"
#~ msgstr ""

#~ msgid ""
#~ "rac{x_{distance}}{ rac{x_{right} - x_{left}}{gaps}}"
#~ " = rac{x_{distance} * gaps}{x_{right} - "
#~ "x_{left}}"
#~ msgstr ""

#~ msgid "Args:"
#~ msgstr ""

#~ msgid ""
#~ "x_distance (float): The minimum interval "
#~ "between peaks (based on the actual "
#~ "input semaphore). x_data (List, np.ndarray):"
#~ " input signal."
#~ msgstr ""

#~ msgid "Returns:"
#~ msgstr ""

#~ msgid "float: Minimum interval between peaks (based on the input signal index)"
#~ msgstr ""

#~ msgid ":py:class:`float`"
#~ msgstr ""

