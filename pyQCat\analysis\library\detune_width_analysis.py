# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/05/09
# __author:       <PERSON><PERSON><PERSON>

import matplotlib.pyplot as plt
import numpy as np

from ..curve_analysis import CurveAnalysis
from ..quality import BaseQuality
from ..specification import ParameterRepr
from ...structures import Options
from ...types import QualityDescribe


class SweepDetuneAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Options:
            index_policy (str): Select fit target index policy.
            interaction_location (int): Select interaction point number.
        """
        options = super()._default_options()
        options.x_label = "Detune (MHz)"
        options.y_label = "Width (ns)"

        options.p_all_list = []

        options.pcolormesh_options = {
            "shading": "nearest",
            "cmap": plt.cm.get_cmap("viridis"),
        }

        options.result_parameters = []

        return options

    def _pre_operation(self):
        """Prepare one more canvas axis."""
        sub_title = []
        y_labels = []
        p_all_list = []

        child_data = self.experiment_data.child_data(index=0)
        for p_label in child_data.y_data.keys():
            p_all_list.append([])
            y_labels.append(self.options.y_label)
            sub_title.append(f"Depth {p_label}")

        self.set_options(
            y_label=y_labels,
            subplots=(2, 1),
            sub_title=sub_title,
            p_all_list=p_all_list,
        )

    def _visualization(self):
        super()._visualization()
        base_ax_index = len(self.experiment_data.y_data.keys())
        p_all_list = self.options.p_all_list

        x_arr = self.experiment_data.x_data
        y_arr = None

        for i, _ in enumerate(x_arr):
            child_data = self.experiment_data.child_data(index=i)
            if y_arr is None:
                y_arr = child_data.x_data

            for j, tub in enumerate(child_data.y_data.items()):
                p_label, p_value = tub
                p_all_list[j].append(p_value)

        for i, new_p_arr in enumerate(p_all_list):
            ax_index = base_ax_index + i
            self.drawer.draw_color_map(
                x_arr,
                y_arr,
                np.array(new_p_arr).T,
                ax_index=ax_index,
                **self.options.pcolormesh_options,
            )

    def run_analysis(self):
        """Run analysis on experiment data."""
        self._pre_operation()

        super().run_analysis()


class SweepAmpWidthAnalysis(SweepDetuneAnalysis):
    def _pre_operation(self):
        """Prepare one more canvas axis."""
        sub_title = ["Oscillate Frequency"]
        y_labels = list(self.experiment_data.y_data.keys())
        p_all_list = []

        child_data = self.experiment_data.child_data(index=0)
        for p_label in child_data.y_data.keys():
            p_all_list.append([])
            y_labels.append(self.options.y_label)
            sub_title.append(f"Depth {p_label}")

        self.set_options(
            y_label=y_labels,
            subplots=(3, 1),
            sub_title=sub_title,
            p_all_list=p_all_list,
        )


class CheckFreqRabiWidthAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.x_label = "Width (ns)"
        options.y_label = "xxxx"
        options.result_parameters = [
            ParameterRepr(
                name="f01", repr="drive freq", unit="MHz", param_path="Qubit.drive_freq"
            ),
        ]
        return options

    def _prepare_subplot(self):
        """Prepare one more canvas axis."""
        y_labels = []
        sub_title = []

        for i, freq in enumerate(list(self.experiment_data.x_data)):
            child_data = self.experiment_data.child_data(index=i)
            for p_label in child_data.y_data.keys():
                y_labels.append(p_label)
                sub_title.append(f"Freq {freq} MHz")

        self.set_options(y_label=y_labels, sub_title=sub_title)

    def _visualization(self):
        pass_freq = self.experiment_data.metadata.process_meta.get("pass_freq")
        osc_freq = self.experiment_data.metadata.process_meta.get("osc_freq")
        if pass_freq:
            self.results.f01.value = pass_freq[osc_freq.index(max(osc_freq))]
            self._quality = BaseQuality.instantiate(QualityDescribe.perfect)
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)

        super()._visualization()
        x_arr = self.experiment_data.child_data(0).x_data
        for i in range(len(self.experiment_data._child_data)):
            child_data = self.experiment_data.child_data(i)
            for j, key in enumerate(list(child_data.y_data.keys())):
                self.drawer.draw_plot_point(
                    x_arr,
                    child_data.y_data.get(key),
                    ax_index=i * 2 + j,
                )
