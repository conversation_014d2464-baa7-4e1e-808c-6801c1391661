# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/12/10
# __author:       SS Fang

import json
import time
from typing import TYPE_CHECKING, Dict

import zmq
from loguru import logger

from pyqcat_visage.gui.chip_protect_ui import Ui_MainWindow
from pyqcat_visage.gui.widgets.title_window import TitleWindow
from pyqcat_visage.protocol import PROXY_PUB_PORT

if TYPE_CHECKING:
    from pyqcat_visage.gui.main_window import VisageGUI


class ChipProtectWindow(TitleWindow):
    def __init__(self, gui: "VisageGUI", parent=None):
        super().__init__(parent)
        self._ui = Ui_MainWindow()
        self._ui.setupUi(self)
        self.gui = gui

        self._username = ""
        self._topic = ""
        self._point_label = ""
        self._operation = "chip_protect"

        self._first_set = True
        self._protect_modes = ["Start", "End"]

        self._context = zmq.Context.instance()
        self._pub_url = ""
        self._pub_client = None

    @property
    def ui(self):
        return self._ui

    def _create_pub_sock(self, pub_url: str):
        sock: zmq.Socket = self._context.socket(zmq.PUB)
        sock.connect(pub_url)
        time.sleep(0.005)
        logger.info(f"Create publish {pub_url} sock complete.")
        self._pub_url = pub_url
        self._pub_client = sock

    def _pub_message(self, topic: str, operation: str, message: Dict):
        topic_bytes = topic.encode(encoding="utf-8")
        operation_bytes = operation.encode(encoding="utf-8")
        body_bytes = json.dumps(message).encode(encoding="utf-8")

        msg_parts = [topic_bytes, operation_bytes, body_bytes]
        self._pub_client.send_multipart(msg_parts)
        logger.info(
            f"Publish {self._pub_url} topic {topic} operation {operation}, message: {message}"
        )

    def _initial(self):
        sample = self.gui.backend.config.system.sample
        env_name = self.gui.backend.config.system.env_name
        point_label = self.gui.backend.config.system.point_label
        invoker_addr = self.gui.backend.config.system.invoker_addr
        username = self.gui.backend.username

        addr_list = invoker_addr.rsplit(":", 1)
        # pub_port = int(addr_list[1]) + 2
        pub_url = f"{addr_list[0].replace('http', 'tcp')}:{PROXY_PUB_PORT}"
        if pub_url != self._pub_url or self._pub_client is None:
            self._create_pub_sock(pub_url)

        self._username = username
        self._topic = f"{sample}_|_{env_name}"
        self._point_label = point_label

        if self._first_set is True:
            self.ui.ProtectModeBox.addItems(self._protect_modes)
            self.ui.ProtectModeBox.setCurrentText("Start")
            self.ui.DurativeTimeEdit.setText("0")
            self._first_set = False

    def load_all_data(self):
        self._initial()

    def set_protect_params(self):
        protect_mode = self.ui.ProtectModeBox.currentText()
        protect_flag = True if protect_mode == "Start" else False
        if not self.ask_ok(
            f"Are you sure to <strong style='color:red'>Set</strong> "
            f"Chip Protect Mode <strong style='color:red'>{protect_mode}</strong> ?",
            "ChipProtect Message",
        ):
            return

        d_time_str = self.ui.DurativeTimeEdit.text()
        err_info = ""
        try:
            d_time = round(float(d_time_str))
            if protect_flag is True and d_time <= 0:
                err_info += f"Protect Mode `{protect_mode}`, Durative Time must set larger than 0!"
                err_info += f"But you set Durative Time {d_time_str} \n"
        except Exception as err:
            err_info += f"Set Durative Time {d_time_str} error: {err} \n"
            d_time = 0

        protect_params = {
            "ProtectFlag": protect_flag,
            "DurativeTime": d_time,
            "LastTime": int(time.time() * 1e3)
        }
        # logger.warning(f"User {self._username} set protect_params: {protect_params}")
        if err_info:
            logger.error(f"Chip Protect information set error: \n{err_info}")
        else:
            self._pub_message(self._topic, self._operation, protect_params)
