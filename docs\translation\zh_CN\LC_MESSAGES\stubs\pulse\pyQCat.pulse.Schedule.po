# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/pulse/pyQCat.pulse.Schedule.rst:2
msgid "pyQCat.pulse.Schedule"
msgstr ""

#: of pyQCat.pulse.schedule.Schedule:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.pulse.schedule.Schedule:1
msgid "Pulse schedule plotter."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule:4
msgid "Notes"
msgstr ""

#: of pyQCat.pulse.schedule.Schedule:5
msgid ""
"If AWG provides voltage, all z pulse was added a prepare delay. Since "
"every Z pulse has the same delay, it can be removed in schedules. Due to "
"the pulse width of the readout waveform is too long, only select a part "
"of the readout waveform to display."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.__init__:1
#: pyQCat.pulse.schedule.Schedule.append:1:<autosummary>:1
msgid "Initialize new Schedule plotter."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.__init__
#: pyQCat.pulse.schedule.Schedule.append pyQCat.pulse.schedule.Schedule.plot
msgid "Parameters"
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.__init__:4
msgid "The name of the plotter."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.__init__:7
msgid "The storage object for the plotter."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.__init__:10
msgid "The fixed delay which provided from AWG."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.__init__:13
msgid "The truncated pulse length of the readout work point."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.__init__:16
msgid "Flag to show real pulse or designed pulse."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Schedule.rst:13
msgid "Methods"
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.append:1:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.pulse.Schedule.__init__>`\\ \\(name\\, "
"save\\_file\\, fixed\\_delay\\, ...\\)"
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.append:1:<autosummary>:1
msgid ":py:obj:`append <pyQCat.pulse.Schedule.append>`\\ \\(pulse\\)"
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.append:1
#: pyQCat.pulse.schedule.Schedule.append:1:<autosummary>:1
msgid "Add pulse to `Schedule` object."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.append:1:<autosummary>:1
msgid ""
":py:obj:`plot <pyQCat.pulse.Schedule.plot>`\\ \\(\\[use\\_envelop\\, "
"is\\_save\\, schedule\\_index\\]\\)"
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.append:1:<autosummary>:1
#: pyQCat.pulse.schedule.Schedule.plot:1
msgid "Plot all qubits and couplers pulse schedules."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.append:4
msgid "The `PulseComponent` object."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.plot:4
msgid "Flag to use envelop or sequence to display."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.plot:7
msgid "Flag to save pic."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule.plot:10
msgid "The index of the schedules."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._add_qubit:1
msgid "add qubits."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._validate_params:1
msgid "Validate parameters."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._get_xy_pulse:1
msgid "Generate XY pulse."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._get_z_pulse:1
msgid "Generate Z pulse."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._split_axs:1
msgid "Split X/Y axis."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._plot_qubit:1
msgid "layout Y axis."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._plot_schedule:1
msgid "Draw pulse schedules."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._plot_pulse:1
msgid "Plotting all kinds of pulses."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._plot_xy:1
msgid "Plot xy pulse schedules."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._plot_z:1
msgid "Plot z pulse schedules."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._plot_measure:1
msgid "Plot readout pulse schedules."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._offset_pluse:1
msgid "Filled the pulse if has delay."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._offset_pluse
msgid "Return type"
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._offset_pluse:4
msgid ":py:class:`~numpy.ndarray`"
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._add_pulse_annotation:1
msgid "Add some simple annotations."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._get_z_pulse_start_index:1
msgid ""
"If AWG provides voltage, all z pulse was added a prepare delay. Since "
"every Z pulse has the same delay, it can be removed in schedules."
msgstr ""

#: of pyQCat.pulse.schedule.Schedule._get_z_pulse_end_index:1
msgid ""
"Since the pulse width of the readout waveform is too long, only select a "
"part of the readout waveform to display."
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.pulse.Schedule.__init__>`\\ "
#~ "\\(name\\, save\\_file\\)"
#~ msgstr ""

#~ msgid ":py:obj:`append <pyQCat.pulse.Schedule.append>`\\ \\(pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot <pyQCat.pulse.Schedule.plot>`\\ "
#~ "\\(\\[use\\_envelop\\, is\\_save\\, schedule\\_index\\]\\)"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.pulse.Schedule.__init__>`\\ "
#~ "\\(name\\, save\\_file\\)"
#~ msgstr ""

#~ msgid ":obj:`append <pyQCat.pulse.Schedule.append>`\\ \\(pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot <pyQCat.pulse.Schedule.plot>`\\ "
#~ "\\(\\[use\\_envelop\\, is\\_save\\, schedule\\_index\\]\\)"
#~ msgstr ""

