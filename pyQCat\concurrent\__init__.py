# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/10
# __author:       <PERSON>

"""
Concurrent Calculate resources and Parallel Experiments.
"""
import atexit
import signal
import sys

from pyQCat.concurrent.calculate_resource import ConcurrentCR
from pyQCat.concurrent.concurrent import cleanup_resources, start_merge_service
from pyQCat.concurrent.merge import CutExpMsg, MergeClient, MPTask, ParallelExpMsg


def keyboard_interrupt_handler(signal, frame):
    """
    This function will be called upon receiving the SIGINT signal,
    equivalent to handling KeyboardInterrupt.
    """
    cleanup_resources(close_transfer=True)
    sys.exit(0)


atexit.register(cleanup_resources)
signal.signal(signal.SIGTERM, keyboard_interrupt_handler)

# signal.signal(signal.SIGINT, keyboard_interrupt_handler)


__all__ = [
    "MergeClient",
    "ConcurrentCR",
    "start_merge_service",
    "ParallelExpMsg",
    "MPTask",
    "CutExpMsg",
]
