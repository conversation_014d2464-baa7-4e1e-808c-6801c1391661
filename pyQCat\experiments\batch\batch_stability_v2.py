# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/05/27
# __author:       <PERSON><PERSON><PERSON>

import os
import re
import time
from datetime import datetime
from uuid import uuid4

import matplotlib
import numpy as np

matplotlib.use("Agg")
from pathlib import Path

import matplotlib.dates as mdates
import matplotlib.pyplot as plt

from pyQCat.concurrent.batch_heart import AnalysisStruct, RecordMode
from pyQCat.experiments.batch_experiment import (
    BatchExperiment,
    S3DataType,
    check_and_start_merge_service,
)
from pyQCat.log import pyqlog
from pyQCat.qubit import NAME_PATTERN
from pyQCat.structures import Optional, Union


class BatchStabilityV2(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()

        options.loops = 3
        options.interval = 10  # Unit: second
        options.filter_params = {}
        options.y_limit_params = {}
        options.is_statistics = False
        options.record_length_limit = 100
        options.save_png_to_local = False

        return options

    @classmethod
    def _default_analysis_options(cls):
        options = super()._default_analysis_options()
        options.interval_of_analysis = 1
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.is_finished = False
        options.group_list = None
        options.history_limit = None
        options.format_data = {}
        options.async_tasks = []
        options.time_tag = None
        return options

    def _batch_up(self):
        super()._batch_up()

        self.record_thread.metadata.update(
            dict(
                interval=self.experiment_options.interval,
                units=self.experiment_options.physical_units,
                loops=self.experiment_options.loops,
            )
        )

        if not self.experiment_options.flows:
            self.experiment_options.flows = self.params_manager.flows

        self.experiment_options.exp_retry = 0
        pyqlog.info("`exp_retry` (an experiment option) is forced to 0.")
        self.experiment_options.record_batch = True
        pyqlog.info("`record_batch` (an experiment option) is forced to True.")
        self.experiment_options.quality_filter = False
        pyqlog.info("`quality_filter` (an experiment option) is forced to False.")

        bit = self.experiment_options.physical_units[0]
        if re.match(NAME_PATTERN.qubit, bit):
            group_list = list(
                self.parallel_allocator_for_qc(
                    self.experiment_options.physical_units,
                    mode="blame9",
                    intermediate_freq_allocate=False
                ).values()
            )
        elif re.match(NAME_PATTERN.qubit_pair, bit):
            group_list = list(
                self.parallel_allocator_for_cgc(
                    self.experiment_options.physical_units,
                    mode="horse"
                ).values()
            )
        else:
            group_list = [self.experiment_options.physical_units]
        self.run_options.group_list = group_list

        # extend record length limit
        record_length_limit = self.experiment_options.record_length_limit
        self.run_options.history_limit = record_length_limit
        self.experiment_options.record_length_limit = (
            record_length_limit * len(group_list) * len(self.experiment_options.flows)
        )

        # init filter params
        filter_params = self.experiment_options.filter_params
        format_data = self.run_options.format_data
        physical_units = self.experiment_options.physical_units
        flows = self.experiment_options.flows
        if filter_params:
            collect_exps = list(filter_params.keys())
        else:
            collect_exps = flows
        for exp_name in collect_exps:
            if exp_name not in format_data:
                format_data[exp_name] = {}

            for unit in physical_units:
                if unit not in format_data[exp_name]:
                    format_data[exp_name][unit] = dict(x_data=None, record=[])

        check_and_start_merge_service(self.backend.context_manager.config)

    def check_refresh_backend(self):
        super().check_refresh_backend()

        interval = int(self.record_thread.metadata.interval)
        if interval != self.experiment_options.interval:
            self.experiment_options.interval = interval

        physical_units = self.record_thread.metadata.units
        if physical_units and set(physical_units) != set(
            self.experiment_options.physical_units
        ):
            self.experiment_options.physical_units = physical_units
            self._batch_up()

    def _run_batch(self):
        loops = self.experiment_options.loops
        interval = self.experiment_options.interval
        # In this batch experiment, `self.experiment_options.flows` is a one-dimensional list.
        flows = self.experiment_options.flows

        # `path_of_temp_fig_folder` is an absolute path.
        path_of_temp_fig_folder = None
        if self.experiment_options.save_png_to_local is True:
            path_of_temp_fig_folder = Path(
                Path(self.run_options.record_path).parent, "Picture"
            )
            os.makedirs(path_of_temp_fig_folder, exist_ok=True)

        if self.analysis_options.interval_of_analysis == -1:
            pyqlog.info(
                f"The value of `interval_of_analysis` (an analysis option) is "
                f"{self.analysis_options.interval_of_analysis}, "
                f"so Monster will **not** do phase analysis. "
                f"For more information, refer to https://document.qpanda.cn/docs/R13j8MzM1OTjD8k5#anchor-oYtJ"
            )
            should_do_phase_analysis: bool = False
        else:
            should_do_phase_analysis: bool = True

        self.run_options.begin_datetime = datetime.now()
        history_limit = self.run_options.history_limit

        try:
            for i in range(loops):
                while True:
                    self._check_async_task()
                    if self._check_time_interval():
                        break
                    time.sleep(1)
                if (i + 1) % history_limit == 0:
                    self.record_meta.execute_meta.sync_history_results()
                    pyqlog.info("Sync history results ...")
                for j, group in enumerate(self.run_options.group_list):
                    self._run_flow(
                        flows=flows,
                        physical_units=group,
                        name=f"Group-{j + 1} Stability ({i + 1}/{loops})",
                    )
                self._save_data_to_json(self.run_options.format_data, "results.json")
                if (
                    should_do_phase_analysis
                    and (i % self.analysis_options.interval_of_analysis == 0)
                ) or i == loops - 1:
                    async_task = AnalysisStruct(
                        id=str(uuid4()),
                        function=stability_analysis_from_process_pool,
                        kwargs=dict(
                            result=self.run_options.format_data,
                            y_limit_params=self.experiment_options.y_limit_params,
                            save_path=path_of_temp_fig_folder,
                            file=self.file,
                        ),
                    )
                    self.run_options.async_tasks.append(async_task)
                    self.record_thread.add_message((RecordMode.BA, async_task))
                    self.record_thread.add_message((RecordMode.RF, ""))
                pyqlog.info(
                    f"{i + 1} loop(s) finished, {loops - (i + 1)} loop(s) left, Waiting for {interval} second(s)..."
                )
                self.run_options.time_tag = datetime.now()
                # time.sleep(interval)
        except Exception:
            import traceback

            pyqlog.warning("An exceptions is thrown when running experiments.")
            pyqlog.error(traceback.format_exc())
            pyqlog.info(
                "The results of the batch experiment are saved by Monster in real time. "
                "Monster will analyse these saved experiment results. "
            )
        else:
            pyqlog.info("Monster will analyse the experiment results. ")
        self.run_options.is_finished = True
        self.run_options.end_datetime = datetime.now()

    def _check_time_interval(self):
        time_tag = self.run_options.time_tag
        interval = self.experiment_options.interval

        if (
            time_tag
            and interval
            and (datetime.now() - time_tag).total_seconds() < interval
        ):
            return False

        return True

    def _record_experiment(self, exp_name, exp, physical_units, err=None):
        record_data = super()._record_experiment(exp_name, exp, physical_units, err)

        record_length_limit = self.experiment_options.record_length_limit
        release_resource = (
            record_length_limit and len(self.batch_records) == record_length_limit
        )
        filter_params = self.experiment_options.filter_params
        format_data = self.run_options.format_data

        if filter_params and exp_name not in filter_params:
            return record_data

        plot_params = filter_params.get(exp_name)
        timestamp = record_data["timestamp"]

        for unit, analysis_data_of_unit in record_data.get("analysis_data", {}).items():
            if release_resource is True:
                format_data[exp_name][unit]["record"].pop(0)

            result = analysis_data_of_unit.get("result")
            experiment_data = analysis_data_of_unit.get("experiment_data")

            if not result and not experiment_data:
                pyqlog.warning(
                    f"There is no analysis data of {unit} to plot "
                    f"in the current record (`{exp_name}`). "
                )
                continue

            record = dict(timestamp=timestamp, result={})

            if exp_name == "SingleShot":
                for name_of_result in ["fidelity0", "fidelity1"]:
                    if plot_params and name_of_result not in plot_params:
                        continue
                    record["result"][name_of_result] = result["dcm"]["fidelity"][
                        int(name_of_result[-1])
                    ]
            else:
                if experiment_data:
                    x_data = experiment_data.pop("x_data", None)
                    if not format_data[exp_name][unit]["x_data"]:
                        format_data[exp_name][unit]["x_data"] = x_data
                    for name_of_data, value_of_data in experiment_data.items():
                        if plot_params and name_of_data not in plot_params:
                            continue
                        record["result"][name_of_data] = value_of_data
                for name_of_result, value_of_result in result.items():
                    if plot_params and name_of_result not in plot_params:
                        continue
                    if isinstance(value_of_result, (int, float)) and not isinstance(
                        value_of_result, bool
                    ):
                        if np.isfinite(value_of_result):
                            record["result"][name_of_result] = value_of_result
                        else:
                            pyqlog.warning(
                                f"{value_of_result} (found in {exp_name} - {unit} - "
                                f"{name_of_result}) is not finite, so this piece of data will not be plotted. "
                            )

            format_data[exp_name][unit]["record"].append(record)

        return record_data

    def _check_async_task(self):
        async_tasks = self.run_options.async_tasks
        if async_tasks:
            async_task = async_tasks[0]
            result = self.record_thread.acquire_batch_analysis_result(async_task)
            if result is None:
                return
            else:
                for exp_name, data_of_exp in result.items():
                    for unit, data_of_unit in data_of_exp.items():
                        self.put_png_to_s3(data_of_unit, [exp_name, unit])
                self.sync_batch_information()
                self.run_options.async_tasks.pop(0)
                pyqlog.info(
                    f"finish one async task, remaining async tasks total {len(self.run_options.async_tasks)}"
                )

    def _run_analysis(self):
        while self.run_options.async_tasks:
            self._check_async_task()
            time.sleep(1)

    def _record_to_json(self):
        pass


def stability_analysis_from_process_pool(kwargs: dict):
    return stability_analysis(**kwargs)


def stability_analysis(
    result: dict,
    y_limit_params: Optional[dict] = None,
    save_path: Union[Path, str] = "",
    file=None,
):
    y_limit_params = y_limit_params or {}
    picture_id_map = {}

    for exp_name, data_of_exp in result.items():
        y_limit_map = y_limit_params.get(exp_name, {})
        picture_id_map[exp_name] = {}

        for unit, data_of_unit in data_of_exp.items():
            x_data = data_of_unit.get("x_data")
            record = data_of_unit.get("record")

            if not record:
                continue

            title = f"{exp_name} ({unit})"

            time_list = [once["timestamp"] for once in record]
            if isinstance(time_list[0], str):
                time_list = [
                    datetime.strptime(t, "%Y-%m-%d %H:%M:%S.%f") for t in time_list
                ]
            time_list_num = [
                mdates.date2num(t) for t in time_list
            ]  # 转换为matplotlib数字
            result_keys = list(record[0]["result"].keys())
            num_of_results = len(result_keys)
            golden_ratio = 2
            fig_width = 8
            fig_height = fig_width / golden_ratio * num_of_results
            fig, axes = plt.subplots(
                num_of_results, 1, figsize=(fig_width, fig_height), sharex=True
            )
            axes_flat = axes.ravel() if hasattr(axes, "ravel") else [axes]
            fig.suptitle(title, fontsize=13, wrap=True)

            for i, key in enumerate(result_keys):
                y_min, y_max = y_limit_map.get(key, (None, None))
                axis = axes_flat[i]
                values = [once["result"][key] for once in record]
                if isinstance(values[0], list):
                    xl, yl = len(time_list), len(values[0])
                    mesh_x, mesh_y = np.meshgrid(
                        time_list_num, x_data if x_data else list(range(yl))
                    )
                    z_data = np.array(values).T
                    map_z = np.reshape(z_data, (yl, xl), order="F")
                    mesh = axis.pcolormesh(
                        mesh_x, mesh_y, map_z, cmap="viridis", shading="nearest"
                    )
                    axis.xaxis_date()
                    axis.xaxis.set_major_formatter(
                        mdates.DateFormatter("%Y-%m-%d %H:%M:%S")
                    )
                    fig.colorbar(mesh, ax=axis)
                else:
                    if y_max is not None:
                        axis.set_ylim(ymax=y_max)
                    if y_min is not None:
                        axis.set_ylim(ymin=y_min)
                    axis.plot(time_list_num, values, label=key, marker="o")
                    axis.grid(True)
                axis.set_ylabel(key)
                axis.xaxis.set_major_formatter(
                    mdates.DateFormatter("%Y-%m-%d %H:%M:%S")
                )
                axis.xaxis.set_major_locator(mdates.AutoDateLocator())
                plt.setp(axis.get_xticklabels(), rotation=45)

            plt.xlabel("timestamps")
            plt.tight_layout()

            if save_path:
                filename = f"{exp_name}-{unit}-stability.png"
                path_of_fig = str(Path(save_path, filename))
                if os.path.exists(path_of_fig):
                    os.remove(path_of_fig)
                plt.savefig(f"{path_of_fig}")

            if file:
                png_id = file.put_data_to_s3(S3DataType.png, fig)
                picture_id_map[exp_name][unit] = png_id
            plt.close()

    return picture_id_map
