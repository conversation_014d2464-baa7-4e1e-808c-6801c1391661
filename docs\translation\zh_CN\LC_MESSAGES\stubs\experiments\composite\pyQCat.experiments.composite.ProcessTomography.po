# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:2
msgid "pyQCat.experiments.composite.ProcessTomography"
msgstr ""

#: of pyQCat.experiments.composite.process_tomography.ProcessTomography:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.experiments.composite.ProcessTomography.__init__>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.composite.ProcessTomography.component_experiment>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.composite.ProcessTomography.from_experiment_context>`\\"
" \\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.composite.ProcessTomography.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.composite.ProcessTomography.options_table>`\\ "
"\\(\\[mode\\, detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid ""
":py:obj:`run <pyQCat.experiments.composite.ProcessTomography.run>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite.process_tomography.ProcessTomography.run:1
msgid "Run composite experiment."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.composite.ProcessTomography.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.composite.ProcessTomography.set_experiment_options>`\\"
" \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.composite.ProcessTomography.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.composite.ProcessTomography.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ProcessTomography.rst:32
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography:1:<autosummary>:1
msgid ""
":py:obj:`analysis "
"<pyQCat.experiments.composite.ProcessTomography.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.composite.ProcessTomography.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.composite.ProcessTomography.child_experiment>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.composite.ProcessTomography.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.composite.ProcessTomography.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:1
msgid "Default experiment options for AC spectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:12
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:4
msgid ""
"init_fringe (float): The init value of fringe. delays (Union[List, "
"np.ndarray]): Delay time scanned when performing Ramsey"
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:6
msgid "experiments."
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:7
msgid ""
"z_amp_list (Union[List, np.ndarray]): Z line amplitude (flux pulse) sweep"
" list. freq_bound (Optional[float], optional): Experiment will be stopped"
" when qubit"
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:9
msgid "frequency delta value lower than this value. Defaults to 800MHz."
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:11
msgid ""
"osc_freq_limit (Optional[float], optional): [description]. Defaults to "
"2.5."
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_analysis_options
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_run_options
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_analysis_options:4
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:14
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_run_options:11
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_analysis_options:1
msgid "Default analysis options for AC spectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_run_options:1
msgid "Set run experiment operate options."
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_run_options:9
msgid "Options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_run_options:4
msgid ""
"QL: Qubit object, which bit name is ql_name. QH: Qubit object, which bit "
"name is qh_name. parking_qubits (List): List of BaseQubit,"
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_run_options:7
msgid "when name in parking_bits."
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._check_options:1
msgid "Check Options."
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._metadata:1
msgid "Return experiment metadata for ExperimentData."
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#~ msgid ":py:class:`~typing.Type`"
#~ msgstr ""

