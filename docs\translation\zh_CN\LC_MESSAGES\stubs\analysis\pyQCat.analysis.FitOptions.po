# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.FitOptions.rst:2
msgid "pyQCat.analysis.FitOptions"
msgstr ""

#: of pyQCat.analysis.specification.FitOptions:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.analysis.specification.FitOptions:1
msgid "Collection of fitting options."
msgstr ""

#: of pyQCat.analysis.specification.FitOptions:3
msgid ""
"This class is initialized with a list of parameter names used in the fit "
"model and corresponding default values provided by users."
msgstr ""

#: of pyQCat.analysis.specification.FitOptions:6
msgid "This class is hashable, and generates fitter keyword arguments."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitOptions.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitOptions.rst:23:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.FitOptions.__init__>`\\ "
"\\(parameters\\[\\, default\\_p0\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitOptions.rst:23:<autosummary>:1
msgid ""
":py:obj:`add_extra_options "
"<pyQCat.analysis.FitOptions.add_extra_options>`\\ \\(\\*\\*kwargs\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitOptions.rst:23:<autosummary>:1
#: of pyQCat.analysis.specification.FitOptions.add_extra_options:1
msgid "Add more fitter options."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitOptions.rst:23:<autosummary>:1
msgid ":py:obj:`copy <pyQCat.analysis.FitOptions.copy>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitOptions.rst:23:<autosummary>:1
#: of pyQCat.analysis.specification.FitOptions.copy:1
msgid "Create copy of this option."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitOptions.rst:25
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.analysis.specification.FitOptions.add_extra_options:1:<autosummary>:1
msgid ":py:obj:`bounds <pyQCat.analysis.FitOptions.bounds>`\\"
msgstr ""

#: of pyQCat.analysis.FitOptions.bounds:1
#: pyQCat.analysis.specification.FitOptions.add_extra_options:1:<autosummary>:1
msgid "Return bounds dictionary."
msgstr ""

#: of
#: pyQCat.analysis.specification.FitOptions.add_extra_options:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.FitOptions.options>`\\"
msgstr ""

#: of pyQCat.analysis.FitOptions.options:1
#: pyQCat.analysis.specification.FitOptions.add_extra_options:1:<autosummary>:1
msgid "Generate keyword arguments of the curve fitter."
msgstr ""

#: of
#: pyQCat.analysis.specification.FitOptions.add_extra_options:1:<autosummary>:1
msgid ":py:obj:`p0 <pyQCat.analysis.FitOptions.p0>`\\"
msgstr ""

#: of pyQCat.analysis.FitOptions.p0:1
#: pyQCat.analysis.specification.FitOptions.add_extra_options:1:<autosummary>:1
msgid "Return initial guess dictionary."
msgstr ""

#: of pyQCat.analysis.FitOptions.bounds pyQCat.analysis.FitOptions.p0
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.FitOptions.p0:3
msgid ":py:class:`~pyQCat.analysis.specification.InitialGuesses`"
msgstr ""

#: of pyQCat.analysis.FitOptions.bounds:3
msgid ":py:class:`~pyQCat.analysis.specification.Boundaries`"
msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.analysis.FitOptions.__init__>`\\ "
#~ "\\(parameters\\[\\, default\\_p0\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`add_extra_options "
#~ "<pyQCat.analysis.FitOptions.add_extra_options>`\\ "
#~ "\\(\\*\\*kwargs\\)"
#~ msgstr ""

#~ msgid ":obj:`copy <pyQCat.analysis.FitOptions.copy>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`bounds <pyQCat.analysis.FitOptions.bounds>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.FitOptions.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`p0 <pyQCat.analysis.FitOptions.p0>`\\"
#~ msgstr ""

