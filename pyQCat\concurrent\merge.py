# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/10
# __author:       <PERSON>, <PERSON><PERSON><PERSON>

"""
Parallel Experiments.
"""

import asyncio
import traceback
from enum import Enum
from typing import ByteString, Dict, List, Optional

import zmq
from bson import ObjectId
from zmq.asyncio import Context, Poller, Socket

from pyQCat.concurrent.util import (
    CutExpMsg,
    MPTask,
    ParallelExpMsg,
    Response,
    ResponseCode,
    Singleton,
    c_error_log,
    c_log,
    from_cache_bytes,
    get_secrets_token,
    to_cache_bytes,
)
from pyQCat.concurrent.worker import CompileResult, CompileStatus
from pyQCat.types import SimulatorMode
from pyQCat.tools.app_data_collector import AppD<PERSON><PERSON>ollector

from ..data_transfer.state import CRASH_STATE, TransferTaskStatusEnum
from ..qm_protocol import CommonMessage
from .concurrent import ConcurrentCR
from .data_client import (
    DataClient,
    concurrent_merge_experiment,
    register_compile_result,
)
from .worker.experiment_allocator import (
    AllocationResult,
    AllocationWorkerStatus,
    async_allocation_experiment,
    concurrent_allocation_experiment,
)

MERGE_ADDR = "inproc://mergeservice"
UNKNOWN_REQUIRE = b"unknown_require"
RETRY_TIME = 4
LOG_TITLE = "MergeService"


async def async_concurrent_merge_experiment(*arg, **kwargs):
    return concurrent_merge_experiment(*arg, **kwargs)


class MergeOp(Enum):
    PARALLEL_REGISTER = b"10"
    PARALLEL_END = b"11"
    PARALLEL_MERGE = b"20"
    PARALLEL_CUT = b"30"
    PARALLEL_CUTS = b"35"
    QUERY_TASK_ID = b"40"


class MergedStatus(Enum):
    INIT = 1
    WAIT_MERGE = 2
    MERGE_FAILED = 3
    WAIT_CHIMERA = 4
    WAIT_EXECUTE = 5
    CHIMERA_FAILED = 6
    EXECUTE_FAILED = 10
    EXECUTE_SUC = 11


class AllocationStatus(Enum):
    INIT = 1
    WAIT_GROUP = 2
    GROUP_SUC = 3
    GROUP_FAIL = 4


class GExpCache:
    """
    Parallel child group experiment.

    This experiment is the smallest set of parallel tasks, which will be merged into an independent Experiment File
    and sent to the driver layer. The tasks here generally consider issues such as quantum bit frequency conflicts
    and hardware limitations.
    """

    __slots__ = [
        "_token",
        "_exp_name",
        "_region",
        "_group",
        "_physical_units_collection",
        "_require_wait_list",
        "_finish_count",
        "_status",
        "_chimera_data",
        "_require_id",
        "_task_id",
        "_retry_time",
        "_merge_result",
        "_simulate",
        "_group_size",
        "_count_identity",
        "_future",
    ]

    def __init__(
        self,
        token: str,
        exp_name: str,
        region: str,
        group: List[MPTask],
        require_wait_list: List[MPTask],
        simulate: SimulatorMode = SimulatorMode.CLOSE,
    ):
        """Create a new parallel child group experiment task.

        Args:
            token (str): Unique identifier for top-level parallel experiments.
            exp_name (str): The name of the sub experiment collection currently being merged.
            region (str): Parallel regions, a parallel experiment may be split into multiple
                parallel regions for asynchronous execution.
            group (List[MPTask]): Registration tasks in this parallel group.
            require_wait_list (List[MPTask]): Waiting queue for reply to client execution results.
            simulate (bool, optional): Simulator switch. Defaults to False.
        """
        self._token = token
        self._exp_name = exp_name
        self._region = region
        self._group: List[MPTask] = group
        self._physical_units_collection = [task.physical_units for task in group]
        self._require_wait_list: List[MPTask] = require_wait_list
        self._finish_count = 0
        self._status = MergedStatus.INIT
        self._chimera_data = None
        self._require_id = ""
        self._task_id = ""
        self._retry_time = 0
        self._merge_result = None
        self._simulate = simulate
        self._group_size = len(group)
        self._count_identity = group[0].count
        self._future = None

    def __str__(self):
        return f"Parallel-Child-Group-Exp({self._exp_name}) | {self._region} | Count-Identity({self._count_identity})"

    @property
    def state(self) -> MergedStatus:
        """Get the current parallel task status.

        Returns:
            MergedStatus: Flow status dedicated to GExpCache.
        """
        return self._status

    @state.setter
    def state(self, new_state: MergedStatus):
        """Set the current parallel task status.

        Args:
            new_state (MergedStatus): Flow status dedicated to GExpCache.
        """
        self._status = new_state

    @property
    def task_id(self) -> str:
        """Obtain the task ID after successful merging and registration.

        Returns:
            str: The unique identifier for a measurement and control task.
        """
        return self._task_id

    def has_finished(self) -> bool:
        """Determine whether the current task has ended. When the number of response messages
        equals the size of the group, the task ends.

        Returns:
            bool: True indicates end, False indicates not end.
        """
        return self._finish_count == self._group_size

    async def monitor(self, sock: Socket, shared_resource: CommonMessage):
        """Experimental monitoring, executing different operations based on the flow status.

        Args:
            sock (Socket): zmq.PAIR from `MergeService`.
            shared_resource (CommonMessage): Parallel experiment sharing resources.

        Notes:
            - MergedStatus.INIT: Start sub experiment merge, switch the status to `MergedStatus.WAIT_MERGE`;
            - MergedStatus.WAIT_MERGE:
                1. Clear group members to reduce resource consumption
                2. If the merge fail, switch the status to `MergedStatus.MERGE_FAILED`
                3. If the merge success, and use simulator, register task to obtain ID and switch the status
                    to `MergedStatus.WAIT_EXECUTE`.
                4. If the merge success, and not use simulator, register task to obtain ID and send scheduler
                    data to chimera and switch the status to `MergedStatus.WAIT_CHIMERA`.
            - MergedStatus.MERGE_FAILED: Notify the client of merge error and end this task.
            - MergedStatus.WAIT_CHIMERA:
                1. Check if the scheduling information has been successfully sent
                2. If sent successfully, switch the status to `MergedStatus.WAIT_EXECUTE`
                3. If sent fail, there are three chances to retry, after consecutive failures, switch the
                    status to `MergedStatus.CHIMERA_FAILED`
            - MergedStatus.CHIMERA_FAILED: Notify the client of chimera error and end this task.
            - MergedStatus.WAIT_EXECUTE:
                1. Query task status, if it is `ExperimentDocStatus.finished` or `ExperimentDocStatus.simulator`,
                    switch the status to `MergedStatus.EXECUTE_SUC`.
                2. if it is `ExperimentDocStatus.aio_error`, take three retries and resend the task, after
                    consecutive failures, switch the status to `MergedStatus.CHIMERA_FAILED`
                3. if it is `ExperimentDocStatus.chimera_validate_error`, switch the status to
                    `MergedStatus.EXECUTE_FAILED`
            - MergedStatus.EXECUTE_FAILED: Notify the client of chimera validate error and end this task.
        """
        if self._region == "fail_tasks":
            for task in self._require_wait_list:
                delete_tasks = []
                if task.physical_units in self._physical_units_collection and task.require_id:
                    result = Response(
                        code=ResponseCode.FAILED,
                        msg="allocation error",
                    )
                    await sock.send_multipart([task.require_id, to_cache_bytes(result)])
                    self._finish_count += 1
                    delete_tasks.append(task)
                for t in delete_tasks:
                    self._require_wait_list.remove(t)
            return

        if self.state in [
            MergedStatus.EXECUTE_SUC,
            MergedStatus.EXECUTE_FAILED,
            MergedStatus.MERGE_FAILED,
            MergedStatus.CHIMERA_FAILED,
        ]:
            for task in self._require_wait_list:
                delete_tasks = []
                if task.physical_units in self._physical_units_collection and task.require_id:
                    if self.state == MergedStatus.EXECUTE_SUC:
                        result = Response(code=ResponseCode.SUCCESS, data=(self._require_id, self._task_id))
                    elif self.state == MergedStatus.EXECUTE_FAILED:
                        result = Response(
                            ResponseCode.FAILED,
                            msg=f"Execute Error | {self.state}",
                        )
                    elif self.state == MergedStatus.CHIMERA_FAILED:
                        result = Response(
                            code=ResponseCode.FAILED,
                            msg=f"chimera error {self.state}",
                        )
                    else:
                        result = Response(
                            code=ResponseCode.FAILED,
                            msg="parallel experiment merge error, maybe resource conflict",
                        )

                    await sock.send_multipart([task.require_id, to_cache_bytes(result)])
                    self._finish_count += 1
                    delete_tasks.append(task)

                for t in delete_tasks:
                    self._require_wait_list.remove(t)

            return

        if self.state == MergedStatus.INIT:
            c_log(f"{self} | start parallel merge process ...")
            self._future = asyncio.create_task(
                ConcurrentCR().run_concurrent_job(
                    concurrent_merge_experiment,
                    [task.merge_struct for task in self._group if task.merge_struct],
                    shared_resource,
                )
            )
            self.state = MergedStatus.WAIT_MERGE
            return

        if self.state == MergedStatus.WAIT_MERGE:
            if self._future and self._future.done():
                try:
                    # bugfix: Process pool exception can cause merge service crash
                    merge_result: CompileResult = self._future.result()
                except Exception as e:
                    merge_result = CompileResult()
                    merge_result.status = CompileStatus.ERROR
                    merge_result.message = str(e)

                # clear merge part first
                self._group.clear()

                # save data to mongo service.
                if merge_result.status == CompileStatus.ERROR:
                    await AppDataCollector().parallel_merge_fail(self, merge_result.message)
                    c_error_log(
                        f"{self} merge failed !",
                        error=merge_result.message,
                    )
                    self.state = MergedStatus.MERGE_FAILED
                    return
                elif self._simulate == SimulatorMode.EXP:
                    self._task_id = merge_result.require_id
                    self._require_id = merge_result.require_id
                    self.state = MergedStatus.WAIT_EXECUTE
                    c_log(f"{self} simulator merge success!")
                    return
                else:
                    state, require_id, task_id = await register_compile_result(merge_result, self._simulate.value)
                    self._require_id = require_id
                    self._task_id = task_id
                    if state == TransferTaskStatusEnum.FAIL:
                        self.state = MergedStatus.EXECUTE_FAILED
                        c_error_log(f"{self} | execute file RID({require_id}) | {state}")
                        return
                    else:
                        self.state = MergedStatus.WAIT_EXECUTE
                    c_log(f"{self} | merge success RID({self._task_id})-TID({task_id})")

            return

        if self.state == MergedStatus.WAIT_EXECUTE:
            if self._simulate == SimulatorMode.EXP:
                self.state = MergedStatus.EXECUTE_SUC
            else:
                await asyncio.sleep(1)
                transfer_status = DataClient().query_task_state(self._require_id)
                if transfer_status in CRASH_STATE:
                    self.state = MergedStatus.EXECUTE_FAILED
                    c_error_log(f"{self} | execute file RID({self._require_id}) | {transfer_status}")
                elif transfer_status == TransferTaskStatusEnum.SUC:
                    self.state = MergedStatus.EXECUTE_SUC
                else:
                    pass
        return


class CExpCache:
    """
    Parallel child experiment.

    This experiment is a parallel experiment composed of top experiments, in which physical units participate;
    The parallel CavityFreqSpectrum, a sub experiment of the refrigerator CavityTunable experiment, is a typical
    representative of this experiment, and of course, the parallel RabiScanAmp experiment is also the same.
    """

    __slots__ = [
        "_token",
        "_exp_name",
        "_count_identity",
        "_parallel_total",
        "_merge_part",
        "_require_wait_list",
        "_status",
        "_future",
        "_simulate",
        "_group_exp_list",
        "_finish_flag",
        "_allocation_result",
    ]

    def __init__(
        self,
        token: str,
        exp_name: str,
        count: int,
        parallel_total: int,
        allocation_result: AllocationResult,
        simulate: SimulatorMode = SimulatorMode.CLOSE,
    ) -> None:
        """Create a new parallel child experiment task.

        Args:
            token (str): Unique identifier for top-level parallel experiments.
            exp_name (str): The name of the sub experiment collection currently being allocated.
            count (int): The identity identification of the current experiment in the composite experiment.
            parallel_total (_type_): The current total number of sub experiments in parallel experiments
            simulate (bool, optional): is use simulator. Defaults to SimulatorMode.CLOSE.
        """
        self._token = token
        self._exp_name = exp_name
        self._count_identity = count
        self._parallel_total = parallel_total
        self._merge_part: List[MPTask] = []
        self._require_wait_list: List[MPTask] = []
        self._status = AllocationStatus.INIT
        self._future = None
        self._simulate = simulate
        self._group_exp_list = []
        self._finish_flag = False
        self._allocation_result = allocation_result

    def __str__(self):
        return f"Parallel-Child-Exp({self._exp_name} | Total({self._parallel_total}) | Count-Identity({self._count_identity})"

    @property
    def state(self) -> AllocationStatus:
        """Get the current parallel task status.

        Returns:
            AllocationStatus: Flow status dedicated to CExpCache.
        """
        return self._status

    @state.setter
    def state(self, new_state: AllocationStatus):
        """Set the current parallel task status.

        Args:
            new_state (AllocationStatus): Flow status dedicated to CExpCache.
        """
        self._status = new_state

    def check_need_allocation(self) -> bool:
        """Check if parallel grouping has started, and start when the total number of
        received message bodies equals the number of parallel packets.

        Returns:
            bool: True means it can start, False means it cannot.
        """
        if (
            len(self._merge_part) >= self._parallel_total
            and len(self._require_wait_list) >= self._parallel_total
            and self._parallel_total
        ):
            return True
        return False

    def add_merge_part(self, merge_part: MPTask):
        """Add an experiment to be merged

        Args:
            merge_part (MPTask): Merge data structures
        """
        self._merge_part.append(merge_part)

    def add_wait_require(self, require_msg: MPTask):
        """Add waiting signal for feedback.

        Args:
            require_msg (MPTask): Waiting for the return of the structure.
        """
        self._require_wait_list.append(require_msg)

    def cat_experiment(self, physical):
        """Crop the parallel number. When a unit of the composite experiment ends prematurely,
        the parallel number needs to be reduced, otherwise subsequent experiments cannot be
        parallelized.

        Args:
            physical (_type_): Identification of physical units relied upon in experiments.
        """
        if physical:
            self._parallel_total -= 1

    def has_finished(self):
        """Determine whether the current task has ended.

        Returns:
            bool: True indicates end, False indicates not end.
        """
        if self._parallel_total <= 0:
            return True

        if self.state == AllocationStatus.GROUP_FAIL:
            return True

        if self.state == AllocationStatus.GROUP_SUC:
            return self._finish_flag

        return False

    async def _start_group_exp_monitor(self, sock: Socket, shared_resource: CommonMessage):
        """Enable asynchronous monitoring process for all `GExpCache`.

        Args:
            sock (Socket): zmq.PAIR from `MergeService`.
            shared_resource (CommonMessage): Parallel experiment sharing resources.
        """
        length = len(self._group_exp_list)
        while True:
            finish_items = []
            for i in range(length):
                if i not in finish_items:
                    g_exp = self._group_exp_list[i]
                    await g_exp.monitor(sock, shared_resource)
                    if g_exp.has_finished() is True:
                        finish_items.append(i)

            if len(finish_items) == length:
                break

            await asyncio.sleep(0.3)
        self._finish_flag = True

    async def monitor(self, sock: Socket, shared_resource: CommonMessage, index: int):
        """Experimental monitoring, executing different operations based on the flow status.

        Args:
            sock (Socket): zmq.PAIR from `MergeService`.
            shared_resource (CommonMessage): Parallel experiment sharing resources.

        Notes:
            - AllocationStatus.INIT: Check if the parallel count is full, if so, start the parallel
                grouping process, switch the status to `AllocationStatus.WAIT_GROUP`
            - AllocationStatus.WAIT_GROUP:
                1. If the grouping fails, switch the status to `AllocationStatus.GROUP_FAIL`
                2. If the grouping success, create `GExpCache` and enable asynchronous monitoring
                    tasks, final switch the status to `AllocationStatus.GROUP_SUC`
            - AllocationStatus.GROUP_FAIL: Task completed.
            - AllocationStatus.GROUP_SUC: Wait until all GExpCache of the `_group_exp_list` are finished
        """
        if self.state == AllocationStatus.INIT:
            if self.check_need_allocation():
                c_log(f"{self} | start parallel allocation process ...")
                if shared_resource.allocation_options.get("allocation", False) is True:
                    if (self._allocation_result and index == 1) or self._allocation_result is None:
                        self._future = asyncio.create_task(
                            ConcurrentCR().run_concurrent_job(
                                concurrent_allocation_experiment,
                                self._merge_part,
                                shared_resource,
                            )
                        )
                    elif self._allocation_result.is_done:
                        self._future = asyncio.create_task(
                            async_allocation_experiment(
                                tasks=self._merge_part,
                                cache_group=self._allocation_result.group_map,
                                shared_resource=shared_resource,
                            )
                        )
                    else:
                        return
                else:
                    self._future = asyncio.create_task(async_allocation_experiment(self._merge_part))
                self.state = AllocationStatus.WAIT_GROUP
        elif self.state == AllocationStatus.WAIT_GROUP:
            if self._future and self._future.done():
                allocation_result: AllocationResult = self._future.result()

                # Synchronized cache
                if index == 1 and self._allocation_result:
                    self._allocation_result.sync_other(allocation_result)

                self._future = None

                # save data to mongo service.
                if allocation_result.status == AllocationWorkerStatus.ERROR:
                    c_error_log(
                        f"{self} | parallel group allocation fail | {allocation_result.msg}",
                    )
                    self.state = AllocationStatus.GROUP_FAIL
                else:
                    self._group_exp_list = [
                        self._to_g_exp(name, task_group) for name, task_group in allocation_result.group_map.items()
                    ]
                    self._future = asyncio.create_task(self._start_group_exp_monitor(sock, shared_resource))
                    self.state = AllocationStatus.GROUP_SUC
                    c_log(f"{self} | parallel group allocation success, group as follows:\n{allocation_result.msg}")

    def _to_g_exp(self, name: str, tasks: List[MPTask]):
        return GExpCache(
            self._token,
            self._exp_name,
            name,
            tasks,
            self._require_wait_list,
            self._simulate,
        )


class PExpCache:
    """
    Parallel experiment cache struct.

    Any experiment combined through `ParallelExperiment` will eventually form a `PExpCache`,
    which is used to manage a complex parallel experiment that may require many sub tasks to
    be completed.
    """

    __slots__ = [
        "_token",
        "_cache_merge",
        "_exp_msg",
        "_parallel_total",
        "_allocation_result",
    ]

    def __init__(self, token: str, exp_msg: ParallelExpMsg) -> None:
        """Create a new parallel experiment task.

        Args:
            token (str): Unique identifier for top-level parallel experiments.
            exp_msg (ParallelExpMsg): Parallel experimental information structure.
        """
        self._token: str = token
        self._cache_merge = {}
        self._exp_msg: ParallelExpMsg = exp_msg
        self._parallel_total = self._exp_msg.parallel_total or 0
        self._allocation_result: Optional[AllocationResult] = None

        if exp_msg.use_allocation_cache is True:
            self._allocation_result = AllocationResult()

    def __str__(self) -> str:
        return f"ParallelExp({self.exp_name} | {self.parallel_total})"

    @property
    def exp_name(self) -> str:
        """Parallel experiment name

        Returns:
            str: Parallel experiment name
        """
        return self._exp_msg.exp_name

    @property
    def simulate(self) -> SimulatorMode:
        """Simulator flag.

        Returns:
            SimulatorMode: Simulator mode.
        """
        return self._exp_msg.simulate

    @property
    def parallel_total(self) -> int:
        """The total number of units participating in parallel.

        Returns:
            int: The total number of units participating in parallel.
        """
        return self._parallel_total

    @parallel_total.setter
    def parallel_total(self, value):
        """Update the total number of units participating in parallel.

        Args:
            value (_type_): new total number of units participating in parallel.
        """
        self._parallel_total = value

    @property
    def child_exp(self) -> Dict:
        """A dictionary composed of `CExpCache`

        Returns:
            Dict: Child parallel experiment collection.
        """
        return self._cache_merge

    @property
    def shared_resource(self) -> CommonMessage:
        """Parallel experiment sharing resources.

        Returns:
            CommonMessage: _description_
        """
        return self._exp_msg.shared_resource

    def push_merge_task(self, mp_task: MPTask):
        """push an experiment task to be merged

        Args:
            merge_part (MPTask): Merge data structures
        """
        if mp_task.count in self.child_exp:
            self.child_exp[mp_task.count].add_merge_part(mp_task)
        else:
            self.child_exp[mp_task.count] = CExpCache(
                token=self._token,
                exp_name=mp_task.merge_struct.label or self.exp_name,
                count=mp_task.count,
                parallel_total=self.parallel_total,
                simulate=self.simulate,
                allocation_result=self._allocation_result,
            )
            self.child_exp[mp_task.count].add_merge_part(mp_task)

    def exp_run_end(self):
        """
        Parallel experiment execution completed.
        """
        c_log(f"{self} | finished ...")

    def cuts_experiment(self, cut_physical, count=None):
        """Remove sub experiment.

        Args:
            cut_physical (_type_, optional): Physical unit to be cut off. Defaults to None.
            count (_type_, optional): Count number to be cut off. Defaults to None.
        """
        if count:
            if count in self.child_exp:
                self.child_exp[count].cat_experiment(cut_physical)
                return

        if cut_physical:
            self.parallel_total -= 1
            for c_exp in self.child_exp.values():
                c_exp.cat_experiment(cut_physical)

        return


class MergeService:
    """
    Merge service.
    """

    def __init__(self) -> None:
        # cache parallel exp by init register. {"token": xxx}
        self._parallel_exp_cache = {}

        self._sock = None
        self.poll = None
        self.listen_router_map = {
            MergeOp.PARALLEL_REGISTER.value: self.parallel_register,
            MergeOp.PARALLEL_END.value: self.concurrent_exp_end,
            MergeOp.PARALLEL_MERGE.value: self.deal_merge_task,
            MergeOp.QUERY_TASK_ID.value: self.query_task_id,
            MergeOp.PARALLEL_CUTS.value: self.cuts_experiment,
            MergeOp.PARALLEL_CUT.value: self.cuts_experiment,
        }

        self._run_flag = True

    @property
    def sock(self) -> Socket:
        return self._sock

    @property
    def exp_cache(self) -> Dict:
        return self._parallel_exp_cache

    def _init_sock(self):
        """
        init sock service
        """

        ctx = Context.instance()
        self._sock = ctx.socket(zmq.PAIR)
        self._sock.bind(MERGE_ADDR)
        self.poll = Poller()
        self.poll.register(self._sock, zmq.POLLIN)

    async def listen(self):
        while self._run_flag:
            res = dict(await self.poll.poll(500))
            if self.sock in res:
                require_id, op, *data = await self.sock.recv_multipart()
                if op in self.listen_router_map:
                    if data:
                        data = [from_cache_bytes(x) for x in data]
                        _ = asyncio.create_task(
                            self.listen_router_map[op](
                                require_id=require_id,
                                data=data,
                            )
                        )
                    else:
                        _ = asyncio.create_task(self.listen_router_map[op](require_id=require_id))
                else:
                    _ = self.sock.send_multipart([
                        require_id,
                        to_cache_bytes(
                            Response(
                                code=ResponseCode.REQUIRE_NOT_EXIST,
                                msg=f"require op :{op} not exist.",
                            )
                        ),
                    ])

    async def watch_merge(self):
        while self._run_flag:
            finish_items = []
            for parallel_exp in self.exp_cache.values():
                for child_count_kye, c_exp in parallel_exp.child_exp.items():
                    await c_exp.monitor(self.sock, parallel_exp.shared_resource, child_count_kye)
                    if c_exp.has_finished() is True:
                        finish_items.append((parallel_exp, child_count_kye))

            if finish_items:
                for parallel_exp, child_count_kye in finish_items:
                    c_exp = parallel_exp.child_exp.pop(child_count_kye)
                    c_log(f"{c_exp} execute finish ... ")

            await asyncio.sleep(0.0001)

    async def run(self):
        c_log(f"{LOG_TITLE} | init")
        try:
            self._init_sock()
            listen_job = asyncio.create_task(self.listen())
            watch_job = asyncio.create_task(self.watch_merge())
            c_log(f"{LOG_TITLE} | running")
            await asyncio.gather(listen_job, watch_job)
        except Exception:
            error_msg = traceback.format_exc()
            c_error_log(f"{LOG_TITLE} | crash", error_msg)
        finally:
            c_log(f"{LOG_TITLE} | closed")
            self._sock.close()

    def close(self):
        self._run_flag = False

    async def parallel_register(self, require_id, data):
        p_exp_msg = data[0]
        res = None
        if isinstance(p_exp_msg, ParallelExpMsg):
            # task_id = get_secrets_token(16)
            task_id = str(ObjectId())
            p_exp_cache = PExpCache(token=str(task_id), exp_msg=p_exp_msg)
            self.exp_cache.update({task_id: p_exp_cache})
            res = Response(code=ResponseCode.SUCCESS, data=task_id)
        else:
            res = Response(
                code=ResponseCode.REQUIRE_BODY_ERROR,
                msg="require struct is not ParallelExpMsg",
            )
        # todo
        await self.sock.send_multipart([require_id, to_cache_bytes(res)])

    async def deal_merge_task(self, require_id, data):
        """A function for asynchronous processing of merge tasks.

        Args:
            require_id (_type_): Unique identifier for the request
            data (_type_): A list containing merged task data, where the first element is the task to be merged.
        """
        merge_task = data[0]
        res = ""
        if isinstance(merge_task, MPTask):
            if merge_task.token in self.exp_cache.keys():
                self.exp_cache[merge_task.token].push_merge_task(merge_task)
                res = Response(ResponseCode.SUCCESS)
            else:
                res = Response(
                    ResponseCode.FAILED,
                    msg=f"get merge task not in parallel register experiment, {merge_task}",
                )
        else:
            res = Response(
                ResponseCode.REQUIRE_BODY_ERROR,
                msg="require struct is not MPTask",
            )
        # todo
        await self.sock.send_multipart([require_id, to_cache_bytes(res)])

    async def query_task_id(self, require_id, data):
        """ """
        task = data[0]
        res = None

        if isinstance(task, MPTask):
            if task.token in self.exp_cache and task.count in self.exp_cache[task.token].child_exp:
                task.require_id = require_id
                self.exp_cache[task.token].child_exp[task.count].add_wait_require(task)
            else:
                res = Response(
                    ResponseCode.FAILED,
                    msg=f"can't find query merge task part msg({task})",
                )

        else:
            res = Response(ResponseCode.REQUIRE_BODY_ERROR, "require struct is not MPTask")

        if res:
            await self.sock.send_multipart([require_id, to_cache_bytes(res)])

    async def concurrent_exp_end(self, require_id, data):
        """ """

        token = data[0]
        res = Response(ResponseCode.SUCCESS)
        if token in self.exp_cache:
            remove_exp = self.exp_cache.pop(token)
            remove_exp.exp_run_end()
            res.msg = "remove {remove_exp} success"
        else:
            res.msg = "remove experiment can't find"

        await self.sock.send_multipart([require_id, to_cache_bytes(res)])

    async def cuts_experiment(self, require_id, data):
        """ """
        cut_msg = data[0]
        if isinstance(cut_msg, CutExpMsg):
            if cut_msg.token in self.exp_cache:
                self.exp_cache[cut_msg.token].cuts_experiment(cut_msg.physical_unit, cut_msg.count)
                res = Response(
                    ResponseCode.SUCCESS,
                    msg=f"{self.exp_cache[cut_msg.token]} cuts:{cut_msg.physical_unit} suc.",
                )
            else:
                res = Response(ResponseCode.SUCCESS, msg="not found need cuts exp")
        else:
            res = Response(
                ResponseCode.REQUIRE_BODY_ERROR,
                msg="require struct is not CutMsgTask",
            )
        await self.sock.send_multipart([require_id, to_cache_bytes(res)])


class MergeClient(metaclass=Singleton):
    """
    merge client
    """

    require_id_length = 10

    def __init__(self) -> None:
        ctx = Context.instance()
        self._sock = ctx.socket(zmq.PAIR)
        self._sock.connect(MERGE_ADDR)
        self._merge_link_cache = {}
        self._run_flag = True
        self._sync_job = None

        self.start_sync_job = False

    @property
    def require_id(self):
        return get_secrets_token(self.require_id_length)

    async def _send_data(self, send_msg: List[ByteString]):
        if self.start_sync_job is True:
            if self._sync_job is None or self._sync_job.done():
                self._sync_job = asyncio.create_task(self._async_msg())
                self.start_sync_job = False

        require_id = self.require_id
        send_msg.insert(0, require_id)
        await self._sock.send_multipart(send_msg)
        return require_id

    async def _async_msg(self):
        while self._run_flag:
            require_id, *data = await self._sock.recv_multipart()
            if data:
                data = [from_cache_bytes(x) for x in data]
            self._merge_link_cache.update({require_id: data})

            # bugfix for zyc 2024/05/11: Using asyncio sleep here can severely block
            # performance when requesting a large amount of data
            # await asyncio.sleep(0.02)

    async def _recv_data(self, require_id):
        while self._run_flag:
            if require_id in self._merge_link_cache:
                return self._merge_link_cache.pop(require_id)[0]
            await asyncio.sleep(0.02)

    def shutdown(self):
        self._run_flag = False
        if self._sync_job:
            self._sync_job.cancel()
        self._sock.close()
        if MergeClient in Singleton._instances:
            Singleton._instances.pop(MergeClient)

    async def register_concurrent_experiment(self, exp_msg: ParallelExpMsg):
        """register concurrent experiment to merge service, return token"""
        send_data = [
            MergeOp.PARALLEL_REGISTER.value,
            to_cache_bytes(exp_msg),
        ]

        re_id = await self._send_data(send_data)
        return await self._recv_data(re_id)

    async def send_to_parallel(self, task: MPTask):
        """
        send task to chimera.
        """
        send_data = [MergeOp.PARALLEL_MERGE.value, to_cache_bytes(task)]
        re_id = await self._send_data(send_data)
        return await self._recv_data(re_id)

    async def get_exp_task_id(self, task: MPTask):
        """
        query task doc id by merge service.
        """

        send_data = [MergeOp.QUERY_TASK_ID.value, to_cache_bytes(task)]
        re_id = await self._send_data(send_data)
        return await self._recv_data(re_id)

    async def concurrent_experiment_end(self, token):
        """
        sync concurrent experiment end msg.
        """

        send_data = [MergeOp.PARALLEL_END.value, to_cache_bytes(token)]
        re_id = await self._send_data(send_data)
        return await self._recv_data(re_id)

    async def cut_merge_exp(self, token, count, physic_unit):
        """
        cut experiment by merge just a point with a merge doc.
        """
        cut_msg = CutExpMsg(token, physic_unit, count)
        send_data = [
            MergeOp.PARALLEL_CUT.value,
            to_cache_bytes(cut_msg),
        ]
        re_id = await self._send_data(send_data)
        return await self._recv_data(re_id)

    async def cuts_concurrent_exp(self, token, physic_unit):
        """
        cuts exp by concurrent experiment with merge.
        """
        cut_msgs = CutExpMsg(token, physic_unit)
        send_data = [
            MergeOp.PARALLEL_CUTS.value,
            to_cache_bytes(cut_msgs),
        ]
        re_id = await self._send_data(send_data)
        return await self._recv_data(re_id)
