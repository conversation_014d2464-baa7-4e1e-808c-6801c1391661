# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/02/19
# __author:       <PERSON><PERSON><PERSON>
from enum import Enum
from typing import TYPE_CHECKING, Dict, List, Union

from PySide6.QtWidgets import QMessageBox

from pyqcat_visage.gui.widgets.dialog.create_platform_dialog import CreatePlatformDialog
from pyqcat_visage.gui.widgets.permissions.table_model_perms_platform import QTableModelPermsPlatform
from pyqcat_visage.gui.widgets.title_window import TitleWindow
from pyqcat_visage.gui.permission_platform_ui import Ui_MainWindow

if TYPE_CHECKING:
    from pyqcat_visage.gui.main_window import VisageGUI


class PermsPlatformWindow(TitleWindow):
    def __init__(self, gui: "VisageGUI", parent=None):
        super().__init__(parent)
        self._ui = Ui_MainWindow()
        self._ui.setupUi(self)
        self.gui = gui

        self.data = []
        self._create_platform_dialog = None
        self._setup_model()
        self.load_default_data()

    @property
    def ui(self):
        return self._ui

    def reset_window_layout(self):
        pass

    def load_default_data(self):
        self.ui.TypeBox.setVisible(False)
        self.ui.label.setVisible(False)

    def _setup_model(self):
        self.table_model_perms_platform = QTableModelPermsPlatform(
            self.gui, self, self._ui.listView
        )
        self._ui.listView.setModel(self.table_model_perms_platform)

    def show(self):
        self.load_default_data()
        super().show()

    def change_page(self, index: int):
        if index:
            self.query_info()

    def change_volume(self, index: int):
        if index:
            self.query_info()

    def query_info(self):
        page_num = self.ui.PageBox.value() or 1
        page_size = self.ui.VolumeBox.value() or 20
        ret_data = self.gui.backend.db.query_platform(
            page_size=page_size,
            page_num=page_num
        )
        if ret_data.get("code") == 200:
            self.data = ret_data["data"]
            self.table_model_perms_platform.refresh_auto(check_count=False)
        else:
            self.data = []
            self.table_model_perms_platform.refresh_auto()

    def create_platform(self):
        if self._create_platform_dialog:
            self._create_platform_dialog.close_()
        self._create_platform_dialog = CreatePlatformDialog(parent=self)
        ret = self._create_platform_dialog.exec()
        if int(ret) == 1:
            (
                sample,
                env_name,
                perms_type
            ) = self._create_platform_dialog.get_input()
            if not sample:
                QMessageBox().warning(self, "Save Fail", "pls input sample!")
                self.create_platform()
                return
            if not env_name:
                QMessageBox().warning(self, "Save Fail", "pls input env_name!")
                self.create_platform()
                return
            ret_data = self.gui.backend.db.add_platform(sample, env_name)
            # self.handler_ret_data(ret_data, show_suc=True)

    def delete_platform(self, name, index):
        if self.ask_ok(
                f"Are you sure to <strong style='color:red'>delete</strong> the platform: {name}? "
                "This operation will not be recoverable.",
                "Visage Message",
        ):
            ret_data = self.gui.backend.db.del_platform(name)
            # self.handler_ret_data(ret_data)
            if ret_data.get("code") == 200:
                self.table_model_perms_platform.removeRows(index)
