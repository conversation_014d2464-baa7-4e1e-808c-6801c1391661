# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/12
# __author:       <PERSON><PERSON><PERSON>


from enum import IntEnum
from typing import Any, Optional, Union

from loguru import logger


class BaseEnum(IntEnum):
    def __new__(cls, value: int, description: Optional[str] = None):
        obj = int.__new__(cls, value)
        obj._value_ = value
        obj.description = description
        return obj

    def __str__(self):
        if self.description is not None:
            return self.description
        return self.name

    @classmethod
    def empty_value(cls, data: Any):
        raise ValueError(f"`{data}` not in BaseEnum {cls.__name__}!")

    @classmethod
    def from_description(cls, description: Union[int, str]):
        for member in cls:
            if member.description == description:
                return member
            try:
                if member.value == int(description):
                    return member
            except ValueError:
                pass
        return cls.empty_value(description)


class TransferTaskStatusEnum(BaseEnum):
    INIT = 0x00, "Task initialization"
    ACQ = 0x01, "Task registration successful"
    BLOCK = 0x02, "Task blocking"
    RETRY = 0x03, "The task needs to be resent"
    FAIL = 0x04, "Task execution failed, detail in pyqcat-venus"
    SUC = 0x05, "Task execution successful"

    TACKLE_SUC = 0x06, "Transfer execution successful"
    TACKLE_FAIL = 0x07, "Transfer execution fail"
    
    ACQ_START = 0x08, "Data acquisition start"

    @classmethod
    def empty_value(cls, data: Any):
        logger.error(f"Dispatcher Transfer Status Error | `{data}`!")
        return TransferTaskStatusEnum.FAIL


class KernelReceiverTypeEnum(BaseEnum):
    ACQ_DATA = 0, "Send Acq Data"
    ERROR = 1, "Send Error Message"
    TASK_UUID = 2, "Send Task UUID"
    TASK_STATUS = 3, "Send Task Status"
    HEART = 4, "Dispatcher Heart"


class DataTypeEnum(BaseEnum):
    IQ = 0, "iq"
    AP = 1, "amp_phase"
    PO = 2, "probability"
    TRACK = 3, "track"

    @staticmethod
    def adapter_pre_version(name: str):
        if name == "I_Q":
            return DataTypeEnum.IQ
        else:
            return DataTypeEnum.from_description(name)
        
    @classmethod
    def adapter_experiment_doc_status(cls, value: int):
        if value == 1:
            return DataTypeEnum.AP
        elif value == 4:
            return DataTypeEnum.IQ
        elif value == 5:
            return DataTypeEnum.TRACK
        else:
            raise ValueError(f"No support {value}")


class DataClientRequireCode:
    QUERY_STATE = b"0x01"
    QUERY_ALL_LOOP = b"0x02"
    QUERY_ONE_LOOP = b"0x03"
    CLEAR_TASK = b"0x04"
    CLEAR_ACQ_DATA = b"0x05"
    GET_ADDR = b"06"
    REGISTER = b"07"
    QUERY_TASK_ID = b"08"
    QUERY_ACQ_STATE = b"09"
    FINISH = b"0x10"
    QUERY_INFO = b"0x11"
    TACKLE_ERROR = b"0x12"
    QUERY_SYSTEM = b"0x13"
    WAIT_ACQ = b"0x14"
    RECORD_MESSAGE = b"0x15"


class LinkRequireCode:
    LINK = b"0x01"
    SET = b"0x02"
    STOP = b"0x03"
    CLOSE = b"0x04"
    QUERY_PROGRAM = b"0x05"


class TaskStatusEnum(BaseEnum):
    INIT = 0x00, "Initial"
    PENDING = 0x01, "Pending"
    COMPILED = 0x02, "compiled"
    SCHEDULING = 0x03, "scheduling"
    RUNNING = 0x10, "Running"
    RETRY = 0x11, "Retry"
    SUCCESS = 0x20, "Success"
    PARSE_FAIL = 0x30, "Compiler parse failed"
    COMPILE_FAIL = 0x31, "Compiler compile failed"
    VALIDATE_FAIL = 0x32, "Core validate failed"
    INCOMPLETE_FAIL = 0x33, "Incomplete Fail"
    CANCELED = 0x3F, "Canceled"
    PROCESS_FAIL = 0x40, "Process failed"
    COMPONENT_FAIL = 0x41, "Component failed"
    QAIO_10000 = 0x50, "QAIO 10000 time out"
    QAIO_5000 = 0x51, "QAIO 5000 time out"
    UNKNOWN_FAILED = 0x52, "unknown exception"
    
    @classmethod
    def empty_value(cls, data: Any):
        return data


CRASH_STATE = [TransferTaskStatusEnum.FAIL, TransferTaskStatusEnum.TACKLE_FAIL]
