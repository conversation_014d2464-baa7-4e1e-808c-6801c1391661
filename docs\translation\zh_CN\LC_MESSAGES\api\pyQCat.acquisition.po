# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.acquisition.rst:2
msgid "pyQCat.acquisition package"
msgstr ""

#: ../../source/api/pyQCat.acquisition.rst:6
msgid "Module contents"
msgstr ""

#: of pyQCat.acquisition:3
msgid "Data Acquisition (:mod:`pyQCat.acquisition`)"
msgstr ""

#: of pyQCat.acquisition:6
msgid "Base Classes"
msgstr ""

#: of pyQCat.acquisition:11:<autosummary>:1
msgid ""
":py:obj:`DataAcquisition <pyQCat.acquisition.DataAcquisition>`\\ "
"\\(id\\_\\[\\, data\\_type\\, ...\\]\\)"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition._start_info:1
msgid "Print log when experiment start."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition._stop_info:1
msgid "Print log when experiment end."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.loop_control:1
msgid "The default method for getting data for each loop."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop
#: pyQCat.acquisition.acquisition.DataAcquisition.loop_control
msgid "Parameters"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.loop_control:4
msgid "readout channel number."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.loop_control:6
msgid "current loop count."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop
#: pyQCat.acquisition.acquisition.DataAcquisition.loop_control
msgid "Returns"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.loop_control:8
msgid ""
"measure result and status. status 1: run experiment with Amp/Phase type "
"data. status 2: finished. status 3: run experiment failed. status 4: run "
"experiment with I/Q type data."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop:1
msgid "Excute measurement by looping through multiple points."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop:3
msgid ""
"A callback function that, if defined, is used for a single circular fetch"
" of data and is used as an alternate interface for special cases. "
"Attention, this function must return the result and state. ex. >>>def "
"func(*args):     >>>    # do something     >>>    return res, status"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop:3
msgid ""
"A callback function that, if defined, is used for a single circular fetch"
" of data and is used as an alternate interface for special cases. "
"Attention, this function must return the result and state. ex. >>>def "
"func(*args):"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop:9
msgid "The argument to the callback function `func`."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop:10
msgid "A callback function of the processing function to one loop."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop:12
msgid "None"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.save_plot_data:1
msgid "Save temp data to temp file use for gui dynamic plot."
msgstr ""

#~ msgid ""
#~ ":py:obj:`DataAcquisition <pyQCat.acquisition.DataAcquisition>`\\"
#~ " \\(id\\_\\[\\, data\\_type\\, ...\\]\\)"
#~ msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`DataAcquisition <pyQCat.acquisition.DataAcquisition>`\\"
#~ " \\(id\\_\\[\\, data\\_type\\, ...\\]\\)"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

