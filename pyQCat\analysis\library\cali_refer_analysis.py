# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/04
# __author:       <PERSON>


from typing import Any, <PERSON><PERSON>

from ...structures import Options
from ..curve_analysis import CurveAnalysis
from ..quality.base_quality import BaseQuality, QualityDescribe
from ..specification import ParameterRepr


class CaliReferAnalysis(CurveAnalysis):
    """An analysis class for coupler cali_refer."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""

        options = super()._default_options()

        options.x_label = "Zamp (V)"
        options.y_label = "OscillateFreq (MHz)"
        options.quality_list = []
        options.min_delta_freq = 5  # MHz
        options.max_delta_freq = 20  # MHz

        options.result_parameters = [
            ParameterRepr(
                name="amp",
                repr="Refer Z amp",
                unit="V",
                param_path="Coupler.cali_refer.amp",
            ),
            ParameterRepr(
                name="delta_freq",
                repr="Refer delta frequency",
                unit="V",
                param_path="Coupler.cali_refer.delta_freq",
            ),
        ]

        return options

    def _evaluate_quality(self) -> Tuple[str, Any]:
        """Evaluates the quality."""
        if self.options.data_key is not None:
            data_key = self.options.data_key[0]
        else:
            data_key = list(self.experiment_data.y_data.keys())[0]

        quality_list = self.options.quality_list
        flag_list = []
        for qua_str in quality_list:
            if qua_str in [QualityDescribe.perfect, QualityDescribe.normal]:
                cd_flag = True
            else:
                cd_flag = False
            flag_list.append(cd_flag)

        if len(flag_list) and all(flag_list):
            self._quality = BaseQuality.instantiate(QualityDescribe.perfect)
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)

        return data_key, self._quality

    def _extract_result(self, data_key: str):
        """Evaluates the results."""
        min_delta_freq = self.options.min_delta_freq
        max_delta_freq = self.options.max_delta_freq

        analysis_data = self.analysis_datas[data_key]
        z_amp_arr = analysis_data.x
        osc_freq_arr = analysis_data.y

        z_amp = z_amp_arr[-1]
        s_freq, e_freq = osc_freq_arr[0], osc_freq_arr[-1]
        delta_freq = round(e_freq - s_freq, 3)

        self.results.amp.value = z_amp
        self.results.delta_freq.value = delta_freq

        # According delta_freq to update quality again.
        if delta_freq < min_delta_freq:
            quality_str = QualityDescribe.bad
        elif min_delta_freq <= delta_freq <= max_delta_freq:
            quality_str = ""
        else:
            quality_str = QualityDescribe.bad
        if quality_str:
            self._quality._quality = quality_str
