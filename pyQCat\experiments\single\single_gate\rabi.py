# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/27
# __author:       <PERSON><PERSON><PERSON> Shi

"""<PERSON>bi experiment for calibrating qubit xy pulse amplitude."""

from copy import deepcopy
from typing import List

from ....analysis.library import (
    RabiAmpAnalysis,
    RabiWidthAnalysis,
    XYCrossRabiWidthOnceAnalysis,
)
from ....analysis.specification import ParameterRepr
from ....errors import ExperimentOptionsError
from ....log import pyqlog
from ....pulse.pulse_function import half_pi_pulse, pi_pulse
from ....pulse.pulse_lib import Constant
from ....qubit import Qubit
from ....structures import MetaData, Options
from ....tools import qarange
from ....types import StandardContext
from ...coupler_experiment_v1 import CouplerBaseExperimentV1 as CouplerBaseExperiment
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class RabiScanAmp(TopExperiment):
    """Rabi experiment scan XY pulse amplitude."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            name (str): Indicates which type of pulse is selected
                                  to perform the experimentOnly 'Xpi' and
                                  'Xpi/2' supported.
            amps (list, np.ndarray): The list of XY pulse amplitudes that
                                     will be scanned in the experiment.
            drive_power (float): The driver power of qubit.
        """
        options = super()._default_experiment_options()

        options.set_validator("name", ["Xpi", "Xpi/2"])
        options.set_validator("amps", list, limit_null=True)
        options.set_validator("drive_power", (-40, -10, 1))

        options.amps = qarange(0, 1, 0.02)
        options.drive_power = None
        options.name = "Xpi"

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)

        options.result_parameters = None
        options.quality_bounds = [0.98, 0.95, 0.91]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()
        options.support_context = [
            StandardContext.QC,
            StandardContext.CPC,
        ]
        return options

    def _metadata(self) -> MetaData:
        """Set RabiScanAmp experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"power": (self.experiment_options.drive_power, "db")}
        return metadata

    def _check_options(self):
        super()._check_options()
        drive_power = self.experiment_options.drive_power
        name = self.experiment_options.name

        if drive_power is None:
            drive_power = (
                self.coupler.drive_power
                if self.is_coupler_exp
                else self.qubit.drive_power
            ) or -30

        if self.is_coupler_exp is True:
            path_dp = "Coupler.drive_power"
            path_pre = "Coupler.drive_XYwave"
        elif self.coupler and self.is_coupler_exp is False:
            path_dp = "Coupler.probe_drive_power"
            path_pre = "Coupler.probe_XYwave"
        else:
            path_dp = "Qubit.drive_power"
            path_pre = "Qubit.XYwave"

        result_parameters = [
            ParameterRepr(
                name="drive_power", repr="dp", unit="db", param_path=f"{path_dp}"
            )
        ]

        if name == "Xpi":
            result_parameters.extend(
                [
                    ParameterRepr(
                        name="Xpi", repr="X-amp", unit="V", param_path=f"{path_pre}.Xpi"
                    ),
                    ParameterRepr(
                        name="Xpi2",
                        repr="X2-amp",
                        unit="V",
                        param_path=f"{path_pre}.Xpi2",
                    ),
                ]
            )
        else:
            result_parameters.append(
                ParameterRepr(
                    name="Xpi2", repr="X2-amp", unit="V", param_path=f"{path_pre}.Xpi2"
                )
            )

        if self.discriminator is not None:
            data_type = "I_Q"
        else:
            data_type = "amp_phase"

        self.set_experiment_options(drive_power=drive_power, data_type=data_type)
        self.set_analysis_options(result_parameters=result_parameters)
        self.set_run_options(
            x_data=self.experiment_options.amps, analysis_class=RabiAmpAnalysis
        )

    @staticmethod
    def get_xy_pulse(qubit: Qubit, type_: str, sweep_list: List):
        pulse_list = []
        for param in sweep_list:
            if type_ == "Xpi":
                pulse = pi_pulse(qubit)
                pulse(amp=param)
            else:
                pulse = half_pi_pulse(qubit)
                pulse = pulse(amp=param) * 2
            pulse_list.append(pulse)
        return pulse_list

    @staticmethod
    def set_xy_pulses(builder):
        pulse_list = RabiScanAmp.get_xy_pulse(
            builder.qubit,
            builder.experiment_options.name,
            builder.experiment_options.amps,
        )
        builder.play_pulse("XY", builder.qubit, pulse_list)

    @staticmethod
    def update_instrument(builder):
        power = builder.experiment_options.drive_power
        builder.inst.set_power("XY_control", builder.qubit.xy_channel, power)


class RabiScanWidth(TopExperiment):
    """Rabi experiment scan XY pulse width."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            widths (list, np.ndarray): The list of XY pulse widths that
                                       will be scanned in the experiment.
        """
        options = super()._default_experiment_options()

        options.set_validator("widths", list, limit_null=True)
        options.set_validator("drive_power", (-40, -10, 1))
        options.set_validator("drive_freq", float)
        options.set_validator("adjust_power_flag", bool)

        options.drive_freq = None
        options.drive_power = None
        options.widths = qarange(5, 200, 5)
        options.adjust_power_flag = False

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.power_path = None
        options.rec_drive_power = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)

        options.quality_bounds = [0.98, 0.95, 0.91]
        options.drive_power = None
        options.xy_time = None

        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        if self.discriminator is not None:
            data_type = "I_Q"
        else:
            data_type = "amp_phase"

        drive_power = self.experiment_options.drive_power
        drive_freq = self.experiment_options.drive_freq
        power_path = self.run_options.power_path
        xy_time = self.analysis_options.xy_time

        if drive_power is None:
            drive_power = (
                self.coupler.drive_power
                if self.is_coupler_exp
                else self.qubit.drive_power
            ) or -30

        if drive_freq is None:
            drive_freq = (
                self.coupler.drive_freq
                if self.is_coupler_exp
                else self.qubit.drive_freq
            ) or 6000

        if self.coupler and self.is_coupler_exp is False:
            power_path = "Coupler.probe_drive_power"
            xy_time = self.coupler.probe_XYwave.time
        elif self.is_coupler_exp is True and self.coupler:
            power_path = "Coupler.drive_power"
            xy_time = self.coupler.drive_XYwave.time
        elif self.qubit:
            power_path = "Qubit.drive_power"
            xy_time = self.qubit.XYwave.time

        self.set_experiment_options(
            data_type=data_type, drive_power=drive_power, drive_freq=drive_freq
        )
        self.set_run_options(
            power_path=power_path,
            x_data=self.experiment_options.widths,
            analysis_class=RabiWidthAnalysis,
        )
        self.set_analysis_options(drive_power=drive_power, xy_time=xy_time)

    def _metadata(self) -> MetaData:
        """Set RabiScanWidth experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "freq": (self.experiment_options.drive_freq, "MHz"),
            "current power": (self.experiment_options.drive_power, "dB"),
        }
        return metadata

    @staticmethod
    def update_instrument(builder):
        drive_freq = builder.experiment_options.drive_freq
        drive_power = builder.experiment_options.drive_power
        builder.inst.set_output_freq("XY_control", builder.qubit.xy_channel, drive_freq)
        builder.inst.set_power("XY_control", builder.qubit.xy_channel, drive_power)
        builder.sweep_readout_trigger_delay(
            builder.qubit.readout_channel, builder._pulse_time_list
        )

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        adjust_power_flag = self.experiment_options.adjust_power_flag
        power_path = self.run_options.power_path
        result_name = self.analysis_options.result_name

        results_obj = self.analysis.results
        osc_flag = results_obj.oscillating.value
        freq = results_obj.freq.value  # unit: MHz
        anal_power = results_obj.drive_power.value  # unit: dB

        validate_flag = False
        rec_drive_power = anal_power
        if isinstance(anal_power, (int, float)):
            if anal_power < -40:
                rec_drive_power = -40
            elif anal_power > -10:
                rec_drive_power = -10
            else:
                validate_flag = True

        self.run_options.rec_drive_power = rec_drive_power

        if adjust_power_flag is True and validate_flag is True:
            pyqlog.info(f"Will set {result_name} result path: {power_path}")
            results_obj.drive_power.extra["path"] = power_path
        elif adjust_power_flag is True and validate_flag is False:
            pyqlog.warning(
                f"analysis recommended drive_power: {anal_power} dB, "
                f"oscillating: {osc_flag}, so validate: {validate_flag}, "
                f"maybe not oscillate or value not in range [-40, -10]"
            )

    @staticmethod
    def get_xy_pulse(qubit, widths: List):
        pulse_list = []
        for param in widths:
            pulse = pi_pulse(qubit)
            pulse_list.append(pulse(time=param, detune=0))
        return pulse_list

    @staticmethod
    def set_xy_pulses(builder):
        pulse_list = RabiScanWidth.get_xy_pulse(
            builder.qubit, builder.experiment_options.widths
        )
        builder.play_pulse("XY", builder.qubit, pulse_list)


class CouplerRabiScanAmp(CouplerBaseExperiment, RabiScanAmp):
    """Coupler Rabi Scan Amp Experiment."""

    @staticmethod
    def update_instrument(self):
        """Update drive power if setting."""
        power = self.experiment_options.drive_power
        self.inst.set_power("XY_control", self.driveQ.xy_channel, power)

    @staticmethod
    def set_xy_pulses(self):
        """Set RabiScanWidth experiment XY pulses."""
        pulse_list = RabiScanAmp.get_xy_pulse(
            self.driveQ, self.experiment_options.name, self.experiment_options.amps
        )
        self.compose_xy_pulses(pulse_list)


class CouplerRabiScanWidth(CouplerBaseExperiment, RabiScanWidth):
    """Coupler Rabi Scan Width Experiment."""

    def _metadata(self) -> MetaData:
        """Set RabiScanWidth experiment metadata."""
        metadata = super(RabiScanWidth, self)._metadata()
        metadata.draw_meta = {"power": (self.experiment_options.drive_power, "dB")}
        return metadata

    @staticmethod
    def set_xy_pulses(self):
        """Set RabiScanWidth experiment XY pulses."""
        pulse_list = RabiScanWidth.get_xy_pulse(
            self.driveQ, self.experiment_options.widths
        )
        self.compose_xy_pulses(pulse_list)

    @staticmethod
    def update_instrument(self):
        drive_freq = self.experiment_options.drive_freq
        drive_power = self.experiment_options.drive_power
        self.inst.set_output_freq("XY_control", self.driveQ.xy_channel, drive_freq)
        self.inst.set_power("XY_control", self.driveQ.xy_channel, drive_power)
        self.sweep_readout_trigger_delay(
            self.probeQ.readout_channel,
            self._pulse_time_list[: len(self.experiment_options.widths)],
        )


class RabiScanWidthDetune(RabiScanWidth):
    """Rabi experiment scan XY pulse width and support set detune."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            widths (list, np.ndarray): The list of XY pulse widths that
                                       will be scanned in the experiment.
        """
        options = super()._default_experiment_options()

        options.set_validator("detune", float)
        options.detune = 0

        return options

    @staticmethod
    def set_xy_pulses(self):
        """Set RabiScanWidth experiment XY pulses."""
        detune = self.experiment_options.detune

        qubit = deepcopy(self.qubit)
        if self.experiment_options.open_half_pi is True:
            qubit.XYwave.detune_pi2 = detune
        else:
            qubit.XYwave.detune_pi = detune
        pyqlog.log("EXP", f"Xpi detune is {qubit.XYwave.detune_pi} MHz!")

        pulse_list = []
        for param in self.experiment_options.widths:
            pulse = pi_pulse(qubit)
            pulse_list.append(pulse(time=param))

        self.play_pulse("XY", self.qubit, pulse_list)

    def _metadata(self) -> MetaData:
        """Set RabiScanWidth experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"detune": (self.experiment_options.detune, "MHz")}
        return metadata


class XYCrossRabiWidthOnce(RabiScanWidth):
    """Add XY Cross RabiScanWidth base experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("xy_name", str)
        options.set_validator("rd_name", str)
        options.set_validator("direct_execute", bool)

        options.xy_name = ""
        options.rd_name = ""
        options.direct_execute = False
        options.bind_probe = False

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("loga_fit", bool)

        options.loga_fit = False

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.xy_qubit = None
        options.rd_qubit = None

        options.injection_func = ["get_xy_pulse"]
        options.support_context = [
            StandardContext.CM,
            StandardContext.URM,
        ]

        return options

    def _check_options(self):
        """Check Options."""
        super(RabiScanWidth, self)._check_options()

        drive_power = self.experiment_options.drive_power
        drive_freq = self.experiment_options.drive_freq
        xy_name = self.experiment_options.xy_name
        rd_name = self.experiment_options.rd_name
        direct_execute = self.experiment_options.direct_execute

        bit_names = []
        qubit_map = {}
        for qubit in self.qubits:
            q_name = qubit.name
            bit_names.append(q_name)
            qubit_map.update({q_name: qubit})

        name_map = {"xy_name": xy_name, "rd_name": rd_name}
        for name_label, t_name in name_map.items():
            if t_name not in bit_names:
                raise ExperimentOptionsError(
                    self,
                    f"Set {name_label} {t_name} not in all bit names: {bit_names}!",
                    name_label,
                    t_name,
                )
        xy_qubit = qubit_map.get(xy_name)
        rd_qubit = qubit_map.get(rd_name)
        multi_readout_channels = [rd_qubit.readout_channel]

        if direct_execute is True:
            xy_qubit.drive_power = rd_qubit.drive_power
            xy_qubit.drive_freq = rd_qubit.drive_freq
            xy_qubit.XYwave = rd_qubit.XYwave

        if drive_power is None:
            drive_power = xy_qubit.drive_power or -30
        if drive_freq is None:
            drive_freq = xy_qubit.drive_freq or 6000

        # Get target bit discriminator.
        if self.discriminator:
            if isinstance(self.discriminator, list):
                dcm_list = self.discriminator
            else:
                dcm_list = [self.discriminator]
            new_dcm = None
            for dcm in dcm_list:
                if rd_name == dcm.name:
                    new_dcm = dcm
                    break
            pyqlog.info(f"Add {new_dcm.name} discriminator: {new_dcm}")
            self.discriminator = new_dcm

        if self.discriminator is not None:
            data_type = "I_Q"
        else:
            data_type = "amp_phase"

        if self.analysis_options.loga_fit:
            analysis_class = XYCrossRabiWidthOnceAnalysis
        else:
            analysis_class = RabiWidthAnalysis

        self.set_experiment_options(
            data_type=data_type,
            drive_power=drive_power,
            drive_freq=drive_freq,
            multi_readout_channels=multi_readout_channels,
        )
        self.set_analysis_options(result_name=rd_name)
        self.set_run_options(
            xy_qubit=xy_qubit,
            rd_qubit=rd_qubit,
            measure_qubits=[rd_qubit],
            x_data=self.experiment_options.widths,
            analysis_class=analysis_class,
        )

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()

        xy_name = self.experiment_options.xy_name  # Bias
        rd_name = self.experiment_options.rd_name  # Target
        metadata.draw_meta.update(
            {
                "xy_name": xy_name,
                "rd_name": rd_name,
            }
        )
        metadata.name = f"{rd_name} {xy_name}-{self.label}"
        return metadata

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulses."""
        widths = self.experiment_options.widths
        xy_qubit = self.run_options.xy_qubit
        xy_pulse_list = self.get_xy_pulse(xy_qubit, widths)

        new_xy_pulse_list = [
            Constant(pulse_obj.width, 0, "XY")() for pulse_obj in xy_pulse_list
        ]
        for qubit in self.qubits:
            if qubit == xy_qubit:
                self.play_pulse("XY", qubit, xy_pulse_list)
            else:
                self.play_pulse("XY", qubit, new_xy_pulse_list)

    @staticmethod
    def update_instrument(self):
        """Update instrument parameters."""
        drive_freq = self.experiment_options.drive_freq
        drive_power = self.experiment_options.drive_power
        widths = self.experiment_options.widths
        xy_qubit = self.run_options.xy_qubit
        rd_qubit = self.run_options.rd_qubit

        xy_channel = xy_qubit.xy_channel
        rd_channel = rd_qubit.readout_channel
        delays = self._pulse_time_list[: len(widths)]

        self.inst.set_output_freq("XY_control", xy_channel, drive_freq)
        self.inst.set_power("XY_control", xy_channel, drive_power)

        self._bind_probe_inst(rd_qubit)
        self.sweep_readout_trigger_delay(rd_channel, delays)


class CouplerRabiScanWidthDetune(CouplerRabiScanWidth):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("detune", float)
        options.detune = 0

        return options

    @staticmethod
    def set_xy_pulses(self):
        """Set RabiScanWidth experiment XY pulses."""
        self.driveQ.XYwave.detune_pi = self.experiment_options.detune

        pulse_list = []
        for param in self.experiment_options.widths:
            pulse = pi_pulse(self.driveQ)
            pulse_list.append(pulse(time=param))

        self.compose_xy_pulses(pulse_list)


class RabiScanWidthAmp(RabiScanWidth):
    """Rabi experiment scan XY pulse width and support set Xpi."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment options:
            amp (float): XY pulse amp value.

        """
        options = super()._default_experiment_options()

        options.set_validator("amp", float)
        options.amp = 0.8

        return options

    @staticmethod
    def set_xy_pulses(self):
        """Set RabiScanWidth experiment XY pulses."""
        amp = self.experiment_options.amp

        qubit = deepcopy(self.qubit)
        qubit.XYwave.Xpi = amp
        pyqlog.log("EXP", f"Xpi amp is {qubit.XYwave.Xpi} V")

        pulse_list = []
        for param in self.experiment_options.widths:
            pulse = pi_pulse(qubit)
            pulse_list.append(pulse(time=param))

        self.play_pulse("XY", self.qubit, pulse_list)

    def _metadata(self) -> MetaData:
        """Set RabiScanWidth experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"amp": (self.experiment_options.amp, "V")}
        return metadata
