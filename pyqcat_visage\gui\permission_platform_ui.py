# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'permission_platform_ui.ui'
##
## Created by: Qt User Interface Compiler version 6.8.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QAction, QBrush, QColor, QConicalGradient,
    QCursor, QFont, QFontDatabase, QGradient,
    QIcon, QImage, QKeySequence, QLinearGradient,
    QPainter, QPalette, QPixmap, QRadialGradient,
    QTransform)
from PySide6.QtWidgets import (QApplication, QGridLayout, QGroupBox, QHBoxLayout,
    QLabel, QMainWindow, QPushButton, QSizePolicy,
    QSpacerItem, QSpinBox, QStatusBar, QToolBar,
    QVBoxLayout, QWidget)

from .widgets.combox_custom.combox_search import SearchComboBox
from .widgets.permissions.table_view_perms_note import QTableViewPermsNoteWidget

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(777, 713)
        self.actionCreatePlatform = QAction(MainWindow)
        self.actionCreatePlatform.setObjectName(u"actionCreatePlatform")
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.gridLayout = QGridLayout(self.centralwidget)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setContentsMargins(-1, 0, -1, 0)
        self.widget_main = QWidget(self.centralwidget)
        self.widget_main.setObjectName(u"widget_main")
        self.verticalLayout_2 = QVBoxLayout(self.widget_main)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(0, 0, -1, -1)
        self.widget_2 = QWidget(self.widget_main)
        self.widget_2.setObjectName(u"widget_2")
        self.horizontalLayout_4 = QHBoxLayout(self.widget_2)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout_4.setContentsMargins(-1, 0, -1, 0)
        self.widget_11 = QWidget(self.widget_2)
        self.widget_11.setObjectName(u"widget_11")
        self.gridLayout_2 = QGridLayout(self.widget_11)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout_2.setContentsMargins(-1, 0, -1, 0)
        self.widget_3 = QWidget(self.widget_11)
        self.widget_3.setObjectName(u"widget_3")
        self.horizontalLayout = QHBoxLayout(self.widget_3)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.label = QLabel(self.widget_3)
        self.label.setObjectName(u"label")

        self.horizontalLayout.addWidget(self.label)

        self.horizontalSpacer = QSpacerItem(77, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer)

        self.TypeBox = SearchComboBox(self.widget_3)
        self.TypeBox.setObjectName(u"TypeBox")

        self.horizontalLayout.addWidget(self.TypeBox)

        self.horizontalSpacer_2 = QSpacerItem(77, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_2)

        self.horizontalLayout.setStretch(0, 2)
        self.horizontalLayout.setStretch(1, 2)
        self.horizontalLayout.setStretch(2, 7)
        self.horizontalLayout.setStretch(3, 2)

        self.gridLayout_2.addWidget(self.widget_3, 0, 0, 1, 1)


        self.horizontalLayout_4.addWidget(self.widget_11)

        self.widget_10 = QWidget(self.widget_2)
        self.widget_10.setObjectName(u"widget_10")
        self.verticalLayout_3 = QVBoxLayout(self.widget_10)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalSpacer = QSpacerItem(20, 18, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_3.addItem(self.verticalSpacer)

        self.Query = QPushButton(self.widget_10)
        self.Query.setObjectName(u"Query")

        self.verticalLayout_3.addWidget(self.Query)

        self.verticalSpacer_2 = QSpacerItem(20, 18, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_3.addItem(self.verticalSpacer_2)


        self.horizontalLayout_4.addWidget(self.widget_10)

        self.horizontalLayout_4.setStretch(0, 4)
        self.horizontalLayout_4.setStretch(1, 1)

        self.verticalLayout_2.addWidget(self.widget_2)

        self.groupBox = QGroupBox(self.widget_main)
        self.groupBox.setObjectName(u"groupBox")
        self.verticalLayout = QVBoxLayout(self.groupBox)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.widget_6 = QWidget(self.groupBox)
        self.widget_6.setObjectName(u"widget_6")
        self.horizontalLayout_5 = QHBoxLayout(self.widget_6)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.horizontalSpacer_7 = QSpacerItem(49, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer_7)

        self.label_4 = QLabel(self.widget_6)
        self.label_4.setObjectName(u"label_4")

        self.horizontalLayout_5.addWidget(self.label_4)

        self.PageBox = QSpinBox(self.widget_6)
        self.PageBox.setObjectName(u"PageBox")
        self.PageBox.setMinimum(1)

        self.horizontalLayout_5.addWidget(self.PageBox)

        self.horizontalSpacer_8 = QSpacerItem(257, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer_8)

        self.label_5 = QLabel(self.widget_6)
        self.label_5.setObjectName(u"label_5")

        self.horizontalLayout_5.addWidget(self.label_5)

        self.VolumeBox = QSpinBox(self.widget_6)
        self.VolumeBox.setObjectName(u"VolumeBox")
        self.VolumeBox.setMinimum(10)
        self.VolumeBox.setMaximum(100)
        self.VolumeBox.setValue(20)

        self.horizontalLayout_5.addWidget(self.VolumeBox)

        self.horizontalSpacer_9 = QSpacerItem(49, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer_9)

        self.horizontalLayout_5.setStretch(0, 1)
        self.horizontalLayout_5.setStretch(1, 1)
        self.horizontalLayout_5.setStretch(2, 2)
        self.horizontalLayout_5.setStretch(3, 5)
        self.horizontalLayout_5.setStretch(4, 1)
        self.horizontalLayout_5.setStretch(5, 2)
        self.horizontalLayout_5.setStretch(6, 1)

        self.verticalLayout.addWidget(self.widget_6)

        self.listView = QTableViewPermsNoteWidget(self.groupBox)
        self.listView.setObjectName(u"listView")

        self.verticalLayout.addWidget(self.listView)


        self.verticalLayout_2.addWidget(self.groupBox)

        self.verticalLayout_2.setStretch(0, 1)
        self.verticalLayout_2.setStretch(1, 8)
        self.groupBox.raise_()
        self.widget_2.raise_()

        self.gridLayout.addWidget(self.widget_main, 0, 0, 1, 1)

        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)
        self.toolBar = QToolBar(MainWindow)
        self.toolBar.setObjectName(u"toolBar")
        MainWindow.addToolBar(Qt.ToolBarArea.TopToolBarArea, self.toolBar)

        self.toolBar.addAction(self.actionCreatePlatform)
        self.toolBar.addSeparator()

        self.retranslateUi(MainWindow)
        self.actionCreatePlatform.triggered.connect(MainWindow.create_platform)
        self.Query.clicked.connect(MainWindow.query_info)
        self.PageBox.valueChanged.connect(MainWindow.change_page)
        self.VolumeBox.valueChanged.connect(MainWindow.change_volume)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"Platform", None))
        self.actionCreatePlatform.setText(QCoreApplication.translate("MainWindow", u"CreatePlatform", None))
#if QT_CONFIG(tooltip)
        self.actionCreatePlatform.setToolTip(QCoreApplication.translate("MainWindow", u"CreatePlatform", None))
#endif // QT_CONFIG(tooltip)
        self.label.setText(QCoreApplication.translate("MainWindow", u"\u6743\u9650\u7c7b\u578b", None))
        self.Query.setText(QCoreApplication.translate("MainWindow", u"query", None))
        self.groupBox.setTitle(QCoreApplication.translate("MainWindow", u"PermissionList", None))
        self.label_4.setText(QCoreApplication.translate("MainWindow", u"Page", None))
        self.label_5.setText(QCoreApplication.translate("MainWindow", u"Volume", None))
        self.toolBar.setWindowTitle(QCoreApplication.translate("MainWindow", u"toolBar", None))
    # retranslateUi

