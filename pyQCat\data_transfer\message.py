# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/19
# __author:       <PERSON><PERSON><PERSON>

"""
Socket for transfer.
"""

import traceback
from threading import Thread
from typing import List

import zmq
from zmq.asyncio import Context, Socket

from .log import LogLevel, logger
from .share import ShareArea
from .state import DataClientRequireCode, KernelReceiverTypeEnum, TransferTaskStatusEnum
from .structure import CompileResult
from .util import build_ipc_file, decode_msg, encode_msg, generate_unique_identity


class DispatchSocket:
    """Dealer module which mainly used to send monster program and receive
    result by Naga kernel.
    """

    def __init__(
        self,
        url: str,
        identity: str,
        share: ShareArea,
    ) -> None:
        self.share = share
        self.context = Context.instance()
        self.socket: Socket = self.context.socket(zmq.DEALER)
        self.identity = generate_unique_identity(identity)
        self.socket.setsockopt(zmq.IDENTITY, self.identity)
        self.socket.connect(url)

        self._recv_link_map = {
            str(KernelReceiverTypeEnum.ACQ_DATA.value): self._recv_acq_data,
            str(KernelReceiverTypeEnum.TASK_UUID.value): self._recv_task_uuid,
            str(KernelReceiverTypeEnum.TASK_STATUS.value): self._recv_task_state,
            str(KernelReceiverTypeEnum.HEART.value): self._recv_heart,
        }

    async def send(self, buffers: List[bytes]):
        await self.socket.send_multipart(buffers)

    async def recv(self):
        try:
            msg_type, buffer = await self.socket.recv_multipart()
            return self._parse(msg_type, buffer)
        except Exception:
            logger.error(
                f"Dispatcher Recv Error | {msg_type} | {traceback.format_exc()}!"
            )

    def _parse(self, msg_type: bytes, buffer: bytes):
        """Parse different message types and distribute them to different
        processing functions.

        Args:
            msg_type (bytes): bytes of KernelReceiverTypeEnum
            buffer (bytes): messages

        Raises:
            ValueError: Message command that cannot be processed

        Returns:
            bool: Binding success returns True, failure returns False
        """
        recv_type = decode_msg(msg_type)
        if recv_type in self._recv_link_map:
            return self._recv_link_map[recv_type](buffer)
        else:
            raise ValueError(f"No support recv type {recv_type}")

    def _recv_acq_data(self, buffer: bytes) -> bool:
        """Received task acq data and stored it in the shared area

        Args:
            buffer (bytes): bytes of acq data

        Returns:
            bool: Binding success returns True, failure returns False
        """
        return self.share.bind_acq_data(buffer)

    def _recv_error(self, buffer: bytes) -> bool:
        """Accepting task error pushed by Naga

        Args:
            buffer (bytes): bytes of error

        Returns:
            bool: Binding success returns True, failure returns False
        """
        return self.share.bind_task_error(decode_msg(buffer))

    def _recv_task_uuid(self, buffer: bytes) -> bool:
        """Accepting task uuid pushed by Naga

        After sending each task to naga, the task can only be officially opened
        when the handshake information is established by returning task_uuid.

        Args:
            buffer (bytes): bytes of task uuid

        Returns:
            bool: Binding success returns True, failure returns False
        """
        return self.share.bind_task_id(decode_msg(buffer))

    def _recv_task_state(self, buffer: bytes) -> bool:
        """Accepting task state pushed by Naga

        Args:
            buffer (bytes): bytes of task state

        Returns:
            bool: Binding success returns True, failure returns False
        """
        return self.share.bind_task_state(decode_msg(buffer))

    def _recv_heart(self, buffer: bytes) -> bool:
        """Accepting heart pushed by Naga

        Args:
            buffer (bytes): bytes of identity naga

        Returns:
            bool: heart receive state
        """
        return self.share.recv_dispatcher_heart_state(buffer)


class TransferSocket(Thread):
    """
    Transfer module which mainly used to accept internal data requests.
    """

    def __init__(
        self, url: str, identity: str, share: ShareArea, thread_token: str = ""
    ) -> None:
        super().__init__(name=thread_token, daemon=True)
        self.running = True
        self.share = share
        self.context = zmq.Context()
        self.socket = self.context.socket(zmq.ROUTER)
        self.thread_token = thread_token
        self.identity = generate_unique_identity(identity + thread_token)
        self.addr = build_ipc_file(url + thread_token)
        self.socket.setsockopt(zmq.IDENTITY, self.identity)
        self.socket.bind(self.addr)
        self._recv_link_map = {
            DataClientRequireCode.QUERY_ALL_LOOP: self._query_all_loop,
            DataClientRequireCode.QUERY_ONE_LOOP: self._query_one_loop,
            DataClientRequireCode.QUERY_STATE: self._query_state,
            DataClientRequireCode.CLEAR_TASK: self._clear_task,
            DataClientRequireCode.CLEAR_ACQ_DATA: self._clear_acq_data,
            DataClientRequireCode.REGISTER: self._register_task,
            DataClientRequireCode.QUERY_TASK_ID: self._query_task_id,
            DataClientRequireCode.QUERY_INFO: self._query_task_information,
            DataClientRequireCode.WAIT_ACQ: self._wait_acquisition,
            DataClientRequireCode.RECORD_MESSAGE: self._monster_record_message,
        }

    def run(self):
        logger.log(LogLevel.SYSTEM, f"Transfer thread {self.addr} start ...")
        while self.share.close is False:
            client, msg_type, *buffer = self.socket.recv_multipart()
            if msg_type in self._recv_link_map:
                try:
                    result = self._recv_link_map[msg_type](*buffer)
                except Exception:
                    import traceback

                    logger.error(
                        f"transfer tackle message error | {traceback.format_exc()}"
                    )
                    logger.debug(f"msg_type={msg_type} buffer={buffer}")
                    self.send(client, DataClientRequireCode.TACKLE_ERROR)
                else:
                    if result:
                        self.send(client, result)
            elif msg_type == DataClientRequireCode.FINISH:
                break
            else:
                logger.error(f"No support recv type {msg_type}")
        self.running = False
        logger.log(LogLevel.SYSTEM, f"Transfer thread {self.addr} end ...")

    def send(self, client, buffers):
        msg = [client]
        if isinstance(buffers, List):
            msg.extend(buffers)
        else:
            msg.append(buffers)
        self.socket.send_multipart(msg)

    def _query_all_loop(self, buffer: bytes) -> List:
        """Collect data from querying all loops

        Args:
            buffer (bytes): bytes of task id and channels

        Returns:
            List: Task status and data collection, the first place on the list is
                always the current task status. If the task is abnormal or no data is
                queried, the list only contains task status information
        """
        info = decode_msg(buffer, parse_json=True)
        return self.share.get_acq_data(**info)

    def _query_task_id(self, buffer: bytes):
        """Query the ID of the task after registration

        Args:
            buffer (bytes): Task require id.

        Returns:
            List[bytes]: If there is a task return task ID, otherwise return TaskStatusEnum.NOT_FOUND
        """
        require_id = decode_msg(buffer)
        task = self.share.get_task_from_require_id(require_id)
        if task:
            return encode_msg(task.task_id)
        else:
            logger.error(f"no find task RID({require_id})")
            return encode_msg(str(TransferTaskStatusEnum.TACKLE_FAIL))

    def _query_task_information(self, buffer: bytes):
        """Query the current information of the task

        Args:
            buffer (bytes): Task require id.

        Returns:
            List[bytes]: If there is a task return task information, otherwise return TaskStatusEnum.NOT_FOUND
        """
        require_id = decode_msg(buffer)
        task = self.share.get_task_from_require_id(require_id)
        if task:
            info = task.information(detail=True)
        else:
            logger.error(f"no find task RID({require_id})")
            info = {
                "state": TransferTaskStatusEnum.TACKLE_FAIL,
                "reason": "no find task",
            }
        return encode_msg(info)

    def _query_one_loop(self, buffer: bytes):
        """Collect data from querying one loops

        Args:
            buffer (bytes): bytes of task id and channels and loop index

        Returns:
            List: Task status and data collection, the first place on the list is
                always the current task status. If the task is abnormal or no data is
                queried, the list only contains task status information
        """
        info = decode_msg(buffer, parse_json=True)
        return self.share.get_acq_data(**info)

    def _query_state(self, buffer: bytes) -> bytes:
        """Query task state from share area.

        Args:
            buffer (bytes): bytes of task require id

        Returns:
            bytes: bytes of task state
        """
        require_id = decode_msg(buffer)
        state = self.share.get_task_state_from_require_id(require_id)
        return encode_msg(str(state))

    def _wait_acquisition(self, buffer: bytes) -> bytes:
        """Query acquisition start from share area.

        Args:
            buffer (bytes): bytes of task require id

        Returns:
            bytes: bytes of task state
        """
        require_id = decode_msg(buffer)
        state = self.share.check_acquisition_start(require_id)
        return encode_msg(str(state))

    def _clear_task(self, buffer: bytes) -> bytes:
        """Clear task which in share area.

        Args:
            buffer (bytes): bytes of task require id

        Returns:
            bytes: bytes of task clear state
        """
        require_id = decode_msg(buffer)
        task = self.share.pop_task_from_require_id(require_id)
        logger.debug(f"clear task RID({require_id}) | {task}")
        if task:
            state = TransferTaskStatusEnum.TACKLE_SUC
        else:
            state = TransferTaskStatusEnum.TACKLE_FAIL
        return encode_msg(str(state))

    def _clear_acq_data(self, buffer: bytes) -> bytes:
        """Clear acq data of task which in share area.

        Args:
            buffer (bytes): bytes of task require id

        Returns:
            List[bytes]: bytes of task clear acq data state
        """
        info = decode_msg(buffer, parse_json=True)
        is_suc = self.share.clear_acq_data(**info)
        state = (
            TransferTaskStatusEnum.TACKLE_SUC
            if is_suc
            else TransferTaskStatusEnum.TACKLE_FAIL
        )
        return encode_msg(str(state))

    def _register_task(self, *buffer: bytes):
        """Accept the task of registering as a monster

        Returns:
            TransferTaskStatusEnum: TransferTaskStatusEnum.TACKLE_SUC
        """
        program_pointer_buf, program_buf, info_buffer = buffer
        infos = decode_msg(info_buffer, parse_json=True)
        infos.update(
            dict(program_pointer_buf=program_pointer_buf, program_buf=program_buf)
        )
        result = CompileResult.from_dict(infos)
        self.share.monster_program_queue.append(result)
        return encode_msg(str(TransferTaskStatusEnum.TACKLE_SUC))

    def _monster_record_message(self, buffer: bytes):
        infos = decode_msg(buffer, parse_json=True)
        messages = ["Experiment Traceback Record:"]
        for k, v in infos.items():
            messages.append(f"{k}: {v}")
        logger.warning("\n".join(messages))


def start_transfer_socket(
    url: str, identity: str, name: str, share: ShareArea
) -> TransferSocket:
    """Start the Transfer basket thread

    Args:
        url (str): IPC address
        identity (str): Socket identifier
        name (str): Process number requested by the client
        share (ShareArea): shared resource

    Returns:
        TransferSocket: _description_
    """
    transfer = TransferSocket(url, identity, share, name)
    transfer.start()
    return transfer
