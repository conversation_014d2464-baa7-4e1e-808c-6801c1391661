# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:2
msgid "pyQCat.analysis.library.RamseyZampAnalysis"
msgstr ""

#: of pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis`"
msgstr ""

#: of pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis:1
msgid "An analysis class for Ramsey experiment with z-amp added."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:1
msgid "Initialize the analysis object."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._extract_result
#: pyQCat.analysis.top_analysis.TopAnalysis.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:4
msgid "pyQCat Experiments Data container class."
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:7
msgid "Whether the experiment contains sub-experiments."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.analysis.library.RamseyZampAnalysis.__init__>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`from_sub_analysis "
"<pyQCat.analysis.library.RamseyZampAnalysis.from_sub_analysis>`\\ "
"\\(x\\_data\\, sub\\_analysis\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:25:<autosummary>:1
msgid "This interface is used for composite experiment."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`run_analysis "
"<pyQCat.analysis.library.RamseyZampAnalysis.run_analysis>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:25:<autosummary>:1
msgid "Run analysis on experiment data."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`set_options "
"<pyQCat.analysis.library.RamseyZampAnalysis.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:25:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`show_results "
"<pyQCat.analysis.library.RamseyZampAnalysis.show_results>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:25:<autosummary>:1
msgid "Show analysis results"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RamseyZampAnalysis.rst:27
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_datas "
"<pyQCat.analysis.library.RamseyZampAnalysis.analysis_datas>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis datas."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`data_filter "
"<pyQCat.analysis.library.RamseyZampAnalysis.data_filter>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid "The parameters provided for data filter functions."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`drawer <pyQCat.analysis.library.RamseyZampAnalysis.drawer>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid "A short-cut for curve drawer instance."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_data "
"<pyQCat.analysis.library.RamseyZampAnalysis.experiment_data>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid "Return the experiment data for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`has_child "
"<pyQCat.analysis.library.RamseyZampAnalysis.has_child>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis has_child value."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.library.RamseyZampAnalysis.options>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis options for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.library.RamseyZampAnalysis.quality>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis data quality."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`results <pyQCat.analysis.library.RamseyZampAnalysis.results>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1:<autosummary>:1
msgid "Get the result datas."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1
msgid "Return the default analysis options."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:11
msgid "Core Options:"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:4
msgid ""
"factor : Fluctuation expansion factor, Default 1, no expansion stft_res: "
"fs (ndarray) : Array of sample frequencies."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:6
msgid ""
"t (ndarray) : Array of segment times. Zxx (ndarray) : STFT of `x`. By "
"default, the last axis of `Zxx`"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:8
msgid "corresponds to the segment times."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:9
msgid ""
"f (ndarray) :Array of sample frequencies. power (ndarray): half of length"
" of np.fft.fft fit data."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options
msgid "Return type"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:13
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._extract_result:1
msgid "Convert frequency from GHz to MHz and judge osc."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._extract_result:4
msgid "The basis for selecting data."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._initialize_canvas:1
msgid "Initialize matplotlib canvas."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._visualization:1
msgid "Draw raw data, fit data and stft data."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._outlier_detectation:1
msgid "Detect outliers. :param phase: phase sequence."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._outlier_detectation
msgid "Returns"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._outlier_detectation:4
msgid "True or False."
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_analysis "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.run_analysis>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_options "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_datas "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`data_filter "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`drawer <pyQCat.analysis.library.RamseyZampAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_data "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`has_child "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`options "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`quality "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`results "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_analysis "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.run_analysis>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_datas "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`data_filter "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid "The parameters provided for data filter funtions."
#~ msgstr ""

#~ msgid ":obj:`drawer <pyQCat.analysis.library.RamseyZampAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_data "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`has_child "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.library.RamseyZampAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.library.RamseyZampAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":obj:`results <pyQCat.analysis.library.RamseyZampAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "Quality of fit outcome."
#~ msgstr ""

