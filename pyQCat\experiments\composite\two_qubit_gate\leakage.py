# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/27
# __author:       <PERSON>, <PERSON><PERSON><PERSON>

"""
Normal Leakage, LeakageNGate Experiment.
"""

import numpy as np
from scipy.interpolate import interp1d

from ....analysis import AnalysisResult, LeakageAmpAnalysis, LeakageNumAnalysis
from ....errors import Experiment<PERSON>ieldError
from ....parameters import options_wrapper, save_scan_map
from ....structures import MetaD<PERSON>, Options
from ....tools import cz_flow_options_adapter
from ....types import ExperimentRunMode, QualityDescribe, StandardContext
from ...composite_experiment import CompositeExperiment
from ...single import LeakageOnce


@options_wrapper
class LeakageAmp(CompositeExperiment):
    _sub_experiment_class = LeakageOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("scan_name", str)
        options.set_validator("z_amp_list", list)
        options.set_validator("scope", dict)
        options.set_validator("scope_detune", bool)

        options.scope = {"l": 30, "r": 30, "p": 31}
        options.scan_name = None
        options.z_amp_list = None
        options.scope_detune = False
        options.support_context = [StandardContext.CGC]

        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options."""
        options = super()._default_analysis_options()
        options.set_validator("use_qc", bool)
        options.set_validator("point", int)
        options.set_validator("leak_threshold", float)

        options.select_key = None
        options.is_plot = True
        options.data_key = None
        options.is_amend = True
        options.use_qc = True
        options.point = 40

        options.leak_threshold = 0.3
        options.f11 = None

        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        cz_flow_options_adapter(self)
        eop = self.experiment_options
        pair = self.qubit_pair
        params = pair.metadata.std.get(eop.child_exp_options.label).params
        fid_list = []
        for dis in self.discriminator:
            fid = dis.fidelity
            fid_list.append(fid[1])
        F11 = fid_list[0] * fid_list[1]
        self.set_analysis_options(f11=F11)
        self.set_analysis_options(result_name=self.qubit_pair.name)
        self.child_experiment.set_analysis_options(use_qc=self.analysis_options.use_qc, cut_index=True)

        if (
            (eop.freq_list is None or len(eop.freq_list) == 0)
            and eop.z_amp_list is None
            and not self.run_options.scan_map
        ):
            left = eop.scope.get("l")
            right = eop.scope.get("r")
            point = eop.scope.get("p")

            if left + right < 1:
                v = params[eop.scan_name]["amp"]
                sweep_list = np.linspace(v - left, v + right, point).tolist()
                self.experiment_options.z_amp_list = sweep_list
            else:
                v = params[eop.scan_name]["freq"]
                if v is None:
                    raise ExperimentFieldError(self, f"{eop.scan_name} default freq is None!")
                sweep_list = np.round(np.linspace(v - left, v + right, point), 3)
                self.experiment_options.freq_list = sweep_list.tolist()
                self._check_options()

        self.set_run_options(x_data=self.run_options.x_data, analysis_class=LeakageAmpAnalysis)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "x_scan": self.experiment_options.scan_name,
            "y_scan": self.experiment_options.child_exp_options.scan_name,
            "x_data": self.experiment_options.get("z_amp_list"),
            "y_data": self._experiments[0].analysis.experiment_data.metadata.process_meta.get("x_data"),
            "child_scan_freq": self._experiments[0].experiment_options.get("freq_list"),
            "scan_freq": self.experiment_options.freq_list,
            "scope_detune": self.experiment_options.scope_detune,
        }
        return metadata

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        save_scan_map(self)
        qh_fit = self.analysis.results.qh_fit.value
        qc_fit = self.analysis.results.qc_fit.value
        qh_max = self.analysis.results.qh_max.value
        qc_max = self.analysis.results.qc_max.value
        if isinstance(self.discriminator, list) and qc_fit:
            name = f"qc-qh_fit"
            self.file.save_data(qc_fit, qh_fit, name=name)
            name = f"qc-qh_max"
            self.file.save_data(qc_max, qh_max, name=name)

        if self.experiment_options.scope_detune is True:
            fit_detune = list(self.analysis.results.qh_fit.value)
            max_detune = list(self.analysis.results.qh_max.value)
            fit_qc = list(self.analysis.results.qc_fit.value)
            max_qc = list(self.analysis.results.qc_max.value)

            if len(fit_detune) > 6 and len(fit_detune) / len(self.run_options.x_data) > 2 / 3:
                func = interp1d(fit_detune, fit_qc, kind="cubic")
                fit_detune = np.linspace(fit_detune[0], fit_detune[-1], self.analysis_options.point)
                self.analysis.results.qc_fit.value = func(fit_detune).tolist()
                self.analysis.results.qh_fit.value = fit_detune.tolist()
                self.analysis.results["fit_detune"] = AnalysisResult(
                    name="fit_detune",
                    value=fit_detune.tolist(),
                    extra={
                        "path": "QubitPair.metadata.std.process.min_leakage_point.fit.detune",
                        "name": self.qubit_pair.name,
                    },
                )
            else:
                # do not use fit when number of effective points is less than 10 (temp)
                self.analysis.results.pop("qc_fit", None)

            # always makes sure the points save to DB are of the same length
            func = interp1d(max_detune, max_qc, kind="cubic")
            max_detune = np.linspace(max_detune[0], max_detune[-1], self.analysis_options.point)
            self.analysis.results.qc_max.value = func(max_detune).tolist()
            self.analysis.results.qh_max.value = max_detune.tolist()

            self.analysis.results["max_detune"] = AnalysisResult(
                name="max_detune",
                value=max_detune.tolist(),
                extra={
                    "path": "QubitPair.metadata.std.process.min_leakage_point.max.detune",
                    "name": self.qubit_pair.name,
                },
            )
            self.analysis.results.pop("qh_fit", None)
            self.analysis.results.pop("qh_max", None)
        else:
            func_fit = interp1d(qh_fit, qc_fit, kind="cubic")
            func_max = interp1d(qh_max, qc_max, kind="cubic")
            new_qh_fit = np.linspace(qh_fit[0], qh_fit[-1], self.analysis_options.point)
            new_qh_max = np.linspace(qh_max[0], qh_max[-1], self.analysis_options.point)
            self.analysis.results.qh_fit.value = new_qh_fit.tolist()
            self.analysis.results.qh_max.value = new_qh_max.tolist()
            self.analysis.results.qc_fit.value = func_fit(new_qh_fit).tolist()
            self.analysis.results.qc_max.value = func_max(new_qh_max).tolist()

        for key, result in self.analysis.results.items():
            if key == "qc_fit":
                result.extra["path"] = "QubitPair.metadata.std.process.min_leakage_point.fit.qc"
            elif key == "qh_fit":
                result.extra["path"] = "QubitPair.metadata.std.process.min_leakage_point.fit.qh"
            elif key == "qc_max":
                result.extra["path"] = "QubitPair.metadata.std.process.min_leakage_point.max.qc"
            elif key == "qh_max":
                result.extra["path"] = "QubitPair.metadata.std.process.min_leakage_point.max.qh"

    def _setup_child_experiment(self, leak_once_exp: LeakageOnce, index: int, freq: float):
        leak_once_exp.run_options.index = index
        label = self.experiment_options.child_exp_options.label
        scan_map = self.run_options.scan_map
        max_iter_count = self.run_options.max_iter_count
        gate_params = leak_once_exp.qubit_pair.metadata.std.get(label).params

        tail_name = "scan"
        for unit, collects in scan_map.items():
            if "freq" in collects:
                freq = collects.get("freq")[index]
                gate_params[unit]["freq"] = freq
                tail_name += f" {unit} {freq}MHz"
            else:
                amp = collects.get("amp")[index]
                gate_params[unit]["amp"] = amp
                gate_params[unit]["freq"] = None
                tail_name += f" {unit} {amp}V"
        leak_once_exp.set_parent_file(self, tail_name, index, max_iter_count)

        self._check_simulator_data(leak_once_exp, index)

    def _handle_child_result(self, leak_once_exp: LeakageOnce):
        results = leak_once_exp.analysis.results
        max_p11 = self._get_p_max(leak_once_exp)
        fit_idx = None
        if leak_once_exp.analysis.quality:
            if leak_once_exp.analysis.quality.descriptor == QualityDescribe.perfect:
                fit_idx = leak_once_exp.run_options.index

        leak_once_exp.analysis.provide_for_parent = {
            "fit_zc": results.fit_zc.value,
            "max_zc": results.max_zc.value,
            "p11": max_p11,
            "fit_idx": fit_idx,
        }

    def _get_p_max(self, exp):
        y_data = exp.analysis.experiment_data.y_data
        p11 = y_data.get("P11", None)
        max_p11 = np.max(p11)
        if exp.experiment_options.is_amend:
            self.set_analysis_options(is_amend=True)
        else:
            self.set_analysis_options(is_amend=False)
        return max_p11


class LeakageNum(CompositeExperiment):
    _sub_experiment_class = LeakageOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()
        options.set_validator("cz_num_list", list)

        options.support_context = [StandardContext.CGC]
        options.cz_num_list = None

        options.run_mode = ExperimentRunMode.async_mode

        return options

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.cz_num_list,
            analysis_class=LeakageNumAnalysis,
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "y_scan": self.experiment_options.child_exp_options.scan_name,
            "y_data": self._experiments[0].experiment_options.get("z_amp_list"),
            "child_scan_freq": self._experiments[0].experiment_options.get("freq_list"),
        }
        return metadata

    def _setup_child_experiment(self, leak_once_exp: LeakageOnce, index: int, cz_num: int):
        leak_once_exp.run_options.index = index
        total = len(self.run_options.x_data)
        c_eop = leak_once_exp.experiment_options
        tail_name = f"cz_num={cz_num}-scan {c_eop.scan_name}"
        leak_once_exp.set_parent_file(self, tail_name, index, total)
        leak_once_exp.set_experiment_options(cz_num=cz_num)
        self._check_simulator_data(leak_once_exp, index)
