# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/09/30
# __author:       <PERSON>

"""
Z Crosstalk new case series normal experiments.
"""

import json
import os
import re
from typing import Dict, List, Type

import numpy as np
import pandas as pd

from ....analysis.algorithms.iqprobability import IQdiscriminator
from ....analysis.library import (
    BzRamseyAnalysis,
    CrosstalkOnceAnalysis,
    RamseyAnalysis,
    RamseyZampAnalysis,
    XYCrossRwAnalysis,
)
from ....analysis.top_analysis import TopAnalysis
from ....errors import ExperimentOptionsError
from ....log import pyqlog
from ....parameters import options_wrapper
from ....pulse.base_pulse import PulseComponent
from ....pulse.pulse_function import half_pi_pulse, pi_pulse, zero_pulse
from ....pulse.pulse_lib import Constant, Linear, SquareEnvelop
from ....qubit import NAME_PATTERN, Qubit
from ....structures import ExperimentData, MetaData, Options
from ....tools.savefile import BaseFile, LocalFile
from ....tools.utilities import format_results, freq_to_amp, qarange
from ....types import ExperimentDocStatus, QualityDescribe
from ...top_experiment import TopExperiment


class SpiltMixin:
    """Some split operate."""

    @staticmethod
    def split_exp_data(
        exp_data: "ExperimentData",
        ms_names: List[str],
        data_type: str = "I_Q",
    ) -> Dict[str, "ExperimentData"]:
        """Split ExperimentData."""
        ms_ed_dict = {}
        x_data = exp_data.x_data
        y_data = exp_data.y_data

        for idx, s_name in enumerate(ms_names):
            if data_type == "amp_phase":
                sy_data = {}
                for key, value in y_data.items():
                    if isinstance(value[0], list):
                        sy_data.update({key: value[:, idx]})
                    else:
                        # Just one.
                        sy_data.update({key: value})
            elif data_type == "I_Q":
                p0_indexes = []
                p1_indexes = []
                for key, value in y_data.items():
                    if key[idx + 1] == "0":
                        p0_indexes.append(value)
                    else:
                        p1_indexes.append(value)
                sy_data = {
                    "P0": list(map(sum, zip(*p0_indexes))),
                    "P1": list(map(sum, zip(*p1_indexes))),
                }
            else:
                sy_data = {
                    "Error_0": np.zeros_like(x_data),
                    "Error_1": np.zeros_like(x_data),
                }

            meta_dict = MetaData(
                name=exp_data.metadata.name,
                qubits=exp_data.metadata.qubits,
                couplers=exp_data.metadata.couplers,
                save_location=exp_data.metadata.save_location,
            )
            meta_dict.draw_meta = {"measure": s_name}
            meta_dict.process_meta = {}
            meta_dict.draw_meta.update(exp_data.metadata.draw_meta)
            meta_dict.process_meta.update(exp_data.metadata.process_meta)
            s_exp_data = ExperimentData(
                x_data=x_data,
                y_data=sy_data,
                experiment_id=exp_data.experiment_id,
                metadata=meta_dict,
            )
            ms_ed_dict.update({s_name: s_exp_data})
        return ms_ed_dict

    @staticmethod
    def save_once_exp_data(
        exp_data: "ExperimentData",
        file_obj: "BaseFile",
        mark: str = "",
        fmt: str = "%.6f",
    ):
        """Save ExperimentData object data."""
        x_data = exp_data.x_data
        y_data = exp_data.y_data
        data_keys = list(y_data.keys())

        new_data = None
        for key, value in y_data.items():
            if new_data is None:
                new_data = np.column_stack((value,))
            else:
                new_data = np.column_stack((new_data, value))
        if x_data is not None and new_data is not None:
            new_data = np.column_stack((np.asarray(x_data), new_data))

        data_keys_info = "_".join(data_keys)
        if len(data_keys_info) > 10:
            data_keys_info = "probability"

        name_info = f"{mark}({data_keys_info.lower()})"
        file_obj.save_data(new_data.T, name=name_info, fmt=fmt)

    @staticmethod
    def run_once_analysis(
        exp_data: "ExperimentData",
        analysis_class: Type["TopAnalysis"],
        analysis_options: "Options",
        has_child: bool = False,
    ) -> "TopAnalysis":
        """Once analysis run."""
        analysis_obj = analysis_class(exp_data, has_child=has_child)
        analysis_obj.set_options(**analysis_options)
        analysis_obj.run_analysis()

        # if show_result is True:
        #     pyqlog.log("RESULT", format_results(analysis_obj))
        return analysis_obj

    @staticmethod
    def save_once_analysis_figure(
        analysis_obj: "TopAnalysis",
        file_obj: "BaseFile",
        mark: str = "",
        record_text: bool = True,
    ):
        """Save analysis object plot."""
        if analysis_obj.options.get("is_plot") is True:
            if hasattr(analysis_obj, "drawer") and getattr(analysis_obj, "drawer"):
                drawer = analysis_obj.drawer
                png_name = f"{mark}"
                if isinstance(drawer.figure, list):
                    for idx, fig in enumerate(drawer.figure):
                        file_obj.save_figure(
                            fig, f"{png_name}_{idx}_(result)", close=True
                        )
                else:
                    file_obj.save_figure(
                        drawer.figure, f"{png_name}(result)", close=True
                    )

                if record_text is True:
                    title_text = f"Title: {drawer.options.title} \n\n"
                    file_obj.save_text(title_text, name="records")


class ZCrossOnceBase(TopExperiment):
    """Z Cross new schema series normal experiment base class."""

    def get_qubit_str(self):
        """Get qubit string."""
        bias_name = self.experiment_options.bias_name
        t_name_list = self.run_options.t_name_list

        length = len(t_name_list)
        if length > 1:
            string = f"B{bias_name}-T{t_name_list[0]}~{t_name_list[-1]}"
        elif length == 1:
            string = f"B{bias_name}-T{t_name_list[0]}"
        else:
            string = f"B{bias_name}-T"
        return string

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment options:
            bias_name (str): Bias Qubit/Coupler name.
            target_name_list (list): Target qubit name list.
            bz_amp (float): Set Bias qubit z_amp value.

        """
        options = super()._default_experiment_options()

        options.set_validator("bias_name", str)
        options.set_validator("target_name_list", list)
        options.set_validator("bz_amp", float)

        options.bias_name = ""
        options.target_name_list = []
        options.bz_amp = 0.0

        # TopExperiment options set.
        options.is_dynamic = 0
        options.enable_one_sweep = True
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.t_name_list = []
        options.t_xy_name_map = {}  # target and xy name map
        options.t_rd_name_map = {}  # target and readout name map
        options.bias_qubit = None  # Qubit or Coupler object
        options.qc_map = {}  # All Qubit or Coupler object map
        options.rd_qubit_map = {}  # Readout name and Qubit map
        options.rd_dcm_map = {}  # Readout name and IQdiscriminator map

        options.cross_z_amp_map = {}  # Note once cross z_amp
        options.extra_data_map = {}  # Note some extra data
        options.analysis_class = XYCrossRwAnalysis
        options.once_analysis_class = None  # Once target analysis class
        options.x_data = []

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        options.set_validator("n_multiple", float)
        options.set_validator("once_ana_options", dict)

        options.n_multiple = 100
        options.cross_coe_map = {}
        options.cross_trust_map = {}
        options.once_ana_options = {}

        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = MetaData(name=str(self), save_location=self.file.dirs)

        if self.run_options.custom_unit_describe:
            metadata.process_meta["custom_unit_describe"] = (
                self.run_options.custom_unit_describe
            )
        metadata.draw_meta = {
            "bias_name": self.experiment_options.bias_name,
            "bz_amp": self.experiment_options.bz_amp,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        bias_name = self.experiment_options.bias_name
        target_name_list = self.experiment_options.target_name_list
        is_dynamic = self.experiment_options.is_dynamic
        figsize = self.analysis_options.figsize
        bz_amp = self.experiment_options.bz_amp

        qc_map = {}
        qubit_map = {qubit.name: qubit for qubit in self.qubits}
        coupler_map = {qubit.name: qubit for qubit in self.couplers}
        qc_map.update(qubit_map)
        qc_map.update(coupler_map)

        q_names = list(qubit_map.keys())
        c_names = list(coupler_map.keys())
        all_names = list(qc_map.keys())
        q_pattern = re.compile(NAME_PATTERN.qubit)
        c_pattern = re.compile(NAME_PATTERN.coupler)

        if bias_name in all_names:
            bias_qubit = qc_map.get(bias_name)
        else:
            raise ExperimentOptionsError(
                self.label,
                msg=f"Please check, not found {bias_name} from qubits and couplers",
                key="bias_name",
                value=bias_name,
            )

        if target_name_list:
            t_name_list = []
            for t_name in target_name_list:
                if isinstance(t_name, int):
                    name = f"q{t_name}"
                    t_name_list.append(name)
                elif isinstance(t_name, str) and q_pattern.match(t_name):
                    t_name_list.append(t_name)
                elif isinstance(t_name, str) and c_pattern.match(t_name):
                    t_name_list.append(t_name)
                else:
                    pyqlog.warning(f"Maybe {t_name} format error!")
        else:
            # NOTE: default set just only support Qubit objects.
            t_name_list = [name for name in q_names if name != bias_name]
        t_name_list.sort(key=lambda x: int("".join(x.split("-"))[1:]))

        check_flag = True
        xy_name_list = []
        rd_name_list = []
        for t_name in t_name_list:
            if t_name in q_names:
                xy_name_list.append(t_name)
                rd_name_list.append(t_name)
            elif t_name in c_names:
                coupler_obj = coupler_map.get(t_name)
                dq_name = f"q{coupler_obj.drive_bit}"
                pq_name = f"q{coupler_obj.probe_bit}"
                if dq_name not in xy_name_list and pq_name not in rd_name_list:
                    # coupler target set xy case, same as ZZShift.
                    xy_name_list.append(pq_name)
                    rd_name_list.append(pq_name)
                else:
                    # TODO, set coupler target, when dq or pq are is exist in history.
                    pyqlog.warning(
                        f"when set coupler {t_name} target, "
                        f"xy_name: {dq_name} or rd_name: {pq_name} maybe is exist in history!"
                    )
                    xy_name_list.append(pq_name)
                    rd_name_list.append(pq_name)
            else:
                pyqlog.error(f"{t_name} not in qubits or couplers: {all_names}")
                if check_flag is True:
                    check_flag = False
        if check_flag is False:
            raise ExperimentOptionsError(
                self.label,
                msg=f"Check target names: {t_name_list} were not in exist environment!",
            )

        t_xy_name_map = dict(zip(t_name_list, xy_name_list))
        t_rd_name_map = dict(zip(t_name_list, rd_name_list))

        dcm_list = []
        if self.discriminator:
            data_type = "I_Q"
            if isinstance(self.discriminator, IQdiscriminator):
                dcm_list.append(self.discriminator)
            elif isinstance(self.discriminator, list):
                dcm_list.extend(self.discriminator)
        else:
            data_type = "amp_phase"

        rd_qubit_map = {}
        rd_dcm_map = {}
        multi_readout_channels = []
        for rd_name in rd_name_list:
            if rd_name:
                q_obj = qubit_map.get(rd_name)
                rd_qubit_map.update({rd_name: q_obj})
                multi_readout_channels.append(q_obj.readout_channel)
                if data_type == "I_Q":
                    for dcm_obj in dcm_list:
                        if dcm_obj.name == rd_name:
                            rd_dcm_map.update({rd_name: dcm_obj})

        # change analysis figsize
        base_len = 10
        q_length = len(t_name_list)
        if figsize in [[12, 8], (12, 8)] and q_length > base_len:
            ratio = q_length / base_len
            figsize = (int(12 * ratio), int(8 * ratio))
        pyqlog.debug(f"{self.label} analysis figsize: {figsize}")

        rd_length = len(rd_qubit_map)
        if rd_length > 1:
            is_dynamic = 0
            measure_qubits = list(rd_qubit_map.keys())
            if data_type == "I_Q":
                self.discriminator = list(rd_dcm_map.values())
        else:
            measure_qubits = []
            if data_type == "I_Q":
                self.discriminator = list(rd_dcm_map.values())[0]

        self.set_analysis_options(figsize=figsize)

        self.set_experiment_options(
            data_type=data_type,
            multi_readout_channels=multi_readout_channels,
            is_dynamic=is_dynamic,
            # save_label=f"bz_amp{bz_amp}",
        )
        self.set_run_options(
            t_name_list=t_name_list,
            t_xy_name_map=t_xy_name_map,
            t_rd_name_map=t_rd_name_map,
            bias_qubit=bias_qubit,
            qc_map=qc_map,
            rd_qubit_map=rd_qubit_map,
            rd_dcm_map=rd_dcm_map,
            measure_qubits=measure_qubits,
        )
        pyqlog.debug(
            f"target names: {self.run_options.t_name_list}, "
            f"measure_qubits: {self.run_options.measure_qubits}, "
            f"target & measure map: {self.run_options.t_rd_name_map}, "
            f"multi_readout_channels: {self.experiment_options.multi_readout_channels}, "
            f"data_type: {self.experiment_options.data_type}"
        )

    def _set_measure_pulses(self):
        """Set measure pulse."""
        rd_qubit_map = self.run_options.rd_qubit_map
        self._set_union_readout_pulse(qubits=list(rd_qubit_map.values()))

    def _extract_once_result(self, analysis_obj: "TopAnalysis") -> List:
        """Extract once analysis result.

        Args:
            analysis_obj: TopAnalysis object.

        Returns:
            list: [trust_flag, crz_amp, coef]
                trust_flag (bool): True or False, cross coefficient trust or not.
                crz_amp (float): Same as cross target z_amp.
                coef (float): Coefficient.

        """
        raise NotImplementedError

    def _split_once_analysis(self):
        """Split run once analysis."""
        bias_name = self.experiment_options.bias_name
        bz_amp = self.experiment_options.bz_amp
        data_type = self.experiment_options.data_type
        rd_qubit_map = self.run_options.rd_qubit_map
        t_rd_name_map = self.run_options.t_rd_name_map
        once_analysis_class = self.run_options.once_analysis_class
        once_ana_options = self.analysis_options.once_ana_options

        measure_names = list(rd_qubit_map.keys())
        rd_ed_dict = SpiltMixin.split_exp_data(
            self.experiment_data, measure_names, data_type
        )

        t_ed_dict = {}
        trust_map = {}
        crz_amp_map = {}
        coefficient_map = {}
        for t_name, rd_name in t_rd_name_map.items():
            exp_data = rd_ed_dict.get(rd_name)
            if isinstance(exp_data, ExperimentData):
                mark_info = f"split{os.sep}{t_name}-bz_amp{bz_amp}"
                SpiltMixin.save_once_exp_data(
                    exp_data, self.file, mark=mark_info, fmt="%.6f"
                )
                analysis_obj = SpiltMixin.run_once_analysis(
                    exp_data, once_analysis_class, once_ana_options
                )
                analysis_obj.options.result_name = t_name
                SpiltMixin.save_once_analysis_figure(
                    analysis_obj, self.file, mark=mark_info, record_text=True
                )
                once_res = self._extract_once_result(analysis_obj)
            else:
                pyqlog.warning(f"target {t_name} no ExperimentData: {exp_data}")
                once_res = [False, 0.0, 0.0]

            trust_flag, crz_amp, coef = once_res
            if coef is np.inf or coef is np.nan:
                trust_flag = False
                coef = 0.0

            trust_map.update({t_name: trust_flag})
            crz_amp_map.update({t_name: crz_amp})
            coefficient_map.update({t_name: coef})
            t_ed_dict.update({t_name: exp_data})

        b_label = f"{bz_amp}"
        cross_coe_map = {b_label: coefficient_map}
        cross_trust_map = {b_label: trust_map}
        cross_z_amp_map = {b_label: crz_amp_map}
        extra_data_map = {b_label: t_ed_dict}

        self.analysis_options.cross_coe_map = cross_coe_map
        self.analysis_options.cross_trust_map = cross_trust_map
        self.run_options.cross_z_amp_map = cross_z_amp_map
        self.run_options.extra_data_map = extra_data_map

    def _special_run_analysis(self):
        """Experiment special analysis run logic."""
        x_data = self.run_options.x_data
        analysis_class = self.run_options.analysis_class
        bz_amp = self.experiment_options.bz_amp

        mark = f"bz_amp{bz_amp}"

        self._data_collection(x_data)

        acq_status = self.data_acquisition.status
        if acq_status == ExperimentDocStatus.finished:
            self._split_once_analysis()

            # create analysis object.
            self.analysis = analysis_class(self.experiment_data)

            # update options.
            self.analysis.set_options(**self._analysis_options)

            # run analysis.
            self.analysis.run_analysis()
            self._save_curve_analysis_plot(save_mark=mark)

            self.analysis.update_result()

            # if self.experiment_options.show_result:
            #     pyqlog.log("RESULT", format_results(self.analysis))
        else:
            status_m = ExperimentDocStatus.status_map.get(acq_status)
            q_info = self.get_qubit_str()
            pyqlog.error(
                f"{self.label}({q_info}) id: {self.id}, status: {acq_status}, {status_m}"
            )

    def _alone_save_result(self):
        """Alone save some special result."""
        bias_name = self.experiment_options.bias_name
        bz_amp = self.experiment_options.bz_amp
        t_name_list = self.run_options.t_name_list
        cross_coe_map = self.analysis_options.cross_coe_map

        mark_info = f"{self}(B{bias_name}Z{bz_amp}_coefficient)"
        json_str = json.dumps(cross_coe_map, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=mark_info, prefix=".json")

        index_list = list(list(cross_coe_map.values())[0].keys())
        cross_coe_df = pd.DataFrame(data=cross_coe_map, index=index_list).T
        pyqlog.log("RESULT", f"{mark_info} {t_name_list}:\n{cross_coe_df}")
        if isinstance(self.file, LocalFile):
            if self.file._extension:
                ss = os.sep
                mark_info = f"{ss}child{ss}{self.file._extension} - {mark_info}"

            csv_name = "".join([self.file.dirs, mark_info, ".csv"])
            dir_path = os.path.dirname(csv_name)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
            cross_coe_df.to_csv(csv_name)

    def run(self):
        """Run logic."""
        super().run()
        self._special_run_analysis()
        self._alone_save_result()


class ZCrossZampOnce(ZCrossOnceBase):
    """Z Crosstalk, scan target z_amp list."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment options:
            tz_amp_list (list): Set target z_amp list.
            drive_type (str): Set target XY line pulse type.

        """
        options = super()._default_experiment_options()

        options.set_validator("tz_amp_list", list)
        options.set_validator("drive_type", ["Drag", "Square"])
        options.set_validator("pre_delay", float)
        options.set_validator("after_delay", float)
        options.set_validator("square_params", dict)

        options.tz_amp_list = qarange(-0.05, 0.05, 0.02)
        options.drive_type = "Drag"
        options.pre_delay = 5000
        options.after_delay = 200
        options.square_params = {
            "time": 5000,
            "offset": 15,
            "amp": 1.0,
            "detune": 0.0,
            "freq": 1050.0,
        }

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.once_analysis_class = CrosstalkOnceAnalysis
        options.xy_pulse_width = 5000

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        options.once_ana_options = {"quality_bounds": [0.8, 0.6, 0.5]}

        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta.update({"drive_type": self.experiment_options.drive_type})
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        tz_amp_list = self.experiment_options.tz_amp_list
        drive_type = self.experiment_options.drive_type
        pre_delay = self.experiment_options.pre_delay
        after_delay = self.experiment_options.after_delay
        square_params = self.experiment_options.square_params

        if drive_type == "Drag":
            xy_wave = self.qubits[0].XYwave
            mid_width = xy_wave.time + 2 * xy_wave.offset
        else:
            mid_width = square_params.get("time")
        xy_pulse_width = pre_delay + mid_width + after_delay

        self.set_run_options(x_data=tz_amp_list, xy_pulse_width=xy_pulse_width)

    def _get_xy_pulse(self, qubit: Qubit) -> PulseComponent:
        """Get once Qubit XY pulse."""
        drive_type = self.experiment_options.drive_type
        pre_delay = self.experiment_options.pre_delay
        after_delay = self.experiment_options.after_delay
        square_params = self.experiment_options.square_params

        if drive_type == "Drag":
            drive_pulse = pi_pulse(qubit)
        elif drive_type == "Square":
            pulse_params = square_params.copy()
            pulse_params.update({"freq": qubit.XYwave.baseband_freq})
            drive_pulse = SquareEnvelop(**pulse_params)
        else:
            # Now no use.
            xy_time = qubit.XYwave.time + 2 * qubit.XYwave.offset
            drive_pulse = Constant(xy_time, 0.0, name="XY")

        pre_pulse = Constant(pre_delay, 0.0, name="XY")
        after_pulse = Constant(after_delay, 0.0, name="XY")
        total_pulse = pre_pulse() + drive_pulse() + after_pulse()
        return total_pulse

    def _set_xy_pulses(self):
        """Set XY pulses."""
        tz_amp_list = self.experiment_options.tz_amp_list
        t_xy_name_map = self.run_options.t_xy_name_map
        t_rd_name_map = self.run_options.t_rd_name_map
        xy_name_list = list(t_xy_name_map.values())
        rd_name_list = list(t_rd_name_map.values())

        length = len(tz_amp_list)
        width_list = []
        for qubit in self.qubits:
            q_name = qubit.name
            if width_list:
                pulse_width = max(width_list)
            else:
                pulse_width = self.run_options.xy_pulse_width

            if q_name in xy_name_list:
                pulse_list = [self._get_xy_pulse(qubit) for i in range(length)]
                width_list.append(pulse_list[0].width)
            elif q_name in rd_name_list:
                pulse_list = [
                    Constant(pulse_width, 0.0, name="XY")() for i in range(length)
                ]
            else:
                pulse_list = [
                    Constant(pulse_width, 0.0, name="XY")() for i in range(length)
                ]
            self.play_pulse("XY", qubit, pulse_list)

        if width_list:
            xy_pulse_width = max(width_list)
            self.run_options.xy_pulse_width = xy_pulse_width

    def _set_z_pulses(self):
        """Set Z pulse."""
        bz_amp = self.experiment_options.bz_amp
        tz_amp_list = self.experiment_options.tz_amp_list
        bias_qubit = self.run_options.bias_qubit
        qc_map = self.run_options.qc_map
        t_name_list = self.run_options.t_name_list
        xy_pulse_width = self.run_options.xy_pulse_width

        length = len(tz_amp_list)
        bz_pulses = [
            Constant(xy_pulse_width, bz_amp, name="Z")() for i in range(length)
        ]
        self.play_pulse("Z", bias_qubit, bz_pulses)

        for qc_name, qc_obj in qc_map.items():
            if qc_name in t_name_list and qc_name != bias_qubit.name:
                tz_pulses = []
                for tz_amp in tz_amp_list:
                    pulse_obj = Constant(xy_pulse_width, tz_amp, name="Z")()
                    tz_pulses.append(pulse_obj)
                self.play_pulse("Z", qc_obj, tz_pulses)

    def _extract_once_result(self, analysis_obj: "TopAnalysis") -> List:
        """Extract once analysis result."""
        bz_amp = self.experiment_options.bz_amp
        quality_obj = analysis_obj.quality
        t_name = analysis_obj.options.result_name
        if quality_obj and quality_obj.descriptor in [
            QualityDescribe.perfect,
            QualityDescribe.normal,
        ]:
            if "t_offset" in analysis_obj.results:
                crz_amp = analysis_obj.results.t_offset.value
            else:
                crz_amp = analysis_obj.results.b.value
            trust_flag = True
        else:
            pyqlog.warning(
                f"once {t_name} analysis quality: {quality_obj}, "
                f"use max index select best_ac value."
            )
            analysis_data = list(analysis_obj.analysis_datas.values())[0]
            x_arr = analysis_data.x
            y_arr = analysis_data.y
            crz_amp = x_arr[np.argmax(y_arr)]
            trust_flag = False

        coef = round(crz_amp / bz_amp, 6)
        return [trust_flag, crz_amp, coef]


@options_wrapper
class ZCrossDelayOnce(ZCrossOnceBase):
    """Z Crosstalk, scan target delay list."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment options:
            delays (list): Set target delay list.
            fringe (float): Set target ramsey fringe value.
            z_amp (float): Set target Z line amp.

        """
        options = super()._default_experiment_options()

        options.set_validator("delays", list)
        options.set_validator("fringe", float)
        options.set_validator("z_amp", float)

        options.delays = qarange(0, 200, 5)
        options.fringe = 50  # MHz
        options.z_amp = None

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.drag_width = 30

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        options.once_ana_options = {
            "factor": 3.5,
            "quality_bounds": [0.8, 0.6, 0.5],
        }

        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta["fringe"] = self.experiment_options.fringe

        z_amp = self.experiment_options.z_amp
        if z_amp is not None:
            metadata.draw_meta["z_amp"] = z_amp
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        delays = self.experiment_options.delays

        if self.experiment_options.z_amp is not None:
            once_analysis_class = RamseyZampAnalysis
        else:
            once_analysis_class = RamseyAnalysis
        self.set_run_options(once_analysis_class=once_analysis_class, x_data=delays)

    @staticmethod
    def _get_ramsey_xy_pulses(qubit, delays: List[float], fringe: float):
        """Get XY line ramsey pulses."""
        xy_pulse_list = []
        for delay in delays:
            front_drag = half_pi_pulse(qubit)
            center_delay = Constant(delay, 0, "XY")
            rear_drag = half_pi_pulse(qubit)

            r_phase = 2 * np.pi * fringe * 1e6 / 1e9 * delay
            ramsey_pulse = front_drag() + center_delay() + rear_drag(phase=r_phase)
            ramsey_pulse.bit = qubit.name
            ramsey_pulse.sweep = "sweep delay"
            xy_pulse_list.append(ramsey_pulse)

        return xy_pulse_list

    @staticmethod
    def _get_zero_xy_pulses(qubit, delays: List[float]):
        """Get XY line zero pulses."""
        xy_pulse_list = []
        for delay in delays:
            front_zero = zero_pulse(qubit)
            center_delay = Constant(delay, 0, "XY")
            rear_zero = zero_pulse(qubit)

            ramsey_pulse = front_zero() + center_delay() + rear_zero()
            ramsey_pulse.bit = qubit.name
            xy_pulse_list.append(ramsey_pulse)

        return xy_pulse_list

    @staticmethod
    def _get_z_pulses(qubit, delays: List[float], z_amp: float, drag_width: float):
        """Get Z line pulse list."""
        z_pulse_list = []
        for delay in delays:
            front_constant = Constant(drag_width, 0)
            center_delay = Constant(delay, z_amp)
            rear_constant = Constant(drag_width, 0)
            ramsey_z_pulse = front_constant() + center_delay() + rear_constant()
            ramsey_z_pulse.bit = qubit.name
            ramsey_z_pulse.sweep = "sweep delay"
            z_pulse_list.append(ramsey_z_pulse)

        return z_pulse_list

    def _set_xy_pulses(self):
        """Set XY pulses."""
        delays = self.experiment_options.delays
        fringe = self.experiment_options.fringe
        t_xy_name_map = self.run_options.t_xy_name_map
        t_rd_name_map = self.run_options.t_rd_name_map
        xy_name_list = list(t_xy_name_map.values())
        rd_name_list = list(t_rd_name_map.values())

        drag_width_list = []
        for qubit in self.qubits:
            q_name = qubit.name
            if q_name in xy_name_list:
                zero_pulse_obj = zero_pulse(qubit)
                drag_width_list.append(zero_pulse_obj.width)
                pulse_list = self._get_ramsey_xy_pulses(qubit, delays, fringe)
            elif q_name in rd_name_list:
                pulse_list = self._get_zero_xy_pulses(qubit, delays)
            else:
                pulse_list = self._get_zero_xy_pulses(qubit, delays)
            self.play_pulse("XY", qubit, pulse_list)

        if drag_width_list:
            drag_width = max(drag_width_list)
            self.run_options.drag_width = drag_width

    def _set_z_pulses(self):
        """Set Z pulse."""
        bz_amp = self.experiment_options.bz_amp
        delays = self.experiment_options.delays
        z_amp = self.experiment_options.z_amp
        bias_qubit = self.run_options.bias_qubit
        qc_map = self.run_options.qc_map
        t_name_list = self.run_options.t_name_list
        drag_width = self.run_options.drag_width

        bz_pulses = self._get_z_pulses(bias_qubit, delays, bz_amp, drag_width)
        self.play_pulse("Z", bias_qubit, bz_pulses)

        z_amp = z_amp if isinstance(z_amp, (int, float)) else 0.0
        for qc_name, qc_obj in qc_map.items():
            if qc_name in t_name_list and qc_name != bias_qubit.name:
                tz_pulses = self._get_z_pulses(bias_qubit, delays, z_amp, drag_width)
                self.play_pulse("Z", qc_obj, tz_pulses)

    def _extract_once_result(self, analysis_obj: "TopAnalysis") -> List:
        """Extract once analysis result."""
        ac_branch = self.experiment_options.ac_branch
        bz_amp = self.experiment_options.bz_amp
        fringe = self.experiment_options.fringe
        qc_map = self.run_options.qc_map

        quality_obj = analysis_obj.quality
        osc_freq = analysis_obj.results.freq.value
        t_name = analysis_obj.options.result_name
        qc_obj = qc_map.get(t_name)

        if quality_obj and quality_obj.descriptor in [
            QualityDescribe.perfect,
            QualityDescribe.normal,
        ]:
            trust_flag = True
        else:
            pyqlog.warning(f"once {t_name} analysis quality: {quality_obj}")
            trust_flag = False

        # AIO 72bit, calculate frequency by oscillate frequency
        if fringe > 0:
            freq = qc_obj.drive_freq + fringe - osc_freq
        else:
            freq = qc_obj.drive_freq + fringe + osc_freq
        z_amp = freq_to_amp(qc_obj, freq, new_offset=0, branch=ac_branch)
        if np.isnan(z_amp):
            pyqlog.warning(
                f"{t_name} frequency {freq} MHz to amp result is nan! "
                f"Please check {t_name} ac_spectrum params or other!"
            )
            trust_flag = False
            crz_amp = 0.0
            coef = 0.0
        else:
            crz_amp = z_amp - qc_obj.idle_point
            coef = round(crz_amp / bz_amp, 4)

        return [trust_flag, crz_amp, coef]


class ZCrossDelayLinearOnce(ZCrossDelayOnce):
    """Z Crosstalk, scan target delay list, but bias Z pulse is Linear Pulse."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment options:
            rate (float): Set bias Z line amp change rate.

        """
        options = super()._default_experiment_options()
        options.set_validator("rate", float)

        options.rate = -0.001
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        # BzRamseyAnalysis options
        options.once_ana_options = {
            "acf": 100,  # AC coefficient, df / dv
            "quality_bounds": [0.95, 0.85, 0.75],
        }

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.once_analysis_class = BzRamseyAnalysis

        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta["rate"] = self.experiment_options.rate
        return metadata

    def _check_options(self):
        """Check Options."""
        super(ZCrossDelayOnce, self)._check_options()

        delays = self.experiment_options.delays
        bz_amp = self.experiment_options.bz_amp
        fringe = self.experiment_options.fringe
        rate = self.experiment_options.rate
        once_ana_options = self.analysis_options.once_ana_options

        once_ana_options.update(
            {
                "rate": rate,
                "bz_amp": bz_amp,
                "fringe": fringe,
            }
        )
        self.set_run_options(x_data=delays)

    @staticmethod
    def _get_bz_pulses(
        qubit, delays: List[float], z_amp: float, drag_width: float, rate: float
    ):
        """Get bias Z line pulse list."""
        z_pulse_list = []
        for delay in delays:
            front_constant = Constant(drag_width, 0)
            center_delay = Linear(delay, z_amp, rate)
            rear_constant = Constant(drag_width, 0)
            ramsey_z_pulse = front_constant() + center_delay() + rear_constant()
            ramsey_z_pulse.bit = qubit.name
            ramsey_z_pulse.sweep = "sweep delay"
            z_pulse_list.append(ramsey_z_pulse)

        return z_pulse_list

    def _set_z_pulses(self):
        """Set Z pulse."""
        bz_amp = self.experiment_options.bz_amp
        delays = self.experiment_options.delays
        z_amp = self.experiment_options.z_amp
        rate = self.experiment_options.rate
        bias_qubit = self.run_options.bias_qubit
        qc_map = self.run_options.qc_map
        t_name_list = self.run_options.t_name_list
        drag_width = self.run_options.drag_width

        bz_pulses = self._get_bz_pulses(bias_qubit, delays, bz_amp, drag_width, rate)
        self.play_pulse("Z", bias_qubit, bz_pulses)

        z_amp = z_amp if isinstance(z_amp, (int, float)) else 0.0
        for qc_name, qc_obj in qc_map.items():
            if qc_name in t_name_list and qc_name != bias_qubit.name:
                tz_pulses = self._get_z_pulses(bias_qubit, delays, z_amp, drag_width)
                self.play_pulse("Z", qc_obj, tz_pulses)

    def _extract_once_result(self, analysis_obj: "TopAnalysis") -> List:
        """Extract once analysis result."""
        bz_amp = self.experiment_options.bz_amp
        delays = self.experiment_options.delays
        rate = self.experiment_options.rate
        max_delay = max(delays)

        quality_obj = analysis_obj.quality
        coef = analysis_obj.results.cr.value
        t_name = analysis_obj.options.result_name

        crz_amp = round(coef * (bz_amp + rate * max_delay) / 2, 4)
        if quality_obj and quality_obj.descriptor in [
            QualityDescribe.perfect,
            QualityDescribe.normal,
        ]:
            trust_flag = True
        else:
            pyqlog.warning(f"once {t_name} analysis quality: {quality_obj}")
            trust_flag = False

        return [trust_flag, crz_amp, coef]
