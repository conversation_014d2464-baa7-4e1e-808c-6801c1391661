# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/12/05
# __author:       <PERSON> Fang

"""
Floquet test single qubit XYZ timing.
"""

from ...composite_experiment import CompositeExperiment
from ...single.single_gate.floquet_xyz_timing_once import FloquetXYZTimingOnce
from ....analysis.library_v2 import FloquetXYZTimingAnalysis
from ....log import pyqlog
from ....structures import MetaData, Options
from ....tools.utilities import qarange
from ....types import ExperimentRunMode


class FloquetXYZTiming(CompositeExperiment):
    """Experiment FloquetXYZTiming class."""

    _sub_experiment_class = FloquetXYZTimingOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options."""
        options = super()._default_experiment_options()

        options.set_validator("delay_list", list)

        options.delay_list = qarange(0, 80, 0.625)

        options.run_mode = ExperimentRunMode.async_mode
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options."""
        options = super()._default_analysis_options()

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default run options."""
        options = super()._default_run_options()

        options.x_data = []
        options.analysis_class = FloquetXYZTimingAnalysis
        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {}
        return metadata

    def _check_options(self):
        super()._check_options()
        delay_list = self.experiment_options.delay_list

        t_qubit = self.qubits[0]

        pyqlog.info(f"result_name: {t_qubit.name}")
        self.set_run_options(x_data=delay_list)
        self.set_analysis_options(result_name=t_qubit.name)

    def _setup_child_experiment(
        self, exp: "FloquetXYZTimingOnce", index: int, value: float
    ):
        """Set child_experiment some options."""
        flo_once_exp = exp
        flo_once_exp.run_options.index = index
        total = len(self.run_options.x_data)

        describe = f"delay={value}"
        flo_once_exp.set_parent_file(self, describe, index, total)
        flo_once_exp.set_experiment_options(delay=value)
        self._check_simulator_data(flo_once_exp, index)

    def _handle_child_result(self, exp: "FloquetXYZTimingOnce"):
        # TODO, will set target value.
        t_val = exp.experiment_options.delay
        exp.analysis.provide_for_parent.update({"target": t_val})
