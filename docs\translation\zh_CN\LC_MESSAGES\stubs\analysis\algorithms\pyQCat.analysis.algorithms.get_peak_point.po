# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.get_peak_point.rst:2
msgid "pyQCat.analysis.algorithms.get\\_peak\\_point"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:1
msgid "Find peaks and transform `pyQCat.structures.Point` object."
msgstr "寻峰并转化成 `pyQCat.structures.Point` 对象"

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:4
msgid "x-axis data"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:7
msgid "y-axis data"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:10
msgid ""
"Required minimal horizontal distance (>= 1) in samples between "
"neighbouring peaks. Smaller peaks are removed first until the condition "
"is fulfilled for all remaining peaks."
msgstr ""
"相邻峰之间的最小距离（>=1），先移除较小的峰，直到所有剩余峰的条件都满足为止"

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:15
msgid ""
"Required height of peaks. Either a number, ``None``, an array matching "
"`x` or a 2-element sequence of the former. The first element is always "
"interpreted as the  minimal and the second, if supplied, as the maximal "
"required height."
msgstr ""
"所需峰的高度，既可以为一个数值也可以是一个包含两个元素的序列。当为一个值时"
"表示低于height的信号都不考虑；当为一个序列时，第一个元素表示峰的最小高度，"
"第二个元素表示峰的最大高度。"

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:21
msgid ""
"Required prominence of peaks. Either a number, ``None``, an array "
"matching `x` or a 2-element sequence of the former. The first element is "
"always interpreted as the  minimal and the second, if supplied, as the "
"maximal required prominence."
msgstr ""
"峰突起程度的限制，既可以为一个数值也可以是一个包含两个元素的序列。当为一个"
"值时表示峰值突起程度的最小值；当为一个序列时，分别表示峰值突起的最小值和最大值限制。"

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:27
msgid ""
"Peak and valley identifier; default is 1, that is to find the peak; when "
"set to 0 or False, the value of the valley"
msgstr ""
"峰谷标志符；默认为 1，即寻找峰值；当设置为 0 或 False，寻谷值"

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point
msgid "Yields"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:31
msgid ""
"*Point* -- `pyQCat.structures.Point` object，contains X-axis and Y-axis "
"information."
msgstr ""
"`pyQCat.structures.Point` 对象，包含X和Y信息。"

#~ msgid "Find peaks and transform `Point` object."
#~ msgstr ""

#~ msgid "x-axis data, np.ndararray"
#~ msgstr ""

#~ msgid "y-axis data, np.ndararray"
#~ msgstr ""

#~ msgid "`find_peaks` args"
#~ msgstr ""

#~ msgid "find_peaks` args"
#~ msgstr ""

#~ msgid "int or bool, mark find peak or valley"
#~ msgstr ""

#~ msgid "Returns"
#~ msgstr ""

#~ msgid "Generator of `Point` object"
#~ msgstr ""

