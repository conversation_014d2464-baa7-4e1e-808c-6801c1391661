# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/04/10
# __author:       <PERSON><PERSON><PERSON>

from ....tools import qarange
from ...composite_experiment import CompositeExperiment, Options
from ...single.undetermine.z_check_exp import ZFluxCheck, StandardCurveAnalysis


class ZFluxCheckComposite(CompositeExperiment):
    _sub_experiment_class = ZFluxCheck

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("amp_list", list)

        options.amp_list = qarange(0, 0.1, 0.01)

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.plot_2d = True
        options.x_label = "amp (v)"
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        return options

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.amp_list,
            analysis_class=StandardCurveAnalysis,
        )

    def _setup_child_experiment(self, cs_exp: ZFluxCheck, index: int, flux: float):
        cs_exp.run_options.index = index
        amp_list = self.experiment_options.amp_list
        total = len(amp_list)
        describe = f"amp={flux}v"
        cs_exp.set_parent_file(self, describe, index, total)
        cs_exp.set_experiment_options(amp=flux)
        self._check_simulator_data(cs_exp, index)

    def _handle_child_result(self, cs_exp: ZFluxCheck):
        """"""
