﻿pyQCat.experiments.composite.T2Spectrum
=======================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: T2Spectrum

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~T2Spectrum.__init__
      ~T2Spectrum.component_experiment
      ~T2Spectrum.from_experiment_context
      ~T2Spectrum.get_qubit_str
      ~T2Spectrum.options_table
      ~T2Spectrum.run
      ~T2Spectrum.set_analysis_options
      ~T2Spectrum.set_experiment_options
      ~T2Spectrum.set_parent_file
      ~T2Spectrum.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~T2Spectrum.analysis
      ~T2Spectrum.analysis_options
      ~T2Spectrum.child_experiment
      ~T2Spectrum.experiment_options
      ~T2Spectrum.run_options
   
   