﻿pyQCat.experiments.single.APE
=============================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: APE

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~APE.__init__
      ~APE.acquire_pulse
      ~APE.cal_fidelity
      ~APE.experiment_info
      ~APE.from_experiment_context
      ~APE.get_qubit_str
      ~APE.jupyter_schedule
      ~APE.options_table
      ~APE.play_pulse
      ~APE.plot_schedule
      ~APE.run
      ~APE.set_analysis_options
      ~APE.set_experiment_options
      ~APE.set_multiple_IF
      ~APE.set_multiple_index
      ~APE.set_parent_file
      ~APE.set_run_options
      ~APE.set_sweep_order
      ~APE.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~APE.analysis
      ~APE.analysis_options
      ~APE.experiment_options
      ~APE.multi_sweep
      ~APE.one_sweep
      ~APE.run_options
   
   