{"cells": [{"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 超导量子芯片基础测控软件包（pyqcat-monster）使用指南\n", "\n", "\n", "在本教程中，我们将演示如何使用 pyqcat-monster 进行基础实验环境的搭建。在这里你可以了解到：\n", "\n", "- 配置文件的配置方式；\n", "- 实验上下文环境的搭建方法；\n", "- 用户注册及登陆方式；\n", "- 实验基础环境配置参数的检索方法；\n", "- 基础测控实验的离线调试方法；\n", "\n", "在此基础上，我们还提供了 [monster]() 的 API 文档，期待您的阅读。当您发现有错误或者需要优化的地方，欢迎您通过 [pyqcat社区]() 向我们反馈，您的意见是我们不断进步的动力！"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 配置文件的配置方式\n", "\n", "在配置实验环境之前，\n", "\n", "*需要注意的是，必须将 pyqcat-monster 加入当前环境目录，当然如果您的虚拟环境安装了 pyqcat-monster 包，可以忽略下面的操作。*"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\n"]}], "source": ["# 若用户未安装 pyqcat 包（monster），通过本地源码测试，需要执行下面操作\n", "# 若安装了 pyqcat 包（monster），跳过此步骤\n", "\n", "import os\n", "import sys\n", "\n", "\n", "package_root = \"../../pyqcat-monster\"\n", "\n", "print(os.path.abspath(package_root))\n", "\n", "sys.path.insert(0, package_root)"]}, {"attachments": {"cd3b014507228dbac43ef6febac4935.jpg": {"image/jpeg": "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"}}, "cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 配置文件参数说明\n", "\n", "在 monster 中我们将一些典型的配置参数改用配置文件的方式导入，方便您的修改和使用。我们罗列出了配置参数如下：\n", "\n", "![cd3b014507228dbac43ef6febac4935.jpg](attachment:cd3b014507228dbac43ef6febac4935.jpg)\n", "\n", "你可以在 `.conf` 文件中配置这些参数，并将它的路径传递给 [ExperimentContext]() 从而将您的配置与实验环境绑定。当然我们在项目内部均设置了默认配置参数，但是仅限本地调试学习。配置方式如下："]}, {"cell_type": "code", "execution_count": 2, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 15:02:16\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1m\n", "|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |        tcp://************:8088         |\n", "|    point_label     |              person_point              |\n", "|       sample       |               test_chip                |\n", "|      env_name      |                D00_1011                |\n", "|      version       |                 0.1.6                  |\n", "|=============================================================|\n", "\u001b[0m\n"]}, {"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': 'e03af2866b0107e81fcac64a0f09ba4a'},\n", " 'msg': 'success'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "\n", "from pyQCat.tools import qarange\n", "from pyQCat.context import ExperimentContext\n", "from pyQCat.invoker import Invoker\n", "from pyQCat.parameters import get_parameters\n", "\n", "\n", "%matplotlib inline\n", "pd.set_option('max_colwidth', None)\n", "\n", "conf_file = r'E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf'\n", "\n", "username = \"test_04\"\n", "password = \"123456\"\n", "\n", "context = ExperimentContext(conf_file)\n", "Invoker.verify_account(username=username, password=password)"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 查看上下文管理器中 config 信息\n", "\n", "查看上下文管理器对象 context 绑定的 config 信息"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>parameter</th>\n", "      <th>module</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>sample</td>\n", "      <td>system</td>\n", "      <td>test_chip</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>env_name</td>\n", "      <td>system</td>\n", "      <td>D00_1011</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>point_label</td>\n", "      <td>system</td>\n", "      <td>person_point</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>invoker_addr</td>\n", "      <td>system</td>\n", "      <td>tcp://************:8088</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>baseband_freq</td>\n", "      <td>system</td>\n", "      <td>566.667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>qaio_type</td>\n", "      <td>system</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>save_type</td>\n", "      <td>system</td>\n", "      <td>local</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>local_root</td>\n", "      <td>system</td>\n", "      <td>D:\\test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>config_path</td>\n", "      <td>system</td>\n", "      <td>E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>inst_host</td>\n", "      <td>mongo</td>\n", "      <td>127.0.0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>inst_port</td>\n", "      <td>mongo</td>\n", "      <td>27017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>s3_root</td>\n", "      <td>minio</td>\n", "      <td>***********:9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>s3_access_key</td>\n", "      <td>minio</td>\n", "      <td>super</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>s3_secret_key</td>\n", "      <td>minio</td>\n", "      <td>80138013</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        parameter  module  \\\n", "0          sample  system   \n", "1        env_name  system   \n", "2     point_label  system   \n", "3    invoker_addr  system   \n", "4        baseband_freq  system   \n", "5       qaio_type  system   \n", "6       save_type  system   \n", "7      local_root  system   \n", "8     config_path  system   \n", "9       inst_host   mongo   \n", "10      inst_port   mongo   \n", "11        s3_root   minio   \n", "12  s3_access_key   minio   \n", "13  s3_secret_key   minio   \n", "\n", "                                                        value  \n", "0                                                   test_chip  \n", "1                                                    D00_1011  \n", "2                                                person_point  \n", "3                                     tcp://************:8088  \n", "4                                                     566.667  \n", "5                                                           8  \n", "6                                                       local  \n", "7                                                     D:\\test  \n", "8   E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf  \n", "9                                                   127.0.0.1  \n", "10                                                      27017  \n", "11                                           ***********:9000  \n", "12                                                      super  \n", "13                                                   80138013  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(context.config_table(context.config))"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 实验基础参数配置\n", "\n", "测控实验的执行需要依赖下面几个重要的对象：\n", "\n", "- **inst**：一体机Instrument 对象\n", "- **qubits**：量子比特\n", "- **couplers**: 可调耦合样品中的耦合器\n", "- **compensates**: 与比特绑定的线路补偿器\n", "- **discriminations**: 各比特对应的 IQ 分类器\n", "- **working_dc**: 实验依赖的电压环境\n", "- **crosstalk**: 线路串扰补偿器\n", "\n", "下面我们会介绍这几个关键对象的配置和检索方式.\n"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["配置实验环境参数，下面以 Q0 为例，配置基础实验环境"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 15:02:25\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1mQubit_(bit=0) is already exist in current environment.\u001b[0m\n"]}, {"data": {"text/plain": ["<pyQCat.context.ExperimentContext at 0x119ad457288>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 加载实验初始化对象，比如 inst, qubit, coupler 等等\n", "\n", "\n", "q_name_list = [\"q0\"]\n", "c_name_list = [\"c0\"]\n", "\n", "environment_elements = [\"q0\", \"q1\", \"q2\", \"q3\", \"c0\", \"c1\", \"c2\"]\n", "\n", "\n", "# 加载一体机 Instrument 对象\n", "context.configure_inst()\n", "\n", "\n", "# 加载实验需要的 qubit 对象\n", "context.configure_qubits(q_name_list)\n", "\n", "\n", "# 若涉及 coupler 实验，加载需要的 coupler 对象\n", "# context.configure_couplers(c_name_list)\n", "\n", "\n", "# 设置compensate policy\n", "context.minimize_compensate() # 仅加载实验需要 qubit/coupler 的 compensate，比如这里只有 q0\n", "# context.maximize_compensate() # 加载 environment_elements 中所有元素的 compensate\n", "\n", "\n", "# 配置大环境，working_dc 获取，compensate 加载等等\n", "context.configure_environment(environment_elements)\n", "\n", "\n", "# 加载 ac、dc 串扰信息\n", "context.configure_crosstalk_dict()\n", "\n", "\n", "# 若实验需要用到判据，需要加载 dcm\n", "# name_list = [\"q0\"]\n", "# union = None # 若联合读取判据，比如, union = [0, 1] 表示 q0, q1 的联合读取\n", "# context.configure_dcm(name_list, union=None)\n", "\n", "context"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 实验环境参数检索\n", "\n", "为了方便进行检索查阅您的配置，我们提供了一系列的方法，展示如下：\n", "\n", "**检索实验环境**"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>parameter</th>\n", "      <th>object</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>config</td>\n", "      <td>E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>qubits</td>\n", "      <td>[Qubit_(bit=0)]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>couplers</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>discriminators</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>compensates</td>\n", "      <td>{Qubit_(bit=0): PulseCorrectionQ0}</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>working_dc</td>\n", "      <td>True</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>crosstalk_dict</td>\n", "      <td>True</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        parameter  \\\n", "0          config   \n", "1          qubits   \n", "2        couplers   \n", "3  discriminators   \n", "4     compensates   \n", "5      working_dc   \n", "6  crosstalk_dict   \n", "\n", "                                                                  object count  \n", "0  E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf        \n", "1                                                        [Qubit_(bit=0)]     1  \n", "2                                                                     []     0  \n", "3                                                                   None     0  \n", "4                                     {Qubit_(bit=0): PulseCorrectionQ0}     1  \n", "5                                                                   True        \n", "6                                                                   True        "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(context.context_table())"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["**检索工作 DC**"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'1': 0.744732,\n", " '2': 0.236088,\n", " '3': 0.203233,\n", " '4': 0.525287,\n", " '5': 0.0,\n", " '6': 0.0,\n", " '7': 0.0}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["context.working_dc"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["**检索线路配置信息**"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>x_delay</th>\n", "      <th>z_delay</th>\n", "      <th>z_compensate</th>\n", "      <th>distortion_width</th>\n", "      <th>distortion_data_len</th>\n", "      <th>distortion_ab</th>\n", "      <th>distortion_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>q0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>width</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  name  x_delay  z_delay  z_compensate  distortion_width  distortion_data_len  \\\n", "0   q0      0.0      0.0           0.0                 0                    0   \n", "\n", "  distortion_ab distortion_type  \n", "0            []           width  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(context.compensates_table(context.compensates.values()))"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["**检索串扰信息**"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th>q0</th>\n", "      <th>q1</th>\n", "      <th>q2</th>\n", "      <th>q3</th>\n", "      <th>c0</th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    q0   q1   q2   q3   c0   c1   c2\n", "0  1.0  0.0  0.0  0.0  0.0  0.0  0.0\n", "1  0.0  1.0  0.0  0.0  0.0  0.0  0.0\n", "2  0.0  0.0  1.0  0.0  0.0  0.0  0.0\n", "3  0.0  0.0  0.0  1.0  0.0  0.0  0.0\n", "4  0.0  0.0  0.0  0.0  1.0  0.0  0.0\n", "5  0.0  0.0  0.0  0.0  0.0  1.0  0.0\n", "6  0.0  0.0  0.0  0.0  0.0  0.0  1.0"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["crosstalk = context.configure_crosstalk_dict()\n", "pd.DataFrame(crosstalk['dc_crosstalk'], columns=[crosstalk['infos']])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th>q0</th>\n", "      <th>q1</th>\n", "      <th>q2</th>\n", "      <th>q3</th>\n", "      <th>c0</th>\n", "      <th>c1</th>\n", "      <th>c2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    q0   q1   q2   q3   c0   c1   c2\n", "0  1.0  0.0  0.0  0.0  0.0  0.0  0.0\n", "1  0.0  1.0  0.0  0.0  0.0  0.0  0.0\n", "2  0.0  0.0  1.0  0.0  0.0  0.0  0.0\n", "3  0.0  0.0  0.0  1.0  0.0  0.0  0.0\n", "4  0.0  0.0  0.0  0.0  1.0  0.0  0.0\n", "5  0.0  0.0  0.0  0.0  0.0  1.0  0.0\n", "6  0.0  0.0  0.0  0.0  0.0  0.0  1.0"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(crosstalk['ac_crosstalk'], columns=[crosstalk['infos']])"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 基础实验执行方式 \n", "\n", "我们以最基本的实验 **CavitySpecturm** 为例，腔能谱实验，用于粗测比特读取频率。\n", "创建实验并开始模拟器执行（当前仅编译实验包，不支持数据回传，仅限本地调试）"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["from pyQCat.experiments import CavityFreqSpectrum\n", "\n", "\n", "# 通过 from_experiment_context() 方法初始化实验\n", "exp = CavityFreqSpectrum.from_experiment_context(context)"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["我们可以直接通过实验 `.from_experiment_context(context)` 的方式创建实验，但是往往这是不够的，我们需要设置期望的入口参数，你可以调用下面的方法来进行可选参数的设置，关于实验选项的解释，您可以浏览 [测试记录](https://document.qpanda.cn/docs/16q8MwQyRlIXr9k7) 进行查阅。"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>experiment option</td>\n", "      <td>show_result</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_shape</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_data_path</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>experiment option</td>\n", "      <td>repeat</td>\n", "      <td>1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>experiment option</td>\n", "      <td>register_pulse_save</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_flag</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_save</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_measure</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_type</td>\n", "      <td>envelop</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_measure</td>\n", "      <td>150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_real</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_index</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>experiment option</td>\n", "      <td>save_label</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>experiment option</td>\n", "      <td>is_dynamic</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>experiment option</td>\n", "      <td>fidelity_matrix</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>experiment option</td>\n", "      <td>ac_prepare_time</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>experiment option</td>\n", "      <td>readout_point_model</td>\n", "      <td>&lt;class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>experiment option</td>\n", "      <td>idle_qubits</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>experiment option</td>\n", "      <td>fc_list</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>experiment option</td>\n", "      <td>points</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>experiment option</td>\n", "      <td>readout_power</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>experiment option</td>\n", "      <td>amp</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>experiment option</td>\n", "      <td>is_opt</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>experiment option</td>\n", "      <td>scope</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 type                   name  \\\n", "0   experiment option            show_result   \n", "1   experiment option        simulator_shape   \n", "2   experiment option    simulator_data_path   \n", "3   experiment option                 repeat   \n", "4   experiment option    register_pulse_save   \n", "5   experiment option          schedule_flag   \n", "6   experiment option          schedule_save   \n", "7   experiment option       schedule_measure   \n", "8   experiment option          schedule_type   \n", "9   experiment option  schedule_show_measure   \n", "10  experiment option     schedule_show_real   \n", "11  experiment option         schedule_index   \n", "12  experiment option             save_label   \n", "13  experiment option             is_dynamic   \n", "14  experiment option        fidelity_matrix   \n", "15  experiment option        ac_prepare_time   \n", "16  experiment option    readout_point_model   \n", "17  experiment option            idle_qubits   \n", "18  experiment option                fc_list   \n", "19  experiment option                 points   \n", "20  experiment option          readout_power   \n", "21  experiment option                    amp   \n", "22  experiment option                 is_opt   \n", "23  experiment option                  scope   \n", "\n", "                                               value  \n", "0                                               True  \n", "1                                               None  \n", "2                                               None  \n", "3                                               1000  \n", "4                                              False  \n", "5                                               True  \n", "6                                               True  \n", "7                                               True  \n", "8                                            envelop  \n", "9                                                150  \n", "10                                              True  \n", "11                                                -1  \n", "12                                              None  \n", "13                                                 1  \n", "14                                              None  \n", "15                                                 0  \n", "16  <class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'>  \n", "17                                                []  \n", "18                                              None  \n", "19                                                61  \n", "20                                              None  \n", "21                                              None  \n", "22                                             False  \n", "23                                                 3  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看 experiment_options\n", "\n", "pd.DataFrame(exp.options_table('experiment'))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>analysis option</td>\n", "      <td>is_plot</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>analysis option</td>\n", "      <td>figsize</td>\n", "      <td>(12, 8)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>analysis option</td>\n", "      <td>quality_bounds</td>\n", "      <td>[0.98, 0.95, 0.85]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              type            name               value\n", "0  analysis option         is_plot                True\n", "1  analysis option         figsize             (12, 8)\n", "2  analysis option  quality_bounds  [0.98, 0.95, 0.85]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看 analysis_options\n", "\n", "pd.DataFrame(exp.options_table('analysis'))"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 设置实验参数\n", "\n", "用户一般可以设置 experiment_options 和 analysis_options\n", "\n", "对于腔能谱实验，我们为其设置为优化模式（加上 X 门后进行腔频测试），读取功率设置为-30, 并启用模拟器模式，设置如下："]}, {"cell_type": "code", "execution_count": 12, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["\n", "# 设置 experiment_options\n", "exp.set_experiment_options(\n", "    is_opt=True,\n", "    simulator_data_path='../scripts/simulator/data/CavityFreqSpectrum/',\n", "    readout_power=-35\n", ")\n", "\n", "\n", "# 设置 analysis_options\n", "exp.set_analysis_options(\n", "    is_plot=True,\n", "    quality_bounds=[0.98, 0.95, 0.85]\n", ")"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 执行实验"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 15:03:37\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mCavityFreqSpectrum register success, id 634909c9befba3a002d92bca\u001b[0m\n", "\u001b[33m2022-10-14 15:03:37\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\test_chip\\CavityFreqSpectrum\\q0\\2022-10-14\\15.03.37\\\u001b[0m\n"]}, {"data": {"application/json": {"ascii": false, "bar_format": null, "colour": null, "elapsed": 0.013962745666503906, "initial": 0, "n": 0, "ncols": null, "nrows": 29, "postfix": null, "prefix": "", "rate": null, "total": 61, "unit": "it", "unit_divisor": 1000, "unit_scale": false}, "application/vnd.jupyter.widget-view+json": {"model_id": "00723f57afec4120978a0bf96d5048ee", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/61 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 15:03:39\u001b[0m | \u001b[32m RESULT \u001b[0m | \u001b[32mExperiment results as follow:\n", "==========================================================\n", "| name | describe |  value   | unit |      quality       | \n", "----------------------------------------------------------\n", "|  fr  |    fc    | 7380.301 | MHz  | R²=0.9975(perfect) | \n", "==========================================================\u001b[0m\n"]}], "source": ["exp.run()"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["**查阅时序图**"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["exp.jupyter_schedule()"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["**查阅结果图**"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA1kAAAI5CAYAAABTgvDdAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/NK7nSAAAACXBIWXMAAAsTAAALEwEAmpwYAACz5UlEQVR4nOzdd3hUZdrH8e9MKin0hBJK6CV0ooKggEhRWBBEpag0xboq6gquiyCKgr52sbACArpipyldIwqigvQmXRJ6S+/zvH+czCSBJCaQZELy+1xXrpycep95MuWep9mMMQYREREREREpFHZ3ByAiIiIiIlKaKMkSEREREREpREqyRERERERECpGSLBERERERkUKkJEtERERERKQQKckSEREREREpRJ7uDkBERDLFx8fz6quvMn/+fOrUqUNISAgVK1akXr161K5dm/79+1/yuceMGcOyZcvYuHEjQUFBABw8eJBKlSpRsWLFPI/99ttvefjhhzl06BDNmjWjR48eREdH8+233+Lv78+hQ4cuOa7cREZG8sQTT1C9enUAqlatit1u55lnnin0azlt2rSJtm3bFtn58+vXX39lypQp+Pr6UrVqVbZv386tt97Kww8/jIeHh2u/pUuXMnfuXGw2G61atWL8+PFujFpERFyMiIiUCCdOnDDNmzc35cuXN+vWrXOt3717t6lRo4b55ptvLuv8119/vfHw8DB79+51rbvhhhvMwYMH83X8XXfdZQAzfPhw17qTJ0+aa6+99rLiys21115rpkyZ4vp7zpw55sYbbyySaxljTFJSkmnatGmRnT+/Pv30U+Pp6Wnuv/9+17rNmzcbb29v079/f5Oenm6MMebgwYPG19fXREREmOPHjxvAzJ07111hi4hIFmouKCJSQowYMYKdO3fy73//m44dO7rWN2nShLfeeuuyz79o0SJ27dpFw4YNAZgwYQLff/99vo+32y9+ywgKCmLIkCGXHduFzp07x7p169iwYQMOhwOAu+++m+uuu67QrwXgcDgYM2YMu3fvLpLz51dkZCSjRo0iLS2Nf/3rX671rVu35qabbmLhwoW88cYbAMyaNYukpCSCg4OpVq0aAB988IE7whYRkQsoyRIRKQG2bt3K0qVLAbjlllsu2t6vXz9at24NwI8//sh9993HsGHDqFu3Lt988w0Ac+bMwWazYbPZWLhwIampqdx9993ceeedbNu2jcaNG9O4cWMOHTrE0qVLmT59OgDDhw/nueee47rrrnM1Ozt9+jR79uyhXr16rFy5MseYN27cyKFDhxgwYADnz5+nS5cu2Gw2Zs2axT333EOlSpU4evQo586dY/jw4Tz//PPccMMN7N27FwBjDJMnT6Zfv3488MADvPrqq67ksly5cvj7+/PNN9/QrVs3fv/9dwDuv/9+AF577TVsNhvXXnst9957LxUrVqRv377ExMS44ps+fTqPPvooffr0Yfbs2a71n332GbfeeivXXXcdt99+O0lJScydO5fPP/8cgN69ezN37lzXNbp27cqiRYsIDg7mzTffZMGCBdhsNkJDQ0lOTubWW2/FZrMxadIkAF5//XVsNhtXXXUVTz75JLVr16ZNmzasXbuWkSNHEhAQ4LqPC82cOZPExERq1apF/fr1s21r3749AG+//TYAv/zyCwDe3t6ufX7//XfS09NzPLeIiBQjd1eliYiIMe+++64BDGASExNz3S8uLs4EBASYcePGGWOMqVGjhgkKCnJt79y5swHMihUrjDHGTJo0yZw9e9YYYzUXBFzNA7t06ZLt7507dxqbzWZq1arlapL21FNPuc49fPjwbM0FR40ala2p4cSJEw1grrvuOjNhwgQTHBxsjh07Zm655RYzePBgY4wxAwYMMDfccIMxxpgXX3zRAObw4cMmJSXF+Pv7m6xvS2+++abrMQHMkCFDXPdy4MABA5igoCBz/Phx8/nnnxvAPPTQQ8YYYxYsWGDsdruJj483CxcuNJ6enmb//v1m69atxsPDw6xatcp88sknBjDz5s0zxhhTt27dbNc/ePCgAUz9+vXN+PHjTVhYmHn77beNw+EwgKlbt64xxpiZM2cawEycONEYY8z+/fsNYGrXrm3OnTtnXn31VQOYm2++2aSnp5t+/foZwOzZs+ei8v3HP/5hANO+ffuLtr3//vuux+Ls2bOmWbNm2crPue3EiRMXHSsiIsVLNVkiIiVAXFyca9nZPC4nnp6ehIeHExAQwNmzZ0lPT+f06dOu7Q899BAAM2bMwBiDzWajUqVKANhstjxjaNasGTfeeCORkZEsXbqUDRs2cMMNN1y038aNG7n22muZNWtWjudp27YtkydP5sSJE9jtdhYsWMCBAweYOnUqAQEBBAQEYIzhzTffpFKlStSpUwcvLy8qV66c7TyPPPIICxYsIDQ0FIBPP/2Ubt26kZ6e7rqX5s2bU61aNW688UYAFixYAFjN5ux2O2+99RZr166lS5cuHDt2jBkzZpCenk6dOnW45ZZbeOmll+jZs2eej8u5c+eYMmUK27dv5+GHH77ocbywGaXz7/r161OxYkXXfV111VXY7XZXeZw4ceKiayUnJwNWTd6FPD09s+2XW42VMSbP+xERkaKnJEtEpARo3ry5azmvkfp8fHxYsWIFXl5eTJw4EQ8Pj2wfqgcOHEhwcDALFizg448//tsE4kIPPPAAAO+99x6rVq1yJS9ZtW/fnp9//pnRo0fneI7GjRu7lg8ePAhYIwOOHz+euXPnsnDhQk6ePMmJEycICAhw7Zs1WTlx4gTbt2+nf//+7N69m6lTp+Lt7c2WLVtYvXr1RdesUKECACdPnnRdNy0tjdGjRzNt2jRWrVpFp06d2L9/PwCnTp3Cz8+P8ePHExwcnOdj0qBBgxz7o12unJIkZxPB8+fPX7QtOjoagMDAQIKCgqhZsyaQPany8fGhSpUqhR6riIgUjJIsEZESoGfPntSpUweA5cuXX7Q9KSmJhIQEHA4Hffr04euvv+att97K1h8HrP45o0ePJi0tjXfffZcOHTrkes2carb69etHrVq1WLp0KWlpadmGC8/KbrfzwAMP5Jh8+Pv7u5adH/g3bNhASkoKAFFRUQQGBgJk60OVVWJiIh9//DFgJQ7jxo3jxRdfBHJOTpKSkgBcw707r7tu3TrXPkePHnVtX7Zs2UXnyK2mL+v9OBVF0gVw2223AbBv3z5XrZbTzp07ARg0aBAeHh5ce+21QOa9A4SHh2er8RIREfdQkiUiUgJ4eXnxySefEBAQwNSpU9m3b59r2+nTp3n44YdJTU1ly5YtrFy5ktjYWBYvXuyq8YiNjXXtP2bMGOx2O8OGDct2DWeNh/O3n58fYCU027dvB8DDw4N7770Xh8NB3759sx3vbMboPL59+/auxDDr9qwaNmxIixYtOHnyJE899RTbtm3jq6++ws/PjxtvvJHo6Gg2b97M0aNHL6q9eeONN/j1119df1eqVIl69eplG2HwyJEjxMbGsmnTJgDXPGIDBgwA4N///jd//PEHb7zxBt7e3tx1112ANXDGV199xbJly1wJl/PxiI+PZ+fOnXk22wwODiY+Ph5jDBs2bMjx8c3t94WPY1Y33HADw4cPJykpia+//tq1Pj4+nkWLFlGtWjWmTJkCwOjRoylXrhyRkZGux27MmDG5xiwiIsXIDf3AREQkF3/++acZOXKkadiwoendu7e5++67zaOPPmqOHz9ujDEmOjrahIWFmZo1a5oPP/zQjBo1ylSqVMnMmDEj23luu+02c+7cOdffmzdvNsHBwQYwL7/8sjHGmBUrVpiqVauajh07mn379rn2PXr0qGnbtm228y1ZssTUq1fPAKZRo0Zm6dKl2bZHR0e7BtYYNmyYOX/+vGvbli1bTNu2bY2vr6+5+eabXdv27dtn2rdvb6pVq2bGjh1rQkJCXANPOAed8PLyMkOGDDHjxo0zd9xxh2uOL+f21q1bm+HDh5vQ0FDTp08f17mTk5PNyJEjjZ+fn2nQoIFZvXq1K553333X1KlTx1SuXNlMmDDBOBwOY4w1D1f58uVN7969zenTp83rr79uABMSEmI2bdqU7X4//PBDU758edOvXz8zZswYA5iuXbuamJgY89prr7kG5di+fbu59dZbDWC6dOlitm7dapo0aWIA869//SvH/4H09HQzbtw4U6tWLTN79myzcuVK0717d9OyZUuzffv2bPt+++23pn///mbQoEGuchUREfezGaMesiIiYg2+4e3tTUpKCnPnzuXBBx8s1uuHhoZy+PDhfA3ccOjQIerVq0eXLl2IiIgo+uDcICYmho4dO7Jz506GDh3Kxx9//LeDl4iISMmg5oIiIgJAr169GDJkCF9++aWrb1BJVRa+HyxfvjwffPAB/v7+fP311yxZssTdIYmISD4pyRIREQCuvvpq1q5dS3p6OkFBQcV67W+//dY1pPmXX375t/s7J2Des2cP69evL9LY3Klz585s2LCBu+++mxdeeIHbb7+dxYsXuzssERH5G2ouKCIiIiIiUohUkyUiIiIiIlKIlGSJiJRB6enpTJgwAZvNRteuXQvtvM6h1MuK2NhY7rrrLipUqEDTpk1Zs2aNa5vD4aBGjRrYbDbXz2uvvZbt+JzK4e233y70chERkeKlJEtEpAzy8PBg1KhRhXrOiIgI3n777UI9Z0n39NNP06BBA9q2bcuePXsYPny4a9vPP/8MQN26dalbty7169d3zdPllFM5/OMf/yj6wEVEpEhpWngRkTKqMIcDP3z4MHfddRfdu3cvtHOWdLGxsTz99NOEhISQlJREcHAwSUlJru0LFixgx44dVK5cOc/zaFh2EZHSRzVZIiIl2K5du6hWrRo2m43ff/+dTp060b59ewB27NjB0KFDeeyxxxgyZAgJCQkA/Pjjj9x3330MGzaMunXrukbiA/jjjz9o3bo1119/Pa+88spF11u2bBkdO3Zk7Nix9OrVi/379wNWwmCz2QgNDSU5OZlbb70Vm83GpEmTSE1N5ZFHHiEyMpKVK1fSu3dvjh07lus97dixw9WM7sUXX6Rhw4Y0aNCAH374wbXP77//zrXXXsvjjz9O9+7d2bBhAwDjxo3DZrNRp04dDh065IrjuuuuIyYmhqeeeso1v1dkZCRDhw5lwoQJ3HzzzZw8eZLz58/TpUsXbDYbs2bN4p577qFSpUr8+OOPBAQE5Prz008/XXQfgYGBhISEAFbC5XA4eOeddwBriPnPP/+c2rVr06pVKyZNmkR8fHy+y8F5zgcffJBq1arRt29fzp49m+tjKiIiJYwbJ0IWEZF86NKliwHM8OHDzahRo8zVV19t4uPjTUhIiHn//feNMcZUrlzZTJ482cTFxZmAgAAzbtw4Y4wxNWrUMEFBQcYYY1JTU01oaKhp0qSJcTgc5quvvjKA6dKlizHGmKNHjxo/Pz/z9NNPG2OM6datm2natKlJS0szDofDAKZu3brGGGNmzpxpADNx4kRjjDE//PCDK8b8uP766w1gZs2aZWJjY01wcLAJCgoyCQkJJiEhwdSsWdMMGTLEGGPMyJEjTdWqVc25c+dMSkqKCQ4ONt7e3ubcuXNm5cqVBjD16tUzxhjz9ttvm+joaONwOEz79u3N+PHjjTHGtG3b1owaNcoYY8zEiRMNYK677jozYcIEExwcbI4dO3bJ5bN69WrTtm1bY7PZzFNPPWWMMWbdunUGyPbTsmVLExsb+7flcPDgQQOYxo0bm7i4OLNo0SIDmJEjR15yjCIiUrxUkyUicoXo378/M2fO5Ndff2XJkiVERUURERHB1KlTufbaa0lNTcXT05Pw8HACAgI4e/Ys6enpnD59GoBffvmFQ4cO0bp1a2w2G+3atct2/rlz55KQkEDt2rUBqFmzJrt372bdunUXNWmz2y/v7cN5vm7duhEQEECHDh04deoUa9euZdGiRRw9ejRbHKdPn2bx4sV4eXkxePBgUlJS+PLLL9m0aRMdO3bk4MGD/P7778TFxVG+fHl+//13Nm7cyKZNm5g6dSoNGjTA0zN7C/m2bdsyefJkTpw4QWRkJBUrVsz15+eff2bSpEmunzfeeMN1nhYtWtCjRw+MMbz88sts2LCB9u3bExkZybp163jiiScIDAxk27ZtzJgx42/LwalGjRr4+/tz/fXXA2h+LBGRK4j6ZImIXCEaN27sWj548CAA7dq141//+le2/VasWMFrr73GxIkT8fDwwGRMhxgZGQmAj49Pjuc/fPhwtu3ORCoqKqoQ7yJnFSpUAODkyZMXxXlhHIMHD+att97i448/pl+/fgwbNoxffvmFl19+mQEDBgCZj0+DBg0YP358jtfM+niGh4dz/vz5XOM7dOgQzz33nOvvunXr8thjjwEQHBzMtGnTSE1N5fXXX2ffvn2Eh4cTEhJCSEgIHTt2ZOTIkVx99dXs3buXGjVqZLu/vxMQEABAXFxcvvYXERH3U02WiMgVwt/f37VcpUoVANatW+daFxUVhcPhoE+fPnz99de89dZbeHt7u7YHBQUB5JpMOGuOUlNTAUhJScm2Pq/aq8sdvME5YET16tX/No6OHTtSt25dfvzxR66//npuv/12PD09Wbx4MX379gVyf3yyyvp4/p3Q0FCMMa6fQ4cOXbTPnXfeCUDTpk0v2hYWFkavXr1o0aLF35bDhRITEwFo1qxZvuMVERH3UpIlIlLCORyOi9b16dMHLy8vFi9ezNy5c1mzZg2//vorW7ZsYeXKlcTGxrJ48WLXB/nY2Fg6d+5M1apV+eWXX4iJiWH58uVAZg3JXXfdha+vL3/99RcAR44coUmTJnTs2BGwamzi4+MxxrgGonDWkvn5+QFWQrB3715XYvR3duzYgTGGzZs3ExQURMeOHenXrx/VqlXLFkflypXp16+f67g77riDhg0bEh4eTlBQEDfeeCM9e/akfPnyAK573bx5My+//DIbN2503W9Oj+el+OSTT2jXrh3bt28HrFq4gQMH0qZNGzZu3EiTJk145ZVXSEtLIy0tDZvNxvDhw/+2HJyczTx3794NwEMPPVQocYuISNFTkiUiUoLt3bvX9SH7yy+/dK2vUaMG8+bNo0aNGjz88MMsWbKEgQMH0qBBA8LCwoiOjubUqVPceuutVKpUifnz5+Pr68unn36Kv78/7dq14/z584SGhtKwYUM2bdpErVq1+Oyzz1i9ejX//Oc/sdvtLFiwwFWD9cILL5CSksItt9ziqmVas2YNsbGxtGvXjv79+7Ns2TIWLFiQrQYtL6tXr6ZTp044HA7mz59PuXLl8Pf355tvvmHfvn2MHTuWffv2sWTJEgIDA13HDRkyhDvuuMP199ChQxk0aJDrb19fXz7//HMaNWrEc889x1tvvcVdd91FTEwMP/74IwCrVq0iOjr6EkvGqlmLi4ujY8eOjBkzhq1bt/K///0PsGrkatSowcSJE+nQoQMvvvgi06dPJyAg4G/LAaBnz57069ePxx9/nKeffpqXXnqJ0aNHX3KsIiJSvGzG+TWkiIhIMenatSs//vgjBw8eJDQ01N3hiIiIFCrVZImISLHT93siIlKaKckSEZFCFxYWluvEvlOmTOHPP/8EYP78+SQnJ7s5WhERkcKl5oIiIiIiIiKFSDVZIiIiJVxhjYhY1K6UOEVEipqSLBEp81JTU3nooYd4+OGHqVKlCh9++OElnef48ePYbLaLfpznW7duHe3btycgIIBbbrmF2NhY17GvvfYaffr04YUXXuD666/PNr/T77//zu23384999zDY489Rnp6eo7Xj42N5a677qJChQo0bdqUNWvWuLYlJiYyYsQInnzySfr27cvevXuzHXv27FkmTpzIwIEDXeuOHDnCk08+6bqPsWPHXnTNW2+91bX9//7v/zh8+DCzZs3Cy8sLm81G37592bRpE5s2baJv377YbDa8vLyYNWtWvifX3bx5M71798Zms1GnTh2efPJJevfuTePGjfnPf/7j+mB/8uRJOnToQGBgIK+++mq+zg0wZ84chg8fzoABA/joo49y3e/HH3+kd+/ePPLII/Tu3ZsjR4787bb09HQCAgIu+p/4z3/+A1hzZfn4+GTb9vXXX7vOm5iYyLPPPsvWrVvzfT8FERkZSZ8+fbj//vupW7cuGzduvKTzOEdF3Lx5M88++6xr3jMRkTLLiIiUca+//roBzA8//GBGjx5tPvvss0s6z9dff20A4+fnZ6pUqWIqVapk7Ha7OXHihDlw4IAZMmSImTBhggkKCjKAmTRpkjHGmE8//dQAZuHChcYYY4YPH26qVq1q0tLSTExMjAkKCjIfffSRSU9PN/7+/mby5Mk5Xv+hhx4yEydONF26dDGACQ0NdW277777TNeuXY0xxtx///2mcePGJjU11RhjzBdffGHatGljANOlS5ds59y/f7/p2LGjAUzlypVNYmKia9vx48fNjTfeaAADmPT0dNe22rVrG8DMmjXLtW7WrFkGMLVr1y7wYztz5syL4uvTp48BzAsvvGCMMWbz5s0mLi7ODB061LRr1y5f5123bp2x2Wzm0KFDZv369cZms5mIiIiL9ouMjDQBAQHm0UcfNcYYEx4eblq2bGnS0tLy3PbHH38YwPj6+poqVaqYKlWqGMD8/vvvxhhj5syZY2rVqmXq1q1r6tata1q2bGlSUlKMMcakpaWZnj17muXLlxf48cqv/v37G8BERkaaO+64w2zevLnA59izZ4/p3bu36+8FCxaYPn36ZPt/EBEpa1STJSJlXtZagg8//JDbb7/9ks5z5MgRtm7dSnx8PKdPn+ajjz6iY8eOBAcHk5yczP/+9z8mT57MG2+8AeD6tt9Z47Rt2zYAqlWrxunTp4mOjuaLL77g1KlTBAcHY7fbqVq1Kh988MFF146NjeXpp59m0qRJLFu2jMDAQNf5ExMT+eijjwgODnad/88//yQiIgKAQYMGMW3atBzvyW63M3ToUAIDAzl79iyff/65a9vHH3/MnXfemW3fC5dtNptrnXM56375ldMxvXr1AuCLL74AoHXr1vj7+5OQkMCzzz6br/O+9957GGMIDg6mWrVqGGP473//e9F+CxcuJC4uzjXZ8VVXXcW2bdv4/vvv89y2fft21qxZQ2JiIqdPn+b777+nVq1ahIeHA1YN2KFDh1w/W7duxcvLC4BXX32VyMhIevbsWcBHK/+c//shISHMnz+f1q1bF+j4c+fOcdttt5GYmOha179/f3bt2sXbb79dqLGKiFxJlGSJSJk2a9YsVq5cCcDTTz/Nvn37AHjrrbcYNmwYbdu25eGHH8YY42rql9PP/fffz0MPPUTLli1d516wYAH9+/cHoGnTpq71J06coHnz5q7md1dddRUAkydPZsGCBWzYsIHRo0dTuXJlfvnlF4Bsk/tGRUVla6oGEBgYSEhICGAlXA6Hg3feeQeAP/74g+Tk5IsmCHae+8LzXyggIIAhQ4YAZEvwjhw5Qt26dXM9Lj+qVq3KCy+8wHPPPYfNZsNut/PHH3/k61hnM0E/Pz/Xus8++4w+ffpQvXp1fvzxx1zLKyAggJ9++inHxzfr4+LkHAHx1KlTAFSuXBmALVu25Llt6NChXHfdda7zLFiwgH79+gFWOf3vf/+jYsWKXHXVVbz++uukpaUBkJKSwrRp0+jUqRMACQkJDB48GJvNxqhRo+jZsyfly5dn/PjxrnMnJSXx4IMP8txzz9GtWzd+++03AB555BFsNhsjRozggw8+oGLFinzzzTeMHTuW48ePA3DbbbfleQ5jDP/+978ZOXIkTZo0YcqUKQD8+9//ZuvWrWzdupXevXuzefNmALp168aLL77ouh8RkTLHvRVpIiLuN3z4cFdzQWOM+fbbbw1g9u3bZ6ZMmWIA89NPPxXonGlpaSYoKMj8+eefrnUpKSlm2rRpxs/Pz1StWjXbOe+//34DGJvNZpo2bWoSEhKMMcbcdNNN2WKrW7euAcyvv/6a43VXr15t2rZta2w2m3nqqaeMMcZ89tlnBjDDhw83xhgzceJEA5gHHnjAddwPP/yQY3PBgwcPmtmzZ5uNGze6mgVu27bNrFmzxnz55Zeu4y58O3HG2alTJzN8+HAzfPhw06lTJwOYunXruvZ7//33jTHGjBs3zgBm8ODBOd7X7Nmzs8WXkJBgrr76agOYefPmGWOMWbFihfH29jaAqVq1ao7nuVC5cuVcsR88eNAAply5chft98cffxi73W7q169v4uPjzdixYw1gXn/99Ty3Xaht27ZmxYoVxhhj/ve//7keO+fPjTfeaNLT082KFSsMYKZNm+Y61tnc8oYbbjCpqanmwQcfNID54osvjDHGPPbYY6ZDhw7GGGPGjh1r6tevb4zJLNuwsDDz7LPPmjp16riapjrLySm3c0yfPt14e3ub+Ph4c++99xrAHDlyxPWYXfh/M3Xq1Gz/tyIiZY1qskRELvDee+8BUKdOHe655x5eeukl2rZty9VXX03FihVz/HnwwQeznWPt2rVUrVqVRo0audZ5eXnRtWtXWrZsyenTp7n//vtd2+rVq0f//v0JCAhg9+7d3HXXXQC5DnJhcpl9o0WLFvTo0QNjDC+//DIbNmwo8Dly0q5dO1cTt/fff5/Fixe7amTycs899/DRRx/x0Ucfcc8991y0/b777iMyMpK33noLb29vXnzxxTzPd+jQIYYMGULt2rXZunUr3377ravJYo8ePUhOTsYYw6lTp9iwYUOu5VWxYkV+/vnnHB+bnB6Xtm3bMmfOHCpWrEjv3r1dTRSbNGmS57as/vrrLw4ePEjXrl0BGDBgAIcPHyYiIoIxY8bg7e3NqlWrWLBgATt27ACsGkonZ3PL6667Dk9PT2688UbAqh0zGc0cz5w5w9SpU4mLi6NevXrZBhfx8PDgueee4/DhwzmWXV7neO+996hatSp+fn489dRT/N///Z+r5jQnzridTWBFRMoaT3cHICJS0uzfvx+wmn/VrFnT1STL2XQqPxYuXMgtt9xy0fqrr76a1atX07RpU1fTxNdff51x48axf/9+jhw5Qrdu3fjqq684cOAANWvWBC7+4O9cf6Hg4GCmTZtGamoqr7/+Ovv27SvwOXJz3333sWHDBubNm8fYsWNdfYcu14QJE0hMTOTxxx+nXr16ee4bGhrK888/z5IlS0hKSmLXrl3cfPPNOe4bHh7O+fPn8zxfzZo1OXToULbHJrfH5c477+TOO+8kLS2NGjVqUKVKFbp37/6325wWLlzITTfd5HrcfH19qVOnDnXq1KFLly4MHjyY7t27s3fvXlJTUwHw8fHJNfYKFSoA1qiKJ0+eJD4+Hi8vr2xNCLNq3Lhxno9FXudwPifS09Np2LAhTzzxRJ7n8vX1BSAmJibP/URESivVZImIXKB69eoALFu27JLPkVuSBeDv78+AAQNc/bRWrVoFWIMPdOnShW7dugFWn51rr70WINuQ2CEhIdSuXZvY2Fjmzp2bbSh4J2ftTtOmTWnfvj0+Pj4XDavdsWPHAt3TkCFDKF++PDExMa4+Wpdr27ZtzJ07l4oVK/LMM89gjOHEiRN5HtOwYUPeeustwOoTtGXLlku+fk6Pr/Nx2b9/P19++eVFyenHH3/M6dOnefHFFy/qy5bXtgULFuT6PwFWP6Y2bdrQokULKlWqdFFcF3Juq169OhUrVsRut7Nv3z5OnjwJwJkzZ1z9xcD6v8tLXueoXr06iYmJ/Pjjj9mOyTqwSVbOgTCciaCISFmjJEtEyjznh2jnb2dTvWeeeYaVK1fyxRdfFKgWa9u2bSQmJroGtADo168fY8aMcX34PHXqFJMnTwZwNcM7fPgwAOXKlaNx48Y0b96c22+/neDgYCIjIzHGEBMTw5gxYwBroIzhw4fz/PPP88knn9CuXTu2b98OWLUSAwcOpE2bNgQEBDBixAgiIyMBa26mRo0auZI5wDVAwYUDFTgcDleTOn9/f4YNG8Z1113nagqXdfLZnJazrnOeJ+u68ePH43A4eOaZZ6hcuTKLFi1yPQ4XxpH198iRI7nttttISUlh6NCh2Ua3K4iHHnoIu91OZGSkq9br3nvvBWDw4MHcdtttLF261LX/hg0bePzxxxk3bpyrHPKz7dy5c/z666/cdNNNrnVLliyhefPmzJ49G2MMsbGxhIaGctNNN9G2bVuAHGvidu7cCWTOTdW/f398fHy4+eabSUlJ4Z///Cc7duzgv//9Lz4+PnlOEJz1fz+vczifEw8//DA///wzs2bNYv/+/a5BRxITEzl16pQrOXPWYBV0tEIRkVKjuDuBiYiUJHv27DEtWrQwgHnwwQdNTEyMcTgc5tlnnzXVqlUzNWrUMO+8806Bzvn888+b+++/P9u6p59+2lSqVMk0atTIjBs3zixevNi1LTEx0TzwwAOmT58+ZsKECaZfv35m3759ru2//vqr6du3r7nzzjvN448/7pp/6LPPPjOBgYHms88+Mz/++KNp1KiRCQgIMPfee6+ZNm2aSUpKcp0jPj7eDBs2zDzwwAOmV69e5uDBg9nOf/vttxvABAQEmPnz55vExERz6NAhM2LECNO2bVuzdu1aY4w1F9XcuXONMcbs2rXL3HXXXa5BG1555RVz6NAhM2vWLOPp6WkA07NnT7Nx40azceNG15xanp6eZtasWebHH380gLHb7eaZZ54xI0eONP7+/mb//v3ZHrtNmzaZXr16uebq+vLLL01qaqo5e/asaz6uW265xRw9erRA5eQ0a9YsM2jQINO3b1/XIBrGGPPII4+YatWqmT///NMcOHDAvPLKK2bo0KHmxx9/zHZ8Xtuc5s2bl20uKWOM2bp1q2nfvr3x8fExXbt2NdOmTTPR0dHGGGPS09NNnTp1XIOVGJM5+Ef//v3NgAEDTFBQkHnqqaeMw+Ewxhjz119/meuvv974+PiYTp06mb/++ssYY8yjjz5qANOmTRuzd+9e1/lWr15tfH19DWA+/PDDPM+RnJxsHnjgAVO5cmVTr1498/nnn7vO8+CDDxo/Pz8zduxY17oRI0aYkJAQk5aWlr9CEBEpZWzGFKDns4iIiBSLuXPn8vTTT3PkyBHsdjsfffQRI0eOZOLEiUyaNMnd4eWpfv36/Oc//2HUqFHuDkVExC3UXFBERKQEuvvuu+nZsydz584FCjYapDt9+umndO7cWQmWiJRpSrJERERKqJkzZ3L+/HnWrl3L8uXLAfjpp584cOCAmyPL2R9//MGJEyeYPXu2u0MREXErNRcUEREREREpRKrJEhERERERKURKskRERERERAqRkiwREREREZFCpCRLRERERESkECnJEhERERERKURKskRERERERAqRkiwREREREZFC5OnuAApD1apVCQ0NLdAx8fHx+Pv7F01AUiKojMsGlXPppzIuG1TOpZ/KuPQra2V86NAhTp8+neO2UpFkhYaGsmHDhgIdExERQdeuXYsmICkRVMZlg8q59FMZlw0q59JPZVz6lbUyDg8Pz3WbmguKiIiIiIgUIiVZIiIiIiIihUhJloiIiIiISCFSkiUiIiIiIlKISsXAFyIiIiIiZZnD4SAyMpL4+PhLP4kxkJIESXGQng4eHuAbAN6+YLP97eEVKlRg165dl379Iojpcvn7+1OrVi3s9oLVTSnJEhERERFxB4cDNiyHFbPh1BEIqg09R0J4L8jpQ30e+58+fRqbzUaTJk0KnBAAVjJz6gjEp0OFSmCzg3FY2/wDIahWzkmNMZAQA9GniQ2uQmByLFSoCn7lLz8JutSYConD4SAqKorTp08THBxcoGOVZImIiIiI5KSgSVBBz/3B47B+Mdg9wMsX9m+G6Q9Dh35w36vZr/E3+5/v/gChoaHZE6wsCRBpKeDpnXsClBAD8dHWuZ1sGcvx58G/PPhXyH6MKwmKtv72DoTkBDj5F/hXvPwk6FJiKkR2u51q1apx+PBhJVkiUoiK8s1FRETc60p/jS/q+AuaBBU0pg3LrXP7V7BqaAC8vK2amvWLrGOu6p3v/dM73oWXl1fm/hcmQDZ73glQdM6T6mbbfmFCk1MSZC/EJOhSYipkXl5epKWlFfg4JVly6a70F+fiUIjNAIr9Mb2UNxcREXGfgryHFHUCkdP+He6F3xLdUwuUW0xVa0GPEdDuRsBY2x3p1s+GFbB2AfgFWsmIAbx8rKTm568gNAzCOlnHGAekp8EXr8DWNda1PbzhVCTs+Bmad4K+9wM269zGwPwXrb5GOBMdk/krOQHmPQexZzPPv/AdSMzol5R1XzL6LMWdx3b+ZMb6zHXYM87vbGZngJjTkJ5q3Y9TUpx1nMOR5QHLcp3EOCtBc54frCQrPc26J8An/oxVY2YAHHDiMJQLuKAQDKSlQmqKdS27DTx9wNMz+z2RcU2MdQ0nL2/rMbPZrWsVMdsl1sQpyZJLUxwvzpcSU0FezEvaN2DFkdQU5TdsIiJXsiv5SzFnPAV5Dynoa3zW89vs1ofzvX/Ant+gfS8YNsH6oJ2WkvEBOhk+fxm2rbH29/Ci0sGNsOAraHwV3HiXtX96mvWTlmr9HNoGu9ZD3DmrSVv9NlCjXua+zt/HDsDWCKv5G2R+6DcOWPpfK64KVTP2T888LvJPK8Gw2QAb7Ntk3a9/RasMszp2wEp2ok/l8Hinw3+fghr1M9fFR8OpvzJqcrJ+MDfWNfZvsWp2nI7sth5X2/mLz2+M9Vh89drF+yfG5bx/eirEnMlcl5qckfzllCQYK96sSZYzmSOX/bFffO201MzrAx5pqdmTtPS0jEQy6zEprqQMbJCekUSlemSW54X3lhPjAE/fnLeVAEqy3KU4XsyL8hqX8+JcFElZTudPTij8hMaNzQAuO6kp6D2vmG3tZ7vgvmx2a/3y2YWTZJW0DyoiUvYUx5diRfk+7nDAuoWwboE16hqAIy2z2daP8633ltAw6wNvajIsehfiY6y/jbHea4yxflKT4c37oVmHjKQpBU4fhSO7rHNm+9BrYOVHsPmH7AlEDglH/a1LrJqPP1bAkT3Z94eMpm3ncdVSnDsBUX/mngClpmTU6lz4uKXDX7uyJ0DOmGJOZU+CbB7WPcSfB78K2WNKS7n4PdApp1qUmDMZ570wSclYF3M6+/k9va3PKs4+RlnllEDkub+xEkeb/YLL2y7+O9s5vTL3cTYnzHFwC8DXL0sSlLGPM3nNOCbdyxuPVGeNmQEPzyw1WTZIS7b+vy5KRDPu2ds3e+KXmmyVm7McLgytQlXXYkREBA888ABLly4lNDT04nsoZkqyCovDAT98Cu89av1DePlApeoQWNn6x7PbyXxC2yBqr9WO1PmEOLAVfl0CFatB3eaZL4w2m/VzcDucOwbYrXMd2g6/fQdVQ6Bhu4wPw7bM8wHs+d16wbLZrO2Hd1ov1tVCoUXnLMdk7L/tR+tFy2YHu6f1ArhxJdRqDO17Wvs772PN59Y/fUpyRpxk3l9qMsz6d8a5Mq59aAdE/A98/KzHyvnNBwZ+yFjfuL11Do+Me182E3b/Zl3T08e6l+0/Q1hnGPCI9cR1fuDf+Qv89CWUC7T2Nw58489aT9a1X0Oj9lZTAJvdOm7TausNyZnQ2GwZLzQm94SmqJOUS0lqijLpO3XEusecePla2y+XmiSKSElQ1F+K5fRat2+T9T7drgfc9i/rw21SHCTGW78TYmDlHOvzAVjX+WsX/L4UKte03ptTkqzjUpMza13sOXwAd6TDgreyJx1Rf1pxJSdcvL8xVkJwdF/muhOHMpOwbHJJIAqacMRHWz8ezg/9tszPFolx1of7qiHW/Xl4wsnD1mcH52cZyKydcqRbn8Ouvjnjs0vGT8SnUK589qG/nb9TkiCgEtzycOb+37xhNXfz8cu+LzbrcQtpBHc/Z71P2ezw5hhITsqeuDjvPy0VfP3h3/Mz99/6I8x9NmMQCnvmNYzDKv8xr1r/S87PKRtXwvuPZf+/c+4fHw3lq0KdppmP6dH9ef9P+PhBzQbZP0sc3QcVguDa/hB2XWbx5TaIRXy01YQw4xrJfpXxjj+deY3gOtn7TB3db5VfbjEZY5WzU079ypw1Zf4VrccuQ9euXTl37tzF580iOjqalStXMmjQoDz3KwxKsgqD88Vz7TfWC4HNDmlxkLg3529f4qPh/InMfzBnG1mAs0etF5gLX3hOH8nY3wHpzv2N9aLnMBd/G3ThN0iutqwGIndDcuLffOOUnLn/gc0Qey77/scPZrw4J178eBhjvXgvn5W5zvnin5TDi7kjHZa8V4Aq94XWB/Gs8TjPH5v55Ar7aSbEn7bO/+4j2c/v3N/ZdjlbPA6YdifUaZ75QuDhATFn4fAO68XT+ULufLFZNhMi91gJrIen9bP9Z+txT4zNsm/Gb0e69eb67QxrX08v6w03NQUc561zupJmm1Xmf+2y3mw9vaz/EbsHfDoF/lhlxedVDqI3wZ8boGP/y0/6gmpbj7NXDlX3qUlQp1nOj11Bvqm9lNq7omzjLyJl0+V+KWYcmU3S0lJh/lTr84AzcfhzA/z2rfVa7erz4wAMrJoDWyL+/n3cxcDJQ9Z77eXUunj5WO/hHp5ZakAy3nPSUqFiMLTuZr0me3pb73NePhe/B9ps1r37+MHIKdb7k6cXvP0AJCeDV+b+O9oOpsOWzzMSjgB4eXXme+bUYdZ+fuUvjj8hxno/+c9nmeteuN16j8pt/wZtYfjk7OvXL4LKNXJ5X0uxyufGuzLX+ZW3vvTzL5/9sTUOMOlwxzhodX3m+jrNrZh8/S8+f3qqlRiHNMxcF1wH9m/KnnynJFr/H9cOgE4Dsr+vdegLW77Pvn9qkrV/h35W8phVhaqZfahyUqFqzl8AHN0P86dB619h6H+gUnDuw7H7lbfew51JEGQ2BbwgCQIK/n9qs1ll718hywiJvrmOkOjrm3vzQWMMjzzyCN26dct1n8KkJKswuD4oVrzgWx5jvYA1bAu1mmQmU6s/tmpcslaHOjv5pSZbL07X9Mk8109fgY9/xv4m+zFpydY/WIvrMqv6MfDbUvAuZ50r25dOGZ0N01OhbhiuTp7b1lgvoh5emfs5j0tPhaR4qN0k81uG05GQEJvlm4gs+5s060lq98iMqair3At6/jz3t2V+O5jVsQMZb6A5HONIt9qQZ03anOfI6xuk72Zkros58/f7vzoqc11eb8BL3oPNq63aVOcb5LY11hth7Nnsb6bOb4V2/wrfvJVZVV+tntV515Ge+WEi6zd+Hf5hJZ7evpkv7AWtlSroB5uCNgu9FGq+KFI6FOS5nFvNvcORWYO0YYX13hN9Cnassz4MR5/K6CeU5ctSY6zX2c+mZq47dsB6f0+/8PU9l/c0yHwftHtm1nw4f6enWu/vnW+1mnB5l7OSoFN/WcmL6zXebg0qkJQAoS3hXx9lvsZvXJGRQFTgogQiPhoeejv76+/hHXknNfVbWY+tkzPhcNYCAUkBVaz3o5Qk64vPrPd8OrJgrSd6jrTiN46L43ekQ6+RF5+noF8ehvey3utyS2ra97y8mOx2uO81CO9tvd+dOmLF0GtkRgsie8H237Mn+/4XJkDZ5piqaG3/fVnuX3bu+BmO7YdajS5+vJwuTIJsWGWeJQn65JNPeOaZZ3jmmWd46l9PcsuN3WgT1oyFK7/ni3df5+CRSHrceS+v/vsJOlxzNT2v7cGdd97J1KlTef/999m9ezdvvPFGrqMIzpw5k9OnT3PmzBlXTVZMTAyPPvoobdq0YeHChXzxxRccO3aMnTt3kpycTPXq1alRowbTp0+nRo0aHDhwgHnz5uV+n5dASVZhcH5Q9PSyvvnJKiHGavN825OZ69Z+Y1X15/ZNSrkAq/rZadMq61uevPZ/4PXs6//saP2T53XMU3My1z3a0XrC5bX/84sz1/22NO8X54enZ744G5PlG6fALElfRmKWGGO9+D/+YeY3fBP6Womdp5drV9eBqSngUw4en5mR9Dngw/FWTZKvv+u8+5v2ps2upZAcD9Xrw+3/yvym8atXrdo473KZMTrjSUm0qqq7Dc0cYciRDkvez6hBsmfuS0Yi7Ei3yqh+q8xOvD7lrE7Bnl5ZOuSazBe4CkHZH+fyVeBUfMZ9XpA0YaxmAFnlmYhijWiUtQOpw2HdW25JXJofrJp7wYaMhNrZPt5kfPPqX9FqRvHNG5m7JiXAiYPWNZ1v8M5mHCs+sj5ENGhj/V/6+FkfDA5shbQ0q6yzfoCwe2Q2Ec0qp5ovu4f1tzv6rYlIyZTf53JiHJyOsl5vog9a+zoHYEhPy/xw7OMHs/+def7khNy/FLuwL42PH2Ayv/h0NV/LeK1zOKz3rgffst5rywVYidKzfSEpEbx9Lr6G8315yNOZ64JqW/fnF3jx+3JKEvR7EMpXzlxf1AlEQfcv6gToUmIqaBJ0KTHZ7db7Vn7fuwqyf35qgQqj/7XNZl3DvwLExkKV7J+FBwwYwJ133kmHDh144dkJ7N22mUdH3cW6jZtZsWYtQ/r34e6B/YhLSKD5VR0ZOXIkJkuz1ClTpuR66Z07d7J48WIWLFhAamoq7733HgBr166lQoUKPProo6xbt44VK1YwZMgQwsLC6Nq1K71792b8+PHcfPPN3HLLLVSrVo1jx45Ro0aNv39c80lJVmEoaN+Vgr6QXEqzraK+RkFeSGw26DXKemGD7C9KzoSj7/3ZOi9Ss0FGPDm8uaSlQO2m0KB15rpBT1jn9/VzvVCcr9EUDv9kVekPfSb7i4Svf95J4qiXLn5RObDl75smPJGliWROb/JZH6P7XrX2cyZlqSkwa7zV3t7uYQ39mpZkJYatusAtj2Q2RUlPhXcezmjq4ZFZ6+n8SU+1kp3OA63zpqVYIyr9sTIj8cqyryPd+juotlX+qVlq/YJqWx2BY7K8OJevevE3rgDnjmdcO4eqPkc6/Pqt9W1wVudP5l17518BJt5ifZjwC4TN31vD0aamuD6gVI7aYSV46WmwaLrV39DXP7PWTSMqipQ9zueyX/mMfsApmb9XfAQHt4FzdDWwfsedy7llwIVfcnl6Wc2+Du+wEigPr4wmbxkfVFMS4e7J1nukfwXrNeTvmrbVawlh12ZfXy3UOibHJKsQko6iTiBy2t+Rbj3W7qgFupR7cF4nv0nNpcRU1LImQDkphv7Xfn5WbWbLli1p2aIFx7ZvYNb8L4k6foLkFGukwWH9b+KxKa/xyKSXSE1N5fPPP+f5558nPj4ef/8cml9mmD9/Pq1bW58Hvby8qFKlCgA33XQTbdq0YdasWURFRZGcnHzRsVOnTmXz5s3MmjWL9PT0HPe5HEqyCkNBE5Si/jaoOK5R1C/OBY2noC/mJekbMLu39b/j6wf/fNdqwrE8S0KQ22Nar+XfJ31Dsnzzmp+kz27PHJgkNdn69jMtJXM5Jck6Jjkx4+/EzPWfTrGSYpsts8Yu69whxlgfSFISM2v2/q72zq9CRk1ahtNRGTVySZkPw5bFVt87Y2DLD/Bk18zaLb/y1pC3pyMz7tkHjh+C7T9B6xtgxAtQMSgzKSvqwUdEpOikp1uDIUT+adU6xZyB86eyDBWdwZEO+/7I3k/XvwIkVLReOz08M7/gs9mgWUcYPN5qqVIhKKNFhsn99fS626DniOzP/+J4H7+UD/hFmUDktL+Pn9XSxZ21QEWdBBU0Jne7lC/yL8Ovv/3GjA8+YMYb/8eajVus55iPH1f36MPppyaxfMUKunTpwi+//MKMGTMICwvL83xnz57F2/vi2H/99VdmzJjBjBkzWLNmTY7Hvvfee6SlpfHPf/6TyZMnF8r9ZaUkqzAURkJQ0G+D/u6FpziuUZQvzoXxjVxeL+Yl8Ruwgu5fZEmf3frm1Nsn5xqr3Pyx8u+Tvv98hmuCxOREa1CQOc9a/cewWR9uUjNq7+qGWXElxVlNehJjreF8nX3Ksg4Y47xvZxMdR0bfs+MHs/dbcyVnBn7+0hp8xL+89eZSvqrVr84Yq/miszO2sxO3p/fF3+iVxPniREqrrM+dE4esZsuN2ltfkkT9aY2K5qyJj9qb0Z8qh476nhn9VDsNsJqGV61l/a5UA3avz9+XXDZb0TcjK473nIK63Pe0iAi4qmvu+xZHLdCVlgQVtUv5AuASORwOPv74Yxo2agT+FTgRm0B6xWrEla9GgH8AQ4cOZeLEiaxbt47IyEhefPFF9u7dm+c5r732Wp5++mmee+45/Pz8SElJITEx0bpOQ2uQkRMnTpCenk5cXBweHh6kpKRw9uxZ3n33XWbMmEF0dDQJCQnExcWRnp6Oh3OU68tkM1kbPV6hwsPD2bBhQ4GOiYiIoGvXroUTQH5rCC48Jr81Fpeyf3FdoyhdZjyFWsaFFFOhx1LQ/7uiVJB+ehfeR34f06zXyBimd32rQXTY+LHV9/HmMVZTU+eIXqvmwdljGf0e0rM3ZXT2s8hp1Mlcmy9WhPCe1oAilWvA2eOweh4EVLT6dNgz+qLlds8lrcyuEEXyXJYSJ89yjo+xBv7Z8oNVs+6aNNXkPIrv8YNWLXy5QOt55hwAyNPL+sLG+aVPcSqO9/ESTs/lorVr1y6aNStgzVMhvy/FxsYSGBiYbd0333zDwIED+fDDDwkJCeHuu+9m4MCBeHt7c/DgQT755BPKly/P3r17WbJkCWPHjuXcuXO89NJLvPzyy38TvoOHHnqItWvXMnDgQL766ivuuecemjRpkuN1lixZwuTJk3njjTdYv349s2fP5v7772f58uV07tyZF154Icfr5PbY5pWDKMkqLKXshbA0KBMv5iXp/644EogcrhFx7Ri6/vB6ztd4tKM154yzGYSzD1p6utVs0dPLalIZc8b62bcJtkZkDLOftaYs47igujlPHeBMyux264Ocl4+1f53m8ORsa24RD89LS0RV81U2nsuSvZzT06x+U7t/tX52rLOGLc+pz5Qx0KqrNRdiSCNrNN8je+CDsQX/0keKlJ7LReuSkiwo1M8SOSVZpcGlJFlqLlhYVP0s7lCS/u+Kq617QZqFXtjW3GbLbAaYlgz1WmWfEyVrEueclDs53uqjFtoCWl5vDfBx9pj1+8KpAJz9xVKSrA9+f/4Ozw+yPhgG1bImEU+IBWxWIublndkHLL9D1mu0QymNjMH3/DH4Yb6VVO3dmH0ajehTuEY59SmXMXqur9WsOTnRaqY86InM/UMa5T2fUE5N7UTKqpL0WaIUUZIlIoWnOF6oC9LGvzD6rdVvlXOiaAxMGmBNJOnlkzHsc0rmaI6pydn7iJ04nDlwR1J85nk8va0PjR6e1jQEqSmZSaFGO5TSLD0dFr8L334Ap6MI860APl4Xj4IW0sga4TSgkrXtwi8WDBf3lyyJo7yJyCWJi4vLtRnfv//9b8qXL0D/8WKkJEtESq+i7Dhus0Gf+6wkrlzAxUlc7Dm4YxxUCbE66Z84ZNV+RZ8GW5Y+X2kZSZkj3Rrg44nrreGhQ1tYcTscXDQXWkHmLxEpaeJjrP/teZOsgWlcNVQO6+/0GnDjcGjWAZpeY83t5BwCPafkKLcR0PTtvEipEBAQwNSpU90dRoEpyRKR0quov83OK4m79hZrII6s12h7I7zzkFVzlZZm7ZuSZDWLcqRZIxymp8HhndbPkd1WkhVz2prI1NvXmqTUp1yhzV8iUmjy6j9os1l9rH7+Gv5YYQ2t7hz502YHX3+ONLuR5pG/Ws3/mnfMnhwV4whoIiKFQUmWiJRuRflt9qVMTdCxX2ZS5lfBamro6w9tusN1t2YkWNutBMvTOyMBs1lNDJPirQE67HZraPnQFtbkzAEVC//eRAoit/6Dbz9oTcdQLsAaYt0p5oz1P1whyGoC6OHJyXpX0fzkVqvp7YW1tJdSKy0i4kZKskRELkdhzxfnPE96GiybDXMmgN0GKclWPy+wPlimpVhDyI/vYSVbLa6DFp2t/ivGlPkRCaWYXdh/MCXR+lIgPho2rcwcmdPuAa27Wv/LBmvgigvlVEurPlYicoVRkiUiUpzym5R5eMJNo+GvHdaH13KBYPeChPOQlGDVDPiXtxKqg9usn8XvQsUg64Pt8UPg6wfefhqRUIreitlWApWUYI0E6Jr4G8BmTSo+eLxVk1uhqjWP1f7NOSdZ6mMlIqWAkiwRkZIqp2/v67Wwvr1v0x0ObYPtP8OOn605uwCi9mX2dUmIsZKzwEpWk0SNSChF5fhBq+lq1pEzwfoywDcAKgZD71GZ69XHSkRKuSL/OjMpKYmrr76a1q1bExYWxsSJEwE4ePAg11xzDQ0bNuSOO+4gJSUFgOTkZO644w4aNmzINddcw6FDh4o6RBGRksv57f1/PoM311m/r+oNXl7QqB0MeAT+8zlMXgS3P2UdY7MDNquWKyHGGj7+xGFrQIGlH7r1dqSUMQbWL7H+vxJirHU2uzWIS82GVlNVDw8IrpP9OGcfq/ho67jUjBE246PVx0qkFHv99dcZP3485cqV49dff72kc7zzzjtcddVVhRxZ4SvyJMvHx4fvv/+eLVu2sHnzZpYtW8b69esZN24cY8eOZd++fVSqVImZM2cCMHPmTCpVqsS+ffsYO3Ys48aNK+oQRUSufFVqQpfbraZY1RtYkx/7V7BGdQOrD0zcefhjJXzzFpw56tZwpYRzOOC3pdbQ6Y92tH7/tjRjSoEMp6Pg7Yesodj9KwAGyvlDjfpWs1VPr7+fk+7h6dCgrVXj5ZxYXE1aRUqlb7/9luPHjzN16lQmTZpE/fr1AYiOjubLL7/M93luvPFGTp06lec+q1evdntFTZG/itlsNgICAgBITU0lNTUVm83G999/z6BBgwAYPnw4CxYsAGDhwoUMHz4cgEGDBrF69WqMMUUdpohI6RBUG9JTrGaCVWpatQkVgqw+XsZhNSNcNRcm9of3H4ddv1q1ESJOzpECpz9s9ZtKjM/s1/fBE5CaCqvmWYnXnt+sY6rXg2v6Wk0DUxKtmqmEmLxrpi6spa1R3/pbCZZIqbR582bKlSsHwLhx4wgKCsIYwyOPPEJcXFy+z+Pr65vn9piYGB599NHLirUwFEufrPT0dNq3b8++fft46KGHaNCgARUrVsTT07p8rVq1iIqKAiAqKoratWtbwXl6UqFCBc6cOUPVqlWLI1QRkSvbhX1dPDyt2q3ASnDuBNRvbQ1MYAxsW2P9BNexJkCO/BPOHtNohGXdhSMFAnh5W/9TP38JB7ZkNg0E6DQA+v/Tqo3auEKj/4mUFF++Ckf2FP11ajeBQU/kucsPP/zADz/8AMDkyZNZunQpL730ElWrVmXnzp0kJydTvXp1evfOuc+ww+FgwoQJVK9enQ0bNrjWb9myhenTp1OjRg0OHDjAvHnzWLt2LUePHuW///0vQ4cOZcuWLWzdupXjx48THh7Oww8/XHj3nodiSbI8PDzYvHkz58+fZ8CAAezevfuyzzljxgxmzJgBQGRkJBEREQU6Pi4ursDHyJVFZVw2qJwvVA76TrJqEMD6kGwymnj5V4SgWpQ7G0nQrgiq7F+PPS0Vzm2CXRtI9/DmVN12HKveAcfO/XD4f1azQzdTGRezY8eg21ir1jODPT2VGnt/otrZM9giD4CXD0nlq3G4853EVW8Mv/+RsacvdH4g81zxwJo1+bqsyrn0UxkXrQoVKhAbG+v62+fAdjwObiny66anppKccd309PRsMTiFh4e7+lGNHTuWRYsWkZCQQN26dWncuDHXXXcdnTp1yvFYgFmzZhEQEMCIESNo3rw5ERERxMbGMmfOHLp160bfvn1p0KABe/fupXPnzgQGBjJkyBDq1KnDPffcw6effsrp06cZMmSIq8VcQSQlJRX4f7dYRxesWLEi3bp145dffuH8+fOkpaXh6elJZGQkISEhAISEhHDkyBFq1apFWloa0dHRVKlS5aJzjRkzhjFjxgBWwXXt2rVAsURERBT4GLmyqIzLBpVzDhyO7DUKQbVzqFG4E+Jj4JMXYPF0rEmLbFTc9i2Ndq2AStWsuboenu720QhVxsXs0aetJoJe3tbfSfFWDWdaqlUDaveAe6YReNM9BDn3KQQq59JPZVy0du3aRWBgYOaK+i2sQZKKmEftJnhnXDc2NjZ7DFn4+FhTNgQGBhIQEICfnx+BgYF4eXnh6+ub63EAX331Fc899xyBgYE0aNAAm81GYGAgr732Gps3b+aLL77A4XDg5eVFYGCgq7tSYGAg69atY9myZezYsYPU1NQ8r5MbX19f2rZtW6BjijzJOnXqFF5eXlSsWJHExERWrlzJuHHj6NatG19++SWDBw9mzpw59O/fH4B+/foxZ84cOnbsyJdffskNN9yAzdlxW0RE/l5+5xPyLw8nD1l9t+weEH3a6k+TlgqnIsHbF779wO1JlhSzoNpWHyxPLzh/EmLPZm7z9ISwztDvQbeFJyL59DdN+K4kZ8+ezbGW67333iMtLY1//vOfTJ48Ocdj77zzTp544gmaNWvGO++8U9ShuhR5I+ljx47RrVs3WrVqxVVXXUWPHj3o27cv06ZN47XXXqNhw4acOXOG0aNHAzB69GjOnDlDw4YNee2115g6dWpRhygiUnadOgLe5az+NNXqWjVYzn44yYmw+QdrVDkNjlF29BxpjQiYNcGy2a25rspXhVsecW98InJFMsbgyDpCaQYPDw9SUlI4e/ZsDkdZrr32WubMmYMxhsTERBITEwF49913CQ8PJzo6moSEBOLi4khPT3ed88yZMyxZsoSwsDCOHj1Kenp6gQbZuBxFnmS1atWKTZs2sXXrVrZv386zzz4LQP369fntt9/Yt28fX3zxhasK0dfXly+++IJ9+/bx22+/uYZ3FBGRIhBUG1KTrGWbDQIrW6O8lQvIGI3QDnMmwLuPaNj3siK8F9RoANEnrWTL7mENyW63Q8f+msNKRArs0KFDrF69mh9++IEtW7awb98+VqxYQXp6Ot27d+e1117jt99+y/X4F154gXPnztG9e3f+97//UalSJVavXs2tt97K4MGDeffdd2nevDnz58/Hw8OD3r17M2bMGM6ePcs111xDx44d2bNnD8YYvv/++2K552LtkyUiIiXMhaMRgtVMrEpN60N15RrWup2/WEN2/+NB6DpYo8WVZj9/ZdVgBdWFxNjMqQA0UqCIXKLQ0FB+/vln19+RkZGu5aFDhzJ06NA8jw8ODs6WHD3//PMAdO/enUmTJgHw9NNPu7ZPnz7dtbxixQrX8siRF8zZV4T0SikiUpaF94IO/7BGI0yIyZzfKCEGugyGad9Dx37WvilJ8NVr8H8jrOHepfT57Tv4bJq1HFwb3lgL72605rLSHFYiIvmmmiwRkbLMbof7XoPw3rnPb3Tns3DVTfC/KXA6Eg7vhAn/AF9/q9arWl3Nq1XSORzW/Fcrsow4eWGZbYmAeZOsZR8/ePgdq+moiEgx2Lp1K//73/8uWl+1alWefPJJN0R0eZRkiYiUdfkZjbDJVfDMZ/DdDPh0CsSdA2zg5WNNbrx3I3ToB/e9qkSrpHE44IPHrQmG7R7g5WuNHjj94cwy+3MDzHra2tfLGx54w0q2RUSKSatWrWjVqpW7wyg0SrJERCR/vH2svjmeXlYtVkqyNZdW7DkIrgXrF1k1IxryvWTZsNxKsPwrZPa78/K2+uGtXwS1GsP3n1hD93t4wr3/B43auTdmEbkkxhhNfVTIzCWOrquvG0VEJP9WzLaGfK9WDwIqWevSUuBkpDWf8fLZbg1PcrBitlWDZbvgLd9mh/R0mDvR6m9nt8OIFyDsWvfEKSKXxcPDg9TUVHeHUeqkpqbi6VnweiklWSIikn+njljNzWw2a06tgIrW+rQUiDkNxw64NTzJgbPMLpSaAjFnIDnB+nvof6DdjcUbm4gUmooVK3LixIkc56KSS+NwODhx4gQVKlQo8LFqLigiIvkXVNvqz+PlnZFoVbfWx52H1GQ4dxxizkL5yu6MUrLKWmZOaalw8jCkp1qDXNz6eOYokiJyRapatSqRkZHs2bPHbTEkJSXh65vDlzpXMH9/f6pWrVrg45RkiYhI/l04r5Yz0TIGYs9YTQnfuh8e/QACK7k7WoGLyyw9DU7+ZSVYGOh+J9yQ9xw1IlLy2e126tSp49YYIiIiaNu2rVtjKCnUXFBERPIvp3m1EmPB2xcatgP/8laTwbcesGq3xP2yllncOThxCFISwZEOTTrA/a+7O0IRkVJHSZaIiOSfc16th6dDg7ZQLsD6/c934dWf4Jo+1n5H91mJVny0NSz4b0vhhdvh0Y7W79+WWuul6DnL7MG3rRrH9DSrieD1t8Mr34OHh7sjFBEpddRcUERECiavebXunGh9kP/tO4jaC2/ebw2OsXFF7nM0aV6tome3W8lUYGXrp213GPWSHnsRkSKiJEtERAqP3Q53TbJqqTYssya5PXscatYHDy9rn6xzNGlereKRlgqL37WWAypZybASLBGRIqNXWBERKVx2O9z9HLTvaQ0Rnp4KpyKtPkBONrtVs6V5tYrH2m/gdJS1fNM94Ovn3nhEREo5JVkiIlL4PDxg+PPgU85KqFKSrBHtsiZaXr7WHE5StJISYOmH1nKVmtB5oHvjEREpA5RkiYhI0fDwgOYdrZEHISPROpI54EVqkjWHkxSt7z+B2LPW8j8eBE8v98YjIlIGKMkSEZGi02u0NfBFuQDr75REiDlt9clypEOvkW4Nr9SLPQer5lnLtRpbTThFRKTIKckSEZGiE94LOvYDX3+rD5YxcP6U1VerQz996C9qy2dBcoK13O9hDXYhIlJM9GorIiJFxzlH0z/fhebXWn/7lLMmLtbw7UXrzFH46UtruVF7q+mmiIgUC727iYhI0XLOq/XSMrj9KahR3xrwYuc6d0dWui153xq6HeCWR8Bmc288IiJliJIsEREpPv0ezuyf9cX/QWqKe+MpraL2wu9LreU2N0BomHvjEREpY5RkiYhI8SlfGfrcZy2fjoTVH7s3ntJq0XSr/5vdDv0ecnc0IiJljpIsEREpXtffDjUbWMvLZ8G5E+6Np7TZ+wds/9la7tgfqtV1bzwiImWQkiwRESleHh5w21PWckoSfP2GW8MpVYyBhW9by17ecNO97o1HRKSMUpIlIiLFr3H7zOHb/1gJe353bzylxdYf4eA2a7nrEKgU7N54RETKKCVZIiLiHgMeA29fa/mLVyA9za3hXPHS062+WAB+5aHHcPfGIyJShinJEhER96gUDL1HW8vHDsCPn7s3nivdb9/C8YPWcs8R4F/ereGIiJRlSrJERMR9bhgGQbWt5W8/gJiz7o3nSpWSbM2LBVAxGLrc4d54RETKOCVZIiLiPl7ecNu/rOWk+MxBG6Rg1nwO509ay33uA28f98YjIlLGebo7ABERKePCroWW18O2NbB+sTXs+NljsGI2nDoCHe6F3xIhvJc175OAwwEblluP0fFDcOIQ+FeA+q3gmr7ujk5EpMxTkiUiIu536+Owez2kpsBLQyA1GTw8wcsXkhNg+sPQoR/c96oSLYcDPnjcSkjtHpAYDwkxkBAN9VuDzebuCEVEyrwy/k4lIiIlQlAtuPFuiI+G4wesdX7lreaEdg+rlmb9Iti4wr1xlgQbllsJln8F8C5nJVd2D/ANgH1/6DESESkBlGSJiEjJ0HOE1S8LG0SftoYkd7LZrURi+Wx3RVdyrJhtPRY2O8SctiYgBqhYzar902MkIuJ2SrJERKRk8PaFgIpW8uBIh+hT2bd7+Vp9tMq6U0esxyItFeLOW+vKBYCvnx4jEZESQkmWiIiUHHXDrCaCAHHnICUpc1tqUuZw72VZUG3rsUiMy1xXvqr1W4+RiEiJUKRJ1pEjR+jWrRvNmzcnLCyMN998E4BJkyYREhJCmzZtaNOmDd99953rmJdeeomGDRvSpEkTli9fXpThiYhISdNrlNUXy+nccas5nHFYtVu9RrovtpKi50jrsUiMtf728LRqAfUYiYiUGEU6uqCnpyevvvoq7dq1IzY2lvbt29OjRw8Axo4dy5NPPplt/507dzJ//nx27NjB0aNHufHGG/nzzz/x8PAoyjBFRKSkCO8FnQfC6o+tmpqkBCoe22UNiNGhH7Tv6e4I3S+8F4T3hmUzrb/LBVoJlyNdj5GISAlRpDVZNWrUoF27dgAEBgbSrFkzoqKict1/4cKFDB48GB8fH+rVq0fDhg357bffijJEEREpSex2uO81GPsh+FUAu50qx/fAw9M1fLuT3W4lokF1wMcPKlSFBm31GImIlCDF9kp86NAhNm3axDXXXAPAO++8Q6tWrRg1ahTnzp0DICoqitq1M9uS16pVK8+kTERESiG7HTrdAgMegdpNqXA+Eppfq+Qhqx1rwb881GkG0zfCfz6Dq3rrMRIRKSGKZTLiuLg4br31Vt544w3Kly/PAw88wIQJE7DZbEyYMIEnnniCWbNmFeicM2bMYMaMGQBERkYSERFR4JgKeoxcWVTGZYPKufQKtFWlcWwsDoeD7R+/yekm17k7pJLBOGj102K8EmM5X6ce+9f94u6ICoWey6Wfyrj0UxlnKvIkKzU1lVtvvZVhw4YxcOBAAKpVq+bafu+999K3b18AQkJCOHIkc+jZyMhIQkJCcjzvmDFjGDNmDADh4eF07dq1QHFFREQU+Bi5sqiMywaVcynmuB62fUNs5EFaJB8FlbPl4DbwNBAYSOBNw6jdqau7IyoUei6Xfirj0k9lnKlI2xUYYxg9ejTNmjXj8ccfd60/duyYa/mbb76hRYsWAPTr14/58+eTnJzMwYMH2bt3L1dffXVRhigiIiWV3Q7trMGS+HODNUGxwLY1mcstOrsvDhERyVWR1mStXbuWefPm0bJlS9q0aQPAiy++yKeffsrmzZux2WyEhobywQcfABAWFsbtt99O8+bN8fT0ZPr06RpZUESkLGvfCxbNsIZx/2MldBvi7ojcz5lk1W1uDXohIiIlTpEmWZ07d8YYc9H6m2++OddjnnnmGZ555pmiDEtERK4UoWEkB1YlkGTYuEJJ1pmjcHS/tdyyi3tjERGRXGkYIhERKblsNs7WC7eWD26zkoyybGuWpoItNRCIiEhJpSRLRERKtHP1s/TN3bDcfYGUBM6mgpWqQUgj98YiIiK5UpIlIiIlWmKlmlCjvvXHxhXuDcadEuNg3x/WcsvrwWZzbzwiIpIrJVkiIlKy2WzWABgAUXvh2AH3xuMuO3+B9DRrueX17o1FRETypCRLRERKvvY9M5fLapPB7T9Zv338oFF798YiIiJ5UpIlIiIlX3Bta8hysJoM5jBybamWng7bf7aWm3UAL2/3xiMiInlSkiUiIlcGZ5PBU0fgr13ujaW4HdgCCTHWspoKioiUeEqyRETkytCuR+ZgD2WtyaCzqaDNBmGd3BuLiIj8LSVZIiJyZagUDA3aWst/rASHw73xFCfn0O31WkJgJffGIiIif0tJloiIXDnCM5oMnj8J+ze7NZRic/IvOHHYWlZTQRGRK4KSLBERuXK07Q72jLeustJkcOuazGUlWSIiVwQlWSIicuUIqAhNO1jLm1dnzhtVmjmbClYNger13BuLiIjki5IsERG5sjibDMadh92/uTWUIhcfAwc2W8str88c+ENEREo0JVkiInJladU1c56ojaW8yeCOtZkDfKipoIjIFSNfSZYxhs8++4x//vOfPProo3zxxRc4ytKoTiIiUnKU84ewztby5h8gNcW98RQl59Dt5QKgYVv3xiIiIvnmmZ+d7r77bj755BPX3++88w7Dhg1j7ty5RRaYiIhIrsJ7webvITkBtv8MbW9wd0SFLy0Vdq6zlptfCx75essWEZESIF81WT/99BNLlixh//79HD58mDVr1rB3796ijk1ERCRnYZ3Bx89aLq1NBvdtgsQ4a7nFde6NRURECiRfSdbgwYOpWrUq/v7++Pj4EBQUxHXXXcepU6c4efJktlouERGRIuftA627Wsvbf4KkBLeGUyScTQXtdgjr5N5YRESkQPKVZL388st07NiRGjVqUKNGDZo1a8arr75K9erVqVGjBnfffXdRxykiIpKdc5TB1BTYGuHWUAqdMZlDt9dvA/7l3RqOiIgUTL4aeLdt25ZWrVrluM0Yw/bt2ws1KBERkb/V5GrwKw8JMdbExFff7O6ICs/xg3A6ylrWqIIiIlecfCVZK1eupHLlytnWRUdHU6FCBQDOnDlT+JGJiIjkxdML2t0IP38Nu9ZDfDT4V3B3VIXDWYsF0EpJlojIlSZfSdb+/fv597//TWxsLMYYAHbv3s0ff/wBQJUqVYouQhERkdy072UlWY502LQaOg90d0SFw5lkVasLwXXcG4uIiBRYvpKsm266ibNnz2ZbZ9Os8yIi4m4N20KFqhB92moyWBqSrNhzcHCbtaymgiIiV6R8DXzRv39/Tp48yblz51w/X3/9dVHHJiIikje7Hdr1tJb3/QHnT7k3nsKw/Wdr4AvQ0O0iIleofNVkXX/99UyaNIng4GDXuj/++IP+/fsXWWAiIiL5Et4LfviflZj8sRJuGOruiC6Pc+h2v/JQv7V7YxERkUuSryTr+eef58CBA9nWqbmgiIiUCHWbQ9UQazS+Dcuv7CQrNQV2/WItt+gMHh7ujUdERC5JvpKspk2bMmvWLHx8fFzrNmzYUGRBiYiI5JvNZg2AsXwWHN5hJVtVQ9wd1aX5cwMkJ1rLaiooInLFylefrJo1a7Jz504OHz7s+tm/f39RxyYiIpI/7XtmLm9c4b44LpezqaCHJzTv6N5YRETkkuWrJuvDDz/M1jzQGIPNZuO1114rssBERETyLaShNdT5yb9gz+/Qa6S7Iyo4YzKHbm/YDsoFuDceERG5ZPlKsu655x46deqUbd3HH39cJAGJiIhckobtrCTr0HZwOKyRB68UDgcsmwk7f4G0FKsm67el1qAeV9J9iIgIkM8ka8aMGa7l1NRUPv30UzUXFBGRkqV+K1i3AJIT4Og+qNXY3RHlj8MBHzwOP3xqxW6zw7kTMP1h6NAP7ntViZaIyBUmX0kWQFRUFO+//z4ffvghJ0+eLMqYRERECi7rcOcHtlw5SdaG5bB+sZVc2T2sn8DKgIH1i6zarKt6uztKEREpgL9Nsr7//numT5/O4sWLSUtLw9PTk0GDBlG5cuXiiE9ERCRvDoeVqCyfBVF7rSTl56+h861XRg3QitlWzKnJ1t/evtaIidis9ctnK8kSEbnC5Jpk7dmzh0GDBrFz504AgoODGTVqFJ6enjz33HOcO3eu2IIUERHJkbOp3frFVkLi6QWJcfDLIvjgiSujqd2pI+DhnT3JcvLytbaLiMgVJdd3niZNmvDdd9/xr3/9i+uuu46IiAimTJlClSpVAKhUqVKxBSkiIpIjZ1M7/wrgV94akc/uARhY982VMZx7UG1IjMn827tc5nJqkrVdRESuKHl+vVe7dm2mTp3K8uXLWb9+Pc888wy7du0C4I8//sjXBY4cOUK3bt1o3rw5YWFhvPnmmwCcPXuWHj160KhRI3r06OGqGTPG8Mgjj9CwYUNatWqV7+uIiEgZ5GxqZ8t4O/P2y9hgg/Q0q6ldSddzJKQkAcb621mTZRzgSL8yh6MXESnj8tWGwsfHh+HDhzNlyhTuvPNOnnzySe666658XcDT05NXX32VnTt3sn79eqZPn87OnTuZOnUq3bt3Z+/evXTv3p2pU6cCsHTpUvbu3cvevXuZMWMGDzzwwKXfnYiIlG6njlhN6pyyNrVLd1wZTe3Ce0G1ulZCBVYTyIQYiI+2RhfMOtGyiIhcEfI9uqBTp06d6NSpE7Vq1crX/jVq1KBGjRoABAYG0qxZM6Kioli4cCEREREADB8+nK5duzJt2jQWLlzI3Xffjc1mo0OHDpw/f55jx465ziEiIuISVBv2bwYvb+tvu91KtFKSIDnuymhqZ7dDlZoQF20NeOEXCEHNrRqs9j1Lfp8yERG5SIGTLKfHHnuswMccOnSITZs2cc0113DixAlX4lS9enVOnDgBWEPF166d+aZYq1YtoqKilGSJiMjFeo605pMyjswmgz5+kJIIqSnQfZh748uP+Bg4HQX+5aHPfXDzve6OSERELtMlJ1kFFRcXx6233sobb7xB+fLls22z2WzYbLYCnW/GjBmuSZIjIyNdtWIFiaegx8iVRWVcNqicS7+8y7gc9J1kNa0DsNmpdGwn9TcvArsne/46SVxybseWDIFRO2kcGwvA3rPJxJTR/2c9l0s/lXHppzLOVCxJVmpqKrfeeivDhg1j4MCBAFSrVs3VDPDYsWMEBwcDEBISwpEjmW3oIyMjCQkJueicY8aMYcyYMQCEh4fTtWvXAsUUERFR4GPkyqIyLhtUzqXf35axw2GNIrh8ttUHq0IQ+PqCf3nCK3tDSf//WHYAAgMBaNd/GASWzdF79Vwu/VTGpZ/KOFORN/Q2xjB69GiaNWvG448/7lrfr18/5syZA8CcOXPo37+/a/3cuXMxxrB+/XoqVKigpoIiIpI7u92arPc/n8Gb62DyQqjdxNq2f7NbQ8uXv6xRe6lco8wmWCIipU2R12StXbuWefPm0bJlS9q0aQPAiy++yPjx47n99tuZOXMmdevW5fPPPwfg5ptv5rvvvqNhw4b4+fkxe/YVMPyuiIiULPVbwdljcHArGGMNKFFSHd5h/a7b3L1xiIhIoSnyJKtz584YY3Lctnr16ovW2Ww2pk+fXtRhiYhIaVa/tTVRcdx5qwlhcB13R5SzmDNw/qS1XEdJlohIaaFxYUVEpPSp3zpz+cBW98Xxdw7vzFxWTZaISKmhJEtEREqfmg3Bp5y1fGCLe2PJS9Ykq04z98UhIiKFSkmWiIiUPh4eENrCWi7JSdZfGUlWcB0oF+DeWEREpNAoyRIRkdLJ2WTw2AFIiHVvLDkxJrMmq26Ye2MREZFCpSRLRERKp3qtMpcPbnNfHLk5dwLizlnLaiooIlKqKMkSEZHSqV7LzOWS2GQw26AXqskSESlNlGSJiEjp5BcINepbywdL4AiDzvmx7PbMyZNFRKRUUJIlIiKlV4M21u9D2yE93a2hXOSvXdbv6vXB29e9sYiISKFSkiUiIqWXs19WciJE7XVvLFk5HJkjC2p+LBGRUkdJloiIlF71sw5+UYKaDJ6OhMQ4a7mOkiwRkdJGSZaIiJReQbUhoJK1XJIGv8g26IWSLBGR0kZJloiIlF42W2ZtVklMsjy9oGZD98YiIiKFTkmWiIiUbs5Jic8eh3Mn3RuLk7M/Vkgj8PJ2bywiIlLolGSJiEjpVtL6ZaWnw5Hd1rL6Y4mIlEpKskREpHSr0xw8PK3lAyUgyTp+EFKSrGX1xxIRKZWUZImISOnm5Q11mlnLBza7NRQgs6kgqCZLRKSUUpIlIiKln3O+rCN7MmuR3MU56IW3L1Sv595YRESkSCjJEhGR0s85+IUjPfvw6e7grMmq1QQ8PNwbi4iIFAklWSIiUvplHfzCnUO5p6VC1F5ruW6Y++IQEZEipSRLRERKvwpVoUpNa9mdg19E7bUSLdCgFyIipZiSLBERKRucTQYPbAFj3BPDX7syl5VkiYiUWkqyRESkbHAmWQkxcOKwe2I4vMP6XS4AqtZyTwwiIlLklGSJiEjZUBImJXbWZNVpBna9BYuIlFZ6hRcRkbKhZkPw8bOW97th8IuUJDi231rW/FgiIqWakiwRESkb7HYIbWEtu6Mm68gecDisZY0sKCJSqinJEhGRsqNBG+v38YMQH1O81/4ry/xcdZoV77VFRKRYKckSEZGyw539spyTIAdUgsrVi/faIiJSrJRkiYhI2RHaAmw2a7m458tyjixYp1lmDCIiUiopyRIRkbKjXIA1AAbA/s3Fd93EODj5l7Ws/lgiIqWekiwRESlb6mU0GTy8A9LTiueamoRYRKRMUZIlIiJlS4OMSYlTkyHyz+K5ppIsEZEyxdPdAYiIiBQrZ01WfDS8OsrqHxVUG3qOhPBeRTNJsLM/VsVgKF+l8M8vIiIlimqyRESkbKlcA6JPwam/rJqsxHirf9b0h+GDJzLnsipMzpEFVYslIlImKMkSEZGyZeMKqxbL7gGOdPDyBr/y4F8B1i+ythem2HNw9pi1XEdJlohIWaAkS0REypYVs8HbF7BZA1+kpVrrbXYr8Vo+u3Cvl60/lkYWFBEpC4o8yRo1ahTBwcG0aNHCtW7SpEmEhITQpk0b2rRpw3fffefa9tJLL9GwYUOaNGnC8uXLizo8EREpa04dgXKBmX8nJ2Yue/la2wvTXzszl+s0K9xzi4hIiVTkSdaIESNYtmzZRevHjh3L5s2b2bx5MzfffDMAO3fuZP78+ezYsYNly5bx4IMPkp6eXtQhiohIWRJU2/rtnBA4JSFzW2pS5vbC4uyPVbUW+Jcv3HOLiEiJVORJ1vXXX0/lypXzte/ChQsZPHgwPj4+1KtXj4YNG/Lbb78VcYQiIlKm9BwJxgFePtbfSRlJlnFYfbR6jSy8axmTObKgBr0QESkz3DaE+zvvvMPcuXMJDw/n1VdfpVKlSkRFRdGhQwfXPrVq1SIqKirH42fMmMGMGTMAiIyMJCIiokDXj4uLK/AxcmVRGZcNKufSr/DLuBz0nUT1rcsI2fsTYGN7WH+S/SqBf0WI94VCup5X/DlaRR0CIDLBxgn9r+ZKz+XST2Vc+qmMM7klyXrggQeYMGECNpuNCRMm8MQTTzBr1qwCnWPMmDGMGTMGgPDwcLp27Vqg4yMiIgp8jFxZVMZlg8q59CuSMnY4IDAVdiyFtBQ67voW7p4E7XsW7jxZWyIg0Or/1azXrTRr1K7wzl3K6Llc+qmMSz+VcSa3jC5YrVo1PDw8sNvt3Hvvva4mgSEhIRw5ktnhODIykpCQEHeEKCIipZndbjULbNEZajeF+q3gqt6FPxGxsz+WzWZdR0REygS3JFnHjh1zLX/zzTeukQf79evH/PnzSU5O5uDBg+zdu5err77aHSGKiEhpZ7NBqy7W8oHN1txZhc05smD1euDrV/jnFxGREqnImwsOGTKEiIgITp8+Ta1atXjuueeIiIhg8+bN2Gw2QkND+eCDDwAICwvj9ttvp3nz5nh6ejJ9+nQ8PDyKOkQRESmrWnWBHz61mg/uWAtX31w453U44Pdl8OPnkJxgDRP/21II71X4tWUiIlLiFHmS9emnn160bvTo0bnu/8wzz/DMM88UZUgiIiKWBm3ArzwkxFj9pwojyXI44IPHYe031nltdog9C9Mfhg794L5XlWiJiJRyepUXEZGyy8MTwjpZy7t+gdSUyz/nhuWwfjF4eoHdw2qWGFgZ/CvA+kWwccXlX0NEREo0JVkiIlK2OftlJSfCnxsu/3wrZlvJVWK89bfNBt4+Vo2W3QOWz778a4iISImmJEtERMq2Zh2tGi2ArRGXf75TR8DDCxJjrb/9ylsJFoCXr7VdRERKNSVZIiJStpXzhyZXWcvb1lh9qi5HUG2rD5Yx1t/+FTK3pSZZ20VEpFRTkiUiItIyo8lg9Gk4svvyztVzJCTGAcbql+WTMXS7cYAj3ZqfS0RESjUlWSIiIi2vz1zeEnF556oWas2J5UgHLx9IS7VGGYyPtkYXbN/z8s4vIiIlnpIsERGRSsFQp5m1vO3HyzvXr0usJoHBdaHpNVAuABq0hYena/h2EZEyosjnyRIREbkitOoCf+2Co/vhdBRUDSn4OVJT4Pel1nK7HvDP6YUbo4iIXBH0dZqIiAhkbzK49RJrs7atsZoGAnTsf/kxiYjIFUlJloiICEBII6hcw1q+1CTrl0XWb79AaN21UMISEZErj5IsERERsCYNdk5MvH8TxMcU7PhzJ2DXL9byVTeBl3fhxiciIlcMJVkiIiJOziaDDgfs+Llgx/76bebcWB36FW5cIiJyRVGSJSIi4tSondXUD6z+VfnlcMAvC63lWo2hdpPCj01ERK4YSrJEREScPDyheSdrecc6a7TA/Ni/2RqREKxaLJutSMITEZErg5IsERGRrJxNBpMT4M8N+TvGWYvl6QVX31Q0cYmIyBVDSZaIiEhWYZ2sGi3IX5PBxHj4Y5W13KoL+FcouthEROSKoCRLREQkq3L+0DjcWt62JnMwi9xsXAGpydayBrwQERGUZImIiFzM2WTw/En4a1fe+/662PpdMRiadSjauERE5IqgJEtERORCzvmyIO8mg8cOwoGt1vI1fcGut1UREVGSJSIicrFK1aB2U2t564+577d+ceZyh38UbUwiInLFUJIlIiKSE2eTwai9cOboxdvT0+DXJdZyw3YQXLv4YhMRkRJNSZaIiEhOWnfNXN6aQ5PBHesg9qy13FEDXoiISCYlWSIiIjkJaQSVq1vL23JoMuicG8vHD9p2L764RESkxFOSJSIikhObLbPJ4N6NEB+TuS3mDGz/yVpu3xN8yhV/fCIiUmIpyRIREclNq67Wb4cDdq7LXP/bd9Y6UFNBERG5iJIsERGR3DRqB+UCrGXnKIPGwC+LrOVqdaFeS/fEJiIiJZaSLBERkdx4eELza63lHWshLRUO7YDjB611HftbzQpFRESyUJIlIiKSF2eTweQEq2/W+oxaLLsdrr7ZbWGJiEjJ5enuAEREREq05h3B7mEN1/7aaDh5xKrhatUFAiu7OzoRESmBVJMlIiKSF19/SEmEU3/B0f3WJMTJCbD7V/jgicwBMERERDIoyRIREcnLhuVw7oRVm2X3sPpgeXlDpWpW08GNK9wdoYiIlDBKskRERPKyYrZVm0WWAS78KmQmXctnuy00EREpmZRkiYiI5OXUEfANAC+fzHUBFa3fXr7WdhERkSyUZImIiOQlqDakJmUOclEuMDPhSk2ytouIiGShJEtERCQvPUeCIx38y0PNhlA1xFpvHNb6XiPdG5+IiJQ4RZ5kjRo1iuDgYFq0aOFad/bsWXr06EGjRo3o0aMH586dA8AYwyOPPELDhg1p1aoVf/zxR1GHJyIikrfwXtDhHxAfbY0ymJYKCTHW3x36Qfue7o5QRERKmCJPskaMGMGyZcuyrZs6dSrdu3dn7969dO/enalTpwKwdOlS9u7dy969e5kxYwYPPPBAUYcnIiKSN7sd7nsNHp4ODdpCuQDr98PT4b5Xre0iIiJZFPlkxNdffz2HDh3Ktm7hwoVEREQAMHz4cLp27cq0adNYuHAhd999NzabjQ4dOnD+/HmOHTtGjRo1ijpMERGR3NntcFVv60dERORvFHmSlZMTJ064Eqfq1atz4sQJAKKioqhdO7MDca1atYiKisoxyZoxYwYzZswAIDIy0pW05VdcXFyBj5Eri8q4bFA5l34q47JB5Vz6qYxLP5VxJrckWVnZbDZsNtvf73iBMWPGMGbMGADCw8Pp2rVrgY6PiIgo8DFyZVEZlw0q59JPZVw2qJxLP5Vx6acyzuSWhuTVqlXj2LFjABw7dozg4GAAQkJCOHIkc76RyMhIQkJC3BGiiIiIiIjIJXFLktWvXz/mzJkDwJw5c+jfv79r/dy5czHGsH79eipUqKD+WCIiIiIickUp8uaCQ4YMISIigtOnT1OrVi2ee+45xo8fz+23387MmTOpW7cun3/+OQA333wz3333HQ0bNsTPz4/Zs2cXdXgiIiIiIiKFymaMMe4O4nJVrVqV0NDQAh1z6tQpgoKCiiYgKRFUxmWDyrn0UxmXDSrn0k9lXPqVtTI+dOgQp0+fznFbqUiyLkV4eDgbNmxwdxhShFTGZYPKufRTGZcNKufST2Vc+qmMM2kGRRERERERkUKkJEtERERERKQQldkkyznHlpReKuOyQeVc+qmMywaVc+mnMi79VMaZymyfLBERERERkaJQZmuyREREREREikKpSbL27NlDmzZtXD/ly5fnjTfeYMKECbRq1Yo2bdrQs2dPjh49CsArr7zi2rdFixZ4eHhw9uzZXM8j7ldYZQzw+uuvExYWRosWLRgyZAhJSUnuvDXJojDL+c0336RFixaEhYXpeVyCFLSMo6Oj+cc//kHr1q0JCwvLNofinDlzaNSoEY0aNXJNci8lQ2GWc+/evalYsSJ9+/Z11+1IDgqrjDdv3kzHjh0JCwujVatWfPbZZ+68LblAYZXz4cOHadeuHW3atCEsLIz333/fnbdV9EwplJaWZqpVq2YOHTpkoqOjXevffPNNc9999120/6JFi0y3bt3yPI+ULJdTxpGRkSY0NNQkJCQYY4y57bbbzOzZs4slbimYyynnbdu2mbCwMBMfH29SU1NN9+7dzd69e4stdsmf/JTxlClTzFNPPWWMMebkyZOmUqVKJjk52Zw5c8bUq1fPnDlzxpw9e9bUq1fPnD171i33IXm7nHI2xphVq1aZRYsWmT59+hR/8JIvl1PGe/bsMX/++acxxpioqChTvXp1c+7cuWK/B/l7l1POycnJJikpyRhjTGxsrKlbt66Jiooq/psoJqWmJiur1atX06BBA+rWrUv58uVd6+Pj47HZbBft/+mnnzJkyJA8zyMly+WWcVpaGomJiaSlpZGQkEDNmjWLJW4pmMsp5127dnHNNdfg5+eHp6cnXbp04euvvy622CV/8lPGNpuN2NhYjDHExcVRuXJlPD09Wb58OT169KBy5cpUqlSJHj16sGzZMnfdiuThcsoZoHv37gQGBroldsmfyynjxo0b06hRIwBq1qxJcHAwp06dcst9SN4up5y9vb3x8fEBIDk5GYfD4ZZ7KC6e7g6gKMyfPz/bB+pnnnmGuXPnUqFCBX744Yds+yYkJLBs2TLeeeedvz2PlByXU8YhISE8+eST1KlTh3LlytGzZ0969uxZrPFL/lxOObdo0YJnnnmGM2fOUK5cOb777jvCw8OLNX75e/kp44cffph+/fpRs2ZNYmNj+eyzz7Db7URFRVG7dm3XsbVq1SIqKqrY70H+3uWUs1wZCquMf/vtN1JSUmjQoEGxxi/5c7nlfOTIEfr06cO+fft45ZVXSveX3O6tSCt8ycnJpkqVKub48eMXbXvxxRfNs88+m23d/PnzTd++fQt0HnGvyy3js2fPmm7dupmTJ0+alJQU079/fzNv3rwij1sKpjCeyx9++KFp166due6668z9999vHn300aIMWQoov2X8xRdfmMcee8w4HA6zd+9eExoaaqKjo80rr7xinn/+edcxkydPNq+88kqxxS/5c7nl7PTDDz+ouWAJVVhlfPToUdO4cWPzyy+/FFvskn+FVc7GWM1Cr7rqqlL9ObvUfUW0dOlS2rVrR7Vq1S7aNmzYML766qts63KrrcrrPOJel1vGq1atol69egQFBeHl5cXAgQNZt25dkcctBVMYz+XRo0ezceNG1qxZQ6VKlWjcuHGRxiwFk98ynj17NgMHDsRms9GwYUPq1avH7t27CQkJ4ciRI65jIiMjCQkJKbb4JX8ut5yl5CuMMo6JiaFPnz5MmTKFDh06FGv8kj+F+VyuWbMmLVq04KeffiqW2N2h1CVZF/a92bt3r2t54cKFNG3a1PV3dHQ0P/74I/379//b80jJcbllXKdOHdavX09CQgLGGFavXk2zZs2KJ3jJt8J4Lp88eRKAv/76i6+//pqhQ4cWcdRSEPkt4zp16rB69WoATpw4wZ49e6hfvz69evVixYoVnDt3jnPnzrFixQp69epVvDchf+tyy1lKvsst45SUFAYMGMDdd9/NoEGDijd4ybfLLefIyEgSExMBOHfuHD///DNNmjQpxjsoZu6uSitMcXFxpnLlyub8+fOudQMHDjRhYWGmZcuWpm/fviYyMtK1bfbs2eaOO+7I13mkZCisMn722WdNkyZNTFhYmLnzzjtdo91IyVBY5dy5c2fTrFkz06pVK7Nq1apiiV3ypyBlHBUVZXr06GFatGhhwsLCsjXvnTlzpmnQoIFp0KCBmTVrVrHfh+StsMq5c+fOpmrVqsbX19eEhISYZcuWFfu9SM4Ko4znzZtnPD09TevWrV0/mzZtcsftSC4Ko5xXrFhhWrZsaVq1amVatmxpPvjgA7fcS3GxGWOMuxM9ERERERGR0qLUNRcUERERERFxJyVZIiIiIiIihUhJloiIiIiISCFSkiUiIiIiIlKIlGSJiIiIiIgUIiVZIiIibrRt27Zs880UpfT0dBYsWFAs1xIRKcuUZImICIsXL6ZLly4MGjSIcePG8fjjjzNixAjOnz9f4HP9/vvvVKlShQ0bNgCQnJzMuHHj8n387t276dGjBzabjVtvvZXJkyczZswYAgMDLymekmzx4sV8+umn1K1bl3nz5uHh4YHNZmPWrFnZ9hs+fDg2m43WrVuzceNGNm7cSOvWrbHZbPzrX//i2LFjLFmyhICAAGw2G3PnziU5Ofmi63l4eFCzZk1GjRpVXLcoIlImebo7ABERca/XXnuNJ554go8++ojhw4cDcPbsWTp06HBJ5/P09KRChQp4eXmRkpLCiBEjOHfuXL6Pb9q0KZ06dWLVqlX84x//YMSIEcTExNCzZ09sNtslxVQSnThxghEjRrB//368vb256667+Oqrr1i4cCEzZsxwJULnz5/H4XAA0LZtW9q3b+9a3rp1K3369KFGjRr07duXqlWrEh8fz913353rda+++momT57MrFmzlGyJiBQR1WSJiJRhR44cYdy4cTRu3NiVYAFUrlyZiRMnYrfbOXbsGI888ggTJ06kXr16/PDDD6SkpHDTTTdhs9mYNGkSqamp9O/fn6+//poPPviAgwcPsmnTJr788kvmz5/Ptm3bGDFiBO+++y42m43w8HDi4uKYN28et9xyC2lpabnGGBMTw0cffcSgQYNwOBy0a9cOm83GqlWrqF69OgsWLODQoUMMHTqUcePG8fzzzwNW07ixY8fy0EMPMXr0aMaPH8/rr79OTEwM4eHh2Gw2Dh06RMeOHV3LACtXruSee+7hrrvu4rvvvsu2/wsvvEBoaCgjRoxwxbd8+XKGDBnC9ddfz9NPP83BgwepWbMmNpuN9evX89dffxEeHs6ePXuy3dc777xDnTp1qFixomtdmzZt6Ny5M7/++itbt24FrNqu2267rcBle+jQIbp27crMmTO56qqrsNlsLFy4EIAbbriBadOmuZI3EREpXEqyRETKsG+//Za0tDTatGlz0bZhw4ZRvnx5XnrpJb777juee+450tLSeOWVV/D29uaVV14BrCTIy8uLTp06MXDgQLp27eo6x9ChQwFo2bIlH330EQ8++CAtW7bk6NGjlCtXjmrVqjFlyhQ8PXNuWPHNN9/Qvn17Dhw4AEClSpVo1aoVALt27WLkyJHUq1eP4cOHU7duXZ599lkmTpzIrl27eOedd3jvvfd47bXXuOWWW5g2bRrJycmUL1+eFi1aAODr60vr1q1d1zt37hy33347t912G4MHD2bUqFEEBga69r/lllvo3r07c+bM4cyZM8TGxjJ48GBuu+027rrrLt544w3q1KnDk08+CVg1gnXq1OHOO++kSZMm2e5t8eLFhIaGXnTP//znPwGYMWMGACkpKZQvXz7XMpwxYwbjx49n/Pjx2WoMfX19+e9//0vXrl3ZsmULnTt3pn///gDUr1+fP//8kx07duR6XhERuXRKskREyrD4+HgAjDG57vPYY4/xf//3f8ybN4+EhAQSExMBaNGiBddddx1z5szh8OHD1K9fP1/XvPfeezl27BjLli3j8OHDhIWF5brvgAEDWLlyJf7+/hdtGzhwIC+99BINGzZkzZo17Ny5k3nz5nHzzTdz9uxZ5s+fT0hICD4+PgQGBuYrtl9++YXz58+zatUqNm/eTHh4OLGxsa7tVatWxcvLC4C4uDjWrFnD+fPn8fT05N577+Wvv/7Cw8ODu+++G29vb2bPns3vv/+eLfF0OnDgQK73FRISwieffMKvv/7KNddck2fMY8aMYerUqUydOpVKlSq51levXp1GjRrx9NNPk5qayv/93/+5tgUEBLhiEBGRwqckS0SkDOvUqRNgjXCXGw8PD5577jnq1q1LYGBgtoTs/vvv5+zZs9x///384x//yNc1hw0bho+PD//9739zrcHKKjQ0lGuvvfai9TVr1gQgKSkJgKCgIO6//36WLFlCp06diI6OzrU5XG59u5wJZOvWrXnmmWdYsmRJrrVIxhhSU1MBXDVCQUFBgJWM9e/fn0WLFrF69eocawq9vLxyTG49PT25//77OX/+PO+9956rFu1S/Pbbb3zxxRfccccdXHPNNa7rOR+X0tTHTUSkJFGSJSJShnXo0IEhQ4awe/duFi9e7FqflpbGzJkzARg/fjyHDh2iTp06JCQkZDt+0KBBBAUF0bx5c3x8fHK8hpeXF+np6Zw+fRqw+nsNGDCARYsW0a1bt3zF2adPH9eyM1FwJghVqlShcePGfPnllxw4cIDt27ezZ88ewsPDOXbsGLGxsURHR2c7n7NmKy0tjTNnzrjWX3311Xh4eDB9+nTi4+P59ttvcxylz6ljx46UK1eO999/n6ioKH755RfXtUaPHk1KSkquxzds2JC4uLgct40ZMwZvb29uuummv3tosrkwafvXv/6Ft7c3L730EgkJCXz77bdAZg1mgwYNCnR+ERHJHyVZIiJl3Lx583j11VeZOHEigwcP5sknn+TZZ5/lxhtvBKB58+bY7Xb++9//0qdPH6KiolyDOHh7ezN69GjGjBnjOt+aNWsAWL9+PWDVdm3cuJGvv/7atc8999zDNddck2OfpF27dvHzzz8DsGzZMteAFGD1mdqyZQsAP/74o2v9J598QkhICO3bt2fJkiU0adKEl156ibZt2/KPf/yDpUuXZrvG6NGjCQoKYsKECZw9exaAn376idq1azNjxgz++usvwsLCsNvtJCYmugahWL9+PZs2bQLg559/plq1asyZMweHw0Hbtm3ZvHkzFSpUAKBHjx7UqVOHwYMH5/i433777a7meikpKbz11lssXryYrVu3EhwczKOPPsrAgQM5deoUixYtAmDTpk2uIdydcXz99dccO3aMBQsWcPLkSVeZLly4kDVr1tCiRQu++OILunbt6krCDhw4QPPmzWnevHmOsYmIyOWxmbwa4ouIiBSBlJQUZs6cyQMPPFAs14uIiKBbt2689NJLjB8/vliuCfDGG2/w2GOP5bgtJiaGpk2b8ttvv1GrVq1iiwmsmsG77ror1wRQREQuj2qyRESk2KxcuZIHHniAVatWMWjQIHeHUyRSU1Pp0aMHBw8epHr16rnuV758eb744gumTZtWjNHBDz/8QLNmzZRgiYgUISVZIiJSbFJTU/nyyy85evSoa5CIopaenu5qLvj7779f1K+ssKWlpXHkyBGeeuop+vbtm+e+nTp14sknn7xoDq2ikp6ejjEm20iDIiJS+NRcUEREREREpBCpJktERERERKQQKckSEREREREpREqyRERERERECpGSLBERERERkUKkJEtERERERKQQKckSEREREREpREqyRERERERECpGSLBERERERkUJU4pKspKQkrr76alq3bk1YWBgTJ050d0giIiIiIiL5ZjPGGHcHkZUxhvj4eAICAkhNTaVz5868+eabdOjQwd2hiYiIiIiI/C1PdwdwIZvNRkBAAACpqamkpqZis9nyPKZq1aqEhoYW6Drx8fH4+/tfaphyBVAZlw0q59JPZVw2qJxLP5Vx6VfWyvjQoUOcPn06x20lLskCSE9Pp3379uzbt4+HHnqIa665Js/9Q0ND2bBhQ4GuERERQdeuXS8jSinpVMZlg8q59FMZlw0q59JPZVz6lbUyDg8Pz3VbiWsumNX58+cZMGAAb7/9Ni1atMi2bcaMGcyYMQOAyMhI5s+fX6Bzx8XFuWrMpHRSGZcNKufST2VcNqicSz+VcelX1sr4ySefzLWip0QnWQCTJ0/Gz8+PJ598Mtd9wsPDVZMlF1EZlw0q59JPZVw2qJxLP5Vx6VfWyjivHKTEjS546tQpzp8/D0BiYiIrV66kadOm7g1KREREREQkn0pcn6xjx44xfPhw0tPTcTgc3H777fTt29fdYYmIiIi4GIfh4Nokti+IJ+Z4OuWre9DiFn/qdfLFZs97wC4RKf1KXJLVqlUrNm3a5O4wRERERHJkHIbvXz7P/ohE7B7g4W3j5O50Vk1JoWG3cnT7V0UlWlLsUlNTiYyMJCkpyW0xVKhQgV27drnt+kXB19eXWrVq4eXlVaDjSlySJSIiIlKSHVybxP6IRHwCba5pZjy8bBhj2PdDIvU6+VKvczk3RyllTWRkJIGBgYSGhv7t9EdFJTY2lsDAQLdcuygYYzhz5gyRkZHUq1evQMcqyRIRKWUK2oxJzZ5ECvY82L4gHrsHF32Qtdls2D0M276JV5IlxS4pKcmtCVZpZLPZqFKlCqdOnSrwsUqyRERKkYI2Y1KzJ5GCPw9ijqfj4Z3z88LD20bs8fTiCl0kGyVYhe9SH1MlWSIipUhBmzGp2ZNIwZ8H5at7cHJ3Oh5eF3/4Sk8xBNb3uGi9aoxFypYSN4S7iIhcurybMcG2b+Iva3+RK4VxGA78lMiiJ07z8bATLHriNAd+SsQ4Lp4etKDPgxa3+ONIt/prZLumMTjSoeUA/4ti+f7l86yaco6Tu1NITXBwcncKq6ac44dXzucYk4hc2VSTJSJSghX02++CNmNSsycpjYq6+V+9Tr406Fou4/wGD28b6SlWgtWwWzlCr/XNtr9qjKUkKy21rBERETzwwAMsXbqU0NBQd4ejmiwRkZLqUr79Ll/dg/SUnL8VT08xBFb3uKz9Ra4EWZMab387Hl7Wb59AG/t+SOTQuuxDXBf0eWCz27jhqYr0+E8lgpt64+1nJ7ipNz3+UynHfoyqMZaSqjTVsnbt2pVz587luU90dDRffvllscSjJEtEpIQq6AdFKHgzpoLuL3IlKOrmf2AlWvU6l6Pfq1UZ9kk1+r1alXqdyxVKDbNIcbmU95mSzNfXN9dtxhgeeeQR4uLiiiUWJVkiIsWoKPuJQGYzpuRYQ0q8g/RU63dyrMmxGVNB9xdxl4I8dy61+V9RPQ9UYywlVXHUsn7yySeEhoby3//+l0qVKjFy5EjefPNNbrjhBs6cOcOGDRuoVKkSs2bNYufOndSqVYvx48cD8P777/PYY4/lef6ZM2cybdo0nnrqKVdNVkxMzEXX2bFjBzt37mTZsmUsW7aMLVu2MGbMGCZOnMhdd9112fd5IfXJEhEpJsUxTLSzGVP9zr5s+yae2OPpBNb3oOUAf0Kvvbh9fUH3d95HaWi/L1eOgj53Cjr636U8DwqixS3+rJqSgjEm24dZ1RiLuxVHLeuAAQO488476dChAy+88AJ79+7l0UcfZd26daxYsYIhQ4Zw9913ExcXR/PmzRk5cmS2WuUpU6bkeu6dO3eyePFiFixYQGpqKu+99x4Aa9eupUKFChddJywsjK5du9K7d2/Gjx/PzTffzC233EK1atU4duwYNWrUuOz7dVKSJSJSTIpjmGjIbMaU3470Bdm/JM6rpaSv9Cvoc+dSkpqCPm8KoqADZYgUl0t9nykIPz8/AFq2bEnLli05duwYs2bNIioqiuTkZACGDRvGY489xiOPPEJqaiqff/45zz//PPHx8fj75/4lxPz582ndujUAXl5eVKlSBYCbbrqJNm3aXHSdrKZOncrmzZuZNWsW6enpOe5zOdRcUESkmBRHP5GiVtLa75emTtuSu4I+d0paM9iCDpQhUlyK+33m119/5T//+Q/Dhw+nYcOGrvVXX301p0+fZvny5XTp0oXatWszY8YMwsLC8jzf2bNniY2Nzfd1snrvvff46aefGDVqFAEBAZd3YzlQTZaIyGUoSC1KUQ8TXRzy/rBr2PZNfLEORa2hsa9cFz53Am9K48BPiYXy3Cnq5n+XoihrykQuVXG+zzgcDj7++GNX0nPixAnS09OJi4sjICCAoUOHMnHiRNatW0dkZCQvvvgie/fuzfOc1157LU8//TTPPfccfn5+pKSkkJiYmOt1PDw8SElJ4ezZs7z77rvMmDGD6OhoEhISiIuLIz09HQ+PwukjqSRLROQSXen9RC5FSRsl7VKTPjUxdK+cnjupSYZVU84VynMHlNSI5EdxvM988803AMyePZs+ffpw9913c/jwYRo1asSCBQu47bbbAKvJYIUKFbDb7QwaNIi9e/fi6Zl3qjJ48GB++uknOnXqxMCBA6lSpQorVqzI9Trdu3dn8uTJ1KlTh1tvvZXBgwdz//3307x5c+bPn88LL7xw2ffrpCRLROQSXen9RC5FcbTfL4hLSfpKYr+ysian547NbnM1Oy2M546I5E9Rv88MGDAgW3PEkydP5rhfo0aNGDt2LACVKlXi5Zdf/ttz2+1212AXAJMmTcrzOkOHDmXo0KEA9O7d27X/008//bfXKij1yRIRyaIoh1gvaf1ELkVJ6yd2KUNjl7R+ZaWFnjsiIplUkyUikqGoh1gvic3/Cqqk9RO7lBqOktavrDTQc0dEikpcXFyuzfj+/e9/U758+WKOKH+UZImIZCiOIdZLWvO/gippH3YvJekraf3KSgM9d0SkqAQEBDB16lR3h1FgSrJEpFQryAAHBa3hKKv9RErSh91LSfpKWr+y0kDPHZGS4cLnlFy+C5vH55eSLBEptYq6CVNJazpXVhU06dMH/MJXGM8d4zDqY3UBjYIpBeHr68uZM2eoUqWKEq1CYozhzJkz+PoW/DVJSZaIlFpF3YSppDWdk/xRcvz3CvrhvjCeO16+Nnr8p5KeOxk0CqYUVK1atYiMjOTUqVNuiyEpKemSEpKSzNfXl1q1ahX4OCVZInJFKcgEpsXRhKkkNZ2T/FFynLdL+XBfGM+diIhdeh5loYm2paC8vLyoV6+eW2OIiIigbdu2bo2hpFCSJSJXjIJOYKrmf5IbJce5u5QP93ruFD6NgilyZVOSJSJXjIJOYKrmfyIFdykf7vXcKXwaBVPkyqYkS0TcqqSN/qcaDinrLvXDvZ47hUujYIpc2ezuDkBEyi5n879VU85xcncKqQn/3969R0dVn/sf/+xMMoRLgqhABIQEkFvCRYwFBTVUoxVQLtUC4gVFc/TUc2orVFx4qZcULVq1yx5tPAiIHtKjRcAb1XiI4F1cqCBCVYgSLiIkgSQkmWTm+/sjPyIhCWRP9tzfr7W6EmbP7Hm2T7PWfub7zPP1ad9WjwpyS7V2YZmMr/HYVH/b/2rKjTyVPnlr638ywQxoWXKKS15P8yOLvR6jpBRu7oMhY3JH+bxNx0czBROIDGFXZO3cuVPjxo3TkCFDlJ6erieeeCLUIQEIkKPb/9wd4+RKqP95pP2v6P3qRs+3e/N3pIUp+64u6jbILXeHOHUb5Fb2XV2YzIWYYnxG29dXafXt+/X8zB+0+vb92r6+qskHGRI39+GCD4mAyBZ27YLx8fF69NFHNXLkSJWXl+uss85Sdna2hgwZEurQAJyA3bHPtP8BgWd3WiBDLMID33MDIlvYFVmnnXaaTjvtNElSUlKSBg8erF27dlFkAWHOn7HPbGCKSBHJm8LanRbIzX344EMiIHKFXZF1tKKiIm3cuFGjRo0KdSgATsCfsc9sYIpIEOmbwvo7LZCbewDwn2WObboOExUVFbrgggs0f/58TZ06tcnxvLw85eXlSZKKi4uVn59v+/ydOnVyJFaEJ3LsDE+FUVWZV95ayZUgtT/JJXenpjeUB4vrVFttmr3ZND6jhERLnXs1/lzHU2F0aE+dLJckHf06I+OVkk+Lb/a9jkaeo1+oc+zE/09DqWRHXf13r6xmYjT1f7Mnp4X+M9dQ5xmBR46jX6zleM6cOdqwYUOzx8KyyKqtrdXEiRN1ySWX6He/+90Jn5+ZmdniBbaksLBQWVlZfkaISECO26a5T++P/l7GsZ/ePz/zB9Ue9jW/KlVr5O4Qp5kvdG/TezSHPEe/UOd49e37tW+rR+6OTWdFeSp96jbIrcsfPTUEkbVOpMQf6jwj8Mhx9Iu1HB+vBgn9R1fHMMZo9uzZGjx4cKsKLACBYbf9z589XfjuByJBOG4Ka+c7Yv4MjAEAtE3YFVnvvfeeli1bpqFDh2rEiBGSpD/+8Y8aP358aAMDokC4bfwr8d0PhL9w2xSWaYEAEP7CrsgaO3Zsk705ALSd3RszJyb/cSOHaBBuK0FMCwSA8Bd2RRaAwAh0+x83cohW4fYBAtMCASD8UWQBESzc2v+4kUM0CrcPEMLxO2IAgMYosoAIRfsfEDzh9AFCuH1HDADQFEUWEKFo/wPCl51VZrvPD7fviAEAmqLIAsII7X9A5LO7ysy0QACIPhRZQJig/Q+IDnZXmZkWCADRhyILCCA7K1O0/wHRwe4qM9MCASD6UGQBAWJ3ZYr2PyA62F1lZlogAESfuFAHAESro1em3B3j5Eqo/9kuydI3a6tU9H51o+f72/5XU27kqfTJW1v/s6bc0P4HhFByiktej2n2mNdjlJTiatPzAQDhjyILsMH4jLavr9Lq2/fr+Zk/aPXt+7V9fZWMr+kN0vFXpqRNL1c2etzujdaR9r/su7qo2yC33B3i1G2QW9l3dWmySgYgeDImd5TPW7+qfLSWVpntPh8AEP5oFwRaKdCDKWj/A6KD3SEzDKUBgOhDkQW0UqAHU3CjhWhld8+oSGd3yAxDaQAg+lBkIWbZvfEL9GAKbrQQjeyuAEcLu6vMrEoDQHShyEJM8ufGLxj7UnGjhWhjdwUYAIBowOALxCS7k/8kBlMA/rA7AAYAgGjAShaixrHtf0mX1mn7+qpm2//82fyTwRSAfewBBQCIRaxkISocaf8ryC3Vvq0e1R72qbbaqCC3VGsXljUZse7PjR/7UgH2sQcUACAWUWQhKjTX/mfFWS22//lz40f7H2Afe0ABAGIR7YIIW3am/wV68l/D+Wj/A2xhawIAQCyiyEJYCvTGv9z4AcHB1gQAgFhEkYWgsbMyFeiNf7nxA4KHFWAAQKyhyEJQ2F2ZCkb7Hzd+AAAACAQGXyAo7O5L5W/739GT/4zPMPkPAAAAQcdKFvwWyMEUTrT/JSRayr6rC+1/AAAACCqKLPgl0IMpnGj/Kyz8ilZAAAAABB3tgvCL3fY/u/tSsfEvAAAAIlXYFVk33HCDunXrpoyMjFCHEnOMz2j7+iqtvn2/np/5g1bfvl/b11fJ+JoWR8dv/5M2vVzZ6HG7G5Ky8S8AAAAiVdi1C86aNUu33nqrrr322lCHElPCcV8qpv8BAAAgEoVdkXX++eerqKgo1GHEHPalAgAAAJxhmWP7t8JAUVGRJk6cqM2bN7f4nLy8POXl5UmSiouLlZ+fb+s9Kioq1KlTpzbFGQk8FUZVZV55ayVXgtT+JJfcnZoWNAeL61RbbZotdozPKCHRUudeP9XkngqjQ3vqZLkk6ejXGBmvlHxafLPvE0yxkuNYR56jHzmODeQ5+pHj6BdrOZ4zZ442bNjQ7LGwW8lqrZycHOXk5EiSMjMzlZWVZev1hYWFtl8TSY60/xUd1f53dHvese1/z8/8QbWHfc2vTNUauTvEadIL3Zuc/9uWzj879N+bivYcox55jn7kODaQ5+hHjqMfOf5JxBZZscbOnlQS7X8AAMQau/cKAAKHIisC2B1KIdnf/NeJfakAAEBo+HOvACBwwm6E+4wZM3TOOedo27Zt6tWrlxYtWhTqkELO7p5Ukv/T/9iXCgCAyOPPvQKAwAm7lazly5eHOoSgsLOkb3dVSqL9DwCAWOLPvQKAwAm7IisWBHpPKon2PwAAYok/9woAAifs2gVjgd0l/eQUl7ye5iftez1GSSmuJo/T/gcAQOzw514BQOCwkuWQQLb/+bsqRfsfAACxwZ97BQCBQ5HlgEC3/x1Zlao/v2myJ1VLq1K0/wEAEBv8vVcAEBgUWQ5gTyoAABBK3CsA4YUiywHsSQUAAEKNewUgfDD4wgHsSQUAAADgCFayHED7HwAAAIAjKLIcQPsfAAAAgCNoF3QA7X8AAAAAjmAlywG0/wEAAAA4giLLIbT/AQAAAJBoFwQAAAAAR1FkAQAAAICDKLIAAAAAwEEUWQAAAADgIIosAAAAAHCQX0VWaWmp7rzzTj344IOSpDfeeENlZWVOxgUAAAAAEcmvIuv666/Xt99+qz179kiSxo0bp+nTpzsaGAAAAABEIr+KrFGjRul///d/NXz4cPl8Pj311FP64IMPnI4NAAAAACKOX5sRHzhwQL169VJdXZ1++9vfqrq6WtOmTXM6NgAAAACIOH4VWQsXLlR6errWrFmjqqoqnXvuubrtttscDg0AAAAAIo9fRVZVVZWmTZum66+/Xp9++qlKSkqUkJDgdGwAAAAAEHH8KrJ69Oihv/71r6qtrdWNN96odu3aacqUKXr++eedjg8AAAAAIopfgy9ycnI0ZMgQ3XLLLTrllFO0ZcsW9enTx+nYAAAAACDi+FVkbdmyRRdccIGMMVq+fLlKSkq0ZMkSx4Jas2aNBg4cqP79++uhhx5y7LwAAAAAEGh+FVkvvfSSli5dqk2bNun8889XeXm5/ud//seRgLxer37961/rjTfe0JYtW7R8+XJt2bLFkXMDAAAAQKD5VWQlJibqvPPOU2Jionbv3q3U1FR9+eWXjgT08ccfq3///urbt6/cbremT5+uVatWOXJuAAAAAAg0v4qsuXPnqnv37kpNTVVaWprS0tL0hz/8wZGAdu3apdNPP73h37169dKuXbscOTcAAAAABJpf0wXffPNNzZ8/Xx999JHOPfdc7d69W5mZmU7Hdlx5eXnKy8uTJBUXF6uwsNDW6ysqKmy/BpGFHMcG8hz9yHFsIM/RjxxHP3L8E7+KrOuuu06jR4/WuHHjlJ+frzFjxuj555/XTTfd1OaAevbsqZ07dzb8u7i4WD179mzyvJycHOXk5EiSMjMzlZWVZet9CgsLbb8GkYUcxwbyHP3IcWwgz9GPHEc/cvwTv9oFq6qqdN5552nv3r3q0KGDZs2apc8++8yRgM4++2x9/fXX2rFjhzwej/Lz83X55Zc7cm4AAAAACDS/VrLmz5+v8ePHa+jQoZoxY4YuvfRS9erVy5mA4uP15JNP6pJLLpHX69UNN9yg9PR0R84NAAAAAIHmV5ElSf369dPevXvl8/k0aNAgvf/++xoyZIgjQY0fP17jx4935FwAAAAAEEx+FVk33HCDli5d2uTx6dOntzkgAAAAAIhkfhVZb7zxhq6++mp16NBBkmSM0aZNmxwNDAAAAAAika0ia9++fZLqJ/tNmDBBqampDcc+/PBDRwMDAAAAgEhkq8hKSUmRZVmSpAcffLDRMcuyVFdX51xkAAAAABCBbBVZo0aN0qBBg5o8bozRli1bHAsKAAAAACKVrSLrkUce0bRp03T66afr9ddfV5cuXRqOHTp0yPHgAAAAACDS2NqMeP78+dq9e7c++ugjPfHEE42OJScnOxoYAAAAAEQiW0VWx44dtX//fn3xxRcqKipqdKykpMTJuAAAAAAgItlqF+zevbu+/PJLGWPkdru1bt06SfXfyVq5cqUee+yxgAQJAAAAAJHCVpG1ZMmSRpsQL1q0qNFxiiwAAAAAsc5WkTV06FCNHDmyyePGGG3cuNGxoAAAAAAgUtkqsp577jkNHz682WOff/65IwEBAAAAQCSzVWS1VGCd6BgAAADCi/EZ7XivWptXVurQXq+SU1zKmNxRaWMSZcVZoQ4PiGi2iiwAAABEPuMz+r8/lenbwirFuSSX29K+rV4V5HrUf1x7jZt7EoUW0Aa2RrgDAAAg8u14r1rfFlapXZIld8c4uRLqf7ZLsvTN2ioVvV8d6hCBiEaRBQAAEGM2r6xUnEuyrMarVZZlKc4lbXq5MkSRAdGBIgsAACDGHNrrlcvdfDugy22pfK83yBEB0YUiCwAAIMYkp7jk9Zhmj3k9RkkpriBHBEQXiiwAAIAYkzG5o3ze+r1Oj2aMkc8rDZ3SMUSRAdGBIgsAACDGpI1JVL+s9qopN/JU+uStrf9ZU27Uf1x7pZ6bGOoQgYjGCHcAAIAYY8VZ+vnvT1LfsYna9HKlyvd6ldTXpaFTOir1XPbJAtqKIgsAACAGWXGW0sa2V9rY9qEOBYg6FFkAgLBifEY73qvW5pWVOrTXq6RL67R9fZXSxvDpOgAgMlBkAQDChvEZ/d+fyvRtYZXiXPWjpGurjQpyS9V/XHuNm3sShRYAIOwx+AIAEDZ2vFetbwur1C7JkrtjnFwJlqw4S+2SLH2ztkpF71eHOkQAAE6IIgsAEDY2r6xUnEuyrMarVZZlKc4lbXq5MkSRAQDQemFVZL344otKT09XXFycNmzYEOpwAABBdmivVy538+2ALrel8r3eIEcEAIB9YVVkZWRkaMWKFTr//PNDHQoAIASSU1zyekyzx7weo6QUV5AjAgDAvrAqsgYPHqyBAweGOgwAQIhkTO4on1cypnGhZYyRzysNndIxRJEBANB6YVVkAQBiW9qYRPXLaq+aciNPpU/eWiPjM6opN+o/rr1Sz00MdYgAAJyQZY79uDDALrroIu3du7fJ47m5uZo0aZIkKSsrS4888ogyMzNbPE9eXp7y8vIkScXFxcrPz7cVR0VFhTp16mTrNYgs5Dg2kOfo5KkwqirzylsruTpXqb27k9ydGN0ezfhbjn7kOPrFWo7nzJnT4hyJoO+TVVBQ4Mh5cnJylJOTI0nKzMxUVlaWrdcXFhbafg0iCzmODeQ5+pHj2ECeox85jn7k+Ce0CwIAAACAg8KqyHr55ZfVq1cvffDBB5owYYIuueSSUIcEAAAAALYEvV3weKZMmaIpU6aEOgwAAAAA8FtYrWQBAAAAQKSjyAIAAAAAB1FkAQAAAICDKLIAAAAAwEEUWQAAAADgIIosAAAAAHAQRRYAAAAAOIgiCwAAAAAcRJEFAAAAAA6iyAIAAAAAB1FkAQAAAICDKLIAAAAAwEEUWQAAAADgIIosAAAAAHAQRRYAAAAAOIgiCwAAAAAcFB/qAAAAABD+jM9ox3vV2ryyUof2epWc4lLG5I5KG5MoK84KdXhAWKHIAgAAwHEZn9H//alM3xZWKc4ludyW9m31qiDXo/7j2mvc3JMotICj0C4IAACA49rxXrW+LaxSuyRL7o5xciXU/2yXZOmbtVUqer861CECYYUiCwAAAMe1eWWl4lySZTVerbIsS3EuadPLlSGKDAhPFFkAAAA4rkN7vXK5m28HdLktle/1BjkiILxRZAEAAOC4klNc8npMs8e8HqOkFFeQIwLCG0UWAAAAjitjckf5vJIxjQstY4x8XmnolI4higwITxRZAAAAOK60MYnql9VeNeVGnkqfvLX1P2vKjfqPa6/UcxNDHSIQVhjhDgAAgOOy4iz9/Pcnqe/YRG16uVLle71K6uvS0CkdlXou+2QhcCJ1fzaKLAAAAJyQFWcpbWx7pY1tH+pQECMieX+2sGoXnDt3rgYNGqRhw4ZpypQpKisrC3VIAAAAAEIgkvdnC6siKzs7W5s3b9YXX3yhAQMGaMGCBaEOCQAAAIBDjM9o+/oqrb59v56f+YNW375f29dXyfiaTq+M5P3ZwqrIuvjiixUfX9/BOHr0aBUXF4c4IgAAAABOONL+V5Bbqn1bPao97NO+rR4V5JZq7cKyJoVWJO/PZpljZ3GGicsuu0zTpk3T1Vdf3ezxvLw85eXlSZKKi4uVn59v6/wVFRXq1KlTm+NE+CLHsYE8Rz9yHBvIc/Qjx9HvRDn2VBgd2lMnyyVJRxdPRsYrJZ8WL3ennx4/WFyn2mrT7PeujM8oIdFS516hGzExZ84cbdiwodljQS+yLrroIu3du7fJ47m5uZo0aVLD7xs2bNCKFSuaLA82JzMzs8ULbElhYaGysrJsvQaRhRzHBvIc/chxbCDP0Y8cR6ejp//VZXym+M0jWpz+t/r2/dq31SN3x6bNdJ5Kn7oNcuvyR09teGz7+ioV5JaqXZLVqCYwxqim3Cj7ri4hHcRyvBok6KVfQUHBcY8vWbJEr776qt5+++1WFVgAAAAAgu/Y6X9dhpj/3/7X/PQ/u+1/R/Znqz+/kcttyeup3wA73PdnC6sR7mvWrNGf/vQnvfPOO+rQoUOowwEAAADQgqOn/1mWJVn10/+MMfpmbZXSxiQ2WmlKTnFp31avXAlNCy2vxyipr6vRY5G8P1tYFVm33nqrampqlJ2dLal++MXTTz8d4qgAAACA6Gd349/jT/8z2vRyZaMiK2NyRxXkemSMadL+5/NKQ6d0bPIekbo/W1gVWd98802oQwAAAABijj8b/8ZS+59dYVVkAQAAAHCGnZWpJq1/klwJVoutf1Jstf/ZRZEFAAAARBm7K1N2W/+k2Gr/syusNiMGAAAA0HZHr0y5O8bJlVD/s12SpW/WVqno/epGz/dn498j7X815UaeSp9k6n/WlJuoa/+zi5UsAAAAIALYaf+zuzJlt/VPatr+Vxdnqdsgd1S2/9lFkQUAAACEObvtf3ZXpvxp/ZMat/8VFsYr6/pTm31erKFdEAAAAAhzdtv/klNc8npMs+fyeoySUhqvTB3b+uetpfWvLVjJAgAAAEIgkO1/dlemYmnyXzBQZAEAAABBFuj2P3/2pIqVyX/BQJEFAAAAxx27SpN0aZ22r69qdpUmFtndl4o9qSILRRYAAAAc1dwqTW21UUFuabOrNNEinNr/JFamQonBFwAAAHBUc0MarDirxSEN0eBIYVmQW6p9Wz2qPezTvq0eFeSWau3CMhlf4yEU/rb/MZgiMlBkAQAAwFHHX6WRNr1cGaLIAifQ0/+OtP9l39VF3Qa55e4Qp26D3Mq+q0vUrgxGMtoFAQAA4Ci7qzThivY/+IuVLAAAADjK7ipNOKL9D23BShYAAAAc5c8qTTDYWZli+h/agiILAAAAjmpujybjMyFdpbG7LxXtf2gLiiwAAAA4qrlVmoRES9l3dQnZKo3dlalgbP6L6EWRBQAAAMcdu0pTWPiVoys2dlr/JPsrU7T/oS0osgAAABBydoomu61/kv2VKdr/0BZMFwQAAEBI2Z3kZ3dPKsn+xEOm/6EtKLIAAAAQUnaLJn82O86Y3FE+b/1K1NFaWpli81+0Be2CAAAACCm735fyZ7NjfwZT0P4Hf1FkAQAAIKTsFk12h1JIDKZAcFFkAQAAIKTsFk3+bnbMyhSChe9kAQAAIKTsfl+KoRQId2G1knX33Xdr1apViouLU7du3bRkyRL16NEj1GEBAAAggOx+X4rWP4S7sCqy5s6dqwceeECS9Je//EX333+/nn766RBHBQAAgEDyp2ii9Q/hLKyKrOTk5IbfKysrm0yYAQAAQHSiaEI0CasiS5Lmz5+v5557Tp07d9batWtDHQ4AAAAA2GKZY79hGGAXXXSR9u7d2+Tx3NxcTZo0qeHfCxYsUHV1te67775mz5OXl6e8vDxJUnFxsfLz823FUVFRoU6dOtl6DSILOY4N5Dn6kePYQJ6jHzmOfrGW4zlz5mjDhg3NHgt6kdVa33//vcaPH6/Nmzef8LmZmZktXmBLCgsLlZWV5Wd0iATkODaQ5+hHjmMDeY5+5Dj6xVqOj1eDhNUI96+//rrh91WrVmnQoEEhjAYAAAAA7Aurlaxf/vKX2rZtm+Li4tSnTx89/fTT6tmz5wlfd+qppyo1NdXWe/3444/q2rWrn5EiEpDj2ECeox85jg3kOfqR4+gXazkuKirS/v37mz0WVkVWMPnTYojIQo5jA3mOfuQ4NpDn6EeOox85/klYtQsCAAAAQKSjyAIAAAAAB8VskZWTkxPqEBBg5Dg2kOfoR45jA3mOfuQ4+pHjn8Tsd7IAAAAAIBBidiULAAAAAAIhaoqsbdu2acSIEQ3/S05O1uOPP667775bw4YN04gRI3TxxRdr9+7dkqSFCxc2PDcjI0Mul0slJSUtngeh51SOJemxxx5Tenq6MjIyNGPGDFVXV4fy0nAUJ/P8xBNPKCMjQ+np6fwdhxG7OT548KAuu+wyDR8+XOnp6Vq8eHHDuZYuXaozzjhDZ5xxhpYuXRqqS0IznMzzL37xC5100kmaOHFiqC4HzXAqx5999pnOOeccpaena9iwYfr73/8eysvCMZzK83fffaeRI0dqxIgRSk9P19NPPx3Kywo8E4Xq6upM9+7dTVFRkTl48GDD40888YT5t3/7tybPX716tRk3btxxz4Pw0pYcFxcXm9TUVHP48GFjjDFXXnmlWbx4cVDihj1tyfOmTZtMenq6qaysNLW1tebCCy80X3/9ddBiR+u0Jse5ubnm97//vTHGmH379pkuXbqYmpoac+DAAZOWlmYOHDhgSkpKTFpamikpKQnJdeD42pJnY4wpKCgwq1evNhMmTAh+8GiVtuR427Zt5l//+pcxxphdu3aZlJQUU1paGvRrwIm1Jc81NTWmurraGGNMeXm56dOnj9m1a1fwLyJIomYl62hvv/22+vXrpz59+ig5Obnh8crKSlmW1eT5y5cv14wZM457HoSXtua4rq5OVVVVqqur0+HDh9WjR4+gxA172pLnr776SqNGjVKHDh0UHx+vCy64QCtWrAha7Gid1uTYsiyVl5fLGKOKigqdfPLJio+P1z//+U9lZ2fr5JNPVpcuXZSdna01a9aE6lJwHG3JsyRdeOGFSkpKCknsaJ225HjAgAE644wzJEk9evRQt27d9OOPP4bkOnB8bcmz2+1Wu3btJEk1NTXy+XwhuYZgiQ91AIGQn5/f6IZ6/vz5eu6559S5c2etXbu20XMPHz6sNWvW6MknnzzheRA+2pLjnj17as6cOerdu7fat2+viy++WBdffHFQ40frtCXPGRkZmj9/vg4cOKD27dvr9ddfV2ZmZlDjx4m1Jse33nqrLr/8cvXo0UPl5eX6+9//rri4OO3atUunn356w2t79eqlXbt2Bf0acGJtyTMig1M5/vjjj+XxeNSvX7+gxo/WaWued+7cqQkTJuibb77RwoULo/tD7tAupDmvpqbGnHLKKWbv3r1Njv3xj38099xzT6PH8vPzzcSJE22dB6HV1hyXlJSYcePGmX379hmPx2MmTZpkli1bFvC4YY8Tf8v//d//bUaOHGnOO+88c/PNN5vf/OY3gQwZNrU2xy+++KK57bbbjM/nM19//bVJTU01Bw8eNAsXLjQPPPBAw2vuv/9+s3DhwqDFj9Zpa56PWLt2Le2CYcqpHO/evdsMGDDAfPDBB0GLHa3nVJ6NqW8LPfvss6P6PjvqPiJ64403NHLkSHXv3r3JsZkzZ+of//hHo8daWq063nkQWm3NcUFBgdLS0tS1a1clJCRo6tSpev/99wMeN+xx4m959uzZ+vTTT7Vu3Tp16dJFAwYMCGjMsKe1OV68eLGmTp0qy7LUv39/paWlaevWrerZs6d27tzZ8Jri4mL17NkzaPGjddqaZ4Q/J3J86NAhTZgwQbm5uRo9enRQ40frOPm33KNHD2VkZGj9+vVBiT0Uoq7IOva7N19//XXD76tWrdKgQYMa/n3w4EG98847mjRp0gnPg/DR1hz37t1bH374oQ4fPixjjN5++20NHjw4OMGj1Zz4W963b58k6fvvv9eKFSt01VVXBThq2NHaHPfu3Vtvv/22JOmHH37Qtm3b1LdvX11yySV68803VVpaqtLSUr355pu65JJLgnsROKG25hnhr6059ng8mjJliq699lpdccUVwQ0erdbWPBcXF6uqqkqSVFpaqnfffVcDBw4M4hUEWaiX0pxUUVFhTj75ZFNWVtbw2NSpU016eroZOnSomThxoikuLm44tnjxYjNt2rRWnQfhwakc33PPPWbgwIEmPT3dXH311Q3TbhAenMrz2LFjzeDBg82wYcNMQUFBUGJH69jJ8a5du0x2drbJyMgw6enpjdp7Fy1aZPr162f69etnnn322aBfB47PqTyPHTvWnHrqqSYxMdH07NnTrFmzJujXguY5keNly5aZ+Ph4M3z48Ib/bdy4MRSXgxY4kec333zTDB061AwbNswMHTrU/O1vfwvJtQSLZYwxoS70AAAAACBaRF27IAAAAACEEkUWAAAAADiIIgsAAAAAHESRBQAAAAAOosgCAAAAAAdRZAEAEEKbNm1qtN9MIHm9Xq1cuTIo7wUAsYwiCwCgV155RRdccIGuuOIK3XHHHfrd736nWbNmqayszPa5PvnkE51yyinasGGDJKmmpkZ33HFHq1+/detWZWdny7Is/fKXv9T999+vnJwcJSUl+RVPOHvllVe0fPly9enTR8uWLZPL5ZJlWXr22WcbPe+6666TZVkaPny4Pv30U3366acaPny4LMvS3LlztWfPHr366qvq1KmTLMvSc889p5qamibv53K51KNHD91www3BukQAiEnxoQ4AABBaf/7zn3X77bdryZIluu666yRJJSUlGj16tF/ni4+PV+fOnZWQkCCPx6NZs2aptLS01a8fNGiQxowZo4KCAl122WWaNWuWDh06pIsvvliWZfkVUzj64YcfNGvWLH377bdyu9265ppr9I9//EOrVq1SXl5eQyFUVlYmn88nSTrzzDN11llnNfz+xRdfaMKECTrttNM0ceJEnXrqqaqsrNS1117b4vv+7Gc/0/33369nn32WYgsAAoSVLACIYTt37tQdd9yhAQMGNBRYknTyySfr3nvvVVxcnPbs2aP//M//1L333qu0tDStXbtWHo9Hl156qSzL0h/+8AfV1tZq0qRJWrFihf72t79px44d2rhxo1566SXl5+dr06ZNmjVrlv7rv/5LlmUpMzNTFRUVWrZsmSZPnqy6uroWYzx06JCWLFmiK664Qj6fTyNHjpRlWSooKFBKSopWrlypoqIiXXXVVbrjjjv0wAMPSKpvjfvtb3+rX//615o9e7bmzZunxx57TIcOHVJmZqYsy1JRUZHOOeecht8l6a233tKNN96oa665Rq+//nqj5z/44INKTU3VrFmzGuL75z//qRkzZuj888/XnXfeqR07dqhHjx6yLEsffvihvv/+e2VmZmrbtm2NruvJJ59U7969ddJJJzU8NmLECI0dO1YfffSRvvjiC0n1q11XXnml7dwWFRUpKytLixYt0tlnny3LsrRq1SpJ0s9//nM9/PDDDcUbAMBZFFkAEMNee+011dXVacSIEU2OzZw5U8nJyVqwYIFef/113Xfffaqrq9PChQvldru1cOFCSfVFUEJCgsaMGaOpU6cqKyur4RxXXXWVJGno0KFasmSJ/v3f/11Dhw7V7t271b59e3Xv3l25ubmKj2++seLll1/WWWedpe3bt0uSunTpomHDhkmSvvrqK11//fVKS0vTddddpz59+uiee+7Rvffeq6+++kpPPvmknnrqKf35z3/W5MmT9fDDD6umpkbJycnKyMiQJCUmJmr48OEN71daWqpf/epXuvLKKzV9+nTdcMMNSkpKanj+5MmTdeGFF2rp0qU6cOCAysvLNX36dF155ZW65ppr9Pjjj6t3796aM2eOpPoVwd69e+vqq6/WwIEDG13bK6+8otTU1CbX/B//8R+SpLy8PEmSx+NRcnJyiznMy8vTvHnzNG/evEYrhomJiXrmmWeUlZWlzz//XGPHjtWkSZMkSX379tW//vUvffnlly2eFwDgP4osAIhhlZWVkiRjTIvPue222/TII49o2bJlOnz4sKqqqiRJGRkZOu+887R06VJ999136tu3b6ve86abbtKePXu0Zs0afffdd0pPT2/xuVOmTNFbb72ljh07Njk2depULViwQP3799e6deu0ZcsWLVu2TOPHj1dJSYny8/PVs2dPtWvXTklJSa2K7YMPPlBZWZkKCgr02WefKTMzU+Xl5Q3HTz31VCUkJEiSKioqtG7dOpWVlSk+Pl433XSTvv/+e7lcLl177bVyu91avHixPvnkk0aF5xHbt29v8bp69uypF154QR999JFGjRp13JhzcnL00EMP6aGHHlKXLl0aHk9JSdEZZ5yhO++8U7W1tXrkkUcajnXq1KkhBgCA8yiyACCGjRkzRlL9hLuWuFwu3XffferTp4+SkpIaFWQ333yzSkpKdPPNN+uyyy5r1XvOnDlT7dq10zPPPNPiCtbRUlNTde655zZ5vEePHpKk6upqSVLXrl11880369VXX9WYMWN08ODBFtvhWvpu15ECcvjw4Zo/f75effXVFleRjDGqra2VpIYVoa5du0qqL8YmTZqk1atX6+233252pTAhIaHZ4jY+Pl4333yzysrK9NRTTzWsovnj448/1osvvqhp06Zp1KhRDe935L9LNH3HDQDCCUUWAMSw0aNHa8aMGdq6dateeeWVhsfr6uq0aNEiSdK8efNUVFSk3r176/Dhw41ef8UVV6hr164aMmSI2rVr1+x7JCQkyOv1av/+/ZLqv+81ZcoUrV69WuPGjWtVnBMmTGj4/UihcKRAOOWUUzRgwAC99NJL2r59uzZv3qxt27YpMzNTe/bsUXl5uQ4ePNjofEdWturq6nTgwIGGx3/2s5/J5XLpr3/9qyorK/Xaa681O6XviHPOOUft27fX008/rV27dumDDz5oeK/Zs2fL4/G0+Pr+/furoqKi2WM5OTlyu9269NJLT/SfppFji7a5c+fK7XZrwYIFOnz4sF577TVJP61g9uvXz9b5AQCtQ5EFADFu2bJlevTRR3Xvvfdq+vTpmjNnju655x5ddNFFkqQhQ4YoLi5OzzzzjCZMmKBdu3Y1DHFwu92aPXu2cnJyGs63bt06SdKHH34oqX6169NPP9WKFSsannPjjTdq1KhRzX4n6auvvtK7774rSVqzZk3DQAqp/jtTn3/+uSTpnXfeaXj8hRdeUM+ePXXWWWfp1Vdf1cCBA7VgwQKdeeaZuuyyy/TGG280eo/Zs2era9euuvvuu1VSUiJJWr9+vU4//XTl5eXp+++/V3p6uuLi4lRVVdUwhOLDDz/Uxo0bJUnvvvuuunfvrqVLl8rn8+nMM8/UZ599ps6dO0uSsrOz1bt3b02fPr3Z/+6/+tWvGtr1PB6P/vKXv+iVV17RF198oW7duuk3v/mNpk6dqh9//FGrV6+WJG3cuLFhhPuROFasWKE9e/Zo5cqV2rdvX0NOV61apXXr1ikjI0MvvviisrKyGoqw7du3a8iQIRoyZEizsQEA2sYyx2vEBwAgADwejxYtWqRbbrklKO9XWFiocePGacGCBZo3b15Q3lOSHn/8cd12223NHjt06JAGDRqkjz/+WL169QpaTFL9yuA111zTYgEIAGgbVrIAAEHz1ltv6ZZbblFBQYGuuOKKUIcTELW1tcrOztaOHTuUkpLS4vOSk5P14osv6uGHHw5idNLatWs1ePBgCiwACCCKLABA0NTW1uqll17S7t27G4ZEBJrX621oF/zkk0+afK/MaXV1ddq5c6d+//vfa+LEicd97pgxYzRnzpwme2gFitfrlTGm0aRBAIDzaBcEAAAAAAexkgUAAAAADqLIAgAAAAAHUWQBAAAAgIMosgAAAADAQRRZAAAAAOAgiiwAAAAAcND/AyZ6oFxXnQowAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 864x576 with 2 Axes>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["exp.analysis.drawer.figure"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["**实验包检索**\n", "\n", "编译完成一个基础实验后，你可以通过执行下面的方法检索您的设备通道参数信息是否配置正确"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experiment information as follows:\n", "{\n", "    \"label\":\"CavityFreqSpectrum\",\n", "    \"measure_aio\":{\n", "        \"pulse_period\":100,\n", "        \"Z_dc_control\":[\n", "            0.744732,\n", "            0.236088,\n", "            0.203233,\n", "            0.525287,\n", "            0.0,\n", "            0.0,\n", "            0.0,\n", "            6.0\n", "        ],\n", "        \"XY_control\":[\n", "            {\n", "                \"channel\":13,\n", "                \"trigger_delay\":[\n", "                    0\n", "                ],\n", "                \"output_frequency\":4289.612,\n", "                \"intermediate_frequency\":566.667,\n", "                \"pulse_power\":-21.3\n", "            }\n", "        ],\n", "        \"Z_flux_control\":[\n", "            {\n", "                \"channel\":1,\n", "                \"trigger_delay\":[\n", "                    0\n", "                ]\n", "            }\n", "        ],\n", "        \"Read_out_control\":[\n", "            {\n", "                \"channel\":1,\n", "                \"trigger_delay\":[\n", "                    110.0\n", "                ],\n", "                \"sampling_delay\":[\n", "                    500\n", "                ],\n", "                \"sampling_time_width\":2850,\n", "                \"output_frequency\":6213.0,\n", "                \"intermediate_frequency\":600.0,\n", "                \"pulse_power\":-35.0,\n", "                \"power_attenuation\":30\n", "            }\n", "        ]\n", "    },\n", "    \"qubit_info\":[],\n", "    \"sweep_control\":[\n", "        {\n", "            \"channel\":1,\n", "            \"func\":\"Read_out_control:output_frequency\",\n", "            \"repeat\":1000,\n", "            \"synchro\":true,\n", "            \"combination_points_length\":61,\n", "            \"points_length\":61\n", "        }\n", "    ]\n", "}\n"]}], "source": ["exp.experiment_info()"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 总结\n", "\n", "前面分步骤进行了每一步的操作，下面演示一个完成的 CavitySpecturm 实验的编写流程"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 15:10:30\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1m\n", "|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |        tcp://************:8088         |\n", "|    point_label     |              person_point              |\n", "|       sample       |               test_chip                |\n", "|      env_name      |                D00_1011                |\n", "|      version       |                 0.1.6                  |\n", "|=============================================================|\n", "\u001b[0m\n", "\u001b[33m2022-10-14 15:10:30\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1mQubit_(bit=0) is already exist in current environment.\u001b[0m\n", "\u001b[33m2022-10-14 15:10:30\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mCavityFreqSpectrum register success, id 63490b66bc8867eab7f7ef1a\u001b[0m\n", "\u001b[33m2022-10-14 15:10:30\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\test_chip\\CavityFreqSpectrum\\q0\\2022-10-14\\15.10.30\\\u001b[0m\n"]}, {"data": {"application/json": {"ascii": false, "bar_format": null, "colour": null, "elapsed": 0.013962030410766602, "initial": 0, "n": 0, "ncols": null, "nrows": 29, "postfix": null, "prefix": "", "rate": null, "total": 61, "unit": "it", "unit_divisor": 1000, "unit_scale": false}, "application/vnd.jupyter.widget-view+json": {"model_id": "1f003e64ce4b436f91a047bb5fb37cca", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/61 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 15:10:31\u001b[0m | \u001b[32m RESULT \u001b[0m | \u001b[32mExperiment results as follow:\n", "==========================================================\n", "| name | describe |  value   | unit |      quality       | \n", "----------------------------------------------------------\n", "|  fr  |    fc    | 7380.301 | MHz  | R²=0.9975(perfect) | \n", "==========================================================\u001b[0m\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "\n", "from pyQCat.tools import qarange\n", "from pyQCat.context import ExperimentContext\n", "from pyQCat.invoker import Invoker\n", "from pyQCat.parameters import get_parameters\n", "from pyQCat.experiments import CavityFreqSpectrum\n", "\n", "\n", "%matplotlib inline\n", "pd.set_option('max_colwidth', None)\n", "\n", "conf_file = r'E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf'\n", "\n", "username = \"test_04\"\n", "password = \"123456\"\n", "\n", "context = ExperimentContext(conf_file)\n", "Invoker.verify_account(username=username, password=password)\n", "\n", "# 实验相关\n", "q_name_list = [\"q0\"]\n", "\n", "environment_elements = [\"q0\", \"q1\", \"q2\", \"q3\", \"c0\", \"c1\", \"c2\"]\n", "\n", "context.configure_inst()\n", "context.configure_qubits(q_name_list)\n", "\n", "context.minimize_compensate()\n", "# context.maximize_compensate()\n", "context.configure_environment(environment_elements)\n", "\n", "context.configure_crosstalk_dict()\n", "\n", "exp = CavityFreqSpectrum.from_experiment_context(context)\n", "\n", "# 模拟测试数据路径 simulator_data_path，不提供将真机测试\n", "exp.set_experiment_options(\n", "    is_opt=True,\n", "    simulator_data_path='../scripts/simulator/data/CavityFreqSpectrum/',\n", "    readout_power=-35\n", ")\n", "\n", "exp.set_analysis_options(\n", "    is_plot=True,\n", "    quality_bounds=[0.98, 0.95, 0.85]\n", ")\n", "\n", "exp.run()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "7c7d380762183b0e586540d44967a4d319470bde080fc51cec06363acaef295f"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.12"}}, "nbformat": 4, "nbformat_minor": 2}