# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.tensor.rst:2
msgid "pyQCat.analysis.algorithms.tensor"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.tensor:1
msgid "Compute the tensor product of a list (or array) of matrices"
msgstr "计算矩阵列表（或数组）的张量积"

#: of pyQCat.analysis.algorithms.tomography.tensor
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.tensor:3
msgid "matrices squence"
msgstr "矩阵序列"

#: of pyQCat.analysis.algorithms.tomography.tensor
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.tensor:5
msgid "the result of `np.korn`"
msgstr "`np.korn` 计算结果"

