﻿pyQCat.experiments.single.XYZTiming
===================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: XYZTiming

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~XYZTiming.__init__
      ~XYZTiming.acquire_pulse
      ~XYZTiming.cal_fidelity
      ~XYZTiming.experiment_info
      ~XYZTiming.from_experiment_context
      ~XYZTiming.get_qubit_str
      ~XYZTiming.get_xy_pulse
      ~XYZTiming.get_z_pulse
      ~XYZTiming.jupyter_schedule
      ~XYZTiming.options_table
      ~XYZTiming.play_pulse
      ~XYZTiming.plot_schedule
      ~XYZTiming.run
      ~XYZTiming.set_analysis_options
      ~XYZTiming.set_experiment_options
      ~XYZTiming.set_multiple_IF
      ~XYZTiming.set_multiple_index
      ~XYZTiming.set_parent_file
      ~XYZTiming.set_run_options
      ~XYZTiming.set_sweep_order
      ~XYZTiming.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~XYZTiming.analysis
      ~XYZTiming.analysis_options
      ~XYZTiming.experiment_options
      ~XYZTiming.run_options
   
   