# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2020/11/28
# __author:       <PERSON>
# __corporation:  OriginQuantum

"""
CavityFreqSpectrum and CavityPowerOptimize experiment.

CavityFreqSpectrum experiment is used to calibrate the qubit cavity frequency.
CavityPowerOptimize experiment is used to calibrate the read power of the qubit.
"""

import numpy as np

from ....analysis import CavityAnalysis
from ....log import pyqlog
from ....pulse.pulse_function import f12_pi_pulse, pi_pulse, zero_pulse
from ....structures import MetaData, Options
from ...top_experiment_v1 import StandardContext, TopExperimentV1 as TopExperiment


class CavityFreqSpectrum(TopExperiment):
    """CavityFreqSpectrum experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            fc_list (List, np.ndarray): Scan cavity frequency list.
            readout_power (float): Set readout channel power.
            add_pi_pulse (bool): True or False, True means set XY pulse.
            pi_amp (int): When add_pi_pulse is True, XY drag pulse amp value.

        """
        options = super()._default_experiment_options()

        options.set_validator("fc_list", list, limit_null=True)
        options.set_validator("points", int)
        options.set_validator("readout_power", (-40, -10, 1))
        options.set_validator("pi_amp", (-1, 1, 2))
        options.set_validator("add_pi_pulse", bool)
        options.set_validator("extend_f12", bool)
        options.set_validator("scope", float)
        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("mode", ["IF", "BF"])

        options.fc_list = None
        options.readout_power = None
        options.add_pi_pulse = False
        options.extend_f12 = False
        options.pi_amp = None
        options.z_amp = None
        options.scope = 3
        options.points = 61
        options.mode = "IF"

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.quality_bounds = [0.98, 0.95, 0.85]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.bf_list = None
        options.support_context = [
            StandardContext.QC,
            StandardContext.CC,
            StandardContext.CPC,
        ]
        return options

    def _check_options(self):
        """
        1. Set the default fc_list
        2. Set the default read power
        3. If the cavity frequency is not measured when the X gate is added,
            the drive parameters will not be bound, and the pulse timing diagram
            will not be drawn.
        4. If the IQ criterion is passed in the current experiment, a warning
            will be given, which is not recommended for the current experiment,
            and the IQ criterion will be automatically blanked
        """
        super()._check_options()
        fc_list = self.experiment_options.fc_list
        readout_power = self.experiment_options.readout_power
        scope = self.experiment_options.scope
        points = self.experiment_options.points

        if fc_list is None:
            probe_freq = self.qubit.probe_freq or 6000
            fc_list = np.round(np.linspace(probe_freq - scope, probe_freq + scope, points), 3).tolist()

        if self.experiment_options.mode == "BF":
            probe_freq = self.qubit.probe_freq or 6000
            baseband_freq = self.qubit.Mwave.baseband_freq
            bf_list = np.array(fc_list) - probe_freq + baseband_freq
            pyqlog.debug(f"Sweep baseband freq: {bf_list}")
            self.run_options.bf_list = bf_list.tolist()

        if readout_power is None:
            readout_power = self.qubit.probe_power or -30

        self.set_experiment_options(
            data_type="amp_phase",
            fc_list=fc_list,
            readout_power=readout_power,
        )

        # CavityFreqSpectrum shield discriminator
        self.discriminator = None

        if self.experiment_options.z_amp is not None:
            com_bits = self.compensates.keys()
            if len(com_bits) == 1:
                self.qubit.ac = self.experiment_options.z_amp
            else:
                for bit in com_bits:
                    if bit.name == self.qubit.name:
                        bit.ac = self.experiment_options.z_amp
        # Shq 2024/04/10
        # for async mode.
        self.set_run_options(x_data=fc_list, analysis_class=CavityAnalysis)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        readout_power = self.experiment_options.readout_power
        readout_point_amp = self.qubit.readout_point.amp
        metadata.draw_meta = {
            "readout_power": (readout_power, "db"),
            "readout_point_amp": (readout_point_amp, "V"),
        }
        return metadata

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        if self.coupler and self.is_coupler_exp is False:
            for key, result in self.analysis.results.items():
                if key == "fr":
                    result.extra["path"] = "Coupler.probe_freq"
                elif key == "power":
                    result.extra["path"] = "Coupler.probe_power"

    @staticmethod
    def set_xy_pulses(builder):
        if builder.experiment_options.add_pi_pulse is True:
            # Calibrate readout frequency, need play a drag pulse on XY module.
            # bind_drive must be True.
            xy_pulse = pi_pulse(builder.qubit)
            pi_amp = builder.experiment_options.pi_amp
            if pi_amp is not None:
                xy_pulse.amp = pi_amp
        else:
            xy_pulse = zero_pulse(builder.qubit)

        xy_pulse = xy_pulse()
        if builder.experiment_options.extend_f12 is True:
            xy_pulse += f12_pi_pulse(builder.qubit)()

        builder.play_pulse("XY", builder.qubit, xy_pulse)

    @staticmethod
    def update_instrument(builder):
        fc_list = builder.experiment_options.fc_list
        readout_power = builder.experiment_options.readout_power

        builder.inst.set_power("Readout_control", builder.qubit.readout_channel, readout_power)

        if builder.experiment_options.mode == "IF":
            builder.inst.sweep_freq(
                "Readout_control",
                builder.qubit.readout_channel,
                points=fc_list,
                repeat=builder.experiment_options.repeat,
            )
