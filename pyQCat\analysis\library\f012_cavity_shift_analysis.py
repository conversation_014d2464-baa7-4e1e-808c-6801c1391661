# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/11/10
# __author:       <PERSON><PERSON><PERSON>

import numpy as np

from ..curve_analysis import CurveAnalysis
from ..specification import ParameterRepr
from ...structures import Options
from ..quality import BaseQuality
from ...types import QualityDescribe


class CavityShiftF012Analysis(CurveAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Options:
            distance_flag (bool): True means use distance opt probe freq.
            intersection_flag (bool): True means select intersection point.
            diff_threshold (float): Twice cavity frequency difference.

        """
        options = super()._default_options()

        options.diff_threshold = 0.1
        options.subplots = (3, 1)
        options.curve_drawer.set_options(figsize=(10, 12))
        options.x_label = "Cavity Frequency [MHz]"
        options.y_label = [
            "Amp",
            "Phase",
            "Distance",
        ]

        options.result_parameters = [
            ParameterRepr(name="frs", repr="frs", unit="MHz"),
            ParameterRepr(name="mean_fr", repr="mean_fc", unit="MHz"),
            ParameterRepr(name="f1", repr="fc_1", unit="MHz", param_path="Qubit.probe_freq",),
            ParameterRepr(name="max_distance", repr="(f01, f02, f12)", unit="v"),
        ]

        return options

    def _extract_result(self, data_key: str):
        """Extract cavity frequency from twice
        cavity frequency spectrum experiment data.

        Args:
            data_key (str): The basis for selecting data.
        """
        analysis_data = self.analysis_datas[data_key]
        y = analysis_data.y

        mean_freq_opt = round(float(np.mean(y)), 3)
        self.results.frs.value = y.tolist()
        self.results.mean_fr.value = mean_freq_opt
        self.results.f1.value = round(float(y[1]), 3)

        f0, f1, f2 = y
        diff_threshold  = self.options.diff_threshold 
        if abs(f1 - f0) > diff_threshold and abs(f2 - f1) > diff_threshold:
            self._quality = BaseQuality.instantiate(QualityDescribe.normal)
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)

        if self.has_child is True:
            freq_arr = None
            amp_arr_list = []
            phase_arr_list = []
            for i, _ in enumerate(self.experiment_data.x_data):
                child_data = self.experiment_data.child_data(index=i)
                if freq_arr is None:
                    freq_arr = child_data.x_data
                amp_arr_list.append(child_data.y_data.get("Amp"))
                phase_arr_list.append(child_data.y_data.get("Phase"))

            # distance optimize cavity frequency
            amp_arr0, amp_arr1, amp_arr2 = amp_arr_list
            phase_arr0, phase_arr1, phase_arr2 = phase_arr_list

            distance_arr01 = self._cal_distance(amp_arr0, amp_arr1, phase_arr0, phase_arr1)
            distance_arr02 = self._cal_distance(amp_arr0, amp_arr2, phase_arr0, phase_arr2)
            distance_arr12 = self._cal_distance(amp_arr1, amp_arr2, phase_arr1, phase_arr2)

            self.results.max_distance.value = [
                max(distance_arr01),
                max(distance_arr02),
                max(distance_arr12),
            ]
            self.results.max_distance.extra.update(
                {
                    'out_flag': False,
                    'distance01': distance_arr01,
                    'distance02': distance_arr02,
                    'distance12': distance_arr12,
                }
            )

    @staticmethod
    def _cal_distance(amps0, amps1, phase0, phase1):
        x_0, y_0 = amps0 * np.cos(phase0), amps0 * np.sin(phase0)
        x_1, y_1 = amps1 * np.cos(phase1), amps1 * np.sin(phase1)
        return np.sqrt(np.square(x_0 - x_1) + np.square(y_0 - y_1))

    def _visualization(self):
        """Plot twice cavity result and distance."""
        # Set plot title.
        self.drawer.set_options(title=self._description())

        if self.has_child is True:
            x_arr = None
            self.drawer.set_options(raw_data_format="plot")
            colors = ['red', 'blue', 'purple']
            distance_labels = ['distance01', 'distance02', 'distance12']
            for i, _ in enumerate(self.experiment_data.x_data):
                child_data = self.experiment_data.child_data(index=i)
                if x_arr is None:
                    x_arr = child_data.x_data

                amp_arr = child_data.y_data.get("Amp")
                phase_arr = child_data.y_data.get("Phase")

                amp_label, phase_label = f"amp{i}", f"phase{i}"

                self.drawer.draw_raw_data(x_data=x_arr,
                                          y_data=amp_arr,
                                          ax_index=0,
                                          label=amp_label,
                                          color=colors[i])

                self.drawer.draw_raw_data(x_data=x_arr,
                                          y_data=phase_arr,
                                          ax_index=1,
                                          label=phase_label,
                                          color=colors[i])

                self.drawer.draw_raw_data(x_data=x_arr,
                                          y_data=self.results.max_distance.extra.get(distance_labels[i]),
                                          ax_index=2,
                                          label=distance_labels[i],
                                          color=colors[i])

        self.drawer.format_canvas()
