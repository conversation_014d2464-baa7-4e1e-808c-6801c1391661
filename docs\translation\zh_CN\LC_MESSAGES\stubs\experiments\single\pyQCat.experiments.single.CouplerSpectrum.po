# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:2
msgid "pyQCat.experiments.single.CouplerSpectrum"
msgstr ""

#: of pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
" :py:class:`~pyQCat.experiments.single.qubit_spectrum.QubitSpectrum`"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
#: of pyQCat.experiments.coupler_experiment.CouplerBaseExperiment.__init__:1
msgid "Create a new experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.single.CouplerSpectrum.__init__>`\\"
" \\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`acquire_pulse "
"<pyQCat.experiments.single.CouplerSpectrum.acquire_pulse>`\\ "
"\\(acq\\_pulse\\[\\, qubit\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Set measure pulse."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`cal_fidelity "
"<pyQCat.experiments.single.CouplerSpectrum.cal_fidelity>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Calculate the fidelity matrix."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`experiment_info "
"<pyQCat.experiments.single.CouplerSpectrum.experiment_info>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Show experiment informations."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.single.CouplerSpectrum.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.single.CouplerSpectrum.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`get_xy_pulse "
"<pyQCat.experiments.single.CouplerSpectrum.get_xy_pulse>`\\ \\(qubit\\, "
"options\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`get_z_pulse "
"<pyQCat.experiments.single.CouplerSpectrum.get_z_pulse>`\\ "
"\\(zpulse\\_params\\, z\\_amp\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`jupyter_schedule "
"<pyQCat.experiments.single.CouplerSpectrum.jupyter_schedule>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Show experiment pulses's schedules in Jupyter Notebook."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.single.CouplerSpectrum.options_table>`\\ "
"\\(\\[mode\\, detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`play_pulse "
"<pyQCat.experiments.single.CouplerSpectrum.play_pulse>`\\ \\(name\\, "
"base\\_qubit\\, pulse\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Play pulse for XY control and Z flux control."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`plot_schedule "
"<pyQCat.experiments.single.CouplerSpectrum.plot_schedule>`\\ "
"\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
"Plot the pulse schedule which is very important to confirm experiment "
"logical correction."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.single.CouplerSpectrum.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Run QubitSpectrum experiment and perform analysis."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.single.CouplerSpectrum.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.single.CouplerSpectrum.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_multiple_IF "
"<pyQCat.experiments.single.CouplerSpectrum.set_multiple_IF>`\\ "
"\\(\\*IF\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Used for multiple qubits experiment, set mutiple IF."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_multiple_index "
"<pyQCat.experiments.single.CouplerSpectrum.set_multiple_index>`\\ "
"\\(\\*args\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
"Used for multiple qubits experiment, set mutiple index. :type args: "
":py:class:`list` :param args: Setting the start and end values :type "
"channel: :py:class:`int` :param channel: channel settings Usage::     "
">>>experiment = TopExperiment()     >>>experiment.set_multiple_index([0, "
"1500], [100, 900], channel=1)."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.single.CouplerSpectrum.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.single.CouplerSpectrum.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_sweep_order "
"<pyQCat.experiments.single.CouplerSpectrum.set_sweep_order>`\\ "
"\\(order\\_list\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid "Old interface."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:41:<autosummary>:1
msgid ""
":py:obj:`sweep_readout_trigger_delay "
"<pyQCat.experiments.single.CouplerSpectrum.sweep_readout_trigger_delay>`\\"
" \\(channel\\, sweep\\_delay\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CouplerSpectrum.rst:43
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._set_xy_pulses:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.single.CouplerSpectrum.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._set_xy_pulses:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._set_xy_pulses:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.single.CouplerSpectrum.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._set_xy_pulses:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._set_xy_pulses:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.single.CouplerSpectrum.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._set_xy_pulses:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.single.CouplerSpectrum.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._set_xy_pulses:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._set_xy_pulses:1
msgid "Set RabiScanWidth experiment XY pulses."
msgstr ""

#: of pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._set_z_pulses:1
msgid "Set z pulse."
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._update_instrument:1
msgid "Update instrument parameters before running."
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " "
#~ ":py:class:`~pyQCat.experiments.single.qubit_spectrum.QubitSpectrum`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.single.CouplerSpectrum.__init__>`\\ "
#~ "\\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`acquire_pulse "
#~ "<pyQCat.experiments.single.CouplerSpectrum.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.single.CouplerSpectrum.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.single.CouplerSpectrum.get_qubit_str>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_xy_pulse "
#~ "<pyQCat.experiments.single.CouplerSpectrum.get_xy_pulse>`\\ "
#~ "\\(qubit\\, options\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_z_pulse "
#~ "<pyQCat.experiments.single.CouplerSpectrum.get_z_pulse>`\\ "
#~ "\\(zpulse\\_params\\, z\\_amp\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`play_pulse "
#~ "<pyQCat.experiments.single.CouplerSpectrum.play_pulse>`\\ "
#~ "\\(name\\, base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot_schedule "
#~ "<pyQCat.experiments.single.CouplerSpectrum.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`run <pyQCat.experiments.single.CouplerSpectrum.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_analysis_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_IF "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_index "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_sweep_order "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis "
#~ "<pyQCat.experiments.single.CouplerSpectrum.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.single.CouplerSpectrum.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.single.CouplerSpectrum.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.single.CouplerSpectrum.run_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :class:`pyQCat.experiments.single.qubit_spectrum.QubitSpectrum`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.single.CouplerSpectrum.__init__>`\\ "
#~ "\\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`acquire_pulse "
#~ "<pyQCat.experiments.single.CouplerSpectrum.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_info "
#~ "<pyQCat.experiments.single.CouplerSpectrum.experiment_info>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.single.CouplerSpectrum.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.single.CouplerSpectrum.get_qubit_str>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_xy_pulse "
#~ "<pyQCat.experiments.single.CouplerSpectrum.get_xy_pulse>`\\ "
#~ "\\(qubit\\, options\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_z_pulse "
#~ "<pyQCat.experiments.single.CouplerSpectrum.get_z_pulse>`\\ "
#~ "\\(zpulse\\_params\\, z\\_amp\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`jupyter_schedule "
#~ "<pyQCat.experiments.single.CouplerSpectrum.jupyter_schedule>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.single.CouplerSpectrum.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`play_pulse "
#~ "<pyQCat.experiments.single.CouplerSpectrum.play_pulse>`\\ "
#~ "\\(name\\, base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot_schedule "
#~ "<pyQCat.experiments.single.CouplerSpectrum.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`run <pyQCat.experiments.single.CouplerSpectrum.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_analysis_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_IF "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_index "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_sweep_order "
#~ "<pyQCat.experiments.single.CouplerSpectrum.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.single.CouplerSpectrum.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.single.CouplerSpectrum.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.single.CouplerSpectrum.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.single.CouplerSpectrum.run_options>`\\"
#~ msgstr ""

