# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/31
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>


from copy import deepcopy
from typing import Union

import numpy as np

from ....analysis.library_v2.photon_analysis import (
    AmpToPhotonAnalysis,
    DephaseRamseyCompAnalysis,
    PhotonNumMeasVsAmpAnalysis,
    PhotonNumMeasVsFreqAnalysis,
    PhotonScanReadoutFreqAnalysis,
    PhotonScanReadoutFreqV2Analysis,
)
from ....analysis.standard_curve_analysis import StandardCurveAnalysis
from ....errors import ExperimentFlowError
from ....structures import MetaData, Options
from ....tools import qarange
from ...composite_experiment import CompositeExperiment
from ...single.readout.photon_experiment import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>n<PERSON>um<PERSON><PERSON>,
    <PERSON>n<PERSON><PERSON><PERSON>,
)
from ..error_quantification.nm_base import NMBase


class DePhaseRamseyComposite(CompositeExperiment):
    _sub_experiment_class = DePhaseRamsey

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("t_relax_list", list)
        options.t_relax_list = qarange(100, 1000, 100)
        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        init_state = self.child_experiment.experiment_options.init_state
        acq_pulse_type = self.child_experiment.experiment_options.acq_pulse_type
        metadata.draw_meta = {"InitState": init_state, "AcqType": acq_pulse_type}
        return metadata

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.t_relax_list,
            analysis_class=DephaseRamseyCompAnalysis,
        )

    def _setup_child_experiment(
        self, dr_exp: DePhaseRamsey, index: int, t_relax: float
    ):
        dr_exp.run_options.index = index
        total = len(self.run_options.x_data)
        dr_exp.set_parent_file(self, f"t_relax={t_relax}ns", index, total)
        dr_exp.set_experiment_options(t_relax=t_relax)
        self._check_simulator_data(dr_exp, index)

    def _handle_child_result(self, exp: DePhaseRamsey):
        result = exp.analysis.results
        photon_num = result.n0.value
        exp.analysis.provide_for_parent["photon_num"] = photon_num


class PhotonScanReadoutFreq(CompositeExperiment):
    _sub_experiment_class = DePhaseRamsey

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("sweep_list", list)
        options.set_validator("scope", dict)
        options.sweep_list = None
        options.scope = {"l": 0.2, "r": 0.0, "s": 0.05}
        return options

    def _check_options(self):
        super()._check_options()
        probe_freq = self.qubit.probe_freq
        if self.experiment_options.sweep_list is None:
            scope = self.experiment_options.scope
            sweep_list = qarange(
                probe_freq - scope.get("l"), probe_freq + scope.get("r"), scope.get("s")
            )
            self.set_experiment_options(sweep_list=sweep_list)
        sweep_list = self.experiment_options.sweep_list
        self.set_run_options(
            x_data=[x for x in sweep_list for _ in range(2)],
            analysis_class=PhotonScanReadoutFreqAnalysis,
        )

    def _metadata(self):
        meta = super()._metadata()
        meta.process_meta["sweep_list"] = self.experiment_options.sweep_list
        return meta

    def _setup_child_experiment(self, dr_exp, index: int, probe_freq: float):
        dr_exp.run_options.index = index
        total = len(self.run_options.x_data)
        init_state = index % 2

        dr_exp.set_parent_file(
            self, f"IS={init_state} probe_freq={probe_freq}MHz", index, total
        )
        dr_exp.set_experiment_options(init_state=init_state)
        dr_exp.qubit.probe_freq = probe_freq
        self._check_simulator_data(dr_exp, index)

    def _handle_child_result(self, exp: DePhaseRamsey):
        exp.analysis.provide_for_parent["photon_num"] = exp.analysis.results.n0.value


class PhotonScanReadoutFreqV2(PhotonScanReadoutFreq):
    _sub_experiment_class = PhotonNumMeas

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.quality_bounds = [0.98, 0.85, 0.8]

        return options

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            analysis_class=PhotonScanReadoutFreqV2Analysis,
        )

    def _setup_child_experiment(self, dr_exp, index: int, probe_freq: float):
        dr_exp.run_options.index = index
        total = len(self.run_options.x_data)
        init_state = index % 2

        dr_exp.set_parent_file(
            self, f"IS={init_state} probe_freq={probe_freq}MHz", index, total
        )
        dr_exp.set_experiment_options(init_state=init_state, fc=probe_freq)
        self._check_simulator_data(dr_exp, index)

    def _handle_child_result(self, exp: PhotonNumMeas):
        exp.analysis.provide_for_parent["photon"] = exp.analysis.results.photon.value


class PhotonNumMeasVsTime(CompositeExperiment):
    _sub_experiment_class = PhotonNumMeas

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("xy_delay_list", list)
        options.xy_delay_list = qarange(100, 1000, 20)
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("chi_eff", float)
        options.chi_eff = -1.0
        options.plot_2d = True
        options.x_label = "XY Delay (ns)"
        return options

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.xy_delay_list,
            analysis_class=StandardCurveAnalysis,
        )

    def _setup_child_experiment(
        self, pmt_exp: PhotonNumMeas, index: int, xy_delay: float
    ):
        pmt_exp.run_options.index = index
        total = len(self.run_options.x_data)
        pmt_exp.set_parent_file(self, f"xy_delay={xy_delay}ns", index, total)
        pmt_exp.set_experiment_options(xy_delay=xy_delay)
        self._check_simulator_data(pmt_exp, index)

    def _handle_child_result(self, exp: PhotonNumMeas):
        if exp.analysis:
            exp.analysis.provide_for_parent.update(
                {
                    "photon_num": exp.analysis.results.photon.value,
                }
            )
        else:
            raise ExperimentFlowError(
                self.label,
                "child exp without analysis object, maybe child exp error!",
            )


class PhotonNumMeasVsAmp(CompositeExperiment):
    _sub_experiment_class = PhotonNumMeas

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("amp_list", list)
        options.amp_list = qarange(0.0, 0.08, 0.02)
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("kappa", float)  # 2pi * MHz
        options.kappa = 1 * 2 * np.pi
        options.effective_chi = -0.6 * 2 * np.pi
        options.plot_2d = True
        options.x_label = "RD Amp (v)"
        return options

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.amp_list,
            analysis_class=PhotonNumMeasVsAmpAnalysis,
        )
        self.set_analysis_options(
            effective_chi=self.analysis_options.child_ana_options.effective_chi
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        chi_eff = self.analysis_options.child_ana_options.effective_chi
        kappa = self.analysis_options.kappa
        if chi_eff is not None:
            metadata.draw_meta = {
                "chi_eff": (round(chi_eff, 3), "rad/us"),
                "kappa": (round(kappa, 3), "rad/us"),
            }
            metadata.process_meta = {"readout_amp": self.qubit.Mwave.amp}
        return metadata

    def _setup_child_experiment(self, pma_exp: PhotonNumMeas, index: int, amp: float):
        pma_exp.run_options.index = index
        total = len(self.run_options.x_data)
        pma_exp.set_parent_file(self, f"amp={amp}v", index, total)
        pma_exp.set_experiment_options(pre_amp=amp)
        self._check_simulator_data(pma_exp, index)

    def _handle_child_result(self, exp: PhotonNumMeas):
        if exp.analysis:
            exp.analysis.provide_for_parent.update(
                {
                    "photon_num": exp.analysis.results.photon.value,
                }
            )
        else:
            raise ExperimentFlowError(
                self.label,
                "child exp without analysis object, maybe child exp error!",
            )

    def _set_result_path(self):
        if self.analysis.analysis_datas.photon_num:
            x = self.analysis.analysis_datas.photon_num.x
            y = self.analysis.analysis_datas.photon_num.y
            self.file.save_data(x, y, name=f"{self}(amp-photon)")


class PhotonNumMeasVsFreq(CompositeExperiment):
    _sub_experiment_class = PhotonNumMeas

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("fc_list", list)
        options.set_validator("scope", dict)
        options.fc_list = None
        options.scope = {"l": 10, "r": 10, "s": 0.5}
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("wq0", float)
        options.set_validator("wrm", float)
        options.set_validator("kappa", float)
        options.set_validator("chi", float)
        options.set_validator("A", float)

        options.wq0 = 4500
        options.wrm = 6500
        options.kappa = 0.5
        options.chi = 0.5
        options.A = 0.04

        return options

    def _check_options(self):
        super()._check_options()

        if not self.experiment_options.fc_list:
            scope = self.experiment_options.scope
            fc_list = qarange(
                self.qubit.probe_freq - scope.get("l", 5),
                self.qubit.probe_freq + scope.get("r", 5),
                scope.get("s", 0.2),
            )
            self.experiment_options.fc_list = fc_list

        self.set_run_options(
            x_data=self.experiment_options.fc_list,
            analysis_class=PhotonNumMeasVsFreqAnalysis,
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta["init_state"] = (
            self.child_experiment.experiment_options.init_state
        )
        return metadata

    def _setup_child_experiment(self, pma_exp: PhotonNumMeas, index: int, freq: float):
        pma_exp.run_options.index = index
        total = len(self.run_options.x_data)
        pma_exp.set_parent_file(self, f"fc={freq}MHz", index, total)
        pma_exp.set_experiment_options(fc=freq)
        # pma_exp.qubit.Mwave.baseband_freq += round(freq - pma_exp.qubit.probe_freq, 3)
        self._check_simulator_data(pma_exp, index)

    def _handle_child_result(self, exp: PhotonNumMeas):
        if exp.analysis:
            target_offset = exp.analysis.results.t_offset.value
            exp.analysis.provide_for_parent.update(
                {
                    "target_offset": target_offset,
                }
            )
        else:
            raise ExperimentFlowError(
                self.label,
                "child exp without analysis object, maybe child exp error!",
            )

    def _set_result_path(self):
        pass


class AmpToPhoton(CompositeExperiment):
    _sub_experiment_class = PhotonRamsey

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("pre_amp_list", list)
        options.pre_amp_list = qarange(0.0, 0.08, 0.02)
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("kappa", float)  # MHz
        options.set_validator("chi_eff", float)  # MHz
        options.kappa = 1
        options.chi_eff = -0.6
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()
        options.child_fringe = None
        options.link_child_exp = True
        return options

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.pre_amp_list,
            analysis_class=AmpToPhotonAnalysis,
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        chi_eff = self.analysis_options.chi_eff
        kappa = self.analysis_options.kappa
        if chi_eff is not None:
            metadata.draw_meta = {
                "effective_chi": (round(chi_eff, 3), "rad/us"),
                "kappa": (round(kappa, 3), "rad/us"),
            }
        return metadata

    def _setup_child_experiment(self, exp: PhotonRamsey, index: int, pre_amp: float):
        exp.run_options.index = index
        exp.set_parent_file(
            self,
            f"pre_amp={pre_amp}v",
            index,
            len(self.experiment_options.pre_amp_list),
        )
        exp.set_experiment_options(pre_amp=pre_amp)
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp: PhotonRamsey):
        result = exp.analysis.results
        osc_freq = result.freq.value
        if exp.run_options.index == 0:
            self.run_options.child_fringe = osc_freq
        delta_freq = osc_freq - self.run_options.child_fringe
        photon_num = abs(delta_freq * 2 * np.pi / (2 * self.analysis_options.chi_eff))
        exp.analysis.provide_for_parent.update(
            {
                "photon_num": photon_num,
            }
        )


class NMBaseParams(NMBase):
    """Base class for parameter optimization experiments.

    This class provides common functionality for optimizing pulse parameters in
    quantum measurement experiments. Subclasses should specify the acquisition
    pulse type and parameter definitions.

    Attributes:
        _sub_experiment_class: The experiment class to run (DePhaseRamsey).
        _acq_pulse_type: The pulse type to optimize (subclasses must override).
    """

    _sub_experiment_class = DePhaseRamsey
    _acq_pulse_type = None  # Must be overridden by subclasses

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Generates default experiment options with parameter definitions.

        Returns:
            Options object containing default experiment configuration and
            parameter optimization settings.
        """
        options = super()._default_experiment_options()
        options.input_data = cls._get_input_data()
        options.mode = "NM"
        return options

    @classmethod
    def _get_input_data(cls) -> dict:
        """Defines the parameters to optimize and their constraints.

        Subclasses must implement this to specify their particular parameters.

        Returns:
            Dictionary containing parameter definitions with:
            - is_opt: bool - whether to optimize this parameter
            - init_v: float - initial value (None to use current)
            - iter_step: float - optimization step size
            - bound: list[float] - min/max bounds for parameter
        """
        raise NotImplementedError("Subclasses must implement _get_input_data")

    def _check_options(self) -> None:
        """Validates and initializes experiment options.

        Sets up initial values for parameters being optimized, using either:
        1. Explicit init_v from options if specified
        2. Current values from child experiment if init_v is None
        """
        super()._check_options()
        init_v = []
        for k, v in self.experiment_options.input_data.items():
            if v.get("is_opt", False):
                init_v.append(
                    v.get("init_v")
                    or self.child_experiment.experiment_options.acq_pulse_params[
                        self._acq_pulse_type
                    ][k]
                )
        self.set_run_options(init_v=init_v)
        self.child_experiment.experiment_options.acq_pulse_type = self._acq_pulse_type

    async def _execute_exp(self, parameters) -> Union[int, float]:
        """Executes a single experiment with given parameters.

        Args:
            parameters: List of parameter values to test in this iteration.

        Returns:
            The measured photon count from the experiment.
        """
        exp = deepcopy(self.child_experiment)
        for i, key in enumerate(self.run_options.opt_keys):
            exp.experiment_options.acq_pulse_params[self._acq_pulse_type][key] = (
                parameters[i]
            )
        await self._run_exp(exp)
        return exp.analysis.results.n0.value

    async def _run_exp(self, exp, index: int = 0) -> None:
        """Runs an experiment and stores results.

        Args:
            exp: The experiment instance to run.
            index: Optional index for experiment tracking (default 0).
        """
        exp.set_parent_file(
            self,
            description=f"NM-{self.run_options.count}",
            index=self.run_options.index,
        )
        self.run_options.index += 1
        self._check_simulator_data(exp, self.run_options.count)
        await exp.run_experiment()
        if index:
            self._experiments.append(exp)


class NMBaseParamsBoth(NMBaseParams):
    """Base class for experiments requiring two measurements per iteration.

    This extends NMBaseParams to run two experiments (with different initial states)
    per optimization iteration and return the maximum photon count.

    Attributes:
        Inherits all attributes from NMBaseParams.
    """

    @classmethod
    def _default_run_options(cls) -> Options:
        """Adds child experiment tracking to run options."""
        options = super()._default_run_options()
        options.child_exp_index = 0  # Tracks multiple experiments per iteration
        return options

    async def _execute_exp(self, parameters) -> Union[int, float]:
        """Executes two experiments (init_state=0 and 1) and returns max photons.

        Args:
            parameters: List of parameter values to test.

        Returns:
            Maximum photon count from both experiments.
        """
        exp1 = deepcopy(self.child_experiment)
        exp1._label += "IS0"
        exp2 = deepcopy(self.child_experiment)
        exp2._label += "IS1"

        # Apply parameters to both experiments
        for i, key in enumerate(self.run_options.opt_keys):
            val = parameters[i]
            exp1.experiment_options.acq_pulse_params[self._acq_pulse_type][key] = val
            exp2.experiment_options.acq_pulse_params[self._acq_pulse_type][key] = val

        exp2.set_experiment_options(init_state=0)
        await self._run_exp(exp1)
        photon1 = exp1.analysis.results.n0.value

        exp2.set_experiment_options(init_state=1)
        await self._run_exp(exp2, index=1)
        photon2 = exp2.analysis.results.n0.value

        return np.max([photon1, photon2])

    async def _run_exp(self, exp, index: int = 0) -> None:
        """Runs experiment with additional tracking for multiple experiments."""
        exp.set_parent_file(
            self,
            index=self.run_options.child_exp_index,
            description=f"NM-{self.run_options.count}",
        )
        self.run_options.child_exp_index += 1
        await super()._run_exp(exp, index)


class NMClearParams(NMBaseParams):
    """Optimization of Clear pulse amplitudes."""

    _acq_pulse_type = "Clear"

    @classmethod
    def _get_input_data(cls) -> dict:
        """Defines Clear pulse parameters to optimize."""
        return {
            "amp1": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 1e-3,
                "bound": [-0.8, 0.8],
            },
            "amp2": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 1e-3,
                "bound": [-0.8, 0.8],
            },
            "tkick": {
                "is_opt": False,
                "init_v": None,
                "iter_step": 10,
                "bound": [50, 150],
            },
        }


class NMPhaseParams(NMBaseParams):
    """Optimization of Phase pulse amplitude and phase."""

    _acq_pulse_type = "Phase"

    @classmethod
    def _get_input_data(cls) -> dict:
        """Defines Phase pulse parameters to optimize."""
        return {
            "amp": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 1e-3,
                "bound": [-0.8, 0.8],
            },
            "phase": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 1e-4,
                "bound": [0, 2 * np.pi],
            },
            "tkick": {
                "is_opt": False,
                "init_v": None,
                "iter_step": 10,
                "bound": [50, 150],
            },
        }


class NMPhaseParamsBoth(NMBaseParamsBoth):
    """Phase pulse optimization with both initial states."""

    _acq_pulse_type = "Phase"

    @classmethod
    def _get_input_data(cls) -> dict:
        """Defines Phase pulse parameters (same as NMPhaseParams)."""
        return NMPhaseParams._get_input_data()


class NMClearParamsBoth(NMBaseParamsBoth):
    """Clear pulse optimization with both initial states."""

    _acq_pulse_type = "Clear"

    @classmethod
    def _get_input_data(cls) -> dict:
        """Defines Clear pulse parameters (same as NMClearParams)."""
        return NMClearParams._get_input_data()
