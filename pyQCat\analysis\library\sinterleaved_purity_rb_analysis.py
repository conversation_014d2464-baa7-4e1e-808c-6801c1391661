# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/11/08
# __author:       xw

import numpy as np

from ...structures import Options
from ...analysis import CurveAnalysis
from ...analysis.specification import ParameterRepr


class SingInterleavedPurityRBAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.subplots = (1, 1)
        options.x_label = "Number of Clifford"
        options.y_label = ["Sequence Purity"]
        options.result_parameters = [
            ParameterRepr(name="r", repr="error rate", unit="", param_path=""),
            ParameterRepr(name="ref", repr="Reference fidelity", unit="", param_path=""),
            ParameterRepr(name="inl", repr="Interleaved fidelity", unit="", param_path=""),
        ]
        options.N = 2

        return options

    def _extract_result(self, date_key):
        pre_anas = self.analysis_datas.analysis.y[0]
        inl_anas = self.analysis_datas.analysis.y[1]

        inl_u = inl_anas.results.U.value
        pre_u = pre_anas.results.U.value
        d = 2 ** self.options.N
        pre_fidelity = pre_anas.results.fidelity.value
        inl_fidelity = inl_anas.results.fidelity.value
        r = ((d-1) / d) * (1 - (np.power(inl_u, 2/3) / np.power(pre_u, 2/3)))

        self.results.ref.value = round(pre_fidelity, 4)
        self.results.inl.value = round(inl_fidelity, 4)
        self.results.r.value = round(r, 4)

    def _visualization(self):
        self.drawer.set_options(title=self._description())

        pre_anas = self.analysis_datas.analysis.y[0]
        inl_anas = self.analysis_datas.analysis.y[1]

        depths = pre_anas.options.depths
        k = pre_anas.options.k
        x_arr = np.asarray(depths).repeat(k)
        y_arr1 = pre_anas.options.rho_list
        y_arr2 = inl_anas.options.rho_list

        data_key = pre_anas.options.data_key[0]

        fit_x_arr = pre_anas.analysis_datas.get(data_key).x
        fit_y_arr1 = pre_anas.analysis_datas.get(data_key).fit_data.y_fit
        fit_y_arr2 = inl_anas.analysis_datas.get(data_key).fit_data.y_fit

        self.drawer.draw_raw_data(
            x_data=x_arr, y_data=y_arr1, label=f"REF (raw)", ax_index=0, c="orangered"
        )

        self.drawer.draw_raw_data(
            x_data=x_arr, y_data=y_arr2, label=f"INT (raw)", ax_index=0, c="blueviolet"
        )

        self.drawer.draw_fit_line(
            x_data=fit_x_arr,
            y_data=fit_y_arr1,
            ax_index=0,
            label=f"REF (fit)",
            color="orangered",
        )

        self.drawer.draw_fit_line(
            x_data=fit_x_arr,
            y_data=fit_y_arr2,
            label=f"INT (fit)",
            ax_index=0,
            color="blueviolet",
        )

        self.drawer.format_canvas()
