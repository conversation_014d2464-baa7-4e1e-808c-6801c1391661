{"cells": [{"cell_type": "markdown", "id": "4acc426b", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["#  Invoker\n", "\n", "\n", "pyqcat-monster 中 `invoker` 模块主要接口使用介绍"]}, {"cell_type": "code", "execution_count": 1, "id": "d48ab21e", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\n"]}], "source": ["# 若用户未安装 pyqcat 包（monster），通过本地源码测试，需要执行下面操作\n", "# 若安装了 pyqcat 包（monster），跳过此步骤\n", "\n", "import os\n", "import sys\n", "\n", "\n", "package_root = \"../../pyqcat-monster\"\n", "\n", "print(os.path.abspath(package_root))\n", "\n", "sys.path.insert(0, package_root)"]}, {"cell_type": "markdown", "id": "955167c4", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# Invoker Account \n", "\n", "用户账号相关操作"]}, {"cell_type": "markdown", "id": "e2b8efe5", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 注册\n", "\n", "1、注册账号之前，查询环境信息，确定是否 `invoker_addr` 信息，正确 `invoker_addr` 才能注册成功, 查询接口 `Invoker.get_env()`；\n", "\n", "2、配置 `invoker_addr` 信息, 接口 `Invoker.set_env()`；\n", "\n", "3、注册账号，需要提供用户名`username`, 密码`password`, 确认密码`repeat_password`, 邮箱`email`，接口 `Invoker.register_account()`；"]}, {"cell_type": "markdown", "id": "14987e52", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 查看本地环境信息\n", "\n", "1、查看环境信息，接口`Invoker.get_env()`；\n", "\n", "2、基本环境参数：\n", "\n", "`invoker_addr` invoker 服务地址；\n", "\n", "`point_label` 比特/Coupler point 标签，查询比特/Coupler 信息根据此标签，若某个比特没有此标签数据，则返回该比特的最新一条数据；\n", "\n", "`sample` 芯片名称；\n", "\n", "`env_name` 测试环境或一体机名称；\n", "\n", "`version` pyqcat-monster 版本号，系统自动识别；"]}, {"cell_type": "code", "execution_count": 2, "id": "7aabb317", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["import pandas as pd\n", "\n", "from pyQCat.invoker import Invoker, DataCenter\n", "from pyQCat.parameters import get_parameters\n", "\n", "\n", "pd.set_option('max_colwidth', None)\n", "\n", "db = DataCenter()"]}, {"cell_type": "code", "execution_count": 3, "id": "12908500", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |                                        |\n", "|    point_label     |                                        |\n", "|       sample       |                                        |\n", "|      env_name      |                                        |\n", "|      version       |                 0.0.1                  |\n", "|=============================================================|\n", "\n"]}], "source": ["# 查看环境信息\n", "\n", "print(Invoker.get_env())"]}, {"cell_type": "markdown", "id": "47a86c38", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 配置 invoker_addr\n", "\n", "1、设置基本环境信息，接口`Invoker.set_env()`；\n", "\n", "2、这里暂时只设置 `invoker_addr`, 只需传参`invoker_addr`;"]}, {"cell_type": "code", "execution_count": 4, "id": "2f7f8af0", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |        tcp://************:8088         |\n", "|    point_label     |                                        |\n", "|       sample       |                                        |\n", "|      env_name      |                                        |\n", "|      version       |                 0.0.1                  |\n", "|=============================================================|\n", "\n"]}], "source": ["# 配置 invoker_addr \n", "\n", "invoker_addr = \"tcp://************:8088\"\n", "Invoker.set_env(invoker_addr=invoker_addr)\n", "\n", "print(Invoker.get_env())"]}, {"cell_type": "markdown", "id": "adcc4786", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 注册账号\n", "\n", "1、用户注册接口 `Invoker.register_account()`；\n", "\n", "2、需要提供参数: 用户名`username`, 密码`password`, 确认密码`repeat_password`, 邮箱`email`；"]}, {"cell_type": "code", "execution_count": 3, "id": "b8ae89c2", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': '55fbb1c24427efc290878a019380b197'},\n", " 'msg': 'success'}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 注册用户\n", "\n", "username = \"test_12\"\n", "password = \"123456\"\n", "repeat_password = \"123456\"\n", "email = \"<EMAIL>\"\n", "\n", "\n", "Invoker.register_account(username, password, repeat_password, email)"]}, {"cell_type": "markdown", "id": "fcbeb613", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 登录\n", "\n", "### 登录有以下三种方式，任意选择一种\n", "\n", "1、使用用户名登录账号，需要提供用户名`username`，密码`password`，接口`Invoker.verify_account()`，使用该接口登录，每次生成新的token，每个用户有且只有一个`token`；\n", "\n", "2、使用本地 token 加载账号，接口`Invoker.load_account()`；\n", "\n", "3、使用指定 token 加载账号，需要提供`token`, 接口 `Invoker.save_account()`；\n"]}, {"cell_type": "code", "execution_count": 4, "id": "e6e5a4bf", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': '44711ece1db44b6ff639c1870830b0ec'},\n", " 'msg': 'success'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 使用用户名、密码登录\n", "\n", "username = \"test_12\"\n", "password = \"123456\"\n", "\n", "Invoker.verify_account(username, password)"]}, {"cell_type": "code", "execution_count": 7, "id": "b5d88cd1", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>username</th>\n", "      <th>groups</th>\n", "      <th>email</th>\n", "      <th>phone_num</th>\n", "      <th>is_super</th>\n", "      <th>is_admin</th>\n", "      <th>status</th>\n", "      <th>create_time</th>\n", "      <th>last_login_time</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>test_12</td>\n", "      <td>normal</td>\n", "      <td><EMAIL></td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>2022-10-14 18:27:00</td>\n", "      <td>2022-10-14 18:27:11</td>\n", "      <td>63493974bca191dfa31b00d5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  username  groups         email phone_num  is_super  is_admin  status  \\\n", "0  test_12  normal  <EMAIL>      None     False     False       0   \n", "\n", "           create_time      last_login_time                        id  \n", "0  2022-10-14 18:27:00  2022-10-14 18:27:11  63493974bca191dfa31b00d5  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 加载本地 token 登录\n", "\n", "ret_data = Invoker.load_account()\n", "\n", "target_data = ret_data.get(\"data\")\n", "pd.DataFrame(target_data, index=[0])"]}, {"cell_type": "code", "execution_count": 9, "id": "c1a38fef", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>username</th>\n", "      <th>groups</th>\n", "      <th>email</th>\n", "      <th>phone_num</th>\n", "      <th>is_super</th>\n", "      <th>is_admin</th>\n", "      <th>status</th>\n", "      <th>create_time</th>\n", "      <th>last_login_time</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>test_04</td>\n", "      <td>ck_software</td>\n", "      <td><EMAIL></td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>2022-10-11 09:07:09</td>\n", "      <td>2022-10-14 17:25:22</td>\n", "      <td>6344c1bd81df4a309ee21f94</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  username       groups        email phone_num  is_super  is_admin  status  \\\n", "0  test_04  ck_software  <EMAIL>      None     False      True       0   \n", "\n", "           create_time      last_login_time                        id  \n", "0  2022-10-11 09:07:09  2022-10-14 17:25:22  6344c1bd81df4a309ee21f94  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 使用指定 token 登录\n", "\n", "token = \"f0dd3fa248e1cec8aaf951ab4e066659\"\n", "\n", "ret_data = Invoker.save_account(token)\n", "\n", "target_data = ret_data.get(\"data\")\n", "pd.DataFrame(target_data, index=[0])"]}, {"cell_type": "markdown", "id": "76be94eb", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 登出\n", "\n", "1、登出账号，接口`Invoker.logout_account()`；\n", "\n", "2、执行此操作，则该用户退出登录，`token`已无效，下次登录，需要使用用户名、密码登录；"]}, {"cell_type": "code", "execution_count": 10, "id": "a8a2c581", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200, 'data': {}, 'msg': 'success'}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 退出登录\n", "\n", "Invoker.logout_account()"]}, {"cell_type": "markdown", "id": "3196da4d", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# Invoker DataCenter\n", "\n", "用户个人信息相关操作\n"]}, {"cell_type": "markdown", "id": "********", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 重置密码\n", "\n", "当用户忘记密码，或者想修改密码\n", "\n", "1、用户重置密码接口 `db.reset_passwd()`;\n", "\n", "2、需要提供参数：用户名`username`, 旧密码`old_password`, 新密码`new_password`, 邮箱`email`；\n", "\n", "3、**确保旧密码或邮箱**，其中有一个值是准确的，才能重置成功；"]}, {"cell_type": "code", "execution_count": 12, "id": "859ed62b", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200, 'data': {'new_password': '123456'}, 'msg': 'success'}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# 重置密码\n", "\n", "username = \"test_12\"\n", "old_password = \"cc\"\n", "new_password = \"123456\"\n", "email = \"<EMAIL>\"\n", "\n", "db.reset_passwd(username, old_password, new_password, email)"]}, {"cell_type": "code", "execution_count": 4, "id": "********", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': '4d8cf0b84bb69034974d9e02cc21bb60'},\n", " 'msg': 'success'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 重新登录\n", "\n", "username = \"test_12\"\n", "password = \"123456\"\n", "\n", "Invoker.verify_account(username, password)"]}, {"cell_type": "markdown", "id": "523ac660", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 查询当前登录信息\n", "\n", "1、查询当前登录信息， 接口`db.query_user_info()`；\n"]}, {"cell_type": "code", "execution_count": 14, "id": "02ba529a", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>username</th>\n", "      <th>groups</th>\n", "      <th>email</th>\n", "      <th>phone_num</th>\n", "      <th>is_super</th>\n", "      <th>is_admin</th>\n", "      <th>status</th>\n", "      <th>create_time</th>\n", "      <th>last_login_time</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>test_12</td>\n", "      <td>normal</td>\n", "      <td><EMAIL></td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>2022-10-14 18:27:00</td>\n", "      <td>2022-10-14 18:28:13</td>\n", "      <td>63493974bca191dfa31b00d5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  username  groups         email phone_num  is_super  is_admin  status  \\\n", "0  test_12  normal  <EMAIL>      None     False     False       0   \n", "\n", "           create_time      last_login_time                        id  \n", "0  2022-10-14 18:27:00  2022-10-14 18:28:13  63493974bca191dfa31b00d5  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询当前登录用户\n", "\n", "ret_data = db.query_user_info()\n", "\n", "target_data = ret_data.get(\"data\")\n", "pd.DataFrame(target_data, index=[0])"]}, {"cell_type": "markdown", "id": "78f2bd5c", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 组相关操作\n", "\n", "用户组跟权限相关，**super** 组成员权限最大，普通组的管理员权限略大"]}, {"cell_type": "markdown", "id": "c601c4d5", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 查询所有组\n", "\n", "1、查询系统所有用户组，接口`db.query_all_groups()`；\n", "\n", "2、返回组名、简介、组管理员信息；"]}, {"cell_type": "code", "execution_count": 15, "id": "e2a9a1da", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>description</th>\n", "      <th>leaders</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>super</td>\n", "      <td>None</td>\n", "      <td>[A_9527, A_9528]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>normal</td>\n", "      <td>None</td>\n", "      <td>[CK01]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ustc</td>\n", "      <td>None</td>\n", "      <td>[xiao]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ck_software</td>\n", "      <td>OriginQuntum Control Test Software Group.</td>\n", "      <td>[test_04, test_05]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>test_demo</td>\n", "      <td>Test Mini Group.</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>test_ab</td>\n", "      <td>Test Mini Group AB.</td>\n", "      <td>[test_08]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>test_abc</td>\n", "      <td>Test Mini Group ABC.</td>\n", "      <td>[test_02]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          name                                description             leaders\n", "0        super                                       None    [A_9527, A_9528]\n", "1       normal                                       None              [CK01]\n", "2         ustc                                       None              [xiao]\n", "3  ck_software  OriginQuntum Control Test Software Group.  [test_04, test_05]\n", "4    test_demo                           Test Mini Group.                  []\n", "5      test_ab                        Test Mini Group AB.           [test_08]\n", "6     test_abc                       Test Mini Group ABC.           [test_02]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询所有用户组基本信息\n", "\n", "ret_data = db.query_all_groups()\n", "\n", "pd.DataFrame(ret_data.get(\"data\"))"]}, {"cell_type": "markdown", "id": "41545d5d", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 查询自己组详细信息\n", "\n", "1、查询自己所在组详情，接口`db.query_group_info()`；\n", "\n", "2、返回组基本信息，以及该组所有用户等信息；"]}, {"cell_type": "code", "execution_count": 16, "id": "26ce2f35", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>username</th>\n", "      <th>groups</th>\n", "      <th>is_super</th>\n", "      <th>is_admin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CK01</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CK02</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ck_test</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>hqbdflbdwjbaoyaahyotcaludqlhuwttyuofcybdgiqfdjlyhfkyehlbweuqrayftfwyyjigfwyayewbkybrgdwjbewuhioeqbjj</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>lgeokqwfqfoufwbliqgactkgqiywforworidrgloaugkgwouaecaolhqhbegjyrwcbkfkefafogdrjieyldykurlecutbdewfjrk</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>kluhbotrdqfelhocokitferwuddgqcwuihaekofflwhweukhkoubcleoblbwaiqkbithdkurdwblyfwlwyffahlftldtiulifyrl</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>qduqwehrwjcqgjrthyekuauojujtwclwdacflllwbiibfiwhkcbkwuohkdejgybjjatrcyqhiqetliecujaqugaucwbyqlojtdab</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>uacdgjkfoataaifafrhbhjqjdigfakjykfhcbjdtbdkigbrrkedccyfqjebcilgedjfyhoejqyjqhdirkycrlbatwlqjolktdahk</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>asdfhaw</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>test_06</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>ssssss</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>A_1000</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>A_1001</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>A_1002</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>A_1003</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>A_1004</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>zyc</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>test_09</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>test_10</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>test_11</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>test_12</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                                                username  \\\n", "0                                                                                                   CK01   \n", "1                                                                                                   CK02   \n", "2                                                                                                ck_test   \n", "3   hqbdflbdwjbaoyaahyotcaludqlhuwttyuofcybdgiqfdjlyhfkyehlbweuqrayftfwyyjigfwyayewbkybrgdwjbewuhioeqbjj   \n", "4   lgeokqwfqfoufwbliqgactkgqiywforworidrgloaugkgwouaecaolhqhbegjyrwcbkfkefafogdrjieyldykurlecutbdewfjrk   \n", "5   kluhbotrdqfelhocokitferwuddgqcwuihaekofflwhweukhkoubcleoblbwaiqkbithdkurdwblyfwlwyffahlftldtiulifyrl   \n", "6   qduqwehrwjcqgjrthyekuauojujtwclwdacflllwbiibfiwhkcbkwuohkdejgybjjatrcyqhiqetliecujaqugaucwbyqlojtdab   \n", "7   uacdgjkfoataaifafrhbhjqjdigfakjykfhcbjdtbdkigbrrkedccyfqjebcilgedjfyhoejqyjqhdirkycrlbatwlqjolktdahk   \n", "8                                                                                                asdfhaw   \n", "9                                                                                                test_06   \n", "10                                                                                                ssssss   \n", "11                                                                                                A_1000   \n", "12                                                                                                A_1001   \n", "13                                                                                                A_1002   \n", "14                                                                                                A_1003   \n", "15                                                                                                A_1004   \n", "16                                                                                                   zyc   \n", "17                                                                                               test_09   \n", "18                                                                                               test_10   \n", "19                                                                                               test_11   \n", "20                                                                                               test_12   \n", "\n", "    groups  is_super  is_admin  \n", "0   normal     False      True  \n", "1   normal     False     False  \n", "2   normal     False     False  \n", "3   normal     False     False  \n", "4   normal     False     False  \n", "5   normal     False     False  \n", "6   normal     False     False  \n", "7   normal     False     False  \n", "8   normal     False     False  \n", "9   normal     False     False  \n", "10  normal     False     False  \n", "11  normal     False     False  \n", "12  normal     False     False  \n", "13  normal     False     False  \n", "14  normal     False     False  \n", "15  normal     False     False  \n", "16  normal     False     False  \n", "17  normal     False     False  \n", "18  normal     False     False  \n", "19  normal     False     False  \n", "20  normal     False     False  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询组信息\n", "\n", "ret_data = db.query_group_info()\n", "\n", "pd.DataFrame(ret_data.get(\"data\"))"]}, {"cell_type": "markdown", "id": "0b331f16", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 创建组\n", "\n", "1、创建组接口`db.create_group()`；\n", "\n", "2、提供参数：组名`group_name`， 组简介`description`；\n", "\n", "3、注意：只有超级组的成员，才有权限创建用户组；"]}, {"cell_type": "code", "execution_count": 17, "id": "4af8a3d1", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': 'fdf512a35ab63902930401eba47664ff'},\n", " 'msg': 'success'}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 登录超级用户\n", "\n", "super_username = \"A_9527\"\n", "password = \"123456\"\n", "\n", "Invoker.verify_account(super_username, password)"]}, {"cell_type": "code", "execution_count": 18, "id": "b82e0600", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200, 'data': {}, 'msg': 'success'}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# 创建组\n", "\n", "group_name = \"test_abcd\"\n", "description = \"Test Mini Group ABCD.\"\n", "\n", "db.create_group(group_name, description)"]}, {"cell_type": "markdown", "id": "b46a1571", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 修改某个用户的组\n", "\n", "1、修改其他用户的组名，接口`db.change_user_group()`；\n", "\n", "2、提供参数: 被修改用户名`target_user`， 目标组名`target_group`；\n", "\n", " * super组 admin：更改所有人 \n", " * super组  非 admin：更改除super组人员\n", " * 非super组 admin: 更改本组成员\n", " * 非super组 非admin: 无权限"]}, {"cell_type": "code", "execution_count": 21, "id": "686f17f8", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200, 'data': {}, 'msg': 'success'}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# 修改某个用户的组\n", "\n", "target_user = \"test_03\"\n", "target_group = \"test_abcd\"\n", "\n", "db.change_user_group(target_user, target_group)"]}, {"cell_type": "code", "execution_count": 22, "id": "2b65bcd0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>username</th>\n", "      <th>groups</th>\n", "      <th>is_super</th>\n", "      <th>is_admin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>test_02</td>\n", "      <td>test_abcd</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>test_03</td>\n", "      <td>test_abcd</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  username     groups  is_super  is_admin\n", "0  test_02  test_abcd     False      True\n", "1  test_03  test_abcd     False     False"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# 超级用户查询某个组信息\n", "\n", "target_group = \"test_abcd\"\n", "\n", "ret_data = db.query_group_info(target_group)\n", "\n", "pd.DataFrame(ret_data.get(\"data\"))"]}, {"cell_type": "markdown", "id": "204ea99c", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 修改某个组的管理员\n", "\n", "1、修改某个组的管理员，接口`db.change_group_leader()`；\n", "\n", "2、提供参数：被修改用户名`target_user`, 目标组`target_group`，是否设置为管理员`is_admin`，\n", "\n", " * super组 admin：更改所有人 \n", " * super组  非 admin：更改除super组人员\n", " * 非super组 admin: 更改本组成员\n", " * 非super组 非admin: 无权限"]}, {"cell_type": "code", "execution_count": 23, "id": "b896e2a5", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200, 'data': {}, 'msg': 'success'}"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# 设置某个组的 leader\n", "\n", "target_user = \"test_03\"\n", "target_group = \"test_abcd\"\n", "is_admin = True\n", "\n", "db.change_group_leader(target_user, target_group, is_admin)"]}, {"cell_type": "code", "execution_count": 24, "id": "dd33bff9", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>description</th>\n", "      <th>leaders</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>super</td>\n", "      <td>None</td>\n", "      <td>[A_9527, A_9528]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>normal</td>\n", "      <td>None</td>\n", "      <td>[CK01]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ustc</td>\n", "      <td>None</td>\n", "      <td>[xiao]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ck_software</td>\n", "      <td>OriginQuntum Control Test Software Group.</td>\n", "      <td>[test_04, test_05]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>test_demo</td>\n", "      <td>Test Mini Group.</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>test_ab</td>\n", "      <td>Test Mini Group AB.</td>\n", "      <td>[test_08]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>test_abc</td>\n", "      <td>Test Mini Group ABC.</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>test_abcd</td>\n", "      <td>Test Mini Group ABCD.</td>\n", "      <td>[test_02, test_03]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          name                                description             leaders\n", "0        super                                       None    [A_9527, A_9528]\n", "1       normal                                       None              [CK01]\n", "2         ustc                                       None              [xiao]\n", "3  ck_software  OriginQuntum Control Test Software Group.  [test_04, test_05]\n", "4    test_demo                           Test Mini Group.                  []\n", "5      test_ab                        Test Mini Group AB.           [test_08]\n", "6     test_abc                       Test Mini Group ABC.                  []\n", "7    test_abcd                      Test Mini Group ABCD.  [test_02, test_03]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询所有用户组基本信息\n", "\n", "ret_data = db.query_all_groups()\n", "\n", "pd.DataFrame(ret_data.get(\"data\"))"]}, {"cell_type": "markdown", "id": "4f43b5d5", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## Invoker DataBase\n", "\n", "芯片信息，比特数据，等相关操作"]}, {"cell_type": "markdown", "id": "4498028b", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 查询芯片接线信息\n", "\n", "1、查询芯片信息，接口`db.query_chip_line()`，\n", "\n", "2、根据本地基本环境信息，`sample` 和 `env_name` 查询芯片信息，主要是比特/Coupler 的接线情况，以及读取频率等；\n", "\n", "3、如果本地基本环境信息中`sample`, `env_name` 与期望不同，那么，需要配置本地基本环境参数；\n"]}, {"cell_type": "code", "execution_count": 25, "id": "ec9a91a1", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |        tcp://************:8088         |\n", "|    point_label     |                                        |\n", "|       sample       |                                        |\n", "|      env_name      |                                        |\n", "|      version       |                 0.0.1                  |\n", "|=============================================================|\n", "\n"]}], "source": ["# 查看环境信息\n", "\n", "print(Invoker.get_env())"]}, {"cell_type": "code", "execution_count": 30, "id": "ef6174bf", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |        tcp://************:8088         |\n", "|    point_label     |              person_point              |\n", "|       sample       |               test_chip                |\n", "|      env_name      |                D00_1011                |\n", "|      version       |                 0.0.1                  |\n", "|=============================================================|\n", "\n"]}], "source": ["# 配置基本环境信息\n", "\n", "# sample 芯片名称\n", "# env_name 测试环境或一体机名称\n", "# point_label 比特/Coupler point 标签，查询比特/Coupler 信息根据此标签，若某个比特没有此标签数据，则返回该比特的最新一条数据\n", "\n", "point_label = \"person_point\"\n", "sample = \"test_chip\"\n", "env_name = \"D00_1011\"\n", "\n", "Invoker.set_env(\n", "    point_label=point_label,\n", "    sample=sample,\n", "    env_name=env_name\n", ")\n", "\n", "print(Invoker.get_env())"]}, {"cell_type": "code", "execution_count": 28, "id": "f5a1ad3e", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': '1f3aa26a84d3fac59b8301baadcaf1fd'},\n", " 'msg': 'success'}"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# 使用用户名、密码登录\n", "\n", "user_name = \"test_12\"\n", "password = \"123456\"\n", "\n", "Invoker.verify_account(user_name, password)"]}, {"cell_type": "code", "execution_count": 29, "id": "f102a197", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>data</th>\n", "      <th>msg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [code, data, msg]\n", "Index: []"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# 根据配置基本环境信息，查询该芯片的线路配置\n", "\n", "ret_data = db.query_chip_line()\n", "\n", "pd.DataFrame(ret_data)"]}, {"cell_type": "markdown", "id": "d208bad1", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 新的测试环境需要配置芯片的接线情况\n", "\n", "### 配置芯片接线信息\n", "\n", "用户通过 sample, env_name 查询不到该芯片的接线信息，需要用户配置该芯片的接线信息\n", "\n", "只有 **super** 组成员，才有权限配置芯片接线\n", "\n", "具体的接线配置文件格式待定，哪种文件格式方便就用哪种，最终封装成 chip_line_connect.json 文件格式\n", "\n", "配置芯片接口`create_chip_line()` 需要提供参数 `data`，字典类型\n", "\n", "\n", "首先需要将 conf_path 目录下的 chip_line_connect.json 文件，通过 `josn.load()` 加载成字典 `data`\n", "\n", "\n", "chip_line_connect.json 文件, 参数说明：\n", "\n", "\n", "`sample` 芯片样品编号\n", "\n", "`env_name` 测试环境名称\n", "\n", "`QubitCount` qubit 数量\n", "\n", "`CouplerCount` coupler 数量\n", "\n", "`QubitParams` 每个qubit 参数，接线信息\n", "\n", "`QubitParams` 每个 coupler 参数，接线信息\n", "\n", "\n", "\n", "```\n", "{\n", "  \"sample\": \"test_chip\",\n", "  \"env_name\": \"D00_1011\",\n", "  \"QubitCount\": 4,\n", "  \"CouplerCount\": 3,\n", "  \"QubitParams\": {\n", "    \"q0\": {\n", "      \"xy_channel\": 5,\n", "      \"z_dc_channel\": 1,\n", "      \"z_flux_channel\": 1,\n", "      \"readout_channel\": 1,\n", "      \"probe_freq\": 6546.2,\n", "      \"sample_delay\": 500,\n", "      \"sample_width\": 500\n", "    },\n", "    \"q1\": {\n", "      \"xy_channel\": 6,\n", "      \"z_dc_channel\": 2,\n", "      \"z_flux_channel\": 2,\n", "      \"readout_channel\": 1,\n", "      \"probe_freq\": 6401.6,\n", "      \"sample_delay\": 500,\n", "      \"sample_width\": 500\n", "    },\n", "    \"q2\": {\n", "      \"xy_channel\": 7,\n", "      \"z_dc_channel\": 3,\n", "      \"z_flux_channel\": 3,\n", "      \"readout_channel\": 1,\n", "      \"probe_freq\": 6496.4,\n", "      \"sample_delay\": 500,\n", "      \"sample_width\": 500\n", "    },\n", "    \"q3\": {\n", "      \"xy_channel\": 8,\n", "      \"z_dc_channel\": 4,\n", "      \"z_flux_channel\": 4,\n", "      \"readout_channel\": 1,\n", "      \"probe_freq\": 6643.8,\n", "      \"sample_delay\": 500,\n", "      \"sample_width\": 500\n", "    }\n", "  },\n", "  \"CouplerParams\": {\n", "    \"c0\": {\n", "      \"drive_bit\": 0,\n", "      \"probe_bit\": 1,\n", "      \"z_dc_channel\": 5,\n", "      \"z_flux_channel\": 5,\n", "      \"probe_pi_width\": 180.0,\n", "      \"probe_pi_offset\": 10.0\n", "    },\n", "    \"c1\": {\n", "      \"drive_bit\": 1,\n", "      \"probe_bit\": 2,\n", "      \"z_dc_channel\": 6,\n", "      \"z_flux_channel\": 6,\n", "      \"probe_pi_width\": 180.0,\n", "      \"probe_pi_offset\": 10.0\n", "    },\n", "    \"c2\": {\n", "      \"drive_bit\": 2,\n", "      \"probe_bit\": 3,\n", "      \"z_dc_channel\": 7,\n", "      \"z_flux_channel\": 7,\n", "      \"probe_pi_width\": 180.0,\n", "      \"probe_pi_offset\": 10.0\n", "    }\n", "  }\n", "}\n", "```"]}, {"cell_type": "code", "execution_count": 31, "id": "cdf18330", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': '880c6d5282457e050de882de0fa87185'},\n", " 'msg': 'success'}"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# 登录超级用户\n", "\n", "username = \"A_9527\"\n", "password = \"123456\"\n", "\n", "Invoker.verify_account(username, password)"]}, {"cell_type": "code", "execution_count": 32, "id": "96d2ee78", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200, 'data': {}, 'msg': 'success'}"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# 根据 sample, env_name 没有查询到，需要配置接线信息\n", "\n", "import json\n", "\n", "\n", "chip_line_conn_file = r\"F:\\PersonTest\\chip_line_connect.json\"\n", "\n", "with open(chip_line_conn_file, mode='r', encoding='utf-8') as fp:\n", "    chip_line_data = json.load(fp)\n", "\n", "db.create_chip_line(chip_line_data)"]}, {"cell_type": "code", "execution_count": 5, "id": "5a9fd9fc", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>data</th>\n", "      <th>msg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>CouplerCount</th>\n", "      <td>200</td>\n", "      <td>3</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CouplerParams</th>\n", "      <td>200</td>\n", "      <td>{'c0': {'drive_bit': 1, 'probe_bit': 2, 'z_dc_channel': 2, 'z_flux_channel': 5, 'probe_pi_width': 180.0, 'probe_pi_offset': 10.0}, 'c1': {'drive_bit': 2, 'probe_bit': 3, 'z_dc_channel': 4, 'z_flux_channel': 6, 'probe_pi_width': 180.0, 'probe_pi_offset': 10.0}, 'c2': {'drive_bit': 3, 'probe_bit': 4, 'z_dc_channel': 6, 'z_flux_channel': 7, 'probe_pi_width': 180.0, 'probe_pi_offset': 10.0}}</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>QubitCount</th>\n", "      <td>200</td>\n", "      <td>4</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>QubitParams</th>\n", "      <td>200</td>\n", "      <td>{'q0': {'xy_channel': 1, 'z_dc_channel': 1, 'z_flux_channel': 1, 'readout_channel': 1, 'probe_freq': 6453.789, 'sample_delay': 800, 'sample_width': 700}, 'q1': {'xy_channel': 2, 'z_dc_channel': 3, 'z_flux_channel': 6, 'readout_channel': 1, 'probe_freq': 6536.473, 'sample_delay': 800, 'sample_width': 700}, 'q2': {'xy_channel': 3, 'z_dc_channel': 5, 'z_flux_channel': 1, 'readout_channel': 1, 'probe_freq': 6411.172, 'sample_delay': 800, 'sample_width': 700}, 'q3': {'xy_channel': 4, 'z_dc_channel': 7, 'z_flux_channel': 2, 'readout_channel': 1, 'probe_freq': 6495.257, 'sample_delay': 800, 'sample_width': 700}}</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>env_name</th>\n", "      <td>200</td>\n", "      <td>D00_1019</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sample</th>\n", "      <td>200</td>\n", "      <td>test_chip</td>\n", "      <td>success</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               code  \\\n", "CouplerCount    200   \n", "CouplerParams   200   \n", "QubitCount      200   \n", "QubitParams     200   \n", "env_name        200   \n", "sample          200   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               data  \\\n", "CouplerCount                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      3   \n", "CouplerParams                                                                                                                                                                                                                                {'c0': {'drive_bit': 1, 'probe_bit': 2, 'z_dc_channel': 2, 'z_flux_channel': 5, 'probe_pi_width': 180.0, 'probe_pi_offset': 10.0}, 'c1': {'drive_bit': 2, 'probe_bit': 3, 'z_dc_channel': 4, 'z_flux_channel': 6, 'probe_pi_width': 180.0, 'probe_pi_offset': 10.0}, 'c2': {'drive_bit': 3, 'probe_bit': 4, 'z_dc_channel': 6, 'z_flux_channel': 7, 'probe_pi_width': 180.0, 'probe_pi_offset': 10.0}}   \n", "QubitCount                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        4   \n", "QubitParams    {'q0': {'xy_channel': 1, 'z_dc_channel': 1, 'z_flux_channel': 1, 'readout_channel': 1, 'probe_freq': 6453.789, 'sample_delay': 800, 'sample_width': 700}, 'q1': {'xy_channel': 2, 'z_dc_channel': 3, 'z_flux_channel': 6, 'readout_channel': 1, 'probe_freq': 6536.473, 'sample_delay': 800, 'sample_width': 700}, 'q2': {'xy_channel': 3, 'z_dc_channel': 5, 'z_flux_channel': 1, 'readout_channel': 1, 'probe_freq': 6411.172, 'sample_delay': 800, 'sample_width': 700}, 'q3': {'xy_channel': 4, 'z_dc_channel': 7, 'z_flux_channel': 2, 'readout_channel': 1, 'probe_freq': 6495.257, 'sample_delay': 800, 'sample_width': 700}}   \n", "env_name                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   D00_1019   \n", "sample                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    test_chip   \n", "\n", "                   msg  \n", "CouplerCount   success  \n", "CouplerParams  success  \n", "QubitCount     success  \n", "QubitParams    success  \n", "env_name       success  \n", "sample         success  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 登录个人账户，再次查询芯片信息\n", "\n", "username = \"test_12\"\n", "password = \"123456\"\n", "\n", "Invoker.verify_account(username, password)\n", "\n", "ret_data = db.query_chip_line()\n", "\n", "pd.DataFrame(ret_data)"]}, {"cell_type": "markdown", "id": "3d5fd203", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 查询个人数据\n", "\n", "1、查询个人数据，接口`db.query_data_names()`；\n", "\n", "2、返回，<PERSON><PERSON><PERSON>、<PERSON><PERSON>tPair, <PERSON><PERSON><PERSON>、Config 等数据的 name 列表；"]}, {"cell_type": "code", "execution_count": 6, "id": "f5d099ac", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>data</th>\n", "      <th>msg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Config</th>\n", "      <td>200</td>\n", "      <td>[]</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>200</td>\n", "      <td>[]</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Qubit</th>\n", "      <td>200</td>\n", "      <td>[]</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>QubitPair</th>\n", "      <td>200</td>\n", "      <td>[]</td>\n", "      <td>success</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           code data      msg\n", "Config      200   []  success\n", "Coupler     200   []  success\n", "Qubit       200   []  success\n", "QubitPair   200   []  success"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询个人测试数据\n", "\n", "username = \"test_12\"\n", "password = \"123456\"\n", "\n", "Invoker.verify_account(username, password)\n", "\n", "ret_data = db.query_data_names()\n", "\n", "pd.DataFrame(ret_data)"]}, {"cell_type": "markdown", "id": "f77d0a38", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 初始化基本数据\n", "\n", "### 初始化 BaseQubit 基本数据\n", "\n", "1、初始化 Qubit, <PERSON><PERSON><PERSON> 基本数据， 接口 `db.init_base_qubit_data()`；\n", "\n", "2、提供参数：XY线驱动中频`baseband_freq`；\n", "\n", "3、根据芯片的接线情况 chip_line_connect.json，以及用户提供的 XY线驱动中频`baseband_freq`进行初始化 基本信息；\n", "\n", "\n", "### 初始化 ConfigStore 基本数据\n", "\n", "1、初始化 ConfigStore 基本数据，接口 `db.init_config_data()`；\n", "\n", "2、提供参数：XY线驱动中频`baseband_freq`， 一体机json 数据`instrument_data`， 以及`base_qubit_names`；\n", "\n", "3、其中`base_qubit_names` 为 Qubit/Coupler name 列表，如：`[\"q0\", \"c0\"]`, 若没有提供，系统根据chip_line_connect.json 中的信息生成； "]}, {"cell_type": "code", "execution_count": 7, "id": "11a90965", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'code': 200, 'data': {}, 'msg': 'success'}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 根据芯片的接线等基本信息，初始化 BaseQubit\n", "\n", "baseband_freq = 566.667\n", "\n", "db.init_base_qubit_data(baseband_freq)"]}, {"cell_type": "code", "execution_count": 9, "id": "b948cb6d", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200, 'data': {}, 'msg': 'success'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 根据芯片的接线等基本信息，初始化 Config 文件数据\n", "\n", "from pyQCat.init_instrument_json import InitInstrument\n", "\n", "baseband_freq = 566.667\n", "qaio_type = 8\n", "instrument_data = InitInstrument(unit_type=qaio_type).instrument\n", "\n", "# name_list = [\"q10\", \"q11\", \"q24\", \"c1024\"]\n", "name_list = []\n", "\n", "db.init_config_data(baseband_freq, instrument_data, name_list)"]}, {"cell_type": "code", "execution_count": 10, "id": "4e15e599", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>data</th>\n", "      <th>msg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Config</th>\n", "      <td>200</td>\n", "      <td>[instrument.json, character.json, crosstalk.json, c2.bin, distortion_c2.dat, c1.bin, distortion_c1.dat, c0.bin, distortion_c0.dat, q3.bin, distortion_q3.dat, q2.bin, distortion_q2.dat, q1.bin, distortion_q1.dat, q0.bin, distortion_q0.dat]</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>200</td>\n", "      <td>[c2, c1, c0]</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Qubit</th>\n", "      <td>200</td>\n", "      <td>[q3, q2, q1, q0]</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>QubitPair</th>\n", "      <td>200</td>\n", "      <td>[]</td>\n", "      <td>success</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           code  \\\n", "Config      200   \n", "Coupler     200   \n", "Qubit       200   \n", "QubitPair   200   \n", "\n", "                                                                                                                                                                                                                                                     data  \\\n", "Config     [instrument.json, character.json, crosstalk.json, c2.bin, distortion_c2.dat, c1.bin, distortion_c1.dat, c0.bin, distortion_c0.dat, q3.bin, distortion_q3.dat, q2.bin, distortion_q2.dat, q1.bin, distortion_q1.dat, q0.bin, distortion_q0.dat]   \n", "<PERSON><PERSON><PERSON>                                                                                                                                                                                                                                      [c2, c1, c0]   \n", "Qubit                                                                                                                                                                                                                                    [q3, q2, q1, q0]   \n", "QubitPair                                                                                                                                                                                                                                              []   \n", "\n", "               msg  \n", "Config     success  \n", "Coupler    success  \n", "Qubit      success  \n", "QubitPair  success  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询个人测试数据\n", "\n", "ret_data = db.query_data_names()\n", "\n", "pd.DataFrame(ret_data)"]}, {"cell_type": "markdown", "id": "86cef9e9", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 借鉴他人基本数据\n", "\n", "1、拷贝他人的 BaseQubit, Config 数据，接口`db.copy_other_user_data()`；\n", "\n", "2、需要提供参数：\n", "\n", "   * 其他用户名`other_username`\n", "   \n", "   * 数据类型`type_name`，目前两种数据类型 `BaseQubit`， `Config`\n", "   \n", "   * 数据元素名称列表`element_names`，\n", "      比如拷贝 `BaseQubit` 数据，传入名称列表`[\"q0\", \"c0\"]`, \n", "      `Config` 数据，传入文件名称列表`[\"q0.bin\", \"distortion_q0.dat\"]` \n", "      \n", "3、拷贝别人数据，需要指定该用户有哪些数据，查询其他用户数据接口 `db.query_other_user_data()`, 需要提供参数：`other_username`, `type_name`"]}, {"cell_type": "code", "execution_count": 35, "id": "66c1b8a9", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>username</th>\n", "      <th>groups</th>\n", "      <th>email</th>\n", "      <th>phone_num</th>\n", "      <th>is_super</th>\n", "      <th>is_admin</th>\n", "      <th>status</th>\n", "      <th>create_time</th>\n", "      <th>last_login_time</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>test_12</td>\n", "      <td>normal</td>\n", "      <td><EMAIL></td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>2022-10-14 18:27:00</td>\n", "      <td>2022-10-14 18:31:12</td>\n", "      <td>63493974bca191dfa31b00d5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  username  groups         email phone_num  is_super  is_admin  status  \\\n", "0  test_12  normal  <EMAIL>      None     False     False       0   \n", "\n", "           create_time      last_login_time                        id  \n", "0  2022-10-14 18:27:00  2022-10-14 18:31:12  63493974bca191dfa31b00d5  "]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["# 借鉴他人的 Base<PERSON><PERSON><PERSON>, Config 数据\n", "\n", "ret_data = db.query_user_info()\n", "\n", "target_data = ret_data.get(\"data\")\n", "pd.DataFrame(target_data, index=[0])"]}, {"cell_type": "code", "execution_count": 36, "id": "e461ee81", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>username</th>\n", "      <th>groups</th>\n", "      <th>is_super</th>\n", "      <th>is_admin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CK01</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CK02</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ck_test</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>hqbdflbdwjbaoyaahyotcaludqlhuwttyuofcybdgiqfdjlyhfkyehlbweuqrayftfwyyjigfwyayewbkybrgdwjbewuhioeqbjj</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>lgeokqwfqfoufwbliqgactkgqiywforworidrgloaugkgwouaecaolhqhbegjyrwcbkfkefafogdrjieyldykurlecutbdewfjrk</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>kluhbotrdqfelhocokitferwuddgqcwuihaekofflwhweukhkoubcleoblbwaiqkbithdkurdwblyfwlwyffahlftldtiulifyrl</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>qduqwehrwjcqgjrthyekuauojujtwclwdacflllwbiibfiwhkcbkwuohkdejgybjjatrcyqhiqetliecujaqugaucwbyqlojtdab</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>uacdgjkfoataaifafrhbhjqjdigfakjykfhcbjdtbdkigbrrkedccyfqjebcilgedjfyhoejqyjqhdirkycrlbatwlqjolktdahk</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>asdfhaw</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>test_06</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>ssssss</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>A_1000</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>A_1001</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>A_1002</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>A_1003</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>A_1004</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>zyc</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>test_09</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>test_10</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>test_11</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>test_12</td>\n", "      <td>normal</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                                                username  \\\n", "0                                                                                                   CK01   \n", "1                                                                                                   CK02   \n", "2                                                                                                ck_test   \n", "3   hqbdflbdwjbaoyaahyotcaludqlhuwttyuofcybdgiqfdjlyhfkyehlbweuqrayftfwyyjigfwyayewbkybrgdwjbewuhioeqbjj   \n", "4   lgeokqwfqfoufwbliqgactkgqiywforworidrgloaugkgwouaecaolhqhbegjyrwcbkfkefafogdrjieyldykurlecutbdewfjrk   \n", "5   kluhbotrdqfelhocokitferwuddgqcwuihaekofflwhweukhkoubcleoblbwaiqkbithdkurdwblyfwlwyffahlftldtiulifyrl   \n", "6   qduqwehrwjcqgjrthyekuauojujtwclwdacflllwbiibfiwhkcbkwuohkdejgybjjatrcyqhiqetliecujaqugaucwbyqlojtdab   \n", "7   uacdgjkfoataaifafrhbhjqjdigfakjykfhcbjdtbdkigbrrkedccyfqjebcilgedjfyhoejqyjqhdirkycrlbatwlqjolktdahk   \n", "8                                                                                                asdfhaw   \n", "9                                                                                                test_06   \n", "10                                                                                                ssssss   \n", "11                                                                                                A_1000   \n", "12                                                                                                A_1001   \n", "13                                                                                                A_1002   \n", "14                                                                                                A_1003   \n", "15                                                                                                A_1004   \n", "16                                                                                                   zyc   \n", "17                                                                                               test_09   \n", "18                                                                                               test_10   \n", "19                                                                                               test_11   \n", "20                                                                                               test_12   \n", "\n", "    groups  is_super  is_admin  \n", "0   normal     False      True  \n", "1   normal     False     False  \n", "2   normal     False     False  \n", "3   normal     False     False  \n", "4   normal     False     False  \n", "5   normal     False     False  \n", "6   normal     False     False  \n", "7   normal     False     False  \n", "8   normal     False     False  \n", "9   normal     False     False  \n", "10  normal     False     False  \n", "11  normal     False     False  \n", "12  normal     False     False  \n", "13  normal     False     False  \n", "14  normal     False     False  \n", "15  normal     False     False  \n", "16  normal     False     False  \n", "17  normal     False     False  \n", "18  normal     False     False  \n", "19  normal     False     False  \n", "20  normal     False     False  "]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询自己所在组的所有用户\n", "\n", "ret_data = db.query_group_info()\n", "\n", "pd.DataFrame(ret_data.get(\"data\"))"]}, {"cell_type": "code", "execution_count": 37, "id": "19cfe7fd", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>data</th>\n", "      <th>msg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>200</td>\n", "      <td>q0</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>200</td>\n", "      <td>c2</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>200</td>\n", "      <td>c1</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>200</td>\n", "      <td>c0</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>200</td>\n", "      <td>q3</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>200</td>\n", "      <td>q2</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>200</td>\n", "      <td>q1</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>200</td>\n", "      <td>q0q1</td>\n", "      <td>success</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   code  data      msg\n", "0   200    q0  success\n", "1   200    c2  success\n", "2   200    c1  success\n", "3   200    c0  success\n", "4   200    q3  success\n", "5   200    q2  success\n", "6   200    q1  success\n", "7   200  q0q1  success"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询同组其他用户的数据信息\n", "\n", "other_username = \"test_06\"\n", "type_name = \"BaseQubit\"\n", "\n", "ret_data = db.query_other_user_data(other_username, type_name)\n", "\n", "pd.DataFrame(ret_data)"]}, {"cell_type": "code", "execution_count": 38, "id": "97b30f19", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>data</th>\n", "      <th>msg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>200</td>\n", "      <td>instrument.json</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>200</td>\n", "      <td>character.json</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>200</td>\n", "      <td>crosstalk.json</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>200</td>\n", "      <td>c2.bin</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>200</td>\n", "      <td>distortion_c2.dat</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>200</td>\n", "      <td>c1.bin</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>200</td>\n", "      <td>distortion_c1.dat</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>200</td>\n", "      <td>c0.bin</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>200</td>\n", "      <td>distortion_c0.dat</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>200</td>\n", "      <td>q3.bin</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>200</td>\n", "      <td>distortion_q3.dat</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>200</td>\n", "      <td>q2.bin</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>200</td>\n", "      <td>distortion_q2.dat</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>200</td>\n", "      <td>q1.bin</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>200</td>\n", "      <td>distortion_q1.dat</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>200</td>\n", "      <td>q0.bin</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>200</td>\n", "      <td>distortion_q0.dat</td>\n", "      <td>success</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    code               data      msg\n", "0    200    instrument.json  success\n", "1    200     character.json  success\n", "2    200     crosstalk.json  success\n", "3    200             c2.bin  success\n", "4    200  distortion_c2.dat  success\n", "5    200             c1.bin  success\n", "6    200  distortion_c1.dat  success\n", "7    200             c0.bin  success\n", "8    200  distortion_c0.dat  success\n", "9    200             q3.bin  success\n", "10   200  distortion_q3.dat  success\n", "11   200             q2.bin  success\n", "12   200  distortion_q2.dat  success\n", "13   200             q1.bin  success\n", "14   200  distortion_q1.dat  success\n", "15   200             q0.bin  success\n", "16   200  distortion_q0.dat  success"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询同组其他用户的数据信息\n", "\n", "other_username = \"test_06\"\n", "type_name = \"Config\"\n", "\n", "ret_data = db.query_other_user_data(other_username, type_name)\n", "\n", "pd.DataFrame(ret_data)"]}, {"cell_type": "code", "execution_count": 39, "id": "fb30a0e9", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200, 'data': {}, 'msg': 'success'}"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["# 拷贝某个用户 BaseQubit 数据\n", "\n", "other_username = \"test_06\"\n", "type_name = \"BaseQubit\"\n", "element_names = ['q0', 'c2', 'c1', 'c0', 'q3', 'q2', 'q1']\n", "\n", "db.copy_other_user_data(other_username, type_name, element_names)"]}, {"cell_type": "code", "execution_count": 40, "id": "39e164d7", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200, 'data': {}, 'msg': 'success'}"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["# 拷贝某个用户 BaseQubit 数据\n", "\n", "other_username = \"test_06\"\n", "type_name = \"Config\"\n", "element_names = [\n", "    'cz_pulse.json',\n", "    'distortion_q3.dat',\n", "    'instrument.json',\n", "    'character.json',\n", "    'crosstalk.json',\n", "    'c2.bin',\n", "    'distortion_c2.dat',\n", "    'c1.bin',\n", "    'distortion_c1.dat',\n", "    'c0.bin',\n", "    'distortion_c0.dat',\n", "    'q3.bin',\n", "    'q2.bin',\n", "    'distortion_q2.dat',\n", "    'q1.bin',\n", "    'distortion_q1.dat',\n", "    'q0.bin',\n", "    'distortion_q0.dat'\n", "]\n", "\n", "db.copy_other_user_data(other_username, type_name, element_names)"]}, {"cell_type": "code", "execution_count": 41, "id": "d841d5c3", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>data</th>\n", "      <th>msg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Config</th>\n", "      <td>200</td>\n", "      <td>[distortion_q0.dat, q0.bin, distortion_q1.dat, q1.bin, distortion_q2.dat, q2.bin, q3.bin, distortion_c0.dat, c0.bin, distortion_c1.dat, c1.bin, distortion_c2.dat, c2.bin, crosstalk.json, character.json, instrument.json, distortion_q3.dat]</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>200</td>\n", "      <td>[c0, c1, c2]</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Qubit</th>\n", "      <td>200</td>\n", "      <td>[q1, q2, q3, q0]</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>QubitPair</th>\n", "      <td>200</td>\n", "      <td>[]</td>\n", "      <td>success</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           code  \\\n", "Config      200   \n", "Coupler     200   \n", "Qubit       200   \n", "QubitPair   200   \n", "\n", "                                                                                                                                                                                                                                                     data  \\\n", "Config     [distortion_q0.dat, q0.bin, distortion_q1.dat, q1.bin, distortion_q2.dat, q2.bin, q3.bin, distortion_c0.dat, c0.bin, distortion_c1.dat, c1.bin, distortion_c2.dat, c2.bin, crosstalk.json, character.json, instrument.json, distortion_q3.dat]   \n", "<PERSON><PERSON><PERSON>                                                                                                                                                                                                                                      [c0, c1, c2]   \n", "Qubit                                                                                                                                                                                                                                    [q1, q2, q3, q0]   \n", "QubitPair                                                                                                                                                                                                                                              []   \n", "\n", "               msg  \n", "Config     success  \n", "Coupler    success  \n", "Qubit      success  \n", "QubitPair  success  "]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询个人测试数据\n", "\n", "\n", "ret_data = db.query_data_names()\n", "\n", "pd.DataFrame(ret_data)"]}, {"cell_type": "markdown", "id": "a9dec80c", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 查询 Config 文件数据\n", "\n", "1、查询接口 `db.query_config()`；\n", "\n", "2、接口参数：文件名`file_name`；\n", "\n", "3、Config 数据支持三种类型的数据\n", "* bytes 一般用于存分类器数据，文件名后缀为 .bin, 比如 q0.bin\n", "* list 一般存放畸变数据，文件名后缀为 .dat, 比如 distortion_q3.dat\n", "* dict 一般存放各种字典格式的数据，文件名后缀为 .json, 比如 instrument.json"]}, {"cell_type": "code", "execution_count": 42, "id": "07a048b2", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>bin</th>\n", "      <th>dat</th>\n", "      <th>filename</th>\n", "      <th>username</th>\n", "      <th>sample</th>\n", "      <th>env_name</th>\n", "      <th>json</th>\n", "      <th>update_time</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>crosstalk.json</td>\n", "      <td>test_12</td>\n", "      <td>test_chip</td>\n", "      <td>D00_1011</td>\n", "      <td>{'infos': ['q0', 'q1', 'q2', 'q3', 'c0', 'c1', 'c2'], 'ac_crosstalk': [[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0]], 'dc_crosstalk': [[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0]]}</td>\n", "      <td>2022-10-14 18:31:55</td>\n", "      <td>63493a9bbca191dfa31b00e2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    bin   dat        filename username     sample  env_name  \\\n", "0  None  None  crosstalk.json  test_12  test_chip  D00_1011   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              json  \\\n", "0  {'infos': ['q0', 'q1', 'q2', 'q3', 'c0', 'c1', 'c2'], 'ac_crosstalk': [[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0]], 'dc_crosstalk': [[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0]]}   \n", "\n", "           update_time                        id  \n", "0  2022-10-14 18:31:55  63493a9bbca191dfa31b00e2  "]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询某个配置文件的具体信息\n", "\n", "file_name = \"crosstalk.json\"\n", "\n", "ret_data = db.query_config(file_name)\n", "\n", "pd.DataFrame(ret_data.get(\"data\"))"]}, {"cell_type": "markdown", "id": "9dfa1775", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 更新 Config 数据\n", "\n", "1、更新 Config 数据，接口 `db.update_single_config()`\n", "\n", "2、提供参数：文件名`file_name`, 具体数据`file_data`；\n", "\n", "3、注意文件类型与数据格式要对应，比如 json 文件对应 dict 数据类型；"]}, {"cell_type": "code", "execution_count": 43, "id": "ec9dfe4d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>bin</th>\n", "      <th>json</th>\n", "      <th>filename</th>\n", "      <th>username</th>\n", "      <th>sample</th>\n", "      <th>env_name</th>\n", "      <th>dat</th>\n", "      <th>update_time</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>distortion_q3.dat</td>\n", "      <td>test_12</td>\n", "      <td>test_chip</td>\n", "      <td>D00_1011</td>\n", "      <td>[]</td>\n", "      <td>2022-10-14 18:31:55</td>\n", "      <td>63493a9bbca191dfa31b00df</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    bin  json           filename username     sample  env_name dat  \\\n", "0  None  None  distortion_q3.dat  test_12  test_chip  D00_1011  []   \n", "\n", "           update_time                        id  \n", "0  2022-10-14 18:31:55  63493a9bbca191dfa31b00df  "]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询某个配置文件的具体信息\n", "\n", "file_name = \"distortion_q3.dat\"\n", "\n", "ret_data = db.query_config(file_name)\n", "\n", "pd.DataFrame(ret_data.get(\"data\"))"]}, {"cell_type": "code", "execution_count": 44, "id": "8bd92e98", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'code': 200, 'data': {}, 'msg': 'success'}"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["# 更新或新增 Config 数据\n", "\n", "import numpy as np\n", "\n", "file = r\"E:\\WorkCode\\Bitbucket\\split_flow_pyQCat\\pyqcat\\demo_dist\\q3_to_store_response_iteration_times0.dat\"\n", "\n", "\n", "dat_data = np.loadtxt(file)\n", "file_data = dat_data.tolist()\n", "\n", "file_name = \"distortion_q3.dat\"\n", "db.update_single_config(file_name, file_data)"]}, {"cell_type": "code", "execution_count": 45, "id": "0af8c729", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>bin</th>\n", "      <th>json</th>\n", "      <th>filename</th>\n", "      <th>username</th>\n", "      <th>sample</th>\n", "      <th>env_name</th>\n", "      <th>dat</th>\n", "      <th>update_time</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>distortion_q3.dat</td>\n", "      <td>test_12</td>\n", "      <td>test_chip</td>\n", "      <td>D00_1011</td>\n", "      <td>[[0.0, 0.96238], [1.25, 0.96342], [2.5, 0.963276], [3.75, 0.966768], [5.0, 0.966792], [6.25, 0.967372], [7.5, 0.967544], [8.75, 0.96714], [10.0, 0.968844], [11.25, 0.969794], [12.5, 0.970254], [13.75, 0.971002], [15.0, 0.970198], [16.25, 0.97159], [17.5, 0.970346], [18.75, 0.970252], [20.0, 0.969736], [21.25, 0.971276], [22.5, 0.97013], [23.75, 0.97169], [25.0, 0.97173], [26.25, 0.971016], [27.5, 0.970316], [28.75, 0.971348], [30.0, 0.973512], [31.25, 0.97477], [32.5, 0.974296], [33.75, 0.974094], [35.0, 0.976326], [36.25, 0.976168], [37.5, 0.976278], [38.75, 0.9777], [40.0, 0.978294], [41.25, 0.978708], [42.5, 0.978988], [43.75, 0.977866], [45.0, 0.97724], [46.25, 0.977518], [47.5, 0.97964], [48.75, 0.979378], [50.0, 0.977316], [52.5, 0.977328], [55.0, 0.97987], [57.5, 0.97828], [60.0, 0.979826], [62.5, 0.979254], [65.0, 0.978888], [67.5, 0.979994], [70.0, 0.982278], [72.5, 0.983224], [75.0, 0.983412], [77.5, 0.983768], [80.0, 0.984534], [82.5, 0.984928], [85.0, 0.984948], [87.5, 0.986968], [90.0, 0.986894], [92.5, 0.985184], [95.0, 0.985862], [97.5, 0.986866], [100.0, 0.986924], [107.5, 0.988012], [115.0, 0.989716], [122.5, 0.990798], [130.0, 0.99143], [137.5, 0.991368], [145.0, 0.992614], [152.5, 0.994074], [160.0, 0.994302], [167.5, 0.993602], [175.0, 0.993382], [182.5, 0.995468], [190.0, 0.995918], [197.5, 0.996822], [205.0, 0.996178], [217.5, 0.995732], [230.0, 0.99688], [242.5, 0.996684], [255.0, 0.999384], [267.5, 0.997328], [280.0, 0.99695], [292.5, 0.997244], [305.0, 0.997634], [317.5, 0.995776], [330.0, 0.99599], [342.5, 0.994888], [355.0, 0.996006], [367.5, 0.995858], [380.0, 0.994434], [392.5, 0.99379], [405.0, 0.99368], [417.5, 0.993962], [430.0, 0.993058], [442.5, 0.993642], [455.0, 0.992874], [467.5, 0.992938], [480.0, 0.993474], [492.5, 0.992502], [505.0, 0.99312], [517.5, 0.993442], ...]</td>\n", "      <td>2022-10-14 18:32:17</td>\n", "      <td>63493a9bbca191dfa31b00df</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    bin  json           filename username     sample  env_name  \\\n", "0  None  None  distortion_q3.dat  test_12  test_chip  D00_1011   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            dat  \\\n", "0  [[0.0, 0.96238], [1.25, 0.96342], [2.5, 0.963276], [3.75, 0.966768], [5.0, 0.966792], [6.25, 0.967372], [7.5, 0.967544], [8.75, 0.96714], [10.0, 0.968844], [11.25, 0.969794], [12.5, 0.970254], [13.75, 0.971002], [15.0, 0.970198], [16.25, 0.97159], [17.5, 0.970346], [18.75, 0.970252], [20.0, 0.969736], [21.25, 0.971276], [22.5, 0.97013], [23.75, 0.97169], [25.0, 0.97173], [26.25, 0.971016], [27.5, 0.970316], [28.75, 0.971348], [30.0, 0.973512], [31.25, 0.97477], [32.5, 0.974296], [33.75, 0.974094], [35.0, 0.976326], [36.25, 0.976168], [37.5, 0.976278], [38.75, 0.9777], [40.0, 0.978294], [41.25, 0.978708], [42.5, 0.978988], [43.75, 0.977866], [45.0, 0.97724], [46.25, 0.977518], [47.5, 0.97964], [48.75, 0.979378], [50.0, 0.977316], [52.5, 0.977328], [55.0, 0.97987], [57.5, 0.97828], [60.0, 0.979826], [62.5, 0.979254], [65.0, 0.978888], [67.5, 0.979994], [70.0, 0.982278], [72.5, 0.983224], [75.0, 0.983412], [77.5, 0.983768], [80.0, 0.984534], [82.5, 0.984928], [85.0, 0.984948], [87.5, 0.986968], [90.0, 0.986894], [92.5, 0.985184], [95.0, 0.985862], [97.5, 0.986866], [100.0, 0.986924], [107.5, 0.988012], [115.0, 0.989716], [122.5, 0.990798], [130.0, 0.99143], [137.5, 0.991368], [145.0, 0.992614], [152.5, 0.994074], [160.0, 0.994302], [167.5, 0.993602], [175.0, 0.993382], [182.5, 0.995468], [190.0, 0.995918], [197.5, 0.996822], [205.0, 0.996178], [217.5, 0.995732], [230.0, 0.99688], [242.5, 0.996684], [255.0, 0.999384], [267.5, 0.997328], [280.0, 0.99695], [292.5, 0.997244], [305.0, 0.997634], [317.5, 0.995776], [330.0, 0.99599], [342.5, 0.994888], [355.0, 0.996006], [367.5, 0.995858], [380.0, 0.994434], [392.5, 0.99379], [405.0, 0.99368], [417.5, 0.993962], [430.0, 0.993058], [442.5, 0.993642], [455.0, 0.992874], [467.5, 0.992938], [480.0, 0.993474], [492.5, 0.992502], [505.0, 0.99312], [517.5, 0.993442], ...]   \n", "\n", "           update_time                        id  \n", "0  2022-10-14 18:32:17  63493a9bbca191dfa31b00df  "]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询某个配置文件的具体信息\n", "\n", "file_name = \"distortion_q3.dat\"\n", "\n", "ret_data = db.query_config(file_name)\n", "\n", "pd.DataFrame(ret_data.get(\"data\"))"]}, {"cell_type": "markdown", "id": "a9bbc3b8", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> \n", "\n", "介绍 Monster 中如何获取 <PERSON><PERSON><PERSON>，<PERSON><PERSON><PERSON>, QubitPair\n", "\n"]}, {"cell_type": "markdown", "id": "63f9dd7f", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### 加载\n", "\n", "1、从 data service 加载， 接口`get_parameters()`, 提供参数: 类型`field`， 名称`name`；\n", "\n", "2、从本地文件获取数据，加载成对象，接口`get_parameters()`, 提供参数: 类型`field`， 名称`name`，文件路径`file_path`；\n", "\n", "* Qubit 对应 `field` **qubit**\n", "\n", "* Coupler 对应 `field` **coupler**\n", "\n", "* QubitPair 对应 `field` **qubit_pair**\n"]}, {"cell_type": "code", "execution_count": 46, "id": "1c02da2a", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 18:32:28\u001b[0m | \u001b[31m\u001b[1m ERROR  \u001b[0m | \u001b[31m\u001b[1mLoad q10 data error: The Server does not find the information you need!\u001b[0m\n", "\u001b[33m2022-10-14 18:32:28\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1mNo Qubit 10 data,that will create a new object.\u001b[0m\n"]}, {"data": {"text/plain": ["{'bit': 10,\n", " 'name': 'q10',\n", " 'tunable': <PERSON><PERSON><PERSON>,\n", " 'goodness': <PERSON><PERSON><PERSON>,\n", " 'drive_freq': None,\n", " 'drive_power': None,\n", " 'probe_freq': None,\n", " 'probe_power': None,\n", " 'tls_freq': None,\n", " 'anharmonicity': -240,\n", " 'dc': 0.0,\n", " 'dc_max': 0.0,\n", " 'dc_min': 0.0,\n", " 'ac': 0.0,\n", " 'T1': 20000,\n", " 'T2': 10000,\n", " 'z_flux_channel': None,\n", " 'z_dc_channel': None,\n", " 'update_time': None,\n", " 'idle_point': 0.0,\n", " '_row': None,\n", " '_col': None,\n", " 'ac_spectrum': {'standard': [0.0, 0.0, 0.0, 0.0, 0.0]},\n", " 'xy_channel': None,\n", " 'readout_channel': None,\n", " 'sample_delay': None,\n", " 'sample_width': None,\n", " 'XYwave': {'Xpi': 0.8,\n", "  'Xpi2': 0.4,\n", "  'Ypi': None,\n", "  'Ypi2': None,\n", "  'Zpi': None,\n", "  'baseband_freq': 566.667,\n", "  'delta': -240,\n", "  'detune_pi': 0.0,\n", "  'detune_pi2': 0.0,\n", "  'alpha': 1,\n", "  'offset': 5.0,\n", "  'time': 20.0},\n", " 'Zwave': {'width': None, 'amp': None},\n", " 'Mwave': {'width': 1500, 'amp': 0.5, 'IF': 600},\n", " 'union_readout': {'width': 4000, 'amp': None, 'index': [], 'probe_IF': None},\n", " 'readout_point': {'amp': 0.0, 'sigma': 1.25, 'buffer': 5}}"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["# 从 Data Service 获取 qubit 信息，没有将初始化对象 \n", "\n", "field = \"qubit\"\n", "name = \"q10\"\n", "\n", "qubit = get_parameters(field, name)\n", "\n", "qubit.to_dict()"]}, {"cell_type": "code", "execution_count": 47, "id": "42ab5cf8", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'bit': 1,\n", " 'name': 'c1',\n", " 'tunable': <PERSON><PERSON><PERSON>,\n", " 'goodness': <PERSON><PERSON><PERSON>,\n", " 'drive_freq': None,\n", " 'drive_power': None,\n", " 'probe_freq': 6496.4,\n", " 'probe_power': -19.0,\n", " 'tls_freq': None,\n", " 'anharmonicity': None,\n", " 'dc': 0.0,\n", " 'dc_max': 0.0,\n", " 'dc_min': 0.0,\n", " 'ac': 0.0,\n", " 'T1': 20000.0,\n", " 'T2': 10000.0,\n", " 'z_flux_channel': 6,\n", " 'z_dc_channel': 6,\n", " 'update_time': None,\n", " 'idle_point': 0.0,\n", " '_row': None,\n", " '_col': None,\n", " 'ac_spectrum': {'standard': [0.0, 0.0, 0.0, 0.0, 0.0]},\n", " 'drive_bit': 1,\n", " 'probe_bit': 2,\n", " 'probe_drive_freq': None,\n", " 'probe_drive_power': None,\n", " 'Zwave': {'width': None, 'amp': None},\n", " 'drive_XYwave': {'Xpi': 0.8,\n", "  'Xpi2': 0.4,\n", "  'Ypi': None,\n", "  'Ypi2': None,\n", "  'Zpi': None,\n", "  'baseband_freq': 566.667,\n", "  'delta': -240.0,\n", "  'detune_pi': 0.0,\n", "  'detune_pi2': 0.0,\n", "  'alpha': 1.0,\n", "  'offset': 5.0,\n", "  'time': 20.0},\n", " 'probe_XYwave': {'Xpi': 0.8,\n", "  'Xpi2': 0.4,\n", "  'Ypi': None,\n", "  'Ypi2': None,\n", "  'Zpi': None,\n", "  'baseband_freq': 566.667,\n", "  'delta': -240.0,\n", "  'detune_pi': 0.0,\n", "  'detune_pi2': 0.0,\n", "  'alpha': 1.0,\n", "  'offset': 10.0,\n", "  'time': 180.0}}"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["# 从本地文件 json或 yaml 格式文件, 获取 qubit 信息，加载成对象 \n", "\n", "field = \"coupler\"\n", "name = \"c1\"\n", "file_path = r\"F:\\PersonTest\\store_bak\\coupler_store\\0826\\c1_config_100020.yaml\"\n", "\n", "coupler = get_parameters(field, name, file_path)\n", "\n", "coupler.to_dict()"]}, {"cell_type": "code", "execution_count": 48, "id": "56d1a455", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 18:32:36\u001b[0m | \u001b[31m\u001b[1m ERROR  \u001b[0m | \u001b[31m\u001b[1mLoad q0q1 data error: The Server does not find the information you need!\u001b[0m\n", "\u001b[33m2022-10-14 18:32:36\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1mNo QubitPair q0q1 data,that will create a new object.\u001b[0m\n"]}, {"data": {"text/plain": ["{'name': 'q0q1',\n", " 'interaction_point': 0.0,\n", " 'accumulation_phase': 0.0,\n", " 'metadata': {}}"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["# 从 Data Service 获取 qubit pair 信息，没有将初始化对象 \n", "\n", "field = \"qubit_pair\"\n", "name = \"q0q1\"\n", "\n", "qubit_pair = get_parameters(field, name)\n", "\n", "qubit_pair.to_dict()"]}, {"cell_type": "code", "execution_count": 56, "id": "ecde423c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name': 'q0q1',\n", " 'interaction_point': 0.0,\n", " 'accumulation_phase': 3.141592653589793,\n", " 'metadata': {'info': {'name': 'q0q1',\n", "   'qh': 'q0',\n", "   'ql': 'q1',\n", "   'accumulation_phase': 3.1415926,\n", "   'width': 150},\n", "  'parking_qubit_pulse_type': 'flattop',\n", "  'parking': {'q2': {'amp': 0.391583}, 'q3': {'amp': 0.391583}},\n", "  'swap': {'11': {'swap_fit_args': {'g': 3.7209967144208e-07,\n", "     'A': 7.19996527111814,\n", "     'Z0': 0.316382926814895},\n", "    'set_tb_flag': True,\n", "    'width': 120.0,\n", "    'sigma': 1.25,\n", "    'ql_z_amp': 0.0,\n", "    'qh_z_amp': 0.32,\n", "    'z_pulse_tc': 1571.358},\n", "   '10': {'swap_fit_args': {'g': -1.14880093924055e-06,\n", "     'A': 16.2658174530869,\n", "     'Z0': 0.303345459887375},\n", "    'set_tb_flag': True,\n", "    'width': 120.0,\n", "    'sigma': 1.25,\n", "    'ql_z_amp': 0.0,\n", "    'qh_z_amp': 0.29,\n", "    'z_pulse_tc': 1.007}},\n", "  'gate': {'q0': {'amp': 0.0,\n", "    'sigma': 1.25,\n", "    'buffer': 6.74409952546175,\n", "    'is_control': True,\n", "    'phase': -0.0296909679736865},\n", "   'q1': {'amp': 0.391583,\n", "    'sigma': 1.25,\n", "    'buffer': 6.74409952546175,\n", "    'is_control': True,\n", "    'phase': -3.80095809067134},\n", "   'q2': {'amp': 0.4,\n", "    'sigma': 1.25,\n", "    'buffer': 6.74409952546175,\n", "    'is_control': True,\n", "    'phase': -1.08247378861842},\n", "   'q3': {'amp': 0.4,\n", "    'sigma': 1.25,\n", "    'buffer': 6.74409952546175,\n", "    'is_control': True,\n", "    'phase': -1.08247378861842}}}}"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["# 修改 qubit pair 的属性值\n", "\n", "import json\n", "\n", "import numpy as np\n", "\n", "qubit_pair.accumulation_phase = np.pi\n", "\n", "with open(\"F:\\PersonTest\\cz_gate.json\", mode=\"r\", encoding=\"utf-8\") as fp:\n", "    data = json.load(fp)\n", "\n", "target_data = data.get(\"q0q1\")\n", "qubit_pair.metadata = target_data if isinstance(target_data, dict) else {}\n", "\n", "qubit_pair.to_dict()"]}, {"cell_type": "code", "execution_count": 57, "id": "361d6e99", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 12:00:43\u001b[0m | \u001b[33m\u001b[1m UPDATE \u001b[0m | \u001b[33m\u001b[1mSave q10 to data service success.\u001b[0m\n", "\u001b[33m2022-10-14 12:00:43\u001b[0m | \u001b[33m\u001b[1m UPDATE \u001b[0m | \u001b[33m\u001b[1mSave c1 to data service success.\u001b[0m\n", "\u001b[33m2022-10-14 12:00:43\u001b[0m | \u001b[33m\u001b[1m UPDATE \u001b[0m | \u001b[33m\u001b[1mSave QubitPair(group=(q0q1)) to data service success.\u001b[0m\n"]}], "source": ["# 保存到 data service，通过对象方法 save_data()\n", "\n", "qubit.save_data()\n", "\n", "coupler.save_data()\n", "\n", "qubit_pair.save_data()"]}, {"cell_type": "code", "execution_count": 58, "id": "a217dd5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 12:04:20\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1mq10 infos save in F:\\PersonTest\\export_data\\q10_20221014_120420.json\u001b[0m\n", "\u001b[33m2022-10-14 12:04:20\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1mc1 infos save in F:\\PersonTest\\export_data\\c1_20221014_120420.json\u001b[0m\n", "\u001b[33m2022-10-14 12:04:20\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1mq0q1 infos save in F:\\PersonTest\\export_data\\q0q1_20221014_120420.json\u001b[0m\n"]}], "source": ["# 保存到本地，支持导出 json/yaml 格式文件， to_file()\n", "\n", "export_path = r\"F:\\PersonTest\\export_data\"\n", "fmt = \"json\"\n", "\n", "qubit.to_file(export_path=export_path, fmt=fmt)\n", "\n", "coupler.to_file(export_path=export_path, fmt=fmt)\n", "\n", "qubit_pair.to_file(export_path=export_path, fmt=fmt)"]}, {"cell_type": "code", "execution_count": 59, "id": "2fc95a23", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 12:04:35\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1mq10 infos save in F:\\PersonTest\\export_data\\q10_20221014_120435.yaml\u001b[0m\n", "\u001b[33m2022-10-14 12:04:35\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1mc1 infos save in F:\\PersonTest\\export_data\\c1_20221014_120435.yaml\u001b[0m\n", "\u001b[33m2022-10-14 12:04:35\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1mq0q1 infos save in F:\\PersonTest\\export_data\\q0q1_20221014_120435.yaml\u001b[0m\n"]}], "source": ["# 保存到本地，支持导出 json/yaml 格式文件， to_file()\n", "\n", "export_path = r\"F:\\PersonTest\\export_data\"\n", "fmt = \"yaml\"\n", "\n", "qubit.to_file(export_path=export_path, fmt=fmt)\n", "\n", "coupler.to_file(export_path=export_path, fmt=fmt)\n", "\n", "qubit_pair.to_file(export_path=export_path, fmt=fmt)"]}, {"cell_type": "code", "execution_count": 5, "id": "d2ec3bc0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>data</th>\n", "      <th>msg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Config</th>\n", "      <td>200</td>\n", "      <td>[distortion_q3.dat, distortion_q0.dat, q0.bin, distortion_q1.dat, q1.bin, distortion_q2.dat, q2.bin, q3.bin, distortion_c0.dat, c0.bin, distortion_c1.dat, c1.bin, distortion_c2.dat, c2.bin, crosstalk.json, character.json, instrument.json]</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>200</td>\n", "      <td>[c1, c0, c2]</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Qubit</th>\n", "      <td>200</td>\n", "      <td>[q10, q1, q2, q3, q0]</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>QubitPair</th>\n", "      <td>200</td>\n", "      <td>[q0q1]</td>\n", "      <td>success</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           code  \\\n", "Config      200   \n", "Coupler     200   \n", "Qubit       200   \n", "QubitPair   200   \n", "\n", "                                                                                                                                                                                                                                                     data  \\\n", "Config     [distortion_q3.dat, distortion_q0.dat, q0.bin, distortion_q1.dat, q1.bin, distortion_q2.dat, q2.bin, q3.bin, distortion_c0.dat, c0.bin, distortion_c1.dat, c1.bin, distortion_c2.dat, c2.bin, crosstalk.json, character.json, instrument.json]   \n", "<PERSON><PERSON><PERSON>                                                                                                                                                                                                                                      [c1, c0, c2]   \n", "Qubit                                                                                                                                                                                                                               [q10, q1, q2, q3, q0]   \n", "QubitPair                                                                                                                                                                                                                                          [q0q1]   \n", "\n", "               msg  \n", "Config     success  \n", "Coupler    success  \n", "Qubit      success  \n", "QubitPair  success  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询个人测试数据\n", "\n", "\n", "ret_data = db.query_data_names()\n", "\n", "pd.DataFrame(ret_data)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.13"}}, "nbformat": 4, "nbformat_minor": 5}