﻿pyQCat.experiments.composite.SingleQubitPhase
=============================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: SingleQubitPhase

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~SingleQubitPhase.__init__
      ~SingleQubitPhase.component_experiment
      ~SingleQubitPhase.from_experiment_context
      ~SingleQubitPhase.get_qubit_str
      ~SingleQubitPhase.options_table
      ~SingleQubitPhase.run
      ~SingleQubitPhase.set_analysis_options
      ~SingleQubitPhase.set_experiment_options
      ~SingleQubitPhase.set_parent_file
      ~SingleQubitPhase.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~SingleQubitPhase.analysis
      ~SingleQubitPhase.analysis_options
      ~SingleQubitPhase.child_experiment
      ~SingleQubitPhase.experiment_options
      ~SingleQubitPhase.run_options
   
   