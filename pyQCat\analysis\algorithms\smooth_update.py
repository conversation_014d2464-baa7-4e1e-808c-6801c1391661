# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/06/12
# __author:       dp, <PERSON> Fang

"""
This codes from measure flow project.
SmoothLineUpdate optimize updating x, y, when iterates test x_scan, y_scan.

"""

import numpy as np
from scipy.interpolate import interp1d

from pyQCat.errors import AnalysisFieldError
from pyQCat.analysis.algorithms.smooth import smooth


class SmoothLineUpdate:
    """Iteratively traversing a smooth (unknown) curve.
    It is generally applied to iteratively update the detection point x,
    while updating the expected value y.
    """

    def __init__(
        self,
        initial_x_step: float,
        expect_y_step: float,
        min_x_step: float = 0.01,
        max_x_step: float = 1.0,
        x_sampling_interval: float = None,
        smooth_window_length: int = None,
        smooth_window: str = "hanning",
        interp_order: int = 3,
        update_x_flag: bool = False,
    ):
        """Initial object, set some parameter initial value.

        Args:
            initial_x_step (float): Initial x_step value.
            expect_y_step (float): The y iterate updating, expect y_step value.
            min_x_step (float): Set min x_step value.
            max_x_step (float): Set max x_step value.
            x_sampling_interval (float): If x need limit, set x sampling interval.
            smooth_window_length (int): If smooth, set window_length.
            smooth_window (str): When smooth, which window chose.
            interp_order (int): Interpolate data process, parameter `kind` value.

        """
        if update_x_flag is True:
            if min_x_step <= max_x_step and min_x_step <= initial_x_step <= max_x_step:
                pass
            else:
                raise AnalysisFieldError(
                    key="step(min, init, max)",
                    value=(min_x_step, initial_x_step, max_x_step),
                    msg="Please make sure min_x_step <= initial_x_step <= max_x_step!"
                )

        self._x = 0.0
        self._y = 0.0
        self.x_list = []
        self.y_list = []

        self._x_step = initial_x_step
        self._min_x_step = min_x_step
        self._max_x_step = max_x_step
        self._x_sampling_interval = x_sampling_interval

        self._y_step = 0.0
        self._expect_y_step = expect_y_step

        self._smooth_window_length = smooth_window_length
        self._smooth_window = smooth_window
        self._interp_order = interp_order

    @property
    def x(self) -> float:
        """Get current x value."""
        return self._x

    @x.setter
    def x(self, value: float):
        """Set x value."""
        self._x = value
        self.x_list.append(self._x)

    @property
    def y(self) -> float:
        """Get current y value."""
        return self._y

    @y.setter
    def y(self, value: float):
        """Set y value."""
        self._y = value
        self.y_list.append(self._y)

    @property
    def x_step(self) -> float:
        """Get current x_step value."""
        return self._x_step

    @x_step.setter
    def x_step(self, value: float):
        """Set x_step value."""
        self._x_step = value

    @property
    def y_step(self) -> float:
        """Get current y_step value."""
        return self._y_step

    @y_step.setter
    def y_step(self, value: float):
        """Set y_step value."""
        self._y_step = value

    def update_x_step(self, y_step: float) -> float:
        """Update x_step.
        Based on the y_step of the current actual measurement value,
        compare width expect_y_step, adjust updating x_step.
        """
        value = abs(y_step / self._expect_y_step)
        old_x_step = self.x_step
        new_x_step = old_x_step
        if value >= 2:
            new_x_step = old_x_step * 1 / 2
        elif 2 > value >= 3 / 2:
            new_x_step = old_x_step * 2 / 3
        elif 3 / 2 > value >= 1:
            new_x_step = old_x_step * 3 / 4
        elif 1 > value >= 2 / 3:
            new_x_step = old_x_step * 4 / 3
        elif 2 / 3 > value >= 1 / 2:
            new_x_step = old_x_step * 3 / 2
        elif 1 / 2 > value:
            new_x_step = old_x_step * 2

        if new_x_step < self._min_x_step:
            new_x_step = self._min_x_step
        elif new_x_step > self._max_x_step:
            new_x_step = self._max_x_step

        if self._x_sampling_interval is not None:
            x_ts = self._x_sampling_interval
            new_x_step = int(new_x_step / x_ts) * x_ts

        return new_x_step

    def update_x(self):
        """Update x."""

    def update_y(self, next_x: float) -> float:
        """Update y.
        Based on the specified next detection x,
        calculate the expected y value.
        """
        points = len(self.x_list)
        if self._smooth_window_length is not None:
            if points >= self._smooth_window_length:
                y_list = smooth(
                    np.array(self.y_list),
                    window_length=self._smooth_window_length,
                    window=self._smooth_window,
                )
            else:
                y_list = self.y_list
        else:
            y_list = self.y_list

        points_for_predict = self._interp_order + 1
        if points < points_for_predict:
            func = interp1d(self.x_list, y_list, kind=1, fill_value="extrapolate")
        else:
            new_x_list = self.x_list[-points_for_predict:]
            new_y_list = y_list[-points_for_predict:]
            func = interp1d(
                new_x_list,
                new_y_list,
                kind=self._interp_order,
                fill_value="extrapolate",
            )
        next_y = func([next_x])[0]

        # # todo, next_y should not be too far from the previous y value.
        # guessed_y_step = next_y - self.y
        # if abs(guessed_y_step / self._expect_y_step) >= 2:
        #     next_y = self.y + abs(self._expect_y_step) * (-1)**(guessed_y_step < 0)

        return next_y

    def update_xy(self, x: float, y: float):
        """Update x, y.
        Based on x_list and y_list map, and according to the current x and y,
        calculate the next x, y value.
        """
        self.x = x
        self.y = y
        points = len(self.x_list)
        if points == 1:
            next_x = self.x + self.x_step
            next_y = y
        else:
            self.y_step = self.y_list[-2] - self.y_list[-1]
            self.x_step = self.update_x_step(self.y_step)
            next_x = self.x + self.x_step
            next_y = self.update_y(next_x)

            # next_y should not be too far from the previous y value.
            guessed_y_step = next_y - self.y
            multiple_val = abs(guessed_y_step / self._expect_y_step)
            while multiple_val >= 2 and self.x_step > self._min_x_step:
                self.x_step = self.x_step / 2
                next_x = self.x + self.x_step
                next_y = self.update_y(next_x)
                guessed_y_step = next_y - self.y
                multiple_val = abs(guessed_y_step / self._expect_y_step)

        if self._x_sampling_interval is not None:
            x_ts = self._x_sampling_interval
            next_x = int(next_x / x_ts) * x_ts
            if points > 1:
                next_y = self.update_y(next_x)
        return next_x, next_y


if __name__ == "__main__":
    import matplotlib.pyplot as plt

    xlist = np.linspace(-100, 100, 501)
    # ylist = -0.001 * xlist ** 2 + 1
    ylist = (
        4 * np.sin(0.05 * xlist)
        + 10 * np.sin(0.1 * xlist)
        + 1 * np.sin(0.01 * xlist)
        + 0.00004 * xlist**3
    )
    inter_func1 = interp1d(xlist, ylist)

    def fake_exp(x, y_scan_list):
        # In real experiments, true_y is find by scaning y_scan_list and find the special value of y.
        # for the example used here, y_scan_list is not used
        true_y = inter_func1(x)
        return true_y

    def dynamic_scan_x_and_y(
        initial_y,
        initial_x,
        initial_x_step,
        expect_y_step,
        y_scan_range,
        y_scan_step,
        max_x_step=0.1,
        min_x_step=0.001,
        max_x=1,
        min_x=-1,
        max_y=8000,
        min_y=4000,
        max_iter=100,
        noise=3,
    ):
        next_x = initial_x
        x_list = []
        next_y = initial_y
        true_y_list = []
        guess_y_list = []
        exp_y_list = []
        iter_num = 0
        SLU = SmoothLineUpdate(
            initial_x_step=initial_x_step,
            expect_y_step=expect_y_step,
            max_x_step=max_x_step,
            min_x_step=min_x_step,
        )
        y_scan_list = np.arange(
            next_y - y_scan_range / 2,
            next_y + y_scan_range / 2 + y_scan_step,
            y_scan_step,
        )
        while (
            max_x >= next_x >= min_x
            and max_y >= np.max(y_scan_list)
            and min_y <= np.min(y_scan_list)
            and iter_num < max_iter
        ):
            x_list.append(next_x)
            guess_y_list.append(next_y)
            true_y = fake_exp(next_x, y_scan_list)
            true_y_list.append(true_y + noise)
            noise = np.random.rand() * noise
            exp_y = true_y + noise
            exp_y_list.append(exp_y)

            # guess next_x and next_y using SLU
            next_x, next_y = SLU.update_xy(next_x, exp_y)

            # update x and y_scan_list
            y_scan_list = np.arange(
                next_y - y_scan_range / 2,
                next_y + y_scan_range / 2 + y_scan_step,
                y_scan_step,
            )
            iter_num += 1
        return x_list, true_y_list, guess_y_list, exp_y_list

    expect_y_step = 2
    x_list, true_y_list, guess_y_list, exp_y_list = dynamic_scan_x_and_y(
        initial_y=-30,
        initial_x=-100,
        initial_x_step=0.5,
        expect_y_step=expect_y_step,
        y_scan_step=0.1,
        y_scan_range=10,
        max_x_step=10,
        min_x_step=0.1,
        max_x=100,
        min_x=-100,
        max_y=1000,
        min_y=-1000,
        max_iter=400,
        noise=10,
    )
    fig, ax = plt.subplots(3, 1)
    ax[0].plot(xlist, ylist, label="origin_data")
    ax[0].plot(x_list, exp_y_list, label="evaluated_data")
    ax[0].plot(x_list, guess_y_list, "*", label=f"guessed_data@y_step={expect_y_step}")
    ax[1].plot(x_list, "*", label=f"guessed_x@y_step={expect_y_step}")
    ax[2].plot(
        np.array(true_y_list[2:]) - np.array(guess_y_list[2:]),
        "*",
        label=f"guessed_y_error@y_step={expect_y_step}",
    )

    # expect_y_step = 0.8
    # x_list, true_y_list, guess_y_list, exp_y_list = dynamic_scan_x_and_y(
    #     initial_y=0.5,
    #     initial_x=-100,
    #     initial_x_step=0.5,
    #     expect_y_step=expect_y_step,
    #     y_scan_range=10,
    #     y_scan_step=0.1,
    #     max_x_step=2,
    #     min_x_step=0.01,
    #     max_x=100,
    #     min_x=-100,
    #     max_y=1000,
    #     min_y=-1000,
    #     max_iter=400,
    # )
    #
    # ax[0].plot(x_list, guess_y_list, "o", label=f"guessed_data,y_step={expect_y_step}")
    # ax[1].plot(x_list, "o", label=f"guessed_x,y_step={expect_y_step}")
    # ax[2].plot(
    #     np.array(true_y_list[2:]) - np.array(guess_y_list[2:]),
    #     "o",
    #     label=f"guessed_y_error,y_step={expect_y_step}",
    # )

    ax[0].legend()
    ax[1].legend()
    ax[2].legend()
    plt.show()
