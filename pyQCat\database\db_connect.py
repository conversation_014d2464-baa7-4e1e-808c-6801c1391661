# -*- coding: utf-8 -*-

import pymongo
from gridfs import GridFS

from pyQCat.structures import QDict, Singleton

"""
use PyMongo
"""


class DBPyMongo(metaclass=Singleton):
    def __init__(self, *args, **kwargs):
        self._db = QDict()
        self._client = QDict()
        self._exp_col = None
        self._meas_col = None
        self._grid_fs = None
        super().__init__(*args, **kwargs)

    @property
    def client(self):
        return self._client

    @client.setter
    def client(self, value):
        self._client = value
        self._db = self._client["measuresystem"]
        self._exp_col = self._db["Experiment"]
        self._meas_col = self._db["MeasureData"]
        self._grid_fs = GridFS(self._db)
        # self._set_monitor_time()

    def _set_monitor_time(self, timeout=60*60*24*2):
        """
        "Set the expiration time, in seconds."
        """
        # def del_fs_chunks(file_collection):
        #     # Find orphaned data blocks in fs.chunks
        #     orphaned_chunks = file_collection.aggregate([
        #         {
        #             "$lookup": {
        #                 "from": "fs.files",
        #                 "localField": "files_id",
        #                 "foreignField": "_id",
        #                 "as": "file_info"
        #             }
        #         },
        #         {"$match": {"file_info": {"$size": 0}}}
        #     ])
        #
        #     # "Delete these orphaned chunks data."
        #     for chunk in orphaned_chunks:
        #         chunk_id = chunk["_id"]
        #         file_collection.delete_one({"_id": chunk_id})

        def del_ttl_index(file_collection):
            existing_indexes = list(file_collection.list_indexes())
            ttl_index = None
            for index in existing_indexes:
                if index.get("expireAfterSeconds"):
                    ttl_index = index
                    break
            if ttl_index:
                current_expiry = ttl_index["expireAfterSeconds"]
                if current_expiry != timeout:
                    # "Delete the existing TTL index."
                    file_collection.drop_index(ttl_index["name"])
                    return True

        # 2024.12.12 BugFixed
        # If link mongodb server failed, using visage can not login.
        try:
            self._client.server_info()
        except Exception as err:
            return

        timestamp = "createdAt"
        if del_ttl_index(self._exp_col):
            self._exp_col.create_index(
                [(timestamp, pymongo.ASCENDING)], expireAfterSeconds=timeout
            )
        if del_ttl_index(self._meas_col):
            self._meas_col.create_index(
                [(timestamp, pymongo.ASCENDING)], expireAfterSeconds=timeout
            )
        files_collection = self._db["fs.files"]
        # del_fs_chunks(self._db["fs.chunks"])
        if del_ttl_index(files_collection):
            files_collection.create_index([("uploadDate", pymongo.ASCENDING)], expireAfterSeconds=timeout)

    @property
    def exp_col(self):
        return self._exp_col

    @property
    def meas_col(self):
        return self._meas_col

    @property
    def grid_fs(self):
        return self._grid_fs


DB = DBPyMongo()
