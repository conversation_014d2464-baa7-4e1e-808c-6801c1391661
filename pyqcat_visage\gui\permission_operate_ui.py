# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'permission_operate_ui.ui'
##
## Created by: Qt User Interface Compiler version 6.8.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QB<PERSON>, QColor, QC<PERSON>alGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QGridLayout, QGroupBox, QHBoxLayout,
    QLabel, QMainWindow, QPushButton, QSizePolicy,
    QSpacerItem, QStatusBar, QVBoxLayout, QWidget)

from .widgets.combox_custom.combox_search import SearchComboBox

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(960, 664)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.gridLayout = QGridLayout(self.centralwidget)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setContentsMargins(-1, 0, -1, 0)
        self.widget_main = QWidget(self.centralwidget)
        self.widget_main.setObjectName(u"widget_main")
        self.verticalLayout = QVBoxLayout(self.widget_main)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.widget = QWidget(self.widget_main)
        self.widget.setObjectName(u"widget")
        self.horizontalLayout_2 = QHBoxLayout(self.widget)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.targetName = QLabel(self.widget)
        self.targetName.setObjectName(u"targetName")

        self.horizontalLayout_2.addWidget(self.targetName)

        self.targetBox = SearchComboBox(self.widget)
        self.targetBox.setObjectName(u"targetBox")

        self.horizontalLayout_2.addWidget(self.targetBox)

        self.horizontalSpacer = QSpacerItem(268, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer)

        self.queryButton = QPushButton(self.widget)
        self.queryButton.setObjectName(u"queryButton")

        self.horizontalLayout_2.addWidget(self.queryButton)

        self.horizontalSpacer_2 = QSpacerItem(267, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_2)

        self.horizontalLayout_2.setStretch(0, 1)
        self.horizontalLayout_2.setStretch(1, 3)
        self.horizontalLayout_2.setStretch(2, 4)
        self.horizontalLayout_2.setStretch(3, 2)
        self.horizontalLayout_2.setStretch(4, 1)

        self.verticalLayout.addWidget(self.widget)

        self.groupBox_2 = QGroupBox(self.widget_main)
        self.groupBox_2.setObjectName(u"groupBox_2")
        self.horizontalLayout_3 = QHBoxLayout(self.groupBox_2)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.ListBox1 = QWidget(self.groupBox_2)
        self.ListBox1.setObjectName(u"ListBox1")

        self.horizontalLayout_3.addWidget(self.ListBox1)

        self.ListBox2 = QWidget(self.groupBox_2)
        self.ListBox2.setObjectName(u"ListBox2")

        self.horizontalLayout_3.addWidget(self.ListBox2)


        self.verticalLayout.addWidget(self.groupBox_2)

        self.widget_3 = QWidget(self.widget_main)
        self.widget_3.setObjectName(u"widget_3")
        self.horizontalLayout = QHBoxLayout(self.widget_3)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalSpacer_3 = QSpacerItem(314, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_3)

        self.saveButton = QPushButton(self.widget_3)
        self.saveButton.setObjectName(u"saveButton")

        self.horizontalLayout.addWidget(self.saveButton)

        self.horizontalSpacer_4 = QSpacerItem(314, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_4)


        self.verticalLayout.addWidget(self.widget_3)

        self.verticalLayout.setStretch(0, 1)
        self.verticalLayout.setStretch(1, 4)
        self.verticalLayout.setStretch(2, 1)

        self.gridLayout.addWidget(self.widget_main, 0, 0, 1, 1)

        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)
        self.queryButton.clicked.connect(MainWindow.query_info)
        self.saveButton.clicked.connect(MainWindow.save_info)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"Permission Manage", None))
        self.targetName.setText(QCoreApplication.translate("MainWindow", u"\u7ec4", None))
        self.queryButton.setText(QCoreApplication.translate("MainWindow", u"query", None))
        self.groupBox_2.setTitle(QCoreApplication.translate("MainWindow", u"Permission Manage", None))
        self.saveButton.setText(QCoreApplication.translate("MainWindow", u"Save", None))
    # retranslateUi

