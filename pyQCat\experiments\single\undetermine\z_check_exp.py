# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/04/10
# __author:       <PERSON><PERSON><PERSON>

from ....analysis import StandardCurveAnalysis
from ....pulse.pulse_function import Constant, half_pi_pulse
from ....tools import qarange
from ...top_experiment_v1 import Options, TopExperimentV1


class ZFluxCheck(TopExperimentV1):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("buffer_list", list)
        options.set_validator("width", float)
        options.set_validator("amp", float)
        options.set_validator("n", int)
        options.set_validator("t_wait", float)

        options.buffer_list = qarange(0, 10, 0.5)
        options.width = 25
        options.amp = 0.05
        options.n = 10
        options.t_wait = 10
        options.resource_filter = False

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.raw_data_format = "plot"
        options.x_label = "buffer (ns)"
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        return options

    def _check_options(self):
        super()._check_options()
        self.set_experiment_options(
            data_type="amp_phase" if self.discriminator is None else "I_Q"
        )
        self.set_run_options(
            x_data=self.experiment_options.buffer_list,
            analysis_class=StandardCurveAnalysis,
        )

    @staticmethod
    def set_xy_pulses(builder):
        qubit = builder.qubit

        width = builder.experiment_options.width
        buffer_list = builder.experiment_options.buffer_list
        n = builder.experiment_options.n
        t_wait = builder.experiment_options.t_wait
        max_buffer = max(buffer_list)

        mid_delay = (max_buffer * 2 + width) * n + t_wait * 2

        xy_pulse = (
            half_pi_pulse(qubit)()
            + Constant(mid_delay, 0, "XY")()
            + half_pi_pulse(qubit)()
        )

        builder.play_pulse("XY", qubit, xy_pulse)

    @staticmethod
    def set_z_pulses(builder):
        qubit = builder.qubit
        x2_width = half_pi_pulse(qubit).width

        width = builder.experiment_options.width
        buffer_list = builder.experiment_options.buffer_list
        n = builder.experiment_options.n
        t_wait = builder.experiment_options.t_wait
        amp = builder.experiment_options.amp
        mid_delay = (max(buffer_list) * 2 + width) * n + t_wait * 2

        z_pulse_list = []
        for buffer in buffer_list:
            compensate_delay = mid_delay - (buffer * 2 + width) * n - t_wait * 2
            ld = round(compensate_delay / 2, 4)
            rd = compensate_delay - ld
            z_pulse = Constant(x2_width + t_wait + ld, 0)()
            for i in range(n):
                z_pulse += Constant(buffer, 0)()
                z_pulse += Constant(width, amp)()
                z_pulse += Constant(buffer, 0)()
            z_pulse += Constant(x2_width + t_wait + rd, 0)()
            z_pulse_list.append(z_pulse)

        builder.play_pulse("Z", qubit, z_pulse_list)
