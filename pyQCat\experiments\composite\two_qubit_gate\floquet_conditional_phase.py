# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/01
# __author:       <PERSON> Fang

"""
FloquetConditionalPhaseFixed child is FloquetCalibrationCZphase.
"""

from ....analysis.library_v2 import FloquetConditionalPhaseAnalysis
from ....analysis.specification import AnalysisResult
from ....log import pyqlog
from ....structures import Options
from ....tools.calculator import qubit_pair_detune_prepare
from ...composite.two_qubit_gate.conditional_phase import ConditionalPhaseFixed
from ...single.two_qubit_gate.floquet_cali_cz_phase import FloquetCalibrationCZphase


class FloquetConditionalPhaseFixed(ConditionalPhaseFixed):
    """Floquet ConditionalPhaseFixed Composite experiment."""

    _sub_experiment_class = FloquetCalibrationCZphase

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.child_exp_options = (
            cls._sub_experiment_class._default_experiment_options()
        )

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Define analysis options."""
        options = super()._default_analysis_options()
        options.child_ana_options = (
            cls._sub_experiment_class._default_analysis_options()
        )

        options.set_validator("fit_points", int)

        options.data_key = ["phase"]
        options.fit_points = 10
        options.x_label = "Delta detune (MHz)"

        return options

    def _check_options(self):
        """Check option."""
        super()._check_options()

        z_amp_list = self.experiment_options.z_amp_list
        if z_amp_list is not None and len(z_amp_list) > 0:
            x_data = z_amp_list
        else:
            x_data = []
            # adjust old version, will repeat two times.
            for idx, x in enumerate(self.run_options.x_data):
                if idx % 2 == 0:
                    x_data.append(x)

        self.set_analysis_options(result_name=self.qubit_pair.name)
        self.set_run_options(
            x_data=x_data,
            analysis_class=FloquetConditionalPhaseAnalysis,
        )

    def _setup_child_experiment(self, exp, index: int, value: float):
        """Set child_experiment some options."""
        exp.run_options.index = index
        total = len(self.run_options.x_data)

        for unit, params in self.run_options.scan_map.items():
            exp.qubit_pair.metadata.std.cz["params"][unit]["amp"] = params["amp"][index]
            exp.qubit_pair.metadata.std.cz["params"][unit]["freq"] = 0

        scan_name = self.experiment_options.scan_name
        if scan_name:
            describe = f"{scan_name} z_amp={value}"
        else:
            describe = f"z_amp={value}"

        exp.set_parent_file(self, describe, index, total)
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp):
        """Collect child experiment result and provide it for parent."""
        phase = exp.analysis.results.phase.value
        exp.analysis.provide_for_parent.update({"phase": phase})

    def _create_composite_experiment_data(self, x_data):
        """Create ExperimentData."""
        exp_data = super(ConditionalPhaseFixed, self)._create_composite_experiment_data(
            x_data
        )
        return exp_data

    def _alone_save_result(self):
        """Alone save some special result."""
        unit_list = []
        amps_list = []
        for unit, params in self.run_options.scan_map.items():
            unit_list.append(unit)
            amps_list.append(params["amp"])
        f_name = f"{self}({''.join(unit_list)}_amp)"
        self.file.save_data(*amps_list, name=f_name)

    def _set_result_path(self):
        """Set path to save parameter of QubitPair."""
        eop = self.experiment_options

        for key in list(self.analysis.results.keys()):
            result = self.analysis.results.get(key)
            if result.value:
                if key == "ac_scan":
                    if self.run_options.use_detune:
                        goal_detune = result.value
                        scope = {
                            "l": abs(self.run_options.detune_point[0]),
                            "r": abs(self.run_options.detune_point[-1]),
                            "p": 30,
                        }
                        freq_map = qubit_pair_detune_prepare(
                            self.qubit_pair, self.qubits, goal_detune=goal_detune, **scope
                        )
                        for unit, params in freq_map.items():
                            self.analysis.results[unit] = AnalysisResult(
                                name=unit,
                                value=round(params[0], 3),
                                extra={
                                    "name": self.qubit_pair.name,
                                    "path": f"QubitPair.metadata.std.cz.params.{unit}.freq",
                                },
                            )
                    else:
                        des = "amp"
                        if result.value > 1:
                            result.value = round(result.value, 3)
                            des = "freq"
                        result.extra[
                            "path"
                        ] = f"QubitPair.metadata.std.cz.params.{eop.scan_name}.{des}"
                elif key == "ac_adapter":
                    des = "amp"
                    if result.value > 1:
                        result.value = round(result.value, 3)
                        des = "freq"
                    result.extra[
                        "path"
                    ] = f"QubitPair.metadata.std.cz.params.{eop.adapter_name}.{des}"
