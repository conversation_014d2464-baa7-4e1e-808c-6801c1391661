# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/fit/pyQCat.analysis.fit.natural_exp.rst:2
msgid "pyQCat.analysis.fit.natural\\_exp"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.natural_exp:1
msgid "natural exp fit formula."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.natural_exp:3
msgid ""
"y = amp \\cdot  e^{-\\frac{x}{tau} } + baseline\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.natural_exp
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.natural_exp:7
msgid ":py:class:`~numpy.ndarray`"
msgstr ""

#~ msgid "T1 fit formula."
#~ msgstr ""

#~ msgid "Parameters"
#~ msgstr ""

#~ msgid "independent variable"
#~ msgstr ""

#~ msgid "fitted parameter"
#~ msgstr ""

#~ msgid "fitted parameter, target args"
#~ msgstr ""

#~ msgid "Returns"
#~ msgstr ""

#~ msgid "strain variable"
#~ msgstr ""

