﻿pyQCat.pulse.VarFreqEnvelop
===========================

.. currentmodule:: pyQCat.pulse

.. autoclass:: VarFreqEnvelop

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~VarFreqEnvelop.__init__
      ~VarFreqEnvelop.correct_ac_crosstalk
      ~VarFreqEnvelop.correct_compensate
      ~VarFreqEnvelop.correct_delay
      ~VarFreqEnvelop.correct_distortion
      ~VarFreqEnvelop.correct_pulse
      ~VarFreqEnvelop.get_pulse
      ~VarFreqEnvelop.get_raw_pulse
      ~VarFreqEnvelop.plot
      ~VarFreqEnvelop.validate_parameters
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~VarFreqEnvelop.attach
      ~VarFreqEnvelop.band_width
      ~VarFreqEnvelop.bit
      ~VarFreqEnvelop.delay
      ~VarFreqEnvelop.envelop
      ~VarFreqEnvelop.id
      ~VarFreqEnvelop.parameters
      ~VarFreqEnvelop.pulse
      ~VarFreqEnvelop.raw_pulse
      ~VarFreqEnvelop.sweep
      ~VarFreqEnvelop.type
      ~VarFreqEnvelop.width
   
   