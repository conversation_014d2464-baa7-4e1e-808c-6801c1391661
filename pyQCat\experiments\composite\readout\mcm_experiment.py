# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/26
# __author:       <PERSON><PERSON><PERSON>


from ....analysis.library.mcm_analysis import (
    McmQubitDePhaseComAnalysis,
    SingleShotExtendCompositeAnalysis,
    StandardCurveAnalysis,
    SingleShotExtendVsSampleWidthAnalysis
)
from ....structures import Options
from ...composite_experiment import CompositeExperiment
from ...single.readout.mcm_experiment import (
    McmQubitDePhase,
    McmSpectator,
    McmSpectatorDePhase,
    SingleShotExtend,
    SingleShotSnr
)
from ....tools import qarange


class McmQubitDePhaseComposite(CompositeExperiment):
    _sub_experiment_class = McmQubitDePhase

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("scale_coef_list", list)
        options.scale_coef_list = [1, 0.95, 0.9]
        options.run_mode = "async"
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.plot_2d = True
        return options

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.scale_coef_list,
            analysis_class=McmQubitDePhaseComAnalysis,
        )

    def _setup_child_experiment(self, child_exp, index: int, coef: float):
        child_exp.run_options.index = index
        total = len(self.run_options.x_data)
        describe = f"scale_coef={coef}"
        child_exp.set_parent_file(self, describe, index, total)
        child_exp.set_experiment_options(scale_coef=coef)
        self._check_simulator_data(child_exp, index)

    def _handle_child_result(self, child_exp):
        child_exp.analysis.provide_for_parent.update(
            {"rho": child_exp.analysis.results.rho.value}
        )


class McmSpectatorComposite(CompositeExperiment):
    _sub_experiment_class = McmSpectator

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("trigger_delay_list", list)
        options.trigger_delay_list = [0, 0, 0]
        return options

    def _check_options(self):
        super()._check_options()
        trigger_delay_list = self.experiment_options.trigger_delay_list
        self.set_run_options(
            x_data=list(range(len(trigger_delay_list))),
            analysis_class=StandardCurveAnalysis,
        )

    def _setup_child_experiment(self, child_exp: McmSpectator, index: int, *args):
        trigger_delay_list = self.experiment_options.trigger_delay_list
        child_exp.run_options.index = index
        total = len(self.run_options.x_data)
        cur_value = trigger_delay_list[: index + 1]
        describe = f"MD-{cur_value}"
        child_exp.set_parent_file(self, describe, index, total)
        child_exp.set_experiment_options(trigger_delay_list=cur_value)
        self._check_simulator_data(child_exp, index)

    def _handle_child_result(self, child_exp: McmSpectator):
        child_exp.analysis.provide_for_parent.update(
            {
                "P0": child_exp.analysis.results.P0.value,
                "P1": child_exp.analysis.results.P1.value,
            }
        )


class McmSpectatorDePhaseComposite(McmQubitDePhaseComposite):
    _sub_experiment_class = McmSpectatorDePhase


class SingleShotExtendComposite(CompositeExperiment):
    _sub_experiment_class = SingleShotExtend

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("scale_coef_list", list)
        options.scale_coef_list = [1, 0.95, 0.9]
        return options

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.scale_coef_list,
            analysis_class=SingleShotExtendCompositeAnalysis,
        )

    def _setup_child_experiment(self, child_exp, index: int, coef: float):
        child_exp.run_options.index = index
        total = len(self.run_options.x_data)
        describe = f"scale_coef={coef}"
        child_exp.set_parent_file(self, describe, index, total)
        child_exp.set_experiment_options(scale_coef=coef)
        self._check_simulator_data(child_exp, index)

    def _handle_child_result(self, child_exp):
        child_exp.analysis.provide_for_parent.update(
            {"snr": child_exp.analysis.results.snr.value}
        )


class SingleShotExtendVsSampleWidth(CompositeExperiment):
    _sub_experiment_class = SingleShotSnr

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("sample_width_list", list)
        options.sample_width_list = qarange(500, 1000, 100)
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("chi", float)
        options.set_validator("n_star", float)
        options.set_validator("kappa", float)
        options.chi = 0.1
        options.n_star = 0.1
        options.kappa = 0.1
        return options

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.sample_width_list,
            analysis_class=SingleShotExtendVsSampleWidthAnalysis,
        )

    def _setup_child_experiment(self, child_exp, index: int, sample_width: float):
        child_exp.run_options.index = index
        total = len(self.run_options.x_data)
        describe = f"sample_width={sample_width}"
        child_exp.set_parent_file(self, describe, index, total)
        child_exp.set_experiment_options(sample_width=sample_width)
        self._check_simulator_data(child_exp, index)

    def _handle_child_result(self, child_exp):
        print(child_exp.analysis.results)
        child_exp.analysis.provide_for_parent.update(
            {"snr": child_exp.analysis.results.snr.value**2}
        )
