# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/05
# __author:       <PERSON> <PERSON>

"""
Distortion T1 composite experiment.

"""

from copy import deepcopy
from datetime import datetime

import numpy as np
import pandas as pd

from ....analysis.library import DistortionT1CompositeAnalysis
from ....errors import ExperimentFlowError
from ....log import pyqlog
from ....parameters import options_wrapper
from ....qaio_property import QAIO
from ....structures import ExperimentData, MetaData, Options
from ....tools.utilities import get_xy_step, qarange
from ....types import ExperimentRunMode, StandardContext, QualityDescribe
from ...composite_experiment import CompositeExperiment
from ...single import (
    CouplerDistortionT1,
    CouplerDistortionZZ,
    DistortionT1,
)


class DistortionT1Composite(CompositeExperiment):
    """Distortion node."""

    _sub_experiment_class = DistortionT1

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            iteration_times (int): Set experiment iteration times.
            xy_delay_start (float): XY line delay start value.
            xy_delay_max (float): XY line max align delay value,
                                  recommend less than tb.
            xy_step_map (dict): Update xy_delay step map.

            bit_type(str): bit type, normal `Qubit` or `Coupler`.
            base_history (bool): Base on history distortion data calibrate.

            update_z_offset_range (bool): Is or not update z_offset_list.
            scan_points (int): Scan point number.

            z_amp (float): The const z_amp of Z line.
            z_offset_list (List, np.ndarray): Scan Z offset range.

        """
        options = super()._default_experiment_options()

        options.set_validator("iteration_times", (1, 10, 0))
        options.set_validator("xy_delay_start", float)
        options.set_validator("xy_delay_max", float)
        options.set_validator("xy_step_map", dict)

        options.set_validator("bit_type", ["Qubit", "Coupler"])
        options.set_validator("base_history", bool)
        options.set_validator("update_z_offset_range", bool)
        options.set_validator("scan_points", int)

        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("z_offset_list", list)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        # distortion t1 composite experiment args
        options.iteration_times = 1
        options.xy_delay_start = 0.0
        options.xy_delay_max = 10000
        options.xy_step_map = {
            "lt_10": 0.625,
            "lt_50": 1.25,
            "lt_100": 5.0,
            "lt_200": 10.0,
            "lt_500": 20.0,
            "lt_1000": 50.0,
            "lt_5000": 100.0,
            "lt_10000": 200.0,
            "gt_10000": 300.0,
            "gt_20000": 400.0,
        }

        options.bit_type = "Qubit"
        options.base_history = False

        # adjust scan points
        options.update_z_offset_range = False
        options.scan_points = 70

        # distortion t1 once experiment args
        options.z_amp = None
        options.z_offset_list = qarange(-0.02, 0.02, 0.0005)

        options.run_mode = ExperimentRunMode.sync_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for DistortionT1Composite experiment.

        Options:
            iteration_time (int): Iteration time number.
            dt_list (List[np.ndarray]):
            so_list (List[np.ndarray]):

        """
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("lfilter_flag", bool)
        options.set_validator("ylim", list)

        options.z_amp = -0.5
        options.dac_sample_rate = 3.2
        options.sample_rate = 1.2
        options.lfilter_flag = False
        options.iteration_time = 0
        options.cal_response_mode = "add"

        options.dt_list = []
        options.so_list = []

        options.quality_bounds = [0.9999, 0.999, 0.9]
        options.data_key = ["Response"]
        options.sub_key = "P1"
        options.ylim = [0.95, 1.02]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method.
        Statistics and saving parameters of running process.

        Options:

            distortion_width (float): Calibrate z line distortion width.
            distortion_ab (List): IIR filter parameters. [ a, b ]
            delay_arr (np.ndarray): Distortion data delay array.
            response_arr (np.ndarray): Distortion data response array.

        """
        options = super()._default_run_options()
        options.start_offset = None
        options.end_offset = None
        options.data_decimal = None
        options.z_step = None

        options.cal_response_mode = ""

        options.dt_list = []
        options.so_list = []

        options.xy_delay_list = []
        options.offset_list = []
        options.normal_offset_list = []
        options.response_list = []

        # iteration update qubit_test, update compensate
        options.distortion_width = None
        options.distortion_ab = None
        options.delay_arr = None
        options.response_arr = None

        # adjust, when `new_case` only once modify child_exp readout point.
        options.child_rdz_amp = None
        options.child_ac_bias = {}

        options.support_context = [
            StandardContext.QC,
            StandardContext.CPC,
        ]

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "Zamp": self.experiment_options.z_amp,
            "cal_response_mode": self.analysis_options.cal_response_mode,
        }
        return metadata

    def _patch_z_amp(self, t_name: str):
        """According to work point value set z_amp."""
        run_mode = self.child_experiment.experiment_options.run_mode
        if self.experiment_options.z_amp is None:
            if self.ac_bias.get(t_name):
                w_point = self.ac_bias[t_name][1]
            elif self.working_dc.get(t_name):
                w_point = self.working_dc[t_name][1]
            else:
                qc_map = {}
                qubit_map = {qubit.name: qubit for qubit in self.qubits}
                coupler_map = {qubit.name: qubit for qubit in self.couplers}
                qc_map.update(qubit_map)
                qc_map.update(coupler_map)
                qc_obj = qc_map.get(t_name)
                if qc_obj:
                    w_point = qc_obj.dc_max + qc_obj.idle_point
                else:
                    raise ValueError(f"Set {t_name} error: not in exist environment!")
            if w_point < 0:
                z_amp = round(0.45 - w_point, 3)
            else:
                z_amp = round(-0.45 - w_point, 3)

            if run_mode == "normal":
                z_amp = z_amp
            elif run_mode == "new_case":
                z_amp = -z_amp

            self.experiment_options.z_amp = z_amp
            pyqlog.info(
                f"Patch {t_name} working_point: {w_point}, run_mode: {run_mode}, z_amp: {z_amp}"
            )
        pyqlog.info(f"{t_name} Set z_amp: {self.experiment_options.z_amp}")

    def _check_options(self):
        """Check options."""
        super()._check_options()

        bit_type = self.experiment_options.bit_type
        z_offset_list = self.experiment_options.z_offset_list

        if bit_type.capitalize() == "Qubit":
            sub_key = "P1"
        elif bit_type.capitalize() == "Coupler":
            sub_key = "P0"
        else:
            pyqlog.warning(
                f"Set bit_type not `Qubit` or `Coupler`! " f"bit_type: {bit_type}"
            )
            sub_key = "P1"

        if self.child_experiment.is_coupler_exp is True:
            result_name = self.child_experiment.coupler.name

            # bugfix: coupler distortion need use coupler compensate
            # x_delay to drive qubit x_delay
            drive_q_compensate = None
            coupler_compensate = None
            for bit, compensate in self.compensates.items():
                if bit.name == self.child_experiment.coupler.name:
                    coupler_compensate = compensate

                if bit.name == self.child_experiment.driveQ.name:
                    drive_q_compensate = compensate

            drive_q_compensate.x_delay = coupler_compensate.x_delay

        elif self.experiment_options.bit_type == "Coupler":
            result_name = self.child_experiment.coupler.name
        else:
            result_name = self.child_experiment.qubit.name

        dac_sample_rate = QAIO.dac_sample_rate
        sample_rate = QAIO.awg_sample_rate
        pyqlog.info(
            f"AIO XY line sample rate: {dac_sample_rate} GHz, "
            f"Z line sample rate: {sample_rate} GHz."
        )

        start_offset = z_offset_list[0]
        end_offset = z_offset_list[-1]
        data_decimal = max([str(i)[::-1].find(".") for i in z_offset_list])
        z_step = round(
            abs(z_offset_list[1] - z_offset_list[0]),
            data_decimal,
        )

        self._patch_z_amp(result_name)

        self.set_analysis_options(
            result_name=result_name,
            dac_sample_rate=dac_sample_rate,
            sample_rate=sample_rate,
            sub_key=sub_key,
        )

        self.set_run_options(
            start_offset=start_offset,
            end_offset=end_offset,
            data_decimal=data_decimal,
            z_step=z_step,
        )

    def _save_curve_analysis_plot(self, save_mark: str = None):
        if save_mark is None:
            save_mark = datetime.now().strftime("%Y-%m-%d %H.%M.%S")
        super()._save_curve_analysis_plot(save_mark=save_mark)

    def _adjust_xy_delay(self, xy_delay: float):
        """By xy_delay and xy_step_map calculate new xy_delay."""
        xy_step_map = self.experiment_options.xy_step_map
        xy_step = get_xy_step(xy_delay, xy_step_map)
        new_xy_delay = xy_delay + xy_step
        return new_xy_delay

    def _adjust_z_offset_list(self, offset: float, xy_delay: float):
        """Adjust z_offset_list."""
        init_z_offset_list = self.experiment_options.z_offset_list
        scan_points = self.experiment_options.scan_points

        offset_list = self.run_options.offset_list
        start_offset = self.run_options.start_offset
        end_offset = self.run_options.end_offset
        data_decimal = self.run_options.data_decimal
        z_step = self.run_options.z_step

        new_offset = init_z_offset_list[
            np.argmin(np.abs(offset - np.array(init_z_offset_list)))
        ]
        half_points = int(scan_points / 2)
        z_offset_start = round(new_offset - half_points * z_step, data_decimal)
        z_offset_end = round(z_offset_start + z_step * scan_points, data_decimal)

        if z_offset_start < start_offset:
            z_offset_start = start_offset
            z_offset_end = round(z_offset_start + z_step * scan_points, data_decimal)
        elif z_offset_end > end_offset:
            z_offset_end = end_offset
            z_offset_start = round(z_offset_end - z_step * scan_points, data_decimal)

        z_offset_list = qarange(z_offset_start, z_offset_end, z_step)
        return z_offset_list

    async def _sync_composite_run(self):
        """Distortion T1 Composite Run Logic."""
        # super().run()

        bit_type = self.experiment_options.bit_type
        base_history = self.experiment_options.base_history
        z_amp = self.experiment_options.z_amp
        init_z_offset_list = self.experiment_options.z_offset_list
        result_name = self.analysis_options.result_name
        sub_key = self.analysis_options.sub_key
        provide_field = self.analysis_options.data_key[0]

        cal_response_mode = self.run_options.cal_response_mode
        child_rdz_amp = self.run_options.child_rdz_amp
        child_ac_bias = self.run_options.child_ac_bias

        init_p1_value_arr = np.zeros_like(init_z_offset_list)
        exp_index = 0
        for i in range(self.experiment_options.iteration_times):
            pyqlog.info(f"DistortionT1Composite iteration_time: {i}")
            dist_t1_exp = deepcopy(self.child_experiment)

            if bit_type.capitalize() == "Qubit":
                q_name = dist_t1_exp.qubit.name
            elif bit_type.capitalize() == "Coupler":
                q_name = dist_t1_exp.coupler.name
            else:
                q_name = "unknown"

            if i == 0 and base_history is False:
                # the first iteration qubit_test, reset compensate
                for compensate in dist_t1_exp.compensates.values():
                    if compensate.name == q_name:
                        compensate.z_distortion_width = 0
                        compensate.z_distortion_ab = []
                        compensate.z_distortion_sos = {}

            elif i == 0 and base_history is True:
                # the first iteration simulator
                # use history distortion calibrate
                for compensate in dist_t1_exp.compensates.values():
                    if compensate.name == q_name:
                        his_dt_arr = np.array(compensate.z_distortion_tlist)
                        his_so_arr = np.array(compensate.z_distortion_solist)
                        if his_dt_arr.tolist() and his_so_arr.tolist():
                            self.run_options.dt_list.append(his_dt_arr)
                            self.run_options.so_list.append(his_so_arr)
            else:
                # the other iteration qubit_test, update compensate
                distortion_width = self.run_options.distortion_width
                distortion_ab = self.run_options.distortion_ab
                delay_arr = self.run_options.delay_arr
                response_arr = self.run_options.response_arr

                for compensate in dist_t1_exp.compensates.values():
                    if compensate.name == q_name:
                        compensate.z_distortion_width = distortion_width
                        compensate.z_distortion_ab = distortion_ab
                        compensate.z_distortion_tlist = delay_arr
                        compensate.z_distortion_solist = response_arr

            self._analysis = None
            self._experiments = []
            self._minimize_experiments = []

            self.run_options.xy_delay_list = []
            self.run_options.offset_list = []
            self.run_options.normal_offset_list = []
            self.run_options.response_list = []

            self.run_options.distortion_width = None
            self.run_options.distortion_ab = None
            self.run_options.delay_arr = None
            self.run_options.response_arr = None

            xy_delay = self.experiment_options.xy_delay_start
            error_count = 0
            z_offset_list = init_z_offset_list
            p0_history = None
            while xy_delay < self.experiment_options.xy_delay_max:
                new_dist_t1_exp = deepcopy(dist_t1_exp)
                new_dist_t1_exp.set_parent_file(
                    self, description=f"iter{i}_xy_delay={xy_delay}"
                )
                new_dist_t1_exp.set_experiment_options(
                    z_amp=z_amp,
                    z_offset_list=z_offset_list,
                    xy_delay=xy_delay,
                )
                new_dist_t1_exp.set_analysis_options(
                    data_key=[sub_key],
                    p0_history=p0_history,
                )
                new_dist_t1_exp.set_run_options(
                    rdz_amp=child_rdz_amp,
                    ac_bias=child_ac_bias,
                )
                self._check_simulator_data(new_dist_t1_exp, exp_index)
                exp_index += 1

                # new_dist_t1_exp.run()
                # new_dist_t1_exp.clear_params()
                await new_dist_t1_exp.run_experiment()

                if child_rdz_amp is None and new_dist_t1_exp.run_options.rdz_amp:
                    child_rdz_amp = new_dist_t1_exp.run_options.rdz_amp
                    self.run_options.child_rdz_amp = child_rdz_amp
                    pyqlog.info(
                        f"Note {result_name} child experiment readout amp: {child_rdz_amp}"
                    )
                if not child_ac_bias and new_dist_t1_exp.run_options.ac_bias:
                    child_ac_bias = new_dist_t1_exp.run_options.ac_bias
                    self.run_options.child_ac_bias = child_ac_bias
                    pyqlog.info(
                        f"Note {result_name} child experiment update ac: {child_ac_bias}"
                    )

                model_name = new_dist_t1_exp.analysis_options.fit_model_name
                offset = new_dist_t1_exp.analysis.results.t_offset.value
                try:
                    fit_data = new_dist_t1_exp.analysis.analysis_datas[sub_key].fit_data
                    if fit_data is not None:
                        p0_history = dict(zip(fit_data.popt_keys, fit_data.popt))
                except Exception as err:
                    pyqlog.warning(f"Calculate p0_history error: {err}")

                pyqlog.debug(f"Sub Experiment Analysis fit_model: {model_name}")
                pyqlog.info(
                    f"{result_name} Run iteration time {i} xy_delay={xy_delay} ns, "
                    f"fit offset: {offset}"
                )

                if z_offset_list[0] <= offset <= z_offset_list[-1]:
                    error_count = 0  # reset error_count

                    if z_amp == 0:
                        normal_offset = offset
                    else:
                        normal_offset = offset / z_amp

                    if not cal_response_mode:
                        run_mode = new_dist_t1_exp.experiment_options.run_mode
                        if run_mode == "normal":
                            cal_response_mode = "add"
                        elif run_mode == "new_case":
                            cal_response_mode = "reduce"
                        self.set_run_options(cal_response_mode=cal_response_mode)

                    if cal_response_mode == "add":
                        response = 1 + normal_offset
                    elif cal_response_mode == "reduce":
                        response = 1 - normal_offset
                    else:
                        # now just support "add" or "reduce"
                        response = 1 + normal_offset

                    new_dist_t1_exp.analysis.provide_for_parent.update(
                        {provide_field: response}
                    )

                    self.run_options.xy_delay_list.append(xy_delay)
                    self.run_options.offset_list.append(offset)
                    self.run_options.normal_offset_list.append(normal_offset)
                    self.run_options.response_list.append(response)

                    experiment_data = new_dist_t1_exp.analysis.experiment_data
                    p_arr = experiment_data.y_data.get(sub_key)
                    x_arr = experiment_data.x_data
                    p_series = pd.Series(
                        data=init_p1_value_arr, index=init_z_offset_list
                    )
                    # p_series[z_offset_list] = p_arr
                    p_series[x_arr] = p_arr
                    adjust_p_arr = np.array(p_series.values.tolist())

                    exp_data = ExperimentData(
                        x_data=init_z_offset_list,
                        y_data={sub_key: adjust_p_arr},
                        experiment_id=new_dist_t1_exp.id,
                        metadata=new_dist_t1_exp._metadata(),
                    )

                    new_dist_t1_exp.analysis._experiment_data = exp_data
                    # self._experiments.append(new_dist_t1_exp)
                    if self.experiment_options.minimize_mode is True:
                        self._minimize_experiments.append(new_dist_t1_exp.analysis.analysis_program())
                    else:
                        self._experiments.append(new_dist_t1_exp)

                    # adjust xy_delay, z_offset_list
                    xy_delay = self._adjust_xy_delay(xy_delay)
                    if self.experiment_options.update_z_offset_range is True:
                        z_offset_list = self._adjust_z_offset_list(offset, xy_delay)

                    pyqlog.info(
                        f"{result_name} Run iteration time {i}, "
                        f"next xy_delay={xy_delay} ns,"
                        f"next z_offset_list: [{z_offset_list[0]},{z_offset_list[-1]}], "
                        f"scan points: {len(z_offset_list)}"
                    )
                else:
                    err_msg = (
                        f"Error_count {error_count}, "
                        f"fit offset {offset}, "
                        f"out [{z_offset_list[0]},{z_offset_list[-1]}] range!"
                    )
                    if error_count < 3:
                        pyqlog.warning(err_msg)
                        error_count += 1
                        z_offset_list = init_z_offset_list
                    else:
                        raise ExperimentFlowError(self, err_msg)

            self.run_options.dt_list.append(np.array(self.run_options.xy_delay_list))
            self.run_options.so_list.append(np.array(self.run_options.response_list))
            self.file.save_data(
                np.array(self.run_options.xy_delay_list),
                np.array(self.run_options.offset_list),
                np.array(self.run_options.normal_offset_list),
                np.array(self.run_options.response_list),
                name=f"{q_name}_iteration_{i}_run_options",
            )

            self.file.save_data(
                np.hstack(tuple(self.run_options.dt_list)),
                np.hstack(tuple(self.run_options.so_list)),
                name=f"{q_name}_iteration_{i}_dt_so_list",
            )

            self.set_analysis_options(
                z_amp=self.experiment_options.z_amp,
                cal_response_mode=self.run_options.cal_response_mode,
                iteration_time=i,
                dt_list=self.run_options.dt_list,
                so_list=self.run_options.so_list,
            )

            self._run_analysis(
                x_data=np.array(self.run_options.xy_delay_list),
                analysis_class=DistortionT1CompositeAnalysis,
            )

            distortion_width = self.analysis.results.distortion_width.value
            distortion_ab = self.analysis.results.distortion_ab.value
            delay_arr = self.analysis.results.delay_arr.value
            response_arr = self.analysis.results.response_arr.value

            self.file.save_data(
                np.array(distortion_ab),
                name=f"{q_name}_iteration_{i}_distortion_ab",
                fmt="%.16f",
            )
            self.file.save_data(
                delay_arr, response_arr, name=f"{q_name}_iteration_{i}_delay_response"
            )

            self.run_options.distortion_width = distortion_width
            self.run_options.distortion_ab = distortion_ab
            self.run_options.delay_arr = delay_arr
            self.run_options.response_arr = response_arr

            lfilter_flag = self.analysis.options.lfilter_flag
            quality_str = self.analysis.quality.descriptor
            limit_flag = self.analysis.results.limit_flag.value

            pyqlog.info(
                f"{result_name} Distortion Composite iteration {i} end, "
                f"use pole model flag: {lfilter_flag}, "
                f"quality: {quality_str},"
                f"result limit_flag: {limit_flag}"
            )

            if quality_str not in [QualityDescribe.perfect, QualityDescribe.normal] and i == 0:
                self.set_analysis_options(lfilter_flag=False)
                self.run_options.distortion_ab = []
                pyqlog.info(
                    f"{result_name} Pole model scheme is not effective, "
                    f"switch to deconvolution scheme!"
                )

            if limit_flag is True:
                pyqlog.info(f"{result_name} Distortion calibrate pass.")
                break


class CouplerDistortionT1Composite(DistortionT1Composite):
    """CouplerDistortionT1 Distortion class."""

    _sub_experiment_class = CouplerDistortionT1


class CouplerDistortionZZComposite(DistortionT1Composite):
    """CouplerDistortionZZ Distortion class."""

    _sub_experiment_class = CouplerDistortionZZ

    def _check_options(self):
        """Check options."""
        super()._check_options()
        sub_key = "P1"
        self.set_analysis_options(sub_key=sub_key)
