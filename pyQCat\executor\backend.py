# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/09/15
# __author:       <PERSON><PERSON><PERSON>

from pyQCat import DEVICE, __version__
from pyQCat.concurrent.data_client import set_transfer_config
from pyQCat.config import PyqcatConfig
from pyQCat.errors import CourierError
from pyQCat.executor.context_manager import MonsterContextManager
from pyQCat.invoker import DataCenter, Invoker
from pyQCat.log import pyqlog
from pyQCat.processor.chip_data import ChipConfigField
from pyQCat.structures import CommonDict, List, Optional, QDict, Union
from pyQCat.tools.record_structure import SystemMeta


class Backend:
    def __init__(
        self,
        username: str,
        config_file: Union[str, CommonDict],
        cache_flag: int = 0,
    ):
        self._username = username
        self._cache_flag = cache_flag
        self._db = None
        self._config = QDict(
            **(
                PyqcatConfig(filename=config_file).to_dict()
                if isinstance(config_file, str)
                else config_file
            )
        )
        self._context_manager = MonsterContextManager(self._config)
        self._set_env()

        self.system = QDict()

    @property
    def context_manager(self):
        return self._context_manager

    @property
    def chip_data(self):
        return self.context_manager.chip_data

    def _set_env(self):
        invoker_addr = self._config.system.invoker_addr
        sample = self._config.system.sample
        env_name = self._config.system.env_name
        point_label = self._config.system.point_label
        Invoker.set_env(
            invoker_addr=invoker_addr,
            point_label=point_label,
            sample=sample,
            env_name=env_name,
            username=self._username,
            cache_flag=self._cache_flag,
        )

        ret_data = Invoker.load_account()
        if ret_data.get("code") == 200:
            username = ret_data.get("data").get("username")
            if self._username and self._username != username:
                ret_data = self._login_user()
            else:
                self._username = username
        elif self._username:
            ret_data = self._login_user()

        if ret_data.get("code") != 200:
            raise CourierError("Can't load account", ret_data)

        # init web client
        self._db = DataCenter()

        # singleton system meta data
        system_meta = SystemMeta(
            version=__version__,
            device=DEVICE,
            username=self._username,
            sample=sample,
            env_name=env_name,
            point_label=point_label,
            env_id=self._db.query_chip_env_id()["data"]["env_id"],
        )
        pyqlog.info(f"Load success, {system_meta}")

        token = ret_data["data"].get("token") or ret_data["data"].get("access_token")
        self._context_manager.token = token

        # set transfer
        set_transfer_config(self._config, token)

    def _login_user(self):
        while True:
            ret_data = Invoker.verify_account(
                username=self._username,
                password=input(f"请输入账户 `{self._username}` 的密码: "),
            )
            if ret_data.get("code") == 400:
                pyqlog.error(f"登录失败! | {ret_data.get('message')}")
            else:
                return ret_data

    def change_point_label(self, point_label: Optional[str] = None):
        if point_label is None:
            point_label = self._config.system.point_label

        sample = self._config.system.sample
        env_name = self._config.system.env_name
        cache_flag = None
        use_cache_nums = set()
        for env in Invoker.read_accounts(username=Invoker.get_env().username):
            if (
                env["sample"] == sample
                and env["env_name"] == env_name
                and env["point_label"] == point_label
            ):
                cache_flag = env["cache_flag"]
                break
            use_cache_nums.add(env["cache_flag"])

        if cache_flag is None:
            for i in range(1000):
                if i not in use_cache_nums:
                    cache_flag = i
                    break

        Invoker.set_env(
            point_label=point_label,
            cache_flag=cache_flag
        )

        ret_data = self.initial_base_qubit_data()
        if ret_data.get("code") != 200:
            raise CourierError("Init base qubit error", ret_data)

        ret_data: CommonDict = self._db.query_chip_all(  # type: ignore
            username=self._username,
            sample=sample,
            env_name=env_name,
            point_label=point_label,
        )
        if ret_data.get("code") != 200:
            raise CourierError("query chip data error", ret_data)

        self._context_manager.refresh_chip_data(ret_data.get("data"))

    def initial_base_qubit_data(self, delete: bool = False):
        """Initial base qubit data from chip line parameters"""

        chip = self.chip_data.cache_config.get(ChipConfigField.chip_line)
        element_names = []
        element_names += list(chip["QubitParams"].keys())
        element_names += list(chip["CouplerParams"].keys())
        xy_baseband_freq = self._config.system.baseband_freq
        m_baseband_freq = self._config.system.m_baseband_freq
        params = QDict(
            m_baseband_freq=m_baseband_freq, xy_baseband_freq=xy_baseband_freq
        )

        ret_data = self._db.init_base_qubit_data(params, element_names, delete)
        return ret_data

    def refresh(self):
        """
        Refresh chip data.
        """
        ret_data: CommonDict = self._db.query_chip_all(  # type: ignore
            username=self._username,
            sample=self._config.system.sample,
            env_name=self._config.system.env_name,
            point_label=self._config.system.point_label,
        )

        chip_data = {}
        if ret_data.get("code") == 200:
            chip_data = ret_data.get("data")
        else:
            pyqlog.error(f"Query chip data timeout! Error code: {ret_data}")

        self._context_manager.refresh_chip_data(chip_data)

    def update_context_from_hot_data(self, record_id: str):
        """Update the hot spot data in the cache chip. Once the quality of the measurement
        and control experiment is passed, a modification trace will be generated. This
        interface automatically recognizes the modified data and persists it to the database.
        """
        hot_data = self._context_manager.extract_hot_data(record_id)
        for k, v in hot_data.items():
            ret = self._db.update_single_config(k, file_data=v)
            if ret.get("code") == 200:
                pyqlog.log("UPDATE", f"Update chip data {k}")
        self._context_manager.update_records.clear()

    def load_user_cache_context(self):
        """
        Load user cache context manager global options.
        """
        cache_data = self._db.query_cache_data()
        if cache_data.get("code") == 200:
            cache_context = cache_data.get("data")
            self.context_manager.global_options.update(**cache_context)
            return cache_context
        else:
            raise CourierError("Load cache context data error", cache_data)

    def infos(self):
        table = self.context_manager.global_table
        table.add_row(["user", self._username])
        pyqlog.info(f"Backend Information:\n{table}")

    def save_chip_data_to_db(self, names: Union[str, List[str]]):
        """Persist the chip data with the specified name to the database. This interface does
         not focus on specific hot spot data, but only on whether the data itself needs to be persisted

        Args:
            names: The data name to be updated supports any combination of the following elements
            [
                qubit series, like q1, q2
                coupler series, like c1-2, c3-4
                qubit pair series, like q1q2, q3q4
                chip_line_connect.json,
                character.json,
                crosstalk.json,
                xy_crosstalk.json,
                hardware_offset.json,
                instrument.json
            ]
        """
        if isinstance(names, str):
            names = [names]

        for name in names:
            if name in ChipConfigField._value2member_map_:
                v = self.context_manager.chip_data.cache_config.get(name)
                ret = self._db.update_single_config(name, file_data=v)
                if ret.get("code") == 200:
                    pyqlog.log("UPDATE", f"Update chip data {name}")
            else:
                obj = self.context_manager.chip_data.get_physical_unit(name)
                if obj:
                    obj.save_data()

    def check_parallel_allocation_options(self):
        parallel_divide = self.system.parallel_divide

        if "ParallelAllocationQC" not in parallel_divide:
            self.system.parallel_divide = (
                QDict(
                    ParallelAllocationQC=QDict(
                        allocation_policy="normal",
                        mode="sub",
                        neighbor_limit=300,
                        anharmonic=240,
                        freq_split=50,
                        freq_delta=10,
                        max_limit=300,
                        xy_cross_threshold_low=0.01,
                        xy_cross_threshold_high=0.05,
                        iter_times=4,
                        check_xy_lo=True,
                        xy_cross_coefficient_map=None,
                        intermediate_freq_allocate=True,
                    ),
                    ParallelAllocationCGC=QDict(
                        allocation_policy="normal", mode="horse", distance_limit=2
                    ),
                    ParallelAllocationCC=QDict(
                        allocation_policy="normal", mode="recursive"
                    ),
                    IntermediateFreqAllocation=QDict(
                        intermediate_freq_accuracy=10,
                        expect_intermediate_freq=1050,
                        goal_gap=None,
                        is_force=False,
                    ),
                    ReadoutAmpAllocation=QDict(
                        standard_amp=0.15, set_index=False, sample_rate=3.2
                    ),
                ),
            )
            pyqlog.warning(
                f"Parallel Allocator Options No Set, Auto Set:\n{self.system.parallel_divide}"
            )

    def transfer_to_unit96(self):
        self.chip_data.transfer_to_unit96()
        self.save_chip_data_to_db(ChipConfigField.chip_line)
