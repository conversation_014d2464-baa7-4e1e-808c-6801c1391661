# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.set_collection_name.rst:2
msgid "pyQCat.tools.set\\_collection\\_name"
msgstr ""

#: of pyQCat.tools.utilities.set_collection_name:1
msgid "Define set ODM Doc collection name."
msgstr ""

#: of pyQCat.tools.utilities.set_collection_name
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.utilities.set_collection_name:4
msgid "ODM Doc class_name"
msgstr ""

#: of pyQCat.tools.utilities.set_collection_name:7
msgid "set collection name"
msgstr ""

#: of pyQCat.tools.utilities.set_collection_name
msgid "Raises"
msgstr ""

#: of pyQCat.tools.utilities.set_collection_name:10
msgid "'pyQCat.database.ODM' object has no attribute `odm_class`."
msgstr ""

#: of pyQCat.tools.utilities.set_collection_name
msgid "Return type"
msgstr ""

#: of pyQCat.tools.utilities.set_collection_name:13
msgid ":py:obj:`None`"
msgstr ""

