# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/15
# __author:       <PERSON> Fang

"""
New cass Z Crosstalk composite experiment.
"""

import json
import os
import re
from copy import deepcopy

import numpy as np
import pandas as pd

from ....analysis.library import (
    CrosstalkCurveAnalysis,
    CrosstalkLinearAnalysis,
    XYCrossRwAnalysis,
    ZCrossDelayAnalysis,
)
from ....errors import ExperimentOptionsError
from ....log import pyqlog
from ....qubit import NAME_PATTERN
from ....structures import ExperimentData, MetaData, Options, QDict
from ....tools.savefile import LocalFile
from ....tools.utilities import qarange
from ...composite_experiment import CompositeExperiment
from ...single.crosstalk.z_cross_once import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ZCrossDelayLinearOnce,
    ZCrossDelayOnce,
    ZCrossZampOnce,
)


class ZCrossBase(CompositeExperiment):
    """New case ZCross composite experiment."""

    def get_qubit_str(self):
        """Get qubit string."""
        bias_name = self.experiment_options.bias_name
        t_name_list = self.run_options.t_name_list

        length = len(t_name_list)
        if length > 1:
            string = f"B{bias_name}-T{t_name_list[0]}~{t_name_list[-1]}"
        elif length == 1:
            string = f"B{bias_name}-T{t_name_list[0]}"
        else:
            string = f"B{bias_name}-T"
        return string

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment options:
            bias_name (str): Bias Qubit/Coupler name.
            target_name_list (list): Target qubit name list.
            bz_amp_list (list): Set Bias qubit z_amp list.

        """
        options = super()._default_experiment_options()

        options.set_validator("bias_name", str)
        options.set_validator("target_name_list", list)
        options.set_validator("bz_amp_list", list)

        options.bias_name = ""
        options.target_name_list = []
        options.bz_amp_list = qarange(0.0, 0.2, 0.01)

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.t_name_list = []
        options.bias_qubit = None  # Qubit or Coupler object
        options.qc_map = {}  # All Qubit or Coupler object map

        options.cross_z_amp_map = {}  # Note once cross z_amp
        options.extra_data_map = {}  # Note some extra data
        options.analysis_class = XYCrossRwAnalysis
        options.once_analysis_class = None  # Once target analysis class
        options.once_analysis_ylabel = ""

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        options.set_validator("n_multiple", float)
        options.set_validator("once_ana_options", dict)

        options.n_multiple = 100
        options.cross_coe_map = {}
        options.cross_trust_map = {}
        options.once_ana_options = {}

        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "bias_name": self.experiment_options.bias_name,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        bias_name = self.experiment_options.bias_name
        target_name_list = self.experiment_options.target_name_list
        bz_amp_list = self.experiment_options.bz_amp_list
        figsize = self.analysis_options.figsize
        once_ana_options = self.analysis_options.once_ana_options

        qc_map = {}
        qubit_map = {qubit.name: qubit for qubit in self.qubits}
        coupler_map = {qubit.name: qubit for qubit in self.couplers}
        qc_map.update(qubit_map)
        qc_map.update(coupler_map)

        q_names = list(qubit_map.keys())
        c_names = list(coupler_map.keys())
        all_names = list(qc_map.keys())
        q_pattern = re.compile(NAME_PATTERN.qubit)
        c_pattern = re.compile(NAME_PATTERN.coupler)

        if bias_name in all_names:
            bias_qubit = qc_map.get(bias_name)
        else:
            raise ExperimentOptionsError(
                self.label,
                msg=f"Please check, not found {bias_name} from qubits and couplers",
                key="bias_name",
                value=bias_name,
            )

        if target_name_list:
            t_name_list = []
            for t_name in target_name_list:
                if isinstance(t_name, int):
                    name = f"q{t_name}"
                    t_name_list.append(name)
                elif isinstance(t_name, str) and q_pattern.match(t_name):
                    t_name_list.append(t_name)
                elif isinstance(t_name, str) and c_pattern.match(t_name):
                    t_name_list.append(t_name)
                else:
                    pyqlog.warning(f"Maybe {t_name} format error!")
        else:
            # NOTE: default set just only support Qubit objects.
            t_name_list = [name for name in q_names if name != bias_name]
        t_name_list.sort(key=lambda x: int("".join(x.split("-"))[1:]))

        check_flag = True
        for t_name in t_name_list:
            if t_name not in all_names:
                pyqlog.error(f"{t_name} not in qubits or couplers: {all_names}")
                if check_flag is True:
                    check_flag = False
        if check_flag is False:
            raise ExperimentOptionsError(
                self.label,
                msg=f"Check target names: {t_name_list} were not in exist environment!",
            )

        # change analysis figsize
        base_len = 10
        row_length = len(bz_amp_list)
        col_length = len(t_name_list)
        if figsize in [[12, 8], (12, 8)]:
            r_ratio = 1
            c_ratio = 1
            if row_length > base_len:
                r_ratio = row_length / base_len
            if col_length > base_len:
                c_ratio = col_length / base_len
            figsize = (int(12 * c_ratio), int(8 * r_ratio))
        pyqlog.info(f"{self.label} analysis figsize: {figsize}")

        once_ana_options.update({"x_label": f"Bias {bias_name} Zamp (V)"})
        self.set_analysis_options(figsize=figsize)
        self.set_run_options(
            t_name_list=t_name_list,
            bias_qubit=bias_qubit,
            qc_map=qc_map,
        )

    def _alone_save_result(self):
        """Alone save some special result."""
        bias_name = self.experiment_options.bias_name
        t_name_list = self.run_options.t_name_list
        cross_coe_map = self.analysis_options.cross_coe_map
        cross_z_amp_map = self.run_options.cross_z_amp_map

        mark_info = f"{self}(B{bias_name}ZCross_coefficient)"
        json_str = json.dumps(cross_coe_map, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=mark_info, prefix=".json")

        extra_info = f"{self}(B{bias_name}ZCross_zamp)"
        extra_json_str = json.dumps(cross_z_amp_map, ensure_ascii=False, indent=4)
        self.file.save_text(extra_json_str, name=extra_info, prefix=".json")

        index_list = list(list(cross_coe_map.values())[0].keys())
        cross_coe_df = pd.DataFrame(data=cross_coe_map, index=index_list).T
        pyqlog.log("RESULT", f"{t_name_list}{mark_info}:\n{cross_coe_df}")
        if isinstance(self.file, LocalFile):
            if not os.path.exists(self.file.dirs):
                os.makedirs(self.file.dirs, exist_ok=True)
            csv_name = "".join([self.file.dirs, mark_info, ".csv"])
            cross_coe_df.to_csv(csv_name)

    def _split_once_analysis(self):
        """Split run once analysis."""
        bias_name = self.experiment_options.bias_name
        t_name_list = self.run_options.t_name_list
        qc_map = self.run_options.qc_map
        cross_z_amp_map = self.run_options.cross_z_amp_map
        extra_data_map = self.run_options.extra_data_map
        once_analysis_class = self.run_options.once_analysis_class
        y_label = self.run_options.once_analysis_ylabel
        once_ana_options = self.run_options.once_ana_options

        fy_coefficient_map = {}
        y_data_key = "bias_v"
        for t_name in t_name_list:
            mark_info = f"B{bias_name}-split{os.sep}{t_name}"
            tq_obj = qc_map.get(t_name)

            sub_title = []
            bq_ac_list = []
            tq_ac_fit_list = []
            tq_ac_array = []
            z0_array = []
            z1_array = []
            for bz_amp_str, t_ed_dict in extra_data_map.items():
                st_exp_data = t_ed_dict.get(t_name)
                if isinstance(st_exp_data, ExperimentData):
                    bz_amp = float(bz_amp_str)
                    fit_tz_amp_map = cross_z_amp_map.get(bz_amp_str, {})
                    fit_tz_amp = fit_tz_amp_map.get(t_name, 0.0)
                    if not sub_title:
                        sub_title = list(st_exp_data.y_data.keys())[:2]
                    x_arr = st_exp_data.x_data
                    z0_arr, z1_arr, *_ = st_exp_data.y_data.values()

                    bq_ac_list.append(bz_amp)
                    tq_ac_fit_list.append(fit_tz_amp)
                    tq_ac_array.append(x_arr)
                    z0_array.append(z0_arr)
                    z1_array.append(z1_arr)
                else:
                    pyqlog.warning(
                        f"B{bias_name} Z{bz_amp_str} {t_name} experiment_data: {st_exp_data}"
                    )
            sy_data = {y_data_key: np.array(tq_ac_fit_list)}

            analysis_params = QDict()
            analysis_params.bq_name = bias_name
            analysis_params.tq_name = t_name
            analysis_params.bq_ac_list = bq_ac_list
            analysis_params.tq_ac_fit_list = tq_ac_fit_list
            analysis_params.tq_ac_array = tq_ac_array
            analysis_params.z0_array = z0_array
            analysis_params.z1_array = z1_array
            analysis_params.drive_freq = tq_obj.drive_freq

            meta_dict = self._metadata()
            meta_dict.draw_meta.update({"target name": t_name})
            meta_dict.process_meta = {"analysis_params": analysis_params}

            t_exp_data = ExperimentData(
                x_data=np.array(bq_ac_list),
                y_data=sy_data,
                experiment_id=self.id,
                metadata=meta_dict,
            )

            new_y_label = f"Target {t_name} {y_label}"
            once_ana_options.update(
                {
                    "y_label": new_y_label,
                    "sub_title": sub_title,
                }
            )

            analysis_obj = SpiltMixin.run_once_analysis(
                t_exp_data,
                once_analysis_class,
                once_ana_options,
                has_child=True,
                # show_result=True,
            )
            analysis_obj.options.result_name = f"B{bias_name}T{t_name}"
            SpiltMixin.save_once_analysis_figure(
                analysis_obj, self.file, mark=mark_info, record_text=True
            )
            fy_coef = analysis_obj.results.get(
                "coefficient"
            ) or analysis_obj.results.get("k")
            fy_coefficient_map.update({t_name: float(fy_coef.value)})
        pyqlog.info(f"fy_coefficient_map: {fy_coefficient_map}")
        mark_info = f"B{bias_name}_finally_coefficient"
        json_str = json.dumps(fy_coefficient_map, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=mark_info, prefix=".json")

    def run(self):
        """Run logic."""
        super().run()

        bias_name = self.experiment_options.bias_name
        bz_amp_list = self.experiment_options.bz_amp_list
        t_name_list = self.run_options.t_name_list
        analysis_class = self.run_options.analysis_class

        cross_coe_map = self.analysis_options.cross_coe_map
        cross_trust_map = self.analysis_options.cross_trust_map
        cross_z_amp_map = self.run_options.cross_z_amp_map
        extra_data_map = self.run_options.extra_data_map

        length = len(bz_amp_list)
        for idx, bz_amp in enumerate(bz_amp_list):
            zcr_exp = deepcopy(self.child_experiment)
            description = f"B{bias_name}Z{bz_amp}"
            zcr_exp.set_parent_file(self, description, idx, length)
            zcr_exp.set_experiment_options(
                bias_name=bias_name,
                target_name_list=t_name_list,
                bz_amp=bz_amp,
            )
            self._check_simulator_data(zcr_exp, idx)
            zcr_exp.run()
            zcr_exp.clear_params()
            self._experiments.append(zcr_exp)

            s_cross_coe_map = zcr_exp.analysis_options.cross_coe_map
            s_cross_trust_map = zcr_exp.analysis_options.cross_trust_map
            s_cross_z_amp_map = zcr_exp.run_options.cross_z_amp_map
            s_extra_data_map = zcr_exp.run_options.extra_data_map

            cross_coe_map.update(s_cross_coe_map)
            cross_trust_map.update(s_cross_trust_map)
            cross_z_amp_map.update(s_cross_z_amp_map)
            extra_data_map.update(s_extra_data_map)

        self._alone_save_result()
        self._run_analysis([], analysis_class)
        self._split_once_analysis()


class ZCrossZamp(ZCrossBase):
    """Sub experiment scan target z_amp."""

    _sub_experiment_class = ZCrossZampOnce

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.once_analysis_class = CrosstalkLinearAnalysis
        options.once_analysis_ylabel = "Zamp (V)"

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        options.once_ana_options = {
            "fit_type": "anal",  # "linear" or "anal"
            "popt": [-4, 0, 10, 4, 0, 7, 0.01, 0.001],
        }

        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        once_ana_options = self.analysis_options.once_ana_options

        fit_type = once_ana_options.get("fit_type", "linear")
        if fit_type == "anal":
            once_analysis_class = CrosstalkCurveAnalysis
        else:
            once_analysis_class = CrosstalkLinearAnalysis

        self.set_run_options(once_analysis_class=once_analysis_class)


class ZCrossDelay(ZCrossBase):
    """Sub experiment scan target z_amp."""

    _sub_experiment_class = ZCrossDelayOnce

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.once_analysis_class = ZCrossDelayAnalysis
        options.once_analysis_ylabel = "Delay (ns)"

        return options


class ZCrossDelayLiner(ZCrossDelay):
    """Sub experiment scan target z_amp."""

    _sub_experiment_class = ZCrossDelayLinearOnce
