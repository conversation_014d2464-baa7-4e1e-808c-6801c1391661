# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/07
# __author:       <PERSON><PERSON><PERSON>
# __corporation:  OriginQuantum

import datetime
import os
from collections import defaultdict
from copy import deepcopy
from typing import List, Union

import numpy as np

from ....analysis import StabilityAnalysis
from ....structures import Options, QDict
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import T1, SingleShot
from ..error_quantification.t2 import T2Ramsey


class StabilityBase(CompositeExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("loops", int)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.loops = 5000

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.subplots = (1, 1)
        options.figsize = (8, 3)
        options.sub_title = None
        options.y_label = None
        options.x_label = "Time"
        options.times = []

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.count = 0
        options.records = defaultdict(list)

        return options

    def save_records(self):
        pass

    def add_records(self, child):
        data = self._extra_data(child)
        self.analysis_options.times.append(datetime.datetime.now())
        arr = np.array(list(data.values())).reshape((1, -1))
        arr = np.c_[np.array([self.run_options.count]), arr]

        file_name = os.path.join(self.file.dirs, "history_data.txt")
        with open(file_name, mode="a+", encoding="utf-8") as fp:
            np.savetxt(fp, arr, fmt="%12.4f")

        for k, v in data.items():
            self.run_options.records[k].append(v)

        # child.analysis.provide_for_parent.update(data.to_dict())

    def _extra_data(self, child) -> QDict:
        pass

    def _update_ana_options(self):
        x, y = self.analysis_options.figsize
        key_num = len(self.run_options.records.keys())
        subplots = (key_num, 1)
        figsize = (x, y * key_num)
        self.set_analysis_options(
            subplots=subplots,
            figsize=figsize,
        )

    async def _sync_composite_run(self):
        loops = self.experiment_options.loops

        for i in range(loops):
            child_exp = deepcopy(self.child_experiment)
            child_exp.set_parent_file(self, f"Loop-{i}", index=i)
            self._check_simulator_data(child_exp, i)
            await child_exp.run_experiment()
            self.add_records(child_exp)
            self.run_options.count += 1
            # self._experiments.append(child_exp)
        self._update_ana_options()

        self._run_analysis(list(range(loops)), StabilityAnalysis)
        self.run_options.count = 0

    def _run_analysis(self, x_data: Union[List, np.ndarray], analysis_class):
        # create analysis object.
        # analysis_list = [sub_exp.analysis for sub_exp in self._experiments]
        records = self.run_options.records
        metadata = self._metadata()
        self._analysis = analysis_class.from_sub_analysis(
            x_data, dict(records), self.id, metadata
        )

        # update options.
        self.analysis.set_options(**self._analysis_options)

        # run analysis.
        self.analysis.run_analysis()
        self._save_curve_analysis_plot()

        self.analysis.update_result()


class StabilitySingleShot(StabilityBase):
    _sub_experiment_class = SingleShot

    def _extra_data(self, child):
        discriminator = list(child.analysis.results.values())[0].value
        f0, f1 = discriminator.fidelity
        fm = np.mean((f0, f1))
        c0 = discriminator.centers[0]
        c1 = discriminator.centers[1]

        return QDict(
            f0=f0,
            f1=f1,
            fm=fm,
            c0_x=c0[0],
            c0_y=c0[1],
            c1_x=c1[0],
            c1_y=c1[1],
        )


class StabilityTBase(StabilityBase):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("show_color_map", bool)
        options.show_color_map = False

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.show_color_map = False
        options.data_key = None

        return options

    def _check_options(self):
        super()._check_options()

        show_color_map = self.experiment_options.show_color_map
        if show_color_map:
            self.set_analysis_options(show_color_map=show_color_map)

    def add_records(self, child):
        super().add_records(child)
        if self.experiment_options.show_color_map:
            if self.analysis_options.data_key is None:
                self.set_analysis_options(
                    data_key=list(self.run_options.records.keys())
                )
            child_x_data = child.experiment_data.x_data
            child_y_data = child.experiment_data.y_data
            data = QDict(child_x_data=child_x_data, child_y_data=child_y_data)
            for k, v in data.items():
                self.run_options.records[k].append(v)


class StabilityT1(StabilityTBase):
    _sub_experiment_class = T1

    def _extra_data(self, child):
        t1 = child.analysis.results.tau.value
        r2 = child.analysis.quality.value

        return QDict(t1=t1, r2=r2)


class StabilityT2Ramsey(StabilityTBase):
    _sub_experiment_class = T2Ramsey

    def _extra_data(self, child):
        t2 = child.analysis.results.tau.value
        freq = child.analysis.results.freq.value
        t2_rate = child.analysis.results.t2_rate.value

        return QDict(t2=t2, freq=freq, t2_rate=t2_rate)
