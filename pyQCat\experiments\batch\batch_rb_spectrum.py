# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/14
# __author:       <PERSON><PERSON><PERSON>

import json
import os
from collections import Counter
from enum import Enum
from typing import List

import matplotlib.pyplot as plt
import numpy as np

from ...executor.batch import divide_same_lo_baseband_freq
from ...log import pyqlog
from ...qubit import Qubit
from ...structures import QDict
from ...tools import freq_to_amp, get_bound_ac_spectrum, qarange
from ..batch_experiment import BatchExperiment


class TraversalPattern(int, Enum):
    Decrement = 0
    Increment = 1
    Expand = 2


class BatchRBSpectrum(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.step = 20
        options.reverse_order = False
        options.traversal_pattern = TraversalPattern.Decrement
        options.pass_point_num_limit = 20
        options.max_batch_count = 2
        options.plot_distribution_map = True
        options.filter_fir = False
        options.filter_iir = False
        options.freq_map = {}
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.fq_dict = {}
        options.idle_point_dict = {}
        options.read_point_dict = {}
        options.rf_dict = {}
        options.xy_gap_dict = {}
        options.rb_records = {}
        options.current_point_map = {}
        options.group_map = {}
        options.br2pass_map = {}
        options.ppt_template.update(
            dict(
                shape=(2, 3),
                split_by_unit=True,
            )
        )
        return options

    def _check_options(self):
        super()._check_options()
        self.run_options.ppt_template.exps = []
        for exp in self.experiment_options.flows:
            if "RBSingle" in exp:
                self.run_options.ppt_template.exps = [exp]
                break

    def _count_max_sweep_count(self, parallel_units: List[str]):
        return max([len(self.run_options.fq_dict.get(unit)) for unit in parallel_units])

    def _change_work_point(self, i: int, parallel_units: List[str]):
        working_units = []
        qubit_list = []
        for bit in self.run_options.fq_dict.keys():
            if len(self.run_options.fq_dict.get(bit)) > i and bit in parallel_units:
                freq = self.run_options.fq_dict[bit][i]
                if freq:
                    working_units.append(bit)
                    qubit_obj: Qubit = self.context_manager.chip_data.cache_qubit[bit]
                    z_amp = freq_to_amp(qubit_obj, freq)
                    qubit_obj.drive_freq = freq
                    qubit_obj.idle_point = z_amp
                    qubit_obj.readout_point.amp = self.run_options.read_point_dict[
                        bit
                    ] - (z_amp - self.run_options.idle_point_dict[bit])
                    qubit_obj.XYwave.detune_pi = 0
                    qubit_obj.XYwave.detune_pi2 = 0

                    # Reset bit attribute parameters
                    qubit_obj.T1 = None
                    qubit_obj.T2 = None
                    qubit_obj.TS2 = None
                    qubit_obj.fidelity = None

                    qubit_list.append(qubit_obj)

                    self.run_options.current_point_map[bit] = str(round(freq, 3))
                    self.run_options.dir_describe[bit] = f"sweep-{round(freq, 3)}"
                    pyqlog.log(
                        "EXP",
                        f"{bit} | drive freq ({qubit_obj.drive_freq}) | "
                        f"idle point ({qubit_obj.idle_point}) | "
                        f"readout point ({qubit_obj.readout_point.amp})",
                    )
        divide_same_lo_baseband_freq(
            qubit_list, self.context_manager.chip_data, is_force=True
        )
        return working_units

    def _batch_up(self):
        super()._batch_up()

        self.backend.system.parallel_divide.intermediate_freq_allocate_options = QDict(
            is_force=True
        )
        traversal_pattern = self.experiment_options.traversal_pattern

        # parallel divide group
        for qubit_name in self.experiment_options.physical_units:
            # record init readout point information
            qubit_obj: Qubit = self.context_manager.chip_data.cache_qubit[qubit_name]
            idle_freq = qubit_obj.drive_freq

            # calculate freq list
            freq_bounds = self.experiment_options.freq_map.get(qubit_name)
            if freq_bounds:
                freq_max, freq_min = freq_bounds
            else:
                freq_max, freq_min = get_bound_ac_spectrum(qubit_obj)

            if freq_max == freq_min:
                freq_max = [idle_freq]
            elif traversal_pattern == TraversalPattern.Decrement:
                freq_list = qarange(
                    freq_max - 0.001,
                    freq_min + 0.001,
                    -abs(self.experiment_options.step),
                )
            elif traversal_pattern == TraversalPattern.Increment:
                freq_list = qarange(
                    freq_min + 0.001,
                    freq_max - 0.001,
                    abs(self.experiment_options.step),
                )
            else:
                freq_list = [idle_freq]
                step = abs(self.experiment_options.step)
                cur_step = step
                print(freq_max, freq_min)
                while True:
                    is_choice = False
                    if idle_freq + cur_step <= freq_max:
                        freq_list.append(idle_freq + cur_step)
                        is_choice = True
                    else:
                        freq_list.append(0)

                    if idle_freq - cur_step >= freq_min:
                        freq_list.append(idle_freq - cur_step)
                        is_choice = True
                    else:
                        freq_list.append(0)

                    if is_choice is False:
                        break
                    cur_step += step
                    print(cur_step)
            freq_list = [round(freq, 3) for freq in freq_list]

            # reverse freq list order
            # if self.experiment_options.reverse_order is True:
            #     freq_list = freq_list[::-1]

            self.run_options.fq_dict[qubit_name] = freq_list

            # init freq record map
            point_map = {}
            for freq in freq_list:
                point_map.update(
                    {
                        str(round(freq, 3)): {
                            "is_pass": False,
                            "rb_fidelity": None,
                            "rb_std": None,
                            "p0": None,
                            "p1": None,
                            "params": None,
                            "error_reason": None,
                            "error_exp": None,
                        }
                    }
                )
            self.run_options.rb_records[qubit_name] = QDict(**point_map)

            # set max point to parallel divide
            qubit_obj.drive_freq = freq_list[0]

        # auto parallel divide
        group_map = self.parallel_allocator_for_qc(self.experiment_options.physical_units)
        self.run_options.group_map = group_map
        self.run_options.dir_describe = {}

        # filter low rf which cause IF lower 800
        for gn, group in group_map.items():
            for unit in group:
                qubit = self.context_manager.chip_data.get_physical_unit(unit)

                # record init qubit information
                self.run_options.idle_point_dict[unit] = qubit.idle_point
                self.run_options.read_point_dict[unit] = qubit.readout_point.amp
                self.run_options.rf_dict[unit] = qubit.drive_freq
                self.run_options.xy_gap_dict[unit] = qubit.inst.xy_gap

                # Minimum frequency limit filtering
                # freq_limit = qubit.XYwave.baseband_freq - 800 + 3750
                freq_list = self.run_options.fq_dict[unit]
                filter_freq = [freq for freq in freq_list if freq > 3800]
                self.run_options.fq_dict[unit] = filter_freq
                pyqlog.info(
                    f"{unit} scope info: max({freq_list[0]}) min({freq_list[-1]}) points({len(freq_list)})"
                )

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "RBSingle" in exp.label:
            for unit in record.analysis_data.keys():
                cur_point = self.run_options.current_point_map[unit]
                point_sate = self.run_options.rb_records.get(unit).get(cur_point)
                if unit in record.pass_units:
                    point_sate.is_pass = True
                    point_sate.rb_fidelity = (
                        record.analysis_data.get(unit).get("result").get("fidelity")
                    )
                    point_sate.rb_std = (
                        record.analysis_data.get(unit).get("result").get("std")
                    )
                else:
                    point_sate.error_reason = record.fail_reason
                    point_sate.error_exp = exp.label
                point_sate.params = self.context_manager.chip_data.cache_qubit.get(
                    unit
                ).to_dict()
        else:
            if "SingleShot" in exp.label:
                for unit in record.analysis_data.keys():
                    cur_point = self.run_options.current_point_map[unit]
                    point_sate = self.run_options.rb_records.get(unit).get(cur_point)
                    fidelity = (
                        record.analysis_data.get(unit)
                        .get("result", {})
                        .get("dcm", {})
                        .get("fidelity")
                    )
                    if fidelity:
                        point_sate.p0, point_sate.p1 = fidelity[0], fidelity[1]

            for unit in record.bad_units:
                cur_point = self.run_options.current_point_map[unit]
                point_sate = self.run_options.rb_records.get(unit).get(cur_point)
                point_sate.error_reason = record.fail_reason
                point_sate.error_exp = exp.label
                point_sate.params = self.context_manager.chip_data.cache_qubit.get(
                    unit
                ).to_dict()

            for unit in record.pass_units:
                cur_point = self.run_options.current_point_map[unit]
                point_sate = self.run_options.rb_records.get(unit).get(cur_point)
                point_sate.params = self.context_manager.chip_data.cache_qubit.get(
                    unit
                ).to_dict()

        return record

    def _run_batch(self):
        for gn, group in self.run_options.group_map.items():
            sweep_count = self._count_max_sweep_count(group)

            for i in range(sweep_count):
                # change working point
                working_units = self._change_work_point(i, group)

                describe = f"{gn} Iter-{i}"

                # run exp flow
                self._run_flow(
                    physical_units=working_units,
                    flows=self.experiment_options.flows,
                    name=describe,
                )

                # record working point state
                self._record_point_state()

        self._run_analysis()

        if self.experiment_options.plot_distribution_map is True:
            self._plot_distribution()

        physical_units = (
            self.record_meta.execute_meta.result.physical_units
            or self.experiment_options.physical_units
        )
        fail_units = self._check_need_retry_units()
        pass_units = [unit for unit in physical_units if unit not in fail_units]
        self.bind_pass_units(pass_units)

    def _record_point_state(self, merge_pre: bool = False):
        record_path = os.path.join(
            self.context_manager.config.system.local_root,
            self.context_manager.config.system.sample,
            self.run_options.dir_prefix,
            f"rb_records_{self.run_options.batch_count}.json",
        )
        self.run_options.br2pass_map[str(self.run_options.batch_count)] = [
            record_path,
            None,
        ]

        if merge_pre and self.run_options.batch_count > 0:
            for cur_path, pass_units in self.run_options.br2pass_map.values():
                if pass_units:
                    with open(cur_path, mode="r", encoding="utf-8") as fp:
                        cur_rb_data = json.load(fp)
                    for unit in pass_units:
                        self.run_options.rb_records.update(
                            {unit: cur_rb_data.get(unit)}
                        )

        with open(record_path, mode="w", encoding="utf-8") as fp:
            json.dump(self.run_options.rb_records, fp, indent=4, ensure_ascii=False)

    def _check_need_retry_units(self):
        # bugfix: When all working points of a certain bit fail, it will result
        # in the inability to perform batch retry
        unit_pass_count = {unit: [] for unit in self.run_options.rb_records.keys()}

        for unit, records in self.run_options.rb_records.items():
            for freq, result in records.items():
                if result.get("is_pass") is True:
                    unit_pass_count[unit].append(freq)

        need_retry_units = []
        all_units = list(self.run_options.rb_records.keys())

        if unit_pass_count:
            for unit, freq_list in unit_pass_count.items():
                if len(freq_list) < self.experiment_options.pass_point_num_limit:
                    pyqlog.warning(
                        f"{unit} pass working point count {len(freq_list)},"
                        f" less {self.experiment_options.pass_point_num_limit}"
                    )
                    need_retry_units.append(unit)
        else:
            need_retry_units = list(self.run_options.rb_records.keys())

        if self.experiment_options.filter_fir is True:
            _, point_map = DistortionApi.find_FIR_distortion_working_point_v2(
                self.run_options.rb_records
            )
            for unit, freq_list in point_map.items():
                if not freq_list:
                    pyqlog.warning(f"{unit} no find FIR distortion working point!")
                    need_retry_units.append(unit)

        if self.experiment_options.filter_iir is True:
            _, point_map = DistortionApi.find_IIR_distortion_working_point_v2(
                self.run_options.rb_records
            )
            for unit, freq_list in point_map.items():
                if not freq_list:
                    pyqlog.warning(f"{unit} no find IIR distortion working point!")
                    need_retry_units.append(unit)

        self._record_point_state(merge_pre=True)
        need_retry_units = list(set(need_retry_units))
        pass_units = [unit for unit in all_units if unit not in need_retry_units]
        self.run_options.br2pass_map[str(self.run_options.batch_count)][1] = pass_units

        if need_retry_units:
            self.run_options.rb_records.clear()
            self.batch_records.clear()

        self._show_batch_execute_result(all_units=all_units, bad_units=need_retry_units)

        return need_retry_units

    def _plot_distribution(self):
        fidelity_list = []
        for qubit_records in self.run_options.rb_records.values():
            for freq_record in qubit_records.values():
                if freq_record.get("is_pass") is True:
                    fidelity_list.append(freq_record["rb_fidelity"])

        result = Counter(fidelity_list)

        plt.scatter(list(result.keys()), list(result.values()), label="Raw Data")
        plt.legend()

        save_path = os.path.join(
            self.context_manager.config.system.local_root,
            self.context_manager.config.system.sample,
            self.run_options.dir_prefix,
            f"rb_fidelity_distribution_{self.run_options.batch_count}.png",
        )

        plt.savefig(save_path)
        plt.close()


class DistortionApi:
    @staticmethod
    def find_frequency(frequency_list, lb, ub, rb_spectrum_step):
        best_freq = []
        # frequency_list是rb谱得出的解空间，（lb, ub）是flux-sensitive的区域，适合用于畸变测试，
        # 此函数用于查找frequency_list 中 处于（lb, ub）范围内的元素，并对其进行打分，最后找到得分最高的频率返回。
        # 最多有 (ub-lb)/rb_spectrum_step + 1 个点，但由于小数点问题，一般最多只会有(ub-lb)/rb_spectrum_step个点
        # 在（lb, ub）区间范围内，ub-lb=search_range，search_range=50，rb_spectrum_step=10，所以range_in里最多会有五个元素为True。
        range_in = (frequency_list >= lb) * (frequency_list <= ub)
        # print(np.sum(range_in))
        if np.sum(range_in) > 0:
            # 把这些在（lb,ub） 范围内的频率取出来，作为可选畸变工作点list，然后我们对其打分
            index = np.where(range_in > 0)
            frequency_candidate = frequency_list[index]
            # 对frequency_candidate里的元素打分
            score_candidate = np.zeros_like(frequency_candidate)
            for index, freq in enumerate(frequency_candidate):
                # 打分方式可以调整，尽可能使得所选工作点附近在RB谱中是连续可用的区间。
                score = (
                    frequency_list.__contains__(freq)
                    + frequency_list.__contains__(freq + rb_spectrum_step) * 3
                    + frequency_list.__contains__(freq - rb_spectrum_step) * 3
                    + frequency_list.__contains__(freq + 2 * rb_spectrum_step)
                    + frequency_list.__contains__(freq - 2 * rb_spectrum_step)
                )
                score_candidate[index] = score
            # 得分一样的选择的是index更靠前的（np.argmax决定的），由于RB谱是从上往下扫，所以同等得分选择的是高频工作点。
            frequency_candidate_max_score = frequency_candidate[
                np.argmax(score_candidate)
            ]
            best_freq.append(frequency_candidate_max_score)
        return best_freq

    @staticmethod
    def find_IIR_distortion_working_point_v2(
        rb_spectrum_data, rb_spectrum_step=10, frequency_down=200, search_range=50
    ):
        # 对每个比特依次处理
        qubit_list = list(rb_spectrum_data.keys())
        distortion_working_point_map = {}
        result_save = []

        for qubit in qubit_list:
            frequency_list_str = list(rb_spectrum_data[qubit].keys())
            # frequency_list = np.array([float(str) for str in frequency_list_str])
            frequency_list = []
            for frequency_str in frequency_list_str:
                if rb_spectrum_data[qubit][frequency_str]["is_pass"] == True:
                    try:
                        freq_max = rb_spectrum_data[qubit][frequency_str]["params"][
                            "ac_spectrum"
                        ]["standard"][0]
                    except:
                        freq_max = rb_spectrum_data[qubit][frequency_str]["params"][
                            "ac_spectrum"
                        ]["bottom_left"][0]
                    bit = rb_spectrum_data[qubit][frequency_str]["params"]["bit"]
                    if rb_spectrum_data[qubit][frequency_str]["rb_fidelity"] > 0.995:
                        frequency_list.append(float(frequency_str))
            frequency_list = np.array(frequency_list)
            target_freq = freq_max - frequency_down
            # 搜索顺序依次是先找在[target_freq-search_range， target_freq],
            # [target_freq,target_freq + search_range]，打分过程建议封装

            # 按照搜索顺序依次搜索，这部分可以根据实际情况再做调整。
            lb = target_freq - search_range
            ub = target_freq
            distortion_working_point_frequency = DistortionApi.find_frequency(
                frequency_list, lb, ub, rb_spectrum_step
            )

            # 按照搜索顺序依次搜索
            if len(distortion_working_point_frequency) == 0:
                lb = target_freq
                ub = target_freq + search_range
                distortion_working_point_frequency = DistortionApi.find_frequency(
                    frequency_list, lb, ub, rb_spectrum_step
                )
            # 按照搜索顺序依次搜索
            if len(distortion_working_point_frequency) == 0:
                lb = target_freq - search_range * 2
                ub = target_freq - search_range
                distortion_working_point_frequency = DistortionApi.find_frequency(
                    frequency_list, lb, ub, rb_spectrum_step
                )

            distortion_working_point_map[qubit] = distortion_working_point_frequency

            # 总结搜索结果, else与最近的if匹配
            rb_fidelity = 1
            rb_std = 0
            if len(distortion_working_point_frequency) == 0:
                # print(
                #     qubit
                #     + ":can not find suitable working point for Z distortion characterization"
                # )
                # print(
                #     qubit + f" maximum frequency is {freq_max}, available frequency list:",
                #     frequency_list,
                # )
                result_freq = freq_max
            else:
                result_freq = distortion_working_point_frequency[0]
                # print(qubit + f':{result_freq} MHz is suitable to characterize Z distortion')
                # result_freq_str
                for frequency_str in frequency_list_str:
                    if float(frequency_str) == result_freq:
                        rb_fidelity = rb_spectrum_data[qubit][frequency_str][
                            "rb_fidelity"
                        ]
                        rb_std = rb_spectrum_data[qubit][frequency_str]["rb_std"]
                        break
            # 保存结果，（比特号，freq_max，distortion_working_point_frequency，从最高点降低的频率）
            if len(frequency_list) > 0:
                result_save.append(
                    [
                        bit,
                        freq_max,
                        result_freq,
                        freq_max - result_freq,
                        rb_fidelity,
                        rb_std,
                    ]
                )
        return result_save, distortion_working_point_map

    @staticmethod
    def find_FIR_distortion_working_point_v2(rb_spectrum_data):
        # 对每个比特依次处理
        qubit_list = list(rb_spectrum_data.keys())
        result_save = []
        distortion_working_point_map = {}
        for qubit in qubit_list:
            frequency_list_str = list(rb_spectrum_data[qubit].keys())
            frequency_list_float = [float(item) for item in frequency_list_str]
            frequency_list_float.sort()
            freq_max = frequency_list_float[-1]
            freq_min = frequency_list_float[0]
            # frequency_list = np.array([float(str) for str in frequency_list_str])
            frequency_list = []

            for frequency_str in frequency_list_str:
                if rb_spectrum_data[qubit][frequency_str]["is_pass"] == True:
                    frequency_list.append(float(frequency_str))

            if len(frequency_list) > 0:
                frequency_list.sort()
                freq_max_find = frequency_list[-1]
                freq_min_find = frequency_list[0]
            if len(frequency_list) > 0:
                if (freq_max - freq_max_find) < (freq_min_find - freq_min) or (
                    freq_max - freq_max_find
                ) == (freq_min_find - freq_min):
                    for frequency_str in frequency_list_str:
                        if float(frequency_str) == freq_max_find:
                            bit = rb_spectrum_data[qubit][frequency_str]["params"][
                                "bit"
                            ]
                            rb_fidelity = rb_spectrum_data[qubit][frequency_str][
                                "rb_fidelity"
                            ]
                            rb_std = rb_spectrum_data[qubit][frequency_str]["rb_std"]
                            break
                    result_save.append(
                        [
                            bit,
                            freq_max,
                            freq_max_find,
                            freq_max - freq_max_find,
                            rb_fidelity,
                            rb_std,
                        ]
                    )
                else:
                    for frequency_str in frequency_list_str:
                        if float(frequency_str) == freq_min_find:
                            bit = rb_spectrum_data[qubit][frequency_str]["params"][
                                "bit"
                            ]
                            rb_fidelity = rb_spectrum_data[qubit][frequency_str][
                                "rb_fidelity"
                            ]
                            rb_std = rb_spectrum_data[qubit][frequency_str]["rb_std"]
                            break
                    result_save.append(
                        [
                            bit,
                            freq_min,
                            freq_min_find,
                            freq_min_find - freq_min,
                            rb_fidelity,
                            rb_std,
                        ]
                    )
            distortion_working_point_map[qubit] = result_save[-1][2]
        return result_save, distortion_working_point_map
