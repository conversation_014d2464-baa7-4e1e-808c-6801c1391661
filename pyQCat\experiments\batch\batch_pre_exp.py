# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/19
# __author:       <PERSON>

import time

import numpy as np

from ...instrument.socket_service.tcp_client import MicroSwitch
from ..batch_experiment import BatchExperiment


class BatchPreExp(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()

        options.rough_flows = [
            "FindBusCavityFreq"
        ]

        options.fine_flows = ["FindBusCavityFreq"]
        options.impa_flow = ["ImpaCavityFluxScan"]
        options.impa_opt = ["ImpaOptiParams"]
        options.stab_flow = [
            # "ImpaGain"
        ]
        options.set_flow = ["ImpaSetParams"]

        options.pre_exp = True
        options.ip = None
        options.port = None
        options.low_power_point_label = "test"
        options.bus_list = []
        options.net_power = -15
        options.low_net_power = -45
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.flux = -0.01
        options.flux_bound = None
        options.fp = 14.75
        options.fp_min = 13.95
        options.fp_bound = None
        options.fp_min_bound = None
        options.pp = -0.1
        options.filter_args_list = []
        options.best_args = None

        return options

    @staticmethod
    def _switch_ctrl(connect, bus):
        connect.bus = bus
        sw1_flag = connect.sw1_opened
        sw2_flag = connect.sw2_opened
        sw3_flag = connect.sw3_opened
        connect.close_sw()
        time.sleep(2)
        if 1 <= bus <= 6 and not sw1_flag:
            if sw2_flag or sw3_flag:
                connect.close_sw()
            connect.open_sw1()
        elif 7 <= bus <= 12 and not sw2_flag:
            if sw1_flag or sw3_flag:
                connect.close_sw()
            connect.open_sw2()
        elif 13 <= bus <= 18 and not sw3_flag:
            if sw1_flag or sw2_flag:
                connect.close_sw()
            connect.open_sw3()
        connect.open_bw()

    def _copy_data(self, element_names):
        low_power_point_label = self.experiment_options.low_power_point_label
        self.backend._config.system.point_label = low_power_point_label
        ctx_manager = self.backend.context_manager

        system = ctx_manager.config.system

        user_name = ctx_manager.username
        sample = system.sample
        env_name = system.env_name
        point_label = system.point_label

        self.backend._context_manager.config = self.backend._config
        self.backend._db.copy_other_user_data(
            user_name,
            sample,
            env_name,
            point_label,
            element_names=element_names,
            local=False,
            to_point_label=low_power_point_label,
        )
        self.backend._set_env()

    def _cali_freq(self):
        qubits = self.backend.context_manager.context.qubits
        freq_list = [qubit.probe_freq for qubit in qubits]
        fc_max = max(freq_list)
        fc_min = min(freq_list)
        fp_bound = np.array([2 * 50, 2 * 250]) / 1e3
        fp_min_bound = np.array([-250, -50]) / 1e3
        self.set_run_options(fp=(2 * fc_max + 150) / 1e3)
        self.set_run_options(fp_min=(2 * fc_min - 150) / 1e3)
        self.set_run_options(fp_bound=fp_bound)
        self.set_run_options(fp_min_bound=fp_min_bound)

    def _run_batch(self):
        rough_pass_bus = []
        fine_pass_bus = []
        ip = self.experiment_options.ip
        port = self.experiment_options.port
        bus_list = self.experiment_options.bus_list
        mic_client = MicroSwitch(ip, port)
        hight_point_label = self.backend.context_manager.config.system.point_label
        net_power = self.experiment_options.net_power
        low_net_power = self.experiment_options.low_net_power
        impa_flow = self.experiment_options.impa_flow
        stab_flow = self.experiment_options.stab_flow
        set_flow = self.experiment_options.set_flow

        for bus in bus_list:
            start_num = (bus - 1) * 6
            qubits_on_bus = [
                f"q{int(col_index) + start_num}" for col_index in range(1, 7)
            ]
            self._switch_ctrl(mic_client, bus)

            rough_flows = self.experiment_options.rough_flows
            fine_flows = self.experiment_options.fine_flows
            fcf_0 = self.params_manager.exp_map.get("FindBusCavityFreq")
            if rough_flows:
                fcf_0.options_for_regular_exec["experiment_options"]["bus"] = bus
                fcf_0.options_for_regular_exec["experiment_options"]["net_power"] = (
                    net_power
                )
                fcf_0.options_for_regular_exec["experiment_options"]["segm_scan"] = (
                    False
                )
                fcf_0.options_for_regular_exec["analysis_options"]["fit_q"] = False
                pass_units = self._run_flow(
                    flows=self.experiment_options.rough_flows,
                    physical_units=qubits_on_bus,
                )
                if pass_units:
                    rough_pass_bus.append(bus)
                    self.context_manager.extract_hot_data()
                    # self._copy_data()
            if fine_flows:
                fcf_0.options_for_regular_exec["experiment_options"]["segm_scan"] = True
                fcf_0.options_for_regular_exec["analysis_options"]["fit_q"] = True
                fcf_0.options_for_regular_exec["experiment_options"]["net_power"] = (
                    net_power
                )
                pass_units = self._run_flow(
                    flows=self.experiment_options.rough_flows,
                    physical_units=qubits_on_bus,
                )
                if pass_units:
                    rough_pass_bus.append(bus)
                    self.context_manager.extract_hot_data()
                    self._copy_data(pass_units)

                # self._switch_ctrl(mic_client, bus)
            if rough_flows:
                fcf_0.options_for_regular_exec["experiment_options"]["segm_scan"] = False
                fcf_0.options_for_regular_exec["analysis_options"]["fit_q"] = False
                fcf_0.options_for_regular_exec["experiment_options"]["net_power"] = (
                    low_net_power
                )
                pass_units = self._run_flow(
                    flows=fine_flows, physical_units=qubits_on_bus
                )
            if fine_flows:
                fcf_0 = self.params_manager.exp_map.get("FindBusCavityFreq")
                fcf_0.options_for_regular_exec["experiment_options"]["segm_scan"] = True
                fcf_0.options_for_regular_exec["experiment_options"]["bus"] = bus
                fcf_0.options_for_regular_exec["experiment_options"]["net_power"] = (
                    low_net_power
                )
                fcf_0.options_for_regular_exec["analysis_options"]["fit_q"] = True
                pass_units = self._run_flow(
                    flows=fine_flows, physical_units=qubits_on_bus
                )
                if pass_units:
                    fine_pass_bus.append(bus)
                    self.context_manager.extract_hot_data()
            self.backend._config.system.point_label = hight_point_label
            self.backend._context_manager.config = self.backend._config
            self.backend._set_env()

            if impa_flow:
                cavity_exp = self.params_manager.exp_map.get(impa_flow[0])
                cavity_exp.options_for_regular_exec["experiment_options"]["bus"] = bus
                self._run_flow(
                    flows=impa_flow,
                    physical_units=qubits_on_bus,
                )
                batch_records = self.batch_records
                for k, record in batch_records.items():
                    if "ImpaCavityFluxScan" in k:
                        for qubit, res in record.analysis_data.items():
                            dc = float(res.result.get("dc"))
                            vp = float(res.result.get("vp"))
                            flux_bound = [-0.6 * vp, 0.6 * vp]
                            self.set_run_options(flux=dc)
                            self.set_run_options(flux_bound=flux_bound)
                self._cali_freq()
                flux = self.run_options.flux
                flux_bound = self.run_options.flux_bound
                fp = self.run_options.fp
                fp_bound = self.run_options.fp_bound

                input_data = {
                    "flux": {"is_opt": True, "init_v": flux, "bound": flux_bound},
                    "fp": {"is_opt": True, "init_v": fp, "bound": fp_bound},
                    "pp": {
                        "is_opt": True,
                        "init_v": self.run_options.pp,
                        "bound": [-25.5, 14.5],
                    },
                }
                impa_opt = self.experiment_options.impa_opt
                if impa_opt:
                    iter_count = 0
                    impa_opt_exp = self.params_manager.exp_map.get("ImpaOptiParams")
                    impa_opt_exp.options_for_regular_exec["experiment_options"][
                        "bus"
                    ] = bus
                    filter_args_list = []
                    input_data.update(
                        {
                            "flux": {
                                "is_opt": True,
                                "init_v": flux,
                                "bound": flux_bound,
                            },
                            "fp": {
                                "is_opt": True,
                                "init_v": self.run_options.fp,
                                "bound": list(self.run_options.fp_bound),
                            },
                            "pp": {
                                "is_opt": True,
                                "init_v": self.run_options.pp,
                                "bound": [-25.5, 14.5],
                            },
                        }
                    )
                    impa_opt_exp.options_for_regular_exec["experiment_options"][
                        "input_data"
                    ].update(input_data)
                    while len(filter_args_list) == 0 and iter_count < 6:
                        self._run_flow(
                            flows=impa_opt,
                            physical_units=qubits_on_bus,
                        )
                        batch_records = self.batch_records
                        k, record = list(batch_records.items())[-1]
                        if "ImpaOptiParams" in k:
                            for qubit, res in record.analysis_data.items():
                                filter_args_list = res.result.get("args")
                                best_args = res.result.get("best_args")
                                self.set_run_options(
                                    filter_args_list=filter_args_list,
                                    best_args=best_args,
                                )
                        iter_count += 1

            if stab_flow:
                filter_args_list = self.run_options.filter_args_list

                for args in filter_args_list:
                    gain_exp = self.params_manager.exp_map.get("ImpaGain")
                    gain_exp.options_for_regular_exec["experiment_options"]["bus"] = bus
                    gain_exp.options_for_regular_exec["experiment_options"][
                        "ffp_list"
                    ] = list(args)

                    self._run_flow(
                        flows=stab_flow,
                        physical_units=qubits_on_bus,
                    )
                    batch_records = self.batch_records
                    for k, record in batch_records.items():
                        if "ImpaGain" in k:
                            for qubit, res in record.analysis_data.items():
                                std_gain = float(res.result.get("std_gain"))
                                if std_gain < 0.1:
                                    self.set_run_options(best_args=args)
                                    break
            if set_flow:
                set_para_exp = self.params_manager.exp_map.get("ImpaSetParams")
                set_para_exp.options_for_regular_exec["experiment_options"]["bus"] = bus
                ffp_list = self.run_options.best_args[0]
                ffp_list[1] = ffp_list[1] / 1e9
                set_para_exp.options_for_regular_exec["experiment_options"][
                    "ffp_list"
                ] = ffp_list

                self._run_flow(
                    flows=set_flow,
                    physical_units=qubits_on_bus,
                )
            mic_client.close_sw()
