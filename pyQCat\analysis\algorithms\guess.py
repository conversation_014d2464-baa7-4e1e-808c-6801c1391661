# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import functools
import math
import warnings
from typing import Callable, Optional, Tuple

import numpy as np
from scipy import signal
from scipy.optimize import differential_evolution


def cosine_fit_guess(xdata: np.ndarray, ydata: np.ndarray) -> Tuple:
    r"""Calculate the initial value of the cosine function fit

    The cosine function formula is as follows:

    .. math::

        f_{\rm est} = \frac{1}{2\pi {\rm max}\left| y \right|}
            {\rm max} \left| \frac{dy}{dx} \right|

    Get `amp`, `freq`, `phase`, `baseline` in cosine formula via `np.fft.fft`

    .. note::

        Assuming that the output signal has `N` sampling points, the result after `fft` transformation
        is `N` complex numbers, each complex number corresponds to a frequency (the frequency corresponding
        to the `n <= N/2` point is `( n-1)/N*Fs` ), the modulus value of this complex number represents
        the amplitude characteristic of this frequency. The relationship between this amplitude feature
        and the amplitude of the original signal is: If the amplitude of the original signal is `A`, then
        the modulo value of each point (except the first DC component point) of the `fft` result is `A` `N/2`
        times; while the modulus value of the first point is `N` times the amplitude of the DC component.

        The `N` complex points that are left after the first point is removed from the `N-1` points are
        conjugate symmetrical about their center, so actually only the spectrum of the first half of the
        points needs to be taken, because the conjugate symmetry The modulus value (amplitude) of the two
        points is the same.

        According to the above steps, the algorithm is roughly implemented as follows:

        - First determine the signal number domain for subsequent determination of the actual signal oscillation frequency
        - Fourier transform to obtain the first half of the calculation results
        - Select the frequency point corresponding to the maximum value of the amplitude feature `index`
        - Get the main signal amplitude by the maximum amplitude characteristic / (N / 2)
        - Calculate the frequency corresponding to the main signal through the maximum frequency point and the number domain
        - Calculate the signal phase from the real and imaginary parts
        - DC signal (`baseline`) can be calculated directly from the modulo value of the first point

    .. code::

        In: np.fft.fft(np.array([1,2,3]))
        Out: array([ 6. +0.j       , -1.5+0.8660254j, -1.5-0.8660254j])
        In: np.fft.fft(np.array([1,2,3,4]))
        Out: array([10.+0.j, -2.+2.j, -2.+0.j, -2.-2.j])

    Args:
        xdata: x data
        ydata: y data

    Returns:
        Cosine function fit initial value
    """
    x_transform = list(map(lambda a: a - xdata[0], xdata))
    multiple = x_transform[-1] / 1

    y_fourier = np.fft.fft(ydata)[:int(len(ydata) / 2)]
    y_modulus = np.abs(y_fourier)
    ym_incision = y_modulus[1:]
    index = np.argmax(ym_incision) + 1

    yreal = y_fourier[index].real
    yimag = y_fourier[index].imag

    # calculate actual amplitude
    guess_amplitude = y_modulus[index] / len(ydata) * 2

    # calculate frequency
    x_top = np.take(xdata, np.argmax(ydata))
    x_bottom = np.take(xdata, np.argmin(ydata))
    t = 2 * abs(x_top - x_bottom)
    if t != 0:
        guess_freq = 1 / t
    else:
        guess_freq = 1

    if index > 1:
        guess_freq = [guess_freq, index / multiple]

    # The Fourier transform is usually performed with a sine function,
    # and the initial value of the cosine signal is calculated at this time,
    # so the phase of pi/2 needs to be compensated
    guess_phase = math.atan2(yimag, yreal) + np.pi / 2

    # extract the dc component of the signal
    guess_offset = y_modulus[0].real / len(ydata)

    return guess_freq, guess_phase, guess_amplitude, guess_offset


def fourier_cycle_guess(x: np.ndarray, y: np.ndarray) -> float:
    """Fourier transform calculates signal oscillation period

    This method is the same as the `cosine_fit_guess` method, the difference is that only the most obvious
    oscillation period in the signal is calculated here. Similarly, you can use it to calculate the oscillation
    frequency of the signal

    .. code::

        t = guess.fourier_cycle_guess(x, y)
        f0 = 1 / t

    Args:
        x: x data
        y: y data

    Returns:
        Main signal oscillation period
    """
    x_transform = list(map(lambda a: a - x[0], x))
    multiple = x_transform[-1] / 1
    y_fourier = np.fft.fft(y)[:int(len(y) / 2)]
    y_modulus = np.abs(y_fourier)
    ym_incision = y_modulus[1:]
    index = np.argmax(ym_incision) + 1
    if index == 1:
        x_top = np.take(x, np.argmax(y))
        x_bottom = np.take(x, np.argmin(y))
        t = 2 * abs(x_top - x_bottom)
    else:
        w = index / multiple * 2 * np.pi
        t = (2 * np.pi) / w
    return t


def frequency(
    x: np.ndarray,
    y: np.ndarray,
    filter_window: int = 5,
    filter_dim: int = 2,
) -> float:
    r"""Get frequency of oscillating signal.

    First this tries FFT. If the true value is likely below or near the frequency resolution,
    the function tries low frequency fit with

    .. math::

        f_{\rm est} = \frac{1}{2\pi {\rm max}\left| y \right|}
            {\rm max} \left| \frac{dy}{dx} \right|

    given :math:`y = A \cos (2\pi f x + phi)`. In this mode, y data points are
    smoothed by a Savitzky-Golay filter to protect against outlier points.

    .. note::

        This function returns always positive frequency.
        This function is sensitive to the DC offset.
        This function assumes sorted, no-overlapping x values.

    Args:
        x: Array of x values.
        y: Array of y values.
        filter_window: Window size of Savitzky-Golay filter. This should be odd number.
        filter_dim: Dimension of Savitzky-Golay filter.

    Returns:
        Frequency estimation of oscillation signal.
    """
    # to run FFT x interval should be identical
    sampling_interval = np.unique(np.round(np.diff(x), decimals=20))

    if len(sampling_interval) != 1:
        # resampling with minimum xdata interval
        sampling_interval = np.min(sampling_interval)
        x_ = np.arange(x[0], x[-1], sampling_interval)
        y_ = np.interp(x_, xp=x, fp=y)
    else:
        sampling_interval = sampling_interval[0]
        x_ = x
        y_ = y

    fft_data = np.fft.fft(y_ - np.average(y_))
    freqs = np.fft.fftfreq(len(x_), sampling_interval)

    positive_freqs = freqs[freqs >= 0]
    positive_fft_data = fft_data[freqs >= 0]

    freq_guess = positive_freqs[np.argmax(np.abs(positive_fft_data))]

    if freq_guess < 1.5 / (sampling_interval * len(x_)):
        # low frequency fit, use this mode when the estimate is near the resolution
        y_smooth = signal.savgol_filter(
            y_, window_length=filter_window, polyorder=filter_dim
        )

        # no offset is assumed
        y_amp = max(np.abs(y_smooth))

        if np.isclose(y_amp, 0.0):
            # no oscillation signal
            return 0.0

        freq_guess = max(np.abs(
            np.diff(y_smooth) / sampling_interval)) / (y_amp * 2 * np.pi)

    return freq_guess


def max_height(
    y: np.ndarray,
    percentile: Optional[float] = None,
    absolute: bool = False,
) -> Tuple[float, int]:
    """Get maximum value of y curve and its index.

    Args:
        y: Array of y values.
        percentile: Return that percentile value if provided, otherwise just return max value.
        absolute: Use absolute y value.

    Returns:
        The maximum y value and index.
    """
    if percentile is not None:
        return get_height(
            y, functools.partial(np.percentile, q=percentile), absolute
        )
    return get_height(y, np.nanmax, absolute)


def min_height(
    y: np.ndarray,
    percentile: Optional[float] = None,
    absolute: bool = False,
) -> Tuple[float, int]:
    """Get minimum value of y curve and its index.

    Args:
        y: Array of y values.
        percentile: Return that percentile value if provided, otherwise just return min value.
        absolute: Use absolute y value.

    Returns:
        The minimum y value and index.
    """
    if percentile is not None:
        return get_height(
            y, functools.partial(np.percentile, q=percentile), absolute
        )
    return get_height(y, np.nanmin, absolute)


def get_height(
    y: np.ndarray,
    find_height: Callable,
    absolute: bool = False,
) -> Tuple[float, int]:
    """Get specific value of y curve defined by a callback and its index.

    Args:
        y: Array of y values.
        find_height: A callback to find preferred y value.
        absolute: Use absolute y value.

    Returns:
        The target y value and index.
    """
    if absolute:
        y_ = np.abs(y)
    else:
        y_ = y

    y_target = find_height(y_)
    index = int(np.argmin(np.abs(y_ - y_target)))

    return y_target, index


def exp_decay(x: np.ndarray, y: np.ndarray) -> float:
    r"""Get exponential decay parameter from monotonically increasing (decreasing) curve.

    This assumes following function form.

    .. math::

        y(x) = e^{\alpha x}

    We can calculate :math:`\alpha` as

    .. math::

        \alpha = \log(y(x)) / x

    To find this number, the numpy polynomial fit with ``deg=1`` is used.

    Args:
        x: Array of x values.
        y: Array of y values.

    Returns:
         Decay rate of signal.
    """
    inds = y > 0
    if np.count_nonzero(inds) < 2:
        return 0

    coeffs = np.polyfit(x[inds], np.log(y[inds]), deg=1)

    return float(coeffs[0])


def oscillation_exp_decay(
    x: np.ndarray,
    y: np.ndarray,
    filter_window: int = 5,
    filter_dim: int = 2,
    freq_guess: Optional[float] = None,
) -> float:
    r"""Get exponential decay parameter from oscillating signal.

    This assumes following function form.

    .. math::

        y(x) = e^{\alpha x} F(x),

    where :math:`F(x)` is arbitrary oscillation function oscillating at ``freq_guess``.
    This function first applies a Savitzky-Golay filter to y value,
    then run scipy peak search to extract peak positions.
    If ``freq_guess`` is provided, the search function will be robust to fake peaks due to noise.
    This function calls :py:func:`exp_decay` function for extracted x and y values at peaks.

    .. note::

        y values should contain more than one cycle of oscillation to use this guess approach.

    Args:
        x: Array of x values.
        y: Array of y values.
        filter_window: Window size of Savitzky-Golay filter. This should be odd number.
        filter_dim: Dimension of Savitzky-Golay filter.
        freq_guess: Optional. Initial frequency guess of :math:`F(x)`.

    Returns:
         Decay rate of signal.
    """
    y_smoothed = signal.savgol_filter(
        y, window_length=filter_window, polyorder=filter_dim
    )

    if freq_guess is not None and np.abs(freq_guess) > 0:
        period = 1 / np.abs(freq_guess)
        dt = np.mean(np.diff(x))
        width_samples = int(np.round(0.8 * period / dt))
        if width_samples < 1:
            width_samples = 1
    else:
        width_samples = 1

    peak_pos, _ = signal.find_peaks(y_smoothed, distance=width_samples)

    if len(peak_pos) < 2:
        return 0.0

    x_peaks = x[peak_pos]
    y_peaks = y_smoothed[peak_pos]

    return exp_decay(x_peaks, y_peaks)


def constant_sinusoidal_offset(y: np.ndarray) -> float:
    """Get constant offset of sinusoidal signal.

    This function finds 95 and 5 percentile y values and take an average of them.
    This method is robust to the dependency on sampling window, i.e.
    if we sample sinusoidal signal for 2/3 of its period, simple averaging may induce
    a drift towards positive or negative direction depending on the phase offset.

    Args:
        y: Array of y values.

    Returns:
        Offset value.
    """
    maxv, _ = max_height(y, percentile=95.)
    minv, _ = min_height(y, percentile=5.)

    return 0.5 * (maxv + minv)


def constant_spectral_offset(
    y: np.ndarray,
    filter_window: int = 5,
    filter_dim: int = 2,
    ratio: float = 0.1
) -> float:
    """Get constant offset of spectral baseline.

    This function searches constant offset by finding a region where 1st and 2nd order
    differentiation are close to zero. A return value is an average y value of that region.
    To suppress the noise contribution to derivatives, this function also applies a
    Savitzky-Golay filter to y value.

    This method is more robust to offset error than just taking median or average of y values
    especially when a peak width is wider compared to the scan range.

    Args:
        y: Array of y values.
        filter_window: Window size of Savitzky-Golay filter. This should be odd number.
        filter_dim: Dimension of Savitzky-Golay filter.
        ratio: Threshold value to decide flat region. This value represent a ratio
            to the maximum derivative value.

    Returns:
        Offset value.
    """
    y_smoothed = signal.savgol_filter(
        y, window_length=filter_window, polyorder=filter_dim
    )

    ydiff1 = np.abs(np.diff(y_smoothed, 1, append=np.nan))
    ydiff2 = np.abs(np.diff(y_smoothed, 2, append=np.nan, prepend=np.nan))
    non_peaks = y_smoothed[(ydiff1 < ratio * np.nanmax(ydiff1)) &
                           (ydiff2 < ratio * np.nanmax(ydiff2))]

    if len(non_peaks) == 0:
        return float(np.median(y))

    return np.average(non_peaks)


def full_width_half_max(
    x: np.ndarray,
    y: np.ndarray,
    peak_index: int,
) -> float:
    """Get full width half maximum value of the peak. Offset of y should be removed.

    Args:
        x: Array of x values.
        y: Array of y values.
        peak_index: Index of peak.

    Returns:
        FWHM of the peak.

    Raises:
        AnalysisError: When peak is too broad and line width is not found.
    """
    y_ = np.abs(y)
    peak_height = y_[peak_index]
    halfmax_removed = np.sign(y_ - 0.5 * peak_height)

    try:
        r_bound = np.min(x[(halfmax_removed == -1) & (x > x[peak_index])])
    except ValueError:
        r_bound = None
    try:
        l_bound = np.max(x[(halfmax_removed == -1) & (x < x[peak_index])])
    except ValueError:
        l_bound = None

    if r_bound and l_bound:
        return r_bound - l_bound
    elif r_bound:
        return 2 * (r_bound - x[peak_index])
    elif l_bound:
        return 2 * (x[peak_index] - l_bound)
    else:
        # return default value.
        return 0.0


def sum_of_squared_error(popt, x, y, func):
    warnings.filterwarnings("ignore")
    y_predicted = func(x, *popt)
    loss = np.sum((y - y_predicted) ** 2)
    return loss


def guess_params(x, y, bounds, func):
    result = differential_evolution(
        sum_of_squared_error,
        bounds,
        strategy="best1bin",
        mutation=(0.1, 1.8),
        init="latinhypercube",
        seed=None,
        args=(x, y, func),
    )
    return result.x
