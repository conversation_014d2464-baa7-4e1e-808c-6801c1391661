# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/15
# __author:       <PERSON><PERSON><PERSON> <PERSON>
"""Analyze oscillating data such as a Ramsey experiment."""

from typing import List, Union

import numpy as np
from scipy.optimize import curve_fit

from ...log import pyqlog
from ...structures import Options, QDict
from ..algorithms import exp_decay, fourier_cycle_guess, guess
from ..curve_fit_analysis import (
    CurveAnalysisData,
    CurveFitAnalysis,
    FitModel,
    FitOptions,
)
from ..fit.fit_models import cos_decay_func, cos_func, exponential_fit
from ..specification import ParameterRepr


class OscillationAnalysis(CurveFitAnalysis):
    r"""Oscillation analysis class based on a fit of the data to a cosine function."""

    @classmethod
    def _default_options(cls) -> Options:
        """Return the default analysis options."""
        options = super()._default_options()
        options.fit_model = FitModel(
            fit_func=cos_func,
            model_description=r"{\rm amp} \cos\left(2 \pi\cdot {\rm freq}\cdot x "
            r"+ {\rm phase}\right) + {\rm base}",
        )
        return options

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Use fft guess initial fit parameters.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        x = data.x
        y = data.y

        freq, phase, amp, baseline = guess.cosine_fit_guess(x, y)
        if not isinstance(freq, List):
            freq = [freq]

        fit_opt.p0.set_if_empty(phase=phase, base=baseline)

        fit_opt_list = []
        for f in freq:
            for p_amp in [amp, max(y) - min(y)]:
                fo = fit_opt.copy()
                fo.p0.set_if_empty(amp=p_amp, freq=f)
                fit_opt_list.append(fo)

        return fit_opt_list

    def calculate(self, x: float, fit_params):
        return self.options.fit_model.fit_func(x, **fit_params)


class DumpedOscillationAnalysis(CurveFitAnalysis):
    r"""A class to analyze general exponential decay curve with sinusoidal oscillation."""

    @classmethod
    def _default_options(cls) -> Options:
        """Return the default analysis options."""
        options = super()._default_options()

        options.fit_model = FitModel(
            fit_func=cos_decay_func,
            model_description=r"$amp\cdot e^ {-\tfrac{x}{tau}} \cdot \cos "
            r"\left(2\pi freq\cdot x+\varphi \right ) + base$",
        )

        return options

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        curve_data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Use fft guess initial frequency.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        x = curve_data.x
        y = curve_data.y

        # Default guess values
        df = 1 / ((x[1] - x[0]) * len(x))
        max_abs_y, _ = guess.max_height(curve_data.y, absolute=True)

        f0 = 1 / guess.fourier_cycle_guess(x, y)
        base = guess.constant_sinusoidal_offset(y)
        if f0 > df:
            alpha = guess.oscillation_exp_decay(
                curve_data.x, curve_data.y - base, freq_guess=float(f0)
            )
        else:
            # Very low frequency. Assume standard exponential decay
            alpha = guess.exp_decay(x, y)

        if alpha != 0.0:
            fit_opt.p0.set_if_empty(tau=abs(-1 / alpha))
        else:
            # Likely there is no slope. Cannot fit constant line with this model.
            # Set some large enough number against to the scan range.
            fit_opt.p0.set_if_empty(tau=100 * np.max(x))

        # set initial parameters.
        fit_opt.p0.set_if_empty(freq=f0, amp=(np.max(y) - np.min(y)) / 2, base=base)

        # set fit bounds.
        fit_opt.bounds.set_if_empty(
            amp=(0, 2 * max_abs_y),
            tau=(0, np.inf),
            # base=(0, max_abs_y),
            base=(min(y), max(y)),
            phi=(-np.pi, np.pi),
            freq=(0, np.inf),
        )

        # more robust estimation
        options = []
        for phi in np.linspace(-np.pi, np.pi, 5)[:-1]:
            new_opt = fit_opt.copy()
            new_opt.p0.set_if_empty(phi=phi)
            options.append(new_opt)

        return options


class RamseySegmBaseAnalysis(CurveFitAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Return the default analysis options."""
        options = super()._default_options()
        options.x_label = "Delay [ns]"
        options.quality_bounds = [0.98, 0.93, 0.81]
        options.result_parameters = [
            ParameterRepr("tau", "T2star", "us"),
            ParameterRepr("freq", "fosc", "MHz"),
            "t2_rate",
        ]
        options.fit_model = FitModel(
            fit_func=exponential_fit,
            model_description=r"amp \exp(-x/tau) + base",
        )
        options.coe_dict = None
        options.osc_freq = None
        return options

    @staticmethod
    def _get_fit_params(x_data, y_data):
        phase_list = np.linspace(-np.pi, np.pi, 5)
        max_value = np.max(y_data)
        min_value = np.min(y_data)
        rmse = {}
        params = {}
        t = 1 / fourier_cycle_guess(x_data, y_data)
        for phase in phase_list:
            initial_guess = [
                0.5 * (max_value - min_value),
                t,
                phase,
                0.5 * (max_value + min_value),
            ]
            lower_bounds = [0, 0, -np.pi, 0]
            upper_bounds = [1, np.inf, np.pi, 1]
            try:
                params[phase], covariance = curve_fit(
                    cos_func,
                    x_data,
                    y_data,
                    p0=initial_guess,
                    bounds=(lower_bounds, upper_bounds),
                )

                residuals = y_data - cos_func(x_data, *params[phase])
                rss = np.sum(residuals**2)
                rmse[phase] = np.sqrt(rss / len(x_data))
            except:
                pass
        min_key = min(rmse, key=rmse.get)
        return params[min_key]

    @staticmethod
    def _cal_rmse(x_data, y_data, params):
        predicted_values = cos_func(x_data, *params)
        residuals = y_data - predicted_values
        rmse = np.sqrt(np.mean(residuals**2)) / (max(y_data) - min(y_data))
        return rmse

    def _create_analysis_data(self) -> QDict:
        """Create Analysis data provided for detailed analyze.

        Returns:
            A QDict object, key represents data type and value is
            CurveAnalysisData object.
        """
        analysis_data_dict = QDict()
        coe_dict = self.options.coe_dict
        keys_array = np.array(list(coe_dict.keys()))
        values_array = np.array(list(coe_dict.values()))
        data_key = list(
            self.options.get("data_key", None) or self._experiment_data.y_data.keys()
        )
        for key in data_key:
            if key in self.experiment_data.y_data:
                analysis_data = CurveAnalysisData(
                    x=keys_array,
                    y=values_array,
                )
                analysis_data_dict[key] = analysis_data
        return analysis_data_dict

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Guess initial fit parameters.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        coe_dict = self.options.coe_dict
        keys_array = np.array(list(coe_dict.keys()))
        values_array = np.array(list(coe_dict.values()))
        alpha = exp_decay(keys_array, values_array)

        tau = abs(-1 / alpha)
        fit_opt.p0.set_if_empty(
            amp=max(values_array), lamb=1 / tau, baseline=min(values_array)
        )
        return fit_opt

    @staticmethod
    def find_nearest_index(x_data, x_T):
        nearest_index = np.argmin(np.abs(x_data - x_T))
        return nearest_index

    def _cal_coe_value(self, start, end, accum, p0_list):
        coe_dict = {}
        x_data = self.experiment_data.x_data[1:]
        while end < len(x_data):
            x = x_data[start:end]
            y = p0_list[start:end]
            try:
                params = self._get_fit_params(x, y)
                x1 = np.linspace(min(x), max(x), 100)
                y1 = cos_func(x1, *params)
                max_index = np.argmax(y1)
                x_max = x1[max_index]
                coe_dict[x[self.find_nearest_index(x, x_max)]] = cos_func(
                    x_max, *params
                )

            except Exception as e:
                pyqlog.warning(f"fit error, error msg:{e}")
            finally:
                start = end
                end += accum
        self.set_options(coe_dict=coe_dict)

    def _pre_analysis(self):
        x_data = self.experiment_data.x_data[1:]
        y_data = self.experiment_data.y_data
        p0_list = y_data.get("P0")[1:]
        p0_max = max(p0_list)
        p0_min = min(p0_list)
        period = (p0_max - p0_min) * 2
        y_intervals = abs(np.diff(p0_list))
        index = {}
        for i in range(1, len(y_intervals)):
            index[i] = np.sum(y_intervals[:i])
        closest_key = min(index, key=lambda x: abs(index[x] - period))
        x = x_data[: closest_key + 1]
        y = p0_list[: closest_key + 1]
        params = self._get_fit_params(x, y)
        self.set_options(osc_freq=params[1])
        x1 = np.linspace(min(x), max(x), 100)
        y1 = cos_func(x1, *params)
        max_index = np.argmax(y1)
        x1_max = x1[max_index]
        x_T = x1_max * 2
        nearest_index = self.find_nearest_index(x, x_T)
        end = nearest_index + 1
        accum = nearest_index + 1
        self._cal_coe_value(0, end, accum, p0_list)

    def _cal_t2_rate(self):
        t2 = self.results.tau.value * 1e3
        max_delay = self.experiment_data.x_data[-1]
        return t2 / max_delay

    def _extract_result(self, data_key: str):
        """Convert frequency from GHz to MHz.

        Args:
            data_key (str): The basis for selecting data.
        """
        params = self.analysis_datas.get("P0").fit_data.popt
        self.results.tau.value = 1 / params[1] / 1e3
        result = self.results.freq

        osc_freq = self.options.osc_freq
        result.value = osc_freq * 1e3
        result = self.results.t2_rate
        result.value = self._cal_t2_rate()
