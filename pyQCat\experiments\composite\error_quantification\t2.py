# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/30
# __author:       <PERSON><PERSON><PERSON>
# __corporation:  OriginQuantum

"""
T2 experiment is used to get the qubit decoherence time.
"""

from copy import deepcopy

from ....analysis.library import T2R<PERSON>eyAnalysis, T2RamseySegmAnalysis
from ....concurrent.worker.analysis_interface import _save_analysis_data_and_picture
from ....log import pyqlog
from ....parameters import options_wrapper
from ....qaio_property import QAIO
from ....structures import MetaData, Options
from ....tools import qarange
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import Co<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, SpinEcho


class T2Base(CompositeExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            delays (List, np.ndarray): The list of delays that will
                                           be scanned in the experiment,
                                           in nanoseconds.
            fringe (float): A frequency shift in Hz that will be applied
                            by means of a virtual Z rotation to increase
                            the frequency of the measured oscillation.
            z_amp (float): A ac pulse amplitude.
        """
        options = super()._default_experiment_options()

        options.set_validator("delays", list)
        options.set_validator("fringe", float)
        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("rate_down", (0, 1, 2))
        options.set_validator("rate_up", (0, 1, 2))
        options.set_validator("max_loops", [1, 2, 3, 4, 5])
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.delays = qarange(200, 20000, 200)
        options.fringe = 0.5  # MHz
        options.z_amp = None

        options.rate_down = 0.3
        options.rate_up = 0.5
        options.max_loops = 5
        options.run_mode = ExperimentRunMode.sync_mode

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Define run_options parameters."""
        options = super()._default_run_options()
        options.frequency = None
        options.best_max_delay = None
        options.link_child_exp = True
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("fit_type", ["osc", "segm"])

        options.fit_type = "osc"
        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        fit_type = self.analysis_options.fit_type
        if fit_type == "segm":
            analysis_class = T2RamseySegmAnalysis
        else:
            analysis_class = T2RamseyAnalysis
        self.child_experiment.set_run_options(analysis_class=analysis_class)

        if self.child_experiment.is_coupler_exp is True:
            result_name = self.child_experiment.coupler.name
        elif self.child_experiment.qubit:
            result_name = self.child_experiment.qubit.name
        else:
            result_name = None
        self.set_analysis_options(result_name=result_name)

    def _metadata(self) -> MetaData:
        """Create MetaData."""
        metadata = super()._metadata()
        metadata.draw_meta = {}
        return metadata

    @staticmethod
    def guess_delays(min_delay: float, max_delay: float):
        """Guess delay, fringe of T2Ramsey Experiment."""
        max_delay = (max_delay // 100 + 1) * 100
        t = (max_delay - min_delay) / 10
        step = (t / 10 // 5 + 1) * 5
        max_delay = min_delay + step * 100
        t = step * 10
        fringe = round(1e3 / t, 3)  # MHz
        delays = qarange(min_delay, max_delay, step)

        baseband_freq = 0  # when XY Line Unbinding baseband_freq
        # if self.child_experiment.is_coupler_exp is False:
        #     baseband_freq = self.child_experiment.qubit.XYwave.baseband_freq
        # else:
        #     baseband_freq = self.child_experiment.couplers[0].drive_XYwave.baseband_freq

        # real_fringe = round(baseband_freq - fringe, 3)
        real_fringe = round(baseband_freq + fringe, 3)  # edited by WTL
        return real_fringe, delays

    def _cal_rate_gap(self, t2_rate: float) -> float:
        """Calculate the min distance t2_rate to rate_threshold."""
        rate_down = self.experiment_options.rate_down
        rate_up = self.experiment_options.rate_up

        down_gap = abs(rate_down - t2_rate)
        up_gap = abs(rate_up - t2_rate)
        min_gap = min([down_gap, up_gap])
        return min_gap

    async def _sync_composite_run(self):
        """Run precise qubit_test T2."""
        rate_down = self.experiment_options.rate_down
        rate_up = self.experiment_options.rate_up
        max_loops = self.experiment_options.max_loops

        z_amp = self.experiment_options.z_amp
        fringe = self.experiment_options.fringe
        delays = self.experiment_options.delays

        pyqlog.log(
            "EXP",
            f"rate_down: {rate_down}, rate_up: {rate_up}, max_loops: {max_loops}",
        )

        best_result = None
        best_gap = None

        count = 0
        ramsey_exp = None
        best_exp = None
        best_max_delay = delays[-1]

        if self.child_experiment.is_coupler_exp is False:
            if self.child_experiment.qubit:
                fd = self.child_experiment.qubit.drive_freq
            elif "tq_obj" in self.run_options:
                fd = self.run_options.tq_obj.drive_freq
            else:
                fd = self.child_experiment.qubits[0].drive_freq
        else:
            fd = self.child_experiment.couplers[0].drive_freq
        frequency = self.run_options.frequency or self.experiment_options.frequency
        while count < max_loops:
            if frequency and isinstance(self, T2Ramsey):
                df = frequency - fd
                if QAIO.type in [QAIO.qaio_8, QAIO.qaio_30]:
                    fringe = round(abs(fringe) - df, 3)
                else:
                    fringe = round(abs(fringe) + df, 3)

            ramsey_exp = deepcopy(self.child_experiment)
            ramsey_exp.set_parent_file(
                self, f"count={count}-z_amp={z_amp}-fringe={fringe}", count
            )

            ramsey_exp.set_experiment_options(
                z_amp=z_amp,
                fringe=fringe,
                delays=delays,
            )

            self._check_simulator_data(ramsey_exp, count)

            await ramsey_exp.run_experiment()
            # ramsey_exp.clear_params()
            ramsey_exp.check_exp_is_done(self.run_options.link_child_exp)

            self._experiments.append(ramsey_exp)

            tau = ramsey_exp.analysis.results.tau.value
            t2_rate = ramsey_exp.analysis.results.t2_rate.value

            min_delay = delays[0]
            max_delay = delays[-1]

            if rate_down <= t2_rate <= rate_up:
                best_result = ramsey_exp.analysis.results
                best_exp = ramsey_exp
                break
            elif t2_rate < 0:
                max_delay = max_delay * 1.5
            else:
                max_delay = (tau * 1e3) / 0.4
                if max_delay > 30000:
                    max_delay = 30000

            fringe, delays = self.guess_delays(min_delay, max_delay)
            # _, delays = self.guess_delays(min_delay, max_delay)
            cur_rate_gap = self._cal_rate_gap(t2_rate)

            if best_result is None:
                best_result = ramsey_exp.analysis.results
                best_exp = ramsey_exp

            if best_gap is None:
                best_gap = cur_rate_gap
                best_max_delay = max_delay
            elif cur_rate_gap < best_gap:
                best_gap = cur_rate_gap
                best_result = ramsey_exp.analysis.results
                best_exp = ramsey_exp
                best_max_delay = max_delay

            count += 1

        # Old version.
        # if ramsey_exp is not None and hasattr(ramsey_exp, "analysis"):
        #     self._analysis = ramsey_exp.analysis
        #     self._analysis._results = best_result
        #     self.experiment_data = best_exp.experiment_data
        #     self._save_curve_analysis_plot()
        #
        #     # feature(from wtl): Save the optimal result in father file at the end of the experiment
        #     ramsey_exp.file = self.file
        #     ramsey_exp._save_source_data(label=self.label)

        # New version adjust, 2024.05.21
        if best_exp is not None and hasattr(best_exp, "analysis"):
            self._analysis = best_exp.analysis
            _save_analysis_data_and_picture(
                self.analysis, self.file, save_s3=self.analysis_options.save_s3
            )

        self.run_options.best_max_delay = best_max_delay


@options_wrapper
class T2Ramsey(T2Base):
    """T2 Ramsey experiment."""

    _sub_experiment_class = Ramsey

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "tau":
                if self.child_experiment.is_coupler_exp is True:
                    result.extra["path"] = "Coupler.T2"
                else:
                    result.extra["path"] = "Qubit.T2"


@options_wrapper
class T2SpinEcho(T2Base):
    """T2 SpinEcho experiment."""

    _sub_experiment_class = SpinEcho

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "tau":
                if self.child_experiment.is_coupler_exp is True:
                    result.extra["path"] = "Coupler.TS2"
                else:
                    result.extra["path"] = "Qubit.TS2"


class CouplerT2Ramsey(T2Ramsey):
    """Coupler T2 Ramsey experiment."""

    _sub_experiment_class = CouplerRamsey


@options_wrapper
class T2RamseyExtend(T2Ramsey):
    """T2 Ramsey extend experiment."""

    _sub_experiment_class = RamseyExtend

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super(T2Ramsey, cls)._default_experiment_options()

        options.set_validator("delays", list)
        options.set_validator("fringe", float)
        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("rate_down", (0, 1, 2))
        options.set_validator("rate_up", (0, 1, 2))
        options.set_validator("max_loops", [1, 2, 3, 4, 5])

        options.delays = qarange(200, 20000, 200)
        options.fringe = 0.5  # MHz
        options.z_amp = None

        options.rate_down = 0.3
        options.rate_up = 0.5
        options.max_loops = 5

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Define run_options parameters."""
        options = super()._default_run_options()
        options.tq_obj = None

        return options

    def _check_options(self):
        """Check Options."""
        super(T2Ramsey, self)._check_options()
        fit_type = self.analysis_options.fit_type
        if fit_type == "segm":
            analysis_class = T2RamseySegmAnalysis
        else:
            analysis_class = T2RamseyAnalysis
        self.child_experiment.set_run_options(analysis_class=analysis_class)

        qubit_map = {qubit.name: qubit for qubit in self.qubits}
        tq_name = self.experiment_options.child_exp_options.tq_name
        bq_name_list = self.experiment_options.child_exp_options.bq_name_list
        if tq_name:
            if tq_name in ["ql", "qh"]:
                tq_name = getattr(self.qubit_pair, tq_name)
            tq_obj = qubit_map.get(tq_name)
        else:
            qubit_list = []
            for q_obj in self.qubits:
                if q_obj.name not in bq_name_list:
                    qubit_list.append(q_obj)
            if qubit_list:
                tq_obj = qubit_list[0]
            else:
                tq_obj = None

        result_name = tq_obj.name
        self.set_run_options(tq_obj=tq_obj)
        self.set_analysis_options(result_name=result_name)
