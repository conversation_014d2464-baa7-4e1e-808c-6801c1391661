# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.init_qpt.rst:2
msgid "pyQCat.analysis.algorithms.init\\_qpt"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.init_qpt:1
msgid "Initialize quantum process tomography for an operator basis."
msgstr "为量子状态层析初始化一组操作基"

#: of pyQCat.analysis.algorithms.tomography.init_qpt
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.init_qpt:3
msgid ""
"a list of matrices giving the basis in which to compute the chi matrix "
"for process tomography.  These matrices should form a 'complete' set to "
"allow the full chi matrix to be represented, though this is not enforced."
msgstr ""
"矩阵列表，给出计算过程断层扫描的 chi 矩阵的基础。 这些矩阵应该形成一个“完整”集"
"，以允许表示完整的 chi 矩阵，尽管这不是强制的。"

#: of pyQCat.analysis.algorithms.tomography.init_qpt
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.init_qpt:8
msgid ""
"Returns a transformation matrix that should be passed to qpt along with "
"input and output density matrices to perform the process tomography."
msgstr ""
"返回应与输入和输出密度矩阵一起传递给 qpt 以执行过程断层扫描的变换矩阵。"

#~ msgid "matrices - a list of matrices giving the basis in which to compute"
#~ msgstr ""

#~ msgid ""
#~ "the chi matrix for process tomography."
#~ "  These matrices should form a "
#~ "'complete' set to allow the full "
#~ "chi matrix to be represented, though "
#~ "this is not enforced."
#~ msgstr ""

