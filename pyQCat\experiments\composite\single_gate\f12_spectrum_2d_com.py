# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/11/10
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy

from ....analysis.library import QubitSpectrum2DComAnalysis
from ....log import pyqlog
from ....structures import MetaData, Options
from ....types import QualityDescribe
from ....tools import qarange
from ...composite_experiment import CompositeExperiment
from ...single import QubitSpectrumF12_2D, RabiScanWidthF12


class QubitSpectrum2DCom(CompositeExperiment):
    """Qubit Spectrum Experiment, find frequency and judge oscillating."""

    _sub_experiment_class = QubitSpectrumF12_2D

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()

        options.set_validator("f12_xpi_list", list)
        options.set_validator("z_amp_list", list)
        options.set_validator("rabi_widths", list)

        options.f12_xpi_list = None
        options.z_amp_list = None
        options.rabi_widths = qarange(5, 200, 5)

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for QubitSpectrum experiment.

        Options:
            freq_list (List, np.ndarray): The frequency calculate
                                          by ac spectrum paras and z amp.
        """
        options = super()._default_analysis_options()
        options.x_label = "Amp [v]"
        options.fine_freq = None
        options.drvie_freq = None

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()
        options.sweep_list = None
        options.sweep_type = None
        options.rb_execute_flag = False

        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        f12_xpi_list = self.experiment_options.f12_xpi_list
        z_amp_list = self.experiment_options.z_amp_list

        if z_amp_list is not None:
            sweep_list = z_amp_list
            sweep_type = "z_amp"
        else:
            sweep_list = f12_xpi_list
            sweep_type = "f12_xpi"

        if (
            self.coupler
            and self.child_experiment.is_coupler_exp is False
        ):
            result_name = self.child_experiment.coupler.name
        else:
            result_name = self.child_experiment.qubit.name

        if self.discriminator:
            self.analysis_options.sub_data_type = "I_Q"
        else:
            self.analysis_options.sub_data_type = "amp_phase"

        self.set_analysis_options(x_label=sweep_type, result_name=result_name)
        self.set_run_options(sweep_list=sweep_list, sweep_type=sweep_type)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "f01": self.qubits[0].drive_freq if self.qubits[0] else None
        }

        return metadata

    def run(self):
        """Run Qubit Spectrum 2D Composite Experiment."""
        super().run()

        def recursive_set_options(_exp, **kwargs):
            _exp.set_run_options(**kwargs)
            if hasattr(_exp, "child_experiment"):
                child_exp = getattr(_exp, "child_experiment")
                recursive_set_options(child_exp, **kwargs)

        sweep_list = self.run_options.sweep_list
        sweep_type = self.run_options.sweep_type
        cali_freq_list = []
        for i, sweep in enumerate(sweep_list):
            qs_12_exp = deepcopy(self.child_experiment)
            qs_12_exp.set_parent_file(
                self, f"sweep_value={sweep}-sweep_type={sweep_type}", i, len(sweep_list)
            )
            if sweep_type == "z_amp":
                qs_12_exp.set_experiment_options(z_amp=sweep)
                f12_xpi = qs_12_exp.experiment_options.f12_xpi
            else:
                f12_xpi = sweep
                qs_12_exp.set_experiment_options(f12_xpi=f12_xpi)
            self._check_simulator_data(qs_12_exp, i)
            qs_12_exp.run()
            f02 = qs_12_exp.analysis.results.f02.value
            cali_freq = f02 if f02 else 0
            qs_12_exp.analysis.provide_for_parent.update({"cali_freq": cali_freq})
            self._experiments.append(qs_12_exp)

            if not cali_freq:
                pyqlog.info("QubitSpectrum scan not find freq!")
                break

            rb_execute_flag = self.run_options.rb_execute_flag
            if rb_execute_flag:
                rabi_width_exp = RabiScanWidthF12(
                    inst=self.inst,
                    qubits=self.qubits,
                    couplers=self.couplers,
                    compensates=self.compensates,
                    discriminators=self.discriminator,
                    working_dc=self.working_dc,
                    ac_bias=self.ac_bias,
                    is_paternal=self.is_paternal,
                )

                recursive_set_options(
                    rabi_width_exp,
                    config=self.run_options.config,
                    crosstalk_dict=self.run_options.crosstalk_dict,
                    online_bits=self.run_options.online_bits,
                    online_dcms=self.run_options.online_dcms,
                    online_coms=self.run_options.online_coms,
                    xy_crosstalk_dict=self.run_options.xy_crosstalk_dict,
                    env_bit_dict=self.run_options.env_bit_dict,
                    # username=self.run_options.username,
                )

                rabi_width_exp.set_parent_file(self, f"rabi_width-freq={cali_freq}")
                rabi_width_exp.set_experiment_options(
                    f12_freq=cali_freq,
                    f12_xpi=f12_xpi,
                    widths=self.experiment_options.rabi_widths,
                )
                rabi_width_exp.run()

                osc_flag = rabi_width_exp.analysis.results.oscillating.value
                osc_quality = rabi_width_exp.analysis.quality.descriptor
                pyqlog.log("RESULT", f"Run RabiWidthF12 result, osc_flag: {osc_flag}")
                if osc_quality == QualityDescribe.perfect:
                    self.set_run_options(rb_execute_flag=False)
                    cali_freq_list.append(cali_freq)
                    self.set_analysis_options(fine_freq=cali_freq)

        self._run_analysis(x_data=sweep_list, analysis_class=QubitSpectrum2DComAnalysis)
        self.file.save_data(cali_freq_list, name=f"perfect_cali_freq")
