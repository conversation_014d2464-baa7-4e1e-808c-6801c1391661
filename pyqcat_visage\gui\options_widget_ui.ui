<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TabWidget</class>
 <widget class="QTabWidget" name="TabWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>627</width>
    <height>551</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>TabWidget</string>
  </property>
  <property name="tabPosition">
   <enum>QTabWidget::TabPosition::West</enum>
  </property>
  <property name="tabShape">
   <enum>QTabWidget::TabShape::Triangular</enum>
  </property>
  <property name="currentIndex">
   <number>1</number>
  </property>
  <widget class="QWidget" name="tab_exp_options">
   <attribute name="title">
    <string>Experiment</string>
   </attribute>
   <layout class="QVBoxLayout" name="verticalLayout_2">
    <property name="leftMargin">
     <number>1</number>
    </property>
    <property name="topMargin">
     <number>1</number>
    </property>
    <property name="rightMargin">
     <number>1</number>
    </property>
    <property name="bottomMargin">
     <number>1</number>
    </property>
    <item>
     <widget class="QTreeViewOptionsWidget" name="exp_tree_view"/>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="tab_ana_options">
   <attribute name="title">
    <string>Analysis</string>
   </attribute>
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="leftMargin">
     <number>1</number>
    </property>
    <property name="topMargin">
     <number>1</number>
    </property>
    <property name="rightMargin">
     <number>1</number>
    </property>
    <property name="bottomMargin">
     <number>1</number>
    </property>
    <item>
     <widget class="QTreeViewOptionsWidget" name="ana_tree_view"/>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QTreeViewOptionsWidget</class>
   <extends>QTreeView</extends>
   <header>.widgets.tree_view_options</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
