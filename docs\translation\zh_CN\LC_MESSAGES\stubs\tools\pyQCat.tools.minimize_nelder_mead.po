# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.minimize_nelder_mead.rst:2
msgid "pyQCat.tools.minimize\\_nelder\\_mead"
msgstr ""

#: of pyQCat.tools.NM_bnd.minimize_nelder_mead
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.NM_bnd.minimize_nelder_mead:1
msgid ""
"Sequence of ``(min, max)`` pairs for each element in `x`. None is used to"
" specify no bound."
msgstr ""

