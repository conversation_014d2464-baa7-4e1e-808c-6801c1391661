# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/16
# __author:       <PERSON><PERSON><PERSON> Shi
"""Analyze oscillating data such as a T1 experiment."""
from typing import Union, List

import numpy as np

from .algorithms import guess
from .curve_analysis import CurveAnalysis, CurveAnalysisData, \
    FitModel, FitOptions
from .fit.fit_models import exponential_decay_func
from ..structures import Options


class DecayAnalysis(CurveAnalysis):
    r"""A class to analyze general exponential decay curve."""
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()
        options.fit_model = FitModel(
            fit_func=exponential_decay_func,
            model_description=r"amp \exp(-x/tau) + base",
        )
        return options

    def _guess_fit_param(
        self,
        user_opt: FitOptions,
        curve_data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Create algorithmic guess with analysis options and curve data.

        Args:
            user_opt: Fit options filled with user provided guess and bounds.
            curve_data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        user_opt.p0.set_if_empty(base=guess.min_height(curve_data.y)[0])

        alpha = guess.exp_decay(curve_data.x, curve_data.y)

        if alpha != 0.0:
            user_opt.p0.set_if_empty(
                tau=-1 / alpha,
                amp=guess.max_height(curve_data.y)[0] - user_opt.p0["base"],
            )
        else:
            # Likely there is no slope. Cannot fit constant line with this model.
            # Set some large enough number against to the scan range.
            user_opt.p0.set_if_empty(
                tau=100 * np.max(curve_data.x),
                amp=guess.max_height(curve_data.y)[0] - user_opt.p0["base"],
            )
        return user_opt
