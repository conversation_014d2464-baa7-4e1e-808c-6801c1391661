# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysisData.rst:2
msgid "pyQCat.analysis.CurveAnalysisData"
msgstr ""

#: of pyQCat.analysis.specification.CurveAnalysisData:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.analysis.specification.CurveAnalysisData:1
msgid "A dataclass to store the process of the analysis."
msgstr ""

#: of pyQCat.analysis.specification.CurveAnalysisData:5
msgid "X-values provided to the analysis."
msgstr ""

#: of pyQCat.analysis.specification.CurveAnalysisData
msgid "type"
msgstr ""

#: of pyQCat.analysis.specification.CurveAnalysisData:7
#: pyQCat.analysis.specification.CurveAnalysisData:13
msgid "numpy.ndarray"
msgstr ""

#: of pyQCat.analysis.specification.CurveAnalysisData:11
msgid "Y-values provided to the analysis."
msgstr ""

#: of pyQCat.analysis.specification.CurveAnalysisData:17
msgid "A dataclass to store the outcome of the fitting."
msgstr ""

#: of pyQCat.analysis.specification.CurveAnalysisData:19
msgid "pyQCat.analysis.specification.FitData"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysisData.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysisData.rst:21:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.CurveAnalysisData.__init__>`\\ "
"\\(\\[x\\, y\\, fit\\_data\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysisData.rst:23
msgid "Attributes"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysisData.rst:28:<autosummary>:1
msgid ":py:obj:`fit_data <pyQCat.analysis.CurveAnalysisData.fit_data>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysisData.rst:28:<autosummary>:1
msgid ":py:obj:`x <pyQCat.analysis.CurveAnalysisData.x>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysisData.rst:28:<autosummary>:1
msgid ":py:obj:`y <pyQCat.analysis.CurveAnalysisData.y>`\\"
msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.analysis.CurveAnalysisData.__init__>`\\"
#~ " \\(\\[x\\, y\\, fit\\_data\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`fit_data <pyQCat.analysis.CurveAnalysisData.fit_data>`\\"
#~ msgstr ""

#~ msgid ":obj:`x <pyQCat.analysis.CurveAnalysisData.x>`\\"
#~ msgstr ""

#~ msgid ":obj:`y <pyQCat.analysis.CurveAnalysisData.y>`\\"
#~ msgstr ""

