# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.instrument.socket_service.rst:2
msgid "pyQCat.instrument.socket\\_service package"
msgstr ""

#: ../../source/api/pyQCat.instrument.socket_service.rst:5
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.instrument.socket_service.rst:8
msgid "pyQCat.instrument.socket\\_service.tcp\\_client module"
msgstr ""

#: of pyQCat.instrument.socket_service.tcp_client.TcpClient:1
#: pyQCat.instrument.socket_service.tcp_server.TcpServer:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: ../../source/api/pyQCat.instrument.socket_service.rst:16
msgid "pyQCat.instrument.socket\\_service.tcp\\_server module"
msgstr ""

#: ../../source/api/pyQCat.instrument.socket_service.rst:24
msgid "Module contents"
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

