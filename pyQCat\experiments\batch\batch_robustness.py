# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/02/10
# __author:       <PERSON><PERSON><PERSON>

import json
import os
import re
from collections import defaultdict
from enum import Enum
from itertools import product
from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np
from loguru import logger

from ...errors import ExperimentOptionsError
from ...executor.context_manager import deepen_set_param_value
from ...processor.chip_data import ChipPhysicalUnit
from ...qubit import NAME_PATTERN
from ..batch_experiment import BatchExperiment, Dict, List, Union


class FieldMode(Enum):
    CROSS = "cross"
    COMBINE = "combine"


class BatchRobustness(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.set_validator("mode", [FieldMode.COMBINE.value, FieldMode.CROSS.value])
        options.mode = FieldMode.COMBINE
        options.customer_map = {}
        options.scope_map = {}
        options.record_batch = True
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.group_map = {}
        options.scan_keys = {}
        options.scan_values = {}
        options.dir_describe = {}
        options.robustness_records = {}
        options.result_path = None
        return options

    def _batch_up(self):
        super()._batch_up()

        # base validator
        validate_pass = True
        scope_map = self.experiment_options.scope_map
        customer_map = self.experiment_options.customer_map
        mode = self.experiment_options.mode
        if not scope_map and not customer_map:
            validate_pass, msg = False, "No scanning parameters"
        if validate_pass is False:
            raise ExperimentOptionsError(self._label, msg=msg)

        # parallel group
        unit = self.experiment_options.physical_units[0]
        if re.match(NAME_PATTERN.qubit, unit):
            bit_type = ChipPhysicalUnit.qubit
            # auto parallel divide
            group_map = self.parallel_allocator_for_qc(
                self.experiment_options.physical_units
            )
        elif re.match(NAME_PATTERN.qubit_pair, unit):
            bit_type = ChipPhysicalUnit.pair
            # auto parallel divide
            group_map = self.parallel_allocator_for_cgc(
                self.experiment_options.physical_units
            )
        else:
            raise ExperimentOptionsError(self._label, msg="Only support qubit and pair")

        # checkout scan field
        scan_map = {}
        if scope_map:
            scan_map = {
                unit: {k: None}
                for k in scope_map
                for unit in self.experiment_options.physical_units
            }
            for k, v in scope_map.items():
                field_map = self.backend.chip_data.physical_unit_field_map(k, bit_type)
                for unit in self.experiment_options.physical_units:
                    cv = field_map[unit]
                    l, r, p = v
                    scan_arr = np.linspace(cv - l, cv + r, p)
                    if "freq" in k:
                        scan_arr = np.round(scan_arr, 3)
                    scan_map[unit][k] = list(scan_arr)
        else:
            scan_map = customer_map

        scan_keys, scan_values = {}, {}
        for unit, scan_dict in scan_map.items():
            scan_keys[unit] = list(scan_dict.keys())
            if mode == FieldMode.CROSS:
                scan_values[unit] = list(product(*scan_dict.values()))
            else:
                scan_values[unit] = list(zip(*scan_dict.values()))

        robustness_records = {}
        for unit in self.experiment_options.physical_units:
            robustness_records[unit] = {}

        result_path = Path(Path(self.run_options.record_path).parent, "result")
        result_path.mkdir(exist_ok=True)

        self.set_run_options(
            result_path=result_path,
            group_map=group_map,
            scan_keys=scan_keys,
            scan_values=scan_values,
            robustness_records=robustness_records,
        )

    def _count_max_sweep_count(self, parallel_units: List[str]):
        return max(
            [len(self.run_options.scan_values.get(unit)) for unit in parallel_units]
        )

    def _change_field(self, i: int, parallel_units: List[str]):
        # todo: refresh chip data
        # self.backend.refresh()
        working_units = []
        for unit in parallel_units:
            if len(self.run_options.scan_values[unit]) > i:
                unit_opj = self.backend.chip_data.get_physical_unit(unit)
                names = []
                for ki, key in enumerate(self.run_options.scan_keys[unit]):
                    v = self.run_options.scan_values[unit][i][ki]
                    deepen_set_param_value(unit_opj, key, v)
                    names.append(f"{key}={v}")
                self.run_options.dir_describe[unit] = " ".join(names)
                working_units.append(unit)
                logger.log(
                    "EXP",
                    f"{unit} | ({i + 1}/{len(self.run_options.scan_values[unit])}) | {self.run_options.dir_describe[unit]}",
                )
        return working_units

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)
        for unit, result in record["analysis_data"].items():
            key = self.run_options.dir_describe[unit]
            if key not in self.run_options.robustness_records[unit]:
                self.run_options.robustness_records[unit][key] = {}
            self.run_options.robustness_records[unit][key].update({exp_name: result})
        return record

    def _record_state(self):
        record_path = Path(self.run_options.result_path, "robustness_records.json")
        with open(str(record_path), mode="w", encoding="utf-8") as fp:
            json.dump(
                self.run_options.robustness_records, fp, indent=4, ensure_ascii=False
            )

    @staticmethod
    def plot_records(robustness_records: Union[str, Dict], save_path: str):
        if isinstance(robustness_records, str) and os.path.isfile(robustness_records):
            with open(robustness_records, mode="r") as f:
                robustness_records: Dict = json.load(f)
        plot_data = {}
        for unit, unit_records in robustness_records.items():
            data_records = defaultdict(list)
            for _, flow_records in unit_records.items():
                for exp_name, exp_records in flow_records.items():
                    data_records[".".join([exp_name, "quality"])].append(
                        exp_records["quality"]
                    )
                    for k, v in exp_records["result"].items():
                        if not isinstance(v, str):
                            data_records[".".join([exp_name, k])].append(v)
            plot_data[unit] = data_records
        plot_history(plot_data, save_path)

    def _run_analysis(self):
        self.plot_records(
            self.run_options.robustness_records, str(self.run_options.result_path)
        )

    def _run_batch(self):
        # todo parallel group
        for gn, group in self.run_options.group_map.items():
            sweep_count = self._count_max_sweep_count(group)

            for i in range(sweep_count):
                # change working point
                working_units = self._change_field(i, group)

                # run exp flow
                self._run_flow(
                    physical_units=working_units,
                    flows=self.experiment_options.flows,
                    name=f"{gn} robustness {i}",
                )

                # record working point state
                self._record_state()


def plot_history(data: Dict, save_path: str):
    for unit, records in data.items():
        # extract attribute
        attributes = list(records.keys())

        # Dynamically create subgraphs
        num_attributes = len(attributes)
        fig, axes = plt.subplots(num_attributes, 1, figsize=(10, 5 * num_attributes))

        # If there is only one attribute, axes is not a list and needs to be converted to a list
        if num_attributes == 1:
            axes = [axes]

        # Traverse each attribute and draw a chart
        for i, attr in enumerate(attributes):
            ax = axes[i]
            values = records[attr]
            length = len(values)
            ax.plot(values, marker="o", label=attr)
            ax.set_xticks(np.arange(0, length, int(length / 10) or 1))
            ax.set_ylabel(attr)
            ax.set_xlabel("Robustness | Index")
            ax.legend()

        # Adjust layout
        plt.tight_layout()

        # Display chart
        png_path = os.path.join(save_path, f"{unit}-robustness.png")
        fig.savefig(png_path)

        plt.close()
