# -*- coding: utf-8 -*-

# This code is part of QStream.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/05/31
# __author:       HanQing Shi

import numpy as np

from ...errors import AnalysisFieldError


def smooth(
    x: np.ndarray, window_length: int = 11, window: str = "hanning"
) -> np.ndarray:
    """smooth the data using a window with requested size.

    This method is based on the convolution of a scaled window with the signal.
    The signal is prepared by introducing reflected copies of the signal
    (with the window size) in both ends so that transient parts are minimized
    in the begining and end part of the output signal.

    For the process of convolution smoothing, please refer to todo

    Args:
        x: the input signal
        window_length: the dimension of the smoothing window; should be an odd integer
        window: the type of window from 'flat', 'hanning', 'hamming', 'bartlett', 'blackman'
            flat window will produce a moving average smoothing.

    Raises:
        SmoothError: The window length must be an odd number
        SmoothError: This function only supports one-dimensional signals
        SmoothError: The signal length shall not be less than the window length

    Returns:
        Smoothed signal
    """
    if int(window_length) & 0x1 == 0:
        window_length += 1

    if x.ndim != 1:
        raise AnalysisFieldError(
            "smooth-x-data-ndim",
            x.ndim,
            expect=1,
            msg="smooth only accepts 1 dimension arrays.",
        )

    if x.size < window_length:
        raise AnalysisFieldError(
            "smooth-x-data-size",
            x.size,
            msg=f"Input vector needs to be bigger than window size ({window_length}).",
        )

    if window_length < 3:
        return x

    if window not in ["flat", "hanning", "hamming", "bartlett", "blackman"]:
        raise AnalysisFieldError(
            "smooth-window",
            window,
            expect=["flat", "hanning", "hamming", "bartlett", "blackman"],
        )

    # Connecting two matrices by columns' means that the two matrices are added up and down,
    # and the number of columns is required to be equal.
    s = np.r_[x[window_length - 1 : 0 : -1], x, x[-1:-window_length:-1]]

    # calculate window weight
    if window == "flat":
        w = np.ones(window_length, "d")
    else:
        w = eval("np." + window + "(window_length)")

    # convolve operation
    y = np.convolve(w / w.sum(), s, mode="valid")

    # Cut edges of y since a mirror image is used
    edge = int((window_length - 1) / 2)
    return y[edge:-edge]


def smooth3rd(x, win_length):
    x = np.array(x).flatten()
    x = np.r_[x[:win_length], smooth(x, win_length)[win_length:]]
    y = x[int(win_length / 2) : int(win_length / 2) + win_length]
    y = smooth(y, int(win_length / 6) * 2 + 1)
    x[int(win_length / 2) : int(win_length / 2) + win_length] = y
    return x
