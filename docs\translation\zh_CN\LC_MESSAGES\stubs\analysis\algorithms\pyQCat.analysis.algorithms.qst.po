# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.qst.rst:2
msgid "pyQCat.analysis.algorithms.qst"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qst:1
msgid "Convert a set of diagonal measurements into a density matrix."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qst
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qst:3
msgid ""
"Measured probabilities (diagonal elements) after acting on the state with"
" each of the unitaries from the qst protocol."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qst:5
msgid ""
"Transformation matrix from init_qst for this protocol, or key passed to "
"init_qst under which the transformation was saved."
msgstr ""

