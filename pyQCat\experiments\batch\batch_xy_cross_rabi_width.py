# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/20
# __author:       <PERSON> Fang

"""
Use BatchExperiment method, develop XYCrossRabiWidth logic.
"""

from copy import deepcopy
from dataclasses import asdict, dataclass, field
from pathlib import Path

import numpy as np

from ...analysis.library.xy_cross_rw_analysis import XYCrossRwAnalysis
from ...concurrent.worker.analysis_interface import run_analysis_process
from ...log import logger
from ...qubit.qubit import Qubit, XYwave
from ...structures import ExperimentData, MetaData, Options
from ...tools.savefile import create_file
from ...tools.utilities import qarange
from ..base_experiment import BaseExperiment
from ..batch_experiment import BatchExperiment


@dataclass
class NoteParams:
    """Note some parameters."""

    drive_freq: float = 4500
    drive_power: float = -30.0
    xy_wave: XYwave = field(default_factory=XYwave)
    trust: bool = True
    osci_freq: float = 20.0
    current: str = ""
    error: str = ""
    bias_cross_dict: dict = field(default_factory=dict)

    def to_dict(self):
        """Convert object to dict structure."""
        composite_attrs = ["xy_wave"]
        data_dict = asdict(self)
        for attr, value in data_dict.items():
            if attr in composite_attrs and hasattr(value, "__dict__"):
                data_dict.update({attr: value.__dict__})
        return data_dict


class BatchXYCrossRabiWidth(BatchExperiment):
    """Batch XYCrossRabiWidth logic."""

    @classmethod
    def _default_experiment_options(cls) -> "Options":
        options = super()._default_experiment_options()

        options.target_bias_map = {}

        options.cali_freq_flag = True
        options.update_target_strength = True
        options.select_coe_threshold = 0.001

        options.bias_widths_1 = qarange(5, 10000, 250)
        options.bias_widths_2 = qarange(5, 2000, 25)
        options.bias_expect_1 = [0.1, 1.0]
        options.bias_expect_2 = [0.5, 10]

        options.cali_freq_flows = ["QubitFreqCalibration"]
        options.strength_flows = ["RabiScanWidth"]
        options.xy_cross_flows = ["XYCrossRabiWidthOnce"]
        options.flows = []  # No use
        options.affect_next_node = False
        options.max_distance = 5

        return options

    @classmethod
    def _default_run_options(cls) -> "Options":
        options = super()._default_run_options()

        options.t_names = []
        options.group_map = {}
        options.note_params_map = {}
        options.cross_coe_map = {}
        options.cross_trust_map = {}

        return options

    def _check_options(self):
        super()._check_options()

        if (
            not self.experiment_options.target_bias_map
            and self.experiment_options.physical_units
        ):
            logger.info("No define target bias map, auto build map")
            target_bias_map = {}
            physical_units = self.experiment_options.physical_units
            for unit in physical_units:
                target_bias_map[unit] = [
                    u
                    for u in physical_units
                    if u != unit
                    and self.backend.chip_data.topology.bit_distance(u, unit)
                    < self.experiment_options.max_distance
                ]
            self.experiment_options.target_bias_map = target_bias_map

    def _batch_up(self):
        """Batch Pre-Execution Processing."""
        super()._batch_up()

        target_bias_map: dict = self.experiment_options.target_bias_map
        t_names = list(target_bias_map.keys())
        logger.info(f"All target names: {t_names}")

        group_map = self.parallel_allocator_for_qc(t_names)

        self.set_run_options(t_names=t_names, group_map=group_map)

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        """After execute experiment, note some information."""
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "RabiScanWidth" in exp_name:
            note_params_map: dict = self.run_options.note_params_map
            if isinstance(exp, BaseExperiment):
                exps = [exp]
            else:
                exps = exp.experiments

            for idx, exp_obj in enumerate(exps):
                t_name = physical_units[idx]
                t_note_obj: "NoteParams" = note_params_map.get(t_name)

                try:
                    analysis_obj = exp_obj.analysis
                    trust = analysis_obj.results.oscillating.value
                    osci_freq = analysis_obj.results.freq.value
                    error = ""
                except Exception as err:
                    logger.warning(f"{t_name} {exp_name} error: {err}")
                    trust = False
                    osci_freq = t_note_obj.osci_freq
                    error = str(err)

                t_note_obj.drive_freq = exp_obj.experiment_options.drive_freq
                t_note_obj.drive_power = exp_obj.experiment_options.drive_power
                t_note_obj.xy_wave = exp_obj.qubits[0].XYwave
                t_note_obj.trust = trust
                t_note_obj.osci_freq = osci_freq
                t_note_obj.current = "After Strength RabiWidth"
                t_note_obj.error = error
                note_params_map.update({t_name: t_note_obj})

            note_params_dict = {
                name: note_obj.to_dict() for name, note_obj in note_params_map.items()
            }
            self._save_data_to_json(note_params_dict, "note_params")

        elif "XYCrossRabiWidthOnce" in exp_name:
            note_params_map: dict = self.run_options.note_params_map
            if isinstance(exp, BaseExperiment):
                exps = [exp]
            else:
                exps = exp.experiments

            for idx, exp_obj in enumerate(exps):
                if self.run_options.config is None:
                    self.run_options.config = exp_obj.run_options.config

                t_name = exp_obj.experiment_options.rd_name
                b_name = exp_obj.experiment_options.xy_name
                t_note_obj: "NoteParams" = note_params_map.get(t_name)

                try:
                    analysis_obj = exp_obj.analysis
                    trust = analysis_obj.results.oscillating.value
                    osci_freq = analysis_obj.results.freq.value
                    error = ""
                except Exception as err:
                    logger.warning(
                        f"target {t_name} bias {b_name} {exp_name} error: {err}"
                    )
                    trust = False
                    osci_freq = 0.0
                    error = str(err)
                one_bias_dict = {
                    "drive_freq": exp_obj.experiment_options.drive_freq,
                    "drive_power": exp_obj.experiment_options.drive_power,
                    "xy_wave": exp_obj.run_options.xy_qubit.XYwave.__dict__,
                    "trust": trust,
                    "osci_freq": osci_freq,
                    "error": error,
                }
                t_note_obj.current = (
                    f"After target {t_name} bias {b_name} XYCrossRabiWidthOnce"
                )
                t_note_obj.bias_cross_dict.update({b_name: one_bias_dict})

            note_params_dict = {
                name: note_obj.to_dict() for name, note_obj in note_params_map.items()
            }
            self._save_data_to_json(note_params_dict, "note_params")

        return record

    def _special_save_data(self):
        """Save some data."""
        select_coe_threshold = self.experiment_options.select_coe_threshold
        note_params_map: dict = self.run_options.note_params_map
        cross_coe_dict: dict = self.run_options.cross_coe_map
        cross_trust_dict: dict = self.run_options.cross_trust_map

        note_params_dict = {}
        select_cross_coe_dict = {}

        for name, note_obj in note_params_map.items():
            coe_dict = {}
            trust_dict = {}
            t_osci_freq = note_obj.osci_freq
            for b_name, one_bias_dict in note_obj.bias_cross_dict.items():
                b_osci_freq = one_bias_dict.get("osci_freq", 0.0)
                trust = one_bias_dict.get("trust", False)
                coefficient = round(b_osci_freq / t_osci_freq, 4)
                coe_dict[b_name] = coefficient
                trust_dict[b_name] = trust

            note_params_dict[name] = note_obj.to_dict()
            cross_coe_dict[name] = coe_dict
            cross_trust_dict[name] = trust_dict

        # Save cross coefficient when large than threshold and the oscillation is true.
        for t_name, coe_dict in cross_coe_dict.items():
            new_coe_dict = {}
            trust_dict = cross_trust_dict.get(t_name, {})
            for b_name, coe_val in coe_dict.items():
                trust_flag = trust_dict.get(b_name, False)
                if trust_flag is True and coe_val > select_coe_threshold:
                    new_coe_dict[b_name] = coe_val
            if new_coe_dict:
                sort_coe_dict = dict(
                    sorted(new_coe_dict.items(), key=lambda x: x[1], reverse=True)
                )
                select_cross_coe_dict[t_name] = sort_coe_dict

        self._save_data_to_json(note_params_dict, "note_params")
        self._save_data_to_json(cross_coe_dict, "cross_coefficient")
        self._save_data_to_json(cross_trust_dict, "cross_trust")
        self._save_data_to_json(select_cross_coe_dict, "select_cross_coefficient")

    def _special_run_analysis(self):
        """Analysis logic."""
        t_names = self.run_options.t_names
        cross_coe_map: dict = self.run_options.cross_coe_map
        cross_trust_map: dict = self.run_options.cross_trust_map
        save_path = str(Path(self.run_options.record_path).parent)

        all_bias_names = []
        for t_name, coe_dict in cross_coe_map.items():
            for b_name in coe_dict.keys():
                if b_name not in all_bias_names:
                    all_bias_names.append(b_name)

        all_bias_names.sort(key=lambda x: int(x[1:]))
        cross_coe_dict = {}
        cross_trust_dict = {}
        default_coe_dict = {b_name: np.nan for b_name in all_bias_names}
        default_trust_dict = {b_name: False for b_name in all_bias_names}
        for t_name in t_names:
            coe_dict = deepcopy(default_coe_dict)
            trust_dict = deepcopy(default_trust_dict)
            coe_dict.update(cross_coe_map.get(t_name, {}))
            trust_dict.update(cross_trust_map.get(t_name, {}))
            cross_coe_dict[t_name] = coe_dict
            cross_trust_dict[t_name] = trust_dict

        base_len = 10
        q_length = len(all_bias_names)
        if q_length > base_len:
            ratio = q_length / base_len
            figsize = [int(12 * ratio), int(8 * ratio)]
        else:
            figsize = [12, 8]
        logger.info(f"Set analysis figsize: {figsize}")

        exp_file_obj = create_file(self.run_options.config, self._label, "")
        exp_file_obj._dirs = save_path
        exp_data = ExperimentData(
            x_data=np.array([]),
            y_data={},
            experiment_id=self.id,
            metadata=MetaData(name=str(self), save_location=save_path),
        )
        analysis_options = Options(
            is_plot=True,
            figsize=figsize,
            pure_exp_mode=False,
            cross_coe_map=cross_coe_dict,
            cross_trust_map=cross_trust_dict,
            n_multiple=100,
        )

        run_analysis_process(
            XYCrossRwAnalysis, exp_data, analysis_options, exp_file_obj
        )

    def _run_analysis(self):
        """Batch some define data analysis process logic."""
        super()._run_analysis()

        # save data.
        self._special_save_data()

        # analysis
        self._special_run_analysis()

        self.bind_pass_units(list(self.experiment_options.target_bias_map.keys()))

    def _cali_target_freq(self):
        """Calibrate target bit frequency."""
        cali_freq_flag = self.experiment_options.cali_freq_flag
        cali_freq_flows = self.experiment_options.cali_freq_flows
        t_names = self.run_options.t_names
        group_map = self.run_options.group_map

        if cali_freq_flag is True:
            for gn, g in group_map.items():
                self._run_flow(cali_freq_flows, g, name=f"{gn} cali target freq")

        # Note target bit some base parameters.
        chip_data = self.context_manager.chip_data
        cache_qubit = chip_data.cache_qubit
        note_params_map: dict = self.run_options.note_params_map

        for t_name in t_names:
            t_qubit: "Qubit" = cache_qubit.get(t_name)
            t_note_obj = NoteParams(
                drive_freq=t_qubit.drive_freq,
                drive_power=t_qubit.drive_power,
                xy_wave=t_qubit.XYwave,
                osci_freq=round(1000 / (t_qubit.XYwave.time * 2), 3),
                current="Before Strength RabiWidth",
            )
            note_params_map[t_name] = t_note_obj

        note_params_dict = {
            name: note_obj.to_dict() for name, note_obj in note_params_map.items()
        }
        self._save_data_to_json(note_params_dict, "note_params")

    def _strength_target(self):
        """Strength target bit RabiWidth."""
        update_target_strength = self.experiment_options.update_target_strength
        strength_flows = self.experiment_options.strength_flows
        group_map = self.run_options.group_map

        if update_target_strength is True:
            for gn, g in group_map.items():
                self._run_flow(strength_flows, g, name=f"{gn} strength target")

    def _xy_cross_bias(self):
        """Bias XYCrossRabiWidthOnce."""
        xy_cross_flows = self.experiment_options.xy_cross_flows
        target_bias_map: dict = self.experiment_options.target_bias_map
        note_params_map: dict = self.run_options.note_params_map

        bias_widths_list = [
            self.experiment_options.bias_widths_1,
            self.experiment_options.bias_widths_2,
        ]
        bias_expect_list = [
            self.experiment_options.bias_expect_1,
            self.experiment_options.bias_expect_2,
        ]

        exp_name = xy_cross_flows[0] if xy_cross_flows else "XYCrossRabiWidthOnce"
        for t_name, bias_names in target_bias_map.items():
            logger.info(f"target: {t_name}, bias_names: {bias_names}")
            t_note_obj: "NoteParams" = note_params_map.get(t_name)

            for b_name in bias_names:
                if b_name == t_name:
                    continue
                unit_str = f"{t_name}{b_name}"
                logger.info(
                    f"target {t_name} bias {b_name} execute {exp_name} start ..."
                )
                for widths, expect_range in zip(bias_widths_list, bias_expect_list):
                    self.change_regular_exec_exp_options(
                        exp_name=exp_name,
                        widths=widths,
                        drive_power=t_note_obj.drive_power,
                        drive_freq=t_note_obj.drive_freq,
                        xy_name=b_name,
                        rd_name=t_name,
                        direct_execute=True,
                    )
                    self._run_flow([exp_name], [unit_str])

                    # judge is not execute next
                    t_min, t_max = min(expect_range), max(expect_range)
                    one_bias_dict: dict = t_note_obj.bias_cross_dict.get(b_name, {})
                    osci_freq = one_bias_dict.get("osci_freq", 0.0)

                    if osci_freq < t_min:
                        one_bias_dict.update({"osci_freq": t_min})
                    elif t_min <= osci_freq <= t_max:
                        break
                    elif osci_freq > t_max:
                        one_bias_dict.update({"osci_freq": t_max})

                logger.info(f"target {t_name} bias {b_name} execute {exp_name} end.")

    def _run_batch(self):
        """Run batch flow logic."""

        # 1. 校准 target 比特频率
        self._cali_target_freq()

        # 2. target 比特 RabiWidth
        self._strength_target()

        # 3. bias 比特 XYCrossRabiWidthOnce
        self._xy_cross_bias()
