# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:2
msgid "pyQCat.pulse.Constant"
msgstr ""

#: of pyQCat.pulse.pulse_lib.Constant:1
msgid "Bases: :py:class:`~pyQCat.pulse.base_pulse.PulseComponent`"
msgstr ""

#: of pyQCat.pulse.pulse_lib.Constant:1
msgid "A simple constant pulse, with an amplitude value and a duration:"
msgstr ""

#: of pyQCat.pulse.pulse_lib.Constant:3
msgid ""
"f(x) = amp    ,  0 <= x < time\n"
"f(x) = 0      ,  elsewhere"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.pulse.Constant.__init__>`\\ \\(time\\, "
"amp\\[\\, name\\]\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid ""
":py:obj:`correct_ac_crosstalk "
"<pyQCat.pulse.Constant.correct_ac_crosstalk>`\\ "
"\\(after\\_ac\\_corsstalk\\_pulse\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid "Correct crosstalk caused by electronics device."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid ""
":py:obj:`correct_compensate <pyQCat.pulse.Constant.correct_compensate>`\\"
" \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid "Correct compensate caused by electronics device."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid ""
":py:obj:`correct_delay <pyQCat.pulse.Constant.correct_delay>`\\ "
"\\(delay\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid "Correct delay time caused by electronics device."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid ""
":py:obj:`correct_distortion <pyQCat.pulse.Constant.correct_distortion>`\\"
" \\(\\[plot\\]\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid "Correct distortion caused by electronics device."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid ":py:obj:`correct_pulse <pyQCat.pulse.Constant.correct_pulse>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid "Correct XY or Z pulse"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid ":py:obj:`get_empty_pulse <pyQCat.pulse.Constant.get_empty_pulse>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid ":py:obj:`get_pulse <pyQCat.pulse.Constant.get_pulse>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1 of
#: pyQCat.pulse.pulse_lib.Constant.get_pulse:1
msgid "Calculate pulse sequence."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid ":py:obj:`get_raw_pulse <pyQCat.pulse.Constant.get_raw_pulse>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid "Get pulse raw data."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid ""
":py:obj:`plot <pyQCat.pulse.Constant.plot>`\\ \\(file\\_name\\[\\, "
"use\\_points\\]\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid "Plot Pulse's sequence use matplotlib."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid ""
":py:obj:`validate_parameters "
"<pyQCat.pulse.Constant.validate_parameters>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:31:<autosummary>:1
msgid "Validate parameters."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.Constant.rst:33
msgid "Attributes"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid ":py:obj:`amp <pyQCat.pulse.Constant.amp>`\\"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1 pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid "The constant value amplitude."
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid ":py:obj:`attach <pyQCat.pulse.Constant.attach>`\\"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid "Flag of Pulse instance excuted '+' or '+=' operation."
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid ":py:obj:`bit <pyQCat.pulse.Constant.bit>`\\"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid "Get pulse on which qubit."
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid ":py:obj:`delay <pyQCat.pulse.Constant.delay>`\\"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid "Get pulse delay."
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid ":py:obj:`envelop <pyQCat.pulse.Constant.envelop>`\\"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid "Get the envelop of the waveform."
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid ":py:obj:`id <pyQCat.pulse.Constant.id>`\\"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid "Unique identifier for this pulse."
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid ":py:obj:`parameters <pyQCat.pulse.Constant.parameters>`\\"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
#: pyQCat.pulse.Constant.parameters:1
msgid "Return a dictionary containing the pulse's parameters."
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid ":py:obj:`pulse <pyQCat.pulse.Constant.pulse>`\\"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid "Data sequence for this pulse."
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid ":py:obj:`raw_pulse <pyQCat.pulse.Constant.raw_pulse>`\\"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid "Get uncorrected waveform raw data."
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid ":py:obj:`sweep <pyQCat.pulse.Constant.sweep>`\\"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid "Get description of pulse sweeped parameters."
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid ":py:obj:`type <pyQCat.pulse.Constant.type>`\\"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid "Get pulse type."
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid ":py:obj:`width <pyQCat.pulse.Constant.width>`\\"
msgstr ""

#: of pyQCat.pulse.Constant.amp:1:<autosummary>:1
msgid "Return the time unit of the pulse."
msgstr ""

#: of pyQCat.pulse.Constant.amp pyQCat.pulse.Constant.parameters
#: pyQCat.pulse.pulse_lib.Constant.validate_parameters
msgid "Return type"
msgstr ""

#: of pyQCat.pulse.Constant.amp:3
msgid ":py:class:`complex`"
msgstr ""

#: of pyQCat.pulse.pulse_lib.Constant.validate_parameters:1
msgid ""
"Validate parameters. :raises PulseError: If the parameters passed are not"
" valid."
msgstr ""

#: of pyQCat.pulse.pulse_lib.Constant.validate_parameters:5
msgid ":py:obj:`None`"
msgstr ""

#: of pyQCat.pulse.Constant.parameters:3
msgid ":py:class:`~typing.Dict`\\[:py:class:`str`, :py:data:`~typing.Any`]"
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.pulse.base_pulse.PulseComponent`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.pulse.Constant.__init__>`\\ "
#~ "\\(time\\, amp\\[\\, name\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_ac_crosstalk "
#~ "<pyQCat.pulse.Constant.correct_ac_crosstalk>`\\ "
#~ "\\(after\\_ac\\_corsstalk\\_pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_compensate "
#~ "<pyQCat.pulse.Constant.correct_compensate>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_delay <pyQCat.pulse.Constant.correct_delay>`\\"
#~ " \\(delay\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_distortion "
#~ "<pyQCat.pulse.Constant.correct_distortion>`\\ \\(\\[plot\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`correct_pulse <pyQCat.pulse.Constant.correct_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_empty_pulse "
#~ "<pyQCat.pulse.Constant.get_empty_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`get_pulse <pyQCat.pulse.Constant.get_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`get_raw_pulse <pyQCat.pulse.Constant.get_raw_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot <pyQCat.pulse.Constant.plot>`\\ "
#~ "\\(file\\_name\\[\\, use\\_points\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`validate_parameters "
#~ "<pyQCat.pulse.Constant.validate_parameters>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`amp <pyQCat.pulse.Constant.amp>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`attach <pyQCat.pulse.Constant.attach>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`bit <pyQCat.pulse.Constant.bit>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`delay <pyQCat.pulse.Constant.delay>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`envelop <pyQCat.pulse.Constant.envelop>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`id <pyQCat.pulse.Constant.id>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`parameters <pyQCat.pulse.Constant.parameters>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`pulse <pyQCat.pulse.Constant.pulse>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`raw_pulse <pyQCat.pulse.Constant.raw_pulse>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`sweep <pyQCat.pulse.Constant.sweep>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`type <pyQCat.pulse.Constant.type>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`width <pyQCat.pulse.Constant.width>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.pulse.base_pulse.PulseComponent`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.pulse.Constant.__init__>`\\ "
#~ "\\(time\\, amp\\[\\, name\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_ac_crosstalk "
#~ "<pyQCat.pulse.Constant.correct_ac_crosstalk>`\\ "
#~ "\\(after\\_ac\\_corsstalk\\_pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_compensate "
#~ "<pyQCat.pulse.Constant.correct_compensate>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_delay <pyQCat.pulse.Constant.correct_delay>`\\ "
#~ "\\(delay\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_distortion "
#~ "<pyQCat.pulse.Constant.correct_distortion>`\\ \\(\\[plot\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`correct_pulse <pyQCat.pulse.Constant.correct_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`get_empty_pulse <pyQCat.pulse.Constant.get_empty_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`get_pulse <pyQCat.pulse.Constant.get_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`get_raw_pulse <pyQCat.pulse.Constant.get_raw_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot <pyQCat.pulse.Constant.plot>`\\ "
#~ "\\(file\\_name\\[\\, use\\_points\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`validate_parameters "
#~ "<pyQCat.pulse.Constant.validate_parameters>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`amp <pyQCat.pulse.Constant.amp>`\\"
#~ msgstr ""

#~ msgid ":obj:`attach <pyQCat.pulse.Constant.attach>`\\"
#~ msgstr ""

#~ msgid ":obj:`bit <pyQCat.pulse.Constant.bit>`\\"
#~ msgstr ""

#~ msgid ":obj:`delay <pyQCat.pulse.Constant.delay>`\\"
#~ msgstr ""

#~ msgid ":obj:`envelop <pyQCat.pulse.Constant.envelop>`\\"
#~ msgstr ""

#~ msgid ":obj:`id <pyQCat.pulse.Constant.id>`\\"
#~ msgstr ""

#~ msgid ":obj:`parameters <pyQCat.pulse.Constant.parameters>`\\"
#~ msgstr ""

#~ msgid ":obj:`pulse <pyQCat.pulse.Constant.pulse>`\\"
#~ msgstr ""

#~ msgid ":obj:`raw_pulse <pyQCat.pulse.Constant.raw_pulse>`\\"
#~ msgstr ""

#~ msgid ":obj:`sweep <pyQCat.pulse.Constant.sweep>`\\"
#~ msgstr ""

#~ msgid ":obj:`type <pyQCat.pulse.Constant.type>`\\"
#~ msgstr ""

#~ msgid ":obj:`width <pyQCat.pulse.Constant.width>`\\"
#~ msgstr ""

