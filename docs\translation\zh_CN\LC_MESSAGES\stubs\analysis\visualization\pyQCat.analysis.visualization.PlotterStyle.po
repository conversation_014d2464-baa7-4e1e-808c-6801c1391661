# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.PlotterStyle.rst:2
msgid "pyQCat.analysis.visualization.PlotterStyle"
msgstr ""

#: of pyQCat.analysis.visualization.style.PlotterStyle:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.analysis.visualization.style.PlotterStyle:1
msgid "A stylesheet for curve analysis figure."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.PlotterStyle.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.PlotterStyle.rst:21:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.analysis.visualization.PlotterStyle.__init__>`\\ \\(\\[figsize\\,"
" legend\\_loc\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.PlotterStyle.rst:23
msgid "Attributes"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.PlotterStyle.rst:32:<autosummary>:1
msgid ""
":py:obj:`axis_label_size "
"<pyQCat.analysis.visualization.PlotterStyle.axis_label_size>`\\"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.PlotterStyle.rst:32:<autosummary>:1
msgid ":py:obj:`figsize <pyQCat.analysis.visualization.PlotterStyle.figsize>`\\"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.PlotterStyle.rst:32:<autosummary>:1
msgid ""
":py:obj:`fit_report_rpos "
"<pyQCat.analysis.visualization.PlotterStyle.fit_report_rpos>`\\"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.PlotterStyle.rst:32:<autosummary>:1
msgid ""
":py:obj:`fit_report_text_size "
"<pyQCat.analysis.visualization.PlotterStyle.fit_report_text_size>`\\"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.PlotterStyle.rst:32:<autosummary>:1
msgid ""
":py:obj:`legend_loc "
"<pyQCat.analysis.visualization.PlotterStyle.legend_loc>`\\"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.PlotterStyle.rst:32:<autosummary>:1
msgid ""
":py:obj:`tick_label_size "
"<pyQCat.analysis.visualization.PlotterStyle.tick_label_size>`\\"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.PlotterStyle.rst:32:<autosummary>:1
msgid ""
":py:obj:`plot_sigma "
"<pyQCat.analysis.visualization.PlotterStyle.plot_sigma>`\\"
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.analysis.visualization.PlotterStyle.__init__>`\\ "
#~ "\\(\\[figsize\\, legend\\_loc\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`axis_label_size "
#~ "<pyQCat.analysis.visualization.PlotterStyle.axis_label_size>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`figsize "
#~ "<pyQCat.analysis.visualization.PlotterStyle.figsize>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`fit_report_rpos "
#~ "<pyQCat.analysis.visualization.PlotterStyle.fit_report_rpos>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`fit_report_text_size "
#~ "<pyQCat.analysis.visualization.PlotterStyle.fit_report_text_size>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`legend_loc "
#~ "<pyQCat.analysis.visualization.PlotterStyle.legend_loc>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`tick_label_size "
#~ "<pyQCat.analysis.visualization.PlotterStyle.tick_label_size>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot_sigma "
#~ "<pyQCat.analysis.visualization.PlotterStyle.plot_sigma>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.visualization.PlotterStyle.__init__>`\\ "
#~ "\\(\\[figsize\\, legend\\_loc\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`axis_label_size "
#~ "<pyQCat.analysis.visualization.PlotterStyle.axis_label_size>`\\"
#~ msgstr ""

#~ msgid ":obj:`figsize <pyQCat.analysis.visualization.PlotterStyle.figsize>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`fit_report_rpos "
#~ "<pyQCat.analysis.visualization.PlotterStyle.fit_report_rpos>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`fit_report_text_size "
#~ "<pyQCat.analysis.visualization.PlotterStyle.fit_report_text_size>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`legend_loc "
#~ "<pyQCat.analysis.visualization.PlotterStyle.legend_loc>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`tick_label_size "
#~ "<pyQCat.analysis.visualization.PlotterStyle.tick_label_size>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot_sigma "
#~ "<pyQCat.analysis.visualization.PlotterStyle.plot_sigma>`\\"
#~ msgstr ""

