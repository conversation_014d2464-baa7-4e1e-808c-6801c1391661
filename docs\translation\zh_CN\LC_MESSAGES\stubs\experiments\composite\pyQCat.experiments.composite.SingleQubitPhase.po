# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:2
msgid "pyQCat.experiments.composite.SingleQubitPhase"
msgstr ""

#: of pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
msgstr ""

#: of pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase:1
msgid "SingleQubitPhase experiment."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.experiments.composite.SingleQubitPhase.__init__>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.composite.SingleQubitPhase.component_experiment>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.composite.SingleQubitPhase.from_experiment_context>`\\"
" \\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.composite.SingleQubitPhase.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.composite.SingleQubitPhase.options_table>`\\ "
"\\(\\[mode\\, detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.composite.SingleQubitPhase.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase.run:1
msgid "SingleQubitPhase Run Logic."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.composite.SingleQubitPhase.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.composite.SingleQubitPhase.set_experiment_options>`\\"
" \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.composite.SingleQubitPhase.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.composite.SingleQubitPhase.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.SingleQubitPhase.rst:32
msgid "Attributes"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid ""
":py:obj:`analysis "
"<pyQCat.experiments.composite.SingleQubitPhase.analysis>`\\"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.composite.SingleQubitPhase.analysis_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.composite.SingleQubitPhase.child_experiment>`\\"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.composite.SingleQubitPhase.experiment_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.composite.SingleQubitPhase.run_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:43
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:10
msgid "dcm_map (Dict): The map of q_name and dcm."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:10
msgid "normal like:"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:8
msgid "{"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:7
msgid "\"q0\": <IQdiscriminator>, \"q1\": <IQdiscriminator>, ..."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:10
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:41
msgid "}"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:12
msgid ""
"readout_type (str): Readout type. is_amend (bool): True means use "
"fidelity_matrix amend result. cz_num (int): Two X/2 center add number CZ "
"pulse, default 1."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:16
msgid "qt_name (str): The target bit name. qc_name (str): The control bit name."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:20
msgid "scan_high_bit (bool): Is or not scan high frequency bit."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:20
msgid "Normal scan control bit, will set z_amp to the bit Z line."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:22
msgid ""
"const_z_amp (float): The set z_amp of no-scan-bit. z_amp (float): The "
"z_amp of scan-bit."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:25
msgid "cz_width (float): The value is Flattop Gaussian Pulse width."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:26
msgid "CZ pulse width, that normal determined by Swap experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:27
msgid ""
"tc (float): The value is Flattop Gaussian Pulse tc. sigma (float): The "
"value is Flattop Gaussian Pulse sigma."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:30
msgid "phase_list (List, array): When simulator bit, last X/2 scan phase list."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:32
msgid "parking_bits (List(str)): List of parking name,"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:33
msgid "normal like: [\"q2\", \"c0\", ...]"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:43
msgid "parking_param_dict (dict):"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:35
msgid "The parking bits parameter of Flattop Gaussian Pulse. normal like: {"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:38
msgid "\"q2\": {\"amp\": 0.4}, \"c0\": {\"amp\": 0.2}, ..."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_run_options
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_experiment_options:45
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_run_options:9
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_run_options:1
msgid "Set run experiment operate options."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_run_options:7
msgid "Options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._default_run_options:4
msgid ""
"phase_const_list (List): List of phase, FGP amp is const_z_amp. "
"phase_z_amp_list (List): List of phase, FGP amp is const_z_amp."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_qubit_phase.SingleQubitPhase._check_options:1
msgid "Check Options."
msgstr ""

