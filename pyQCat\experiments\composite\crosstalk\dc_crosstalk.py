# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/12
# __author:       <PERSON><PERSON><PERSON> <PERSON>

"""
DC crosstalk experiment.
"""

import numpy as np

from ....analysis.fit.fit_models import amp2freq_formula
from ....analysis.library import CrosstalkAnalysis
from ....analysis.specification import ParameterRepr
from ....log import pyqlog
from ....structures import Options
from ...single import Ramsey
from .base_crosstalk import Crosstalk


class DCCrosstalk(Crosstalk):
    """DC crosstalk experiment to get the dc crosstalk matrix elements."""

    _sub_experiment_class = Ramsey

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options for crosstalk experiment."""
        options = super()._default_experiment_options()

        options.set_validator("freq_bound", float)

        options.freq_bound = 800
        options.init_v_bias = 0.05

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()
        options.v_target = None
        options.bias_qubit = None
        options.v_background = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> "Options":
        opt = super()._default_analysis_options()
        opt.result_parameters = [
            ParameterRepr("k", "coefficient", param_path="Crosstalk.dc_crosstalk")
        ]
        return opt

    def _create_child_experiment(self) -> Ramsey:
        """Create a child experiment object.

        Returns:
            BaseExperiment object.
        """
        child_exp = self._sub_experiment_class(
            self.inst,
            self.qubits[0],
            self.couplers,
            self.compensates,
            self.discriminator,
            self.working_dc,
        )
        return child_exp

    async def _sync_composite_run(self):
        """Run DC crosstalk experiment and get the crosstalk coefficient.

        Unlike the logic of AC crosstalk dynamically adjusting fringe, the
        Ramsey experiment for DC crosstalk dynamically adjusts the frequency
        of qubit while maintaining the oscillation frequency at a preset value.
        """
        # super().run()

        target_qubit, bias_qubit = self._get_qubits()
        self.set_run_options(bias_qubit=bias_qubit)

        init_fd, baseband_freq, fringe = self._get_runtime_parameters(target_qubit)

        fq = target_qubit.drive_freq

        v_target = self.run_options.v_target or target_qubit.dc_max

        # Get bias voltage list
        self.set_run_options(v_background=bias_qubit.dc_min)
        await self._get_v_bias_list(
            v_target=v_target,
            fringe=fringe,
            fd=init_fd,
            fq=fq,
            baseband_freq=baseband_freq,
        )

        for index, v_bias in enumerate(self.run_options.v_bias_list):
            v_bias = round(v_bias, 3)
            v_predict = v_target + self.run_options.guess_coefficient * (
                v_bias - self.run_options.v_background
            )
            v_predict = round(v_predict, 3)
            fd = amp2freq_formula(v_predict, *self.experiment_options.spectrum_params)
            fd = round(fd, 3)
            pyqlog.info(
                f"v_target={v_target}v, v_bias={v_bias}v, v_predict={v_predict}v"
            )
            pyqlog.info(f"Qubit {target_qubit.bit} drive freq: {fd}")

            validation = self._validate_freq(fd, init_fd)
            if not validation:
                break

            v_real = await self._run_ramsey(
                index=index + 1,
                v_bias=v_bias,
                fringe=fringe,
                fd=fd,
                baseband_freq=baseband_freq,
            )
            pyqlog.info(f"v_real={v_real}v")
            self.run_options.v_real_list.append(v_real)

        self._run_analysis(
            x_data=np.asarray(self.run_options.v_bias_list),
            analysis_class=CrosstalkAnalysis,
        )

        self.file.save_data(
            self.run_options.v_bias_list, self.run_options.v_real_list, name=self._label
        )

    async def _run_ramsey(
        self,
        index: int,
        v_bias: float,
        fringe: float,
        fd: float,
        baseband_freq: float,
        v_target: float = None,
    ) -> float:
        """Execute ramsey experiment to get the actual voltage applied to target qubit."""
        # update target qubit frequency.
        self._child_experiment.qubit.drive_freq = fd
        # update bias qubit voltage.
        # self._child_experiment.working_dc[str(self.run_options.bias_qubit.z_dc_channel)] = v_bias
        self._child_experiment.working_dc[self.run_options.bias_qubit.name][1] = v_bias
        return await super()._run_ramsey(index, v_bias, fringe, fd, baseband_freq)

    def _validate_freq(self, fd: float, init_fd: float):
        """Validate the qubit drive frequency.

        Adjusting the driving frequency will lead to the deviation of the static
        working point of the qubit, resulting in the failure of the pulse resonance
        to drive the qubit, and also cause the change of the reading criterion.
        However, within a certain range, no influence will be caused, so the experiment
        can continue.

        Args:
            fd (float): The qubit drive frequency.
            init_fd (float): The qubit initialized drive frequency.

        Returns:
            True of False.
        """
        ret = fd - init_fd
        validation = True
        if ret > self.experiment_options.freq_bound:
            pyqlog.info(f"qubit frequency={fd}MHz too high ! Stop experiment.")
            validation = False
        elif ret < -self.experiment_options.freq_bound:
            pyqlog.info(f"qubit frequency={fd}MHz too low ! Stop experiment.")
            validation = False

        return validation
