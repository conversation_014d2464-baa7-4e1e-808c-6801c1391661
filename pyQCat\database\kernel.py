# -*- coding: utf-8 -*-
"""
__date:         2018/0/0
__author:       <PERSON> Shih
__corporation:  OriginQuantum
__usage:        
"""
from .ODM import *
import sys
from ..qaio_property import QAIO


# global variable for mapping `XY_control`, `Z_flux_control`, `Read_out_control`, `Trig_out_control`
MODULE_MAP = {
    'XY_control': XYlineDoc,
    'Z_flux_control': ZLineDoc,
    'Read_out_control': ReadoutDoc,
    'Trig_out_control': TrigOutDoc
}


class Kernel:

    def __init__(self, qubits):
        self.instrument = MeasureAIODoc()
        self.measurement = None
        self.qubits = qubits

    def set_period(self, time):
        self.instrument.pulse_period = time

    def set_trig_mode(self, mode):
        self.instrument.trig_way = mode

    def set_dc_module(self, channel, value):
        channel -= 1
        control_module = self.instrument.Z_dc_control

        if control_module:
            control_module[channel] = value
        else:
            dc_num = QAIO.dc_channel[-1]
            control_module = [99.] * dc_num
            self.instrument.Z_dc_control = control_module
            control_module[channel] = value

    def set_hardware_module(self, module, channel, name, value):
        control_module = self._get_target_module(module, channel)

        if control_module:
            setattr(control_module, name, value)
        else:
            control_module = MODULE_MAP[module](channel=channel)
            setattr(control_module, name, value)
            getattr(self.instrument, module).append(control_module)

    def set_waveform_module(self, module, channel, wave_type, wave_param):
        """Set the `WaveFormDoc` object """
        control_module = self._get_target_module(module, channel)
        if control_module is None:
            control_module = MODULE_MAP[module](channel=channel)
            getattr(self.instrument, module).append(control_module)
        if control_module.waveform is not None:
            self._set_wave(control_module.waveform, wave_type, wave_param)
        else:
            waveform = WaveFormDoc()
            self._set_wave(waveform, wave_type, wave_param)
            control_module.waveform = waveform
            # getattr(self.instrument, module).append(control_module)

    def set_sweep_channel(self, num):
        self.measurement = SweepDoc()
        self.measurement.channel = num

    def set_sweep_func(self, func_name: str, func_param: str):
        self.measurement.func = func_name + ':' + func_param

    def set_sweep_points(self, points: list):
        self.measurement.points = points

    def set_sweep_wave(self, wavefile):
        customwave = CustomWaveDoc()
        wave_size = sys.getsizeof(wavefile)
        # print(f'wavefile size: {wave_size/1e6}MB')
        if wave_size < 16 * 1e6:
            customwave.wavefile = wavefile
        else:
            customwave.bigwavefile.put(wavefile)
        self.measurement.waveform = customwave

    def set_sweep_repeat(self, num):
        self.measurement.repeat = num

    def _get_target_module(self, name, channel):
        """return the target module name"""
        module_set = getattr(self.instrument, name)
        target_module = None

        if not self._is_empty(module_set):
            for item in module_set:
                if item.channel == channel:
                    target_module = item

        return target_module

    @staticmethod
    def _set_wave(waveform, wave_type, wave_param):
        if wave_type == 0:
            if waveform.sine_wave is None:
                waveform.sine_wave = SineWaveDoc()
            for key, value in wave_param.items():
                if value is not None:
                    setattr(waveform.sine_wave, key, value)
            del waveform.dc_wave
            del waveform.custom_wave

        if wave_type == 1:
            if waveform.dc_wave is None:
                waveform.dc_wave = DcWaveDoc()
            for key, value in wave_param.items():
                if value is not None:
                    setattr(waveform.dc_wave, key, value)
            del waveform.sine_wave
            del waveform.custom_wave

        if wave_type == 2:
            if waveform.custom_wave is None:
                waveform.custom_wave = CustomWaveDoc()
            wave_data = wave_param['wavefile']
            wave_size = sys.getsizeof(wave_data)
            # print(f'wavefile size: {wave_size/1e6}MB')
            if wave_size < 16 * 1e6:
                waveform.custom_wave.wavefile = wave_data
            else:
                waveform.custom_wave.bigwavefile.new_file()
                waveform.custom_wave.bigwavefile.put(wave_data)
                waveform.custom_wave.bigwavefile.close()
            del waveform.sine_wave
            del waveform.dc_wave

    @staticmethod
    def _is_empty(seqs):
        if len(seqs) > 0:
            return 0
        return 1

