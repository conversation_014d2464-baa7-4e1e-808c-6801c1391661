# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
"""
===============================================================
Analysis Quality  (:mod:`pyQCat.analysis.quality`)
===============================================================

Base Classes
========================================================

.. autosummary::
    :toctree: ../stubs/analysis/quality/

    GoodnessofFit
    SingleShotQuality
"""

from .goodness_of_fit import GoodnessofFit
from .single_shot_quality import SingleShotQuality
from .snr_quality import SNRQuality
from .base_quality import BaseQuality
