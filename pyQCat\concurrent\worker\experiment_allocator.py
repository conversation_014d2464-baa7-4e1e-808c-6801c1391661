# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/04
# __author:       <PERSON><PERSON><PERSON>

"""
Parallel Allocation Utils:

Used to allocate all participating sub experiments into different parallel groups.
"""

import copy
import traceback
from collections import defaultdict
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional

from loguru import logger

from ...processor.chip_data import MiddleChipData
from ...qm_protocol import CommonMessage, ExperimentFile
from ...qubit import Qubit
from ...tools.allocation import (
    IntermediateFreqAllocation,
    ParallelAllocationQC,
    ReadoutAmpAllocation,
)
from ...tools.utilities import unit_group_formation_table


@dataclass
class Resource:
    qubits: Dict[str, Qubit] = field(default_factory=dict)
    xy_bits: List[str] = field(default_factory=list)
    m_bits: List[str] = field(default_factory=list)
    bit_xy_ctl_map: Dict[str, int] = field(default_factory=dict)
    bit_m_ctl_map: Dict[str, int] = field(default_factory=dict)
    bit_m_amp_map: Dict[str, int] = field(default_factory=dict)


class AllocationWorkerStatus(str, Enum):
    INIT = "init"
    DONE = "allocation jobs have successfully run"
    ERROR = "allocation jobs error"


class AllocationResult:
    __slots__ = ("group_map", "zero_xy_tasks", "fail_tasks", "status", "msg")

    def __init__(self) -> None:
        self.group_map: Dict[str, List] = defaultdict(list)
        self.status: AllocationWorkerStatus = AllocationWorkerStatus.INIT
        self.msg = ""

    @property
    def is_done(self) -> bool:
        return self.status == AllocationWorkerStatus.DONE

    def sync_other(self, other_result: "AllocationResult"):
        groups = {}
        other_result.result_table()
        for group_name, group_value in other_result.group_map.items():
            groups[group_name] = [task.physical_units for task in group_value]
        self.group_map = groups
        self.status = other_result.status
        self.msg = other_result.msg

    def result_table(self):
        if not self.msg:
            groups = {}
            for group_name, group_value in self.group_map.items():
                groups[group_name] = [task.physical_units for task in group_value]
            self.msg = unit_group_formation_table(groups)


class ExperimentAllocator:
    def __init__(
        self,
        tasks: List,
        shared_resource: Optional[CommonMessage] = None,
        cache_group: Optional[Dict] = None,
        debug: bool = False,
    ) -> None:
        self._tasks = tasks
        self._shared_resource = shared_resource
        self._cache_group = cache_group
        self._resource_map = {}
        self._result: AllocationResult = AllocationResult()
        self._chip_data = None
        self._debug = debug

    @property
    def result(self) -> AllocationResult:
        return self._result

    def _allocate_from_cache(self):
        if self._cache_group:
            task_map = {}
            for task in self._tasks:
                task_map[task.physical_units] = task
            group = defaultdict(list)
            for group_name, group_value in self._cache_group.items():
                for physical_units in group_value:
                    if physical_units in task_map:
                        group[group_name].append(task_map[physical_units])
            self._result.group_map = group

    def _empty_allocation(self):
        self._result.group_map["default"] = self._tasks

    def _extract_allocation_resource(self):
        resource_map = {}
        for task in self._tasks:
            if not task.merge_struct or task.merge_struct.extra.get("seat", False) is True:
                logger.warning(f"Invalid task {task}")
                continue
            resource_map[task] = extract_simple_resource(task.merge_struct, self._shared_resource)
        self._resource_map = resource_map
        self._build_middle_chip_data()

    def _allocate_group(self):
        xy_physical_unit_set = set()
        for task, resource in self._resource_map.items():
            if resource.xy_bits:
                xy_physical_unit_set = xy_physical_unit_set.union(set(resource.xy_bits))

        if xy_physical_unit_set:
            allocator = ParallelAllocationQC(chip_data=self._chip_data)
            allocator.set_allocation_options(**self._shared_resource.allocation_options)
            allocator.allocate(list(xy_physical_unit_set))
            parallel_group = allocator.run_options.parallel_name_map

            for task, resource in self._resource_map.items():
                has_add = False
                if resource:
                    for group_name, group_units in parallel_group.items():
                        if set(resource.xy_bits).issubset(set(group_units)):
                            self.result.group_map[group_name].append(task)
                            has_add = True
                            break
                    if has_add is False:
                        self.result.group_map["fail_tasks"].append(task)
                else:
                    self.result.group_map["fail_tasks"].append(task)

    def _allocate_resource(self):
        for group_name, group in self._result.group_map.items():
            if group_name == "fail_tasks":
                continue
            m_lo_gap_map = defaultdict(list)
            bus_power_map = defaultdict(list)
            for task in group:
                resource = self._resource_map[task]
                for bit in resource.m_bits:
                    qubit = resource.qubits[bit]
                    bus_power_map[qubit.inst.bus].append((qubit.probe_power, bit))
                    m_lo_gap_map[qubit.inst.m_lo].append((qubit.Mwave.baseband_freq, bit))
            # Same bus read power consistency detection and allocation
            for _, same_bus_collection in bus_power_map.items():
                if len(set([collection[0] for collection in same_bus_collection])) != 1:
                    allocator = ReadoutAmpAllocation(self._chip_data)
                    allocator.set_allocation_options()
                    allocator.allocate([collection[1] for collection in same_bus_collection])
            # Same read local oscillator consistency detection and allocation
            for _, same_m_lo_collection in m_lo_gap_map.items():
                if len(set([qubit.probe_freq - collection[0] for collection in same_m_lo_collection])) != 1:
                    allocator = IntermediateFreqAllocation(self._chip_data)
                    allocator.set_allocation_options(mode="m")
                    allocator.allocate([collection[1] for collection in same_m_lo_collection])
            # resource update
            for task in group:
                self._allocate_one_task_resource(task)

    def _build_middle_chip_data(self):
        collection = {}

        for task, resource in self._resource_map.items():
            if not resource.xy_bits:
                self.result.group_map["empty_xy_tasks"].append(task)
            collection.update(resource.qubits)

        self._chip_data = MiddleChipData(collection, self._shared_resource.topology)

    def _allocate_one_task_resource(self, task):
        """To reallocate resources for a single task, this mainly involves
        modifying the baseband frequency in the xy control waveform.

        Args:
            task (MPTask): Parallel subtasks to be merged
        """
        resource = self._resource_map[task]
        for bit in resource.xy_bits:
            qubit = resource.qubits[bit]
            xy_ctrl = task.merge_struct.measure_aio.XY_control[resource.bit_xy_ctl_map.get(bit)]
            if qubit.XYwave.baseband_freq != xy_ctrl.intermediate_frequency:
                if self._debug:
                    logger.info(
                        f"Change {bit} xy baseband freq [{xy_ctrl.intermediate_frequency} | {qubit.XYwave.baseband_freq}]"
                    )
                xy_ctrl.intermediate_frequency = qubit.XYwave.baseband_freq
                if xy_ctrl.waveform:
                    for pulse in xy_ctrl.waveform[0]._fake_pulse:
                        if hasattr(pulse, "freq"):
                            setattr(pulse, "freq", qubit.XYwave.baseband_freq * 1e-3)
                for sweep_ctrl in task.merge_struct.sweep_control:
                    if sweep_ctrl.func == "XY_control:waveform" and sweep_ctrl.channel == xy_ctrl.channel:
                        for c_pulse in sweep_ctrl.waveform:
                            for pulse in c_pulse._fake_pulse:
                                if hasattr(pulse, "freq"):
                                    setattr(pulse, "freq", qubit.XYwave.baseband_freq * 1e-3)
        for bit in resource.m_bits:
            qubit = resource.qubits[bit]
            m_ctrl = task.merge_struct.measure_aio.Readout_control[resource.bit_m_ctl_map[bit]]
            if m_ctrl.pulse_power != qubit.probe_power:
                if self._debug:
                    logger.info(f"Change {bit} probe power [{m_ctrl.pulse_power} | {qubit.probe_power}]")
                m_ctrl.pulse_power = qubit.probe_power
            readout_amp = resource.bit_m_amp_map.get(bit)
            if readout_amp is not None and readout_amp != qubit.Mwave.amp:
                if self._debug:
                    logger.info(f"Change {bit} readout amp [{readout_amp} | {qubit.Mwave.amp}]")
                for pulse in m_ctrl.waveform[0]._fake_pulse:
                    if hasattr(pulse, "amp_list"):
                        setattr(pulse, "amp_list", [qubit.Mwave.amp])
            if qubit.Mwave.baseband_freq != m_ctrl.intermediate_frequency:
                if self._debug:
                    logger.info(
                        f"Change {bit} m baseband freq [{m_ctrl.intermediate_frequency} | {qubit.Mwave.baseband_freq}]"
                    )
                m_ctrl.intermediate_frequency = qubit.Mwave.baseband_freq
                if m_ctrl.waveform:
                    for pulse in m_ctrl.waveform[0]._fake_pulse:
                        if hasattr(pulse, "freq"):
                            setattr(pulse, "freq", qubit.Mwave.baseband_freq * 1e-3)

    def _clear_resource(self):
        self._resource_map.clear()
        self._shared_resource = None
        self._tasks = None
        self._result = None
        self._chip_data = None

    def run(self):
        try:
            if self._cache_group:
                self._extract_allocation_resource()
                self._allocate_from_cache()
                self._allocate_resource()
            elif self._shared_resource is None:
                self._empty_allocation()
            else:
                self._extract_allocation_resource()
                self._allocate_group()
                self._allocate_resource()
            self._result.status = AllocationWorkerStatus.DONE
            self._result.result_table()
        except Exception:
            self._result.status = AllocationWorkerStatus.ERROR
            self._result.msg = str(traceback.format_exc())
        result = self._result
        self._clear_resource()
        return result


def extract_simple_resource(experiment: ExperimentFile, shared_resource: CommonMessage):
    resource = Resource()

    def _get_qubit():
        qubit = resource.qubits.get(qubit_name)
        if not qubit:
            qubit = copy.deepcopy(shared_resource.physical_units.get(qubit_name))
            if qubit:
                resource.qubits[qubit_name] = qubit
        return qubit

    for idx, ctrl in enumerate(experiment.measure_aio.XY_control):
        qubit_name = shared_resource.channel_bit_map[f"xy-{ctrl.channel}"]
        qubit = _get_qubit()
        if qubit:
            qubit.drive_freq = ctrl.output_frequency
            qubit.XYwave.baseband_freq = ctrl.intermediate_frequency
        else:
            return
        resource.xy_bits.append(qubit_name)
        resource.bit_xy_ctl_map[qubit_name] = idx

    for idx, ctrl in enumerate(experiment.measure_aio.Readout_control):
        qubit_name = shared_resource.channel_bit_map[f"m-{ctrl.channel}"]
        qubit = _get_qubit()
        readout_amp = None
        if ctrl.waveform:
            readout_amp = getattr(ctrl.waveform[0], "amp_list")[0]
            if readout_amp is not None:
                resource.bit_m_amp_map[qubit_name] = readout_amp
        if qubit:
            qubit.probe_freq = ctrl.output_frequency
            qubit.probe_power = ctrl.pulse_power
            qubit.Mwave.baseband_freq = ctrl.intermediate_frequency
            qubit.Mwave.amp = readout_amp
        else:
            return
        resource.m_bits.append(qubit_name)
        resource.bit_m_ctl_map[qubit_name] = idx

    return resource


def concurrent_allocation_experiment(
    tasks: List,
    shared_resource: Optional[CommonMessage] = None,
    cache_group: Optional[Dict] = None,
):
    allocation = ExperimentAllocator(tasks, shared_resource, cache_group)
    return allocation.run()


async def async_allocation_experiment(
    tasks: List,
    shared_resource: Optional[CommonMessage] = None,
    cache_group: Optional[Dict] = None,
):
    allocation = ExperimentAllocator(tasks, shared_resource, cache_group)
    return allocation.run()
