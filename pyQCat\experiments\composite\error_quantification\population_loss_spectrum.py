# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/27
# __author:       <PERSON>

from ....analysis.library import PopulationLossSpectrumAnalysis
from ....analysis.top_analysis import PackedYData
from ....errors import ExperimentFlowError, ExperimentOptionsError
from ....parameters import options_wrapper
from ....structures import Options
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import PopulationLossOnce


@options_wrapper
class PopulationLossSpectrum(CompositeExperiment):
    _sub_experiment_class = PopulationLossOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.delay_list = [100, 1000, 10000]
        options.set_validator("delay_list", list)  # Unit: ns

        options.z_amp_list = []
        options.set_validator("z_amp_list", list)
        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.z_amp_list = None
        options.freq_list = None
        options.delay_list = None

        return options

    def _check_options(self):
        super()._check_options()

        if len(self.qubits) != 1:
            raise ExperimentOptionsError(
                self.label,
                msg=f"The number of physical units should be 1 in this experiment (`{self.label}`).",
                key="physical_unit",
                value=self.qubits,
            )
        self.set_analysis_options(
            result_name=self.qubits[0].name,
            z_amp_list=self.experiment_options.z_amp_list,
            freq_list=self.experiment_options.freq_list,
            delay_list=self.experiment_options.delay_list,
        )
        self.run_options.x_data = self.experiment_options.delay_list
        self.run_options.analysis_class = PopulationLossSpectrumAnalysis

    def _setup_child_experiment(
        self, p_exp: PopulationLossOnce, index: int, delay: float
    ):
        p_exp.run_options.index = index

        total = len(self.run_options.x_data)
        describe = f"delay={delay}"
        p_exp.set_parent_file(self, describe, index, total)
        p_exp.set_experiment_options(
            delay=delay, z_amp_list=self.experiment_options.z_amp_list
        )
        self._check_simulator_data(p_exp, index)

    def _handle_child_result(self, p_exp: PopulationLossOnce):
        if p_exp.analysis is None:
            raise ExperimentFlowError(
                self.label,
                "child exp without analysis object, maybe child exp error!",
            )

        else:
            # replace with previous result
            p0s = p_exp.analysis.experiment_data.y_data.get("P0").tolist()
            p1s = p_exp.analysis.experiment_data.y_data.get("P1").tolist()
        i = p_exp.run_options.index
        p_exp.analysis.provide_for_parent.update(
            {
                f"p0s_{i}": PackedYData(p0s),
                f"p1s_{i}": PackedYData(p1s),
            }
        )
        
    def _create_composite_experiment_data(self, x_data):
        experiment_data = super()._create_composite_experiment_data(x_data)
        experiment_data._x_data = self.experiment_options.z_amp_list
        return experiment_data
