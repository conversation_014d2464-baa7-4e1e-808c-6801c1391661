# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/pulse/pyQCat.pulse.WaveStruct.rst:2
msgid "pyQCat.pulse.WaveStruct"
msgstr ""

#: of pyQCat.pulse.generator.WaveStruct:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveStruct.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveStruct.rst:16:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.pulse.WaveStruct.__init__>`\\ "
"\\(\\[device\\_code\\, channel\\, ...\\]\\)"
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.pulse.WaveStruct.__init__>`\\ "
#~ "\\(\\[device\\_code\\, channel\\, ...\\]\\)"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.pulse.WaveStruct.__init__>`\\ "
#~ "\\(\\[device\\_code\\, channel\\, ...\\]\\)"
#~ msgstr ""

