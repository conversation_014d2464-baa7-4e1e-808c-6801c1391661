# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:2
msgid "pyQCat.pulse.WaveGenerator"
msgstr ""

#: of pyQCat.pulse.generator.WaveGenerator:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.pulse.WaveGenerator.__init__>`\\ "
"\\(\\[sample\\_rate\\]\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`c1 <pyQCat.pulse.WaveGenerator.c1>`\\ \\(time\\, freq\\, amp\\, "
"phase\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`c2 <pyQCat.pulse.WaveGenerator.c2>`\\ \\(time\\, freq\\, amp\\, "
"phase\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ":py:obj:`clear <pyQCat.pulse.WaveGenerator.clear>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`cos <pyQCat.pulse.WaveGenerator.cos>`\\ \\(time\\, freq\\, "
"amp\\, phase\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`cosN <pyQCat.pulse.WaveGenerator.cosN>`\\ \\(time\\, amp\\, "
"phase\\, \\*freq\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ":py:obj:`dc <pyQCat.pulse.WaveGenerator.dc>`\\ \\(time\\, value\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`gauss <pyQCat.pulse.WaveGenerator.gauss>`\\ \\(time\\, amp\\, "
"sigma\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ":py:obj:`get_data <pyQCat.pulse.WaveGenerator.get_data>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ":py:obj:`one_cos <pyQCat.pulse.WaveGenerator.one_cos>`\\ \\(time\\, amp\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`sin <pyQCat.pulse.WaveGenerator.sin>`\\ \\(time\\, freq\\, "
"amp\\, phase\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`sinN <pyQCat.pulse.WaveGenerator.sinN>`\\ \\(time\\, phase\\, "
"amp\\_list\\, freq\\_list\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`square <pyQCat.pulse.WaveGenerator.square>`\\ \\(time\\, freq\\,"
" amp\\, phase\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`x1 <pyQCat.pulse.WaveGenerator.x1>`\\ \\(time\\, freq\\, amp\\, "
"phase\\, alpha\\, delta\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`x2 <pyQCat.pulse.WaveGenerator.x2>`\\ \\(time\\, freq\\, amp\\, "
"phase\\, alpha\\, delta\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`xx1 <pyQCat.pulse.WaveGenerator.xx1>`\\ \\(time\\, freq\\, "
"amp\\, phase\\, alpha\\, delta\\, ...\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`y1 <pyQCat.pulse.WaveGenerator.y1>`\\ \\(time\\, freq\\, amp\\, "
"phase\\, alpha\\, delta\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`y2 <pyQCat.pulse.WaveGenerator.y2>`\\ \\(time\\, freq\\, amp\\, "
"phase\\, alpha\\, delta\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.WaveGenerator.rst:34:<autosummary>:1
msgid ""
":py:obj:`yy1 <pyQCat.pulse.WaveGenerator.yy1>`\\ \\(time\\, freq\\, "
"amp\\, phase\\, alpha\\, delta\\, ...\\)"
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.pulse.WaveGenerator.__init__>`\\ "
#~ "\\(\\[sample\\_rate\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`c1 <pyQCat.pulse.WaveGenerator.c1>`\\ \\(time\\,"
#~ " freq\\, amp\\, phase\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`c2 <pyQCat.pulse.WaveGenerator.c2>`\\ \\(time\\,"
#~ " freq\\, amp\\, phase\\)"
#~ msgstr ""

#~ msgid ":py:obj:`clear <pyQCat.pulse.WaveGenerator.clear>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`cos <pyQCat.pulse.WaveGenerator.cos>`\\ "
#~ "\\(time\\, freq\\, amp\\, phase\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`cosN <pyQCat.pulse.WaveGenerator.cosN>`\\ "
#~ "\\(time\\, amp\\, phase\\, \\*freq\\)"
#~ msgstr ""

#~ msgid ":py:obj:`dc <pyQCat.pulse.WaveGenerator.dc>`\\ \\(time\\, value\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`gauss <pyQCat.pulse.WaveGenerator.gauss>`\\ "
#~ "\\(time\\, amp\\, sigma\\)"
#~ msgstr ""

#~ msgid ":py:obj:`get_data <pyQCat.pulse.WaveGenerator.get_data>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`one_cos <pyQCat.pulse.WaveGenerator.one_cos>`\\ "
#~ "\\(time\\, amp\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`sin <pyQCat.pulse.WaveGenerator.sin>`\\ "
#~ "\\(time\\, freq\\, amp\\, phase\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`sinN <pyQCat.pulse.WaveGenerator.sinN>`\\ "
#~ "\\(time\\, phase\\, amp\\_list\\, freq\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`square <pyQCat.pulse.WaveGenerator.square>`\\ "
#~ "\\(time\\, freq\\, amp\\, phase\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`x1 <pyQCat.pulse.WaveGenerator.x1>`\\ \\(time\\,"
#~ " freq\\, amp\\, phase\\, alpha\\, delta\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`x2 <pyQCat.pulse.WaveGenerator.x2>`\\ \\(time\\,"
#~ " freq\\, amp\\, phase\\, alpha\\, delta\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`xx1 <pyQCat.pulse.WaveGenerator.xx1>`\\ "
#~ "\\(time\\, freq\\, amp\\, phase\\, alpha\\,"
#~ " delta\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`y1 <pyQCat.pulse.WaveGenerator.y1>`\\ \\(time\\,"
#~ " freq\\, amp\\, phase\\, alpha\\, delta\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`y2 <pyQCat.pulse.WaveGenerator.y2>`\\ \\(time\\,"
#~ " freq\\, amp\\, phase\\, alpha\\, delta\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`yy1 <pyQCat.pulse.WaveGenerator.yy1>`\\ "
#~ "\\(time\\, freq\\, amp\\, phase\\, alpha\\,"
#~ " delta\\, ...\\)"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.pulse.WaveGenerator.__init__>`\\ "
#~ "\\(\\[sample\\_rate\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`c1 <pyQCat.pulse.WaveGenerator.c1>`\\ \\(time\\, "
#~ "freq\\, amp\\, phase\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`c2 <pyQCat.pulse.WaveGenerator.c2>`\\ \\(time\\, "
#~ "freq\\, amp\\, phase\\)"
#~ msgstr ""

#~ msgid ":obj:`clear <pyQCat.pulse.WaveGenerator.clear>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`cos <pyQCat.pulse.WaveGenerator.cos>`\\ \\(time\\,"
#~ " freq\\, amp\\, phase\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`cosN <pyQCat.pulse.WaveGenerator.cosN>`\\ \\(time\\,"
#~ " amp\\, phase\\, \\*freq\\)"
#~ msgstr ""

#~ msgid ":obj:`dc <pyQCat.pulse.WaveGenerator.dc>`\\ \\(time\\, value\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`gauss <pyQCat.pulse.WaveGenerator.gauss>`\\ "
#~ "\\(time\\, amp\\, sigma\\)"
#~ msgstr ""

#~ msgid ":obj:`get_data <pyQCat.pulse.WaveGenerator.get_data>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`one_cos <pyQCat.pulse.WaveGenerator.one_cos>`\\ \\(time\\, amp\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`sin <pyQCat.pulse.WaveGenerator.sin>`\\ \\(time\\,"
#~ " freq\\, amp\\, phase\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`sinN <pyQCat.pulse.WaveGenerator.sinN>`\\ \\(time\\,"
#~ " phase\\, amp\\_list\\, freq\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`square <pyQCat.pulse.WaveGenerator.square>`\\ "
#~ "\\(time\\, freq\\, amp\\, phase\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`x1 <pyQCat.pulse.WaveGenerator.x1>`\\ \\(time\\, "
#~ "freq\\, amp\\, phase\\, alpha\\, delta\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`x2 <pyQCat.pulse.WaveGenerator.x2>`\\ \\(time\\, "
#~ "freq\\, amp\\, phase\\, alpha\\, delta\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`xx1 <pyQCat.pulse.WaveGenerator.xx1>`\\ \\(time\\,"
#~ " freq\\, amp\\, phase\\, alpha\\, delta\\,"
#~ " ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`y1 <pyQCat.pulse.WaveGenerator.y1>`\\ \\(time\\, "
#~ "freq\\, amp\\, phase\\, alpha\\, delta\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`y2 <pyQCat.pulse.WaveGenerator.y2>`\\ \\(time\\, "
#~ "freq\\, amp\\, phase\\, alpha\\, delta\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`yy1 <pyQCat.pulse.WaveGenerator.yy1>`\\ \\(time\\,"
#~ " freq\\, amp\\, phase\\, alpha\\, delta\\,"
#~ " ...\\)"
#~ msgstr ""

