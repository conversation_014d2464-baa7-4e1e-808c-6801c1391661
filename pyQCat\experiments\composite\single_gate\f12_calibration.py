# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/09/16
# __author:       <PERSON><PERSON><PERSON>

from ...single import RamseyF12
from .qubit_freq_calibration import QubitFreqCalibration


class F12Calibration(QubitFreqCalibration):
    """F12 Calibration Experiment."""

    _sub_experiment_class = RamseyF12

    def _check_options(self):
        """F12 Cali check options."""
        super()._check_options()
        drive_freq = self.child_experiment.qubit.f12_options.drive_freq
        self.set_analysis_options(drive_freq=drive_freq)

    def _set_result_path(self):
        """Set path to save parameter of <PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON>."""
        for key, result in self.analysis.results.items():
            if key == "f01":
                result.extra["path"] = "Qubit.f12_options.drive_freq"
