# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/02/06
# __author:       <PERSON><PERSON><PERSON>

from typing import TYPE_CHECKING, Dict, List, Union

from pyqcat_visage.gui.widgets.component.tree_delegate_options import QOptionsDelegate
from pyqcat_visage.gui.widgets.perm_manage import PermissionManageWindow
from pyqcat_visage.gui.widgets.permissions.table_model_perms_note import QTableModelPermsNote
from pyqcat_visage.gui.widgets.permissions.tree_model_perms_note import QTreeModelPermsNote
from pyqcat_visage.gui.widgets.perms_oprate import PermsOperateWindow
from pyqcat_visage.gui.widgets.perms_platform import PermsPlatformWindow
from pyqcat_visage.gui.widgets.perms_platform_oprate import PlatformManageWindow
from pyqcat_visage.gui.widgets.title_window import TitleWindow
from pyqcat_visage.gui.permission_note_ui import Ui_MainWindow

if TYPE_CHECKING:
    from pyqcat_visage.gui.main_window import VisageGUI


class PermsNoteWindow(TitleWindow):
    def __init__(self, gui: "VisageGUI", parent=None):
        super().__init__(parent)
        self._ui = Ui_MainWindow()
        self._ui.setupUi(self)
        self.gui = gui
        self.user_list = []
        self.group_list = []
        self.perms_list = []

        self.data = []
        self._item = {}

        self._setup_model()
        self.first_show = False
        self.group_manage_widget = None
        self.user_manage_widget = None
        self.platform_manage_widget = None
        self.permission_manage_widget = None
        self._platform_manage = None
        self.ui.listView.choose_item_signal.connect(
            self._edit_item
        )

    @property
    def ui(self):
        return self._ui

    @property
    def item(self):
        return self._item

    def _edit_item(self, item: Dict):
        self.set_item(item)
        self.ui.listView.hide_placeholder_text()

    def reset_window_layout(self):
        if self.is_super:
            self.ui.actionPlatformMannage.setVisible(True)
        else:
            self.ui.actionPlatformMannage.setVisible(False)
        # if self.is_super:
        #     # self.ui.actionGroupManage.setVisible(True)
        #     # self.ui.actionUserManage.setVisible(True)
        #     self.ui.actionPlatformMannage.setVisible(True)
        #
        #     self.ui.actionPlatformGroup.setVisible(True)
        #     # self.ui.actionPlatformUser.setVisible(True)
        #     self.ui.actionNormalGroup.setVisible(True)
        #     # self.ui.actionNormalUser.setVisible(True)
        #
        #     # self.ui.actionPlatformPermission.setVisible(True)
        #     self.ui.GroupBox.setVisible(True)
        #     self.ui.UserBox.setVisible(True)
        #     self.ui.listView.right_click_menu.delete.setVisible(True)
        # elif self.is_admin:
        #     # self.ui.actionGroupManage.setVisible(False)
        #     # self.ui.actionUserManage.setVisible(True)
        #     self.ui.actionPlatformMannage.setVisible(False)
        #
        #     self.ui.actionPlatformGroup.setVisible(False)
        #     # self.ui.actionPlatformUser.setVisible(False)
        #     self.ui.actionNormalGroup.setVisible(False)
        #     self.ui.actionNormalUser.setVisible(True)
        #
        #     # self.ui.actionPlatformPermission.setVisible(False)
        #     self.ui.GroupBox.setVisible(False)
        #     self.ui.UserBox.setVisible(True)
        #     self.ui.listView.right_click_menu.delete.setVisible(False)
        #
        # else:
        #     # self.ui.actionGroupManage.setVisible(False)
        #     # self.ui.actionUserManage.setVisible(False)
        #     self.ui.actionPlatformMannage.setVisible(False)
        #
        #     self.ui.actionPlatformGroup.setVisible(False)
        #     # self.ui.actionPlatformUser.setVisible(False)
        #     self.ui.actionNormalGroup.setVisible(False)
        #     # self.ui.actionNormalUser.setVisible(False)
        #
        #     # self.ui.actionPlatformPermission.setVisible(False)
        #     self.ui.GroupBox.setVisible(False)
        #     self.ui.UserBox.setVisible(False)
        #     self.ui.listView.right_click_menu.delete.setVisible(False)

    def load_default_data(self):
        self.ui.UserBox.clear()
        self.group_list = ["platform", "normal"]
        if self.is_super:
            self.user_list = self.gui.backend.db.query_usernames().get("data") or []
            self.user_list = [""] + self.user_list
        elif self.is_admin:
            self.user_list = self.gui.backend.db.query_usernames(self.group_name).get("data") or []
            self.user_list = [""] + self.user_list
        else:
            self.user_list = [self.username]

        self.ui.UserBox.clear()
        self.ui.GroupBox.clear()
        self.ui.UserBox.addItems(self.user_list)
        self.ui.GroupBox.addItems(self.group_list)

    def _setup_model(self):
        self.table_model_perms_note = QTableModelPermsNote(
            self.gui, self, self._ui.listView
        )
        self._ui.listView.setModel(self.table_model_perms_note)

        self.context_tree_model = QTreeModelPermsNote(
            self, self.gui, self.ui.treeView
        )
        self.ui.treeView.setModel(self.context_tree_model)
        self.tree_model_delegate = QOptionsDelegate(self)
        self.ui.treeView.setItemDelegate(self.tree_model_delegate)

    def refresh(self):
        self.load_default_data()

    def change_page(self, index: int):
        if index:
            self.query_perms_note()

    def change_volume(self, index: int):
        if index:
            self.query_perms_note()

    def change_perm_type(self, index: int):
        if self.ui.GroupBox.currentText() == "platform":
            self.perms_list = self.gui.backend.db.query_perms_list(group="group", platform_ident="platform_ident").get("data") or []
        else:
            self.perms_list = self.gui.backend.db.query_perms_list().get("data") or []
        self.ui.PermissionBox.clear()
        self.ui.PermissionBox.addItem("", "")
        for perms in self.perms_list:
            self.ui.PermissionBox.addItem(perms["name"], perms["id"])

    def query_perms_note(self):
        target = self.ui.UserBox.currentText()
        types = self.ui.GroupBox.currentText()
        perms_id = self.ui.PermissionBox.currentData()
        page_num = self.ui.PageBox.value() or 1
        page_size = self.ui.VolumeBox.value() or 10
        ret_data = self.gui.backend.db.query_perms_note(target, types,
                                                        perms_id, page_num, page_size)
        if ret_data.get("code") == 200:
            self.data = ret_data["data"]
            self.table_model_perms_note.refresh_auto(check_count=False)
        else:
            self.data = []
            self.table_model_perms_note.refresh_auto()

        # qml接入示例
        # from PySide6.QtQuickWidgets import QQuickWidget
        # from PySide6.QtQuick import QQuickView
        # from PySide6.QtCore import QUrl, Slot
        # from pyqcat_visage.gui.case1 import main
        # from pyqcat_visage.structures import PermsItem
        # from pyqcat_visage.tool.utilies import check_perms
        # from pathlib import Path
        # self.qquick_widget = QQuickWidget()
        # self.qquick_widget.setResizeMode(QQuickWidget.SizeRootObjectToView)
        # path_ = Path(__file__).parent.parent.joinpath("case_qml/QmlIntegration/Main.qml")
        # self.qquick_widget.setSource(QUrl.fromLocalFile(path_.__str__()))

    def show(self):
        if not self.first_show:
            self.load_default_data()
            self.first_show = True
        super().show()

    def user_manage(self):
        if not self.is_super and not self.is_admin:
            return
        if self.user_manage_widget:
            self.user_manage_widget.close_()
        self.user_manage_widget = PermsOperateWindow(self.gui, user_flag=True)
        self.user_manage_widget.show()

    def group_manage(self):
        if not self.is_super:
            return
        if self.group_manage_widget:
            self.group_manage_widget.close_()
        self.group_manage_widget = PermsOperateWindow(self.gui, user_flag=False)
        self.group_manage_widget.show()

    def platform_manage(self):
        if not self.is_super:
            return
        if self.platform_manage_widget:
            self.platform_manage_widget.close_()
        self.platform_manage_widget = PermsPlatformWindow(self.gui)
        self.platform_manage_widget.show()

    def permission_manage(self):
        if self.permission_manage_widget:
            self.permission_manage_widget.close_()
        self.permission_manage_widget = PermissionManageWindow(self.gui, parent=self)
        self.permission_manage_widget.show()

    def force_refresh(self):
        """Force refresh."""
        self.context_tree_model.load()

    def set_item(self, item=None):
        self._item = item

        if item is None:
            self.force_refresh()
            return

        # Labels
        # ) from {space.__class__.__module__}
        # label_text = f"{space.data_dict.name} | {space.data_dict.class_name}"
        # self.ui.labelComponentName.setText(label_text)
        # self.ui.labelComponentName.setCursorPosition(0)  # Move to left
        # self.setWindowTitle(label_text)
        # self.parent().setWindowTitle(label_text)

        self.force_refresh()

        self.ui.treeView.autoresize_columns()  # resize columns

        data_dict = item
        if not isinstance(item, dict):
            data_dict = item.to_dict()

        # self.ui.textEdit.setText(json.dumps(data_dict, indent=4))

    def _look_item(self, item):
        self.set_item(item)
        self.ui.treeView.hide_placeholder_text()

    def platform_permission(self):
        if self._platform_manage:
            self._platform_manage.show()
            return
        self._platform_manage = PlatformManageWindow(self.gui, self)
        self._platform_manage.show()
