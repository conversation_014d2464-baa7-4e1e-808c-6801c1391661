# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/16
# __author:       <PERSON><PERSON><PERSON>

import asyncio
import os

from .log import Logger, logger
from .structure import settings
from .transfer import Transfer
from .util import remove_ipc_file

NAME = r"""
      ___           ___           ___           ___           ___           ___           ___           ___     
     /\  \         /\  \         /\  \         /\__\         /\  \         /\  \         /\  \         /\  \    
     \:\  \       /::\  \       /::\  \       /::|  |       /::\  \       /::\  \       /::\  \       /::\  \   
      \:\  \     /:/\:\  \     /:/\:\  \     /:|:|  |      /:/\ \  \     /:/\:\  \     /:/\:\  \     /:/\:\  \   
      /::\  \   /::\~\:\  \   /::\~\:\  \   /:/|:|  |__   _\:\~\ \  \   /::\~\:\  \   /::\~\:\  \   /::\~\:\  \ 
     /:/\:\__\ /:/\:\ \:\__\ /:/\:\ \:\__\ /:/ |:| /\__\ /\ \:\ \ \__\ /:/\:\ \:\__\ /:/\:\ \:\__\ /:/\:\ \:\__\
    /:/  \/__/ \/_|::\/:/  / \/__\:\/:/  / \/__|:|/:/  / \:\ \:\ \/__/ \/__\:\ \/__/ \:\~\:\ \/__/ \/_|::\/:/  /
   /:/  /         |:|::/  /       \::/  /      |:/:/  /   \:\ \:\__\        \:\__\    \:\ \:\__\      |:|::/  / 
   \/__/          |:|\/__/        /:/  /       |::/  /     \:\/:/  /         \/__/     \:\ \/__/      |:|\/__/  
                  |:|  |         /:/  /        /:/  /       \::/  /                     \:\__\        |:|  |    
                   \|__|         \/__/         \/__/         \/__/                       \/__/         \|__|    
"""


def _set_up_logger(process_name, log_path):
    """Set up logger."""

    transfer_logger = Logger(
        process_name=process_name, log_path=log_path, process_safe=True
    )

    transfer_logger.set_log_level()
    transfer_logger.save_log()

    logger.warning(
        f"\n\n{NAME}\n\nInitialize transfer-{process_name} logger successfully {os.getpid()}. "
        f"Data transfer log will save at: {transfer_logger.log_path}",
    )
    
    return transfer_logger


def main(process_name: str = "", log_path: str = ""):
    """Main function."""
    transfer_logger = _set_up_logger(process_name, log_path)
    transfer = Transfer(settings, process_name)
    asyncio.run(transfer.run())
    remove_ipc_file(transfer_logger.log_ipc_file)


if __name__ == "__main__":
    main()
