# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2020/11/21
# __author:       <PERSON>

"""
SingleShot is used to get the probability that the qubit is in state 0 and state 1.
"""

import math
import pickle
from typing import List

import pandas as pd

from ....analysis.library.single_shot_analysis import (
    IQTrackSingleShotAnalysis,
    SingleShotAnalysis,
)
from ....log import pyqlog
from ....parsefile import update_config_data
from ....pulse.base_pulse import PulseComponent
from ....pulse.pulse_function import f12_pi_pulse, pi_pulse, zero_pulse
from ....pulse.pulse_lib import Constant
from ....qaio_property import QAIO
from ....qubit import Qubit
from ....structures import MetaData, Options
from ....types import QualityDescribe
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class SingleShot(TopExperiment):
    """In quantum bits, usually only read |0>, |1>, |2> probability.
    And this is called projection measurement.

    """

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            level_str (str): Mark energy level string, `01`, `02`,`012`
            is_check (bool): Is or not check discriminator quality.
            save_bin (bool): Save bin file or not.

        """
        options = super()._default_experiment_options()

        options.set_validator("level_str", ["01", "02", "012"])
        # options.set_validator('save_bin', bool)
        # options.set_validator('is_check', bool)

        options.level_str = "01"
        options.save_bin = False
        options.is_check = False
        options.repeat = 5000

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options.

        Analysis Options:
            level_str (str): Mark energy level string, `01`, `02`,`012`
            n_clusters (int): Preset clustering number.
            method (str): classifier model name, `GMM` or `KMeans`
            n_multiple (float): Calculate cluster radius,
                                multiple of standard deviation.
                                Default set `3.0`.
            is_plot (bool): Set ``True`` to create figure for fit result.
            quality_bounds (Iterable[float]): The bounds value of the
                                              goodness of fit.
            result_parameters (List): result data key list.
        """
        # quality_bounds, meaning of each parameter, just default
        # k_recommend = 2, F0 >= 0.85, F1 >= 0.7, outlier <= 0.011

        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("method", ["GMM", "KMeans"])
        options.set_validator("n_multiple", float)
        options.set_validator("set_proportion", bool)
        options.set_validator("heat_stimulate", bool)
        options.set_validator("plot_circle", bool)
        options.set_validator("remove_outlier", bool)
        options.set_validator("check_k", bool)

        options.level_str = "01"  # This parameter is not open to users set.
        options.n_clusters = 2
        options.method = "GMM"
        options.n_multiple = 3.0
        options.quality_bounds = [2, 0.85, 0.7, 0.011]
        options.result_parameters = []
        options.figsize = (7, 6)
        options.set_proportion = False
        options.heat_stimulate = False
        options.plot_circle = False
        options.remove_outlier = False
        options.check_k = False

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default set run options of experiment.

        Options:
            target_name (str): Save bin file name, like`q0.bin` or `c0.bin`.

        """
        options = super()._default_run_options()
        options.target_name = None

        options.x_data = []
        options.analysis_class = SingleShotAnalysis
        options.injection_func = [
            "create_xy_pulses",
            "stimulate_state_zero",
            "stimulate_state_one",
            "stimulate_state_two",
        ]
        options.support_context = [StandardContext.QC, StandardContext.CPC]

        return options

    def _update_loop_num(self):
        """Update experiment repeat and loop_num."""
        repeat = self.experiment_options.repeat
        if repeat > 10000:
            div, mod = divmod(repeat, 10000)
            if mod != 0:
                div += 1
            loop_num = div
            repeat = math.ceil(repeat / loop_num)
        else:
            loop_num = 1

        return repeat, loop_num

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulse."""
        loop_num = self.experiment_options.loop_num
        level_str = self.experiment_options.level_str

        xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
        self.play_pulse("XY", self.qubit, xy_pulse_list)

    def create_xy_pulses(
        self, qubit: Qubit, level_str: str = None, loop_num: int = 1
    ) -> List[PulseComponent]:
        """Create single qubit xy pulses by level_str."""
        drive_zero_pulse = self.stimulate_state_zero(qubit)
        drive_one_pulse = self.stimulate_state_one(qubit)

        xy_pulse_list = []
        if level_str == "02":
            drive_two_pulse = self.stimulate_state_two(qubit)
            xy_pulse_list.extend([drive_zero_pulse] * loop_num)
            xy_pulse_list.extend([drive_two_pulse] * loop_num)
        elif level_str == "012":
            drive_two_pulse = self.stimulate_state_two(qubit)
            xy_pulse_list.extend([drive_zero_pulse] * loop_num)
            xy_pulse_list.extend([drive_one_pulse] * loop_num)
            xy_pulse_list.extend([drive_two_pulse] * loop_num)
        else:
            # default value "01"
            xy_pulse_list.extend([drive_zero_pulse] * loop_num)
            xy_pulse_list.extend([drive_one_pulse] * loop_num)

        return xy_pulse_list

    @staticmethod
    def stimulate_state_zero(qubit: Qubit):
        zero_wave = zero_pulse(qubit)
        return zero_wave()

    @staticmethod
    def stimulate_state_one(qubit: Qubit):
        pi_wave = pi_pulse(qubit)
        return pi_wave()

    @staticmethod
    def stimulate_state_two(qubit: Qubit):
        pi_wave = pi_pulse(qubit)
        f12_pi = f12_pi_pulse(qubit)
        return pi_wave() + f12_pi()

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        readout_freq_dict = {}
        readout_power_dict = {}
        level_str = self.experiment_options.level_str

        for qubit in self.qubits:
            probe_freq = qubit.probe_freq
            probe_power = qubit.probe_power
            readout_freq_dict.update({qubit.name: probe_freq})
            readout_power_dict.update({qubit.name: probe_power})
        for coupler in self.couplers:
            probe_freq = coupler.probe_freq
            probe_power = coupler.probe_power
            readout_freq_dict.update({coupler.name: probe_freq})
            readout_power_dict.update({coupler.name: probe_power})

        metadata.draw_meta = {
            "level_str": level_str,
            "readout_freq": readout_freq_dict,
            "readout_power": readout_power_dict,
        }
        metadata.process_meta = {
            "unit": self.coupler.name if self.couplers else self.qubit.name
        }
        return metadata

    def _check_options(self):
        """Before run, some operations."""
        super()._check_options()
        self.discriminator = None
        repeat, loop_num = self._update_loop_num()
        pyqlog.debug(f"Final repeat: {repeat}, loop_num: {loop_num}")

        self.set_experiment_options(
            repeat=repeat,
            data_type="I_Q",
            loop_num=loop_num,
            is_dynamic=0,
            iq_flag=True,
        )

        # adjust coupler probe bit calibration
        target_name = self.run_options.target_name
        if self.coupler and self.is_coupler_exp is False:
            target_name = self.coupler.name
            result_parameters = ["dcm"]
        elif self.qubit:
            target_name = self.qubit.name
            result_parameters = ["dcm"]
        else:
            result_parameters = [qubit.name for qubit in self.qubits]

        self.set_run_options(target_name=target_name)
        self.set_analysis_options(result_parameters=result_parameters)

        level_str = self.experiment_options.level_str
        if level_str and level_str in ["01", "02", "012"]:
            self.set_analysis_options(level_str=level_str, n_clusters=len(level_str))
            self._label = f"{self._label}_{level_str}"

        self.run_options.x_data = list(range(loop_num * 2))

    def _alone_save_result(self):
        """Alone save some special result."""
        target_name = self.run_options.target_name
        self.discriminator = self.analysis.results.get("dcm").value

        quality = self.analysis.quality
        if quality.descriptor not in [QualityDescribe.perfect, QualityDescribe.normal]:
            # optimize, SingleShot not according to quality save bin.
            pyqlog.warning(
                f"{self.qubit.name} classifier quality: {quality.descriptor}, "
                f"your quality bounds is {self.analysis_options.quality_bounds}, "
                f"please update quality bounds or restart calibration!"
            )
            # return

        # save bin file
        save_bin = self.experiment_options.save_bin
        save_result = self.experiment_options.save_result
        if save_bin is True or save_result is True:
            self.save_bin_file(target_name, self.discriminator)

        iq_data = self.experiment_data.y_data.get(target_name)
        self.file.save_data(iq_data.T, name=f"{self}({target_name}-IQ)")

    def save_bin_file(self, save_name, discriminator):
        """Save discriminator."""
        level_str = self.experiment_options.level_str
        if level_str:
            store_name = f"{save_name}_{level_str}.bin"
        else:
            store_name = f"{save_name}.bin"

        discriminator.I_list.clear()
        discriminator.Q_list.clear()
        dcm_bin = pickle.dumps(discriminator)

        # save to data service
        update_config_data(store_name, dcm_bin, discriminator.to_dict())

        # save to local disk
        # self.file.save_text(dcm_bin, name=save_name, prefix=".bin")

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        eop = self.experiment_options
        for key, result in self.analysis.results.items():
            if key == "dcm" and result.value:
                result.extra["path"] = f"IQdiscriminator.{eop.level_str}"


class PrepareSingleShot(SingleShot):
    def _check_options(self):
        dcm = self.discriminator
        super()._check_options()
        self.discriminator = dcm


class FixedMeasureStartSingleShot(SingleShot):
    """Fixed measure start time SingleShot experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()
        options.set_validator("measure_start", float)

        options.measure_start = 100
        return options

    def _check_options(self):
        """Check options."""
        super()._check_options()

        measure_start = self.experiment_options.measure_start
        target_name = self.run_options.target_name

        new_ms_start = QAIO.delay_ceil(measure_start)
        pyqlog.info(f"Fixed {target_name} measure_start: {new_ms_start} ns")
        self.set_experiment_options(measure_start=new_ms_start)

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulse."""
        loop_num = self.experiment_options.loop_num
        level_str = self.experiment_options.level_str
        measure_start = self.experiment_options.measure_start
        target_name = self.run_options.target_name

        xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
        new_xy_pulses = []
        for xy_pulse in xy_pulse_list:
            diff_width = measure_start - xy_pulse.width
            if diff_width > 0:
                offset_pulse = Constant(diff_width, 0.0, name="XY")()
                new_xy_pulse = xy_pulse + offset_pulse
            else:
                pyqlog.warning(
                    f"Fixed {target_name} measure_start: {measure_start}, "
                    f"origin xy_pulse width: {xy_pulse.width}, "
                    f"maybe add large measure_start time!"
                )
                new_xy_pulse = xy_pulse
            new_xy_pulses.append(new_xy_pulse)

        self.play_pulse("XY", self.qubit, new_xy_pulses)


class IQTrackSingleShot(SingleShot):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("offset", float)
        options.offset = 0

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super(SingleShot, cls)._default_analysis_options()
        return options

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.draw_meta = {
            "level_str": self.experiment_options.level_str,
            "readout_freq": self.qubit.probe_freq,
            "readout_power": self.qubit.probe_power,
            "offset": self.experiment_options.offset,
        }
        metadata.process_meta = {
            "unit": self.coupler.name if self.couplers else self.qubit.name,
        }
        return metadata

    def _check_options(self):
        """Before run, some operations."""
        super(SingleShot, self)._check_options()
        self.discriminator = None
        repeat, loop_num = self._update_loop_num()
        pyqlog.debug(f"Final repeat: {repeat}, loop_num: {loop_num}")

        self.set_experiment_options(
            repeat=repeat,
            data_type="track",
            loop_num=loop_num,
            is_dynamic=0,
            iq_flag=True,
            file_flag=1,
        )

        level_str = self.experiment_options.level_str
        self._label = f"{self._label}_{level_str}"

        self.run_options.x_data = list(range(loop_num * 2))
        self.run_options.analysis_class = IQTrackSingleShotAnalysis

    def _alone_save_result(self):
        save_data = {"time": self.experiment_data.x_data}
        save_data.update(self.experiment_data.y_data)
        df = pd.DataFrame(save_data)
        df.to_csv(rf"{self.file.dirs}/{self}.csv", index=False)

    def _set_result_path(self):
        pass

    @staticmethod
    def update_instrument(builder):
        offset = builder.experiment_options.offset
        builder.inst.set_sample_delay(
            builder.qubit.readout_channel, builder.qubit.sample_delay + offset
        )
