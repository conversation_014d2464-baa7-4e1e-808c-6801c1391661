<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TitleWindow</class>
 <widget class="QWidget" name="TitleWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>780</width>
    <height>347</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>PyQCat-Visage</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QGridLayout" name="gridLayoutTitle">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QWidget" name="TitleWidget" native="true">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>50</height>
      </size>
     </property>
     <property name="cursor">
      <cursorShape>ArrowCursor</cursorShape>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="inputMethodHints">
      <set>Qt::InputMethodHint::ImhNone</set>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayoutTitle" stretch="1,3,5,1,1,1">
      <property name="spacing">
       <number>5</number>
      </property>
      <property name="leftMargin">
       <number>5</number>
      </property>
      <property name="topMargin">
       <number>5</number>
      </property>
      <property name="rightMargin">
       <number>5</number>
      </property>
      <property name="bottomMargin">
       <number>5</number>
      </property>
      <item>
       <widget class="QLabel" name="IconLabel">
        <property name="minimumSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::TextFormat::AutoText</enum>
        </property>
        <property name="pixmap">
         <pixmap resource="_imgs/_imgs.qrc">:/login-logo.png</pixmap>
        </property>
        <property name="scaledContents">
         <bool>true</bool>
        </property>
        <property name="wordWrap">
         <bool>false</bool>
        </property>
        <property name="openExternalLinks">
         <bool>true</bool>
        </property>
        <property name="textInteractionFlags">
         <set>Qt::TextInteractionFlag::NoTextInteraction</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="TitleLabel">
        <property name="text">
         <string>PyQCat: Quantum Chip Calibration</string>
        </property>
        <property name="textFormat">
         <enum>Qt::TextFormat::AutoText</enum>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="WindowMin">
        <property name="minimumSize">
         <size>
          <width>35</width>
          <height>20</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>35</width>
          <height>20</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>PointingHandCursor</cursorShape>
        </property>
        <property name="toolTip">
         <string>最小化</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="iconSize">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
        <property name="autoDefault">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="WindowMax">
        <property name="minimumSize">
         <size>
          <width>35</width>
          <height>20</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>35</width>
          <height>20</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>PointingHandCursor</cursorShape>
        </property>
        <property name="toolTip">
         <string>最大化</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="WindowClose">
        <property name="minimumSize">
         <size>
          <width>35</width>
          <height>20</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>35</width>
          <height>20</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>PointingHandCursor</cursorShape>
        </property>
        <property name="toolTip">
         <string>关闭</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QWidget" name="RealWidget" native="true">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="minimumSize">
      <size>
       <width>35</width>
       <height>30</height>
      </size>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="_imgs/_imgs.qrc"/>
  <include location="_imgs/_imgs.qrc"/>
 </resources>
 <connections/>
</ui>
