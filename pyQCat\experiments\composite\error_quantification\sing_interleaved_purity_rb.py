# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/11/08
# __author:       xw

from ....analysis import SingInterleavedPurityRBAnalysis
from ....gate import GateCollection
from ....structures import Options
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import PurityRBSingle


class PurityRBInterleavedSingle(CompositeExperiment):
    _sub_experiment_class = PurityRBSingle

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("interleaved_gate", GateCollection.single_gate_infos())
        options.interleaved_gate = "I"
        options.run_mode = ExperimentRunMode.async_mode
        return options

    def _check_options(self):
        super()._check_options()
        self.set_analysis_options(result_name=self.qubits[0].name)
        self.set_run_options(
            x_data=[0, 1], analysis_class=SingInterleavedPurityRBAnalysis
        )

    def _setup_child_experiment(self, exp: PurityRBSingle, index: int, vol):
        interleaved_gate = (
            None if index == 0 else self.experiment_options.interleaved_gate
        )
        exp.run_options.index = index
        total = len(self.run_options.x_data)
        describe = "Reference RB" if index == 0 else "Interleaved CZ RB"
        exp.set_parent_file(self, describe, index, total)
        exp.set_experiment_options(interleaved_gate=interleaved_gate)
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp: PurityRBSingle):
        exp.analysis.provide_for_parent.update({"analysis": exp.analysis})
