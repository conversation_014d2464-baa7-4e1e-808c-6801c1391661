# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.calculate_offset_arr.rst:2
msgid "pyQCat.analysis.algorithms.calculate\\_offset\\_arr"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_offset_arr:1
msgid "Calculate z offset array, by response array."
msgstr "通过畸变测试的原始 response array, 反推 z_offset_arr"

#: of pyQCat.analysis.algorithms.distortion.calculate_offset_arr
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_offset_arr:4
msgid "Array of response, distortion origin response."
msgstr "畸变测试的原始 response array"

#: of pyQCat.analysis.algorithms.distortion.calculate_offset_arr:7
msgid "When set z line amp value."
msgstr "畸变测试时, Z ac线施加的固定幅值"

#: of pyQCat.analysis.algorithms.distortion.calculate_offset_arr
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_offset_arr:10
msgid "Array of z offset."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_offset_arr
msgid "Return type"
msgstr ""

