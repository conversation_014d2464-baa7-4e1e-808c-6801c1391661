# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/15
# __author:       <PERSON><PERSON><PERSON>

import json
import os
import re
from enum import Enum
from typing import List, Union

from prettytable import PrettyTable

from ...errors import BitNameError
from ...log import pyqlog
from ...qubit import NAME_PATTERN, Coupler
from ...tools.allocation import ReadoutAmpAllocation
from ..batch_experiment import EXP_TYPE, BatchExperiment


class CavityState(str, Enum):
    IS = "init state"
    BR = "bad and reset"
    FR = "false and reset"
    TT = "try individual test"

    TG = "true and good"
    TB = "true and bad"
    QB = "probe and drive qubit bad"

    ER = "system error"
    IF = "goodness false"


class RunType(int, Enum):
    Qubit = 0
    Coupler = 1


class ExpCollection(str, Enum):
    CP = "CavityPowerScan"
    CF = "CavityFreqSpectrum"
    CTC = "CavityTunable_for_coupler"
    CTQ = "CavityTunable_for_qubit"


class BatchTunable(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.parallel_qubit = None
        options.parallel_coupler = None
        options.open_cavity_power = True
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.states = {}
        options.r2_map = {}
        return options

    def _batch_up(self):
        super()._batch_up()
        self._check_parallel_qubit()

    def _check_parallel_qubit(self):
        if not self.experiment_options.parallel_qubit:
            env_bits = self.context_manager.global_options.env_bits
            parallel_qubits = []
            for bit in env_bits:
                if re.match(NAME_PATTERN.qubit, bit):
                    parallel_qubits.append(bit)
            self.experiment_options.parallel_qubit = parallel_qubits

    def _run_cavity_power_scan(self):
        if self.experiment_options.open_cavity_power is True:
            if self.experiment_options.parallel_qubit:
                # run CavityPowerScan experiment
                pass_units = self._run_flow(
                    flows=["CavityPowerScan"],
                    physical_units=self.experiment_options.parallel_qubit,
                    name="cavity power flow",
                )

                # bus readout amp divide
                aller = ReadoutAmpAllocation(self.context_manager.chip_data)
                aller.allocate(pass_units)
                self.experiment_options.parallel_qubit = aller.run_options.pass_units
            else:
                pyqlog.error("Parallel qubit is empty!")

        if self.experiment_options.parallel_qubit:
            self._run_flow(
                flows=["CavityFreqSpectrum"],
                physical_units=self.experiment_options.parallel_qubit,
                name="cavity freq flow",
            )
        else:
            pyqlog.error("CavityFreqSpectrum can not find any qubit!")
            exit(0)

    def _transform_coupler(self, coupler: Union[str, Coupler]):
        if isinstance(coupler, str):
            coupler_obj: Coupler = self.context_manager.chip_data.cache_coupler.get(
                coupler
            )
        else:
            coupler_obj = coupler

        pq = coupler_obj.probe_bit
        coupler_obj.probe_bit = coupler_obj.drive_bit
        coupler_obj.drive_bit = pq
        pyqlog.log(
            "EXP",
            f"Swap {coupler_obj.name} probe qubit and drive qubit | probe {pq} to {coupler_obj.probe_bit}",
        )

    def _init_component_state(self):
        # extract env bits
        env_bits = self.context_manager.global_options.env_bits

        # init coupler status
        for coupler_list in self.experiment_options.parallel_coupler:
            for coupler_name in coupler_list:
                if coupler_name not in env_bits:
                    raise BitNameError(f"{coupler_name} not in env bits")

                if re.match(NAME_PATTERN.coupler, coupler_name):
                    coupler_obj = self.context_manager.chip_data.cache_coupler.get(
                        coupler_name
                    )

                    if coupler_obj.goodness is False:
                        self.run_options.states[coupler_name] = [CavityState.IF]
                        continue

                    pq, dq = f"q{coupler_obj.probe_bit}", f"q{coupler_obj.drive_bit}"
                    probe_qubit = self.context_manager.chip_data.cache_qubit.get(pq)
                    drive_qubit = self.context_manager.chip_data.cache_qubit.get(dq)

                    if pq not in env_bits:
                        probe_qubit.goodness = False

                    if dq not in env_bits:
                        drive_qubit.goodness = False

                    if probe_qubit.goodness is False:
                        if drive_qubit.goodness is False:
                            self.run_options.states[coupler_name] = [CavityState.QB]
                        else:
                            self.run_options.states[coupler_name] = [CavityState.FR]
                    else:
                        if drive_qubit.goodness is False:
                            self._transform_coupler(coupler_obj)
                            self.run_options.states[coupler_name] = [CavityState.FR]
                        else:
                            self.run_options.states[coupler_name] = [CavityState.IS]
                else:
                    raise BitNameError(f"{coupler_name} is not standard coupler name")

        # init qubit state
        for qubit_name in self.experiment_options.parallel_qubit:
            qubit_obj = self.context_manager.chip_data.get_physical_unit(qubit_name)
            if qubit_obj.goodness is False:
                self.run_options.states[qubit_name] = [CavityState.IF]
            else:
                self.run_options.states[qubit_name] = [CavityState.IS]

    def _show_state(self):
        table = PrettyTable()
        field_names = ["Physical unit", "State flow"]
        table.field_names = field_names
        for unit, flow in self.run_options.states.items():
            records = [str(unit), str(flow)]
            table.add_row(records)
        pyqlog.log("EXP", f"Current State Map as Follow:\n{table}")
        record_path = os.path.join(
            self.context_manager.config.system.local_root,
            self.context_manager.config.system.sample,
            self.run_options.dir_prefix,
            "states.json",
        )
        dirs = os.path.dirname(record_path)
        if not os.path.exists(dirs):
            os.makedirs(dirs, exist_ok=True)
        with open(record_path, mode="w", encoding="utf-8") as f:
            json.dump(self.run_options.states, f, indent=4, ensure_ascii=False)

    def _run_tunable(self, unit_list: [List[str]]):
        # show current all unit state
        self._show_state()

        # clarify the current task execution type
        run_type = RunType.Qubit
        if unit_list and re.match(NAME_PATTERN.coupler, unit_list[0]):
            run_type = RunType.Coupler
        exp_name = (
            ExpCollection.CTC if run_type == RunType.Coupler else ExpCollection.CTQ
        )

        # init parallel and individual unit
        act_parallel_unit = []
        individual_unit = []

        # filter unit from last state
        for unit_name in unit_list:
            last_state = self.run_options.states.get(unit_name)[-1]

            # (QB | ER | TG | TB): close flow
            if last_state in [
                CavityState.QB,
                CavityState.ER,
                CavityState.TG,
                CavityState.TB,
                CavityState.IF,
            ]:
                continue

            # (TT): TT coupler no part in parallel task
            if last_state == CavityState.TT:
                individual_unit.append(unit_name)
                continue

            # (BR | FR | IS): add parallel task
            act_parallel_unit.append(unit_name)

        # run parallel unit cavity tunable
        if act_parallel_unit:
            # build parallel experiment
            self._run_flow(
                [exp_name], act_parallel_unit, "parallel cavity tunable flow"
            )

        # run individual unit cavity tunable
        if individual_unit:
            for unit_name in individual_unit:
                # generate experiment context
                self._run_flow([exp_name], unit_name, "individual cavity tunable flow")

        # retry parallel unit
        if act_parallel_unit:
            self._run_tunable(act_parallel_unit)

    def _run_batch(self):
        self._run_cavity_power_scan()
        self._init_component_state()

        for coupler_list in self.experiment_options.parallel_coupler:
            # one parallel group run flow
            self._run_tunable(coupler_list)

        # all qubit run flow
        self._run_tunable(self.experiment_options.parallel_qubit)

        # final state show
        self._show_state()

    def _record_experiment(
        self, exp_name: str, exp: EXP_TYPE, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        print(exp_name, physical_units)

        if exp_name in [ExpCollection.CTC, ExpCollection.CTQ]:
            # check qubit or coupler
            one_bit = (
                physical_units if isinstance(physical_units, str) else physical_units[0]
            )
            is_coupler = re.match(NAME_PATTERN.coupler, one_bit)

            # perfect unit
            for unit in record.pass_units:
                self.run_options.states.get(unit).append(CavityState.TG)

            # bad unit
            for unit in record.bad_units:
                state_flow: List = self.run_options.states.get(unit)
                last_state = state_flow[-1]
                analysis_data = record.analysis_data.get(unit, {})
                if analysis_data and "tunable" in analysis_data:
                    if analysis_data.tunable is False:
                        if last_state == CavityState.IS:
                            state_flow.append(CavityState.FR)
                            if is_coupler:
                                self._transform_coupler(unit)
                        elif last_state == CavityState.BR:
                            state_flow.append(CavityState.TT)
                            if is_coupler:
                                self._transform_coupler(unit)
                        elif last_state == CavityState.FR:
                            if is_coupler:
                                state_flow.append(CavityState.TB)
                            else:
                                state_flow.append(CavityState.TT)
                        elif last_state == CavityState.TT:
                            state_flow.append(CavityState.TB)
                        else:
                            pyqlog.warning(f"{unit} no except state | {state_flow}")
                    else:
                        if last_state == CavityState.IS:
                            state_flow.append(CavityState.BR)
                            if is_coupler:
                                self.run_options.r2_map[unit] = (
                                    eval(analysis_data.get("result").get("r2")) or 0
                                )
                                self._transform_coupler(unit)
                        elif last_state == CavityState.BR:
                            state_flow.append(CavityState.TT)
                            if is_coupler and (
                                eval(analysis_data.get("result").get("r2")) or 0
                            ) < self.run_options.r2_map.get(unit, 0):
                                self._transform_coupler(unit)
                        elif last_state == CavityState.FR:
                            state_flow.append(CavityState.TT)
                        elif last_state == CavityState.TT:
                            state_flow.append(CavityState.TB)
                        else:
                            pyqlog.warning(f"{unit} no except state | {state_flow}")
                else:
                    state_flow.append(CavityState.ER)

        return record

    def _batch_down(self):
        super()._batch_down()
        if self.experiment_options.save_every_exp is True:
            for unit, state_list in self.run_options.states.items():
                if state_list[-1] == CavityState.BR:
                    pyqlog.warning(
                        f"{unit} cavity tunable bad, update tunable field to false..."
                    )
                    base_qubit = self.context_manager.chip_data.get_physical_unit(unit)
                    base_qubit.tunable = False
                    base_qubit.save_data()
