# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.get_rd_ch_bit_map.rst:2
msgid "pyQCat.tools.get\\_rd\\_ch\\_bit\\_map"
msgstr ""

#: of pyQCat.tools.utilities.get_rd_ch_bit_map:1
msgid "Get readout channel Qubit list map."
msgstr ""

#: of pyQCat.tools.utilities.get_rd_ch_bit_map
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.utilities.get_rd_ch_bit_map:4
msgid "List of Qubit object."
msgstr ""

#: of pyQCat.tools.utilities.get_rd_ch_bit_map
msgid "Returns"
msgstr ""

#: of pyQCat.tools.utilities.get_rd_ch_bit_map:7
msgid "Normal like {\"1\": [<Qubit 0>, <Qubit 1>]}"
msgstr ""

#: of pyQCat.tools.utilities.get_rd_ch_bit_map
msgid "Return type"
msgstr ""

