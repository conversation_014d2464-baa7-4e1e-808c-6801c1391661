# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.create_verify_pulse.rst:2
msgid "pyQCat.analysis.algorithms.create\\_verify\\_pulse"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.create_verify_pulse:1
msgid "Create an ideal pulse, may be used to verify distortion."
msgstr "创建验证验证波形数据, 由多组平顶高斯波组合而成"

#: of pyQCat.analysis.algorithms.distortion.create_verify_pulse
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.create_verify_pulse:4
msgid "Model pulse repeat times."
msgstr "平顶高斯波的数量, 默认为10"

#: of pyQCat.analysis.algorithms.distortion.create_verify_pulse:7
msgid "Sample rate, unit: GHz."
msgstr "测试比特 Z ac线的采样率, 默认1.6, 单位 GHz"

#: of pyQCat.analysis.algorithms.distortion.create_verify_pulse
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.create_verify_pulse:10
msgid "PulseComponent object."
msgstr "验证波形 pyQCat.pulse.base_pulse.PulseComponent 对象"

#: of pyQCat.analysis.algorithms.distortion.create_verify_pulse
msgid "Return type"
msgstr ""

