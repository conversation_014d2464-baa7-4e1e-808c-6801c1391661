# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:2
msgid "pyQCat.experiments.composite.Swap"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap:1
msgid "Swap experiment."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.composite.Swap.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.composite.Swap.component_experiment>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.composite.Swap.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.composite.Swap.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.composite.Swap.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.composite.Swap.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite.swap.Swap.run:1
msgid "Swap Run Logic."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.composite.Swap.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.composite.Swap.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.composite.Swap.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.composite.Swap.set_run_options>`\\ \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.Swap.rst:32
msgid "Attributes"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.composite.Swap.analysis>`\\"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.composite.Swap.analysis_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.composite.Swap.child_experiment>`\\"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.composite.Swap.experiment_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce:1:<autosummary>:1
msgid ":py:obj:`run_options <pyQCat.experiments.composite.Swap.run_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:51
msgid "Experiment options:"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:10
msgid "scan_z_amp_map (Dict): Set bit name where scan that z_amp."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:5
msgid "normal like: {"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:7
msgid "\"q1\": [0.230, 0.231, ...], \"c0\": [0.562, 0.564, ...], ..."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:10
#: pyQCat.experiments.composite.swap.Swap._default_experiment_options:46
msgid "}"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:12
msgid ""
"x_label_bit_name (str): Analysis plot x label mark bit name. z_amp_list "
"(List): Swap scan bit z_amp list. is_width (bool): Swap Analysis process "
"frequency or width. interaction_location (int): Select interaction point "
"number."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:17
msgid ""
"swap_state (str): Swap experiment initial state of QH, QL. readout_type "
"(str): Readout type. is_amend (bool): True means use fidelity_matrix "
"amend result."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:21
msgid ""
"ql_name (str): The low frequency bit name. qh_name (str): The high "
"frequency bit name."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:24
msgid ""
"scan_buffer (bool): Is or not scan Z Flattop Gaussian Pulse buffer. "
"scan_high_bit (bool): Is or not scan high frequency bit."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:27
msgid ""
"const_z_amp (float): The set z_amp of no-scan-bit. z_amp (float): The "
"z_amp of scan-bit."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:30
msgid "fixed_width (float): When scan_buffer is True,"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:31
msgid "the value is Flattop Gaussian Pulse width."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:32
msgid ""
"tc_list (List, array): List of tc, Z Flattop Gaussian Pulse tc. sigma "
"(float): The value is Flattop Gaussian Pulse sigma. buffer (float): When "
"scan_buffer is False,"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:35
msgid "the value is Flattop Gaussian Pulse buffer."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:37
msgid "parking_bits (List(str)): List of parking name,"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:38
msgid "normal like: [\"q2\", \"c0\", ...]"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:46
msgid "parking_param_dict (dict):"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:40
msgid "The parking bits parameter of Flattop Gaussian Pulse. normal like: {"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:43
msgid "\"q2\": {\"amp\": 0.4}, \"c0\": {\"amp\": 0.2}, ..."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:51
msgid "drag_assign_amp_map (dict): When QH, QL spplied excitation waveform,"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_experiment_options:49
msgid "the map of bit and amp Z Line Flattop Gaussian Pulse."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_analysis_options
#: pyQCat.experiments.composite.swap.Swap._default_experiment_options
#: pyQCat.experiments.composite.swap.Swap._default_run_options
#: pyQCat.experiments.composite.swap.Swap._metadata
msgid "Return type"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_analysis_options:11
#: pyQCat.experiments.composite.swap.Swap._default_experiment_options:53
#: pyQCat.experiments.composite.swap.Swap._default_run_options:12
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_run_options:1
msgid "Set run experiment operate options."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_run_options:10
msgid "Options:"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_run_options:4
msgid "all_z_amp_list: Used to save all bits scan z_amp_list,"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_run_options:5
msgid "where according to SwapOnce quality, effective z_amp list."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_run_options:6
msgid ""
"freq_list: Run SwapOnce, get swap frequency list. width_list: Run "
"SwapOnce, get target width list, zz_tc_list: Run SwapOnce, get zz_tc "
"value list ."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_analysis_options:1
msgid "Default analysis options."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_analysis_options:9
msgid "Analysis Options:"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_analysis_options:4
msgid "quality_bounds (Iterable[float]):"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_analysis_options:5
msgid "The bounds value of the goodness of fit."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._default_analysis_options:6
msgid ""
"is_plot (bool): Plot or not analysis fit photo. data_key (List[str]): "
"List of mark select data. x_label (str): X-axis label name."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._metadata:1
msgid "Set metadata."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._check_options:1
msgid "Check Options."
msgstr ""

#: of pyQCat.experiments.composite.swap.Swap._save_swap_json:1
msgid "Save swap json."
msgstr ""

