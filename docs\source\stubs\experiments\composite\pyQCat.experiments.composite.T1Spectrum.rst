﻿pyQCat.experiments.composite.T1Spectrum
=======================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: T1Spectrum

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~T1Spectrum.__init__
      ~T1Spectrum.component_experiment
      ~T1Spectrum.from_experiment_context
      ~T1Spectrum.get_qubit_str
      ~T1Spectrum.options_table
      ~T1Spectrum.run
      ~T1Spectrum.set_analysis_options
      ~T1Spectrum.set_experiment_options
      ~T1Spectrum.set_parent_file
      ~T1Spectrum.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~T1Spectrum.analysis
      ~T1Spectrum.analysis_options
      ~T1Spectrum.child_experiment
      ~T1Spectrum.experiment_options
      ~T1Spectrum.run_options
   
   