{"cells": [{"cell_type": "markdown", "id": "378ba5fd", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# AmpOptimize\n", "\n", "精细校准 X、X/2 门幅值\n", "\n", "## 初始化实验环境"]}, {"cell_type": "code", "execution_count": 1, "id": "24aad650", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\n"]}], "source": ["# 若用户未安装 pyqcat 包（monster），通过本地源码测试，需要执行下面操作\n", "# 若安装了 pyqcat 包（monster），跳过此步骤\n", "\n", "import os\n", "import sys\n", "\n", "\n", "package_root = \"../../../pyqcat-monster\"\n", "\n", "print(os.path.abspath(package_root))\n", "\n", "sys.path.insert(0, package_root)"]}, {"cell_type": "code", "execution_count": 2, "id": "756dba7a", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:02:47\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1m\n", "|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |        tcp://************:8088         |\n", "|    point_label     |              person_point              |\n", "|       sample       |               test_chip                |\n", "|      env_name      |                D00_1011                |\n", "|      version       |                 0.1.6                  |\n", "|=============================================================|\n", "\u001b[0m\n"]}, {"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': '92d03139d6185170c289629bb1e8218e'},\n", " 'msg': 'success'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "\n", "from pyQCat.tools import qarange\n", "from pyQCat.context import ExperimentContext\n", "from pyQCat.invoker import Invoker\n", "from pyQCat.parameters import get_parameters\n", "\n", "\n", "%matplotlib inline\n", "pd.set_option('max_colwidth', None)\n", "\n", "conf_file = r'E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf'\n", "\n", "username = \"test_04\"\n", "password = \"123456\"\n", "\n", "context = ExperimentContext(conf_file)\n", "Invoker.verify_account(username=username, password=password)"]}, {"cell_type": "markdown", "id": "2b65ab37", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 配置实验参数"]}, {"cell_type": "code", "execution_count": 3, "id": "2ea5991a", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:02:50\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1mQubit_(bit=0) is already exist in current environment.\u001b[0m\n"]}], "source": ["# context 加载实验参数\n", "\n", "q_name_list = [\"q0\"]\n", "environment_elements = [\"q0\", \"q1\", \"q2\", \"q3\", \"c0\", \"c1\", \"c2\"]\n", "\n", "context.configure_inst()\n", "context.configure_qubits(q_name_list)\n", "context.minimize_compensate()\n", "# context.maximize_compensate()\n", "context.configure_environment(environment_elements)\n", "context.configure_crosstalk_dict()\n", "context.configure_dcm(q_name_list)\n", "\n", "qubit = context.qubit"]}, {"cell_type": "code", "execution_count": 4, "id": "9f14f6ca", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["{'1': 0.744732,\n", " '2': 0.236088,\n", " '3': 0.203233,\n", " '4': 0.525287,\n", " '5': 0.0,\n", " '6': 0.0,\n", " '7': 0.0}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看DC\n", "\n", "context.working_dc"]}, {"cell_type": "code", "execution_count": 5, "id": "cc01f52c", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>parameter</th>\n", "      <th>object</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>config</td>\n", "      <td>E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>qubits</td>\n", "      <td>[Qubit_(bit=0)]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>couplers</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>discriminators</td>\n", "      <td>K=2 F0=0.9417 F1=0.7042 AVE=0.8229500000000001 OL=0.0092</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>compensates</td>\n", "      <td>{Qubit_(bit=0): PulseCorrectionQ0}</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>working_dc</td>\n", "      <td>True</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>crosstalk_dict</td>\n", "      <td>True</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        parameter  \\\n", "0          config   \n", "1          qubits   \n", "2        couplers   \n", "3  discriminators   \n", "4     compensates   \n", "5      working_dc   \n", "6  crosstalk_dict   \n", "\n", "                                                                  object count  \n", "0  E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf        \n", "1                                                        [Qubit_(bit=0)]     1  \n", "2                                                                     []     0  \n", "3               K=2 F0=0.9417 F1=0.7042 AVE=0.8229500000000001 OL=0.0092     2  \n", "4                                     {Qubit_(bit=0): PulseCorrectionQ0}     1  \n", "5                                                                   True        \n", "6                                                                   True        "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看配置信息\n", "pd.DataFrame(context.context_table())"]}, {"cell_type": "markdown", "id": "6f17fedf", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## 创建实验\n", "\n", "### X 门校准"]}, {"cell_type": "code", "execution_count": 6, "id": "843caa54", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["from pyQCat.experiments import AmpOptimize\n", "\n", "\n", "ao = AmpOptimize.from_experiment_context(context)"]}, {"cell_type": "code", "execution_count": 7, "id": "6b0c1b72", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>experiment option</td>\n", "      <td>show_result</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_shape</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_data_path</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>experiment option</td>\n", "      <td>repeat</td>\n", "      <td>1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>experiment option</td>\n", "      <td>register_pulse_save</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_flag</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_save</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_measure</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_type</td>\n", "      <td>envelop</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_measure</td>\n", "      <td>150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_real</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_index</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>experiment option</td>\n", "      <td>save_label</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>experiment option</td>\n", "      <td>is_dynamic</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>experiment option</td>\n", "      <td>fidelity_matrix</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>experiment option</td>\n", "      <td>ac_prepare_time</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>experiment option</td>\n", "      <td>readout_point_model</td>\n", "      <td>&lt;class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>experiment option</td>\n", "      <td>idle_qubits</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>experiment option</td>\n", "      <td>theta_type</td>\n", "      <td>Xpi</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>experiment option</td>\n", "      <td>amp_init</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>experiment option</td>\n", "      <td>amp_list</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>experiment option</td>\n", "      <td>points</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>experiment option</td>\n", "      <td>N</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>experiment option</td>\n", "      <td>threshold</td>\n", "      <td>(0.9, 1.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>analysis option</td>\n", "      <td>is_plot</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>analysis option</td>\n", "      <td>figsize</td>\n", "      <td>(12, 8)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>analysis option</td>\n", "      <td>data_key</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>analysis option</td>\n", "      <td>result_parameters</td>\n", "      <td>[Amp(repr: X-amp, unit: V)]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>analysis option</td>\n", "      <td>quality_bounds</td>\n", "      <td>[0.98, 0.95, 0.85]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 type                   name  \\\n", "0   experiment option            show_result   \n", "1   experiment option        simulator_shape   \n", "2   experiment option    simulator_data_path   \n", "3   experiment option                 repeat   \n", "4   experiment option    register_pulse_save   \n", "5   experiment option          schedule_flag   \n", "6   experiment option          schedule_save   \n", "7   experiment option       schedule_measure   \n", "8   experiment option          schedule_type   \n", "9   experiment option  schedule_show_measure   \n", "10  experiment option     schedule_show_real   \n", "11  experiment option         schedule_index   \n", "12  experiment option             save_label   \n", "13  experiment option             is_dynamic   \n", "14  experiment option        fidelity_matrix   \n", "15  experiment option        ac_prepare_time   \n", "16  experiment option    readout_point_model   \n", "17  experiment option            idle_qubits   \n", "18  experiment option             theta_type   \n", "19  experiment option               amp_init   \n", "20  experiment option               amp_list   \n", "21  experiment option                 points   \n", "22  experiment option                      N   \n", "23  experiment option              threshold   \n", "24    analysis option                is_plot   \n", "25    analysis option                figsize   \n", "26    analysis option               data_key   \n", "27    analysis option      result_parameters   \n", "28    analysis option         quality_bounds   \n", "\n", "                                               value  \n", "0                                               True  \n", "1                                               None  \n", "2                                               None  \n", "3                                               1000  \n", "4                                              False  \n", "5                                               True  \n", "6                                               True  \n", "7                                               True  \n", "8                                            envelop  \n", "9                                                150  \n", "10                                              True  \n", "11                                                -1  \n", "12                                              None  \n", "13                                                 1  \n", "14                                              None  \n", "15                                                 0  \n", "16  <class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'>  \n", "17                                                []  \n", "18                                               Xpi  \n", "19                                              None  \n", "20                                              None  \n", "21                                                61  \n", "22                                                 7  \n", "23                                        (0.9, 1.1)  \n", "24                                              True  \n", "25                                           (12, 8)  \n", "26                                              None  \n", "27                       [Amp(repr: X-amp, unit: V)]  \n", "28                                [0.98, 0.95, 0.85]  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(ao.options_table())"]}, {"cell_type": "markdown", "id": "72619881", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["**experiment options**\n", "\n", "*辅助功能选型*\n", "- simulator_data_path (str): 模拟数据地址\n", "- simulator (bool): 是否开启模拟器\n", "- repeat (int): 小循环次数\n", "- fidelity_matrix (np.ndarray): 保真度矩阵，支持外部传入\n", "- show_result (bool): 是否在日志中展示分析结果\n", "- save_lable (str): 采集数据存储标签，用于生成文件名，默认为空\n", "- is_dynamic (int): 是否进行动态绘图，默认为1，单比特读取动态绘图，0关闭动态绘图\n", "\n", "*脉冲时序选项*\n", "- schedule_flag (bool): 是否绘制脉冲时序图\n", "- schedule_save (bool): 是否存储脉冲时序图\n", "- schedule_measure (bool): 是否绘制测量波形\n", "- schedule_type (str): 脉冲时序图的类型，支持 squence 和 envelop 两种\n", "- schedule_index (int or list(int)): 绘制脉冲时序图的索引\n", "- register_pulse_save (bool): 波形存储数据\n", "\n", "*实验参数选项*\n", "\n", "- amp_list (list): 扫描 Drag 波形幅值\n", "- amp_init (float): 初始幅值\n", "- threshold (tuple): 幅值扫描左右范围\n", "- points (int): 扫描点数，默认为61\n", "- theta_type (str): 校准类型，默认为 Xpi\n", "- N (int): 门个数，默认为7\n", "\n", "\n", "**analysis options**\n", "- data_keys (int): 寻峰窗口长度，默认为11\n", "- result_parameters (list): 实验结果参数\n", "- quality_bounds (list): 拟合质量评估阈值\n", "\n", "\n", "幅值优化实验中，我们实现了：\n", "\n", "- 选项 `theta_type` 仅支持 `Xpi` 和 `Xpi/2` 两种类型；\n", "- 如果选项 `amp_list` 为空，则使用选项 `amp_init` 和 `threshold` 进行构造；\n", "- 如果选项 `amp_init` 为空，则从比特对象 `XYwave` 属性中加载；\n", "- 如果选项 `theta_type` 为 `Xpi/2` 时，选项 `N` 必须为偶数；\n", "- 选项 `data_key` 会根据 `theta_type` 和 `N` 进行调整；"]}, {"cell_type": "code", "execution_count": 8, "id": "83199b24", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:03:47\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mtheta_type: Xpi, N: 7\u001b[0m\n", "\u001b[33m2022-10-14 17:03:48\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mAmpOptimize register success, id 634925f4ba95a3ef7d85fb18\u001b[0m\n", "\u001b[33m2022-10-14 17:03:48\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\test_chip\\AmpOptimize\\q0\\2022-10-14\\17.03.47\\\u001b[0m\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "eb7be1d13ff94d3c9aae0024827ec226", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/41 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:03:49\u001b[0m | \u001b[32m RESULT \u001b[0m | \u001b[32mExperiment results as follow:\n", "========================================================\n", "| name | describe | value  | unit |      quality       | \n", "--------------------------------------------------------\n", "| Xpi  |  X-amp   | 0.6016 |  V   | R²=0.9917(perfect) | \n", "========================================================\u001b[0m\n"]}], "source": ["ao.set_experiment_options(\n", "    theta_type=\"Xpi\", N=7,\n", "    points=41,\n", "    simulator_data_path='../../scripts/simulator/data/AmpOptimize/'\n", ")\n", "\n", "ao.run()"]}, {"cell_type": "code", "execution_count": 9, "id": "66c4aa0a", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["ao.jupyter_schedule()"]}, {"cell_type": "code", "execution_count": 10, "id": "e1d11bb6", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "      <th>unit</th>\n", "      <th>extra</th>\n", "      <th>quality</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>X-amp</td>\n", "      <td>0.6016</td>\n", "      <td>V</td>\n", "      <td>{}</td>\n", "      <td>R²=0.9917(perfect)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    name   value unit extra             quality\n", "0  X-amp  0.6016    V    {}  R²=0.9917(perfect)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(ao.analysis.show_results())"]}, {"cell_type": "code", "execution_count": 11, "id": "00612ce0", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["R²=0.9917(perfect)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["ao.analysis.quality"]}, {"cell_type": "code", "execution_count": 12, "id": "049ad65b", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 2 Axes>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["ao.analysis.drawer.figure"]}, {"cell_type": "markdown", "id": "eaa41458", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## X/2 门校准"]}, {"cell_type": "code", "execution_count": null, "id": "3806a3fe", "metadata": {}, "outputs": [], "source": ["ao2 = AmpOptimize.from_experiment_context(context)\n", "\n", "ao2.set_experiment_options(\n", "    theta_type=\"Xpi/2\", N=14,\n", "    points=41,\n", "    simulator_data_path='../../test/data/AmpOptimize/AmpOptimize(p0_p1)-X2.dat'\n", ")\n", "\n", "ao2.run()"]}, {"cell_type": "code", "execution_count": 33, "id": "76ad2ed9", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["ao2.jupyter_schedule()"]}, {"cell_type": "code", "execution_count": 34, "id": "601207b6", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "      <th>unit</th>\n", "      <th>extra</th>\n", "      <th>quality</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>X2-amp</td>\n", "      <td>0.36</td>\n", "      <td>V</td>\n", "      <td>{}</td>\n", "      <td>R²=0.9905(perfect)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     name  value unit extra             quality\n", "0  X2-amp   0.36    V    {}  R²=0.9905(perfect)"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(ao2.analysis.show_results())"]}, {"cell_type": "code", "execution_count": 35, "id": "cd0e9c27", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["R²=0.9905(perfect)"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["ao2.analysis.quality"]}, {"cell_type": "code", "execution_count": 36, "id": "42d5ac2e", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x864 with 2 Axes>"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["ao2.analysis.drawer.figure"]}, {"cell_type": "code", "execution_count": null, "id": "435ff6c1", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}