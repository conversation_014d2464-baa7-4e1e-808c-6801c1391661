# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.gen_ideal_chi_matrix.rst:2
msgid "pyQCat.analysis.algorithms.gen\\_ideal\\_chi\\_matrix"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix:1
msgid "Generate Chi matrix for standard gate operations"
msgstr "生成理想门操作的 Chi 矩阵"

#: of pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix:3
msgid "Pauli matrix of the gate to be characterized."
msgstr "待表征门的泡利矩阵"

#: of pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix:4
msgid "Quantum process tomography for an operator basis `[I, X, Y, Z]`."
msgstr "量子过程层析一组操作基 ``[I, X, Y, Z]``"

#: of pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix:6
msgid "Base gate matrix list `[\"I\", \"X/2\", \"Y/2\", \"-X/2\"]`."
msgstr "基础门矩阵"

#: of pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix:8
msgid "Ideal chi matrix."
msgstr "理想情况下的 Chi 矩阵"

