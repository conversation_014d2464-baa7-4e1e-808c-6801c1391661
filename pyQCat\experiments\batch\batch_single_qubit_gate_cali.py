# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON><PERSON>

from ..batch_experiment import BatchExperiment


class BatchSingleQubitCalibration(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()

        options.flows = [
            "SingleShot",
            "QubitFreqCalibration_0",
            "XpiDetection_0",
            "SingleShot_0",
            "DetuneCalibration_0",
            "RabiScanAmp_1",
            "AmpComposite_0",
            "AmpComposite_1",
        ]
        options.affect_next_node = True
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.ppt_template.update(
            dict(
                shape=(2, 3),
                split_by_unit=True,
            )
        )
        return options

    def _run_batch(self):
        suc_units = []
        qubit_groups = self.parallel_allocator_for_qc(
            self.experiment_options.physical_units
        )
        for group, qubits in qubit_groups.items():
            passed_units = self._run_flow(
                flows=self.experiment_options.flows,
                physical_units=qubits,
                name=f"{group}/{len(qubit_groups)}: Single Gate Flow",
            )
            suc_units.extend(passed_units)
        self.bind_pass_units(suc_units)
        if self.experiment_options.save_db:
            self.backend.save_chip_data_to_db(suc_units)
