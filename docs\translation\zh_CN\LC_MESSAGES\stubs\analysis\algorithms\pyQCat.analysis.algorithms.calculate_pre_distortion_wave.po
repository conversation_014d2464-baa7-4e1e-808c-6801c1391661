# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.calculate_pre_distortion_wave.rst:2
msgid "pyQCat.analysis.algorithms.calculate\\_pre\\_distortion\\_wave"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave:1
msgid "Calculate pre distortion wave, by response and ideal wave."
msgstr "模拟理想波形经过畸变后输出的波形, 一般称为预校准波形"

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave:4
msgid "Collect distortion data delay array."
msgstr "经过计算后得到的畸变数据中的 delay array "

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave:7
msgid "Collect distortion data response array."
msgstr "经过计算后得到的畸变数据中的 response array"

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave:10
msgid "Ideal wave array."
msgstr "理想波形数据 array"

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave:13
msgid "Sample rate."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave:16
msgid "Array of pre distortion wave."
msgstr "经过畸变后输出的波形数据 array"

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave
msgid "Return type"
msgstr ""

