# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.analysis.algorithms.rst:2
msgid "pyQCat.analysis.algorithms package"
msgstr ""

#: ../../source/api/pyQCat.analysis.algorithms.rst:5
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.analysis.algorithms.rst:8
msgid "pyQCat.analysis.algorithms.distortion module"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion:1
msgid "Distortion data process function."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_offset_arr:1
#: pyQCat.analysis.algorithms:19:<autosummary>:1
msgid "Calculate z offset array, by response array."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion
#: pyQCat.analysis.algorithms.distortion.calculate_lfilter_paras
#: pyQCat.analysis.algorithms.distortion.calculate_offset_arr
#: pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave
#: pyQCat.analysis.algorithms.distortion.calculate_simulation_wave
#: pyQCat.analysis.algorithms.distortion.create_verify_pulse
#: pyQCat.analysis.algorithms.find_peak.distance_to_point
#: pyQCat.analysis.algorithms.find_peak.find_coincident_point
#: pyQCat.analysis.algorithms.find_peak.find_peaks_dips
#: pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence
#: pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point
#: pyQCat.analysis.algorithms.find_peak.get_peak_point
#: pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point
#: pyQCat.analysis.algorithms.find_peak.judge_peak_dip
#: pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset
#: pyQCat.analysis.algorithms.guess.constant_spectral_offset
#: pyQCat.analysis.algorithms.guess.cosine_fit_guess
#: pyQCat.analysis.algorithms.guess.exp_decay
#: pyQCat.analysis.algorithms.guess.fourier_cycle_guess
#: pyQCat.analysis.algorithms.guess.frequency
#: pyQCat.analysis.algorithms.guess.full_width_half_max
#: pyQCat.analysis.algorithms.guess.get_height
#: pyQCat.analysis.algorithms.guess.max_height
#: pyQCat.analysis.algorithms.guess.min_height
#: pyQCat.analysis.algorithms.guess.oscillation_exp_decay
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_radius
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._inside_k_recommend
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_probability
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.train
#: pyQCat.analysis.algorithms.smooth.smooth
#: pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix
#: pyQCat.analysis.algorithms.tomography.init_qpt
#: pyQCat.analysis.algorithms.tomography.init_qst
#: pyQCat.analysis.algorithms.tomography.qpt
#: pyQCat.analysis.algorithms.tomography.qpt_mle
#: pyQCat.analysis.algorithms.tomography.qst
#: pyQCat.analysis.algorithms.tomography.qst_mle
#: pyQCat.analysis.algorithms.tomography.tensor
#: pyQCat.analysis.algorithms.tomography.tensor_combinations
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_offset_arr:4
msgid "Array of response, distortion origin response."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_offset_arr:7
msgid "When set z line amp value."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion
#: pyQCat.analysis.algorithms.distortion.calculate_lfilter_paras
#: pyQCat.analysis.algorithms.distortion.calculate_offset_arr
#: pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave
#: pyQCat.analysis.algorithms.distortion.calculate_simulation_wave
#: pyQCat.analysis.algorithms.distortion.create_verify_pulse
#: pyQCat.analysis.algorithms.find_peak.distance_to_point
#: pyQCat.analysis.algorithms.find_peak.find_coincident_point
#: pyQCat.analysis.algorithms.find_peak.find_peaks_dips
#: pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence
#: pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point
#: pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point
#: pyQCat.analysis.algorithms.find_peak.judge_peak_dip
#: pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset
#: pyQCat.analysis.algorithms.guess.constant_spectral_offset
#: pyQCat.analysis.algorithms.guess.cosine_fit_guess
#: pyQCat.analysis.algorithms.guess.exp_decay
#: pyQCat.analysis.algorithms.guess.fourier_cycle_guess
#: pyQCat.analysis.algorithms.guess.frequency
#: pyQCat.analysis.algorithms.guess.full_width_half_max
#: pyQCat.analysis.algorithms.guess.get_height
#: pyQCat.analysis.algorithms.guess.max_height
#: pyQCat.analysis.algorithms.guess.min_height
#: pyQCat.analysis.algorithms.guess.oscillation_exp_decay
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq
#: pyQCat.analysis.algorithms.smooth.smooth
#: pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix
#: pyQCat.analysis.algorithms.tomography.init_qpt
#: pyQCat.analysis.algorithms.tomography.init_qst
#: pyQCat.analysis.algorithms.tomography.tensor
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_offset_arr:10
msgid "Array of z offset."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion
#: pyQCat.analysis.algorithms.distortion.calculate_lfilter_paras
#: pyQCat.analysis.algorithms.distortion.calculate_offset_arr
#: pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave
#: pyQCat.analysis.algorithms.distortion.calculate_simulation_wave
#: pyQCat.analysis.algorithms.distortion.create_verify_pulse
#: pyQCat.analysis.algorithms.find_peak.distance_to_point
#: pyQCat.analysis.algorithms.find_peak.find_coincident_point
#: pyQCat.analysis.algorithms.find_peak.find_peaks_dips
#: pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence
#: pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point
#: pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point
#: pyQCat.analysis.algorithms.find_peak.judge_peak_dip
#: pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset
#: pyQCat.analysis.algorithms.guess.constant_spectral_offset
#: pyQCat.analysis.algorithms.guess.cosine_fit_guess
#: pyQCat.analysis.algorithms.guess.exp_decay
#: pyQCat.analysis.algorithms.guess.fourier_cycle_guess
#: pyQCat.analysis.algorithms.guess.frequency
#: pyQCat.analysis.algorithms.guess.full_width_half_max
#: pyQCat.analysis.algorithms.guess.get_height
#: pyQCat.analysis.algorithms.guess.max_height
#: pyQCat.analysis.algorithms.guess.min_height
#: pyQCat.analysis.algorithms.guess.oscillation_exp_decay
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._check_repeat
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._fit_predict
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq
#: pyQCat.analysis.algorithms.iqprobability.get_qubits_probability
#: pyQCat.analysis.algorithms.iqprobability.simulate_qgate
#: pyQCat.analysis.algorithms.smooth.smooth
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion:1
#: pyQCat.analysis.algorithms:19:<autosummary>:1
msgid "Calculate distortion delay array, response array."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion:4
msgid "List of delay array."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion:7
msgid "List of response array."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion:10
#: pyQCat.analysis.algorithms.distortion.create_verify_pulse:7
msgid "Sample rate, unit: GHz."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion:13
msgid "Tuple object, (delay_arr, response_arr)."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_lfilter_paras:1
#: pyQCat.analysis.algorithms:19:<autosummary>:1
msgid ""
"Calculate paras of lfilter, by pole model(complex_pole_temp) fit "
"parameters."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_lfilter_paras:4
msgid "Pole model fit parameters."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_lfilter_paras:6
msgid "Paras of lfilter."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.create_verify_pulse:1
#: pyQCat.analysis.algorithms:19:<autosummary>:1
msgid "Create an ideal pulse, may be used to verify distortion."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.create_verify_pulse:4
msgid "Model pulse repeat times."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.create_verify_pulse:10
msgid "PulseComponent object."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave:1
#: pyQCat.analysis.algorithms:19:<autosummary>:1
msgid "Calculate pre distortion wave, by response and ideal wave."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave:4
msgid "Collect distortion data delay array."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave:7
msgid "Collect distortion data response array."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave:10
msgid "Ideal wave array."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave:13
msgid "Sample rate."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_pre_distortion_wave:16
#: pyQCat.analysis.algorithms.distortion.calculate_simulation_wave:4
msgid "Array of pre distortion wave."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_simulation_wave:1
#: pyQCat.analysis.algorithms:19:<autosummary>:1
msgid "Calculate simulation wave of system output, by distortion response."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_simulation_wave:7
msgid "Distortion data response array."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_simulation_wave:10
msgid "Array of system output simulation."
msgstr ""

#: ../../source/api/pyQCat.analysis.algorithms.rst:16
msgid "pyQCat.analysis.algorithms.find\\_peak module"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak:1
msgid "find peak algorithms."
msgstr "寻峰算法"

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:1
#: pyQCat.analysis.algorithms:33:<autosummary>:1
msgid "Find peaks and transform `pyQCat.structures.Point` object."
msgstr "寻峰并转化成 `pyQCat.structures.Point` 对象"

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:4
msgid "x-axis data"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:7
msgid "y-axis data"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:24
#: pyQCat.analysis.algorithms.find_peak.get_peak_point:10
msgid ""
"Required minimal horizontal distance (>= 1) in samples between "
"neighbouring peaks. Smaller peaks are removed first until the condition "
"is fulfilled for all remaining peaks."
msgstr "相邻峰之间的最小距离（>=1），先移除较小的峰，直到所有剩余峰的条件都满足为止"

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:15
msgid ""
"Required height of peaks. Either a number, ``None``, an array matching "
"`x` or a 2-element sequence of the former. The first element is always "
"interpreted as the  minimal and the second, if supplied, as the maximal "
"required height."
msgstr "所需峰的高度，既可以为一个数值也可以是一个包含两个元素的序列。当为一个值时表示低于height的信号都不考虑；当为一个序列时，第一个元素表示峰的最小高度，第二个元素表示峰的最大高度。"

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:21
msgid ""
"Required prominence of peaks. Either a number, ``None``, an array "
"matching `x` or a 2-element sequence of the former. The first element is "
"always interpreted as the  minimal and the second, if supplied, as the "
"maximal required prominence."
msgstr "峰突起程度的限制，既可以为一个数值也可以是一个包含两个元素的序列。当为一个值时表示峰值突起程度的最小值；当为一个序列时，分别表示峰值突起的最小值和最大值限制。"

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:27
msgid ""
"Peak and valley identifier; default is 1, that is to find the peak; when "
"set to 0 or False, the value of the valley"
msgstr "峰谷标志符；默认为 1，即寻找峰值；当设置为 0 或 False，寻谷值"

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point
msgid "Yields"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_peak_point:31
msgid ""
"*Point* -- `pyQCat.structures.Point` object，contains X-axis and Y-axis "
"information."
msgstr "`pyQCat.structures.Point` 对象，包含X和Y信息。"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:1
#: pyQCat.analysis.algorithms:33:<autosummary>:1
msgid "Adaptive peak-seeking algorithm based on protrusion value."
msgstr "根据突起值自适应寻峰算法"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:4
msgid ""
"This method adds some preprocessing of peak-finding, hoping to make the "
"peak-finding operation easier. The details of the algorithm are as "
"follows:"
msgstr "此方法中加入了一些寻峰的预处理，期望将寻峰操作变得更加简单，算法细节如下："

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:7
msgid ""
"First, it will be determined by the data distribution characteristics "
"that the data is more suitable for peak or valley search;"
msgstr "首先会通过数据分布特征确定数据更适合寻峰还是寻谷；"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:9
msgid ""
"We use the `scipy.signal.find_peaks` method with all default parameters "
"to find all possible peaks, and then calculate the prominence of each "
"peak, denoted as `prominence`;"
msgstr ""
"我们通过全默认参数下的 `scipy.signal.find_peaks` 方法找到所有可能性的峰，然后计算每个峰的突起程度，记为 "
"`prominence`,"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:11
msgid ""
"Select the maximum degree of protrusion as the peak-seeking condition for"
" the next peak-seeking;"
msgstr "选取最大的突起程度，作为下一次寻峰的寻峰条件；"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:12
msgid "Finally, the index and value corresponding to the peak are returned"
msgstr "最终返回峰对应的索引和数值"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:14
msgid "Most of the small peaks can be filtered in this way."
msgstr "以这样的方式可以过滤大多数细小的峰"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:17
msgid "Input signals."
msgstr "输入信号"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:20
msgid ""
"The number of peaks expected to be found, the default is 1, and the peak "
"with the most prominence."
msgstr "期望寻找峰的序号，默认为1，即突起程度最大的峰。"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:29
msgid "The index and peak value corresponding to the peak."
msgstr "峰对应的索引和峰值"

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip:1
#: pyQCat.analysis.algorithms:33:<autosummary>:1
msgid "Evaluate whether the peaks or valleys are more obvious in the data."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip:5
msgid "Find the mean, maximum and minimum values of the data set respectively;"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip:6
msgid ""
"If the difference between the maximum value and the mean value is greater"
" than the difference between the mean value and the minimum value, the "
"peak data is considered, and the subsequent peak search operation can be "
"performed;"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip:9
msgid ""
"On the contrary, the valley data is considered, and the subsequent valley"
" search operation is performed;"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip:13
msgid "data to be evaluated."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip:16
msgid "`peaks` or `dips`, `peaks` for peak data, `dips` for valley data"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_dips:1
msgid "Basic peak-finding (valley) algorithm"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_dips:4
msgid "X-axis data"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_dips:7
msgid "Y-axis data"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_dips:10
msgid ""
"Interpolation number. In some cases, the peak width is too narrow, and "
"only one point has a relatively obvious protrusion. In this case, the "
"difference function can be used to describe the data distribution state "
"more clearly. This value represents the number of differences. If the "
"original data length is 10 and the number of differences is 1, it means "
"that in the original data of length 10, a value is inserted between every"
" two numbers, so the length of the new list is ` 10 + 9 = 19`."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_dips:19
msgid ""
"Smoothing window size, if it is greater than 0, the smoothing operation "
"will be performed, the default is 0."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_dips:23
msgid ""
"The minimum distance between adjacent peaks (>=1), the smaller peaks are "
"removed first, until the conditions of all remaining peaks are satisfied,"
" the default is empty."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_dips:28
msgid "Peak number check condition, the default is none, no check."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_dips:31
msgid ""
"Whether to filter according to the protrusion program input, this "
"parameter depends on `peaks_num`."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_dips:35
msgid ""
"Peak Status (Peak/Valley), Peak X Label, Peak Properties (dictionary     "
"containing properties of the returned peak computed as an intermediate "
"result during     evaluation of the specified conditions)."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_dips:38
msgid "Peak Status (Peak/Valley), Peak X Label, Peak Properties (dictionary"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_dips:38
msgid ""
"containing properties of the returned peak computed as an intermediate "
"result during evaluation of the specified conditions)."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point:1
#: pyQCat.analysis.algorithms:33:<autosummary>:1
msgid ""
"Calculate the index distance required for peak finding based on the "
"X-axis numerical distance"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point:3
msgid ""
"For example, in the bit spectrum scanning, we assume that the peak "
"interval between the 1 energy level and the 2 energy level is 100MHz. If "
"this is the limit modulation of the distance that needs to be added for "
"peak searching, you cannot directly set the distance to 100, because it "
"is a dimensionless value. , the distance is judged according to the index"
" of the output signal, so it is necessary to map 100MHz into the index "
"distance, which needs to be calculated by the following formula:"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point:10
msgid ""
"n_{distance} = \\frac{x_{distance}}{\\frac{x_{right} - x_{left}}{gaps}} ="
" \\frac{x_{distance} * gaps}{x_{right} - x_{left}}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point:14
msgid "The minimum interval between peaks (based on the actual input semaphore)."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point:17
msgid "input signal."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.distance_to_point:20
msgid "Minimum interval between peaks (based on the input signal index)"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:1
#: pyQCat.analysis.algorithms:33:<autosummary>:1
msgid "Get qubit sweet point dc value."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:5
msgid ""
"This method is generally used for bit cavity modulation spectrum "
"experiments, and it mainly implements the following logic:"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:8
msgid ""
"Find all peak points between the threshold range (-5, 5) according to the"
" oscillation period and the initial peak voltage, i.e. `dc_max_list`;"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:10
msgid ""
"Find all valley points between the threshold range (-5, 5) according to "
"the oscillation period and the initial peak voltage, i.e. `dc_min_list`;"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:12
msgid "Find all `dc_max_list` voltages closest to `0v` and return as `dc_max`;"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:13
msgid "Find all `dc_min_list` voltages closest to `0v` and return as `dc_mix`;"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:16
msgid ""
"The DC value corresponding to the sweet point, at which time the cavity "
"frequency is the largest."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:20
msgid "Qubit modulation spectrum oscillation period."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:23
msgid "Voltage upper and lower limits, the default is -5 to 5v."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point:10
#: pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:26
msgid ""
"dc_min (DC voltage corresponding to the minimum cavity frequency), dc_max"
" (DC voltage corresponding to the maximum cavity frequency)"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point:1
#: pyQCat.analysis.algorithms:33:<autosummary>:1
msgid "Get coupler sweet point dc value."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point:4
msgid "Scanned DC list."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point:7
msgid "Corresponding cavity frequency at each DC."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point:1
#: pyQCat.analysis.algorithms:33:<autosummary>:1
msgid "Find coincidence points."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point:3
msgid ""
"This method serves with APE experiments to find the peak coincidence "
"point of three APEOnce experiment."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point:6
msgid "Peak point structure `pyQCat.structures.Point`."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point:9
msgid "Number of peak-finding windows."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point:12
msgid "Search to get the nearest N peak points."
msgstr ""

#: ../../source/api/pyQCat.analysis.algorithms.rst:24
msgid "pyQCat.analysis.algorithms.guess module"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:1
#: pyQCat.analysis.algorithms:51:<autosummary>:1
msgid "Calculate the initial value of the cosine function fit"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:3
msgid "The cosine function formula is as follows:"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:5
#: pyQCat.analysis.algorithms.guess.frequency:6
msgid ""
"f_{\\rm est} = \\frac{1}{2\\pi {\\rm max}\\left| y \\right|}\n"
"    {\\rm max} \\left| \\frac{dy}{dx} \\right|"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:10
msgid "Get `amp`, `freq`, `phase`, `baseline` in cosine formula via `np.fft.fft`"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:14
msgid ""
"Assuming that the output signal has `N` sampling points, the result after"
" `fft` transformation is `N` complex numbers, each complex number "
"corresponds to a frequency (the frequency corresponding to the `n <= N/2`"
" point is `( n-1)/N*Fs` ), the modulus value of this complex number "
"represents the amplitude characteristic of this frequency. The "
"relationship between this amplitude feature and the amplitude of the "
"original signal is: If the amplitude of the original signal is `A`, then "
"the modulo value of each point (except the first DC component point) of "
"the `fft` result is `A` `N/2` times; while the modulus value of the first"
" point is `N` times the amplitude of the DC component."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:22
msgid ""
"The `N` complex points that are left after the first point is removed "
"from the `N-1` points are conjugate symmetrical about their center, so "
"actually only the spectrum of the first half of the points needs to be "
"taken, because the conjugate symmetry The modulus value (amplitude) of "
"the two points is the same."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:27
msgid ""
"According to the above steps, the algorithm is roughly implemented as "
"follows:"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:29
msgid ""
"First determine the signal number domain for subsequent determination of "
"the actual signal oscillation frequency"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:30
msgid "Fourier transform to obtain the first half of the calculation results"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:31
msgid ""
"Select the frequency point corresponding to the maximum value of the "
"amplitude feature `index`"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:32
msgid ""
"Get the main signal amplitude by the maximum amplitude characteristic / "
"(N / 2)"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:33
msgid ""
"Calculate the frequency corresponding to the main signal through the "
"maximum frequency point and the number domain"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:34
msgid "Calculate the signal phase from the real and imaginary parts"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:35
msgid ""
"DC signal (`baseline`) can be calculated directly from the modulo value "
"of the first point"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:45
#: pyQCat.analysis.algorithms.guess.fourier_cycle_guess:13
msgid "x data"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:47
#: pyQCat.analysis.algorithms.guess.fourier_cycle_guess:15
msgid "y data"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:49
msgid ":py:data:`~typing.Tuple`"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.cosine_fit_guess:50
msgid "Cosine function fit initial value"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.fourier_cycle_guess:1
#: pyQCat.analysis.algorithms:51:<autosummary>:1
msgid "Fourier transform calculates signal oscillation period"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.fourier_cycle_guess:3
msgid ""
"This method is the same as the `cosine_fit_guess` method, the difference "
"is that only the most obvious oscillation period in the signal is "
"calculated here. Similarly, you can use it to calculate the oscillation "
"frequency of the signal"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset:11
#: pyQCat.analysis.algorithms.guess.constant_spectral_offset:21
#: pyQCat.analysis.algorithms.guess.exp_decay:22
#: pyQCat.analysis.algorithms.guess.fourier_cycle_guess:17
#: pyQCat.analysis.algorithms.guess.frequency:29
#: pyQCat.analysis.algorithms.guess.full_width_half_max:10
#: pyQCat.analysis.algorithms.guess.oscillation_exp_decay:30
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.fourier_cycle_guess:18
msgid "Main signal oscillation period"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency:1
#: pyQCat.analysis.algorithms:51:<autosummary>:1
msgid "Get frequency of oscillating signal."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency:3
msgid ""
"First this tries FFT. If the true value is likely below or near the "
"frequency resolution, the function tries low frequency fit with"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency:11
msgid ""
"given :math:`y = A \\cos (2\\pi f x + phi)`. In this mode, y data points "
"are smoothed by a Savitzky-Golay filter to protect against outlier "
"points."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency:16
msgid ""
"This function returns always positive frequency. This function is "
"sensitive to the DC offset. This function assumes sorted, no-overlapping "
"x values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:18
#: pyQCat.analysis.algorithms.guess.frequency:21
#: pyQCat.analysis.algorithms.guess.full_width_half_max:4
#: pyQCat.analysis.algorithms.guess.oscillation_exp_decay:20
msgid "Array of x values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset:9
#: pyQCat.analysis.algorithms.guess.constant_spectral_offset:12
#: pyQCat.analysis.algorithms.guess.exp_decay:20
#: pyQCat.analysis.algorithms.guess.frequency:23
#: pyQCat.analysis.algorithms.guess.full_width_half_max:6
#: pyQCat.analysis.algorithms.guess.get_height:4
#: pyQCat.analysis.algorithms.guess.max_height:4
#: pyQCat.analysis.algorithms.guess.min_height:4
#: pyQCat.analysis.algorithms.guess.oscillation_exp_decay:22
msgid "Array of y values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:14
#: pyQCat.analysis.algorithms.guess.frequency:25
#: pyQCat.analysis.algorithms.guess.oscillation_exp_decay:24
msgid "Window size of Savitzky-Golay filter. This should be odd number."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:16
#: pyQCat.analysis.algorithms.guess.frequency:27
#: pyQCat.analysis.algorithms.guess.oscillation_exp_decay:26
msgid "Dimension of Savitzky-Golay filter."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency:30
msgid "Frequency estimation of oscillation signal."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.max_height:1
#: pyQCat.analysis.algorithms:51:<autosummary>:1
msgid "Get maximum value of y curve and its index."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.max_height:6
msgid "Return that percentile value if provided, otherwise just return max value."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.get_height:8
#: pyQCat.analysis.algorithms.guess.max_height:8
#: pyQCat.analysis.algorithms.guess.min_height:8
msgid "Use absolute y value."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.get_height:10
#: pyQCat.analysis.algorithms.guess.max_height:10
#: pyQCat.analysis.algorithms.guess.min_height:10
msgid ":py:data:`~typing.Tuple`\\[:py:class:`float`, :py:class:`int`]"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.max_height:11
msgid "The maximum y value and index."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.min_height:1
#: pyQCat.analysis.algorithms:51:<autosummary>:1
msgid "Get minimum value of y curve and its index."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.min_height:6
msgid "Return that percentile value if provided, otherwise just return min value."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.min_height:11
msgid "The minimum y value and index."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.get_height:1
#: pyQCat.analysis.algorithms:51:<autosummary>:1
msgid "Get specific value of y curve defined by a callback and its index."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.get_height:6
msgid "A callback to find preferred y value."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.get_height:11
msgid "The target y value and index."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:1
#: pyQCat.analysis.algorithms:51:<autosummary>:1
msgid ""
"Get exponential decay parameter from monotonically increasing "
"(decreasing) curve."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:3
#: pyQCat.analysis.algorithms.guess.oscillation_exp_decay:3
msgid "This assumes following function form."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:5
msgid "y(x) = e^{\\alpha x}"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:9
msgid "We can calculate :math:`\\alpha` as"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:11
msgid "\\alpha = \\log(y(x)) / x"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:15
msgid "To find this number, the numpy polynomial fit with ``deg=1`` is used."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:23
#: pyQCat.analysis.algorithms.guess.oscillation_exp_decay:31
msgid "Decay rate of signal."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:1
#: pyQCat.analysis.algorithms:51:<autosummary>:1
msgid "Get exponential decay parameter from oscillating signal."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:5
msgid "y(x) = e^{\\alpha x} F(x),"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:9
msgid ""
"where :math:`F(x)` is arbitrary oscillation function oscillating at "
"``freq_guess``. This function first applies a Savitzky-Golay filter to y "
"value, then run scipy peak search to extract peak positions. If "
"``freq_guess`` is provided, the search function will be robust to fake "
"peaks due to noise. This function calls :py:func:`exp_decay` function for"
" extracted x and y values at peaks."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:17
msgid ""
"y values should contain more than one cycle of oscillation to use this "
"guess approach."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:28
msgid "Optional. Initial frequency guess of :math:`F(x)`."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset:1
#: pyQCat.analysis.algorithms:51:<autosummary>:1
msgid "Get constant offset of sinusoidal signal."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset:3
msgid ""
"This function finds 95 and 5 percentile y values and take an average of "
"them. This method is robust to the dependency on sampling window, i.e. if"
" we sample sinusoidal signal for 2/3 of its period, simple averaging may "
"induce a drift towards positive or negative direction depending on the "
"phase offset."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset:12
#: pyQCat.analysis.algorithms.guess.constant_spectral_offset:22
msgid "Offset value."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:1
#: pyQCat.analysis.algorithms:51:<autosummary>:1
msgid "Get constant offset of spectral baseline."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:3
msgid ""
"This function searches constant offset by finding a region where 1st and "
"2nd order differentiation are close to zero. A return value is an average"
" y value of that region. To suppress the noise contribution to "
"derivatives, this function also applies a Savitzky-Golay filter to y "
"value."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:8
msgid ""
"This method is more robust to offset error than just taking median or "
"average of y values especially when a peak width is wider compared to the"
" scan range."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:18
msgid ""
"Threshold value to decide flat region. This value represent a ratio to "
"the maximum derivative value."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max:1
msgid ""
"Get full width half maximum value of the peak. Offset of y should be "
"removed."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max:8
msgid "Index of peak."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max:11
msgid "FWHM of the peak."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max
#: pyQCat.analysis.algorithms.smooth.smooth
msgid "Raises"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max:13
msgid "When peak is too broad and line width is not found."
msgstr ""

#: ../../source/api/pyQCat.analysis.algorithms.rst:32
msgid "pyQCat.analysis.algorithms.iqprobability module"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.get_iqprob:1
msgid ""
"get the probability of 0 and 1 by i/q couple. :param i: ndarray type data"
" sequence. :param q: ndarray type data sequence. :param state: only can "
"be set to 0 or 1. :param wave_type: only can be set to 0 or 1. :return: 0"
" and 1 probability. such as 0.45, 0.55."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.probability_multiple:1
msgid ""
"get the probability of 0 and 1 by i/q couple. Please put \"0\"state first"
" :param i: ndarray type data sequence. :param q: ndarray type data "
"sequence. :return: 0 and 1 probability. such as 0.45, 0.55."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator:1
msgid ""
"IQdiscriminator is used to diagnose the probability of P0 and P1 for the "
"I and Q data collected by the experiment. Usually, the object is saved in"
" the database or local bin file in binary form. Therefore, when "
"obtaining, first query from the database, if not found, read from the "
"local configuration file directory."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.centers:1
msgid "Model centers."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.fidelity:1
msgid "Model fidelity."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.probability:1
msgid "Predict probability."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.radius:1
msgid "Model radius value."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.outlier:1
msgid "Outlier value."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.k_recommend:1
msgid "Model ideal clusters."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:1
msgid ""
"Calculate radius. The radius calculated from the plane coordinate system "
"to the center."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:5
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:7
msgid "one dimension array"
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:9
msgid "value of center's x direction"
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:12
msgid "value of center's y direction"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_radius:6
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:15
msgid "multiple of standard deviation"
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:18
msgid "radius value"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_radius:1
msgid "Get model radius, and assign to self._radius."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_radius:4
msgid "label array, one dimension array"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr:1
msgid "Get target data bool index."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr:4
msgid "i data, need of filter, one dimension array."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr:6
msgid "q data, need of filter, one dimension array."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr:8
msgid ""
"mark target data, iq: default value, means get iq data within model "
"radius; outlier: means get iq data without model radius."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr:13
msgid "target data bool index, one dimension array."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._screen_outlier:1
msgid ""
"Screen i_outlier, q_outlier data, without model radius, and assign to "
"self._iq_outlier. Calculate rate of outlier, update self._outlier value."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq:1
msgid "Screen i, q data, within model radius."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq:4
msgid "original i data, one dimension array"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq:6
msgid "original q data, one dimension array"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq:8
msgid "tuple of i, q"
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._inside_k_recommend:1
msgid "Simple and easy get X data k recommend value."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._inside_k_recommend:4
msgid "np.array(I, Q)"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.train:1
msgid "Train model."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.train:4
msgid "multiple of standard deviation, calculate model radius."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict:1
msgid "Predict state label."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_probability:4
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict:4
msgid "Input I data."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_probability:7
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict:7
msgid "Input Q data."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_probability:10
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict:10
msgid "Screen iq or not."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict:13
msgid "Label array."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_probability:1
msgid "Calculate probability."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.plot:1
msgid "Plot IQ round spot photos."
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._fit_predict:2
#: pyQCat.analysis.algorithms.iqprobability.simulate_qgate:2
#: pyQCat.analysis.algorithms.smooth.smooth:22
#: pyQCat.analysis.algorithms:62:<autosummary>:1
msgid ":py:class:`~numpy.ndarray`"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._check_repeat:2
msgid ":py:class:`bool`"
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend:1
msgid "Get k_recommend, by `self.I_list` and `self.I_list`."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend:4
msgid "Qubit or Coupler name."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend:7
msgid "Get IQ data, the readout power."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend:10
msgid "Save plot figure path."
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend:13
msgid "k_recommend"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.get_qubits_probability:2
#: pyQCat.analysis.algorithms:62:<autosummary>:1
msgid ":py:class:`~typing.List`"
msgstr ""

#: ../../source/api/pyQCat.analysis.algorithms.rst:40
msgid "pyQCat.analysis.algorithms.smooth module"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:1
#: pyQCat.analysis.algorithms:70:<autosummary>:1
msgid "smooth the data using a window with requested size."
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:3
msgid ""
"This method is based on the convolution of a scaled window with the "
"signal. The signal is prepared by introducing reflected copies of the "
"signal (with the window size) in both ends so that transient parts are "
"minimized in the begining and end part of the output signal."
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:8
msgid "For the process of convolution smoothing, please refer to todo"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:11
msgid "the input signal"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:13
msgid "the dimension of the smoothing window; should be an odd integer"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:15
msgid ""
"the type of window from 'flat', 'hanning', 'hamming', 'bartlett', "
"'blackman' flat window will produce a moving average smoothing."
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:18
msgid "The window length must be an odd number"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:19
msgid "This function only supports one-dimensional signals"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:20
msgid "The signal length shall not be less than the window length"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:23
msgid "Smoothed signal"
msgstr ""

#: ../../source/api/pyQCat.analysis.algorithms.rst:48
msgid "pyQCat.analysis.algorithms.tomography module"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography._dot3:1
msgid "Compute the dot product of three matrices"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography._qpt_pointer:1
msgid "Calculates the pointer-basis chi-matrix."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography._qpt_pointer:3
msgid ""
"rhos - array of input density matrices e_rhos - array of output density "
"matrices."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography._qpt_pointer:6
msgid ""
"Uses linalg.lstsq to calculate the closest fit when the chi-matrix is "
"overdetermined by the data. The return_all flag specifies whether to "
"return all the parameters returned from linalg.lstsq, such as the "
"residuals and the rank of the chi-matrix.  By default (return_all=False) "
"only chi is returned."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography._transform_chi_pointer:1
msgid "Convert a chi matrix from the pointer basis into a different basis."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography._transform_chi_pointer:3
msgid ""
"transform_chi_pointer(chi_pointer, As) will transform the chi_pointer "
"matrix from the pointer basis (as produced by qpt_pointer, for example) "
"into the basis specified by operator elements in the cell array As."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.init_qst:1
#: pyQCat.analysis.algorithms:85:<autosummary>:1
msgid "Initialize quantum state tomography for a set of unitaries."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.init_qst:3
msgid ""
"a list of unitary operations that will be applied to the state before "
"measuring the diagonal elements.  These unitaries should form a "
"'complete' set to allow the full density matrix to be determined, though "
"this is not enforced."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.init_qst:8
msgid ""
"Returns a transformation matrix that should be passed to qst along with "
"measurement data to perform the state tomography."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qst:1
#: pyQCat.analysis.algorithms:85:<autosummary>:1
msgid "Convert a set of diagonal measurements into a density matrix."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qst:3
msgid ""
"Measured probabilities (diagonal elements) after acting on the state with"
" each of the unitaries from the qst protocol."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qst:5
msgid ""
"Transformation matrix from init_qst for this protocol, or key passed to "
"init_qst under which the transformation was saved."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt_mle:1
#: pyQCat.analysis.algorithms.tomography.qst_mle:1
#: pyQCat.analysis.algorithms:85:<autosummary>:1
msgid "State tomography with maximum-likelihood estimation."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qst_mle:3
msgid ""
"a 2D array of measured probabilities. The first index indicates which "
"operation from Us was applied, while the second index tells which "
"measurement result this was (e.g. 000, 001, etc.)."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt_mle:8
#: pyQCat.analysis.algorithms.tomography.qst_mle:6
msgid "the unitary operations that were applied to the system before measuring."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qst_mle:8
msgid ""
"a 'fidelity' matrix, relating the actual or 'intrinsic' probabilities to "
"the measured probabilities, via pms = dot(F, pis). If no fidelity matrix "
"is given, the identity will be used."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qst_mle:11
msgid "an initial guess for the density matrix, e.g. from linear tomography."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.init_qpt:1
#: pyQCat.analysis.algorithms:85:<autosummary>:1
msgid "Initialize quantum process tomography for an operator basis."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.init_qpt:3
msgid ""
"a list of matrices giving the basis in which to compute the chi matrix "
"for process tomography.  These matrices should form a 'complete' set to "
"allow the full chi matrix to be represented, though this is not enforced."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.init_qpt:8
msgid ""
"Returns a transformation matrix that should be passed to qpt along with "
"input and output density matrices to perform the process tomography."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt:1
#: pyQCat.analysis.algorithms:85:<autosummary>:1
msgid "Calculate the chi matrix of a quantum process."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt:3
msgid "array of input density matrices"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt:4
msgid "array of output density matrices"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt:5
msgid ""
"transformation matrix from init_qpt for the desired operator basis, or "
"key passed to init_qpt under which this basis was saved"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.tensor:1
#: pyQCat.analysis.algorithms:85:<autosummary>:1
msgid "Compute the tensor product of a list (or array) of matrices"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.tensor:3
msgid "matrices squence"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.tensor:5
msgid "the result of `np.korn`"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.tensor_combinations:1
#: pyQCat.analysis.algorithms:85:<autosummary>:1
msgid "Matrix tensor combinations"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.tensor_combinations:3
msgid "List of matrices to be merged"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.tensor_combinations:4
msgid "Number of mergers"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix:1
#: pyQCat.analysis.algorithms:85:<autosummary>:1
msgid "Generate Chi matrix for standard gate operations"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix:3
msgid "Pauli matrix of the gate to be characterized."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix:4
msgid "Quantum process tomography for an operator basis `[I, X, Y, Z]`."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix:6
msgid "Base gate matrix list `[\"I\", \"X/2\", \"Y/2\", \"-X/2\"]`."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.gen_ideal_chi_matrix:8
msgid "Ideal chi matrix."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt_mle:3
msgid ""
"a list of 2D array of measured probabilites.  the list index indicates "
"the # rho prepared and processed by Chi for the 2D array, (see qst_mle) "
"The first index indicates which operation from Us was applied, while the "
"second index tells which measurement result this was (e.g. 000, 001, "
"etc.)."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt_mle:10
msgid ""
"a 'fidelity' matrix, relating the actual or 'intrinsic' probabilities to "
"the measured probabilites, via pms = dot(F, pis).  If no fidelity matrix "
"is given, the identity will be used."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt_mle:13
msgid "an initial guess for the chi matrix, e.g. from linear qpt."
msgstr ""

#: ../../source/api/pyQCat.analysis.algorithms.rst:56
msgid "Module contents"
msgstr ""

#: of pyQCat.analysis.algorithms:3
msgid "Analysis  (:mod:`pyQCat.analysis.algorithms`)"
msgstr ""

#: of pyQCat.analysis.algorithms:5
msgid "Analysis submodule, special operation interface modules."
msgstr ""

#: of pyQCat.analysis.algorithms:8
msgid "distortion function"
msgstr ""

#: of pyQCat.analysis.algorithms:19:<autosummary>:1
msgid ""
":py:obj:`calculate_offset_arr "
"<pyQCat.analysis.algorithms.calculate_offset_arr>`\\ "
"\\(response\\_arr\\[\\, z\\_amp\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:19:<autosummary>:1
msgid ""
":py:obj:`calculate_distortion "
"<pyQCat.analysis.algorithms.calculate_distortion>`\\ \\(dt\\_list\\, "
"so\\_list\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:19:<autosummary>:1
msgid ""
":py:obj:`calculate_lfilter_paras "
"<pyQCat.analysis.algorithms.calculate_lfilter_paras>`\\ "
"\\(\\*parameters\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:19:<autosummary>:1
msgid ""
":py:obj:`create_verify_pulse "
"<pyQCat.analysis.algorithms.create_verify_pulse>`\\ \\(\\[num\\, "
"sample\\_rate\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:19:<autosummary>:1
msgid ""
":py:obj:`calculate_pre_distortion_wave "
"<pyQCat.analysis.algorithms.calculate_pre_distortion_wave>`\\ "
"\\(delay\\_arr\\, ...\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:19:<autosummary>:1
msgid ""
":py:obj:`calculate_simulation_wave "
"<pyQCat.analysis.algorithms.calculate_simulation_wave>`\\ \\(...\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:21
msgid "find peak function"
msgstr ""

#: of pyQCat.analysis.algorithms:33:<autosummary>:1
msgid ""
":py:obj:`get_peak_point <pyQCat.analysis.algorithms.get_peak_point>`\\ "
"\\(xdata\\, ydata\\[\\, distance\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:33:<autosummary>:1
msgid ""
":py:obj:`judge_peak_dip <pyQCat.analysis.algorithms.judge_peak_dip>`\\ "
"\\(data\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:33:<autosummary>:1
msgid ""
":py:obj:`distance_to_point "
"<pyQCat.analysis.algorithms.distance_to_point>`\\ \\(x\\_distance\\, "
"x\\_data\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:33:<autosummary>:1
msgid ""
":py:obj:`get_qubit_dc_point "
"<pyQCat.analysis.algorithms.get_qubit_dc_point>`\\ \\(max\\_reference\\, "
"freq\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:33:<autosummary>:1
msgid ""
":py:obj:`get_coupler_dc_point "
"<pyQCat.analysis.algorithms.get_coupler_dc_point>`\\ \\(dc\\_list\\, "
"fr\\_list\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:33:<autosummary>:1
msgid ""
":py:obj:`find_coincident_point "
"<pyQCat.analysis.algorithms.find_coincident_point>`\\ \\(points\\, N\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:33:<autosummary>:1
msgid ""
":py:obj:`find_peaks_with_prominence "
"<pyQCat.analysis.algorithms.find_peaks_with_prominence>`\\ \\(xdata\\[\\,"
" ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:35
msgid "guess function"
msgstr ""

#: of pyQCat.analysis.algorithms:51:<autosummary>:1
msgid ""
":py:obj:`cosine_fit_guess "
"<pyQCat.analysis.algorithms.cosine_fit_guess>`\\ \\(xdata\\, ydata\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:51:<autosummary>:1
msgid ""
":py:obj:`fourier_cycle_guess "
"<pyQCat.analysis.algorithms.fourier_cycle_guess>`\\ \\(x\\, y\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:51:<autosummary>:1
msgid ""
":py:obj:`frequency <pyQCat.analysis.algorithms.frequency>`\\ \\(x\\, "
"y\\[\\, filter\\_window\\, filter\\_dim\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:51:<autosummary>:1
msgid ""
":py:obj:`max_height <pyQCat.analysis.algorithms.max_height>`\\ \\(y\\[\\,"
" percentile\\, absolute\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:51:<autosummary>:1
msgid ""
":py:obj:`min_height <pyQCat.analysis.algorithms.min_height>`\\ \\(y\\[\\,"
" percentile\\, absolute\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:51:<autosummary>:1
msgid ""
":py:obj:`get_height <pyQCat.analysis.algorithms.get_height>`\\ \\(y\\, "
"find\\_height\\[\\, absolute\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:51:<autosummary>:1
msgid ":py:obj:`exp_decay <pyQCat.analysis.algorithms.exp_decay>`\\ \\(x\\, y\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:51:<autosummary>:1
msgid ""
":py:obj:`oscillation_exp_decay "
"<pyQCat.analysis.algorithms.oscillation_exp_decay>`\\ \\(x\\, y\\[\\, "
"filter\\_window\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:51:<autosummary>:1
msgid ""
":py:obj:`constant_sinusoidal_offset "
"<pyQCat.analysis.algorithms.constant_sinusoidal_offset>`\\ \\(y\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:51:<autosummary>:1
msgid ""
":py:obj:`constant_spectral_offset "
"<pyQCat.analysis.algorithms.constant_spectral_offset>`\\ \\(y\\[\\, "
"filter\\_window\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:51:<autosummary>:1
msgid ""
":py:obj:`full_width_half_max "
"<pyQCat.analysis.algorithms.full_width_half_max>`\\ \\(x\\, y\\, "
"peak\\_index\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:51:<autosummary>:1
msgid "Get full width half maximum value of the peak."
msgstr ""

#: of pyQCat.analysis.algorithms:53
msgid "iqprobability function"
msgstr ""

#: of pyQCat.analysis.algorithms:62:<autosummary>:1
msgid ""
":py:obj:`IQdiscriminator <pyQCat.analysis.algorithms.IQdiscriminator>`\\ "
"\\(I\\_list\\, Q\\_list\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:62:<autosummary>:1
msgid ""
"IQdiscriminator is used to diagnose the probability of P0 and P1 for the "
"I and Q data collected by the experiment."
msgstr ""

#: of pyQCat.analysis.algorithms:62:<autosummary>:1
msgid ""
":py:obj:`get_qubits_probability "
"<pyQCat.analysis.algorithms.get_qubits_probability>`\\ "
"\\(discriminator\\_list\\, ...\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:62:<autosummary>
msgid "rtype"
msgstr ""

#: of pyQCat.analysis.algorithms:62:<autosummary>:1
msgid ""
":py:obj:`correct_fidelity "
"<pyQCat.analysis.algorithms.correct_fidelity>`\\ \\(bit\\_num\\, "
"index\\_list\\, std\\_p\\, ...\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:62:<autosummary>:1
msgid ""
":py:obj:`simulate_qgate <pyQCat.analysis.algorithms.simulate_qgate>`\\ "
"\\(qubit\\_num\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:64
msgid "smooth function"
msgstr ""

#: of pyQCat.analysis.algorithms:70:<autosummary>:1
msgid ""
":py:obj:`smooth <pyQCat.analysis.algorithms.smooth>`\\ \\(x\\[\\, "
"window\\_length\\, window\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:72
msgid "tomography function"
msgstr ""

#: of pyQCat.analysis.algorithms:85:<autosummary>:1
msgid ":py:obj:`init_qst <pyQCat.analysis.algorithms.init_qst>`\\ \\(us\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:85:<autosummary>:1
msgid ""
":py:obj:`qst <pyQCat.analysis.algorithms.qst>`\\ \\(measure\\_data\\, "
"u\\_array\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:85:<autosummary>:1
msgid ""
":py:obj:`qst_mle <pyQCat.analysis.algorithms.qst_mle>`\\ "
"\\(measure\\_data\\, u\\_array\\[\\, F\\, rho0\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:85:<autosummary>:1
msgid ":py:obj:`init_qpt <pyQCat.analysis.algorithms.init_qpt>`\\ \\(matrices\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:85:<autosummary>:1
msgid ""
":py:obj:`qpt <pyQCat.analysis.algorithms.qpt>`\\ \\(rhos\\, e\\_rhos\\, "
"T\\[\\, return\\_all\\]\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:85:<autosummary>:1
msgid ":py:obj:`tensor <pyQCat.analysis.algorithms.tensor>`\\ \\(matrices\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:85:<autosummary>:1
msgid ""
":py:obj:`tensor_combinations "
"<pyQCat.analysis.algorithms.tensor_combinations>`\\ \\(matrices\\, "
"repeat\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:85:<autosummary>:1
msgid ""
":py:obj:`gen_ideal_chi_matrix "
"<pyQCat.analysis.algorithms.gen_ideal_chi_matrix>`\\ \\(unitary\\, "
"qpt\\_ops\\, tomo\\_ops\\)"
msgstr ""

#: of pyQCat.analysis.algorithms:85:<autosummary>:1
msgid ""
":py:obj:`qpt_mle <pyQCat.analysis.algorithms.qpt_mle>`\\ "
"\\(diags\\_out\\, rhos\\_in\\, Us\\, As\\, T\\[\\, F\\, chi0\\]\\)"
msgstr ""

#~ msgid "Find peaks and transform `Point` object."
#~ msgstr ""

#~ msgid "x-axis data, np.ndararray"
#~ msgstr ""

#~ msgid "y-axis data, np.ndararray"
#~ msgstr ""

#~ msgid "`find_peaks` args"
#~ msgstr ""

#~ msgid "find_peaks` args"
#~ msgstr ""

#~ msgid "int or bool, mark find peak or valley"
#~ msgstr ""

#~ msgid "Generator of `Point` object"
#~ msgstr ""

#~ msgid ""
#~ "Find the one-dimensional array peak "
#~ "and return peak index value_list: Need"
#~ " to find the peak of the array,"
#~ " ndarray num_peak: the number of "
#~ "peak/valley in the dataset. distance: "
#~ "number, optional"
#~ msgstr ""

#~ msgid "peak index and peak value"
#~ msgstr ""

#~ msgid "Get peak dips"
#~ msgstr ""

#~ msgid ""
#~ "Indicates peaks or dips. peaks : "
#~ "ndarray Indices of peaks in `x` "
#~ "that satisfy all given conditions."
#~ msgstr ""

#~ msgid "properties"
#~ msgstr ""

#~ msgid "dict"
#~ msgstr ""

#~ msgid ""
#~ "A dictionary containing properties of "
#~ "the returned peaks which were calculated"
#~ " as intermediate results during evaluation"
#~ " of the specified conditions: * "
#~ "'peak_heights'"
#~ msgstr ""

#~ msgid "If `height` is given, the height of each peak in `x`."
#~ msgstr ""

#~ msgid "'prominences', 'right_bases', 'left_bases'"
#~ msgstr ""

#~ msgid ""
#~ "If `prominence` is given, these keys "
#~ "are accessible. See `peak_prominences` for "
#~ "a description of their content."
#~ msgstr ""

#~ msgid ""
#~ "To calculate and return properties "
#~ "without excluding peaks, provide the "
#~ "open interval ``(None, None)`` as a "
#~ "value to the appropriate argument "
#~ "(excluding `distance`)."
#~ msgstr ""

#~ msgid "Distance convert to point."
#~ msgstr ""

#~ msgid "Peak minimum spacing."
#~ msgstr ""

#~ msgid "The scanned frequency range."
#~ msgstr ""

#~ msgid "float value."
#~ msgstr ""

#~ msgid ""
#~ "Get max dc and min dc :param "
#~ "max_reference: Reference point :param freq:"
#~ " Oscillation cycle :param threshold: "
#~ "Threshold points for maximum and minimum"
#~ " values"
#~ msgstr ""

#~ msgid ""
#~ "Calculate coupler tunable dc min and "
#~ "max point. :param dc_list: List of "
#~ "scan dc range. :type dc_list: List, "
#~ "np.ndarray :param fr_list: List of "
#~ "frequency value. :type fr_list: List, "
#~ "np.ndarray"
#~ msgstr ""

#~ msgid "min_point, max_point"
#~ msgstr ""

#~ msgid "Obtain coincidence points of APE experiment"
#~ msgstr ""

#~ msgid "A list consisting of `pyQCat.Point` objects."
#~ msgstr ""

#~ msgid "Number of coincidence points."
#~ msgstr ""

#~ msgid ""
#~ "The coincident point list, element is"
#~ " the                    instance of class "
#~ "`pyQCat.Point`."
#~ msgstr ""

#~ msgid "The coincident point list, element is the"
#~ msgstr ""

#~ msgid "instance of class `pyQCat.Point`."
#~ msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid "This method is based on the convolution of a scaled window with the"
#~ msgstr ""

#~ msgid "signal."
#~ msgstr ""

#~ msgid ""
#~ "The signal is prepared by introducing"
#~ " reflected copies of the signal (with"
#~ " the window size) in both ends "
#~ "so that transient parts are"
#~ msgstr ""

#~ msgid "minimized"
#~ msgstr ""

#~ msgid "in the begining and end part of the output signal."
#~ msgstr ""

#~ msgid "input:"
#~ msgstr ""

#~ msgid ""
#~ "x: the input signal window_length: the"
#~ " dimension of the smoothing window; "
#~ "should be an"
#~ msgstr ""

#~ msgid "odd integer"
#~ msgstr ""

#~ msgid "window: the type of window from 'flat', 'hanning', 'hamming',"
#~ msgstr ""

#~ msgid "'bartlett', 'blackman'"
#~ msgstr ""

#~ msgid "flat window will produce a moving average smoothing."
#~ msgstr ""

#~ msgid "output:"
#~ msgstr ""

#~ msgid "the smoothed signal"
#~ msgstr ""

#~ msgid "example:"
#~ msgstr ""

#~ msgid "t=linspace(-2,2,0.1) x=sin(t)+randn(len(t))*0.1 y=smooth(x)"
#~ msgstr ""

#~ msgid "see also:"
#~ msgstr ""

#~ msgid ""
#~ "numpy.hanning, numpy.hamming, numpy.bartlett, "
#~ "numpy.blackman, numpy.convolve scipy.signal.lfilter"
#~ msgstr ""

#~ msgid "us - a list of unitary operations that will be applied to the"
#~ msgstr ""

#~ msgid ""
#~ "state before measuring the diagonal "
#~ "elements.  These unitaries should form a"
#~ " 'complete' set to allow the full "
#~ "density matrix to be determined, though"
#~ " this is not enforced."
#~ msgstr ""

#~ msgid "State tomography with maximum-likelihood estimation. 使用最大似然估计进行量子状态层析"
#~ msgstr ""

#~ msgid ""
#~ "a 2D array of measured probabilities."
#~ " The first index indicates which "
#~ "operation from Us was applied, while "
#~ "the second index tells which measurement"
#~ " result this was (e.g. 000, 001, "
#~ "etc.). 测量结果的二维数组, 每个Us操作对应一个测量结果."
#~ msgstr ""

#~ msgid ""
#~ "the unitary operations that were applied"
#~ " to the system before measuring. "
#~ "在测量之前应用于系统的单一Us操作, 如[I, X/2, Y/2]."
#~ msgstr ""

#~ msgid ""
#~ "a 'fidelity' matrix, relating the actual"
#~ " or 'intrinsic' probabilities to the "
#~ "measured probabilities, via pms = dot(F,"
#~ " pis). If no fidelity matrix is "
#~ "given, the identity will be used. "
#~ "一个“保真度”矩阵, 通过 pms = dot(F, pis) "
#~ "将实际或实际概率与测量的概率相关联, 如果未给出保真度矩阵, 则将使用单位矩阵保持测试的概率进行计算。"
#~ msgstr ""

#~ msgid ""
#~ "an initial guess for the density "
#~ "matrix, e.g. from linear tomography. "
#~ "初始密度矩阵, 默认为空, 如线性层析扫描"
#~ msgstr ""

#~ msgid "matrices - a list of matrices giving the basis in which to compute"
#~ msgstr ""

#~ msgid ""
#~ "the chi matrix for process tomography."
#~ "  These matrices should form a "
#~ "'complete' set to allow the full "
#~ "chi matrix to be represented, though "
#~ "this is not enforced."
#~ msgstr ""

#~ msgid ""
#~ "rhos - array of input density "
#~ "matrices e_rhos - array of output "
#~ "density matrices"
#~ msgstr ""

#~ msgid "T - transformation matrix from init_qpt for the desired operator"
#~ msgstr ""

#~ msgid "basis, or key passed to init_qpt under which this basis was saved"
#~ msgstr ""

#~ msgid ""
#~ "diags_out - a list of 2D array "
#~ "of measured probabilites.  the list "
#~ "index"
#~ msgstr ""

#~ msgid ""
#~ "indicates the # rho prepared and "
#~ "processed by Chi for the 2D array,"
#~ " (see qst_mle) The first index "
#~ "indicates which operation from Us was"
#~ " applied, while the second index "
#~ "tells which measurement result this was"
#~ " (e.g. 000, 001, etc.)."
#~ msgstr ""

#~ msgid "Us - the unitary operations that were applied to the system before"
#~ msgstr ""

#~ msgid "measuring."
#~ msgstr ""

#~ msgid ""
#~ "F - a 'fidelity' matrix, relating "
#~ "the actual or 'intrinsic' probabilities"
#~ msgstr ""

#~ msgid ""
#~ "to the measured probabilites, via pms"
#~ " = dot(F, pis).  If no fidelity "
#~ "matrix is given, the identity will "
#~ "be used."
#~ msgstr ""

#~ msgid "chi0 - an initial guess for the chi matrix, e.g. from linear qpt."
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`calculate_offset_arr "
#~ "<pyQCat.analysis.algorithms.calculate_offset_arr>`\\ "
#~ "\\(response\\_arr\\[\\, z\\_amp\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`calculate_distortion "
#~ "<pyQCat.analysis.algorithms.calculate_distortion>`\\ "
#~ "\\(dt\\_list\\, so\\_list\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`calculate_lfilter_paras "
#~ "<pyQCat.analysis.algorithms.calculate_lfilter_paras>`\\ "
#~ "\\(\\*parameters\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`create_verify_pulse "
#~ "<pyQCat.analysis.algorithms.create_verify_pulse>`\\ \\(\\[num\\,"
#~ " sample\\_rate\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`calculate_pre_distortion_wave "
#~ "<pyQCat.analysis.algorithms.calculate_pre_distortion_wave>`\\ "
#~ "\\(delay\\_arr\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`calculate_simulation_wave "
#~ "<pyQCat.analysis.algorithms.calculate_simulation_wave>`\\ "
#~ "\\(...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_peak_point "
#~ "<pyQCat.analysis.algorithms.get_peak_point>`\\ \\(xdata\\, "
#~ "ydata\\, distance\\, height\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`judge_peak_dip "
#~ "<pyQCat.analysis.algorithms.judge_peak_dip>`\\ \\(data\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`distance_to_point "
#~ "<pyQCat.analysis.algorithms.distance_to_point>`\\ "
#~ "\\(freq\\_distance\\, x\\_data\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_dc_point "
#~ "<pyQCat.analysis.algorithms.get_qubit_dc_point>`\\ "
#~ "\\(max\\_reference\\, freq\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_coupler_dc_point "
#~ "<pyQCat.analysis.algorithms.get_coupler_dc_point>`\\ "
#~ "\\(dc\\_list\\, fr\\_list\\)"
#~ msgstr ""

#~ msgid "Calculate coupler tunable dc min and max point."
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`find_coincident_point "
#~ "<pyQCat.analysis.algorithms.find_coincident_point>`\\ "
#~ "\\(points\\, N\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`cosine_fit_guess "
#~ "<pyQCat.analysis.algorithms.cosine_fit_guess>`\\ \\(xdata\\, "
#~ "ydata\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`fourier_cycle_guess "
#~ "<pyQCat.analysis.algorithms.fourier_cycle_guess>`\\ \\(x\\, "
#~ "y\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`frequency <pyQCat.analysis.algorithms.frequency>`\\ "
#~ "\\(x\\, y\\[\\, filter\\_window\\, "
#~ "filter\\_dim\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`max_height <pyQCat.analysis.algorithms.max_height>`\\"
#~ " \\(y\\[\\, percentile\\, absolute\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`min_height <pyQCat.analysis.algorithms.min_height>`\\"
#~ " \\(y\\[\\, percentile\\, absolute\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_height <pyQCat.analysis.algorithms.get_height>`\\"
#~ " \\(y\\, find\\_height\\[\\, absolute\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`exp_decay <pyQCat.analysis.algorithms.exp_decay>`\\ "
#~ "\\(x\\, y\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`oscillation_exp_decay "
#~ "<pyQCat.analysis.algorithms.oscillation_exp_decay>`\\ \\(x\\,"
#~ " y\\[\\, filter\\_window\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`constant_sinusoidal_offset "
#~ "<pyQCat.analysis.algorithms.constant_sinusoidal_offset>`\\ "
#~ "\\(y\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`constant_spectral_offset "
#~ "<pyQCat.analysis.algorithms.constant_spectral_offset>`\\ "
#~ "\\(y\\[\\, filter\\_window\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`full_width_half_max "
#~ "<pyQCat.analysis.algorithms.full_width_half_max>`\\ \\(x\\, "
#~ "y\\, peak\\_index\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`IQdiscriminator "
#~ "<pyQCat.analysis.algorithms.IQdiscriminator>`\\ \\(I\\_list\\,"
#~ " Q\\_list\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubits_probability "
#~ "<pyQCat.analysis.algorithms.get_qubits_probability>`\\ "
#~ "\\(discriminator\\_list\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_fidelity "
#~ "<pyQCat.analysis.algorithms.correct_fidelity>`\\ \\(bit\\_num\\,"
#~ " index\\_list\\, std\\_p\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`simulate_qgate "
#~ "<pyQCat.analysis.algorithms.simulate_qgate>`\\ \\(qubit\\_num\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`smooth <pyQCat.analysis.algorithms.smooth>`\\ "
#~ "\\(x\\[\\, window\\_length\\, window\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`init_qst <pyQCat.analysis.algorithms.init_qst>`\\ \\(us\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`qst <pyQCat.analysis.algorithms.qst>`\\ "
#~ "\\(measure\\_data\\, u\\_array\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`qst_mle <pyQCat.analysis.algorithms.qst_mle>`\\ "
#~ "\\(measure\\_data\\, u\\_array\\[\\, F\\, rho0\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`init_qpt <pyQCat.analysis.algorithms.init_qpt>`\\ "
#~ "\\(matrices\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`qpt <pyQCat.analysis.algorithms.qpt>`\\ "
#~ "\\(rhos\\, e\\_rhos\\, T\\[\\, return\\_all\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`tensor <pyQCat.analysis.algorithms.tensor>`\\ \\(matrices\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`tensor_combinations "
#~ "<pyQCat.analysis.algorithms.tensor_combinations>`\\ "
#~ "\\(matrices\\, repeat\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`gen_ideal_chi_matrix "
#~ "<pyQCat.analysis.algorithms.gen_ideal_chi_matrix>`\\ "
#~ "\\(unitary\\, qpt\\_ops\\, tomo\\_ops\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`qpt_mle <pyQCat.analysis.algorithms.qpt_mle>`\\ "
#~ "\\(diags\\_out\\, rhos\\_in\\, Us\\, As\\, "
#~ "T\\[\\, F\\, chi0\\]\\)"
#~ msgstr ""

#~ msgid "Find the mean, maximum and minimum values of the data set respectively;"
#~ msgstr ""

#~ msgid ""
#~ "2. If the difference between the "
#~ "maximum value and the mean value "
#~ "is greater than the difference between"
#~ " the mean value and the minimum "
#~ "value, the peak data is considered, "
#~ "and the subsequent peak search operation"
#~ " can be performed; 3. On the "
#~ "contrary, the valley data is considered,"
#~ " and the subsequent valley search "
#~ "operation is performed;"
#~ msgstr ""

#~ msgid ""
#~ "n_{distance} =\n"
#~ "\n"
#~ msgstr ""

#~ msgid ""
#~ "rac{x_{distance}}{ rac{x_{right} - x_{left}}{gaps}}"
#~ " = rac{x_{distance} * gaps}{x_{right} - "
#~ "x_{left}}"
#~ msgstr ""

#~ msgid "Args:"
#~ msgstr ""

#~ msgid ""
#~ "x_distance (float): The minimum interval "
#~ "between peaks (based on the actual "
#~ "input semaphore). x_data (List, np.ndarray):"
#~ " input signal."
#~ msgstr ""

#~ msgid "Returns:"
#~ msgstr ""

#~ msgid "float: Minimum interval between peaks (based on the input signal index)"
#~ msgstr ""

#~ msgid ""
#~ "1. Find all peak points between "
#~ "the threshold range (-5, 5) according"
#~ " to the oscillation period and the"
#~ " initial peak voltage, i.e. `dc_max_list`;"
#~ " 2. Find all valley points between"
#~ " the threshold range (-5, 5) "
#~ "according to the oscillation period and"
#~ " the initial peak voltage, i.e. "
#~ "`dc_min_list`; 3. Find all `dc_max_list` "
#~ "voltages closest to `0v` and return "
#~ "as `dc_max`; 4. Find all `dc_min_list`"
#~ " voltages closest to `0v` and return"
#~ " as `dc_mix`;"
#~ msgstr ""

#~ msgid ""
#~ "dc_min (DC voltage corresponding to the"
#~ " minimum cavity frequency),     dc_max (DC"
#~ " voltage corresponding to the maximum "
#~ "cavity frequency)"
#~ msgstr ""

#~ msgid "dc_min (DC voltage corresponding to the minimum cavity frequency),"
#~ msgstr ""

#~ msgid "dc_max (DC voltage corresponding to the maximum cavity frequency)"
#~ msgstr ""

#~ msgid "Example"
#~ msgstr ""

#~ msgid "Input:"
#~ msgstr ""

#~ msgid "matrices = [A1, A2, A3, A4], repeat = 2"
#~ msgstr ""

#~ msgid "Output:"
#~ msgstr ""

#~ msgid "matrices = ["
#~ msgstr ""

#~ msgid ""
#~ "A1*A1, A1*A2, A1*A3, A1*A4, A2*A1, "
#~ "A2*A2, A2*A3, A2*A4, A3*A1, A3*A2, "
#~ "A3*A3, A3*A4, A4*A1, A4*A2, A4*A3, A4*A4"
#~ msgstr ""

#~ msgid "]"
#~ msgstr ""

#~ msgid "Notes"
#~ msgstr ""

#~ msgid ""
#~ "① First, it will be determined by"
#~ " the data distribution characteristics that"
#~ " the data is more suitable for "
#~ "peak or valley search; ② We use"
#~ " the `scipy.signal.find_peaks` method with "
#~ "all default parameters to find all "
#~ "possible peaks, and then calculate the"
#~ " prominence of each peak, denoted as"
#~ " `prominence`; ③ Select the maximum "
#~ "degree of protrusion as the peak-"
#~ "seeking condition for the next peak-"
#~ "seeking; ④ Finally, the index and "
#~ "value corresponding to the peak are "
#~ "returned"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`calculate_offset_arr "
#~ "<pyQCat.analysis.algorithms.calculate_offset_arr>`\\ "
#~ "\\(response\\_arr\\[\\, z\\_amp\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`calculate_distortion "
#~ "<pyQCat.analysis.algorithms.calculate_distortion>`\\ "
#~ "\\(dt\\_list\\, so\\_list\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`calculate_lfilter_paras "
#~ "<pyQCat.analysis.algorithms.calculate_lfilter_paras>`\\ "
#~ "\\(\\*parameters\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`create_verify_pulse "
#~ "<pyQCat.analysis.algorithms.create_verify_pulse>`\\ \\(\\[num\\,"
#~ " sample\\_rate\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`calculate_pre_distortion_wave "
#~ "<pyQCat.analysis.algorithms.calculate_pre_distortion_wave>`\\ "
#~ "\\(delay\\_arr\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`calculate_simulation_wave "
#~ "<pyQCat.analysis.algorithms.calculate_simulation_wave>`\\ "
#~ "\\(...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_peak_point "
#~ "<pyQCat.analysis.algorithms.get_peak_point>`\\ \\(xdata\\, "
#~ "ydata\\[\\, distance\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`judge_peak_dip "
#~ "<pyQCat.analysis.algorithms.judge_peak_dip>`\\ \\(data\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`distance_to_point "
#~ "<pyQCat.analysis.algorithms.distance_to_point>`\\ "
#~ "\\(x\\_distance\\, x\\_data\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_dc_point "
#~ "<pyQCat.analysis.algorithms.get_qubit_dc_point>`\\ "
#~ "\\(max\\_reference\\, freq\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_coupler_dc_point "
#~ "<pyQCat.analysis.algorithms.get_coupler_dc_point>`\\ "
#~ "\\(dc\\_list\\, fr\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`find_coincident_point "
#~ "<pyQCat.analysis.algorithms.find_coincident_point>`\\ "
#~ "\\(points\\, N\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`find_peaks_with_prominence "
#~ "<pyQCat.analysis.algorithms.find_peaks_with_prominence>`\\ "
#~ "\\(xdata\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`cosine_fit_guess "
#~ "<pyQCat.analysis.algorithms.cosine_fit_guess>`\\ \\(xdata\\, "
#~ "ydata\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`fourier_cycle_guess "
#~ "<pyQCat.analysis.algorithms.fourier_cycle_guess>`\\ \\(x\\, "
#~ "y\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`frequency <pyQCat.analysis.algorithms.frequency>`\\ "
#~ "\\(x\\, y\\[\\, filter\\_window\\, "
#~ "filter\\_dim\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`max_height <pyQCat.analysis.algorithms.max_height>`\\ "
#~ "\\(y\\[\\, percentile\\, absolute\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`min_height <pyQCat.analysis.algorithms.min_height>`\\ "
#~ "\\(y\\[\\, percentile\\, absolute\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_height <pyQCat.analysis.algorithms.get_height>`\\ "
#~ "\\(y\\, find\\_height\\[\\, absolute\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`exp_decay <pyQCat.analysis.algorithms.exp_decay>`\\ \\(x\\, y\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`oscillation_exp_decay "
#~ "<pyQCat.analysis.algorithms.oscillation_exp_decay>`\\ \\(x\\,"
#~ " y\\[\\, filter\\_window\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`constant_sinusoidal_offset "
#~ "<pyQCat.analysis.algorithms.constant_sinusoidal_offset>`\\ "
#~ "\\(y\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`constant_spectral_offset "
#~ "<pyQCat.analysis.algorithms.constant_spectral_offset>`\\ "
#~ "\\(y\\[\\, filter\\_window\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`full_width_half_max "
#~ "<pyQCat.analysis.algorithms.full_width_half_max>`\\ \\(x\\, "
#~ "y\\, peak\\_index\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`IQdiscriminator "
#~ "<pyQCat.analysis.algorithms.IQdiscriminator>`\\ \\(I\\_list\\,"
#~ " Q\\_list\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubits_probability "
#~ "<pyQCat.analysis.algorithms.get_qubits_probability>`\\ "
#~ "\\(discriminator\\_list\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_fidelity "
#~ "<pyQCat.analysis.algorithms.correct_fidelity>`\\ \\(bit\\_num\\,"
#~ " index\\_list\\, std\\_p\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`simulate_qgate "
#~ "<pyQCat.analysis.algorithms.simulate_qgate>`\\ \\(qubit\\_num\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`smooth <pyQCat.analysis.algorithms.smooth>`\\ "
#~ "\\(x\\[\\, window\\_length\\, window\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`init_qst <pyQCat.analysis.algorithms.init_qst>`\\ \\(us\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`qst <pyQCat.analysis.algorithms.qst>`\\ "
#~ "\\(measure\\_data\\, u\\_array\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`qst_mle <pyQCat.analysis.algorithms.qst_mle>`\\ "
#~ "\\(measure\\_data\\, u\\_array\\[\\, F\\, rho0\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`init_qpt <pyQCat.analysis.algorithms.init_qpt>`\\ \\(matrices\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`qpt <pyQCat.analysis.algorithms.qpt>`\\ \\(rhos\\,"
#~ " e\\_rhos\\, T\\[\\, return\\_all\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`tensor <pyQCat.analysis.algorithms.tensor>`\\ \\(matrices\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`tensor_combinations "
#~ "<pyQCat.analysis.algorithms.tensor_combinations>`\\ "
#~ "\\(matrices\\, repeat\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`gen_ideal_chi_matrix "
#~ "<pyQCat.analysis.algorithms.gen_ideal_chi_matrix>`\\ "
#~ "\\(unitary\\, qpt\\_ops\\, tomo\\_ops\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`qpt_mle <pyQCat.analysis.algorithms.qpt_mle>`\\ "
#~ "\\(diags\\_out\\, rhos\\_in\\, Us\\, As\\, "
#~ "T\\[\\, F\\, chi0\\]\\)"
#~ msgstr ""

