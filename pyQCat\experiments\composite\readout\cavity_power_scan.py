# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/02/28
# __author:       <PERSON><PERSON><PERSON><PERSON>

from ....analysis.library.cavity_power_scan_analysis import CavityPowerScanAnalysis
from ....structures import MetaData, Options
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import CavityFreqSpectrum


class CavityPowerScan(CompositeExperiment):
    _sub_experiment_class = CavityFreqSpectrum

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("power_list", list)
        options.power_list = None
        options.run_mode = ExperimentRunMode.sync_mode
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("f_threshold", float)
        options.set_validator("coeff", [1, 2, 3])

        options.coeff = 2
        options.quality_bounds = [0.98, 0.95, 0.85]
        options.data_key = ["fc"]
        options.is_plot = True
        options.figsize = (12, 8)
        options.f_threshold = 0.5  # Unit: M

        return options

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        coeff = self.analysis_options.coeff
        metadata.draw_meta = {"coeff": coeff}
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        power_list = self.experiment_options.power_list

        if self.coupler and self.child_experiment.is_coupler_exp is False:
            result_name = self.child_experiment.coupler.name
        else:
            result_name = self.child_experiment.qubit.name

        self.set_analysis_options(result_name=result_name)

        self.set_run_options(
            x_data=power_list,
            analysis_class=CavityPowerScanAnalysis,
        )

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "power":
                result.extra["path"] = "Qubit.probe_power"
            elif key == "fr":
                result.extra["path"] = "Qubit.probe_freq"
            elif key == "goodness":
                result.extra["path"] = "Qubit.goodness"

    def _setup_child_experiment(
        self, exp: "CavityFreqSpectrum", index: int, value: float
    ):
        """Set child_experiment some options."""
        cs_exp = exp
        cs_exp.run_options.index = index
        total = len(self.run_options.x_data)

        cs_exp.set_parent_file(self, f"power={value}dBm", index, total)
        cs_exp.set_experiment_options(
            readout_power=value,
            add_pi_pulse=False,
        )
        self._check_simulator_data(cs_exp, index)

    def _handle_child_result(self, exp: "CavityFreqSpectrum"):
        # collect child experiment result and provide it for parent.
        provide_field = self.analysis_options.data_key[0]
        fr = exp.analysis.results.fr.value
        exp.analysis.provide_for_parent.update({provide_field: fr})
        exp.experiment_data.metadata.process_meta["quality"] = exp.analysis.quality

    def _alone_save_result(self):
        """Save special result."""
        if self.analysis.analysis_datas.fc:
            x = self.analysis.analysis_datas.fc.x
            y = self.analysis.analysis_datas.fc.y
            self.file.save_data(x, y, name=f"{self}(power-fc)")
