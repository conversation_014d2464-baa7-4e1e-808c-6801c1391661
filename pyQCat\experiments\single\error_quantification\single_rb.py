# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/29
# __author:       <PERSON><PERSON><PERSON>

"""
Standard RB Experiment class.
"""

import os
import pathlib
import time
from typing import List

import numpy as np

from ....analysis import RBAnalysis
from ....errors import FileTypeError
from ....gate.notable_gate import CLIFFORD_GATE_SET, INTERLEAVED_GATE_1Q, GateBucket
from ....structures import MetaD<PERSON>, Options
from ....tools import RandomType, qarange, seed_randomer, time_cal
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment

try:
    import rb_generator
except ImportError:
    rb_generator = None

_CLIFFORD_DATA = os.path.join(os.path.dirname(os.path.dirname(__file__)), "clifford")


class RBSingle(TopExperiment):
    """Standard single qubit randomized benchmarking experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()

        interleaved_gates = [None]
        interleaved_gates.extend(list(INTERLEAVED_GATE_1Q.keys()))

        options.set_validator("times", (1, 50, 0))
        options.set_validator("depth1", list)
        options.set_validator("depth2", list)
        options.set_validator("depth3", list)
        options.set_validator("depth4", list)
        options.set_validator("interleaved_gate", interleaved_gates)
        options.set_validator("gate_split", bool)
        options.set_validator("mode", ["cache", "dynamic", "cpp"])
        options.set_validator("open_seed", bool)
        options.set_validator("seed", str)

        options.times = 30
        options.depth1 = qarange(2, 10, 2)
        options.depth2 = qarange(15, 50, 5)
        options.depth3 = qarange(60, 100, 10)
        options.depth4 = qarange(120, 200, 20)
        options.interleaved_gate = None
        options.gate_split = False
        options.mode = "cpp"
        options.open_seed = False
        options.seed = None
        options.check_matrix = False

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("std_bound", float)
        options.set_validator("fidelity_threshold", float)

        options.depths = None
        options.k = None
        options.rate = None
        options.data_key = None
        options.quality_bounds = [0.9, 0.85, 0.77]
        options.expect_depth = 100
        options.std_bound = 0.05
        options.fidelity_threshold = 0.995

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.gate_bucket = GateBucket()
        options.standard_clifford_set = CLIFFORD_GATE_SET
        options.depths = []
        options.clifford_compose_1q = np.load(
            str(pathlib.Path(_CLIFFORD_DATA, "clifford_compose_1q.npz"))
        )["table"]
        options.clifford_inverse_1q = np.load(
            str(pathlib.Path(_CLIFFORD_DATA, "clifford_inverse_1q.npz"))
        )["table"]
        options.seed = 0
        options.gate_1q = list(INTERLEAVED_GATE_1Q.keys())
        options.injection_func = [
            "_gate_split",
            "_get_quantum_circuits",
            "_get_quantum_circuits2",
            "_get_quantum_circuits3",
            "_get_clifford_matrix_set",
            "_get_ancilla_gate",
            "_split_gate",
            "_check_circuit",
            "_check_cpp_matrix",
        ]

        return options

    @staticmethod
    def update_instrument(builder):
        builder.sweep_readout_trigger_delay(
            builder.qubit.readout_channel, builder._pulse_time_list
        )

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        self.run_options.gate_bucket.bind_single_gates(self.qubit)

        depths = []
        depth1 = self.experiment_options.depth1
        depth2 = self.experiment_options.depth2
        depth3 = self.experiment_options.depth3
        depth4 = self.experiment_options.depth4
        for ds in [depth1, depth2, depth3, depth4]:
            if ds:
                depths.extend(ds)

        if self.experiment_options.gate_split is True:
            rate = 53 / 24
        else:
            rate = 1.875

        interleaved_gate = self.experiment_options.interleaved_gate
        if interleaved_gate is None or interleaved_gate == "None":
            interleaved_gate = None

        seed = self.experiment_options.seed or int(time.time())
        self.set_run_options(depths=depths, seed=int(seed))
        self.set_experiment_options(
            data_type="I_Q", interleaved_gate=interleaved_gate, seed=str(seed)
        )
        self.set_analysis_options(
            depths=depths, k=self.experiment_options.times, rate=rate
        )
        self._gate_split()

        if rb_generator is None and self.experiment_options.mode == "cpp":
            self.experiment_options.mode = "cache"

        self.run_options.x_data = np.asarray(depths).repeat(
            self.experiment_options.times
        )
        self.run_options.analysis_class = RBAnalysis

    def _gate_split(self):
        new_gate_1q = []
        for gate in self.run_options.gate_1q:
            if self.experiment_options.gate_split:
                if gate == "X":
                    new_gate_1q.append(["X/2", "X/2"])
                elif gate == "Y":
                    new_gate_1q.append(["Y/2", "Y/2"])
                else:
                    new_gate_1q.append([gate])
            else:
                new_gate_1q.append([gate])
        self.run_options.gate_1q = new_gate_1q

    def _metadata(self) -> MetaData:
        """Set RB experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"K": self.experiment_options.times}
        if self.experiment_options.open_seed:
            metadata.draw_meta = {"Seed": self.experiment_options.seed}
        return metadata

    @staticmethod
    def set_xy_pulses(self):
        if self.experiment_options.gate_split and self.experiment_options.mode != "cpp":
            self._split_gate()

        if self.experiment_options.mode == "dynamic":
            clifford_matrix_set = self._get_clifford_matrix_set()
            quantum_circuits = self._get_quantum_circuits(clifford_matrix_set)
        elif self.experiment_options.mode == "cache":
            quantum_circuits = self._get_quantum_circuits2()
        else:
            quantum_circuits = self._get_quantum_circuits3()

        pulse_list = []
        for circuits in quantum_circuits:
            pulse_list.append(
                self.run_options.gate_bucket.get_xy_pulse(self.qubit, circuits)
            )
        self.play_pulse("XY", self.qubit, pulse_list)

    @time_cal
    def _get_quantum_circuits(self, clifford_matrix_set: List):
        """todo"""
        random_gate_max_value = len(self.run_options.standard_clifford_set) - 1
        quantum_circuit_list = []
        for circuit_depth in self.run_options.depths:
            for _ in range(self.experiment_options.times):
                quantum_circuit_cache = []
                quantum_circuit_matrix_cache = np.eye(2)
                for __ in range(int(circuit_depth)):
                    seed = None
                    if self.experiment_options.open_seed is True:
                        seed = self.run_options.seed
                        self.run_options.seed += 1
                    random_gate_index = seed_randomer(
                        style=RandomType.randint,
                        right=random_gate_max_value,
                        seed=seed,
                    )
                    random_gate = self.run_options.standard_clifford_set[
                        random_gate_index
                    ]
                    random_gate_matrix = clifford_matrix_set[random_gate_index]
                    quantum_circuit_matrix_cache = np.dot(
                        random_gate_matrix, quantum_circuit_matrix_cache
                    )
                    quantum_circuit_cache += random_gate
                    if self.experiment_options.interleaved_gate:
                        interleaved_gate = [self.experiment_options.interleaved_gate]
                        quantum_circuit_cache += interleaved_gate
                        interleaved_gate_matrix = np.eye(2)
                        for interleaved_gate in interleaved_gate[::-1]:
                            interleaved_gate_matrix = np.dot(
                                interleaved_gate_matrix,
                                self.run_options.gate_bucket.get_matrix(
                                    interleaved_gate
                                ),
                            )
                        quantum_circuit_matrix_cache = np.dot(
                            interleaved_gate_matrix, quantum_circuit_matrix_cache
                        )
                # Use the Clifford matrix set to calculate the matrix corresponding
                # to the spliced circuit quantum_circuit_cache, and obtain the
                # ancilla gate
                ancilla_gate = self._get_ancilla_gate(
                    clifford_matrix_set, quantum_circuit_matrix_cache
                )
                quantum_circuit_cache += ancilla_gate
                quantum_circuit_list.append(quantum_circuit_cache)
        return quantum_circuit_list

    @time_cal
    def _get_quantum_circuits2(self):
        random_gate_max_value = len(self.run_options.standard_clifford_set) - 1
        clifford_compose_1q = self.run_options.clifford_compose_1q
        clifford_inverse_1q = self.run_options.clifford_inverse_1q
        standard_clifford_set = self.run_options.standard_clifford_set
        times = self.experiment_options.times

        quantum_circuit_list = []
        for circuit_depth in self.run_options.depths:
            for _ in range(times):
                cliff_index = None
                quantum_circuit_cache = []
                for __ in range(int(circuit_depth)):
                    seed = None
                    if self.experiment_options.open_seed is True:
                        seed = self.run_options.seed
                        self.run_options.seed += 1
                    random_gate_index = seed_randomer(
                        style=RandomType.randint,
                        right=random_gate_max_value,
                        seed=seed,
                    )
                    if cliff_index is None:
                        cliff_index = random_gate_index
                    else:
                        cliff_index = clifford_compose_1q[
                            cliff_index, random_gate_index
                        ]

                    random_gate = standard_clifford_set[random_gate_index]
                    quantum_circuit_cache += random_gate

                    if self.experiment_options.interleaved_gate:
                        cliff_index = (
                            clifford_compose_1q[
                                cliff_index,
                                INTERLEAVED_GATE_1Q.get(
                                    self.experiment_options.interleaved_gate
                                ),
                            ]
                        )
                        quantum_circuit_cache.append(
                            self.experiment_options.interleaved_gate
                        )

                quantum_circuit_cache += standard_clifford_set[
                    clifford_inverse_1q[cliff_index]
                ]
                quantum_circuit_list.append(quantum_circuit_cache)

        return quantum_circuit_list

    @time_cal
    def _get_quantum_circuits3(self):
        times = self.experiment_options.times
        rb22 = rb_generator.RB22()
        if not rb22.load_from_file(str(pathlib.Path(_CLIFFORD_DATA, "rb22.dat"))):
            raise FileTypeError("load rb22 data error!")

        quantum_circuit_list = []
        for circuit_depth in self.run_options.depths:
            for _ in range(times):
                cur_circuit = []

                # rb22 inverse gate
                sequence = clifford_random(
                    rb22,
                    circuit_depth,
                    self.run_options.seed,
                    self.experiment_options.interleaved_gate,
                )
                self.run_options.seed += 1

                # build quantum circuit
                for cir in sequence:
                    cur_circuit.extend(self.run_options.gate_1q[cir])

                # check matrix
                if self.experiment_options.check_matrix:
                    self._check_cpp_matrix(cur_circuit)

                quantum_circuit_list.append(cur_circuit)

        return quantum_circuit_list

    def _get_clifford_matrix_set(self):
        standard_clifford_set = self.run_options.standard_clifford_set
        gate_bucket = self.run_options.gate_bucket
        clifford_matrix_struct = []
        for _, gate_group in enumerate(standard_clifford_set):
            matrix_temp = 1
            # Reverse the list.
            # The order of Clifford gate set is the opposite of
            # the order of matrix calculation.
            gate_group_reverse = gate_group[::-1]
            for gate in gate_group_reverse:
                matrix_temp = np.around(
                    np.dot(matrix_temp, gate_bucket.get_matrix(gate)), decimals=8
                )
            clifford_matrix_struct.append(matrix_temp)
        return clifford_matrix_struct

    def _get_ancilla_gate(self, clifford_matrix_set, gate_matrix):
        ancilla_gate = None
        for i, m in enumerate(clifford_matrix_set):
            # judge m == matrix_ancilla
            if abs(np.trace(np.dot(m, gate_matrix))) / 2 > 0.999:
                # print(CLIFFORD_GATE_SET[i])
                ancilla_gate = self.run_options.standard_clifford_set[i]
                # print(f"ancilla_gate={ancilla_gate}")
                break
        return ancilla_gate

    def _split_gate(self):
        for gate_set in self.run_options.standard_clifford_set:
            for i, gate in enumerate(gate_set):
                if gate == "X":
                    gate_set.pop(i)
                    gate_set.insert(i, "X/2")
                    gate_set.insert(i + 1, "X/2")
                if gate == "-X":
                    gate_set.pop(i)
                    gate_set.insert(i, "-X/2")
                    gate_set.insert(i + 1, "-X/2")
                if gate == "Y":
                    gate_set.pop(i)
                    gate_set.insert(i, "Y/2")
                    gate_set.insert(i + 1, "Y/2")
                if gate == "-Y":
                    gate_set.pop(i)
                    gate_set.insert(i, "-Y/2")
                    gate_set.insert(i + 1, "-Y/2")

    def _check_circuit(self, circuit_list):
        res = []
        for circuit in circuit_list:
            u = np.eye(2)
            for gate in circuit:
                u = np.dot(u, self.experiment_options.gate_map[gate].matrix)
            res.append(u)
        return res

    def _set_result_path(self):
        """Set path to save parameter of Qubit."""
        for key, result in self.analysis.results.items():
            if key == "fidelity":
                result.extra["path"] = "Qubit.fidelity"
            elif key == "depth":
                result.extra["path"] = "Qubit.rb_depth"

    def _check_cpp_matrix(self, cur_circuit: List):
        matrix = np.eye(2)
        for cir in cur_circuit:
            matrix = np.dot(self.run_options.gate_bucket.get_matrix(cir), matrix)
        assert round(abs(np.trace(matrix)) / 2, 8) == 1.0


def clifford_random(
    rb,
    depth: int,
    seed: int,
    interleaved_gate=None,
):
    np.random.seed(seed)
    clifford_list = np.random.randint(0, rb.N, depth)

    # interleaved gate
    if interleaved_gate:
        special_gate_names = rb.get_special_operations_str()
        special_gates = rb.get_special_operations()
        if interleaved_gate not in special_gate_names:
            raise RuntimeError("Invalid interleaved gate.")
        interleaved_gate_pos = special_gate_names.index(interleaved_gate)
        interleaved_group_pos = special_gates[interleaved_gate_pos]
        new_clifford_list = []
        for cir in clifford_list:
            new_clifford_list.extend([cir, interleaved_group_pos])
        clifford_list = new_clifford_list

    # generate sequence
    sequence, inverse_sequence = rb.get_full_sequence_and_inverse_sequence(
        clifford_list
    )
    sequence.extend(inverse_sequence)

    return sequence
