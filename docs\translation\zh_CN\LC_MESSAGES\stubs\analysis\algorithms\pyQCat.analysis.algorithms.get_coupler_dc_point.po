# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 19:06+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.get_coupler_dc_point.rst:2
msgid "pyQCat.analysis.algorithms.get\\_coupler\\_dc\\_point"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point:1
msgid "Get coupler sweet point dc value."
msgstr "获取耦合器简并点电压"

#: of pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point:4
msgid "Scanned DC list."
msgstr "扫描电压列表"

#: of pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point:7
msgid "Corresponding cavity frequency at each DC."
msgstr "每个DC下获取的腔频"

#: of pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point:10
msgid ""
"dc_min (DC voltage corresponding to the minimum cavity frequency), dc_max"
" (DC voltage corresponding to the maximum cavity frequency)"
msgstr "dc_min(最小腔频对应的电压)，dc_max(最大腔频对应的电压)"

#: of pyQCat.analysis.algorithms.find_peak.get_coupler_dc_point
msgid "Return type"
msgstr ""

#~ msgid ""
#~ "Calculate coupler tunable dc min and "
#~ "max point. :param dc_list: List of "
#~ "scan dc range. :type dc_list: List, "
#~ "np.ndarray :param fr_list: List of "
#~ "frequency value. :type fr_list: List, "
#~ "np.ndarray"
#~ msgstr ""

#~ msgid "min_point, max_point"
#~ msgstr ""

#~ msgid ""
#~ "dc_min (DC voltage corresponding to the"
#~ " minimum cavity frequency),     dc_max (DC"
#~ " voltage corresponding to the maximum "
#~ "cavity frequency)"
#~ msgstr ""

#~ msgid "dc_min (DC voltage corresponding to the minimum cavity frequency),"
#~ msgstr ""

#~ msgid "dc_max (DC voltage corresponding to the maximum cavity frequency)"
#~ msgstr ""

