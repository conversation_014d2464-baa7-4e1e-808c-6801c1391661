# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/02/14
# __author:       xw

"""
Standard distortion RB Experiment class.
"""

import numpy as np

from ....analysis.library.rb_analysis import (
    DistortionRBAnalysis,
    DistortionRBFitAnalysis,
)
from ....gate.notable_gate import CLIFFORD_GATE_SET, GateBucket
from ....pulse.pulse_lib import Constant
from ....qaio_property import QAIO
from ....structures import MetaData, Options
from ..error_quantification.single_rb import RBSingle


class Distortion_RB(RBSingle):
    """Standard single qubit distortion randomized benchmarking experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()
        options.set_validator("z_pulse_params", dict)

        options.z_pulse_params = {
            "t_head": 50,
            "t_bottom": 100,
            "t_wait": 30,
            "amp_bottom": 0,
            "amp_tail": 0,
        }

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)

        options.depths = None
        options.k = None

        options.data_key = None
        options.quality_bounds = [0.9, 0.85, 0.77]
        options.result_parameters = ["rc", "rg", "fidelity", "depth", "goal"]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.gate_bucket = GateBucket()
        options.standard_clifford_set = CLIFFORD_GATE_SET
        options.xy_pulse_list = []
        options.z_pulse_list = []
        return options

    @staticmethod
    def update_instrument(self):
        depths = len(self.run_options.depths) * self.experiment_options.times

        self.sweep_readout_trigger_delay(
            self.qubit.readout_channel, self._pulse_time_list[:depths]
        )

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        x_data = np.asarray(self.run_options.depths).repeat(
            self.experiment_options.times
        )
        self.run_options.x_data = x_data
        if len(self.run_options.depths) < 3:
            analysis_class = DistortionRBAnalysis
        else:
            analysis_class = DistortionRBFitAnalysis
        self.run_options.analysis_class = analysis_class

    def _metadata(self) -> MetaData:
        """Set RB experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"K": self.experiment_options.times}
        return metadata

    def _get_pulses(self):
        if self.experiment_options.gate_split:
            self._split_gate()

        if self.experiment_options.mode == "dynamic":
            clifford_matrix_set = self._get_clifford_matrix_set()
            quantum_circuits = self._get_quantum_circuits(clifford_matrix_set)
        else:
            quantum_circuits = self._get_quantum_circuits2()

        for circuits in quantum_circuits:
            pulse = self.run_options.gate_bucket.get_xy_pulse(self.qubit, circuits)
            # pulse upward is original RB pulse
            front_constant = Constant(
                self.experiment_options.z_pulse_params["t_head"], 0, "Z"
            )

            center_delay = Constant(
                self.experiment_options.z_pulse_params["t_bottom"],
                self.experiment_options.z_pulse_params["amp_bottom"],
                "Z",
            )

            rear_constant = Constant(
                self.experiment_options.z_pulse_params["t_wait"],
                self.experiment_options.z_pulse_params["amp_tail"],
                "Z",
            )

            z_pulse_before = front_constant() + center_delay() + rear_constant()
            # generate xy pulse and z pulse from z_pulse_before and pulse
            # add 5 ns 0 at the end of the pulse
            xy_zeros_end = Constant(5, 0, "XY")
            z_zeros_end = Constant(5, 0, "Z")
            # xy_pulse
            xy_pulse_before = Constant(z_pulse_before.width, 0, "XY")
            # xy_pulse_before.get_pulse()
            xy_pulse = xy_pulse_before() + pulse + xy_zeros_end()
            # z_pulse
            z_pulse_after = Constant(
                pulse.width, self.experiment_options.z_pulse_params["amp_tail"], "Z"
            )

            z_pulse = z_pulse_before + z_pulse_after() + z_zeros_end()
            z_pulse.only_z_correct = [0, int(z_pulse.width * QAIO.awg_sample_rate) + 1]
            self.run_options.xy_pulse_list.append(xy_pulse)
            self.run_options.z_pulse_list.append(z_pulse)

    @staticmethod
    def set_xy_pulses(self):
        Distortion_RB._get_pulses(self)
        xy_pulse_list = self.run_options.xy_pulse_list
        self.play_pulse("XY", self.qubit, xy_pulse_list)

    @staticmethod
    def set_z_pulses(self):
        z_pulse_list = self.run_options.z_pulse_list
        self.play_pulse("Z", self.qubit, z_pulse_list)
