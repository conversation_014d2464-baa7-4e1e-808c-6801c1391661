<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1161</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>WorkSpace Sync History</string>
  </property>
  <property name="windowIcon">
   <iconset resource="_imgs/_imgs.qrc">
    <normaloff>:/context-edit.png</normaloff>:/context-edit.png</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <widget class="QSplitter" name="splitter_2">
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
      <widget class="QWidget" name="widget_2" native="true">
       <layout class="QVBoxLayout" name="verticalLayout_4" stretch="2,8">
        <item>
         <widget class="QWidget" name="widget" native="true">
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QWidget" name="widget_12" native="true">
             <layout class="QVBoxLayout" name="verticalLayout_2">
              <property name="leftMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QWidget" name="widget_6" native="true">
                <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="2,6,3">
                 <property name="spacing">
                  <number>6</number>
                 </property>
                 <property name="topMargin">
                  <number>0</number>
                 </property>
                 <property name="bottomMargin">
                  <number>0</number>
                 </property>
                 <item>
                  <widget class="QLabel" name="label_4">
                   <property name="text">
                    <string>user</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="SearchComboBox" name="UserContent"/>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_4">
                   <property name="orientation">
                    <enum>Qt::Orientation::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>91</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_7" native="true">
                <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="2,6,3">
                 <property name="sizeConstraint">
                  <enum>QLayout::SizeConstraint::SetDefaultConstraint</enum>
                 </property>
                 <item>
                  <widget class="QLabel" name="label_5">
                   <property name="text">
                    <string>sample</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="SearchComboBox" name="SampleContent">
                   <property name="enabled">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_5">
                   <property name="orientation">
                    <enum>Qt::Orientation::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>91</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_13" native="true">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="2,6,3">
                 <property name="spacing">
                  <number>5</number>
                 </property>
                 <property name="topMargin">
                  <number>0</number>
                 </property>
                 <property name="bottomMargin">
                  <number>0</number>
                 </property>
                 <item>
                  <widget class="QLabel" name="label_6">
                   <property name="text">
                    <string>env_name</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="SearchComboBox" name="EnvContent"/>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_6">
                   <property name="orientation">
                    <enum>Qt::Orientation::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>38</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_3" native="true">
                <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,6,3">
                 <property name="topMargin">
                  <number>0</number>
                 </property>
                 <property name="bottomMargin">
                  <number>0</number>
                 </property>
                 <item>
                  <widget class="QLabel" name="label">
                   <property name="text">
                    <string>name</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLineEdit" name="nameContent"/>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer">
                   <property name="orientation">
                    <enum>Qt::Orientation::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>103</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="widget_14" native="true">
             <layout class="QVBoxLayout" name="verticalLayout_5">
              <item>
               <spacer name="verticalSpacer_3">
                <property name="orientation">
                 <enum>Qt::Orientation::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>15</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="queryButton">
                <property name="text">
                 <string>query</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="verticalSpacer_4">
                <property name="orientation">
                 <enum>Qt::Orientation::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>15</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_2">
          <property name="title">
           <string>note list</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <item>
            <layout class="QHBoxLayout" name="page_layout" stretch="1,1,1,1,1,1">
             <item>
              <widget class="QLabel" name="label_3">
               <property name="text">
                <string>        Page</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QSpinBox" name="page_spinBox">
               <property name="enabled">
                <bool>true</bool>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_7">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="label_7">
               <property name="text">
                <string>      Volume</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QSpinBox" name="volume_spinBox">
               <property name="enabled">
                <bool>true</bool>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
               <property name="maximum">
                <number>10000000</number>
               </property>
               <property name="value">
                <number>10</number>
               </property>
               <property name="displayIntegerBase">
                <number>10</number>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_3">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QTableViewWorkSpaceNoteWidget" name="table_view_context"/>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QGroupBox" name="groupBox_3">
       <property name="title">
        <string>Change</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <widget class="QSplitter" name="splitter">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <widget class="QTreeViewSpaceNoteWidget" name="tree_view_context"/>
          <widget class="QTextEdit" name="textEdit"/>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QTreeViewSpaceNoteWidget</class>
   <extends>QTreeView</extends>
   <header>.widgets.chip_manage_files.tree_view_workspace_note</header>
  </customwidget>
  <customwidget>
   <class>QTableViewWorkSpaceNoteWidget</class>
   <extends>QTableView</extends>
   <header>.widgets.chip_manage_files.table_view_workspace_note</header>
  </customwidget>
  <customwidget>
   <class>SearchComboBox</class>
   <extends>QComboBox</extends>
   <header>.widgets.combox_custom.combox_search</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="_imgs/_imgs.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>queryButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>query_workspace_his()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>499</x>
     <y>98</y>
    </hint>
    <hint type="destinationlabel">
     <x>580</x>
     <y>349</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>page_spinBox</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>change_page()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>156</x>
     <y>221</y>
    </hint>
    <hint type="destinationlabel">
     <x>580</x>
     <y>349</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>volume_spinBox</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>change_volume()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>416</x>
     <y>221</y>
    </hint>
    <hint type="destinationlabel">
     <x>580</x>
     <y>349</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>UserContent</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>user_change()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>279</x>
     <y>47</y>
    </hint>
    <hint type="destinationlabel">
     <x>580</x>
     <y>349</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>SampleContent</sender>
   <signal>currentTextChanged(QString)</signal>
   <receiver>MainWindow</receiver>
   <slot>sample_change()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>279</x>
     <y>86</y>
    </hint>
    <hint type="destinationlabel">
     <x>580</x>
     <y>349</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>query_workspace_his()</slot>
  <slot>change_page()</slot>
  <slot>change_volume()</slot>
  <slot>user_change()</slot>
  <slot>sample_change()</slot>
 </slots>
</ui>
