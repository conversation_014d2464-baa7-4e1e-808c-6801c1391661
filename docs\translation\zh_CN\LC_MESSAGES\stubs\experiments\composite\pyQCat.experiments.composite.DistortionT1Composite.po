# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:2
msgid "pyQCat.experiments.composite.DistortionT1Composite"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite:1
msgid "Distortion node."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.experiments.composite.DistortionT1Composite.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.composite.DistortionT1Composite.component_experiment>`\\"
" \\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.composite.DistortionT1Composite.from_experiment_context>`\\"
" \\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.composite.DistortionT1Composite.get_qubit_str>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.composite.DistortionT1Composite.options_table>`\\ "
"\\(\\[mode\\, detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid ""
":py:obj:`run <pyQCat.experiments.composite.DistortionT1Composite.run>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite.run:1
msgid "Distortion T1 Composite Run Logic."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.composite.DistortionT1Composite.set_analysis_options>`\\"
" \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.composite.DistortionT1Composite.set_experiment_options>`\\"
" \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.composite.DistortionT1Composite.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.composite.DistortionT1Composite.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DistortionT1Composite.rst:32
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1:1:<autosummary>:1
msgid ""
":py:obj:`analysis "
"<pyQCat.experiments.composite.DistortionT1Composite.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.composite.DistortionT1Composite.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.composite.DistortionT1Composite.child_experiment>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.composite.DistortionT1Composite.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.composite.DistortionT1Composite.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:25
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:4
msgid ""
"iteration_times (int): Set experiment iteration times. xy_delay_max "
"(float): XY line max align delay value,"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:6
msgid "recommend less than tb."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:7
msgid ""
"sample_rate (float): Sample rate. init_step (float): Initial update xy "
"delay step,"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:9
msgid "must be multiple of sample period."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:10
msgid "delay_watershed (float): When xy delay more than watershed,"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:11
msgid "will scan points which set."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:12
msgid "scan_points (int): Scan point number."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:14
msgid ""
"z_amp (float): The const z_amp of Z line. gauss_sigma (float): The sigma "
"of GaussUP, GaussDown wave. gauss_width (float): The width of GaussUP, "
"GaussDown wave. const_width (float): The width of Constant wave. ta "
"(float): Set a width of Constant wave. tb (float): Set a width of "
"Constant wave. z_offset_list (List, np.ndarray): Scan Z offset range."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:22
msgid ""
"bit_type(str): bit type, normal `Qubit` or `Coupler`. base_history "
"(bool): Base on history distortion data calibrate."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._create_child_experiment
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_analysis_options
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_run_options
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_analysis_options:10
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:27
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_run_options:13
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_analysis_options:1
msgid "Default analysis options for DistortionT1Composite experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_analysis_options:8
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_run_options:4
msgid "Options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_analysis_options:4
msgid ""
"iteration_time (int): Iteration time number. dt_list (List[np.ndarray]): "
"so_list (List[np.ndarray]):"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_run_options:1
msgid ""
"Default options values for the experiment :meth:`run` method. Statistics "
"and saving parameters of running process."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_run_options:6
msgid ""
"distortion_width (float): Calibrate z line distortion width. "
"distortion_ab (List): IIR filter parameters. [ a, b ] delay_arr "
"(np.ndarray): Distortion data delay array. response_arr (np.ndarray): "
"Distortion data response array."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._metadata:1
msgid "Set metadata."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._create_child_experiment:1
msgid "Create a child experiment object."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._create_child_experiment:3
msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._create_child_experiment
msgid "Returns"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._create_child_experiment:4
msgid "BaseExperiment object."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._save_curve_analysis_plot:1
msgid "Save CurveAnalysis plot figure."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._adjust_xy_delay:1
msgid "According to the current xy delay, update changing step of xy delay."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._adjust_z_offset_list:1
msgid "Adjust z_offset_list."
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.__init__>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`component_experiment "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.component_experiment>`\\"
#~ " \\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.get_qubit_str>`\\"
#~ " \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.run>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.set_analysis_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.set_parent_file>`\\"
#~ " \\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.set_run_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`child_experiment "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.run_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.__init__>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`component_experiment "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.component_experiment>`\\"
#~ " \\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.get_qubit_str>`\\"
#~ " \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.options_table>`\\"
#~ " \\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.run>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.set_analysis_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.set_parent_file>`\\"
#~ " \\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.set_run_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`child_experiment "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.composite.DistortionT1Composite.run_options>`\\"
#~ msgstr ""

