# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.update_single_rd_ch_data.rst:2
msgid "pyQCat.tools.update\\_single\\_rd\\_ch\\_data"
msgstr ""

#: of pyQCat.tools.utilities.update_single_rd_ch_data:1
msgid "Update single readout channel args and update on this channel bit args."
msgstr ""

#: of pyQCat.tools.utilities.update_single_rd_ch_data
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.utilities.update_single_rd_ch_data:4
msgid "readout channel."
msgstr ""

#: of pyQCat.tools.utilities.update_single_rd_ch_data:7
msgid "List of Qubit object."
msgstr ""

#: of pyQCat.tools.utilities.update_single_rd_ch_data:13
msgid "Sample rate."
msgstr ""

#: of pyQCat.tools.utilities.update_single_rd_ch_data:16
msgid "Calculate bit's amp or not."
msgstr ""

#: of pyQCat.tools.utilities.update_single_rd_ch_data
msgid "Returns"
msgstr ""

#: of pyQCat.tools.utilities.update_single_rd_ch_data:19
msgid "union_bit_args (Dict):"
msgstr ""

#: of pyQCat.tools.utilities.update_single_rd_ch_data
msgid "Return type"
msgstr ""

