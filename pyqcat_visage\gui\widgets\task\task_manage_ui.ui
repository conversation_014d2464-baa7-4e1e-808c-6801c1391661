<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>910</width>
    <height>682</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dag Manage</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <widget class="QGroupBox" name="groupBox">
      <property name="title">
       <string>Task list</string>
      </property>
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="0" column="0">
        <widget class="QWidget" name="task_widget" native="true">
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <item>
           <widget class="QWidget" name="task_search_widget" native="true">
            <layout class="QVBoxLayout" name="verticalLayout">
             <item>
              <widget class="QWidget" name="task_id_widget" native="true">
               <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,8">
                <property name="spacing">
                 <number>9</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>9</number>
                </property>
                <property name="bottomMargin">
                 <number>9</number>
                </property>
                <item>
                 <widget class="QLabel" name="label">
                  <property name="text">
                   <string>task_id</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="task_id"/>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="name_widget" native="true">
               <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="2,8">
                <property name="spacing">
                 <number>9</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="label_2">
                  <property name="text">
                   <string>task_name</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="task_name"/>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QTableViewTaskWidget" name="tableTaskView"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QWidget" name="info_widget" native="true">
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <widget class="QTreeViewTaskInfoWidget" name="task_info_tree_view"/>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string extracomment="test">toolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="separator"/>
   <addaction name="actionQueryTask"/>
   <addaction name="separator"/>
   <addaction name="actionQueryHistory"/>
   <addaction name="separator"/>
   <addaction name="actionQuery"/>
   <addaction name="separator"/>
   <addaction name="actionRefresh"/>
  </widget>
  <action name="actionQueryTask">
   <property name="text">
    <string>QueryAll</string>
   </property>
   <property name="toolTip">
    <string>QueryAll</string>
   </property>
  </action>
  <action name="actionRefresh">
   <property name="text">
    <string>Refresh</string>
   </property>
  </action>
  <action name="actionQueryHistory">
   <property name="text">
    <string>QueryHistory</string>
   </property>
  </action>
  <action name="actionQuery">
   <property name="text">
    <string>Query</string>
   </property>
   <property name="toolTip">
    <string>Query</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QTableViewTaskWidget</class>
   <extends>QTableView</extends>
   <header location="global">.widgets.dag.table_view_task</header>
  </customwidget>
  <customwidget>
   <class>QTreeViewTaskInfoWidget</class>
   <extends>QTreeView</extends>
   <header>pyqcat_visage.gui.widgets.dag.tree_view_task_info</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>actionQueryTask</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>query_task()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>414</x>
     <y>314</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionRefresh</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>refresh()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>541</x>
     <y>335</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionQueryHistory</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>query_history()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>454</x>
     <y>340</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionQuery</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>query()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>454</x>
     <y>340</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>create_task()</slot>
  <slot>query_task()</slot>
  <slot>query_workspace()</slot>
  <slot>creat_work_space()</slot>
  <slot>refresh()</slot>
  <slot>chip_sample_change()</slot>
  <slot>space_sample_change()</slot>
  <slot>query_history()</slot>
  <slot>query()</slot>
 </slots>
</ui>
