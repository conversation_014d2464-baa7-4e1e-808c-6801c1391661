# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/08/10
# __author:       SS Fang

"""
XY Crosstalk Npi composite experiment.
"""

from ....analysis.library.xy_cross_analysis import XYCrosstalkNpiAnalysis
from ....structures import MetaD<PERSON>, Options
from ....types import ExperimentRunMode, StandardContext
from ...composite_experiment import CompositeExperiment
from ...single import XYCrosstalkNpiOnce


class XYCrosstalkNpi(CompositeExperiment):
    """XY Crosstalk composite experiment."""

    _sub_experiment_class = XYCrosstalkNpiOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment Options:
            target_name (str): Sub experiment target bit name.
            num_list (str): Scan crosstalk X pulse amp or phase number list.

        """
        options = super()._default_experiment_options()

        options.set_validator("target_name", str)
        options.set_validator("num_list", list)

        options.target_name = ""
        options.num_list = [19, 31, 51]

        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options."""
        options = super()._default_run_options()

        options.support_context = [StandardContext.CM]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options."""
        options = super()._default_analysis_options()

        options.set_validator("use_child_fit", bool)

        options.x_label = "amp_coe"
        options.y_label = "P0"
        options.plot_key = "P0"
        options.is_plot = True
        options.data_key = ["points"]
        options.fine = False
        options.step = 0.01
        options.use_child_fit = False

        return options

    def get_qubit_str(self):
        """Get qubit str, an args qubit for SaveFile class.

        Returns:
            qubit_str: string, use to create save data path.
        """
        target_name = self.experiment_options.target_name
        return target_name

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()

        target_name = self.experiment_options.target_name
        sweep_name = self.child_experiment.experiment_options.sweep_name
        metadata.draw_meta = {
            "target": target_name,
            "sweep": sweep_name,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        sweep_name = self.child_experiment.experiment_options.sweep_name
        sweep_list = self.child_experiment.experiment_options.sweep_list
        sweep_step = sweep_list[1] - sweep_list[0]
        self.set_analysis_options(x_label=sweep_name, step=sweep_step)
        self.set_run_options(
            x_data=self.experiment_options.num_list,
            analysis_class=XYCrosstalkNpiAnalysis,
        )

    def _setup_child_experiment(
        self, exp: "XYCrosstalkNpiOnce", index: int, value: float
    ):
        """Set child_experiment some options."""
        target_name = self.experiment_options.target_name
        length = len(self.experiment_options.num_list)
        xy_exp = exp

        xy_exp.set_parent_file(self, f"num={value}", index, length)
        xy_exp.set_experiment_options(target_name=target_name, num=int(value))
        self._check_simulator_data(xy_exp, index)

    def _handle_child_result(self, exp: "XYCrosstalkNpiOnce"):
        # collect child experiment result and provide it for parent.
        provide_field = self.analysis_options.data_key[0]
        use_child_fit = self.analysis_options.use_child_fit

        xy_exp = exp
        if use_child_fit is False:
            points = xy_exp.analysis.results.points_0.value
        else:
            points = xy_exp.analysis.results.fit_points_0.value
        xy_exp.analysis.provide_for_parent.update({provide_field: points})
