# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2020/11/27
# __author:       <PERSON>

"""T1 Experiment is used to get the qubit energy relaxation time."""

from copy import deepcopy
from typing import List, Optional, Union

import numpy as np

from ....analysis import T1Analysis, T1AnalysisV2
from ....errors import ExperimentOptionsError
from ....log import pyqlog
from ....parameters import options_wrapper
from ....pulse.pulse_function import f12_pi_pulse, pi_pulse
from ....pulse.pulse_lib import Constant
from ....structures import MetaData, Options
from ....tools import qarange
from ...coupler_experiment_v1 import (
    CouplerBaseExperimentV1 as CouplerBaseExperiment,
)
from ...coupler_experiment_v1 import (
    StandardContext,
)
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment
from ..single_gate.ramsey import RamseyExtend


@options_wrapper
class T1(TopExperiment):
    """T1 Experiment, get the qubit energy relaxation time."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            delays (List, np.ndarray): The list of delays that will
                                           be scanned in the experiment,
                                           in nanoseconds.
            z_amp (float): A ac pulse amplitude.
        """
        options = super()._default_experiment_options()

        options.set_validator("delays", list, limit_null=True)
        options.set_validator("z_amp", (-1, 1, 2))

        options.delays = qarange(200, 30000, 300)
        options.z_amp = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options.

        Analysis Options:
            quality_bounds (Iterable[float]): The bounds value of the
                                              goodness of fit.
            is_plot (bool): Plot or not analysis fit photo.
            data_key (List[str]): List of mark select data.
            p0 (Dict): Initial value of fitting parameters.
        """
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("quati_particle_analysis", bool)
        options.quality_bounds = [0.9, 0.85, 0.77]
        options.data_key = None
        options.p0 = {"tau": 10000}
        options.quati_particle_analysis = False

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default run options."""
        options = super()._default_run_options()
        options.injection_func = ["get_xy_pulse", "get_z_pulse"]
        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        if self.discriminator is not None:
            data_type = "I_Q"
        else:
            data_type = "amp_phase"

        self.set_experiment_options(data_type=data_type)

        self.set_run_options(
            x_data=self.experiment_options.delays,
            analysis_class=T1AnalysisV2
            if self.analysis_options.quati_particle_analysis is True
            else T1Analysis,
        )

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulses."""
        delay_list = self.experiment_options.delays
        pulses = self.get_xy_pulse(
            self.qubit, delay_list, self.experiment_options.fill_readout_point
        )
        self.play_pulse("XY", self.qubit, pulses)

    @staticmethod
    def set_z_pulses(self):
        """Set Z pulses."""
        if self.experiment_options.fill_readout_point is True:
            z_pulse_list = self.get_z_pulse(
                self.qubit,
                self.experiment_options.delays,
                self.experiment_options.z_amp or 0.0,
            )
            self.play_pulse("Z", self.qubit, z_pulse_list)
            return z_pulse_list

    @staticmethod
    def update_instrument(self):
        pulse = pi_pulse(self.qubit)
        real_delay_list = [
            delay + pulse.width for delay in self.experiment_options.delays
        ]
        self.sweep_readout_trigger_delay(self.qubit.readout_channel, real_delay_list)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {}
        z_amp = self.experiment_options.z_amp
        if z_amp is not None:
            metadata.draw_meta["ZAmp"] = (z_amp, "v")
        return metadata

    @staticmethod
    def get_xy_pulse(
        qubit, delay_list: Union[List, np.ndarray], fill_readout_point: bool = True
    ):
        """Get XY pulse list."""
        pulse = pi_pulse(qubit)()
        xy_pulse_list = []

        # optimize for zyc 2024/02/19: Cancel the scanning operation of the xy channel
        if fill_readout_point is True:
            for delay in delay_list:
                offset_pulse = Constant(delay, 0, name="XY")
                xy_pulse = deepcopy(pulse) + offset_pulse()
                xy_pulse_list.append(xy_pulse)
        else:
            for _ in delay_list:
                xy_pulse_list.append(deepcopy(pulse))

        return xy_pulse_list

    @staticmethod
    def get_z_pulse(qubit, delay_list: Union[List, np.ndarray], z_amp: float):
        """Get Z pulse list"""
        z_pulse_list = []
        drag = pi_pulse(qubit)
        drag_time = drag.width
        for delay in delay_list:
            front_constant = Constant(drag_time, 0)
            center_delay = Constant(delay, z_amp)

            t2_z_pulse = front_constant() + center_delay()
            t2_z_pulse.bit = qubit.bit
            t2_z_pulse.sweep = "sweep delay"

            z_pulse_list.append(t2_z_pulse)
        return z_pulse_list

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "tau":
                result.extra["path"] = "Coupler.T1" if self.coupler else "Qubit.T1"


class CouplerT1(CouplerBaseExperiment, T1):
    """Coupler T1 Experiment"""

    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        delay_list = builder.experiment_options.delays
        pulses = T1.get_xy_pulse(builder.driveQ, delay_list)
        builder.compose_xy_pulses(pulses)

    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""
        if builder.experiment_options.z_amp is not None:
            z_pulse_list = T1.get_z_pulse(
                builder.coupler,
                builder.experiment_options.delays,
                builder.experiment_options.z_amp,
            )
            builder.compose_z_pulses(z_pulse_list)
        else:
            CouplerBaseExperiment.set_z_pulses(builder)

    @staticmethod
    def update_instrument(builder):
        real_delay_list = [
            pulse.width for pulse in builder.xy_pulses.get(builder.probeQ)
        ]
        builder.sweep_readout_trigger_delay(
            builder.probeQ.readout_channel, real_delay_list
        )


class QCT1(T1):
    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.support_context = [StandardContext.CPC]
        return options

    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""
        if builder.experiment_options.z_amp is not None:
            z_pulse_list = T1.get_z_pulse(
                builder.qubit,
                builder.experiment_options.delays,
                builder.experiment_options.z_amp,
            )
            builder.play_pulse("Z", builder.couplers[0], z_pulse_list)
            return z_pulse_list

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        self.set_experiment_options(save_result=False)


class T1Extend(T1):
    """T1Extend experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""

        options = super()._default_experiment_options()
        options.set_validator("tq_name", str)
        options.set_validator("bq_name_list", list)
        options.set_validator("bq_z_amp_list", list)
        options.set_validator("bq_freq_list", list)
        options.set_validator("auto_set_coupler_zamp", bool)
        options.set_validator("auto_set_label", ["cz", "zz"])

        options.tq_name = None
        options.bq_name_list = None
        options.bq_freq_list = None
        options.bq_z_amp_list = None

        # get z amp form qubit pair
        options.auto_set_coupler_zamp = False
        options.auto_set_label = "cz"

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.drag_width = 0.0
        options.bq_list = []
        options.bq_z_amp_map = {}
        options.tq = None
        options.support_context = [StandardContext.URM]

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta.update({"tq": self.experiment_options.tq_name})
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        # function options
        tq_name = self.experiment_options.tq_name
        bq_name_list = self.experiment_options.bq_name_list or []
        bq_z_amp_list = self.experiment_options.bq_z_amp_list or []
        bq_freq_list = self.experiment_options.bq_freq_list or []

        # verify that the three lists of name/amp/freq are of equal length
        if bq_name_list:
            amp_list_length = len(bq_z_amp_list)
            freq_list_length = len(bq_freq_list)
            name_list_length = len(bq_name_list)
            if amp_list_length < name_list_length:
                bq_z_amp_list.extend(
                    [None for _ in range(name_list_length - amp_list_length)]
                )
            if freq_list_length < name_list_length:
                bq_freq_list.extend(
                    [None for _ in range(name_list_length - freq_list_length)]
                )

        # collect base qubit/coupler
        qubit_map = {qubit.name: qubit for qubit in self.qubits}
        coupler_map = {coupler.name: coupler for coupler in self.couplers}
        all_qc_map = {}
        all_qc_map.update(qubit_map)
        all_qc_map.update(coupler_map)
        all_qc_names = list(all_qc_map.keys())

        # obtain qubit object corresponding to tq_name
        if tq_name:
            if tq_name in ["ql", "qh"]:
                tq_name = getattr(self.qubit_pairs[0], tq_name)
            tq_obj = qubit_map.get(tq_name)
        else:
            qubit_list = []
            for q_obj in self.qubits:
                if q_obj.name not in bq_name_list:
                    qubit_list.append(q_obj)
            if qubit_list:
                tq_obj = qubit_list[0]
            else:
                tq_obj = None

        # tq_obj assert not None
        if tq_obj is None:
            raise ExperimentOptionsError(
                self,
                f"Set target {tq_name} not in all bit names!",
                "tq_name",
                tq_name,
            )

        # discriminator filter only for tq object
        if self.discriminator:
            if not isinstance(self.discriminator, list):
                dcm_list = [self.discriminator]
            else:
                dcm_list = self.discriminator

            tq_dcm = None
            for dcm_obj in dcm_list:
                if dcm_obj.name == tq_name:
                    tq_dcm = dcm_obj
                    break
            self.discriminator = tq_dcm

        # build bias qubit z amp map
        bq_list = []
        rp_bq_z_amp_map = {}
        for index, bq_name in enumerate(bq_name_list):
            if bq_name in ["qc", "qh", "ql"]:
                if bq_name == "qc" and self.experiment_options.auto_set_coupler_zamp:
                    # auto set qc z amp
                    bq_name = getattr(self.qubit_pair, bq_name)
                    gate_params = self.qubit_pair.gate_params(
                        self.experiment_options.auto_set_label
                    )
                    amp = gate_params.get(bq_name).amp
                    bq_z_amp_list[index] = amp
                else:
                    bq_name = getattr(self.qubit_pairs[0], bq_name)

            if bq_name not in all_qc_names:
                pyqlog.warning(
                    f"{bq_name} not in all names: {all_qc_names}, "
                    f"so will remove {bq_name} from {bq_name_list} ! "
                )
            else:
                bq_obj = all_qc_map.get(bq_name)
                bq_freq = bq_freq_list[index]
                bq_z_amp = bq_z_amp_list[index]

                trans_z_amp = RamseyExtend._trans_freq_to_amp(bq_obj, bq_freq, bq_z_amp)
                pyqlog.debug(f"{bq_name} trans result z_amp: {trans_z_amp}")
                if trans_z_amp is not None and not np.isnan(trans_z_amp):
                    bq_list.append(bq_obj)
                    rp_bq_z_amp_map.update({bq_name: trans_z_amp})

        # check run options
        multi_readout_channels = [tq_obj.readout_channel]
        drag_width = tq_obj.XYwave.time + 2 * tq_obj.XYwave.offset
        self.set_experiment_options(
            multi_readout_channels=multi_readout_channels,
        )
        self.set_run_options(
            measure_qubits=[tq_obj],
            tq=tq_obj,
            drag_width=drag_width,
            bq_list=bq_list,
            bq_z_amp_map=rp_bq_z_amp_map,
        )
        pyqlog.info(f"bias_qubit_zamp:{rp_bq_z_amp_map}")

    @staticmethod
    def get_z_pulse(
        qubit,
        delays: Union[List, np.ndarray],
        z_amp: Union[int, float],
        drag_time: Optional[Union[float, int]] = None,
    ):
        """Get Z pulse list"""
        if drag_time is None:
            drag = pi_pulse(qubit)
            drag_time = drag().width

        z_pulse_list = []
        for delay in delays:
            front_constant = Constant(drag_time, 0)
            center_delay = Constant(delay, z_amp)

            t2_z_pulse = front_constant() + center_delay()
            t2_z_pulse.bit = qubit.bit
            t2_z_pulse.sweep = "sweep delay"

            z_pulse_list.append(t2_z_pulse)
        return z_pulse_list

    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        delays = builder.experiment_options.delays
        xy_pulse_list = T1.get_xy_pulse(builder.run_options.tq, delays)

        for qubit in builder.qubits:
            if qubit == builder.run_options.tq:
                builder.play_pulse("XY", qubit, xy_pulse_list)
            else:
                new_xy_pulse_list = [
                    Constant(pulse_obj.width, 0, "XY")() for pulse_obj in xy_pulse_list
                ]
                builder.play_pulse("XY", qubit, new_xy_pulse_list)

    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""
        delays = builder.experiment_options.delays
        z_amp = builder.experiment_options.z_amp
        drag_width = builder.run_options.drag_width
        bq_z_amp_map = builder.run_options.bq_z_amp_map

        z_amp = z_amp or 0.0
        z_pulse_list = T1Extend.get_z_pulse(
            builder.run_options.tq, delays, z_amp, drag_width
        )
        builder.play_pulse("Z", builder.run_options.tq, z_pulse_list)

        for qc_obj in builder.qubits + builder.couplers:
            if qc_obj.name != builder.run_options.tq.name:
                bq_z_amp = bq_z_amp_map.get(qc_obj.name, 0.0)
                z_pulse_list = T1Extend.get_z_pulse(
                    qc_obj, delays, bq_z_amp, drag_width
                )
                builder.play_pulse("Z", qc_obj, z_pulse_list)

    @staticmethod
    def update_instrument(builder):
        qubit = builder.run_options.tq
        pulse = pi_pulse(qubit)
        real_delay_list = [
            delay + pulse.width for delay in builder.experiment_options.delays
        ]
        builder.sweep_readout_trigger_delay(qubit.readout_channel, real_delay_list)


class T1F12(T1):
    @staticmethod
    def get_xy_pulse(
        qubit, delay_list: Union[List, np.ndarray], fill_readout_point: bool = True
    ):
        """Get XY pulse list."""
        pulse = pi_pulse(qubit)() + f12_pi_pulse(qubit)()
        xy_pulse_list = []

        for delay in delay_list:
            offset_pulse = Constant(delay, 0, name="XY")
            xy_pulse = deepcopy(pulse) + offset_pulse() + pi_pulse(qubit)()
            xy_pulse_list.append(xy_pulse)

        return xy_pulse_list

    @staticmethod
    def get_z_pulse(qubit, delay_list: Union[List, np.ndarray], z_amp: float):
        """Get Z pulse list"""
        z_pulse_list = []
        drag_01 = pi_pulse(qubit)
        drag_12 = f12_pi_pulse(qubit)
        for delay in delay_list:
            front_constant = Constant(drag_01.width + drag_12.width, 0)
            center_delay = Constant(delay, z_amp)
            tail_delay = Constant(drag_01.width, 0)
            t2_z_pulse = front_constant() + center_delay() + tail_delay()
            t2_z_pulse.bit = qubit.bit
            t2_z_pulse.sweep = "sweep delay"
            z_pulse_list.append(t2_z_pulse)
        return z_pulse_list
