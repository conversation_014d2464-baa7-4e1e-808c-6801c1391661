# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/09
# __author:       ssfang

"""
Amp Optimization Analysis.

"""

import numpy as np

from ..oscillation_analysis import OscillationAnalysis
from ..specification import ParameterRepr
from ...structures import Options


class AmpOptAnalysis(OscillationAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.x_label = "Amplitude (v)"
        options.result_parameters = [ParameterRepr(name='Amp', repr="X-amp", unit="V"),
                                     ParameterRepr(name='points', repr="points", unit=None),
                                     ParameterRepr(name='freq', repr="freq", unit=None)]
        options.quality_bounds = [0.98, 0.95, 0.85]
        return options

    def _extract_result(self, data_key: str, quality=None):
        """Extract Xpi from fitted data.

        Args:
            data_key (str): The basis for selecting data.
            quality : Quality of fit outcome.
        """

        analysis_data = self.analysis_datas[data_key]
        x = analysis_data.x
        fit_data = analysis_data.fit_data
        y_fit = fit_data.y_fit
        _, freq, *_ = fit_data.popt
        target_index = np.argmax(y_fit)

        xpi_x = x[target_index]
        xpi_y = y_fit[target_index]

        for p in self.options.result_parameters:
            if p.name == "points":
                self.results.points.value = (round(xpi_x, 4), round(xpi_y, 4))
            elif p.name == "freq":
                self.results.freq.value = freq
            else:
                self.results.get(p.name).value = round(xpi_x, 4)

        if self.options.is_plot is True:
            pos = (round(xpi_x, 4), round(xpi_y, 4))
            self.drawer.set_options(text_pos=[pos],
                                    text_rp=[f"Xpi\n{pos}"],
                                    text_key=[data_key])
