# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:2
msgid "pyQCat.pulse.PulseComponent"
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent:1
msgid "Bases: :py:class:`~abc.ABC`"
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent:1
msgid "The abstract superclass for pulses."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.pulse.PulseComponent.__init__>`\\ \\(time\\, "
"type\\_\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid ""
":py:obj:`correct_ac_crosstalk "
"<pyQCat.pulse.PulseComponent.correct_ac_crosstalk>`\\ "
"\\(after\\_ac\\_corsstalk\\_pulse\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid "Correct crosstalk caused by electronics device."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid ""
":py:obj:`correct_compensate "
"<pyQCat.pulse.PulseComponent.correct_compensate>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid "Correct compensate caused by electronics device."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid ""
":py:obj:`correct_delay <pyQCat.pulse.PulseComponent.correct_delay>`\\ "
"\\(delay\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid "Correct delay time caused by electronics device."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid ""
":py:obj:`correct_distortion "
"<pyQCat.pulse.PulseComponent.correct_distortion>`\\ \\(\\[plot\\]\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid "Correct distortion caused by electronics device."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid ""
":py:obj:`correct_pulse <pyQCat.pulse.PulseComponent.correct_pulse>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
#: of pyQCat.pulse.base_pulse.PulseComponent.correct_pulse:1
msgid "Correct XY or Z pulse"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid ":py:obj:`get_pulse <pyQCat.pulse.PulseComponent.get_pulse>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
#: of pyQCat.pulse.base_pulse.PulseComponent.get_pulse:1
msgid "Calculate pulse sequence."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_raw_pulse <pyQCat.pulse.PulseComponent.get_raw_pulse>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
#: of pyQCat.pulse.base_pulse.PulseComponent.get_raw_pulse:1
msgid "Get pulse raw data."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid ""
":py:obj:`plot <pyQCat.pulse.PulseComponent.plot>`\\ \\(file\\_name\\[\\, "
"use\\_points\\]\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
#: of pyQCat.pulse.base_pulse.PulseComponent.plot:1
msgid "Plot Pulse's sequence use matplotlib."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid ""
":py:obj:`validate_parameters "
"<pyQCat.pulse.PulseComponent.validate_parameters>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:30:<autosummary>:1
msgid "Validate parameters."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseComponent.rst:32
msgid "Attributes"
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid ":py:obj:`attach <pyQCat.pulse.PulseComponent.attach>`\\"
msgstr ""

#: of pyQCat.pulse.PulseComponent.attach:1
#: pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid "Flag of Pulse instance excuted '+' or '+=' operation."
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid ":py:obj:`bit <pyQCat.pulse.PulseComponent.bit>`\\"
msgstr ""

#: of pyQCat.pulse.PulseComponent.bit:1
#: pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid "Get pulse on which qubit."
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid ":py:obj:`delay <pyQCat.pulse.PulseComponent.delay>`\\"
msgstr ""

#: of pyQCat.pulse.PulseComponent.delay:1
#: pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid "Get pulse delay."
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid ":py:obj:`envelop <pyQCat.pulse.PulseComponent.envelop>`\\"
msgstr ""

#: of pyQCat.pulse.PulseComponent.envelop:1
#: pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid "Get the envelop of the waveform."
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid ":py:obj:`id <pyQCat.pulse.PulseComponent.id>`\\"
msgstr ""

#: of pyQCat.pulse.PulseComponent.id:1
#: pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid "Unique identifier for this pulse."
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid ":py:obj:`parameters <pyQCat.pulse.PulseComponent.parameters>`\\"
msgstr ""

#: of pyQCat.pulse.PulseComponent.parameters:1
#: pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid "Return a dictionary containing the pulse's parameters."
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid ":py:obj:`pulse <pyQCat.pulse.PulseComponent.pulse>`\\"
msgstr ""

#: of pyQCat.pulse.PulseComponent.pulse:1
#: pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid "Data sequence for this pulse."
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid ":py:obj:`raw_pulse <pyQCat.pulse.PulseComponent.raw_pulse>`\\"
msgstr ""

#: of pyQCat.pulse.PulseComponent.raw_pulse:1
#: pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid "Get uncorrected waveform raw data."
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid ":py:obj:`sweep <pyQCat.pulse.PulseComponent.sweep>`\\"
msgstr ""

#: of pyQCat.pulse.PulseComponent.sweep:1
#: pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid "Get description of pulse sweeped parameters."
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid ":py:obj:`type <pyQCat.pulse.PulseComponent.type>`\\"
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1
#: pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid "Get pulse type."
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
msgid ":py:obj:`width <pyQCat.pulse.PulseComponent.width>`\\"
msgstr ""

#: of pyQCat.pulse.PulseComponent.type:1:<autosummary>:1
#: pyQCat.pulse.PulseComponent.width:1
msgid "Return the time unit of the pulse."
msgstr ""

#: of pyQCat.pulse.PulseComponent.attach pyQCat.pulse.PulseComponent.bit
#: pyQCat.pulse.PulseComponent.delay pyQCat.pulse.PulseComponent.envelop
#: pyQCat.pulse.PulseComponent.id pyQCat.pulse.PulseComponent.parameters
#: pyQCat.pulse.PulseComponent.pulse pyQCat.pulse.PulseComponent.raw_pulse
#: pyQCat.pulse.PulseComponent.sweep pyQCat.pulse.PulseComponent.type
#: pyQCat.pulse.PulseComponent.width
#: pyQCat.pulse.base_pulse.PulseComponent.get_pulse
#: pyQCat.pulse.base_pulse.PulseComponent.validate_parameters
msgid "Return type"
msgstr ""

#: of pyQCat.pulse.PulseComponent.bit:3 pyQCat.pulse.PulseComponent.sweep:3
#: pyQCat.pulse.PulseComponent.type:3
msgid ":py:class:`str`"
msgstr ""

#: of pyQCat.pulse.PulseComponent.delay:3 pyQCat.pulse.PulseComponent.width:3
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.pulse.PulseComponent.id:3
msgid ":py:class:`int`"
msgstr ""

#: of pyQCat.pulse.PulseComponent.attach:3
msgid ":py:class:`bool`"
msgstr ""

#: of pyQCat.pulse.PulseComponent.envelop:3 pyQCat.pulse.PulseComponent.pulse:3
#: pyQCat.pulse.PulseComponent.raw_pulse:3
#: pyQCat.pulse.base_pulse.PulseComponent.get_pulse:5
msgid ":py:class:`~numpy.ndarray`"
msgstr ""

#: of pyQCat.pulse.PulseComponent.parameters:3
msgid ":py:class:`~typing.Dict`\\[:py:class:`str`, :py:data:`~typing.Any`]"
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent.validate_parameters:1
msgid ""
"Validate parameters. :raises PulseError: If the parameters passed are not"
" valid."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent.validate_parameters:5
msgid ":py:obj:`None`"
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent.correct_ac_crosstalk
#: pyQCat.pulse.base_pulse.PulseComponent.correct_distortion
#: pyQCat.pulse.base_pulse.PulseComponent.plot
msgid "Parameters"
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent.plot:4
msgid "picture path and name."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent.plot:6
msgid "means use points or time."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent.correct_delay:1
msgid ""
"Correct delay time caused by electronics device. Support 'XY' and 'Z' "
"pulse."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent.correct_distortion:1
msgid "Correct distortion caused by electronics device. Only support 'Z' pulse."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent.correct_distortion:4
msgid "Flag to plot result."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent.correct_compensate:1
msgid "Correct compensate caused by electronics device. Only support 'Z' pulse."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent.correct_ac_crosstalk:1
msgid "Correct crosstalk caused by electronics device. Only support 'Z' pulse."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseComponent.correct_ac_crosstalk:5
msgid "The pulse which after ac crosstalk correction."
msgstr ""

#~ msgid "Bases: :py:class:`~abc.ABC`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.pulse.PulseComponent.__init__>`\\ "
#~ "\\(time\\, type\\_\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_ac_crosstalk "
#~ "<pyQCat.pulse.PulseComponent.correct_ac_crosstalk>`\\ "
#~ "\\(after\\_ac\\_corsstalk\\_pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_compensate "
#~ "<pyQCat.pulse.PulseComponent.correct_compensate>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_delay "
#~ "<pyQCat.pulse.PulseComponent.correct_delay>`\\ \\(delay\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_distortion "
#~ "<pyQCat.pulse.PulseComponent.correct_distortion>`\\ "
#~ "\\(\\[plot\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_pulse "
#~ "<pyQCat.pulse.PulseComponent.correct_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`get_pulse <pyQCat.pulse.PulseComponent.get_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_raw_pulse "
#~ "<pyQCat.pulse.PulseComponent.get_raw_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot <pyQCat.pulse.PulseComponent.plot>`\\ "
#~ "\\(file\\_name\\[\\, use\\_points\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`validate_parameters "
#~ "<pyQCat.pulse.PulseComponent.validate_parameters>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`attach <pyQCat.pulse.PulseComponent.attach>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`bit <pyQCat.pulse.PulseComponent.bit>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`delay <pyQCat.pulse.PulseComponent.delay>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`envelop <pyQCat.pulse.PulseComponent.envelop>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`id <pyQCat.pulse.PulseComponent.id>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`parameters <pyQCat.pulse.PulseComponent.parameters>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`pulse <pyQCat.pulse.PulseComponent.pulse>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`raw_pulse <pyQCat.pulse.PulseComponent.raw_pulse>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`sweep <pyQCat.pulse.PulseComponent.sweep>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`type <pyQCat.pulse.PulseComponent.type>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`width <pyQCat.pulse.PulseComponent.width>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`abc.ABC`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.pulse.PulseComponent.__init__>`\\ "
#~ "\\(time\\, type\\_\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_ac_crosstalk "
#~ "<pyQCat.pulse.PulseComponent.correct_ac_crosstalk>`\\ "
#~ "\\(after\\_ac\\_corsstalk\\_pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_compensate "
#~ "<pyQCat.pulse.PulseComponent.correct_compensate>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_delay "
#~ "<pyQCat.pulse.PulseComponent.correct_delay>`\\ \\(delay\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_distortion "
#~ "<pyQCat.pulse.PulseComponent.correct_distortion>`\\ "
#~ "\\(\\[plot\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_pulse "
#~ "<pyQCat.pulse.PulseComponent.correct_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`get_pulse <pyQCat.pulse.PulseComponent.get_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_raw_pulse "
#~ "<pyQCat.pulse.PulseComponent.get_raw_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot <pyQCat.pulse.PulseComponent.plot>`\\ "
#~ "\\(file\\_name\\[\\, use\\_points\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`validate_parameters "
#~ "<pyQCat.pulse.PulseComponent.validate_parameters>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`attach <pyQCat.pulse.PulseComponent.attach>`\\"
#~ msgstr ""

#~ msgid ":obj:`bit <pyQCat.pulse.PulseComponent.bit>`\\"
#~ msgstr ""

#~ msgid ":obj:`delay <pyQCat.pulse.PulseComponent.delay>`\\"
#~ msgstr ""

#~ msgid ":obj:`envelop <pyQCat.pulse.PulseComponent.envelop>`\\"
#~ msgstr ""

#~ msgid ":obj:`id <pyQCat.pulse.PulseComponent.id>`\\"
#~ msgstr ""

#~ msgid ":obj:`parameters <pyQCat.pulse.PulseComponent.parameters>`\\"
#~ msgstr ""

#~ msgid ":obj:`pulse <pyQCat.pulse.PulseComponent.pulse>`\\"
#~ msgstr ""

#~ msgid ":obj:`raw_pulse <pyQCat.pulse.PulseComponent.raw_pulse>`\\"
#~ msgstr ""

#~ msgid ":obj:`sweep <pyQCat.pulse.PulseComponent.sweep>`\\"
#~ msgstr ""

#~ msgid ":obj:`type <pyQCat.pulse.PulseComponent.type>`\\"
#~ msgstr ""

#~ msgid ":obj:`width <pyQCat.pulse.PulseComponent.width>`\\"
#~ msgstr ""

