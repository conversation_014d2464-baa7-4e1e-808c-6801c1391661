# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/06/12
# __author:       <PERSON> Fang


from ....analysis.library.unitary_tomography_analysis import (
    UnitaryTomographyAnalysis,
)
from ....gate import GateBucket, GateCollection
from ....log import pyqlog
from ....structures import MetaData, Options
from ....types import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment
from ..two_qubit_gate.swap_once import (
    validate_qubit_pair_cz_std,
    validate_two_qubit_exp_read_options,
)


class UnitaryTomography(TopExperiment):
    """Unitary Tomography, QubitPair experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Define experiment options."""
        options = super()._default_experiment_options()
        options.set_validator("goal_gate", GateCollection.gate_infos())
        options.set_validator("times", int)

        options.goal_gate = "CZ"
        options.times = 1

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Define run options."""
        options = super()._default_run_options()

        options.support_context = [StandardContext.CGC]
        options.x_data = []
        options.analysis_class = UnitaryTomographyAnalysis

        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []

        options.gate_bucket = GateBucket()
        options.map_gates = {}
        options.initial_states = [
            ["X/2", "I"],
            ["I", "X/2"],
            ["I", "X/2"],
            ["X/2", "I"],
            ["X", "X/2"],
            ["X", "X/2"],
        ]
        options.tomo_names = ["qh", "qh", "ql", "ql", "qh", "ql"]
        options.tomo_gates = ["X/2", "Y/2"]
        # options.tomo_gates = ["-Y/2", "X/2"]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Define analysis options."""
        options = super()._default_analysis_options()

        options.tomo_names = ["qh", "qh", "ql", "ql", "qh", "ql"]
        options.tomo_gates = ["X/2", "Y/2"]
        options.t_gate = 40.0  # goal gate width
        options.ql_freq = 4250.0
        options.qh_freq = 4550.0

        options.raw_data_format = "plot"

        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "Gate": self.experiment_options.goal_gate,
            "K": self.experiment_options.times,
        }
        return metadata

    def _check_options(self):
        """Check option."""
        super()._check_options()

        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)

        # bind default gate to qubit/coupler
        gate_bucket: "GateBucket" = self.run_options.gate_bucket
        for qubit in self.qubits:
            gate_bucket.bind_single_gates(qubit)
            self.qubit_pair.set_cz_value(qubit.name, "phase", 0.0, label="cz")

        units = []
        units.extend(self.qubits)
        units.extend(self.couplers)
        gate_bucket.bind_cz_gates(self.qubit_pair, units)

        # create qubit/coupler loop gates.
        self._generate_gates()

        # set some analysis options.
        goal_gate = self.experiment_options.goal_gate
        qh_obj = self.run_options.qh
        ql_obj = self.run_options.ql
        gate_bucket: "GateBucket" = self.run_options.gate_bucket
        t_pulse = gate_bucket.get_z_pulse(ql_obj, goal_gate or "CZ")
        t_gate = t_pulse.width

        self.run_options.measure_qubits = [qh_obj, ql_obj]
        self.set_analysis_options(
            tomo_names=self.run_options.tomo_names,
            tomo_gates=self.run_options.tomo_gates,
            t_gate=t_gate,
            ql_freq=ql_obj.drive_freq,
            qh_freq=qh_obj.drive_freq,
        )

    def _generate_gates(self):
        """Generate qubit/coupler gates."""
        goal_gate = self.experiment_options.goal_gate
        times = self.experiment_options.times
        initial_states = self.run_options.initial_states
        tomo_names = self.run_options.tomo_names
        tomo_gates = self.run_options.tomo_gates

        qh_name = self.qubit_pair.qh
        ql_name = self.qubit_pair.ql
        parking_bits = self.qubit_pair.parking_bits

        units = []
        units.extend(self.qubits)
        units.extend(self.couplers)
        map_gates = {qc_obj.name: [] for qc_obj in units}
        for states, tm_name in zip(initial_states, tomo_names):
            qh_state, ql_state = states
            for tm_gate in tomo_gates:
                for qc_obj in units:
                    if qc_obj.name == qh_name:
                        e_gate = tm_gate if tm_name == "qh" else "I"
                        gates = [qh_state, goal_gate, e_gate]
                    elif qc_obj.name == ql_name:
                        e_gate = tm_gate if tm_name == "ql" else "I"
                        gates = [ql_state, goal_gate, e_gate]
                    elif qc_obj.name in parking_bits:
                        gates = ["I", goal_gate, "I"]
                    else:
                        gates = []
                    if gates:
                        map_gates[qc_obj.name].append(gates)

        final_map_gates = {k: v * times for k, v in map_gates.items()}
        loop = times * len(initial_states) * len(tomo_gates)
        x_data = list(range(loop))
        pyqlog.info(f"goal_gate: {goal_gate}, times: {times}, loop: {loop}")
        self.set_run_options(x_data=x_data, map_gates=final_map_gates)

    @staticmethod
    def set_xy_pulses(self):
        """Set xy pulses."""
        gate_bucket: "GateBucket" = self.run_options.gate_bucket
        map_gates = self.run_options.map_gates

        for qubit in self.qubits:
            gates_list = map_gates.get(qubit.name, [])
            if gates_list:
                pulse_list = []
                for gates in gates_list:
                    pulse_obj = gate_bucket.get_xy_pulse(qubit, gates)
                    pulse_list.append(pulse_obj)
                self.play_pulse("XY", qubit, pulse_list)

    @staticmethod
    def set_z_pulses(self):
        """Set z pulses."""
        gate_bucket: "GateBucket" = self.run_options.gate_bucket
        map_gates = self.run_options.map_gates

        units = []
        units.extend(self.qubits)
        units.extend(self.couplers)
        for qubit in units:
            gates_list = map_gates.get(qubit.name, [])
            if gates_list:
                pulse_list = []
                for gates in gates_list:
                    pulse_obj = gate_bucket.get_z_pulse(qubit, gates)
                    pulse_list.append(pulse_obj)
                self.play_pulse("Z", qubit, pulse_list)

    def _alone_save_result(self):
        """Alone save some special result."""
        if self.analysis:
            u_labels = ["u22", "u21", "u11", "u12", "u12_excited", "u22_excited"]

            result_name = self.analysis_options.result_name
            unitary_list = self.analysis.options.unitary_list
            f_sim_parameter = self.analysis.options.f_sim_parameter
            t_matrix = self.analysis.results.t_matrix.value

            msg = f"\nunitary_list\n"
            for idx, once_u in enumerate(unitary_list):
                msg += f"{u_labels[idx]}: {once_u}\n"
            msg += "\nf_sim_parameter:\n"
            for key, value in f_sim_parameter.items():
                msg += f"{key}: {value}\n"
            msg += f"\ntarget matrix:\n{t_matrix}"

            extra_info = f"{self}({result_name}-result)"
            self.file.save_text(msg, name=extra_info, prefix=".txt")
