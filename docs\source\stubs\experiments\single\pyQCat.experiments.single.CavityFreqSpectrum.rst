﻿pyQCat.experiments.single.CavityFreqSpectrum
============================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: CavityFreqSpectrum

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CavityFreqSpectrum.__init__
      ~CavityFreqSpectrum.acquire_pulse
      ~CavityFreqSpectrum.cal_fidelity
      ~CavityFreqSpectrum.experiment_info
      ~CavityFreqSpectrum.from_experiment_context
      ~CavityFreqSpectrum.get_qubit_str
      ~CavityFreqSpectrum.jupyter_schedule
      ~CavityFreqSpectrum.options_table
      ~CavityFreqSpectrum.play_pulse
      ~CavityFreqSpectrum.plot_schedule
      ~CavityFreqSpectrum.run
      ~CavityFreqSpectrum.set_analysis_options
      ~CavityFreqSpectrum.set_experiment_options
      ~CavityFreqSpectrum.set_multiple_IF
      ~CavityFreqSpectrum.set_multiple_index
      ~CavityFreqSpectrum.set_parent_file
      ~CavityFreqSpectrum.set_run_options
      ~CavityFreqSpectrum.set_sweep_order
      ~CavityFreqSpectrum.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CavityFreqSpectrum.analysis
      ~CavityFreqSpectrum.analysis_options
      ~CavityFreqSpectrum.experiment_options
      ~CavityFreqSpectrum.run_options
   
   