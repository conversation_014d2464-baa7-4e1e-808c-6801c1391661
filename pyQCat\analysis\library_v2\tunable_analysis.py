# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/30
# __author:       ssfang


"""
Tunable modulation spectrum analysis.
"""

from typing import List, Union

import matplotlib.pyplot as plt
import numpy as np

from ...log import pyqlog
from ...structures import Options
from ...types import QualityDescribe
from ..algorithms import get_coupler_dc_point, get_qubit_dc_point
from ..algorithms.guess import fourier_cycle_guess
from ..curve_fit_analysis import CurveFitAnalysis
from ..fit.fit_models import amp2cavity_freq_formula
from ..specification import (CurveAnalysisData, FitModel, FitOptions,
                             ParameterRepr)


class TunableAnalysisV2(CurveFitAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.fit_model = FitModel(fit_func=amp2cavity_freq_formula)
        options.p0 = {"fq_max": 6, "fc": 0.24, "d": 0.01, "g": 0.08}
        options.curve_fit_extra.update({"ftol": 1.49012e-8, "xtol": 1.49012e-8})

        options.data_key = ["fc"]
        options.x_label = "AcBias/AC/DC [V]"
        options.y_label = [
            "Frequency [GHz]",
            "Q",
            "Amp - Fc [MHz]",
            "Phase - Fc [MHz]",
        ]
        options.pcolormesh_options = {
            "shading": "auto",
            "cmap": plt.cm.get_cmap("rainbow"),
        }

        options.tackle_type = None
        options.diff_threshold = 0.1

        options.result_parameters = [
            ParameterRepr(name="dc_min", repr="dc_min", unit="v"),
            ParameterRepr(name="dc_max", repr="dc_max", unit="v"),
            ParameterRepr(name="fc_min", repr="fc_min", unit="MHz"),
            ParameterRepr(name="fc_max", repr="fc_max", unit="MHz"),
            ParameterRepr(name="Ql_min", repr="ql_min", unit=""),
            ParameterRepr(name="Ql_max", repr="ql_max", unit=""),
            ParameterRepr(name="tunable", repr="tunable", unit=""),
            ParameterRepr(name="r2", repr="r2", unit=""),
        ]

        options.data_mode = "amp_phase"

        # max/min point threshold. For example, the absolute value of DC working point
        # is less than 5, and the absolute value of ac bias working point is less than 0.5
        options.threshold = (-0.5, 0.5)

        return options

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Guess initial fit parameters.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        x = data.x
        y = data.y

        fr0 = min(y)
        M = 1 / fourier_cycle_guess(x, y)
        offset = x[np.argmax(y)]

        fit_opt.p0.set_if_empty(fr0=fr0, M=M, offset=offset)
        return fit_opt

    def _extract_result(self):
        """Extract DC Max Min from fitted data.

        Args:
            data_key (str): The basis for selecting data.
        """
        data_key = self.experiment_data.metadata.process_meta.get("best_data_key")
        analysis_data = self.analysis_datas[data_key]
        x = analysis_data.x
        y = analysis_data.y
        ql = self.experiment_data.y_data.get("ql")

        if self.options.tackle_type.capitalize() == "Qubit":
            M = analysis_data.fit_data.fitval("M")
            offset = analysis_data.fit_data.fitval("offset")
            if M == float("inf"):
                min_point, max_point = 0, 0
            else:
                try:
                    min_point, max_point = get_qubit_dc_point(
                        offset, M, self.options.threshold
                    )

                    # bug: offset value maybe a min dc value, but
                    # get_qubit_dc_point default see it as a max dc value
                    min_index = (np.abs(x - min_point)).argmin()
                    max_index = (np.abs(x - max_point)).argmin()
                    if y[min_index] > y[max_index]:
                        min_point, max_point = max_point, min_point
                except Exception as err:
                    pyqlog.warning(f"Get qubit min max point error: {err}")
                    min_point, max_point = 0, 0

            fr_max, fr_min = max(y), min(y)
            ql_min = ql[np.argmin(y)]
            ql_max = ql[np.argmax(y)]

            self.results.Ql_min.value = round(ql_min, 3)
            self.results.Ql_max.value = round(ql_max, 3)
            self.results.fc_min.value = round(fr_min * 1e3, 3)
            self.results.fc_max.value = round(fr_max * 1e3, 3)

            diff = abs(fr_max - fr_min) * 1e3
            if diff >= self.options.diff_threshold:
                self.results.tunable.value = True
            else:
                self.results.tunable.value = False
                self.quality.descriptor = QualityDescribe.bad

        elif self.options.tackle_type.capitalize() == "Coupler":
            try:
                min_point, max_point = get_coupler_dc_point(x, y)
            except Exception as err:
                pyqlog.warning(f"Get coupler min max point error: {err}")
                min_point, max_point = 0, 0
            fc_min = y[(np.abs(x - min_point)).argmin()]
            fc_max = y[(np.abs(x - max_point)).argmin()]
            ql_min = ql[(np.abs(x - min_point)).argmin()]
            ql_max = ql[(np.abs(x - max_point)).argmin()]

            self.results.Ql_min.value = round(ql_min, 3)
            self.results.Ql_max.value = round(ql_max, 3)
            self.results.fc_min.value = round(fc_min * 1e3, 3)
            self.results.fc_max.value = round(fc_max * 1e3, 3)
            # todo, Coupler work judge method
        else:
            min_point, max_point = 0, 0

        self.results.dc_min.value = round(min_point, 3)
        self.results.dc_max.value = round(max_point, 3)

        if abs(min_point) > 0.5 or abs(max_point) > 0.5:
            pyqlog.warning("Point outside limit, set quality bad!")
            self._quality.descriptor = QualityDescribe.bad

        self.results.r2.value = self._quality.value

    def _visualization(self):
        """Tunable plot depth, amp and phase."""
        super()._visualization()
        if self.options.tackle_type != "rough" and self.results.dc_max.value:
            min_data = (self.results.dc_min.value, self.results.fc_min.value * 1e-3)
            max_data = (self.results.dc_max.value, self.results.fc_max.value * 1e-3)
            text_pos = [min_data, max_data]
            text_rp = [f"min{str(min_data)}", f"max{str(max_data)}"]
            self.drawer.set_options(
                text_pos=text_pos, text_rp=text_rp, text_key="result"
            )
            self.drawer.draw_text(
                ax_index=0,
                fontdict={
                    "fontsize": 16,
                    "color": "blue",
                    "family": "monospace",
                    "weight": "normal",
                },
            )
