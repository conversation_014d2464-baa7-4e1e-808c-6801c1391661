# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.qst_mle.rst:2
msgid "pyQCat.analysis.algorithms.qst\\_mle"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qst_mle:1
msgid "State tomography with maximum-likelihood estimation."
msgstr "使用最大似然估计进行状态层析计算"

#: of pyQCat.analysis.algorithms.tomography.qst_mle
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qst_mle:3
msgid ""
"a 2D array of measured probabilities. The first index indicates which "
"operation from Us was applied, while the second index tells which "
"measurement result this was (e.g. 000, 001, etc.)."
msgstr ""
"测量概率的二维数组。 第一个索引指示应用了来自 Us 的哪个操作，而第二个索引"
"指示这是哪个测量结果（例如 000、001 等）。"

#: of pyQCat.analysis.algorithms.tomography.qst_mle:6
msgid "the unitary operations that were applied to the system before measuring."
msgstr "在测量之前应用于系统的酉运算。"

#: of pyQCat.analysis.algorithms.tomography.qst_mle:8
msgid ""
"a 'fidelity' matrix, relating the actual or 'intrinsic' probabilities to "
"the measured probabilities, via pms = dot(F, pis). If no fidelity matrix "
"is given, the identity will be used."
msgstr ""
"一个“保真度”矩阵，通过 pms = dot(F, pis) 将实际或“内在”概率与测量概率相关联。"
" 如果没有给出保真度矩阵，则使用恒等式。"

#: of pyQCat.analysis.algorithms.tomography.qst_mle:11
msgid "an initial guess for the density matrix, e.g. from linear tomography."
msgstr "密度矩阵的初始猜测，例如 从线性断层扫描。"

#~ msgid "State tomography with maximum-likelihood estimation. 使用最大似然估计进行量子状态层析"
#~ msgstr ""

#~ msgid ""
#~ "a 2D array of measured probabilities."
#~ " The first index indicates which "
#~ "operation from Us was applied, while "
#~ "the second index tells which measurement"
#~ " result this was (e.g. 000, 001, "
#~ "etc.). 测量结果的二维数组, 每个Us操作对应一个测量结果."
#~ msgstr ""

#~ msgid ""
#~ "the unitary operations that were applied"
#~ " to the system before measuring. "
#~ "在测量之前应用于系统的单一Us操作, 如[I, X/2, Y/2]."
#~ msgstr ""

#~ msgid ""
#~ "a 'fidelity' matrix, relating the actual"
#~ " or 'intrinsic' probabilities to the "
#~ "measured probabilities, via pms = dot(F,"
#~ " pis). If no fidelity matrix is "
#~ "given, the identity will be used. "
#~ "一个“保真度”矩阵, 通过 pms = dot(F, pis) "
#~ "将实际或实际概率与测量的概率相关联, 如果未给出保真度矩阵, 则将使用单位矩阵保持测试的概率进行计算。"
#~ msgstr ""

#~ msgid ""
#~ "an initial guess for the density "
#~ "matrix, e.g. from linear tomography. "
#~ "初始密度矩阵, 默认为空, 如线性层析扫描"
#~ msgstr ""

