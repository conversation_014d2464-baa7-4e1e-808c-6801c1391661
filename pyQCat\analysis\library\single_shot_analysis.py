# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/14
# __author:       ssfang

"""
SingleShot Analysis.
"""

from typing import Dict

import numpy as np
import scipy
from scipy.fft import fft, fftfreq
from scipy.signal import find_peaks

from ...structures import Options, QDict
from ..algorithms.iqprobability import IQdiscriminator
from ..quality.single_shot_quality import SingleShotQuality
from ..specification import SingleShotAnalysisData
from ..standard_curve_analysis import StandardCurveAnalysis
from ..top_analysis import TopAnalysis
from ..visualization import CurveDrawer


class SingleShotAnalysis(TopAnalysis):
    @property
    def drawer(self) -> CurveDrawer:
        """A short-cut for curve drawer instance."""
        return self._options.curve_drawer

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Analysis Options:
            level_str (str): Mark energy level string, `01`, `02`,`012`
            n_clusters (int): Preset clustering number.
            method (str): Clustering algorithm type. `GMM` or `KMeans`
            n_multiple (float): Calculate cluster radius,
                                multiple of standard deviation.
                                Default set `3.0`.
            is_plot (bool): Set ``True`` to create figure for fit result.
            quality_bounds (Tuple): Boundary of success conditions.
            result_parameters (List): result data key list.
        """
        options = super()._default_options()

        options.level_str = "01"
        options.n_clusters = 2
        options.method = "GMM"
        options.n_multiple = 3.0
        options.set_proportion = False
        options.heat_stimulate = False
        options.plot_circle = False

        options.curve_drawer = CurveDrawer()

        # Quality options. SingleShot Success Conditions:
        # k_recommend = 2, F0 >= 0.85, F1 >= 0.7, outlier <= 0.011
        options.quality_bounds = (2, 0.85, 0.7, 0.011)

        # Result options.
        options.result_parameters = []

        # When calculating the fidelity of the classifier, should outlier data be removed
        options.remove_outlier = False
        
        # Check the value of k
        options.check_k = False

        return options

    def _create_analysis_data(self) -> QDict:
        """Create Analysis data provided for detailed analyze.
            {
                'q0': data0, # np.array(I0, Q0, I1, Q1)
                'q1': data1,
                'c0': data2
                ...
            }
        Returns:
            A QDict object, key represents data type and value is
        """
        analysis_data_dict = QDict()

        data_key = list(
            self.options.get("data_key", None) or self._experiment_data.y_data.keys()
        )
        for key in data_key:
            source_data = self.experiment_data.y_data[key]
            analysis_data_dict[key] = SingleShotAnalysisData(source_data=source_data)

        return analysis_data_dict

    def _run_training(self):
        """Training analysis_data source_data."""

        for data_key, analysis_data in self.analysis_datas.items():
            data = analysis_data.source_data

            I_list = []
            Q_list = []

            for i in range(self.options.n_clusters):
                I_list.append(data[:, i * 2])
                Q_list.append(data[:, i * 2 + 1])

            dcm = IQdiscriminator(
                I_list,
                Q_list,
                n_clusters=self.options.n_clusters,
                method=self.options.method,
                name=data_key,
                level_str=self.options.level_str,
                set_proportion=self.options.set_proportion,
                heat_stimulate=self.options.heat_stimulate,
                remove_outlier=self.options.remove_outlier,
            )
            dcm.train(n_multiple=self.options.n_multiple)
            analysis_data.discriminator = dcm

    def _evaluate_quality(self):
        """Parse analysis data Classification Quality."""
        quality_dict = {}
        for data_key, analysis_data in self.analysis_datas.items():
            dcm = analysis_data.discriminator
            k_recommend = dcm.k_recommend
            fidelity = dcm.fidelity
            outlier = dcm.outlier
            proportion = dcm.proportion
            dcm_quality = SingleShotQuality(
                k_recommend,
                *fidelity,
                outlier=outlier,
                quality_bounds=self.options.quality_bounds,
                proportion=proportion,
            )
            dcm_quality.evaluate(self.options.check_k)
            analysis_data.quality = dcm_quality
            quality_dict.update({data_key: dcm_quality})

        self._quality = dcm_quality

    def _extract_result(self):
        """Extract result."""
        for data_key, result in self.results.items():
            if data_key in self.analysis_datas:
                analysis_data = self.analysis_datas[data_key]
            else:
                analysis_data = list(self.analysis_datas.values())[0]
            result.value = analysis_data.discriminator

    def _visualization(self):
        """Visualization, define plot."""
        metadata = self.experiment_data.metadata
        save_location = metadata.save_location
        save_mark = metadata.draw_meta.get("save_mark")
        readout_freq_dict = metadata.draw_meta.get("readout_freq")
        readout_power_dict = metadata.draw_meta.get("readout_power")

        self.options.curve_drawer._figure = []
        for data_key, analysis_data in self.analysis_datas.items():
            if isinstance(readout_freq_dict, Dict):
                readout_freq = readout_freq_dict.get(data_key)
            else:
                readout_freq = None
            if isinstance(readout_power_dict, Dict):
                readout_power = readout_power_dict.get(data_key)
            else:
                readout_power = None

            dcm = analysis_data.discriminator
            dcm.plot_circle = self.options.plot_circle
            fig = dcm.plot(
                dirs=save_location,
                name=save_mark,
                is_save=False,
                power=readout_power,
                freq=readout_freq,
                figsize=self.options.figsize,
            )
            self.options.curve_drawer._figure.append(fig)

    def run_analysis(self):
        """Run analysis on experiment data."""
        # Prepare
        self._analysis_data_dict = self._create_analysis_data()
        self._results = self._create_analysis_result()

        # train
        self._run_training()

        # parse quality
        self._evaluate_quality()

        # update result data
        self._extract_result()

        # plot I, Q scatter
        if self.options.is_plot is True:
            self._visualization()


class IQTrackSingleShotAnalysis(StandardCurveAnalysis):
    def _data_processing(self):
        exp_data = self.experiment_data

        track = exp_data.y_data.pop("track")
        middle = track.shape[0] // 2
        level_str = exp_data.metadata.draw_meta.get("level_str")
        offset = self.experiment_data.metadata.draw_meta.get("offset")

        y_data = {}
        for idx, label in enumerate(level_str):
            y_data[f"I-{label}-mean"] = track[:, idx * 2][:middle]
            y_data[f"I-{label}-std"] = track[:, idx * 2][middle:]
            y_data[f"Q-{label}-mean"] = track[:, idx * 2 + 1][:middle]
            y_data[f"Q-{label}-std"] = track[:, idx * 2 + 1][middle:]

        exp_data._x_data = np.arange(middle) / 3.2 + offset
        exp_data.y_data.update(y_data)

    def _initialize_canvas(self):
        # Set Canvas Options
        self.drawer.set_options(
            subplots=(3, 4),
            xlabel=[
                "Sample Time (ns)",
                "Sample Time (ns)",
                "Half Freq (MHz)",
                "Half Freq (MHz)",
                "Sample Time (ns)",
                "Sample Time (ns)",
                "Half Freq (MHz)",
                "Half Freq (MHz)",
                "Sample Time (ns)",
                "Sample Time (ns)",
            ],
            ylabel=[
                "I",
                "Q",
                "Power [db]",
                "Power [db]",
                "I",
                "Q",
                "Power [db]",
                "Power [db]",
                "I",
                "Q",
            ],
            sub_title=[
                "Track-I-Origin",
                "Track-Q-Origin",
                "Origin-I-FFT",
                "Origin-Q-FFT",
                "Track-I-filt",
                "Track-Q-filt",
                "Filt-I-FFT",
                "Filt-Q-FFT",
                "Track-I",
                "Track-Q",
            ],
            figsize=(20, 15),
        )

        # CurveDrawer initialize canvas
        self.drawer.initialize_canvas()

    def _visualization(self) -> None:
        """Draw all data, including raw data and fit data and so on.
        Some composite experiments may need to override this function.
        """
        # Set plot title.
        self.drawer.set_options(title=self._description())

        # plot raw data.
        level_str = self.experiment_data.metadata.draw_meta.get("level_str")
        y_data = self.experiment_data.y_data
        x_data = self.experiment_data.x_data
        colors = ["blue", "red"]
        eb, ea = scipy.signal.butter(8, 2 * 0.2 / 3.2, "lowpass")

        for idx, label in enumerate(level_str):
            mean_i = y_data[f"I-{label}-mean"]
            std_i = y_data[f"I-{label}-std"]
            mean_q = y_data[f"Q-{label}-mean"]
            std_q = y_data[f"Q-{label}-std"]

            stft_mean_i = fft_analysis2(mean_i)
            stft_mean_q = fft_analysis2(mean_q)

            filter_envelope_i = scipy.signal.filtfilt(eb, ea, mean_i)
            filter_envelope_q = scipy.signal.filtfilt(eb, ea, mean_q)

            stft_filter_i = fft_analysis2(filter_envelope_i)
            stft_filter_q = fft_analysis2(filter_envelope_q)

            # filter_envelope_i = self._filter(mean_i)
            # filter_envelope_q = self._filter(mean_q)
            self.experiment_data.y_data[f"I-{label}-filt"] = filter_envelope_i
            self.experiment_data.y_data[f"Q-{label}-filt"] = filter_envelope_q

            self.drawer.draw_plot_point(
                x_data=x_data,
                y_data=mean_i,
                ax_index=0,
                color=colors[idx],
                label=f"|{label}>",
            )
            self.drawer.draw_fill_between(
                x_data=x_data,
                down_data=mean_i - std_i,
                up_data=mean_i + std_i,
                ax_index=0,
                color=colors[idx],
                alpha=0.2,
            )
            self.drawer.draw_plot_point(
                x_data=x_data,
                y_data=mean_q,
                ax_index=1,
                color=colors[idx],
                label=f"|{label}>",
            )
            self.drawer.draw_fill_between(
                x_data=x_data,
                down_data=mean_q - std_q,
                up_data=mean_q + std_q,
                ax_index=1,
                color=colors[idx],
                alpha=0.2,
            )
            self.drawer.draw_semilogx_point(
                x_data=stft_mean_i[0],
                y_data=stft_mean_i[2],
                color=colors[idx],
                label=f"|{label}> {stft_mean_i[-2]}",
                ax_index=2,
            )
            self.drawer.draw_semilogx_point(
                x_data=stft_mean_q[0],
                y_data=stft_mean_q[2],
                color=colors[idx],
                label=f"|{label}> {stft_mean_i[-2]}",
                ax_index=3,
            )
            self.drawer.draw_plot_point(
                x_data=x_data,
                y_data=filter_envelope_i,
                ax_index=4,
                color=colors[idx],
                label=f"|{label}>",
            )
            self.drawer.draw_fill_between(
                x_data=x_data,
                down_data=mean_i - std_i,
                up_data=mean_i + std_i,
                ax_index=4,
                color=colors[idx],
                alpha=0.2,
            )
            self.drawer.draw_plot_point(
                x_data=x_data,
                y_data=filter_envelope_q,
                ax_index=5,
                color=colors[idx],
                label=f"|{label}>",
            )
            self.drawer.draw_fill_between(
                x_data=x_data,
                down_data=mean_q - std_q,
                up_data=mean_q + std_q,
                ax_index=5,
                color=colors[idx],
                alpha=0.2,
            )
            self.drawer.draw_semilogx_point(
                x_data=stft_filter_i[0],
                y_data=stft_filter_i[2],
                color=colors[idx],
                label=f"|{label}> {stft_filter_q[-2]}",
                ax_index=6,
            )
            self.drawer.draw_semilogx_point(
                x_data=stft_filter_q[0],
                y_data=stft_filter_q[2],
                color=colors[idx],
                label=f"|{label}> {stft_filter_q[-2]}",
                ax_index=7,
            )
            self.drawer.draw_plot_point(
                x_data=x_data,
                y_data=filter_envelope_i,
                ax_index=8,
                color=colors[idx],
                label=f"|{label}>",
            )
            self.drawer.draw_plot_point(
                x_data=x_data,
                y_data=filter_envelope_q,
                ax_index=9,
                color=colors[idx],
                label=f"|{label}>",
            )

    def _format_canvas(self):
        # Finalize plot.
        self.drawer.format_canvas()

    @staticmethod
    def _filter(data, win: int = 8):
        index = 0
        filter_data = np.array([])

        while index + win <= len(data):
            child_data = data[index : index + win]
            filter_data = np.hstack(
                (filter_data, np.full_like(child_data, np.mean(child_data)))
            )
            index += 8

        if index < len(data):
            child_data = data[index:]
            filter_data = np.hstack(
                (filter_data, np.full_like(child_data, np.mean(child_data)))
            )

        return filter_data


def fft_analysis2(y0):
    # 参数设置
    fs = 3.2 * 1e9  # 采样频率 (Hz)
    T = 1.0 / fs  # 采样间隔
    N = len(y0)
    yf = fft(y0)  # FFT结果
    xf = fftfreq(N, T)[: N // 2]  # 频率向量 (单边谱)
    magnitude = 2.0 / N * np.abs(yf[0 : N // 2])  # 幅度谱
    xf = xf * 1e-6  # MHz
    
    
    peaks_idx, _ = find_peaks(
        magnitude,
        height=0.2 * np.max(magnitude), 
        prominence=0.5
    )
    
    peaks_freq = xf[peaks_idx]
    peaks_mag = magnitude[peaks_idx]

    return xf, yf, magnitude, peaks_freq, peaks_mag
