# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/10
# __author:       <PERSON>

"""
Concurrent Calculate resources.
"""

import asyncio
import concurrent.futures
import subprocess
import sys
import time
from concurrent.futures.process import BrokenProcessPool
from multiprocessing import cpu_count

from pyQCat.concurrent.util import CONCURRENT_CACHE, Singleton, c_error_log, c_log
from pyQCat.errors import ConcurrentCRError


async def run_in_executor(executor, fn, *args):
    # running loop is stricter because it only returns the event loop
    # if there is already a running event loop in the current thread,
    # otherwise it raises an error.
    loop = asyncio.get_running_loop()
    try:
        return await loop.run_in_executor(executor, fn, *args)
    except BrokenProcessPool as e:
        c_error_log(f"CR Process Pool Broken, {e}")
        CONCURRENT_CACHE["process_broken"] = True
        raise e
    except Exception:
        import traceback

        raise ConcurrentCRError(str(traceback.format_exc()))


def pipe_commands(commands):
    prev_proc = None
    for cmd in commands:
        if prev_proc:
            curr_proc = subprocess.Popen(
                cmd, stdin=prev_proc.stdout, stdout=subprocess.PIPE
            )
            prev_proc.stdout.close()  # Allow the child process to receive a SIG PIPE if the next command exits.
        else:
            curr_proc = subprocess.Popen(cmd, stdout=subprocess.PIPE)

        prev_proc = curr_proc

    return curr_proc.communicate()[0].decode("utf-8").strip()


def count_python_processes_unix():
    commands = [["ps", "-ef"], ["grep", "python"], ["grep", "-v", "grep"], ["wc", "-l"]]
    output = output = pipe_commands(commands)
    return int(output)


def count_python_processes_windows():
    command = ["tasklist", "/NH", "/FO", "CSV"]
    output = subprocess.check_output(command)
    lines = output.split(b"\n")

    python_count = 0
    for line in lines:
        # if line and re.search(r"^python.exe", line, re.IGNORECASE):
        if line and b"python.exe" in line:
            python_count += 1
    return python_count


def get_cpu_count():
    """
    get computer cpu count.
    """
    try:
        cpu_thread = cpu_count()
        if sys.platform == "win32":
            python_process_count = count_python_processes_windows()
        else:
            python_process_count = count_python_processes_unix()

        real_thread = cpu_thread // (python_process_count // (cpu_thread * 2) + 2)
        if real_thread < 2:
            real_thread = 2
    except Exception:
        real_thread = 6
    return real_thread


def platform_adapter(cr, worker):
    if sys.platform == "win32":
        c_log(
            "Activate parallel computing resource pool on windows operating system..."
        )
        c_log(
            "Depending on the hardware characteristics of the platform, this may take "
            "a while, so please be patient!"
        )
        task_list = []
        for x in range(worker):
            task_list.append(cr.register_job(cpu_count))
        while True:
            for x in task_list:
                if x.done():
                    task_list.remove(x)
            if task_list:
                time.sleep(0.1)
            else:
                break
        c_log("Activation Success!")


def _initializer(
    qaio_type: int = 72,
    transfer_name: str = "",
    link_transfer: bool = True,
    open_s3: bool = False,
):
    """Initialize some global variables for concurrent calculation."""
    from pyQCat.concurrent.util import record_process_id, CONCURRENT_CACHE
    from pyQCat.concurrent.data_client import DataClient
    from pyQCat.qaio_property import QAIO

    CONCURRENT_CACHE["transfer_name"] = transfer_name

    record_process_id()
    QAIO.type = qaio_type

    if link_transfer:
        DataClient()

    if open_s3 is True:
        from pyQCat.tools.s3storage import S3Storage

        S3Storage()


class ConcurrentCR(metaclass=Singleton):
    """
    Concurrent Calculate Resource.
    """

    def __init__(
        self, qaio_type: int = 72, link_transfer: bool = True, open_s3: bool = False
    ) -> None:
        works = int(get_cpu_count())
        transfer_name = CONCURRENT_CACHE["transfer_name"]
        self._concurrent = concurrent.futures.ProcessPoolExecutor(
            max_workers=works,
            initializer=_initializer,
            initargs=(qaio_type, transfer_name, link_transfer, open_s3),
        )
        CONCURRENT_CACHE["concurrent_flag"] = True
        platform_adapter(cr=self, worker=works)
        c_log(f"Parallel computing resources init success, pool num: {works}.")

    def register_job(self, fn, *args):
        """
        register worker to concurrent calculate resource. return asyncio work future.
        """
        return self._concurrent.submit(fn, *args)

    async def run_concurrent_job(self, fn, *args):
        """
        concurrent job with async, need await.
        """
        return await run_in_executor(self._concurrent, fn, *args)

    def map(self, fn, iterables, timeout=None):
        """
        concurrent map.
        """
        return self._concurrent.map(fn, iterables, timeout=timeout)

    def shutdown(self):
        self._concurrent.shutdown()
        if ConcurrentCR in Singleton._instances:
            Singleton._instances.pop(ConcurrentCR)
        CONCURRENT_CACHE["concurrent_flag"] = False
