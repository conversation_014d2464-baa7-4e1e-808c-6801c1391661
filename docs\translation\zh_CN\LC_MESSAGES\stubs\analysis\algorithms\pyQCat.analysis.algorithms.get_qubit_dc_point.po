# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 19:06+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.get_qubit_dc_point.rst:2
msgid "pyQCat.analysis.algorithms.get\\_qubit\\_dc\\_point"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:1
msgid "Get qubit sweet point dc value."
msgstr "获取比特的简并点电压"

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:5
msgid ""
"This method is generally used for bit cavity modulation spectrum "
"experiments, and it mainly implements the following logic:"
msgstr "此方法一般用于比特的腔调制谱实验，它主要实现下面的逻辑："

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:8
msgid ""
"Find all peak points between the threshold range (-5, 5) according to the"
" oscillation period and the initial peak voltage, i.e. `dc_max_list`;"
msgstr ""
"根据振荡周期和初始峰值电压找到在阈值范围(-5, 5)之间所有峰值点，即 `dc_max_list`;"

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:10
msgid ""
"Find all valley points between the threshold range (-5, 5) according to "
"the oscillation period and the initial peak voltage, i.e. `dc_min_list`;"
msgstr ""
"根据振荡周期和初始峰值电压找到在阈值范围(-5, 5)之间所有谷值点，即 `dc_min_list`;"

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:12
msgid "Find all `dc_max_list` voltages closest to `0v` and return as `dc_max`;"
msgstr "找出所有 `dc_max_list` 最接近 `0v` 的电压作为 `dc_max` 返回;"

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:13
msgid "Find all `dc_min_list` voltages closest to `0v` and return as `dc_mix`;"
msgstr "找出所有 `dc_min_list` 最接近 `0v` 的电压作为 `dc_mix` 返回;"

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:16
msgid ""
"The DC value corresponding to the sweet point, at which time the cavity "
"frequency is the largest."
msgstr "简并点对应的DC值，此时腔频最大"

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:20
msgid "Qubit modulation spectrum oscillation period."
msgstr "量子比特调制谱振荡周期"

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:23
msgid "Voltage upper and lower limits, the default is -5 to 5v."
msgstr "电压上下限，默认为-5至5v"

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point:26
msgid ""
"dc_min (DC voltage corresponding to the minimum cavity frequency), dc_max"
" (DC voltage corresponding to the maximum cavity frequency)"
msgstr "dc_min(腔频最小对应的DC电压)、dc_max(腔频最大对应的DC电压)"

#: of pyQCat.analysis.algorithms.find_peak.get_qubit_dc_point
msgid "Return type"
msgstr ""

#~ msgid ""
#~ "Get max dc and min dc :param "
#~ "max_reference: Reference point :param freq:"
#~ " Oscillation cycle :param threshold: "
#~ "Threshold points for maximum and minimum"
#~ " values"
#~ msgstr ""

#~ msgid "Returns:"
#~ msgstr ""

#~ msgid "Notes"
#~ msgstr ""

#~ msgid ""
#~ "1. Find all peak points between "
#~ "the threshold range (-5, 5) according"
#~ " to the oscillation period and the"
#~ " initial peak voltage, i.e. `dc_max_list`;"
#~ " 2. Find all valley points between"
#~ " the threshold range (-5, 5) "
#~ "according to the oscillation period and"
#~ " the initial peak voltage, i.e. "
#~ "`dc_min_list`; 3. Find all `dc_max_list` "
#~ "voltages closest to `0v` and return "
#~ "as `dc_max`; 4. Find all `dc_min_list`"
#~ " voltages closest to `0v` and return"
#~ " as `dc_mix`;"
#~ msgstr ""

#~ msgid ""
#~ "dc_min (DC voltage corresponding to the"
#~ " minimum cavity frequency),     dc_max (DC"
#~ " voltage corresponding to the maximum "
#~ "cavity frequency)"
#~ msgstr ""

#~ msgid "dc_min (DC voltage corresponding to the minimum cavity frequency),"
#~ msgstr ""

#~ msgid "dc_max (DC voltage corresponding to the maximum cavity frequency)"
#~ msgstr ""

