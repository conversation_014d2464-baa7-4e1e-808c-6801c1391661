# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/06/13
# __author:       <PERSON> <PERSON>

"""
Distortion T1 composite experiment new.

"""

from copy import deepcopy
from datetime import datetime

import numpy as np

from ....analysis.algorithms.smooth_update import SmoothLineUpdate
from ....analysis.library.distortion_t1_composite_analysis import (
    DistortionT1CompositeNewAnalysis,
)
from ....errors import ExperimentFlowError
from ....log import pyqlog
from ....parameters import options_wrapper
from ....qaio_property import QAIO
from ....structures import MetaData, Options
from ....tools.savefile import LocalFile, S3File
from ....tools.utilities import get_xy_step, qarange
from ....types import <PERSON>RunMode, QualityDescribe
from ...composite_experiment import CompositeExperiment
from ...single import (
    CouplerDistortionT1,
    CouplerDistortionZZ,
    DistortionT1,
)
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class DistortionT1CompositeNew(CompositeExperiment):
    """DistortionT1 Composite Experiment new.
    Calibrate distortion, when use distortion_type is `width`.
    """

    _sub_experiment_class = DistortionT1

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            iteration_times (int): Set experiment iteration times.
            xy_delay_start (float): XY line delay start value.
            xy_delay_max (float): XY line delay max or end value.
            xy_step_map (dict): Update xy_delay step map.
            exp_max_num (int): Use SLU update xy_delay the max iteration experiments.

            width_num (int): When update z_offset_list by width_num multiple FWHM.
            width_min (float): Limit width too small, width min value.
            width_max (float): Limit width too large, width max value.
            min_scan_points (int): Min scan points of z_offset_list.
            max_scan_points (int): Max scan points of z_offset_list.

            bit_type(str): bit type, normal `Qubit` or `Coupler`.
            base_history (bool): Base on history distortion data calibrate.
            update_xy_delay_slu (bool): Is or not update xy_delay use SLU.
            update_z_offset_range (bool): Is or not update z_offset_list.

            z_amp (float): The const z_amp of Z line.
            z_offset_list (List, np.ndarray): Scan Z offset range.
            expect_z_step (float): Set z_offset update expect value.
            z_offset_min (float): Set z_offset min value.
            z_offset_max (float): Set z_offset max value.
                When z_offset_list min value less than `z_offset_min`,
                or max value greater than `z_offset_max` terminate iteration.

        """
        options = super()._default_experiment_options()

        options.set_validator("iteration_times", (1, 10, 0))
        options.set_validator("xy_delay_start", float)
        options.set_validator("xy_delay_max", float)
        options.set_validator("xy_step_map", dict)
        options.set_validator("exp_max_num", int)

        options.set_validator("width_num", int)
        options.set_validator("width_min", float)
        options.set_validator("width_max", float)
        options.set_validator("min_scan_points", int)
        options.set_validator("max_scan_points", int)

        options.set_validator("bit_type", ["Qubit", "Coupler"])
        options.set_validator("base_history", bool)
        options.set_validator("update_xy_delay_slu", bool)
        options.set_validator("update_z_offset_range", bool)

        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("z_offset_list", list)
        options.set_validator("expect_z_step", float)
        options.set_validator("z_offset_min", float)
        options.set_validator("z_offset_max", float)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        # distortion t1 composite experiment args
        options.iteration_times = 1
        options.xy_delay_start = 0.0
        options.xy_delay_max = 10000
        options.xy_step_map = {
            "lt_10": 0.625,
            "lt_50": 1.25,
            "lt_100": 5.0,
            "lt_200": 10.0,
            "lt_500": 20.0,
            "lt_1000": 50.0,
            "lt_5000": 100.0,
            "lt_10000": 200.0,
            "gt_10000": 300.0,
            "gt_20000": 400.0,
        }
        options.exp_max_num = 100

        options.width_num = 1
        options.width_min = 0.02
        options.width_max = 0.05
        options.min_scan_points = 31
        options.max_scan_points = 101

        options.bit_type = "Qubit"
        options.base_history = False
        options.update_xy_delay_slu = False
        options.update_z_offset_range = True

        # DistortionT1 once experiment parameters
        options.z_amp = None
        options.z_offset_list = qarange(-0.02, 0.02, 0.0005)

        options.expect_z_step = None
        options.z_offset_min = -0.05
        options.z_offset_max = 0.05

        options.run_mode = ExperimentRunMode.sync_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for DistortionT1Composite experiment.

        Options:
            iteration_time (int): Iteration time number.
            dt_list (List[np.ndarray]):
            so_list (List[np.ndarray]):

        """
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("lfilter_flag", bool)
        options.set_validator("expect_width", float)
        options.set_validator("ylim", list)

        options.z_amp = -0.5
        options.dac_sample_rate = 3.2
        options.sample_rate = 1.2
        options.lfilter_flag = False
        options.iteration_time = 0
        options.cal_response_mode = "add"

        options.dt_list = []
        options.so_list = []

        options.quality_bounds = [0.9999, 0.999, 0.9]
        options.data_key = ["Response"]
        options.sub_key = "P1"
        options.expect_width = None
        options.ylim = [0.95, 1.02]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method.
        Statistics and saving parameters of running process.

        Options:

            distortion_width (float): Calibrate z line distortion width.
            distortion_ab (List): IIR filter parameters. [ a, b ]
            delay_arr (np.ndarray): Distortion data delay array.
            response_arr (np.ndarray): Distortion data response array.

        """
        options = super()._default_run_options()

        options.mark_info = ""
        options.iteration_time = 0
        options.z_step = 0.001
        options.x_sampling_interval = 0.3125
        options.initial_x_step = 0.3125

        options.cal_response_mode = ""

        options.dt_list = []
        options.so_list = []

        options.xy_delay_list = []
        options.offset_list = []
        options.normal_offset_list = []
        options.response_list = []

        # iteration update qubit_test, update compensate
        options.distortion_width = None
        options.distortion_ab = None
        options.delay_arr = None
        options.response_arr = None

        # adjust, when `new_case` only once modify child_exp readout point.
        options.child_rdz_amp = None
        options.child_ac_bias = {}

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "Zamp": self.experiment_options.z_amp,
            "cal_response_mode": self.analysis_options.cal_response_mode,
        }
        return metadata

    def _patch_z_amp(self, t_name: str):
        """According to work point value set z_amp."""
        run_mode = self.child_experiment.experiment_options.run_mode
        if self.experiment_options.z_amp is None:
            if self.ac_bias.get(t_name):
                w_point = self.ac_bias[t_name][1]
            elif self.working_dc.get(t_name):
                w_point = self.working_dc[t_name][1]
            else:
                qc_map = {}
                qubit_map = {qubit.name: qubit for qubit in self.qubits}
                coupler_map = {qubit.name: qubit for qubit in self.couplers}
                qc_map.update(qubit_map)
                qc_map.update(coupler_map)
                qc_obj = qc_map.get(t_name)
                if qc_obj:
                    w_point = qc_obj.dc_max + qc_obj.idle_point
                else:
                    raise ValueError(f"Set {t_name} error: not in exist environment!")
            if w_point < 0:
                z_amp = round(0.45 - w_point, 3)
            else:
                z_amp = round(-0.45 - w_point, 3)

            if run_mode == "normal":
                z_amp = z_amp
            elif run_mode == "new_case":
                z_amp = -z_amp

            self.experiment_options.z_amp = z_amp
            
            # bugfix: Cancel mandatory settings
            # self.experiment_options.z_offset_min = -0.05
            # self.experiment_options.z_offset_max = 0.05

            pyqlog.info(
                f"Patch {t_name} working_point: {w_point}, run_mode: {run_mode}, z_amp: {z_amp}"
            )
            pyqlog.info(
                f"Patch z_offset_min: {self.experiment_options.z_offset_min}, "
                f"z_offset_max: {self.experiment_options.z_offset_max}"
            )

        # # bugfix: z offset min/max limit bt z_amp
        # abs_z_amp = abs(self.experiment_options.z_amp)
        # if abs_z_amp < 0.5:
        #     self.experiment_options.z_offset_max = 0.5 - abs_z_amp
        #     self.experiment_options.z_offset_min = abs_z_amp - 0.5
        # else:
        #     raise ExperimentOptionsError(
        #         self,
        #         key="z_amp",
        #         value=self.experiment_options.z_amp,
        #         msg="asb zamp must be less than 0.5"
        #     )
        pyqlog.info(f"{t_name} Set z_amp: {self.experiment_options.z_amp}")

    def _check_options(self):
        """Check options."""
        super()._check_options()

        xy_delay_start = self.experiment_options.xy_delay_start
        xy_step_map = self.experiment_options.xy_step_map
        bit_type = self.experiment_options.bit_type
        z_offset_list = self.experiment_options.z_offset_list
        expect_z_step = self.experiment_options.expect_z_step

        result_name = self.analysis_options.result_name
        sub_key = self.analysis_options.sub_key

        if bit_type.capitalize() == "Qubit":
            result_name = self.child_experiment.qubit.name
            sub_key = "P1"
        elif bit_type.capitalize() == "Coupler":
            result_name = self.child_experiment.coupler.name
            sub_key = "P0"

        if self.child_experiment.is_coupler_exp is True:
            result_name = self.child_experiment.coupler.name
            # bugfix: coupler distortion need use coupler compensate
            # x_delay to drive qubit x_delay
            drive_q_compensate = None
            coupler_compensate = None
            for bit, compensate in self.compensates.items():
                if bit.name == self.child_experiment.coupler.name:
                    coupler_compensate = compensate
                if bit.name == self.child_experiment.driveQ.name:
                    drive_q_compensate = compensate
            drive_q_compensate.x_delay = coupler_compensate.x_delay

        dac_sample_rate = QAIO.dac_sample_rate
        sample_rate = QAIO.awg_sample_rate
        pyqlog.info(
            f"AIO XY line sample rate: {dac_sample_rate} GHz, "
            f"Z line sample rate: {sample_rate} GHz."
        )

        x_ts = round(1 / dac_sample_rate, 5)

        if expect_z_step is not None:
            z_step = expect_z_step
        else:
            z_decimal = max(
                ["{:f}".format(i).rstrip("0")[::-1].find(".") for i in z_offset_list]
            )
            z_step = round(abs(z_offset_list[1] - z_offset_list[0]), z_decimal)

        initial_x_step = get_xy_step(xy_delay_start, xy_step_map)

        self._patch_z_amp(result_name)

        self.set_run_options(
            z_step=z_step,
            x_sampling_interval=x_ts,
            initial_x_step=initial_x_step,
        )

        self.set_analysis_options(
            result_name=result_name,
            dac_sample_rate=dac_sample_rate,
            sample_rate=sample_rate,
            sub_key=sub_key,
        )

    def _save_curve_analysis_plot(self, save_mark: str = None):
        """Save CurveAnalysis plot figure. Overwrite."""
        if save_mark is None:
            mark_info = self.run_options.mark_info
            time_str = datetime.now().strftime("%Y-%m-%d %H.%M.%S")
            save_mark = f"{mark_info}_{time_str}"
        super()._save_curve_analysis_plot(save_mark=save_mark)

    def _update_file_dirs(self, file_path: str, label: str):
        """Update self.file object dirs value."""
        if not label:
            self.file.dirs = file_path
        elif isinstance(self.file, LocalFile):
            self.file.dirs = f"{file_path}{label}\\"
        elif isinstance(self.file, S3File):
            self.file.dirs = f"{file_path}{label}/"

    def _adjust_xy_delay(self, xy_delay: float):
        """By xy_delay and xy_step_map calculate new xy_delay."""
        xy_step_map = self.experiment_options.xy_step_map
        xy_step = get_xy_step(xy_delay, xy_step_map)
        new_xy_delay = xy_delay + xy_step
        return new_xy_delay

    def _adjust_z_offset_list(self, offset: float, width: float):
        """Adjust z_offset_list."""
        width_num = self.experiment_options.width_num
        width_min = self.experiment_options.width_min
        width_max = self.experiment_options.width_max
        min_scan_points = self.experiment_options.min_scan_points
        max_scan_points = self.experiment_options.max_scan_points

        z_step = self.run_options.z_step

        if width < width_min:
            width = width_min
        elif width > width_max:
            width = width_max

        z_offset_arr = np.arange(
            offset - width * width_num, offset + width * width_num, z_step
        )

        scan_points = len(z_offset_arr)
        if scan_points < min_scan_points:
            z_offset_arr = np.linspace(
                offset - width * width_num * 2,
                offset + width * width_num * 2,
                min_scan_points,
            )
        elif scan_points > max_scan_points:
            z_offset_arr = np.linspace(
                offset - width * width_num, offset + width * width_num, max_scan_points
            )

        return z_offset_arr.tolist()

    def _modify_z_offset_list(self, z_offset_list):
        """Modify z_offset_list, when iteration is not 0."""
        min_scan_points = self.experiment_options.min_scan_points
        max_scan_points = self.experiment_options.max_scan_points

        z_start, z_end = z_offset_list[0], z_offset_list[-1]
        z_offset_arr = np.array(z_offset_list)
        scan_points = len(z_offset_arr)
        if scan_points < min_scan_points:
            z_offset_arr = np.linspace(z_start, z_end, min_scan_points)
        elif scan_points > max_scan_points:
            z_offset_arr = np.linspace(z_start, z_end, max_scan_points)

        return z_offset_arr.tolist()

    async def _run_once(self, experiment: "TopExperiment"):
        """Single time run logic. Loop xy_delay_list."""
        xy_delay_start = self.experiment_options.xy_delay_start
        xy_delay_max = self.experiment_options.xy_delay_max
        exp_max_num = self.experiment_options.exp_max_num
        update_xy_delay_slu = self.experiment_options.update_xy_delay_slu
        update_z_offset_range = self.experiment_options.update_z_offset_range

        z_amp = self.experiment_options.z_amp
        init_z_offset_list = self.experiment_options.z_offset_list
        z_offset_min = self.experiment_options.z_offset_min
        z_offset_max = self.experiment_options.z_offset_max

        result_name = self.analysis_options.result_name
        sub_key = self.analysis_options.sub_key
        provide_field = self.analysis_options.data_key[0]

        cal_response_mode = self.run_options.cal_response_mode
        initial_x_step = self.run_options.initial_x_step
        z_step = self.run_options.z_step
        x_ts = self.run_options.x_sampling_interval
        i = self.run_options.iteration_time
        child_rdz_amp = self.run_options.child_rdz_amp
        child_ac_bias = self.run_options.child_ac_bias

        slu = SmoothLineUpdate(
            initial_x_step=initial_x_step,
            expect_y_step=z_step,
            min_x_step=x_ts,
            max_x_step=x_ts * 400,
            x_sampling_interval=x_ts,
            smooth_window_length=5,
            smooth_window="hanning",
            interp_order=3,
            update_x_flag=update_xy_delay_slu,
        )

        self._analysis = None
        self._experiments = []
        self._minimize_experiments = []

        self.run_options.xy_delay_list = []
        self.run_options.offset_list = []
        self.run_options.normal_offset_list = []
        self.run_options.response_list = []

        self.run_options.distortion_width = None
        self.run_options.distortion_ab = None
        self.run_options.delay_arr = None
        self.run_options.response_arr = None

        error_count = 0
        p0_history = None
        xy_delay = xy_delay_start
        if i == 0:
            z_offset_list = init_z_offset_list
        else:
            z_offset_list = self._modify_z_offset_list(init_z_offset_list)
        exp_idx = 0
        while xy_delay_start <= xy_delay <= xy_delay_max:

            if not (z_offset_min <= np.min(z_offset_list) and z_offset_max >= np.max(z_offset_list)):
                pyqlog.warning(
                    f"z_offset_list: {z_offset_list[0]} ~ {z_offset_list[-1]}, "
                    f"but limit min: {z_offset_min}， max: {z_offset_max}"
                )
                break

            if update_xy_delay_slu is True and exp_idx >= exp_max_num:
                pyqlog.info(
                    f"{result_name} update_xy_delay_slu is {update_xy_delay_slu}, "
                    f"executed experiment count {exp_idx}, "
                    f"greater than exp_max_num {exp_max_num}"
                )
                break

            new_dist_t1_exp = deepcopy(experiment)
            new_dist_t1_exp.set_parent_file(
                self, description=f"iter{i}_xy_delay={xy_delay}_err_count={error_count}", index=exp_idx
            )
            new_dist_t1_exp.set_experiment_options(
                z_amp=z_amp, z_offset_list=z_offset_list, xy_delay=xy_delay
            )
            new_dist_t1_exp.set_analysis_options(
                data_key=[sub_key],
                p0_history=p0_history,
            )
            new_dist_t1_exp.set_run_options(
                rdz_amp=child_rdz_amp,
                ac_bias=child_ac_bias,
            )
            self._check_simulator_data(new_dist_t1_exp, exp_idx)

            # new_dist_t1_exp.run()
            # new_dist_t1_exp.clear_params()
            await new_dist_t1_exp.run_experiment()

            if child_rdz_amp is None and new_dist_t1_exp.run_options.rdz_amp:
                child_rdz_amp = new_dist_t1_exp.run_options.rdz_amp
                self.run_options.child_rdz_amp = child_rdz_amp
                pyqlog.info(
                    f"Note {result_name} child experiment readout amp: {child_rdz_amp}"
                )
            if not child_ac_bias and new_dist_t1_exp.run_options.ac_bias:
                child_ac_bias = new_dist_t1_exp.run_options.ac_bias
                self.run_options.child_ac_bias = child_ac_bias
                pyqlog.info(
                    f"Note {result_name} child experiment update ac: {child_ac_bias}"
                )

            model_name = new_dist_t1_exp.analysis_options.fit_model_name
            offset = new_dist_t1_exp.analysis.results.t_offset.value
            width = new_dist_t1_exp.analysis.results.width.value
            try:
                fit_data = new_dist_t1_exp.analysis.analysis_datas[sub_key].fit_data
                if fit_data is not None:
                    p0_history = dict(zip(fit_data.popt_keys, fit_data.popt))
            except Exception as err:
                pyqlog.warning(f"Calculate p0_history error: {err}")

            pyqlog.debug(f"Sub Experiment Analysis fit_model: {model_name}")
            pyqlog.info(
                f"{result_name} Run iteration time {i}, exp_idx: {exp_idx}, "
                f"xy_delay={xy_delay} ns, fit offset: {offset}, width: {width}"
            )

            if z_offset_list[0] <= offset <= z_offset_list[-1]:
                if z_amp == 0:
                    normal_offset = offset
                else:
                    normal_offset = offset / z_amp

                if not cal_response_mode:
                    run_mode = new_dist_t1_exp.experiment_options.run_mode
                    if run_mode == "normal":
                        cal_response_mode = "add"
                    elif run_mode == "new_case":
                        cal_response_mode = "reduce"
                    self.set_run_options(cal_response_mode=cal_response_mode)

                if cal_response_mode == "add":
                    response = 1 + normal_offset
                elif cal_response_mode == "reduce":
                    response = 1 - normal_offset
                else:
                    # now just support "add" or "reduce"
                    response = 1 + normal_offset

                new_dist_t1_exp.analysis.provide_for_parent.update(
                    {provide_field: response}
                )
                # self._experiments.append(new_dist_t1_exp)
                if self.experiment_options.minimize_mode is True:
                    self._minimize_experiments.append(new_dist_t1_exp.analysis.analysis_program())
                else:
                    self._experiments.append(new_dist_t1_exp)

                self.run_options.xy_delay_list.append(xy_delay)
                self.run_options.offset_list.append(offset)
                self.run_options.normal_offset_list.append(normal_offset)
                self.run_options.response_list.append(response)

                # adjust xy_delay, z_offset_list
                old_xy_delay = xy_delay
                try:
                    if update_xy_delay_slu is True:
                        xy_delay, next_offset = slu.update_xy(old_xy_delay, offset)
                    else:
                        slu.x = old_xy_delay
                        slu.y = offset
                        xy_delay = self._adjust_xy_delay(old_xy_delay)
                        next_offset = slu.update_y(xy_delay)
                except Exception as err:
                    pyqlog.warning(f"SLU update x, y error: {err}")
                    xy_delay = self._adjust_xy_delay(old_xy_delay)
                    next_offset = offset

                pyqlog.info(f"{result_name} predict next z_offset: {next_offset}")
                if update_z_offset_range is True:
                    z_offset_list = self._adjust_z_offset_list(next_offset, width)

                pyqlog.info(
                    f"{result_name} Run iteration time {i}, exp_idx: {exp_idx}, "
                    f"next xy_delay={xy_delay} ns,"
                    f"next z_offset_list: [{z_offset_list[0]},{z_offset_list[-1]}], "
                    f"scan points: {len(z_offset_list)}"
                )

                error_count = 0  # reset error_count
                exp_idx += 1
                err_msg = ""
            else:
                err_msg = f"fit offset {offset} out [{z_offset_list[0]},{z_offset_list[-1]}] range!"

            if err_msg:
                if error_count < 3:
                    pyqlog.warning(f"error_count: {error_count}, {err_msg}")
                    error_count += 1
                    z_offset_arr = np.linspace(
                        z_offset_list[0] - 0.03,
                        z_offset_list[-1] + 0.03,
                        len(z_offset_list),
                    )
                    z_offset_list = z_offset_arr.tolist()
                else:
                    raise ExperimentFlowError(
                        self, f"error_count: {error_count}, {err_msg}"
                    )

        self.run_options.dt_list.append(np.array(self.run_options.xy_delay_list))
        self.run_options.so_list.append(np.array(self.run_options.response_list))
        self.file.save_data(
            np.array(self.run_options.xy_delay_list),
            np.array(self.run_options.offset_list),
            np.array(self.run_options.normal_offset_list),
            np.array(self.run_options.response_list),
            name=f"{result_name}_iteration_{i}_run_options",
        )

        self.file.save_data(
            np.hstack(tuple(self.run_options.dt_list)),
            np.hstack(tuple(self.run_options.so_list)),
            name=f"{result_name}_iteration_{i}_dt_so_list",
        )

        self.set_analysis_options(
            z_amp=self.experiment_options.z_amp,
            cal_response_mode=self.run_options.cal_response_mode,
            iteration_time=i,
            dt_list=self.run_options.dt_list,
            so_list=self.run_options.so_list,
        )

        self._run_analysis(
            x_data=np.array(self.run_options.xy_delay_list),
            analysis_class=DistortionT1CompositeNewAnalysis,
        )

        distortion_width = self.analysis.results.distortion_width.value
        distortion_ab = self.analysis.results.distortion_ab.value
        delay_arr = self.analysis.results.delay_arr.value
        response_arr = self.analysis.results.response_arr.value

        self.file.save_data(
            np.array(distortion_ab),
            name=f"{result_name}_iteration_{i}_distortion_ab",
            fmt="%.16f",
        )
        self.file.save_data(
            delay_arr, response_arr, name=f"{result_name}_iteration_{i}_delay_response"
        )

        self.run_options.distortion_width = distortion_width
        self.run_options.distortion_ab = distortion_ab
        self.run_options.delay_arr = delay_arr
        self.run_options.response_arr = response_arr

        lfilter_flag = self.analysis.options.lfilter_flag
        quality_str = self.analysis.quality.descriptor
        limit_flag = self.analysis.results.limit_flag.value

        pyqlog.info(
            f"{result_name} Distortion Composite iteration {i} end, "
            f"use pole model flag: {lfilter_flag}, "
            f"quality: {quality_str},"
            f"result limit_flag: {limit_flag}"
        )

        if quality_str not in [QualityDescribe.perfect, QualityDescribe.normal] and i == 0:
            self.set_analysis_options(lfilter_flag=False)
            self.run_options.distortion_ab = []
            pyqlog.info(
                f"{result_name} Pole model scheme is not effective, "
                f"switch to deconvolution scheme!"
            )

    async def _sync_composite_run(self):
        """Distortion T1 Composite Run Logic."""
        # super().run()

        base_history = self.experiment_options.base_history
        result_name = self.analysis_options.result_name

        origin_file_dirs = self.file._dirs
        for i in range(self.experiment_options.iteration_times):
            pyqlog.info(f"{result_name} DistortionT1Composite iteration_time: {i}")
            dist_t1_exp = deepcopy(self.child_experiment)

            if i == 0 and base_history is False:
                # the first iteration qubit_test, reset compensate
                for compensate in dist_t1_exp.compensates.values():
                    if compensate.name == result_name:
                        compensate.z_distortion_width = 0
                        compensate.z_distortion_ab = []
                        compensate.z_distortion_sos = {}
            elif i == 0 and base_history is True:
                # the first iteration simulator
                # use history distortion calibrate
                for compensate in dist_t1_exp.compensates.values():
                    if compensate.name == result_name:
                        his_dt_arr = np.array(compensate.z_distortion_tlist)
                        his_so_arr = np.array(compensate.z_distortion_solist)
                        if his_dt_arr.tolist() and his_so_arr.tolist():
                            self.run_options.dt_list.append(his_dt_arr)
                            self.run_options.so_list.append(his_so_arr)
            else:
                # the other iteration qubit_test, update compensate
                distortion_width = self.run_options.distortion_width
                distortion_ab = self.run_options.distortion_ab
                delay_arr = self.run_options.delay_arr
                response_arr = self.run_options.response_arr

                for compensate in dist_t1_exp.compensates.values():
                    if compensate.name == result_name:
                        compensate.z_distortion_width = distortion_width
                        compensate.z_distortion_ab = distortion_ab
                        if isinstance(delay_arr, np.ndarray):
                            delay_arr = delay_arr.tolist()
                        if isinstance(response_arr, np.ndarray):
                            response_arr = response_arr.tolist()
                        compensate.z_distortion_tlist = delay_arr
                        compensate.z_distortion_solist = response_arr

            mark_info = f"{result_name}_iter{i}"
            self.set_run_options(mark_info=mark_info, iteration_time=i)
            self._update_file_dirs(origin_file_dirs, mark_info)
            await self._run_once(dist_t1_exp)


class CouplerDistortionT1CompositeNew(DistortionT1CompositeNew):
    """CouplerDistortionT1 Distortion class."""

    _sub_experiment_class = CouplerDistortionT1


class CouplerDistortionZZCompositeNew(DistortionT1CompositeNew):
    """CouplerDistortionZZ Distortion class."""

    _sub_experiment_class = CouplerDistortionZZ

    def _check_options(self):
        """Check options."""
        super()._check_options()
        sub_key = "P1"
        self.set_analysis_options(sub_key=sub_key)
