# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.analysis.library.rst:2
msgid "pyQCat.analysis.library package"
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:5
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:8
msgid "pyQCat.analysis.library.ac\\_spectrum\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.ac_spectrum_analysis:1
msgid "AC Spectrum Analysis."
msgstr ""

#: of pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis:1
#: pyQCat.analysis.library.ape_composite_analysis.APECompositeAnalysis:1
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis:1
#: pyQCat.analysis.library.crosstalk_analysis.CrosstalkAnalysis:1
#: pyQCat.analysis.library.distortion_t1_analysis.DistortionT1Analysis:1
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis:1
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis:1
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis:1
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis:1
#: pyQCat.analysis.library.rb_analysis.RBAnalysis:1
#: pyQCat.analysis.library.readout_freq_cali_analysis.ReadoutFreqCaliAnalysis:1
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis:1
#: pyQCat.analysis.library.t1_analysis.T1Analysis:1
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis:1
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis:1
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis:1
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.curve_analysis.CurveAnalysis`"
msgstr ""

#: of pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis:1
#: pyQCat.analysis.library:39:<autosummary>:1
msgid "AC/DC spectrum result analysis class."
msgstr ""

#: of
#: pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis._default_options:1
#: pyQCat.analysis.library.amp_opt_analysis.AmpOptAnalysis._default_options:1
#: pyQCat.analysis.library.ape_analysis.APEAnalysis._default_options:1
#: pyQCat.analysis.library.ape_composite_analysis.APECompositeAnalysis._default_options:1
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1
#: pyQCat.analysis.library.crosstalk_analysis.CrosstalkAnalysis._default_options:1
#: pyQCat.analysis.library.dc_spectrum_analysis.DCSpectrumAnalysis._default_options:1
#: pyQCat.analysis.library.distortion_t1_analysis.DistortionT1Analysis._default_options:1
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._default_options:1
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1
#: pyQCat.analysis.library.readout_freq_cali_analysis.ReadoutFreqCaliAnalysis._default_options:1
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options:1
#: pyQCat.analysis.library.single_shot_composite_analysis.ReadoutPowerCaliAnalysis._default_options:1
#: pyQCat.analysis.library.single_shot_composite_analysis.SampleWidthOptimizeAnalysis._default_options:1
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._default_options:1
#: pyQCat.analysis.library.t1_analysis.T1Analysis._default_options:1
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._default_options:1
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._default_options:1
msgid "Create the Analysis options, and set some fields."
msgstr ""

#: of
#: pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis._default_options
#: pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis._guess_fit_param
#: pyQCat.analysis.library.amp_opt_analysis.AmpOptAnalysis._default_options
#: pyQCat.analysis.library.ape_analysis.APEAnalysis._default_options
#: pyQCat.analysis.library.ape_composite_analysis.APECompositeAnalysis._default_options
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param
#: pyQCat.analysis.library.crosstalk_analysis.CrosstalkAnalysis._default_options
#: pyQCat.analysis.library.dc_spectrum_analysis.DCSpectrumAnalysis._default_options
#: pyQCat.analysis.library.distortion_t1_analysis.DistortionT1Analysis._default_options
#: pyQCat.analysis.library.distortion_t1_analysis.DistortionT1Analysis._guess_fit_param
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._default_options
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._evaluate_quality
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._guess_fit_param
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._default_options
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._guess_fit_param
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._default_options
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._guess_fit_param
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._default_options
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._evaluate_quality
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._get_background_noise
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options
#: pyQCat.analysis.library.rabi_analysis.RabiWidthAnalysis._default_options
#: pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis._default_options
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._create_analysis_data
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param
#: pyQCat.analysis.library.readout_freq_cali_analysis.ReadoutFreqCaliAnalysis._default_options
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis.drawer
#: pyQCat.analysis.library.single_shot_composite_analysis.ReadoutPowerCaliAnalysis._default_options
#: pyQCat.analysis.library.single_shot_composite_analysis.SampleWidthOptimizeAnalysis._default_options
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._default_options
#: pyQCat.analysis.library.t1_analysis.T1Analysis._default_options
#: pyQCat.analysis.library.t1_analysis.T1Analysis._guess_fit_param
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options
#: pyQCat.analysis.library.t2ramsey_analysis.T2RamseyAnalysis._default_options
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._default_options
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._guess_fit_param
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._default_options
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._guess_fit_param
msgid "Return type"
msgstr ""

#: of
#: pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis._default_options:4
#: pyQCat.analysis.library.amp_opt_analysis.AmpOptAnalysis._default_options:4
#: pyQCat.analysis.library.ape_analysis.APEAnalysis._default_options:4
#: pyQCat.analysis.library.ape_composite_analysis.APECompositeAnalysis._default_options:7
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:4
#: pyQCat.analysis.library.crosstalk_analysis.CrosstalkAnalysis._default_options:4
#: pyQCat.analysis.library.dc_spectrum_analysis.DCSpectrumAnalysis._default_options:6
#: pyQCat.analysis.library.distortion_t1_analysis.DistortionT1Analysis._default_options:4
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._default_options:11
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._default_options:4
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._default_options:4
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._default_options:4
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:4
#: pyQCat.analysis.library.rabi_analysis.RabiWidthAnalysis._default_options:4
#: pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis._default_options:4
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:13
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:21
#: pyQCat.analysis.library.readout_freq_cali_analysis.ReadoutFreqCaliAnalysis._default_options:8
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options:14
#: pyQCat.analysis.library.single_shot_composite_analysis.ReadoutPowerCaliAnalysis._default_options:4
#: pyQCat.analysis.library.single_shot_composite_analysis.SampleWidthOptimizeAnalysis._default_options:4
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._default_options:10
#: pyQCat.analysis.library.t1_analysis.T1Analysis._default_options:4
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:27
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:25
#: pyQCat.analysis.library.t2ramsey_analysis.T2RamseyAnalysis._default_options:4
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._default_options:4
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._default_options:4
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis._guess_fit_param:1
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param:1
#: pyQCat.analysis.library.distortion_t1_analysis.DistortionT1Analysis._guess_fit_param:1
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._guess_fit_param:1
#: pyQCat.analysis.library.t1_analysis.T1Analysis._guess_fit_param:1
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._guess_fit_param:1
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._guess_fit_param:1
msgid "Guess initial fit parameters."
msgstr ""

#: of
#: pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis._guess_fit_param
#: pyQCat.analysis.library.amp_opt_analysis.AmpOptAnalysis._extract_result
#: pyQCat.analysis.library.ape_analysis.APEAnalysis._extract_result
#: pyQCat.analysis.library.ape_composite_analysis.APECompositeAnalysis._extract_result
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._extract_result
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param
#: pyQCat.analysis.library.distortion_t1_analysis.DistortionT1Analysis._guess_fit_param
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._extract_result
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._guess_fit_param
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._extract_result
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._guess_fit_param
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._extract_result
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._guess_fit_param
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._extract_result
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._get_background_noise
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._extract_result
#: pyQCat.analysis.library.rabi_analysis.RabiWidthAnalysis._extract_result
#: pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis._extract_result
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._extract_result
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param
#: pyQCat.analysis.library.readout_freq_cali_analysis.ReadoutFreqCaliAnalysis._extract_result
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._extract_result
#: pyQCat.analysis.library.t1_analysis.T1Analysis._extract_result
#: pyQCat.analysis.library.t1_analysis.T1Analysis._guess_fit_param
#: pyQCat.analysis.library.t2ramsey_analysis.T2RamseyAnalysis._extract_result
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._extract_result
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._guess_fit_param
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._extract_result
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._guess_fit_param
msgid "Parameters"
msgstr ""

#: of
#: pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis._guess_fit_param:4
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param:4
#: pyQCat.analysis.library.distortion_t1_analysis.DistortionT1Analysis._guess_fit_param:4
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._guess_fit_param:4
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._guess_fit_param:4
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._guess_fit_param:4
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:10
#: pyQCat.analysis.library.t1_analysis.T1Analysis._guess_fit_param:4
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._guess_fit_param:4
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._guess_fit_param:4
msgid "Fit options filled with user provided guess and bounds."
msgstr ""

#: of
#: pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis._guess_fit_param:6
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param:6
#: pyQCat.analysis.library.distortion_t1_analysis.DistortionT1Analysis._guess_fit_param:6
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._guess_fit_param:6
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._guess_fit_param:6
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._guess_fit_param:6
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:12
#: pyQCat.analysis.library.t1_analysis.T1Analysis._guess_fit_param:6
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._guess_fit_param:6
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._guess_fit_param:6
msgid "Formatted data collection to fit."
msgstr ""

#: of
#: pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis._guess_fit_param:8
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param:8
#: pyQCat.analysis.library.distortion_t1_analysis.DistortionT1Analysis._guess_fit_param:8
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._guess_fit_param:8
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._guess_fit_param:8
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._guess_fit_param:8
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:14
#: pyQCat.analysis.library.t1_analysis.T1Analysis._guess_fit_param:8
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._guess_fit_param:8
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._guess_fit_param:8
msgid ""
":py:data:`~typing.Union`\\[:py:class:`~pyQCat.analysis.specification.FitOptions`,"
" "
":py:class:`~typing.List`\\[:py:class:`~pyQCat.analysis.specification.FitOptions`]]"
msgstr ""

#: of
#: pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis._guess_fit_param
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param
#: pyQCat.analysis.library.distortion_t1_analysis.DistortionT1Analysis._guess_fit_param
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._evaluate_quality
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._guess_fit_param
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._guess_fit_param
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._guess_fit_param
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._evaluate_quality
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._get_background_noise
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._outlier_detectation
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._create_analysis_data
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data
#: pyQCat.analysis.library.t1_analysis.T1Analysis._guess_fit_param
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._guess_fit_param
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._guess_fit_param
msgid "Returns"
msgstr ""

#: of
#: pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis._guess_fit_param:9
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param:9
#: pyQCat.analysis.library.distortion_t1_analysis.DistortionT1Analysis._guess_fit_param:9
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._guess_fit_param:9
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._guess_fit_param:9
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._guess_fit_param:9
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:15
#: pyQCat.analysis.library.t1_analysis.T1Analysis._guess_fit_param:9
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._guess_fit_param:9
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._guess_fit_param:9
msgid "List of fit options that are passed to the fitter function."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:16
msgid "pyQCat.analysis.library.amp\\_opt\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.amp_opt_analysis:1
msgid "Amp Optimization Analysis."
msgstr ""

#: of pyQCat.analysis.library.amp_opt_analysis.AmpOptAnalysis:1
#: pyQCat.analysis.library.ape_analysis.APEAnalysis:1
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis:1
msgid ""
"Bases: "
":py:class:`~pyQCat.analysis.oscillation_analysis.OscillationAnalysis`"
msgstr ""

#: of pyQCat.analysis.library.amp_opt_analysis.AmpOptAnalysis._extract_result:1
msgid "Extract Xpi from fitted data."
msgstr ""

#: of pyQCat.analysis.library.amp_opt_analysis.AmpOptAnalysis._extract_result:4
#: pyQCat.analysis.library.ape_analysis.APEAnalysis._extract_result:4
#: pyQCat.analysis.library.ape_composite_analysis.APECompositeAnalysis._extract_result:4
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._extract_result:4
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._extract_result:4
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._extract_result:4
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._extract_result:4
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._extract_result:4
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._extract_result:6
#: pyQCat.analysis.library.rabi_analysis.RabiWidthAnalysis._extract_result:4
#: pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis._extract_result:4
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._extract_result:4
#: pyQCat.analysis.library.readout_freq_cali_analysis.ReadoutFreqCaliAnalysis._extract_result:5
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._extract_result:5
#: pyQCat.analysis.library.t1_analysis.T1Analysis._extract_result:4
#: pyQCat.analysis.library.t2ramsey_analysis.T2RamseyAnalysis._extract_result:4
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._extract_result:4
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._extract_result:4
msgid "The basis for selecting data."
msgstr ""

#: of pyQCat.analysis.library.amp_opt_analysis.AmpOptAnalysis._extract_result:6
#: pyQCat.analysis.library.ape_analysis.APEAnalysis._extract_result:6
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._extract_result:6
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._extract_result:7
msgid "Quality of fit outcome."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:24
msgid "pyQCat.analysis.library.ape\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.ape_analysis:1
msgid "APE Analysis."
msgstr ""

#: of pyQCat.analysis.library.ape_analysis.APEAnalysis._extract_result:1
#: pyQCat.analysis.library.xyztiming_analysis.XYZTimingAnalysis._extract_result:1
msgid "Extract the Analysis results from fitted data."
msgstr ""

#: of pyQCat.analysis.library.ape_analysis.APEPoints:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:32
msgid "pyQCat.analysis.library.ape\\_composite\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.ape_composite_analysis:1
msgid "APE Composite Analysis."
msgstr ""

#: of
#: pyQCat.analysis.library.ape_composite_analysis.APECompositeAnalysis._default_options:5
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._default_options:9
#: pyQCat.analysis.library.readout_freq_cali_analysis.ReadoutFreqCaliAnalysis._default_options:6
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._default_options:8
msgid "Options:"
msgstr ""

#: of
#: pyQCat.analysis.library.ape_composite_analysis.APECompositeAnalysis._default_options:4
msgid "diff_threshold (float): Twice fine scan results difference."
msgstr ""

#: of
#: pyQCat.analysis.library.ape_composite_analysis.APECompositeAnalysis._extract_result:1
msgid "Extract detune value from points, and judge coincident or not."
msgstr ""

#: of
#: pyQCat.analysis.library.ape_composite_analysis.APECompositeAnalysis._visualization:1
msgid "Plot N times P0."
msgstr ""

#: of
#: pyQCat.analysis.library.ape_composite_analysis.APECompositeAnalysis._check_points:1
msgid "Check coincident point"
msgstr ""

#: of
#: pyQCat.analysis.library.ape_composite_analysis.APECompositeAnalysis._check_points:3
msgid ""
"If the sum of the intervals of the three coincident points is greater "
"than 2 times of sweep step, the task coincidence point is abnormal, and "
"the default X value uses the mean of the three coincidence points."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:40
msgid "pyQCat.analysis.library.cavity\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis:1
msgid "Cavity Analysis."
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._extract_result:1
msgid "Extract cavity frequency from fitted data."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:48
msgid "pyQCat.analysis.library.crosstalk\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.crosstalk_analysis.CrosstalkAnalysis:1
#: pyQCat.analysis.library:39:<autosummary>:1
msgid "An analysis class for getting crosstalk coefficients."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:56
msgid "pyQCat.analysis.library.dc\\_spectrum\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.dc_spectrum_analysis:1
msgid "DC Spectrum analysis."
msgstr ""

#: of pyQCat.analysis.library.dc_spectrum_analysis.DCSpectrumAnalysis:1
msgid ""
"Bases: "
":py:class:`~pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis`"
msgstr ""

#: of
#: pyQCat.analysis.library.dc_spectrum_analysis.DCSpectrumAnalysis._visualization:1
msgid "Plot dc and frequency relationship."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:64
msgid "pyQCat.analysis.library.distortion\\_t1\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.distortion_t1_analysis:1
msgid "Distortion T1 Analysis."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:72
msgid "pyQCat.analysis.library.distortion\\_t1\\_composite\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.distortion_t1_composite_analysis:1
msgid "Distortion T1 Composite Analysis."
msgstr ""

#: of
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._default_options:4
msgid ""
"z_amp (float): When run experiment,set Z amp of Z line pulse. sample_rate"
" (float): Sample rate. lfilter_flag (float): Use or not pole model. "
"iteration_time (int): Iteration time distortion composite."
msgstr ""

#: of
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._pre_operation:1
msgid ""
"When not the first iteration qubit_test, change experiment data, about "
"experiment_data.x_data, y_data."
msgstr ""

#: of
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._evaluate_quality:1
msgid "Evaluates the quality of the fit or other."
msgstr ""

#: of
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._evaluate_quality:3
msgid ":py:data:`~typing.Tuple`\\[:py:class:`str`, :py:data:`~typing.Any`]"
msgstr ""

#: of
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._evaluate_quality:4
msgid "The goodness of fit."
msgstr ""

#: of
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._extract_result:1
msgid "Extract analysis result."
msgstr ""

#: of
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis._visualization:1
msgid "Plot visualization."
msgstr ""

#: of
#: pyQCat.analysis.library.distortion_t1_composite_analysis.DistortionT1CompositeAnalysis.run_analysis:1
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis.run_analysis:1
msgid "Run analysis on experiment data."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:80
msgid "pyQCat.analysis.library.qubit\\_spectrum\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.qubit_spectrum_analysis:1
msgid "The analysis class for the Qubit Spectrum experiment."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis:1
#: pyQCat.analysis.library:39:<autosummary>:1
msgid "QubitSpectrum analysis class to judge noise and get peaks/dips."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._default_options:1
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._default_options:1
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._default_options:1
msgid "Create the QubitSpectrum Analysis options, and set some fields."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._evaluate_quality:1
msgid "Evaluates the quality of the background noise."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._evaluate_quality:3
msgid ""
":py:data:`~typing.Tuple`\\[:py:class:`str`, "
":py:class:`~pyQCat.structures.QDict`]"
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._evaluate_quality:4
msgid "The QDict object."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._extract_result:1
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:1
#: pyQCat.analysis.library.t1_analysis.T1Analysis._extract_result:1
msgid "Extract analysis results from important fit parameters."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._get_background_noise:1
msgid ""
"Get the background noise by signal-to-noise ratio, defined as the "
"amplitude of the peak divided by the square root of the median y-value "
"less the fit offset, greater than a threshold of two."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._get_background_noise:6
msgid "Formatted data collection to analysis."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._get_background_noise:9
msgid ""
"Indicates peaks or dips. values (np.ndarray): Indices of peaks in `x` "
"that satisfy all given                      conditions. SNR (float): "
"signal-to-noise ratio."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._get_background_noise:9
msgid ""
"Indicates peaks or dips. values (np.ndarray): Indices of peaks in `x` "
"that satisfy all given"
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._get_background_noise:11
msgid "conditions."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._get_background_noise:12
msgid "SNR (float): signal-to-noise ratio."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumPreAnalysis._visualization:1
msgid "Plot z_amp, frequency and T2* relationship."
msgstr ""

#: of pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis:1
#: pyQCat.analysis.library:39:<autosummary>:1
msgid ""
"A class to analyze a qubit spectrum peak with a square rooted Lorentzian "
"function."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._guess_fit_param:1
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._guess_fit_param:1
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:1
msgid "Create algorithmic guess with analysis options and curve data."
msgstr ""

#: of
#: pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis._extract_result:1
#: pyQCat.analysis.library.qubit_spectrum_analysis.QubitSpectrumAnalysis._extract_result:1
#: pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis._extract_result:1
#: pyQCat.analysis.library.t2ramsey_analysis.T2RamseyAnalysis._extract_result:1
msgid "Convert frequency from GHz to MHz."
msgstr ""

#: of pyQCat.analysis.library.qubit_spectrum_analysis.AnharmonicityAnalysis:1
#: pyQCat.analysis.library:39:<autosummary>:1
msgid ""
"A class to analyze two qubit spectrum peaks with a twin Lorentzian "
"function."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:88
msgid "pyQCat.analysis.library.rabi\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis:1
msgid "The analysis class for the Rabi experiment."
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis:1
#: pyQCat.analysis.library:39:<autosummary>:1
msgid "An analysis class for getting pi-pulse amplitude."
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis:4
msgid "Notes"
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis:5
msgid "Get Xpi as pi-pulse amplitude."
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1
#: pyQCat.analysis.library.rabi_analysis.RabiWidthAnalysis._default_options:1
#: pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis._default_options:1
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:1
#: pyQCat.analysis.library.t2ramsey_analysis.T2RamseyAnalysis._default_options:1
msgid "Return the default analysis options."
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._extract_result:1
msgid ""
"Extract Xpi from fitted data. The map of amp-phase to P0-P1 depends on "
"the location of the cavity frequency."
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiWidthAnalysis:1
#: pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis:1
#: pyQCat.analysis.library.t2ramsey_analysis.T2RamseyAnalysis:1
msgid ""
"Bases: "
":py:class:`~pyQCat.analysis.oscillation_analysis.DumpedOscillationAnalysis`"
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiWidthAnalysis._extract_result:1
msgid "Judge the fit data is oscillating or not."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:96
msgid "pyQCat.analysis.library.ramsey\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.ramsey_analysis:1
msgid "The analysis class for the Ramsey experiment."
msgstr ""

#: of pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis:1
#: pyQCat.analysis.library:39:<autosummary>:1
msgid ""
"An analysis class for getting oscillation frequency based on a fit of the"
" data to a cosine function."
msgstr ""

#: of pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis`"
msgstr ""

#: of pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis:1
#: pyQCat.analysis.library:39:<autosummary>:1
msgid "An analysis class for Ramsey experiment with z-amp added."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:11
msgid "Core Options:"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:4
msgid ""
"factor : Fluctuation expansion factor, Default 1, no expansion stft_res: "
"fs (ndarray) : Array of sample frequencies."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:6
msgid ""
"t (ndarray) : Array of segment times. Zxx (ndarray) : STFT of `x`. By "
"default, the last axis of `Zxx`"
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:8
msgid "corresponds to the segment times."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._default_options:9
msgid ""
"f (ndarray) :Array of sample frequencies. power (ndarray): half of length"
" of np.fft.fft fit data."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._extract_result:1
msgid "Convert frequency from GHz to MHz and judge osc."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._initialize_canvas:1
msgid "Initialize matplotlib canvas."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._visualization:1
msgid "Draw raw data, fit data and stft data."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._outlier_detectation:1
msgid "Detect outliers. :param phase: phase sequence."
msgstr ""

#: of
#: pyQCat.analysis.library.ramsey_analysis.RamseyZampAnalysis._outlier_detectation:4
msgid "True or False."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:104
msgid "pyQCat.analysis.library.rb\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis:1
#: pyQCat.analysis.library:39:<autosummary>:1
msgid "A class to analyze general randomized benchmarking experiment."
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:3
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:3
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:3
msgid "**Analysis Options**:"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:5
msgid "**fit_model (FitModel)** - Curve fitting model, the formula is as follows:"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:7
msgid ""
"y = {\\rm amp} \\cdot {\\rm base}^{\\left(x + {\\rm x0} \\right)} + {\\rm"
" baseline}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:10
msgid ""
"**x_label (str)** - The labels of the X-axis in the resulting graph, "
"default is `Number of cliffords`"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:13
msgid ""
"**quality_bounds (List)** - Fit quality evaluation metrics, default is "
"`[0.9, 0.85, 0.77]`"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:15
msgid "**depths (List)** - RB scan depth, determined by experiment"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:17
msgid ""
"**k (int)** - Random number of times at each depth, determined by "
"experiment."
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:19
msgid ""
"**result_parameters (List)** - Expected Extracted Experimental Results, "
"default is `['rc', 'rg', \"fidelity\"]`."
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:22
msgid "RB experiment analysis options."
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:3
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._extract_result:3
msgid "We do as follows:"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:5
msgid ""
"Use the mean of the data excluding the first 5 points as the initial "
"`baseline`"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:6
msgid ""
"The difference between the first y value and the baseline is used as the "
"initial `amp`"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:7
msgid "`base` defaults to 0.9"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._create_analysis_data:1
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data:6
msgid "Create Analysis data provided for detailed analyze."
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._create_analysis_data:5
msgid ""
"Since the RB experiment will repeat k times of experiments at a certain "
"depth m, it is necessary to reshape the experimental results."
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._create_analysis_data:8
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data:8
msgid ":py:class:`~pyQCat.structures.QDict`"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._create_analysis_data:9
msgid ""
"A QDict object, key represents data type and value is CurveAnalysisData "
"object."
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:3
msgid "The error rate of a clifford individual is:"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:5
msgid "rc = \\frac{1 - base}{2}"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:9
msgid ""
"An average individual in a single-bit Clifford cluster contains 1.875 "
"single gates, so the single-bit gate error rate is calculated as follows:"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:12
msgid "rg = \\frac{rc}{1.875}"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:16
msgid "So the basic book fidelity is calculated as follows:"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:18
msgid "fidelity = 1 - rg"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._visualization:1
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._visualization:1
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._visualization:1
msgid "Visualization of experimental results"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._visualization:3
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._visualization:3
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._visualization:3
msgid "We will draw four graphs:"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._visualization:5
msgid "Plot a scatter plot of repeated experiments at each depth;"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._visualization:6
msgid "Plot the frequency sampling results at each depth and fit;"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._visualization:7
msgid "Plot Fidelity Calculation Results Title;"
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:112
msgid "pyQCat.analysis.library.readout\\_freq\\_cali\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.readout_freq_cali_analysis:1
msgid "Readout frequency calibrate analysis."
msgstr ""

#: of
#: pyQCat.analysis.library.readout_freq_cali_analysis.ReadoutFreqCaliAnalysis._default_options:4
msgid ""
"distance_flag (bool): True means use distance opt probe freq. "
"diff_threshold (float): Twice cavity frequency difference."
msgstr ""

#: of
#: pyQCat.analysis.library.readout_freq_cali_analysis.ReadoutFreqCaliAnalysis._extract_result:1
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._extract_result:1
msgid ""
"Extract cavity frequency from twice cavity frequency spectrum experiment "
"data."
msgstr ""

#: of
#: pyQCat.analysis.library.readout_freq_cali_analysis.ReadoutFreqCaliAnalysis._visualization:1
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._visualization:1
msgid "Plot twice cavity result and distance."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:120
msgid "pyQCat.analysis.library.single\\_shot\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.single_shot_analysis:1
msgid "SingleShot Analysis."
msgstr ""

#: of pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.top_analysis.TopAnalysis`"
msgstr ""

#: of pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis.drawer:1
msgid "A short-cut for curve drawer instance."
msgstr ""

#: of pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis.drawer:3
msgid ":py:class:`~pyQCat.analysis.visualization.curve_drawer.CurveDrawer`"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options:12
msgid "Analysis Options:"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options:4
msgid ""
"n_clusters (int): Preset clustering number. method (str): Clustering "
"algorithm type. `GMM` or `KMeans` n_multiple (float): Calculate cluster "
"radius,"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options:7
msgid "multiple of standard deviation. Default set `3.0`."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options:9
msgid ""
"is_plot (bool): Set ``True`` to create figure for fit result. "
"quality_bounds (Tuple): Boundary of success conditions. result_parameters"
" (List): result data key list."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data:5
msgid "{"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data:3
msgid "'q0': data0, # np.array(I0, Q0, I1, Q1) 'q1': data1, 'c0': data2 ..."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data:7
msgid "}"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data:9
msgid "A QDict object, key represents data type and value is"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._run_training:1
msgid "Training analysis_data source_data."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._evaluate_quality:1
msgid "Parse analysis data Classification Quality."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:128
msgid "pyQCat.analysis.library.single\\_shot\\_composite\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.single_shot_composite_analysis:1
msgid "SingleShot Composite Analysis."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._default_options:4
msgid ""
"fidelity_threshold (List): Threshold of F0, F1. outlier (float): Set "
"outlier threshold value. distance_flag (bool): True means use center-"
"distance"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._default_options:7
msgid "to optimize target field."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._get_status:1
msgid "According to optimize_field, specific logic return status."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis._get_target_index:1
msgid "Find optimize_field the best index."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_composite_analysis.ReadoutPowerCaliAnalysis:1
#: pyQCat.analysis.library.single_shot_composite_analysis.SampleWidthOptimizeAnalysis:1
msgid ""
"Bases: "
":py:class:`~pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis`"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_composite_analysis.ReadoutPowerCaliAnalysis:1
#: pyQCat.analysis.library:39:<autosummary>:1
msgid "ReadoutPowerCaliAnalysis inherit SingleShotCompositeAnalysis."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_composite_analysis.ReadoutPowerCaliAnalysis._get_status:1
msgid "Readout power optimize get status logic."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_composite_analysis.SampleWidthOptimizeAnalysis:1
#: pyQCat.analysis.library:39:<autosummary>:1
msgid "SampleWidthOptimizeAnalysis."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_composite_analysis.SampleWidthOptimizeAnalysis._get_status:1
msgid "Sample width optimize get status logic."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:136
msgid "pyQCat.analysis.library.t1\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.t1_analysis:1
msgid "T1 Analysis."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:144
msgid "pyQCat.analysis.library.t1\\_spectrum\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.t1_spectrum_analysis:1
msgid "T1 Spectrum analysis."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:5
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:5
msgid ""
"**freq_list (List, array)** - List of frequencies calculated from the AC "
"spectrum, Used to plot the relationship between qubit frequency and T1."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:8
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:8
msgid ""
"**r_square_threshold (float)** - T1 experiment goodness-of-fit threshold,"
" used to extract abnormal sub-experiments."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:11
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:11
msgid ""
"**rate_threshold (float)** - The proportion of the decoherence time `t1` "
"to the maximum scan delay `max_delay` is used to extract abnormal sub-"
"experiments."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:14
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:14
msgid ""
"**subplots (Tuple)** - The layout of the drawing canvas of the "
"experimental results."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:16
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:16
msgid ""
"**x_label (List)** - The labels of the X-axis in the resulting graph, "
"corresponding to the number in `subplots`."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:19
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:19
msgid ""
"**y_label (List)** - The labels of the Y-axis in the resulting graph, "
"corresponding to the number in `subplots`."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:22
msgid ""
"**pcolormesh_options (Dict)** - Parameter selection for drawing "
"chromatogram, refer to `plt.pcolormesh()` method."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:25
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:22
msgid "**result_parameters (List)** - Expected Extracted Experimental Results."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:28
msgid "T1Spectrum experiment analysis options."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._extract_result:1
msgid "Extract experiment result."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._extract_result:5
msgid "Limit not showing abnormal points in title;"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._extract_result:6
msgid ""
"Scan the sub-experiment results and collect the sub-experiment "
"information whose goodness of fit and T1 do not meet the conditions;"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._extract_result:8
msgid "You will see abnormal AC, T1 and Frequceny in the results."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._visualization:5
msgid "The relationship between `AC` and `T1`;"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._visualization:6
msgid "Depth spectrogram between `AC` and `Delay`;"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._visualization:7
msgid "The relationship between `Frequncy` and `T1`;"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._visualization:8
msgid "Depth spectrogram between `Frequency` and `Delay`."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:152
msgid "pyQCat.analysis.library.t2\\_spectrum\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.t2_spectrum_analysis:1
msgid "T2 Spectrum analysis."
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._visualization:5
msgid "The relationship between `AC` and `T2`;"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._visualization:6
msgid "The relationship between `Frequncy` and `T2`;"
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:160
msgid "pyQCat.analysis.library.t2ramsey\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.t2ramsey_analysis:1
msgid "The analysis class for the T2Ramsey experiment."
msgstr ""

#: of pyQCat.analysis.library.t2ramsey_analysis.T2RamseyAnalysis:1
#: pyQCat.analysis.library:39:<autosummary>:1
msgid "T2 Ramsey result analysis class."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:168
msgid "pyQCat.analysis.library.tunable\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.tunable_analysis:1
msgid "Tunable modulation spectrum analysis."
msgstr ""

#: of
#: pyQCat.analysis.library.tunable_analysis.TunableAnalysis._extract_result:1
msgid "Extract DC Max Min from fitted data."
msgstr ""

#: of pyQCat.analysis.library.tunable_analysis.TunableAnalysis._visualization:1
msgid "Tunable plot depth, amp and phase."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:176
msgid "pyQCat.analysis.library.xyztiming\\_analysis module"
msgstr ""

#: of pyQCat.analysis.library.xyztiming_analysis:1
msgid "XYZTiming Analysis."
msgstr ""

#: ../../source/api/pyQCat.analysis.library.rst:184
msgid "Module contents"
msgstr ""

#: of pyQCat.analysis.library:3
msgid "Analysis Library  (:mod:`pyQCat.analysis.library`)"
msgstr ""

#: of pyQCat.analysis.library:5
msgid "Analysis submodule, collecting standard analysis modules."
msgstr ""

#: of pyQCat.analysis.library:8
msgid "Analysis Classes"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`ACSpectrumAnalysis "
"<pyQCat.analysis.library.ACSpectrumAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`AmpOptAnalysis <pyQCat.analysis.library.AmpOptAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`APEAnalysis <pyQCat.analysis.library.APEAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`APECompositeAnalysis "
"<pyQCat.analysis.library.APECompositeAnalysis>`\\ "
"\\(experiment\\_data\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`CavityAnalysis <pyQCat.analysis.library.CavityAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`CrosstalkAnalysis <pyQCat.analysis.library.CrosstalkAnalysis>`\\"
" \\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`DCSpectrumAnalysis "
"<pyQCat.analysis.library.DCSpectrumAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`DistortionT1Analysis "
"<pyQCat.analysis.library.DistortionT1Analysis>`\\ "
"\\(experiment\\_data\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`DistortionT1CompositeAnalysis "
"<pyQCat.analysis.library.DistortionT1CompositeAnalysis>`\\ "
"\\(experiment\\_data\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`QubitSpectrumAnalysis "
"<pyQCat.analysis.library.QubitSpectrumAnalysis>`\\ "
"\\(experiment\\_data\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`QubitSpectrumPreAnalysis "
"<pyQCat.analysis.library.QubitSpectrumPreAnalysis>`\\ "
"\\(experiment\\_data\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`AnharmonicityAnalysis "
"<pyQCat.analysis.library.AnharmonicityAnalysis>`\\ "
"\\(experiment\\_data\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`RabiAmpAnalysis <pyQCat.analysis.library.RabiAmpAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`RabiWidthAnalysis <pyQCat.analysis.library.RabiWidthAnalysis>`\\"
" \\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`RamseyAnalysis <pyQCat.analysis.library.RamseyAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`RamseyZampAnalysis "
"<pyQCat.analysis.library.RamseyZampAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`ReadoutFreqCaliAnalysis "
"<pyQCat.analysis.library.ReadoutFreqCaliAnalysis>`\\ "
"\\(experiment\\_data\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`SingleShotAnalysis "
"<pyQCat.analysis.library.SingleShotAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`ReadoutPowerCaliAnalysis "
"<pyQCat.analysis.library.ReadoutPowerCaliAnalysis>`\\ "
"\\(experiment\\_data\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`SampleWidthOptimizeAnalysis "
"<pyQCat.analysis.library.SampleWidthOptimizeAnalysis>`\\ "
"\\(experiment\\_data\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`T1Analysis <pyQCat.analysis.library.T1Analysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`T2RamseyAnalysis <pyQCat.analysis.library.T2RamseyAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`TunableAnalysis <pyQCat.analysis.library.TunableAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`T1SpectrumAnalysis "
"<pyQCat.analysis.library.T1SpectrumAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`T2SpectrumAnalysis "
"<pyQCat.analysis.library.T2SpectrumAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`XYZTimingAnalysis <pyQCat.analysis.library.XYZTimingAnalysis>`\\"
" \\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis.library:39:<autosummary>:1
msgid ""
":py:obj:`RBAnalysis <pyQCat.analysis.library.RBAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.analysis.curve_analysis.CurveAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.analysis.oscillation_analysis.OscillationAnalysis`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.analysis.oscillation_analysis.DumpedOscillationAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ "Draw all data, including raw data "
#~ "and fit data and so on. Some "
#~ "composite experiments may need to "
#~ "override this function."
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.analysis.top_analysis.TopAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ "freq_list (List, array): Corresponding to "
#~ "z_amp frequency list. r_square_threshold "
#~ "(float): To extract abnormal points,"
#~ msgstr ""

#~ msgid "set threshold of the analysis quality's r_square."
#~ msgstr ""

#~ msgid "rate_threshold (float): To extract abnormal points,"
#~ msgstr ""

#~ msgid "set threshold of the analysis results' rate."
#~ msgstr ""

#~ msgid "Plot z_amp, frequency and T1 relationship."
#~ msgstr ""

#~ msgid "freq_list (List, array): Corresponding to z_amp frequency list."
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`ACSpectrumAnalysis "
#~ "<pyQCat.analysis.library.ACSpectrumAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`AmpOptAnalysis "
#~ "<pyQCat.analysis.library.AmpOptAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`APEAnalysis <pyQCat.analysis.library.APEAnalysis>`\\"
#~ " \\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`APECompositeAnalysis "
#~ "<pyQCat.analysis.library.APECompositeAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CavityAnalysis "
#~ "<pyQCat.analysis.library.CavityAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CrosstalkAnalysis "
#~ "<pyQCat.analysis.library.CrosstalkAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`DCSpectrumAnalysis "
#~ "<pyQCat.analysis.library.DCSpectrumAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`DistortionT1Analysis "
#~ "<pyQCat.analysis.library.DistortionT1Analysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`DistortionT1CompositeAnalysis "
#~ "<pyQCat.analysis.library.DistortionT1CompositeAnalysis>`\\ "
#~ "\\(experiment\\_data\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`QubitSpectrumAnalysis "
#~ "<pyQCat.analysis.library.QubitSpectrumAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`QubitSpectrumPreAnalysis "
#~ "<pyQCat.analysis.library.QubitSpectrumPreAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`AnharmonicityAnalysis "
#~ "<pyQCat.analysis.library.AnharmonicityAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`RabiAmpAnalysis "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`RabiWidthAnalysis "
#~ "<pyQCat.analysis.library.RabiWidthAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`RamseyAnalysis "
#~ "<pyQCat.analysis.library.RamseyAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`RamseyZampAnalysis "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`ReadoutFreqCaliAnalysis "
#~ "<pyQCat.analysis.library.ReadoutFreqCaliAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`SingleShotAnalysis "
#~ "<pyQCat.analysis.library.SingleShotAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`ReadoutPowerCaliAnalysis "
#~ "<pyQCat.analysis.library.ReadoutPowerCaliAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`SampleWidthOptimizeAnalysis "
#~ "<pyQCat.analysis.library.SampleWidthOptimizeAnalysis>`\\ "
#~ "\\(experiment\\_data\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`T1Analysis <pyQCat.analysis.library.T1Analysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`T2RamseyAnalysis "
#~ "<pyQCat.analysis.library.T2RamseyAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`TunableAnalysis "
#~ "<pyQCat.analysis.library.TunableAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`T1SpectrumAnalysis "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`T2SpectrumAnalysis "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`XYZTimingAnalysis "
#~ "<pyQCat.analysis.library.XYZTimingAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.analysis.curve_analysis.CurveAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.analysis.oscillation_analysis.OscillationAnalysis`"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid "Now no fit model."
#~ msgstr ""

#~ msgid "APE Composite run analysis."
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.analysis.library.ac_spectrum_analysis.ACSpectrumAnalysis`"
#~ msgstr ""

#~ msgid "Quality of background noise."
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.analysis.oscillation_analysis.DumpedOscillationAnalysis`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.analysis.library.ramsey_analysis.RamseyAnalysis`"
#~ msgstr ""

#~ msgid "Plot a scatterplot of repeated experiments at each depth;"
#~ msgstr ""

#~ msgid "Run analysis."
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.analysis.top_analysis.TopAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ "Create Analysis result data structure to"
#~ " save analysis results. The AnalysisResult"
#~ " object will get more attributes "
#~ "after analysis."
#~ msgstr ""

#~ msgid ""
#~ "A QDict object, key represents result"
#~ " type and value is AnalysisResult "
#~ "object."
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.analysis.library.single_shot_composite_analysis.SingleShotCompositeAnalysis`"
#~ msgstr ""

#~ msgid "This experiment is currently not being evaluated for quality."
#~ msgstr ""

#~ msgid "Run analysis"
#~ msgstr ""

#~ msgid "initialize curve analysis with experiment data;"
#~ msgstr ""

#~ msgid "extract experiment results;"
#~ msgstr ""

#~ msgid "expeirment results visualization."
#~ msgstr ""

#~ msgid "This experiment does not extract experimental results."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`ACSpectrumAnalysis "
#~ "<pyQCat.analysis.library.ACSpectrumAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`AmpOptAnalysis <pyQCat.analysis.library.AmpOptAnalysis>`\\"
#~ " \\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`APEAnalysis <pyQCat.analysis.library.APEAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`APECompositeAnalysis "
#~ "<pyQCat.analysis.library.APECompositeAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CavityAnalysis <pyQCat.analysis.library.CavityAnalysis>`\\"
#~ " \\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CrosstalkAnalysis "
#~ "<pyQCat.analysis.library.CrosstalkAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`DCSpectrumAnalysis "
#~ "<pyQCat.analysis.library.DCSpectrumAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`DistortionT1Analysis "
#~ "<pyQCat.analysis.library.DistortionT1Analysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`DistortionT1CompositeAnalysis "
#~ "<pyQCat.analysis.library.DistortionT1CompositeAnalysis>`\\ "
#~ "\\(experiment\\_data\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`QubitSpectrumAnalysis "
#~ "<pyQCat.analysis.library.QubitSpectrumAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`QubitSpectrumPreAnalysis "
#~ "<pyQCat.analysis.library.QubitSpectrumPreAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`AnharmonicityAnalysis "
#~ "<pyQCat.analysis.library.AnharmonicityAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`RabiAmpAnalysis "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`RabiWidthAnalysis "
#~ "<pyQCat.analysis.library.RabiWidthAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`RamseyAnalysis <pyQCat.analysis.library.RamseyAnalysis>`\\"
#~ " \\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`RamseyZampAnalysis "
#~ "<pyQCat.analysis.library.RamseyZampAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`ReadoutFreqCaliAnalysis "
#~ "<pyQCat.analysis.library.ReadoutFreqCaliAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`SingleShotAnalysis "
#~ "<pyQCat.analysis.library.SingleShotAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`ReadoutPowerCaliAnalysis "
#~ "<pyQCat.analysis.library.ReadoutPowerCaliAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`SampleWidthOptimizeAnalysis "
#~ "<pyQCat.analysis.library.SampleWidthOptimizeAnalysis>`\\ "
#~ "\\(experiment\\_data\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`T1Analysis <pyQCat.analysis.library.T1Analysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`T2RamseyAnalysis "
#~ "<pyQCat.analysis.library.T2RamseyAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`TunableAnalysis "
#~ "<pyQCat.analysis.library.TunableAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`T1SpectrumAnalysis "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`T2SpectrumAnalysis "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`XYZTimingAnalysis "
#~ "<pyQCat.analysis.library.XYZTimingAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`RBAnalysis <pyQCat.analysis.library.RBAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

