# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/07/30
# __author:       <PERSON><PERSON><PERSON>

import numpy as np
from scipy.signal import savgol_filter

from ..errors import CurveFittingError
from ..log import pyqlog
from ..structures import Dict, List, Options, QDict, Union
from .fit.curve_fit import curve_fitting
from .specification import CurveAnalysisData, FitModel, FitOptions, GoodnessofFit
from .standard_curve_analysis import StandardCurveAnalysis


class CurveFitAnalysis(StandardCurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        # Set validator for particular option values.
        options.set_validator(field="fit_model", validator_value=FitModel)

        # Data filter
        # default method is data smooth.
        options.filter = None

        # Fit options.
        options.p0 = None
        options.bounds = {}
        options.curve_fit_extra = {
            "rmse_bound": 1e-3,
            "max_iteration": 1,
            "maxfev": 100000,
            "ftol": 1.49012e-4,
            "xtol": 1.49012e-4,
        }
        options.text_pos = {}

        # Quality options.
        # Every experiment has different quality bounds.
        options.quality_bounds = [0.98, 0.95, 0.85]

        return options

    @property
    def data_filter(self) -> Dict:
        """The parameters provided for data filter functions."""
        return self._options.filter

    def _create_analysis_data(self) -> QDict:
        """Create Analysis data provided for detailed analyze.

        Returns:
            A QDict object, key represents data type and value is
            CurveAnalysisData object.
        """
        analysis_data_dict = QDict()
        data_key = list(
            self.options.get("data_key", None) or self._experiment_data.y_data.keys()
        )
        for key in data_key:
            if key in self.experiment_data.y_data:
                analysis_data = CurveAnalysisData(
                    x=np.copy(self._experiment_data.x_data),
                    y=np.copy(self.experiment_data.y_data.get(key, [])),
                )
                analysis_data_dict[key] = analysis_data
        return analysis_data_dict

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,  # pylint: disable=unused-argument
    ) -> Union[FitOptions, List[FitOptions]]:
        """Create algorithmic guess with analysis options and curve data.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        return fit_opt

    def _evaluate_quality(self):
        """Evaluates the quality of the fit based on the fit result.

        Returns:
            The goodness of fit.
        """
        quality_dict = {}
        if self.options.get("fit_model"):
            for data_key, analysis_data in self.analysis_datas.items():  # type: ignore
                if analysis_data.fit_data is not None:
                    quality_dict[data_key] = analysis_data.fit_data.goodness_of_fit

        if quality_dict:
            sorted_quality = sorted(quality_dict.items(), key=lambda kv: kv[1].value)
            *_, better_quality = sorted_quality
            data_key, self._quality = better_quality
            self.experiment_data.metadata.process_meta.update(
                {"best_data_key": data_key}
            )

    def _extract_result(self):
        """Extract analysis results from important fit parameters.

        Args:
            data_key (str): The basis for selecting data.
        """
        best_data_key = self.experiment_data.metadata.process_meta.get("best_data_key")
        if best_data_key:
            analysis_data = self.analysis_datas[best_data_key]  # type: ignore
            for p_name, result in self.results.items():  # type: ignore
                # Sometimes the fit values are not the final result.
                # The target value needs to extract which depends on fit value.
                if p_name in analysis_data.fit_data.popt_keys:
                    target_val = analysis_data.fit_data.fitval(p_name)
                    result.value = target_val

    def _data_filter(self):
        """Pre-processing for raw data."""
        for analysis_data in self.analysis_datas.values():  # type: ignore
            smooth_y = savgol_filter(analysis_data.y, **self.data_filter)
            analysis_data.y = smooth_y

    def _run_fitting(self) -> bool:
        """Perform curve fitting on given data collection and fit models.

        Returns:
            fitting success or failed.
        """
        default_fit_opt = FitOptions(
            parameters=self.options.fit_model.signature,
            default_p0=self.options.p0,
            default_bounds=self.options.bounds,
            **self.options.curve_fit_extra,
        )
        fit_results = []
        for analysis_data in self.analysis_datas.values():  # type: ignore
            # Guess initial parameters.
            new_fit_opt = default_fit_opt.copy()
            guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)
            if isinstance(guess_fit_options, FitOptions):
                guess_fit_options = [guess_fit_options]

            # Run fit for each guessed parameters.
            sub_fit_results = []
            for guess_fit_option in set(guess_fit_options):
                try:
                    fit_data = curve_fitting(
                        analysis_data, guess_fit_option, self.options.fit_model
                    )
                    sub_fit_results.append(fit_data)
                except CurveFittingError as analysis_error:
                    # Some guesses might be too far from the true parameters and may thus fail.
                    # We ignore initial guesses that fail and continue with the next fit candidate.
                    # todo log remind fitting failed
                    pyqlog.warning(f"<Curve Fitting Error> | {analysis_error}")
                    pass

            # fixed bug:
            fit_results += sub_fit_results

            if sub_fit_results:
                # Find best value with r-square value.
                for fit_data in sub_fit_results:
                    fit_data.goodness_of_fit = GoodnessofFit(
                        *self.options.quality_bounds
                    )
                    fit_data.goodness_of_fit.evaluate(analysis_data.y, fit_data.y_fit)
                analysis_data.fit_data = sorted(
                    sub_fit_results, key=lambda r: r.goodness_of_fit.value
                )[-1]

        # if all guess parameters fitting failed, return false.
        if len(fit_results) == 0:
            pyqlog.warning(
                "All initial guesses and parameter boundaries failed to fit the data. "
                "Please provide better initial guesses or fit parameter boundaries.",
            )
            # at least return raw data points rather than terminating
            return False
        else:
            return True

    def _visualization(self):
        """Draw all data, including raw data and fit data and so on.
        Some composite experiments may need to override this function.
        """
        # super standard visualization
        super()._visualization()

        # get experiment keys.
        exp_keys = list(self.experiment_data.y_data.keys())

        # plot fit data and filtered data.
        for data_key, analysis_data in self.analysis_datas.items():  # type: ignore
            # Sometimes, only one set of the raw data needs to be fitted.
            # So we need to figure out on which axis is the result of the fit drawn.
            draw_index = exp_keys.index(data_key)
            analysis_x = analysis_data.x
            if self.options.get("fit_model"):
                analysis_fit = analysis_data.fit_data
                if analysis_fit is not None:
                    fit_data = analysis_fit.y_fit
                    self.drawer.draw_fit_line(
                        x_data=analysis_x, y_data=fit_data, ax_index=draw_index
                    )
            if self.options.filter:
                filtered_data = analysis_data.y
                self.drawer.draw_filter_data(
                    x_data=analysis_x, y_data=filtered_data, ax_index=draw_index
                )

            # plot annotations.
            if self.options.text_pos and data_key in self.options.text_pos:
                text_pos, text_rp = self.options.text_pos[data_key]
                self.drawer.options.text_pos = text_pos
                self.drawer.options.text_rp = text_rp
                self.drawer.draw_text(ax_index=draw_index)
            elif self.drawer.options.text_pos and self.drawer.options.text_rp:
                if data_key in self.drawer.options.text_key:
                    self.drawer.draw_text(ax_index=draw_index)

    def _data_processing(self):
        # filter data.
        if self.data_filter:
            self._data_filter()

        # Run data fitting.
        if self.options.get("fit_model"):
            res = self._run_fitting()
            # records fitting states
            self.update_analysis_state("fit", res)
