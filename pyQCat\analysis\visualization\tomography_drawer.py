# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/20
# __author:       <PERSON><PERSON><PERSON>

from typing import Union, List

import matplotlib.pyplot as plt
import numpy as np
from matplotlib import cm, colorbar, colors
from numpy.core.umath import arctan2

from .base_drawer import BaseCurveDrawer, get_tomo_fig
from ...errors import AnalysisDrawerError
from ...structures import Options


class TomographyDrawer(BaseCurveDrawer):
    """Tomography drawer for MatplotLib backend."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        **Analysis Options**:
            
            - **labels (List)** - Subplot title, default is ``[real, image]``.
            
            - **figsize (<PERSON>ple)** - Canvas size, defaultn is ``(14, 7)``
            
            - **sup_title (str)** - Top-level canvas title, ``QPT`` or ``QST``

        Returns:
            Tomography drawer options.
        """
        options = super()._default_options()
        options.title = ["real", "image"]
        options.figsize = (14, 7)
        options.sup_title = "QST"

        return options

    def initialize_canvas(self):
        """Initialize the drawing canvas."""
        figure = get_tomo_fig(self.options.subplots)
        self._figure = figure
        self._figure.set_size_inches(*self.options.figsize)

    def format_canvas(self):
        """Add a title to the canvas."""
        self.figure.suptitle(self.options.sup_title)

    def draw_matrix_divide(self, matrix: Union[np.ndarray, List], start: int = 0, row: int = 0, col: int = 0):
        """Draw matrix.

        Args:
            matrix (Union[np.ndarray, List]): goal matrix.
            start (int): start index.
            row (int): row num.
            col (int): col num.

        Raises:
            TomographyDrawerError: wrong dimension
        """
        datas = []

        if not isinstance(matrix, List):
            matrix = [matrix]

        for m in matrix:
            datas.append(np.real(m))
            datas.append(np.imag(m))

        n = np.size(datas[0])
        x_pos, y_pos = np.meshgrid(range(datas[0].shape[0]), range(datas[0].shape[1]))
        x_pos = x_pos.T.flatten() + 0.2
        y_pos = y_pos.T.flatten() + 0.2
        z_pos = np.zeros(n)
        dx = dy = 0.6 * np.ones(n)

        if not row or not col:
            row = (len(datas) - 1) // 2 + 1
            col = 2

        for i, data in enumerate(datas):
            dz = data.flatten()
            z_min = min(dz)
            z_max = max(dz)
            if z_min == z_max:
                z_min -= 0.1
                z_max += 0.1
            dz -= z_min

            norm = colors.Normalize(z_min, z_max)
            color_map = cm.get_cmap('jet')
            color = color_map(norm(dz))

            ax = self._figure.add_subplot(row, col, start + i + 1, projection='3d')
            ax.bar3d(x_pos, y_pos, z_pos, dx, dy, dz, color=color)
            ax.set_title(self.options.title[start + i], x=0.6, y=1, fontsize=10)

            # x axis
            x_tics = np.arange(data.shape[0])
            ax.axes.w_xaxis.set_major_locator(plt.FixedLocator(x_tics))
            if self.options.xlabels:
                nx_labels = len(self.options.xlabels)
                if nx_labels != len(x_tics):
                    raise AnalysisDrawerError(f"got {nx_labels} xlabels, but needed {len(x_tics)}")
                ax.set_xticklabels(self.options.xlabels)
            ax.tick_params(axis='x', labelsize=8)
            ax.set_xlim3d([x_tics[0] - 0.2, x_tics[-1] + 1.2])

            # y axis
            y_tics = np.arange(data.shape[1])
            ax.axes.w_yaxis.set_major_locator(plt.FixedLocator(y_tics))
            if self.options.ylabels:
                ny_labels = len(self.options.ylabels)
                if ny_labels != len(y_tics):
                    raise AnalysisDrawerError(f"got {ny_labels} ylabels but needed {len(y_tics)}")
                ax.set_yticklabels(self.options.ylabels)
            ax.tick_params(axis='y', labelsize=8)
            ax.set_ylim3d([y_tics[0] - 0.2, y_tics[-1] + 1.2])

            # z axis
            ax.axes.w_zaxis.set_major_locator(plt.IndexLocator(1, 0.5))
            ax.set_zlim3d([0, z_max - z_min])

            # color axis
            cax, kw = colorbar.make_axes(ax, shrink=0.5, pad=0.1)
            colorbar.ColorbarBase(cax, cmap=color_map, norm=norm)

    def draw_matrix(self, matrix: Union[np.ndarray, List], start: int = 0, row: int = 0, col: int = 0):
        """Draw matrix.

        Args:
            matrix (Union[np.ndarray, List]): goal matrix.
            start (int): start index.
            row (int): row num.
            col (int): col num.

        Raises:
            TomographyDrawerError: wrong dimension
        """

        def _complex_phase_cmap():
            """
            Create a cyclic colormap for representing the phase of complex variables

            Returns
            -------
            cmap :
                A matplotlib linear segmented colormap.
            """
            cdict = {'blue': ((0.00, 0.0, 0.0),
                              (0.25, 0.0, 0.0),
                              (0.50, 1.0, 1.0),
                              (0.75, 1.0, 1.0),
                              (1.00, 0.0, 0.0)),
                     'green': ((0.00, 0.0, 0.0),
                               (0.25, 1.0, 1.0),
                               (0.50, 0.0, 0.0),
                               (0.75, 1.0, 1.0),
                               (1.00, 0.0, 0.0)),
                     'red': ((0.00, 1.0, 1.0),
                             (0.25, 0.5, 0.5),
                             (0.50, 0.0, 0.0),
                             (0.75, 0.0, 0.0),
                             (1.00, 1.0, 1.0))}

            return colors.LinearSegmentedColormap('phase_colormap', cdict, 256)

        def _angle(z):
            z = np.asarray(z)
            z_real = z.real
            z_imag = z.imag
            a = arctan2(z_imag, z_real)
            return a

        datas = []

        if not isinstance(matrix, List):
            matrix = [matrix]

        for m in matrix:
            datas.append(m)

        if not row or not col:
            total = len(matrix)
            row, col = (total - 1) // 2 + 1, 2

        n = np.size(datas[0])
        x_pos, y_pos = np.meshgrid(range(datas[0].shape[0]), range(datas[0].shape[1]))
        # x_pos = x_pos.T.flatten() - 0.5
        # y_pos = y_pos.T.flatten() - 0.5
        x_pos = x_pos.T.flatten()
        y_pos = y_pos.T.flatten()
        z_pos = np.zeros(n)
        dx = dy = 0.8 * np.ones(n)

        for i, data in enumerate(datas):
            mvec = np.array(data).flatten()
            dz = abs(mvec)
            idx, = np.where(abs(mvec) < 0.001)
            mvec[idx] = abs(mvec[idx])

            phase_min = -np.pi
            phase_max = np.pi
            norm = colors.Normalize(phase_min, phase_max)
            cmap = _complex_phase_cmap()
            color = cmap(norm(_angle(mvec)))

            # ax = self._figure.add_subplot(row, col, start + i + 1, projection='3d', azim=-35, elev=35)
            ax = self._figure.add_subplot(row, col, start + i + 1, projection='3d')
            ax.bar3d(x_pos, y_pos, z_pos, dx, dy, dz, color=color)
            ax.set_title(self.options.title[start + i], x=0.6, y=1, fontsize=10)

            # x axis
            x_tics = np.arange(data.shape[0])
            ax.axes.w_xaxis.set_major_locator(plt.FixedLocator(x_tics))
            if self.options.xlabels:
                nx_labels = len(self.options.xlabels)
                if nx_labels != len(x_tics):
                    raise AnalysisDrawerError(f"got {nx_labels} xlabels but needed {len(x_tics)}")
                ax.set_xticklabels(self.options.xlabels)
            ax.tick_params(axis='x', labelsize=8)
            ax.set_xlim3d([x_tics[0] - 0.2, x_tics[-1] + 1.2])

            # y axis
            y_tics = np.arange(data.shape[1])
            ax.axes.w_yaxis.set_major_locator(plt.FixedLocator(y_tics))
            if self.options.ylabels:
                ny_labels = len(self.options.ylabels)
                if ny_labels != len(y_tics):
                    raise AnalysisDrawerError(f"got {ny_labels} ylabels but needed {len(y_tics)}")
                ax.set_yticklabels(self.options.ylabels)
            ax.tick_params(axis='y', labelsize=8)
            ax.set_ylim3d([y_tics[0] - 0.2, y_tics[-1] + 1.2])

            # z axis
            ax.axes.w_zaxis.set_major_locator(plt.IndexLocator(1, 0.5))
            ax.set_zlim3d([0, 1])

            # color axis
            cax, kw = colorbar.make_axes(ax, shrink=0.5, pad=0.1)
            colorbar.ColorbarBase(cax, cmap=cmap, norm=norm)
