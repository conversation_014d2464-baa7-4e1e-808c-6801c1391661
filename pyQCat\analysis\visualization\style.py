# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/05/24
# __author:       <PERSON><PERSON><PERSON> Shi
"""
Configurable stylesheet.
"""
import dataclasses
from typing import Tuple, List


@dataclasses.dataclass
class PlotterStyle:
    """A stylesheet for curve analysis figure."""

    # size of figure (width, height)
    figsize: Tuple[int, int] = (8, 5)

    # legent location (vertical, horizontal)
    legend_loc: str = "center right"

    # size of tick label
    tick_label_size: int = 14

    # size of axis label
    axis_label_size: int = 16

    # relative position of fit report
    fit_report_rpos: Tuple[float, float] = (0.6, 0.95)

    # size of fit report text
    fit_report_text_size: int = 14

    # sigma values for confidence interval, which are the tuple of (sigma, alpha).
    # the alpha indicates the transparency of the corresponding interval plot.
    plot_sigma: List[Tuple[float, float]] = dataclasses.field(
        default_factory=lambda: [(1.0, 0.7), (3.0, 0.3)])
    
    __annotations__ = {
        "figsize": Tuple[int, int],
        "legend_loc": str,
        "tick_label_size": int,
        "axis_label_size": int,
        "fit_report_rpos": Tuple[float, float],
        "fit_report_text_size": int,
        "plot_sigma": List[Tuple[float, float]]
    }
