# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/04/20
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy
from typing import List

import numpy as np

from ....pulse.pulse_function import half_pi_pulse, pi_pulse, zero_pulse
from ....pulse.pulse_lib import Constant
from .ramsey import Ramsey, RamseyZZ


class SpinEcho(Ramsey):
    @staticmethod
    def get_xy_pulse(qubit, delays: List, fringe: float, offset: float = 0.0):
        """Get XY line wave."""
        pulse_list = []
        for delay in delays:
            left_delay_time = round(delay / 2, 4)
            right_delay_time = delay - left_delay_time

            front_drag = half_pi_pulse(qubit)
            left_delay = Constant(left_delay_time + offset, 0, "XY")
            mid_drag = pi_pulse(qubit)
            right_delay = Constant(right_delay_time + offset, 0, "XY")
            rear_drag = deepcopy(front_drag)

            exp_pulse = (
                front_drag()
                + left_delay()
                + mid_drag()
                + right_delay()
                + rear_drag(phase=2 * np.pi * fringe * 1e6 / 1e9 * delay)
            )
            exp_pulse.bit = qubit.name
            exp_pulse.sweep = "sweep delay"

            pulse_list.append(exp_pulse)

        return pulse_list

    @staticmethod
    def get_z_pulse(qubit, delays: List, z_amp: float):
        z_pulse_list = []

        x2_width = half_pi_pulse(qubit).width
        x_width = pi_pulse(qubit).width
            
        for delay in delays:
            left_delay_time = round(delay / 2, 4)
            right_delay_time = delay - left_delay_time

            front_drag = Constant(x2_width, 0)
            left_delay = Constant(left_delay_time, z_amp)
            mid_drag = Constant(x_width, 0)
            right_delay = Constant(right_delay_time, z_amp)
            rear_drag = Constant(x2_width, 0)

            exp_pulse = (
                front_drag() + left_delay() + mid_drag() + right_delay() + rear_drag()
            )
            z_pulse_list.append(exp_pulse)

        return z_pulse_list

    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        xy_pulse_list = SpinEcho.get_xy_pulse(
            builder.qubit,
            builder.experiment_options.delays,
            builder.experiment_options.fringe,
        )
        builder.play_pulse("XY", builder.qubit, xy_pulse_list)

    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""
        if builder.experiment_options.z_amp is not None:
            z_pulse_list = SpinEcho.get_z_pulse(
                builder.qubit,
                builder.experiment_options.delays,
                builder.experiment_options.z_amp,
            )
            builder.play_pulse("Z", builder.qubit, z_pulse_list)

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "tau":
                result.extra["path"] = (
                    "Coupler.TS2" if self.is_coupler_exp is True else "Qubit.TS2"
                )


class SpinEchoZZ(SpinEcho, RamseyZZ):
    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        ramsey_qubit = builder.run_options.ramsey_qubit
        drag_qubit = builder.run_options.drag_qubit

        ramsey_qubit_x_width = pi_pulse(ramsey_qubit)().width
        drag_qubit_x_width = pi_pulse(drag_qubit)().width

        ramsey_xy_pulses = SpinEcho.get_xy_pulse(
            ramsey_qubit,
            builder.experiment_options.delays,
            builder.experiment_options.fringe,
            (drag_qubit_x_width - ramsey_qubit_x_width) / 2,
        )
        builder.play_pulse("XY", ramsey_qubit, ramsey_xy_pulses)

        delays = builder.experiment_options.delays
        drag_pulse_list = []
        for delay in delays:
            left_delay_time = round(delay / 2, 4)
            right_delay_time = delay - left_delay_time

            front_drag = zero_pulse(ramsey_qubit)
            left_delay = Constant(left_delay_time, 0, "XY")
            mid_drag = pi_pulse(drag_qubit)
            right_delay = Constant(right_delay_time, 0, "XY")
            rear_drag = deepcopy(front_drag)

            exp_pulse = (
                front_drag() + left_delay() + mid_drag() + right_delay() + rear_drag()
            )

            exp_pulse.bit = drag_qubit.name
            exp_pulse.sweep = "sweep delay"
            drag_pulse_list.append(exp_pulse)
        builder.play_pulse("XY", drag_qubit, drag_pulse_list)

        for qubit in builder.qubits:
            if qubit.name not in [ramsey_qubit.name, drag_qubit.name]:
                pulses = [
                    Constant(pulse.width, 0, name="XY")() for pulse in drag_pulse_list
                ]
                builder.play_pulse("XY", qubit, pulses)

    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""

        if builder.experiment_options.z_amp is not None:
            z_pulse_list = SpinEcho.get_z_pulse(
                builder.run_options.amp_coupler,
                builder.experiment_options.delays,
                builder.experiment_options.z_amp,
            )
            builder.play_pulse("Z", builder.run_options.amp_coupler, z_pulse_list)
