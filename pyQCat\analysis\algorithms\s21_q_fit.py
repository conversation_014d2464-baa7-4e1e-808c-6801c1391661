# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/08/21
# __author:       <PERSON><PERSON><PERSON>

import warnings

import numpy as np
import scipy.optimize as sopt
import scipy.optimize as spopt
from scipy import stats
from scipy.constants import hbar


class QNotchPort(object):
    """
    notch type port probed in transmission
    """

    def __init__(self, f_data=None, z_data_raw=None):
        self.port_type = "notch"
        self.fit_result = {}
        self.z_data = None

        if f_data is not None:
            self.f_data = np.array(f_data)
        else:
            self.f_data = None
        if z_data_raw is not None:
            self.z_data_raw = np.array(z_data_raw)
        else:
            self.z_data_raw = None

    def get_delay(self, f_data, z_data, delay=None, guess=True):
        """
        retrieves the cable delay assuming the ideal resonance has a circular shape
        modifies the cable delay until the shape Im(S21) vs Re(S21) is circular
        see "do_calibration"
        :param f_data: scan frequency
        :param z_data: S21 data
        :param delay: cable delay
        :param guess:
        :return:
        """
        maxvalue = np.max(np.absolute(z_data))
        z_data = z_data / maxvalue
        a1, a2, a3, a4, fr, ql = self._fit_skewed_lorentzian(f_data, z_data)
        a2 = 0
        if delay is None:
            if guess is True:
                delay = self._guess_delay(f_data, z_data)
            else:
                delay = 0.0
            delay = self._fit_delay(f_data, z_data, delay, maxiter=200)
        params = [a1, a2, a3, a4, fr, ql]
        return delay, params

    def do_calibration(
        self,
        f_data,
        z_data,
        guess_delay=True,
        fixed_delay=None,
        ql_guess=None,
        fr_guess=None,
    ):
        """
        performs an automated calibration and tries to determine the prefactors a, alpha, delay
        fr, Ql, and a possible slope are extra information, which can be used as start parameters for subsequent fits
        see also "do_normalization"
        the calibration procedure works for transmission line resonators as well
        :param f_data: scan frequency
        :param z_data: S21_raw_data
        """
        delay, params = self.get_delay(
            f_data, z_data, guess=guess_delay, delay=fixed_delay
        )
        # data calibration
        z_data = (z_data - params[1] * (f_data - params[4])) * np.exp(
            2.0 * 1j * np.pi * delay * f_data
        )
        xc, yc, r0 = self._fit_circle(z_data)
        zc = complex(xc, yc)
        if ql_guess is None:
            ql_guess = np.absolute(params[5])
        if fr_guess is None:
            fr_guess = params[4]
        fitparams = self._phase_fit(
            f_data, self._center(z_data, zc), 0.0, ql_guess, fr_guess
        )
        theta, ql, fr = fitparams
        beta = self._periodic_boundary(theta + np.pi, np.pi)
        offrespoint = complex((xc + r0 * np.cos(beta)), (yc + r0 * np.sin(beta)))
        alpha = np.angle(offrespoint)
        a = np.absolute(offrespoint)
        return delay, a, alpha, fr, ql, params[1], params[4]

    def do_normalization(self, f_data, z_data, delay, amp_norm, alpha, a2, frcal):
        """
        removes the prefactors a, alpha, delay and returns the calibrated data, see also "do_calibration"
        works also for transmission line resonators
        """
        return (
            (z_data - a2 * (f_data - frcal))
            / amp_norm
            * np.exp(1j * (-alpha + 2.0 * np.pi * delay * f_data))
        )

    def circlefit(self, f_data, z_data, fr=None, ql=None, calc_errors=True):
        """
        performs a circle fit on a frequency vs. complex resonator scattering data set
        Data has to be normalized!!
        INPUT:
        f_data,z_data: input data (frequency, complex S21 data)
        OUTPUT:
        outpus a dictionary {key:value} consisting of the fit values, errors and status information about the fit
        values: {"phi0":phi0, "Ql":Ql, "absolute(Qc)":absQc, "Qi": Qi, "electronic_delay":delay, "complexQc":complQc, "resonance_freq":fr, "prefactor_a":a, "prefactor_alpha":alpha}
        errors: {"phi0_err":phi0_err, "Ql_err":Ql_err, "absolute(Qc)_err":absQc_err, "Qi_err": Qi_err, "electronic_delay_err":delay_err, "resonance_freq_err":fr_err, "prefactor_a_err":a_err, "prefactor_alpha_err":alpha_err}
        for details, see:
            [1] (not diameter corrected) Jiansong Gao, "The Physics of Superconducting Microwave Resonators" (PhD Thesis), Appendix E, California Institute of Technology, (2008)
            [2] (diameter corrected) M. S. Khalil, et. al., J. Appl. Phys. 111, 054510 (2012)
            [3] (fitting techniques) N. CHERNOV AND C. LESORT, "Least Squares Fitting of Circles", Journal of Mathematical Imaging and Vision 23, 239, (2005)
            [4] (further fitting techniques) P. J. Petersan, S. M. Anlage, J. Appl. Phys, 84, 3392 (1998)
        the program fits the circle with the algebraic technique described in [3], the rest of the fitting is done with the scipy.optimize least square fitting toolbox
        also, check out [5] S. Probst et al. "Efficient and reliable analysis of noisy complex scatterung resonator data for superconducting quantum circuits" (in preparation)
        """

        if fr is None:
            fr = f_data[np.argmin(np.absolute(z_data))]
        if ql is None:
            ql = 1e6
        xc, yc, r0 = self._fit_circle(z_data)
        phi0 = -np.arcsin(yc / r0)
        theta0 = self._periodic_boundary(phi0 + np.pi, np.pi)
        z_data_corr = self._center(z_data, complex(xc, yc))
        theta0, Ql, fr = self._phase_fit(f_data, z_data_corr, theta0, ql, fr)
        # print("Ql from phasefit is: " + str(Ql))
        absQc = Ql / (2.0 * r0)
        complQc = absQc * np.exp(1j * ((-1.0) * phi0))
        Qc = (
            1.0 / (1.0 / complQc).real
        )  # here, taking the real part of (1/complQc) from diameter correction method
        Qi_dia_corr = 1.0 / (1.0 / Ql - 1.0 / Qc)
        Qi_no_corr = 1.0 / (1.0 / Ql - 1.0 / absQc)

        results = {
            "Qi_dia_corr": Qi_dia_corr,
            "Qi_no_corr": Qi_no_corr,
            "absQc": absQc,
            "Qc_dia_corr": Qc,
            "Ql": Ql,
            "fr": fr,
            "theta0": theta0,
            "phi0": phi0,
        }

        # calculation of the error
        p = [fr, absQc, Ql, phi0]
        # chi_square, errors = rt.get_errors(rt.residuals_notch_ideal,f_data,z_data,p)
        if calc_errors == True:
            chi_square, cov = self._get_cov_fast_notch(f_data, z_data, p)
            # chi_square, cov = rt.get_cov(rt.residuals_notch_ideal,f_data,z_data,p)

            if cov is not None:
                errors = np.sqrt(np.diagonal(cov))
                fr_err, absQc_err, Ql_err, phi0_err = errors
                # calc Qi with error prop (sum the squares of the variances and covariaces)
                dQl = 1.0 / ((1.0 / Ql - 1.0 / absQc) ** 2 * Ql**2)
                dabsQc = -1.0 / ((1.0 / Ql - 1.0 / absQc) ** 2 * absQc**2)
                Qi_no_corr_err = np.sqrt(
                    (dQl**2 * cov[2][2])
                    + (dabsQc**2 * cov[1][1])
                    + (2 * dQl * dabsQc * cov[2][1])
                )  # with correlations
                # calc Qi dia corr with error prop
                dQl = 1 / ((1 / Ql - np.cos(phi0) / absQc) ** 2 * Ql**2)
                dabsQc = -np.cos(phi0) / (
                    (1 / Ql - np.cos(phi0) / absQc) ** 2 * absQc**2
                )
                dphi0 = -np.sin(phi0) / ((1 / Ql - np.cos(phi0) / absQc) ** 2 * absQc)
                ##err1 = ( (dQl*cov[2][2])**2 + (dabsQc*cov[1][1])**2 + (dphi0*cov[3][3])**2 )
                err1 = (
                    (dQl**2 * cov[2][2])
                    + (dabsQc**2 * cov[1][1])
                    + (dphi0**2 * cov[3][3])
                )
                err2 = (
                    dQl * dabsQc * cov[2][1]
                    + dQl * dphi0 * cov[2][3]
                    + dabsQc * dphi0 * cov[1][3]
                )
                Qi_dia_corr_err = np.sqrt(err1 + 2 * err2)  # including correlations
                errors = {
                    "phi0_err": phi0_err,
                    "Ql_err": Ql_err,
                    "absQc_err": absQc_err,
                    "fr_err": fr_err,
                    "chi_square": chi_square,
                    "Qi_no_corr_err": Qi_no_corr_err,
                    "Qi_dia_corr_err": Qi_dia_corr_err,
                }
                results.update(errors)
            else:
                print("WARNING: Error calculation failed!")
        else:
            # just calc chisquared:
            fun2 = lambda x: self._residuals_notch_ideal(x, f_data, z_data) ** 2
            chi_square = 1.0 / float(len(f_data) - len(p)) * (fun2(p)).sum()
            errors = {"chi_square": chi_square}
            results.update(errors)

        return results

    def _fit_skewed_lorentzian(self, f_data, z_data):
        """
        fit abs(S21)**2 versus f, but will consider a linear shift. A2 is considered the slope parameter.
        :param f_data: scan frequency
        :param z_data: S21 data
        :return: Fitting coefficient：A1,A2,A3,A4,fr,Ql.
        """
        amplitude = np.absolute(z_data)
        amplitude_sqr = amplitude**2
        A1a = np.minimum(amplitude_sqr[0], amplitude_sqr[-1])
        A3a = -np.max(amplitude_sqr)
        fra = f_data[np.argmin(amplitude_sqr)]
        A2a, A4a, Qla = [0.0, 0.0, 1e3]

        def fitfunc(x, A1, A2, A3, A4, fr, Ql):
            return (
                A1
                + A2 * (x - fr)
                + (A3 + A4 * (x - fr)) / (1.0 + 4.0 * Ql**2 * ((x - fr) / fr) ** 2)
            )

        p0 = [A1a, A2a, A3a, A4a, fra, Qla]
        try:
            popt, pcov = spopt.curve_fit(
                fitfunc, np.array(f_data), np.array(amplitude_sqr), p0=p0
            )
            if pcov is not None:
                self.df_error = np.sqrt(pcov[4][4])
                self.dQl_error = np.sqrt(pcov[5][5])
            else:
                self.df_error = np.inf
                self.dQl_error = np.inf
        except:
            popt = p0
            self.df_error = np.inf
            self.dQl_error = np.inf
        return popt

    def _fit_circle(self, z_data):
        """
        Use Lagrange method to fit a circle.
        F = sum(A*zi+B*xi+C*yi+D)^2-eta*(B^2+C^2-4*A*D)
        Use matrix determinant calculation instead of expanding to polynomial
        """
        x = z_data.real
        y = z_data.imag
        z = x**2 + y**2
        M = np.array(
            [
                [sum(z * z), sum(x * z), sum(y * z), sum(z)],
                [sum(x * z), sum(x * x), sum(x * y), sum(x)],
                [sum(y * z), sum(x * y), sum(y * y), sum(y)],
                [sum(z), sum(x), sum(y), len(x)],
            ]
        )
        B = np.array([[0, 0, 0, -2], [0, 1, 0, 0], [0, 0, 1, 0], [-2, 0, 0, 0]])
        f = lambda eta: np.linalg.det(np.mat(M - eta * B))

        x0 = sopt.fsolve(f, 0)
        M = M - x0[0] * B
        U, s, Vt = np.linalg.svd(M)
        A_vec = Vt[np.argmin(s), :]

        xc = -A_vec[1] / (2.0 * A_vec[0])
        yc = -A_vec[2] / (2.0 * A_vec[0])
        r0 = (
            1.0
            / (2.0 * np.absolute(A_vec[0]))
            * np.sqrt(
                A_vec[1] * A_vec[1] + A_vec[2] * A_vec[2] - 4.0 * A_vec[0] * A_vec[3]
            )
        )

        return xc, yc, r0

    def _fit_delay(self, f_data, z_data, delay=0.0, maxiter=0):
        """
        fit delay so that Re(S21) vs. Im(S21)
        :param f_data: scan frequency
        :param z_data: S21_raw_data
        :param delay: cable delay
        :param maxiter:
        :return: delay
        """

        def residuals(p, x, y):
            phasedelay = p
            z_data_temp = y * np.exp(1j * (2.0 * np.pi * phasedelay * x))
            xc, yc, r0 = self._fit_circle(z_data_temp)
            err = (
                np.sqrt((z_data_temp.real - xc) ** 2 + (z_data_temp.imag - yc) ** 2)
                - r0
            )
            return err

        p_final = spopt.leastsq(
            residuals,
            delay,
            args=(f_data, z_data),
            maxfev=maxiter,
            ftol=1e-12,
            xtol=1e-12,
        )
        return p_final[0][0]

    def _phase_fit(self, f_data, z_data, theta0, Ql, fr):
        """
        fit phase vs. frequency: theta = theta0+2*arctan(2*Q_l(1-f/fr))
        :param f_data: scan frequency
        :param z_data: S21_cal - (xc+j*yc)
        :param theta0: Initial theta
        :param Ql: Initial Ql coming from function get_delay
        :param fr: Initial fr coming from function get_delay
        :return: theta, Ql, fr
        """
        phase = np.angle(z_data)

        def residuals_5(p, x, y):
            theta0, Ql, fr = p
            err = self._dist(y - (theta0 + 2.0 * np.arctan(2.0 * Ql * (1.0 - x / fr))))
            return err

        p0 = np.array([theta0, Ql, fr], dtype="float64")
        p_final = spopt.leastsq(residuals_5, p0, args=(f_data, phase))
        return p_final[0]

    def _guess_delay(self, f_data, z_data):
        """
        The cable delay tilts the phase signal by a slope 2πτ
        :param f_data:
        :param z_data:
        :return:
        """
        phase2 = np.unwrap(np.angle(z_data))
        gradient, intercept, r_value, p_value, std_err = stats.linregress(
            f_data, phase2
        )
        return gradient * (-1.0) / (np.pi * 2.0)

    def _S21_notch(
        self, f, fr=10e9, Ql=900, Qc=1000.0, phi=0.0, a=1.0, alpha=0.0, delay=0.0
    ):
        """
        full model for notch type resonances
        """
        return (
            a
            * np.exp(complex(0, alpha))
            * np.exp(-2j * np.pi * f * delay)
            * (1.0 - Ql / Qc * np.exp(1j * phi) / (1.0 + 2j * Ql * (f - fr) / fr))
        )

    def get_single_photon_limit(self, unit="dBm", diacorr=True):
        """
        returns the amout of power in units of W necessary
        to maintain one photon on average in the cavity
        unit can be 'dBm' or 'watt'
        """
        if self.fit_result != {}:
            fr = self.fit_result["fr"]
            if diacorr:
                k_c = 2 * np.pi * fr / self.fit_result["Qc_dia_corr"]
                k_i = 2 * np.pi * fr / self.fit_result["Qi_dia_corr"]
            else:
                k_c = 2 * np.pi * fr / self.fit_result["absQc"]
                k_i = 2 * np.pi * fr / self.fit_result["Qi_no_corr"]
            if unit == "dBm":
                return self.Watt2dBm(
                    1.0 / (4.0 * k_c / (2.0 * np.pi * hbar * fr * (k_c + k_i) ** 2))
                )
            elif unit == "watt":
                return 1.0 / (4.0 * k_c / (2.0 * np.pi * hbar * fr * (k_c + k_i) ** 2))
        else:
            warnings.warn("Please perform the fit first", UserWarning)
            return None

    def get_photons_in_resonator(self, power, unit="dBm", diacorr=True):
        """
        returns the average number of photons
        for a given power in units of W
        unit can be 'dBm' or 'watt'
        """
        if self.fit_result != {}:
            if unit == "dBm":
                power = self.dBm2Watt(power)
            fr = self.fit_result["fr"]
            if diacorr:
                k_c = 2 * np.pi * fr / self.fit_result["Qc_dia_corr"]
                k_i = 2 * np.pi * fr / self.fit_result["Qi_dia_corr"]
            else:
                k_c = 2 * np.pi * fr / self.fit_result["absQc"]
                k_i = 2 * np.pi * fr / self.fit_result["Qi_no_corr"]
            return 4.0 * k_c / (2.0 * np.pi * hbar * fr * (k_c + k_i) ** 2) * power
        else:
            warnings.warn("Please perform the fit first", UserWarning)
            return None

    def Watt2dBm(self, x):
        """
        converts from units of watts to dBm
        """
        return 10.0 * np.log10(x * 1000.0)

    def dBm2Watt(self, x):
        """
        converts from units of watts to dBm
        """
        return 10 ** (x / 10.0) / 1000.0

    def _dist(self, x):
        np.absolute(x, x)
        c = (x > np.pi).astype(int)
        return x + c * (-2.0 * x + 2.0 * np.pi)

    def _periodic_boundary(self, x, bound):
        return np.fmod(x, bound) - np.trunc(x / bound) * bound

    def _center(self, z_data, zc):
        return z_data - zc

    def _get_cov_fast_notch(
        self, xdata, ydata, fitparams
    ):  # enhanced by analytical derivatives
        # derivatives of notch_ideal model with respect to parameters
        def dS21_dQl(p, f):
            fr, absQc, Ql, phi0 = p
            return -(np.exp(1j * phi0) * fr**2) / (
                absQc * (fr + 2j * Ql * f - 2j * Ql * fr) ** 2
            )

        def dS21_dQc(p, f):
            fr, absQc, Ql, phi0 = p
            return (np.exp(1j * phi0) * Ql * fr) / (
                2j * (f - fr) * absQc**2 * Ql + absQc**2 * fr
            )

        def dS21_dphi0(p, f):
            fr, absQc, Ql, phi0 = p
            return -(1j * Ql * fr * np.exp(1j * phi0)) / (
                2j * (f - fr) * absQc * Ql + absQc * fr
            )

        def dS21_dfr(p, f):
            fr, absQc, Ql, phi0 = p
            return -(2j * Ql**2 * f * np.exp(1j * phi0)) / (
                absQc * (fr + 2j * Ql * f - 2j * Ql * fr) ** 2
            )

        u = self._residuals_notch_ideal_complex(fitparams, xdata, ydata)
        chi = np.absolute(u)
        u = u / chi  # unit vector pointing in the correct direction for the derivative

        aa = dS21_dfr(fitparams, xdata)
        bb = dS21_dQc(fitparams, xdata)
        cc = dS21_dQl(fitparams, xdata)
        dd = dS21_dphi0(fitparams, xdata)

        Jt = np.array(
            [
                aa.real * u.real + aa.imag * u.imag,
                bb.real * u.real + bb.imag * u.imag,
                cc.real * u.real + cc.imag * u.imag,
                dd.real * u.real + dd.imag * u.imag,
            ]
        )
        A = np.dot(Jt, np.transpose(Jt))
        chisqr = 1.0 / float(len(xdata) - len(fitparams)) * (chi**2).sum()
        try:
            cov = np.linalg.inv(A) * chisqr
        except:
            cov = None
        return chisqr, cov

    def _residuals_notch_ideal_complex(self, p, x, y):
        fr, absQc, Ql, phi0 = p
        err = y - (
            1.0
            - (Ql / float(absQc) * np.exp(1j * phi0))
            / (1 + 2j * Ql * (x - fr) / float(fr))
        )
        return err


def auto_fit(port):
    delay, amp_norm, alpha, fr, Ql, A2, frcal = port.do_calibration(
        port.f_data, port.z_data_raw, guess_delay=True
    )
    port.z_data = port.do_normalization(
        port.f_data, port.z_data_raw, delay, amp_norm, alpha, A2, frcal
    )
    port.fit_result = port.circlefit(port.f_data, port.z_data, fr, Ql, calc_errors=True)
    port.z_data_sim = A2 * (port.f_data - frcal) + port._S21_notch(
        port.f_data,
        fr=port.fit_result["fr"],
        Ql=port.fit_result["Ql"],
        Qc=port.fit_result["absQc"],
        phi=port.fit_result["phi0"],
        a=amp_norm,
        alpha=alpha,
        delay=delay,
    )
    port.z_data_sim_cali = port.do_normalization(
        port.f_data, port.z_data_sim, delay, amp_norm, alpha, A2, frcal
    )


def q_fit(amp, freq, phase) -> QNotchPort:
    amp = np.asarray(amp)
    freq = np.asarray(freq)
    phase = np.asarray(phase) / 180 * np.pi
    s11 = 10 ** (amp / 20.0) * np.exp(1j * phase)
    port = QNotchPort(f_data=freq, z_data_raw=s11)
    auto_fit(port)
    return port
