# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.fourier_cycle_guess.rst:2
msgid "pyQCat.analysis.algorithms.fourier\\_cycle\\_guess"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.fourier_cycle_guess:1
msgid "Fourier transform calculates signal oscillation period"
msgstr "傅里叶变换计算信号振荡周期"

#: of pyQCat.analysis.algorithms.guess.fourier_cycle_guess:3
msgid ""
"This method is the same as the `cosine_fit_guess` method, the difference "
"is that only the most obvious oscillation period in the signal is "
"calculated here. Similarly, you can use it to calculate the oscillation "
"frequency of the signal"
msgstr ""
"这个方法和cosine_fit_guess方法一样，不同的是这里只计算信号中最明显的振荡周"
"期。 同样，你可以用它来计算信号的振荡频率"

#: of pyQCat.analysis.algorithms.guess.fourier_cycle_guess
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.fourier_cycle_guess:13
msgid "x data"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.fourier_cycle_guess:15
msgid "y data"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.fourier_cycle_guess
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.fourier_cycle_guess:17
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.fourier_cycle_guess
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.fourier_cycle_guess:18
msgid "Main signal oscillation period"
msgstr "主信号振荡周期"

