# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/05/17
# __author:       <PERSON><PERSON><PERSON> Shi
"""Recovering the frequency of the bits by voltage calibration."""

from copy import deepcopy

import numpy as np
import sympy

from ....analysis import (
    AnalysisResult,
    CouplerFixedPointCalibrationAnalysis,
    CouplerSweetPointCalibrationAnalysis,
    GradientCalibrationAnalysis,
    # OneStepCalibrationAnalysis,
    ParameterRepr,
    SweetPointCalibrationAnalysis,
)
from ....errors import (
    ExperimentContextError,
    ExperimentFieldError,
    ExperimentOptionsError,
)
from ....log import pyqlog
from ....parameters import options_wrapper
from ....structures import Optional, Options
from ....tools.utilities import (
    amp_to_freq,
    freq_to_amp,
    qarange,
    solve_equations,
    validate_ac_spectrum,
)
from ....types import ExperimentRunMode, QualityDescribe
from ...base_experiment import BaseExperiment
from ...composite_experiment import CompositeExperiment
from ...single import CouplerRamseyByZZShift, Ramsey, RamseyExtend, SwapOnce


class VoltageDriftGradientCalibration(CompositeExperiment):
    """Based on gradient optimization or other optimization schemes,
    the bit frequency is calibrated several times to obtain the voltage
    needed to recover the operating point.
    """

    _sub_experiment_class = Ramsey

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("iteration", int)
        options.set_validator("threshold", float)
        options.set_validator("guess_step", float)
        options.set_validator("cali_point", ["idle_point", "sweet_point"])
        options.set_validator("max_threshold", float)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.iteration = 5
        options.threshold = 0.01  # MHz
        options.fringe = 50  # MHz
        options.delays = qarange(100, 800, 10)
        options.guess_step = 0.02  # mV
        options.vol_max = 0.5
        options.cali_point = "idle_point"

        # add max delta freq threshold (unit: MHz)
        options.max_threshold = 5

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default options for analysis."""
        options = super()._default_analysis_options()

        options.threshold = 0.01
        options.max_threshold = 5
        options.dc_max = None
        options.idle_point = None

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options."""
        options = super()._default_run_options()
        options.cli_index = 0
        options.running_exp = None
        options.initial_vol = 0.0
        options.initial_osc_freq = 10.0
        options.guess_vol = 0.0
        options.guess_osc_freq = 0.0
        options.analysis_class = GradientCalibrationAnalysis
        options.real_fringe = None

        # fixed bug:
        options.fixed_amp = None

        options.tunable = False
        options.ctrl_quality = QualityDescribe.normal

        return options

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        if self.analysis.results is None:
            return

        cali_point = self.experiment_options.cali_point
        dc_max = self.analysis_options.dc_max
        idle_point = self.analysis_options.idle_point

        point_field_map = {"idle_point": "idle_point", "sweet_point": "dc_max"}
        point_field = point_field_map.get(cali_point) or "idle_point"

        if self.child_experiment.is_coupler_exp is True:
            extra_path = f"Coupler.{point_field}"
        elif self.coupler and self.child_experiment.is_coupler_exp is False:
            extra_path = f"Coupler.{point_field}"
        else:
            extra_path = f"Qubit.{point_field}"

        for key, result in self.analysis.results.items():
            if key == "vol":
                result.extra["path"] = extra_path
                if cali_point == "sweet_point":
                    note_value = result.value
                    result.value = dc_max + (note_value - idle_point)

    def _check_options(self):
        """check options."""
        super()._check_options()
        self.run_options.target_osc = self.child_experiment.experiment_options.fringe
        self.run_options.real_fringe = self.child_experiment.experiment_options.fringe
        acquisition_key = self.child_experiment.run_options.acquisition_key
        self.child_experiment.set_run_options(acquisition_key=acquisition_key)

        if self.child_experiment.qubit:
            result_name = self.child_experiment.qubit.name
            dc_max = self.child_experiment.qubit.dc_max
            idle_point = self.child_experiment.qubit.idle_point
            tunable = self.child_experiment.qubit.tunable
        else:
            result_name = self.child_experiment.coupler.name
            dc_max = self.child_experiment.coupler.dc_max
            idle_point = self.child_experiment.coupler.idle_point
            tunable = self.child_experiment.coupler.tunable

        self.set_run_options(tunable=tunable)
        self.set_analysis_options(
            result_name=result_name,
            threshold=self.experiment_options.threshold,
            max_threshold=self.experiment_options.max_threshold,
            dc_max=dc_max,
            idle_point=idle_point,
        )

    async def _run_child_experiment(self, z_amp, add_child: bool = True) -> float:
        """Run child experiment and extract result data."""
        idx = self.run_options.cli_index
        real_fringe = self.run_options.real_fringe
        target_osc = self.run_options.target_osc
        child_exp: Ramsey = self.run_options.running_exp

        # bug fix: sweet point calibration target freq is right osc freq
        # self.run_options.target_osc = child_exp.experiment_options.fringe

        # set child experiment voltage.
        if self.coupler:
            component = child_exp.coupler
        else:
            component = child_exp.qubit
        component.idle_point = z_amp
        new_ac_bias = component.dc_max + component.idle_point
        if abs(new_ac_bias) > self.experiment_options.vol_max:
            pyqlog.error(f"{component.name} new_ac_bias: {new_ac_bias}")
            if add_child is True:
                err_delta_f = 10000
            else:
                err_delta_f = 10000
            self.run_options.ctrl_quality = QualityDescribe.bad
            return err_delta_f

        child_exp.update_bias(component.name, new_ac_bias)

        description = f"idx={idx} fringe={real_fringe}MHz,z_amp={z_amp}"
        if idx == 0:
            description += f"target_osc={target_osc}MHz"

        # set child experiment options.
        child_exp.set_parent_file(self, description, idx)
        child_exp.set_experiment_options(
            # delays=self.experiment_options.delays,
            fringe=self.run_options.real_fringe,
            z_amp=self.run_options.fixed_amp,
        )

        self._check_simulator_data(child_exp, self.run_options.cli_index)
        await child_exp.run_experiment()
        # child_exp.clear_params()

        if add_child:
            self._experiments.append(child_exp)

        result = self.run_options.running_exp.analysis.results
        self.run_options.guess_osc_freq = result.freq.value
        delta_f = abs(self.run_options.guess_osc_freq - self.run_options.target_osc)
        if add_child:
            child_exp.analysis.provide_for_parent["delta_f"] = delta_f

        return delta_f

    async def _calculate_gradient(self):
        """Calculate the voltage gradient and get the frequency."""
        result_name = self.analysis_options.result_name
        z_amps = []
        while self.run_options.cli_index <= self.experiment_options.iteration:
            if self.run_options.running_exp.coupler:
                component = self.run_options.running_exp.coupler
            else:
                component = self.run_options.running_exp.qubit

            idx = self.run_options.cli_index
            pyqlog.info(
                f"{result_name} iteration {idx} voltage: {self.run_options.guess_vol} v"
            )

            ramsey_exp = deepcopy(self.child_experiment)
            self.run_options.running_exp = ramsey_exp
            delta_f = await self._run_child_experiment(self.run_options.guess_vol)
            if delta_f == 10000:
                break

            z_amps.append(self.run_options.guess_vol)

            if delta_f <= self.analysis_options.threshold:
                break

            delta_vol = self.run_options.guess_vol - self.run_options.initial_vol
            delta_freq = (
                self.run_options.guess_osc_freq - self.run_options.initial_osc_freq
            )
            freq_distance = (
                self.run_options.guess_osc_freq - self.run_options.target_osc
            )
            k = delta_vol / delta_freq
            pyqlog.info(
                f"{result_name} iteration {idx} "
                f"delta_vol: {delta_vol}, delta_freq: {delta_freq}, k: {k}, "
                f"freq_distance:{freq_distance} MHz"
            )
            if abs(k) < 1e-5:
                new_k = 1e-3 if k > 0 else -1e-3
                pyqlog.warning(
                    f"{result_name} iteration {idx} "
                    f"k: {k} is too small, will change k: {new_k}"
                )
                k = new_k

            if abs(self.run_options.guess_osc_freq - self.run_options.target_osc) < abs(
                self.run_options.initial_osc_freq - self.run_options.target_osc
            ):
                self.run_options.initial_osc_freq = self.run_options.guess_osc_freq
                self.run_options.initial_vol = self.run_options.guess_vol

            self.run_options.guess_vol = round(
                self.run_options.guess_vol
                - k * (self.run_options.guess_osc_freq - self.run_options.target_osc),
                6,
            )

            # if abs(self.run_options.guess_vol) > self.experiment_options.vol_max:
            new_ac_bias = component.dc_max + component.idle_point
            if abs(new_ac_bias) > self.experiment_options.vol_max:
                pyqlog.error(
                    f"{component.name}:too large voltage:{self.run_options.guess_vol}, "
                    f"current dc_max:{component.dc_max}, "
                    f"current idle point:{component.idle_point}"
                )
                # z_amps = z_amps[:-1]
                # self.analysis.analysis_datas.y = self.analysis.analysis_datas.y[:-1]
                self.run_options.ctrl_quality = QualityDescribe.bad
                break
            self.run_options.cli_index += 1
        else:
            self.run_options.ctrl_quality = QualityDescribe.bad

        ctrl_quality = self.run_options.ctrl_quality
        analysis_class = self.run_options.analysis_class
        if ctrl_quality == QualityDescribe.bad:
            pyqlog.warning(
                f"{result_name} ctrl_quality: {ctrl_quality} set bad analysis!"
            )
            self.analysis = analysis_class.empty_analysis(QualityDescribe.bad)
        else:
            self._run_analysis(z_amps, analysis_class)

    async def _check_drift(self) -> bool:
        """Check the voltage drift."""
        self.run_options.running_exp = deepcopy(self.child_experiment)
        child_exp = self.run_options.running_exp
        if self.run_options.running_exp.coupler:
            component = self.run_options.running_exp.coupler
        else:
            component = self.run_options.running_exp.qubit
        self.run_options.initial_vol = child_exp.qubit.idle_point
        pyqlog.log(
            "EXP",
            f"{component.name} Target osc freq is {self.run_options.target_osc} MHz!",
        )

        delta_f = await self._run_child_experiment(
            self.run_options.initial_vol, add_child=False
        )

        if delta_f <= self.experiment_options.threshold:
            pyqlog.info(
                f"{component.name} Frequency stability, no calibration required: {self.run_options.guess_osc_freq} MHz"
            )
            return False
        else:
            self.run_options.guess_vol = (
                self.run_options.initial_vol - self.experiment_options.guess_step * 1e-3
            )
            self.run_options.initial_osc_freq = self.run_options.guess_osc_freq
            return True

    async def _sync_composite_run(self):
        """Run the experiment."""
        # super().run()
        if self.run_options.tunable is True:
            is_drift = await self._check_drift()
        else:
            pyqlog.log(
                "EXP",
                f"{self.analysis_options.result_name} is not tunable, "
                f"so no need to calibrate the voltage",
            )
            is_drift = False

        if is_drift:
            await self._calculate_gradient()
        else:
            self.analysis = self.run_options.analysis_class.empty_analysis(
                QualityDescribe.perfect
            )


@options_wrapper
class SweetPointCalibration(VoltageDriftGradientCalibration):
    """Based on gradient optimization to calibrate sweet point
    voltage.
    """

    _sub_experiment_class = Ramsey

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("z_amp", float)
        options.cali_point = "sweet_point"
        options.z_amp = None

        return options

    def _check_options(self):
        """check options."""
        super()._check_options()
        qubit = self.child_experiment.qubit

        if not self.experiment_options.z_amp:
            pyqlog.info(f"into {qubit.ac}")
            self.experiment_options.z_amp = qubit.ac
        self.set_run_options(analysis_class=SweetPointCalibrationAnalysis)
        self.set_analysis_options(dc_max=qubit.dc_max, idle_point=qubit.idle_point)

        if self.child_experiment.qubit:
            result_name = self.child_experiment.qubit.name
        else:
            result_name = self.child_experiment.coupler.name

        self.set_analysis_options(result_name=result_name)

    async def _check_z_amp(self, z_amp, i):
        """Check the voltage drift."""
        child_exp: Ramsey = self.run_options.running_exp

        # qubit pair
        amp = z_amp - child_exp.qubit.idle_point

        # change idea from wang peng: It is necessary to ensure that the
        # fringes on both sides are consistent
        if i == 0:
            self._set_child_exp_fringe(z_amp)

        child_exp.set_experiment_options(
            # delays=self.experiment_options.delays,
            fringe=self.run_options.real_fringe,
            z_amp=amp,
        )

        # set child experiment options.
        child_exp.set_parent_file(
            self,
            f"fringe={child_exp.experiment_options.fringe}MHz, "
            f"z_amp={z_amp}v, idle_point={child_exp.qubit.idle_point}v",
            i,
        )

        # fixed bug:
        if i == 0:
            self.run_options.fixed_amp = amp

        self._check_simulator_data(child_exp, self.run_options.cli_index)
        await child_exp.run_experiment()
        # child_exp.clear_params()

        # self._experiments.append(child_exp)

        result = self.run_options.running_exp.analysis.results

        return result.freq.value

    async def _check_drift(self) -> bool:
        """Check the voltage drift."""
        self.run_options.running_exp = deepcopy(self.child_experiment)
        child_exp = self.run_options.running_exp

        self.run_options.initial_vol = child_exp.qubit.idle_point

        left_osc = await self._check_z_amp(self.experiment_options.z_amp, 0)

        self.run_options.running_exp = deepcopy(self.child_experiment)
        right_osc = await self._check_z_amp(-self.experiment_options.z_amp, 1)

        self.run_options.target_osc = right_osc

        delta_f = abs(left_osc - right_osc)
        if delta_f <= self.experiment_options.threshold:
            pyqlog.info(
                f"Frequency stability, no calibration required: {self.run_options.guess_osc_freq} MHz"
            )
            return False
        else:
            self.run_options.guess_vol = (
                self.run_options.initial_vol - self.experiment_options.guess_step * 1e-3
            )
            self.run_options.initial_osc_freq = left_osc
            return True

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        if self.analysis.results:
            for key, result in self.analysis.results.items():
                if key == "dc_max":
                    if self.child_experiment.is_coupler_exp is True:
                        result.extra["path"] = "Coupler.dc_max"
                    elif self.coupler and self.child_experiment.is_coupler_exp is False:
                        result.extra["path"] = "Coupler.dc_max"
                    else:
                        result.extra["path"] = "Qubit.dc_max"

    def _set_child_exp_fringe(self, z_amp: float):
        """Dynamic set fringe by ac spectrum."""
        if self.child_experiment.qubit:
            physical_bit = self.child_experiment.qubit

            res, _ = validate_ac_spectrum(
                ac_spectrum=physical_bit.ac_spectrum, z_amp=z_amp
            )

            fq_max, fc, M, offset, d, w, g = res

            if fq_max == 0:
                # use experiment options fringe as real fringe.
                self.run_options.real_fringe = (
                    self.child_experiment.experiment_options.fringe
                )
            else:
                # use qubit ac spectrum parameters update real fringe.
                f_guess = amp_to_freq(physical_unit=physical_bit, z_amp=z_amp)

                self.run_options.real_fringe = round(
                    (
                        self.child_experiment.experiment_options.fringe
                        + f_guess
                        - physical_bit.drive_freq
                    ),
                    3,
                )


@options_wrapper
class SweetPointCalibrationVMin(SweetPointCalibration):
    async def _check_drift(self) -> bool:
        """Check the voltage drift."""
        self.run_options.running_exp = deepcopy(self.child_experiment)
        child_exp = self.run_options.running_exp

        qubit = self.child_experiment.qubit

        self.run_options.initial_vol = child_exp.qubit.idle_point

        left_osc = await self._check_z_amp(
            qubit.dc_min - qubit.dc_max - self.experiment_options.z_amp, 0
        )

        self.run_options.running_exp = deepcopy(self.child_experiment)
        right_osc = await self._check_z_amp(
            qubit.dc_min - qubit.dc_max + self.experiment_options.z_amp, 1
        )

        self.run_options.target_osc = right_osc

        delta_f = abs(left_osc - right_osc)
        if delta_f <= self.experiment_options.threshold:
            pyqlog.info(
                f"Frequency stability, no calibration required: {self.run_options.guess_osc_freq} MHz"
            )
            return False
        else:
            self.run_options.guess_vol = (
                self.run_options.initial_vol - self.experiment_options.guess_step * 1e-3
            )
            self.run_options.initial_osc_freq = left_osc
            return True

    def _check_options(self):
        """check options."""
        super()._check_options()
        qubit = self.child_experiment.qubit
        self.set_analysis_options(dc_max=qubit.dc_min, idle_point=qubit.idle_point)

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        if self.analysis.results:
            for key, result in self.analysis.results.items():
                if key == "dc_max":
                    if self.child_experiment.is_coupler_exp is True:
                        result.extra["path"] = "Coupler.dc_min"
                    elif self.coupler and self.child_experiment.is_coupler_exp is False:
                        result.extra["path"] = "Coupler.dc_min"
                    else:
                        result.extra["path"] = "Qubit.dc_min"


# class CouplerSweetPointCalibration(SweetPointCalibration):
#     """Based on gradient optimization to calibrate sweet point
#     voltage.
#     """
#
#     _sub_experiment_class = CouplerRamsey


@options_wrapper
class SwapSweetPointCalibration(SweetPointCalibration):
    """Based on gradient optimization to calibrate sweet point
    voltage.
    """

    _sub_experiment_class = SwapOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("scan_name", str)
        options.scan_name = None

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options."""
        options = super()._default_run_options()
        options.coupler = None

        return options

    def _check_options(self):
        """check options."""
        super(SweetPointCalibration, self)._check_options()
        if self.experiment_options.scan_name is None:
            self.experiment_options.scan_name = self.child_experiment.couplers[0].name

        for coupler in self.couplers:
            if coupler.name == self.experiment_options.scan_name:
                self.run_options.coupler = coupler
                break
        if self.run_options.coupler is None:
            raise ExperimentOptionsError(
                self,
                key="scan_name",
                value=self.experiment_options.scan_name,
                msg=f"{self.experiment_options.scan_name} not in current environment.",
            )
        self.set_run_options(analysis_class=SweetPointCalibrationAnalysis)
        self.set_analysis_options(
            dc_max=self.run_options.coupler.dc_max,
            idle_point=self.run_options.coupler.idle_point,
        )

    async def _check_z_amp(self, z_amp, i):
        """Check the voltage drift."""
        child_exp: SwapOnce = self.run_options.running_exp

        # set child experiment options.
        child_exp.set_parent_file(
            self,
            f"z_amp={z_amp}v, idle_point={self.run_options.coupler.idle_point}v",
            i,
        )

        amp = z_amp - self.run_options.coupler.idle_point

        child_exp.qubit_pair.update_cz_gate_params(
            params={self.experiment_options.scan_name: {"amp": amp}}
        )

        if i == 0:
            self.run_options.fixed_amp = amp

        self._check_simulator_data(child_exp, self.run_options.cli_index)
        await child_exp.run_experiment()
        # child_exp.clear_params()

        # self._experiments.append(child_exp)

        result = self.run_options.running_exp.analysis.results

        return result.freq.value

    async def _run_child_experiment(self, z_amp, add_child: bool = True) -> float:
        """Run child experiment and extract result data."""
        child_exp: SwapOnce = self.run_options.running_exp

        # set child experiment voltage.
        component = self.run_options.coupler
        component.idle_point = z_amp
        new_ac_bias = component.dc_max + component.idle_point
        if abs(new_ac_bias) > self.experiment_options.vol_max:
            pyqlog.error(f"{component.name} new_ac_bias: {new_ac_bias}")
            if add_child is True:
                err_delta_f = 10000
            else:
                err_delta_f = 10000
            self.run_options.ctrl_quality = QualityDescribe.bad
            return err_delta_f

        child_exp.update_bias(component.name, new_ac_bias)

        # set child experiment options.
        child_exp.set_parent_file(
            self,
            f"target_osc={self.experiment_options.target_osc}vol={z_amp}v",
            self.run_options.cli_index,
        )

        child_exp.qubit_pair.update_cz_gate_params(
            params={
                self.experiment_options.scan_name: {"amp": self.run_options.fixed_amp}
            }
        )

        self._check_simulator_data(child_exp, self.run_options.cli_index)
        await child_exp.run_experiment()
        # child_exp.clear_params()

        self._experiments.append(child_exp)

        result = self.run_options.running_exp.analysis.results
        self.run_options.guess_osc_freq = result.freq.value
        delta_f = abs(self.run_options.guess_osc_freq - self.run_options.target_osc)
        child_exp.analysis.provide_for_parent["delta_f"] = delta_f

        return delta_f

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        if self.analysis.results:
            for key, result in self.analysis.results.items():
                if key == "dc_max":
                    result.extra["path"] = "Coupler.dc_max"

    async def _check_drift(self) -> bool:
        """Check the voltage drift. zyc"""
        self.run_options.running_exp = deepcopy(self.child_experiment)
        child_exp = self.run_options.running_exp

        self.run_options.initial_vol = child_exp.coupler.idle_point

        left_osc = await self._check_z_amp(self.experiment_options.z_amp, 0)

        self.run_options.running_exp = deepcopy(self.child_experiment)
        right_osc = await self._check_z_amp(-self.experiment_options.z_amp, 1)

        self.run_options.target_osc = right_osc
        pyqlog.log("EXP", f"Target osc freq is {self.run_options.target_osc} MHz!")

        delta_f = abs(left_osc - right_osc)
        if delta_f <= self.experiment_options.threshold:
            pyqlog.info(
                f"Frequency stability, no calibration required: {self.run_options.guess_osc_freq} MHz"
            )
            return False
        else:
            self.run_options.guess_vol = (
                self.run_options.initial_vol - self.experiment_options.guess_step * 1e-3
            )
            self.run_options.initial_osc_freq = left_osc
            return True


@options_wrapper
class ZZShiftSweetPointCalibration(VoltageDriftGradientCalibration):
    _sub_experiment_class = CouplerRamseyByZZShift

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("z_amp", float)
        options.z_amp = None

        return options

    def _check_options(self):
        """check options."""
        super()._check_options()
        coupler = self.child_experiment.coupler
        if self.experiment_options.z_amp is None:
            self.experiment_options.z_amp = coupler.ac
        self.set_run_options(analysis_class=SweetPointCalibrationAnalysis)
        self.set_analysis_options(
            dc_max=coupler.dc_max,
            idle_point=coupler.idle_point,
            result_name=coupler.name,
        )

    async def _check_z_amp(self, z_amp, i):
        """Check the voltage drift."""
        child_exp: CouplerRamseyByZZShift = self.run_options.running_exp

        # set child experiment options.
        child_exp.set_parent_file(
            self,
            f"fringe={child_exp.experiment_options.fringe}MHz, "
            f"z_amp={z_amp}v, idle_point={child_exp.coupler.idle_point}v",
            i,
        )
        # qubit pair
        amp = z_amp - child_exp.coupler.idle_point

        child_exp.set_experiment_options(
            # delays=self.experiment_options.delays,
            # fringe=self.experiment_options.fringe,
            z_amp=amp,
        )

        # fixed bug:
        if i == 0:
            self.run_options.fixed_amp = amp

        self._check_simulator_data(child_exp, self.run_options.cli_index)
        await child_exp.run_experiment()
        # child_exp.clear_params()

        # self._experiments.append(child_exp)

        result = self.run_options.running_exp.analysis.results

        return result.freq.value

    async def _check_drift(self) -> bool:
        """Check the voltage drift."""
        self.run_options.running_exp = deepcopy(self.child_experiment)
        child_exp = self.run_options.running_exp

        self.run_options.initial_vol = child_exp.coupler.idle_point

        left_osc = await self._check_z_amp(self.experiment_options.z_amp, 0)

        self.run_options.running_exp = deepcopy(self.child_experiment)
        right_osc = await self._check_z_amp(-self.experiment_options.z_amp, 1)

        self.run_options.target_osc = right_osc

        delta_f = abs(left_osc - right_osc)
        if delta_f <= self.experiment_options.threshold:
            pyqlog.info(
                f"Frequency stability, no calibration required: {self.run_options.guess_osc_freq} MHz"
            )
            return False
        else:
            self.run_options.guess_vol = (
                self.run_options.initial_vol - self.experiment_options.guess_step * 1e-3
            )
            self.run_options.initial_osc_freq = left_osc
            return True

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "dc_max":
                result.extra["path"] = "Coupler.dc_max"


class FixedPointCalibration(CompositeExperiment):
    """Normal fixed target bit frequency, add bias pulses or not, calibrate target z_amp."""

    _sub_experiment_class = RamseyExtend

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("z_amp", float)
        options.set_validator("frequency", float, limit_null=True)
        options.set_validator("diff_frequency", float)
        # options.set_validator("ac_branch", ["right", "left"])
        options.set_validator("label", ["cz", "zz"])
        options.set_validator("gradient_type", ["linear", "quadratic"])
        options.set_validator("threshold", float)
        options.set_validator("guess_step", float)
        options.set_validator("iteration", int)
        options.set_validator("trans_amp_to_freq", bool)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.z_amp = None  # V
        options.frequency = None  # MHz
        options.diff_frequency = None  # MHz
        # options.ac_branch = "right"
        options.label = "cz"
        options.gradient_type = "quadratic"
        options.threshold = 0.1  # MHz
        options.guess_step = 0.2  # mV
        options.iteration = 5
        options.trans_amp_to_freq = False
        options.vol_max = 0.5

        # add max delta freq threshold (unit: MHz)
        options.max_threshold = 5

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default options for analysis."""
        options = super()._default_analysis_options()

        # options.set_validator("tolerance", float)
        # options.set_validator("threshold", float)

        # options.tolerance = 0.05  # MHz
        options.threshold = 0.01  # MHz
        options.max_threshold = 5
        options.force_bad_quality = False

        options.result_parameters = [
            ParameterRepr(name="freq", repr="frequency", unit="MHz"),
            ParameterRepr(name="vol", repr="drift-vol", unit="V"),
        ]
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options."""
        options = super()._default_run_options()

        options.cli_index = 0
        options.running_exp = None
        options.initial_vol = 0.0
        options.initial_osc_freq = 10.0
        options.guess_vol = 0.0
        options.guess_osc_freq = 0.0
        options.target_osc = None
        options.real_fringe = None
        options.analysis_class = GradientCalibrationAnalysis
        options.target_vol = None

        options.tq = None
        options.vol_list = []
        options.osc_freq_list = []
        options.delta_f_list = []

        options.tunable = True
        options.ctrl_quality = QualityDescribe.normal

        return options

    # def get_qubit_str(self):
    #     """Get qubit str, an args qubit for SaveFile class.

    #     Returns:
    #         qubit_str: string, use to create save data path.
    #     """
    #     tq_name = self.experiment_options.child_exp_options.tq_name
    #     bq_name_list = self.experiment_options.child_exp_options.bq_name_list

    #     string = f"tq({tq_name})-bq({','.join(bq_name_list)})-{super().get_qubit_str()}"
    #     return string

    def _check_options(self):
        """check options."""
        super()._check_options()

        threshold = self.experiment_options.threshold
        tq_name = self.child_experiment.experiment_options.tq_name
        bq_name_list = self.child_experiment.experiment_options.bq_name_list
        qubit_pair = self.qubit_pair

        qubit_map = {qubit.name: qubit for qubit in self.qubits}
        if tq_name:
            if tq_name in ["ql", "qh"]:
                if not qubit_pair:
                    raise ExperimentFieldError(self, "No find any qubit pair!")
                tq_name = getattr(self.qubit_pair, tq_name)
            tq_obj = qubit_map.get(tq_name)
        else:
            qubit_list = []
            for q_obj in self.qubits:
                if q_obj.name not in bq_name_list:
                    qubit_list.append(q_obj)
            if qubit_list:
                tq_obj = qubit_list[0]
            else:
                tq_obj = None

        if tq_obj is None:
            raise ExperimentOptionsError(
                self,
                f"Set child_experiment target {tq_name} not in all bit names!",
                "tq_name",
                tq_name,
            )
        if not tq_obj.tunable:
            self.run_options.tunable = False
        z_amp = self.experiment_options.z_amp
        frequency = self.experiment_options.frequency
        diff_frequency = self.experiment_options.diff_frequency

        if z_amp is None and frequency is None and diff_frequency is None:
            frequency = self.qubit_pair.cz_value(
                tq_name, "freq", self.experiment_options.label
            )
            if not frequency:
                raise ExperimentOptionsError(
                    exp=self,
                    msg="Options z_amp, frequency, diff_frequency, at least set one!",
                )
            self.experiment_options.frequency = frequency
        elif isinstance(diff_frequency, (int, float)):
            freq = tq_obj.drive_freq + diff_frequency
            self.set_experiment_options(frequency=freq)
            pyqlog.info(
                f"By diff_frequency: {diff_frequency}, drive_freq: {tq_obj.drive_freq}, "
                f"calculate {tq_obj.name} frequency: {self.experiment_options.frequency}"
            )

        self.child_experiment.qubit = tq_obj
        self.set_run_options(tq=tq_obj)

        result_name = tq_name if not self.qubit_pair else self.qubit_pair.name
        self.set_analysis_options(
            threshold=threshold,
            max_threshold=self.experiment_options.max_threshold,
            result_name=result_name,
        )

    def _set_child_exp_fringe(self, z_amp: Optional[float] = None):
        """Dynamic set fringe by ac spectrum."""
        freq = self.experiment_options.frequency
        result_name = self.analysis_options.result_name
        tq = self.run_options.tq
        default_fringe = self.child_experiment.experiment_options.fringe

        real_fringe = round((default_fringe + freq - tq.drive_freq), 3)

        if z_amp is None:
            res, _ = validate_ac_spectrum(ac_spectrum=tq.ac_spectrum, z_amp=0)
            fq_max, fc, M, offset, d, w, g = res

            if fq_max == 0:
                pyqlog.warning(
                    f"{tq.name} invalid AC Spectrum parameters:{fq_max, fc, M, offset, d}"
                )
                z_amp = 0
            else:
                ac_branch = "left" if tq.idle_point < 0 else "right"
                pyqlog.debug(
                    f"{tq} idle_point: {tq.idle_point}, ac_branch: {ac_branch}"
                )

                z_amp = freq_to_amp(
                    physical_unit=tq,
                    freq=freq,
                    branch=ac_branch,
                )
                if np.isnan(z_amp):
                    pyqlog.error(
                        f"{tq.name} frequency {freq} MHz to amp result is nan!"
                    )
                    raise ValueError(f"Freq {freq} MHz to amp error, result is nan!")
                z_amp -= tq.idle_point

        pyqlog.info(
            f"target name: {result_name}, "
            f"Set frequency: {freq} MHz, z_amp: {z_amp} V, "
            f"target_osc: {default_fringe}, real_fringe: {real_fringe}"
        )

        self.set_run_options(
            initial_vol=z_amp,
            target_osc=default_fringe,
            real_fringe=real_fringe,
        )

    def _get_current_cali_vol(self, exp: "BaseExperiment") -> float:
        """Note current calibrate voltage value."""
        vol = exp.experiment_options.z_amp
        return vol

    async def _run_child_experiment(
        self, z_amp: float, add_child: bool = True
    ) -> float:
        """Run child experiment and extract result data."""
        freq = self.experiment_options.frequency
        idx = self.run_options.cli_index
        target_osc = self.run_options.target_osc
        real_fringe = self.run_options.real_fringe
        result_name = self.analysis_options.result_name

        description = f"idx={idx}, z_amp={round(z_amp, 5)}V"
        if idx == 0:
            description += f"target_freq_osc={freq}_{target_osc}MHz,"

        child_exp = self.run_options.running_exp
        child_exp.set_parent_file(self, description, idx)
        child_exp.set_experiment_options(fringe=real_fringe, z_amp=z_amp)
        self._check_simulator_data(child_exp, idx)
        await child_exp.run_experiment()
        # child_exp.clear_params()

        vol = self._get_current_cali_vol(child_exp)
        osc_freq = child_exp.analysis.results.freq.value
        delta_f = abs(osc_freq - target_osc)

        if add_child is True:
            child_exp.analysis.provide_for_parent["delta_f"] = delta_f
            self._experiments.append(child_exp)

        pyqlog.info(f"{result_name} iteration {idx} result, osc_freq: {osc_freq} MHz")

        self.run_options.guess_osc_freq = osc_freq
        self.run_options.vol_list.append(vol)
        self.run_options.osc_freq_list.append(osc_freq)
        self.run_options.delta_f_list.append(delta_f)

        return delta_f

    async def _check_drift(self) -> bool:
        """Check the voltage drift."""
        threshold = self.experiment_options.threshold
        initial_vol = self.run_options.initial_vol
        target_osc = self.run_options.target_osc

        pyqlog.log("EXP", f"Target osc freq is {target_osc} MHz!")

        self.run_options.running_exp = deepcopy(self.child_experiment)
        delta_f = await self._run_child_experiment(initial_vol, add_child=False)
        osc_freq = self.run_options.guess_osc_freq

        if delta_f <= threshold:
            drift_flag = False
            pyqlog.info(f"Frequency stability, no calibration required: {osc_freq} MHz")
        else:
            drift_flag = True
            guess_step = self.experiment_options.guess_step * 1e-3
            self.run_options.cli_index += 1
            self.run_options.initial_osc_freq = osc_freq
            self.run_options.guess_vol = initial_vol - guess_step

        return drift_flag

    def _get_guess_vol(self, idx: int) -> float:
        """Guess next voltage value."""
        gradient_type = self.experiment_options.gradient_type
        guess_step = self.experiment_options.guess_step * 1e-3
        initial_vol = self.run_options.initial_vol
        initial_osc_freq = self.run_options.initial_osc_freq
        guess_vol = self.run_options.guess_vol
        guess_osc_freq = self.run_options.guess_osc_freq
        target_osc = self.run_options.target_osc
        vol_list = self.run_options.vol_list
        osc_freq_list = self.run_options.osc_freq_list
        result_name = self.analysis_options.result_name

        liner_flag = False
        new_guess_vol = guess_vol - guess_step
        if gradient_type == "quadratic" and (len(vol_list) >= 3):
            try:
                last_vol = vol_list[-1]
                a, b, c = solve_equations(vol_list[-3:], osc_freq_list[-3:])
                x = sympy.Symbol("x")
                func = a * x**2 + b * x + c - target_osc
                solve_vol_list = sympy.solve(func, x)

                diff_list = []
                for solve_vol in solve_vol_list:
                    diff = abs(float(solve_vol) - last_vol)
                    diff_list.append(diff)
                t_idx = np.argmin(diff_list)
                new_guess_vol = round(float(solve_vol_list[t_idx]), 6)
                pyqlog.info(
                    f"{result_name} iteration {idx} use `quadratic` guess, "
                    f"abc={[a, b, c]}, solve_vol_list: {solve_vol_list}, "
                    f"guess next vol: {new_guess_vol}"
                )
            except Exception as err:
                pyqlog.warning(
                    f"{result_name} iteration {idx} use `quadratic` guess, "
                    f"error: {err}, so checkout `liner` guess."
                )
                liner_flag = True
        else:
            liner_flag = True

        if liner_flag is True:
            delta_vol = guess_vol - initial_vol
            delta_freq = guess_osc_freq - initial_osc_freq
            k = delta_vol / delta_freq
            if abs(k) < 1e-5:
                new_k = 1e-3 if k > 0 else -1e-3
                pyqlog.warning(
                    f"{result_name} iteration {idx} k: {k} is too small, will change k: {new_k}"
                )
                k = new_k
            new_guess_vol = round(guess_vol - k * (guess_osc_freq - target_osc), 6)
            if abs(guess_osc_freq - target_osc) < abs(initial_osc_freq - target_osc):
                self.run_options.initial_vol = guess_vol
                self.run_options.initial_osc_freq = guess_osc_freq

            pyqlog.info(
                f"{result_name} iteration {idx} use `liner` guess, "
                f"delta_vol: {delta_vol}, delta_freq: {delta_freq}, k: {k}, "
                f"guess next vol: {new_guess_vol}"
            )
        return new_guess_vol

    async def _calculate_gradient(self):
        """Calculate the voltage gradient and get the frequency."""
        threshold = self.experiment_options.threshold
        max_count = self.experiment_options.iteration
        result_name = self.analysis_options.result_name

        z_amps = []
        while self.run_options.cli_index <= max_count:
            idx = self.run_options.cli_index
            pyqlog.info(
                f"{result_name} iteration {idx} voltage: {self.run_options.guess_vol} v"
            )

            self.run_options.running_exp = deepcopy(self.child_experiment)
            delta_f = await self._run_child_experiment(
                self.run_options.guess_vol, add_child=True
            )
            z_amps.append(self.run_options.guess_vol)

            if delta_f <= threshold:
                break
            # self.run_options.guess_vol = self._get_guess_vol(idx)
            guess_vol = self._get_guess_vol(idx)

            if abs(guess_vol) > self.experiment_options.vol_max:
                pyqlog.error(
                    f"{self.run_options.tq.name}:too large voltage:{guess_vol}"
                )
                self.run_options.ctrl_quality = QualityDescribe.bad
                break
            else:
                self.run_options.guess_vol = guess_vol
                self.run_options.cli_index += 1
        else:
            self.run_options.ctrl_quality = QualityDescribe.bad

        ctrl_quality = self.run_options.ctrl_quality
        analysis_class = self.run_options.analysis_class
        if ctrl_quality == QualityDescribe.bad:
            self.analysis_options.force_bad_quality = True
            self._run_analysis(z_amps, analysis_class)
            pyqlog.warning(
                f"{result_name} ctrl_quality: {ctrl_quality} set bad analysis!"
            )
            # self.analysis = analysis_class.empty_analysis(Quality.bad.value)
            # self.analysis._quality.descriptor = QualityDescrive.bad
        else:
            self._run_analysis(z_amps, analysis_class)

    def _set_result_path(self):
        """Set result path."""
        z_amp = self.experiment_options.z_amp
        freq = self.experiment_options.frequency
        label = self.experiment_options.label
        initial_vol = self.run_options.initial_vol
        guess_vol = self.run_options.guess_vol
        tq_name = self.run_options.tq.name

        tg_z_amp = guess_vol or initial_vol
        if self.analysis and self.analysis.results:
            for key, result in self.analysis.results.items():
                if key == "vol":
                    tg_z_amp = result.value
                    pn = "amp"
                    if self.experiment_options.trans_amp_to_freq:
                        tg_freq = amp_to_freq(
                            self.run_options.tq,
                            tg_z_amp + self.run_options.tq.idle_point,
                        )
                        pyqlog.info(
                            f"Transform {tq_name} z_amp({tg_z_amp}) to {tg_freq} MHz!"
                        )
                        result.value = tg_freq
                        pn = "freq"

                    if self.qubit_pair:
                        c_path = f"QubitPair.metadata.std.{label}.params.{tq_name}.{pn}"
                    else:
                        # todo: current only support qubit pair update
                        c_path = None

                    result.extra["path"] = c_path

        self.run_options.target_vol = tg_z_amp
        pyqlog.log("RESULT", f"Target {tq_name} z_amp is {tg_z_amp} V")

        file_name = f"{tq_name}_cali_result"
        note_info = (
            f"target name: {tq_name}\n"
            f"Set frequency: {freq} MHz, z_amp: {z_amp} V\n"
            f"After calibrate result z_amp: {tg_z_amp} V"
        )
        self.file.save_text(note_info, file_name)

        save_file = f"{tq_name}_fixed_point_run_options"
        self.file.save_data(
            np.asarray(self.run_options.vol_list),
            np.asarray(self.run_options.osc_freq_list),
            np.asarray(self.run_options.delta_f_list),
            name=save_file,
        )

    async def _sync_composite_run(self):
        """Run the experiment."""
        # super().run()
        if self.run_options.tunable:
            self._set_child_exp_fringe(self.experiment_options.z_amp)
            is_drift = await self._check_drift()
        else:
            pyqlog.log(
                "EXP",
                f"{self.run_options.tq.name} target qubit is not tunable, so no need to calibrate the voltage",
            )
            is_drift = False
        if is_drift:
            await self._calculate_gradient()
        else:
            analysis_class = self.run_options.analysis_class
            self.analysis = analysis_class.empty_analysis(QualityDescribe.perfect)


class FixedSwapFreqCaliCoupler(FixedPointCalibration):
    """Fixed swap frequency calibrate one coupler z_amp of QubitPair object."""

    _sub_experiment_class = SwapOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("cali_name", str)
        options.cali_name = ""  # calibrate coupler name
        return options

    # def get_qubit_str(self):
    #     """Get qubit str, an args qubit for SaveFile class.

    #     Returns:
    #         qubit_str: string, use to create save data path.
    #     """
    #     cali_name = self.experiment_options.cali_name
    #     qp_name = self.qubit_pair.name if self.qubit_pair else ""

    #     string = f"cali({cali_name})-pair({qp_name})-{super().get_qubit_str()}"
    #     return string

    def _check_options(self):
        """check options."""
        super(FixedPointCalibration, self)._check_options()

        if self.child_experiment.qubit_pair is None:
            raise ExperimentContextError(
                f"{self._label} should be use `cz_gate_calibration` context!"
            )

        qubit_pair = self.child_experiment.qubit_pair
        z_amp = self.experiment_options.z_amp
        freq = self.experiment_options.frequency
        label = self.experiment_options.label
        threshold = self.experiment_options.threshold
        cali_name = self.experiment_options.cali_name

        if cali_name is None:
            cali_name = self.child_experiment.qubit_pair.qc
            self.experiment_options.cali_name = cali_name

        coupler_map = {coupler.name: coupler for coupler in self.couplers}
        tq_obj = coupler_map.get(cali_name)

        if tq_obj is None:
            raise ExperimentOptionsError(
                self,
                f"Set calibrate name {cali_name} not in all coupler names!",
                "cali_name",
                cali_name,
            )

        o_amp = qubit_pair.metadata.std[label].params[cali_name].amp
        initial_vol = z_amp or o_amp

        pyqlog.info(
            f"Set target swap frequency: {freq} MHz, "
            f"target name: {cali_name}, initial z_amp: {initial_vol} V"
        )

        self.set_experiment_options(z_amp=initial_vol)
        self.set_run_options(
            tq=tq_obj,
            target_osc=freq,
            initial_vol=initial_vol,
        )
        self.set_analysis_options(
            threshold=threshold,
            max_threshold=self.experiment_options.max_threshold,
            # result_name=cali_name,
            result_name=self.qubit_pair.name,
        )

    def _get_current_cali_vol(self, exp: "BaseExperiment") -> float:
        """Note current calibrate voltage value."""
        label = self.experiment_options.label
        cali_name = self.experiment_options.cali_name

        qubit_pair = exp.qubit_pair
        vol = qubit_pair.metadata.std[label].params[cali_name].amp
        return vol

    async def _run_child_experiment(
        self, z_amp: float, add_child: bool = True
    ) -> float:
        """Run child experiment and extract result data."""
        label = self.experiment_options.label
        cali_name = self.experiment_options.cali_name
        idx = self.run_options.cli_index
        target_osc = self.run_options.target_osc

        description = f"idx={idx}, z_amp={round(z_amp, 5)}"
        if idx == 0:
            description += f"target_swap_freq={target_osc}MHz"

        child_exp = self.run_options.running_exp
        child_exp.qubit_pair.metadata.std[label].params[cali_name].amp = z_amp
        child_exp.set_parent_file(self, description, idx)
        self._check_simulator_data(child_exp, idx)
        await child_exp.run_experiment()
        # child_exp.clear_params()

        vol = self._get_current_cali_vol(child_exp)
        osc_freq = child_exp.analysis.results.freq.value
        delta_f = abs(osc_freq - target_osc)

        if add_child is True:
            child_exp.analysis.provide_for_parent["delta_f"] = delta_f
            self._experiments.append(child_exp)

        self.run_options.guess_osc_freq = osc_freq
        self.run_options.vol_list.append(vol)
        self.run_options.osc_freq_list.append(osc_freq)
        self.run_options.delta_f_list.append(delta_f)

        return delta_f

    async def _sync_composite_run(self):
        """Run the experiment."""
        # super(FixedPointCalibration, self).run()

        is_drift = await self._check_drift()
        if is_drift:
            await self._calculate_gradient()
        else:
            analysis_class = self.run_options.analysis_class
            self.analysis = analysis_class.empty_analysis(QualityDescribe.perfect)

    def _set_result_path(self):
        """Set result path."""
        z_amp = self.experiment_options.z_amp
        freq = self.experiment_options.frequency
        label = self.experiment_options.label
        initial_vol = self.run_options.initial_vol
        guess_vol = self.run_options.guess_vol
        tq_name = self.run_options.tq.name

        tg_z_amp = guess_vol or initial_vol
        if self.analysis and self.analysis.results:
            for key, result in self.analysis.results.items():
                if key == "vol":
                    tg_z_amp = result.value
                    pn = "amp"
                    c_path = f"QubitPair.metadata.std.{label}.params.{tq_name}.{pn}"
                else:
                    # pn = key
                    # c_path = f"QubitPair.metadata.std.{label}.params.{tq_name}.{pn}"
                    c_path = None
                result.extra["path"] = c_path

        self.run_options.target_vol = tg_z_amp
        pyqlog.log("RESULT", f"Target {tq_name} z_amp is {tg_z_amp} V")

        file_name = f"{tq_name}_cali_result"
        note_info = (
            f"target name: {tq_name}\n"
            f"Set frequency: {freq} MHz, z_amp: {z_amp} V\n"
            f"After calibrate result z_amp: {tg_z_amp} V"
        )
        self.file.save_text(note_info, file_name)

        save_file = f"{tq_name}_fixed_point_run_options"
        self.file.save_data(
            np.asarray(self.run_options.vol_list),
            np.asarray(self.run_options.osc_freq_list),
            np.asarray(self.run_options.delta_f_list),
            name=save_file,
        )


class FixedSwapFreqCaliCoupler2(FixedPointCalibration):
    """Fixed swap frequency calibrate one coupler z_amp of QubitPair object."""

    _sub_experiment_class = SwapOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("cali_name", str)
        options.cali_name = ""  # calibrate coupler name
        options.zamp_list = None
        options.scope = {"l": 0.1, "r": 0.1, "p": 31}
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.freq_list = []
        options.zamp_list = []
        return options

    # def get_qubit_str(self):
    #     """Get qubit str, an args qubit for SaveFile class.

    #     Returns:
    #         qubit_str: string, use to create save data path.
    #     """
    #     cali_name = self.experiment_options.cali_name
    #     qp_name = self.qubit_pair.name if self.qubit_pair else ""

    #     string = f"cali({cali_name})-pair({qp_name})-{super().get_qubit_str()}"
    #     return string

    def _check_options(self):
        """check options."""
        super(FixedPointCalibration, self)._check_options()

        if self.child_experiment.qubit_pair is None:
            raise ExperimentContextError(
                f"{self._label} should be use `cz_gate_calibration` context!"
            )

        qubit_pair = self.child_experiment.qubit_pair
        z_amp = self.experiment_options.z_amp
        freq = self.experiment_options.frequency
        label = self.experiment_options.label
        threshold = self.experiment_options.threshold
        cali_name = self.experiment_options.cali_name

        if cali_name is None:
            cali_name = self.child_experiment.qubit_pair.qc
            self.experiment_options.cali_name = cali_name

        coupler_map = {coupler.name: coupler for coupler in self.couplers}
        tq_obj = coupler_map.get(cali_name)

        if tq_obj is None:
            raise ExperimentOptionsError(
                self,
                f"Set calibrate name {cali_name} not in all coupler names!",
                "cali_name",
                cali_name,
            )

        o_amp = qubit_pair.metadata.std[label].params[cali_name].amp
        initial_vol = z_amp or o_amp

        eop = self.experiment_options
        if self.experiment_options.zamp_list is None:
            left = eop.scope.get("l")
            right = eop.scope.get("r")
            point = eop.scope.get("p")
            sweep_list = np.round(
                np.linspace(initial_vol - left, initial_vol + right, point), 3
            )
            eop.zamp_list = sweep_list.tolist()
        pyqlog.info(
            f"Set target swap frequency: {freq} MHz, "
            f"target name: {cali_name}, initial z_amp: {initial_vol} V"
        )

        self.set_experiment_options(z_amp=initial_vol)
        self.set_run_options(
            tq=tq_obj,
            target_osc=freq,
            initial_vol=initial_vol,
        )
        self.set_analysis_options(
            threshold=threshold,
            max_threshold=self.experiment_options.max_threshold,
            result_name=self.qubit_pair.name,
        )

    def _get_current_cali_vol(self, exp: "BaseExperiment") -> float:
        """Note current calibrate voltage value."""
        label = self.experiment_options.label
        cali_name = self.experiment_options.cali_name

        qubit_pair = exp.qubit_pair
        vol = qubit_pair.metadata.std[label].params[cali_name].amp
        return vol

    async def _run_child_experiment(
        self, z_amp: float, add_child: bool = True
    ) -> float:
        """Run child experiment and extract result data."""
        label = self.experiment_options.label
        cali_name = self.experiment_options.cali_name
        idx = self.run_options.cli_index
        target_osc = self.run_options.target_osc

        description = (
            f"idx={idx},target_swap_freq={target_osc}MHz,"
            # f"vol={round(z_amp, 6)}v"
        )

        child_exp = deepcopy(self.run_options.running_exp)
        child_exp.qubit_pair.metadata.std[label].params[cali_name].amp = z_amp
        child_exp.set_parent_file(self, description, idx)
        self._check_simulator_data(child_exp, idx)
        await child_exp.run_experiment()
        # child_exp.clear_params()

        vol = self._get_current_cali_vol(child_exp)
        osc_freq = child_exp.analysis.results.freq.value
        delta_f = abs(osc_freq - target_osc)

        if add_child is True:
            child_exp.analysis.provide_for_parent["delta_f"] = delta_f
            self._experiments.append(child_exp)

        self.run_options.guess_osc_freq = osc_freq
        self.run_options.vol_list.append(vol)
        self.run_options.osc_freq_list.append(osc_freq)
        self.run_options.delta_f_list.append(delta_f)

        return delta_f

    async def _run_child_experiment_2(
        self, z_amp: float, add_child: bool = True, idx: int = 0
    ) -> float:
        """Run child experiment and extract result data."""
        label = self.experiment_options.label
        cali_name = self.experiment_options.cali_name
        # idx = self.run_options.cli_index
        target_osc = self.run_options.target_osc

        description = (
            f"zamp={z_amp},target_swap_freq={target_osc}MHz,"
            # f"vol={round(z_amp, 6)}v"
        )

        child_exp = deepcopy(self.run_options.running_exp)
        child_exp.qubit_pair.metadata.std[label].params[cali_name].amp = z_amp
        child_exp.set_parent_file(self, description, idx)
        self._check_simulator_data(child_exp, idx)
        await child_exp.run_experiment()
        # child_exp.clear_params()

        from pyQCat.tools import judge_exp_failed

        if not judge_exp_failed(child_exp.analysis.quality):
            results = child_exp.analysis.results
            self.run_options.freq_list.append(results.freq.value)
            self.run_options.zamp_list.append(z_amp)

    async def _check_drift(self) -> bool:
        """Check the voltage drift."""
        threshold = self.experiment_options.threshold
        initial_vol = self.run_options.initial_vol
        target_osc = self.run_options.target_osc

        pyqlog.log("EXP", f"Target osc freq is {target_osc} MHz!")

        self.run_options.running_exp = deepcopy(self.child_experiment)
        for idx, zamp in enumerate(self.experiment_options.zamp_list):
            await self._run_child_experiment_2(zamp, add_child=False)
            if np.any(self.run_options.freq_list):
                freq_list = self.run_options.freq_list
                delta_flist = abs(target_osc - np.array(freq_list))
                min_index = np.argmin(delta_flist)
                delta_f = delta_flist[min_index]
                guess_vol = self.run_options.zamp_list[min_index]

                # pyqlog.log("EXP", f"the best guess_vol:{guess_vol}V !")
                if delta_f < threshold * 2:
                    pyqlog.log("EXP", f"the best guess_vol:{guess_vol}V !")
                    drift_flag = True
                    guess_step = self.experiment_options.guess_step * 1e-3
                    self.run_options.cli_index = 0
                    self.run_options.initial_osc_freq = freq_list[min_index]
                    self.run_options.initial_vol = guess_vol
                    self.run_options.guess_vol = guess_vol + guess_step / 2
                    return drift_flag

        if np.any(self.run_options.freq_list):
            freq_list = self.run_options.freq_list
            delta_flist = abs(target_osc - np.array(freq_list))
            min_index = np.argmin(delta_flist)
            delta_f = delta_flist[min_index]
            guess_vol = self.run_options.zamp_list[min_index]

            pyqlog.log("EXP", f"the best guess_vol:{guess_vol}V !")
            drift_flag = True
            guess_step = self.experiment_options.guess_step * 1e-3
            self.run_options.cli_index = 0
            self.run_options.initial_osc_freq = freq_list[min_index]
            self.run_options.initial_vol = guess_vol
            self.run_options.guess_vol = guess_vol + guess_step
        else:
            drift_flag = True
            guess_step = self.experiment_options.guess_step * 1e-3
            self.run_options.cli_index = 0
            self.run_options.initial_osc_freq = 0
            self.run_options.guess_vol = initial_vol + guess_step

        return drift_flag

    async def _sync_composite_run(self):
        """Run the experiment."""

        # super(FixedPointCalibration, self).run()
        is_drift = await self._check_drift()
        if is_drift:
            await self._calculate_gradient()
        else:
            analysis_class = self.run_options.analysis_class
            self.analysis = analysis_class.empty_analysis(QualityDescribe.perfect)

    def _set_result_path(self):
        """Set result path."""
        z_amp = self.experiment_options.z_amp
        freq = self.experiment_options.frequency
        label = self.experiment_options.label
        initial_vol = self.run_options.initial_vol
        guess_vol = self.run_options.guess_vol
        tq_name = self.run_options.tq.name

        tg_z_amp = guess_vol or initial_vol
        if self.analysis and self.analysis.results:
            for key, result in self.analysis.results.items():
                if key == "vol":
                    tg_z_amp = result.value
                    pn = "amp"
                    c_path = f"QubitPair.metadata.std.{label}.params.{tq_name}.{pn}"
                else:
                    # pn = key
                    # c_path = f"QubitPair.metadata.std.{label}.params.{tq_name}.{pn}"
                    c_path = None
                result.extra["path"] = c_path

        self.run_options.target_vol = tg_z_amp
        pyqlog.log("RESULT", f"Target {tq_name} z_amp is {tg_z_amp} V")

        file_name = f"{tq_name}_cali_result"
        note_info = (
            f"target name: {tq_name}\n"
            f"Set frequency: {freq} MHz, z_amp: {z_amp} V\n"
            f"After calibrate result z_amp: {tg_z_amp} V"
        )
        self.file.save_text(note_info, file_name)

        save_file = f"{tq_name}_fixed_point_run_options"
        self.file.save_data(
            np.asarray(self.run_options.vol_list),
            np.asarray(self.run_options.osc_freq_list),
            np.asarray(self.run_options.delta_f_list),
            name=save_file,
        )


class ZZShiftSweetPointCalibrationNew(CompositeExperiment):
    """New case calibrate coupler sweep_point."""

    _sub_experiment_class = CouplerRamseyByZZShift

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("iteration", int)
        options.set_validator("threshold", float)
        options.set_validator("guess_step", float)
        options.set_validator("mode", ["awg_bias", "wave"])
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.iteration = 5
        options.threshold = 0.01  # MHz
        options.guess_step = 0.02  # mV
        options.mode = "awg_bias"
        options.vol_max = 0.5

        # add max delta freq threshold (unit: MHz)
        options.max_threshold = 5

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default options for analysis."""
        options = super()._default_analysis_options()

        # options.set_validator("tolerance", float)
        # options.set_validator("threshold", float)

        # options.tolerance = 0.05  # MHz
        options.threshold = 0.01  # MHz
        options.max_threshold = 5
        options.dc_max = 0.0

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options."""
        options = super()._default_run_options()

        options.cli_index = 0
        options.running_exp = None
        options.tunable = False
        options.analysis_class = CouplerSweetPointCalibrationAnalysis
        options.real_fringe = 50
        options.ac_bias = 0.0

        options.initial_vol = 0.0
        options.initial_freq = 0.0
        options.guess_vol = 0.0
        options.guess_freq = 0.0

        options.refer_z_amp = 0.0
        options.refer_delta_freq = 0.0
        options.vol_list = []
        options.freq_list = []
        options.delta_f_list = []

        options.ctrl_quality = QualityDescribe.normal

        return options

    def _check_options(self):
        """check options."""
        super()._check_options()
        threshold = self.experiment_options.threshold

        coupler_obj = self.child_experiment.coupler
        fringe = self.child_experiment.experiment_options.fringe
        z_amp = coupler_obj.cali_refer.amp
        delta_freq = coupler_obj.cali_refer.delta_freq
        result_name = coupler_obj.name

        ac_bias_keys = list(self.child_experiment.ac_bias.keys())
        if result_name not in ac_bias_keys:
            raise ExperimentFieldError(
                self.label,
                f"{result_name} not in current environment.\n{ac_bias_keys}",
            )
        ac_bias = self.child_experiment.ac_bias.get(result_name)[1]

        pyqlog.info(
            f"{result_name} cali_refer z_amp: {z_amp}, delta_freq: {delta_freq}"
        )
        self.set_run_options(
            real_fringe=fringe,
            refer_z_amp=z_amp,
            refer_delta_freq=delta_freq,
            ac_bias=ac_bias,
            initial_vol=0.0,
            tunable=coupler_obj.tunable,
        )

        self.set_analysis_options(
            dc_max=coupler_obj.dc_max,
            threshold=threshold,
            max_threshold=self.experiment_options.max_threshold,
            result_name=result_name,
        )

    async def _run_child_experiment(
        self, z_amp: float, add_child: bool = True
    ) -> float:
        """Run child experiment and extract result data."""
        vol_max = self.experiment_options.vol_max
        mode = self.experiment_options.mode
        result_name = self.analysis_options.result_name
        ac_bias = self.run_options.ac_bias
        real_fringe = self.run_options.real_fringe
        refer_delta_freq = self.run_options.refer_delta_freq
        vol = self.run_options.guess_vol
        idx = self.run_options.cli_index

        child_exp = self.run_options.running_exp
        if mode == "awg_bias":
            new_ac_bias = ac_bias + vol - child_exp.coupler.idle_point
            new_z_amp = z_amp
            child_exp.update_bias(result_name, new_ac_bias)
        else:
            new_ac_bias = ac_bias - child_exp.coupler.idle_point
            new_z_amp = z_amp + vol

        if abs(new_ac_bias) > vol_max or abs(new_z_amp) > vol_max:
            pyqlog.error(
                f"{result_name} voltage out of range, "
                f"new_ac_bias: {new_ac_bias}, new_z_amp: {new_z_amp}"
            )
            if add_child is True:
                err_delta_f = 0
            else:
                err_delta_f = 10000
            self.run_options.ctrl_quality = QualityDescribe.bad
            return err_delta_f

        description = f"idx={idx} ac_bias={new_ac_bias},z_amp={new_z_amp}"
        if idx == 0:
            description += f",delta_freq={refer_delta_freq}MHz"

        child_exp.set_parent_file(self, description, idx)
        child_exp.set_experiment_options(fringe=real_fringe, z_amp=new_z_amp)
        self._check_simulator_data(child_exp, idx)
        await child_exp.run_experiment()
        # child_exp.clear_params()

        osc_freq = child_exp.analysis.results.freq.value
        delta_freq = osc_freq - real_fringe
        delta_f = abs(refer_delta_freq - delta_freq)

        pyqlog.info(
            f"{result_name} iteration {idx} fringe: {real_fringe}, osc_freq: {osc_freq}, "
            f"delta_freq: {round(delta_freq, 3)}, "
            f"refer_delta_freq: {refer_delta_freq}, "
            f"delta_f: {delta_f}"
        )
        if add_child is True:
            child_exp.analysis.provide_for_parent["delta_f"] = delta_f
            self._experiments.append(child_exp)

        self.run_options.guess_freq = delta_freq
        self.run_options.vol_list.append(vol)
        self.run_options.freq_list.append(delta_freq)
        self.run_options.delta_f_list.append(delta_f)

        return delta_f

    async def _check_drift(self) -> bool:
        """Check the voltage drift."""
        threshold = self.experiment_options.threshold
        refer_z_amp = self.run_options.refer_z_amp
        initial_vol = self.run_options.initial_vol

        self.run_options.guess_vol = initial_vol
        self.run_options.running_exp = deepcopy(self.child_experiment)
        delta_f = await self._run_child_experiment(refer_z_amp, add_child=False)

        if delta_f <= threshold:
            drift_flag = False
            pyqlog.info(f"Stability, no calibration required: delta {delta_f} MHz")
        else:
            drift_flag = True
            guess_step = self.experiment_options.guess_step * 1e-3
            self.run_options.cli_index += 1
            self.run_options.initial_freq = self.run_options.guess_freq
            self.run_options.guess_vol = initial_vol - guess_step

        return drift_flag

    def _get_guess_vol(self, idx: int) -> float:
        """Guess next voltage value."""
        initial_vol = self.run_options.initial_vol
        initial_freq = self.run_options.initial_freq
        guess_vol = self.run_options.guess_vol
        guess_freq = self.run_options.guess_freq
        refer_delta_freq = self.run_options.refer_delta_freq

        d_vol = guess_vol - initial_vol
        d_freq = guess_freq - initial_freq
        k = d_vol / d_freq
        if abs(k) < 1e-5:
            new_k = 1e-3 if k > 0 else -1e-3
            pyqlog.warning(
                f"iteration {idx} k: {k} is too small, will change k: {new_k}"
            )
            k = new_k
        new_guess_vol = round(guess_vol - k * (guess_freq - refer_delta_freq), 6)
        if abs(guess_freq - refer_delta_freq) < abs(initial_freq - refer_delta_freq):
            self.run_options.initial_vol = guess_vol
            self.run_options.initial_freq = guess_freq

        pyqlog.info(
            f"iteration {idx} d_vol: {d_vol}, d_freq: {d_freq}, k: {k}, "
            f"guess next vol: {new_guess_vol}"
        )
        return new_guess_vol

    async def _calculate_gradient(self):
        """Calculate the voltage gradient and get the frequency."""
        threshold = self.experiment_options.threshold
        max_count = self.experiment_options.iteration
        result_name = self.analysis_options.result_name
        refer_z_amp = self.run_options.refer_z_amp

        z_amps = []
        while self.run_options.cli_index <= max_count:
            idx = self.run_options.cli_index
            pyqlog.info(
                f"{result_name} iteration {idx} voltage: {self.run_options.guess_vol} v"
            )
            self.run_options.running_exp = deepcopy(self.child_experiment)
            delta_f = await self._run_child_experiment(refer_z_amp, add_child=True)
            z_amps.append(self.run_options.guess_vol)

            if delta_f <= threshold:
                break

            self.run_options.guess_vol = self._get_guess_vol(idx)
            if abs(self.run_options.guess_vol) > self.experiment_options.vol_max:
                pyqlog.error(
                    f"{result_name} too large voltage:{self.run_options.guess_vol}"
                )
                self.run_options.ctrl_quality = QualityDescribe.bad
                break
            self.run_options.cli_index += 1
        else:
            self.run_options.ctrl_quality = QualityDescribe.bad

        ctrl_quality = self.run_options.ctrl_quality
        analysis_class = self.run_options.analysis_class
        if ctrl_quality == QualityDescribe.bad:
            pyqlog.warning(
                f"{result_name} ctrl_quality: {ctrl_quality} set bad analysis!"
            )
            self.analysis = analysis_class.empty_analysis(QualityDescribe.bad)
        else:
            self._run_analysis(z_amps, analysis_class)

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""

    async def _sync_composite_run(self):
        """Run the experiment."""
        # super().run()
        if self.run_options.tunable is True:
            is_drift = await self._check_drift()
        else:
            pyqlog.log(
                "EXP",
                f"{self.analysis_options.result_name} is not tunable, "
                f"so no need to calibrate the voltage",
            )
            is_drift = False

        if is_drift:
            await self._calculate_gradient()
        else:
            analysis_class = self.run_options.analysis_class
            self.analysis = analysis_class.empty_analysis(QualityDescribe.perfect)

        result_name = self.analysis_options.result_name
        save_file = f"{result_name}_zz_shift_run_options"
        self.file.save_data(
            np.asarray(self.run_options.vol_list),
            np.asarray(self.run_options.freq_list),
            np.asarray(self.run_options.delta_f_list),
            name=save_file,
        )


class ZZShiftFixedPointCalibration(ZZShiftSweetPointCalibrationNew):
    """New case calibrate coupler fixed point."""

    _sub_experiment_class = CouplerRamseyByZZShift

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()
        options.set_validator("z_amp", float)
        options.set_validator("delta_freq", float)

        options.mode = "wave"
        options.z_amp = 0.0
        options.delta_freq = 0.0
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default options for analysis."""
        options = super(
            ZZShiftSweetPointCalibrationNew, cls
        )._default_analysis_options()

        # options.set_validator("tolerance", float)
        # options.set_validator("threshold", float)

        # options.tolerance = 0.05  # MHz
        options.threshold = 0.01  # MHz
        options.max_threshold = 5
        options.z_amp = 0.0

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options."""
        options = super()._default_run_options()

        options.analysis_class = CouplerFixedPointCalibrationAnalysis
        return options

    def _check_options(self):
        """check options."""
        super(ZZShiftSweetPointCalibrationNew, self)._check_options()
        threshold = self.experiment_options.threshold
        z_amp = self.experiment_options.z_amp
        delta_freq = self.experiment_options.delta_freq

        coupler_obj = self.child_experiment.coupler
        fringe = self.child_experiment.experiment_options.fringe
        result_name = coupler_obj.name

        ac_bias_keys = list(self.child_experiment.ac_bias.keys())
        if result_name not in ac_bias_keys:
            raise ExperimentFieldError(
                self.label,
                f"{result_name} not in current environment.\n{ac_bias_keys}",
            )
        ac_bias = self.child_experiment.ac_bias.get(result_name)[1]

        pyqlog.info(
            f"{result_name} cali_refer z_amp: {z_amp}, delta_freq: {delta_freq}"
        )
        self.set_run_options(
            real_fringe=fringe,
            refer_z_amp=z_amp,
            refer_delta_freq=delta_freq,
            ac_bias=ac_bias,
            initial_vol=0.0,
            tunable=coupler_obj.tunable,
        )

        self.set_analysis_options(
            z_amp=z_amp,
            threshold=threshold,
            max_threshold=self.experiment_options.max_threshold,
            result_name=result_name,
        )


class FreqShiftByCoupler(ZZShiftFixedPointCalibration):
    """Frequency Shift test by Coupler."""

    async def _sync_composite_run(self):
        """Run the experiment."""
        # super().run()
        if self.run_options.tunable is True:
            is_drift = await self._check_drift()
        else:
            pyqlog.log(
                "EXP",
                f"{self.analysis_options.result_name} is not tunable, "
                f"so no need to calibrate the voltage",
            )
            is_drift = False

        if is_drift:
            await self._calculate_gradient()
        else:
            analysis_class = self.run_options.analysis_class
            self.analysis = analysis_class.empty_analysis(QualityDescribe.perfect)
            unit = "z_amp"
            self.analysis.results[unit] = AnalysisResult(
                name=unit,
                value=float(self.experiment_options.z_amp),
                extra={
                    "name": self.analysis_options.result_name,
                    "path": "Coupler.idle_point",
                },
            )

        result_name = self.analysis_options.result_name
        save_file = f"{result_name}_zz_shift_run_options"
        self.file.save_data(
            np.asarray(self.run_options.vol_list),
            np.asarray(self.run_options.freq_list),
            np.asarray(self.run_options.delta_f_list),
            name=save_file,
        )
