# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/12/20
# __author:       <PERSON><PERSON><PERSON><PERSON>


import json
from copy import deepcopy
from itertools import permutations
from math import factorial

import numpy as np
import pandas as pd
from numpy.linalg import inv
from scipy.linalg import expm

np.set_printoptions(
    precision=3,
    formatter={
        'float_kind': '{:.2f}'.format,  # 浮点型数据格式
        # 'complex_kind': '{:.3f}'.format  # 复数型数据格式
    },
    suppress=True
)


def integer_decompose(n, max_num=None):
    """
    将整数分解成所有可能的更小的整数组合, e.g: 3 -> [[1, 1, 1], [2, 1], [3]]
    """
    if n == 0:
        return [[]]
    if max_num is None or max_num > n:
        max_num = n
    
    decompositions = []
    for i in range(1, max_num + 1):
        for decomposition in integer_decompose(n - i, i):
            decompositions.append([i] + decomposition)
    
    return decompositions


def m_order_set(m: int):
    """
    返回一个整数所有可能的分解字典，并将分解中最大整数相同的列表归类到同一个key下
    e.g: 3 -> {
        3: (3, )
        2: (2, 1)  (1, 2)
        1: (1, 1, 1)
    """
    decompositions = integer_decompose(m)
    m_set = {idx: [] for idx in range(1, m + 1)}

    for decom in decompositions:
        # idx = m + 1 - max(decom)  # 原始文献中举例的m=5的系数分类有错误，应该按照项数分类
        idx = len(decom)
        m_set[idx].extend(list(set(permutations(decom))))

    return m_set


def comm(*mats):
    mats = list(mats)
    if len(mats) == 2:
        return mats[0] @ mats[1] - mats[1] @ mats[0]
    
    last_comm = comm(mats[-2], mats[-1])
    return comm(*mats[:-2], last_comm)


def Sjkom(H0j: np.ndarray, H0k: np.ndarray, Hxom_jk: np.ndarray):
    """
    返回第k阶的Sjk
    Sjkom = mat[(tensor(H0j, eye(H0k)) - tensor(eye(H0j), H0k.T))^-1 * vec(-1j * Hxom_jk)]
    ref: MAGESAN E, GAMBETTA J M. Effective Hamiltonian models of the cross-resonance gate [J]. Physical Review A, 2020, 101(5).
    """
    Ik = np.eye(*H0k.shape)
    Ij = np.eye(*H0j.shape)
    Sjkom = inv(np.kron(H0j, Ik) - np.kron(Ij, H0k.T)) @ np.reshape(-1j * Hxom_jk, (-1, 1))
    Sjkom = Sjkom.reshape(H0j.shape[0], H0k.shape[0])
    return Sjkom


def Hxom(
    H0: np.ndarray, V: np.ndarray,
    S_set: dict,
    m_set: dict = None, m1_set: dict = None,
    flag_print: bool = False
):
    """
    H^(m)({Sj}_j=1^m, H0, V) = i[Sm, H0] + H_x^(m)({Sj}_j=1^m-1, H0, V)
    H_x^(m)({Sj}_j=1^m-1, H0, V) = fn({Sj}_j=1^n-1, H0) + fn-1({Sj}_j=1^n-1, V)

    Args:
        H0 (np.ndarray): _description_
        V (np.ndarray): _description_
        S_set (dict): 如果需要计算m阶，需要{S1: , S2: , ..., Sm-1: }
        m_set (dict, optional): _description_. Defaults to None.
        m1_set (dict, optional): _description_. Defaults to None.
    """
    m = len(S_set) + 1
    m_set = m_order_set(m) if m_set is None else m_set
    if m1_set is None:
        if m != 1:
            m1_set = m_order_set(m-1)
        else:  
            m1_set = {0: None}

    m_set = deepcopy(m_set)  # 这里为了数据隔离一定要deepcopy!!!
    m1_set = deepcopy(m1_set)  # 这里为了数据隔离一定要deepcopy!!!
    m_set.pop(1, None)  # 计算Hx^(m)时，H0的相关对易子只需计算到m-1阶 

    terms_H0 = []
    for idx, tup_list in m_set.items():
        coe = 1j ** idx / factorial(idx)
        
        for tup in tup_list:
            comm_idx = coe * comm(*[S_set.get(i) for i in tup], H0)
            terms_H0.append(comm_idx)

    terms_V = []
    for idx, tup_list in m1_set.items():
        if idx == 0: # 一阶项直接加V，不用计算对易子
            terms_V.append(V)
            continue

        coe = 1j ** idx / factorial(idx)
        
        for tup in tup_list:
            comm_idx = coe * comm(*[S_set.get(i) for i in tup], V)
            terms_V.append(comm_idx)

    if flag_print:
        desc, desc_H0, desc_V = str_Hom(m)
        print(desc)

    return sum(terms_H0 + terms_V)


def str_Hom(
    m: int
):
    """生成第m阶下哈密顿量表达式的字符串，方便验证表达式是否正确
    """
    m_set = m_order_set(m) 
    m1_set = m_order_set(m-1) if m != 1 else {0: None}
    # m_set.pop(1, None)  # 计算Hx^(m)时，H0的相关对易子只需计算到m-1阶 

    desc_H0 = []  # 所有包含H0项的字符串描述，方便验证表达式是否正确
    for idx, tup_list in m_set.items():
        if idx == 1:
            desc_H0.append(f'i[S{m}, H0]')  # idx=1对应最高阶项
            continue

        desc_idx = f'i^{idx}/{idx}! * ('  # 某一个系数下的所有项
        
        desc_comm = []  # 某一个系数下的所有对易子
        for tup in tup_list:
            desc_comm_tup = [f'[S{i},' for i in tup]
            desc_comm.append(''.join(desc_comm_tup) + 'H0' + f'{"]" * len(tup)}')

        desc_idx += ' + '.join(desc_comm) + ')'
        desc_H0.append(desc_idx)

    desc_H0 = ' + \n'.join(desc_H0)

    desc_V = []
    for idx, tup_list in m1_set.items():
        if idx == 0:
            desc_V.append(f'V')  # idx=1对应最高阶项
            continue

        if idx == 1:
            desc_V.append(f'i[S{m-1}, V]')  # idx=1对应最高阶项
            continue

        desc_idx = f'i^{idx}/{idx}! * ('  # 某一个系数下的所有项

        desc_comm = []  # 某一个系数下的所有对易子
        for tup in tup_list:
            desc_comm_tup = [f'[S{i},' for i in tup]
            desc_comm.append(''.join(desc_comm_tup) + 'V' + f'{"]" * len(tup)}')

        desc_idx += ' + '.join(desc_comm) + ')'
        desc_V.append(desc_idx)
    
    desc_V = ' + \n'.join(desc_V)

    desc = f'lam^{m} * ' + '{\n' + desc_H0 + ' +\n' + desc_V + '\n}\n'
    return desc, desc_H0, desc_V


def SWT(order: int, H: np.ndarray, splits: list):
    """_summary_

    Args:
        order (int): SWT保留至几阶
        H (np.ndarray): 完整哈密顿量
        splits (list): 将矩阵切割为几块block，每一个元素表示block包含的行数/列数而不是索引
    """
    # H = np.asarray(H, dtype=np.complex_)
    assert sum(splits) == H.shape[0], f'Please make sure the sum of splits={sum(splits)} match with the H dims'
    idxs = [0]
    for s in splits:
        idxs.append(idxs[-1] + s)
    
    slice_set = {i: slice(idxs[i], idxs[i+1]) for i in range(len(idxs) - 1)}
    H0_block = {}
    V_block = {}
    H0 = np.zeros_like(H)
    V = np.zeros_like(H)

    for i, sli in slice_set.items():
        H0[sli, sli] = H[sli, sli]
        H0_block[i] = H[sli, sli]

    for j, k in permutations(range(len(slice_set)), 2):
        V[slice_set[j], slice_set[k]] = H[slice_set[j], slice_set[k]]
        V_block[(j, k)] = H[slice_set[j], slice_set[k]]

    # print(f'slice_set: \n{slice_set}')
    # print(f'H0_block: \n{H0_block}')
    # print(f'V_block: \n{V_block}')
    # print(f'H0: \n{H0}')
    # print(f'V: \n{V}')

    # 创建系数集合，结构：{order: m_order_set(oerder), order-1: m_order_set(order-1)}
    order_set = {o: m_order_set(o) for o in range(1, order+1)}

    S_set = {}
    Hswt = H0.astype(np.complex_)
    for m in range(1, order + 1):
        # 1. 计算第m阶下的H_x^(m)，它是{Sj}_j=1^m-1, H0, V的函数
        if m == 1:
            Hx_m = V
        else:
            Hx_m = Hxom(H0, V, S_set, order_set[m], order_set[m-1])

        # 2. 计算m阶下的Sm，并将Sm更新进S_set用于下一阶H_x^(m)的计算
        S_m = np.zeros_like(H, dtype=np.complex_)
        for j, k in permutations(range(len(slice_set)), 2):
            Sjk_m = Sjkom(H0_block[j], H0_block[k], Hx_m[slice_set[j], slice_set[k]])
            S_m[slice_set[j], slice_set[k]] = Sjk_m
        S_set[m] = S_m

        Hswt += 1j * comm(S_m, H0) + Hx_m
        # print(f'Hswt(order={m}):\n{Hswt.real}')

    Hswt = Hswt.real

    json_S_set = {key: [str(v) for v in np.around(value, 4)] for key, value in S_set.items()}
    json_S_set = json.dumps(json_S_set, indent=4)
    # print(f'S_set:\n{json_S_set}')

    Hswt2 = expm(1j * sum(S_set.values())) @ H @ expm(-1j * sum(S_set.values()))
    Hswt2 = Hswt2.real
    Hsswt = expm(-1j * sum(S_set.values())) @ Hswt @ expm(1j * sum(S_set.values()))
    Hsswt = Hsswt.real
    Hres = Hsswt - H.real

    # print(f'Hswt:\n{Hswt}')
    # print(f'Hswt from e^iS @ H e^-iS:\n{Hswt2}')
    # print(f'SWT^-1(Hswt):\n{Hsswt}')
    # print(f'H - SWT^-1(Hswt):\n{Hres}')
    return Hswt, Hres


def H2Q1C(w_set, gnn_set, gnnn_set):
    """

    Args:
        w_set (_type_): wQ-wC-wQ
        gnn_set (_type_): 最近邻耦合, 即gqc
        gnnn_set (_type_): 次近邻耦合, 即gqq

    Returns:
        _type_: _description_
    """
    H0 = np.diag(w_set)
    V = np.diag(gnn_set, 1) + np.diag(gnn_set, -1)
    V += np.diag(gnnn_set, 2) + np.diag(gnnn_set, -2)
    H = H0 + V
    # 将Q-C-Q变换成2Q-1C的形式
    H[[1,2], :] = H[[2,1], :]  
    H[:, [1,2]] = H[:, [2,1]]

    print(f'H2Q1C:\n{H}\n')
    return H


def H3Q2C(w_set, gnn_set, gnnn_set):
    H0 = np.diag(w_set)
    V = np.diag(gnn_set, 1) + np.diag(gnn_set, -1)
    V += np.diag(gnnn_set, 2) + np.diag(gnnn_set, -2)
    H = H0 + V
    # 将Q-C-Q-C-Q变换成3Q-2C的形式
    H[[1,2], :] = H[[2,1], :]  
    H[:, [1,2]] = H[:, [2,1]]
    H[[2, 4], :] = H[[4, 2], :]
    H[:, [2, 4]] = H[:, [4, 2]]
    H[[3, 4], :] = H[[4, 3], :]
    H[:, [3, 4]] = H[:, [4, 3]]

    print(f'H3Q2C:\n{H}\n')
    return H


def H2Q1Ce2(w_set, gnn_set, gnnn_set, eta_set):
    """双激发子空间下的Q-C-Q哈密顿量

    Args:
        w_set (_type_): _description_
        eta_set (_type_): _description_
        gnn_set (_type_): _description_
        gnnn_set (_type_): _description_
    """
    wqh, wqb, wql = w_set
    g_hb, g_lb = gnn_set
    g_hl = gnnn_set[0]
    e_hb, e_lb = np.sqrt(2) * np.array(gnn_set)
    e_hl = np.sqrt(2) * g_hl

    eta_set = - np.abs(eta_set)
    l200 = 2 * wqh + eta_set[0]
    l101 = wqh + wql
    l011 = wqb + wql
    l110 = wqh + wqb
    l020 = 2 * wqb + eta_set[1]
    l002 = 2 * wql + eta_set[2]

    H = np.array([
        [l200, e_hl, 0000, 0000, e_hb, 0000],
        [e_hl, l101, e_hl, g_hb, g_lb, 0000],
        [0000, e_hl, l002, e_lb, 0000, 0000],
        [0000, g_hb, e_lb, l011, g_hl, e_lb],
        [e_hb, g_lb, 0000, g_hl, l110, e_hb],
        [0000, 0000, 0000, e_lb, e_hb, l020],

    ])

    print(f'H2Q1Ce2:\n{np.around(H, 2)}\n')
    return H


if __name__ == '__main__':
    # # ########## 2Q + 1C ########## 
    gnn_set = [125., 120.]
    gnnn_set = [10.]
    # H = H2Q1C(
    #     [4094, 5000, 4358],
    #     gnn_set, gnnn_set
    #     )
    # Hswt, Hres = SWT(
    #     10,
    #     H,
    #     splits=[2, 1]
    # )

    H = H2Q1Ce2(
        [4358, 5000, 4094],
        gnn_set, gnnn_set, eta_set=[240] * 3
    )

    Hswt, Hres = SWT(
        4,
        H,
        splits=[2, 4]
    )

    # # ########## 3Q+2C ########## 
    # w_set = [4094, 6500, 4658, 6520, 4000]
    # gnn_set = [125., 120., 130., 122.]
    # gnnn_set = [10., 0., 8.]
    # H = H3Q2C(w_set, gnn_set, gnnn_set)

    # Hswt, Hres = SWT(
    #     10, 
    #     H,
    #     splits=[2, 1]
    # )

