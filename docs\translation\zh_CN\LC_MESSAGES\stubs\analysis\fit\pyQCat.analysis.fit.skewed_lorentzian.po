# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/fit/pyQCat.analysis.fit.skewed_lorentzian.rst:2
msgid "pyQCat.analysis.fit.skewed\\_lorentzian"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.skewed_lorentzian:1
msgid "skewed lorentzian formula."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.skewed_lorentzian:3
msgid ""
"y = A1 + A2(x - fr) + \\frac{A3 + A4\\cdot (x-fr)}{1 + "
"4{Q_1}^2(\\frac{x-fr}{fr} )^2}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.skewed_lorentzian
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.skewed_lorentzian:7
msgid ":py:class:`~numpy.ndarray`"
msgstr ""

#~ msgid "Parameters"
#~ msgstr ""

#~ msgid "independent variable"
#~ msgstr ""

#~ msgid "fitted parameter"
#~ msgstr ""

#~ msgid "lorentz peak or valley corresponding `x` value"
#~ msgstr ""

#~ msgid "reflecting the narrowness of the valley"
#~ msgstr ""

#~ msgid "Returns"
#~ msgstr ""

#~ msgid "strain variable"
#~ msgstr ""

