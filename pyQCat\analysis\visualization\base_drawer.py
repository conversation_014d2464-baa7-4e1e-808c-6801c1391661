# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/05/24
# __author:       <PERSON><PERSON><PERSON> Shi

from abc import ABC, abstractmethod
from typing import Sequence, Optional

from matplotlib.backends.backend_svg import FigureCanvasSVG
from matplotlib.figure import Figure
import platform
from ...structures import Options

if platform.system() == "Windows":
    DRAW_FONT = "Times New Roman"
else:
    DRAW_FONT = "DejaVu Sans"


def get_ax(sub_shape=(1, 1), default_figure_canvas=FigureCanvasSVG):
    """Return a matplotlib axes that can be used in a child thread.

    Analysis/plotting is done in a separate thread (so it doesn't block the
    main thread), but matplotlib doesn't support GUI mode in a child thread.
    This function creates a separate Figure and attaches a non-GUI
    SVG canvas to it.

    Returns:
        matplotlib.axes.Axes: A matplotlib axes that can be used in a child thread.
    """
    # figure = Figure()
    figure = Figure(tight_layout=True)
    _ = default_figure_canvas(figure)
    return figure, figure.subplots(*sub_shape)


def get_tomo_fig(default_figure_canvas=FigureCanvasSVG):
    figure = Figure()
    # _ = default_figure_canvas(figure)
    return figure


class BaseCurveDrawer(ABC):
    """Abstract class for the serializable Qiskit Experiments curve drawer.

    A curve drawer may be implemented by different drawing backends such as matplotlib
    or plotly. Sub-classes that wrap these backends by subclassing `BaseCurveDrawer` must
    implement the following abstract methods.

    initialize_canvas

        This method should implement a protocol to initialize a drawing canvas
        with user input ``axis`` object. Note that curve analysis drawer
        supports visualization of experiment results in multiple canvases
        tiled into N (row) x M (column) inset grids, which is specified in the option ``subplots``.
        By default, this is N=1, M=1 and thus no inset grid will be initialized.
        The data points to draw might be provided with a canvas number defined in
        :attr:`SeriesDef.canvas` which defaults to ``None``, i.e. no-inset grids.

        This method should first check the drawing options for the axis object
        and initialize the axis only when it is not provided by the options.
        Once axis is initialized, this is set to the instance member ``self._axis``.

    format_canvas

        This method should implement a protocol to format the appearance of canvas.
        Typically, it updates axis and tick labels. Note that the axis SI unit
        may be specified in the drawing options. In this case, axis numbers should be
        auto-scaled with the unit prefix.

    draw_raw_data

        This method is called after data processing is completed.
        This method draws raw experiment data points on the canvas.

    draw_fit_line

        This method is called after fitting is completed and when there is valid fit outcome.
        This method is called with the interpolated x and y values.

    draw_text

        This method is called after fitting is completed and when there is valid fit outcome.
        This method is called with the interpolated x and a pair of y values
        that represent the upper and lower bound within certain confidence interval.
        This might be called multiple times with different interval sizes.

    draw_fit_report

        This method is called after fitting is completed and when there is valid fit outcome.
        This method is called with the list of analysis results and the reduced chi-squared values.
        The fit report should be generated to show this information on the canvas.

    """

    def __init__(self):
        self._options = self._default_options()
        self._axis = None
        self._figure = None

    @property
    def options(self) -> Options:
        """Return the drawing options."""
        return self._options

    @property
    def figure(self) -> Figure:
        """Return figure object handler to be saved in the database.

        In the MatplotLib the ``Figure`` and ``Axes`` are different object.
        User can pass a part of the figure (i.e. multi-axes) to the drawer option ``axis``.
        For example, a user wants to combine two different experiment results in the
        same figure, one can call ``pyplot.subplots`` with two rows and pass one of the
        generated two axes to each experiment drawer. Once all the experiments complete,
        the user will obtain the single figure collecting all experimental results.

        Note that this method returns the entire figure object, rather than a single axis.
        Thus, the experiment data saved in the database might have a figure
        collecting all child axes drawings.
        """
        return self._figure

    @classmethod
    def _default_options(cls) -> Options:
        """Return default draw options.

        Draw Options:
            axis (Any): Arbitrary object that can be used as a drawing canvas.
            subplots (Tuple[int, int]): Number of rows and columns when the experimental
                result is drawn in the multiple windows.
            xlabel (Union[str, List[str]]): X-axis label string of the output figure.
                If there are multiple columns in the canvas, this could be a list of labels.
            ylabel (Union[str, List[str]]): Y-axis label string of the output figure.
                If there are multiple rows in the canvas, this could be a list of labels.
            xlim (Tuple[float, float]): Min and max value of the horizontal axis.
                If not provided, it is automatically scaled based on the input data points.
            ylim (Tuple[float, float]): Min and max value of the vertical axis.
                If not provided, it is automatically scaled based on the input data points.
            xval_unit (str): SI unit of x values. No prefix is needed here.
                For example, when the x values represent time, this option will be just "s"
                rather than "ms". In the output figure, the prefix is automatically selected
                based on the maximum value in this axis. If your x values are in [1e-3, 1e-4],
                they are displayed as [1 ms, 10 ms]. This option is likely provided by the
                analysis class rather than end-users. However, users can still override
                if they need different unit notation. By default, this option is set to ``None``,
                and no scaling is applied. If nothing is provided, the axis numbers will be
                displayed in the scientific notation.
            yval_unit (str): Unit of y values. See ``xval_unit`` for details.
            figsize (Tuple[int, int]): A tuple of two numbers representing the size of
                the output figure (width, height). Note that this is applicable
                only when ``axis`` object is not provided. If any canvas object is provided,
                the figure size associated with the axis is preferentially applied.
            legend_loc (str): Vertical and horizontal location of the curve legend window in
                a single string separated by a space. This defaults to ``upper right``.
                Vertical position can be ``upper``, ``center``, ``lower``.
                Horizontal position can be ``right``, ``center``, ``left``.
            tick_label_size (int): Size of text representing the axis tick numbers.
            axis_label_size (int): Size of text representing the axis label.
            fit_report_rpos (Tuple[int, int]): A tuple of numbers showing the location of
                the fit report window. These numbers are horizontal and vertical position
                of the top left corner of the window in the relative coordinate
                on the output figure, i.e. ``[0, 1]``.
                The fit report window shows the selected fit parameters and the reduced
                chi-squared value.
            fit_report_text_size (int): Size of text in the fit report window.
            plot_sigma (List[Tuple[float, float]]): A list of two number tuples
                showing the configuration to write confidence intervals for the fit curve.
                The first argument is the relative sigma (n_sigma), and the second argument is
                the transparency of the interval plot in ``[0, 1]``.
                Multiple n_sigma intervals can be drawn for the single curve.
            text_pos (List[Tuple]): The position of the annotations to place on the figure.
            text_rp (List[str]): The contents of the annotations to place on the figure.
            text_key (List[str]): Indicates the data key to annotate.
        """
        options = Options(
            axis=None,
            subplots=(1, 1),
            xlabel=None,
            ylabel=None,
            xlim=None,
            ylim=None,
            title=None,
            sub_title=None,
            figsize=(10, 9),
            logarithm=None,
            legend_loc="best",
            axis_legend={
                "family": DRAW_FONT,
                "weight": "normal",
                "size": 20,
            },
            axis_title={
                "fontfamily": DRAW_FONT,
                "fontweight": "bold",
                "fontsize": 20,
            },
            sup_title={
                "fontfamily": DRAW_FONT,
                "fontweight": "bold",
                "fontsize": 20,
            },
            tick_label_size=18,
            axis_label={
                "fontfamily": DRAW_FONT,
                "fontweight": "bold",
                "fontsize": 20,
            },
            default_colors=["orangered", "blueviolet"],
            text_pos=None,
            text_rp=None,
            text_key=None,
            raw_data_format="scatter",
            marker="o"
        )
        return options

    def set_options(self, **fields):
        """Set the drawing options.
        Args:
            fields: The fields to update the options
        """
        self._options.update(**fields)

    @abstractmethod
    def initialize_canvas(self):
        """Initialize the drawing canvas."""

    @abstractmethod
    def format_canvas(self):
        """Final cleanup for the canvas appearance."""

    def draw_raw_data(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        ax_index: Optional[int] = None,
        **options,
    ):
        """Draw raw data.

        Args:
            x_data: X values.
            y_data: Y values.
            ax_index: Index of canvas if multiple inset axis exist.
            options: Valid options for the drawer backend API.
        """

    def draw_filter_data(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        ax_index: Optional[int] = None,
        **options,
    ):
        """Draw smooth data

        Args:
            x_data: X values.
            y_data: Y values.
            ax_index: Index of canvas if multiple inset axis exist.
            options: Valid options for the drawer backend API.
        """
        pass

    def draw_fit_line(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        ax_index: Optional[int] = None,
        **options,
    ):
        """Draw fit line.

        Args:
            x_data: X values.
            y_data: Fit Y values.
            ax_index: Index of canvas if multiple inset axis exist.
            options: Valid options for the drawer backend API.
        """

    def draw_text(self, ax_index: Optional[int] = None, **options):
        """
        Add text to the axes.
        Add the text *s* to the axes at location *x*, *y* in data coordinates.
        """
        pass

    def draw_color_map(
        self,
        x_data: Sequence[float],
        y_data: Sequence[float],
        z_data: Sequence[float],
        **options,
    ):
        """
        using norm to map colormaps onto data in non-linear ways
        Args:
            x_data:
            y_data:
            z_data:
        """
        pass
