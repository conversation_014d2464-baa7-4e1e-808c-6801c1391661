# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/4/18
# __author:       <PERSON><PERSON><PERSON>
# __corporation:  OriginQuantum

from typing import Any, List, Optional, Type, Union

import numpy as np

from pyQCat.acquisition.acq_tackle import UnionMode
from pyQCat.acquisition.acquisition import (
    DataAcquisition,
    SimulateAcquisition,
    SimulateSQMCAcquisition,
    SQMCAcquisition,
    WeirdoAcquisition,
)
from pyQCat.analysis import CurveAnalysis, TopAnalysis
from pyQCat.analysis.algorithms.iqprobability import IQdiscriminator, get_p_labels
from pyQCat.qubit import Qubit
from pyQCat.structures import ExperimentData, MetaData, QDict
from pyQCat.tools.savefile import BaseFile
from pyQCat.tools.serialization import pick_to_binary_data
from pyQCat.tools.utilities import display_dict_as_table
from pyQCat.types import QualityDescribe

AcquisitionClassMap = {
    "normal": DataAcquisition,
    "simulator": SimulateAcquisition,
    "weirdo": WeirdoAcquisition,
    "sqmc": SQMCAcquisition,
    "sqmc_simulator": SimulateSQMCAcquisition,
}


def generate_acquisition_options(self, require_id):
    # get discriminator
    if self.run_options.online_dcms:
        discriminator = self.run_options.online_dcms
    else:
        discriminator = self.discriminator

    # get sweet points
    sweep_points = self.run_options.sweep_points

    # get acq class
    acq_class = AcquisitionClassMap.get(self.run_options.acquisition_key)

    # DataAcquisition args `measure_qubits` type is list of string.
    note_measure_qubits = self.run_options.measure_qubits or []
    measure_qubits = [
        q_obj.name if isinstance(q_obj, Qubit) else q_obj
        for q_obj in note_measure_qubits
    ]

    # get sample channels
    # sample_channels = self.run_options.sample_channels
    # bugfix: Ensure that the order of sample_channel and measure-bit is consistent
    physical_units_map = self.physical_units_map()
    sample_channels = [
        physical_units_map[bit].readout_channel for bit in measure_qubits
    ]

    # build acquisition_options
    acquisition_options = QDict(
        id_=require_id,
        data_type=self.experiment_options.data_type,
        discriminator=discriminator,
        is_dynamic=self.experiment_options.is_dynamic,
        is_amend=self.experiment_options.is_amend,
        fidelity_correct_type=self.experiment_options.fidelity_correct_type,
        post_select_type=self.experiment_options.post_select_type,
        measure_qubits=measure_qubits,
        simulator_data_path=self.run_options.simulator_data_path,
        # simulator_remote_path=self.experiment_options.simulator_remote_path,
        save_path=self.file.dirs,
        sample_channels=sample_channels,
        simulator_name=self.run_options.simulator_name,
        use_simulator=self.run_options.use_simulator,
        simulator_index=self.run_options.simulator_index,
        config=self.config,
        plot_iq=self.experiment_options.plot_iq,
        is_retry=False,
        repeat=self.experiment_options.repeat,
        loop=len(sweep_points),
        loop_num=self.experiment_options.loop_num,
        online_bits=self.run_options.online_bits,
        exp_name=str(self),
        measure_modes=self.run_options.measure_modes,
        prepare_measure_bits=self.run_options.prepare_measure_bits,
        is_parallel=self.run_options.parallel,
        acq_index=self.experiment_options.acq_index,
        x_data2=self.run_options.x_data2,
        union_mode=self.experiment_options.union_mode or "union",
    )

    return acq_class, acquisition_options


def parallel_top_experiment_analysis(
    x_data: Union[List, np.ndarray],
    acq_class: Type[DataAcquisition],
    analysis_class: Type[TopAnalysis],
    acquisition_options: QDict,
    analysis_options: QDict,
    qubits: List[Qubit],
    metadata: MetaData,
    experiment_file: BaseFile,
    experiment_data: Optional[ExperimentData] = None,
):
    for options in [acquisition_options, analysis_options]:
        object.__setattr__(options, "__parent", None)
        object.__setattr__(options, "__key", None)
        object.__setattr__(options, "__frozen", False)

    # _initialize_data_acquisition
    data_acquisition = acq_class(**acquisition_options)
    data_acquisition.x_list = x_data

    if experiment_data is None:
        data_acquisition.execute_loop()

        if data_acquisition.error:
            return data_acquisition.error

    # base analysis flow
    return base_analysis_process(
        data_acquisition,
        acquisition_options,
        qubits,
        metadata,
        experiment_file,
        analysis_options,
        analysis_class,
        experiment_data,
    )


async def top_experiment_analysis(
    x_data: Union[List, np.ndarray],
    acq_class: Type[DataAcquisition],
    analysis_class: Type[TopAnalysis],
    acquisition_options: QDict,
    analysis_options: QDict,
    qubits: List[Qubit],
    metadata: MetaData,
    experiment_file: BaseFile,
    experiment_data: Optional[ExperimentData] = None,
) -> Union[TopAnalysis, Any]:
    # _initialize_data_acquisition
    data_acquisition = acq_class(**acquisition_options)
    data_acquisition.x_list = x_data

    # data acquisition
    use_simulator = acquisition_options.use_simulator

    if experiment_data is None:
        if use_simulator is True:
            data_acquisition.execute_loop()
        else:
            await data_acquisition.async_execute_loop()
        if data_acquisition.error:
            return data_acquisition.error

    # base analysis flow
    return base_analysis_process(
        data_acquisition,
        acquisition_options,
        qubits,
        metadata,
        experiment_file,
        analysis_options,
        analysis_class,
        experiment_data,
    )


def base_analysis_process(
    data_acquisition: DataAcquisition,
    acquisition_options: QDict,
    qubits: List[Qubit],
    metadata: MetaData,
    experiment_file: BaseFile,
    analysis_options: QDict,
    analysis_class: Type[TopAnalysis],
    experiment_data: Optional[ExperimentData] = None,
):
    # create experiment data
    if experiment_data is None:
        experiment_data = _create_experiment_data(
            data_acquisition, acquisition_options, qubits, metadata, analysis_options
        )
    elif analysis_options.data_key and data_acquisition.multiple_wrapper:
        y_keys = list(experiment_data.y_data.keys())
        new_data_key = []

        if "all" in analysis_options.data_key:
            analysis_options.data_key = y_keys
        else:
            for set_data_type in analysis_options.data_key:
                if set_data_type in y_keys:
                    new_data_key.append(set_data_type)
                elif isinstance(set_data_type, int) and set_data_type < len(y_keys):
                    new_data_key.append(y_keys[set_data_type])
            analysis_options.data_key = new_data_key

    if experiment_data:
        # run common analysis process
        return run_analysis_process(
            analysis_class, experiment_data, analysis_options, experiment_file
        )

    return CurveAnalysis.empty_analysis(QualityDescribe.bad)


def run_analysis_process(
    analysis_class: Type[TopAnalysis],
    experiment_data: ExperimentData,
    analysis_options: QDict,
    experiment_file: Optional[BaseFile] = None,
):
    save_s3 = analysis_options.get("save_s3", False)

    # save origin experiment data
    if experiment_file:
        # serialized ExperimentData and save to local
        save_exp_data = analysis_options.get("save_exp_data", True)
        if save_exp_data:
            experiment_data.metadata.process_meta.update(
                dict(
                    ana_class_name=analysis_class.__name__,
                    analysis_options=analysis_options,
                )
            )
            serialized_data = pick_to_binary_data(experiment_data)
            experiment_file.save_text(
                text=serialized_data,
                name=str(experiment_data),
                prefix=".epd",
                save_s3=True,
            )

        # save origin experiment data
        _save_experiment_data(experiment_data, experiment_file)

    if analysis_options.pure_exp_mode is False:
        # Determine whether there are sub data in the experimental data
        has_child = True if experiment_data.child_data() else False

        # create analysis object.
        analysis_obj = analysis_class(experiment_data, has_child=has_child)

        # update options.
        analysis_obj.set_options(**analysis_options)

        # run analysis.
        analysis_obj.run_analysis()

        # update analysis result
        analysis_obj.update_result()

        # save result to local
        if experiment_file:
            _save_analysis_data_and_picture(analysis_obj, experiment_file, save_s3)

            for ana in analysis_obj.options.sub_analysis:
                _save_analysis_data_and_picture(ana, experiment_file, save_s3)

            experiment_file.wait_thread_clear()

        return analysis_obj

    return CurveAnalysis.empty_analysis(QualityDescribe.bad)


def _create_experiment_data(
    data_acquisition: DataAcquisition,
    acquisition_options: QDict,
    qubits: List[Qubit],
    metadata: MetaData,
    analysis_options: QDict,
):
    if isinstance(data_acquisition, SQMCAcquisition):
        x_data = np.array(data_acquisition.x_list)
        y_data = data_acquisition.y_data
    else:
        # Experimental data can only be created if the collection is successful
        # x_data and loop num from data acquisition
        x_data = np.array(data_acquisition.x_list)
        loop_num = acquisition_options.loop_num

        # default origin y data and correct data
        y_data, correct_data = {}, {}

        if acquisition_options.data_type == "amp_phase":
            y_data = {
                "Amp": np.array(data_acquisition.amp),
                "Phase": np.array(data_acquisition.phase),
            }
        elif acquisition_options.data_type == "I_Q":
            if data_acquisition.multiple_wrapper:
                if data_acquisition.multiple_wrapper._union_mode == UnionMode.AU:
                    return
                y_data = {}
                for k, data in data_acquisition.multiple_wrapper.record_data.items():
                    y_data[k] = np.array(data)

                new_data_key = []
                result_labels = data_acquisition.multiple_wrapper.result_labels

                if analysis_options.data_key:
                    if "all" in analysis_options.data_key:
                        new_data_key = result_labels
                    else:
                        for key in analysis_options.data_key:
                            if isinstance(key, int):
                                new_data_key.append(
                                    data_acquisition.multiple_wrapper.result_labels[key]
                                )
                            else:
                                new_data_key.append(key)
                    analysis_options.data_key = new_data_key
                else:
                    analysis_options.data_key = result_labels[-2:]
            elif acquisition_options.online_bits or isinstance(
                acquisition_options.discriminator, List
            ):
                # feature: when use online ctx, default multiple discriminator
                p_labels = get_p_labels(acquisition_options.discriminator)
                prob_arr = np.array(data_acquisition.probability_list).T
                if acquisition_options.is_amend:
                    prob_c_arr = np.array(data_acquisition.probability_c_list).T
                    for i, p_label in enumerate(p_labels):
                        correct_data.update({p_label: prob_c_arr[i]})
                y_data = {}
                for i, p_label in enumerate(p_labels):
                    y_data.update({p_label: prob_arr[i]})
            elif isinstance(data_acquisition.discriminator, IQdiscriminator):
                p_labels = get_p_labels(data_acquisition.discriminator)
                y_data = {}
                for p_label in p_labels:
                    p_list = getattr(data_acquisition, p_label)
                    y_data.update({p_label: np.array(p_list)})
                    if acquisition_options.is_amend:
                        c_p_label = "C" + p_label
                        c_p_list = getattr(data_acquisition, c_p_label) or p_list
                        correct_data.update({p_label: np.array(c_p_list)})
            else:
                length = len(qubits)
                if length > 1:
                    # Union Readout IQ data
                    repeat = data_acquisition.repeat
                    col = repeat * loop_num
                    total = len(data_acquisition.I)
                    data_collector = []
                    loop_index = 0
                    while loop_index < total:
                        data_collector.append(
                            np.asarray(
                                data_acquisition.I[loop_index : loop_index + loop_num]
                            )
                        )
                        data_collector.append(
                            np.asarray(
                                data_acquisition.Q[loop_index : loop_index + loop_num]
                            )
                        )
                        loop_index += loop_num

                    y_data = {}
                    qubit_list = []
                    if "rd_ch_bit_map" in acquisition_options:
                        rd_ch_bit_map = acquisition_options.rd_ch_bit_map
                        for rd_ch_str, q_value in rd_ch_bit_map.items():
                            qubit_list.extend(q_value)
                    else:
                        qubit_list = qubits

                    new_length = len(qubit_list)
                    for q_inx, qubit in enumerate(qubit_list):
                        single_iq = []
                        for data_arr in data_collector:
                            new_data_arr = data_arr.T.reshape((new_length, col))
                            single_iq.append(new_data_arr[q_inx])
                        y_data.update({qubit.name: np.column_stack(tuple(single_iq))})
                else:
                    # one bit SingleShot IQ data
                    total = len(data_acquisition.I)
                    data_collector = []
                    loop_index = 0
                    while loop_index < total:
                        data_collector.append(
                            np.asarray(
                                data_acquisition.I[loop_index : loop_index + loop_num]
                            ).flatten()
                        )
                        data_collector.append(
                            np.asarray(
                                data_acquisition.Q[loop_index : loop_index + loop_num]
                            ).flatten()
                        )
                        loop_index += loop_num

                    # adjust coupler probe bit calibration
                    if metadata.process_meta:
                        target_name = metadata.process_meta.get("unit", qubits[0].name)
                    else:
                        target_name = qubits[0].name

                    max_shot = min([len(v) for v in data_collector])
                    new_data_collector = [v[:max_shot] for v in data_collector]

                    y_data = {target_name: np.column_stack(tuple(new_data_collector))}
        elif acquisition_options.data_type == "track":
            # only one bit of IQ track
            i0, i1 = data_acquisition.I
            q0, q1 = data_acquisition.Q
            y_data = {"track": np.column_stack((i0, q0, i1, q1))}
        else:
            raise ValueError("Only support `amp_phase`, `I_Q` and `track`")

        # bugfix: extend correct data replace y data
        if metadata.process_meta is None:
            metadata.process_meta = {}
        if correct_data:
            origin_y_data = y_data
            y_data = correct_data
            metadata.process_meta["origin_y_data"] = origin_y_data

    # record analysis options in meta process meta
    metadata.process_meta["analysis_options"] = analysis_options

    exp_data = ExperimentData(
        x_data=x_data,
        y_data=y_data,
        experiment_id=data_acquisition._tid,
        metadata=metadata,
    )

    if acquisition_options.x_data2:
        exp_data._x_data2 = acquisition_options.x_data2

    return exp_data


def _save_analysis_data_and_picture(
    analysis_obj: TopAnalysis, experiment_file: BaseFile, save_s3: bool = False
):
    experiment_name = analysis_obj.experiment_data.metadata.name

    record_text = True
    if (
        analysis_obj.experiment_data.metadata.process_meta
        and "record_text" in analysis_obj.experiment_data.metadata.process_meta
    ):
        record_text = analysis_obj.experiment_data.metadata.process_meta["record_text"]

    # Record Title/ID/requireID/RESULT/Quality into records.txt
    infos = {
        "Title": analysis_obj.experiment_data.metadata.name,
        "taskID": analysis_obj.experiment_data.experiment_id,
    }

    # get record_id
    record_id = _get_record_id_from_analysis_options(analysis_obj)
    if record_id:
        infos["recordID"] = record_id

    # save picture
    if analysis_obj.options.is_plot is True:
        if analysis_obj.drawer:
            drawer = analysis_obj.drawer

            if isinstance(drawer.figure, list):
                for idx, fig in enumerate(drawer.figure):
                    if analysis_obj.options.sub_figure_names:
                        f_name = f"{experiment_name}_{analysis_obj.options.sub_figure_names[idx]}"
                    else:
                        f_name = f"{experiment_name}_{idx}_(result)"
                    experiment_file.save_figure(
                        fig,
                        f_name,
                        close=True,
                        save_s3=save_s3,
                    )
            else:
                experiment_file.save_figure(
                    drawer.figure,
                    f"{experiment_name}(result)",
                    close=True,
                    save_s3=save_s3,
                )

    # update s3 result
    analysis_obj.s3_record.update(experiment_file.s3_record)

    # record results
    if analysis_obj.results:
        for result_name, result in analysis_obj.results.items():
            infos[result_name] = str(result)

    # record quality
    if analysis_obj.quality:
        infos["Quality"] = str(analysis_obj.quality)

    # save txt
    if record_text is True:
        experiment_file.save_text(f"{display_dict_as_table(infos)}\n\n", name="records")


def _save_experiment_data(exp_data: ExperimentData, exp_file: BaseFile):
    """Save experiment data."""
    exp_name = exp_data.metadata.name

    if exp_data.metadata.process_meta:
        origin_y_data = exp_data.metadata.process_meta.get("origin_y_data")
    else:
        origin_y_data = {}

    y_data = exp_data.y_data
    if origin_y_data:
        names = ["CORRECT", "ORIGIN"]
        data_list = [y_data, origin_y_data]
    else:
        names = [""]
        data_list = [y_data]

    for name, data in zip(names, data_list):
        save_data_struct = exp_data.to_save_data(dict(data))
        if save_data_struct:
            data_keys_info, save_data = save_data_struct
            name_info = f"{exp_name}({data_keys_info.lower()}){name}"
            exp_file.save_data(save_data, name=name_info, fmt="%.6f", suffix=None)


def _get_record_id_from_analysis_options(analysis_obj: TopAnalysis) -> Optional[str]:
    """Get record_id from the options for the analysis object"""
    if "record_id" in analysis_obj.experiment_data.metadata.process_meta:
        return str(analysis_obj.experiment_data.metadata.process_meta["record_id"])

    return None
