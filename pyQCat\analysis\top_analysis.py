# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/00/00
# __author:       <PERSON><PERSON><PERSON> <PERSON>

from __future__ import annotations

from abc import ABC, abstractmethod
from collections import defaultdict
from copy import deepcopy
from typing import List, Any, Union

import numpy as np

from .quality.base_quality import BaseQuality, QualityDescribe
from .specification import ParameterRepr, AnalysisResult
from ..structures import ExperimentData, Options, QDict, MetaData
from ..log import pyqlog


class PackedYData:
    def __init__(self, value):
        self.value: list | np.ndarray = value
        # cython compile use
        # self.value: Union[list, np.ndarray] = value


class TopAnalysis(ABC):
    """Abstract base class for analyzing Experiment data.

    The data produced by experiments are analyzed with subclasses
    of TopAnalysis. The analysis is typically run after the data has
    been gathered by the experiment.
    For example, an analysis may perform some data processing of the
    measured data and a fit to a function to extract a parameter.

    Any configurable values should be specified in the `_default_options`
    class method. One can override these values by calling the
    `set_options` method.
    """

    def __init__(self, experiment_data: ExperimentData, has_child: bool = False):
        """Initialize the analysis object.

        Args:
            experiment_data (ExperimentData): pyQCat Experiments Data container class.
            has_child (bool): Whether the experiment contains sub-experiments.
        """
        self._experiment_data = experiment_data
        self._has_child = has_child
        self._options = self._default_options()
        self._analysis_data_dict = QDict()
        self._results = QDict()
        self._quality = None
        # Optional.The value provided for parent experiment.
        self.provide_for_parent = {}
        self.s3_record = QDict(experiment_data=[], picture=[])

    def __reduce__(self):
        self.options.curve_drawer = None
        return super().__reduce__()

    @property
    def drawer(self):
        """A short-cut for curve drawer instance."""
        return self._options.curve_drawer

    @classmethod
    def from_sub_analysis(
        cls,
        x_data: np.ndarray,
        sub_analysis_list: List["TopAnalysis"],
        exp_id: str,
        metadata: MetaData = None,
    ) -> "TopAnalysis":
        """This interface is used for composite experiment.

        Args:
            exp_id: experiment id
            x_data (np.ndarray): The value of the x data.
            sub_analysis_list (List[TopAnalysis]): sub-analysis object.
            metadata (MetaData): Some information provided by experiment which maybe
                                 used for detailed analysis.

        Returns:
            A TopAnalysis object that contains the sub-analysis results.
        """
        experiment_data = generate_experiment_data(
            x_data, sub_analysis_list, exp_id, metadata
        )
        return cls(experiment_data, has_child=True)

    @classmethod
    def empty_analysis(cls, quality_describe: QualityDescribe = QualityDescribe.normal):
        ana = cls(experiment_data=None)
        ana._quality = BaseQuality.instantiate(quality_describe)
        ana._results = {}
        return ana

    @property
    def has_child(self) -> bool:
        """Return the analysis has_child value."""
        return self._has_child

    @property
    def options(self) -> Options:
        """Return the analysis options for :meth:`run` method."""
        return self._options

    @property
    def experiment_data(self) -> ExperimentData:
        """Return the experiment data for :meth:`run` method."""
        return self._experiment_data

    @property
    def results(self):
        """Get the result datas."""
        return self._results

    @property
    def analysis_datas(self):
        """Get the analysis datas."""
        return self._analysis_data_dict

    @property
    def quality(self):
        """Get the analysis data quality."""
        return self._quality

    @classmethod
    def _default_options(cls) -> Options:
        """Default analysis options common to all analyzes."""
        # Raw data selections.
        options = Options(
            data_key=None,
            is_plot=True,
            is_save_data=True,
            figsize=(12, 8),
            curve_drawer=None,
        )
        return options

    def set_options(self, **fields):
        """Set the analysis options.

        Args:
            fields: The fields to update the options
        """
        self._options.update(fields)

    @abstractmethod
    def _create_analysis_data(self) -> QDict:
        """Create Analysis data provided for detailed analyze."""
        pass

    def _create_analysis_result(self) -> QDict:
        """Create Analysis result data structure to save analysis results.
        The AnalysisResult object will get more attributes after analysis.

        Returns:
            A QDict object, key represents result type and value is
            AnalysisResult object.
        """
        results_dict = QDict()
        if not isinstance(self.options.result_parameters, List):
            self.options.result_parameters = [self.options.result_parameters]
        for param_repr in self.options.result_parameters:
            
            if isinstance(param_repr, dict):
                param_repr = ParameterRepr(**param_repr)
                
            if isinstance(param_repr, ParameterRepr):
                p_name = param_repr.name
                p_repr = param_repr.repr or param_repr.name
                unit = param_repr.unit
                extra = {"path": param_repr.param_path, "name": param_repr.param_name}
            else:
                p_name = param_repr
                p_repr = param_repr
                unit = None
                extra = {"path": None, "name": None}
            results_dict[p_name] = AnalysisResult(name=p_repr, unit=unit, extra=extra)
        return results_dict

    def run_analysis(self, *args, **kwargs):
        """Run analysis on experiment data."""
        pass

    def show_results(self):
        """Show analysis results"""
        data = defaultdict(list)
        for result in self.results.values():
            data["name"].append(result.name)
            data["value"].append(result.value)
            data["unit"].append(result.unit)
            data["extra"].append(result.extra)
            data["quality"].append(str(self.quality))
        return data

    def update_result(self):
        if self.results:
            for _, value in self.results.items():
                value.extra["name"] = self.options.result_name

    def update_analysis_state(self, name: str, state: Any):
        if self.experiment_data.metadata.process_meta is None:
            self.experiment_data.metadata.process_meta = {name: state}
        else:
            self.experiment_data.metadata.process_meta.update({name: state})

    def analysis_state(self, name: str):
        if self.experiment_data.metadata.process_meta:
            return self.experiment_data.metadata.process_meta.get(name)

    def analysis_program(self):
        """ Extract minimal analysis information

        Returns:
            experiment_data: Record the original collected data information.
            result: Record analysis structure
            quality: Record analysis quality.
            provide_for_parent: Record the result information provided to the parent experiment
        """
        return QDict(
            experiment_data=self.experiment_data,
            result=self.results,
            quality=self._quality,
            provide_for_parent=self.provide_for_parent,
        )


def generate_experiment_data(
    x_data: Union[List, np.ndarray],
    sub_analysis_list: List["TopAnalysis"],
    exp_id: str,
    metadata: MetaData = None,
):
    y_data = {}
    # 2024/01/05：Try to avoid changes to the base class as much as possible,
    # and this change will be adjusted later
    for sub_analysis in sub_analysis_list:
        for key in sub_analysis.provide_for_parent.keys():
            y_data[key] = [0] * len(x_data)

    for i, sub_analysis in enumerate(sub_analysis_list):
        for key, value in sub_analysis.provide_for_parent.items():
            if isinstance(value, PackedYData):
                y_data[key] = deepcopy(value.value)
            else:
                y_data[key][i] = value

    for key, value in y_data.items():
        try:
            y_data[key] = np.array(value)
        except ValueError:
            y_data[key] = value

    experiment_data = ExperimentData(np.asarray(x_data), y_data, exp_id, metadata)

    for sub_analysis in sub_analysis_list:
        child_experiment_data = sub_analysis.experiment_data
        experiment_data.add_child_data(child_experiment_data)

    return experiment_data
