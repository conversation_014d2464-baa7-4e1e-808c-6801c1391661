# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.adjust_scan_freq_range.rst:2
msgid "pyQCat.tools.adjust\\_scan\\_freq\\_range"
msgstr ""

#: of pyQCat.tools.utilities.adjust_scan_freq_range:1
msgid "Get frequency scan range, normal drive frequency."
msgstr ""

#: of pyQCat.tools.utilities.adjust_scan_freq_range
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.utilities.adjust_scan_freq_range:4
msgid "Given frequency value."
msgstr ""

#: of pyQCat.tools.utilities.adjust_scan_freq_range:7
msgid "Scan frequency step."
msgstr ""

#: of pyQCat.tools.utilities.adjust_scan_freq_range:10
msgid "Scan frequency upper and lower threshold."
msgstr ""

#: of pyQCat.tools.utilities.adjust_scan_freq_range:13
msgid "System frequency upper and lower bounds."
msgstr ""

#: of pyQCat.tools.utilities.adjust_scan_freq_range
msgid "Returns"
msgstr ""

#: of pyQCat.tools.utilities.adjust_scan_freq_range:16
msgid "The target frequency list."
msgstr ""

#: of pyQCat.tools.utilities.adjust_scan_freq_range
msgid "Return type"
msgstr ""

