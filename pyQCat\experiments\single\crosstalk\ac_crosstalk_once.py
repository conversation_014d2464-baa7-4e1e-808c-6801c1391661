# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/11/10
# __author:       <PERSON><PERSON><PERSON>

import copy

from ....analysis import CrosstalkOnceAnalysis, DistortionT1Analysis
from ....errors import RunOptionsError
from ....pulse import Constant, SquareEnvelop
from ....pulse.pulse_function import pi_pulse
from ....qubit import Qubit
from ....structures import MetaD<PERSON>, Options, QDict
from ....tools import cz_flow_options_adapter
from ...coupler_experiment_v1 import CouplerBaseExperimentV1 as CouplerBaseExperiment
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class ACCrosstalkOnce(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("drive_freq", float)
        options.set_validator("tq_name", str, limit_null=True)
        options.set_validator("bq_name", str, limit_null=True)
        options.set_validator("tq_ac_list", list, limit_null=True)
        options.set_validator("bq_ac", (-1, 1, 2))
        options.set_validator("drive_type", ["Drag", "Square"])
        # new requirement: https://document.qpanda.cn/presentation/913JVW9l2KH4Z3E6
        options.set_validator("ac_buffer_pre", float)
        options.set_validator("ac_buffer_after", float)

        options.drive_freq = None
        options.tq_name = None
        options.bq_name = None
        options.tq_ac_list = None
        options.bq_ac = 0.2
        options.drive_type = "Drag"
        # new requirement: https://document.qpanda.cn/presentation/913JVW9l2KH4Z3E6
        options.ac_buffer_pre = 5000
        options.ac_buffer_after = 200

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.quality_bounds = [0.91, 0.80, 0.70]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.tq = None
        options.bq = None
        options.xy_pulse_width = None
        options.support_context = [StandardContext.CM]

        return options

    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        xy_pulse = ACCrosstalkOnce.get_xy_pulse(
            builder.run_options.tq, builder.experiment_options
        )
        builder.run_options.xy_pulse_width = xy_pulse.width
        builder.play_pulse("XY", builder.run_options.tq, xy_pulse)
        return xy_pulse

    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""
        tq_z_pulses, bq_z_pulse = ACCrosstalkOnce.get_z_pulse(
            builder.run_options.xy_pulse_width, builder.experiment_options
        )
        builder.play_pulse("Z", builder.run_options.tq, tq_z_pulses)
        builder.play_pulse("Z", builder.run_options.bq, bq_z_pulse)
        return tq_z_pulses

    @staticmethod
    def get_xy_pulse(tq: Qubit, options: QDict):
        # new requirement: https://document.qpanda.cn/presentation/913JVW9l2KH4Z3E6
        pre_delay = Constant(options.ac_buffer_pre, 0, name="XY")()
        after_delay = Constant(options.ac_buffer_after, 0, name="XY")()

        if options.drive_type == "Drag":
            drive_pulse = pi_pulse(tq)
        else:
            baseband_freq = tq.XYwave.baseband_freq
            pulse_params = {
                "time": 5000,
                "offset": 15,
                "amp": 1.0,
                "detune": 0,
                "freq": baseband_freq,
            }
            drive_pulse = SquareEnvelop(**pulse_params)

        return copy.deepcopy(pre_delay) + drive_pulse() + copy.deepcopy(after_delay)

    @staticmethod
    def get_z_pulse(xy_pulse_width: float, options: QDict):
        tq_z_pulses = []
        for tq_ac in options.tq_ac_list:
            mid_pulse = Constant(xy_pulse_width, float(tq_ac))
            tq_z_pulses.append(mid_pulse())

        bq_z_pulse = Constant(xy_pulse_width, options.bq_ac)()

        return tq_z_pulses, bq_z_pulse

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        self._check_run_options()

        drive_freq = self.experiment_options.drive_freq
        if drive_freq is None:
            self.set_experiment_options(drive_freq=self.run_options.tq.drive_freq)
        else:
            self.run_options.tq.drive_freq = drive_freq

        self.set_run_options(
            x_data=self.experiment_options.tq_ac_list,
            analysis_class=CrosstalkOnceAnalysis,
            measure_qubits=[self.run_options.tq],
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "fd": (self.experiment_options.drive_freq, "MHz"),
            "bq_ac": (self.experiment_options.bq_ac, "V"),
        }
        return metadata

    def _check_run_options(self):
        tq_name = self.experiment_options.tq_name
        bq_name = self.experiment_options.bq_name

        for qubit in self.qubits:
            if tq_name == qubit.name:
                self.run_options.tq = qubit
            elif bq_name == qubit.name:
                self.run_options.bq = qubit

        for coupler in self.couplers:
            if tq_name == coupler.name:
                self.run_options.tq = coupler
            elif bq_name == coupler.name:
                self.run_options.bq = coupler

        if not self.run_options.tq:
            raise RunOptionsError(
                self.label,
                key="tq",
                value=self.run_options.tq,
                msg=f"No find {self.experiment_options.tq_name}",
            )

        if not self.run_options.bq:
            raise RunOptionsError(
                self.label,
                key="bq",
                value=self.run_options.bq,
                msg=f"No find {self.experiment_options.bq_name}",
            )

        if self.discriminator is not None:
            data_type = "I_Q"
        else:
            data_type = "amp_phase"

        self.set_experiment_options(data_type=data_type)
        self.set_run_options(
            x_data=self.experiment_options.tq_ac_list,
            analysis_class=CrosstalkOnceAnalysis,
        )

        if isinstance(self.run_options.tq, Qubit):
            self.set_experiment_options(
                multi_readout_channels=[self.run_options.tq.readout_channel]
            )
            self.set_run_options(measure_qubits=[self.run_options.tq])


class CouplerACCrosstalkOnce(CouplerBaseExperiment, ACCrosstalkOnce):
    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.support_context = [StandardContext.CM]
        return options

    @staticmethod
    def set_xy_pulses(builder):
        xy_pulse = ACCrosstalkOnce.get_xy_pulse(
            builder.run_options.tq, builder.experiment_options
        )
        builder.run_options.xy_pulse_width = xy_pulse.width
        builder.compose_xy_pulses(xy_pulse)

    @staticmethod
    def set_z_pulses(builder):
        # bug records: directly band coupler
        # builder._compose_z_pulses(tq_z_pulses, builder.run_options.tq)
        tq_z_pulses, bq_z_pulse = ACCrosstalkOnce.get_z_pulse(
            builder.run_options.xy_pulse_width, builder.experiment_options
        )
        builder.compose_z_pulses(tq_z_pulses, builder.coupler)
        builder.compose_z_pulses(bq_z_pulse, builder.run_options.bq)

    def get_qubit_str(self):
        """Get qubit str, an args qubit for SaveFile class.

        Returns:
            qubit_str: string, use to create save data path.
        """
        q_name_list = list(set(qubit.name for qubit in self.qubits))
        c_name_list = list(set(coupler.name for coupler in self.couplers))
        return f'tq={"".join(sorted(q_name_list))}bq={"".join(sorted(c_name_list))}'

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        drive_freq = self.experiment_options.drive_freq
        self.run_options.tq = self.driveQ
        self.run_options.tq.drive_freq = drive_freq
        # bugfix: measure qubits set error
        probe_q = self.probeQ
        self.set_experiment_options(multi_readout_channels=[probe_q.readout_channel])
        self.set_run_options(measure_qubits=[probe_q])


class QCZShiftOnce(ACCrosstalkOnce):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.tq_name = "ql"
        options.bq_name = "qc"
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super(ACCrosstalkOnce, cls)._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator(
            "fit_model_name",
            [
                "lorentzian",
                "bi_lorentz_tilt",
                "gauss_lorentzian",
                "skewed_lorentzian",
                "skewed_gauss_lorentz",
            ],
        )
        options.set_validator("cali_offset_method", ["direct", "undirect"])
        options.set_validator("cut_index", bool)
        options.set_validator("adjust_noise", bool)

        options.data_key = ["P1"]
        options.quality_bounds = [0.91, 0.80, 0.70]
        options.fit_model_name = "gauss_lorentzian"
        options.cali_offset_method = "direct"
        options.p0_history = None
        options.cut_index = False

        options.adjust_noise = True
        options.n_multiple = 4.0

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.support_context = [StandardContext.CGC]
        return options

    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        xy_pulse = ACCrosstalkOnce.get_xy_pulse(
            builder.run_options.tq, builder.experiment_options
        )
        builder.run_options.xy_pulse_width = xy_pulse.width
        builder.play_pulse("XY", builder.run_options.tq, xy_pulse)

        for qubit in builder.qubits:
            if qubit.name != builder.run_options.tq.name:
                zero_pulse = Constant(xy_pulse.width, 0, "XY")()
                builder.play_pulse("XY", qubit, zero_pulse)

        return xy_pulse

    def _check_options(self):
        cz_flow_options_adapter(self)
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.tq_ac_list,
            analysis_class=DistortionT1Analysis,
        )
