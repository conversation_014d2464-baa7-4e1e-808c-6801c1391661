# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# __date:         2024/09/15
# __author:       zza


import json
import math
from typing import List, Union
import numpy as np

from ...errors import EnvBitCheckError
from ...executor.batch import divide_coupler_calibration_parallel_group
from ...log import pyqlog
from ...tools import get_bound_ac_spectrum, judge_exp_failed, qarange
from ..batch_experiment import BatchExperiment
from ..base_experiment import BaseExperiment
from ..parallel_experiment import ParallelExperiment
from pyQCat.analysis.fit.fit_models import amp2freq_formula

EXP_TYPE = Union[BaseExperiment, ParallelExperiment]


def work_point_cal_new(
        unit_str,
        z_amp_arr,
        f10_arr,
        r_square_arr,
        ac_params,
        rq_threshold,
        grad_threshold,
):
    select_idxs_list = []
    idxs = []
    length = len(z_amp_arr)
    for idx in range(length):
        if idx == 0:
            continue

        delta_x = z_amp_arr[idx] - z_amp_arr[idx - 1]
        delta_y = f10_arr[idx] - f10_arr[idx - 1]
        abs_grad = abs(delta_y / delta_x)
        ramsey_rq = r_square_arr[idx]

        if abs_grad > grad_threshold and ramsey_rq > rq_threshold:
            if idx - 1 not in idxs and r_square_arr[idx - 1] > rq_threshold:
                idxs.append(idx - 1)
            idxs.append(idx)
        elif idxs:
            select_idxs_list.append(idxs)
            idxs = []

        if idx == length - 1 and idxs:
            select_idxs_list.append(idxs)

    pyqlog.info(f"{unit_str} select_idxs_list: {select_idxs_list}")
    fit_error_list = []
    for i, idx_list in enumerate(select_idxs_list):
        x_list = []
        y_list = []
        for idx in idx_list:
            x_list.append(z_amp_arr[idx])
            y_list.append(f10_arr[idx])

        x_arr = np.array(x_list)
        y_arr = np.array(y_list)
        y_fit_arr = amp2freq_formula(x_arr, *ac_params)
        fit_error = np.mean(np.abs(y_arr - y_fit_arr))
        fit_error_list.append(fit_error)

    sort_values = sorted(zip(fit_error_list, select_idxs_list))
    target_idx_list = []
    for fit_error, idx_list in sort_values:
        if len(idx_list) > 5:
            target_idx_list = idx_list
            break

    if len(target_idx_list) > 5:
        target_z_amp_list = [z_amp_arr[idx] for idx in target_idx_list]
        z_amp = float(np.median(np.array(target_z_amp_list)))
    else:
        z_amp = 0
    return z_amp


class BatchCouplerOptimizeFirDicarlo(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.r_square_threshold = 0.7
        options.gradient_threshold = 500
        options.record_ac_flag = False
        options.origin_data_path = None
        options.coupler_shift_flows = None
        options.qubit_cali_flows = None
        options.coupler_timing_flows = None
        options.distortion_flows = None
        options.ACSpectrum_flows = None
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.qc2qh_map = {}
        options.origin_data = {}
        options.ac_bit = {}
        return options

    def _qc_to_qh(self, qc_units: List[str]):
        return [self.run_options.qc2qh_map.get(qc) for qc in qc_units]

    def _qc_to_read(self, qc_units: List[str]):
        for qc in qc_units:
            coupler = self.context_manager.chip_data.get_physical_unit(qc)
            return f"q{coupler.probe_bit}"

    def _qh_to_qc(self, qh_units: List[str]):
        qc_units = []
        for qc, qh in self.run_options.qc2qh_map.items():
            if qh in qh_units:
                qc_units.append(qc)
        return qc_units

    def parse_ac_json(self, working_units: list):
        path = self.experiment_options.origin_data_path
        r_square_threshold = self.experiment_options.r_square_threshold
        gradient_threshold = self.experiment_options.gradient_threshold
        exp_name = self.experiment_options.distortion_flows[0]
        with open(path, encoding="utf-8") as f:
            origin_ac_data = json.load(f)
            for unit_str in working_units:
                params = origin_ac_data.get(unit_str)
                if params:
                    z_amp_arr = params.get("z_amp_arr")
                    f10_arr = params.get("f10_arr")
                    r_square_arr = params.get("r_square_arr")
                    ac_params = params.get("ac_params")
                    branch = params.get("branch")
                    z_amp_work = work_point_cal_new(
                        unit_str,
                        z_amp_arr,
                        f10_arr,
                        r_square_arr,
                        ac_params,
                        rq_threshold=r_square_threshold,
                        grad_threshold=gradient_threshold,
                    )
                    if len(working_units) > 1:
                        self.change_parallel_exec_exp_options(
                            exp_name=exp_name,
                            unit=unit_str,
                            z_amp=z_amp_work,
                            ac_data=[z_amp_arr, f10_arr],
                            fname=None,
                            branch=branch,
                        )
                    else:
                        self.change_regular_exec_exp_options(
                            exp_name=exp_name,
                            z_amp=z_amp_work,
                            ac_data=[z_amp_arr, f10_arr],
                            fname=None,
                            branch=branch,
                        )
                else:
                    pyqlog.error(f"{unit_str} not in origin ac data!")

    def _run_batch(self):
        qh_list = []
        for unit in self.experiment_options.physical_units:
            coupler = self.context_manager.chip_data.get_physical_unit(unit)
            qh_list.append(f"q{coupler.probe_bit}")
        groups = divide_coupler_calibration_parallel_group(
            self.experiment_options.physical_units,
            self.context_manager.chip_data,
            **self.backend.system.parallel_divide,
        )
        self._set_qubit_ac_bias()

        # coupler fixed point calibration flows；根据Coupler参数自动选择实验branch
        for unit in self.experiment_options.physical_units:
            coupler = self.context_manager.chip_data.get_physical_unit(unit)
            idle = coupler.idle_point
            half_period = abs(coupler.dc_max - coupler.dc_min)
            if coupler.dc_max > 0:
                ac_branch = "left"
                z_amp = -half_period * 0.66 - idle
            else:
                ac_branch = "right"
                z_amp = half_period * 0.66 - idle
            self.change_parallel_exec_exp_options(
                # exp_name="ZZShiftFixedPointCalibration_0",
                exp_name="FreqShiftByCoupler",
                unit=unit,
                z_amp=z_amp,
                options={"child_exp_options.ac_branch": ac_branch},
            )
        self._set_coupler_ac_bias(self.experiment_options.physical_units)
        for group in groups:

            if not self.experiment_options.coupler_shift_flows:
                pass_units = group
            else:
                pass_units = self._run_flow(
                    flows=self.experiment_options.coupler_shift_flows,
                    physical_units=group,
                )
            if not pass_units:
                pyqlog.warning(f"No find any unit, final in coupler shift flows!")
                continue

            for unit in pass_units:
                coupler = self.context_manager.chip_data.get_physical_unit(unit)
                idle = coupler.idle_point
                if idle > 0:
                    z_amp_list = qarange(idle - 0.03, idle + 0.0255, 0.0015)
                else:
                    z_amp_list = qarange(idle + 0.03, idle - 0.0255, -0.0015)
                self.change_parallel_exec_exp_options(
                    exp_name="ACSpectrumByCoupler", unit=unit, z_amps=z_amp_list
                )
            # 经过FreqShiftByCoupler实验，实验结果更新到idle_point，造成实验环境发生变化，需要将idle_point重置
            self._reset_coupler_ac_bias(pass_units)

            # qubit freq calibration flow
            qh_units = self._qc_to_qh(pass_units)
            # qh_units = self._qc_to_read(pass_units)
            pass_qh_units = self._run_flow(
                flows=self.experiment_options.qubit_cali_flows,
                physical_units=qh_units,
            )
            if not pass_qh_units:
                pyqlog.warning(f"No find any unit, final in qubit cali flows!")
                continue

            # ACSpectrum_flows
            # pass_qh_units = self._qc_to_qh(pass_qc_units)
            pass_qc_units = self._qh_to_qc(
                pass_qh_units
            )  # 根据qh反向计算得到的qc需要判断是否包含在初始的list之中
            physical_psss_units = []
            for unit in pass_qc_units:
                if unit in pass_units:
                    physical_psss_units.append(unit)
                else:
                    pass
            if self.experiment_options.record_ac_flag:
                # physical_units = self.run_options.ac_bit
                self.parse_ac_json(working_units=physical_psss_units)
            else:
                physical_psss_units = self._run_flow(
                    flows=self.experiment_options.ACSpectrum_flows,
                    physical_units=physical_psss_units,
                )
            if not physical_psss_units:
                pyqlog.warning(f"No find any unit, final in ACSpectrum flows!")
                continue

            self.run_options.ac_bit = physical_psss_units

            # distortion composite flow
            # Set amp to avoid areas with close frequencies
            pass_qc_units = self._run_flow(
                flows=self.experiment_options.distortion_flows,
                physical_units=physical_psss_units,
            )

            fail_units = [
                unit
                for unit in self.experiment_options.physical_units
                if unit not in pass_qc_units
            ]
            pyqlog.info(f"Pass Units: {pass_qc_units} | Fail Units: {fail_units}")

    def _record_experiment(self, exp_name, exp, physical_units, err: Exception = None):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if record.exp_name == "ACSpectrumByCoupler":
            self._set_work_point_new(exp, physical_units, err)
            self._save_data_to_json(self.run_options.origin_data, "origin_ac_data.json")

        return record

    def _set_work_point_new(self, exp: EXP_TYPE, physical_units, err=None):
        """Set xxx work point."""
        unit_list = []
        z_amp_target_list = []
        origin_data = self.run_options.origin_data

        r_square_threshold = self.experiment_options.r_square_threshold
        gradient_threshold = self.experiment_options.gradient_threshold
        exp_name = self.experiment_options.distortion_flows[0]

        if isinstance(err, EnvBitCheckError):
            err = None

        if exp is None or err:
            return unit_list, z_amp_target_list
        elif isinstance(exp, BaseExperiment):
            exps = [exp]
            parallel_flag = False
        else:
            exps = exp.experiments
            parallel_flag = True

        for idx, exp_obj in enumerate(exps):
            unit_str = physical_units[idx]

            if not exp_obj.analysis or judge_exp_failed(
                    exp_obj.analysis.quality,
                    self.experiment_options.simulator_pass_rate,
            ):
                pyqlog.warning(f"{unit_str} {exp_obj.label} failed")
                continue

            # get some args from `ACSpectrum` experiment
            z_amp_arr = exp_obj.run_options.z_amps_sort
            f10_arr = exp_obj.run_options.f10_list_sort
            r_square_arr = exp_obj.run_options.r_square_list_sort
            ac_params = exp_obj.analysis.results.params.value
            branch = "left" if z_amp_arr[0] < 0 else "right"

            origin_data.update(
                {
                    unit_str: {
                        "z_amp_arr": z_amp_arr.tolist(),
                        "f10_arr": f10_arr.tolist(),
                        "r_square_arr": r_square_arr.tolist(),
                        "ac_params": ac_params,
                        "branch": branch,
                    }
                }
            )

            z_amp_work = work_point_cal_new(
                unit_str,
                z_amp_arr,
                f10_arr,
                r_square_arr,
                ac_params,
                rq_threshold=r_square_threshold,
                grad_threshold=gradient_threshold,
            )
            pyqlog.info(f"{unit_str} z_amp_work: {z_amp_work}")

            unit_list.append(unit_str)
            z_amp_target_list.append(z_amp_work)
            if parallel_flag is True and z_amp_work != 0:
                self.change_parallel_exec_exp_options(
                    exp_name=exp_name,
                    unit=unit_str,
                    z_amp=z_amp_work,
                    ac_data=[z_amp_arr, f10_arr],
                    fname=None,
                    branch=branch,
                )
            else:
                self.change_regular_exec_exp_options(
                    exp_name=exp_name,
                    z_amp=z_amp_work,
                    ac_data=[z_amp_arr, f10_arr],
                    fname=None,
                    branch=branch,
                )
        self.set_run_options(origin_data=origin_data)
        return unit_list, z_amp_target_list

    def _set_qubit_ac_bias(self):
        for coupler_name in self.experiment_options.physical_units:
            # get coupler structure
            coupler_struct = self.context_manager.chip_data.get_coupler_struct(
                coupler_name
            )
            qh = coupler_struct.qh
            ql = coupler_struct.ql
            self.run_options.qc2qh_map[coupler_name] = qh.name

            # set the env bit working voltage
            self.refresh_working_point(ql, qh)

    def _set_coupler_ac_bias(self, units: List[str]):
        for coupler_name in units:
            coupler = self.context_manager.chip_data.cache_coupler.get(coupler_name)
            pyqlog.info(f"{coupler}: {coupler.idle_point}")
            coupler.dc_max += coupler.idle_point
            coupler.idle_point = 0

    def _reset_coupler_ac_bias(self, units: List[str]):
        for coupler_name in units:
            coupler = self.context_manager.chip_data.cache_coupler.get(coupler_name)
            coupler.idle_point = 0

    def _reset_xy_pulse_params(
            self,
            units: List[str],
            detune: float = None,
            width: float = None,
            freq: float = None,
    ):
        for qubit_name in units:
            qubit = self.context_manager.chip_data.cache_qubit.get(qubit_name)
            if detune is not None:
                qubit.XYwave.detune_pi = detune
                qubit.XYwave.detune_pi2 = detune
            if width is not None:
                delta_power = math.log2(width / qubit.XYwave.time) * 6
                qubit.XYwave.time = width
                new_power = qubit.drive_power - delta_power
                if -40 < new_power < -10:
                    qubit.drive_power = round(new_power, 1)
                elif new_power < -40:
                    qubit.drive_power = -40
                else:
                    qubit.drive_power = -10
            if freq is not None:
                qubit.drive_freq -= 20
            # qubit.save_data()

    @staticmethod
    def refresh_working_point(ql, qh):
        ql_max_freq, ql_min_freq = get_bound_ac_spectrum(ql)
        if ql_max_freq != ql_min_freq:
            if abs(ql_max_freq - qh.drive_freq) > abs(ql_min_freq - qh.drive_freq):
                ql.dc_min = ql.dc_max
            else:
                ql.dc_min = ql.dc_max - 1 / (2 * ql.ac_spectrum_v[2])
        return ql_max_freq, ql_min_freq
