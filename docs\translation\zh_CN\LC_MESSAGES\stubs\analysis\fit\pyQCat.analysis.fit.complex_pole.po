# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/fit/pyQCat.analysis.fit.complex_pole.rst:2
msgid "pyQCat.analysis.fit.complex\\_pole"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.complex_pole:1
msgid "Decoration of distortion IIR fit model."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.complex_pole
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.complex_pole:4
msgid "Sample period, when sample rate 1.6 GHz this is 0.625 ns."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.complex_pole
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.complex_pole:7
msgid "Pole model."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.complex_pole
msgid "Return type"
msgstr ""

