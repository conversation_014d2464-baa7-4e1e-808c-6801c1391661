# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:2
msgid "pyQCat.analysis.library.SingleShotAnalysis"
msgstr ""

#: of pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.top_analysis.TopAnalysis`"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:1
msgid "Initialize the analysis object."
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:4
msgid "pyQCat Experiments Data container class."
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:7
msgid "Whether the experiment contains sub-experiments."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.analysis.library.SingleShotAnalysis.__init__>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`from_sub_analysis "
"<pyQCat.analysis.library.SingleShotAnalysis.from_sub_analysis>`\\ "
"\\(x\\_data\\, sub\\_analysis\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:25:<autosummary>:1
msgid "This interface is used for composite experiment."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`run_analysis "
"<pyQCat.analysis.library.SingleShotAnalysis.run_analysis>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:25:<autosummary>:1
#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis.run_analysis:1
msgid "Run analysis on experiment data."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`set_options "
"<pyQCat.analysis.library.SingleShotAnalysis.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:25:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`show_results "
"<pyQCat.analysis.library.SingleShotAnalysis.show_results>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:25:<autosummary>:1
msgid "Show analysis results"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.SingleShotAnalysis.rst:27
msgid "Attributes"
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid ""
":py:obj:`analysis_datas "
"<pyQCat.analysis.library.SingleShotAnalysis.analysis_datas>`\\"
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid "Get the analysis datas."
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`drawer <pyQCat.analysis.library.SingleShotAnalysis.drawer>`\\"
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1
#: pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid "A short-cut for curve drawer instance."
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid ""
":py:obj:`experiment_data "
"<pyQCat.analysis.library.SingleShotAnalysis.experiment_data>`\\"
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid "Return the experiment data for :meth:`run` method."
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid ""
":py:obj:`has_child "
"<pyQCat.analysis.library.SingleShotAnalysis.has_child>`\\"
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid "Return the analysis has_child value."
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.library.SingleShotAnalysis.options>`\\"
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid "Return the analysis options for :meth:`run` method."
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.library.SingleShotAnalysis.quality>`\\"
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid "Get the analysis data quality."
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`results <pyQCat.analysis.library.SingleShotAnalysis.results>`\\"
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:1:<autosummary>:1
msgid "Get the result datas."
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.library.SingleShotAnalysis.drawer:3
msgid ":py:class:`~pyQCat.analysis.visualization.curve_drawer.CurveDrawer`"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options:1
msgid "Create the Analysis options, and set some fields."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options:12
msgid "Analysis Options:"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options:4
msgid ""
"n_clusters (int): Preset clustering number. method (str): Clustering "
"algorithm type. `GMM` or `KMeans` n_multiple (float): Calculate cluster "
"radius,"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options:7
msgid "multiple of standard deviation. Default set `3.0`."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options:9
msgid ""
"is_plot (bool): Set ``True`` to create figure for fit result. "
"quality_bounds (Tuple): Boundary of success conditions. result_parameters"
" (List): result data key list."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._default_options:14
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data:6
msgid "Create Analysis data provided for detailed analyze."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data:5
msgid "{"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data:3
msgid "'q0': data0, # np.array(I0, Q0, I1, Q1) 'q1': data1, 'c0': data2 ..."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data:7
msgid "}"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data:8
msgid ":py:class:`~pyQCat.structures.QDict`"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data
msgid "Returns"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._create_analysis_data:9
msgid "A QDict object, key represents data type and value is"
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._run_training:1
msgid "Training analysis_data source_data."
msgstr ""

#: of
#: pyQCat.analysis.library.single_shot_analysis.SingleShotAnalysis._evaluate_quality:1
msgid "Parse analysis data Classification Quality."
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.analysis.top_analysis.TopAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_analysis "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.run_analysis>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_options "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_datas "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_data "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`has_child "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`options "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`quality "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`results "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.analysis.top_analysis.TopAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_analysis "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.run_analysis>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_datas "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_data "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`has_child "
#~ "<pyQCat.analysis.library.SingleShotAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.library.SingleShotAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.library.SingleShotAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":obj:`results <pyQCat.analysis.library.SingleShotAnalysis.results>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Create Analysis result data structure to"
#~ " save analysis results. The AnalysisResult"
#~ " object will get more attributes "
#~ "after analysis."
#~ msgstr ""

#~ msgid ""
#~ "A QDict object, key represents result"
#~ " type and value is AnalysisResult "
#~ "object."
#~ msgstr ""

