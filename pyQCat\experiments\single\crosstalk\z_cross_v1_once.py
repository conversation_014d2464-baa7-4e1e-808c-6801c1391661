# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/10/15
# __author:       <PERSON> Fang

"""
Integrate YXY version Z cross case.
ZCrossV1OnceBase is based on TopExperimentV1.

"""

import asyncio
import json
import os
import re
from copy import deepcopy
from typing import List

import numpy as np
import pandas as pd

from pyQCat.analysis.algorithms.iqprobability import IQdiscriminator
from pyQCat.analysis.library import (
    CrosstalkOnceAnalysis,
    RamseyAnalysis,
    RamseyZampAnalysis,
    XYCrossRwAnalysis,
    BzRamseyAnalysis,
)
from pyQCat.analysis.top_analysis import TopAnalysis
from pyQCat.concurrent.data_client import DataClient
from pyQCat.concurrent.worker.analysis_interface import (
    generate_acquisition_options,
    base_analysis_process,
    run_analysis_process,
)
from pyQCat.data_transfer.state import TransferTaskStatusEnum
from pyQCat.errors import ExperimentOptionsError
from pyQCat.experiments.top_experiment_v1 import TopExperimentV1
from pyQCat.log import pyqlog
from pyQCat.parameters import options_wrapper
from pyQCat.pulse.base_pulse import PulseComponent
from pyQCat.pulse.pulse_function import pi_pulse, half_pi_pulse, zero_pulse
from pyQCat.pulse.pulse_lib import Linear, Constant, SquareEnvelop
from pyQCat.qubit import NAME_PATTERN, Qubit
from pyQCat.structures import Options, MetaData, ExperimentData
from pyQCat.tools.savefile import BaseFile, LocalFile
from pyQCat.tools.utilities import qarange, freq_to_amp, format_results
from pyQCat.types import QualityDescribe as Quality, StandardContext


class ZCrossV1OnceBase(TopExperimentV1):
    """Z Cross new schema series normal experiment base class."""

    def get_qubit_str(self):
        """Get qubit string."""
        b_name_list = self.run_options.b_name_list
        t_name_list = self.run_options.t_name_list
        b_length = len(b_name_list)
        t_length = len(t_name_list)

        if b_length > 1:
            bias_info = f"{b_name_list[0]}~{b_name_list[-1]}"
        elif b_length == 1:
            bias_info = f"{b_name_list[0]}"
        else:
            bias_info = ""

        if t_length > 1:
            target_info = f"{t_name_list[0]}~{t_name_list[-1]}"
        elif t_length == 1:
            target_info = f"{t_name_list[0]}"
        else:
            target_info = ""

        string = f"B{bias_info}-T{target_info}"
        return string

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment options:
            bias_name_list (list): Bias Qubit/Coupler name list.
            target_name_list (list): Target qubit name list.
            bz_amp (float): Set Bias qubit z_amp value.

        """
        options = super()._default_experiment_options()

        options.set_validator("bias_name_list", list)
        options.set_validator("target_name_list", list)
        options.set_validator("bz_amp", float)
        options.set_validator("bz_amp_map", dict)  # maybe use app set this value.

        options.bias_name_list = []
        options.target_name_list = []
        options.bz_amp = 0.1
        options.bz_amp_map = {}

        # TopExperimentV1 options set.
        options.is_dynamic = 0
        options.enable_one_sweep = True
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.b_name_list = []
        options.t_name_list = []
        options.t_xy_name_map = {}  # target and xy name map
        options.t_rd_name_map = {}  # target and readout name map
        options.qc_map = {}  # All Qubit or Coupler object map
        options.rd_qubit_map = {}  # Readout name and Qubit map
        options.rd_dcm_map = {}  # Readout name and IQdiscriminator map

        options.cross_z_amp_map = {}  # Note once cross z_amp
        options.extra_data_map = {}  # Note some extra data
        options.x_data = []
        options.analysis_class = XYCrossRwAnalysis
        options.once_analysis_class = None  # Once target analysis class

        options.support_context = [StandardContext.URM.value]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        options.set_validator("n_multiple", float)
        options.set_validator("once_ana_options", dict)

        options.n_multiple = 100
        options.cross_coe_map = {}
        options.cross_trust_map = {}
        options.once_ana_options = {}

        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {}
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        bz_amp = self.experiment_options.bz_amp
        bz_amp_map = self.experiment_options.bz_amp_map
        bias_name_list = self.experiment_options.bias_name_list
        target_name_list = self.experiment_options.target_name_list
        is_dynamic = self.experiment_options.is_dynamic
        figsize = self.analysis_options.figsize

        qc_map = {}
        qubit_map = {qubit.name: qubit for qubit in self.qubits}
        coupler_map = {qubit.name: qubit for qubit in self.couplers}
        qc_map.update(qubit_map)
        qc_map.update(coupler_map)

        q_names = list(qubit_map.keys())
        c_names = list(coupler_map.keys())
        all_names = list(qc_map.keys())
        q_pattern = re.compile(NAME_PATTERN.qubit)
        c_pattern = re.compile(NAME_PATTERN.coupler)

        b_name_list = []
        if bias_name_list:
            for b_name in bias_name_list:
                if isinstance(b_name, int):
                    b_name = f"q{b_name}"
                if b_name in all_names:
                    b_name_list.append(b_name)
                else:
                    pyqlog.warning(
                        f"Please check, not found bias {b_name} from qubits and couplers!"
                    )
        else:
            raise ExperimentOptionsError(
                self.label,
                msg=f"Please check, `bias_name_list` is empty!",
                key="bias_name_list",
                value=bias_name_list,
            )

        if target_name_list:
            t_name_list = []
            for t_name in target_name_list:
                if isinstance(t_name, int):
                    t_name = f"q{t_name}"
                if t_name in b_name_list:
                    pyqlog.warning(
                        f"Set target {t_name} in bias_name_list: {b_name_list}"
                    )
                    t_name_list.append(t_name)
                elif t_name in all_names:
                    t_name_list.append(t_name)
                else:
                    pyqlog.warning(
                        f"Please check, not found target {t_name} from qubits and couplers!"
                    )
        else:
            # NOTE: default set just only support Qubit objects.
            t_name_list = [name for name in q_names if name not in b_name_list]

        b_name_list.sort(key=lambda x: int("".join(x.split("-"))[1:]))
        t_name_list.sort(key=lambda x: int("".join(x.split("-"))[1:]))

        check_flag = True
        xy_name_list = []
        rd_name_list = []
        for t_name in t_name_list:
            if t_name in q_names:
                xy_name_list.append(t_name)
                rd_name_list.append(t_name)
            elif t_name in c_names:
                coupler_obj = coupler_map.get(t_name)
                dq_name = f"q{coupler_obj.drive_bit}".split(".")[0]
                pq_name = f"q{coupler_obj.probe_bit}".split(".")[0]
                if dq_name not in xy_name_list and pq_name not in rd_name_list:
                    # coupler target set xy case, same as ZZShift.
                    xy_name_list.append(pq_name)
                    rd_name_list.append(pq_name)
                else:
                    # TODO, set coupler target, when dq or pq are is exist in history.
                    pyqlog.warning(
                        f"when set coupler {t_name} target, "
                        f"xy_name: {dq_name} or rd_name: {pq_name} maybe is exist in history!"
                    )
                    xy_name_list.append(pq_name)
                    rd_name_list.append(pq_name)
            else:
                pyqlog.error(f"{t_name} not in qubits or couplers: {all_names}")
                if check_flag is True:
                    check_flag = False
        if check_flag is False:
            raise ExperimentOptionsError(
                self.label,
                msg=f"Check target names: {t_name_list} were not in exist environment!",
            )

        t_xy_name_map = dict(zip(t_name_list, xy_name_list))
        t_rd_name_map = dict(zip(t_name_list, rd_name_list))

        dcm_map = {}
        if self.discriminator:
            data_type = "I_Q"
            dcm_list = []
            if isinstance(self.discriminator, IQdiscriminator):
                dcm_list.append(self.discriminator)
            elif isinstance(self.discriminator, list):
                dcm_list.extend(self.discriminator)
            dcm_map = {dcm_obj.name: dcm_obj for dcm_obj in dcm_list}
        else:
            data_type = "amp_phase"

        rd_qubit_map = {}
        rd_dcm_map = {}
        multi_readout_channels = []
        for rd_name in rd_name_list:
            if rd_name:
                q_obj = qubit_map.get(rd_name)
                rd_qubit_map.update({rd_name: q_obj})
                multi_readout_channels.append(q_obj.readout_channel)
                if data_type == "I_Q":
                    dcm_obj = dcm_map.get(rd_name)
                    rd_dcm_map.update({rd_name: dcm_obj})

        # change analysis figsize
        base_len = 10
        q_length = len(t_name_list)
        if figsize in [[12, 8], (12, 8)] and q_length > base_len:
            ratio = q_length / base_len
            figsize = (int(12 * ratio), int(8 * ratio))
        pyqlog.debug(f"{self.label} analysis figsize: {figsize}")

        rd_length = len(rd_qubit_map)
        if rd_length > 1:
            is_dynamic = 0
            measure_qubits = list(rd_qubit_map.values())
            if data_type == "I_Q":
                self.discriminator = list(rd_dcm_map.values())
        else:
            measure_qubits = []
            if data_type == "I_Q":
                self.discriminator = list(rd_dcm_map.values())[0]

        for b_name in b_name_list:
            if b_name not in bz_amp_map:
                bz_amp_map.update({b_name: bz_amp})
                pyqlog.warning(
                    f"{b_name} not in bz_amp_map, set {b_name} bias z_amp {bz_amp}"
                )

        self.set_analysis_options(figsize=figsize)

        self.set_experiment_options(
            data_type=data_type,
            multi_readout_channels=multi_readout_channels,
            is_dynamic=is_dynamic,
        )
        self.set_run_options(
            b_name_list=b_name_list,
            t_name_list=t_name_list,
            t_xy_name_map=t_xy_name_map,
            t_rd_name_map=t_rd_name_map,
            qc_map=qc_map,
            rd_qubit_map=rd_qubit_map,
            rd_dcm_map=rd_dcm_map,
            measure_qubits=measure_qubits,
        )
        pyqlog.debug(
            f"bias names: {self.run_options.b_name_list}, "
            f"target names: {self.run_options.t_name_list}, "
            f"measure_qubits: {self.run_options.measure_qubits}, "
            f"target & measure map: {self.run_options.t_rd_name_map}, "
            f"multi_readout_channels: {self.experiment_options.multi_readout_channels}, "
            f"data_type: {self.experiment_options.data_type}"
        )

    def _alone_save_result(self):
        """Alone save some special result."""
        t_name_list = self.run_options.t_name_list
        cross_coe_map = self.analysis_options.cross_coe_map

        mark_info = f"{self}_coefficient"
        json_str = json.dumps(cross_coe_map, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=mark_info, prefix=".json")

        index_list = list(list(cross_coe_map.values())[0].keys())
        cross_coe_df = pd.DataFrame(data=cross_coe_map, index=index_list).T
        pyqlog.log("RESULT", f"{mark_info} {t_name_list}:\n{cross_coe_df}")
        if isinstance(self.file, LocalFile):
            if self.file._extension:
                ss = os.sep
                mark_info = f"{ss}{self.file._extension}{ss}{mark_info}"

            csv_name = "".join([self.file.dirs, mark_info, ".csv"])
            dir_path = os.path.dirname(csv_name)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
            cross_coe_df.to_csv(csv_name)

    def _extract_once_result(self, analysis_obj: "TopAnalysis") -> List:
        """Extract once analysis result.

        Args:
            analysis_obj: TopAnalysis object.

        Returns:
            list: [trust_flag, crz_amp, coef]
                trust_flag (bool): True or False, cross coefficient trust or not.
                crz_amp (float): Same as cross target z_amp.
                coef (float): Coefficient.

        """
        raise NotImplementedError

    def _split_once_analysis(self):
        """Split run once analysis."""
        x_data = self.run_options.x_data
        index = self.run_options.index
        parallel = self.run_options.parallel
        use_simulator = self.run_options.use_simulator
        rd_qubit_map = self.run_options.rd_qubit_map
        rd_dcm_map = self.run_options.rd_dcm_map
        t_rd_name_map = self.run_options.t_rd_name_map
        once_analysis_class = self.run_options.once_analysis_class
        once_ana_options = self.analysis_options.once_ana_options

        metadata = self._metadata()
        acq_class, acquisition_options = generate_acquisition_options(self)

        t_ed_dict = {}
        trust_map = {}
        crz_amp_map = {}
        coefficient_map = {}
        for t_name, rd_name in t_rd_name_map.items():
            rd_qubit: "Qubit" = rd_qubit_map.get(rd_name)
            rd_dcm = rd_dcm_map.get(rd_name)
            acq_options = deepcopy(acquisition_options)
            acq_options.sample_channels = [rd_qubit.readout_channel]
            acq_options.discriminator = rd_dcm
            acq_options.measure_qubits = []

            # acquisition data
            acq_obj = acq_class(**acq_options)
            if x_data is not None:
                acq_obj.x_list = x_data
            if parallel is False or use_simulator:
                # asyncio.run(acq_obj.async_execute_loop())
                acq_obj.execute_loop()
            else:
                acq_obj.execute_loop()

            meta_dict = MetaData(
                name=f"{self.label} {t_name} index-{index}",
                qubits=metadata.qubits,
                couplers=metadata.couplers,
                save_location=metadata.save_location,
                draw_meta={"measure": rd_name},
                process_meta={},
            )
            if metadata.draw_meta:
                meta_dict.draw_meta.update(metadata.draw_meta)
            if metadata.process_meta:
                meta_dict.process_meta.update(metadata.process_meta)

            t_file: "BaseFile" = deepcopy(self.file)
            t_file.file_extension(
                f"{t_name}{os.sep}",
                "",
                "",
                self.experiment_options.is_sub_merge,
            )

            t_ana_options = Options(**once_ana_options)
            t_ana_options.result_name = t_name
            t_ana_options.pure_exp_mode = False

            t_analysis_obj = base_analysis_process(
                acq_obj,
                acq_options,
                [rd_qubit],
                meta_dict,
                t_file,
                t_ana_options,
                once_analysis_class,
            )
            pyqlog.log("RESULT", format_results(t_analysis_obj))
            exp_data = t_analysis_obj.experiment_data

            try:
                once_res = self._extract_once_result(t_analysis_obj)
            except Exception as err:
                pyqlog.warning(f"target {t_name} analysis extract error: {err}")
                once_res = [False, 0.0, 0.0]

            trust_flag, crz_amp, coef = once_res
            if coef is np.inf or coef is np.nan:
                trust_flag = False
                coef = 0.0

            trust_map.update({t_name: trust_flag})
            crz_amp_map.update({t_name: crz_amp})
            coefficient_map.update({t_name: coef})
            t_ed_dict.update({t_name: exp_data})

        b_label = f"{index}"
        cross_coe_map = {b_label: coefficient_map}
        cross_trust_map = {b_label: trust_map}
        cross_z_amp_map = {b_label: crz_amp_map}
        extra_data_map = {b_label: t_ed_dict}

        self.analysis_options.cross_coe_map = cross_coe_map
        self.analysis_options.cross_trust_map = cross_trust_map
        self.run_options.cross_z_amp_map = cross_z_amp_map
        self.run_options.extra_data_map = extra_data_map

    async def _check_device_execution_end(self):
        """Overwrite. Check experiment execute end."""
        require_id = self.id
        while True:
            await asyncio.sleep(0.5)
            state = DataClient().query_task_state(require_id)
            if state in [
                TransferTaskStatusEnum.SUC,
                TransferTaskStatusEnum.TACKLE_SUC,
            ]:
                pyqlog.log( "EXP", f"RID: {require_id} run success, state: {state}")
                return True, ""
            elif state in [
                TransferTaskStatusEnum.FAIL,
                TransferTaskStatusEnum.TACKLE_FAIL,
            ]:
                msg = f"RID: {require_id} run failed, state: {state}"
                pyqlog.error(msg)
                return False, msg
            elif state in [TransferTaskStatusEnum.BLOCK]:
                msg = f"RID: {require_id} blocked, state: {state}"
                pyqlog.error(msg)
                return False, msg

    async def _async_analysis(self):
        """Define analysis operate."""
        await asyncio.sleep(0.001)
        execute_flag, msg = await self._check_device_execution_end()

        if execute_flag is True:
            self._split_once_analysis()

            experiment_data = ExperimentData(
                x_data=np.array([]),
                y_data={},
                experiment_id=self.id,
                metadata=self._metadata(),
            )
            analysis_obj = run_analysis_process(
                self.run_options.analysis_class,
                experiment_data,
                self.analysis_options,
                self.file,
            )
            return analysis_obj
        else:
            pyqlog.error(f"{self} id: {self.id} execute error: {msg}")


class ZCrossZampOnce(ZCrossV1OnceBase):
    """Z Crosstalk, scan target z_amp list."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment options:
            tz_amp_list (list): Set target z_amp list.
            drive_type (str): Set target XY line pulse type.

        """
        options = super()._default_experiment_options()

        options.set_validator("tz_amp_list", list)
        options.set_validator("drive_type", ["Drag", "Square"])
        options.set_validator("pre_delay", float)
        options.set_validator("after_delay", float)
        options.set_validator("square_params", dict)

        options.tz_amp_list = qarange(-0.02, 0.02, 0.002)
        options.drive_type = "Drag"
        options.pre_delay = 5000
        options.after_delay = 200
        options.square_params = {
            "time": 5000,
            "offset": 15,
            "amp": 1.0,
            "detune": 0.0,
            "freq": 1050.0,
        }

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.once_analysis_class = CrosstalkOnceAnalysis
        options.xy_pulse_width = 5000
        options.injection_func = ["_get_xy_pulse"]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        options.once_ana_options = {"quality_bounds": [0.8, 0.6, 0.5]}

        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta.update({"drive_type": self.experiment_options.drive_type})
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        tz_amp_list = self.experiment_options.tz_amp_list
        drive_type = self.experiment_options.drive_type
        pre_delay = self.experiment_options.pre_delay
        after_delay = self.experiment_options.after_delay
        square_params = self.experiment_options.square_params

        if drive_type == "Drag":
            xy_wave = self.qubits[0].XYwave
            mid_width = xy_wave.time + 2 * xy_wave.offset
        else:
            mid_width = square_params.get("time")
        xy_pulse_width = pre_delay + mid_width + after_delay

        self.set_run_options(x_data=tz_amp_list, xy_pulse_width=xy_pulse_width)

    def _get_xy_pulse(self, qubit: "Qubit") -> "PulseComponent":
        """Get once Qubit XY pulse."""
        drive_type = self.experiment_options.drive_type
        pre_delay = self.experiment_options.pre_delay
        after_delay = self.experiment_options.after_delay
        square_params = self.experiment_options.square_params

        if drive_type == "Drag":
            drive_pulse = pi_pulse(qubit)
        elif drive_type == "Square":
            pulse_params = square_params.copy()
            pulse_params.update({"freq": qubit.XYwave.baseband_freq})
            drive_pulse = SquareEnvelop(**pulse_params)
        else:
            # Now no use.
            xy_time = qubit.XYwave.time + 2 * qubit.XYwave.offset
            drive_pulse = Constant(xy_time, 0.0, name="XY")

        pre_pulse = Constant(pre_delay, 0.0, name="XY")
        after_pulse = Constant(after_delay, 0.0, name="XY")
        total_pulse = pre_pulse() + drive_pulse() + after_pulse()
        return total_pulse

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulses."""
        tz_amp_list = self.experiment_options.tz_amp_list
        t_xy_name_map = self.run_options.t_xy_name_map
        t_rd_name_map = self.run_options.t_rd_name_map
        xy_name_list = list(t_xy_name_map.values())
        rd_name_list = list(t_rd_name_map.values())

        length = len(tz_amp_list)
        width_list = []
        for qubit in self.qubits:
            q_name = qubit.name
            if width_list:
                pulse_width = max(width_list)
            else:
                pulse_width = self.run_options.xy_pulse_width

            if q_name in xy_name_list:
                pulse_list = [self._get_xy_pulse(qubit) for i in range(length)]
                width_list.append(pulse_list[0].width)
            elif q_name in rd_name_list:
                pulse_list = [
                    Constant(pulse_width, 0.0, name="XY")() for i in range(length)
                ]
            else:
                pulse_list = [
                    Constant(pulse_width, 0.0, name="XY")() for i in range(length)
                ]
            self.play_pulse("XY", qubit, pulse_list)

        if width_list:
            xy_pulse_width = max(width_list)
            self.run_options.xy_pulse_width = xy_pulse_width

    @staticmethod
    def set_z_pulses(self):
        """Set Z pulse."""
        bz_amp_map = self.experiment_options.bz_amp_map
        tz_amp_list = self.experiment_options.tz_amp_list
        qc_map = self.run_options.qc_map
        b_name_list = self.run_options.b_name_list
        t_name_list = self.run_options.t_name_list
        xy_pulse_width = self.run_options.xy_pulse_width

        for qc_name, qc_obj in qc_map.items():
            if qc_name in b_name_list:
                bz_amp = bz_amp_map.get(qc_name, 0.0)
                z_pulses = [
                    Constant(xy_pulse_width, bz_amp, name="Z")() for i in tz_amp_list
                ]
            elif qc_name in t_name_list:
                z_pulses = []
                for tz_amp in tz_amp_list:
                    pulse_obj = Constant(xy_pulse_width, tz_amp, name="Z")()
                    z_pulses.append(pulse_obj)
            else:
                z_pulses = [
                    Constant(xy_pulse_width, 0.0, name="Z")() for i in tz_amp_list
                ]
            self.play_pulse("Z", qc_obj, z_pulses)

    def _extract_once_result(self, analysis_obj: "TopAnalysis") -> List:
        """Extract once analysis result."""
        bias_name = self.experiment_options.bias_name_list[0]
        bz_amp = self.experiment_options.bz_amp_map.get(bias_name)
        quality_obj = analysis_obj.quality
        t_name = analysis_obj.options.result_name
        if quality_obj and quality_obj.descriptor in [
            Quality.perfect.value,
            Quality.normal.value,
        ]:
            if "t_offset" in analysis_obj.results:
                crz_amp = analysis_obj.results.t_offset.value
            else:
                crz_amp = analysis_obj.results.b.value
            trust_flag = True
        else:
            pyqlog.warning(
                f"once {t_name} analysis quality: {quality_obj}, "
                f"use max index select best_ac value."
            )
            analysis_data = list(analysis_obj.analysis_datas.values())[0]
            x_arr = analysis_data.x
            y_arr = analysis_data.y
            crz_amp = x_arr[np.argmax(y_arr)]
            trust_flag = False

        coef = round(crz_amp / bz_amp, 6)
        return [trust_flag, crz_amp, coef]


@options_wrapper
class ZCrossDelayOnce(ZCrossV1OnceBase):
    """Z Crosstalk, scan target delay list."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment options:
            delays (list): Set target delay list.
            fringe (float): Set target ramsey fringe value.
            z_amp (float): Set target Z line amp.

        """
        options = super()._default_experiment_options()

        options.set_validator("delays", list)
        options.set_validator("fringe", float)
        options.set_validator("z_amp", float)

        options.delays = qarange(0, 100, 2.5)
        options.fringe = 50  # MHz
        options.z_amp = None

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.drag_width = 30
        options.injection_func = [
            "_get_ramsey_xy_pulses",
            "_get_zero_xy_pulses",
            "_get_z_pulses",
        ]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        options.once_ana_options = {
            "factor": 3.5,
            "quality_bounds": [0.9, 0.8, 0.7],
        }

        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta["fringe"] = self.experiment_options.fringe

        z_amp = self.experiment_options.z_amp
        if z_amp is not None:
            metadata.draw_meta["z_amp"] = z_amp
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        delays = self.experiment_options.delays

        if self.experiment_options.z_amp is not None:
            once_analysis_class = RamseyZampAnalysis
        else:
            once_analysis_class = RamseyAnalysis
        self.set_run_options(once_analysis_class=once_analysis_class, x_data=delays)

    @staticmethod
    def _get_ramsey_xy_pulses(qubit, delays: List[float], fringe: float):
        """Get XY line ramsey pulses."""
        xy_pulse_list = []
        for delay in delays:
            front_drag = half_pi_pulse(qubit)
            center_delay = Constant(delay, 0, "XY")
            rear_drag = half_pi_pulse(qubit)

            r_phase = 2 * np.pi * fringe * 1e6 / 1e9 * delay
            ramsey_pulse = front_drag() + center_delay() + rear_drag(phase=r_phase)
            ramsey_pulse.bit = qubit.name
            ramsey_pulse.sweep = "sweep delay"
            xy_pulse_list.append(ramsey_pulse)

        return xy_pulse_list

    @staticmethod
    def _get_zero_xy_pulses(qubit, delays: List[float]):
        """Get XY line zero pulses."""
        xy_pulse_list = []
        for delay in delays:
            front_zero = zero_pulse(qubit)
            center_delay = Constant(delay, 0, "XY")
            rear_zero = zero_pulse(qubit)

            ramsey_pulse = front_zero() + center_delay() + rear_zero()
            ramsey_pulse.bit = qubit.name
            xy_pulse_list.append(ramsey_pulse)

        return xy_pulse_list

    @staticmethod
    def _get_z_pulses(qubit, delays: List[float], z_amp: float, drag_width: float):
        """Get Z line pulse list."""
        z_pulse_list = []
        for delay in delays:
            front_constant = Constant(drag_width, 0)
            center_delay = Constant(delay, z_amp)
            rear_constant = Constant(drag_width, 0)
            ramsey_z_pulse = front_constant() + center_delay() + rear_constant()
            ramsey_z_pulse.bit = qubit.name
            ramsey_z_pulse.sweep = "sweep delay"
            z_pulse_list.append(ramsey_z_pulse)

        return z_pulse_list

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulses."""
        delays = self.experiment_options.delays
        fringe = self.experiment_options.fringe
        t_xy_name_map = self.run_options.t_xy_name_map
        t_rd_name_map = self.run_options.t_rd_name_map
        xy_name_list = list(t_xy_name_map.values())
        rd_name_list = list(t_rd_name_map.values())

        drag_width_list = []
        for qubit in self.qubits:
            q_name = qubit.name
            if q_name in xy_name_list:
                zero_pulse_obj = zero_pulse(qubit)
                drag_width_list.append(zero_pulse_obj.width)
                pulse_list = self._get_ramsey_xy_pulses(qubit, delays, fringe)
            elif q_name in rd_name_list:
                pulse_list = self._get_zero_xy_pulses(qubit, delays)
            else:
                pulse_list = self._get_zero_xy_pulses(qubit, delays)
            self.play_pulse("XY", qubit, pulse_list)

        if drag_width_list:
            drag_width = max(drag_width_list)
            self.run_options.drag_width = drag_width

    @staticmethod
    def set_z_pulses(self):
        """Set Z pulse."""
        bz_amp_map = self.experiment_options.bz_amp_map
        delays = self.experiment_options.delays
        z_amp = self.experiment_options.z_amp
        qc_map = self.run_options.qc_map
        b_name_list = self.run_options.b_name_list
        t_name_list = self.run_options.t_name_list
        drag_width = self.run_options.drag_width

        z_amp = z_amp if isinstance(z_amp, (int, float)) else 0.0
        for qc_name, qc_obj in qc_map.items():
            if qc_name in b_name_list:
                qc_z_amp = bz_amp_map.get(qc_name, 0.0)
            elif qc_name in t_name_list:
                qc_z_amp = z_amp
            else:
                qc_z_amp = 0.0
            z_pulses = self._get_z_pulses(qc_obj, delays, qc_z_amp, drag_width)
            self.play_pulse("Z", qc_obj, z_pulses)

    def _extract_once_result(self, analysis_obj: "TopAnalysis") -> List:
        """Extract once analysis result."""
        ac_branch = self.experiment_options.ac_branch
        bz_amp = self.experiment_options.bz_amp
        fringe = self.experiment_options.fringe
        qc_map = self.run_options.qc_map

        quality_obj = analysis_obj.quality
        osc_freq = analysis_obj.results.freq.value
        t_name = analysis_obj.options.result_name
        qc_obj = qc_map.get(t_name)

        if quality_obj and quality_obj.descriptor in [
            Quality.perfect.value,
            Quality.normal.value,
        ]:
            trust_flag = True
        else:
            pyqlog.warning(f"once {t_name} analysis quality: {quality_obj}")
            trust_flag = False

        # AIO 72bit, calculate frequency by oscillate frequency
        if fringe > 0:
            freq = qc_obj.drive_freq + fringe - osc_freq
        else:
            freq = qc_obj.drive_freq + fringe + osc_freq
        z_amp = freq_to_amp(qc_obj, freq, new_offset=0, branch=ac_branch)
        if np.isnan(z_amp):
            pyqlog.warning(
                f"{t_name} frequency {freq} MHz to amp result is nan! "
                f"Please check {t_name} ac_spectrum params or other!"
            )
            trust_flag = False
            crz_amp = 0.0
            coef = 0.0
        else:
            crz_amp = z_amp - qc_obj.idle_point
            coef = round(crz_amp / bz_amp, 4)

        return [trust_flag, crz_amp, coef]

    @staticmethod
    def update_instrument(self):
        """Update instrument parameters."""
        delays = self.experiment_options.delays
        rd_channels = self.experiment_options.multi_readout_channels

        sweep_delay = self._pulse_time_list[: len(delays)]
        for channel in rd_channels:
            self.sweep_readout_trigger_delay(channel, sweep_delay)


class ZCrossDelayLinearOnce(ZCrossDelayOnce):
    """Z Crosstalk, scan target delay list, but bias Z pulse is Linear Pulse."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment options:
            rate (float): Set bias Z line amp change rate.

        """
        options = super()._default_experiment_options()
        options.set_validator("rate", float)

        options.rate = -0.001
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        # BzRamseyAnalysis options
        options.once_ana_options = {
            "acf": 100,  # AC coefficient, df / dv
            "quality_bounds": [0.95, 0.85, 0.75],
        }

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.once_analysis_class = BzRamseyAnalysis
        options.injection_func.extend(["_get_bz_pulses"])

        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta["rate"] = self.experiment_options.rate
        return metadata

    def _check_options(self):
        """Check Options."""
        super(ZCrossDelayOnce, self)._check_options()

        delays = self.experiment_options.delays
        bz_amp = self.experiment_options.bz_amp
        fringe = self.experiment_options.fringe
        rate = self.experiment_options.rate
        once_ana_options = self.analysis_options.once_ana_options

        once_ana_options.update(
            {
                "rate": rate,
                "bz_amp": bz_amp,
                "fringe": fringe,
            }
        )
        self.set_run_options(x_data=delays)

    @staticmethod
    def _get_bz_pulses(
        qubit, delays: List[float], z_amp: float, drag_width: float, rate: float
    ):
        """Get bias Z line pulse list."""
        z_pulse_list = []
        for delay in delays:
            front_constant = Constant(drag_width, 0)
            center_delay = Linear(delay, z_amp, rate)
            rear_constant = Constant(drag_width, 0)
            ramsey_z_pulse = front_constant() + center_delay() + rear_constant()
            ramsey_z_pulse.bit = qubit.name
            ramsey_z_pulse.sweep = "sweep delay"
            z_pulse_list.append(ramsey_z_pulse)

        return z_pulse_list

    @staticmethod
    def set_z_pulses(self):
        """Set Z pulse."""
        bz_amp_map = self.experiment_options.bz_amp_map
        delays = self.experiment_options.delays
        z_amp = self.experiment_options.z_amp
        rate = self.experiment_options.rate
        qc_map = self.run_options.qc_map
        b_name_list = self.run_options.b_name_list
        t_name_list = self.run_options.t_name_list
        drag_width = self.run_options.drag_width

        z_amp = z_amp if isinstance(z_amp, (int, float)) else 0.0
        for qc_name, qc_obj in qc_map.items():
            if qc_name in b_name_list:
                bz_amp = bz_amp_map.get(qc_name, 0.0)
                z_pulses = self._get_bz_pulses(qc_obj, delays, bz_amp, drag_width, rate)
            elif qc_name in t_name_list:
                z_pulses = self._get_z_pulses(qc_obj, delays, z_amp, drag_width)
            else:
                z_pulses = self._get_z_pulses(qc_obj, delays, 0.0, drag_width)
            self.play_pulse("Z", qc_obj, z_pulses)

    def _extract_once_result(self, analysis_obj: "TopAnalysis") -> List:
        """Extract once analysis result."""
        bz_amp = self.experiment_options.bz_amp
        delays = self.experiment_options.delays
        rate = self.experiment_options.rate
        max_delay = max(delays)

        quality_obj = analysis_obj.quality
        coef = analysis_obj.results.cr.value
        t_name = analysis_obj.options.result_name

        crz_amp = round(coef * (bz_amp + rate * max_delay) / 2, 4)
        if quality_obj and quality_obj.descriptor in [
            Quality.perfect.value,
            Quality.normal.value,
        ]:
            trust_flag = True
        else:
            pyqlog.warning(f"once {t_name} analysis quality: {quality_obj}")
            trust_flag = False

        return [trust_flag, crz_amp, coef]


class SpinEchoZCrosstalk0nce(ZCrossDelayOnce):
    """YXY develop personal experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment options:
            delays (list): Set target delay list.
            fringe (float): Set target ramsey fringe value.
            z_amp (float): Set target Z line amp.

        """
        options = super()._default_experiment_options()

        options.tz_amp_map = {}

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.injection_func.extend(
            [
                "_get_xy_pulse",
                "_get_zero_xy_pulses",
                "_get_z_pulses",
                "_get_target_z_pulses",
            ]
        )

        return options

    @staticmethod
    def _get_xy_pulse(qubit, delays: List, fringe: float):
        """Get XY line wave."""
        pulse_list = []
        buffer = 100
        for delay in delays:
            left_delay_time = round(delay / 2, 4)
            right_delay_time = delay - left_delay_time

            front_drag = half_pi_pulse(qubit)
            left_delay = Constant(left_delay_time, 0, "XY")
            left_buffer = Constant(buffer, 0, "XY")
            mid_drag = pi_pulse(qubit)
            right_delay = Constant(right_delay_time, 0, "XY")
            right_buffer = Constant(buffer, 0, "XY")
            rear_drag = deepcopy(front_drag)

            exp_pulse = (
                front_drag()
                + left_delay()
                + left_buffer()
                + mid_drag()
                + right_delay()
                + right_buffer()
                + rear_drag(phase=2 * np.pi * fringe * 1e6 / 1e9 * delay)
            )
            exp_pulse.bit = qubit.name
            exp_pulse.sweep = "sweep delay"

            pulse_list.append(exp_pulse)

        return pulse_list

    @staticmethod
    def _get_zero_xy_pulses(qubit, delays: List[float]):
        """Get XY line zero pulses."""
        pulse_list = []
        buffer = 100
        for delay in delays:
            left_delay_time = round(delay / 2, 4)
            right_delay_time = delay - left_delay_time

            front_drag = zero_pulse(qubit)
            left_delay = Constant(left_delay_time, 0, "XY")
            left_buffer = Constant(buffer, 0, "XY")
            mid_drag = zero_pulse(qubit)
            right_delay = Constant(right_delay_time, 0, "XY")
            right_buffer = Constant(buffer, 0, "XY")
            rear_drag = deepcopy(front_drag)

            exp_pulse = (
                front_drag()
                + left_delay()
                + left_buffer()
                + mid_drag()
                + right_delay()
                + right_buffer()
                + rear_drag()
            )
            exp_pulse.bit = qubit.name
            exp_pulse.sweep = "sweep delay"

            pulse_list.append(exp_pulse)
        return pulse_list

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulses."""
        delays = self.experiment_options.delays
        fringe = self.experiment_options.fringe
        t_xy_name_map = self.run_options.t_xy_name_map
        t_rd_name_map = self.run_options.t_rd_name_map
        xy_name_list = list(t_xy_name_map.values())
        rd_name_list = list(t_rd_name_map.values())

        drag_width_list = []
        for qubit in self.qubits:
            q_name = qubit.name
            if q_name in xy_name_list:
                zero_pulse_obj = zero_pulse(qubit)
                drag_width_list.append(zero_pulse_obj.width)
                pulse_list = self._get_xy_pulse(qubit, delays, fringe)
            elif q_name in rd_name_list:
                pulse_list = self._get_zero_xy_pulses(qubit, delays)
            else:
                pulse_list = self._get_zero_xy_pulses(qubit, delays)
            self.play_pulse("XY", qubit, pulse_list)

        if drag_width_list:
            drag_width = max(drag_width_list)
            self.run_options.drag_width = drag_width

    @staticmethod
    def _get_z_pulses(qubit, delays: List[float], bz_amp: float, drag_width: float):
        """Get Z line pulse list."""
        z_pulse_list = []
        buffer = 100
        for delay in delays:
            left_delay_time = round(delay / 2, 4)
            right_delay_time = delay - left_delay_time

            front_drag = Constant(drag_width, 0)
            left_delay = Constant(left_delay_time, bz_amp)
            left_buffer = Constant(buffer, 0)
            mid_drag = Constant(drag_width, 0)
            right_delay = Constant(right_delay_time, -bz_amp)
            right_buffer = Constant(buffer, 0)
            rear_drag = Constant(drag_width, 0)

            exp_pulse = (
                front_drag()
                + left_delay()
                + left_buffer()
                + mid_drag()
                + right_delay()
                + right_buffer()
                + rear_drag()
            )
            exp_pulse.bit = qubit.name
            exp_pulse.sweep = "sweep delay"
            z_pulse_list.append(exp_pulse)

        return z_pulse_list

    @staticmethod
    def _get_target_z_pulses(
        qubit, delays: List[float], z_amp: float, drag_width: float
    ):
        """Get Z line pulse list."""
        z_pulse_list = []
        buffer = 100
        for delay in delays:
            left_delay_time = round(delay / 2, 4)
            right_delay_time = delay - left_delay_time

            front_drag = Constant(drag_width, 0)
            left_delay = Constant(left_delay_time, z_amp)
            left_buffer = Constant(buffer, 0)
            mid_drag = Constant(drag_width, 0)
            right_delay = Constant(right_delay_time, z_amp)
            right_buffer = Constant(buffer, 0)
            rear_drag = Constant(drag_width, 0)

            exp_pulse = (
                front_drag()
                + left_delay()
                + left_buffer()
                + mid_drag()
                + right_delay()
                + right_buffer()
                + rear_drag()
            )
            exp_pulse.bit = qubit.name
            exp_pulse.sweep = "sweep delay"
            z_pulse_list.append(exp_pulse)

        return z_pulse_list

    @staticmethod
    def set_z_pulses(self):
        """Set Z pulse."""
        bz_amp_map = self.experiment_options.bz_amp_map
        delays = self.experiment_options.delays
        z_amp = self.experiment_options.z_amp
        tz_amp_map = self.experiment_options.tz_amp_map
        qc_map = self.run_options.qc_map
        b_name_list = self.run_options.b_name_list
        t_name_list = self.run_options.t_name_list
        drag_width = self.run_options.drag_width

        z_amp = z_amp if isinstance(z_amp, (int, float)) else 0.0
        for qc_name, qc_obj in qc_map.items():
            if qc_name in b_name_list:
                qc_z_amp = bz_amp_map.get(qc_name, 0.0)
                z_pulses = self._get_z_pulses(qc_obj, delays, qc_z_amp, drag_width)
            elif qc_name in t_name_list:
                qc_z_amp = tz_amp_map.get(qc_name, z_amp)
                z_pulses = self._get_target_z_pulses(
                    qc_obj, delays, qc_z_amp, drag_width
                )
            else:
                qc_z_amp = 0.0
                z_pulses = self._get_z_pulses(qc_obj, delays, qc_z_amp, drag_width)

            self.play_pulse("Z", qc_obj, z_pulses)

    def _extract_once_result(self, analysis_obj: "TopAnalysis") -> List:
        """Extract once analysis result."""
        ac_branch = self.experiment_options.ac_branch
        # bz_amp = self.experiment_options.bz_amp
        fringe = self.experiment_options.fringe
        qc_map = self.run_options.qc_map

        quality_obj = analysis_obj.quality
        osc_freq = analysis_obj.results.freq.value
        t_name = analysis_obj.options.result_name
        qc_obj = qc_map.get(t_name)

        if quality_obj and quality_obj.descriptor in [
            Quality.perfect.value,
            # Quality.normal.value,
        ]:
            trust_flag = True
        else:
            pyqlog.warning(f"once {t_name} analysis quality: {quality_obj}")
            trust_flag = False

        # AIO 72bit, calculate frequency by oscillate frequency
        # if fringe > 0:
        #     freq = qc_obj.drive_freq + fringe - osc_freq
        # else:
        #     freq = qc_obj.drive_freq + fringe + osc_freq

        return [trust_flag, osc_freq - fringe, 0]
