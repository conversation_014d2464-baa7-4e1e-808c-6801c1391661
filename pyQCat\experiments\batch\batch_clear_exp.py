# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/04/02
# __author:       <PERSON>

import json
import os
from pathlib import Path

import numpy as np

from ...analysis.algorithms.generate_clear_params import (
    generate_clear_params,
    generate_clear_params_opt,
)
from ...log import pyqlog
from ...structures import QDict
from ..batch_experiment import BatchExperiment
from ..composite.readout.photon_experiment import NMClearParams


def save_text(path: str, text):
    os.makedirs(os.path.dirname(path), exist_ok=True)

    with open(path, "wt", encoding="utf-8") as write_file:
        write_file.write(text)


class BatchClearExp(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.set_validator("step", float)

        options.points = 11
        options.clear_flow = [
            "ReadoutFreqCalibrate",
            "SingleShot_0",
            "PhotonScanReadoutFreq",
            "NewSingleShot_0",
            "AmpToPhoton",
            "SingleShot_1",
            "DephaseRamseyComposite",
            "NMClearParamsBoth",
        ]

        options.physical_units = (["q24", "q51"],)
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()

        options.res_dict = QDict()
        options.pass_units = None
        options.cal_clear_params = QDict()
        options.qubit_maps = None
        options.nm_params = QDict()

        return options

    def _set_ana_params(self, qubit_maps, unit, exp):
        exp_name = exp.meta.get("exp_class_name")
        ana_params = qubit_maps.get(unit).get("analysis_params")
        self.change_parallel_exec_ana_options(
            exp_name,
            unit,
            options={
                "child_ana_options.chi": ana_params.get("chi"),
                "child_ana_options.kappa": ana_params.get("kappa"),
                "child_ana_options.t2echo": ana_params.get("t2echo"),
            },
        )

    def _run_flow_for_rd_freq_cali(self, flow, physical_units):
        res_dict = self.run_options.res_dict
        pass_units = self._run_flow(
            flows=[flow],
            physical_units=physical_units,
            name="ReadoutFreqCalibrate Flow",
        )

        for key, params in self.batch_records.items():
            if flow in key:
                for unit in pass_units:
                    if params.analysis_data.get(unit):
                        res = params.analysis_data.get(unit).result
                        mean_freq = res.get("mean_fr")
                        frs = res.get("frs")
                        res_dict[unit].update(
                            {"mean_fr": mean_freq, "fr0": frs[0], "fr1": frs[1]}
                        )
        self.set_run_options(res_dict=res_dict)
        return pass_units

    def _run_flow_for_ss0(self, flow, physical_units):
        res_dict = self.run_options.res_dict
        self._save_data_to_json(res_dict, "result")
        exp_units = []
        for unit in physical_units:
            if res_dict:
                mean_freq = res_dict.get(unit).get("mean_fr")
                fr0 = res_dict.get(unit).get("fr0")
                fr1 = res_dict.get(unit).get("fr1")
                if mean_freq and fr0 and fr1:
                    # probe_freq = round((mean_freq + fr1) / 2, 3)
                    probe_freq = round(mean_freq, 3)
                    # probe_freq = round((mean_freq + fr0) / 2, 3)
                    q_obj = self.context_manager.chip_data.cache_qubit.get(unit)
                    pyqlog.info(f"probe_freq = {probe_freq}")
                    setattr(q_obj, "probe_freq", probe_freq)
                    q_obj._check_lo("M")
                    exp_units.append(unit)
            else:
                pyqlog.error("mean_freq parameter cannot be empty")

        pass_units = self._run_flow(
            flows=[flow], physical_units=exp_units, name="SingleShot Flow"
        )
        return pass_units

    def _run_flow_for_photo(self, flow, physical_units):
        res_dct = self.run_options.res_dict
        exp = self.params_manager.exp_map.get(flow)
        exp_name = exp.meta.get("exp_class_name")
        points = self.experiment_options.points
        for unit in physical_units:
            fr0 = res_dct.get(unit).get("fr0")
            fr1 = res_dct.get(unit).get("fr1")
            mean_fr = res_dct.get(unit).get("mean_fr")
            # sweep_list = np.linspace(fr1, mean_fr, step)
            sweep_list = np.round(
                np.linspace(
                    round((fr1 + mean_fr) / 2, 3), round((fr0 + mean_fr) / 2, 3), points
                ),
                3,
            ).tolist()
            # sweep_list = np.linspace(mean_fr, fr0, step)
            pyqlog.info(f"{exp_name}, sweep_freq_list: {sweep_list}")
            self.change_parallel_exec_exp_options(
                exp_name, unit, options={"sweep_list": sweep_list}
            )
        res_dict = {}
        qubit_maps = self.run_options.qubit_maps
        for unit in physical_units:
            self._set_ana_params(qubit_maps, unit, exp)
        pass_units = self._run_flow(
            flows=[flow],
            physical_units=physical_units,
            name="PhotonScanReadoutFreq Flow",
        )
        for key, params in self.batch_records.items():
            if flow in key:
                for unit in physical_units:
                    if params.analysis_data.get(unit):
                        res = params.analysis_data.get(unit).result
                        target_freq = res.get("target_freq")
                        res_dict.update({unit: {"target_freq": target_freq}})
                        if target_freq:
                            probe_freq = round(target_freq, 3)
                            q_obj = self.context_manager.chip_data.cache_qubit.get(unit)
                            setattr(q_obj, "probe_freq", probe_freq)
                            q_obj._check_lo("M")
                            pyqlog.info(f"probe_freq = {probe_freq}")
                        else:
                            pyqlog.error("target_freq parameter cannot be empty")

        self.set_run_options(res_dict=res_dict)
        return pass_units

    def _run_flow_for_ns(self, flow, physical_units):
        # res_dict = self.run_options.res_dict
        # exp_units = []
        # for unit in physical_units:
        #     if res_dict:
        #         target_freq = res_dict.get(unit).get("target_freq")
        #         if target_freq:
        #             probe_freq = round(target_freq, 3)
        #             q_obj = self.context_manager.chip_data.cache_qubit.get(unit)
        #             pyqlog.info(f"probe_freq = {probe_freq}")
        #             setattr(q_obj, "probe_freq", probe_freq)
        #             exp_units.append(unit)
        #     else:
        #         pyqlog.error(f"target_freq parameter cannot be empty")
        pass_units = self._run_flow(
            flows=[flow],
            physical_units=physical_units,
        )
        return pass_units

    def _run_flow_for_atphoto(self, flow, physical_units):
        cal_clear_params = self.run_options.cal_clear_params
        qubit_maps = self.run_options.qubit_maps
        exp = self.params_manager.exp_map.get(flow)
        exp_name = exp.meta.get("exp_class_name")
        exp_units = []
        for unit in physical_units:
            params = qubit_maps.get(unit)
            if params:
                analysis_params = params.get("analysis_params")
                exp_units.append(unit)
                chi = analysis_params.get("chi")
                kappa = analysis_params.get("kappa")
                self.change_parallel_exec_ana_options(
                    exp_name, unit, options={"kappa": kappa, "chi_eff": chi}
                )

        pass_units = self._run_flow(
            flows=[flow], physical_units=exp_units, name="PhotonNumMeasVsAmp Flow"
        )
        for key, params in self.batch_records.items():
            if flow in key:
                for unit in pass_units:
                    if params.analysis_data.get(unit):
                        res = params.analysis_data.get(unit).result
                        coe = res.get("coeff")
                        offset = res.get("offset")
                        x0 = res.get("x0")
                        cal_clear_params[unit].update(
                            {"coeff": coe, "offset": offset, "x0": x0}
                        )
        self.set_run_options(cal_clear_params=cal_clear_params)
        json_str = json.dumps(cal_clear_params, ensure_ascii=False, indent=4)
        save_text(rf"{os.getcwd()}\cal_clear_params.json", json_str)
        return pass_units

    def _run_for_flow_dp_com(self, flow, physical_units):
        cal_clear_params = self.run_options.cal_clear_params
        exp = self.params_manager.exp_map.get(flow)
        qubit_maps = self.run_options.qubit_maps
        for unit in physical_units:
            self._set_ana_params(qubit_maps, unit, exp)
        pass_units = self._run_flow(
            flows=[flow],
            physical_units=physical_units,
            name="DePhaseRamseyComposite Flow",
        )
        for key, params in self.batch_records.items():
            if flow in key:
                for unit in pass_units:
                    if params.analysis_data.get(unit):
                        res = params.analysis_data.get(unit).result
                        n0 = res.get("n0")
                        cal_clear_params[unit].update({"n0": n0})
        self.set_run_options(cal_clear_params=cal_clear_params)
        json_str = json.dumps(cal_clear_params, ensure_ascii=False, indent=4)
        save_text(rf"{os.getcwd()}\cal_clear_params.json", json_str)
        return pass_units

    def _run_for_flow_nm_clear(self, flow, physical_units):
        cal_clear_params = self.run_options.cal_clear_params
        qubit_maps = self.run_options.qubit_maps
        exp = self.params_manager.exp_map.get(flow)
        exp_units = []
        for unit in physical_units:
            nmc_params = {}
            sys_params = qubit_maps.get(unit).get("system_params")
            params = cal_clear_params.get(unit)
            # params = {
            #     "coeff": 523,
            #     "offset": 0,
            #     "x0": 0,
            #     "n0": 0
            # }
            exp_name = exp.meta.get("exp_class_name")
            if sys_params and params:
                tkick = sys_params.get("tkick")
                exp_units.append(unit)
                sys_params.update(params)
                amp1, amp2 = generate_clear_params(**sys_params, qubit_name=unit)
                nmc_params.update(
                    {"Clear": {"amp1": amp1, "amp2": amp2, "tkick": tkick * 1e3}}
                )
                # nmc_params.update(
                #     {"Clear": {"amp1": -0.18, "amp2": 0.1, "tkick": 0.15 * 1e3}}
                # )
                self.change_parallel_exec_exp_options(
                    exp_name,
                    unit,
                    options={
                        "child_exp_options.acq_pulse_params": nmc_params,
                        "child_exp_options.acq_pulse_type": "Clear",
                    },
                )
                self._set_ana_params(qubit_maps, unit, exp)

        pass_units = self._run_flow(
            flows=[flow], physical_units=exp_units, name="NMClearParamsBoth Flow"
        )
        return pass_units

    def _run_batch(self):
        physical_units = self.experiment_options.physical_units
        clear_flow = self.experiment_options.clear_flow
        self.pre_operate()
        if clear_flow:
            pass_units = self.run_options.pass_units
            for flow in clear_flow:
                if flow == "ReadoutFreqCalibrate":
                    pass_units = self._run_flow_for_rd_freq_cali(flow, physical_units)
                elif flow == "SingleShot_0":
                    pass_units = self._run_flow_for_ss0(flow, physical_units)
                elif flow == "PhotonScanReadoutFreq":
                    pass_units = self._run_flow_for_photo(flow, physical_units)
                elif flow == "PhotonNumMeasVsAmp":
                    pass_units = self._run_flow_for_atphoto(flow, physical_units)
                elif flow == "SingleShot_1":
                    self._run_flow(
                        flows=[flow],
                        physical_units=pass_units,
                        name="SingleShot Flow 2",
                    )
                elif flow == "DePhaseRamseyComposite":
                    pass_units = self._run_for_flow_dp_com(flow, physical_units)
                elif flow == "NMClearParamsBoth":
                    pass_units = self._run_for_flow_nm_clear(flow, physical_units)

    def pre_operate(self):
        data_json_path = Path(
            r"D:\code\shenxiang\pyqcat-apps-B-0.22.4\pyqcat-apps\app\batch_test\libs\cavity_reset\exp\qubit_data.json"
        )

        with open(data_json_path, "r", encoding="utf-8") as f:
            content = f.read()
            data = json.loads(content)

        qubit_maps = {}

        for qubit, single_params in data.items():
            # clear_params = single_params.get("clear_params", None)
            # phase_params0 = single_params.get("phase_params0", None)
            # phase_params1 = single_params.get("phase_params1", None)
            cavity_kappa = single_params.get("cavity_decay") * 2 * np.pi
            idle_2chi = single_params.get("idle_2chi") * 2 * np.pi
            t2echo = single_params.get("t2_echo")
            g = single_params.get("QCavity_couple") * 2 * np.pi
            alpha = single_params.get("anharm") * 2 * np.pi
            idle_freq = single_params.get("idle_freq") * 2 * np.pi
            bare_cavity_freq = single_params.get("bare_cavity") * 2 * np.pi

            tau = single_params.get("tau")
            tkick = single_params.get("tkick")

            idle_chi_eff = idle_2chi / 2

            sys_params = {
                "amp1_list": np.linspace(-30, 0, 201) * 2 * np.pi,
                "amp2_list": np.linspace(0, 30, 201) * 2 * np.pi,
                "wq": idle_freq,
                "wr": bare_cavity_freq,
                "alpha": alpha,
                "g": g,
                "tau": tau,
                "kappa": cavity_kappa,
                "tkick": tkick,
            }

            ana_params = {"kappa": cavity_kappa, "chi": idle_chi_eff, "t2echo": t2echo}

            qubit_maps.update(
                {qubit: {"system_params": sys_params, "analysis_params": ana_params}}
            )

        self.set_run_options(qubit_maps=qubit_maps)


class BatchClearCalibration(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.wr_flow = ["CavityFreqSpectrum_high_power"]
        options.clear_flow = [
            "T2SpinEcho",
            "CavityFreqSpectrum_0",
            "ReadoutFreqCalibrate",
            "XpiDetection",
            "PhotonScanReadoutFreqV2",
            "SingleShot_0",
            "PhotonNumMeasVsAmp",
            "NMClearParamsBoth",
        ]
        options.tkick = 100  # ns
        options.point = 10  # 0、1 腔区间内扫描的点数
        options.high_m_amp = 0.9
        options.bare_cav_f_shift = 20
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.clear_opt_params = QDict()
        options.clear_records = {}
        options.his_params = {}
        return options

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if exp_name == "CavityFreqSpectrum_high_power":
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    qubit = self.backend.chip_data.cache_qubit.get(unit)
                    self.run_options.clear_opt_params[unit] = QDict(
                        wq=qubit.drive_freq * 2 * np.pi,
                        wr=record.analysis_data.get(unit).result.get("fr") * 2 * np.pi,
                        alpha=qubit.anharmonicity * 2 * np.pi,
                        chi_eff=None,
                        kappa=None,
                        tau=qubit.Mwave.width / 1e3,
                        tkick=self.experiment_options.tkick / 1e3,
                        coeff=None,
                        offset=None,
                        x0=None,
                        n0=None,
                        qubit_name=unit,
                        punish=3,
                        punish_value=5,
                    )
                    self.run_options.clear_records[unit] = {
                        "clear_freq": None,
                        "sim_params": None,
                        "opt_params": None,
                        "t2_echo": None,
                    }
        elif exp_name == "T2SpinEcho":
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    t2_echo = record.analysis_data.get(unit).result.get("tau")
                    self.run_options.clear_records[unit]["t2_echo"] = t2_echo
        elif exp_name == "CavityFreqSpectrum_0":
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    fr = record.analysis_data.get(unit).result.get("fr")
                    Ql = record.analysis_data.get(unit).result.get("Ql")
                    kappa = (fr / Ql) * 2 * np.pi
                    self.run_options.clear_opt_params[unit]["kappa"] = kappa
        elif exp_name == "ReadoutFreqCalibrate":
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    qubit = self.backend.chip_data.cache_qubit.get(unit)
                    frs = record.analysis_data.get(unit).result.get("frs")
                    chi_eff = (frs[1] - frs[0]) * np.pi
                    self.run_options.clear_opt_params[unit]["chi_eff"] = chi_eff
                    fr_list = np.linspace(frs[0], frs[1], self.experiment_options.point)
                    self.change_parallel_exec_exp_options(
                        exp_name="PhotonScanReadoutFreqV2",
                        unit=unit,
                        options={
                            "sweep_list": list(fr_list),
                            "child_exp_options.pre_amp": qubit.Mwave.amp,
                        },
                    )
                    self.change_parallel_exec_ana_options(
                        exp_name="PhotonScanReadoutFreqV2",
                        unit=unit,
                        options={"child_ana_options.effective_chi": chi_eff},
                    )
                    self.change_parallel_exec_ana_options(
                        exp_name="PhotonNumMeasVsAmp",
                        unit=unit,
                        options={
                            "kappa": self.run_options.clear_opt_params[unit]["kappa"],
                            "child_ana_options.effective_chi": chi_eff,
                        },
                    )
                    self.run_options.his_params[unit] = dict(
                        time=qubit.XYwave.time,
                        Xpi=qubit.XYwave.Xpi,
                        Xpi2=qubit.XYwave.Xpi2,
                        drive_power=qubit.drive_power,
                    )
                    qubit.XYwave.time = 100
        elif exp_name == "PhotonScanReadoutFreqV2":
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    qubit = self.backend.chip_data.cache_qubit.get(unit)
                    target_freq = record.analysis_data.get(unit).result.get(
                        "target_freq"
                    )
                    qubit.probe_freq = target_freq
                    qubit._check_lo("M")

                    self.run_options.clear_records[unit]["clear_freq"] = target_freq
        elif exp_name == "PhotonNumMeasVsAmp":
            bad_units = []
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    qubit = self.backend.chip_data.cache_qubit.get(unit)

                    qubit.XYwave.time = self.run_options.his_params[unit]["time"]
                    qubit.XYwave.Xpi = self.run_options.his_params[unit]["Xpi"]
                    qubit.XYwave.Xpi2 = self.run_options.his_params[unit]["Xpi2"]
                    qubit.drive_power = self.run_options.his_params[unit]["drive_power"]

                    for k in ["n0", "coeff", "x0", "offset"]:
                        self.run_options.clear_opt_params[unit][k] = (
                            record.analysis_data.get(unit).result.get(k)
                        )
                    try:
                        v1, v2 = generate_clear_params_opt(
                            **self.run_options.clear_opt_params[unit]
                        )
                        nmc_params = NMClearParams._get_input_data()
                        nmc_params["amp1"] = v1
                        nmc_params["amp2"] = v2
                        nmc_params["tkick"] = (
                            self.run_options.clear_opt_params[unit]["tkick"] * 1e3
                        )
                        self.run_options.clear_records[unit]["sim_params"] = {
                            "amp1": v1,
                            "amp2": v2,
                            "tkick": self.run_options.clear_opt_params[unit]["tkick"] * 1e3,
                        }
                        self.change_parallel_exec_exp_options(
                            exp_name="NMClearParamsBoth",
                            unit=unit,
                            # input_data=nmc_params,
                            options={
                                "child_exp_options.acq_pulse_params": {"Clear": nmc_params}
                            },
                        )
                        self.change_parallel_exec_ana_options(
                            exp_name="NMClearParamsBoth",
                            unit=unit,
                            options={
                                "child_ana_options.chi": self.run_options.clear_opt_params[
                                    unit
                                ]["chi_eff"],
                                "child_ana_options.kappa": self.run_options.clear_opt_params[
                                    unit
                                ]["kappa"],
                                "child_ana_options.t2echo": self.run_options.clear_records[
                                    unit
                                ]["t2_echo"],
                            },
                        )
                    except RuntimeError:
                        pyqlog.error(f"{unit} generate_clear_params_opt error!")
                        bad_units.append(unit)
            for unit in bad_units:
                record.pass_units.remove(unit)
                record.bad_units.append(unit)
        elif exp_name == "NMClearParamsBoth":
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    self.run_options.clear_records[unit]["opt_params"] = {
                        "amp1": record.analysis_data.get(unit).result.get("sim")[0],
                        "amp2": record.analysis_data.get(unit).result.get("sim")[1],
                        "tkick": self.run_options.clear_opt_params[unit]["tkick"] * 1e3,
                    }

                    self.run_options.clear_records[unit].update(
                        self.run_options.clear_opt_params[unit].to_dict()
                    )

        return record

    def _run_batch(self):
        physical_units = []
        for unit in self.experiment_options.physical_units:
            qubit = self.backend.chip_data.cache_qubit.get(unit)
            amp = qubit.Mwave.amp
            qubit.Mwave.amp = self.experiment_options.high_m_amp
            qubit.probe_freq -= self.experiment_options.bare_cav_f_shift
            pass_units = self._run_flow(
                flows=self.experiment_options.wr_flow,
                physical_units=[unit],
                name=f"High Power CS {unit}",
            )
            physical_units.extend(pass_units)
            qubit.Mwave.amp = amp
            qubit.probe_freq += self.experiment_options.bare_cav_f_shift

        all_pass_units = []
        group_map = self.parallel_allocator_for_qc(physical_units)
        for name, group in group_map.items():
            pass_units = self._run_flow(
                flows=self.experiment_options.clear_flow,
                physical_units=group,
                name=f"Clear Calibration {name}",
            )
            all_pass_units.extend(pass_units)
        self._save_data_to_json(self.run_options.clear_records, "clear_records")
        self.save_clear_data(self.run_options.clear_records, all_pass_units)
        self.bind_pass_units(all_pass_units)

    def save_clear_data(self, data, pass_units=None):
        if isinstance(data, dict):
            pass
        else:
            with open(data, mode='r', encoding='utf-8') as fp:
                data = json.load(fp)

        if pass_units is None:
            pass_units = list(data.keys())

        for unit in pass_units:
            qubit = self.backend.chip_data.cache_qubit.get(unit)
            qubit.probe_freq = data[unit]["clear_freq"]
            qubit.Mwave.name = "AcquireClear"
            qubit.Mwave.amp1 = data[unit]["opt_params"]["amp1"]
            qubit.Mwave.amp2 = data[unit]["opt_params"]["amp2"]
            qubit.Mwave.tkick = data[unit]["opt_params"]["tkick"]
            qubit.save_data()
