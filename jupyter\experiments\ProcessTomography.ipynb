{"cells": [{"cell_type": "markdown", "id": "c7952dca", "metadata": {}, "source": ["# ProcessTomography\n", "\n", "单比特量子过程层析保定单门保真度\n", "\n", "## 初始化实验环境"]}, {"cell_type": "code", "execution_count": 1, "id": "79034090", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\n"]}], "source": ["# 若用户未安装 pyqcat 包（monster），通过本地源码测试，需要执行下面操作\n", "# 若安装了 pyqcat 包（monster），跳过此步骤\n", "\n", "import os\n", "import sys\n", "\n", "\n", "package_root = \"../../../pyqcat-monster\"\n", "\n", "print(os.path.abspath(package_root))\n", "\n", "sys.path.insert(0, package_root)"]}, {"cell_type": "code", "execution_count": 2, "id": "735bec4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:12:38\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1m\n", "|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |        tcp://************:8088         |\n", "|    point_label     |              person_point              |\n", "|       sample       |               test_chip                |\n", "|      env_name      |                D00_1011                |\n", "|      version       |                 0.1.6                  |\n", "|=============================================================|\n", "\u001b[0m\n"]}, {"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': 'f3802623bba86d6a9cee4513ba9e688d'},\n", " 'msg': 'success'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "\n", "from pyQCat.tools import qarange\n", "from pyQCat.context import ExperimentContext\n", "from pyQCat.invoker import Invoker\n", "from pyQCat.parameters import get_parameters\n", "\n", "\n", "%matplotlib inline\n", "pd.set_option('max_colwidth', None)\n", "\n", "conf_file = r'E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf'\n", "\n", "username = \"test_04\"\n", "password = \"123456\"\n", "\n", "context = ExperimentContext(conf_file)\n", "Invoker.verify_account(username=username, password=password)"]}, {"cell_type": "markdown", "id": "5b81a12e", "metadata": {}, "source": ["## 配置实验参数"]}, {"cell_type": "code", "execution_count": 3, "id": "801b29dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:12:46\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1mQubit_(bit=0) is already exist in current environment.\u001b[0m\n"]}], "source": ["# context 加载实验参数\n", "\n", "q_name_list = [\"q0\"]\n", "environment_elements = [\"q0\", \"q1\", \"q2\", \"q3\", \"c0\", \"c1\", \"c2\"]\n", "\n", "context.configure_inst()\n", "context.configure_qubits(q_name_list)\n", "context.minimize_compensate()\n", "# context.maximize_compensate()\n", "context.configure_environment(environment_elements)\n", "context.configure_crosstalk_dict()\n", "context.configure_dcm(q_name_list)\n", "\n", "qubit = context.qubit"]}, {"cell_type": "code", "execution_count": 4, "id": "e5c90d35", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'1': 0.744732,\n", " '2': 0.236088,\n", " '3': 0.203233,\n", " '4': 0.525287,\n", " '5': 0.0,\n", " '6': 0.0,\n", " '7': 0.0}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看DC\n", "\n", "context.working_dc"]}, {"cell_type": "code", "execution_count": 4, "id": "ef41ff3e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>parameter</th>\n", "      <th>object</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>config</td>\n", "      <td>D:\\project\\SupQAutomation\\code\\develop\\pyqcat-monster\\conf\\config.conf</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>qubits</td>\n", "      <td>[Q0]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>couplers</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>discriminators</td>\n", "      <td>K=2 F0=0.9417 F1=0.7042 AVE=0.8229500000000001 OL=0.0092</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>compensates</td>\n", "      <td>[PulseCorrectionQ0]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>working_dc</td>\n", "      <td>True</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>crosstalk_dict</td>\n", "      <td>True</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        parameter  \\\n", "0          config   \n", "1          qubits   \n", "2        couplers   \n", "3  discriminators   \n", "4     compensates   \n", "5      working_dc   \n", "6  crosstalk_dict   \n", "\n", "                                                                   object  \\\n", "0  D:\\project\\SupQAutomation\\code\\develop\\pyqcat-monster\\conf\\config.conf   \n", "1                                                                    [Q0]   \n", "2                                                                      []   \n", "3                K=2 F0=0.9417 F1=0.7042 AVE=0.8229500000000001 OL=0.0092   \n", "4                                                     [PulseCorrectionQ0]   \n", "5                                                                    True   \n", "6                                                                    True   \n", "\n", "  count  \n", "0        \n", "1     1  \n", "2     0  \n", "3     2  \n", "4     1  \n", "5        \n", "6        "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看配置信息\n", "\n", "pd.DataFrame(context.context_table())"]}, {"cell_type": "markdown", "id": "2773f2d8", "metadata": {}, "source": ["## 创建实验\n", "\n", "### I 门保真度标定"]}, {"cell_type": "code", "execution_count": 5, "id": "fb3f6c00", "metadata": {}, "outputs": [], "source": ["qpt_exp = ProcessTomography2.from_experiment_context(context)"]}, {"cell_type": "code", "execution_count": 6, "id": "e4b5429b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>experiment option</td>\n", "      <td>show_result</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_data_path</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>experiment option</td>\n", "      <td>simulator</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>experiment option</td>\n", "      <td>qt_name</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>experiment option</td>\n", "      <td>qc_name</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>experiment option</td>\n", "      <td>qubit_nums</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>experiment option</td>\n", "      <td>is_amend</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>experiment option</td>\n", "      <td>base_gates</td>\n", "      <td>[I, X/2, Y/2, -X/2]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>experiment option</td>\n", "      <td>gate_map</td>\n", "      <td>{'I': I, 'X': R_φ0.0°_θ180.0°, matrix=[[6.123234e-17+0.j 0.000000e+00-1.j]\n", " [0.000000e+00-1.j 6.123234e-17+0.j]]), 'Y': R_φ90.0°_θ180.0°, matrix=[[ 6.123234e-17+0.000000e+00j -1.000000e+00-6.123234e-17j]\n", " [ 1.000000e+00-6.123234e-17j  6.123234e-17+0.000000e+00j]]), 'X/2': R_φ0.0°_θ90.0°, matrix=[[0.70710678+0.j         0.        -0.70710678j]\n", " [0.        -0.70710678j 0.70710678+0.j        ]]), '-X/2': R_φ180.0°_θ90.0°, matrix=[[ 7.07106781e-01+0.j         -8.65956056e-17+0.70710678j]\n", " [ 8.65956056e-17+0.70710678j  7.07106781e-01+0.j        ]]), 'Y/2': R_φ90.0°_θ90.0°, matrix=[[ 0.70710678+0.00000000e+00j -0.70710678-4.32978028e-17j]\n", " [ 0.70710678-4.32978028e-17j  0.70710678+0.00000000e+00j]]), '-Y/2': R_φ-90.0°_θ90.0°, matrix=[[ 0.70710678+0.00000000e+00j  0.70710678-4.32978028e-17j]\n", " [-0.70710678-4.32978028e-17j  0.70710678+0.00000000e+00j]]), '-X': R_φ180.0°_θ180.0°, matrix=[[ 6.1232340e-17+0.j -1.2246468e-16+1.j]\n", " [ 1.2246468e-16+1.j  6.1232340e-17+0.j]]), '-Y': R_φ-90.0°_θ180.0°, matrix=[[ 6.123234e-17+0.000000e+00j  1.000000e+00-6.123234e-17j]\n", " [-1.000000e+00-6.123234e-17j  6.123234e-17+0.000000e+00j]])}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>experiment option</td>\n", "      <td>goal_gate</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>analysis option</td>\n", "      <td>is_plot</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>analysis option</td>\n", "      <td>use_mle</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>analysis option</td>\n", "      <td>sigma_basis</td>\n", "      <td>[[[(1+0j), 0j], [0j, (1+0j)]], [[0j, (1+0j)], [(1+0j), 0j]], [[0j, (-0-1j)], [1j, 0j]], [[(1+0j), 0j], [0j, (-1+0j)]]]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>analysis option</td>\n", "      <td>goal_gate_matrix</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>analysis option</td>\n", "      <td>base_ops</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>analysis option</td>\n", "      <td>qst_base_ops</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>analysis option</td>\n", "      <td>labels</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>analysis option</td>\n", "      <td>fidelity_accuracy</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 type                 name  \\\n", "0   experiment option          show_result   \n", "1   experiment option  simulator_data_path   \n", "2   experiment option            simulator   \n", "3   experiment option              qt_name   \n", "4   experiment option              qc_name   \n", "5   experiment option           qubit_nums   \n", "6   experiment option             is_amend   \n", "7   experiment option           base_gates   \n", "8   experiment option             gate_map   \n", "9   experiment option            goal_gate   \n", "10    analysis option              is_plot   \n", "11    analysis option              use_mle   \n", "12    analysis option          sigma_basis   \n", "13    analysis option     goal_gate_matrix   \n", "14    analysis option             base_ops   \n", "15    analysis option         qst_base_ops   \n", "16    analysis option               labels   \n", "17    analysis option    fidelity_accuracy   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     value  \n", "0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    False  \n", "1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     None  \n", "2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    False  \n", "3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     None  \n", "4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     None  \n", "5                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        2  \n", "6                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     True  \n", "7                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      [I, X/2, Y/2, -X/2]  \n", "8   {'I': I, 'X': R_φ0.0°_θ180.0°, matrix=[[6.123234e-17+0.j 0.000000e+00-1.j]\n", " [0.000000e+00-1.j 6.123234e-17+0.j]]), 'Y': R_φ90.0°_θ180.0°, matrix=[[ 6.123234e-17+0.000000e+00j -1.000000e+00-6.123234e-17j]\n", " [ 1.000000e+00-6.123234e-17j  6.123234e-17+0.000000e+00j]]), 'X/2': R_φ0.0°_θ90.0°, matrix=[[0.70710678+0.j         0.        -0.70710678j]\n", " [0.        -0.70710678j 0.70710678+0.j        ]]), '-X/2': R_φ180.0°_θ90.0°, matrix=[[ 7.07106781e-01+0.j         -8.65956056e-17+0.70710678j]\n", " [ 8.65956056e-17+0.70710678j  7.07106781e-01+0.j        ]]), 'Y/2': R_φ90.0°_θ90.0°, matrix=[[ 0.70710678+0.00000000e+00j -0.70710678-4.32978028e-17j]\n", " [ 0.70710678-4.32978028e-17j  0.70710678+0.00000000e+00j]]), '-Y/2': R_φ-90.0°_θ90.0°, matrix=[[ 0.70710678+0.00000000e+00j  0.70710678-4.32978028e-17j]\n", " [-0.70710678-4.32978028e-17j  0.70710678+0.00000000e+00j]]), '-X': R_φ180.0°_θ180.0°, matrix=[[ 6.1232340e-17+0.j -1.2246468e-16+1.j]\n", " [ 1.2246468e-16+1.j  6.1232340e-17+0.j]]), '-Y': R_φ-90.0°_θ180.0°, matrix=[[ 6.123234e-17+0.000000e+00j  1.000000e+00-6.123234e-17j]\n", " [-1.000000e+00-6.123234e-17j  6.123234e-17+0.000000e+00j]])}  \n", "9                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     None  \n", "10                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    True  \n", "11                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    True  \n", "12                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  [[[(1+0j), 0j], [0j, (1+0j)]], [[0j, (1+0j)], [(1+0j), 0j]], [[0j, (-0-1j)], [1j, 0j]], [[(1+0j), 0j], [0j, (-1+0j)]]]  \n", "13                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    None  \n", "14                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    None  \n", "15                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    None  \n", "16                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    None  \n", "17                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       3  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(qpt_exp.options_table())"]}, {"cell_type": "code", "execution_count": 7, "id": "09c86c6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-09-09 16:53:29\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mStateTomography2 register success, id 631aff0925142b2d912f0e73\u001b[0m\n", "\u001b[33m2022-09-09 16:53:29\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\fivago\\ProcessTomography2\\q0\\2022-09-09\\16.53.29\\StateTomography2\\16-53-29-['I']\\\u001b[0m\n"]}, {"data": {"application/json": {"ascii": false, "bar_format": null, "colour": null, "elapsed": 0.015006542205810547, "initial": 0, "n": 0, "ncols": null, "nrows": 29, "postfix": null, "prefix": "", "rate": null, "total": 3, "unit": "it", "unit_divisor": 1000, "unit_scale": false}, "application/vnd.jupyter.widget-view+json": {"model_id": "e1bfd13189504843b0c1dc35cca00957", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: 3.058181\n", "         Iterations: 66\n", "         Function evaluations: 118\n", "\u001b[33m2022-09-09 16:53:30\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mStateTomography2 register success, id 631aff0a25142b2d912f0e79\u001b[0m\n", "\u001b[33m2022-09-09 16:53:30\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\fivago\\ProcessTomography2\\q0\\2022-09-09\\16.53.29\\StateTomography2\\16-53-30-['X-2']\\\u001b[0m\n"]}, {"data": {"application/json": {"ascii": false, "bar_format": null, "colour": null, "elapsed": 0.015957355499267578, "initial": 0, "n": 0, "ncols": null, "nrows": 29, "postfix": null, "prefix": "", "rate": null, "total": 3, "unit": "it", "unit_divisor": 1000, "unit_scale": false}, "application/vnd.jupyter.widget-view+json": {"model_id": "40e46f24ef0a4308997d8911aa9e8408", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: 3.878746\n", "         Iterations: 41\n", "         Function evaluations: 77\n", "\u001b[33m2022-09-09 16:53:31\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mStateTomography2 register success, id 631aff0b25142b2d912f0e7f\u001b[0m\n", "\u001b[33m2022-09-09 16:53:31\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\fivago\\ProcessTomography2\\q0\\2022-09-09\\16.53.29\\StateTomography2\\16-53-31-['Y-2']\\\u001b[0m\n"]}, {"data": {"application/json": {"ascii": false, "bar_format": null, "colour": null, "elapsed": 0.016954421997070312, "initial": 0, "n": 0, "ncols": null, "nrows": 29, "postfix": null, "prefix": "", "rate": null, "total": 3, "unit": "it", "unit_divisor": 1000, "unit_scale": false}, "application/vnd.jupyter.widget-view+json": {"model_id": "fdf5ef06755440e0a704534e584a571b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: 3.877245\n", "         Iterations: 34\n", "         Function evaluations: 68\n", "\u001b[33m2022-09-09 16:53:32\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mStateTomography2 register success, id 631aff0c25142b2d912f0e85\u001b[0m\n", "\u001b[33m2022-09-09 16:53:32\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\fivago\\ProcessTomography2\\q0\\2022-09-09\\16.53.29\\StateTomography2\\16-53-32-['-X-2']\\\u001b[0m\n"]}, {"data": {"application/json": {"ascii": false, "bar_format": null, "colour": null, "elapsed": 0.014960050582885742, "initial": 0, "n": 0, "ncols": null, "nrows": 29, "postfix": null, "prefix": "", "rate": null, "total": 3, "unit": "it", "unit_divisor": 1000, "unit_scale": false}, "application/vnd.jupyter.widget-view+json": {"model_id": "f80f40d987b44b269c5cd5b55c8d2139", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: 2.961267\n", "         Iterations: 87\n", "         Function evaluations: 155\n", "\u001b[33m2022-09-09 16:53:33\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mStateTomography2 register success, id 631aff0d25142b2d912f0e8b\u001b[0m\n", "\u001b[33m2022-09-09 16:53:33\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\fivago\\ProcessTomography2\\q0\\2022-09-09\\16.53.29\\StateTomography2\\16-53-32-['I', 'I']\\\u001b[0m\n"]}, {"data": {"application/json": {"ascii": false, "bar_format": null, "colour": null, "elapsed": 0.014959335327148438, "initial": 0, "n": 0, "ncols": null, "nrows": 29, "postfix": null, "prefix": "", "rate": null, "total": 3, "unit": "it", "unit_divisor": 1000, "unit_scale": false}, "application/vnd.jupyter.widget-view+json": {"model_id": "d8a8f11e8995465ab20ed93cf95be7d4", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: 2.886476\n", "         Iterations: 87\n", "         Function evaluations: 147\n", "\u001b[33m2022-09-09 16:53:33\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mStateTomography2 register success, id 631aff0d25142b2d912f0e91\u001b[0m\n", "\u001b[33m2022-09-09 16:53:33\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\fivago\\ProcessTomography2\\q0\\2022-09-09\\16.53.29\\StateTomography2\\16-53-33-['X-2', 'I']\\\u001b[0m\n"]}, {"data": {"application/json": {"ascii": false, "bar_format": null, "colour": null, "elapsed": 0.01798224449157715, "initial": 0, "n": 0, "ncols": null, "nrows": 29, "postfix": null, "prefix": "", "rate": null, "total": 3, "unit": "it", "unit_divisor": 1000, "unit_scale": false}, "application/vnd.jupyter.widget-view+json": {"model_id": "cabc16950a3d48589d5bde290eedba5c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: 3.894605\n", "         Iterations: 42\n", "         Function evaluations: 82\n", "\u001b[33m2022-09-09 16:53:34\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mStateTomography2 register success, id 631aff0e25142b2d912f0e97\u001b[0m\n", "\u001b[33m2022-09-09 16:53:34\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\fivago\\ProcessTomography2\\q0\\2022-09-09\\16.53.29\\StateTomography2\\16-53-34-['Y-2', 'I']\\\u001b[0m\n"]}, {"data": {"application/json": {"ascii": false, "bar_format": null, "colour": null, "elapsed": 0.016953468322753906, "initial": 0, "n": 0, "ncols": null, "nrows": 29, "postfix": null, "prefix": "", "rate": null, "total": 3, "unit": "it", "unit_divisor": 1000, "unit_scale": false}, "application/vnd.jupyter.widget-view+json": {"model_id": "52222f4240d54a7d91defeabdece87fc", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: 3.742149\n", "         Iterations: 49\n", "         Function evaluations: 89\n", "\u001b[33m2022-09-09 16:53:35\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mStateTomography2 register success, id 631aff0f25142b2d912f0e9d\u001b[0m\n", "\u001b[33m2022-09-09 16:53:35\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\fivago\\ProcessTomography2\\q0\\2022-09-09\\16.53.29\\StateTomography2\\16-53-35-['-X-2', 'I']\\\u001b[0m\n"]}, {"data": {"application/json": {"ascii": false, "bar_format": null, "colour": null, "elapsed": 0.015956401824951172, "initial": 0, "n": 0, "ncols": null, "nrows": 29, "postfix": null, "prefix": "", "rate": null, "total": 3, "unit": "it", "unit_divisor": 1000, "unit_scale": false}, "application/vnd.jupyter.widget-view+json": {"model_id": "c14d064567c043388f06d8043e574844", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: 2.961848\n", "         Iterations: 90\n", "         Function evaluations: 158\n"]}], "source": ["    simulator_data_path = [\n", "        '../../test/data/ProcessTomography/StateTomography(p0_p1)-(I).dat',\n", "        '../../test/data/ProcessTomography/StateTomography(p0_p1)-(X2).dat',\n", "        '../../test/data/ProcessTomography/StateTomography(p0_p1)-(Y2).dat',\n", "        '../../test/data/ProcessTomography/StateTomography(p0_p1)-(-X2).dat',\n", "        '../../test/data/ProcessTomography/StateTomography(p0_p1)-(I I).dat',\n", "        '../../test/data/ProcessTomography/StateTomography(p0_p1)-(X2 I).dat',\n", "        '../../test/data/ProcessTomography/StateTomography(p0_p1)-(Y2 I).dat',\n", "        '../../test/data/ProcessTomography/StateTomography(p0_p1)-(-X2 I).dat',\n", "    ]\n", "\n", "    qpt_exp.set_experiment_options(\n", "        goal_gate='I',\n", "        qubit_nums=1,\n", "        simulator_data_path=simulator_data_path\n", "    )\n", "    qpt_exp.set_analysis_options(use_mle=True)\n", "\n", "    qpt_exp.run()"]}, {"cell_type": "code", "execution_count": 8, "id": "61473924", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "      <th>unit</th>\n", "      <th>extra</th>\n", "      <th>quality</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>exp_chi_matrix</td>\n", "      <td>[[(0.9844882437842929+0j), (0.004328688999525831+0.0022740400177561674j), (0.0013366471844196985-0.035584199977598555j), (0.0052407067057397986-0.002056831176257904j)], [(0.004328688999525831-0.0022740400177561674j), (0.004959050129394279+0j), (-0.0008154912988763681-0.005234986354658051j), (-0.004169150615707827+0.001336569179914568j)], [(0.0013366471844196985+0.035584199977598555j), (-0.0008154912988763681+0.005234986354658051j), (0.00661884464862459+0j), (-0.0006877302995332898-0.004323552201851427j)], [(0.0052407067057397986+0.002056831176257904j), (-0.004169150615707827-0.001336569179914568j), (-0.0006877302995332898+0.004323552201851427j), (0.003959043439803628+0j)]]</td>\n", "      <td>None</td>\n", "      <td>{}</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ideal_chi_matrix</td>\n", "      <td>[[(1.0000000000000004+8.896076791452605e-17j), (-3.691512718008711e-16-1.6987262336050975e-16j), (-1.7034584494356492e-16+1.9313783985913177e-16j), (2.683038976177461e-16+2.5110201167889545e-16j)], [(3.5168909705432314e-16+9.364043526578366e-17j), (-1.2601442290656783e-16+2.774675023637665e-17j), (2.868624899295446e-16-6.81859201523387e-16j), (-3.6995586895202886e-16-4.135709452965771e-17j)], [(-1.2805663994388442e-16+3.6277826960460415e-16j), (2.66414084600553e-16-5.39386125564285e-16j), (-1.2601442290656744e-16-4.819515556536797e-17j), (1.8033998068001015e-16-4.698889988642635e-16j)], [(-5.088522196198632e-16-3.1816568570081174e-16j), (1.5820466489607833e-16+1.416984842921471e-16j), (1.9851998401192817e-16-2.7870694560655994e-16j), (1.5728159515523054e-16-2.1897093892609826e-17j)]]</td>\n", "      <td>None</td>\n", "      <td>{}</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>fidelity</td>\n", "      <td>99.221</td>\n", "      <td>%</td>\n", "      <td>{}</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>process_fidelity</td>\n", "      <td>98.446</td>\n", "      <td>%</td>\n", "      <td>{}</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               name  \\\n", "0    exp_chi_matrix   \n", "1  ideal_chi_matrix   \n", "2          fidelity   \n", "3  process_fidelity   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        value  \\\n", "0                                                                                                                   [[(0.9844882437842929+0j), (0.004328688999525831+0.0022740400177561674j), (0.0013366471844196985-0.035584199977598555j), (0.0052407067057397986-0.002056831176257904j)], [(0.004328688999525831-0.0022740400177561674j), (0.004959050129394279+0j), (-0.0008154912988763681-0.005234986354658051j), (-0.004169150615707827+0.001336569179914568j)], [(0.0013366471844196985+0.035584199977598555j), (-0.0008154912988763681+0.005234986354658051j), (0.00661884464862459+0j), (-0.0006877302995332898-0.004323552201851427j)], [(0.0052407067057397986+0.002056831176257904j), (-0.004169150615707827-0.001336569179914568j), (-0.0006877302995332898+0.004323552201851427j), (0.003959043439803628+0j)]]   \n", "1  [[(1.0000000000000004+8.896076791452605e-17j), (-3.691512718008711e-16-1.6987262336050975e-16j), (-1.7034584494356492e-16+1.9313783985913177e-16j), (2.683038976177461e-16+2.5110201167889545e-16j)], [(3.5168909705432314e-16+9.364043526578366e-17j), (-1.2601442290656783e-16+2.774675023637665e-17j), (2.868624899295446e-16-6.81859201523387e-16j), (-3.6995586895202886e-16-4.135709452965771e-17j)], [(-1.2805663994388442e-16+3.6277826960460415e-16j), (2.66414084600553e-16-5.39386125564285e-16j), (-1.2601442290656744e-16-4.819515556536797e-17j), (1.8033998068001015e-16-4.698889988642635e-16j)], [(-5.088522196198632e-16-3.1816568570081174e-16j), (1.5820466489607833e-16+1.416984842921471e-16j), (1.9851998401192817e-16-2.7870694560655994e-16j), (1.5728159515523054e-16-2.1897093892609826e-17j)]]   \n", "2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      99.221   \n", "3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      98.446   \n", "\n", "   unit extra quality  \n", "0  None    {}    None  \n", "1  None    {}    None  \n", "2     %    {}    None  \n", "3     %    {}    None  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(qpt_exp.analysis.show_results())"]}, {"cell_type": "code", "execution_count": 11, "id": "eadc4a0c", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1400x700 with 8 Axes>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["qpt_exp.analysis.drawer.figure"]}, {"cell_type": "code", "execution_count": null, "id": "8d443924", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}