# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:2
msgid "pyQCat.experiments.single.RabiScanAmp"
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp:1
msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp:1
msgid "Rabi experiment scan XY pulse amplitude."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.__init__:1
msgid "Create a new experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.single.RabiScanAmp.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`acquire_pulse "
"<pyQCat.experiments.single.RabiScanAmp.acquire_pulse>`\\ "
"\\(acq\\_pulse\\[\\, qubit\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Set measure pulse."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`cal_fidelity "
"<pyQCat.experiments.single.RabiScanAmp.cal_fidelity>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Calculate the fidelity matrix."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`experiment_info "
"<pyQCat.experiments.single.RabiScanAmp.experiment_info>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Show experiment informations."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.single.RabiScanAmp.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.single.RabiScanAmp.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`get_xy_pulse "
"<pyQCat.experiments.single.RabiScanAmp.get_xy_pulse>`\\ \\(qubit\\, "
"type\\_\\, sweep\\_list\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`jupyter_schedule "
"<pyQCat.experiments.single.RabiScanAmp.jupyter_schedule>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Show experiment pulses's schedules in Jupyter Notebook."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.single.RabiScanAmp.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`play_pulse <pyQCat.experiments.single.RabiScanAmp.play_pulse>`\\"
" \\(name\\, base\\_qubit\\, pulse\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Play pulse for XY control and Z flux control."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`plot_schedule "
"<pyQCat.experiments.single.RabiScanAmp.plot_schedule>`\\ "
"\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
"Plot the pulse schedule which is very important to confirm experiment "
"logical correction."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.single.RabiScanAmp.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
#: of pyQCat.experiments.single.rabi.RabiScanAmp.run:1
msgid "Run RabiScanAmp experiment and perform analysis."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.single.RabiScanAmp.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.single.RabiScanAmp.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_multiple_IF "
"<pyQCat.experiments.single.RabiScanAmp.set_multiple_IF>`\\ \\(\\*IF\\[\\,"
" channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Used for multiple qubits experiment, set mutiple IF."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_multiple_index "
"<pyQCat.experiments.single.RabiScanAmp.set_multiple_index>`\\ "
"\\(\\*args\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
"Used for multiple qubits experiment, set mutiple index. :type args: "
":py:class:`list` :param args: Setting the start and end values :type "
"channel: :py:class:`int` :param channel: channel settings Usage::     "
">>>experiment = TopExperiment()     >>>experiment.set_multiple_index([0, "
"1500], [100, 900], channel=1)."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.single.RabiScanAmp.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.single.RabiScanAmp.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_sweep_order "
"<pyQCat.experiments.single.RabiScanAmp.set_sweep_order>`\\ "
"\\(order\\_list\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid "Old interface."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:40:<autosummary>:1
msgid ""
":py:obj:`sweep_readout_trigger_delay "
"<pyQCat.experiments.single.RabiScanAmp.sweep_readout_trigger_delay>`\\ "
"\\(channel\\, sweep\\_delay\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.RabiScanAmp.rst:42
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.single.RabiScanAmp.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.single.RabiScanAmp.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.single.RabiScanAmp.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.single.RabiScanAmp.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:10
msgid "Experiment options:"
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:5
msgid "name (str): Indicates which type of pulse is selected"
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:5
msgid "to perform the experimentOnly 'Xpi' and 'Xpi/2' supported."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:7
msgid "amps (list, np.ndarray): The list of XY pulse amplitudes that"
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:8
msgid "will be scanned in the experiment."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:9
msgid "drive_power (float): The driver power of qubit."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_analysis_options
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options
#: pyQCat.experiments.single.rabi.RabiScanAmp._metadata
msgid "Return type"
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_analysis_options:4
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:12
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_analysis_options:1
msgid "Default kwarg options for analysis."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._set_xy_pulses:1
msgid "Set RabiScanAmp experiment XY pulses."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._metadata:1
msgid "Set RabiScanAmp experiment metadata."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._update_instrument:1
msgid "Update drive power if setting."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._check_options:1
msgid "Check Options."
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.single.RabiScanAmp.__init__>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`acquire_pulse "
#~ "<pyQCat.experiments.single.RabiScanAmp.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.single.RabiScanAmp.from_experiment_context>`\\ "
#~ "\\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.single.RabiScanAmp.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_xy_pulse "
#~ "<pyQCat.experiments.single.RabiScanAmp.get_xy_pulse>`\\ "
#~ "\\(qubit\\, type\\_\\, sweep\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`play_pulse "
#~ "<pyQCat.experiments.single.RabiScanAmp.play_pulse>`\\ \\(name\\,"
#~ " base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot_schedule "
#~ "<pyQCat.experiments.single.RabiScanAmp.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`run <pyQCat.experiments.single.RabiScanAmp.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_IF "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_index "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_sweep_order "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":py:obj:`analysis <pyQCat.experiments.single.RabiScanAmp.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.single.RabiScanAmp.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.single.RabiScanAmp.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.single.RabiScanAmp.run_options>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.single.RabiScanAmp.__init__>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`acquire_pulse "
#~ "<pyQCat.experiments.single.RabiScanAmp.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_info "
#~ "<pyQCat.experiments.single.RabiScanAmp.experiment_info>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.single.RabiScanAmp.from_experiment_context>`\\ "
#~ "\\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.single.RabiScanAmp.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_xy_pulse "
#~ "<pyQCat.experiments.single.RabiScanAmp.get_xy_pulse>`\\ "
#~ "\\(qubit\\, type\\_\\, sweep\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`jupyter_schedule "
#~ "<pyQCat.experiments.single.RabiScanAmp.jupyter_schedule>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.single.RabiScanAmp.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`play_pulse "
#~ "<pyQCat.experiments.single.RabiScanAmp.play_pulse>`\\ \\(name\\,"
#~ " base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot_schedule "
#~ "<pyQCat.experiments.single.RabiScanAmp.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`run <pyQCat.experiments.single.RabiScanAmp.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_IF "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_index "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_sweep_order "
#~ "<pyQCat.experiments.single.RabiScanAmp.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.single.RabiScanAmp.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.single.RabiScanAmp.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.single.RabiScanAmp.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.single.RabiScanAmp.run_options>`\\"
#~ msgstr ""

