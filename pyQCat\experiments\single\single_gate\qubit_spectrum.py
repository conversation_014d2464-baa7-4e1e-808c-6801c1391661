# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/13
# __author:       <PERSON> <PERSON>

"""
QubitSpectrum experiment.
QubitSpectrum is used to find qubit drive frequency.
"""

from copy import deepcopy
from typing import Dict, List, Optional, Union

import numpy as np

from ....analysis.library import QubitSpectrumAnalysis, QubitSpectrumPreAnalysis
from ....errors import ExperimentFieldError, ExperimentOptionsError
from ....log import pyqlog
from ....pulse.pulse_function import zero_pulse
from ....pulse.pulse_lib import (
    Constant,
    GaussianSquare,
    PulseComponent,
    SquareEnvelop,
    VarFreqEnvelop,
)
from ....qubit import Qubit
from ....structures import MetaData, Options
from ....tools import qarange
from ....types import StandardContext
from ...coupler_experiment_v1 import CouplerBaseExperimentV1 as CouplerBaseExperiment
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class QubitSpectrum(TopExperiment):
    """QubitSpectrum experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            freq_list (List): Scan frequency list.
            drive_power (float): Set driver power value.
            z_amp (float): Set Z line pulse amp.
            use_square (bool): XY line pulse model,
                               True use square, False use chirp.
            band_width (float): When use_square is False,
                                set chirp pulse band widths.
            fine_flag (bool): Fine scan or not, select analysis not same.

        Rough scan analysis options:
            rough_window_length (int): Set window_length for rough scan.
            rough_freq_distance (float): Set freq_distance for rough scan.

        Fine scan analysis options:
            fine_window_length (int): Set window_length for fine scan.
            fine_freq_distance (float): Set freq_distance for fine scan.

        """
        options = super()._default_experiment_options()

        options.set_validator("freq_list", list, limit_null=True)
        options.set_validator("drive_power", (-40, -10, 1))
        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("use_square", bool)
        options.set_validator("band_width", float)
        options.set_validator("fine_flag", bool)
        options.set_validator("smooth_params", dict)
        options.set_validator("xpulse_params", dict)
        options.set_validator("zpulse_params", dict)
        options.set_validator("scope", dict)
        options.set_validator("f02_scope", dict)
        options.set_validator("mode", ["BF", "IF"])
        options.set_validator("f_type", ["f01", "f02"])

        # visage can directly edit in xpulse_params
        # options.set_validator('xpulse_amp', float)

        options.freq_list = None
        options.drive_power = None
        options.z_amp = None
        options.use_square = True
        options.band_width = 50.0
        options.xpulse_amp = 1.0
        options.fine_flag = False

        # for analysis smooth tackle
        options.smooth_params = {
            "rough_window_length": 7,
            "rough_freq_distance": 70.0,
            "fine_window_length": 11,
            "fine_freq_distance": 80.0,
        }

        # xy pulse parameters
        options.xpulse_params = {
            "time": 5000,
            "offset": 15,
            "amp": options.xpulse_amp,
            "detune": 0,
            "freq": 466.667,
        }

        # z pulse parameters
        options.zpulse_params = {
            "time": 5100,
            "amp": 0.0,
            "sigma": 5.0,
            "fast_m": False,
        }

        # auto scope
        options.scope = {"l": 400, "r": 200, "s": 5}
        options.f02_scope = {"l": 150, "r": -50, "s": 2}

        # IF or BF test mode
        options.mode = "IF"

        # f01 or f02 test mode
        options.f_type = "f01"

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("snr_bounds", (0, 10, 1))

        options.window_length = 11
        options.freq_distance = 80

        options.snr_bounds = 1.5
        options.data_key = None
        options.quality_bounds = [0.8, 0.6, 0.5]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.bf_list = None

        options.injection_func = [
            "get_xy_pulse",
            "get_z_pulse",
        ]
        options.support_context = [
            StandardContext.QC,
            StandardContext.CPC,
        ]
        return options

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulses."""
        if self.experiment_options.mode == "IF":
            x_pulse = self.get_xy_pulse(self.qubit, self.experiment_options)
        else:
            x_pulse = []
            for freq in self.run_options.bf_list:
                qubit = deepcopy(self.qubit)
                qubit.XYwave.baseband_freq = freq
                xp = self.get_xy_pulse(qubit, self.experiment_options)
                x_pulse.append(xp)
        self.play_pulse("XY", self.qubit, x_pulse)

    @staticmethod
    def set_z_pulses(self):
        """Set z pulse."""
        z_pulse_params = self.experiment_options.zpulse_params
        z_amp = self.experiment_options.z_amp
        z_pulse = self.get_z_pulse(z_pulse_params, z_amp)
        if z_pulse is not None:
            if self.coupler:
                self.play_pulse("Z", self.coupler, z_pulse())
            else:
                self.play_pulse("Z", self.qubit, z_pulse())

    @staticmethod
    def update_instrument(self):
        freq_list = self.experiment_options.freq_list
        drive_power = self.experiment_options.drive_power

        self.inst.set_power("XY_control", self.qubit.xy_channel, drive_power)

        if self.experiment_options.mode == "IF":
            self.inst.sweep_freq(
                "XY_control",
                self.qubit.xy_channel,
                points=freq_list,
                repeat=self.experiment_options.repeat,
            )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()

        metadata.draw_meta = {
            "mode": self.experiment_options.mode,
            "drive_power": self.experiment_options.drive_power,
            "z_amp": self.experiment_options.z_amp,
            "use_square": self.experiment_options.use_square,
            "fine_flag": self.experiment_options.fine_flag,
        }

        if self.experiment_options.f_type == "f02":
            metadata.process_meta = {
                "f01": self.qubit.drive_freq if self.qubit else None
            }

        return metadata

    def _check_options(self):
        super()._check_options()
        drive_power = self.experiment_options.drive_power
        fine_flag = self.experiment_options.fine_flag
        freq_list = self.experiment_options.freq_list
        use_square = self.experiment_options.use_square

        if freq_list is None:
            drive_freq = (
                self.coupler.drive_freq
                if self.is_coupler_exp
                else self.qubit.drive_freq
            )
            if not drive_freq:
                raise ExperimentOptionsError(self, msg="not set freq list, but bit drive freq is null")

            if self.experiment_options.f_type == "f01":
                scope = self.experiment_options.scope
            else:
                scope = self.experiment_options.f02_scope

            # new feature: auto drive freq divide
            freq_list = qarange(
                round(drive_freq - scope.get("l"), 3),
                round(drive_freq + scope.get("r"), 3),
                scope.get("s"),
            )

            use_square = True
            # fine_flag = True

        if self.experiment_options.mode == "BF":
            baseband_freq = self.qubit.XYwave.baseband_freq
            drive_freq = (
                self.coupler.drive_freq
                if self.is_coupler_exp
                else self.qubit.drive_freq
            )
            bf_list = np.round(np.array(freq_list) - drive_freq + baseband_freq, 3)
            if np.min(bf_list) < 800 or np.max(bf_list) > 1300:
                raise ExperimentFieldError(
                    self.label, f"Sweep baseband freq muse limit in [800, 1300]!"
                )
            self.run_options.bf_list = bf_list.tolist()
            pyqlog.debug(f"Sweep baseband freq: {bf_list}")

        if drive_power is None:
            if self.is_coupler_exp:
                drive_power = self.coupler.drive_power
            else:
                drive_power = self.qubit.drive_power

        if fine_flag:
            window_length = self.experiment_options.smooth_params.get(
                "fine_window_length"
            )
            freq_distance = self.experiment_options.smooth_params.get(
                "fine_freq_distance"
            )
            analysis_class = QubitSpectrumAnalysis
        else:
            window_length = self.experiment_options.smooth_params.get(
                "rough_window_length"
            )
            freq_distance = self.experiment_options.smooth_params.get(
                "rough_freq_distance"
            )
            analysis_class = QubitSpectrumPreAnalysis

        data_type = "amp_phase"
        if self.is_coupler_exp or self.discriminator:
            data_type = "I_Q"

        self.set_experiment_options(
            data_type=data_type,
            drive_power=drive_power,
            freq_list=freq_list,
            use_square=use_square,
        )

        self.set_analysis_options(
            window_length=window_length, freq_distance=freq_distance
        )

        self.set_run_options(x_data=freq_list, analysis_class=analysis_class)

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "freq":
                if self.coupler and self.is_coupler_exp is False:
                    result.extra["path"] = "Coupler.probe_drive_freq"
                elif self.is_coupler_exp is True:
                    result.extra["path"] = "Coupler.drive_freq"
                else:
                    result.extra["path"] = "Qubit.drive_freq"
            elif key == "power":
                if self.coupler and self.is_coupler_exp is False:
                    result.extra["path"] = "Coupler.probe_drive_power"
                elif self.is_coupler_exp is True:
                    result.extra["path"] = "Coupler.drive_power"
                else:
                    result.extra["path"] = "Qubit.drive_power"

    @staticmethod
    def get_xy_pulse(qubit: Qubit, options: Options):
        xpulse_params = options.xpulse_params
        zpulse_params = options.zpulse_params

        xpulse_params.update({"freq": qubit.XYwave.baseband_freq})

        fine_flag = options.fine_flag
        use_square = options.use_square
        if fine_flag or use_square:
            x_pulse = SquareEnvelop(**xpulse_params)
        else:
            x_pulse = VarFreqEnvelop(**xpulse_params)
            x_pulse.band_width = options.band_width

        # bug solve: SquareEnvelop width equal time + 2 * offset, GaussianSquare width is time
        x_pulse_width = xpulse_params["time"] + 2 * xpulse_params["offset"]
        fast_m = zpulse_params.get("fast_m")
        z_pulse_width = zpulse_params["time"]
        if fast_m is True:
            z_pulse_width = zpulse_params["time"] + 6 * zpulse_params["sigma"]

        z_gap = z_pulse_width - x_pulse_width
        if z_gap < 0:
            raise ExperimentOptionsError(
                exp="QubitSpectrum",
                key="pulse_params (x/z) width",
                value=(x_pulse_width, z_pulse_width),
                msg="z pulse width must large xy pulse",
            )
        if z_gap > 0:
            l_gap = z_gap // 2
            r_gap = z_gap - l_gap
            l_zero = Constant(l_gap, 0, "XY")
            r_zero = Constant(r_gap, 0, "XY")
            x_pulse = l_zero() + x_pulse() + r_zero()
        else:
            x_pulse()

        return x_pulse

    @staticmethod
    def get_z_pulse(zpulse_params: Dict, z_amp: Optional[Union[float, int]] = None):
        z_amp = z_amp or 0.0
        zpulse_params.update({"amp": float(z_amp)})
        z_pulse = GaussianSquare(**zpulse_params)()
        return z_pulse


class CouplerSpectrum(CouplerBaseExperiment, QubitSpectrum):
    @staticmethod
    def set_xy_pulses(builder):
        """Set RabiScanWidth experiment XY pulses."""
        pulse_list = QubitSpectrum.get_xy_pulse(
            builder.driveQ, builder.experiment_options
        )
        builder.compose_xy_pulses([pulse_list])

    @staticmethod
    def set_z_pulses(builder):
        pulse_list = QubitSpectrum.get_z_pulse(
            builder.experiment_options.zpulse_params, builder.experiment_options.z_amp
        )
        builder.compose_z_pulses([pulse_list])

    @staticmethod
    def update_instrument(builder):
        freq_list = builder.experiment_options.freq_list
        drive_power = builder.experiment_options.drive_power

        builder.inst.set_power("XY_control", builder.driveQ.xy_channel, drive_power)

        builder.inst.sweep_freq(
            "XY_control",
            builder.driveQ.xy_channel,
            points=freq_list,
            repeat=builder.experiment_options.repeat,
        )


class NewCouplerSpectrum(CouplerBaseExperiment, QubitSpectrum):
    """Special Coupler Spectrum experiment, different set `probe_pi_pulse`."""

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.injection_func.append("_spec_compose_xy_pulses")

        return options

    def _spec_compose_xy_pulses(self, exp_pulse_list: Union[List, PulseComponent]):
        """Compose xy line pulses."""
        if isinstance(exp_pulse_list, PulseComponent):
            exp_pulse_list = [exp_pulse_list]

        xy_head_pulse = deepcopy(self.run_options.co.head_pulse)
        xy_head_pulse.type = "XY"

        xy_right_pulse = deepcopy(self.run_options.co.right_zero_pulse)
        xy_right_pulse.type = "XY"

        xy_tail_pulse = deepcopy(self.run_options.co.tail_pulse)
        xy_tail_pulse.type = "XY"
        # probe_pi_pulse = Constant(510, 0, 'XY')
        # probe_zero_pulse = Constant(510, 0, 'XY')
        probe_pi_pulse = zero_pulse(self.probeQ)
        probe_zero_pulse = zero_pulse(self.probeQ)

        tail_probe_pulse = (
            deepcopy(xy_right_pulse)()
            + deepcopy(probe_pi_pulse)()
            + deepcopy(xy_tail_pulse)()
        )
        tail_zero_pulse = (
            deepcopy(xy_right_pulse)()
            + deepcopy(probe_zero_pulse)()
            + deepcopy(xy_tail_pulse)()
        )

        drive_xy_pulse_list = []
        probe_xy_pulse_list = []

        for exp_pulse in exp_pulse_list:
            drive_xy_pulse = deepcopy(xy_head_pulse)()
            probe_xy_pulse = deepcopy(xy_head_pulse)()
            exp_zero_pulse = Constant(exp_pulse.width, 0, name="XY")
            exp_zero_pulse()

            drive_xy_pulse = drive_xy_pulse + exp_pulse + deepcopy(tail_zero_pulse)
            probe_xy_pulse = (
                probe_xy_pulse + exp_zero_pulse + deepcopy(tail_probe_pulse)
            )

            drive_xy_pulse_list.append(drive_xy_pulse)
            probe_xy_pulse_list.append(probe_xy_pulse)

        return probe_xy_pulse_list, drive_xy_pulse_list

    @staticmethod
    def set_xy_pulses(self):
        """Set RabiScanWidth experiment XY pulses."""
        pulse_list = self.get_xy_pulse(self.probeQ, self.experiment_options)
        probe_xy_pulse_list, drive_xy_pulse_list = self._spec_compose_xy_pulses(
            [pulse_list]
        )

        self.play_pulse("XY", self.run_options.co.probeQ, probe_xy_pulse_list)
        self.play_pulse("XY", self.run_options.co.driveQ, drive_xy_pulse_list)

    @staticmethod
    def set_z_pulses(self):
        pulse_list = self.get_z_pulse(
            self.experiment_options.zpulse_params, self.experiment_options.z_amp
        )
        self.compose_z_pulses([pulse_list])

    @staticmethod
    def update_instrument(self):
        freq_list = self.experiment_options.freq_list
        drive_power = self.experiment_options.drive_power

        self.inst.set_power("XY_control", self.driveQ.xy_channel, drive_power)

        self.inst.sweep_freq(
            "XY_control",
            self.driveQ.xy_channel,
            points=freq_list,
            repeat=self.experiment_options.repeat,
        )
