# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.calculate_lfilter_paras.rst:2
msgid "pyQCat.analysis.algorithms.calculate\\_lfilter\\_paras"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_lfilter_paras:1
msgid ""
"Calculate paras of lfilter, by pole model(complex_pole_temp) fit "
"parameters."
msgstr "计算畸变模型参数 a, b"

#: of pyQCat.analysis.algorithms.distortion.calculate_lfilter_paras
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_lfilter_paras:4
msgid "Pole model fit parameters."
msgstr "畸变极点拟合模型拟合参数"

#: of pyQCat.analysis.algorithms.distortion.calculate_lfilter_paras
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_lfilter_paras:6
msgid "Paras of lfilter."
msgstr "lfilter 模型参数 a, b 构成两元素元组 (a, b)"

#: of pyQCat.analysis.algorithms.distortion.calculate_lfilter_paras
msgid "Return type"
msgstr ""

