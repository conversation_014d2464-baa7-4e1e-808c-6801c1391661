# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2021 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/12/21
# __author:       <PERSON>

from copy import deepcopy

import numpy as np

from ....analysis.algorithms.distortion import calculate_distortion
from ....analysis.library import Dicarlo2Analysis, DicarloAnalysis
from ....parameters import get_parameters
from ....structures import Options
from ....tools.utilities import qarange, recursion_dir_all_file
from ...composite_experiment import CompositeExperiment
from ...single import DistortionAssist


class Dicarlo(CompositeExperiment):
    """ """

    _sub_experiment_class = DistortionAssist

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("repeat_times", int)
        options.set_validator("separa_num", int)
        options.set_validator("is_amend", bool)
        options.set_validator("data_stop_index", int)
        options.set_validator("repeat_loops", list)
        options.set_validator("simulator_data_path", list)

        options.repeat_times = None
        options.separa_num = 2
        options.is_amend = True
        options.data_stop_index = None
        options.repeat_loops = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("ac_spectrum_params", list)
        options.set_validator("z_amp", float)
        options.set_validator("drive_freq", float)
        options.set_validator("cali_offset", bool)
        options.set_validator("freq_switch", bool)
        options.set_validator("savgol_win_length", int)
        options.set_validator("smooth_win_length", float)

        options.ac_spectrum_params = []
        options.z_amp = None
        options.drive_freq = None
        options.cali_offset = True
        options.freq_switch = False
        options.savgol_win_length = 5
        options.smooth_win_length = 0.08

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options.
        Options:
        """
        options = super()._default_run_options()
        options.width_list = None
        options.mark_name = None
        options.dt_list = []
        options.so_list = []
        return options

    def _check_options(self):
        super()._check_options()
        self.set_analysis_options(
            ac_spectrum_params=self.analysis_options.ac_spectrum_params,
            drive_freq=self.qubits[0].drive_freq,
        )

    def _check_simulator_data(self, exp, index, **kwargs):
        simulator_data_path = recursion_dir_all_file(
            self.experiment_options.simulator_data_path
        )
        if simulator_data_path:
            exp.set_experiment_options(simulator_data_path=simulator_data_path[index])

    def _save_curve_analysis_plot(self, save_mark: str = None):
        if save_mark is None:
            save_mark = self.run_options.mark_name
        super()._save_curve_analysis_plot(save_mark=save_mark)

    def run(self):
        super().run()
        simulator_data_path = self.experiment_options.simulator_data_path
        for i, loop in enumerate(self.experiment_options.repeat_loops):
            if simulator_data_path:
                self.set_experiment_options(simulator_data_path=simulator_data_path[i])
            separation, z_amp, sample_step = loop
            self.set_analysis_options(z_amp=z_amp)
            compensate = get_parameters(
                "compensate", self.child_experiment.qubits[0].name
            )
            width_end = separation / self.experiment_options.separa_num
            if width_end <= separation / 2:
                width_end = width_end
            else:
                width_end = separation / 2
            width_list = np.array(qarange(0, width_end, sample_step))

            amp_response_list = []
            self._experiments = []
            for count in range(self.experiment_options.repeat_times):
                Xoperation_file_name = (
                    f"Phi={round(0, 4)}_Yoperation_Zamp={z_amp}_repeat_time_{count}"
                )
                Yoperation_file_name = f"Phi={round(np.pi / 2, 4)}_Xoperation_Zamp={z_amp}_repeat_time_{count}"

                self.Xoperation = deepcopy(self.child_experiment)
                self.Yoperation = deepcopy(self.child_experiment)

                self.Xoperation.set_parent_file(self, Xoperation_file_name)

                self.Yoperation.set_parent_file(self, Yoperation_file_name)

                self.Xoperation.set_experiment_options(
                    z_amp=z_amp,
                    separation=separation,
                    separa_num=self.experiment_options.separa_num,
                    is_amend=self.experiment_options.is_amend,
                    sample_step=sample_step,
                    phi=0,
                    data_type="I_Q",
                    # show_result=False,
                )
                self._check_simulator_data(self.Xoperation, 0)
                self.Xoperation.run()
                self.Xoperation.clear_params()
                self.Xoperation.analysis.provide_for_parent.update(
                    {"width_list": width_list}
                )
                self._experiments.append(self.Xoperation)
                self.Yoperation.set_experiment_options(
                    z_amp=z_amp,
                    separation=separation,
                    separa_num=self.experiment_options.separa_num,
                    is_amend=self.experiment_options.is_amend,
                    sample_step=sample_step,
                    phi=np.pi / 2,
                    data_type="I_Q",
                )
                self._check_simulator_data(self.Yoperation, 1)
                self.Yoperation.run()
                self.Yoperation.clear_params()
                self.Yoperation.analysis.provide_for_parent.update(
                    {"width_list": width_list}
                )
                self._experiments.append(self.Yoperation)
                self.set_run_options(mark_name=f"dicarlo_first_{i}")
                self._run_analysis([0, np.pi / 2] * (count + 1), DicarloAnalysis)
                self.set_run_options(mark_name=f"dicarlo_second_{i}")
                self._run_analysis([0, np.pi / 2] * (count + 1), Dicarlo2Analysis)
                self.amp_response = self.analysis.analysis_datas.amp_response_list
                self.file.save_data(
                    width_list,
                    self.amp_response,
                    name=f"{self.qubits[0].name}_amp_response",
                )
                amp_response_list.append(self.amp_response)
            amp_avg_list = np.mean(amp_response_list, axis=0)

            new_width_list = width_list[: self.experiment_options.data_stop_index]
            new_amp_response_list = amp_avg_list[
                : self.experiment_options.data_stop_index
            ]
            q_name = self.child_experiment.qubits[0].name
            if compensate.z_distortion_type == "width":
                if i == 0:
                    self.run_options.dt_list.append(new_width_list.tolist())
                    self.run_options.so_list.append(new_amp_response_list.tolist())

                else:
                    delay_arr, response_arr = calculate_distortion(
                        self.run_options.dt_list, self.run_options.so_list
                    )
                    self.run_options.dt_list[0] = delay_arr
                    self.run_options.so_list[0] = response_arr
                    self.file.save_data(
                        np.array(delay_arr),
                        np.array(response_arr),
                        name=f"{q_name}_iteration_{i}_res_amp_time",
                    )

                compensate.z_distortion_tlist = self.run_options.dt_list
                compensate.z_distortion_solist = self.run_options.so_list

            self.file.save_data(
                np.array(self.run_options.dt_list),
                np.array(self.run_options.so_list),
                name=f"{q_name}_iteration_{i}_dt_so_list",
            )
            self.file.save_data(new_amp_response_list, name=f"amp_response_{i}")
