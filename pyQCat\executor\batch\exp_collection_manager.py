# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/07
# __author:       <PERSON><PERSON><PERSON>

import json
from typing import Dict, List, Any

from pyQCat.structures import CommonDict

from .exp_collection import ExpCollection
from .types import OptionMode
from ...invoker import DataCenter
from ...log import pyqlog


class ExpCollectionManager:
    def __init__(self, exp_map: Dict[str, ExpCollection] = None):
        self.exp_map = exp_map

    def get(self, exp_name: str) -> ExpCollection:
        return self.exp_map.get(exp_name)

    @property
    def flows(self):
        return list(self.exp_map.keys())

    @classmethod
    def from_json(cls, json_path: str):
        exp_map = {}

        if isinstance(json_path, str):
            with open(json_path, encoding="utf-8") as f:
                data = json.load(f)

            for exp_name, exp_data in data.items():
                exp_map[exp_name] = ExpCollection.from_dict(exp_name, exp_data)

        return ExpCollectionManager(exp_map)

    @classmethod
    def from_db(cls, names: str = None):
        # todo: init exp map from cache data, transform std template
        db = DataCenter()

        if names:
            cache_data = db.query_exp_options(name=names)
        else:
            cache_data = db.query_exp_list()

        exp_map = {}
        if cache_data.get("code") == 200:
            for exp_collection_name, exp_collection in cache_data.get("data").items():
                for exp_data in exp_collection:
                    exp_collection = ExpCollection.from_db_data(exp_data)
                    exp_map.update(
                        {exp_collection.meta.get("exp_class_name"): exp_collection}
                    )

        return cls(exp_map=exp_map)

    def update_exp_collection_options(
        self,
        exp_name: str,
        option_type: str,
        option_mode: str = None,
        unit: str = None,
        options: Dict = None,
        **kwargs,
    ):
        """ Update option parameters in the ExpCollectionManager.

        Args:
            exp_name (str): The name of the experiment that exists in ExpCollectionManager.
            option_type (str): OptionType (experiment_options / analysis_options).
            option_mode (str): OptionMode (options_for_regular_exec / options_for_parallel_exec).
            unit (str): Physical work unit, only valid in mode OptionMode.parallel.
            options (Dict): Option parameters to be updated, eg:
                {
                    "z_amp": 0.1,
                    "child_exp_options.fringe": 25,
                    "child_exp_options.child_exp_options.sweep_name": "detune"
                }
            **kwargs: The way to extend options.
        """
        def _set_kv(k: str, v: Any, data: Dict):
            if "." not in k:
                if not unit:
                    data[k] = v
                else:
                    cd = data.get(k, {})
                    cd.update({unit: v})
                    data[k] = cd
            else:
                cur_k, *child_ks = k.split(".")
                c_data = data.get(cur_k, {})
                c_data = _set_kv(".".join(list(child_ks)), v, c_data)
                data[cur_k] = c_data
            return data

        # update options
        if exp_name in self.exp_map:

            if option_mode is None and unit is not None:
                option_mode = OptionMode.parallel

            if option_mode is None:
                option_mode = OptionMode.regular

            exp_collection = self.exp_map.get(exp_name)
            options_for_exec = getattr(exp_collection, option_mode)
            current_options = options_for_exec.get(option_type, {})

            options = options or {}
            options.update(kwargs)

            for key, value in options.items():
                current_options = _set_kv(key, value, current_options)

            options_for_exec[option_type] = current_options
            setattr(exp_collection, option_mode, options_for_exec)
        else:
            pyqlog.error(f"{exp_name} is not in current experiment collections!")

    def update_exp_context_options(self, exp_name: str, **kwargs):
        if exp_name in self.exp_map:

            exp_collection = self.exp_map.get(exp_name)
            context_options: CommonDict = exp_collection.context_options
            if kwargs:
                context_options.update(kwargs)
                pyqlog.info(f"update {exp_name} context options: {kwargs}!")
        else:
            pyqlog.error(f"{exp_name} is not in current experiment collections!")

    def to_file(self):
        pass
