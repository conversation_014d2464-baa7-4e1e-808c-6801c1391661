# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/15
# __author:       <PERSON><PERSON>, <PERSON><PERSON><PERSON>

from ...log import pyqlog
from ..batch_experiment import BatchExperiment, List


class BatchXZTiming(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.flows = [
            "CavityFreqSpectrum",
            "SingleShot",
            # "XpiDetection",
            "QubitFreqCalibration",
            "XpiDetection",
            "SingleShot_1",
        ]

        options.timing_flows = [
            "XYZTimingComposite",
        ]
        options.z_amp_list = []
        options.rates = []
        options.retry_point_labels = []
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.xyz_result = {}
        return options

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)
        if "XYZTimingComposite" in exp_name:
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    qubit = self.backend.chip_data.cache_qubit.get(unit)
                    xy_delay = record.analysis_data.get(unit).get("result").get("xy_delay")
                    z_delay = record.analysis_data.get(unit).get("result").get("z_delay")
                    self.run_options.xyz_result[unit] = {
                        "xy": {"channel": qubit.xy_channel, "delay": xy_delay},
                        "z": {"channel": qubit.z_flux_channel, "delay": z_delay},
                    }
            self._save_data_to_json(self.run_options.xyz_result, "xyz_delay")
        return record

    def _run_once_point_label(self, physical_units: List[str]):
        group_map = self.parallel_allocator_for_qc(physical_units)
        cur_pass_units = []
        for group_name, group in group_map.items():
            pyqlog.info(f"start calibration of {group_name } | Count-{len(group_map)}")
            pass_units = self._run_flow(flows=self.experiment_options.flows, physical_units=group)
            if pass_units:
                cur_pass_units.extend(self._run_timing(pass_units))
        cur_fail_units = [unit for unit in physical_units if unit not in cur_pass_units]
        return cur_pass_units, cur_fail_units

    def _run_batch(self):
        pass_units, fail_units = self._run_once_point_label(self.experiment_options.physical_units)
        for point_label in self.experiment_options.retry_point_labels:
            pyqlog.info(f"Change Point Label `{point_label}`")
            self.backend.change_point_label(point_label)
            cur_pass_units, fail_units = self._run_once_point_label(fail_units)
            pass_units.extend(cur_pass_units)
        self.record_meta.execute_meta.result.hot_data = self.run_options.xyz_result
        self.bind_pass_units(pass_units)
        self.backend._set_env()
        if self.experiment_options.save_db is True:
            self.backend.update_context_from_hot_data(self.record_id)

    def _calculate_z_amp_list(self, parallel_units):
        z_amp_map = {}

        if self.experiment_options.rates:
            pyqlog.info("use rate z amp mode")
            for unit in parallel_units:
                qubit = self.context_manager.chip_data.cache_qubit.get(unit)
                if (
                    qubit.tunable
                    and qubit.dc_max != qubit.dc_min
                    and (qubit.dc_max - qubit.dc_min) * qubit.idle_point <= 0
                ):
                    max_gap = max(abs(qubit.idle_point), abs(qubit.dc_min - qubit.dc_max - qubit.idle_point))
                    z_loc_list = [
                        i * r * max_gap + qubit.dc_max + qubit.idle_point
                        for i in [-1, 1]
                        for r in self.experiment_options.rates
                    ]
                    z_amp_list = []
                    v_max, v_min = (
                        (qubit.dc_max, qubit.dc_min) if qubit.dc_max > qubit.dc_min else (qubit.dc_min, qubit.dc_max)
                    )
                    for z_loc in z_loc_list:
                        if v_min <= z_loc <= v_max:
                            z_amp_list.append(z_loc - qubit.dc_max - qubit.idle_point)
                    if z_amp_list:
                        z_amp_map[unit] = z_amp_list
                        pyqlog.info(f"{unit} support z amp is {z_amp_list}")
        elif self.experiment_options.z_amp_list:
            pyqlog.info(f"use customize z amp mode, {self.experiment_options.z_amp_list}")
            for unit in parallel_units:
                z_amp_map[unit] = self.experiment_options.z_amp_list
        else:
            raise ValueError("Please input experiment options `rates` or `z_amp_list`")
        return z_amp_map

    def _run_timing(self, parallel_units):
        z_amp_map = self._calculate_z_amp_list(parallel_units)
        max_length = max([len(v) for v in z_amp_map.values()])
        good_units = []
        fail_units = [unit for unit in parallel_units if unit not in z_amp_map]

        for idx in range(max_length):
            cur_units = [unit for unit in parallel_units if unit not in good_units and unit not in fail_units]

            if not cur_units:
                break

            work_units = []
            for unit in cur_units:
                if len(z_amp_map[unit]) > idx:
                    z_amp = z_amp_map[unit][idx]
                    self.change_parallel_exec_exp_options(
                        exp_name="XYZTimingComposite",
                        unit=unit,
                        options={"child_exp_options.z_pulse_params.amp": z_amp},
                    )
                    pyqlog.log("FLOW", f"Change {unit} z amp to {z_amp}")
                    work_units.append(unit)
                else:
                    fail_units.append(unit)
            if work_units:
                pass_units = self._run_flow(
                    flows=self.experiment_options.timing_flows,
                    physical_units=work_units,
                )
                if pass_units:
                    for unit in pass_units:
                        good_units.append(unit)

        return good_units
