# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:2
msgid "pyQCat.experiments.composite.ReadoutPowerCalibrate"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite.single_shot_composite.SingleShotComposite`"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate:1
msgid "Optimize Readout Power of Qubit."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.component_experiment>`\\"
" \\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.from_experiment_context>`\\"
" \\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.get_qubit_str>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.options_table>`\\ "
"\\(\\[mode\\, detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`run <pyQCat.experiments.composite.ReadoutPowerCalibrate.run>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate.run:1
msgid "Run SingleShot Composite Experiment Optimize sample width."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.set_analysis_options>`\\"
" \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.set_experiment_options>`\\"
" \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutPowerCalibrate.rst:32
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.child_experiment>`\\"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:7
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:4
msgid ""
"optimize_field (str): Give target optimize field. sweep_list (List, "
"np.ndarray): Scan optimize field list."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_analysis_options
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_run_options
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_analysis_options:4
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:9
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_run_options:4
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_analysis_options:1
msgid "Default analysis options for optimize experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_run_options:1
msgid "Default options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._metadata:1
msgid "Set metadata."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.composite.single_shot_composite.SingleShotComposite`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.__init__>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`component_experiment "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.component_experiment>`\\"
#~ " \\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.get_qubit_str>`\\"
#~ " \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.run>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.set_analysis_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.set_parent_file>`\\"
#~ " \\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.set_run_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`child_experiment "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.run_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.composite.single_shot_composite.SingleShotComposite`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.__init__>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`component_experiment "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.component_experiment>`\\"
#~ " \\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.get_qubit_str>`\\"
#~ " \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.options_table>`\\"
#~ " \\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.run>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.set_analysis_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.set_parent_file>`\\"
#~ " \\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.set_run_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`child_experiment "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate.run_options>`\\"
#~ msgstr ""

