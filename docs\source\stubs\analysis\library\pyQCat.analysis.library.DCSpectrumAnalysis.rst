﻿pyQCat.analysis.library.DCSpectrumAnalysis
==========================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: DCSpectrumAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~DCSpectrumAnalysis.__init__
      ~DCSpectrumAnalysis.from_sub_analysis
      ~DCSpectrumAnalysis.run_analysis
      ~DCSpectrumAnalysis.set_options
      ~DCSpectrumAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~DCSpectrumAnalysis.analysis_datas
      ~DCSpectrumAnalysis.data_filter
      ~DCSpectrumAnalysis.drawer
      ~DCSpectrumAnalysis.experiment_data
      ~DCSpectrumAnalysis.has_child
      ~DCSpectrumAnalysis.options
      ~DCSpectrumAnalysis.quality
      ~DCSpectrumAnalysis.results
   
   