# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/20
# __author:       <PERSON><PERSON><PERSON>

"""
Request data client from transfer service.
"""

import asyncio
import json
import os
import sys
from typing import List, Tuple, Union

import zmq
from loguru import logger

from pyQCat.protobuf.v1.acquisition_pb2 import AmpPhaseData, IQData, Probability
from pyQCat.structures import QDict

from ..data_transfer.state import (
    DataClientRequireCode,
    DataTypeEnum,
    LinkRequireCode,
    TransferTaskStatusEnum,
)
from ..data_transfer.structure import TransferSetting, settings
from ..data_transfer.util import (
    build_ipc_file,
    decode_msg,
    encode_msg,
    generate_unique_identity,
)
from ..errors import CompilerExperimentError
from ..qm_protocol import CommonMessage, ExperimentFile
from .util import CONCURRENT_CACHE
from .worker.experiment_compiler import CompileResult, CompileStatus, ExperimentCompiler

DATA_CLIENT_RECV_TIME = 600000


def singleton(cls):
    """Class singleton decorator ensures that there is only a unique instance in each process

    Returns:
        _type_: Single instance object.
    """
    _instances = {}

    def get_instance(*args, **kwargs):
        pid = os.getpid()
        if pid not in _instances:
            _instances[pid] = cls(*args, **kwargs)
        return _instances[pid]

    return get_instance


@singleton
class DataClient:
    """
    DataClient is used to request task status, task collection data, etc. from
    the Transfer service. In addition, it can also issue requests to clear
    resources, etc.
    """

    data_type_map = {
        DataTypeEnum.IQ: IQData,
        DataTypeEnum.AP: AmpPhaseData,
        DataTypeEnum.PO: Probability,
        DataTypeEnum.TRACK: IQData,
    }

    def __init__(self, process_name: str = "") -> None:
        """Initializes the instance of the class.

        Asynchronous zmq DEALER, with Transfer zmq Router establishes connection,
        DataClient supports adding process pools to enable independent requests
        from different processes.
        """
        process_name = process_name or CONCURRENT_CACHE["transfer_name"]
        self._pid_str = str(os.getpid())
        ctx = zmq.Context()
        self._link_sock = ctx.socket(zmq.DEALER)
        self._link_sock.setsockopt(
            zmq.IDENTITY,
            generate_unique_identity(settings.data_client_id + self._pid_str + "link"),
        )
        self._link_sock.connect(build_ipc_file(settings.link_url + process_name))
        self._acquire_link()

        url = build_ipc_file(settings.transfer_url + self._pid_str)
        self._sock = ctx.socket(zmq.DEALER)
        self._identity = generate_unique_identity(settings.data_client_id + self._pid_str)
        self._sock.setsockopt(zmq.IDENTITY, self._identity)
        self._sock.setsockopt(zmq.RCVTIMEO, DATA_CLIENT_RECV_TIME)
        self._sock.connect(url)

        logger.debug(f"DataClient addr {url}")

    def _acquire_link(self):
        """
        The DataClient requests to establish a connection with the transfer service.
        Upon receiving a reply, the DataClient establishes a Sock to communicate with
        TransferSocket, and each process's DataClient has an independent TransferSocket
        thread to communicate with it
        """
        self._link_sock.send_multipart([LinkRequireCode.LINK, encode_msg(self._pid_str)])
        self._link_sock.recv_multipart()

    def set_dispatcher(self, setting: TransferSetting):
        """Set or update Naga service address

        Args:
            setting (TransferSetting): Settings Object
        """
        self._link_sock.send_multipart([LinkRequireCode.SET, setting.to_bytes()])
        response = decode_msg(self._link_sock.recv_multipart()[0], parse_json=True)
        status = TransferTaskStatusEnum.from_description(response["status"])
        if status == TransferTaskStatusEnum.TACKLE_FAIL:
            logger.error(f"Set transfer error \n{response.get('reason')}")

    def close_transfer(self):
        """
        System exit, close Transfer process
        """
        self._link_sock.send_multipart([LinkRequireCode.CLOSE])

    def stop_transfer(self):
        """
        Temporarily suspend the transfer process and clear all unfinished tasks
        """
        self._link_sock.send_multipart([LinkRequireCode.STOP])

    def client_close(self):
        """
        The client process ends and releases the socket that matches it in the Transfer service
        """
        self._sock.send_multipart([DataClientRequireCode.FINISH, encode_msg(self._pid_str)])

    def shutdown(self):
        """
        Close data client socket.
        """
        if self._sock:
            self._sock.close()
        if self._link_sock:
            self._link_sock.close()
        sys.stdout.write(f"Transfer DataClient {self._pid_str} close ...")

    def query_acq_data_one(
        self,
        require_id: str,
        channels: List[Union[str, int]],
        index: int,
        data_type: DataTypeEnum,
    ):
        """Request data collection for a single Loop.

        Args:
            require_id (str): Task require id.
            channels (List[Union[str, int]]): The requested collection channel.
            index (int): Task cycle loop index number.
            data_type (DataTypeEnum): Task request data type.

        Returns:
            Tuple: Return the current status and collection results of the task.
        """
        self._sock.send_multipart([
            DataClientRequireCode.QUERY_ONE_LOOP,
            encode_msg(
                json.dumps({
                    "require_id": require_id,
                    "channels": [str(c) for c in channels],
                    "index": index,
                })
            ),
        ])
        acq_data_buf = self._sock.recv_multipart()
        return self._parse_acq_data(acq_data_buf, data_type)

    def query_acq_data_all(self, require_id: str, channels: List[Union[str, int]], data_type: DataTypeEnum) -> Tuple:
        """Request data collection for all Loop.

        Args:
            require_id (str): Task require id.
            channels (List[Union[str, int]]): The requested collection channel.
            data_type (DataTypeEnum): Task request data type.

        Returns:
            Tuple: Return the current status and collection results of the task.
        """
        self._sock.send_multipart([
            DataClientRequireCode.QUERY_ALL_LOOP,
            encode_msg(
                json.dumps({
                    "require_id": require_id,
                    "channels": [str(c) for c in channels],
                })
            ),
        ])
        acq_data_buf = self._sock.recv_multipart()
        return self._parse_acq_data(acq_data_buf, data_type)

    def query_task_state(self, require_id: str):
        """Query task status fromm require id

        Args:
            require_id (str): Task require id.

        Returns:
            TaskStatusEnum: Task status
        """
        self._sock.send_multipart([DataClientRequireCode.QUERY_STATE, encode_msg(require_id)])
        message = self._sock.recv_multipart()
        return TransferTaskStatusEnum.from_description(decode_msg(message[0]))

    def query_task_uuid(self, require_id: str):
        """Query the ID of the requested task, which was generated by the Naga Dispatcher module

        Args:
            require_id (str): Task require id.

        Returns:
            str: If the request is successful, return uuid, otherwise an empty string
        """
        self._sock.send_multipart([DataClientRequireCode.QUERY_TASK_ID, encode_msg(require_id)])
        message = self._sock.recv_multipart()
        return decode_msg(message[0])

    def clear_task(self, require_id: str):
        """Clear task resource from require id.

        Args:
            require_id (str): Task require id.

        Returns:
            TransferTaskStatusEnum: Resource clear state.
        """
        self._sock.send_multipart([DataClientRequireCode.CLEAR_TASK, encode_msg(require_id)])
        message = self._sock.recv_multipart()
        return TransferTaskStatusEnum.from_description(decode_msg(message[0]))

    def clear_acq_data(self, require_id: str, readout_channels: List):
        """Clear the collection data of the specified reading channel in the task.

        Args:
            require_id (str): Task require id.
            readout_channels (List): Task readout channels.

        Returns:
            TransferTaskStatusEnum: Resource clear state.
        """
        self._sock.send_multipart([
            DataClientRequireCode.CLEAR_ACQ_DATA,
            encode_msg(
                json.dumps({
                    "require_id": require_id,
                    "channels": [str(c) for c in readout_channels],
                })
            ),
        ])
        message = self._sock.recv_multipart()
        return TransferTaskStatusEnum.from_description(decode_msg(message[0]))

    def query_acq_state(self, require_id: str):
        """Check if the task has been collected successfully.

        Args:
            require_id (str): Task require id.

        Returns:
            TransferTaskStatusEnum: If the collection is completed, return ACQ_FINISH; otherwise, return the task status
        """
        self._sock.send_multipart([DataClientRequireCode.QUERY_ACQ_STATE, encode_msg(require_id)])
        message = self._sock.recv_multipart()
        return TransferTaskStatusEnum.from_description(decode_msg(message[0]))

    def register_program(self, compile_result, simulator: bool = False):
        """Registration task

        Args:
            compile_result (_type_): The program generated by the Monster experiment
            simulator (bool, optional): Do you want to use a simulator. Defaults to False.
        """
        extra_data = compile_result.to_register()
        extra_data["simulator"] = simulator
        
        if simulator is True:
            logger.warning(f"Register simulator task {compile_result.require_id}")

        self._sock.send_multipart([
            DataClientRequireCode.REGISTER,
            compile_result.program_pointer_buf,
            compile_result.program_buf,
            encode_msg(json.dumps(extra_data)),
        ])
        self._sock.recv_multipart()

    def query_task_information(self, require_id: str) -> dict:
        """Query task information

        Args:
            require_id (str): Task require id.

        Returns:
            dict: task concurrent state
        """
        self._sock.send_multipart([DataClientRequireCode.QUERY_INFO, encode_msg(require_id)])
        message = self._sock.recv_multipart()
        return decode_msg(message[0], parse_json=True)

    def query_task_program(self, task_id: str):
        """Query task landing data

        Args:
            task_id (str): Server ID of the task

        Returns:
            _type_: _description_
        """
        self._link_sock.send_multipart([LinkRequireCode.QUERY_PROGRAM, encode_msg(task_id)])
        message = self._link_sock.recv_multipart()
        return message[0]

    def wait_for_acquisition(self, require_id: str):
        """Check if the task has started collecting data, the task is considered to have started
        collecting data upon receiving it.

        Args:
            require_id (str): Task require id.

        Returns:
            dict: task concurrent state
        """
        self._sock.send_multipart([DataClientRequireCode.WAIT_ACQ, encode_msg(require_id)])
        status = self._sock.recv_multipart()[0]
        return TransferTaskStatusEnum.from_description(decode_msg(status))

    def _parse_acq_data(self, buffers: List[bytes], data_type: DataTypeEnum) -> Tuple:
        """Analyze the collected raw data sent by transfer.

        Args:
            buffers (List[bytes]): raw bytes data.
            data_type (DataTypeEnum): Expected data parsing type.

        Returns:
            Tuple: Return the current status and collection results of the task.
        """
        proto_cls = self.data_type_map[data_type]
        acq_data = []
        state_buf = buffers[0]
        for buf in buffers[1:]:
            proto = proto_cls.FromString(buf)
            if data_type in [DataTypeEnum.IQ, DataTypeEnum.TRACK]:
                acq_data.append(
                    QDict(
                        I=[b for b in proto.i],
                        Q=[b for b in proto.q],
                    )
                )
            elif data_type == DataTypeEnum.AP:
                acq_data.append(
                    QDict(
                        amp=[b for b in proto.amp],
                        phase=[b for b in proto.phase],
                    )
                )
        return TransferTaskStatusEnum.from_description(decode_msg(state_buf)), acq_data

    def traceback_user_record(self, message: dict):
        self._sock.send_multipart(
            [DataClientRequireCode.RECORD_MESSAGE, encode_msg(message)]
        )


def set_transfer_config(config, token):
    """Set up configuration for Transfer service

    Args:
        config (QDict): PyQCatConfig
        token (_type_): token key
    """
    # set transfer
    DataClient().set_dispatcher(
        TransferSetting(
            dispatcher_url=config.naga.url,
            web_url=config.naga.web,
            token=token,
        )
    )
    
    CONCURRENT_CACHE["special_tag"] = config.run.special_tag


async def register_compile_result(result: CompileResult, use_simulator: int = 0):
    if result.status == CompileStatus.DONE:
        require_id = result.require_id
    else:
        raise CompilerExperimentError(msg=str(result.message))
    if use_simulator == 1:
        return TransferTaskStatusEnum.ACQ, require_id, require_id

    client = DataClient()
    client.register_program(result, use_simulator == 2)

    is_block = False
    while True:
        await asyncio.sleep(0.1)
        state = client.query_task_state(require_id)
        if state in [TransferTaskStatusEnum.ACQ, TransferTaskStatusEnum.SUC]:
            task_id = client.query_task_uuid(require_id)
            return state, require_id, task_id
        elif state == TransferTaskStatusEnum.FAIL:
            task_id = client.query_task_uuid(require_id)
            client.clear_task(require_id)
            return state, require_id, task_id
        if state == TransferTaskStatusEnum.BLOCK and is_block is False:
            logger.warning("Task block, please check transfer log ...")
            is_block = True


def concurrent_merge_experiment(experiments: List[ExperimentFile], parallel_cache: CommonMessage):
    """
    concurrent merge experiment, use to merge experiment.
    """
    compiler = ExperimentCompiler(experiments, parallel_cache)
    compiler.run()
    return compiler.result
