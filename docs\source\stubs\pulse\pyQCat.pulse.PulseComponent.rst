﻿pyQCat.pulse.PulseComponent
===========================

.. currentmodule:: pyQCat.pulse

.. autoclass:: PulseComponent

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~PulseComponent.__init__
      ~PulseComponent.correct_ac_crosstalk
      ~PulseComponent.correct_compensate
      ~PulseComponent.correct_delay
      ~PulseComponent.correct_distortion
      ~PulseComponent.correct_pulse
      ~PulseComponent.get_pulse
      ~PulseComponent.get_raw_pulse
      ~PulseComponent.plot
      ~PulseComponent.validate_parameters
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~PulseComponent.attach
      ~PulseComponent.bit
      ~PulseComponent.delay
      ~PulseComponent.envelop
      ~PulseComponent.id
      ~PulseComponent.parameters
      ~PulseComponent.pulse
      ~PulseComponent.raw_pulse
      ~PulseComponent.sweep
      ~PulseComponent.type
      ~PulseComponent.width
   
   