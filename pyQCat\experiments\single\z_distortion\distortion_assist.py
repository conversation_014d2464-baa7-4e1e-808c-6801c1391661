# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/12/21
# __author:       xw

import numpy as np

from ....analysis.algorithms import IQdiscriminator
from ....analysis.library import DistortionAssistAnalysis
from ....errors import Experiment<PERSON>ieldError
from ....parameters import options_wrapper
from ....pulse.pulse_function import half_pi_pulse, zero_pulse
from ....pulse.pulse_lib import Constant
from ....structures import Options
from ....tools import qarange
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


@options_wrapper
class DistortionAssist(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
        """
        options = super()._default_experiment_options()

        options.set_validator("separation", int)
        options.set_validator("width_list", list)
        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("phi", float)
        options.set_validator("fringe", float)
        options.set_validator("sample_step", float)
        options.set_validator("xy_width", float)
        options.set_validator("repeat", int)
        options.set_validator("separa_num", int)
        options.set_validator("is_amend", bool)

        options.separation = 3000
        options.sample_step = 2.5
        options.width_list = None
        options.z_amp = 0.13
        options.fringe = 25  # MHz
        options.phi = 0
        options.xy_width = None
        options.repeat = 2000
        options.separa_num = 2
        options.is_amend = True
        options.width_list = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """"""
        options = super()._default_analysis_options()
        options.z_amp = None
        options.fringe = None
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.exp_qubit = None
        options.drag_qubit = None

        options.support_context = [StandardContext.QC]

        return options

    def _check_options(self):
        super()._check_options()
        if self.discriminator is None:
            data_type = "amp_phase"
        else:
            data_type = "I_Q"

        width_end = (
            self.experiment_options.separation / self.experiment_options.separa_num
        )
        if width_end <= self.experiment_options.separation / 2:
            width_end = width_end
        else:
            width_end = self.experiment_options.separation / 2

        self.set_experiment_options(
            data_type=data_type,
            width_list=qarange(0, width_end, self.experiment_options.sample_step),
        )

        self.set_analysis_options(
            z_amp=self.experiment_options.z_amp, fringe=self.experiment_options.fringe
        )
        self.set_run_options(
            x_data=self.experiment_options.width_list,
            analysis_class=DistortionAssistAnalysis,
            exp_qubit=self.qubit,
            drag_qubit=self.qubit,
        )

    @staticmethod
    def set_xy_pulses(self):
        width_list = self.experiment_options.width_list
        pulses = DistortionAssist.get_xy_pulses(self, width_list)
        self.experiment_options.xy_width = pulses[0].width
        self.play_pulse("XY", self.run_options.drag_qubit, pulses)

    def get_xy_pulses(self, width_list):
        xy_pulse_list = []
        for width in width_list:
            front_pulse = half_pi_pulse(self.run_options.drag_qubit)
            step_pulse = Constant(width_list[-1], 0, "XY")
            rear_pulse = half_pi_pulse(self.run_options.drag_qubit)
            rear_pulse.phase = (
                self.experiment_options.phi
                + 2 * np.pi * self.experiment_options.fringe * 1e6 / 1e9 * width
            )
            xy_pulse = front_pulse() + step_pulse() + rear_pulse()
            xy_pulse_list.append(xy_pulse)
        return xy_pulse_list

    @staticmethod
    def set_z_pulses(self):
        xy_width = self.experiment_options.xy_width
        width_list = self.experiment_options.width_list
        z_amp = self.experiment_options.z_amp
        z_pulses = DistortionAssist.get_z_pulses(
            self.run_options.drag_qubit, width_list, z_amp, xy_width
        )
        self.play_pulse("Z", self.run_options.exp_qubit, z_pulses)

    @staticmethod
    def get_z_pulses(qubit, width_list, z_amp, xy_width):
        z_pulse_list = []
        for width in width_list:
            delay_pulse = zero_pulse(qubit, "Z")
            step_pulse = Constant(round(width, 4), z_amp)
            rear_pulse = Constant(round(xy_width - (width + delay_pulse.width), 4), 0)
            z_pulse = delay_pulse() + step_pulse() + rear_pulse()
            z_pulse_list.append(z_pulse)
        return z_pulse_list


class CouplerDistortionAssist(DistortionAssist):
    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.support_context = [StandardContext.CC]

        return options

    def _check_options(self):
        if self.coupler is None:
            raise ExperimentFieldError(
                self.label, f"No coupler information is configured!"
            )

        if not isinstance(self.discriminator, IQdiscriminator):
            raise ExperimentFieldError(self.label, "No iq discriminator is configured!")

        super()._check_options()
        self.set_run_options(exp_qubit=self.coupler, drag_qubit=self.qubit)
