# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/04/20
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy

import numpy as np

from ....analysis import ZZShiftZampAnalysis, ZZShitAnalysis
from ....parameters import options_wrapper
from ....structures import MetaData, Options
from ....tools import qarange
from ...composite_experiment import CompositeExperiment, ExperimentRunMode
from ...single import RamseyZZ, SpinEchoZZ


@options_wrapper
class ZZShiftSpinEcho(CompositeExperiment):
    _sub_experiment_class = SpinEchoZZ

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("z_amp_list", list)
        options.set_validator("scan_name", str)
        options.set_validator("scan_mode", ["ac", "awg_bias"])
        options.amp_list = qarange(0.1, 0.2, 0.01)
        options.scan_name = None
        options.run_mode = ExperimentRunMode.async_mode
        options.scan_mode = "ac"
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.type = "spin_echo"
        return options

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.draw_meta = {
            "scan": self.experiment_options.child_exp_options.amp_c_bit,
        }
        return metadata

    def _check_options(self):
        super()._check_options()
        # self.set_experiment_options(show_result=False)
        self.set_run_options(
            x_data=self.experiment_options.z_amp_list, analysis_class=ZZShitAnalysis
        )

    def _setup_child_experiment(self, exp: SpinEchoZZ, index: int, vol: float):
        exp.run_options.index = index
        total = len(self.run_options.x_data)

        exp.set_parent_file(self, f"amp={vol}v", index, total)

        if self.experiment_options.scan_mode == "ac":
            exp.set_experiment_options(z_amp=vol)
        else:
            exp.run_options.ac_bias = {
                self.qubit_pair.qc: vol + self.couplers[0].dc_max
            }

        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp: SpinEchoZZ):
        fringe = exp.experiment_options.fringe
        exp.analysis.provide_for_parent = {
            "freq": exp.analysis.results.freq.value - fringe
        }


class ZZShiftRamsey(ZZShiftSpinEcho):
    _sub_experiment_class = RamseyZZ

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])
        options.run_mode = ExperimentRunMode.sync_mode
        return options

    async def _sync_composite_run(self):
        # super(ZZShiftSpinEcho, self).run()

        self.set_analysis_options(type="ramsey")
        z_amp_list = self.experiment_options.z_amp_list

        for i, amp in enumerate(z_amp_list):
            for j, gate in enumerate(["I", "X"]):
                child_exp = deepcopy(self.child_experiment)
                child_exp.set_parent_file(
                    self, f"amp={amp}v-gate-{gate}", i, len(z_amp_list)
                )
                if self.experiment_options.scan_mode == "ac":
                    child_exp.set_experiment_options(z_amp=amp, gate=gate)
                else:
                    child_exp.run_options.ac_bias = {
                        self.qubit_pair.qc: amp + self.couplers[0].dc_max
                    }
                self._check_simulator_data(child_exp, i * 2 + j)
                await child_exp.run_experiment()
                child_exp.analysis.provide_for_parent = {
                    "freq": child_exp.analysis.results.freq.value,
                }
                self._experiments.append(child_exp)

        self._run_analysis(
            x_data=np.repeat(np.array(z_amp_list), 2), analysis_class=ZZShitAnalysis
        )


@options_wrapper
class ZZShiftSpinEchoZAmp(CompositeExperiment):
    _sub_experiment_class = ZZShiftSpinEcho

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("z_amp_list", list)
        options.set_validator("cur_scan_name", str)
        options.z_amp_list = qarange(0.1, 0.2, 0.01)
        options.cur_scan_name = None
        options.run_mode = ExperimentRunMode.async_mode
        return options

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.process_meta = {
            "row_bit": self.experiment_options.child_exp_options.child_exp_options.amp_c_bit,
            "col_bit": self.experiment_options.child_exp_options.child_exp_options.amp_bit,
        }
        return metadata

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.z_amp_list,
            analysis_class=ZZShiftZampAnalysis,
        )

    def _setup_child_experiment(self, exp: SpinEchoZZ, index: int, vol: float):
        exp.run_options.index = index
        total = len(self.run_options.x_data)

        exp.set_parent_file(self, f"amp={vol}v", index, total)
        exp.experiment_options.child_exp_options.bit_z_amp = vol
        self._check_simulator_data(exp, index)
