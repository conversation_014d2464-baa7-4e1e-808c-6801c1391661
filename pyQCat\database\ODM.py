# -*- coding: utf-8 -*-
"""
__date:         2019/09/18
__author:       <PERSON>
__corporation:  OriginQuantum
__usage:        
"""

from datetime import datetime

import numpy as np
from mongoengine import (<PERSON>ument, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,
                         <PERSON><PERSON><PERSON><PERSON>, Embed<PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON>dedD<PERSON><PERSON><PERSON><PERSON>ield,
                         <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ield, <PERSON><PERSON>ield,
                         <PERSON><PERSON><PERSON><PERSON>ield, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ield)

__all__ = ('SineWaveDoc', 'DcWaveDoc', 'CustomWaveDoc', 'WaveFormDoc',
           'XYlineDoc', 'ZLineDoc', 'ReadoutDoc', 'MeasureAIODoc',
           'MeasureResultDoc', 'XYWaveDoc', 'ZWaveDoc', 'QubitDoc', 'SweepDoc',
           'ExperimentDoc', 'InstrumentAIODoc', 'TrigOutDoc', "CompensateDoc")

#####################################################################
#                   Instrument Document
#####################################################################

# trigger way of Measure AIO module(0 means Internal trigger, 1-4 means External trigger channel)
TRIG = (0, 1, 2, 3, 4)


class SineWaveDoc(EmbeddedDocument):
    """ The clasee used for getting a sin waveform with Measure AIO instrument.
    """
    pulse_width = IntField(required=True)
    baseband_frequency = FloatField(required=True)
    initial_phase = FloatField(required=True)
    amplitude = FloatField()


class DcWaveDoc(EmbeddedDocument):
    pulse_width = IntField(required=True)
    amplitude = FloatField(required=True)


class CustomWaveDoc(EmbeddedDocument):
    wavefile = BinaryField()
    bigwavefile = FileField()


class WaveFormDoc(EmbeddedDocument):
    sine_wave = EmbeddedDocumentField(SineWaveDoc)
    dc_wave = EmbeddedDocumentField(DcWaveDoc)
    custom_wave = EmbeddedDocumentField(CustomWaveDoc)


class XYlineDoc(EmbeddedDocument):
    pulse_period = IntField()
    trig_way = IntField(choices=TRIG)
    channel = IntField(required=True)
    trigger_delay = ListField(
        required=True)  # since of 30bit OriginQAIO changed.
    output_frequency = FloatField(required=True)
    intermediate_frequency = FloatField(default=600, required=True)
    pulse_power = FloatField(required=True)
    waveform = EmbeddedDocumentField(WaveFormDoc, required=True)
    trigger_out_delay = IntField()  # 5 ~ 8


class ZLineDoc(EmbeddedDocument):
    pulse_period = IntField()
    trig_way = IntField(choices=TRIG)
    channel = IntField(required=True)
    trigger_delay = ListField(
        required=True)  # since of 30bit OriginQAIO changed.
    waveform = EmbeddedDocumentField(WaveFormDoc, required=True)
    trigger_out_delay = IntField()  # 1 ~ 4


class ReadoutDoc(EmbeddedDocument):
    pulse_period = IntField()
    trig_way = IntField(choices=TRIG)
    channel = IntField(required=True)
    trigger_delay = ListField(
        required=True)  # since of 30bit OriginQAIO changed.
    sampling_delay = ListField(
        required=True, default=[50])  # since of 30bit OriginQAIO changed.
    sampling_time_width = ListField(required=True, default=[1000])
    output_frequency = FloatField(required=True)
    intermediate_frequency = FloatField(default=600, required=True)
    pulse_power = FloatField(required=True)
    power_attenuation = IntField(default=0, required=True)
    waveform = EmbeddedDocumentField(WaveFormDoc, required=True)


class TrigOutDoc(EmbeddedDocument):
    pulse_period = IntField()
    trig_way = IntField(choices=TRIG)
    channel = IntField(required=True)
    trigger_delay = ListField(
        required=True)  # since of 30bit OriginQAIO changed.
    pulse_width = IntField(required=True)


class MeasureAIODoc(Document):
    pulse_period = IntField(default=10, required=True)
    trig_way = IntField(choices=TRIG, default=0, required=True)
    Z_dc_control = ListField()
    XY_control = ListField(EmbeddedDocumentField(XYlineDoc))
    Z_flux_control = ListField(EmbeddedDocumentField(ZLineDoc))
    Read_out_control = ListField(EmbeddedDocumentField(ReadoutDoc))
    Trig_out_control = ListField(EmbeddedDocumentField(TrigOutDoc))
    meta = {'collection': 'MeasureAIO'}


#####################################################################
#                   Measure Result Document
#####################################################################


class MeasureResultDoc(Document):
    amp = ListField()
    phase = ListField()
    I = ListField()
    Q = ListField()
    meta = {'collection': 'MeasureData'}


#####################################################################
#                    Qubit Document
#####################################################################


class XYWaveDoc(EmbeddedDocument):
    Xpi = FloatField(default=0.8)
    Xpi2 = FloatField(default=0.4)
    Ypi = FloatField(default=None, null=True)
    Ypi2 = FloatField(default=None, null=True)
    Zpi = FloatField(default=None, null=True)
    baseband_freq = FloatField(default=466.667)
    delta = FloatField(default=-240.0)
    detune_pi = FloatField(default=0)
    detune_pi2 = FloatField(default=0)
    alpha = FloatField(default=1)
    offset = FloatField(default=5)
    time = FloatField(default=20)


class ZWaveDoc(EmbeddedDocument):
    width = FloatField(default=None, null=True)
    amp = FloatField(default=None, null=True)


class MWaveDoc(EmbeddedDocument):
    width = FloatField(default=1500)
    amp = FloatField(default=0.5)
    baseband_freq = FloatField(default=600)


class QubitDoc(EmbeddedDocument):
    sample = StringField()
    bit = IntField(min_value=0, max_value=23, required=True)
    name = StringField(required=True)
    probe_freq = FloatField()
    probe_power = FloatField()
    drive_freq = FloatField()
    drive_power = FloatField()
    dc = FloatField(min_value=-5, max_value=5)
    T1 = FloatField()
    T2 = FloatField()
    XYwave = EmbeddedDocumentField(XYWaveDoc)
    Zwave = EmbeddedDocumentField(ZWaveDoc)
    Mwave = EmbeddedDocumentField(MWaveDoc)
    meta = {'collection': 'Qubit'}


#####################################################################
#                  Experiment Document
#####################################################################


class SweepDoc(Document):
    channel = IntField()
    func = StringField()
    combination_points = ListField(FloatField())
    points = ListField(FloatField())
    repeat = IntField(min_value=1, max_value=10000)
    waveform = EmbeddedDocumentField(CustomWaveDoc)
    synchro = BooleanField(default=True)
    meta = {'collection': 'Sweep'}


class NumpyArraryDoc(EmbeddedDocument):
    values = ListField(ListField(default=float))

    def set_array(self, arr):
        self.values = arr.tolist()

    def get_array(self):
        return np.array(self.values)


class ExperimentDoc(Document):
    label = StringField(required=True)
    date_modified = DateTimeField(default=datetime.now)
    measure_aio = ReferenceField(MeasureAIODoc, required=True)
    qubit_info = ListField(EmbeddedDocumentField(QubitDoc))
    measure_data = DictField(ListField(
        StringField()))  # inline MeasureResultDoc objectId
    sweep_control = ListField(ReferenceField(SweepDoc), required=True)
    status = IntField(default=0)
    file_flag = IntField(default=0)
    # for multiple IF
    IF = DictField(ListField())
    index = DictField(ListField(ListField()))
    circuit_time = IntField()
    ac_crosstalk = EmbeddedDocumentField(NumpyArraryDoc)
    xy_crosstalk = DictField()
    xy_power_dict = DictField()
    fake_pulse = BooleanField()
    username = StringField(required=True, default="")
    ac_bias = DictField()
    compensate_dict = DictField()
    meta = {'collection': 'Experiment', 'cascade': True}


class CompensateDoc(Document):
    name = StringField(required=True, default="")
    params = DictField(default={})
    create_time = StringField(default=datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"))
    meta = {"collection": "Compensate"}


#####################################################################
#                  Instrument Document
#####################################################################


class InstrumentAIODoc(Document):
    measure_aio = ReferenceField(MeasureAIODoc)
    VOL_output_channel = ListField(IntField())
    AWG_output_channel = ListField(IntField())
    RF_output_channel = ListField(IntField())
    ADDA_output_channel = ListField(IntField())
    TO_output_channel = ListField(IntField())
    status = IntField(default=0)
    meta = {'collection': 'Instrument'}
