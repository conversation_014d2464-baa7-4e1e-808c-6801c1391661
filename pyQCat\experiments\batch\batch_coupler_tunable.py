# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/25
# __author:       <PERSON><PERSON><PERSON>

import textwrap
from collections import defaultdict
from typing import Dict, List

from prettytable import PrettyTable

from ..batch_experiment import BatchExperiment, BatchPhysicalUnitType, QDict, pyqlog


def unit_group_formation_table(group: Dict) -> str:
    """Display grouping information using prettytable

    Args:
        group (Dict): grouping information.

    Returns:
        str: Table string

    Example:
        group = {
            "group-1": ["q1", "q2", "q3"],
            "group-2": ["q4", "q5", "q6"]
        }
    """
    table = PrettyTable()
    table.field_names = ["Groups", "Physical Units", "Size"]
    for group_name, group_value in group.items():
        table.add_row(
            [
                group_name,
                "\n".join(textwrap.wrap(str(group_value), width=120)),
                len(group_value),
            ]
        )
    return str(table)


class BatchCouplerTunable(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.set_validator("mode", ["circle", "line", "horse"])
        options.mode = "circle"
        options.physical_unit_type = BatchPhysicalUnitType.COUPLER
        options.flows = ["CavityTunable_for_coupler"]
        options.period = 10
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.ppt_template.update(
            dict(
                shape=(3, 4),
                split_by_unit=False,
            )
        )
        return options

    def _run_batch(self):
        pass_units, fail_units = self._run_tunable(
            self.experiment_options.physical_units, 0
        )
        if fail_units:
            self._change_coupler_probe_bit(fail_units)
            pass_units_1, _ = self._run_tunable(fail_units, 1)
            pass_units.extend(pass_units_1)
        if self.experiment_options.save_db is True:
            self.backend.save_chip_data_to_db(pass_units)
        self.bind_pass_units(pass_units)

    def _run_tunable(self, physical_units, index: int = 0):
        pass_units = []
        group_map = self.parallel_allocator_for_cc(
            physical_units, mode=self.experiment_options.mode
        )
        for group_name, couplers in group_map.items():
            couplers = self._check_goodness(couplers)
            pass_units.extend(
                self._run_flow(
                    flows=["CavityTunable_for_coupler"],
                    physical_units=couplers,
                    name=f"{group_name}-Try-{index}",
                )
            )
        fail_units = [unit for unit in physical_units if unit not in pass_units]
        return pass_units, fail_units

    def _check_goodness(self, couplers: List[str]):
        check_couplers = []
        for coupler in couplers:
            coupler_obj = self.backend.chip_data.cache_coupler[coupler]
            probe_qubit = self.backend.chip_data.cache_qubit[
                f"q{coupler_obj.probe_bit}"
            ]
            if probe_qubit.goodness is True and coupler_obj.goodness is True:
                check_couplers.append(coupler)
        return check_couplers

    def _change_coupler_probe_bit(self, couplers: List[str]):
        for coupler in couplers:
            coupler_obj = self.backend.chip_data.cache_coupler[coupler]
            coupler_obj.revert_probe_drive()


class BatchQubitTunable(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.physical_unit_type = BatchPhysicalUnitType.QUBIT
        options.flows = ["CavityTunable_for_qubit"]
        options.set_validator("mode", ["sub", "add", "empty", "blame9", "line"])
        options.mode = "blame9"
        options.period = 10
        options.affect_next_node = False
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.check_qubits = None
        options.ppt_template.update(
            dict(
                shape=(2, 3),
                split_by_unit=False,
            )
        )
        return options

    def _run_tunable(self, physical_units):
        group_map = self.parallel_allocator_for_qc(
            physical_units, mode=self.experiment_options.mode
        )
        pass_units = []
        for group_name, group in group_map.items():
            pass_units.extend(
                self._run_flow(
                    flows=["CavityTunable_for_qubit"],
                    physical_units=group,
                    name=f"{group_name} Qubit Tunable",
                )
            )
        fail_units = [unit for unit in physical_units if unit not in pass_units]
        return pass_units, fail_units

    def _run_batch(self):
        if self.run_options.check_qubits is not None:
            physical_units = self.run_options.check_qubits
        else:
            physical_units = self.experiment_options.physical_units
        pass_units, fail_units = self._run_tunable(physical_units)
        if self.experiment_options.save_db:
            self.backend.save_chip_data_to_db(pass_units)

        if self.run_options.check_qubits is not None:
            pass_units = [
                unit
                for unit in self.experiment_options.physical_units
                if unit not in fail_units
            ]
        self.bind_pass_units(pass_units)

    def bind_process_data(self, process_data: QDict):
        process_data["BatchQubitCavityCheck"] = dict(
            run_options=dict(
                fail_qubits=self.record_meta.execute_meta.result.fail_units
            )
        )


class BatchQubitCavityCheck(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.physical_unit_type = BatchPhysicalUnitType.QUBIT
        options.flows = ["CavityCheck"]
        options.tunable_flows = ["CavityTunable_for_qubit"]
        options.affect_next_node = False
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.pass_qubits = []
        options.fail_qubits = []
        options.ppt_template.update(
            dict(
                shape=(2, 3),
                split_by_unit=False,
            )
        )
        return options

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if exp_name == "CavityCheck":
            for unit in record.analysis_data.keys():
                qubit_cavity_map = (
                    record.analysis_data.get(unit).get("result").get("qubit_cavity_map")
                )
                if qubit_cavity_map:
                    for qubit, cavity in qubit_cavity_map.items():
                        obj = self.backend.chip_data.cache_qubit.get(qubit)
                        obj.probe_freq = cavity
                        obj.check_lo()
                        self.run_options.pass_qubits.append(qubit)

        return record

    def _run_batch(self):
        self.change_regular_exec_exp_options(
            exp_name=self.experiment_options.flows[0], init_cavity=None
        )
        fail_qubits = self.run_options.fail_qubits
        pre_pass_units = [
            unit
            for unit in self.experiment_options.physical_units
            if unit not in fail_qubits
        ]

        bus_qubit_map = defaultdict(list)
        for bit in self.run_options.fail_qubits:
            qubit = self.backend.chip_data.cache_qubit.get(bit)
            bus_qubit_map[f"bus-{qubit.inst.bus}"].append(bit)

        pyqlog.info(f"find bus qubit map: {bus_qubit_map}")

        for bus, bits in bus_qubit_map.items():
            if len(bits) > 1:
                pyqlog.info(f"Start check {bus} : {bits}")
                self._run_flow(
                    flows=self.experiment_options.flows, physical_units=[",".join(bits)]
                )
            else:
                pyqlog.info(f"Start check {bus} : only one qubit fake tunable, next!")

        pass_units = []
        group_map = self.parallel_allocator_for_qc(self.run_options.pass_qubits)
        for gm, g in group_map.items():
            pass_units.extend(
                self._run_flow(
                    flows=self.experiment_options.tunable_flows,
                    physical_units=g,
                    name=f"{gm} Tunable",
                )
            )

        if self.experiment_options.save_db is True:
            self.backend.save_chip_data_to_db(pass_units)
        pre_pass_units.extend(pass_units)
        self.bind_pass_units(pre_pass_units)
