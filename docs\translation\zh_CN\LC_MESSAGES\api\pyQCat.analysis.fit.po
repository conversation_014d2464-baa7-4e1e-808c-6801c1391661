# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.analysis.fit.rst:2
msgid "pyQCat.analysis.fit package"
msgstr ""

#: ../../source/api/pyQCat.analysis.fit.rst:5
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.analysis.fit.rst:8
msgid "pyQCat.analysis.fit.curve\\_fit module"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:1
#: pyQCat.analysis.fit:15:<autosummary>:1
msgid "Calculate curve goodness of fit."
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:3
msgid ""
"We usually use goodness of fit to assess the quality of experimental "
"results, which is calculated as:"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:6
msgid "rmse = \\sqrt{\\frac{\\sum_{i=0}^{n} {(y_2 - y_1)}^2}{n} }"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse
#: pyQCat.analysis.fit.curve_fit.curve_fitting
#: pyQCat.analysis.fit.fit_models.bi_lorentz_tilt
#: pyQCat.analysis.fit.fit_models.complex_pole
#: pyQCat.analysis.fit.fit_models.lorentzian
#: pyQCat.analysis.fit.fit_models.swap_formula
#: pyQCat.analysis.fit.fit_models.twin_lorentzian
#: pyQCat.analysis.fit.fit_models.typecast_float
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:11
msgid "x-axis data."
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:13
msgid "y1-axis data, usually input raw signal."
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:15
msgid "y2-axis data, usually input fit signal."
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse
#: pyQCat.analysis.fit.curve_fit.curve_fitting
#: pyQCat.analysis.fit.fit_models.bi_lorentz_tilt
#: pyQCat.analysis.fit.fit_models.complex_pole
#: pyQCat.analysis.fit.fit_models.cos pyQCat.analysis.fit.fit_models.cos_decay
#: pyQCat.analysis.fit.fit_models.exponential_decay
#: pyQCat.analysis.fit.fit_models.natural_exp
#: pyQCat.analysis.fit.fit_models.skewed_lorentzian
#: pyQCat.analysis.fit.fit_models.sqrt_lorentzian
#: pyQCat.analysis.fit.fit_models.typecast_float
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:17
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse
#: pyQCat.analysis.fit.curve_fit.curve_fitting
#: pyQCat.analysis.fit.fit_models.bi_lorentz_tilt
#: pyQCat.analysis.fit.fit_models.complex_pole
#: pyQCat.analysis.fit.fit_models.swap_formula
#: pyQCat.analysis.fit.fit_models.typecast_float
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:18
msgid "Goodness of fit"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:1
#: pyQCat.analysis.fit:15:<autosummary>:1
msgid "Curve fitting using least squares"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:3
msgid "We used the ``scipy.optimize.curve_fit`` implementation."
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:6
msgid "Curve fit to raw data, including x and y."
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:8
msgid ""
"Fitting options, including initial values corresponding to the fitted "
"model, and fitting iteration parameter boundary conditions, etc."
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:11
msgid "Fit the model, support custom fitting formula."
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting
msgid "Raises"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:13
msgid "scipy.optimize.curve_fit failed with error."
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:15
msgid ":py:class:`~pyQCat.analysis.specification.FitData`"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:16
msgid "Returns a structure of fit parameters, fitted y, and goodness of fit."
msgstr ""

#: ../../source/api/pyQCat.analysis.fit.rst:16
msgid "pyQCat.analysis.fit.fit\\_models module"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.typecast_float:1
msgid ""
"A decorator to typecast y values to a float array if the input parameters"
" have no error."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.typecast_float:4
msgid "Fit function that returns a ufloat array or an array of float."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.typecast_float:6
msgid ":py:data:`~typing.Callable`"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.typecast_float:7
msgid "Fit function with typecast."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.cos:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "Cosine function."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.cos:3
msgid ""
"y = {\\rm amp} \\cdot \\cos\\left(2 \\pi {\\rm freq} \\cdot x\n"
"    + {\\rm phase}\\right) + {\\rm baseline}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.cos:8
#: pyQCat.analysis.fit.fit_models.cos_decay:8
#: pyQCat.analysis.fit.fit_models.exponential_decay:7
#: pyQCat.analysis.fit.fit_models.natural_exp:7
#: pyQCat.analysis.fit.fit_models.skewed_lorentzian:7
#: pyQCat.analysis.fit.fit_models.sqrt_lorentzian:7
msgid ":py:class:`~numpy.ndarray`"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.exponential_decay:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "Exponential function"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.exponential_decay:3
msgid ""
"y = {\\rm amp} \\cdot {\\rm base}^{\\left( - \\lambda x + {\\rm x0} "
"\\right)} + {\\rm baseline}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.cos_decay:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "Cosine function with exponential decay."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.cos_decay:3
msgid ""
"y = {\\rm amp} \\cdot e^{-x/\\tau} \\cos\\left(2 \\pi \\cdot {\\rm freq} "
"\\cdot x\n"
"+ {\\rm phase}\\right) + {\\rm baseline}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2freq_formula:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "Calculate frequency from AC."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2cavity_freq_formula:3
#: pyQCat.analysis.fit.fit_models.amp2freq_formula:3
msgid ""
"phi = \\pi \\ast M \\ast (x - offset)\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2freq_formula:6
msgid ""
"fq = (fq\\_max + detune) \\times \\sqrt{\\sqrt{1 + d^2 (\\tan (phi))^2} "
"\\times \\left | \\cos (phi) \\right | }\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "Single lorentz with background."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:3
msgid ""
"y = offset + \\frac{A \\cdot kappa}{\\pi \\cdot ((f - f0)^2 + {kappa}^2)}"
"\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:7
msgid "frequency sweep points in Hz"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:9
msgid "amplitude of the tallest/deepest Lorentzian structure in the data"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:11
msgid "the offset value of the tallest/deepest Lorentzian structure in the data."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:14
#: pyQCat.analysis.fit.fit_models.twin_lorentzian:18
msgid "frequency of the tallest/deepest Lorentzian structure in the data."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:17
#: pyQCat.analysis.fit.fit_models.twin_lorentzian:26
msgid "kappa (FWHM) of the tallest/deepest Lorentzian structure in the data."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.sqrt_lorentzian:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "Square-root Lorentzian function for spectroscopy."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.sqrt_lorentzian:3
msgid ""
"y = \\frac{{\\rm amp} |\\kappa|}{\\sqrt{\\kappa^2 + 4(x -x_0)^2}} + {\\rm"
" baseline}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "Twin lorentz with background."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:3
msgid ""
"y = \\frac{A_2\\cdot kappa_2}{\\pi\\cdot ((f-f_2)^2 + {kappa_2}^2)} +\n"
" \\frac{A\\cdot kappa}{\\pi\\cdot ((f-f_0)^2 + {kappa}^2)} + background\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:8
msgid "frequency sweep points in Hz."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:10
msgid "amplitude of the tallest/deepest Lorentzian structure in the data."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:13
msgid ""
"amplitude of the other Lorentzian structure in the data; since this "
"function is used for high power qubit spectroscopy, this parameter refers"
" to the Lorentzian structure corresponding to the gf/2 transition."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:21
msgid ""
"frequency of the other Lorentzian structure in the data; since this "
"function is used for high power qubit spectroscopy, this parameter refers"
" to the Lorentzian structure corresponding to the gf/2 transition."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:29
msgid ""
"kappa (FWHM) of the other Lorentzian structure in the data; since this "
"function is used for high power qubit spectroscopy, this parameter refers"
" to the Lorentzian structure corresponding to the gf/2 transition."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:34
msgid "background offset."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.skewed_lorentzian:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "skewed lorentzian formula."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.skewed_lorentzian:3
msgid ""
"y = A1 + A2(x - fr) + \\frac{A3 + A4\\cdot (x-fr)}{1 + "
"4{Q_1}^2(\\frac{x-fr}{fr} )^2}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.natural_exp:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "natural exp fit formula."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.natural_exp:3
msgid ""
"y = amp \\cdot  e^{-\\frac{x}{tau} } + baseline\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2cavity_freq_formula:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "DC modulation model."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2cavity_freq_formula:6
msgid ""
"fr1 = (fq\\_max + fc) \\times \\sqrt{\\sqrt{1 + d^2 (\\tan (phi))^2} "
"\\times \\left | \\cos (phi) \\right | }\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2cavity_freq_formula:9
msgid ""
"delta = fr1 - fr0\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2cavity_freq_formula:12
msgid ""
"kai = \\frac{g^2}{delta\\cdot (1 - \\frac{delta}{fc} )}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2cavity_freq_formula:15
msgid ""
"y = fr0 - kai\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.linear:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "Linear fitting model."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.linear:3
msgid ""
"y = k \\cdot  x +baseline\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.freq2amp_formula:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "Calculate AC based on frequency"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.freq2amp_formula:3
msgid ""
"\\alpha = \\frac{x + detune }{detune + fq_{max}}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.freq2amp_formula:6
msgid ""
"\\beta = \\frac{{\\alpha}^4 - d^2}{1 - d^2}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.freq2amp_formula:9
msgid ""
"amp = \\left | \\frac{\\arccos \\beta}{M\\cdot \\pi}  \\right | + offset\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.complex_pole:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "Decoration of distortion IIR fit model."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.complex_pole:4
msgid "Sample period, when sample rate 1.6 GHz this is 0.625 ns."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.complex_pole:7
msgid "Pole model."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:1
#: pyQCat.analysis.fit:35:<autosummary>:1
msgid "Bia Lorentz fit formula."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:3
msgid ""
"amp = (coe\\cdot x + a) \\cdot \\left [ \\frac{1}{1 + (\\frac{x-b}{c} "
")^2} + offset \\right]\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:7
msgid "independent variable"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:10
msgid "peak height"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:13
msgid "base offset"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:16
msgid "peek position x0 value"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:19
msgid "left peek width"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:22
msgid "right peek width"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:25
msgid "slope coefficient"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:28
msgid "strain variable"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.swap_formula:1
msgid "Swap fit formula."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.swap_formula:3
msgid ""
"y = \\sqrt{4g^2 + A^2(z-z0)^2}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.swap_formula:7
msgid "Independent variable, z_amp value."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.swap_formula:9
#: pyQCat.analysis.fit.fit_models.swap_formula:11
msgid "fitting args"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.swap_formula:13
msgid "The z_map value of symmetry z_amp axis."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.swap_formula:15
msgid "Swap oscillation frequency."
msgstr ""

#: ../../source/api/pyQCat.analysis.fit.rst:24
msgid "Module contents"
msgstr ""

#: of pyQCat.analysis.fit:3
msgid "Analysis Fit  (:mod:`pyQCat.analysis.fit`)"
msgstr ""

#: of pyQCat.analysis.fit:5
msgid "Analysis submodule, collecting fit formula modules."
msgstr ""

#: of pyQCat.analysis.fit:8
msgid "curve fit function"
msgstr ""

#: of pyQCat.analysis.fit:15:<autosummary>:1
msgid ":py:obj:`cal_rmse <pyQCat.analysis.fit.cal_rmse>`\\ \\(x\\, y1\\, y2\\)"
msgstr ""

#: of pyQCat.analysis.fit:15:<autosummary>:1
msgid ""
":py:obj:`curve_fitting <pyQCat.analysis.fit.curve_fitting>`\\ \\(data\\, "
"fit\\_options\\, fit\\_model\\)"
msgstr ""

#: of pyQCat.analysis.fit:17
msgid "fit models"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ""
":py:obj:`cos <pyQCat.analysis.fit.cos>`\\ \\(x\\[\\, amp\\, freq\\, "
"phase\\, baseline\\]\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ""
":py:obj:`exponential_decay <pyQCat.analysis.fit.exponential_decay>`\\ "
"\\(x\\[\\, amp\\, lamb\\, base\\, x0\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ""
":py:obj:`cos_decay <pyQCat.analysis.fit.cos_decay>`\\ \\(x\\[\\, amp\\, "
"tau\\, freq\\, phase\\, baseline\\]\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ""
":py:obj:`amp2freq_formula <pyQCat.analysis.fit.amp2freq_formula>`\\ "
"\\(x\\, fq\\_max\\, detune\\, M\\, offset\\, d\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ""
":py:obj:`freq2amp_formula <pyQCat.analysis.fit.freq2amp_formula>`\\ "
"\\(x\\, fq\\_max\\, detune\\, M\\, offset\\, d\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ""
":py:obj:`lorentzian <pyQCat.analysis.fit.lorentzian>`\\ \\(f\\, A\\, "
"offset\\, f0\\, kappa\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ""
":py:obj:`skewed_lorentzian <pyQCat.analysis.fit.skewed_lorentzian>`\\ "
"\\(x\\, A1\\, A2\\, A3\\, A4\\, fr\\, Ql\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ""
":py:obj:`bi_lorentz_tilt <pyQCat.analysis.fit.bi_lorentz_tilt>`\\ \\(x\\,"
" a\\, offset\\, b\\, cl\\, cr\\, coe\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ""
":py:obj:`sqrt_lorentzian <pyQCat.analysis.fit.sqrt_lorentzian>`\\ "
"\\(x\\[\\, amp\\, kappa\\, x0\\, baseline\\]\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ""
":py:obj:`twin_lorentzian <pyQCat.analysis.fit.twin_lorentzian>`\\ \\(f\\,"
" A2\\, A\\, f2\\, f0\\, kappa2\\, kappa\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ""
":py:obj:`natural_exp <pyQCat.analysis.fit.natural_exp>`\\ \\(x\\, amp\\, "
"baseline\\, tau\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ""
":py:obj:`amp2cavity_freq_formula "
"<pyQCat.analysis.fit.amp2cavity_freq_formula>`\\ \\(x\\, fq\\_max\\, "
"fc\\, M\\, ...\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ":py:obj:`linear <pyQCat.analysis.fit.linear>`\\ \\(x\\, k\\, baseline\\)"
msgstr ""

#: of pyQCat.analysis.fit:35:<autosummary>:1
msgid ":py:obj:`complex_pole <pyQCat.analysis.fit.complex_pole>`\\ \\(ts\\)"
msgstr ""

#~ msgid "todo"
#~ msgstr ""

#~ msgid ""
#~ "Args: f (float):          frequency sweep "
#~ "points in Hz A (float):          "
#~ "amplitude of the tallest/deepest Lorentzian"
#~ " structure"
#~ msgstr ""

#~ msgid "in the data"
#~ msgstr ""

#~ msgid ""
#~ "A_gf_over_2 (float):    amplitude of the "
#~ "other Lorentzian structure in the"
#~ msgstr ""

#~ msgid ""
#~ "data; since this function is used "
#~ "for high power qubit spectroscopy, this"
#~ " parameter refers to the Lorentzian "
#~ "structure corresponding to the gf/2 "
#~ "transition"
#~ msgstr ""

#~ msgid ""
#~ "f0 (float):         frequency of the "
#~ "tallest/deepest Lorentzian structure"
#~ msgstr ""

#~ msgid ""
#~ "f0_gf_over_2 (float):   frequency of the "
#~ "other Lorentzian structure in the"
#~ msgstr ""

#~ msgid ""
#~ "kappa (float):      kappa (FWHM) of the"
#~ " tallest/deepest Lorentzian structure"
#~ msgstr ""

#~ msgid ""
#~ "kappa_gf_over_2 (float): kappa (FWHM) of "
#~ "the other Lorentzian structure in"
#~ msgstr ""

#~ msgid ""
#~ "the data; since this function is "
#~ "used for high power qubit spectroscopy,"
#~ " this parameter refers to the "
#~ "Lorentzian structure corresponding to the "
#~ "gf/2 transition"
#~ msgstr ""

#~ msgid "background (float):     background offset"
#~ msgstr ""

#~ msgid "fitted parameter"
#~ msgstr ""

#~ msgid "lorentz peak or valley corresponding `x` value"
#~ msgstr ""

#~ msgid "reflecting the narrowness of the valley"
#~ msgstr ""

#~ msgid "T1 fit formula."
#~ msgstr ""

#~ msgid "fitted parameter, target args"
#~ msgstr ""

#~ msgid "Qubit dc modulation function."
#~ msgstr ""

#~ msgid "Linear fitting formula."
#~ msgstr ""

#~ msgid ":py:obj:`cal_rmse <pyQCat.analysis.fit.cal_rmse>`\\ \\(x\\, y1\\, y2\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`curve_fitting <pyQCat.analysis.fit.curve_fitting>`\\"
#~ " \\(data\\, fit\\_options\\, fit\\_model\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`cos <pyQCat.analysis.fit.cos>`\\ \\(x\\[\\, "
#~ "amp\\, freq\\, phase\\, baseline\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`exponential_decay "
#~ "<pyQCat.analysis.fit.exponential_decay>`\\ \\(x\\[\\, "
#~ "amp\\, lamb\\, base\\, x0\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`cos_decay <pyQCat.analysis.fit.cos_decay>`\\ "
#~ "\\(x\\[\\, amp\\, tau\\, freq\\, phase\\, "
#~ "baseline\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`amp2freq_formula "
#~ "<pyQCat.analysis.fit.amp2freq_formula>`\\ \\(x\\, "
#~ "fq\\_max\\, detune\\, M\\, offset\\, d\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`freq2amp_formula "
#~ "<pyQCat.analysis.fit.freq2amp_formula>`\\ \\(x\\, "
#~ "fq\\_max\\, detune\\, M\\, offset\\, d\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`lorentzian <pyQCat.analysis.fit.lorentzian>`\\ "
#~ "\\(f\\, A\\, offset\\, f0\\, kappa\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`skewed_lorentzian "
#~ "<pyQCat.analysis.fit.skewed_lorentzian>`\\ \\(x\\, A1\\,"
#~ " A2\\, A3\\, A4\\, fr\\, Ql\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`bi_lorentz_tilt "
#~ "<pyQCat.analysis.fit.bi_lorentz_tilt>`\\ \\(x\\, a\\, "
#~ "offset\\, b\\, cl\\, cr\\, coe\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`sqrt_lorentzian "
#~ "<pyQCat.analysis.fit.sqrt_lorentzian>`\\ \\(x\\[\\, amp\\,"
#~ " kappa\\, x0\\, baseline\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`twin_lorentzian "
#~ "<pyQCat.analysis.fit.twin_lorentzian>`\\ \\(f\\, "
#~ "A\\_gf\\_over\\_2\\, A\\, ...\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`natural_exp <pyQCat.analysis.fit.natural_exp>`\\ "
#~ "\\(x\\, amp\\, baseline\\, tau\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`amp2cavity_freq_formula "
#~ "<pyQCat.analysis.fit.amp2cavity_freq_formula>`\\ \\(x\\, "
#~ "fq\\_max\\, fc\\, M\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`linear <pyQCat.analysis.fit.linear>`\\ \\(x\\,"
#~ " k\\, baseline\\)"
#~ msgstr ""

#~ msgid ":py:obj:`complex_pole <pyQCat.analysis.fit.complex_pole>`\\ \\(ts\\)"
#~ msgstr ""

#~ msgid ":obj:`cal_rmse <pyQCat.analysis.fit.cal_rmse>`\\ \\(x\\, y1\\, y2\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`curve_fitting <pyQCat.analysis.fit.curve_fitting>`\\ "
#~ "\\(data\\, fit\\_options\\, fit\\_model\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`cos <pyQCat.analysis.fit.cos>`\\ \\(x\\[\\, "
#~ "amp\\, freq\\, phase\\, baseline\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`exponential_decay "
#~ "<pyQCat.analysis.fit.exponential_decay>`\\ \\(x\\[\\, "
#~ "amp\\, lamb\\, base\\, x0\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`cos_decay <pyQCat.analysis.fit.cos_decay>`\\ "
#~ "\\(x\\[\\, amp\\, tau\\, freq\\, phase\\, "
#~ "baseline\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`amp2freq_formula <pyQCat.analysis.fit.amp2freq_formula>`\\"
#~ " \\(x\\, fq\\_max\\, detune\\, M\\, "
#~ "offset\\, d\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`freq2amp_formula <pyQCat.analysis.fit.freq2amp_formula>`\\"
#~ " \\(x\\, fq\\_max\\, detune\\, M\\, "
#~ "offset\\, d\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`lorentzian <pyQCat.analysis.fit.lorentzian>`\\ "
#~ "\\(f\\, A\\, offset\\, f0\\, kappa\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`skewed_lorentzian "
#~ "<pyQCat.analysis.fit.skewed_lorentzian>`\\ \\(x\\, A1\\,"
#~ " A2\\, A3\\, A4\\, fr\\, Ql\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`bi_lorentz_tilt <pyQCat.analysis.fit.bi_lorentz_tilt>`\\"
#~ " \\(x\\, a\\, offset\\, b\\, cl\\, "
#~ "cr\\, coe\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`sqrt_lorentzian <pyQCat.analysis.fit.sqrt_lorentzian>`\\"
#~ " \\(x\\[\\, amp\\, kappa\\, x0\\, "
#~ "baseline\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`twin_lorentzian <pyQCat.analysis.fit.twin_lorentzian>`\\"
#~ " \\(f\\, A2\\, A\\, f2\\, f0\\, "
#~ "kappa2\\, kappa\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`natural_exp <pyQCat.analysis.fit.natural_exp>`\\ "
#~ "\\(x\\, amp\\, baseline\\, tau\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`amp2cavity_freq_formula "
#~ "<pyQCat.analysis.fit.amp2cavity_freq_formula>`\\ \\(x\\, "
#~ "fq\\_max\\, fc\\, M\\, ...\\)"
#~ msgstr ""

#~ msgid ":obj:`linear <pyQCat.analysis.fit.linear>`\\ \\(x\\, k\\, baseline\\)"
#~ msgstr ""

#~ msgid ":obj:`complex_pole <pyQCat.analysis.fit.complex_pole>`\\ \\(ts\\)"
#~ msgstr ""

