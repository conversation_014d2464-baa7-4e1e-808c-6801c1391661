# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/19
# __author:       <PERSON><PERSON><PERSON>

# type: ignore[attribute-error]

from dataclasses import asdict, dataclass, field
from datetime import datetime, timedelta
from typing import Optional

from ..structures import QDict
from .state import TransferTaskStatusEnum, DataTypeEnum
from .util import encode_msg

settings = QDict(
    dispatcher_id="monster dispatch identity",
    transfer_url="transfer",
    transfer_id="monster transfer socket",
    link_url="link",
    link_id="monster link socket",
    data_client_id="monster acq data socket",
    pub_url="pub",
    log_url="log"
)


@dataclass
class TaskStruct:
    require_id: str = ""
    task_id: str = ""
    pointer: Optional[bytes] = None
    program: Optional[bytes] = None
    readout_channels: dict = field(default_factory=dict)
    loop: int = 0
    data_type: Optional[DataTypeEnum] = None
    state: TransferTaskStatusEnum = TransferTaskStatusEnum.INIT
    result: dict = field(default_factory=dict)
    error: Optional[str] = None
    remove_channels: set = field(default_factory=set)
    acq_loops: set = field(default_factory=set)
    retry_start: Optional[datetime] = None
    count: int = 0
    simulator: bool = False
    shot: int = 1000
    register_time: str = None

    def information(self, detail: bool = False):
        info = {
            "require_id": self.require_id,
            "task_id": self.task_id,
            "loop": self.loop,
            "state": self.state.name,
            "count": self.count,
        }
        if detail is True:
            info.update({
                "readout_channels": self.readout_channels,
                "remove_channels": list(self.remove_channels),
                "acq_loops": list(self.acq_loops),
            })
        return info

    def init_result_struct(self):
        if self.readout_channels and self.loop:
            self.result = {}
            for channel in self.readout_channels:
                self.result[str(channel)] = [None for _ in range(self.loop)]

    def is_finish(self):
        if self.readout_channels:
            return len(self.readout_channels) == len(self.remove_channels)
        else:
            self.state in [TransferTaskStatusEnum.SUC, TransferTaskStatusEnum.FAIL]
        
    def is_acq_finish(self):
        return self.loop and len(self.acq_loops) == self.loop

    def is_resend(self):
        if self.state == TransferTaskStatusEnum.RETRY and self.retry_start:
            if datetime.now() >= self.retry_start + timedelta(seconds=60):
                self.retry_start = None
                self.state = TransferTaskStatusEnum.INIT
                return True
            else:
                return False
        else:
            return False

    def to_program(self):
        cr = CompileResult()
        cr.program_buf = self.program
        cr.program_pointer_buf = self.pointer
        return cr

    def __repr__(self):
        return f"Task[RID({self.require_id}) | TID({self.task_id}) | DataType({self.data_type}) | Loop({self.loop})]"
    
    def check_time_out(self, timeout=1):
        cur_time = datetime.now()
        if cur_time - self.register_time >= timedelta(hours=timeout):
            self.register_time = cur_time
            return True
        return False


@dataclass
class TransferSetting:
    dispatcher_url: str = ""
    web_url: str = ""
    log_path: str = ""
    token: str = ""

    def to_bytes(self) -> bytes:
        return encode_msg(asdict(self))


class Heart:
    __slots__ = ("token", "state", "is_restart")

    def __init__(self) -> None:
        self.token = None
        self.state = 0
        self.is_restart = False


class CompileResult:
    __slots__ = (
        "program_buf",
        "status",
        "message",
        "options",
        "require_id",
        "program_pointer_buf",
        "readout_channels",
        "loop",
        "simulator",
        "data_type",
        "shot",
    )

    def __init__(self):
        self.require_id: str = ""
        self.program_buf: bytes = b""
        self.program_pointer_buf: bytes = b""
        self.readout_channels = None
        self.loop: int = 0
        self.status = None
        self.message = None
        self.options = None
        self.simulator = False
        self.data_type = None
        self.shot = None
        
    @classmethod
    def from_dict(cls, data: dict):
        obj = cls()
        for key in cls.__slots__:
            if key in data:
                setattr(obj, key, data[key])
        return obj
