# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/11/08
# __author:       xw

"""
Purity RB Multiple Experiment class.
"""

import itertools
from copy import deepcopy

import numpy as np

from ....analysis import PurityRBAnalysis
from ....analysis.algorithms.tomography import tensor_combinations
from ....analysis.specification import ParameterRepr
from ....structures import MetaD<PERSON>, Options
from ....tools import time_cal
from .multiple_rb import RBMultiple


class PurityRBMultiple(RBMultiple):
    """Purity RB Multiple qubit randomized benchmarking experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()
        options.set_validator("base_gate", list)

        options.base_gate = ["I", "X/2", "Y/2"]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)

        options.depths = None
        options.k = None
        options.quality_bounds = [0.9, 0.85, 0.77]
        options.base_ops = None
        options.use_mle = True
        options.N = 1
        options.data_key = ["trace_rho"]

        options.result_parameters = [
            ParameterRepr(
                name="fidelity", repr="average fidelity", unit="", param_path=""
            ),
            ParameterRepr(name="A", repr="A", unit="", param_path=""),
            ParameterRepr(name="B", repr="B", unit="", param_path=""),
            ParameterRepr(name="U", repr="u", unit="", param_path=""),
        ]

        return options

    def _check_options(self):
        super()._check_options()
        depths = self.run_options.depths
        k = self.experiment_options.times
        counts = 3 ** (2 if self.qubit_pair else 1)
        x_data = np.asarray(depths).repeat(k * counts)

        # set run options
        self.set_run_options(x_data=x_data, analysis_class=PurityRBAnalysis)

    @time_cal
    def _generate_pulse(self):
        experiment_pulses = {}
        quantum_circuits = self.run_options.quantum_circuit_list
        gate_bucket = self.run_options.gate_bucket
        qh = self.run_options.qh
        ql = self.run_options.ql

        for qubit in [qh, ql]:
            experiment_pulses[qubit] = {"XY": [], "Z": []}

        # check base ops and base gate
        base_ops = self.analysis_options.base_ops
        base_gate = self.experiment_options.base_gate
        if base_ops is None:
            base_ops = [gate_bucket.get_matrix(gate) for gate in base_gate]
            if self.qubit_pair is not None:
                self.set_analysis_options(N=2)
                base_ops = tensor_combinations(base_ops, repeat=2)
            self.set_analysis_options(base_ops=base_ops)
        if self.qubit_pair is not None and isinstance(base_gate[0], str):
            base_gate = list(itertools.product(base_gate, repeat=2))
            self.set_experiment_options(base_gate=base_gate)

        # play parking qubit pylse
        base_qubit_list = []
        base_qubit_list.extend(self.qubits)
        base_qubit_list.extend(self.couplers)
        parking_qubits = []

        for bit_name in gate_bucket.cz_gate.parking_qubits:
            for qubit in base_qubit_list:
                if qubit.name == bit_name:
                    parking_qubits.append(qubit)
                    experiment_pulses[qubit] = {"Z": []}
                    break
        exp_gates = []

        for pre_gate in quantum_circuits:
            for gate in self.experiment_options.base_gate:
                pre_gates = deepcopy(pre_gate)
                pre_gates[0].append(gate[0])
                pre_gates[1].append(gate[1])
                exp_gates.append(pre_gates)

        for index, one_loop_gates in enumerate(exp_gates):
            qh_gates, ql_gates = one_loop_gates
            experiment_pulses[qh]["XY"].append(gate_bucket.get_xy_pulse(qh, qh_gates))
            experiment_pulses[qh]["Z"].append(gate_bucket.get_z_pulse(qh, qh_gates))
            experiment_pulses[ql]["XY"].append(gate_bucket.get_xy_pulse(ql, ql_gates))
            experiment_pulses[ql]["Z"].append(gate_bucket.get_z_pulse(ql, ql_gates))
            for qubit in parking_qubits:
                experiment_pulses[qubit]["Z"].append(
                    gate_bucket.get_z_pulse(qubit, qh_gates)
                )

        self.set_run_options(experiment_pulses=experiment_pulses)

    def _metadata(self) -> MetaData:
        """Set RB experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"K": self.experiment_options.times}
        return metadata
