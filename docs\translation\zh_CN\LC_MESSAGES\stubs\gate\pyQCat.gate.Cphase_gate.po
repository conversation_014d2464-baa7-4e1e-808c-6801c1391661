# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/gate/pyQCat.gate.Cphase_gate.rst:2
msgid "pyQCat.gate.Cphase\\_gate"
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ":py:obj:`__init__ <pyQCat.gate.Cphase_gate.__init__>`\\ \\(qc\\, qt\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`to_pulse <pyQCat.gate.Cphase_gate.to_pulse>`\\ "
#~ "\\(base\\_qubit\\)"
#~ msgstr ""

#~ msgid ":py:obj:`info <pyQCat.gate.Cphase_gate.info>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`parking_bit_names "
#~ "<pyQCat.gate.Cphase_gate.parking_bit_names>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`pulses <pyQCat.gate.Cphase_gate.pulses>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`width <pyQCat.gate.Cphase_gate.width>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid "Methods"
#~ msgstr ""

#~ msgid ":obj:`__init__ <pyQCat.gate.Cphase_gate.__init__>`\\ \\(qc\\, qt\\)"
#~ msgstr ""

#~ msgid ":obj:`to_pulse <pyQCat.gate.Cphase_gate.to_pulse>`\\ \\(base\\_qubit\\)"
#~ msgstr ""

#~ msgid "Attributes"
#~ msgstr ""

#~ msgid ":obj:`info <pyQCat.gate.Cphase_gate.info>`\\"
#~ msgstr ""

#~ msgid ":obj:`parking_bit_names <pyQCat.gate.Cphase_gate.parking_bit_names>`\\"
#~ msgstr ""

#~ msgid ":obj:`pulses <pyQCat.gate.Cphase_gate.pulses>`\\"
#~ msgstr ""

#~ msgid ":obj:`width <pyQCat.gate.Cphase_gate.width>`\\"
#~ msgstr ""

