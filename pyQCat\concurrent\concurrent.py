# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/10
# __author:       <PERSON>

"""
Concurrent use api.
"""

import asyncio
import os
import time
from pathlib import Path
from threading import Thread
from typing import Dict, Union, Optional

from pyQCat.concurrent.calculate_resource import ConcurrentCR
from pyQCat.concurrent.merge import MergeClient, MergeService
from pyQCat.concurrent.util import (
    CONCURRENT_CACHE,
    Singleton,
    c_log,
    reset_concurrent_module_cache,
)
from pyQCat.concurrent.data_client import DataClient


def close_experiment_registered():
    DataClient().stop_transfer()
    

def check_concurrent_cache():
    if ConcurrentCR in Singleton._instances and CONCURRENT_CACHE["concurrent_flag"]:
        return True
    return False


def check_merge_service():
    """
    Check if the merge service is currently running.

    This function accepts no arguments.

    Returns:
        bool: Returns True if the merge service thread is alive; otherwise, returns False.
    """
    if (
        CONCURRENT_CACHE["merge_thread"]
        and CONCURRENT_CACHE["merge_service"]
        and CONCURRENT_CACHE["merge_thread"].is_alive()
    ):
        return True
    return False


def start_merge_service(conf_file: Union[str, Path, Dict] = None):
    """
    start merge service
    """

    def _start_merge_service():
        merge_service = MergeService()
        CONCURRENT_CACHE["merge_service"] = merge_service
        asyncio.run(merge_service.run())

    if check_merge_service():
        c_log("Merge service was running, can't start again!")
        return

    thread_service = Thread(
        name="merge service", target=_start_merge_service, daemon=True
    )
    thread_service.start()
    CONCURRENT_CACHE["merge_thread"] = thread_service
    time.sleep(0.1)

    # init client sock
    MergeClient()

    if not check_concurrent_cache():
        ConcurrentCR()


def close_merge_service():
    if CONCURRENT_CACHE["merge_thread"]:
        if CONCURRENT_CACHE["merge_service"]:
            MergeClient().shutdown()
            CONCURRENT_CACHE["merge_service"].close()
        CONCURRENT_CACHE["merge_thread"].join()

    CONCURRENT_CACHE["merge_service"] = None
    CONCURRENT_CACHE["merge_thread"] = None
    CONCURRENT_CACHE["temp_trans"].clear()


def close_concurrent_cache():
    """
    close concurrent cache.
    """
    if check_concurrent_cache():
        ConcurrentCR().shutdown()
    CONCURRENT_CACHE["concurrent_flag"] = False
    if ConcurrentCR in Singleton._instances:
        del Singleton._instances[ConcurrentCR]


def cleanup_resources(close_transfer=True):
    """
    clear all resources.
    """
    close_concurrent_cache()
    close_merge_service()
    reset_concurrent_module_cache()

    client = None

    time.sleep(1)
    ipc_file = f"transferservice{os.getpid()}"
    if os.path.exists(ipc_file):
        client = DataClient()
        client.client_close()
        os.remove(ipc_file)

    if CONCURRENT_CACHE["main_process"] is True and close_transfer is True:
        client = DataClient()
        client.close_transfer()

    if client:
        client.shutdown()


def check_and_start_merge_service(conf_file: Union[str, Path, Dict] = None):
    if not check_merge_service():
        start_merge_service(conf_file)


def check_process_broken_state():
    if CONCURRENT_CACHE["process_broken"] is True:
        cleanup_resources(close_transfer=False)
        c_log("Process pool broken, restart 10 s next")
        time.sleep(10)
        CONCURRENT_CACHE["process_broken"] = False
        return True
    else:
        return False
    
    
def set_global_pulse_period(pulse_period: Optional[int] = None):
    CONCURRENT_CACHE["pulse_period"] = pulse_period
    c_log(f"Set global pulse period to {pulse_period}!")
