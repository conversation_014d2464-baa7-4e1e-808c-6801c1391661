# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/15
# __author:       <PERSON> Fang

"""
ZCrossDelayLinearOnce Analysis.

"""

from typing import List, Union

import numpy as np

from ..algorithms import guess
from ..curve_analysis import CurveAnalysis
from ..fit.fit_models import bz_cosine
from ..specification import CurveAnalysisData, FitModel, FitOptions, ParameterRepr
from ...structures import Options, QDict


class BzRamseyAnalysis(CurveAnalysis):
    """BzRamseyAnalysis class."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""

        options = super()._default_options()

        options.fit_model_name = "bz_cosine"
        options.acf = 100  # AC coefficient, df / dv
        options.rate = -0.001
        options.bz_amp = 0.15
        options.fringe = 50

        options.quality_bounds = [0.95, 0.85, 0.75]
        options.x_label = "Delay [ns]"
        options.result_parameters = [
            ParameterRepr(name="cr", repr="ZCrossCoefficient"),
            ParameterRepr(name="tau", repr="T2*", unit="us"),
        ]
        return options

    def _pre_operation(self):
        """Do some special operation."""
        fit_model_name = self.options.fit_model_name
        acf = self.options.acf
        rate = self.options.rate
        bz_amp = self.options.bz_amp
        fringe = self.options.fringe

        # According to fit_model_name adjust fit_model.
        if fit_model_name == "bz_cosine":
            bz_fit_obj = bz_cosine(acf, rate, bz_amp, fringe)
            fit_model = FitModel(fit_func=bz_fit_obj.fit_model)
        else:
            fit_model = None

        self.set_options(fit_model=fit_model)

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Guess initial fit parameters."""
        x = data.x
        y = data.y

        min_y = min(y)
        max_y = max(y)
        mean_y = np.mean(y)
        g_freq, g_phase, g_amp, g_base = guess.cosine_fit_guess(x, y)
        if isinstance(g_freq, List):
            g_freq = g_freq[0]

        # Set the initial values for each fitted model specifically.
        fit_opt_list = []
        fit_model_name = self.options.fit_model_name
        if fit_model_name == "bz_cosine":
            amp = (min_y + max_y) / 2
            df = 1 / ((x[1] - x[0]) * len(x))
            if g_freq > df:
                alpha = guess.oscillation_exp_decay(x, y - mean_y, freq_guess=g_freq)
            else:
                # Very low frequency. Assume standard exponential decay
                alpha = guess.exp_decay(x, y)
            if alpha != 0.0:
                tau = abs(-1 / alpha)
            else:
                # Likely there is no slope. Cannot fit constant line with this model.
                # Set some large enough number against to the scan range.
                tau = 100 * np.max(x)

            cr = 0.00001
            fit_opt.bounds.set_if_empty(
                amp=(-2 * min_y, 2 * max_y),
                tau=(-np.inf, np.inf),
                cr=(-np.inf, np.inf),
                phase=(-np.pi, np.pi),
                base=(-min_y, max_y),
            )

            fit_params = {"amp": amp, "tau": tau, "cr": cr, "phase": 0.0, "base": min_y}
            for phase in np.linspace(-np.pi, np.pi, 5)[:-1]:
                fit_params.update({"phase": phase})
                new_fit_opt = fit_opt.copy()
                new_fit_opt.p0.set_if_empty(**fit_params)
                fit_opt_list.append(new_fit_opt)

        return fit_opt_list

    def _extract_result(self, data_key: str):
        """Extract the Analysis results from fitted data."""
        super()._extract_result(data_key)
        tau_val = self.results.tau.value
        self.results.tau.value = round(tau_val / 1e3, 3)

    def run_analysis(self):
        """Run analysis on experiment data."""
        self._pre_operation()
        super().run_analysis()
