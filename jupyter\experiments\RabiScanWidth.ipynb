{"cells": [{"cell_type": "markdown", "id": "17ee3eab", "metadata": {}, "source": ["# RabiScanWidth\n", "\n", "Rabi 扫描脉冲宽度\n", "\n", "\n", "## 初始化实验环境"]}, {"cell_type": "code", "execution_count": 1, "id": "3b0a620d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\n"]}], "source": ["# 若用户未安装 pyqcat 包（monster），通过本地源码测试，需要执行下面操作\n", "# 若安装了 pyqcat 包（monster），跳过此步骤\n", "\n", "import os\n", "import sys\n", "\n", "\n", "package_root = \"../../../pyqcat-monster\"\n", "\n", "print(os.path.abspath(package_root))\n", "\n", "sys.path.insert(0, package_root)"]}, {"cell_type": "code", "execution_count": 2, "id": "ac2c4fc3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:25:22\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1m\n", "|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |        tcp://************:8088         |\n", "|    point_label     |              person_point              |\n", "|       sample       |               test_chip                |\n", "|      env_name      |                D00_1011                |\n", "|      version       |                 0.1.6                  |\n", "|=============================================================|\n", "\u001b[0m\n"]}, {"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': '591584438b1a9590dc4dfeb7cfc45715'},\n", " 'msg': 'success'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "\n", "from pyQCat.tools import qarange\n", "from pyQCat.context import ExperimentContext\n", "from pyQCat.invoker import Invoker\n", "from pyQCat.parameters import get_parameters\n", "\n", "\n", "%matplotlib inline\n", "pd.set_option('max_colwidth', None)\n", "\n", "conf_file = r'E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf'\n", "\n", "username = \"test_04\"\n", "password = \"123456\"\n", "\n", "context = ExperimentContext(conf_file)\n", "Invoker.verify_account(username=username, password=password)"]}, {"cell_type": "markdown", "id": "986ad08c", "metadata": {}, "source": ["## 配置实验参数"]}, {"cell_type": "code", "execution_count": 3, "id": "031aef98", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:25:22\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1mQubit_(bit=0) is already exist in current environment.\u001b[0m\n"]}], "source": ["# context 加载实验参数\n", "\n", "q_name_list = [\"q0\"]\n", "environment_elements = [\"q0\", \"q1\", \"q2\", \"q3\", \"c0\", \"c1\", \"c2\"]\n", "\n", "context.configure_inst()\n", "context.configure_qubits(q_name_list)\n", "context.minimize_compensate()\n", "# context.maximize_compensate()\n", "context.configure_environment(environment_elements)\n", "context.configure_crosstalk_dict()\n", "\n", "qubit = context.qubit"]}, {"cell_type": "code", "execution_count": 4, "id": "0eb812e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'1': 0.744732,\n", " '2': 0.236088,\n", " '3': 0.203233,\n", " '4': 0.525287,\n", " '5': 0.0,\n", " '6': 0.0,\n", " '7': 0.0}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看DC\n", "\n", "context.working_dc"]}, {"cell_type": "code", "execution_count": 5, "id": "0348601d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>parameter</th>\n", "      <th>object</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>config</td>\n", "      <td>E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>qubits</td>\n", "      <td>[Qubit_(bit=0)]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>couplers</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>discriminators</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>compensates</td>\n", "      <td>{Qubit_(bit=0): PulseCorrectionQ0}</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>working_dc</td>\n", "      <td>True</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>crosstalk_dict</td>\n", "      <td>True</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        parameter  \\\n", "0          config   \n", "1          qubits   \n", "2        couplers   \n", "3  discriminators   \n", "4     compensates   \n", "5      working_dc   \n", "6  crosstalk_dict   \n", "\n", "                                                                  object count  \n", "0  E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf        \n", "1                                                        [Qubit_(bit=0)]     1  \n", "2                                                                     []     0  \n", "3                                                                   None     0  \n", "4                                     {Qubit_(bit=0): PulseCorrectionQ0}     1  \n", "5                                                                   True        \n", "6                                                                   True        "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看配置信息\n", "pd.DataFrame(context.context_table())"]}, {"cell_type": "markdown", "id": "f919032e", "metadata": {}, "source": ["## 创建实验"]}, {"cell_type": "code", "execution_count": 6, "id": "6c24fb41", "metadata": {}, "outputs": [], "source": ["from pyQCat.experiments import RabiScanWidth\n", "\n", "rsw = RabiScanWidth.from_experiment_context(context)"]}, {"cell_type": "code", "execution_count": 7, "id": "641da96c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>experiment option</td>\n", "      <td>show_result</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_shape</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_data_path</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>experiment option</td>\n", "      <td>repeat</td>\n", "      <td>1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>experiment option</td>\n", "      <td>register_pulse_save</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_flag</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_save</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_measure</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_type</td>\n", "      <td>envelop</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_measure</td>\n", "      <td>150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_real</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_index</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>experiment option</td>\n", "      <td>save_label</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>experiment option</td>\n", "      <td>is_dynamic</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>experiment option</td>\n", "      <td>fidelity_matrix</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>experiment option</td>\n", "      <td>ac_prepare_time</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>experiment option</td>\n", "      <td>readout_point_model</td>\n", "      <td>&lt;class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>experiment option</td>\n", "      <td>idle_qubits</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>experiment option</td>\n", "      <td>widths</td>\n", "      <td>[5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100, 105, 110, 115, 120, 125, 130, 135, 140, 145, 150, 155, 160, 165, 170, 175, 180, 185, 190, 195, 200]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>experiment option</td>\n", "      <td>drive_freq</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>experiment option</td>\n", "      <td>drive_power</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>analysis option</td>\n", "      <td>is_plot</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>analysis option</td>\n", "      <td>figsize</td>\n", "      <td>(12, 8)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>analysis option</td>\n", "      <td>quality_bounds</td>\n", "      <td>[0.98, 0.95, 0.91]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 type                   name  \\\n", "0   experiment option            show_result   \n", "1   experiment option        simulator_shape   \n", "2   experiment option    simulator_data_path   \n", "3   experiment option                 repeat   \n", "4   experiment option    register_pulse_save   \n", "5   experiment option          schedule_flag   \n", "6   experiment option          schedule_save   \n", "7   experiment option       schedule_measure   \n", "8   experiment option          schedule_type   \n", "9   experiment option  schedule_show_measure   \n", "10  experiment option     schedule_show_real   \n", "11  experiment option         schedule_index   \n", "12  experiment option             save_label   \n", "13  experiment option             is_dynamic   \n", "14  experiment option        fidelity_matrix   \n", "15  experiment option        ac_prepare_time   \n", "16  experiment option    readout_point_model   \n", "17  experiment option            idle_qubits   \n", "18  experiment option                 widths   \n", "19  experiment option             drive_freq   \n", "20  experiment option            drive_power   \n", "21    analysis option                is_plot   \n", "22    analysis option                figsize   \n", "23    analysis option         quality_bounds   \n", "\n", "                                                                                                                                                                                   value  \n", "0                                                                                                                                                                                   True  \n", "1                                                                                                                                                                                   None  \n", "2                                                                                                                                                                                   None  \n", "3                                                                                                                                                                                   1000  \n", "4                                                                                                                                                                                  False  \n", "5                                                                                                                                                                                   True  \n", "6                                                                                                                                                                                   True  \n", "7                                                                                                                                                                                   True  \n", "8                                                                                                                                                                                envelop  \n", "9                                                                                                                                                                                    150  \n", "10                                                                                                                                                                                  True  \n", "11                                                                                                                                                                                    -1  \n", "12                                                                                                                                                                                  None  \n", "13                                                                                                                                                                                     1  \n", "14                                                                                                                                                                                  None  \n", "15                                                                                                                                                                                     0  \n", "16                                                                                                                                      <class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'>  \n", "17                                                                                                                                                                                    []  \n", "18  [5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100, 105, 110, 115, 120, 125, 130, 135, 140, 145, 150, 155, 160, 165, 170, 175, 180, 185, 190, 195, 200]  \n", "19                                                                                                                                                                                  None  \n", "20                                                                                                                                                                                  None  \n", "21                                                                                                                                                                                  True  \n", "22                                                                                                                                                                               (12, 8)  \n", "23                                                                                                                                                                    [0.98, 0.95, 0.91]  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(rsw.options_table())"]}, {"cell_type": "markdown", "id": "9d74de63", "metadata": {}, "source": ["**experiment options**\n", "\n", "*辅助功能选型*\n", "- simulator_data_path (str): 模拟数据地址\n", "- simulator (bool): 是否开启模拟器\n", "- repeat (int): 小循环次数\n", "- fidelity_matrix (np.ndarray): 保真度矩阵，支持外部传入\n", "- show_result (bool): 是否在日志中展示分析结果\n", "- save_lable (str): 采集数据存储标签，用于生成文件名，默认为空\n", "- is_dynamic (int): 是否进行动态绘图，默认为1，单比特读取动态绘图，0关闭动态绘图\n", "\n", "*脉冲时序选项*\n", "- schedule_flag (bool): 是否绘制脉冲时序图\n", "- schedule_save (bool): 是否存储脉冲时序图\n", "- schedule_measure (bool): 是否绘制测量波形\n", "- schedule_type (str): 脉冲时序图的类型，支持 squence 和 envelop 两种\n", "- schedule_index (int or list(int)): 绘制脉冲时序图的索引\n", "- register_pulse_save (bool): 波形存储数据\n", "\n", "*实验参数选项*\n", "\n", "- drive_freq (float): 驱动频率\n", "- drive_power (float): 驱动功率\n", "- widths (list): 扫描脉冲宽度\n", "\n", "\n", "**analysis options**\n", "- quality_bounds (list): 拟合质量评估阈值"]}, {"cell_type": "code", "execution_count": 8, "id": "6339aa2f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:25:23\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mRabiScanWidth register success, id 63492b03708eb4e480bd23bf\u001b[0m\n", "\u001b[33m2022-10-14 17:25:23\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\test_chip\\RabiScanWidth\\q0\\2022-10-14\\17.25.22\\\u001b[0m\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e4ac11d503c64ce19076e1dd0f1c05d9", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/40 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:25:25\u001b[0m | \u001b[32m RESULT \u001b[0m | \u001b[32mExperiment results as follow:\n", "=================================================================\n", "|    name     |  describe   | value | unit |      quality       | \n", "-----------------------------------------------------------------\n", "| oscillating | oscillating | True  | None | R²=0.9893(perfect) | \n", "=================================================================\u001b[0m\n"]}], "source": ["rsw.set_experiment_options(\n", "    drive_freq=5540.357,\n", "    drive_power=-20,\n", "    simulator_data_path='../../scripts/simulator/data/RabiScanWidth'\n", ")\n", "\n", "rsw.run()"]}, {"cell_type": "code", "execution_count": 9, "id": "98fe701d", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA4QAAAJYCAYAAAA6xSjbAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/av/WaAAAACXBIWXMAAA9hAAAPYQGoP6dpAACCrUlEQVR4nO3dd3iUVcL+8XvSJo0USEINoYOAFQFBERAUKVYsu+u6WNa2ur/tq7jvrm1X1rqvq2tbX9e6ig0VFQuIICigKEUp0jukkF4mycz5/REzTs8kTDIhz/dzXbkgM2fOc555ZmDuOc1mjDECAAAAAFhOTLQbAAAAAACIDgIhAAAAAFgUgRAAAAAALIpACAAAAAAWRSAEAAAAAIsiEAIAAACARREIAQAAAMCiCIQAAAAAYFEEQgAAAACwKAIhAAAAAFgUgRAAAAAALIpACAA++vTpoxkzZrT6cXbu3CmbzaZnnnmmRY+32Wy6/fbbI9qmSLviiiuUmpoaVtmj4XwiLdxzvv3222Wz2cKqs7FsYWHhEbVt2rRpuuaaa46ojrb2zDPPyGazaefOna12jA0bNiguLk7ffPNNqx0DANoSgRBAh7B+/XpddNFFysvLU2Jionr27KkzzzxTDz/8cLSbdtTo06ePbDab+yclJUWjRo3Sc889F7U2tdfrmp+fL5vNpl/96ld+9/3qV7+SzWbTbbfd5nffz372M8XHx6uqquqI23D33XfrzTffPOJ6Alm+fLk+/PBD3Xzzza1S/9Fs6NChmj59uv7yl79EuykAEBFx0W4AABypzz77TBMnTlTv3r11zTXXqFu3btqzZ49WrFihhx56SL/85S+j3cSjxgknnKDf/e53kqQDBw7oqaee0qxZs+RwOFq9t6i6ulpxcT/8t9Ser2tOTo4GDhyoZcuW+d23fPlyxcXFafny5QHvO/HEE5WcnCzJ/5yb4+6779ZFF12k888/v0WPD+W+++7TpEmTNGDAgIjX3RFcf/31mjZtmrZt26b+/ftHuzkAcEQIhACOen/729+Unp6uL774QhkZGV735efnR6dRR6mePXvqpz/9qfv3K664Qv369dM//vGPVg+EiYmJXr+39+t62mmn6bnnnlNFRYV7WGxlZaXWrl2rSy65RG+//bacTqdiY2MlNQTs7du367zzznPX4XvO7UF+fr7effddPf7449FuSrs1efJkZWZm6tlnn9Wdd94Z7eYAwBFhyCiAo962bds0bNgwv9AgNfTk+HrhhRc0atQoJScnKzMzU6effro+/PBDv3LLli3TqFGjlJiYqH79+gUcOllSUqJf//rXys3Nld1u14ABA3TPPffI5XL5lbviiiuUnp6ujIwMzZo1SyUlJX71TZgwQRMmTPC7/YorrlCfPn2CPgeN9u3bp6uuukpdu3aV3W7XsGHD9PTTTzf5uGCys7M1ZMgQbdu2zev2Tz/9VBdffLF69+4tu92u3Nxc/eY3v1F1dXXAerZv364pU6YoJSVFPXr00J133iljjFcZ3/l0kb6ub731lqZPn64ePXrIbrerf//+uuuuu+R0Or3qmTBhgoYPH64NGzZo4sSJSk5OVs+ePXXvvfd6lTvttNPkdDq1YsUK920rV65UfX29fv/736uiokJr1qxx39fYY3jaaacFPWep4XU3cuRIJSYmqn///nriiSf8ztVms6myslLPPvuse4jvFVdc4VWm8TWXkZGh9PR0XXnllWENVX333XdVX1+vyZMn+90Xzuu9cW7s/fffryeffFL9+/eX3W7XyJEj9cUXX7jL3X///bLZbNq1a5ffcWbPnq2EhAQVFxe7b1u5cqXOPvtspaenKzk5WePHjw/YCxvIo48+qmHDhslut6tHjx668cYb/d5/jdd99erVGjt2rJKSktS3b9+AwTg+Pl4TJkzQW2+9FdbxAaA9IxACOOrl5eVp9erVYS3ycMcdd+jyyy9XfHy87rzzTt1xxx3Kzc3Vxx9/7FVu69atuuiii3TmmWfqgQceUGZmpq644gp9++237jJVVVUaP368XnjhBf3sZz/TP//5T5166qmaPXu2fvvb37rLGWN03nnn6fnnn9dPf/pT/fWvf9XevXs1a9asyD0Jkg4dOqRTTjlFCxcu1E033aSHHnpIAwYM0NVXX63//d//bVGd9fX12rt3rzIzM71uf/XVV1VVVaUbbrhBDz/8sKZMmaKHH35YP/vZz/zqcDqdOvvss9W1a1fde++9GjFihG677baAc+w8Rfq6PvPMM0pNTdVvf/tbPfTQQxoxYoT+8pe/6JZbbvGrr7i4WGeffbaOP/54PfDAAxoyZIhuvvlmLViwwF2mMdh5Dhtdvny5Bg0apBNPPFG9evXyCiyBAqGv9evX66yzzlJ+fr5uv/12XXnllbrttts0b948r3LPP/+87Ha7xo0bp+eff17PP/+8rrvuOq8yl1xyicrLyzVnzhxdcskleuaZZ3THHXc0+Vx+9tln6tKli/Ly8rxuD/f13ui///2v7rvvPl133XX661//qp07d+rCCy9UXV2du302m02vvPKK32NfeeUVnXXWWe7X3ccff6zTTz9dZWVluu2223T33XerpKREZ5xxhlatWhXyfG6//XbdeOON6tGjhx544AHNnDlTTzzxhM466yx3WxoVFxdr2rRpGjFihO6991716tVLN9xwQ8AvVUaMGKFvvvlGZWVloZ9QAGjvDAAc5T788EMTGxtrYmNjzZgxY8wf//hH88EHH5ja2lqvclu2bDExMTHmggsuME6n0+s+l8vl/nteXp6RZJYuXeq+LT8/39jtdvO73/3Ofdtdd91lUlJSzHfffedV1y233GJiY2PN7t27jTHGvPnmm0aSuffee91l6uvrzbhx44wk85///Md9+/jx48348eP9znHWrFkmLy/P6zZJ5rbbbnP/fvXVV5vu3bubwsJCr3I/+tGPTHp6uqmqqvKr11NeXp4566yzTEFBgSkoKDDr1683l19+uZFkbrzxRq+ygeqaM2eOsdlsZteuXV7tlmR++ctfum9zuVxm+vTpJiEhwRQUFAQ9n0hf10Btvu6660xycrKpqalx3zZ+/HgjyTz33HPu2xwOh+nWrZuZOXOm1+NzcnLMpEmT3L9PmTLFXHnllcYYYy655BJz8cUXu+87+eSTzcCBA70e73vO559/vklMTPR6Djds2GBiY2ON73/ZKSkpZtasWX7ndNtttxlJ5qqrrvK6/YILLjBdunTxK+/rtNNOMyNGjPC7PdzX+44dO4wk06VLF3P48GF3ubfeestIMvPnz3ffNmbMGL9jrVq1yuv5d7lcZuDAgWbKlCl+17Nv377mzDPPdN/2n//8x0gyO3bsMMY0vG8TEhLMWWed5fXaeOSRR4wk8/TTT7tva7zuDzzwgPs2h8NhTjjhBJOTk+P3uvvvf/9rJJmVK1cGeSYB4OhADyGAo96ZZ56pzz//XOeee67Wrl2re++9V1OmTFHPnj319ttvu8u9+eabcrlc+stf/qKYGO9//nyX9B86dKjGjRvn/j07O1uDBw/W9u3b3be9+uqrGjdunDIzM1VYWOj+mTx5spxOp5YuXSpJeu+99xQXF6cbbrjB/djY2NiILopijNHrr7+uc845R8YYr/ZMmTJFpaWl+uqrr5qs58MPP1R2drays7N17LHH6vnnn9eVV16p++67z6tcUlKS+++VlZUqLCzU2LFjZYzR119/7VfvTTfd5P67zWbTTTfdpNraWi1cuDBoWyJ9XT3bXF5ersLCQo0bN05VVVXatGmT1+NSU1O95lImJCRo1KhRXtdfkk499VStXLlSTqdTLpdLK1as0NixY933NfYKVlVVac2aNSF7B51Opz744AOdf/756t27t/v2Y445RlOmTAn6uGCuv/56r9/HjRunoqKiJnu0ioqK/HqEpfBf740uvfRSr3oa30+ez+Gll16q1atXew1Jnjt3rux2u3uu5Zo1a7Rlyxb95Cc/UVFRkfu4lZWVmjRpkpYuXeo3RLvRwoULVVtbq1//+tder41rrrlGaWlpevfdd73Kx8XFefW0JiQk6LrrrlN+fr5Wr17tVbbx3I50ew8AiDYCIYAOYeTIkXrjjTdUXFysVatWafbs2SovL9dFF12kDRs2SGqYkxYTE6OhQ4c2WZ/nB/JGmZmZXnOatmzZovfff98doBp/GudeNS58smvXLnXv3t1vP77Bgwe3+Hx9FRQUqKSkRE8++aRfe6688kqv9oQyevRoffTRR3r//fd1//33KyMjQ8XFxUpISPAqt3v3bl1xxRXq3LmzUlNTlZ2drfHjx0uSSktLvcrGxMSoX79+XrcNGjRIkprcLy6S1/Xbb7/VBRdcoPT0dKWlpSk7O9sd+nzb3KtXL78vCXyvv9Qw/LNxruA333yj0tJSnXrqqZKksWPHav/+/dq5c6d7bmGoQFhQUKDq6moNHDjQ776WvFZ8X8ONAcb3HAIxPvM7pfBf7805/sUXX6yYmBjNnTvXfdxXX31VU6dOVVpamvu4kjRr1iy/Yz/11FNyOBx+169R4/xE3+cvISFB/fr185u/2KNHD6WkpHjdFuy12vgchbs/JAC0V6wyCqBDSUhI0MiRIzVy5EgNGjRIV155pV599dUm56v5alwZ0pfnB2WXy6UzzzxTf/zjHwOWbfwg2Rw2my3gh3HfhU98NfaQ/PSnPw06N/G4445r8vhZWVnuD/hTpkzRkCFDNGPGDD300EPueWJOp1NnnnmmDh8+rJtvvllDhgxRSkqK9u3bpyuuuCJob82RONLrWlJSovHjxystLU133nmn+vfvr8TERH311Ve6+eab/doczvWXvOcRJiQkqHPnzhoyZIikhi08kpOTtWzZMu3YscOrfFsI9xx8denSJWBobO7rPZzj9+jRQ+PGjdMrr7yiW2+9VStWrNDu3bt1zz33eB1XatgK44QTTghYp++XLW2h8TnKyspq82MDQCQRCAF0WCeffLKkhuX+Jal///5yuVzasGFD0A+WzdG/f39VVFQEXI3RU15enhYtWuS1PYEkbd682a9sZmam37BESQFXYvSUnZ2tTp06yel0Ntme5pg+fbrGjx+vu+++W9ddd51SUlK0fv16fffdd3r22We9FpH56KOPAtbhcrm0fft2r8Dw3XffSVJYK6f6asl1/eSTT1RUVKQ33nhDp59+uvv2xqDWUieddJI79Nntdo0ZM8bdYxQXF6eRI0dq+fLl2rFjh3JyckJ+SZCdna2kpCR3j5inQK+V1uqZGjJkiF5//XW/28N9vTfXpZdeql/84hfavHmz5s6dq+TkZJ1zzjlex5WktLS0Zh+7cWGczZs3e/VS19bWaseOHX717d+/X5WVlV69hMFeqzt27FBMTEyLvvgBgPaEIaMAjnqLFy8O2Ovx3nvvSfphuNj555+vmJgY3XnnnX49Qk31mgRyySWX6PPPP9cHH3zgd19JSYnq6+slSdOmTVN9fb0ee+wx9/1Op1MPP/yw3+P69++vTZs2qaCgwH3b2rVrm1xePzY2VjNnztTrr78ecFVOz/qa6+abb1ZRUZH+/e9/u48leT9nxhg99NBDQet45JFHvMo+8sgjio+P16RJk4I+JpLXNVCba2tr9eijjwY9fjji4uI0evRoLV++XMuXL3fPH2w0duxYLV26VCtWrHAPJQ0mNjZWU6ZM0Ztvvqndu3e7b9+4cWPA11hKSkrArUuO1JgxY1RcXOz3xUS4r/fmmjlzpmJjY/XSSy/p1Vdf1YwZM7wC2YgRI9S/f3/df//9qqio8Ht8qNf25MmTlZCQoH/+859e1/7//u//VFpaqunTp3uVr6+v99rmo7a2Vk888YSys7M1YsQIr7KrV6/WsGHDlJ6e3uxzBoD2hB5CAEe9X/7yl6qqqtIFF1ygIUOGqLa2Vp999pnmzp2rPn36uOfQDRgwQH/605901113ady4cbrwwgtlt9v1xRdfqEePHpozZ06zjvuHP/xBb7/9tmbMmKErrrhCI0aMUGVlpdavX6/XXntNO3fuVFZWls455xydeuqpuuWWW7Rz504NHTpUb7zxRsB5T1dddZUefPBBTZkyRVdffbXy8/P1+OOPa9iwYU0uBvL3v/9dixcv1ujRo3XNNddo6NChOnz4sL766istXLhQhw8fbtb5NZo6daqGDx+uBx98UDfeeKOGDBmi/v376/e//7327duntLQ0vf7660HnpiUmJur999/XrFmzNHr0aC1YsEDvvvuubr31VmVnZwc9biSv69ixY5WZmalZs2bp//2//yebzabnn3++RV8E+DrttNO0ePFiSfILfWPHjnW/rsIZLnrHHXfo/fff17hx4/SLX/xC9fX1evjhhzVs2DCtW7fOq+yIESO0cOFCPfjgg+rRo4f69u2r0aNHH/H5TJ8+XXFxcVq4cKGuvfZa9+3hvt6bKycnRxMnTtSDDz6o8vJyXXrppV73x8TE6KmnntLUqVM1bNgwXXnllerZs6f27dunxYsXKy0tTfPnzw9Yd3Z2tmbPnq077rhDZ599ts4991xt3rxZjz76qEaOHOm1cJDUMIT1nnvu0c6dOzVo0CDNnTtXa9as0ZNPPqn4+Hh3ubq6Oi1ZskS/+MUvmn2+ANDutOmapgDQChYsWGCuuuoqM2TIEJOammoSEhLMgAEDzC9/+Utz6NAhv/JPP/20OfHEE43dbjeZmZlm/Pjx5qOPPnLfn5eXZ6ZPn+73uEBbQpSXl5vZs2ebAQMGmISEBJOVlWXGjh1r7r//fq9l6ouKiszll19u0tLSTHp6urn88svN119/7bfthDHGvPDCC6Zfv34mISHBnHDCCeaDDz4Ia9sJY4w5dOiQufHGG01ubq6Jj4833bp1M5MmTTJPPvlkk89jsPM2xphnnnnGq60bNmwwkydPNqmpqSYrK8tcc801Zu3atX7nM2vWLJOSkmK2bdtmzjrrLJOcnGy6du1qbrvtNr8tInzPJ9LXdfny5eaUU04xSUlJpkePHu5tLCSZxYsXu8uNHz/eDBs2zK/+QNfAGOOuIy4uzlRWVnrdV1RUZGw2W9DtCQJdwyVLlpgRI0aYhIQE069fP/P444+7t5LwtGnTJnP66aebpKQkI8m9BUVjWc8tPYzx35IhlHPPPddrO41G4bzeG7eduO+++8I6X2OM+fe//20kmU6dOpnq6uqAbfr666/NhRdeaLp06WLsdrvJy8szl1xyiVm0aFGT5/jII4+YIUOGmPj4eNO1a1dzww03mOLiYq8yjdf9yy+/NGPGjDGJiYkmLy/PPPLII35tWbBggZFktmzZErCtAHA0sRkTga9HAQBAh/Hpp59qwoQJ2rRpU8BVTzuiCRMmqLCwMOCQa1/nn3++bDab5s2b1wYtA4DWxRxCAADgZdy4cTrrrLN07733Rrsp7c7GjRv1zjvv6K677op2UwAgIphDCAAA/CxYsCDaTWiXjjnmmBYvoAMA7RE9hAAAAABgUcwhBAAAAACLoocQAAAAACyKQAgAAAAAFkUgBAAAAACLIhACAAAAgEURCAEAAADAogiEAAAAAGBRBEIAAAAAsCgCIQAAAABYFIEQAAAAACyKQAgAAAAAFkUgBAAAAACLIhACAAAAgEURCAEAAADAogiEAAAAAGBRBEIAAAAAsCgCIQAAAABYVFy0G2AlDodDDofD/bsxRrW1tcrKypLNZotiywAAAABYET2EbWjOnDlKT093/2RkZCgnJ0fl5eXRbhoAAAAAC7IZY0y0G2EVvj2EZWVlys3NVWlpqdLS0qLYMgAAAABWxJDRNmS322W326PdDAAAAACQxJBRAAAAALAsAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAi4qLdgOsxOFwyOFwuH8vKyuLYmsAAAAAWB09hG1ozpw5Sk9Pd//k5uZGu0kAAAAALMxmjDHRboRVBOohzM3NVWlpqdLS0qLYMgAAAABWxJDRNmS322W326PdDAAAAACQxJBRAAAAALAsAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKLiot0AADgaGWPkqHfJUedSrdOluu9/6l1G8TExio+zKSE2RvFxMbLHxcgeFxvtJgMAAPghEAKAj9LqOu0trtLe4mrtK67W3uJqHSit1uHKWpVU1am4quHPWqcr7DqT4mOVmRyvjOQEZabEq3OKXT0zktQzM0m9MpPUKyNJvTKTlZRAcAQAAG3HsoFw3759ev311zXvzbe0Z+9eVVRUtqie2NhYZWSk6+STTtTFF1+sM888U3a7PcKtBdAaauqc2nigTN8dKtfmgxUNfx4qV0G5o9l1xdpsio2xyWaTXMbI6TJymR/ur65zqrrUqf2lNSHrye2cpMFdO2lQ104a3K3hZ2BOJ8XG2JrdJgAAgKbYjDGm6WIdh9Pp1PXXX6+nnnpKMbFxSuxzomIzeygmIVlqwect43LKVV2u+n3fqqZgt9LS0/X6a69p8uTJTT62rKxM6enpKi0tVVpaWgvOBkC4jDHac7haX+8p1te7S/T17mJtOFCmOmfgfwKT4mOVlhSnTonxSkts+DMpPlaJ8THf/xkre3yM4mJiFGOTbDb/f0CMMXIao3qnUU2dUzV1ru//dKqq1qmymjqV19SrrKZOZdX1QXscUxJidVyvDJ3YO0Mn5GbopLxMZaXyxRMAADhylgqETqdTV111tZ5//nllTLxaqcdOUkxiakTqNsaornCXSj/5j+r3fat335nfZCgkEAKtq6Dcoc+2FWr51kIt31qkfSXVfmWS4mOV3cmuLikJ6pKaoC4pdnVOSVBCXNuvuVVVW6/DlbUqqqhVUWWtiiocKqyoDRgUB3ftpFMHZOm0gV00qm8XpdotO+ADAAAcAUsFwqeeekrXXHutsmb8XilDx7fKMUx9rQrn/U06tFkF+YeUnJwctCyBEIgsl8to7d4SfbThkD7elK9NB8u97o+xSTmdEtUtPVHd0hLVPT1RnRLjAvbutRcuY1RcWasDZTU6WNrwU1RZ61UmLsamE3tnaNIxXXXm0K7qnx2ZL7oAAEDHZ6lAeMYZk7Ryx2FlX/rXVj1O3eF92v/v6/Taa69p5syZQcsRCIEjV1vv0vKthfpww0Et3JjvN/8vu5NdvTOTlds5ST0ykhQfe/TvtlNd69Te4irtLq7SnsPVKq2u87q/X3aKzhzaVWcN7aaTeme068ALAACiyzKBsKCgQN26dVPGmTeo0wlTW/14+c/+P51z+kjNnfty0DIEQqBlXC6jlTsO6+21+/Xe+gNegSghNkZ5XZLVLztFeZ1TLLFqZ2l1nXYWVmp7YaX2Fld5LWbTKzNJ5x7fQ+ee0ENDuvHvDAAA8GaZSSfr1q2Ty+VSYu/j2uR4cbnHaeUXX7bJsQCr2HKoXK98uUfz1x7QwbIfVutMTohV/+xU9c9OUc/MJMXFHP29gM2RnhSv43MzdHxuhhz1Tu0qqtK2ggrtKKzU3uJqPfrJNj36yTYN7tpJ557QQxeN6KWuaYnRbjYAAGgHLBMIS0pKJEkxSW3zDXlsUppKSorb5FhAR1Zd69S76w/opVW7tXrXD++phLgYDcxJ1eCundQzM0kxDIuUJNnjYjXo+20r6pwu7Sys1OZD5dpZWKXNh8p13web9eBH32ni4GxdOrK3Jg7OVlwHGEYLAABaxjKBsL6+XpJki22jU46Jk/P7YwJovm/3l+rlVXv05pp9Kq/5/v1rk/plpeiY7mnK65JsuZ7A5oqPjdHArp00sGsnOeqc2lJQoY37y7S/tEYLN+Zr4cZ85XSy6+KTe+mnp+Spe3pStJsMAADamGUCIYD2z+ky+vDbg/q/ZTv0pUdvYFpinIb1TNew7mlKYXuFFrHHx2p4j3QN75Guw5W1+nZ/qTYeKFd+uUP/WrxNjy/ZrrOHd9NVp/bRSb0zWYgGAACL4JOVh1umDtEDH24OulE1gNZRXlOnuV/s0TOf7dTe4oa9AmNsUv/sVA3vma7czCQCSgR1TknQuIHZGts/S9sLKrR2b6n2lVTr3XUH9O66AzquV7quPLWPph/bIyr7MQIAgLZDIPQwdXg3TRicrd/OXasNB8qi3Rygw9tbXKWnl+3UK1/uUYWjYVhoYnyMju2ZruN7ZdAb2MpiY2zuIaUF5Q6t2VOizYfKtW5vqX4zd63ufm+Trj6try4b3VudEuOj3VwAANAK+LTlYcr/LtWt047RG78Yq0cWb9W/Fm9VJDflcDgccjh+2COtrIzQCWvaXlChRz/Zpje/3qf67/dI6JycoBN6Z2hIt04dYq/Ao012J7vOHNpVpw7oom/2lWndvhIVlDv09wWb9OjirbpibB9dcWpfdU5JiHZTAQBABBEIPdTUufSXt77VgvUHdc/M4zR5SI4eW7JNTpd3Kly4Mb9F9c+ZM0d33HFHJJoKHJU2HSzTvxZv07vr9rv3ysvNTNJJeZnK65zMsNB2IDkhTqP6dtaIvExtPlSuL3ceVnFVnf758Vb9+9Md+vGo3rrm9L4sQAMAQAdhmY3p586dqx/96EfK/c2riklo+oPM5GNy9PhPR/gtZW8k9b/1vSYfX7ryDdWvfk3lZaXu2wL1EObm5rIxPTq89XtL9fDHW/ThhkPu2/pmpWhUn87qls5+eO2ZMUbbCir1xc7Dyi9v+PcrPtamS07O1U1nDCAYAgBwlKOH0Ic9Lka3TB2in4zqrYc/3qqHP94iV4Qis91ul91uj0xlwFFgy6FyPfDhd3r/24Pu2wbmpGpkn87K7sR74Whgs9k0ICdV/bNTtPtwlb7YWax9JdV6ceVuvbp6ry4b3Vu/mDCA6wkAwFGKQOjhpN6ZeuCS41Vb79LMxz/TN/uY4we0xJ7DVfrHwu/05tf73F+oDOnWSSP7dGYO2lHKZrMpr0uK8rqkaF9xtT7bXqj9JTX6z/KdennVHs0a20fXnd5PmVxfAACOKgRCDy9fe4qe/Xyn7nt/s2qdrmg3Bzjq5JfV6JHFW/XSqt3u7Vv6Z6doTL8u6pJKD1JH0TMzSRed1Eu7D1dpxfbDOlhWo8eXbNMLK3bp5+P66ppx/VghFgCAowT/Y3v4ctdh/X3BJr9FZBr1SE/UPRcdp8v/b1Ubtwxo30qr6/T4km36z/Idqqlr+DKld+dkjenfRd3SmCPYETX2GPbunKydRVX6fHuRCsod+t+FW/Tiyt367ZmDdPGIXopjxVgAANo1AqGHvC4peuvGU/XbV9bou0MVXvf9ZFRvzZ42RKt3FUepdUD7U+d06b8rd+t/F36n4qo6SVK3tESN7d9FuZ2To9w6tAWbzaa+WSnq0yVZW/MrtHxbQzCc/cZ6Pb1sh26ZOkRnDMlhBVkAANopAqGHKf9YqjvOG6b5N52mhxZt0WNLtql7WqLuveh4HZebrrvf26iXVu2JdjOBqDPGaNHGfN29YKO2F1RKkjqnJOjU/l3UNyuFD/8WZLM1bHLfLztV6/aWaNWOw9qSX6Grn/1Sp/TrrFunHaPjemVEu5kAAMAHgdBDhaNev3tlrd7/5qDuvmC4ZhzXXb06J2vtnhJN/d9Pta+kOtpNBKLu2/2l+tu7G/XZtiJJUlJ8rE7p11nDe6QrJoYgaHWxMTad2DtTQ7un6YtdxVqzp0Qrth/WuY8s13kn9NDNZw9Rjwy2qgAAoL0gEAbw9e5ibTpYrlP7Z6mqzqlHPt5KGITlHSqr0f0fbNZrX+2VMQ0f/E/IzdDIPpmyx8VGu3loZ+zxsTptQJaO65Wuz7cVadPBcr21Zr8+/PaQfjGhv645vZ8S43ndAAAQbcz293Hu8T300W/GK8Zm0+QHl+iFFbv03NWj9OcZx8gex9MF66mtd+nxJds08f5P9OrqhjA4qGuqfnZKnk4bkEUYREhpifGaMqybfjwyVz3SE1Vd59QDH32nMx9cog++PShjIrTRKwAAaBF6CD089tOTdPrAbN37wWY9+9lOSdLfF2zSh98e0n0XH6cJg3P0h1fX6qvdJVFtJ9BWln5XoNvnf+ueJ9gtLVGnD8pS93SG/KF5ctISddGIXvruUIWWbS3UnuJqXff8ap02IEu3nTNUA7t2inYTAQCwJOt1eYX4Njo71a7p//zUHQYbfbW7WNMe+lRLvyvQy9eOCfNALhbWwFFrb3GVrn9+tX729CptL6hUUnyszhzaVZec3IswiBaz2Wwa3K2TLj8lTyP7ZCrWZtOyrYU6+6FPdef8DSqtrot2EwEAsBzL9BB26tTw7bOrtkox9sDL4V/8xOdB86Kj3qU75m/Qgm8OhnU8l6NaqZ3SWtRWIFpq6px6cul2/WvxVjnqXbLZpON7ZeiUfp0ZGoqISYiL0dj+WRraPU2fbinU9sJKPb18h95as083nz1EF43oxQJFAAC0EcsEwt69e0uS6gp2Ka5TVsAy4UxlWbXjcFjHcxbtUl7v3LDbB0RT4zYSd76zQbsPV0mSemYkacLgbGWl2qPcOnRUGckJOuf4HtpVVKkl3xWoqLJWf3x9neZ+uUd/PX+4junOl2oAALQ2ywwZHTZsmPL69lPV5uWtfiyXo0rV27/SRTMvbPVjAUdqb3GVrnnuS/38uS+1+3CVUu1xmjq8m2ae1JMwiDaR1yVFl43O07gBWYqPtWn1rmLNeHiZ/vrOBlU46qPdPAAAOjTLBEKbzaYfX3qJHFs/V315Yaseq2Ldh3LV1+qiiy5q1eMAR6LO6dITS7bpzAeXauHGfMXYpBF5mbr8lDwN6tqJObBoU7ExNp30/etvQHaqnC6jp5bt0OQHlmjB+gOsRgoAQCuxGQv9L7tnzx6dMmasDtcYdbn0b0GHjh6JivWLVLTgf3Xdtdfq8ccfD1m2rKxM6enpKi0tVVoaQ6PQdlbvKtaf5q3XpoPlkhqGh04cnK0u9AiindhRWKlPNuerrKahh3Di4Gzdce5w9e4SeA44AABoGUsFQknavn27xp0+XocKCpXY72QlDTpV8V16ypaQ3KIeEeOsl6umQjW718vx3TJVH9iqn//853riiScUExO6A5ZAiLZWWlWnez7YpJdW7ZYxUmJ8jE4b0LC4Bz2CaG/qnS59sbNYX+46LJeR7HExumniAF07vh+LHAEAECGWC4SStH//fj377LN6ae4rWr92TUTqtCcmacb0abrkkkt00UUXNRkGJQIh2o4xRm+v3a8739mgoopaSdIx3Ttp3IBsJSXwwRrtW3FlrT7enK+9xdWSpH7ZKfrrecM1dkDkR3kAAGA1lgyEnvbs2aP9+/ervLy8RY+Pi4tTRkaGBg4cqJSUlGY9lkCItrCzsFL/8+Y3Wra1Ye5sZnK8zhiSo16ZDL3D0cMYo82HyvXplkJV1TolSRec2FN/mn4Mix8BAHAELB8Io4lAiNbkqHfqiSXb9cjiraqtdyk2xqZRfTrrpLwMxYXRgw20R446pz7bXqR1e0slSelJ8bpl6hBdenIuexcCANACBMIoIhCitXy+rUh/enO9thdUSpJ6d07WxMHZykhOiHLLgMg4WFqjjzflq6DCIUk6OS9Tf7vgWA3u1inKLQMA4OhCIIwiAiEi7XBlrf727ka9/tVeSVJyQqxOH5itQV1TWTQGHY7LZbRmb4lWbC9SndMoLsamn4/rp19NGsjcWAAAwkQgjCICISLFGKPXVu/V3e9tVHFVnSTp2J7pOrV/F9nj+WCMjq28pk5LvivQtu97xHtlJumu84Zr4pCcKLcMAID2j0AYRQRCRML2ggr9ad43+nx7kSSpS2qCJg3JUff0pCi3DGhb2wsqtHhzgSocDXsXTju2m247Z5i6piVGuWUAALRfBMIoIhDiSDjqnXr8k+16ZPEW93C50f0668TcTMWyuAYsqrbepZU7ivT1nhIZI6Xa4/T7swbp8jF9eF8AABAAgTCKCIRoqVU7DuvWeeu1Nb9CkpTXOVkTh+QoPSk+yi0D2oeCcoc+3pSvg2U1kqTjeqXr7guO1fCe6VFuGQAA7QuBMIoIhGiukqpa/X3BJr38xR5JUlJ8rMYPYtEYIBCXMfpmX6k+21YkR71LMTZp1tg++t1Zg5Vqj4t28wAAaBcIhFFEIES4jDF6e+1+3fnOBhVV1EqShvdI06kDspTIojFASJWOei3dUqDvDjX0qHdLS9Tt5w7VlGHd+CIFAGB5BMIoIhAiHLuKKvU/b36jT7cUSpI6pyTojCE56pnBojFAc+wqqtTizQUqrW5YiXfSkBzdcd4w9cpMjnLLAACIHgJhFBEIEUqd06V/f7pdDy3cIke9S7ExNo3q01kj8lg0BmipeqdLq3Ye1updxXKZhmHXv548UFed1lfxsTHRbh4AAG2OQBhFBEIEs3pXsW59Y702HyqX1LCv2hlDcpSZnBDllgEdw+HKWn28KV/7SqolSUO6ddLfLjhWI/Iyo9wyAADaFoGwDTkcDjkcDvfvZWVlys3NJRDCrbS6Tvd9sEkvrtwtY6TE+BidPjBbQ7p1Yq4TEGHGGG04UKblWwtVXeeSzSb9eFRv3TxliNKTWbEXAGANBMI2dPvtt+uOO+7wu51ACGOM5n29T397b6N70ZhjunfSuAHZSkpg0RigNVXXOvXp1gJtPNDQI5+VmqA/zxiqc4/vwRcxAIAOj0DYhughRCCbD5brz299o1U7DkuSMpPjNXFwjnI7s9AF0Jb2Flfp4035Kq5qWHRm3MAs3XXecPXJSolyywAAaD0EwihiDqG1VTrq9dCiLXp62Q7Vu4ziYmwa1bezTurNojFAtNS7XPpqV4lW7Twsp8soIS5GN00coOvG95M9jt56AEDHQyCMIgKhNRlj9N76g7rrnQ06WFYjSeqfnaLTB2YrLYl5S0B7UFJVq8WbC7T7cJUkqV92iv52/rEa079LlFsGAEBkEQijiEBoPTsKK/WXt37YUzAtMU4TBueoL0PSgHbHGKPvDlVo6ZYCVdU6JUkzT+qlP00/Rp1TWPEXANAxEAijiEBoHdW1Tj32yVY9tmSb6pxGsTabRvTJ1Mi8TMWx9xnQrjnqnFq+rUjr95VKkjKS4jV72hBdPCJXMQzvBgAc5QiEUUQg7PiMMXpn3QHNeW+j9pc2DA/N65ys8YOz2VMQOMocKK3Wx5vyVfj9SsDH9UrXbecM1Yi8zlFuGQAALUcgjCICYcf2zb5S3TH/W32xs1iS1CkxTuMGZmlAdipL2QNHKafLaO2eEq3ccVi1Tpck6dzje+iWqUPUIyMpyq0DAKD5CIRRRCDsmAorHLr/g82a++UeGSPFxdh0cp9MjejN8FCgo6h01Ovz7UX6dn+ZJCkxPkbXj++v607vz96hAICjCoEwigiEHUttvUvPfrZT/1y0ReWOeknS4K6ddOqALuqUyOqhQEeUX1ajJVsKtL+kYUh4j/RE3Tx1CJvaAwCOGgTCKCIQdgzGGL27/oDu+2CzdhU1LFGf08mu8YOyGUIGWIAxRlvzK/Tp1kKV1zR8GTQiL1N/njFUJ+RmRLdxAAA0gUAYRQTCo9/n24r09wUbtXZvw+qDyQmxGtu/i4Z2T6N3ALCYeqdLX+0u0Rc7D6ve1fBf6/Rju+t3Zw1Sv+zUKLcOAIDACIRRRCA8em0+WK573t+kjzflS5LiY206qXemTuqdqYQ45gkCVlZRU6/Pthdq44FySVJsjE0/GpmrX00aqJy0xCi3DgAAbwTCKCIQHn0OlFbrwQ+/0+tf7ZXLSDE2aXiPdI3q21kp9rhoNw9AO1JY4dBn24q0o7BSkpQUH6urT+ura8f3UxrzigEA7QSBMIoIhEePgnKHHl+yTS+s2CVHfcNS8wNyUjW2fxf2EwQQ0r7iai3bWqiDZQ0Lz2Qmx+vGiQP001PylBjPiqQAgOgiEEYRgbD9K6pw6Iml2/Xc5ztVU9cQBHtkJOq0AVnqns6CMQDCY4zR9sJKLd9aqOKqOkkNi0/dMKG/fjyqN8EQABA1BMIoIhC2X4cra/Xk90GwqtYpSeqWlqhT+nVW787JLBgDoEVcLqMNB8q0csdhVXy/PU12J7tuGN9fPxlNMAQAtD0CYRQRCNufQ2U1enrZDr2wYpcqvw+COZ3sGtOvi/K6EAQBRIbz+2D4xc7D7q0qsjvZdf34/vrJqN5sbg8AaDMEwigiELYf2wsq9OTS7Xrjq32qdTYMDc1OteuUfp3VNyuFIAigVQQKhpnJ8frZmD762Zg8dUm1R7mFAICOjkAYRQTC6Fu7p0SPL9mm9789qMZ3Qvf0RJ2cl0kQBNBmnC6jjd8Hw7Lvg2FifIwuHpGrn4/rq7wuKVFuIQCgoyIQRhGBMDrqnC59+O0hPfv5Tq3acdh9e9+sFI3Iy1TPDBaLARAdLpfR1oIKrd5VrPxyh6SG7W2mDu+uq07rq5N6Z/BFFQAgogiEUUQgbFuFFQ69tHK3Xly52738u80mDe7aSSPyMpXF0CwA7YQxRnuLq7V6d7F2FVW5bx/WI02Xn5Knc0/ooeQE9j4FABw5AmEUEQhbnzFGX+0u0QsrdunddQfc8wOT4mN1bM90De+Zpk5sEA2gHSsod+jrPcX67lCFnK6G/7I7JcbpohG99NNT8tQ/OzXKLQQAHM0IhFFEIGw9+WU1euPrfXr1yz3aVlDpvr1bWqKO75WuAV1TFRcTE8UWAkDzVNc5tXF/mdbtK1VpdZ379lP6ddbMk3pp2rHdlWKn1xAA0DwEwigiEEZWda1TH2/K1xtf7dUn3xW4v0mPi7FpYNdUHdcrQ93SEqPcSgA4MsYY7T5cpXV7S7WjsFKN/4knxcdq6vBumjmil8b066KYGOYaAgCaRiCMIgLhkaupc2rJdwV6Z90BLdp4yL2JvNSwWujQ7mka2DVV9jj29ALQ8ZTX1GnjwXJt3F+mEo9ew5xOdk0d3k3Tju2uk/t0VizhEAAQBIEwigiELVNaVadPvsvXx5vy9fHGfJU76t33dUqM06CunTS0e5o6pyREsZUA0HaMMTpYVqMNB8r03aEK1da73Pdlpdp19vCumjKsm0b17cwXZAAALwTCKCIQhsflMtp8qFxLvyvQok35Wr2r2D0cVJJS7XEamJOqQV07qWuanSXZAVhavculPYertSW/XNsLKuXwCIfJCbEa27+LJgzO0YTB2eqVmRzFlgIA2gMCYRQRCAMzxmhLfoU+31akFdsbfoqr6rzKdElJUJ+sFPXNSlGP9ERCIAAE4HQZ7Smu0tb8Cu0srFSlx7B6qWH/1VF9Omtk384a3bezemUm8e8pAFgMgTCKCIQ/DHNau6dU6/aWaN3ehj/Lauq9ysXF2NQzI0l9s1LUJytF6UlsFQEAzWGMUWFFrXYWVWpnUaUOlNbI9xNA9/REndg7Q8N6pGt4z3QN65HGHq0A0MERCKPISoGwvKZOB0tr3N9Ub8uv1NaCCm3Nr/BaPr1RXIxN3TMS1SsjWb0yk9Q1LZFFEQAgghz1Tu0vqdG+kmrtL6nWobIauQJ8IuienqgBOanq9/2ojD5ZKeqXlapu6YlKiGP7HgA42hEIo6g9BcKNB8q0t7haLmNkTMM3yUb64Xd9f5v54TbX92WMMap3GZXX1Kusuk5lNXUqq65XcVWtDpbW6GBpjdfCL75stoYhoF3TEr//satLip0ACABtqM7p0sHSGuWXO5RfXqOCcoffcH1fmcnx6pqWqOxOduV0SlRaUpxS7XFK+f4n1R6ruJgY2WxSjM0mmySbzaYYm0IOTT11QBclJ7CnIgC0Bf61bUMOh0MOh8P9e1lZWRRb4+2FFbv04srdrXqMpPhYZSTHK6eTXTnfB7+cTg0fJOJj+ZYZAKKtX3aq1++NvYgF3wfEgopaFZQ7VFThUL3LqLiqTsVVddp0sDyi7Vj6h4nq3YWPKADQFvjXtg3NmTNHd9xxR7SbEVDvzsk6sXeG97e3sslma+jBs8mmmBjP2xq+6W38ljc2xqZOiXFKS4xXWlK80hLjlJ4Ur+7pSeqWnqju6YlKsfNyA4COwOUyKqmu06Gy73sUy2pUUOFQeU29Kh31qnA0/FnpcMrpMl4jSoyRnE0MTmIoKgC0HYaMtqFAPYS5ubntYsgoAAAAAOuhy6YN2e122e2s1gYAAACgfWBMBgAAAABYFIEQAAAAACyKIaNtyHcOYWlpqaSGSfYAAAAA0NboIWxDc+bMUXp6uvund+/ekkLvxQQAAAAArYVVRttQoB7C3r17s8ooAAAAgKhgyGgbYpVRAAAAAO0JQ0YBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABYVF+0GWInD4ZDD4XD/XlZWFsXWAAAAALA6egjb0Jw5c5Senu7+yc3NjXaTAAAAAFiYzRhjot0IqwjUQ5ibm6vS0lKlpaVFsWUAAAAArIgho23IbrfLbrdHuxkAAAAAIIkhowAAAABgWQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsKi7aDYgmY4w2bNigffv2qaKiokV1xMbGKiMjQ8OGDVNWVlaEWwgAAAAArceSgXD79u168skn9ercl7R95+6I1BkbG6MzJkzQJT/6sa644grFxVnyqQUAAABwFLEZY0y0G9GWNmzYoDMmnK66qlLNHGzTRUPjNbhLjDrZbbK1oL46l9HhaqNPdjr16kaXFu+o1Y8u/ZGee/75JkNhWVmZ0tPTVVpaqrS0tJadEAAAAAC0kKW6sbZv364zJpyunNhyLbohUdkpkZhCaVNOijQkK1bXnyy9viFWl74yV3FxsXru+RciUD8AAAAAtI4WBUJjjLauztfWL/OVv6tM1eV1ikuIUWrnRPU+prOOndhLnTonBn18VVmtvnx3h3Z+U6TKUofsSXHqMTBDI87uo+zenVp8Mk157LHH5Kwu1aLrIxUG/c0cGq9/VRld/8KLuu32O9S/f/9WOQ4AAAAAHKlmDxmtLHHovcfXK39nmWSTcnp3Ulp2kuprXTq4vVQ1FXWKjYvRqRcN0LETevk9vuRQld64f7Wqy+uUlpWonLw0lRVWK39XuWJibJpyzXD1OzE7YifYyBijvnm5mpqTr8dmJEW8fk+VtUbZD1TqL3f8TbfcckvQcgwZBQAAABBNzeohrKms0xv3r1ZZYY2yclM1+cqh6tIj1X2/y+nS2o/36vN527T05e/kchkdf0au+35jjD546htVl9dp8OhuOmPWMYqJaZi59+2n+/TJi5u18JkNuqzfKUpJt0foFBusW7dOu/bs00UTkiNabyApCTZNHxCrt+a9HjIQAgAAAEA0NWvc5NKXv1NZYY3SshJ1/m9O9AqDkhQTG6MTz+ytcZcMlCR99vpWFR+sdN+/65siFe6pkD05Tqf/eJA7DErSsHE91WtIpuocTq37eO+RnFNAe/c21HlMdttsvTiki0379kb+PAAAAAAgUsJOR6UF1dr65SFJ0tiZA2RPjg9advj4nurSK1Uup9HXH/6wrcOONQWSpD7HZSkh0b9zcuDIrpKk7d+Xi6TGfQY7JbRkLdHm62S3qay8ZXsbAgAAAEBbCDsQ7lxXKGMke3Kc+h4XegN2m82mwaO7SZJ2rCtU4zTFgj0NASknL/DCMTl5DfPoSvKrVOdwhtu0ZrG1TR5UTIDjOBwOlZWVef0AAAAAQLSEHQgLdpdLkrJyUxUT2/TDuvZpCH01FXUqL6qRJJUVVUuSUjMDr0Camvn9vEHzQ9k29bO3pWPOCX5/cmfpV2tbXP2cOXOUnp7u/snNzW36QQAAAADQSsIOhNUVtZKk5E4JYZVP8ihXXVEnSaqraej1i7fHBnxMfOIPtzeWbVN9x0kXPyNNmB34fluslN7yEDd79myVlpa6f/bs2dPiugAAAADgSLXJxvTG1aydLaLrnd9KZ90ldR0mvXGtVFcVsartdrvs9siungoAAAAALRV2D2FiasMiMlXltWGVr/Yol9Sp4bGNPYDB5gd69gp69ha2qc3vSk9NlnKOkX6+UMrsE512AAAAAEArCzsQ5vRuWPClYHeFXE5Xk+UP7WiYc5iQFKe0Lg0bwTf+WVFcE/AxFcWOhr/YpE6dA88zbBOF30lPTpTK9knXLJb6TYheWwAAAACglYQdCPsc10U2m1RbXa8dawtDljXGaPPKg5Kkvsdnyfb9kpvZuQ37FubvKg/4uPxdDatuZuQkB9yWok05yqQXL5a+ela67FXplF9Etz0AAAAAEGFhB8L07GQNGJEjSfrsja1yVNUFLfvNkn0q2lehmDibTjyrt/v2vidkS2rYwiLQsNEtXzTsc9jv+3JtzgSY67jwdmne9dIZf5LOfbjNmwQAAAAArSXsQChJp/94sDp1SVRZYY3e/MfXKtrvvfG6y+nSmoW79ekrWyRJEy8boi49Ut335w3voqzcVDmq6rXkpc1yeSw28+2n+7R3U7Hi7bE67oxeR3JOIQXKfG7BNin85nXp6bOlrkMjcxwAAAAAaAeaNS4zMSVeF/7+JL332HoV7C7Xy3etUk7vTkrPTlJdrUuHdpSqurxOCYmxGjtzgIaM6e71eJvNprOuHqZ5D3ylzSsO6sDWEuX0SVNZYY3yd5YpJsamyVcMVUp65FfiTE5OliRV1hl1sgcJfs/MkKqLA993cL30xHhp0JSwjldRa5SamtySpgIAAABAm7AZ0/y+LOMy2rL6kLZ+ma/8nWWqrqiTy9lQTVxCjH7051FKzw4ehipLHVr93k7tXF+kyjKH7Elx6j4gQydP7aPs3p1afjYhrFq1SqNHj9byq5I1Nrf15yf+bF61vo05Rqu/Dr6RfVlZmdLT01VaWqq0tLRWbxMAAAAAeGpRIAzEUV2vNx/8SoV7KpQ7tLOm33CcYuObNSK1VTmdTvXolqPLB1bo/rNadwXTWqdR1werddNvb9Fdd90VtByBEAAAAEA0RSyx2ZPidO7/O0GZ3ZK1Z8NhffDUN2FtT9FWYmNjdeFFl+iVjUZVda07wW/+5nqVVNXr4osvbtXjAAAAAMCRiGgXXlKnBJ336xM1ckZfZfVKVf7uwNtLRMt1112nIkeszpvraLVQ+Nmeel3xdq0mTZygY489tlWOAQAAAACRELEho0eLJUuWaNrUs5XXyanLh8fooqFxGtA5RrZgK4yGwVFv9MlOp17dUKeXvnXp5FGn6L33P1BKSkrIxzFkFAAAAEA0WS4QStLKlSv14AMP6J135ququkYxNqlTYlzQXSdCqXMaVX6/p2L/vnm65Ec/0Z/+9Kcmw6BEIAQAAAAQXZYMhI2qqqq0aNEi7du3T+XlLRveGhcXp4yMDJ1wwgk64YQTmtXTSCAEAAAAEE2WDoTRRiAEAAAAEE3tZ18IAAAAAECbIhACAAAAgEURCAEAAADAogiEAAAAAGBRBEIAAAAAsCgCIQAAAABYFIEQAAAAACyKQAgAAAAAFkUgBAAAAACLIhACAAAAgEURCAEAAADAogiEAAAAAGBRBEIAAAAAsCgCIQAAAABYFIEQAAAAACwqLtoNsBKHwyGHw+H+vaysLIqtAQAAAGB19BC2oTlz5ig9Pd39k5ubG+0mAQAAALAwmzHGRLsRVhGohzA3N1elpaVKS0uLYssAAAAAWBFDRtuQ3W6X3W6PdjMAAAAAQBJDRgEAAADAsgiEAAAAAGBRBEIAAAAAsCgCIQAAAABYFIEQAAAAACyKQAgAAAAAFkUgBAAAAACLIhACAAAAgEURCAEAAADAogiEAAAAAGBRBEIAAAAAsCgCIQAAAABYFIEQAAAAACyKQAgAAAAAFkUgBAAAAACLIhACAAAAgEURCAEAAADAogiEAAAAAGBRBEIAAAAAsCgCIQAAAABYFIEQAAAAACyKQAgAAAAAFkUgBAAAAACLIhACAAAAgEURCAEAAADAogiEAAAAAGBRBEIAAAAAsCgCIQAAAABYVFy0G4B2wBiprirarQAAAACOHvHJks0W7VYcMQJhG3I4HHI4HO7fy8rKotgaD3VV0t09ot0KAAAA4Ohx634pISXarThiDBltQ3PmzFF6err7Jzc3N9pNAgAAAGBhNmOMiXYjrCJQD2Fubq5KS0uVlpYWvYYxZBQAAABoHoaMornsdrvsdnu0m+HPZusQ3d0AAAAAmochowAAAABgUQRCAAAAALAoAiEAAAAAWBRzCNuQ76IypaWlkiTW9QEAAAAQDfQQtiHfbSd69+4tSbJ1gNWJAAAAABx92HaiDQXqIezdu3f0t50AAAAAYEkMGW1D7XbbCQAAAACWxJBRAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAi4qLdgOsxOFwyOFwuH8vKyuLYmsAAAAAWB09hG1ozpw5Sk9Pd//k5uZGu0kAAAAALMxmjDHRboRVBOohzM3NVWlpqdLS0qLYMgAAAABWxJDRNmS322W326PdDAAAAACQxJBRAAAAALAsAiEAAAAAWBSBEAAAAAF9vv9zrS9Y32S5ZfuWaUPRhibLLd27VJsPb26y3Me7P9bW4q0hyxhjtHDXQu0o3dFkuQ92fqDdZbtDlnMZl97f8b72lu8NWc7pcuq97e/pQMWBkOXqXHV6d/u7OlR5KGS5Wmet3tn+jgqrC0OWq6mv0fxt83W45nDIclV1VZq/bb5KakpClqusq9T8bfNVVht61fvy2nLN3zZfFbUVIcuVOko1f9t8VdVVhSxXUlOi+dvmq7q+OmS5ouoizd82Xw6nI2S5gqoCvbP9HdU560KWO1h5UO9uf1f1rvqQ5Q5UHNCCHQvkdDlDlutICIQAAADwc6jykK796Fr95L2fhCxXVF2kXyz8hX69+Nchyx2sPKgbF92o3y/5fchyu8p26VeLf6XZy2aHLLelZIt+88lv9D/L/ydkuW+LvtXvl/xed3x+R8hyXx36Sn9Y+gfdvfLukOVWHFihmz+9Wfd+cW/Ickv3LtUtn96if3z1j5DlFu1epNmfztYjXz8SstyCHQt067Jb9fjax0OWe3vb27p12a16+punQ5Z77bvXdOuyW/X8hudDlntp00u6ddmtennzyyHLPfvts7p12a16fcvrIcs9tf4p3brsVs3fNj9kucfWPqZbl92qD3Z+ELLcP7/+p2Z/OluL9iwKWe7B1Q/qlk9v0bJ9y0KW+/uqv+uPS/+oVQdXhSzXkRAIAQAA4OdAZegesEYVdRUyMip1lIYs13h/Uz1SjeXCrs8Rur4SR0mz6iutDV3OXV8T5Rrb1dRxm92+CJ1HuPU1tq+p57nZ9UXoddBa7Wvq+etICIQAAABoMZdxSZKMQu9k1nh/Y/mm6ot4OTVR7vv7m9qRrbnHjVh94bZPkX3+Go8XbvuarC/Cr4Ow2xdufd+3z0o78xEIAQAA4Mfzg3OoD8fR+kDe3MAQsfrCbV+4Aanx+WsqsIYZbJsb4MJ+XsI8blNfDET9dRCh9nUkBEIAAAD48fzgHOpDdKsFs7YOIJEOts0Mek09L81tX9j1Rah9rdZzGm7PblOvg+YGdAIhAAAArMzzA3GoD8eR7mlq7hDKiJULd+hrpINeM4dQRvp5buv2NbtHtI2DfLivg46EQAgAAICQwhoyGmZPTpsP8Qy3J7G5wTHCPWYRe/6iNFcz7ONG+3UQoaG+HQmBEAAAAH68eghDhJVW+0Ae7pDRCAeGaC2e0tZDPJvdvnCH5kZqCGprBccIva46EgIhAAAA/HgGgHCGjEqhP0RHe/XQiAezSA1Bba3g2EHaF/b1jfDrgB5CAAAAWJrnB/twhoxKTQTHCPfQRKsHqdk9ZlEaghrp9kV8Ll+UeoAj1UPdkRAIAQAA4CfcoOc0zrDKhfvBvbE+z3pDta+pD/jh1tdqPZiROt/Gnromgoq7Pld45xux56W5PXCudvo6cIXXvo6EQAgAAAA/XkNGQ4QQr+AYaq5hO58TdrRsrB723MBobScR7uI99BC2GwRCAAAA+PH8gB3O3EDfvx9pfRHvkYrUhu5RWrTFau1r9rYi4W5jEaHXQUdCIAQAAICfsBeVCTPohT3XMEo9SM3uMbPIXMNI7/MXtbmGEV5cqCMhEAIAAMBPuAGuJcExnCGoERsqqCgNQY10+5oZlCO18Xtze+oiFYCjNmSUVUYBAAAA78U3Qn3IDzvoedQRye0pIrU/XmttrN7mPWuNwSzS21NEqr4IB8eIr5baGNCbaF9HEhftBkRbWVmZDh48qIqKihY9PjY2VhkZGerZs6fi4iz/dAIAgA6iteYGNlmfR2AwxshmswUsF+nFXSI+JLO5wSxSG863VnCMUI9jc9sXqQDX3KG+VuohtGSCKS4u1iuvvKK5r8zVkk+WRGRZ2cwumbp45sW65JJLdMYZZwT9xwsAAOBoUG/q3X8POWS0mfsQNqc+IyObAn+mivrQwwgHkPY+FzJSAbPVnudIDx0mEHZcBw4c0Ljx47R923Z1GtpJ3S7vpoRuCYpNim1RfcZp5Kx0qnJTpZ5/83k9+eSTuvnmmzVnzhxCIQAAOGp5fmEesudPLQt6QevzCY4xtsAznFptA/u2ngPX3O0u2ng1zVbr6Wyni+2E+zroSCwVCA8dOqRx48dp3+F9GnD3ANm72SNWd6fjOslcbFT0YZHuuece2Ww2zZkzJ2L1AwAAtCWvDedD7S/oOWQ0VNALMzg2dwhqxAJIc7c5iPTcwEgNGW3udhIRnmsYdrCN1qqlEbq+HYmlFpV57LHHtHvvbuX+MTeiYbCRzWZT1pQs5VyQo3vuuUf79++P+DEAAADaglcgbMsho56rlkZwo/tIB5C23uYg0j1mzQ5mHWT11Uj1THYklgqE/335v0o5MUX2rpEPg566TO4iW6xNr7/+eqseBwAAoLU4XR6rjEZgsRivIaNRqC/icw3bOIC01iI6TfZMhjs3sLlDc9t6G5AIt68jsUwg3LRpk7Zs3qK0kWmtfqzYlFilDk3Vq6+/2urHAgAAaA3h9hB6fmAPO+iFsQ9hs+oLo8exzRdZaaWgF/ZG8m29amlz5wa218V2wuxx7EgsEwh37twpSUrsndgmx0vonaDt27e3ybEAAAAirSUBLpwhnr51hzxuuOXCmOMYbk9YW69WGfFFVlppH8JIL1ITraG+kaqvI7FMICwrK5MkxSa3bDXR5opNinUfEwAA4GjjtTF9Ww7xVHhBL9Ib3Ud8KGOEg2O0Fk9ptW07Irz6aqR6Jhky2oG5L2ob7QRhi7FZ6oUEAAA6Fs85hBEfMtqWcw1bKThGrGct3CGjrdTzF+nVV9mH8OhjmUDYHjgcDpWVlXn9AAAAtEeB5hDuKdujOledquqqdLDyoKTAq4zuLtutele9KusqdajykNd9UsOHc2OMdpXtktPlVEVthfKr8v3KuYxLxhjtLN0pl3GprLZMhdWFTZYrdZSqqLooZDljjIprilVcU+xVzjMANZYrqi5SqaPUq5xn8GosV1hdqLLaMu/6vv/T6XJqV9kuGWOUX5WvitqKgPXVu+q1u2y3JOlg5UFV1VUFbF+dq057yvZIkg5UHPAr1xiA6px12lPeUG5/xX7V1Nf4tV+Sap212lu+V5K0r2KfHE5HwPY5nA7tq9gnSdpTvkd1zjrv+r5vX019jQ5UHGgo9/3rxvP+xvZ5vpYaXzeBnj/P11Lj6yZQucbXUuN1Dlau8bXk+brxbFdjOxtfS56vm46GQOjjzrF3hvXTEnPmzFF6err7Jzc3N8KtBwAAiAyvIaMyWn1otabNm6Z7Vt2jmz6+SVNfn6qCqgK/uXyf7ftM0+dN1z9W/0PXfnitpr0xTSU1Jd7bSRiXPtnziWbMm6F/rfmXZr0/S9PnTVd5bblfgPtg5wc6581z9O91/9Zl716mc+ado+r6ar+N7t/a9pbOefMcPfftc7p4/sW64K0LVOes82vfq9+9qnPePEf/3fRfXfj2hZr59kw5XU6/HqQXNrygc948R69+96rOe/M8XfrOpTLG+PWY/d/6/9M5b56jeVvnaca8Gfrpez/1qqfxz0fXPqoZ82bo7W1va/ob03XVB1d51dNY70NfPaTp86Zr/rb5mvbGNF370bUB67t31b2aNm+a3tv+nqa+MVX/7+P/51VPY713rbhL096YpgU7Fujs18/W75b8zuv+xvr+vPzPmvrGVL2/432d/frZmv3pbK/7G+u9eenNOvv1s/X+jvc17Y1p+stnf/E+j+///PUnv9bZb5ytBTsWaNq8afrbir951/d9ucbX0rvb39X0edN13xf3Bayv8bU0f9t8zZg3Qw99/ZD38/L99Wt8LTW+Hp5Y90TA+n7y7k907rxz9fqW13XOm+fo6W+e9ntejDG6eP7FOv+t8/XImkd0zpvn6MHVD6qjIRD6OG/AeRrZbaQ6JXRSmj0t6E9LzJ49W6Wlpe6fPXv2RLj1AAAAkeE7ZLSx12pv+V7tKd+jelOv/ZX7vXv+jNGu8l2SGnqP9pTvUa2rVoeqDvkFvd3l3vXV1Nc09NjIO+jtKmuob29FQ7mKugqV1JT4LVLT2L6dZTt1oPKAih3FKq8r9xuC2ljfluItKqwuVEF1gWqcNX6BobF9G4o2qLS2VPsq9slpnH4BpLG+dQXrVFlX6e61812tsrF9X+d/rRpnjbt+34DUWN/qQ6sbegG/790L1r5VB1fJaZxN1rfywEoZGXc7fINj4+0rDqzw+t33uI31ucuVe5drPP7ust1yGZdWHlgpSUHPY1fZLtWben1x8Auv+nwD+q7yXap11Wr1odUN9fk+zx7nUVNfozX5a7zr87huja+D8rpyfVP4TdD21bnqdKDygEocJXpy3ZOSpGe+fUYdTVy0G9DevLL5FU3tO1U9U3vqza1v6p3t77i7/o+U3W6X3d66eyACAABEgm/Qcw/5My73ffWuer+ev8Yhf569afWu+uD1yeU1XNI3ONabH4YQeh7Xr9z3x/Xs2ax31fsFx8ZyjX+6z8MnMAStzyfQNLavsVy9qXf3LnmV8zmue2ikz9w73+M2Pk++cw19y/kOtXTXZwIf17fnL1i5ptrnri9I+/zqCzBENlQ5z9dQqPNt8nnxCI6+1zTg8+LxWuno6CH08beVf9PEVybqP9/+RxNyJ+ijiz7S/ePv19geY6PdNAAAgDbjO4fQ84N1yADnUa7xvjpXnd/2FJ4f6MMJei7j+mFenKnz6/lrDE6ePZvhBsc6l/fQUqOm6/MLIJ7lTH3Q4BN2oHEFCXoyocupiSBlQgcz33LBgm2w9jUV4Hzn6LX0eakz3nMXG0O4O6AHa59P0PMN3p5zHBtv6+joIQygzlWnBTsWaMGOBeqe0l3nDThP/3PK/yjWFqvz3zpf1fXV0W4iAABAq/IKhPLp+fPsIfSZy+cV4DyDY5CeOt+eRN+g11RPnW/7GgNB0PaZAAEuVE+ib30+QxmDlfMNNI0BpvG4jcNPfYegunvqfHrsggUk33K+cw2bDHDNDXpNtC/YcT17mMM5brDz8DuuRwD2ugbBnheP12hTzws9hJD0/YvCSDbZFGtrmz0MAQAAos13H0LPniXPXqpgPXBewdH4DxkNGByNTzDzCHpNBrhgQS9I+0IGR88gGqTnz6+Hy+e4vvvjhRscmwpInr2uXuVM6KDnF5B8euoa62ss5xvgmgqYQYfI+gTIoENGgwW9718fja/HsHv+wizneVzfFXOtEgjpIQwgPiZek/Mm64IBF+jEnBO1dO9S3b3ybi3bt6zJvVAAAAA6At9FZYIO8fTZh9AzSHiGoWAftj2HlgYKcO7g49NDGE5PYp2rzi84hjW0VCHmJDYxZ63Jci7v9jU1N9DIyOly+s/l85m76J5r2Mwhnk0GvXDnLvos7hJs6KZf+3zL+QZbBR7iGagnMVQ5z/Z5hnLP9vl9aWEIhJb0p9F/0tS+U3Ww8qDmbZ2nPy79o0ocJdFuFgAAQJsK2vOnH3r+fOfyeS7YETLoyRW0xzGsnrpQcwN9evTCCY5O4wy6mE2wABdu+xr/9Bwq6tm+poKZb7lgPZONz53vPn/unrUgQSpY0HNfx+YO8Wyc5+kT5AMN8fT8ciFU0POcy9fcHkLfoBwsODqNU8F6pzs6AqGPSwZfogOVB7S3Yq9O7nqyTu56csByv/nkN23cMgAAgLbj+WE42NxAp8vpH/QCDBn13Oev8b6w5gZ6BEyvXh2fANfYi+ZXLkD7ApXzHOLZ2L6w6vMIjr7ta2oOnG99weboNZbzDXCB2ucZsHyHlgYbuukXyILNDfQJmE3W59M+3w3iPZ/jgMcNtiqoT8+o59DcUOWC9TgGGzLq276OzDKBMCbm++mSrtDl5m+bH5FhocZlfjgmAADAUSZY8GlqO4lAwbHO1PnXF87cwCBzCH176po1h7CZcw3DHlrq0z6/1T6DHLepIZ7u+sLsSQxnyGignsQWz10MMATV8wuAUPWF1fPnO2S0BXMIjTFeQ1+DlQs2XLmjs0wgTEtLkyQ5q5yKTQ6+OMz/LP+fiBzPWeVUWnpaROo6Gh377LH66KKP1C2lW8hyf1r2J/Xu1FvXHX9dG7UMAACEI+i2Ez5zCIPtQ+gyrqDBMegcvTCHeDZrrmEYPZO+2064FKS+FrSvybmGTezz51su2HYNvscNVZ9nD2Zz5gYGOt9Acw19e0sbnzvP4zVVLuy5gR71eQVMj3LBXgO+5YJ9edDRWSYQDho0SJJUvb1aCVkJrX48x3aHThx8YqsfJ9KmvDZFxY5ifXLJJ0qOT5YkVddXa8LcCcqwZ+iDiz6Icgtb7s2tb+q/G/+r3eW7lZaQpksGX6KfH/tz9/2VdZW694t7tXDXQhljNCF3gu4ed7ckqaa+Rrd/frsW716sNHuafnPSbzSt37SAx3EZl+774j69tfUtxcfG6+rhV+tnw37WJucIAECk+K0y6hEQwtlI3nOuYcAP5c1dFdRzmF+IuYa+5cLt+QvWPr/6gm1PEWb7wt3XMFh9vouxBGtfU6ugNjU30G9bDGP8wqfnn0316Lnn8nkM8WxJz1/j35uaG+jZvlBBz7N9Xq8Bn/Z1ZJYJhH379tUJJ52gHV/sUPqo9FY9Vl1JnSo2V+jS313aqsdpLTnJOfp4z8ea0W+GJGnx7sXKTs5WnfPo7javddbq1tG3aljWMOVX5ev6j65X95Tumt5vuiTpz8v/rO4p3fXBzA9kj7Nra/FW92MfXfOoSmpKtOjiRdpWuk03LLxBx3Q5Rn3T+/od55XNr+iLg19o/gXzVVFXoavev0qDOg/SKd1PabNzBQDgSIUzN9BvOwmF36MXaE5dyAAX5j6EwRZt8W2f7551LTpuM8r5zuWTvBflCdnzF2DxmUA9mHWuuqA9f37Ps0fPpFcADrYtRrDVPgNsJxFsjp5nOb+gF2RbjLBXDw3RvmB7ZXodN8RroKOz1CS3H13yI1WsrVD1jtbbWN4Yo4L5BYqxxej8889vteO0pql9p+rd7e+6f39n+zua3nd6yMfM2zJPk1+drAlzJ+jV7171uq/UUao/Lv2jxs8dr7NfP1tvbX0rYB2ljlJd/9H1GvfyOJ3+8um6/bPbVeuslSRd++G1enPrm+6yVXVVGv3iaB2sPBj2eV0y+BKdkHOC4mPi1TO1pybnTdbagrWSpG0l27ShaIN+O+K3Sk1IVXxMvI7pcoz7sfO3z9e1x12r1IRUHZ99vCbmTtR7O94LeJz52+dr1rBZ6pLURXlpeZo5aKbmb5sfdjsBAGgPPD8MhxoK6rvKaFjbOoTbUyfjt6G7u74gPXV+cw2DbHfRkvb51hf2Rveh9lMMZ25gGD16Qetrau6iAveYNZ5vsKDnuUiM53zRcOcGBqsvUI9j42sgZLlQcwODvFb8yhEIO74bbrhBJ514knbfv1uVmyu93tCR4Kp16eDLB3V40WH94x//UJcuXSJaf1sZ1W2UthRv0eGawzpcc1jfFX+nU3oE793aUrxF93xxjx6c8KDen/m+vj70tdf9sz+drZykHH100Ud6dPKjeuirh7T58Ga/eowx+tGQH2nRxYv0+rmva0PRBs3dPFeSNKP/DC3YscBddvGexRraZai6pXTTgYoDGvvfsUF/DlQcCNju1YdWa0DGAEnSN4XfKC8tT39a/ied9vJpuuzdy7Qmf42khqBaWF2oQZmD3I8dlDlI20q2Bax3e8l2r7IDMwdqa8nWgGUBAGivgi4W4zFkNNA+fwG3V/AdQhmsJzHcnrrmzOULtg+h55w6j1U8Q7Yv3B7CAO3z3c7Bt31NzTUMttF90LmLQbZ1aGxf0CDlO9cw2GIsIeY4hurR8wyOgcoF2u4irJ6/ID2JfvNDgx3X9zVgGDLaIaWlpWnhhws1+azJ+mLOF0rukayUESlK6Jag2KRYydb8Ok29kbPSqapNVapYW6H66nr985//1C9/+cvIn0AbibXF6sy8M/X+jvclSWfmnakYW/DvDhbuWqhJvSfpuOzjJEnXH3+95m9v6BErrC7Ul4e+1ENnPKT4mHj1S++naX2nadHuRRrcebBXPRmJGZqQO0GSlJ2crYsGXaQVB1bo8qGXa3LvyZqzco6KqovUJamLFuxY4J7D1z21uz77yWfNOsdnv31WpY5Sndv/XElSflW+Ptv/me4Ye4fuOvUuLdy1UL/8+Jd698J3VV3f0KOcEp/ifnxKfIqq6qoC1l1VX6XUhNSwygIA0F75LSrTODTQ5REyXE6v7oVgc/n89njzqK+puXe+c9Aaj+v7Id93g/bGcsGCWVNzAwPWZ7y3z/AMF57lwp0L6dk+I++hm8Hq8w14wdrnGc4Dnq/PqqBNlfOdexesfcHmGjbOKfUKjj7DUz3r9Zq76NNr2fjcBXsN+JbzC3oBjhuoV9ezvo7MUoFQagiFy5Yu08KFC/Xqq6/qjTffUEFJwRHXe8ywY/Tj2T/WxRdfrCFDhkSgpdE1o98MzVk1R0ZGs0fNDvmGKKgu8FpN1PPvByoOyOF0aPzL4923OY3TPW/PU2Vdpf624m9aeXClKusq5TIuDc8aLklKjk/WuF7j9OGuDzWt7zStOrhKfzvtby06t3e2v6PnNzyvZ85+RolxiZIke6xdPVN76sKBF0pqGDb75Lontb5gvbsNlXWV7qBXWVfpXnTHV3JcsipqK7zOK1hZAADaK88P4b5z9Ny9VKZeMSbGu5zP/m9S6F6alvTABVoVNKwexyDlQm6LEaJ9zemZbO4cx2A9jo29fgF7/nyCWaBVNxvLhd3z11if7yIwIdoXVn0h5gZ6bosRtIcwQIALuMpoiNeAX31B5n12dJYLhJKUkJCgadOmadq0afrPf/6jmpoalZeXt6iuuLg4paWlKTY2+FYWR6NhWcNU6iiVJA3PGu6eaxdIdlK29lbsdf/uOa8vJzlHyXHJWv7j5bLZQnfBPvftczrsOKzXznlNmYmZemXzK17DRGf0m6Gn1j+l+Jh4je4+Wun2hsWBDlQc0HlvnRe03rfOe0vdU7tLkj7e/bHu/+J+PXXWU+rVqZe7zIDMAX6Pa2xvuj1dWUlZ2lKyRSfmNKwcu6V4i/pn9A94vH4Z/bSlZIu7B3Rr8Vb30FQAAI4WQYeMetxe76pXnC3O6zFB5waGu+pmmHPvfFcFPeK5i6YF9XkG4FD1qelVRptqX1Nz73zLBVtN068+mZDlgga9INetqXLh1Oc0P/T+BZvjGOo15VsurDmETQxB7cgsGQh9JSYmKjExMdrNaHf+d+L/hlVuct5kXb7gcv248McakDFAT6x7wn1f15SuOj77eD389cO65rhrFB8Tr++Kv5M91u4XqCrrKpUUm6TUhFTtr9ivuZvnKi3hh70cx/YYq78s/4te2PCCrj/+evft3VO7a9Vlq5ps54oDK3TbZ7fp0UmP+gXAkd1GSpLe2vqWZvSboUW7F6mwqlDHZh8rqSGMPrHuCT0w/gFtL9muxXsW64VpLwQ8zox+M/Tst89qbI+xqqit0GtbXmtxbyYAANHi+SE8ZI9ZbJg9ZuH06AX6kB+gxzHsuXwBgmMk2xeJnk7P9nm2t7lz9HzbF7JHzzc4hjM3MAI9iZ49u6HOw7NnLmhPoqkPulemV7mmgmOYcw07MkstKoPm6Z/RP2gvmKeBmQP1h5P/oF8t/pWmvD5FJ+Sc4HX/38f9XYeqDmnq61M1fu543bPqHtU4a/zq+enQn6qgukCnvnSqfvPJbzSp9ySv++Ni4jSlzxQdqDzgnmvYHE+ue1LlteW6+sOrNerFURr14ijd+fmdkqT4mHg9NPEhvbTpJY15aYyeWv+UHjrjIXcgvfGEG5WekK6Jr0zUbz75jW4dfat7y4nVh1Zr1Iuj3Me5dPClOrnryZrxxgxdvuBy/Wzoz9hyAgBw1AkWkELN+Qs2Vy5UL02wOWshy4UYBuhZLtSHfL/zCDI30K8+BQ7Avu3ze16C7Rvo0b5aV63XfV7tC7KKZ7D2+QaaYO0LWZ8JvspoqPaFKue53UWwco2rzIdzvp7lAtUX6jXlW87rNSDrBEJ6COEl2Mbzx2cfH3JT+pmDZmrmoJnu3y8edLH77xmJGUF7yDxv75bSLWivW6PuKd11Ru8z3HP/muPpKU+HvH9w58F6ecbLAe9LjEvUPaffE/C+EV1HePVQxthidPOom3XzqJub3UYAANoLzyF8wXrqAs7lC9Kj5zl1JGhPXTP2Awy60X1TPX8ByvkuUBJ2T2KoHkKP4BhyLp9HcPQMQqGOG059vovKBGtfsB4zyXtBFr+hmyF6HMNtXzg9hKHKeT5ffkNfPVYt9epJDHIeoV4DHR09hDhqVNZV6o2tb7gXfgEAAK0n2HC8UHPggs4NDBX0mtqHsC3mBobbvgA9f56BIlj76pz+++g1u5zCKOezgX1Ycw19h262dG5gsI3kg7QvVH2e5+c3BDVIubDrCzbXkI3pgfZt8e7FmvjKRB2ffbx7vh8AAGg9voHQ84OzZ5mgQy19lvb3XU3Tcy5YsHKegavJckHaF2yfRN/6vIKeAh/XtxfJayhjiPq8hoKGaF/Q+nyCaLBynu3z3dA9WPt8t2sI9jz7DfH0aZ/ncUPV57mfom8djTyfr1DtCzXXMFg5vyGjjdup+BzHtyexI2PIKI4KE3tPDGvhGAAAEBmeH47D7qkLsSpoXIz3aqRH1PPnuyJkuKt4Bmmf79xAlytEz59HOa8hniF6HIOWCzVkNMR5hKwv2HYSLelJ9Jxr2FRPYjN7/kKV851DGI1yDBkFAACApblcgTeSb3JV0COcexf2qqBBNrqPxKqlnj1UIef8hSjnFRxDlQvzuC0pF8l9CP22sQi2ummIOYm+PX/hlAu7vhBzHL3qC7EKaqj2efIMkR0BgRAAAAB+vHoIQ+0bGGaPXovmGobocfQ9bjg9jp6/t6inroU9f8Hm/Pn2TIbT8xeqnOeqoKGO6zuH0Cs4+rQvrLmBYc41DNUDF7QnMUTPZLj1hdtDGGquoaeONpSUQAgAAAA/wRaV8ZtjFqSnrqk5esHKBd0mIlR9Qcr5Bq6gc+9MeHMDfVdVDTWHsCVzDaNRX6i5d15zEoPMvfOrTybo3EWvuXw+PXqh5gaGuh7u+kK0z2+uYRjHDTWHsKMNJSUQAgAAwI9nz0mwPdnqTF3QuXyeAgbHIOVCbSLuVZ9Pz18goXr+QrUvVLlgPXWh2tem5cI9jyDbPwQ7brBrK4UOZp5C9dQdcbkQ+wZG+rgEQgAAAHR4wYaMegrVo+dbLuyg5xFonMYZsJcm7GAWYohnqPZFIphFtH0msu3z7DkNFfR8N7CPZOAKNSSzVesLMziGqs8zAHcEBEIAAAD48dt2wvgvpBHuZt6B9ngLtDBH2D1/JvgQSr/2BRky6inUUFDf+oINyQzVPs/FSvzqUxj1+QTlcNsX9LghhsgGO67vojKefIdkBlt0JeyeRJ9FYAK99gLWF2b7wjkui8oAAADA0oIt2uLJtycsVA+hZ31Be/58e8KCBJpQq276tS+S9YUZ9Fo8FDQS7WvmkNFwjxt2kArVAxditU+vcuH2EIYZ4MIuF2ZPIovKAAAAoMPz3YcwrCGjQRbi8A2O4cxZk7xXffSrL8gqni2uT2HWpxa0L9yeyUifb6ievzCO67fISrAhlL77CwbrqQu3XJg9iV6rgoaa4+i7amm4PYkWWVSGjenbkMPhkMPhcP9eVlYWxdYAAAAE57WoTLCeP58eM98hpO5yYfaY+Q6hDNXDZbPZmi4X5tw73yGUEZlrGE5wDHduYJhzDetcdeEHxzB6EkNtJO8p0nMDfbd/CBZsIz3XMNzz7WiBkB7CNjRnzhylp6e7f3Jzc6PdJAAAgICCbTvhya9nLcweqWBDFH17zI50CKXfPn9HOLS0Oe0Le0hmuO1T2w+l9b09aDDzGZIZTrlmDUEN0lMX8SGjYZYjEKLFZs+erdLSUvfPnj17ot0kAACAgDwDoW8g8bw9oj1rUVp1M+LbOjRjddNIl4vk6qa+t4dTLuztHyK8TUTEVy0NtfpqBwuEDBltQ3a7XXa7PdrNAAAAaJLLFeEevQj21NW76hVniwurXMR76trzojfN6DkNp8fR9/ZwykViFc/W7Ek0Cj43MOweQhaVAQAAQEfn+aE32Fw0p3GGNWfNb5uDI1y0xW87iSM8bp0Jb+5dc84jnEVqmlVfBNvne3skywXbosS3XKhFW3wXgQm6jUWYc/5aXM4ii8oQCAEAAOCnufvUSc1Y7CTc1S9DrM4Zdn1h7N/X4tU5w50zGeYQz5D7GoZ5HuEMpfV9fDiLtoQq57U6Z6jVPsPdh9B3iOcR7i/YkuOGqq+jBUKGjAIAAMCPZy9PxPfRa6u5hj4f3I94bmC4c/RauojOEc4hDHcRnbDnBvoOGW2ruYbhDhmN9FzDMPdT7GiBkB5CAAAA+PEcphfuHLiwexKD1Oe3/UMbzalzupyRnctnmvG8tOJcw3BW+wxVn+/j22quoW+PXlirloba6D7MuYbh9jgSCAEAANChuYyrZT1hR7gqqF8P1xH21LVKD1wkVyNtzr6GYbbPUyR79CJdLhKrfYY7tLQlx2XbCQAAAFiW76IgR7p4il/P1REuntKs+sJpnwmzPt+evwi0L6wN7MMs15qLxUS6XMi5hq24WEwkNqavM4HP72hFIAQAAIAXzzAjhR4aGe7Q0nC3dYjWdg1hb/zejrenaMnQzfZeLuLbU4RbX4jtKYKtenq0IhACAADAi+8H3mBDACXvYYptNTSyOUNVo7VBfLgb2Ic1lNaEN1S1PQwFjXS5sBeVCXOj+2YNQWXIKOBvTf4avfbda9FuBgAAaKGle5dq4a6Ffr2AnvyGjAZZ1MP3vlCLxXjWGXLbhAhuw+C7WEyobRPCWYwl3Pr8gmiIxWzC3cYinOcl3EVgwt52wufx4R73SMv5LgITdF9D3+0kwlgsJtweQisFQradQFjW5K/Rv9b8SysOrJAkzeg3Q4lxiVFuFQAAaK4Hv3xQ20q3qW96X910wk2anDdZMTbvPgK/IaMhegg97ws2d8z3vmD1+a0yGokexzB66pzGqRiPfpJI9DiGPdcwnH0cfR4fbg9csOOG21PX0uMeablw5/K1qCfRhAiYYc41JBDCUjyDYKwt1n2757dUAADg6NEYQHaW7tTvlvwuYDD0/cAbbg/hkZbzDYSR2CA+3O0f4mLiwioXVg9mmO2rc9WF3YMZ6nfKNbEITEs2sA+xPUWw249WBEIEddHbF2lz8Wb3757fplz1/lV+3yYCAID2b3/Ffkk/fLm7o3SHfrfkd0qNT9WiixcpOT65xT2EbVWuOXP0wu3R8/xcE4ltJ8LatiPM9rWHuXztvVy4cwhdCrGvoUXnEBIIEVSftD7aUrJFxhi/HsH+Gf29vkkDAABHhx2lO+RwOty/22STkVH3lO6Kj4mX5D+HMNhcNN/72qqc78IgoebKhbuKZ+O5N1Uu3Poiubppe1jts72Xi/gqoyb4KqOhesKPRnyiR1D3T7hfhdWFeuabZ/TfTf/1Gsbxp1P+pKS4pCi3EAAANNdX+V+pvLRcsbZYuYxLZ+adqRuOv0EDMge4y/gFwnbWQxhuudZYPTSSq6W2h561jlIu4nMN5Qq6vURH23aCQIiQspKy9PuRv9cVw6/wCoYMFwUA4OiUEJMgm2ya1HuSXxBs5PuBN9zFYtpjOc8P/0e8gb3v3Moj3MC+PWwQ31HKGZngcw19F6kJ1vMX5mI2zCGEJXkGw91lu2WPtUe7SQAAoAX+Pu7viomJUb/0fkHLNGfIaCQXlWlOuXCHoIZTX72pD2tbDN8vxcPdwD6S2zCEOm60FneJVvtasi1GqJ4/v8Vngi0qwxxCWFlWUpaykrKi3QwAANBCgXoEfTVrUZkozCH0bVMk2hfOnETf1UjDHjJ6pHPlwh1CGa25fNFqXwuOG+5cQ6dxsqgMAAAArMn3A29Eglmky0U6OIZRzmmcshlbk8f12yD+SOfKRTogRbpctNrXguM6jVM2l63JcqFCX0cLhEwEAwAAgBffHsJQH4CjNWTUs01t2b5wjhvpoZG+x2mr47b39rX0uOGsHhrua6UjIBACAADAi+8cQgA/oIcQAAAAHRqBEAiuo70/CIQAAADw0tH2WQMiiR5CAAAAdGgdrQcEiCQCIQAAADo030VlAPyAQAgAAIAOjSGjQHAEQgAAAHRoDBkFgqszbDsBAACADoxACARHDyEAAAA6NAIhEFxHG1JNIAQAAICXjvaBF4ikjtZDGBftBliJw+GQw+Fw/15WVhbF1nhbuGuh1uSviXYzAABAO7CzbGe0mwC0WwRCtNicOXN0xx13RLsZAa04sEJzN8+NdjMAAACAdq3edKxAaDPGmGg3wioC9RDm5uaqtLRUaWlpUWyZtGjXIq0tWBvVNgAAgPYjLiZORkZPrX8q2k0B2o1LB1+qPml99NOhP412UyKGHsI2ZLfbZbfbo92MgCblTdKkvEnRbgYAAGhH5m+bH+0mAO3KH0b+QfbY9vl5vqVYVAYAAAAB2Wy2aDcBaFdiOmB86nhnBAAAgIjoiB9+gSPREb8k4V0OAACAgGJsfFQEPHXE90THOyMAAABEREfsDQGOhE0d7z3BKqNRVFZWpvT09HaxyqjD4dCcOXM0e/bsdrvwjZVwPdoXrkf7wbVoX7ge7QvXo33herQfXIvQCIRRZIxReXm5OnXqFPVv4NpTOAXXo73herQfXIv2hevRvnA92heuR/vBtQiNbSeiyGaz8aIEAAAAEDXMIQQAAAAAiyIQAgAAAIBFEQghSbLb7brtttuYaNtOcD3aF65H+8G1aF+4Hu0L16N94Xq0H1yL0FhUBgAAAAAsih5CAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIIQk6V//+pf69OmjxMREjR49WqtWrYp2kzq822+/XTabzetnyJAh7vtramp04403qkuXLkpNTdXMmTN16NChKLa4Y1m6dKnOOecc9ejRQzabTW+++abX/cYY/eUvf1H37t2VlJSkyZMna8uWLV5lDh8+rMsuu0xpaWnKyMjQ1VdfrYqKijY8i46jqetxxRVX+L1fzj77bK8yXI/ImDNnjkaOHKlOnTopJydH559/vjZv3uxVJpx/n3bv3q3p06crOTlZOTk5+sMf/qD6+vq2PJUOIZzrMWHCBL/3x/XXX+9Vhutx5B577DEdd9xxSktLU1pamsaMGaMFCxa47+d90baauh68L8JHIITmzp2r3/72t7rtttv01Vdf6fjjj9eUKVOUn58f7aZ1eMOGDdOBAwfcP8uWLXPf95vf/Ebz58/Xq6++qiVLlmj//v268MILo9jajqWyslLHH3+8/vWvfwW8/95779U///lPPf7441q5cqVSUlI0ZcoU1dTUuMtcdtll+vbbb/XRRx/pnXfe0dKlS3Xttde21Sl0KE1dD0k6++yzvd4vL730ktf9XI/IWLJkiW688UatWLFCH330kerq6nTWWWepsrLSXaapf5+cTqemT5+u2tpaffbZZ3r22Wf1zDPP6C9/+Us0TumoFs71kKRrrrnG6/1x7733uu/jekRGr1699Pe//12rV6/Wl19+qTPOOEPnnXeevv32W0m8L9paU9dD4n0RNgPLGzVqlLnxxhvdvzudTtOjRw8zZ86cKLaq47vtttvM8ccfH/C+kpISEx8fb1599VX3bRs3bjSSzOeff95GLbQOSWbevHnu310ul+nWrZu577773LeVlJQYu91uXnrpJWOMMRs2bDCSzBdffOEus2DBAmOz2cy+ffvarO0dke/1MMaYWbNmmfPOOy/oY7gerSc/P99IMkuWLDHGhPfv03vvvWdiYmLMwYMH3WUee+wxk5aWZhwOR9ueQAfjez2MMWb8+PHmV7/6VdDHcD1aT2Zmpnnqqad4X7QTjdfDGN4XzUEPocXV1tZq9erVmjx5svu2mJgYTZ48WZ9//nkUW2YNW7ZsUY8ePdSvXz9ddtll2r17tyRp9erVqqur87ouQ4YMUe/evbkubWDHjh06ePCg1/Ofnp6u0aNHu5//zz//XBkZGTr55JPdZSZPnqyYmBitXLmyzdtsBZ988olycnI0ePBg3XDDDSoqKnLfx/VoPaWlpZKkzp07Swrv36fPP/9cxx57rLp27eouM2XKFJWVlXl9e4/m870ejV588UVlZWVp+PDhmj17tqqqqtz3cT0iz+l06uWXX1ZlZaXGjBnD+yLKfK9HI94X4YmLdgMQXYWFhXI6nV5vBknq2rWrNm3aFKVWWcPo0aP1zDPPaPDgwTpw4IDuuOMOjRs3Tt98840OHjyohIQEZWRkeD2ma9euOnjwYHQabCGNz3Gg90XjfQcPHlROTo7X/XFxcercuTPXqBWcffbZuvDCC9W3b19t27ZNt956q6ZOnarPP/9csbGxXI9W4nK59Otf/1qnnnqqhg8fLklh/ft08ODBgO+fxvvQMoGuhyT95Cc/UV5ennr06KF169bp5ptv1ubNm/XGG29I4npE0vr16zVmzBjV1NQoNTVV8+bN09ChQ7VmzRreF1EQ7HpIvC+ag0AIRMnUqVPdfz/uuOM0evRo5eXl6ZVXXlFSUlIUWwa0Pz/60Y/cfz/22GN13HHHqX///vrkk080adKkKLasY7vxxhv1zTffeM1vRvQEux6ec2WPPfZYde/eXZMmTdK2bdvUv3//tm5mhzZ48GCtWbNGpaWleu211zRr1iwtWbIk2s2yrGDXY+jQobwvmoEhoxaXlZWl2NhYv1WwDh06pG7dukWpVdaUkZGhQYMGaevWrerWrZtqa2tVUlLiVYbr0jYan+NQ74tu3br5LbxUX1+vw4cPc43aQL9+/ZSVlaWtW7dK4nq0hptuuknvvPOOFi9erF69erlvD+ffp27dugV8/zTeh+YLdj0CGT16tCR5vT+4HpGRkJCgAQMGaMSIEZozZ46OP/54PfTQQ7wvoiTY9QiE90VwBEKLS0hI0IgRI7Ro0SL3bS6XS4sWLfIag43WV1FRoW3btql79+4aMWKE4uPjva7L5s2btXv3bq5LG+jbt6+6devm9fyXlZVp5cqV7ud/zJgxKikp0erVq91lPv74Y7lcLvd/Omg9e/fuVVFRkbp37y6J6xFJxhjddNNNmjdvnj7++GP17dvX6/5w/n0aM2aM1q9f7xXSP/roI6WlpbmHcyE8TV2PQNasWSNJXu8PrkfrcLlccjgcvC/aicbrEQjvixCivaoNou/ll182drvdPPPMM2bDhg3m2muvNRkZGV6rLiHyfve735lPPvnE7NixwyxfvtxMnjzZZGVlmfz8fGOMMddff73p3bu3+fjjj82XX35pxowZY8aMGRPlVncc5eXl5uuvvzZff/21kWQefPBB8/XXX5tdu3YZY4z5+9//bjIyMsxbb71l1q1bZ8477zzTt29fU11d7a7j7LPPNieeeKJZuXKlWbZsmRk4cKD58Y9/HK1TOqqFuh7l5eXm97//vfn888/Njh07zMKFC81JJ51kBg4caGpqatx1cD0i44YbbjDp6enmk08+MQcOHHD/VFVVucs09e9TfX29GT58uDnrrLPMmjVrzPvvv2+ys7PN7Nmzo3FKR7WmrsfWrVvNnXfeab788kuzY8cO89Zbb5l+/fqZ008/3V0H1yMybrnlFrNkyRKzY8cOs27dOnPLLbcYm81mPvzwQ2MM74u2Fup68L5oHgIhjDHGPPzww6Z3794mISHBjBo1yqxYsSLaTerwLr30UtO9e3eTkJBgevbsaS699FKzdetW9/3V1dXmF7/4hcnMzDTJycnmggsuMAcOHIhiizuWxYsXG0l+P7NmzTLGNGw98ec//9l07drV2O12M2nSJLN582avOoqKisyPf/xjk5qaatLS0syVV15pysvLo3A2R79Q16OqqsqcddZZJjs728THx5u8vDxzzTXX+H1pxfWIjEDXQZL5z3/+4y4Tzr9PO3fuNFOnTjVJSUkmKyvL/O53vzN1dXVtfDZHv6aux+7du83pp59uOnfubOx2uxkwYID5wx/+YEpLS73q4Xocuauuusrk5eWZhIQEk52dbSZNmuQOg8bwvmhroa4H74vmsRljTNv1RwIAAAAA2gvmEAIAAACARREIAQAAAMCiCIQAAAAAYFEEQgAAAACwKAIhAAAAAFgUgRAAAAAALIpACAAAAAAWRSAEAAAAAIsiEAIAAACARREIAQAAAMCiCIQAAAAAYFEEQgAAAACwKAIhAAAAAFgUgRAAAAAALIpACAAAAAAWRSAEAAAAAIsiEAIAAACARREIAQAAAMCiCIQAAAAAYFEEQgAAAACwKAIhAAAAAFgUgRAAAAAALIpACAAAAAAWRSAEAAAAAIsiEAIAAACARREIAQAAAMCiCIQAAAAAYFEEQgAAAACwKAIhAAAAAFgUgRAAAAAALIpACAAAAAAWRSAEAAAAAIsiEAIAAACARREIAQAAAMCiCIQAAAAAYFEEQgAAAACwKAIhAAAAAFgUgRAAAAAALIpACAAAAAAWRSAEAAAAAIsiEAIAAACARREIAQAAAMCiCIQAAAAAYFEEQgAAAACwKAIhAAAAAFgUgRAAAAAALIpACAAAAAAWRSAEAAAAAIsiEAIAAACARREIAQAAAMCiCIQAAAAAYFEEQgAAAACwKAIhAAAAAFgUgRAAAAAALIpACAAAAAAWRSAEAAAAAIsiEAIAAACARREIAQAAAMCiCIQAAAAAYFEEQgAAAACwKAIhAAAAAFgUgRAAAAAALIpACAAAAAAWRSAEAAAAAIsiEAIAAACARREIAQAAAMCiCIQAAAAAYFEEQgAAAACwKAIhAAAAAFgUgRAAAAAALIpACAAAAAAWRSAEAAAAAIsiEAIAAACARREIAQAA0GLvbH9HNy26Kej9U16boq8OfRXwvn0V+3TCcye0UssAhINACAAAgBab0W+GHpn0SFhl/7TsT3pi7ROt1pY1+Wt03LPH+R3jqfVP6fSXT9epL52qB798UMYY933fFH6jC9++UCNfGKkr3r9C+yv2u++rqa/RLZ/eotEvjtaZr52p97a/F/TYLuPSPavu0dj/jtX4ueP13LfPRf4EgVZAIAQAAMBRz2Vcuu+L+zQ8a7jX7Uv3LtXLm17Wi9Ne1Fvnv6VP932qeVvnSZJqnbX69eJf67Ihl2nZj5fppJyTNPvT2e7HPrrmUZXUlGjRxYt0//j79deVf9WO0h0Bj//K5lf0xcEvNP+C+Xpu6nN69ttnteLAitY7YSBCCIQAAAAI6LXvXtMfl/5RklTnqtOoF0fpsbWPSZJ2lu7UpFcm6c2tb+rnH/7c/ZhP936qaW9M06kvnarH1jzmvv2trW/pve3v6Yl1T2jUi6N05+d3eh1n4isTNWHuBL219a0Wt/XY7GPVN72v1+3vbHtHFw+6WLlpucpKytKsYbP09ra3JUlfHPxCCbEJmjlopuyxdl1z3DXaULRBe8v3SpLmb5+va4+7VqkJqTo++3hNzJ2o93YE7iWcv32+Zg2bpS5JXZSXlqeZg2Zq/rb5LToXoC3FRbsBAAAAaJ9O6nqSHl/7uCRpY9FGdU7srK8PfS1J+ir/K53U9SSv8odrDuv3S36ve0+/V2N7jNXDXz+sQ1WHJEnnDThPqw6uUu9OvXXd8ddJaphD6DRObS3Zqg9nfqgvDn6hX3/ya03Om6yU+BQ9tf4pPb3+6YBtO7HrifrXpH9JkkpqSvT8huf14vQXdc+qe7zKbSvdpql9p7p/H5g5UNtKtjXcV7JNgzIHue9LiktSr069tK1kmzoldFJhdaHX/YMyB2ltwdqA7dlest2r7MDMgVqyd0mwpxZoNwiEAAAACKhfej/Vueq0t3yvvjr0lS4edLH+u/G/crqcWn1otU7MOdGr/Kd7P9XQLkM1Pne8JOmGE27Q8xufb/I41x93veJj4zW251glxSVpT/keDek8RD8/9uf6+bE/b/Lx//z6n7p86OVKS0jzu6+qrkqpCanu31PjU1VVV9VwX32VUuJTvMqnxqeqqr5K1fXVkuR1f0p8ivuxfsep9z5OqLJAe8KQUQAAAAR1Ys6J+ir/K63OX62Tu52sgZ0HatPhTfrq0Fca0XWEV9mC6gJ1S+nm/j0pLkkZ9oyQ9cfaYpWR+EOZxNjEZgWpjUUb9U3hN5o5cGbA+5Pjk1VRW+H+vaKuQsnxyQ33xSWrsq7Sq3xFXYWS45KVFJckSV73V9ZVuh/rd5w47+OEKgu0J/QQAgAAIKiTck7Slwe/1KbDmzS0y1CNyBmhD3Z+oFJHqQZmDtTGwxvdZbOTsrV833L37zX1NSpxlLh/t8nWrGP/e92/9e/1/w7crq4n6fHJj+vLQ19qZ9lOTXp1kqSGQBdri9We8j3662l/Vf/0/tpSskUTe0+UJG0t3qr+Gf0lSf0z+mvu5rle7d1bvlf9M/or3Z6urKQsbSnZ4u4J3VK8xf1YX/0y+mlLyRYN7jzYfZwBGQOadb5ANBAIAQAAENSIriP06NpHNazLMMXHxGtE1xH6xaJf6MScExVj8x5sNq7XON298m4t3btUY7qP0WNrH/Pa4qFzUmftr9zve4igrjnuGl1z3DUhy1w06CKvOYJ/X/V39UztqauPvVqSNKP/DN214i5N7TtVSXFJem7Dc7rsmMskSSO7jVSNs0bztszT9H7T9eS6JzW0y1D16tSr4bH9ZuiJdU/ogfEPaHvJdi3es1gvTHshYDtm9JuhZ799VmN7jFVFbYVe2/Ka/nba38I+VyBaGDIKAACAoIZ0HiJjjLuXbHjWcNW76nVSzkl+ZTsndta9p9+rOSvnaPwr45UYm6iuyV3d918w4AKtK1insf8dq7+u+GtE2pcUl6SspCz3jz3WruS4ZPd8wtN7na5LB1+qn7z7E5375rk6tcepumDABZKkhNgEPTTxIT2/8XmNfWmsVh9arTnj5rjrvvGEG5WekK6Jr0zUbz75jW4dfat7FdPVh1Zr1Iuj3GUvHXypTu56sma8MUOXL7hcPxv6M53S/ZSInCPQmmzG82sbAAAAAIBl0EMIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKIIhAAAAABgUQRCAAAAALAoAiEAAAAAWBSBEAAAAAAsikAIAAAAABZFIAQAAAAAiyIQAgAAAIBFEQgBAAAAwKL+P/cPCiYOJdlmAAAAAElFTkSuQmCC\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["rsw.jupyter_schedule()"]}, {"cell_type": "code", "execution_count": 10, "id": "1fcb0acd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "      <th>unit</th>\n", "      <th>extra</th>\n", "      <th>quality</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>oscillating</td>\n", "      <td>True</td>\n", "      <td>None</td>\n", "      <td>{}</td>\n", "      <td>R²=0.9893(perfect)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          name  value  unit extra             quality\n", "0  oscillating   True  None    {}  R²=0.9893(perfect)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(rsw.analysis.show_results())"]}, {"cell_type": "code", "execution_count": 11, "id": "1524b350", "metadata": {}, "outputs": [{"data": {"text/plain": ["R²=0.9893(perfect)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["rsw.analysis.quality"]}, {"cell_type": "code", "execution_count": 12, "id": "6d7c9206", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 2 Axes>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["rsw.analysis.drawer.figure"]}, {"cell_type": "code", "execution_count": null, "id": "3b848aa1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}