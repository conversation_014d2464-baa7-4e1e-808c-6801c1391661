# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.database.rst:2
msgid "pyQCat.database package"
msgstr ""

#: ../../source/api/pyQCat.database.rst:5
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.database.rst:8
msgid "pyQCat.database.ODM module"
msgstr ""

#: of pyQCat.database.ODM:1
msgid ""
"__date:         2019/09/18 __author:       Miracle Shih __corporation:  "
"OriginQuantum __usage:"
msgstr ""

#: of pyQCat.database.ODM.CustomWaveDoc:1 pyQCat.database.ODM.DcWaveDoc:1
#: pyQCat.database.ODM.QubitDoc:1 pyQCat.database.ODM.ReadoutDoc:1
#: pyQCat.database.ODM.SineWaveDoc:1 pyQCat.database.ODM.SweepDoc:1
#: pyQCat.database.ODM.TrigOutDoc:1 pyQCat.database.ODM.WaveFormDoc:1
#: pyQCat.database.ODM.XYWaveDoc:1 pyQCat.database.ODM.XYlineDoc:1
#: pyQCat.database.ODM.ZLineDoc:1 pyQCat.database.ODM.ZWaveDoc:1
msgid "Bases: :py:class:`~mongoengine.document.EmbeddedDocument`"
msgstr ""

#: of pyQCat.database.ODM.SineWaveDoc:1
msgid "The clasee used for getting a sin waveform with Measure AIO instrument."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.DcWaveDoc.pulse_width:1
#: pyQCat.database.ODM.ExperimentDoc.circuit_time:1
#: pyQCat.database.ODM.ExperimentDoc.file_flag:1
#: pyQCat.database.ODM.ExperimentDoc.status:1
#: pyQCat.database.ODM.InstrumentAIODoc.status:1
#: pyQCat.database.ODM.MeasureAIODoc.pulse_period:1
#: pyQCat.database.ODM.MeasureAIODoc.trig_way:1
#: pyQCat.database.ODM.QubitDoc.bit:1 pyQCat.database.ODM.ReadoutDoc.channel:1
#: pyQCat.database.ODM.ReadoutDoc.power_attenuation:1
#: pyQCat.database.ODM.ReadoutDoc.pulse_period:1
#: pyQCat.database.ODM.ReadoutDoc.sampling_time_width:1
#: pyQCat.database.ODM.ReadoutDoc.trig_way:1
#: pyQCat.database.ODM.SineWaveDoc.pulse_width:1
#: pyQCat.database.ODM.SweepDoc.channel:1 pyQCat.database.ODM.SweepDoc.repeat:1
#: pyQCat.database.ODM.TrigOutDoc.channel:1
#: pyQCat.database.ODM.TrigOutDoc.pulse_period:1
#: pyQCat.database.ODM.TrigOutDoc.pulse_width:1
#: pyQCat.database.ODM.TrigOutDoc.trig_way:1
#: pyQCat.database.ODM.XYlineDoc.channel:1
#: pyQCat.database.ODM.XYlineDoc.pulse_period:1
#: pyQCat.database.ODM.XYlineDoc.trig_way:1
#: pyQCat.database.ODM.XYlineDoc.trigger_out_delay:1
#: pyQCat.database.ODM.ZLineDoc.channel:1
#: pyQCat.database.ODM.ZLineDoc.pulse_period:1
#: pyQCat.database.ODM.ZLineDoc.trig_way:1
#: pyQCat.database.ODM.ZLineDoc.trigger_out_delay:1
msgid "32-bit integer field."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.DcWaveDoc.amplitude:1
#: pyQCat.database.ODM.QubitDoc.T1:1 pyQCat.database.ODM.QubitDoc.T2:1
#: pyQCat.database.ODM.QubitDoc.dc:1 pyQCat.database.ODM.QubitDoc.drive_freq:1
#: pyQCat.database.ODM.QubitDoc.drive_power:1
#: pyQCat.database.ODM.QubitDoc.probe_freq:1
#: pyQCat.database.ODM.QubitDoc.probe_power:1
#: pyQCat.database.ODM.ReadoutDoc.intermediate_frequency:1
#: pyQCat.database.ODM.ReadoutDoc.output_frequency:1
#: pyQCat.database.ODM.ReadoutDoc.pulse_power:1
#: pyQCat.database.ODM.SineWaveDoc.amplitude:1
#: pyQCat.database.ODM.SineWaveDoc.baseband_frequency:1
#: pyQCat.database.ODM.SineWaveDoc.initial_phase:1
#: pyQCat.database.ODM.XYWaveDoc.Xpi:1 pyQCat.database.ODM.XYWaveDoc.Xpi2:1
#: pyQCat.database.ODM.XYWaveDoc.Ypi:1 pyQCat.database.ODM.XYWaveDoc.Ypi2:1
#: pyQCat.database.ODM.XYWaveDoc.Zpi:1 pyQCat.database.ODM.XYWaveDoc.alpha:1
#: pyQCat.database.ODM.XYWaveDoc.delta:1
#: pyQCat.database.ODM.XYWaveDoc.detune_pi:1
#: pyQCat.database.ODM.XYWaveDoc.detune_pi2:1
#: pyQCat.database.ODM.XYWaveDoc.baseband_freq:1
#: pyQCat.database.ODM.XYWaveDoc.offset:1 pyQCat.database.ODM.XYWaveDoc.time:1
#: pyQCat.database.ODM.XYlineDoc.intermediate_frequency:1
#: pyQCat.database.ODM.XYlineDoc.output_frequency:1
#: pyQCat.database.ODM.XYlineDoc.pulse_power:1
#: pyQCat.database.ODM.ZWaveDoc.amp:1 pyQCat.database.ODM.ZWaveDoc.width:1
msgid "Floating point number field."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.CustomWaveDoc.wavefile:1
msgid "A binary data field."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.CustomWaveDoc.bigwavefile:1
msgid "A GridFS storage field."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.QubitDoc.Mwave:1
#: pyQCat.database.ODM.QubitDoc.XYwave:1 pyQCat.database.ODM.QubitDoc.Zwave:1
#: pyQCat.database.ODM.ReadoutDoc.waveform:1
#: pyQCat.database.ODM.SweepDoc.waveform:1
#: pyQCat.database.ODM.WaveFormDoc.custom_wave:1
#: pyQCat.database.ODM.WaveFormDoc.dc_wave:1
#: pyQCat.database.ODM.WaveFormDoc.sine_wave:1
#: pyQCat.database.ODM.XYlineDoc.waveform:1
#: pyQCat.database.ODM.ZLineDoc.waveform:1
msgid ""
"An embedded document field - with a declared document_type. Only valid "
"values are subclasses of :class:`~mongoengine.EmbeddedDocument`."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.qubit_info:1
#: pyQCat.database.ODM.ExperimentDoc.sweep_control:1
#: pyQCat.database.ODM.InstrumentAIODoc.AWG_output_channel:1
#: pyQCat.database.ODM.InstrumentAIODoc.RF_output_channel:1
#: pyQCat.database.ODM.InstrumentAIODoc.TO_output_channel:1
#: pyQCat.database.ODM.InstrumentAIODoc.VOL_output_channel:1
#: pyQCat.database.ODM.MeasureAIODoc.Read_out_control:1
#: pyQCat.database.ODM.MeasureAIODoc.Trig_out_control:1
#: pyQCat.database.ODM.MeasureAIODoc.XY_control:1
#: pyQCat.database.ODM.MeasureAIODoc.Z_dc_control:1
#: pyQCat.database.ODM.MeasureAIODoc.Z_flux_control:1
#: pyQCat.database.ODM.MeasureResultDoc.I:1
#: pyQCat.database.ODM.MeasureResultDoc.Q:1
#: pyQCat.database.ODM.MeasureResultDoc.amp:1
#: pyQCat.database.ODM.MeasureResultDoc.phase:1
#: pyQCat.database.ODM.ReadoutDoc.sampling_delay:1
#: pyQCat.database.ODM.ReadoutDoc.trigger_delay:1
#: pyQCat.database.ODM.SweepDoc.combination_points:1
#: pyQCat.database.ODM.SweepDoc.points:1
#: pyQCat.database.ODM.TrigOutDoc.trigger_delay:1
#: pyQCat.database.ODM.XYlineDoc.trigger_delay:1
#: pyQCat.database.ODM.ZLineDoc.trigger_delay:1
msgid ""
"A list field that wraps a standard field, allowing multiple instances of "
"the field to be used as a list in the database."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.qubit_info:4
#: pyQCat.database.ODM.ExperimentDoc.sweep_control:4
#: pyQCat.database.ODM.InstrumentAIODoc.AWG_output_channel:4
#: pyQCat.database.ODM.InstrumentAIODoc.RF_output_channel:4
#: pyQCat.database.ODM.InstrumentAIODoc.TO_output_channel:4
#: pyQCat.database.ODM.InstrumentAIODoc.VOL_output_channel:4
#: pyQCat.database.ODM.MeasureAIODoc.Read_out_control:4
#: pyQCat.database.ODM.MeasureAIODoc.Trig_out_control:4
#: pyQCat.database.ODM.MeasureAIODoc.XY_control:4
#: pyQCat.database.ODM.MeasureAIODoc.Z_dc_control:4
#: pyQCat.database.ODM.MeasureAIODoc.Z_flux_control:4
#: pyQCat.database.ODM.MeasureResultDoc.I:4
#: pyQCat.database.ODM.MeasureResultDoc.Q:4
#: pyQCat.database.ODM.MeasureResultDoc.amp:4
#: pyQCat.database.ODM.MeasureResultDoc.phase:4
#: pyQCat.database.ODM.ReadoutDoc.sampling_delay:4
#: pyQCat.database.ODM.ReadoutDoc.trigger_delay:4
#: pyQCat.database.ODM.SweepDoc.combination_points:4
#: pyQCat.database.ODM.SweepDoc.points:4
#: pyQCat.database.ODM.TrigOutDoc.trigger_delay:4
#: pyQCat.database.ODM.XYlineDoc.trigger_delay:4
#: pyQCat.database.ODM.ZLineDoc.trigger_delay:4
msgid "If using with ReferenceFields see: :ref:`many-to-many-with-listfields`"
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.qubit_info:7
#: pyQCat.database.ODM.ExperimentDoc.sweep_control:7
#: pyQCat.database.ODM.InstrumentAIODoc.AWG_output_channel:7
#: pyQCat.database.ODM.InstrumentAIODoc.RF_output_channel:7
#: pyQCat.database.ODM.InstrumentAIODoc.TO_output_channel:7
#: pyQCat.database.ODM.InstrumentAIODoc.VOL_output_channel:7
#: pyQCat.database.ODM.MeasureAIODoc.Read_out_control:7
#: pyQCat.database.ODM.MeasureAIODoc.Trig_out_control:7
#: pyQCat.database.ODM.MeasureAIODoc.XY_control:7
#: pyQCat.database.ODM.MeasureAIODoc.Z_dc_control:7
#: pyQCat.database.ODM.MeasureAIODoc.Z_flux_control:7
#: pyQCat.database.ODM.MeasureResultDoc.I:7
#: pyQCat.database.ODM.MeasureResultDoc.Q:7
#: pyQCat.database.ODM.MeasureResultDoc.amp:7
#: pyQCat.database.ODM.MeasureResultDoc.phase:7
#: pyQCat.database.ODM.ReadoutDoc.sampling_delay:7
#: pyQCat.database.ODM.ReadoutDoc.trigger_delay:7
#: pyQCat.database.ODM.SweepDoc.combination_points:7
#: pyQCat.database.ODM.SweepDoc.points:7
#: pyQCat.database.ODM.TrigOutDoc.trigger_delay:7
#: pyQCat.database.ODM.XYlineDoc.trigger_delay:7
#: pyQCat.database.ODM.ZLineDoc.trigger_delay:7
msgid "Required means it cannot be empty - as the default for ListFields is []"
msgstr ""

#: of pyQCat.database.ODM.ExperimentDoc:1
#: pyQCat.database.ODM.InstrumentAIODoc:1 pyQCat.database.ODM.MeasureAIODoc:1
#: pyQCat.database.ODM.MeasureResultDoc:1
msgid "Bases: :py:class:`~mongoengine.document.Document`"
msgstr ""

#: of pyQCat.database.ODM.DoesNotExist:1
msgid "Bases: :py:class:`~mongoengine.errors.DoesNotExist`"
msgstr ""

#: of pyQCat.database.ODM.MultipleObjectsReturned:1
msgid "Bases: :py:class:`~mongoengine.errors.MultipleObjectsReturned`"
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.id:1
#: pyQCat.database.ODM.InstrumentAIODoc.id:1
#: pyQCat.database.ODM.MeasureAIODoc.id:1
#: pyQCat.database.ODM.MeasureResultDoc.id:1
msgid "A field wrapper around MongoDB's ObjectIds."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.objects:1
#: pyQCat.database.ODM.InstrumentAIODoc.objects:1
#: pyQCat.database.ODM.MeasureAIODoc.objects:1
#: pyQCat.database.ODM.MeasureResultDoc.objects:1
msgid "The default QuerySet Manager."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.objects:3
#: pyQCat.database.ODM.InstrumentAIODoc.objects:3
#: pyQCat.database.ODM.MeasureAIODoc.objects:3
#: pyQCat.database.ODM.MeasureResultDoc.objects:3
msgid ""
"Custom QuerySet Manager functions can extend this class and users can add"
" extra queryset functionality.  Any custom manager methods must accept a "
":class:`~mongoengine.Document` class as its first argument, and a "
":class:`~mongoengine.queryset.QuerySet` as its second argument."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.objects:8
#: pyQCat.database.ODM.InstrumentAIODoc.objects:8
#: pyQCat.database.ODM.MeasureAIODoc.objects:8
#: pyQCat.database.ODM.MeasureResultDoc.objects:8
msgid ""
"The method function should return a "
":class:`~mongoengine.queryset.QuerySet` , probably the same one that was "
"passed in, but modified in some way."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.label:1
#: pyQCat.database.ODM.QubitDoc.name:1 pyQCat.database.ODM.QubitDoc.sample:1
#: pyQCat.database.ODM.SweepDoc.func:1
msgid "A unicode string field."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.SweepDoc.synchro:1
msgid "Boolean field type."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.date_modified:1
msgid "Datetime field."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.date_modified:3
msgid ""
"Uses the python-dateutil library if available alternatively use "
"time.strptime to parse the dates.  Note: python-dateutil's parser is "
"fully featured and when installed you can utilise it to convert varying "
"types of date formats into valid python datetime objects."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.date_modified:8
msgid ""
"Note: To default the field to the current datetime, use: "
"DateTimeField(default=datetime.utcnow)"
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.date_modified:12
msgid "Note: Microseconds are rounded to the nearest millisecond."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.date_modified:11
msgid ""
"Pre UTC microsecond support is effectively broken. Use "
":class:`~mongoengine.fields.ComplexDateTimeField` if you need accurate "
"microsecond support."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.measure_aio:1
#: pyQCat.database.ODM.InstrumentAIODoc.measure_aio:1
msgid ""
"A reference to a document that will be automatically dereferenced on "
"access (lazily)."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.measure_aio:4
#: pyQCat.database.ODM.InstrumentAIODoc.measure_aio:4
msgid ""
"Note this means you will get a database I/O access everytime you access "
"this field. This is necessary because the field returns a "
":class:`~mongoengine.Document` which precise type can depend of the value"
" of the `_cls` field present in the document in database. In short, using"
" this type of field can lead to poor performances (especially if you "
"access this field only to retrieve it `pk` field which is already known "
"before dereference). To solve this you should consider using the "
":class:`~mongoengine.fields.LazyReferenceField`."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.measure_aio:13
#: pyQCat.database.ODM.InstrumentAIODoc.measure_aio:13
msgid ""
"Use the `reverse_delete_rule` to handle what should happen if the "
"document the field is referencing is deleted.  EmbeddedDocuments, "
"DictFields and MapFields does not support reverse_delete_rule and an "
"`InvalidDocumentError` will be raised if trying to set on one of these "
"Document / Field types."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.measure_aio:18
#: pyQCat.database.ODM.InstrumentAIODoc.measure_aio:18
msgid "The options are:"
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.measure_aio:20
#: pyQCat.database.ODM.InstrumentAIODoc.measure_aio:20
msgid "DO_NOTHING (0)  - don't do anything (default)."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.measure_aio:21
#: pyQCat.database.ODM.InstrumentAIODoc.measure_aio:21
msgid "NULLIFY    (1)  - Updates the reference to null."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.measure_aio:22
#: pyQCat.database.ODM.InstrumentAIODoc.measure_aio:22
msgid "CASCADE    (2)  - Deletes the documents associated with the reference."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.measure_aio:23
#: pyQCat.database.ODM.InstrumentAIODoc.measure_aio:23
msgid "DENY       (3)  - Prevent the deletion of the reference object."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.measure_aio:24
#: pyQCat.database.ODM.InstrumentAIODoc.measure_aio:24
msgid ""
"PULL       (4)  - Pull the reference from a "
":class:`~mongoengine.fields.ListField` of references"
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.measure_aio:26
#: pyQCat.database.ODM.InstrumentAIODoc.measure_aio:26
msgid ""
"Alternative syntax for registering delete rules (useful when implementing"
" bi-directional delete rules)"
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.IF:1
#: pyQCat.database.ODM.ExperimentDoc.index:1
#: pyQCat.database.ODM.ExperimentDoc.measure_data:1
msgid ""
"A dictionary field that wraps a standard Python dictionary. This is "
"similar to an embedded document, but the structure is not defined."
msgstr ""

#: ../../docstring of pyQCat.database.ODM.ExperimentDoc.IF:5
#: pyQCat.database.ODM.ExperimentDoc.index:5
#: pyQCat.database.ODM.ExperimentDoc.measure_data:5
msgid "Required means it cannot be empty - as the default for DictFields is {}"
msgstr ""

#: ../../source/api/pyQCat.database.rst:16
msgid "pyQCat.database.kernel module"
msgstr ""

#: of pyQCat.database.kernel:1
msgid ""
"__date:         2018/0/0 __author:       Miracle Shih __corporation:  "
"OriginQuantum __usage:"
msgstr ""

#: of pyQCat.database.kernel.Kernel:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.database.kernel.Kernel.set_waveform_module:1
msgid "Set the `WaveFormDoc` object"
msgstr ""

#: of pyQCat.database.kernel.Kernel._get_target_module:1
msgid "return the target module name"
msgstr ""

#: ../../source/api/pyQCat.database.rst:24
msgid "Module contents"
msgstr ""

#: of pyQCat.database:3
msgid "DataBase Structures (:mod:`pyQCat.database`)"
msgstr ""

#: of pyQCat.database:5
msgid "DataBase modules, database related models."
msgstr ""

#~ msgid "Bases: :py:class:`~mongoengine.document.EmbeddedDocument`"
#~ msgstr ""

#~ msgid "If using with ReferenceFields see: :ref:`one-to-many-with-listfields`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~mongoengine.document.Document`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~mongoengine.errors.DoesNotExist`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~mongoengine.errors.MultipleObjectsReturned`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid "A field that validates input as an email address."
#~ msgstr ""

#~ msgid "Bases: :class:`mongoengine.document.EmbeddedDocument`"
#~ msgstr ""

#~ msgid "Bases: :class:`mongoengine.document.Document`"
#~ msgstr ""

#~ msgid "Bases: :class:`mongoengine.errors.DoesNotExist`"
#~ msgstr ""

#~ msgid "Bases: :class:`mongoengine.errors.MultipleObjectsReturned`"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

