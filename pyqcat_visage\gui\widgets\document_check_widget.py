# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/12/16
# __author:       <PERSON><PERSON><PERSON>

"""Experiment Document Translator Window."""

from __future__ import annotations

from typing import TYPE_CHECKING, List, Optional, Union

from loguru import logger
from PySide6.QtCharts import QChart, QLineSeries, QValueAxis
from PySide6.QtCore import Qt, QThread, Signal, Slot
from PySide6.QtGui import QPainter, QPen
from PySide6.QtWidgets import <PERSON><PERSON>abel, QWidget

from pyQCat.invoker import DataCenter
from pyQCat.qaio_property import QAIO
from pyQCat.structures import QDict
from pyQCat.tools.document_translate.translate import (
    TranslateResult,
    translate_experiment_document,
)
from pyqcat_visage.config import GUI_CONFIG
from pyqcat_visage.gui.document_check_ui import Ui_MainWindow
from pyqcat_visage.gui.widgets.document.doc_tree_model import DocTreeModel
from pyqcat_visage.gui.widgets.title_window import TitleWindow

if TYPE_CHECKING:
    from pyqcat_visage.gui.main_window import VisageGUI


class QueryThead(QThread):
    result_probe = Signal(TranslateResult)

    def __init__(self) -> None:
        super().__init__()

        self.eid = None
        self.running = False

    def run(self):
        self.running = True
        if self.eid:
            result: TranslateResult = translate_experiment_document(self.eid)
            self.result_probe.emit(result)
        self.running = False


class QMyChart(QChart):
    callout_press = Signal(str)


class DocumentCheckWindow(TitleWindow):
    """Experimental Data Package Retrieval Component.

    It can be used to:

        1. Retrieve the timing chart of the XY, Z, and M lines in the experiment;
        2. Retrieve the hardware parameters of the XY, Z, and M line channel output in the experiment;
        3. Check the scanning status of the experiment, including changes in waveform or hardware parameters;
        4. Review experimental registration related information, including user, experimental name, experimental
        environment, experimental options, analysis options, etc.;
    """

    ctrl_map = {
        "xy_control": "X",
        "z_flux_control": "Z",
        "readout_control": "M",
    }

    def __init__(self, gui: "VisageGUI", parent: QWidget = None):
        """Init DocumentCheckWindow.

        Args:
            gui (VisageGUi): Used to request experimental information from the database.
            parent (QWidget): Suggest inheriting from TitleWindow.
        """
        super().__init__(parent)

        self._ui = Ui_MainWindow()
        self._ui.setupUi(self)
        self._ui.splitter.setStretchFactor(0, 1)
        self._ui.splitter.setStretchFactor(1, 5)

        self.gui = gui

        self._result: TranslateResult | None = None
        self._model_data: dict = {}
        self._parts = None
        self._eid = None
        self._query_thread = QueryThead()

        self._setup_model()
        self._setup_status_bar()

        # Indicates that the engine should antialias edges of primitives if possible.
        self._ui.schedule_chart_view.setRenderHint(QPainter.Antialiasing)

        # Set the mouse pointer to a cross star
        self._ui.schedule_chart_view.setCursor(Qt.CursorShape.CrossCursor)

        # mouseMove signal/slot
        self._ui.schedule_chart_view.mouseMove.connect(self._do_chart_view_mouse_move)

        # config schedule line color
        self.__color_line = GUI_CONFIG.document_schedule.color_lines

        self._query_thread.result_probe.connect(self.refresh_view)

        self._init_chart()

    @property
    def model_data(self):
        return self._model_data

    def _do_chart_view_mouse_move(self, point):
        self._ui.schedule_chart_view.chart().mapToValue(point)

    def _init_chart(self):
        self.chart = QMyChart()
        self.chart.setAcceptHoverEvents(True)
        self._ui.schedule_chart_view.setChart(self.chart)
        self._ui.schedule_chart_view.init_callout()

        # create axis x
        self.__axis_x = QValueAxis()
        self.__axis_x.setTitleText(GUI_CONFIG.document_schedule.x_label)
        self.__axis_x.setRange(0, 10)
        self.__axis_x.setTickCount(GUI_CONFIG.document_schedule.x_tick_count)
        self.__axis_x.setLabelFormat("%.3f")
        self.chart.addAxis(self.__axis_x, Qt.AlignBottom)

        # create axis y
        self.__axis_y = QValueAxis()
        self.__axis_y.setTitleText(GUI_CONFIG.document_schedule.y_label)
        self.__axis_y.setRange(-1, 1)
        self.__axis_y.setTickCount(GUI_CONFIG.document_schedule.y_tick_count)
        self.__axis_y.setLabelFormat("%.3f")
        self.chart.addAxis(self.__axis_y, Qt.AlignLeft)

        self.chart.callout_press.connect(self._ui.schedule_chart_view.del_callout)

    def _setup_model(self):
        self.doc_tree_model = DocTreeModel(self, self.gui, self._ui.treeView)
        self._ui.treeView.setModel(self.doc_tree_model)

    def _setup_status_bar(self):
        self.user_label = QLabel(f" User (***) ")
        self._ui.statusbar.addWidget(self.user_label)

        self.type_label = QLabel(f" Type (***) ")
        self._ui.statusbar.addWidget(self.type_label)

        self.name_label = QLabel(f" Name (***) ")
        self._ui.statusbar.addWidget(self.name_label)

        self.time_label = QLabel(f" Time (***) ")
        self._ui.statusbar.addWidget(self.time_label)

        self.fake_label = QLabel(" Pulse (***) ")
        self._ui.statusbar.addWidget(self.fake_label)

    def _draw_raw_wave(self, waves, sample_rates, names, set_range):
        self.chart.removeAllSeries()

        for i, sample_rate in enumerate(sample_rates):
            series_wave = QLineSeries()
            pen = QPen(self.__color_line[i % len(self.__color_line)])
            pen.setWidth(GUI_CONFIG.document_schedule.pen_width)
            series_wave.setPen(pen)
            series_wave.setName(names[i])

            # bugfix 2023/03/31 by YangChao Zhao: The sampling rate cannot be taken as a round operation,
            # as a sampling rate of 1.2 leads to inaccurate single sampling cycles. If the
            # round operation is used, if the Z-line timing is too long, it will cause time
            # accuracy distortion
            vx = 0
            step = 1 / sample_rate
            wave = waves[i]

            point_count = len(wave)
            for j in range(point_count):
                value = wave[j]
                series_wave.append(vx, value)
                vx = vx + step

            if set_range:
                if i == 0:
                    self.__axis_x.setRange(0, vx - step)
                else:
                    cur_max = self.__axis_x.max()
                    self.__axis_x.setRange(0, max(vx - step, cur_max))

            self.chart.addSeries(series_wave)
            series_wave.attachAxis(self.__axis_x)
            series_wave.attachAxis(self.__axis_y)
            series_wave.hovered.connect(self._ui.schedule_chart_view.tooltip)
            series_wave.clicked.connect(self._ui.schedule_chart_view.keep_callout)

    def _init_selector(self):
        # set module combox
        self._ui.module_com.clear()
        self._ui.module_com.addItems([
            "context",
            "xy_control",
            "z_flux_control",
            "readout_control",
            "sweep_control",
            "execute_data",
        ])
        self._ui.module_com.setCurrentIndex(0)

        # set loop combox
        self._ui.loop_com.clear()
        loops = self._result.context.get("loops")
        loop_items = [str(i) for i in range(loops)]
        self._ui.loop_com.addItems(loop_items)

        # set combine combox
        unit_names = []
        for ctrl_name in list(self._result.static_control.keys()):
            unit = self._result.unit_map.get(ctrl_name)
            if unit:
                ctrl_name = f"{unit}-{ctrl_name}"
            unit_names.append(ctrl_name)
        self._ui.combo_com.set_units(unit_names)

    def _schedule_plot(
        self,
        parts: List = None,
        loop: Optional[Union[int, str]] = None,
        set_range: bool = True,
    ):
        loop = loop or self._ui.loop_com.currentText()
        show_delay = self._ui.show_delay.isChecked()
        # logger.info(f"Select Show Delay is {show_delay} .")

        if loop is None or loop == "":
            return

        loop = int(loop)

        parts = parts or self._parts
        modules, channels = [], []

        if parts is None:
            modules.append(self._ui.module_com.currentText())
            channels.append(self._ui.channel_com.currentText())
        else:
            self._parts = parts
            for part in parts:
                m, c = part
                modules.append(m) if m else modules.append(self._ui.module_com.currentText())
                channels.append(c) if c else channels.append(self._ui.channel_com.currentText())

        if len(modules) != len(channels):
            return

        waves = []
        sample_rates = []
        names = []

        for i, module in enumerate(modules):
            channel = channels[i]
            sample_rate = self._get_sample_rate(module)

            if module and channel and (loop or loop == 0):
                ctrl_name = f"{module}:waveform-C{channel}"
                module_data = self._result.static_control.get(ctrl_name)

                if not module_data:
                    return

                self._model_data = module_data
                self.doc_tree_model.refresh(expand=False)

                if show_delay is True:
                    waveform = self._result.waveform.get(ctrl_name)
                else:
                    waveform = self._result.nd_waveform.get(ctrl_name)

                # bugfix:
                if waveform:
                    if len(waveform) == self._result.context.get("loops"):
                        wave = waveform[int(loop)]
                    else:
                        wave = waveform[0]

                    if wave is not None and len(wave) > 0:
                        waves.append(wave)
                        sample_rates.append(sample_rate)
                else:
                    waves.append([])

                unit = self._result.unit_map.get(ctrl_name)
                if unit:
                    names.append(f"{unit}-{self.ctrl_map.get(module)}-{channel}-{sample_rate}")
                else:
                    names.append(f"{self.ctrl_map.get(module)}-{channel}-{sample_rate}")

        self._draw_raw_wave(waves, sample_rates, names, set_range)

    @staticmethod
    def _get_sample_rate(module: str):
        sample_rate_map = {
            "xy_control": QAIO.dac_sample_rate,
            "z_flux_control": QAIO.awg_sample_rate,
            "readout_control": QAIO.adda_sample_rate
        }
        return sample_rate_map.get(module.lower(), 1)

    @Slot(TranslateResult)
    def refresh_view(self, result: TranslateResult):
        logger.info("Start refresh result")
        self._ui.actionQuery.setEnabled(True)

        if result.is_done():
            # refresh base infos
            self._result = result
            self._ui.treeView.hide_placeholder_text()
            self.user_label.setText(f" User ({self._result.context.get('username', 'xxx')}) ")
            self.type_label.setText(f" Type ({self._result.context.get('task_type', 'xxx')}) ")
            self.name_label.setText(f" Name ({self._result.context.get('label', 'xxx')}) ")
            self.time_label.setText(f" Time ({self._result.context.get('date_modified', 'xxx')}) ")
            self.fake_label.setText(f" Time ({self._result.context.get('fake_pulse', 'xxx')}) ")

            # init some combox
            self._init_selector()

            # set experiment id
            self._eid = result.context.get("id")

    @Slot()
    def query(self):
        eid = self._ui.exp_id_edit.text().strip()
        if self._eid == eid:
            logger.warning(f"ID({eid}) result has exist, avoid repeat search!")
        else:
            self._ui.actionQuery.setEnabled(False)
            self._query_thread.eid = self._ui.exp_id_edit.text().strip()
            self._query_thread.start()

    @Slot()
    def enlarge(self):
        self._ui.schedule_chart_view.chart().zoom(1.2)

    @Slot()
    def narrow(self):
        self._ui.schedule_chart_view.chart().zoom(0.8)

    @Slot()
    def reset(self):
        self._ui.schedule_chart_view.chart().zoomReset()
        self.__axis_y.setRange(-1, 1)
        self._schedule_plot()

    @Slot(str)
    def change_module(self, name: str):
        if not self._ui.fix_canvas.isChecked():
            self.reset()

        if name == "sweep_control":
            self._model_data = self._result.sweep_control
            self.doc_tree_model.refresh(expand=False)
        elif name == "context":
            self._model_data = self._result.context
            self.doc_tree_model.refresh(expand=False)
        elif name == "execute_data":
            self._model_data = self._result.execute_data
            self.doc_tree_model.refresh(expand=False)
        elif name:
            ctrl_list = []
            for ctrl_name, ctrl in self._result.static_control.items():
                if ctrl_name.startswith(name):
                    ctrl_list.append(ctrl)
            if ctrl_list:
                self._ui.channel_com.clear()
                self._ui.channel_com.addItems([str(c.get("channel")) for c in ctrl_list])
                self._ui.channel_com.setCurrentIndex(0)

    @Slot(str)
    def change_channel(self, name: str):
        if not self._ui.fix_canvas.isChecked():
            self.reset()

        module_name = self._ui.module_com.currentText()
        ctrl_name = f"{module_name}:waveform-C{name}"

        data = self._result.static_control.get(ctrl_name)

        if data:
            self._model_data = data
            self.doc_tree_model.refresh(expand=False)
            self._schedule_plot(parts=[(None, name)])

    @Slot(str)
    def change_loop(self, name: str):
        if not self._ui.fix_canvas.isChecked():
            self.reset()

        self._schedule_plot(loop=name, set_range=False)

    @Slot()
    def compare_pulse(self):
        if not self._ui.fix_canvas.isChecked():
            self.reset()

        items = self._ui.combo_com.currentText()
        if len(items) == 0:
            self.handler_ret_data(QDict(code=600, message="Please choose combo item first!"))
        else:
            parts = []
            for item in items:
                module, cs = item.split(":")
                channel = cs.split("C")[-1]
                # split info of module: "q7-xy_control"
                if "-" in module:
                    module = module.rsplit("-", 1)[1]
                parts.append([module, channel])
            self._schedule_plot(parts=parts)
