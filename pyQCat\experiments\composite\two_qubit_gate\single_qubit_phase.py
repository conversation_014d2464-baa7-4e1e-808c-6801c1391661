# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/27
# __author:       <PERSON>, <PERSON><PERSON><PERSON>

"""
SingleQubitPhase Experiment.
"""

from collections import defaultdict

import numpy as np

from ....analysis.library import SQPhaseTMSEAnalysis
from ....analysis.specification import ParameterRepr
from ....parameters import options_wrapper
from ....structures import Options
from ....tools import qarange
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import CPhaseTMSE, CZAssist


@options_wrapper
class SQPhaseTMSE(CompositeExperiment):
    _sub_experiment_class = CPhaseTMSE

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("gate_nums", list)
        options.gate_nums = qarange(1, 8, 1)
        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("std_limit", float)
        options.result_parameters = []
        options.std_limit = 0.5
        return options

    def _check_options(self):
        super()._check_options()

        for qubit in self.qubits:
            if qubit.name in [self.qubit_pair.qh, self.qubit_pair.ql]:
                self.analysis_options.result_parameters.append(
                    ParameterRepr(
                        name=f"{qubit.name}_phase",
                        repr=f"{qubit.name}_phase",
                        unit="",
                        param_path=f"QubitPair.metadata.std.cz.params.{qubit.name}.phase",
                    )
                )
        self.analysis_options.result_name = self.qubit_pair.name

        self.set_run_options(
            x_data=list(
                range(len(self.qubits) * len(self.experiment_options.gate_nums))
            ),
            analysis_class=SQPhaseTMSEAnalysis,
        )

    def _setup_child_experiment(self, exp: CPhaseTMSE, index: int, value: float):
        exp.run_options.index = index
        gate_nums = self.experiment_options.gate_nums

        q_idx, g_idx = divmod(index, len(gate_nums))
        qubit = self.qubits[q_idx]
        cz_num = gate_nums[g_idx]

        total = len(self.run_options.x_data)
        describe = f"{qubit.name}-cz_num={cz_num}"

        exp.set_parent_file(self, describe, index, total)
        exp.set_experiment_options(
            cz_num=cz_num, ramsey_bit=qubit.name, phase_mode="single", mode="SE-TM"
        )
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, child_exp: CPhaseTMSE):
        delta_phase = child_exp.experiment_data.y_data.get("delta_phase")
        child_exp.analysis.provide_for_parent.update(
            {f"{child_exp.experiment_options.ramsey_bit}_phase": -delta_phase[0]}
        )

    def _create_composite_experiment_data(self, x_data):
        experiment_data = super()._create_composite_experiment_data(x_data)
        experiment_data._x_data = np.asarray(self.experiment_options.gate_nums)

        y_data = defaultdict(list)
        for child_exp in self._experiments:
            for k, v in child_exp.analysis.provide_for_parent.items():
                y_data[k].append(v)

        experiment_data._y_data = y_data
        return experiment_data


class SingleQubitPhase(CompositeExperiment):
    _sub_experiment_class = CZAssist

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("gate_nums", list)
        options.gate_nums = qarange(1, 8, 1)
        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("std_limit", float)
        options.result_parameters = []
        options.std_limit = 0.5
        return options

    def _check_options(self):
        super()._check_options()

        for qubit in self.qubits:
            self.analysis_options.result_parameters.append(
                ParameterRepr(
                    name=f"{qubit.name}_phase",
                    repr=f"{qubit.name}_phase",
                    unit="",
                    param_path=f"QubitPair.metadata.std.cz.params.{qubit.name}.phase",
                )
            )
        self.analysis_options.result_name = self.qubit_pair.name
        self.set_run_options(
            x_data=list(
                range(len(self.qubits) * len(self.experiment_options.gate_nums) * 2)
            ),
            analysis_class=SQPhaseTMSEAnalysis,
        )

    def _setup_child_experiment(self, exp: CZAssist, index: int, value: float):
        exp.run_options.index = index
        gate_nums = self.experiment_options.gate_nums

        q_idx, gg_idx = divmod(index, len(gate_nums) * 2)
        g_idx, a_idx = divmod(gg_idx, 2)
        qubit = self.qubits[q_idx]
        cz_num = gate_nums[g_idx]
        add_cz = True if a_idx else False

        total = len(self.run_options.x_data)
        describe = f"{qubit.name}-cz_num={cz_num}-add_cz={add_cz}"

        exp.set_parent_file(self, describe, index, total)
        exp.set_experiment_options(cz_num=cz_num, add_cz=add_cz, ramsey_bit=qubit.name)
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, child_exp: CZAssist):
        child_exp.analysis.provide_for_parent.update(
            {
                f"{child_exp.experiment_options.ramsey_bit}_phase": -child_exp.analysis.results.phase.value
            }
        )

    def _create_composite_experiment_data(self, x_data):
        experiment_data = super()._create_composite_experiment_data(x_data)
        act_x_data = np.asarray(self.experiment_options.gate_nums)
        experiment_data._x_data = act_x_data

        y_data = defaultdict(list)
        for child_exp in self._experiments:
            for k, v in child_exp.analysis.provide_for_parent.items():
                y_data[k].append(v)

        for k in list(y_data.keys()):
            phase_list = y_data[k]
            new_phase_list = []
            for i in range(len(act_x_data)):
                idx = i * 2
                new_phase_list.append(phase_list[idx + 1] - phase_list[idx])
            y_data[k] = new_phase_list

        experiment_data._y_data = y_data
        return experiment_data
