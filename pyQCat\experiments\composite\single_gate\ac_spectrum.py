# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/01
# __author:       <PERSON><PERSON><PERSON> <PERSON>
"""
AC Spectrum experiment.
"""

import copy

import numpy as np

from ....analysis.library import ACSpectrumAnalysis
from ....log import pyqlog
from ....qaio_property import QAIO
from ....structures import MetaData, Options
from ....tools.utilities import QDict, qarange, solve_equations, amp_to_freq
from ....types import ExperimentRunMode, QualityDescribe
from ...composite_experiment import CompositeExperiment
from ...single import <PERSON>uplerRamsey, CouplerRamseyByZZShift, Ramsey


class ACSpectrum(CompositeExperiment):
    """AC Spectrum experiment to get the relationship of qubit frequency-zamp."""

    _sub_experiment_class = Ramsey

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for AC spectrum experiment."""
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator(
            "fit_model_name",
            [
                "amp2freq_formula",
                "flat_top_amp2freq_formula",
                "flat_bottom_amp2freq_formula",
                "segm_formula",
            ],
        )

        options.fit_model_name = "amp2freq_formula"
        options.quality_bounds = [0.995, 0.992, 0.990]
        options.data_key = None
        options.segm_x_data = None
        options.segm_y_data = None
        options.fit_func = None
        options.w = None
        options.spectrum_type = None

        return options

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options for AC spectrum experiment.

        Experiment options:
            init_fringe (float): To initialize value of fringe.
            delays (Union[List, np.ndarray]): Delay time scanned when performing Ramsey
                                              experiments.
            z_amp_list (Union[List, np.ndarray]): Z line amplitude (flux pulse) sweep list.
            freq_bound (Optional[float], optional): Experiment will be stopped when qubit's
                                                    frequency delta value lower than this value.
                                                    Defaults to 800MHz.
            osc_freq_limit (Optional[float], optional): [description]. Defaults to 2.5.
        """
        options = super()._default_experiment_options()

        options.set_validator("z_amps", list)
        options.set_validator("delays", list)
        options.set_validator("freq_bound", float)
        options.set_validator("osc_freq_limit", float)
        options.set_validator("init_fringe", float)
        options.set_validator("spectrum_type", str)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])
        options.set_validator("fit_model_name_list", list)
        options.set_validator("use_ac_spectrum", bool)
        options.set_validator("start_from_max", bool)

        options.z_amps = qarange(-0.02, 0.4, 0.02)
        options.delays = qarange(200, 800, 10)
        options.freq_bound = 800
        options.osc_freq_limit = 2.5
        options.init_fringe = 10
        options.spectrum_type = "standard"
        options.fit_model_name_list = [
            "amp2freq_formula",
            "flat_bottom_amp2freq_formula",
            "flat_top_amp2freq_formula",
            "segm_formula",
        ]
        options.use_ac_spectrum = False
        options.start_from_max = False
        options.run_mode = ExperimentRunMode.sync_mode
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()
        options.z_amps = []
        options.osc_freq_list = []
        options.r_square_list = []
        options.f10_list = []
        options.fringe_list = []
        options.detune_list = []
        options.abnormal_count = 0
        options.segm_data = QDict()
        options.result_path = None
        options.result_value = None
        options.z_amps_sort = None
        options.f10_list_sort = None
        options.r_square_list_sort = None

        return options

    def _metadata(self) -> MetaData:
        """Return experiment metadata for ExperimentData."""
        metadata = super()._metadata()
        metadata.draw_meta = {"Freq reduce": max(self.run_options.f10_list) - min(self.run_options.f10_list)}

        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        if self.child_experiment.is_coupler_exp is True:
            result_name = self.child_experiment.coupler.name
        else:
            result_name = self.child_experiment.qubit.name
        self.set_analysis_options(result_name=result_name)

        # sort x_data with abs order
        # feature: Adjusted by TianLe Wang in the laboratory
        eop = self.experiment_options
        if eop.use_ac_spectrum and eop.start_from_max:
            z_amps_raw = self.experiment_options.z_amps
            # 这部操作目的是将z_amps以-idle_point为中心(dc_max)排序，实际扫描范围为dc_max + z_amps
            # z_amps_sort = (np.array(sorted(z_amps_raw + self.qubits[0].idle_point, key=abs)) - 2*self.qubits[0].idle_point).tolist()
            z_amps_sort = (np.array(sorted(z_amps_raw, key=abs)) - self.qubits[0].idle_point).tolist()
        else:
            z_amps_raw = self.experiment_options.z_amps
            z_amps_sort = sorted(z_amps_raw, key=abs)
        self.experiment_options.z_amps = z_amps_sort

    def _set_ac_result_path(self, spectrum_type: str = None):
        """Set path to save parameter of Qubit or Coupler."""

        for key, result in self.analysis.results.items():
            if key == "params":
                spectrum_type = "standard"
                if result.value:
                    self.file.save_data(result.value, name="ac_params")
            elif key == "bottom_left":
                spectrum_type = "bottom_left"
            elif key == "bottom_right":
                spectrum_type = "bottom_right"
            elif key == "top":
                spectrum_type = "top"
            else:
                spectrum_type = None
            if spectrum_type and result.value:
                self.file.save_data(result.value, name=f"ac_params_{spectrum_type}")
                if type(self).__name__ == "ACSpectrumByCoupler":
                    continue
                if self.child_experiment.is_coupler_exp is True:
                    result.extra["path"] = f"Coupler.ac_spectrum.{spectrum_type}"
                else:
                    result.extra["path"] = f"Qubit.ac_spectrum.{spectrum_type}"

    async def _sync_composite_run(self):
        """
        The first three points are used for quadratic function fitting to predict
        the bit frequency value f_guess at the current z_amp.
        By running this experiment, you can get the relationship between flux pulse's
        amplitude and quits frequency.
        """
        # super().run()
        qubit = self.qubits[0]
        fd = qubit.drive_freq  # MHz
        # baseband_freq = qubit.XYwave.baseband_freq  # MHz
        # Solve the problem of IF and pulse time binding.
        baseband_freq = 0

        max_sample_freq = (
            1e9 / (self.experiment_options.delays[1] - self.experiment_options.delays[0]) / 2 * 1e-6
        )  # MHz

        index = 0
        for i, z_amp in enumerate(self.experiment_options.z_amps):
            # initialize sub-experiment.
            ramsey_exp = copy.deepcopy(self.child_experiment)

            # update fringe
            real_fringe = self._update_fringe(index, baseband_freq, fd, z_amp)
            # update experiment options.
            # ramsey_exp.set_experiment_options(
            #     delays=self.experiment_options.delays, fringe=real_fringe, z_amp=z_amp
            # )
            if self.experiment_options.use_ac_spectrum:
                f_guess = amp_to_freq(physical_unit=qubit, z_amp=self.qubits[0].idle_point + z_amp)

                calculate_fringe = round(
                    (self.experiment_options.init_fringe + f_guess - qubit.drive_freq),
                    3,
                )

                pyqlog.info(f'{self.qubits[0].name} f_guess: {f_guess}')

                if i < 3:
                    real_fringe = calculate_fringe
                    pyqlog.log(
                        "EXP",
                        f"z_amp = {z_amp}v,f_guess = {f_guess}MHz,init_fringe = {self.experiment_options.init_fringe}MHz, calculate_fringe = {calculate_fringe}MHz",
                    )
                    ramsey_exp.set_experiment_options(
                        delays=self.experiment_options.delays, fringe=real_fringe, z_amp=z_amp
                    )
                else:
                    ramsey_exp.set_experiment_options(
                        delays=self.experiment_options.delays, fringe=real_fringe, z_amp=z_amp
                    )
            else:
                ramsey_exp.set_experiment_options(
                    delays=self.experiment_options.delays, fringe=real_fringe, z_amp=z_amp
                )
            self._check_simulator_data(ramsey_exp, i)
            ramsey_exp.set_parent_file(self, f"z_amp={z_amp}", i, len(self.experiment_options.z_amps))

            # run ramsey
            pyqlog.log("EXP", f"z_amp = {z_amp}v, fringe = {real_fringe}MHz")

            await ramsey_exp.run_experiment()
            # ramsey_exp.clear_params()
            # real_fringe = ramsey_exp.run_options.real_fringe
            result = ramsey_exp.analysis.results

            osc_freq = result.freq.value
            r_square = ramsey_exp.analysis.quality.value

            # # old version
            # if QAIO.type in [QAIO.qaio_8, QAIO.qaio_30]:
            #     # 1,2 inst
            #     f10 = fd + baseband_freq - fringe - osc_freq
            # else:
            #     # 3 inst
            #     f10 = fd - (baseband_freq - fringe) - osc_freq

            # edited by WTL
            fringe = real_fringe - baseband_freq
            # if self.experiment_options.use_init_fringe:
            #     fringe = self.experiment_options.init_fringe
            if QAIO.type in [QAIO.qaio_8, QAIO.qaio_30]:
                f10 = fd - fringe + osc_freq if self.experiment_options.init_fringe > 0 else fd - fringe - osc_freq
            else:
                f10 = fd + fringe - osc_freq if self.experiment_options.init_fringe > 0 else fd + fringe + osc_freq

            ramsey_exp.analysis.provide_for_parent["qubit_freq"] = f10

            pyqlog.log("RESULT", f"osc_freq: {osc_freq}, new f10: {f10}")

            # validate osc freq
            self._validate_osc_freq(index, osc_freq, max_sample_freq)
            if self.run_options.abnormal_count >= 3:
                self._abnormal_diagnose(ramsey_exp)
                break
            if self.run_options.abnormal_count != 0:
                continue

            # validate f10 and T2
            validation = self._validate_result(fd=fd, f10=f10)
            if not validation:
                break

            # record experiment data during running.
            self.run_options.osc_freq_list.append(osc_freq)
            self.run_options.f10_list.append(f10)
            self.run_options.detune_list.append(fd - f10)
            self.run_options.fringe_list.append(real_fringe)
            self.run_options.z_amps.append(z_amp)
            self.run_options.r_square_list.append(r_square)
            self._experiments.append(ramsey_exp)

            pyqlog.info(f"z_amp={z_amp} qubit freq = {f10}MHz")

            index += 1
            self.file.save_data(
                np.array(self.run_options.z_amps),
                np.array(self.run_options.f10_list),
                np.array(self.run_options.detune_list),
                np.array(self.run_options.fringe_list),
                np.array(self.run_options.osc_freq_list),
                np.array(self.run_options.r_square_list),
                name=self._label + "unsorted",
            )
        # dealing with the situation of x_data disorder
        data_sorted = np.array(
            sorted(
                zip(
                    self.run_options.z_amps[:index],
                    self.run_options.f10_list,
                    self.run_options.fringe_list[:index],
                    self.run_options.osc_freq_list,
                    self.run_options.r_square_list,
                )
            )
        )
        z_amps_sort, f10_list_sort, fringe_list_sort, osc_freq_list_sort, r_square_list_sort = [
            data_sorted[:, i] for i in range(data_sorted.shape[1])
        ]
        self.set_run_options(
            f10_list_sort=f10_list_sort,
            z_amps_sort=z_amps_sort,
            r_square_list_sort=r_square_list_sort,
        )
        detune_list = fd - f10_list_sort
        self.file.save_data(
            z_amps_sort,
            f10_list_sort,
            detune_list,
            fringe_list_sort,
            osc_freq_list_sort,
            r_square_list_sort,
            name=self._label,
        )

        ramsey_exp_list = []
        for ramsey_exp, f10 in zip(self._experiments, f10_list_sort):
            ramsey_exp.analysis.provide_for_parent["qubit_freq"] = f10
            ramsey_exp_list.append(ramsey_exp)
        self._experiments = ramsey_exp_list
        count = 0
        fit_model_name_list = self.experiment_options.fit_model_name_list
        while True:
            self.set_analysis_options(fit_model_name=fit_model_name_list[count])
            try:
                self._run_analysis(x_data=np.asarray(z_amps_sort), analysis_class=ACSpectrumAnalysis)
                quality_value = self.analysis.quality.descriptor
            except Exception as e:
                pyqlog.error(f"Ac spectrum analysis error, error msg={e}")
                break
            if quality_value == QualityDescribe.perfect:
                self._set_ac_result_path()
                break
            elif count == len(fit_model_name_list) - 1:
                break
            count += 1

    def _update_fringe(self, index: int, baseband_freq: float, fd: float, z_amp: float) -> float:
        """Update fringe frequency.

        Args:
            index (int): Ramsey index number.
            baseband_freq (float): baseband_freq.
            fd (float): Qubit frequency get from Ramsey exp.
            z_amp (float): Z line amp at now.

        Returns:
            float: Fringe frequency for the next Ramsey experiment.
        """
        # # old version
        # if index < 3:
        #     if QAIO.type in [QAIO.qaio_8, QAIO.qaio_30]:
        #         # 1,2 inst
        #         fringe = baseband_freq - self.experiment_options.init_fringe
        #     else:
        #         # 3 inst
        #         fringe = baseband_freq + self.experiment_options.init_fringe
        # else:
        #     f_guess = self._guess_next_freq(index, z_amp)
        #     if QAIO.type in [QAIO.qaio_8, QAIO.qaio_30]:
        #         # 1,2 inst
        #         fringe = (baseband_freq - self.experiment_options.init_fringe + fd - f_guess)
        #     else:
        #         # 3 inst
        #         fringe = (baseband_freq + (self.experiment_options.init_fringe - fd + f_guess))

        # eop = self.experiment_options
        real_fringe = baseband_freq + self.experiment_options.init_fringe

        if index < 3:
            # if eop.use_ac_spectrum and eop.start_from_max:
            #     fq_max, fq_min = get_bound_ac_spectrum(self.qubits[0])
            #     df = fq_max - fd
            #     print(f'{self.qubits[0].name} fq_max - fd: {fq_max - fd}')
            #     if QAIO.type in [QAIO.qaio_8, QAIO.qaio_30]:
            #         updated_fringe = real_fringe - df
            #     else:
            #         updated_fringe = real_fringe + df
            # else:
            updated_fringe = real_fringe

        else:
            f_guess = self._guess_next_freq(index, z_amp)
            df = f_guess - fd
            if QAIO.type in [QAIO.qaio_8, QAIO.qaio_30]:
                updated_fringe = real_fringe - df
            else:
                updated_fringe = real_fringe + df
        return updated_fringe

    def _guess_next_freq(self, index: int, z_amp: float) -> float:
        """Predict next drive frequency."""
        A = self.experiment_options.z_amps[index - 3 : index]
        y = self.run_options.f10_list[index - 3 : index]
        a, b, c = solve_equations(A, y)
        pyqlog.debug(f"abc={[a, b, c]}")
        f_guess = a * z_amp**2 + b * z_amp + c  # MHz
        return f_guess

    def _validate_osc_freq(self, index: int, osc_freq: float, max_sample_freq: float):
        """Validate oscillation frequency.

        Args:
            index (int): The index of the
            osc_freq (float):
            max_sample_freq (float):
        """
        # todo: use goodness of fit to judge.
        if index < 3:
            if abs(osc_freq) > max_sample_freq:
                pyqlog.error(f"osc freq={osc_freq}MHz is out of range!")
                self.run_options.abnormal_count = 3
        elif abs(osc_freq - abs(self.experiment_options.init_fringe)) > self.experiment_options.osc_freq_limit:
            self.run_options.abnormal_count += 1
            pyqlog.error(f"osc freq={osc_freq}MHz is not excepted!")
        else:
            self.run_options.abnormal_count = 0

    def _validate_result(self, fd: float, f10: float) -> bool:
        """
        Truncation condition of AC/DC Spectrum experiment.
        Args:
            fd (float): The value of qubit's drive frequency.
            f10 (float): The value of qubit's real frequency.

        Returns:
            bool variable. True represent validate successfully and False
            represent validate failed!
        """

        if fd - f10 > self.experiment_options.freq_bound:
            pyqlog.info(f"qubit frequency={f10}MHz too low! Stop experiment.")
            return False
        else:
            return True

    @staticmethod
    def _abnormal_diagnose(ramsey_exp: Ramsey):
        """Diagnose experiment failed possible reason."""
        result = ramsey_exp.analysis.results
        if not result.stft_flag:
            pyqlog.error(
                "The adjust ability of qubit frequency versus AC "
                "voltage is bad!\nPlease check whether the Z-line "
                "distortion is corrected"
            )


class CouplerACSpectrum(ACSpectrum):
    _sub_experiment_class = CouplerRamsey


class ACSpectrumByCoupler(ACSpectrum):
    _sub_experiment_class = CouplerRamseyByZZShift
