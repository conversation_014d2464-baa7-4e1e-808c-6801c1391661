# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.full_width_half_max.rst:2
msgid "pyQCat.analysis.algorithms.full\\_width\\_half\\_max"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max:1
msgid ""
"Get full width half maximum value of the peak. Offset of y should be "
"removed."
msgstr "获取峰值的全宽半最大值。 应删除 y 的偏移量。"

#: of pyQCat.analysis.algorithms.guess.full_width_half_max
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max:4
msgid "Array of x values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max:6
msgid "Array of y values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max:8
msgid "Index of peak."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max:10
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max:11
msgid "FWHM of the peak."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max
msgid "Raises"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.full_width_half_max:13
msgid "When peak is too broad and line width is not found."
msgstr "当峰太宽且找不到线宽时。"

