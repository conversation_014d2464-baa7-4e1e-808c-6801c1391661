<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>587</width>
    <height>411</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Create Task</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="7,1">
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>Task</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="0" column="0">
       <widget class="QWidget" name="widget" native="true">
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <widget class="QWidget" name="widget_2" native="true">
           <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="2,8">
            <property name="spacing">
             <number>9</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="task_name">
              <property name="text">
               <string>task_name</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit"/>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="widget_3" native="true">
           <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="2,8">
            <property name="spacing">
             <number>9</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="dag_policy">
              <property name="text">
               <string>dag_policy</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="SearchComboBox" name="DagPolicyText">
              <item>
               <property name="text">
                <string>schedule</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>timing</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QTreeView" name="treeView"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <widget class="QPushButton" name="pushButton">
        <property name="text">
         <string>OK</string>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../_imgs/ok.png</normaloff>../../_imgs/ok.png</iconset>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QPushButton" name="pushButton_2">
        <property name="text">
         <string>Cancel</string>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../_imgs/cancel.png</normaloff>../../_imgs/cancel.png</iconset>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SearchComboBox</class>
   <extends>QComboBox</extends>
   <header location="global">..combox_custom.combox_search</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>pushButton</sender>
   <signal>clicked()</signal>
   <receiver>Dialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>547</x>
     <y>107</y>
    </hint>
    <hint type="destinationlabel">
     <x>305</x>
     <y>-11</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_2</sender>
   <signal>clicked()</signal>
   <receiver>Dialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>547</x>
     <y>202</y>
    </hint>
    <hint type="destinationlabel">
     <x>222</x>
     <y>-9</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>DagPolicyText</sender>
   <signal>currentTextChanged(QString)</signal>
   <receiver>Dialog</receiver>
   <slot>select_policy()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>266</x>
     <y>125</y>
    </hint>
    <hint type="destinationlabel">
     <x>283</x>
     <y>144</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>select_policy()</slot>
 </slots>
</ui>
