# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/02/14
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

"""
Leakage Once Experiment.
"""

import json
from copy import deepcopy

import numpy as np

from ....analysis import LeakageOnceAnalysis
from ....errors import ExperimentFieldError
from ....parameters import options_wrapper
from ....pulse.pulse_function import stimulate_state_pulse, zero_pulse
from ....pulse.pulse_lib import Constant
from ....pulse_adjust import params_to_pulse
from ....structures import MetaData, Options
from ....tools.utilities import cz_flow_options_adapter
from ....types import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment
from .swap_once import (
    validate_data_key,
    validate_qubit_pair_cz_std,
    validate_two_qubit_exp_read_options,
)


@options_wrapper
class LeakageOnce(TopExperiment):
    """LeakageOnce scan default name z amp list."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("swap_state", ["10", "11", "01"])
        options.set_validator("scan_name", str)
        options.set_validator("z_amp_list", list)
        options.set_validator("cz_num", int)
        options.set_validator("scope", dict)
        options.set_validator("scope_detune", bool)
        options.set_validator("label", ["zz", "cz"])

        options.scope = {"l": 30, "r": 30, "p": 31}
        options.swap_state = "11"
        options.readout_type = "ql-01"
        options.scan_name = "qc"
        options.z_amp_list = None
        options.cz_num = 1
        options.scope_detune = False
        options.label = "cz"

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []
        options.offset_width = None
        options.support_context = [StandardContext.CGC]
        options.scan_map_data = {}
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options.

        Analysis Options:
            is_plot (bool): Plot or not analysis fit photo.
            data_key (List[str]): List of mark select data.

        """
        options = super()._default_analysis_options()
        options.set_validator("quality_bounds", list)
        options.set_validator(
            "fit_model_name",
            [
                "lorentzian",
                "bi_lorentz_tilt",
                "gauss_lorentzian",
                "skewed_lorentzian",
                "skewed_gauss_lorentz",
            ],
        )
        options.set_validator("cali_offset_method", ["direct", "undirect"])
        options.set_validator("adjust_noise", bool)
        options.set_validator("cut_index", bool)

        options.data_key = None
        options.quality_bounds = [0.8, 0.6, 0.5]
        options.use_qc = False
        options.scan_qh = True
        options.cali_offset_method = "direct"
        options.fit_model_name = "skewed_lorentzian"
        options.adjust_noise = True
        options.n_multiple = 4.0
        options.cut_index = False
        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "scan_name": self.experiment_options.scan_name,
            "cz_num": self.experiment_options.cz_num,
        }
        metadata.process_meta = {
            "x_data": self.run_options.scan_map.get("sweep_list"),
            "scope_detune": self.experiment_options.scope_detune,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        cz_flow_options_adapter(self)

        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)
        validate_data_key(self)

        # check offset width
        cz_num = self.experiment_options.cz_num
        offset_width = self.run_options.width * cz_num
        self.set_run_options(offset_width=offset_width)

        pair = self.qubit_pair
        eop = self.experiment_options
        params = pair.metadata.std.cz.params

        if self.analysis_options.use_qc and pair.metadata.std.process.fit_qc:
            self.experiment_options.z_amp_list = pair.metadata.std.process.fit_qc
        # check scope
        elif (
            (eop.freq_list is None or len(eop.freq_list) == 0)
            and eop.z_amp_list is None
            and not self.run_options.scan_map
        ):
            left = eop.scope.get("l")
            right = eop.scope.get("r")
            point = eop.scope.get("p")

            if left + right < 1:
                v = params[eop.scan_name]["amp"]
                sweep_list = np.linspace(v - left, v + right, point).tolist()
                self.experiment_options.z_amp_list = sweep_list
                self.run_options.max_iter_count = point
                self.run_options.x_data = sweep_list
            else:
                v = params[eop.scan_name]["freq"]
                if v is None:
                    raise ExperimentFieldError(
                        self.label, f"{eop.scan_name} default freq is None!"
                    )
                sweep_list = np.round(np.linspace(v - left, v + right, point), 3)
                self.experiment_options.freq_list = sweep_list.tolist()
                self._check_options()

        # for async mode.
        self.set_run_options(analysis_class=LeakageOnceAnalysis)
        readout_type = self.experiment_options.readout_type

        if readout_type in ["ql-01", "ql-02", "ql-012"]:
            self.run_options.measure_qubits = [self.run_options.ql]
        elif readout_type in ["qh-01", "qh-02", "qh-012"]:
            self.run_options.measure_qubits = [self.run_options.qh]
        else:
            self.run_options.measure_qubits = [self.run_options.qh, self.run_options.ql]

    @staticmethod
    def set_xy_pulses(builder):
        """Set LeakageOnce experiment XY pulses."""
        eop = builder.experiment_options
        rop = builder.run_options

        for qubit in rop.env_bits:
            if qubit.name == rop.qh.name:
                state = eop.swap_state[0]
            elif qubit.name == rop.ql.name:
                state = eop.swap_state[1]
            elif qubit.name.startswith("q"):
                state = "0"
            else:
                continue

            state_pulse = stimulate_state_pulse(state, qubit)
            offset_pulse = Constant(rop.offset_width, 0, name="XY")

            xy_pulse = state_pulse() + offset_pulse()
            builder.play_pulse(
                "XY", qubit, [deepcopy(xy_pulse) for _ in range(rop.max_iter_count)]
            )

    @staticmethod
    def set_z_pulses(builder):
        """Set LeakageOnce experiment Z pulses."""
        eop = builder.experiment_options
        rop = builder.run_options
        scan_map = builder.run_options.scan_map
        max_iter_count = builder.run_options.max_iter_count
        gate_params = rop.gate_params
        zero_x = zero_pulse(rop.qh, "Z")()

        # qc shift compensate
        aft_scan_map = qc_shift_compensate(
            scan_map, builder.qubit_pair, builder.qubits, len(rop.x_data)
        )
        if aft_scan_map:
            scan_map = aft_scan_map
            builder.experiment_protocol.experiment.extra["scan_map_data"] = scan_map

        for qubit in rop.env_bits:
            z_pulse_list = []
            q_assign_pulse = Constant(zero_x.width, 0)
            s_gate_params = gate_params.get(qubit.name)

            collects = scan_map.get(qubit.name, {})
            z_amp_list = collects.get("amp") or [
                s_gate_params.get("amp") for _ in range(max_iter_count)
            ]

            for amp in z_amp_list:
                new_q_assign_pulse = deepcopy(q_assign_pulse)
                s_gate_params.update({"amp": amp, "time": rop.width})
                target_pulse = params_to_pulse(**s_gate_params)
                all_cz_pulse = target_pulse() * eop.cz_num
                once_pulse = new_q_assign_pulse() + all_cz_pulse
                z_pulse_list.append(once_pulse)

            builder.play_pulse("Z", qubit, z_pulse_list)

    def _alone_save_result(self):
        
        if self.run_options.extra:
            scan_map = self.run_options.extra.get("scan_map_data")

        if scan_map:
            self.file.save_text(
                json.dumps(scan_map, indent=4, ensure_ascii=False),
                name=f"{self}(scan_data)",
                prefix=".json",
            )


def qc_shift_compensate(scan_map: dict, pair, qubits, loop):
    from pyQCat.analysis.algorithms.comp_wq2q2q_fit import comp_wqhql
    from pyQCat.log import pyqlog

    if pair.metadata.std.process.qc_shift.coupling:
        coupling = pair.metadata.std.process.qc_shift.coupling
        if not coupling.popt:
            return
    else:
        return

    idle_point_map = {}
    new_scan_map = {}

    for q in qubits:
        idle_point_map[q.name] = q.idle_point

    if pair.qh not in scan_map:
        qh_amp = pair.cz_value(pair.qh, "amp")
        qh_freq = pair.cz_value(pair.qh, "freq")
        scan_map[pair.qh] = {
            "amp": [qh_amp for _ in range(loop)],
            "freq": [qh_freq for _ in range(loop)],
        }

    if pair.ql not in scan_map:
        ql_amp = pair.cz_value(pair.ql, "amp")
        ql_freq = pair.cz_value(pair.ql, "freq")
        scan_map[pair.ql] = {
            "amp": [ql_amp for _ in range(loop)],
            "freq": [ql_freq for _ in range(loop)],
        }

    if pair.qc not in scan_map:
        qc_amp = pair.cz_value(pair.qc, "amp")
        scan_map[pair.qc] = {"amp": [qc_amp for _ in range(loop)]}

    new_scan_map = deepcopy(scan_map)
    for value in new_scan_map.values():
        value["amp"] = [0 for _ in range(loop)]
        value["origin_amp"] = [0 for _ in range(loop)]

    def _shift():
        qc = pair.qc
        qh = pair.qh
        ql = pair.ql

        qc_amp = scan_map.get(qc).get("amp")[idx]
        origin_qh_freq = scan_map.get(qh).get("freq")[idx]
        origin_ql_freq = scan_map.get(ql).get("freq")[idx]
        origin_qh_amp = scan_map.get(qh).get("amp")[idx]
        origin_ql_amp = scan_map.get(ql).get("amp")[idx]

        qh_com_amp, ql_com_amp = comp_wqhql(
            qc_amp,
            origin_qh_freq,
            origin_ql_freq,
            deepcopy(coupling.spectrum),
            {"h-b": coupling.popt[0], "l-b": coupling.popt[1], "h-l": coupling.popt[2]},
        )
        qh_final_amp = qh_com_amp - idle_point_map.get(qh)
        ql_final_amp = ql_com_amp - idle_point_map.get(ql)

        new_scan_map[qc]["amp"][idx] = qc_amp
        new_scan_map[qh]["amp"][idx] = qh_final_amp
        new_scan_map[ql]["amp"][idx] = ql_final_amp
        new_scan_map[qh]["origin_amp"][idx] = origin_qh_amp
        new_scan_map[ql]["origin_amp"][idx] = origin_ql_amp

        pyqlog.info(
            f"qc shift compensate | qc({qc_amp}):\n"
            f"qh={origin_qh_freq}M({origin_qh_amp}->{qh_com_amp}({qh_final_amp})\n"
            f"ql={origin_ql_freq}M({origin_ql_amp}->{ql_com_amp}({ql_final_amp})"
        )

    for idx in range(loop):
        _shift()

    return new_scan_map
