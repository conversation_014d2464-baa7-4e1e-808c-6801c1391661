# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/14
# __author:       <PERSON><PERSON><PERSON>

"""
Experiment protocol unused resource filter.

1. Modules with waveform amplitudes of 0 in `XY_control` and its corresponding 
`SweepControl` are considered useless resources and will be discarded.

2. Modules with waveform amplitudes of 0 in `Z_flux_control` and its corresponding 
`SweepControl` are considered useless resources and will be discarded.
"""

from typing import Dict

from loguru import logger

from ...qm_protocol import ExperimentFile


class ExperimentResourceFilter:
    def __init__(self, experiment: ExperimentFile, debug: bool = False) -> None:
        self._experiment = experiment
        self._debug = debug

    @property
    def measure_aio(self):
        return self._experiment.measure_aio

    @property
    def sweep_control(self):
        return self._experiment.sweep_control

    def run(self):
        remove_control_map = self._check_mio_empty_control()
        remove_control_map = self._check_sweep_control(remove_control_map)
        self._remove_control(remove_control_map)

    def _check_mio_empty_control(self):
        remove_control_map = {}  # channel: control

        for ctrl in self.measure_aio.XY_control:
            if ctrl.is_empty():
                remove_control_map[f"XY_control-{ctrl.channel}"] = ctrl

        for ctrl in self.measure_aio.Z_flux_control:
            if ctrl.is_empty():
                remove_control_map[f"Z_flux_control-{ctrl.channel}"] = ctrl

        return remove_control_map

    def _check_sweep_control(self, remove_control_map: Dict):
        remove_control_map["sweep"] = []
        for ctrl in self.sweep_control:
            module, param, *_ = ctrl.func.split(":")
            if param == "waveform":
                is_empty = ctrl.is_empty()
                if is_empty is False:
                    module_name = f"{module}-{ctrl.channel}"
                    remove_control_map.pop(module_name, None)
                else:
                    remove_control_map["sweep"].append((ctrl))
        return remove_control_map

    def _remove_control(self, remove_control_map: Dict):
        for name, ctrl in remove_control_map.items():
            if name == "sweep":
                for sweep_ctrl in ctrl:
                    self.sweep_control.remove(sweep_ctrl)
                    if self._debug:
                        logger.info(
                            f"remove sweep control {sweep_ctrl.func} | {sweep_ctrl.channel}"
                        )
            elif name.startswith("XY_control"):
                self.measure_aio.XY_control.remove(ctrl)
                if self._debug:
                    logger.info(f"remove xy control {ctrl.channel}")
            else:
                self.measure_aio.Z_flux_control.remove(ctrl)
                if self._debug:
                    logger.info(f"remove z control {ctrl.channel}")
