# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/04
# __author:       ssfang

"""
Readout frequency calibrate node composite experiment.

"""

import numpy as np

from .single_shot_composite import SingleShotComposite
from ...composite_experiment import CompositeExperiment
from ...single import CavityFreqSpectrum
from ....analysis import CavityShiftF012Analysis
from ....analysis.library import ReadoutFreqCaliAnalysis, ReadoutFreqSSCaliAnalysis
from ....structures import Options, MetaData
from ....types import ExperimentRunMode


class ReadoutFreqCalibrate(CompositeExperiment):
    """Optimize Readout Frequency."""

    _sub_experiment_class = CavityFreqSpectrum

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            fc_list (List, np.ndarray): Scan cavity frequency list.
            readout_power (float): Set readout channel power.

        """
        options = super()._default_experiment_options()

        options.set_validator("fc_list", list)
        options.set_validator("readout_power", (-40, -10, 1))
        options.set_validator("readout_type", ["01", "02", "012"])

        options.fc_list = None
        options.readout_power = None
        options.readout_type = "01"
        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for Readout frequency calibrate experiment.

        Options:
            save_mode (str): mean_point | max_distance_point | intersection_point
            diff_threshold (float): Twice cavity frequency difference.
        """
        options = super()._default_analysis_options()

        options.set_validator(
            "save_mode", ["mean_point", "max_distance_point", "intersection_point", "fc0"]
        )
        options.set_validator("diff_threshold", (0, 1, 2))

        options.save_mode = "max_distance_point"
        options.diff_threshold = 0.1

        options.data_key = ["Frequency"]
        options.figsize = (12, 12)

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()

        options.extra_param = False
        options.add_pi_pulse_list = []
        options.extend_f12_list = []

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        readout_power = self.experiment_options.readout_power
        if readout_power is None:
            readout_power = self.child_experiment.qubit.probe_power

        readout_point_amp = self.child_experiment.qubit.readout_point.amp
        metadata.draw_meta = {
            "readout_power": (readout_power, "db"),
            "readout_point_amp": (readout_point_amp, "V"),
        }
        return metadata

    def _check_options(self):
        """Check options."""
        super()._check_options()

        if (
            self.coupler
            and self.child_experiment.is_coupler_exp is False
        ):
            result_name = self.child_experiment.coupler.name
        else:
            result_name = self.child_experiment.qubit.name

        self.set_analysis_options(result_name=result_name)
        if self.experiment_options.readout_type == "012":
            self._label = "F12CavityShift"

        Xpi = self.child_experiment.qubit.XYwave.Xpi
        readout_type = self.experiment_options.readout_type

        amp_list = [0, Xpi]
        analysis_class = ReadoutFreqCaliAnalysis
        extra_param = True
        add_pi_pulse_list = [True, True]
        extend_f12_list = [False, False]

        if readout_type == "01":
            pass
        elif self.experiment_options.readout_type == "012":
            amp_list = [0, Xpi, Xpi]
            add_pi_pulse_list = [False, True, True]
            extend_f12_list = [False, False, True]
            analysis_class = CavityShiftF012Analysis
            extra_param = False
        elif readout_type == "02":
            extend_f12_list = [False, True]

        self.set_run_options(
            x_data=amp_list,
            analysis_class=analysis_class,
            extra_param=extra_param,
            add_pi_pulse_list=add_pi_pulse_list,
            extend_f12_list=extend_f12_list,
        )

    def _setup_child_experiment(
        self, exp: "CavityFreqSpectrum", index: int, value: float
    ):
        """Set child_experiment some options."""
        cs_exp = exp
        cs_exp.run_options.index = index
        total = len(self.run_options.x_data)

        readout_power = self.experiment_options.readout_power
        add_pi_pulse_list = self.run_options.add_pi_pulse_list
        extend_f12_list = self.run_options.extend_f12_list

        cs_exp.set_parent_file(self, f"xy_pulse amp={value}", index, total)
        cs_exp.set_experiment_options(
            fc_list=self.experiment_options.fc_list,
            readout_power=readout_power,
            pi_amp=value,
        )
        if add_pi_pulse_list[index]:
            cs_exp.set_experiment_options(add_pi_pulse=True)
        if extend_f12_list[index]:
            cs_exp.set_experiment_options(extend_f12=True)
        self._check_simulator_data(cs_exp, index)

    def _handle_child_result(self, exp: "CavityFreqSpectrum"):
        # collect child experiment result and provide it for parent.
        cs_exp = exp

        provide_field = self.analysis_options.data_key[0]
        extra_param = self.run_options.extra_param

        fr = cs_exp.analysis.results.fr.value
        cs_exp.analysis.provide_for_parent.update({provide_field: fr})
        if extra_param:
            ql = cs_exp.analysis.results.Ql.value
            quality = cs_exp.analysis.quality
            cs_exp.analysis.provide_for_parent.update({"Ql": ql, "quality": quality})

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        if self.experiment_options.readout_type != "012":
            coupler_probe_flag = False
            if (
                self.coupler
                and self.child_experiment.is_coupler_exp is False
            ):
                coupler_probe_flag = True

            for key, result in self.analysis.results.items():
                if key == "fr":
                    if coupler_probe_flag is True:
                        result.extra["path"] = "Coupler.probe_freq"
                elif key == "power":
                    if coupler_probe_flag is True:
                        result.extra["path"] = "Coupler.probe_power"


class ReadoutFreqSSCalibrate(SingleShotComposite):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            optimize_field (str): Give target optimize field.
            sweep_list (List, np.ndarray): Scan optimize field list.

        """
        options = super()._default_experiment_options()

        options.set_validator("sweep_list", list)
        options.set_validator("scope", float)
        options.set_validator("point", int)

        options.optimize_field = "probe_freq"
        options.sweep_list = None
        options.scope = 1.5
        options.point = 31

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()

        options.analysis_class = ReadoutFreqSSCaliAnalysis
        return options

    def _check_options(self):
        """Check options."""
        super()._check_options()

        sweep_list = self.experiment_options.sweep_list
        if sweep_list is None:
            probe_freq = self.child_experiment.qubit.probe_freq
            scope = self.experiment_options.scope
            point = self.experiment_options.point
            # bugfix zyc 2024/05/24: Loss of freq accuracy may lead to parallel failure
            # sweep_list = np.round(np.linspace(-scope, scope, point), 3) + probe_freq
            sweep_arr = np.round(np.linspace(-scope, scope, point) + probe_freq, 3)
            sweep_list = sweep_arr.tolist()
        self.set_experiment_options(sweep_list=sweep_list)
        self.set_run_options(x_data=sweep_list)
