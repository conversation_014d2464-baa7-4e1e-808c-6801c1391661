# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.ParameterRepr.rst:2
msgid "pyQCat.analysis.ParameterRepr"
msgstr ""

#: of pyQCat.analysis.specification.ParameterRepr:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.analysis.specification.ParameterRepr:1
msgid "Detailed description of fitting parameter."
msgstr ""

#: of pyQCat.analysis.specification.ParameterRepr:5
msgid "Original name of the fit parameter being defined in the fit model."
msgstr ""

#: of pyQCat.analysis.specification.ParameterRepr
msgid "type"
msgstr ""

#: of pyQCat.analysis.specification.ParameterRepr:7
msgid "str"
msgstr ""

#: of pyQCat.analysis.specification.ParameterRepr:11
msgid ""
"Optional. Human-readable parameter name shown in the analysis result and "
"in the figure."
msgstr ""

#: of pyQCat.analysis.specification.ParameterRepr:13
#: pyQCat.analysis.specification.ParameterRepr:19
msgid "Optional[str]"
msgstr ""

#: of pyQCat.analysis.specification.ParameterRepr:17
msgid "Optional. Physical unit of this parameter if applicable."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ParameterRepr.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ParameterRepr.rst:21:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.ParameterRepr.__init__>`\\ "
"\\(name\\[\\, repr\\, unit\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ParameterRepr.rst:23
msgid "Attributes"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ParameterRepr.rst:28:<autosummary>:1
msgid ":py:obj:`repr <pyQCat.analysis.ParameterRepr.repr>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ParameterRepr.rst:28:<autosummary>:1
msgid ":py:obj:`unit <pyQCat.analysis.ParameterRepr.unit>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ParameterRepr.rst:28:<autosummary>:1
msgid ":py:obj:`name <pyQCat.analysis.ParameterRepr.name>`\\"
msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.analysis.ParameterRepr.__init__>`\\ "
#~ "\\(name\\[\\, repr\\, unit\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`repr <pyQCat.analysis.ParameterRepr.repr>`\\"
#~ msgstr ""

#~ msgid ":obj:`unit <pyQCat.analysis.ParameterRepr.unit>`\\"
#~ msgstr ""

#~ msgid ":obj:`name <pyQCat.analysis.ParameterRepr.name>`\\"
#~ msgstr ""

