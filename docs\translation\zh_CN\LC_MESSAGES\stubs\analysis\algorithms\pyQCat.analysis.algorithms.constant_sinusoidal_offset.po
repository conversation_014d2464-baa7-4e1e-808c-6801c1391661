# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.constant_sinusoidal_offset.rst:2
msgid "pyQCat.analysis.algorithms.constant\\_sinusoidal\\_offset"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset:1
msgid "Get constant offset of sinusoidal signal."
msgstr "获得正弦信号的恒定偏移。"

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset:3
msgid ""
"This function finds 95 and 5 percentile y values and take an average of "
"them. This method is robust to the dependency on sampling window, i.e. if"
" we sample sinusoidal signal for 2/3 of its period, simple averaging may "
"induce a drift towards positive or negative direction depending on the "
"phase offset."
msgstr ""
"此函数查找 95 和 5 个百分位数的 y 值并取它们的平均值。 这种方法对采样窗口的"
"依赖性具有鲁棒性，即如果我们在其周期的 2/3 内采样正弦信号，简单的平均可能会"
"根据相位偏移导致正向或负向漂移。"

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset:9
msgid "Array of y values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset:11
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_sinusoidal_offset:12
msgid "Offset value."
msgstr "偏移量"

