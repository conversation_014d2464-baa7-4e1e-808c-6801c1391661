# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/tools/pyQCat.tools.S3Storage.rst:2
msgid "pyQCat.tools.S3Storage"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage:1
msgid "s3 storage, use minio oss(object store)"
msgstr ""

#: ../../source/stubs/tools/pyQCat.tools.S3Storage.rst:13
msgid "Methods"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.tools.S3Storage.__init__>`\\ "
"\\(\\[endpoint\\_url\\, access\\_key\\, ...\\]\\)"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
msgid ""
":py:obj:`bucket_exist <pyQCat.tools.S3Storage.bucket_exist>`\\ "
"\\(bucket\\_name\\)"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
#: pyQCat.tools.s3storage.S3Storage.bucket_exist:1
msgid "Check bucket is exist."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
msgid ""
":py:obj:`compose_object <pyQCat.tools.S3Storage.compose_object>`\\ "
"\\(bucket\\_name\\, ...\\)"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
msgid "compose object."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
msgid ""
":py:obj:`get_object <pyQCat.tools.S3Storage.get_object>`\\ "
"\\(bucket\\_name\\, object\\_name\\)"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
#: pyQCat.tools.s3storage.S3Storage.get_object:1
msgid "Get object return bytes."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
msgid ":py:obj:`list_buckets <pyQCat.tools.S3Storage.list_buckets>`\\ \\(\\)"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
#: pyQCat.tools.s3storage.S3Storage.list_buckets:1
msgid "Return list of bucket."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
msgid ""
":py:obj:`list_objects <pyQCat.tools.S3Storage.list_objects>`\\ "
"\\(bucket\\_name\\[\\, prefix\\, ...\\]\\)"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
#: pyQCat.tools.s3storage.S3Storage.list_objects:1
msgid "Return list of objects."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
msgid ""
":py:obj:`make_bucket <pyQCat.tools.S3Storage.make_bucket>`\\ "
"\\(bucket\\_name\\[\\, location\\]\\)"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
#: pyQCat.tools.s3storage.S3Storage.make_bucket:1
msgid "If bucket don't exist and create success return True."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
msgid ""
":py:obj:`put_figure <pyQCat.tools.S3Storage.put_figure>`\\ "
"\\(bucket\\_name\\, object\\_name\\, figure\\)"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
msgid ""
":py:obj:`put_object <pyQCat.tools.S3Storage.put_object>`\\ "
"\\(bucket\\_name\\, object\\_name\\, data\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
#: pyQCat.tools.s3storage.S3Storage.put_object:1
msgid "Put object to se storage."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
msgid ""
":py:obj:`remove_bucket <pyQCat.tools.S3Storage.remove_bucket>`\\ "
"\\(bucket\\_name\\)"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
#: pyQCat.tools.s3storage.S3Storage.remove_bucket:1
msgid "Remove bucket."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
msgid ""
":py:obj:`remove_object <pyQCat.tools.S3Storage.remove_object>`\\ "
"\\(bucket\\_name\\, object\\_name\\)"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1:<autosummary>:1
#: pyQCat.tools.s3storage.S3Storage.remove_object:1
msgid "Delete object."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage._connect:1
msgid "config s3 client."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.bucket_exist
#: pyQCat.tools.s3storage.S3Storage.get_object
#: pyQCat.tools.s3storage.S3Storage.list_objects
#: pyQCat.tools.s3storage.S3Storage.make_bucket
#: pyQCat.tools.s3storage.S3Storage.put_object
#: pyQCat.tools.s3storage.S3Storage.remove_bucket
#: pyQCat.tools.s3storage.S3Storage.remove_object
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.bucket_exist:4
#: pyQCat.tools.s3storage.S3Storage.get_object:4
#: pyQCat.tools.s3storage.S3Storage.list_objects:4
#: pyQCat.tools.s3storage.S3Storage.make_bucket:6
#: pyQCat.tools.s3storage.S3Storage.put_object:5
#: pyQCat.tools.s3storage.S3Storage.remove_bucket:4
#: pyQCat.tools.s3storage.S3Storage.remove_object:4
msgid "str, the name or s3 bucket."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.bucket_exist
#: pyQCat.tools.s3storage.S3Storage.get_object
#: pyQCat.tools.s3storage.S3Storage.list_buckets
#: pyQCat.tools.s3storage.S3Storage.list_objects
#: pyQCat.tools.s3storage.S3Storage.make_bucket
#: pyQCat.tools.s3storage.S3Storage.remove_bucket
#: pyQCat.tools.s3storage.S3Storage.remove_object
msgid "Return type"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.bucket_exist:6
#: pyQCat.tools.s3storage.S3Storage.make_bucket:8
msgid ":py:class:`bool`"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.bucket_exist
#: pyQCat.tools.s3storage.S3Storage.get_object
#: pyQCat.tools.s3storage.S3Storage.list_buckets
#: pyQCat.tools.s3storage.S3Storage.list_objects
#: pyQCat.tools.s3storage.S3Storage.make_bucket
#: pyQCat.tools.s3storage.S3Storage.remove_bucket
msgid "Returns"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.bucket_exist:7
msgid "if bucket exist, return True else return False."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.remove_bucket:6
#: pyQCat.tools.s3storage.S3Storage.remove_object:9
msgid ":py:obj:`None`"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.remove_bucket:7
msgid "None"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.list_buckets:3
#: pyQCat.tools.s3storage.S3Storage.list_objects:12
msgid ":py:class:`~typing.List`"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.list_buckets:4
msgid "list, the list of bucket, the bucket type is minio bucket."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.make_bucket:9
msgid ""
"if bucket create success return True else return False, create failed "
"reason maybe is bucket is existed."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.list_objects:6
msgid ""
"the find objects prefix, if want get all \"d4\" object , prefix = \"d4\","
" prefix must start the header."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.list_objects:13
msgid "list, the list of object, the bucket type is minio object."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.get_object:6
#: pyQCat.tools.s3storage.S3Storage.put_object:7
#: pyQCat.tools.s3storage.S3Storage.remove_object:6
msgid "str, the name for object key."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.get_object:8
msgid ":py:class:`bytes`"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.get_object:9
msgid "bytes"
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.put_object:9
msgid "str or bytes, the minio need io.bytesio, but we use str and bytes more."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.put_object:11
msgid "int the data length, if don't give, we get length by data."
msgstr ""

#: of pyQCat.tools.s3storage.S3Storage.compose_object:1
msgid "compose object. the min object must > 5MB."
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.tools.S3Storage.__init__>`\\ "
#~ "\\(\\[endpoint\\_url\\, access\\_key\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`bucket_exist <pyQCat.tools.S3Storage.bucket_exist>`\\"
#~ " \\(bucket\\_name\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`compose_object "
#~ "<pyQCat.tools.S3Storage.compose_object>`\\ \\(bucket\\_name\\,"
#~ " ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_object <pyQCat.tools.S3Storage.get_object>`\\ "
#~ "\\(bucket\\_name\\, object\\_name\\)"
#~ msgstr ""

#~ msgid ":py:obj:`list_buckets <pyQCat.tools.S3Storage.list_buckets>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`list_objects <pyQCat.tools.S3Storage.list_objects>`\\"
#~ " \\(bucket\\_name\\[\\, prefix\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`make_bucket <pyQCat.tools.S3Storage.make_bucket>`\\ "
#~ "\\(bucket\\_name\\[\\, location\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`put_figure <pyQCat.tools.S3Storage.put_figure>`\\ "
#~ "\\(bucket\\_name\\, object\\_name\\, figure\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`put_object <pyQCat.tools.S3Storage.put_object>`\\ "
#~ "\\(bucket\\_name\\, object\\_name\\, data\\[\\, "
#~ "...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`remove_bucket <pyQCat.tools.S3Storage.remove_bucket>`\\"
#~ " \\(bucket\\_name\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`remove_object <pyQCat.tools.S3Storage.remove_object>`\\"
#~ " \\(bucket\\_name\\, object\\_name\\)"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.tools.S3Storage.__init__>`\\ "
#~ "\\(\\[endpoint\\_url\\, access\\_key\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`bucket_exist <pyQCat.tools.S3Storage.bucket_exist>`\\ "
#~ "\\(bucket\\_name\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`compose_object <pyQCat.tools.S3Storage.compose_object>`\\"
#~ " \\(bucket\\_name\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_object <pyQCat.tools.S3Storage.get_object>`\\ "
#~ "\\(bucket\\_name\\, object\\_name\\)"
#~ msgstr ""

#~ msgid ":obj:`list_buckets <pyQCat.tools.S3Storage.list_buckets>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`list_objects <pyQCat.tools.S3Storage.list_objects>`\\ "
#~ "\\(bucket\\_name\\[\\, prefix\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`make_bucket <pyQCat.tools.S3Storage.make_bucket>`\\ "
#~ "\\(bucket\\_name\\[\\, location\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`put_figure <pyQCat.tools.S3Storage.put_figure>`\\ "
#~ "\\(bucket\\_name\\, object\\_name\\, figure\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`put_object <pyQCat.tools.S3Storage.put_object>`\\ "
#~ "\\(bucket\\_name\\, object\\_name\\, data\\[\\, "
#~ "...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`remove_bucket <pyQCat.tools.S3Storage.remove_bucket>`\\"
#~ " \\(bucket\\_name\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`remove_object <pyQCat.tools.S3Storage.remove_object>`\\"
#~ " \\(bucket\\_name\\, object\\_name\\)"
#~ msgstr ""

