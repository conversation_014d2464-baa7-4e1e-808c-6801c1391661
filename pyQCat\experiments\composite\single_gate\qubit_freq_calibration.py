# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/09/16
# __author:       <PERSON><PERSON><PERSON>
"""Calibrate qubit frequency by doing <PERSON> twice."""

from ....analysis import QubitFreqCaliAnalysis
from ....analysis.algorithms import get_p_labels
from ....errors import ExperimentOptionsError
from ....structures import Options
from ....tools.utilities import qarange, set_sub_title
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ramsey


class QubitFreqCalibration(CompositeExperiment):
    """Qubit Frequency Cali."""

    _sub_experiment_class = Ramsey

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("fringes", list)
        options.set_validator("delays", list)

        options.fringes = [10, -10]
        options.delays = qarange(100, 800, 10)

        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("freq_gap_threshold", (0, 10, 2))

        options.drive_freq = 5600.0
        options.freq_gap_threshold = 0.1
        options.y_label = None
        options.sub_title = None
        options.is_plot = True
        options.subplots = (2, 2)

        return options
    
    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.link_child_exp = True
        return options

    def _check_options(self):
        """check options."""
        super()._check_options()
        fringes = self.experiment_options.fringes
        acquisition_key = self.child_experiment.run_options.acquisition_key

        if self.child_experiment.is_coupler_exp is True:
            drive_freq = self.child_experiment.coupler.drive_freq
            result_name = self.child_experiment.coupler.name
        elif (
            self.coupler
            and self.child_experiment.is_coupler_exp is False
        ):
            drive_freq = self.child_experiment.coupler.probe_drive_freq
            result_name = self.child_experiment.coupler.name
        else:
            drive_freq = self.child_experiment.qubit.drive_freq
            result_name = self.child_experiment.qubit.name

        if self.discriminator is None:
            y_label = ["Amp", "Phase"]
        else:
            y_label = get_p_labels(self.discriminator)
            if "2" in self.discriminator.level_str:
                acquisition_key = "weirdo"

        sub_title = [f"fringe={fringes[0]}MHz", f"fringe={fringes[1]}MHz"] * len(
            y_label
        )

        new_y_label = []
        for label in y_label:
            new_y_label.extend([label, label])

        self.child_experiment.set_run_options(acquisition_key=acquisition_key)
        
        if not drive_freq:
            raise ExperimentOptionsError(self, msg="drive freq is null")

        self.set_analysis_options(
            subplots=(len(y_label), 2),
            drive_freq=drive_freq,
            y_label=new_y_label,
            sub_title=sub_title,
            result_name=result_name,
        )

        # Shq 2024/04/29
        # for async mode.
        self.run_options.x_data = self.experiment_options.fringes
        self.run_options.analysis_class = QubitFreqCaliAnalysis

    # Shq 2024/04/29
    # for async mode.
    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "f01":
                if self.child_experiment.is_coupler_exp is True:
                    result.extra["path"] = f"Coupler.drive_freq"
                elif (
                    self.coupler
                    and self.child_experiment.is_coupler_exp is False
                ):
                    result.extra["path"] = f"Coupler.probe_drive_freq"
                else:
                    result.extra["path"] = f"Qubit.drive_freq"

    # Shq 2024/04/29
    # for async mode.
    def _setup_child_experiment(self, exp: Ramsey, index: int, fringe: float):
        exp.run_options.index = index
        exp.set_parent_file(
            self, f"fringe={fringe}MHz", index, len(self.experiment_options.fringes)
        )
        # Solve the problem of IF and pulse time binding.
        if exp.is_coupler_exp is True:
            # baseband_freq = exp.coupler.drive_XYwave.baseband_freq
            baseband_freq = 0
        else:
            # baseband_freq = exp.qubit.XYwave.baseband_freq
            baseband_freq = 0

        # # old version
        # if QAIO.type in [QAIO.qaio_8, QAIO.qaio_30]:
        #     # 1,2 inst
        #     real_fringe = baseband_freq + fringe
        # else:
        #     # 3 inst
        #     real_fringe = baseband_freq - fringe

        # edited by WTL
        real_fringe = baseband_freq + fringe

        exp.set_experiment_options(
            delays=self.experiment_options.delays, fringe=real_fringe
        )

        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp: Ramsey):
        sub_title_list = ["" for _ in range(4)]
        res = exp.analysis.results
        ana_res = res["freq"]
        index = exp.run_options.index
        fringe = self.experiment_options.fringes[index]
        new_title = set_sub_title(ana_res, fringe)
        sub_title_list[index] = new_title[0]
        sub_title_list[index + 2] = new_title[1]

        exp.analysis.provide_for_parent.update(
            {
                "f01": exp.analysis.results.freq.value,
                "quality": exp.analysis.quality,
                "analysis_data": exp.analysis.analysis_datas,
            }
        )


class CouplerFreqCalibration(QubitFreqCalibration):
    """Coupler Frequency Cali."""

    _sub_experiment_class = CouplerRamsey
