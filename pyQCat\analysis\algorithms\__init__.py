# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

"""
===============================================================
Analysis  (:mod:`pyQCat.analysis.algorithms`)
===============================================================

Analysis submodule, special operation interface modules.

distortion function
========================================================

.. autosummary::
    :toctree: ../stubs/analysis/algorithms/

    calculate_offset_arr
    calculate_distortion
    calculate_lfilter_paras
    create_verify_pulse
    calculate_pre_distortion_wave
    calculate_simulation_wave

find peak function
========================================================

.. autosummary::
    :toctree: ../stubs/analysis/algorithms/

    get_peak_point
    judge_peak_dip
    distance_to_point
    get_qubit_dc_point
    get_coupler_dc_point
    find_coincident_point
    find_peaks_with_prominence

guess function
========================================================

.. autosummary::
    :toctree: ../stubs/analysis/algorithms/

    cosine_fit_guess
    fourier_cycle_guess
    frequency
    max_height
    min_height
    get_height
    exp_decay
    oscillation_exp_decay
    constant_sinusoidal_offset
    constant_spectral_offset
    full_width_half_max

iqprobability function
========================================================

.. autosummary::
    :toctree: ../stubs/analysis/algorithms/

    IQdiscriminator
    get_qubits_probability
    correct_fidelity
    simulate_qgate

smooth function
========================================================

.. autosummary::
    :toctree: ../stubs/analysis/algorithms/

    smooth

tomography function
========================================================

.. autosummary::
    :toctree: ../stubs/analysis/algorithms/

    init_qst
    qst
    qst_mle
    init_qpt
    qpt
    tensor
    tensor_combinations
    gen_ideal_chi_matrix
    qpt_mle
"""


from .distortion import (
    calculate_offset_arr,
    calculate_distortion,
    calculate_lfilter_paras,
    create_verify_pulse,
    calculate_pre_distortion_wave,
    calculate_simulation_wave,
    get_poles_bounds_p0,
    generate_ab_from_poles_params_dict,
    get_ab_full,
    sosfilter_design_using_ab,
    create_ideal_wave,
    gaussian_fir_low_pass_filter
)
from .find_peak import (
    get_peak_point,
    judge_peak_dip,
    distance_to_point,
    get_qubit_dc_point,
    get_coupler_dc_point,
    find_coincident_point,
    find_peaks_with_prominence
)
from .guess import (
    cosine_fit_guess,
    fourier_cycle_guess,
    frequency,
    max_height,
    min_height,
    get_height,
    exp_decay,
    oscillation_exp_decay,
    constant_sinusoidal_offset,
    constant_spectral_offset,
    full_width_half_max
)
from .iqprobability import (
    IQdiscriminator,
    get_qubits_probability,
    correct_fidelity,
    simulate_qgate,
    get_p_labels,
    get_multi_bits_probability,
    post_selection
)
from .smooth import smooth
from .smooth_update import SmoothLineUpdate

from .tomography import (
    init_qst,
    qst,
    qst_mle,
    init_qpt,
    qpt,
    tensor,
    tensor_combinations,
    gen_ideal_chi_matrix,
    qpt_mle,
    phase_tomograph,
    change_phase
)
