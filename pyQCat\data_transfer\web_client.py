# -*- coding: utf-8 -*-
import time
import uuid
from hashlib import md5
from typing import Union

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/09/18
# __author:       <PERSON><PERSON><PERSON>


import requests
from loguru import logger
from requests.exceptions import RequestException

from pyQCat import get_version
from ..structures import Singleton


class SimpleHTTPClient:
    def __init__(self, base_url=None):
        self.base_url = base_url

    def get(self, endpoint, params=None, headers=None, stream=None):
        """Send GET request"""
        try:
            url = f"{self.base_url}{endpoint}" if self.base_url else endpoint
            response = requests.get(url, params=params, headers=headers, stream=stream)
            response.raise_for_status()
            return response
        except RequestException as e:
            logger.error(f"Request Error: {e} | {response.text}")
            return None
        except Exception as e:
            logger.error(f"Request Error: {e}")

    def post(self, endpoint, data=None, json=None, headers=None):
        """Send POST request"""
        try:
            url = f"{self.base_url}{endpoint}" if self.base_url else endpoint
            response = requests.post(url, data=data, json=json, headers=headers)
            response.raise_for_status()
            return response
        except RequestException as e:
            logger.error(f"Request Error: {e} | {response.text}")
            return None
        except Exception as e:
            logger.error(f"Request Error: {e}")

    def put(self, endpoint, data=None, json=None, headers=None):
        """Send PUT request"""
        try:
            url = f"{self.base_url}{endpoint}" if self.base_url else endpoint
            response = requests.put(url, data=data, json=json, headers=headers)
            response.raise_for_status()
            return response
        except RequestException as e:
            logger.error(f"Request Error: {e} | {response.text}")
            return None
        except Exception as e:
            logger.error(f"Request Error: {e}")

    def delete(self, endpoint, headers=None, json=None):
        """Send DELETE request"""
        try:
            response = None
            url = f"{self.base_url}{endpoint}" if self.base_url else endpoint
            response = requests.delete(url, headers=headers, json=json)
            response.raise_for_status()
            return response
        except RequestException as e:
            logger.error(f"Request Error: {e} | {response}")
        except Exception as e:
            logger.error(f"Request Error: {e}")

    def handle_response(self, response):
        """Handle response data"""
        if response is not None:
            try:
                return response.json()
            except ValueError:
                return response.text
        return None


class WebClient(metaclass=Singleton):
    def __init__(self, token: str = "", web_url: str = "http://127.0.0.1:8030"):
        self.SECRET_KEY = "0K9shRRzZM27LcfSHIPq1TjGEhhbOX8y"
        self._client = SimpleHTTPClient(web_url)
        self._token = token
        logger.info(f"WebClient start suc: Token | {self._token}")

    def md5encry_sign(
            self,
            auth_timestamp: Union[int, str],
            auth_nonce: str,
            auth_token: str,
            auth_source: str = "",
            auth_version: str = "",
    ):
        joint_str = (
            f"auth_timestamp={auth_timestamp}&auth_nonce={auth_nonce}&auth_token={auth_token}"
            f"&auth_source={auth_source}&auth_version={auth_version}&secret_key={self.SECRET_KEY}"
        )
        sign = md5(joint_str.encode()).hexdigest().upper()
        return sign

    def init_http_headers(self, token: str = ""):
        auth_timestamp = str(int(time.time()))
        auth_nonce = str(uuid.uuid4())[-12:]
        auth_token = token
        auth_source = "monster"
        auth_version = get_version()

        auth_sign = self.md5encry_sign(auth_timestamp, auth_nonce, auth_token, auth_source, auth_version)
        headers = {
            "Auth-Timestamp": auth_timestamp,
            "Auth-Nonce": auth_nonce,
            "Authorization": auth_token,
            "Auth-Source": auth_source,
            "Auth-Version": auth_version,
            "Auth-Sign": auth_sign,
            "Accept-Language": "zh_CN",
        }
        return headers

    @property
    def token(self):
        return self._token

    @token.setter
    def token(self, value):
        self._token = value

    @property
    def url(self):
        return self._client.base_url

    @url.setter
    def url(self, value):
        self._client.base_url = value

    def delete_task(self, task_uuid):
        """Clear task request.

        Args:
            task_uuid (List[str]): Task uuid
        """
        infos = "\n".join(task_uuid)
        logger.warning(f"Clear task, total {len(task_uuid)}, details: \n{infos}")
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json",
        }
        headers.update(self.init_http_headers(self._token))
        response = self._client.delete(
            "/core/task/operation",
            headers=headers,
            json={"task_id": task_uuid, "data_retained": 2},
        )
        return self._client.handle_response(response)

    def query_program(self, task_uuid: str):
        """Query the original information of the task.

        Args:
            task_uuid (str): Task uuid
        """
        headers = {
            "accept": "application/json",
        }
        headers.update(self.init_http_headers(self._token))
        response = self._client.get(
            "/task/prog/info",
            params={"task_uuid": task_uuid},
            headers=headers,
            stream=True,
        )
        file_content = bytearray()
        if response:
            for chunk in response.iter_content(chunk_size=8192):  # Read 8k each time
                if chunk:
                    file_content.extend(chunk)
        return {"data": {"program": file_content}}


if __name__ == "__main__":
    client = WebClient(token="123456")
    print(client.query_program("1231234125414"))
    print(client.delete_task("1231234125414"))
