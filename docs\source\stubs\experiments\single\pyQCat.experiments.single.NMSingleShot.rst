﻿pyQCat.experiments.single.NMSingleShot
======================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: NMSingleShot

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~NMSingleShot.__init__
      ~NMSingleShot.acquire_pulse
      ~NMSingleShot.cal_fidelity
      ~NMSingleShot.experiment_info
      ~NMSingleShot.from_experiment_context
      ~NMSingleShot.get_qubit_str
      ~NMSingleShot.jupyter_schedule
      ~NMSingleShot.options_table
      ~NMSingleShot.play_pulse
      ~NMSingleShot.plot_schedule
      ~NMSingleShot.run
      ~NMSingleShot.save_bin_file
      ~NMSingleShot.set_analysis_options
      ~NMSingleShot.set_experiment_options
      ~NMSingleShot.set_multiple_IF
      ~NMSingleShot.set_multiple_index
      ~NMSingleShot.set_parent_file
      ~NMSingleShot.set_run_options
      ~NMSingleShot.set_sweep_order
      ~NMSingleShot.stimulate_state_one
      ~NMSingleShot.stimulate_state_zero
      ~NMSingleShot.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~NMSingleShot.analysis
      ~NMSingleShot.analysis_options
      ~NMSingleShot.experiment_options
      ~NMSingleShot.run_options
   
   