# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/12/22
# __author:       <PERSON>

from abc import abstractmethod

from ...types import QualityDescribe


class BaseQuality:
    def __init__(self) -> None:
        self._quality: QualityDescribe = QualityDescribe.empty

    @property
    def descriptor(self) -> QualityDescribe:
        return self._quality

    @descriptor.setter
    def descriptor(self, v: QualityDescribe):
        self._quality = v

    @abstractmethod
    def evaluate(self, *args, **kwargs):
        pass

    @property
    def value(self) -> str:
        """Return quality describe.

        Returns:
            QualityDescribe: the quality describe.
        """
        return self._quality.value

    def __repr__(self) -> str:
        return self.value

    @classmethod
    def instantiate(cls, quality_str: QualityDescribe):
        quality = cls()
        quality._quality = quality_str
        return quality

    def to_dict(self):
        return dict(descriptor=self.descriptor)

    def set_bad(self):
        self._quality = QualityDescribe.bad

    def set_normal(self):
        self._quality = QualityDescribe.normal

    def set_perfect(self):
        self._quality = QualityDescribe.perfect

    def is_pass(self):
        return self._quality in [QualityDescribe.normal, QualityDescribe.perfect]
