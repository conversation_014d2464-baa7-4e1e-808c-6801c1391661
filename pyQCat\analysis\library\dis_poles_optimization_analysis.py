# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/02/23
# __author:       xw


"""
Distortion poles optimization analysis.
"""

import numpy as np

from ...structures import Options, QDict
from ..curve_analysis import CurveAnalysis
from ..quality import BaseQuality
from ..specification import CurveAnalysisData
from ...types import QualityDescribe


class OptimizationAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.x_label = "iterations"
        options.y_label = ["goal"]
        options.goal_function = None
        return options

    def _create_analysis_data(self):
        analysis_data_dict = QDict()
        goal_function = self.options.goal_function
        bests = []
        for ii in range(len(goal_function)):
            bests.append(np.min(np.array(goal_function[ii])))

        analysis_data = CurveAnalysisData(
            x=np.copy(self._experiment_data.x_data),
            y=np.asarray(bests)
        )

        analysis_data_dict["goal"] = analysis_data
        return analysis_data_dict

    def _extract_result(self, data_key: str):
        """Extract DC Max Min from fitted data.

        Args:
            data_key (str): The basis for selecting data.
        """
        self._quality = BaseQuality.instantiate(QualityDescribe.perfect)
