# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:2
msgid "pyQCat.experiments.CouplerBaseExperiment"
msgstr ""

#: of pyQCat.experiments.coupler_experiment.CouplerBaseExperiment:1
msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
msgstr ""

#: of pyQCat.experiments.coupler_experiment.CouplerBaseExperiment:1
msgid "Abstract quantum experiment base class."
msgstr ""

#: of pyQCat.experiments.coupler_experiment.CouplerBaseExperiment:3
msgid ""
"The top-level interface of the experiment contains the properties and "
"methods necessary for the coupler qubit_test experiment. If you want to "
"implement a new experiment, it is recommended that you inherit this class"
" and rewrite the ``run()`` method to implement specific operations."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
#: of pyQCat.experiments.coupler_experiment.CouplerBaseExperiment.__init__:1
msgid "Create a new experiment."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.CouplerBaseExperiment.__init__>`\\ "
"\\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`acquire_pulse "
"<pyQCat.experiments.CouplerBaseExperiment.acquire_pulse>`\\ "
"\\(acq\\_pulse\\[\\, qubit\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._set_measure_pulses:1
msgid "Set measure pulse."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`cal_fidelity "
"<pyQCat.experiments.CouplerBaseExperiment.cal_fidelity>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Calculate the fidelity matrix."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`experiment_info "
"<pyQCat.experiments.CouplerBaseExperiment.experiment_info>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Show experiment informations."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.CouplerBaseExperiment.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.CouplerBaseExperiment.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`jupyter_schedule "
"<pyQCat.experiments.CouplerBaseExperiment.jupyter_schedule>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Show experiment pulses's schedules in Jupyter Notebook."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.CouplerBaseExperiment.options_table>`\\ \\(\\[mode\\,"
" detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`play_pulse "
"<pyQCat.experiments.CouplerBaseExperiment.play_pulse>`\\ \\(name\\, "
"base\\_qubit\\, pulse\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Play pulse for XY control and Z flux control."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`plot_schedule "
"<pyQCat.experiments.CouplerBaseExperiment.plot_schedule>`\\ "
"\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
"Plot the pulse schedule which is very important to confirm experiment "
"logical correction."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`run <pyQCat.experiments.CouplerBaseExperiment.run>`\\ "
"\\(\\*args\\, \\*\\*kwargs\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Run experiment"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.CouplerBaseExperiment.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.CouplerBaseExperiment.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_multiple_IF "
"<pyQCat.experiments.CouplerBaseExperiment.set_multiple_IF>`\\ "
"\\(\\*IF\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Used for multiple qubits experiment, set mutiple IF."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_multiple_index "
"<pyQCat.experiments.CouplerBaseExperiment.set_multiple_index>`\\ "
"\\(\\*args\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
"Used for multiple qubits experiment, set mutiple index. :type args: "
":py:class:`list` :param args: Setting the start and end values :type "
"channel: :py:class:`int` :param channel: channel settings Usage::     "
">>>experiment = TopExperiment()     >>>experiment.set_multiple_index([0, "
"1500], [100, 900], channel=1)."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.CouplerBaseExperiment.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.CouplerBaseExperiment.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_sweep_order "
"<pyQCat.experiments.CouplerBaseExperiment.set_sweep_order>`\\ "
"\\(order\\_list\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid "Old interface."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`sweep_readout_trigger_delay "
"<pyQCat.experiments.CouplerBaseExperiment.sweep_readout_trigger_delay>`\\"
" \\(channel\\, sweep\\_delay\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CouplerBaseExperiment.rst:41
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._set_measure_pulses:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.CouplerBaseExperiment.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._set_measure_pulses:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._set_measure_pulses:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.CouplerBaseExperiment.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._set_measure_pulses:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._set_measure_pulses:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.CouplerBaseExperiment.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._set_measure_pulses:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.CouplerBaseExperiment.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._set_measure_pulses:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:1
msgid "Default kwarg options for experiment."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:40
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:4
msgid "bind_dc (bool): Whether to bind the initial DC of the current qubit"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:5
msgid "to the inst."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:6
msgid "bind_drive (bool): Whether to bind the drive frenquency of the"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:7
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:9
msgid "current qubit to the inst."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:8
msgid "bind_probe (bool): Whether to bind the probe frenquency of the"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:10
msgid "file_flag (int): OriginQAIO collection source data type,"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:11
msgid "and save or not."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:13
msgid "multi_readout_channels (List, optional): Multiple readout channels,"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:13
msgid ""
"list of int, normal used to appoint the readout channel, or multiple bits"
" experiment, readout channel not same."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:15
msgid "repeat (int): Repeat number."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:18
msgid "data_type (str): The data type determines how the collected data"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:18
msgid "is processed. Now only support \"amp_phase\", \"I_Q\" and \"track\"."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:20
msgid ""
"enable_one_sweep (bool):The flag bit of a single loop experiment. "
"register_pulse_save (bool): Save or not register pulse data."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:23
msgid "schedule_flag (bool): Plot or not schedule, normal cavity frequency"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:24
msgid "experiment XY or Z line no pulse, so no plot."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:25
msgid ""
"schedule_index (Union[int, List]): Select a pulse schedule to plot. "
"schedule_save (bool): Save or not, schedule plot figure. schedule_measure"
" (bool): Plot schedule, show or not measure pulse. schedule_type (str, "
"optional): Pulse timing diagram drawing method,"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:29
msgid "`sequence` or `envelop`."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:31
msgid ""
"is_dynamic (int): When collect data, dynamic plot or not. fidelity_matrix"
" (array): Fidelity amend matrix. measure_bits (List): When multiple bits "
"experiment,"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:34
msgid "set measure bits, list of int."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:35
msgid "loop_num (int): Mark field, SingleShot experiment use loop_num"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:36
msgid "to adjust repeat > 10000."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:37
msgid ""
"save_label (str): Save source data, file name mark information. iq_flag "
"(bool): Save source data, data like SingleShot iq or not."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._default_experiment_options:42
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._compose_xy_pulses:1
msgid "Compose xy line pulses."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._compose_z_pulses:1
msgid "Compose z line pulses."
msgstr ""

#: of
#: pyQCat.experiments.coupler_experiment.CouplerBaseExperiment._set_z_pulses:1
msgid "Set z pulse."
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.CouplerBaseExperiment.__init__>`\\ "
#~ "\\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`acquire_pulse "
#~ "<pyQCat.experiments.CouplerBaseExperiment.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.CouplerBaseExperiment.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.CouplerBaseExperiment.get_qubit_str>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`play_pulse "
#~ "<pyQCat.experiments.CouplerBaseExperiment.play_pulse>`\\ "
#~ "\\(name\\, base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot_schedule "
#~ "<pyQCat.experiments.CouplerBaseExperiment.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run <pyQCat.experiments.CouplerBaseExperiment.run>`\\"
#~ " \\(\\*args\\, \\*\\*kwargs\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_IF "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_index "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_sweep_order "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis "
#~ "<pyQCat.experiments.CouplerBaseExperiment.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.CouplerBaseExperiment.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.CouplerBaseExperiment.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.CouplerBaseExperiment.run_options>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.CouplerBaseExperiment.__init__>`\\ "
#~ "\\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`acquire_pulse "
#~ "<pyQCat.experiments.CouplerBaseExperiment.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_info "
#~ "<pyQCat.experiments.CouplerBaseExperiment.experiment_info>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.CouplerBaseExperiment.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.CouplerBaseExperiment.get_qubit_str>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`jupyter_schedule "
#~ "<pyQCat.experiments.CouplerBaseExperiment.jupyter_schedule>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.CouplerBaseExperiment.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`play_pulse "
#~ "<pyQCat.experiments.CouplerBaseExperiment.play_pulse>`\\ "
#~ "\\(name\\, base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot_schedule "
#~ "<pyQCat.experiments.CouplerBaseExperiment.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run <pyQCat.experiments.CouplerBaseExperiment.run>`\\ "
#~ "\\(\\*args\\, \\*\\*kwargs\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_IF "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_index "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_sweep_order "
#~ "<pyQCat.experiments.CouplerBaseExperiment.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.CouplerBaseExperiment.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.CouplerBaseExperiment.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.CouplerBaseExperiment.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.CouplerBaseExperiment.run_options>`\\"
#~ msgstr ""

