# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/12/05
# __author:       <PERSON> Fang

"""
Floquet test single qubit XYZ timing..
"""

from copy import deepcopy

from ...top_experiment_v1 import TopExperimentV1 as TopExperiment
from ....analysis.library_v2 import FloquetXYZTimingOnceAnalysis
from ....log import pyqlog
from ....pulse.pulse_function import pi_pulse
from ....pulse.pulse_lib import Constant, FlatTopGaussian
from ....structures import MetaData, Options
from ....tools.utilities import qarange
from ....types import StandardContext


class FloquetXYZTimingOnce(TopExperiment):
    """FloquetXYZTiming once experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Define experiment options."""
        options = super()._default_experiment_options()
        options.set_validator("N_list", list)
        options.set_validator("delay", float)
        options.set_validator("z_pulse_params", dict)

        options.N_list = qarange(1, 20, 1)
        options.delay = 50.0
        options.z_pulse_params = {
            "time": 30.0,
            "amp": 0.1,
            "sigma": 2.5,
            "buffer": 5.0,
        }

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Define run options."""
        options = super()._default_run_options()

        options.x_data = []
        options.analysis_class = FloquetXYZTimingOnceAnalysis
        options.support_context = [StandardContext.QC.value]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Define analysis options."""
        options = super()._default_analysis_options()

        options.raw_data_format = "plot"
        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        z_pulse_params = self.experiment_options.z_pulse_params
        metadata.draw_meta = {
            "delay": self.experiment_options.delay,
            "NZ-amp": z_pulse_params.get("amp"),
        }
        return metadata

    def _check_options(self):
        """Check option."""
        super()._check_options()
        N_list = self.experiment_options.N_list

        if not self.discriminator:
            pyqlog.warning(f"Please check `context` no discriminator: {self.discriminator}")

        self.set_experiment_options(data_type="I_Q")
        self.set_run_options(x_data=N_list)
        self.set_analysis_options(result_name=self.qubits[0].name)

    @staticmethod
    def set_xy_pulses(self):
        """Set xy pulses."""
        N_list = self.experiment_options.N_list
        delay = self.experiment_options.delay
        z_pulse_params = self.experiment_options.z_pulse_params
        z_width = z_pulse_params.get("time", 0.0)

        xy_pulse_list = []
        x_pulse = pi_pulse(self.qubit)
        xz_pulse = Constant(z_width * 2, 0.0, name="XY")
        for N in N_list:
            pulse_obj = Constant(delay, 0.0, name="XY")()
            for i in range(N):
                pulse_obj += deepcopy(x_pulse)()
                pulse_obj += deepcopy(xz_pulse)()
            xy_pulse_list.append(pulse_obj)

        qubit_obj = self.qubits[0]
        self.play_pulse("XY", qubit_obj, xy_pulse_list)

    @staticmethod
    def set_z_pulses(self):
        """Set z pulses."""
        N_list = self.experiment_options.N_list
        delay = self.experiment_options.delay
        z_pulse_params = self.experiment_options.z_pulse_params

        x_pulse = pi_pulse(self.qubit)
        x_width = x_pulse.width

        zx_pulse = Constant(x_width, 0.0, name="Z")
        p_pulse = FlatTopGaussian(
            time=z_pulse_params.get("time"),
            amp=z_pulse_params.get("amp", 0.0),
            sigma=z_pulse_params.get("sigma"),
            buffer=z_pulse_params.get("buffer"),
        )
        n_pulse = FlatTopGaussian(
            time=z_pulse_params.get("time"),
            amp=-z_pulse_params.get("amp", 0.0),
            sigma=z_pulse_params.get("sigma"),
            buffer=z_pulse_params.get("buffer"),
        )

        z_pulse_list = []
        for N in N_list:
            pulse_obj = Constant(delay, 0.0, name="Z")()
            for i in range(N):
                pulse_obj += deepcopy(zx_pulse)()
                pulse_obj += deepcopy(p_pulse)()
                pulse_obj += deepcopy(n_pulse)()

            z_pulse_list.append(pulse_obj)

        qubit_obj = self.qubits[0]
        self.play_pulse("Z", qubit_obj, z_pulse_list)

    @staticmethod
    def update_instrument(self):
        """Update some instrument parameters."""
        N_list = self.experiment_options.N_list

        length = len(N_list)
        sweep_delay = self._pulse_time_list[:length]
        channel = self.qubits[0].readout_channel
        self.sweep_readout_trigger_delay(channel, sweep_delay)
