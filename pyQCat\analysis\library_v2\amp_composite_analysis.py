# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/05/31
# __author:       xw
# __corporation:  OriginQuantum

from typing import List

import numpy as np

from ...log import pyqlog
from ...structures import Options
from ...types import QualityDescribe
from ..quality import BaseQuality
from ..specification import ParameterRepr
from ..standard_curve_analysis import StandardCurveAnalysis


class AmpCompositeAnalysisV2(StandardCurveAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Options:
            diff_threshold (float): Twice amp results difference.
        """
        options = super()._default_options()

        options.fine = True
        options.step = None
      
        options.x_label = "Amp [v]"
        options.y_label = ["P1"]
        options.plot_keys = "P1"

        options.use_deviation = False
        options.deviation_limit = 0.5
        options.diff_threshold = 0.2

        options.result_parameters = [
            ParameterRepr(name="Xpi", repr="X-amp", unit="v"),
            ParameterRepr(name="amp_diff", repr="Diff", unit="v")
        ]

        return options

    @staticmethod
    def find_coincident_amp(amp_list: List, N: int) -> List:
        order_amp_list = sorted(amp_list)
        distance_list = []
        iter_count = len(amp_list) - N + 1
        for loop in range(iter_count):
            temp_distance_list = []
            temp_amp = order_amp_list[loop : loop + N]
            for i, _ in enumerate(temp_amp):
                if i > 0:
                    temp_distance = temp_amp[i] - temp_amp[i - 1]
                    temp_distance_list.append(temp_distance)
            distance = sum(temp_distance_list)
            distance_list.append(distance)
        min_dis = min(distance_list)
        min_index = distance_list.index(min_dis)
        min_amp_list = order_amp_list[min_index : min_index + N]
        return min_amp_list

    def _data_processing(self):
        x_data = self.experiment_data.x_data
        y = np.array(self.experiment_data.y_data.get("points"))
        freq = np.array(self.experiment_data.y_data.get("freq"))
        t_list = 1 / freq
        amp_list = [item[0] for item in y]
        y_list = [item[1] for item in y]
        res_list = []
        for amp, t in zip(amp_list, t_list):
            res_list.extend([amp - t, amp, amp + t])
            res_list = [x for x in res_list if 0 < x < 1]

        target_amp_list = self.find_coincident_amp(res_list, len(x_data))
        max_amp = max(target_amp_list)
        min_amp = min(target_amp_list)
        mean_amp = round((sum(target_amp_list)) / len(target_amp_list), 5)
        mean_y = round((sum(y_list)) / len(y_list), 5)
        amp_diff = abs(max_amp - min_amp)

        pos_list = [(mean_amp, mean_y)]
        rp_list = [f"Coincident Point\n{pos}" for pos in pos_list]
        self.drawer.set_options(text_pos=pos_list, text_rp=rp_list)

        pyqlog.info(f"{self.options.result_name} mean_amp: {mean_amp}, amp_diff: {amp_diff}")

        for result in self.results.values():
            result.value = mean_amp
        
        self.results.amp_diff.value = float(amp_diff)

    def _evaluate_quality(self) -> None:
        """Set quality."""
        use_deviation = self.options.use_deviation
        deviation_limit = self.options.deviation_limit
        diff_threshold = self.options.diff_threshold
        mean_amp = self.results.Xpi.value
        amp_diff = self.results.amp_diff.value

        # 2024.08.06, Issue by Kong Zong, recommend validate deviation.
        if use_deviation is True:
            amp_arr = self.experiment_data.child_data(0).x_data
            min_amp, max_amp = min(amp_arr), max(amp_arr)
            mid_amp = (max_amp + min_amp) / 2
            scope = (mid_amp - min_amp) * deviation_limit
            lb, rb = mid_amp - scope, mid_amp + scope
            if lb < mean_amp < rb and amp_diff <= diff_threshold:
                self._quality = BaseQuality.instantiate(QualityDescribe.perfect)
            else:
                self._quality = BaseQuality.instantiate(QualityDescribe.bad)
        elif amp_diff <= diff_threshold:
            self._quality = BaseQuality.instantiate(QualityDescribe.perfect)
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)

    def _initialize_canvas(self):

        # Set Canvas Options
        self.drawer.set_options(
            subplots=(1, 1),
            xlabel=self.options.x_label,
            ylabel=self.options.y_label,
            figsize=self.options.figsize,
            raw_data_format=self.options.raw_data_format,
        )

        # CurveDrawer initialize canvas
        self.drawer.initialize_canvas()

    def _visualization(self):
        # Set plot title.
        self.drawer.set_options(title=self._description())

        default_colors = ["blueviolet", "orangered", "green"]
        x_arr = None

        plot_keys = self.options.plot_keys
        if isinstance(plot_keys, str):
            plot_keys = [plot_keys]
        length = len(plot_keys)

        for i, n in enumerate(self.experiment_data.x_data):
            child_data = self.experiment_data.child_data(index=i)

            if x_arr is None:
                x_arr = child_data.x_data

            i_key = plot_keys[i] if i < length else plot_keys[-1]
            y_arr = child_data.y_data.get(i_key)

            color = default_colors[i % len(default_colors)]

            self.drawer.draw_raw_data(
                x_data=x_arr, y_data=y_arr, ax_index=0, label=f"N={n}", color=color
            )

        self.drawer.draw_text(ax_index=0)
