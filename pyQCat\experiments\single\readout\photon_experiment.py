# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/31
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>


from copy import deepcopy
from typing import List

import numpy as np

from ....acquisition.acq_tackle import QubitMeasureMode
from ....analysis.library import RamseyAnalysis
from ....analysis.library_v2.photon_analysis import (
    DePhaseRamseyAnalysis,
    PhotonNumMeasAnalysis,
)
from ....errors import Experiment<PERSON>ieldError
from ....log import pyqlog
from ....pulse.pulse_function import half_pi_pulse, pi_pulse, zero_pulse
from ....pulse.pulse_lib import Constant
from ....qaio_property import QAIO
from ....qubit import Qubit
from ....structures import Meta<PERSON><PERSON>, Options, QDict
from ....tools import qarange
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment
from ..readout.mcm_experiment import generate_measure_pulse


class DePhaseRamsey(TopExperiment):
    """fit ramsey oscillation with the decaying photons induced de phase"""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("delays", list, limit_null=True)
        options.set_validator("fringe", float)
        options.set_validator("t_relax", float)
        options.set_validator("t_buffer", float)
        options.set_validator("init_state", [0, 1])
        options.set_validator("acq_pulse_params", dict)
        options.set_validator("acq_pulse_type", ["Clear", "Phase", "Square"])
        options.set_validator("scale_coef", float)

        options.delays = qarange(0, 300, 2.5)
        options.fringe = 25
        options.t_relax = 300
        options.t_buffer = 100
        options.init_state = 0
        options.acq_pulse_params = {
            "Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100},
            "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100},
        }
        options.acq_pulse_type = "Square"
        options.scale_coef = 1.0

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("kappa", float)  # MHz * 2 *pi
        options.set_validator("chi", float)  # MHz * 2 *pi
        options.set_validator("t2echo", float)  # us

        options.data_key = None
        options.quality_bounds = [0.9, 0.8, 0.75]
        options.kappa = 1.198
        options.chi = 0.35
        options.t2echo = 14

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.Mwave_width = None
        options.multiple_measure_options = None
        options.measure_modes = None

        return options

    def _check_options(self):
        super()._check_options()

        acq_pulse_params = self.experiment_options.acq_pulse_params
        acq_pulse_type = self.experiment_options.acq_pulse_type
        t_relax = self.experiment_options.t_relax
        t_buffer = self.experiment_options.t_buffer
        delays = self.experiment_options.delays

        acq_pulse = generate_measure_pulse(
            self.qubit,
            acq_pulse_type,
            acq_pulse_params,
            scale_coef=self.experiment_options.scale_coef,
        )
        acq_pulse2 = generate_measure_pulse(self.qubit, "Square")
        x2_pulse = half_pi_pulse(self.qubit)()
        td1 = QAIO.delay_ceil(x2_pulse.width)
        td2 = [t_relax + t_buffer + delay + x2_pulse.width * 2 for delay in delays]

        if not self.discriminator:
            raise ValueError("No find dcm!")

        if self.experiment_options.init_state:
            data_key = [1]
        else:
            data_key = [0]

        self.set_analysis_options(data_key=data_key)
        self.set_experiment_options(data_type="I_Q")
        self.set_run_options(
            x_data=self.experiment_options.delays,
            analysis_class=DePhaseRamseyAnalysis,
            multiple_measure_options={
                self.qubit: QDict(
                    measure_delay=[td1, td2],
                    measure_index=[0, 1],
                    waveform=[acq_pulse, acq_pulse2],
                )
            },
            Mwave_width=acq_pulse.width,
            measure_modes=QubitMeasureMode(
                name=self.qubit.name,
                measure_total=2,
                measure_num_list=[-1],
                base_label_list=[""],
            ),
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        t_relax = self.experiment_options.t_relax
        t_buffer = self.experiment_options.t_buffer
        fringe = self.experiment_options.fringe
        init_state = self.experiment_options.init_state
        readout_type = self.experiment_options.acq_pulse_type
        if t_relax or fringe is not None:
            metadata.draw_meta = {
                "t_relax": (t_relax, "ns"),
                "init_state": init_state,
                "t_buffer": (t_buffer, "ns"),
                "fringe": (fringe, "MHz"),
                "readout": readout_type,
            }

        metadata.process_meta = {"fringe": self.experiment_options.fringe}
        return metadata

    @staticmethod
    def set_xy_pulses(builder):
        qubit = builder.qubit
        t_relax = builder.experiment_options.t_relax
        Mwave_width = builder.run_options.Mwave_width
        multiple_measure_options = builder.run_options.multiple_measure_options

        pi_pulse_obj = pi_pulse(qubit)()
        zero_pulse_obj = zero_pulse(qubit)()
        pre_pi_pulse_width = multiple_measure_options[qubit]["measure_delay"][0]
        pulse_list = DePhaseRamsey.get_xy_pulse(
            qubit, builder.experiment_options.delays, builder.experiment_options.fringe
        )

        new_pulse_list = []
        center_delay = Mwave_width + pre_pi_pulse_width + t_relax - pi_pulse_obj.width
        center_delay_pulse = Constant(center_delay, 0, "XY")()
        if builder.experiment_options.init_state:
            for pulse in pulse_list:
                new_pulse_list.append(
                    deepcopy(pi_pulse_obj) + center_delay_pulse + pulse
                )
        else:
            for pulse in pulse_list:
                new_pulse_list.append(
                    deepcopy(zero_pulse_obj) + center_delay_pulse + pulse
                )

        builder.play_pulse("XY", qubit, new_pulse_list)

    @staticmethod
    def get_xy_pulse(qubit: Qubit, delays: List, fringe: float):
        pulse_list = []
        for delay in delays:
            front_drag = half_pi_pulse(qubit)
            center_delay = Constant(delay, 0, "XY")
            rear_drag = deepcopy(front_drag)

            ramsey_pulse = (
                front_drag()
                + center_delay()
                + rear_drag(phase=2 * np.pi * fringe * 1e6 / 1e9 * delay)
            )
            ramsey_pulse.bit = qubit.bit
            ramsey_pulse.sweep = "sweep delay"

            pulse_list.append(ramsey_pulse)

        return pulse_list


class PhotonNumMeas(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("xy_delay", float)
        options.set_validator("extra_delay", float)
        options.set_validator("nor_rd_trigger", float)
        options.set_validator("scope", dict)
        options.set_validator("mode", ["BF", "LO"])
        options.set_validator("exp_rd_pulse_params", dict)
        options.set_validator("nor_rd_pulse_params", dict)
        options.set_validator("exp_rd_pulse_type", ["Clear", "Phase", "Square"])
        options.set_validator("nor_rd_pulse_type", ["Clear", "Phase", "Square"])
        options.set_validator("init_state", int)
        options.set_validator("pre_amp", float)
        options.set_validator("pre_width", float)
        options.set_validator("fc", float)

        options.exp_rd_pulse_params = {
            "Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100},
            "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100},
        }
        options.exp_rd_pulse_type = "Square"
        options.nor_rd_pulse_params = {
            "Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100},
            "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100},
        }
        options.nor_rd_pulse_type = "Square"
        options.xy_delay = 1800
        options.extra_delay = 50
        options.nor_rd_trigger = 500
        options.scope = {"l": 10, "r": 1, "s": 0.1}
        options.mode = "LO"
        options.init_state = 0
        options.pre_amp = None
        options.pre_width = None
        options.fc = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("quality_bounds", list)
        options.set_validator("effective_chi", float)
        options.data_key = None
        options.quality_bounds = [0.9, 0.8, 0.7]
        options.effective_chi = -0.5
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.bf_list = None
        options.freq_list = None
        options.multiple_measure_options = None
        options.exp_rd_pulse_meas_delay = None
        return options

    def _check_options(self):
        super()._check_options()

        if self.experiment_options.init_state:
            data_key = [0]
        else:
            data_key = [1]

        if not self.discriminator:
            raise ValueError("No find dcm!")

        scope = self.experiment_options.scope
        drive_freq = self.qubit.drive_freq
        exp_rd_pulse_params = self.experiment_options.exp_rd_pulse_params
        exp_rd_pulse_type = self.experiment_options.exp_rd_pulse_type
        nor_rd_pulse_params = self.experiment_options.nor_rd_pulse_params
        nor_rd_pulse_type = self.experiment_options.nor_rd_pulse_type
        nor_rd_trigger = self.experiment_options.nor_rd_trigger
        pre_amp = self.experiment_options.pre_amp
        pre_width = self.experiment_options.pre_width
        fc = self.experiment_options.fc or self.qubit.probe_freq
        pre_amp = pre_amp if pre_amp is not None else self.qubit.Mwave.amp
        pre_width = pre_width if pre_width is not None else self.qubit.Mwave.width
        bf_list = None

        freq_list = qarange(
            round(drive_freq - scope.get("l"), 3),
            round(drive_freq + scope.get("r"), 3),
            scope.get("s"),
        )
        scope_list = qarange(
            round(-scope.get("l"), 3),
            round(scope.get("r"), 3),
            scope.get("s"),
        )
        if self.experiment_options.mode == "BF":
            baseband_freq = self.qubit.XYwave.baseband_freq
            bf_list = np.round(np.array(scope_list) + baseband_freq, 3)
            start, end, _ = QAIO.get_range("intermediate_frequency")
            if np.min(bf_list) < start or np.max(bf_list) > end:
                raise ExperimentFieldError(
                    self.label, f"Sweep baseband freq muse limit in [{start}, {end}]!"
                )
            bf_list = bf_list.tolist()
            pyqlog.info(f"Sweep baseband freq: {bf_list}")

        temp_qubit = deepcopy(self.qubit)
        temp_qubit.Mwave.amp = pre_amp
        temp_qubit.Mwave.width = pre_width
        temp_qubit.Mwave.baseband_freq -= temp_qubit.probe_freq - fc
        exp_rd_pulse = generate_measure_pulse(
            temp_qubit, exp_rd_pulse_type, exp_rd_pulse_params
        )
        nor_rd_pulse = generate_measure_pulse(
            self.qubit, nor_rd_pulse_type, nor_rd_pulse_params
        )

        xy_pulse_width = (pi_pulse(self.qubit)()).width
        exp_rd_trigger = QAIO.delay_ceil(1.5 * xy_pulse_width)

        self.set_experiment_options(
            data_type="I_Q", pre_amp=pre_amp, pre_width=pre_width
        )
        self.set_analysis_options(data_key=data_key)
        self.set_run_options(
            multiple_measure_options={
                self.qubit: QDict(
                    measure_index=[0, 1],
                    measure_delay=[exp_rd_trigger, nor_rd_trigger],
                    waveform=[exp_rd_pulse, nor_rd_pulse],
                )
            },
            measure_modes=QubitMeasureMode(
                name=self.qubit.name,
                measure_total=2,
                measure_num_list=[-1],
                base_label_list=[""],
            ),
            bf_list=bf_list,
            freq_list=freq_list,
            x_data=scope_list,
            analysis_class=PhotonNumMeasAnalysis,
            exp_rd_pulse_meas_delay=exp_rd_trigger,
        )

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        xy_delay = self.experiment_options.xy_delay
        exp_rd_pulse_type = self.experiment_options.exp_rd_pulse_type
        nor_rd_pulse_type = self.experiment_options.nor_rd_pulse_type
        fringe = self.experiment_options.fringe
        metadata.draw_meta = {
            "xy_delay": (xy_delay, "ns"),
            "exp_rd_pulse_type": exp_rd_pulse_type,
            "nor_rd_pulse_type": nor_rd_pulse_type,
        }
        metadata.process_meta = {"fringe": fringe}
        return metadata

    @staticmethod
    def set_xy_pulses(builder):
        qubit = builder.qubit
        init_state = builder.experiment_options.init_state
        extra_delay = builder.experiment_options.extra_delay
        xy_delay = builder.experiment_options.xy_delay

        head_pulse = pi_pulse(qubit)() if init_state else zero_pulse(qubit)()
        head_delay = (
            builder.run_options.exp_rd_pulse_meas_delay - 1.5 * head_pulse.width
        )
        head_delay_pulse = Constant(head_delay, 0, "XY")()

        if builder.experiment_options.mode == "LO":
            x_pulse = (
                head_pulse
                + head_delay_pulse
                + PhotonNumMeas.get_xy_pulse(qubit, xy_delay, extra_delay)
            )
        else:
            x_pulse = []
            for freq in builder.run_options.bf_list:
                temp_qubit = deepcopy(qubit)
                temp_qubit.XYwave.baseband_freq = freq
                xp = (
                    deepcopy(head_pulse)
                    + deepcopy(head_delay_pulse)
                    + PhotonNumMeas.get_xy_pulse(temp_qubit, xy_delay, extra_delay)
                )
                x_pulse.append(xp)

        builder.play_pulse("XY", qubit, x_pulse)

    @staticmethod
    def update_instrument(builder):
        freq_list = builder.run_options.freq_list
        if builder.experiment_options.mode == "LO":
            builder.inst.sweep_freq(
                "XY_control",
                builder.qubit.xy_channel,
                points=freq_list,
                repeat=builder.experiment_options.repeat,
            )

    @staticmethod
    def get_xy_pulse(qubit: Qubit, xy_delay: float, extra_delay: float):
        pi_pulse_obj = pi_pulse(qubit)()
        pre_delay_pulse = Constant(xy_delay, 0, "XY")()
        extra_delay_pulse = Constant(extra_delay, 0, "XY")()
        x_pulse = pre_delay_pulse + pi_pulse_obj + extra_delay_pulse
        return x_pulse


class PhotonRamsey(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("delays", list, limit_null=True)
        options.set_validator("nor_rd_trigger", float)
        options.set_validator("exp_rd_pulse_params", dict)
        options.set_validator("nor_rd_pulse_params", dict)
        options.set_validator("exp_rd_pulse_type", ["Clear", "Phase", "Square"])
        options.set_validator("nor_rd_pulse_type", ["Clear", "Phase", "Square"])
        options.set_validator("fringe", float)
        options.set_validator("init_state", int)
        options.set_validator("ramsey_end", float)
        options.set_validator("pre_amp", float)
        options.set_validator("pre_width", float)

        options.delays = qarange(20, 170, 2.5)
        options.nor_rd_trigger = 500
        options.exp_rd_pulse_params = {
            "Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100},
            "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100},
        }
        options.exp_rd_pulse_type = "Square"
        options.nor_rd_pulse_params = {
            "Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100},
            "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100},
        }
        options.nor_rd_pulse_type = "Square"
        options.fringe = 25
        options.init_state = 0
        options.ramsey_end = 1500
        options.pre_amp = None
        options.pre_width = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("factor", float)

        options.factor = 3.5
        options.data_key = None
        options.quality_bounds = [0.98, 0.93, 0.81]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.multiple_measure_options = None
        return options

    def _check_options(self):
        super()._check_options()

        if not self.discriminator:
            raise ValueError("No find dcm!")

        exp_rd_pulse_params = self.experiment_options.exp_rd_pulse_params
        exp_rd_pulse_type = self.experiment_options.exp_rd_pulse_type
        nor_rd_pulse_params = self.experiment_options.nor_rd_pulse_params
        nor_rd_pulse_type = self.experiment_options.nor_rd_pulse_type
        nor_rd_trigger = self.experiment_options.nor_rd_trigger
        delays = self.experiment_options.delays
        pre_amp = self.experiment_options.pre_amp
        pre_width = self.experiment_options.pre_width
        pre_amp = pre_amp if pre_amp is not None else self.qubit.Mwave.amp
        pre_width = pre_width if pre_width is not None else self.qubit.Mwave.amp

        temp_qubit = deepcopy(self.qubit)
        temp_qubit.Mwave.amp = pre_amp
        temp_qubit.Mwave.width = pre_width
        exp_rd_pulse = generate_measure_pulse(
            temp_qubit, exp_rd_pulse_type, exp_rd_pulse_params
        )
        nor_rd_pulse = generate_measure_pulse(
            self.qubit, nor_rd_pulse_type, nor_rd_pulse_params
        )

        self.qubit.Mwave.amp = pre_amp
        self.set_experiment_options(
            data_type="I_Q", pre_amp=pre_amp, pre_width=pre_width
        )
        self.set_run_options(
            multiple_measure_options={
                self.qubit: QDict(
                    measure_index=[0, 1],
                    measure_delay=[0, nor_rd_trigger],
                    waveform=[exp_rd_pulse, nor_rd_pulse],
                )
            },
            measure_modes=QubitMeasureMode(
                name=self.qubit.name,
                measure_total=2,
                measure_num_list=[-1],
                base_label_list=[""],
            ),
            x_data=delays,
            analysis_class=RamseyAnalysis,
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        pre_amp = self.experiment_options.pre_amp
        pre_width = self.experiment_options.pre_width
        init = self.experiment_options.init_state
        if pre_amp or pre_width is not None:
            metadata.draw_meta = {
                "pre_amp": (pre_amp, "v"),
                "init_state": init,
                "pre_width": (pre_width, "ns"),
            }
        return metadata

    @staticmethod
    def set_xy_pulses(builder):
        qubit = builder.qubit
        ramsey_end = builder.experiment_options.ramsey_end
        init_state = builder.experiment_options.init_state

        init_pulse = pi_pulse(qubit)() if init_state else zero_pulse(qubit)()
        init_pulse_width = init_pulse.width

        # fixed the last half pi pulse, keep the xy pulse end between the readout pulse begin length consistent
        ramsey_pulse_list = PhotonRamsey.get_xy_pulse_pre(
            builder.qubit,
            builder.experiment_options.delays,
            builder.experiment_options.fringe,
        )

        ramsey_pulse_width = ramsey_pulse_list[-1].width
        center_delay = ramsey_end - ramsey_pulse_width - init_pulse_width
        if center_delay < 0:
            raise ValueError(f"center delay {center_delay} < 0")
        pre_delay_pulse = Constant(center_delay, 0, "XY")()

        new_pulse_list = []
        for pulse in ramsey_pulse_list:
            new_pulse_list.append(
                deepcopy(init_pulse) + deepcopy(pre_delay_pulse) + pulse
            )

        builder.play_pulse("XY", qubit, new_pulse_list)

    @staticmethod
    def get_xy_pulse_pre(qubit, delays: List, fringe: float):
        """Get XY line wave"""
        sum_delay = delays[-1]
        pulse_list = []
        for delay in delays:
            front_delay = Constant(sum_delay - delay, 0, "XY")
            front_drag = half_pi_pulse(qubit)
            center_delay = Constant(delay, 0, "XY")
            rear_drag = deepcopy(front_drag)

            ramsey_pulse = (
                front_delay()
                + front_drag()
                + center_delay()
                + rear_drag(phase=2 * np.pi * fringe * 1e6 / 1e9 * delay)
            )
            ramsey_pulse.bit = qubit.bit
            ramsey_pulse.sweep = "sweep delay"

            pulse_list.append(ramsey_pulse)

        return pulse_list
