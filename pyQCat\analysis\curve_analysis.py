# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/05/24
# __author:       <PERSON><PERSON><PERSON>
"""
Base class of curve analysis.
"""

import math
import textwrap
import warnings
from typing import Union, List, Tuple, Any
from pyQCat.structures import CommonDict


import numpy as np
from scipy.signal import savgol_filter

from .fit.curve_fit import curve_fitting
from .quality import GoodnessofFit, BaseQuality
from .specification import FitOptions, FitModel, CurveAnalysisData
from .top_analysis import TopAnalysis
from .visualization.curve_drawer import CurveDrawer
from ..errors import CurveFittingError
from ..log import pyqlog
from ..structures import Options, QDict
from ..types import QualityDescribe


class CurveAnalysis(TopAnalysis):
    """Abstract superclass of curve analysis base classes."""

    @property
    def data_filter(self) -> CommonDict:
        """The parameters provided for data filter functions."""
        return self._options.filter

    @classmethod
    def _default_options(cls) -> Options:
        """Return default analysis options.

        Analysis Options:
            p0 (Dict[str, float]): Initial guesses for the fit parameters.
                The dictionary is keyed on the fit parameter names.
            bounds (Dict[str, Tuple[float, float]]): Boundary of fit parameters.
                The dictionary is keyed on the fit parameter names and
                values are the tuples of (min, max) of each parameter.
            curve_fit_extra (Dict[str, Any]) Options that are passed to the
                scipy curve fit which performs the least square fitting on the experiment results.
            curve_drawer (BaseCurveDrawer): A curve drawer instance to visualize
                the analysis result.
            is_plot (bool): Set ``True`` to create figure for fit result.
                This is ``True`` by default.
            plot_raw_data (bool): Set ``True`` to draw processed data points,
                dataset without formatting, on canvas. This is ``False`` by default.
            plot_style (Dict[str, Any]): A stylesheet for curve analysis figure.
            plot_iq_data (bool): Set ``True`` to draw processed IQ data points.
                This is ``False`` by default.
            quality_bounds (Tuple): Boundary of fit quality.
        """
        options = super()._default_options()
        _default_curve_fit_extra = {
            "rmse_bound": 1e-3,
            "max_iteration": 1,
            "maxfev": 100000,
            "ftol": 1.49012e-4,
            "xtol": 1.49012e-4,
        }
        # Set validator for particular option values.
        options.set_validator(field="fit_model", validator_value=FitModel)
        options.set_validator(field="curve_drawer", validator_value=CurveDrawer)

        # Data filter
        # default method is data smooth.
        options.filter = None

        # Fit options.
        options.p0 = None
        options.bounds = {}
        options.curve_fit_extra = _default_curve_fit_extra

        # Plot options.
        options.curve_drawer = CurveDrawer()
        options.plot_raw_data = True
        options.x_label = None
        options.y_label = None
        options.sub_title = None
        options.raw_data_format = "scatter"

        # Quality options.
        # Every experiment has different quality bounds.
        options.quality_bounds = [0.98, 0.95, 0.85]

        # Result options.
        options.result_parameters = []

        # result figure size
        options.figsize = (12, 8)

        # plot all y data
        options.merge_y_data = False

        return options

    def _create_analysis_data(self) -> CommonDict:
        """Create Analysis data provided for detailed analyze.

        Returns:
            A QDict object, key represents data type and value is
            CurveAnalysisData object.
        """
        analysis_data_dict = QDict()
        data_key = list(
            self.options.get("data_key", None) or self._experiment_data.y_data.keys()
        )
        for key in data_key:
            if key in self.experiment_data.y_data:
                analysis_data = CurveAnalysisData(
                    x=np.copy(self._experiment_data.x_data),
                    y=np.copy(self.experiment_data.y_data.get(key, [])),
                )
                analysis_data_dict[key] = analysis_data
        return analysis_data_dict

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,  # pylint: disable=unused-argument
    ) -> Union[FitOptions, List[FitOptions]]:
        """Create algorithmic guess with analysis options and curve data.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        return fit_opt

    def _evaluate_quality(self) -> Tuple[str, Any]:
        """Evaluates the quality of the fit based on the fit result.

        Returns:
            The goodness of fit.
        """
        quality_dict = {}
        if self.options.get("fit_model"):
            for data_key, analysis_data in self.analysis_datas.items(): # type: ignore
                if analysis_data.fit_data is not None:
                    quality_dict[data_key] = analysis_data.fit_data.goodness_of_fit

        if quality_dict:
            sorted_quality = sorted(quality_dict.items(), key=lambda kv: kv[1].value)
            *_, better_quality = sorted_quality
            data_key, self._quality = better_quality
            return data_key, better_quality
        else:
            data_key, quality = "", None
            y_data_keys = list(self.experiment_data.y_data.keys())
            if self.options.data_key is not None:
                data_key = self.options.data_key[0]
            elif y_data_keys:
                data_key = y_data_keys[0]
            return data_key, quality

    def _initialize(self):
        """Initialize curve analysis with experiment data."""
        # do some prepare flow
        self._prepare_subplot()

        # create analysis data and result data.
        self._analysis_data_dict = self._create_analysis_data()
        self._results = self._create_analysis_result()

        # Initialize canvas
        if self.options.is_plot:
            self._initialize_canvas()

        # filter data.
        if self.data_filter:
            self._data_filter()

    def _data_filter(self):
        """Pre-processing for raw data."""
        for analysis_data in self.analysis_datas.values():
            smooth_y = savgol_filter(analysis_data.y, **self.data_filter)
            analysis_data.y = smooth_y

    def _run_fitting(self) -> bool:
        """Perform curve fitting on given data collection and fit models.

        Returns:
            fitting success or failed.
        """
        default_fit_opt = FitOptions(
            parameters=self.options.fit_model.signature,
            default_p0=self.options.p0,
            default_bounds=self.options.bounds,
            **self.options.curve_fit_extra,
        )
        fit_results = []
        for analysis_data in self.analysis_datas.values():
            # Guess initial parameters.
            new_fit_opt = default_fit_opt.copy()
            guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)
            if isinstance(guess_fit_options, FitOptions):
                guess_fit_options = [guess_fit_options]

            # Run fit for each guessed parameters.
            sub_fit_results = []
            for guess_fit_option in set(guess_fit_options):
                try:
                    fit_data = curve_fitting(
                        analysis_data, guess_fit_option, self.options.fit_model
                    )
                    sub_fit_results.append(fit_data)
                except CurveFittingError as analysis_error:
                    # Some guesses might be too far from the true parameters and may thus fail.
                    # We ignore initial guesses that fail and continue with the next fit candidate.
                    # todo log remind fitting failed
                    pyqlog.warning(f"<Curve Fitting Error> | {analysis_error}")
                    pass

            # fixed bug:
            fit_results += sub_fit_results

            if sub_fit_results:
                # Find best value with r-square value.
                for fit_data in sub_fit_results:
                    fit_data.goodness_of_fit = GoodnessofFit(
                        *self.options.quality_bounds
                    )
                    fit_data.goodness_of_fit.evaluate(analysis_data.y, fit_data.y_fit)
                analysis_data.fit_data = sorted(
                    sub_fit_results, key=lambda r: r.goodness_of_fit.value
                )[-1]

        # if all guess parameters fitting failed, return false.
        if len(fit_results) == 0:
            warnings.warn(
                "All initial guesses and parameter boundaries failed to fit the data. "
                "Please provide better initial guesses or fit parameter boundaries.",
                UserWarning,
            )
            # at least return raw data points rather than terminating
            return False
        else:
            return True

    def _description(self, width: int = 70):
        """Returns the description of the analysis result, which is often used as the title of the experiment result
        plot.

        Args:
          width: The width of the description. If width is `30`, then each line of the description has 30 characters.
          If set to `None`, then no wrap will be performed.
          The default value of `figsize` (set in `pyQCat.experiments.base_experiment.BaseExperiment.
          _default_analysis_options`) is `(12, 8)`, in which case `52` can be an appropriate value for width.

        Returns:
          The description of the analysis result.

        Raises:
          ValueError: If width <= 0, this error is raised.
        """
        if width and width <= 0:
            raise ValueError(f"The width ({width}) is invalid (must be > 0).")

        metadata = self.experiment_data.metadata
        if not metadata:
            return f"{self.__class__.__name__}"

        str_list = []

        if metadata.name:
            str_list.append(metadata.name)

        if metadata.qubits or metadata.couplers:
            list_of_units = []
            if metadata.qubits:
                list_of_units.extend(metadata.qubits)
            if metadata.couplers:
                list_of_units.extend(metadata.couplers)
            unit_description = "("
            unit_description += ", ".join(list_of_units)
            unit_description += ")"
            str_list.append(unit_description)

        if metadata.draw_meta:
            list_of_draw_meta = []
            for k, v in metadata.draw_meta.items():
                if isinstance(v, tuple):
                    list_of_draw_meta.append(f"{k}={v[0]}{v[1]}")
                else:
                    list_of_draw_meta.append(f"{k}={v}")
            str_list.append(", ".join(list_of_draw_meta))

        if self.results is not None and len(self.results) > 0:
            for result in self.results.values():
                if result.extra.get("out_flag") is not False:
                    str_list.append(result.__str__())

        if self.quality:
            if isinstance(self.quality, str):
                str_list.append(f"quality={self.quality}")
            else:
                str_list.append(self.quality.__repr__())

        description = " ".join(str_list)
        if width:
            description = "\n".join(textwrap.wrap(description, width=width))

        return description

    def _visualization(self):
        """Draw all data, including raw data and fit data and so on.
        Some composite experiments may need to override this function.
        """
        # Set plot title.
        self.drawer.set_options(title=self._description())

        # get experiment keys.
        exp_keys = list(self.experiment_data.y_data.keys())

        # plot raw data.
        if self.options.plot_raw_data:
            for i, key in enumerate(exp_keys):
                x_data = self.experiment_data.x_data
                raw_data = self.experiment_data.y_data[key]
                self.drawer.draw_raw_data(x_data=x_data, y_data=raw_data, ax_index=i)
        # plot fit data and filtered data.
        for data_key, analysis_data in self.analysis_datas.items(): # type: ignore
            # Sometimes, only one set of the raw data needs to be fitted.
            # So we need to figure out on which axis is the result of the fit drawn.
            draw_index = exp_keys.index(data_key)
            analysis_x = analysis_data.x
            if self.options.get("fit_model"):
                analysis_fit = analysis_data.fit_data
                if analysis_fit is not None:
                    fit_data = analysis_fit.y_fit
                    self.drawer.draw_fit_line(
                        x_data=analysis_x, y_data=fit_data, ax_index=draw_index
                    )
            if self.options.filter:
                filtered_data = analysis_data.y
                self.drawer.draw_filter_data(
                    x_data=analysis_x, y_data=filtered_data, ax_index=draw_index
                )
            # plot annotations.
            if self.drawer.options.text_pos and self.drawer.options.text_rp:
                if data_key in self.drawer.options.text_key:
                    self.drawer.draw_text(ax_index=draw_index)

        # plot all y data in on axis
        if self.options.merge_y_data:
            self.drawer.set_options(raw_data_format="plot")

            x_arr = self.experiment_data.x_data
            y_data = self.experiment_data.y_data
            default_colors = self.drawer.options.default_colors

            base_ax_index = len(y_data.keys())
            color_len = len(default_colors)
            draw_ops = {"linewidth": 2.5, "color": None}
            for i, key in enumerate(y_data.keys()):
                y_arr = y_data.get(key)
                draw_ops.update({"label": key})
                if color_len >= base_ax_index:
                    draw_ops.update({"color": default_colors[i]})
                self.drawer.draw_raw_data(
                    x_data=x_arr, y_data=y_arr, ax_index=base_ax_index, **draw_ops
                )

        # Finalize plot.
        self.drawer.format_canvas()

    def _extract_result(self, data_key: str):
        """Extract analysis results from important fit parameters.

        Args:
            data_key (str): The basis for selecting data.
        """
        analysis_data = self.analysis_datas[data_key] # type: ignore
        for p_name, result in self.results.items(): # type: ignore
            # Sometimes the fit values are not the final result.
            # The target value needs to extract which depends on fit value.
            if p_name in analysis_data.fit_data.popt_keys:
                target_val = analysis_data.fit_data.fitval(p_name)
                result.value = target_val

    def run_analysis(self):
        """Run analysis on experiment data.

        Notes:
            Sub analysis classes may need to override this method.
        """
        # Prepare for fitting
        self._initialize()

        # Run data fitting.
        if self.options.get("fit_model"):
            res = self._run_fitting()
            # when set one data_key, and fit error should plot raw data.

            # records fitting states
            self.update_analysis_state("fit", res)

        # Create figure and result data
        if self.analysis_datas:
            better_data_key, _ = self._evaluate_quality()
            better_data_key = better_data_key or ""
            self.update_analysis_state("result", True)
            if not self.options.get("fit_model"):
                self._extract_result(better_data_key)
            elif self.options.get("fit_model") and self.quality:
                self._extract_result(better_data_key)
            else:
                if self.quality is None:
                    self._quality = BaseQuality.instantiate(QualityDescribe.bad)
                self.update_analysis_state("result", False)

        if self.options.is_plot:
            self._visualization()

    def _initialize_canvas(self):
        """Initialize matplotlib canvas."""
        self.drawer.set_options(
            subplots=self.options.get("subplots")
            or (len(self.experiment_data.y_data), 1),
            xlabel=self.options.get("x_label") or "x",
            ylabel=self.options.get("y_label")
            or list(self.experiment_data.y_data.keys()),
            sub_title=self.options.sub_title,
            figsize=self.options.figsize,
            raw_data_format=self.options.get("raw_data_format"),
        )
        self.drawer.initialize_canvas()

    def _prepare_subplot(self):
        if self.options.merge_y_data:
            # extend sub plot all y data
            y_labels = list(self._experiment_data.y_data.keys())
            y_labels.append("Probability")

            length = len(y_labels)
            row = math.ceil(length / 2)
            subplots = (row, 2)

            diff = subplots[0] * subplots[1] - length
            if diff > 0:
                y_labels.extend([""] * diff)

            self.set_options(y_label=y_labels, subplots=subplots)
