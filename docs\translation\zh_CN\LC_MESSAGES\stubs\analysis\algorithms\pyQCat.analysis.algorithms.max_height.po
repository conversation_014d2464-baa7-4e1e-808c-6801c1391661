# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.max_height.rst:2
msgid "pyQCat.analysis.algorithms.max\\_height"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.max_height:1
msgid "Get maximum value of y curve and its index."
msgstr "获取y曲线的最大值及其索引。"

#: of pyQCat.analysis.algorithms.guess.max_height
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.max_height:4
msgid "Array of y values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.max_height:6
msgid "Return that percentile value if provided, otherwise just return max value."
msgstr "如果提供，则返回该百分位值，否则只返回最大值。"

#: of pyQCat.analysis.algorithms.guess.max_height:8
msgid "Use absolute y value."
msgstr "是否对y值使用绝对值"

#: of pyQCat.analysis.algorithms.guess.max_height
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.max_height:10
msgid ":py:data:`~typing.Tuple`\\[:py:class:`float`, :py:class:`int`]"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.max_height
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.max_height:11
msgid "The maximum y value and index."
msgstr "最大的y值及索引"

