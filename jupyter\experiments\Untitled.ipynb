{"cells": [{"cell_type": "markdown", "id": "378ba5fd", "metadata": {}, "source": ["# SwapOnce寻峰出错的问题"]}, {"cell_type": "code", "execution_count": 34, "id": "756dba7a", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.optimize import curve_fit\n", "import math\n", "\n", "def cosine(x, freq, phi, amp, offset):\n", "    y = amp * np.cos(2 * np.pi * freq * (x - phi)) + offset\n", "    return y\n", "\n", "def curve_fit_step(x, y, p0, func, num, bounds):\n", "    popt, rmse, fit_y = None, None, None\n", "    p = p0\n", "    for _ in range(num):\n", "        popt, pocv = curve_fit(func, x, y, p, bounds=bounds, maxfev=100000)\n", "        p = popt\n", "        fit_y = func(x, *popt)\n", "        rmse = RMSE(x, y, fit_y)\n", "        if rmse <= 1e-3:\n", "            break\n", "    return popt, rmse, fit_y\n", "\n", "def RMSE(x, y1, y2):\n", "    variances = list(map(lambda x, y: (x - y) ** 2, y1, y2))\n", "    variance = np.sum(variances)\n", "    rmse = np.sqrt(variance / len(x))\n", "    # print(variance)\n", "    return rmse\n", "\n", "def cosine_fit(xdata, ydata, iter_count: int = 1000, guess_freq=None, guess_phase=None, guess_amplitude=None,\n", "               guess_offset=None, bounds=(-np.inf, np.inf)):\n", "    x_fig = np.array(list(map(lambda x: x - xdata[0], xdata)))\n", "    multiple = x_fig[-1] / 1\n", "    y_fit = np.fft.fft(ydata)[range(int(len(ydata) / 2))]\n", "    yfreal = y_fit.real\n", "    yfimag = y_fit.imag\n", "    yf = np.sqrt(yfreal ** 2 + yfimag ** 2)\n", "    yf1 = yf[range(1, len(yf))]\n", "    index = np.argmax(yf1) + 1\n", "    yreal = y_fit[index].real\n", "    yimag = y_fit[index].imag\n", "    if guess_amplitude is None:\n", "        guess_amplitude = yf[index] / len(ydata) * 2\n", "    if guess_freq is None:\n", "        if index == 1:\n", "            x_top = np.take(xdata, np.argmax(ydata))\n", "            x_bottom = np.take(xdata, np.argmin(ydata))\n", "            t = 2 * abs(x_top - x_bottom)\n", "            guess_freq = 1 / t\n", "        else:\n", "            guess_freq = index / multiple\n", "    if guess_phase is None:\n", "        guess_phase = math.atan2(yimag, yreal) + np.pi / 2\n", "    if guess_offset is None:\n", "        guess_offset = yf[0].real / len(ydata)\n", "    p0 = [guess_freq, guess_phase, guess_amplitude, guess_offset]\n", "    popt, rmse, fit_data = curve_fit_step(xdata, ydata, p0, cosine, iter_count, bounds=bounds)\n", "    return popt, rmse, fit_data"]}, {"cell_type": "code", "execution_count": 45, "id": "299fb7c0", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x25683ec6a08>"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = np.loadtxt(\n", "    r'F:\\Data\\ExperimentData\\2022-09-05\\2022-09-06_02.13.22\\SwapOnce\\q2q3q4\\2022-09-06\\2022-09-06_02.15.41\\Z=0.136_constZ=0.0_state=11_raw_data.dat'\n", ")\n", "popt, _, fit_p1 = cosine_fit(data[:, 0], data[:, 2])\n", "plt.plot(data[:, 0], data[:, 2], label='raw data')\n", "plt.plot(data[:, 0], fit_p1, label='fit data')\n", "plt.legend()"]}, {"cell_type": "code", "execution_count": 46, "id": "2ea5991a", "metadata": {}, "outputs": [], "source": ["from scipy.signal import argrelmax, find_peaks\n", "\n", "# 优化前的\n", "def find_first_max_p1_width_pre(x, y):\n", "    maximum = argrelmax(y)\n", "    # 此处多做了一步，寻找值最大的极值点对应的索引\n", "    max_index = np.argmax(y[maximum])\n", "    P_max_index = maximum[0][max_index]\n", "    return x[P_max_index]\n", "\n", "# 优化后的\n", "def find_first_max_p1_width_opt(x, y):\n", "    max_index = argrelmax(y)[0]\n", "    \n", "    return x[max_index[0]]"]}, {"cell_type": "code", "execution_count": 47, "id": "9f14f6ca", "metadata": {}, "outputs": [{"data": {"text/plain": ["160.0"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["find_first_max_p1_width_pre(data[:, 0], fit_p1)"]}, {"cell_type": "code", "execution_count": 48, "id": "cc01f52c", "metadata": {}, "outputs": [{"data": {"text/plain": ["82.5"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["find_first_max_p1_width_opt(data[:, 0], fit_p1)"]}, {"cell_type": "code", "execution_count": null, "id": "fbd8b524", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.12"}}, "nbformat": 4, "nbformat_minor": 5}