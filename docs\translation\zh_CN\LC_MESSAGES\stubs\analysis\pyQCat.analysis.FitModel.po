# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.FitModel.rst:2
msgid "pyQCat.analysis.FitModel"
msgstr ""

#: of pyQCat.analysis.specification.FitModel:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitModel.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitModel.rst:21:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.FitModel.__init__>`\\ "
"\\(fit\\_func\\[\\, name\\, plot\\_color\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitModel.rst:23
msgid "Attributes"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitModel.rst:31:<autosummary>:1
msgid ":py:obj:`model_description <pyQCat.analysis.FitModel.model_description>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitModel.rst:31:<autosummary>:1
msgid ":py:obj:`name <pyQCat.analysis.FitModel.name>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitModel.rst:31:<autosummary>:1
msgid ":py:obj:`plot_color <pyQCat.analysis.FitModel.plot_color>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitModel.rst:31:<autosummary>:1
msgid ":py:obj:`plot_symbol <pyQCat.analysis.FitModel.plot_symbol>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitModel.rst:31:<autosummary>:1
msgid ":py:obj:`fit_func <pyQCat.analysis.FitModel.fit_func>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitModel.rst:31:<autosummary>:1
msgid ":py:obj:`signature <pyQCat.analysis.FitModel.signature>`\\"
msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.analysis.FitModel.__init__>`\\ "
#~ "\\(fit\\_func\\[\\, name\\, plot\\_color\\, "
#~ "...\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`model_description <pyQCat.analysis.FitModel.model_description>`\\"
#~ msgstr ""

#~ msgid ":obj:`name <pyQCat.analysis.FitModel.name>`\\"
#~ msgstr ""

#~ msgid ":obj:`plot_color <pyQCat.analysis.FitModel.plot_color>`\\"
#~ msgstr ""

#~ msgid ":obj:`plot_symbol <pyQCat.analysis.FitModel.plot_symbol>`\\"
#~ msgstr ""

#~ msgid ":obj:`fit_func <pyQCat.analysis.FitModel.fit_func>`\\"
#~ msgstr ""

#~ msgid ":obj:`signature <pyQCat.analysis.FitModel.signature>`\\"
#~ msgstr ""

