# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/05/16
# __author:       <PERSON> <PERSON>

"""
Optimize FIR response composite experiment.

"""

from copy import deepcopy
from datetime import datetime
from typing import List, <PERSON>ple, Union

import numpy as np
import pandas as pd
from scipy.interpolate import interp1d

from ....analysis.algorithms.smooth import smooth
from ....analysis.library.optimize_fir_analysis import OptimizeFIRAnalysis
from ....log import pyqlog
from ....parameters import options_wrapper
from ....qaio_property import QAIO
from ....structures import ExperimentData, MetaData, Options
from ....tools.savefile import LocalFile, S3File
from ....tools.utilities import format_results, get_xy_step, qarange
from ....types import ExperimentRunMode, QualityDescribe, StandardContext
from ...composite_experiment import CompositeExperiment
from ...single import (
    CouplerDistortionT1,
    CouplerDistortionZZ,
    DistortionT1,
)
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class OptimizeFIR(CompositeExperiment):
    """Optimize FIR response composite experiment class."""

    _sub_experiment_class = DistortionT1

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("iteration_times", (1, 10, 0))
        options.set_validator("average_times", (1, 10, 0))
        options.set_validator("xy_delay_start", float)
        options.set_validator("xy_delay_max", float)
        options.set_validator("add_width", float)
        options.set_validator("xy_step_first_map", dict)
        options.set_validator("xy_step_avg_map", dict)

        options.set_validator("bit_type", ["Qubit", "Coupler"])
        options.set_validator("mean_smooth", bool)
        options.set_validator("first_flag", bool)
        options.set_validator("update_z_offset_range", bool)
        options.set_validator("scan_points", int)

        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("z_offset_list", list)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        # distortion t1 composite experiment args
        options.iteration_times = 2
        options.average_times = 3

        options.xy_delay_start = 0.0
        options.xy_delay_max = 2000
        options.add_width = 100.0
        options.xy_step_first_map = {
            "lt_10": 0.625,
            "lt_50": 1.25,
            "lt_100": 5.0,
            "lt_200": 10.0,
            "lt_500": 20.0,
            "lt_1000": 50.0,
            "lt_5000": 100.0,
            "lt_10000": 200.0,
            "gt_10000": 300.0,
            "gt_20000": 400.0,
        }

        options.xy_step_avg_map = {
            "lt_10": 0.625,
            "lt_50": 1.25,
            "lt_100": 2.5,
            "lt_200": 5.0,
            "lt_400": 10.0,
            "lt_600": 15.0,
            "lt_1000": 20.0,
            "lt_10000": 50.0,
            "gt_10000": 100.0,
        }

        options.bit_type = "Qubit"
        options.mean_smooth = False
        options.first_flag = False
        options.update_z_offset_range = False
        options.scan_points = 70

        # distortion t1 once experiment args
        options.z_amp = None
        options.z_offset_list = qarange(-0.1, 0.1, 0.002)

        options.run_mode = ExperimentRunMode.sync_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for DistortionT1Composite experiment.

        Options:
            target_std (float): Set standard sigma, calculate fir_width.
            target_fir_width (float): Set target fir_width, judge analysis quality.
            t_min (float): Used to calculate h array, t start value.
            t_max (float): Used to calculate h array, t end value.

        """
        options = super()._default_analysis_options()

        options.set_validator("target_std", float)
        options.set_validator("target_fir_width", float)
        options.set_validator("t_min", float)
        options.set_validator("t_max", float)

        options.set_validator("adjust_noise", bool)
        # options.set_validator("n_multiple", float)
        options.set_validator("use_inline", bool)
        options.set_validator("rate", float)

        options.sample_rate = 1.2  # AIO AWG sample rate
        options.target_std = 0.00015  # used to calculate fir_width
        options.target_fir_width = 30.0  # used to judge fir_width
        options.t_min = None  # calculate h array, t start value
        options.t_max = None  # calculate h array, t end value
        options.h_old_list = []  # need set, list of list, or list of array
        options.y_field = "Response"  # self.experiment_data.y_data field
        options.sub_key = "P1"  # set sub analysis data key string
        options.z_amp = -0.5
        options.cal_response_mode = "add"

        options.adjust_noise = True
        options.n_multiple = 1.0
        options.use_inline = False
        options.rate = 0.92

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method.
        Statistics and saving parameters of running process.

        Options:
            mark_info (str): Mark iteration or average main information.

        """
        options = super()._default_run_options()

        options.mark_info = ""
        options.set_xy_delay_list = []
        options.p0_history = None

        options.xy_delay_list = []
        options.offset_list = []
        options.normal_offset_list = []
        options.response_list = []

        options.mean_delay_arr = np.array([])
        options.mean_resp_arr = np.array([])
        options.normal_resp_arr = np.array([])

        options.init_p1_value_arr = np.array([])
        options.start_offset = -0.1
        options.end_offset = 0.1
        options.data_decimal = 3
        options.z_step = 0.001
        options.cal_response_mode = ""

        # adjust, when `new_case` only once modify child_exp readout point.
        options.child_rdz_amp = None
        options.child_ac_bias = {}

        options.support_context = [
            StandardContext.QC,
            StandardContext.CPC,
        ]

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "Zamp": self.experiment_options.z_amp,
            "cal_response_mode": self.analysis_options.cal_response_mode,
        }
        return metadata

    def _patch_z_amp(self, t_name: str):
        """According to work point value set z_amp."""
        run_mode = self.child_experiment.experiment_options.run_mode
        if self.experiment_options.z_amp is None:
            if self.ac_bias.get(t_name):
                w_point = self.ac_bias[t_name][1]
            elif self.working_dc.get(t_name):
                w_point = self.working_dc[t_name][1]
            else:
                qc_map = {}
                qubit_map = {qubit.name: qubit for qubit in self.qubits}
                coupler_map = {qubit.name: qubit for qubit in self.couplers}
                qc_map.update(qubit_map)
                qc_map.update(coupler_map)
                qc_obj = qc_map.get(t_name)
                if qc_obj:
                    w_point = qc_obj.dc_max + qc_obj.idle_point
                else:
                    raise ValueError(f"Set {t_name} error: not in exist environment!")
            if w_point < 0:
                z_amp = round(0.45 - w_point, 3)
            else:
                z_amp = round(-0.45 - w_point, 3)

            if run_mode == "normal":
                z_amp = z_amp
            elif run_mode == "new_case":
                z_amp = -z_amp

            self.experiment_options.z_amp = z_amp
            pyqlog.info(
                f"Patch {t_name} working_point: {w_point}, run_mode: {run_mode} z_amp: {z_amp}"
            )
        pyqlog.info(f"{t_name} Set z_amp: {self.experiment_options.z_amp}")

    def _check_options(self):
        """Check options."""
        super()._check_options()

        bit_type = self.experiment_options.bit_type
        init_z_offset_list = self.experiment_options.z_offset_list
        result_name = self.analysis_options.result_name
        h_old_list = self.analysis_options.h_old_list
        sub_key = self.analysis_options.sub_key

        if bit_type.capitalize() == "Qubit":
            result_name = self.child_experiment.qubit.name
            sub_key = "P1"
        elif bit_type.capitalize() == "Coupler":
            result_name = self.child_experiment.coupler.name
            sub_key = "P0"

        if self.child_experiment.is_coupler_exp is True:
            result_name = self.child_experiment.coupler.name
            # bugfix: coupler distortion need use coupler compensate
            # x_delay to drive qubit x_delay
            drive_q_compensate = None
            coupler_compensate = None
            for bit, compensate in self.compensates.items():
                if bit.name == self.child_experiment.coupler.name:
                    coupler_compensate = compensate
                if bit.name == self.child_experiment.driveQ.name:
                    drive_q_compensate = compensate
            drive_q_compensate.x_delay = coupler_compensate.x_delay

        sample_rate = QAIO.awg_sample_rate
        pyqlog.info(f"AIO Z line sample rate: {sample_rate} GHz.")

        for compensate in self.compensates.values():
            if compensate.name == result_name:
                sos_params = compensate.z_distortion_sos
                h_old_list = sos_params.get("Low_temperature_FIR_tf_filter", [])

        # set some run options
        init_p1_value_arr = np.zeros_like(init_z_offset_list)
        start_offset = init_z_offset_list[0]
        end_offset = init_z_offset_list[-1]
        data_decimal = max(
            ["{:f}".format(i).rstrip("0")[::-1].find(".") for i in init_z_offset_list]
        )
        z_step = round(abs(init_z_offset_list[1] - init_z_offset_list[0]), data_decimal)

        self._patch_z_amp(result_name)
        z_amp = self.experiment_options.z_amp

        self.set_run_options(
            init_p1_value_arr=init_p1_value_arr,
            start_offset=start_offset,
            end_offset=end_offset,
            data_decimal=data_decimal,
            z_step=z_step,
        )

        self.set_analysis_options(
            result_name=result_name,
            sample_rate=sample_rate,
            sub_key=sub_key,
            h_old_list=h_old_list,
            z_amp=z_amp,
        )

    def _save_curve_analysis_plot(self, save_mark: str = None):
        """Save CurveAnalysis plot figure. Overwrite."""
        if save_mark is None:
            mark_info = self.run_options.mark_info
            time_str = datetime.now().strftime("%Y-%m-%d %H.%M.%S")
            save_mark = f"{mark_info}_{time_str}"
        super()._save_curve_analysis_plot(save_mark=save_mark)

    def _adjust_xy_delay_list(
        self, start: float, end: float, mode: str = "first"
    ) -> List:
        """Adjust xy_delay_list."""
        if mode == "first":
            xy_step_map = self.experiment_options.xy_step_first_map
        elif mode == "avg":
            xy_step_map = self.experiment_options.xy_step_avg_map
        else:
            xy_step_map = {
                "lt_10": 0.625,
                "lt_50": 1.25,
                "lt_100": 5.0,
                "lt_200": 10.0,
                "lt_400": 20.0,
                "lt_600": 40.0,
                "lt_1000": 80.0,
                "lt_5000": 120.0,
                "gt_10000": 200.0,
            }

        xy_delay_list = []
        xy_delay = start
        while xy_delay <= end:
            xy_delay_list.append(xy_delay)
            update_step = get_xy_step(xy_delay, xy_step_map)
            xy_delay += update_step
        return xy_delay_list

    def _adjust_z_offset_list(self, offset: float, xy_delay: float):
        """Adjust z_offset_list."""
        init_z_offset_list = self.experiment_options.z_offset_list
        scan_points = self.experiment_options.scan_points

        offset_list = self.run_options.offset_list
        start_offset = self.run_options.start_offset
        end_offset = self.run_options.end_offset
        data_decimal = self.run_options.data_decimal
        z_step = self.run_options.z_step

        new_offset = init_z_offset_list[
            np.argmin(np.abs(offset - np.array(init_z_offset_list)))
        ]
        half_points = int(scan_points / 2)
        z_offset_start = round(new_offset - half_points * z_step, data_decimal)
        z_offset_end = round(z_offset_start + z_step * scan_points, data_decimal)

        if z_offset_start < start_offset:
            z_offset_start = start_offset
            z_offset_end = round(z_offset_start + z_step * scan_points, data_decimal)
        elif z_offset_end > end_offset:
            z_offset_end = end_offset
            z_offset_start = round(z_offset_end - z_step * scan_points, data_decimal)

        z_offset_list = qarange(z_offset_start, z_offset_end, z_step)
        return z_offset_list

    def _update_file_dirs(self, file_path: str, label: str):
        """Update self.file object dirs value."""
        if not label:
            self.file.dirs = file_path
        elif isinstance(self.file, LocalFile):
            self.file.dirs = f"{file_path}{label}\\"
        elif isinstance(self.file, S3File):
            self.file.dirs = f"{file_path}{label}/"

    async def _child_exp_run(
        self,
        experiment: TopExperiment,
        xy_delay: float,
        z_offset_list: Union[List[float], np.ndarray],
        exp_index: int,
        length: int,
    ):
        """Run child experiment."""
        z_amp = self.experiment_options.z_amp
        init_z_offset_list = self.experiment_options.z_offset_list

        cal_response_mode = self.run_options.cal_response_mode
        mark_info = self.run_options.mark_info
        p0_history = self.run_options.p0_history
        init_p1_value_arr = self.run_options.init_p1_value_arr
        sub_key = self.analysis_options.sub_key
        provide_field = self.analysis_options.y_field

        result_name = self.analysis_options.result_name
        child_rdz_amp = self.run_options.child_rdz_amp
        child_ac_bias = self.run_options.child_ac_bias

        error_count = 0
        while error_count < 3:
            new_dist_t1_exp = deepcopy(experiment)
            description = f"{mark_info}_xy_delay={xy_delay}"
            new_dist_t1_exp.set_parent_file(self, description, exp_index, length)
            new_dist_t1_exp.set_experiment_options(
                z_amp=z_amp,
                z_offset_list=z_offset_list,
                xy_delay=xy_delay,
            )
            new_dist_t1_exp.set_analysis_options(
                data_key=[sub_key],
                p0_history=p0_history,
            )
            new_dist_t1_exp.set_run_options(
                rdz_amp=child_rdz_amp,
                ac_bias=child_ac_bias,
            )
            self._check_simulator_data(new_dist_t1_exp, exp_index)

            # new_dist_t1_exp.run()
            # new_dist_t1_exp.clear_params()
            await new_dist_t1_exp.run_experiment()

            if child_rdz_amp is None and new_dist_t1_exp.run_options.rdz_amp:
                child_rdz_amp = new_dist_t1_exp.run_options.rdz_amp
                self.run_options.child_rdz_amp = child_rdz_amp
                pyqlog.info(
                    f"Note {result_name} child experiment readout amp: {child_rdz_amp}"
                )
            if not child_ac_bias and new_dist_t1_exp.run_options.ac_bias:
                child_ac_bias = new_dist_t1_exp.run_options.ac_bias
                self.run_options.child_ac_bias = child_ac_bias
                pyqlog.info(
                    f"Note {result_name} child experiment update ac: {child_ac_bias}"
                )

            model_name = new_dist_t1_exp.analysis_options.fit_model_name
            offset = new_dist_t1_exp.analysis.results.t_offset.value
            try:
                fit_data = new_dist_t1_exp.analysis.analysis_datas[sub_key].fit_data
                if fit_data is not None:
                    p0_history = dict(zip(fit_data.popt_keys, fit_data.popt))
            except Exception as err:
                pyqlog.warning(f"Calculate p0_history error: {err}")

            pyqlog.debug(f"Sub Experiment Analysis fit_model: {model_name}")
            pyqlog.info(f"Run {mark_info} xy_delay={xy_delay} ns, fit offset: {offset}")

            if z_offset_list[0] <= offset <= z_offset_list[-1]:
                if z_amp == 0:
                    normal_offset = offset
                else:
                    normal_offset = offset / z_amp

                if not cal_response_mode:
                    run_mode = new_dist_t1_exp.experiment_options.run_mode
                    if run_mode == "normal":
                        cal_response_mode = "add"
                    elif run_mode == "new_case":
                        cal_response_mode = "reduce"
                    self.set_run_options(cal_response_mode=cal_response_mode)

                if cal_response_mode == "add":
                    response = 1 + normal_offset
                elif cal_response_mode == "reduce":
                    response = 1 - normal_offset
                else:
                    # now just support "add" or "reduce"
                    response = 1 + normal_offset

                new_dist_t1_exp.analysis.provide_for_parent.update(
                    {provide_field: response}
                )

                self.run_options.p0_history = p0_history
                self.run_options.xy_delay_list.append(xy_delay)
                self.run_options.offset_list.append(offset)
                self.run_options.normal_offset_list.append(normal_offset)
                self.run_options.response_list.append(response)

                experiment_data = new_dist_t1_exp.analysis.experiment_data
                p_arr = experiment_data.y_data.get(sub_key)
                x_arr = experiment_data.x_data
                p_series = pd.Series(data=init_p1_value_arr, index=init_z_offset_list)
                # p_series[z_offset_list] = p_arr
                p_series[x_arr] = p_arr
                adjust_p_arr = np.array(p_series.values.tolist())

                exp_data = ExperimentData(
                    x_data=init_z_offset_list,
                    y_data={sub_key: adjust_p_arr},
                    experiment_id=new_dist_t1_exp.id,
                    metadata=new_dist_t1_exp._metadata(),
                )

                new_dist_t1_exp.analysis._experiment_data = exp_data
                # self._experiments.append(new_dist_t1_exp)
                if self.experiment_options.minimize_mode is True:
                    self._minimize_experiments.append(
                        new_dist_t1_exp.analysis.analysis_program()
                    )
                else:
                    self._experiments.append(new_dist_t1_exp)
                # Execute child experiment success, no need re execute.
                break
            else:
                pyqlog.warning(
                    f"Error_count {error_count}, {description} fit offset {offset}, "
                    f"out [{z_offset_list[0]},{z_offset_list[-1]}] range!"
                )
                z_offset_list = init_z_offset_list
                error_count += 1

    def _save_analysis_data(self):
        """Save analysis some options data."""
        mark_info = self.run_options.mark_info

        fir_width = self.analysis.options.fir_width
        h_new_arr = self.analysis.options.h_new_arr
        h_all_arr = self.analysis.options.h_all_arr
        xy_delay_list = self.run_options.xy_delay_list
        offset_resp_arr = self.analysis.options.offset_resp_arr
        af_noise_resp_arr = self.analysis.options.af_noise_resp_arr

        pyqlog.info(f"{mark_info} fir_width: {fir_width} ns")

        self.file.save_data(
            np.asarray(h_new_arr),
            name=f"{mark_info}_h_new",
            fmt="%.18f",
        )
        self.file.save_data(
            np.asarray(h_all_arr),
            name=f"{mark_info}_h_composite",
            fmt="%.18f",
        )

        self.file.save_data(
            np.asarray(xy_delay_list),
            np.asarray(offset_resp_arr),
            name=f"{mark_info}_delay_response_offset",
        )

        if len(xy_delay_list) == len(af_noise_resp_arr):
            self.file.save_data(
                np.asarray(xy_delay_list),
                np.asarray(af_noise_resp_arr),
                name=f"{mark_info}_delay_response_after_noise",
            )

    async def _run_once(self, experiment: TopExperiment):
        """Single time run logic. Loop xy_delay_list."""
        init_z_offset_list = self.experiment_options.z_offset_list
        update_z_offset_range = self.experiment_options.update_z_offset_range

        mark_info = self.run_options.mark_info
        set_xy_delay_list = self.run_options.set_xy_delay_list

        # initial some parameters
        self._experiments = []
        self._minimize_experiments = []
        self._analysis = None
        self.set_run_options(
            p0_history=None,
            xy_delay_list=[],
            offset_list=[],
            normal_offset_list=[],
            response_list=[],
        )

        length = len(set_xy_delay_list)
        z_offset_list = init_z_offset_list
        for exp_index, xy_delay in enumerate(set_xy_delay_list):
            await self._child_exp_run(
                experiment, xy_delay, z_offset_list, exp_index, length
            )

            # adjust z_offset_list
            if update_z_offset_range is True:
                offset_list = self.run_options.offset_list
                if len(offset_list) > 0:
                    offset = offset_list[-1]
                    z_offset_list = self._adjust_z_offset_list(offset, xy_delay)

        self.file.save_data(
            np.array(self.run_options.xy_delay_list),
            np.array(self.run_options.offset_list),
            np.array(self.run_options.normal_offset_list),
            np.array(self.run_options.response_list),
            name=f"{mark_info}_run_options",
        )
        self.file.save_data(
            np.array(self.run_options.xy_delay_list),
            np.array(self.run_options.response_list),
            name=f"{mark_info}_delay_response",
        )

        self.set_analysis_options(
            cal_response_mode=self.run_options.cal_response_mode,
        )
        self._run_analysis(
            x_data=np.array(self.run_options.xy_delay_list),
            analysis_class=OptimizeFIRAnalysis,
        )
        self._save_analysis_data()

    def _calculate_mean(self, delay_arr_list, resp_arr_list):
        """Average over, calculate mean delay and offset response array."""
        set_xy_delay_list = self.run_options.set_xy_delay_list
        set_length = len(set_xy_delay_list)

        jump_point_flag = False
        max_delay_list = []
        for delay_arr in delay_arr_list:
            max_delay_list.append(delay_arr[-1])
            if len(delay_arr) != set_length:
                jump_point_flag = True

        if jump_point_flag is True:
            max_delay = min(max_delay_list)
            if max_delay in set_xy_delay_list:
                max_idx = set_xy_delay_list.index(max_delay)
                mean_delay_arr = np.array(set_xy_delay_list[: max_idx + 1])
            else:
                # No case, there is no medicine to save.
                mean_delay_arr = np.array(set_xy_delay_list)

            new_resp_arr_list = []
            for delay_arr, resp_arr in zip(delay_arr_list, resp_arr_list):
                ipd_func = interp1d(delay_arr, resp_arr, kind="cubic")
                new_resp_arr = ipd_func(mean_delay_arr)
                new_resp_arr_list.append(new_resp_arr)
            mean_resp_arr = np.mean(np.vstack(new_resp_arr_list), axis=0)
        else:
            mean_delay_arr = np.array(set_xy_delay_list)
            mean_resp_arr = np.mean(np.vstack(resp_arr_list), axis=0)

        return mean_delay_arr, mean_resp_arr

    def _direct_run_analysis(self):
        """Direct run analysis"""
        y_field = self.analysis_options.y_field
        mean_delay_arr = self.run_options.mean_delay_arr
        mean_resp_arr = self.run_options.mean_resp_arr
        normal_resp_arr = self.run_options.normal_resp_arr

        metadata = self._metadata()

        exp_data = ExperimentData(
            x_data=mean_delay_arr,
            y_data={y_field: mean_resp_arr},
            experiment_id=self.id,
            metadata=metadata,
        )

        self._analysis = OptimizeFIRAnalysis(exp_data)

        # update options.
        self.analysis.set_options(normal_resp_arr=normal_resp_arr)
        self.analysis.set_options(**self._analysis_options)

        # run analysis.
        self.analysis.run_analysis()
        self._save_curve_analysis_plot()

        self.analysis.update_result()

        # if self.experiment_options.show_result:
        #     pyqlog.log("RESULT", format_results(self.analysis))

        self._save_analysis_data()

    def _judge_analysis_quality(self) -> Tuple:
        """Judge analysis quality is perfect or not."""
        target_fir_width = self.analysis_options.target_fir_width

        fir_width = None
        quality_flag = False
        if isinstance(self.analysis, OptimizeFIRAnalysis):
            quality = self.analysis.quality
            fir_width = self.analysis.options.fir_width
            if quality.descriptor in [QualityDescribe.perfect, QualityDescribe.normal]:
                pyqlog.info(
                    f"Iteration end, analysis quality: {quality}, "
                    f"target_fir_width: {target_fir_width}, "
                    f"fir_width: {fir_width}"
                )
                quality_flag = True

        return fir_width, quality_flag

    async def _sync_composite_run(self):
        """Optimize FIR distortion Composite Run Logic."""
        # super().run()

        iteration_times = self.experiment_options.iteration_times
        average_times = self.experiment_options.average_times
        xy_delay_start = self.experiment_options.xy_delay_start
        xy_delay_max = self.experiment_options.xy_delay_max
        mean_smooth = self.experiment_options.mean_smooth
        first_flag = self.experiment_options.first_flag
        add_width = self.experiment_options.add_width

        result_name = self.analysis_options.result_name
        h_old_list = self.analysis_options.h_old_list

        fir_width = None
        origin_file_dirs = self.file.dirs
        for i in range(iteration_times):
            pyqlog.info(f"{self._label} iteration_time: {i}")
            dist_t1_exp = deepcopy(self.child_experiment)

            mark_prefix = f"{result_name}_iter{i}"
            self._update_file_dirs(origin_file_dirs, "")
            for j, h_old in enumerate(h_old_list):
                self.file.save_data(
                    np.asarray(h_old),
                    name=f"{mark_prefix}_h_old_{j}",
                    fmt="%.18f",
                )
            for compensate in dist_t1_exp.compensates.values():
                if compensate.name == result_name:
                    compensate.z_distortion_sos.update(
                        {"Low_temperature_FIR_tf_filter": h_old_list}
                    )

            # if first_flag is True, scan long time delay response,
            # check iteration effect base on h_old_list.
            if first_flag is True:
                xy_delay_end = xy_delay_max
                xy_delay_list = self._adjust_xy_delay_list(
                    xy_delay_start, xy_delay_end, mode="first"
                )
                pyqlog.debug(f"first xy_delay_list: {xy_delay_list}")

                mark_info = f"{mark_prefix}_first"
                self._update_file_dirs(origin_file_dirs, mark_info)
                self.set_run_options(
                    mark_info=mark_info, set_xy_delay_list=xy_delay_list
                )
                await self._run_once(dist_t1_exp)

                fir_width, quality_flag = self._judge_analysis_quality()
                if quality_flag is True:
                    break

            # if fir_width, set xy_delay_end is fir_width add add_width,
            # fine scan delay response average_times, calculate mean response.
            if fir_width is not None:
                new_xy_delay_end = fir_width + add_width
            else:
                new_xy_delay_end = xy_delay_max
            pyqlog.info(
                f"fir_width: {fir_width} ns, add_width: {add_width} ns, "
                f"xy_delay start: {xy_delay_start}, end: {new_xy_delay_end}"
            )

            new_xy_delay_list = self._adjust_xy_delay_list(
                xy_delay_start, new_xy_delay_end, mode="avg"
            )
            pyqlog.debug(f"average xy_delay_list: {new_xy_delay_list}")
            self.set_run_options(set_xy_delay_list=new_xy_delay_list)

            delay_arr_list = []
            offset_resp_arr_list = []
            for k in range(average_times):
                mark_info = f"{mark_prefix}_avg{k}"
                self._update_file_dirs(origin_file_dirs, mark_info)
                self.set_run_options(mark_info=mark_info)
                await self._run_once(dist_t1_exp)

                xy_delay_arr = np.array(self.run_options.xy_delay_list)
                offset_resp_arr = self.analysis.options.offset_resp_arr
                delay_arr_list.append(xy_delay_arr)
                offset_resp_arr_list.append(offset_resp_arr)

            mark_info = f"{mark_prefix}_mean"
            self._update_file_dirs(origin_file_dirs, mark_info)
            mean_delay_arr, mean_resp_arr = self._calculate_mean(
                delay_arr_list, offset_resp_arr_list
            )

            if mean_smooth is True:
                mean_smooth_resp_arr = smooth(mean_resp_arr)
                self.set_run_options(
                    mark_info=mark_info,
                    mean_delay_arr=mean_delay_arr,
                    mean_resp_arr=mean_smooth_resp_arr,
                    normal_resp_arr=mean_resp_arr,
                )
                self.file.save_data(
                    mean_delay_arr,
                    mean_resp_arr,
                    mean_smooth_resp_arr,
                    name=f"{mark_info}_delay_response",
                )
            else:
                self.set_run_options(
                    mark_info=mark_info,
                    mean_delay_arr=mean_delay_arr,
                    mean_resp_arr=mean_resp_arr,
                    normal_resp_arr=np.array([]),
                )
                self.file.save_data(
                    mean_delay_arr,
                    mean_resp_arr,
                    name=f"{mark_info}_delay_response",
                )

            self._direct_run_analysis()

            fir_width, quality_flag = self._judge_analysis_quality()
            if quality_flag is True:
                break

            h_all_arr = self.analysis.options.h_all_arr
            h_old_list = [h_all_arr.tolist()]
            self.set_analysis_options(h_old_list=h_old_list)


class CouplerOptimizeFIR(OptimizeFIR):
    """CouplerOptimizeFIR class."""

    _sub_experiment_class = CouplerDistortionT1


class CouplerZZOptimizeFIR(OptimizeFIR):
    """CouplerDistortionZZ Distortion class."""

    _sub_experiment_class = CouplerDistortionZZ

    def _check_options(self):
        """Check options."""
        super()._check_options()
        sub_key = "P1"
        self.set_analysis_options(sub_key=sub_key)
