# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.smooth.rst:2
msgid "pyQCat.analysis.algorithms.smooth"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:1
msgid "smooth the data using a window with requested size."
msgstr "使用具有请求大小的窗口平滑数据"

#: of pyQCat.analysis.algorithms.smooth.smooth:3
msgid ""
"This method is based on the convolution of a scaled window with the "
"signal. The signal is prepared by introducing reflected copies of the "
"signal (with the window size) in both ends so that transient parts are "
"minimized in the begining and end part of the output signal."
msgstr ""
"该方法基于缩放窗口与信号的卷积。 通过在两端引入信号的反射副本（具有窗口大小）"
"来准备信号，以便在输出信号的开始和结束部分最小化瞬态部分。"

#: of pyQCat.analysis.algorithms.smooth.smooth:8
msgid "For the process of convolution smoothing, please refer to todo"
msgstr "卷积平滑的过程感兴趣可参考todo"

#: of pyQCat.analysis.algorithms.smooth.smooth
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:11
msgid "the input signal"
msgstr "输入信号"

#: of pyQCat.analysis.algorithms.smooth.smooth:13
msgid "the dimension of the smoothing window; should be an odd integer"
msgstr "平滑窗口大小，应该为奇数"

#: of pyQCat.analysis.algorithms.smooth.smooth:15
msgid ""
"the type of window from 'flat', 'hanning', 'hamming', 'bartlett', "
"'blackman' flat window will produce a moving average smoothing."
msgstr ""
"窗口类型，'flat', 'hanning', 'hamming', 'bartlett', 'blackman' 可选"

#: of pyQCat.analysis.algorithms.smooth.smooth
msgid "Raises"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:18
msgid "The window length must be an odd number"
msgstr "窗口长度比特为奇数"

#: of pyQCat.analysis.algorithms.smooth.smooth:19
msgid "This function only supports one-dimensional signals"
msgstr "改函数仅支持一维信号"

#: of pyQCat.analysis.algorithms.smooth.smooth:20
msgid "The signal length shall not be less than the window length"
msgstr "信号长度不应低于窗口长度"

#: of pyQCat.analysis.algorithms.smooth.smooth
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:22
msgid ":py:class:`~numpy.ndarray`"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.smooth.smooth:23
msgid "Smoothed signal"
msgstr "平滑处理后的信号"

#~ msgid "This method is based on the convolution of a scaled window with the"
#~ msgstr ""

#~ msgid "signal."
#~ msgstr ""

#~ msgid ""
#~ "The signal is prepared by introducing"
#~ " reflected copies of the signal (with"
#~ " the window size) in both ends "
#~ "so that transient parts are"
#~ msgstr ""

#~ msgid "minimized"
#~ msgstr ""

#~ msgid "in the begining and end part of the output signal."
#~ msgstr ""

#~ msgid "input:"
#~ msgstr ""

#~ msgid ""
#~ "x: the input signal window_length: the"
#~ " dimension of the smoothing window; "
#~ "should be an"
#~ msgstr ""

#~ msgid "odd integer"
#~ msgstr ""

#~ msgid "window: the type of window from 'flat', 'hanning', 'hamming',"
#~ msgstr ""

#~ msgid "'bartlett', 'blackman'"
#~ msgstr ""

#~ msgid "flat window will produce a moving average smoothing."
#~ msgstr ""

#~ msgid "output:"
#~ msgstr ""

#~ msgid "the smoothed signal"
#~ msgstr ""

#~ msgid "example:"
#~ msgstr ""

#~ msgid "t=linspace(-2,2,0.1) x=sin(t)+randn(len(t))*0.1 y=smooth(x)"
#~ msgstr ""

#~ msgid "see also:"
#~ msgstr ""

#~ msgid ""
#~ "numpy.hanning, numpy.hamming, numpy.bartlett, "
#~ "numpy.blackman, numpy.convolve scipy.signal.lfilter"
#~ msgstr ""

