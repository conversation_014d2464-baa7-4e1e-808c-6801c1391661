# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.find_coincident_point.rst:2
msgid "pyQCat.analysis.algorithms.find\\_coincident\\_point"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point:1
msgid "Find coincidence points."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point:3
msgid ""
"This method serves with APE experiments to find the peak coincidence "
"point of three APEOnce experiment."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point:6
msgid "Peak point structure `pyQCat.structures.Point`."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point:9
msgid "Number of peak-finding windows."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point:12
msgid "Search to get the nearest N peak points."
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_coincident_point
msgid "Return type"
msgstr ""

#~ msgid "Obtain coincidence points of APE experiment"
#~ msgstr ""

#~ msgid "A list consisting of `pyQCat.Point` objects."
#~ msgstr ""

#~ msgid "Number of coincidence points."
#~ msgstr ""

#~ msgid ""
#~ "The coincident point list, element is"
#~ " the                    instance of class "
#~ "`pyQCat.Point`."
#~ msgstr ""

#~ msgid "The coincident point list, element is the"
#~ msgstr ""

#~ msgid "instance of class `pyQCat.Point`."
#~ msgstr ""

