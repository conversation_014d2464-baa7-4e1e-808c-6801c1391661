# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/05/24
# __author:       HanQing Shi

import numpy as np
import matplotlib.pyplot as plt


def iq_scatter_drawer(i_datas, q_datas, dcms, loop, save_path, plot_idx: bool = False):
    assert len(i_datas) == len(q_datas) == len(dcms)
    i_datas = np.asarray(i_datas)
    q_datas = np.asarray(q_datas)

    i_lim = (np.min(i_datas), np.max(i_datas))
    q_lim = (np.min(q_datas), np.max(q_datas))
    i_range = i_lim[1] - i_lim[0]
    q_range = q_lim[1] - q_lim[0]
    margin = 0.1
    i_limit = (i_lim[0] - i_range * margin, i_lim[1] + i_range * margin)
    q_limit = (q_lim[0] - q_range * margin, q_lim[1] + q_range * margin)

    number = len(dcms)

    if number == 1:
        fig, axs = plt.subplots(figsize=(4, 4))
        axs = [axs]
    elif number == 2:
        fig, axs = plt.subplots(ncols=2, figsize=(8, 4), layout="constrained")
    else:
        col = ((number - 1) // 3) + 1
        fig, axs = plt.subplots(col, 3, figsize=(12, col * 4), layout="constrained")

    axs = np.array(axs).flatten()
    for index, ax in enumerate(axs):
        ax.axis("equal")
        title = f"{dcms[index].name}-loop-{loop}{f'-idx-{index}' if plot_idx else ''}"
        ax.scatter(i_datas[index], q_datas[index])
        ax.set(xlim=i_limit, ylim=q_limit, xlabel="I", ylabel="Q", title=title)

        dcm = dcms[index]
        center_list = dcm.centers
        r_list = dcm.radius
        colors = ["r", "b", "green"]

        state = 0
        for center, radius in zip(center_list, r_list):
            circle = plt.Circle(
                center,
                radius=radius,
                linewidth=1,
                fill=False,
                color=colors[state],
                label=f"|{state}>",
            )
            ax.add_patch(circle)
            state += 1

        ax.legend()

    fig.savefig(save_path)
    plt.close()
