from pyQCat.experiments.top_experiment_v1 import TopExperimentV1
from pyQCat.structures import Options
from pyQCat.gate import GateBucket
from pyQCat.analysis.standard_curve_analysis import StandardCurveAnalysis


class GateExp(TopExperimentV1):

    @classmethod
    def _default_experiment_options(cls) -> Options:

        options = super()._default_experiment_options()

        options.set_validator("gates", list, limit_null=True)
        options.set_validator("cycle", int)

        options.gates = ["X"]
        options.cycle = 2

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.gate_bucket = GateBucket()
        return options

    def _check_options(self):
        super()._check_options()
        self.run_options.gate_bucket.bind_single_gates(self.qubit)

        if self.discriminator is not None:
            data_type = "I_Q"
        else:
            data_type = "amp_phase"

        self.set_experiment_options(data_type=data_type)
        self.set_run_options(x_data=range(self.experiment_options.cycle), analysis_class=StandardCurveAnalysis)
        self.set_analysis_options(raw_data_format="plot")

    @staticmethod
    def set_xy_pulses(builder):
        gate_bucket = builder.run_options.gate_bucket
        gates = builder.experiment_options.gates
        cycle = builder.experiment_options.cycle

        pulse_list = []
        for _ in range(cycle):
            pulse_list.append(gate_bucket.get_xy_pulse(builder.qubit, gates))

        builder.play_pulse("XY", builder.qubit, pulse_list)
