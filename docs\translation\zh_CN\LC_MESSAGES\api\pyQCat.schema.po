# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/api/pyQCat.schema.rst:2
msgid "pyQCat.schema package"
msgstr ""

#: ../../source/api/pyQCat.schema.rst:5
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.schema.rst:8
msgid "pyQCat.schema.qschema module"
msgstr ""

#: ../../source/api/pyQCat.schema.rst:16
msgid "Module contents"
msgstr ""

#: of pyQCat.schema:3
msgid "Schema (:mod:`pyQCat.schema`)"
msgstr ""

#: of pyQCat.schema:5
msgid "Schema modules."
msgstr ""

