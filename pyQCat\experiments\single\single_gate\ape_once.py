# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2020/11/23
# __author:       <PERSON>
# __corporation:  OriginQuantum

"""
APE experiment.

The APE experiment is used to calibrate the detune parameters of the single-gate XY line waveform.
"""

from typing import TYPE_CHECKING, List

if TYPE_CHECKING:
    from ....concurrent.worker.experiment_builder import Experiment<PERSON>rotocol<PERSON><PERSON>er

import numpy as np

from ....analysis.library import APEAnalysis
from ....analysis.specification import ParameterRepr
from ....errors import ExperimentOptionsError
from ....log import pyqlog
from ....pulse.pulse_function import (
    f12_pi_pulse,
    half_f12_pi_pulse,
    half_pi_pulse,
    pi_pulse,
)
from ....qubit import Qubit
from ....structures import MetaData, Options
from ....tools import qarange
from ...coupler_experiment_v1 import CouplerBaseExperimentV1 as CouplerBaseExperiment
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class APE(TopExperiment):
    """APE experiment."""

    one_sweep = ["detune", "alpha", "phase"]
    multi_sweep = ["alpha_detune", "n_phase"]

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            sweep_name (str): Experiment purpose, "detune" or "phase".
            sweep_list (List, np.ndarray): Scan detune or phase list.
            phi_num (int): Multiples phase of π or π/2 pulse.
            theta_type (str): Support `θ` type, normal "Xpi" or "Xpi/2".
                              Calibrate π gate or π/2 gate.
            N (int): Repeat times of pairing drag pulse.
        """
        options = super()._default_experiment_options()

        options.set_validator("sweep_name", ["detune", "phase"])
        options.set_validator("theta_type", ["Xpi", "Xpi/2"])
        options.set_validator("sweep_list", list, limit_null=True)
        options.set_validator("phi_num", (1, 10, 0))
        options.set_validator("N", int)

        options.sweep_name = "detune"
        options.sweep_list = qarange(-25, 10, 1)
        options.phi_num = 1
        options.theta_type = "Xpi"
        options.N = 9
        options.f12_opt = False

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("filter", dict)
        options.set_validator("fine", bool)
        options.set_validator("prominence_divisor", float)

        options.sweep_name = "detune"
        options.fine = False
        options.prominence_divisor = 4.0

        options.x_label = None
        options.data_key = None
        options.filter = {"window_length": 5, "polyorder": 3}
        options.result_parameters = None
        options.quality_bounds = [0.95, 0.9, 0.8]

        return options

    # Shq 2024/04/29
    # for async mode.
    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.injection_func = ["_get_xy_pulse"]
        options.support_context = [StandardContext.QC, StandardContext.CPC]
        return options

    # Shq 2024/04/29
    # for async mode.
    @staticmethod
    def get_detune_xy_pulse(
        qubit: Qubit,
        detune_list: List,
        n: int,
        theta: float = np.pi,
        phi_num: int = 1,
        f12_opt: bool = False,
    ):
        xy_pulses = []
        for _detune in detune_list:
            ape_pulse = (
                _get_couple_of_ape_detune_pulse(
                    qubit, theta, phi_num, float(_detune), f12_opt
                )
                * n
            )

            if f12_opt:
                ape_pulse = pi_pulse(qubit)() + ape_pulse + pi_pulse(qubit)()

            xy_pulses.append(ape_pulse)
        return xy_pulses

    @staticmethod
    def get_phase_xy_pulse(
        qubit: Qubit,
        phase_list: List,
        n: int,
        theta: float = np.pi,
        phi_num: int = 1,
        f12_opt: bool = False,
    ):
        xy_pulses = []
        for phase in phase_list:
            ape_pulse_front = _get_couple_of_ape_detune_pulse(
                qubit, theta, phi_num, f12_opt
            ) * (n - 1)
            ape_pulse_tail = _get_last_of_ape_phase_pulse(qubit, theta, phase, f12_opt)
            ape_pulse = ape_pulse_front + ape_pulse_tail

            if f12_opt:
                ape_pulse = pi_pulse(qubit)() + ape_pulse + pi_pulse(qubit)()

            xy_pulses.append(ape_pulse)
        return xy_pulses

    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        xy_pulse_list = APE._get_xy_pulse(builder)
        builder.play_pulse("XY", builder.qubit, xy_pulse_list)

    @staticmethod
    def _get_xy_pulse(builder: "ExperimentProtocolBuilder", qubit: Qubit = None):
        if qubit is None:
            qubit = builder.qubit

        sweep_name = builder.experiment_options.sweep_name
        sweep_list = builder.experiment_options.sweep_list
        phi_num = builder.experiment_options.phi_num
        theta_type = builder.experiment_options.theta_type
        n = builder.experiment_options.N
        f12_opt = builder.experiment_options.f12_opt

        theta = APE.check_theta(builder.label, theta_type)
        if sweep_name == "detune":
            xy_pulse_list = APE.get_detune_xy_pulse(
                qubit, sweep_list, int(n), theta, phi_num, f12_opt
            )
        else:
            xy_pulse_list = APE.get_phase_xy_pulse(
                qubit, sweep_list, int(n), theta, phi_num, f12_opt
            )

        return xy_pulse_list

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "sweep_name": self.experiment_options.sweep_name,
            "theta_type": self.experiment_options.theta_type,
            "N": self.experiment_options.N,
        }
        return metadata

    def _check_options(self):
        """
        `sweep_name` only supports two types: detune and phase;
        `theta_type` only supports two types: xpi and xpi/2;
        If the criterion is used, process P0 data, otherwise the amplitude and phase data are processed;
        The output result name is determined by sweep name.
        """
        super()._check_options()
        sweep_name = self.experiment_options.sweep_name

        # check result parameters
        if sweep_name == "detune":
            result_parameters = [
                ParameterRepr(name="points_0", repr="points_0"),
                ParameterRepr(name="fit_points_0", repr="fit_points_0"),
            ]
            x_label = "Detune (MHz)"
        else:
            result_parameters = [ParameterRepr(name="phase_point", repr="phase_point")]
            x_label = "Phase (°)"

        # check data keys
        if self.discriminator is not None:
            if self.is_coupler_exp:
                data_key = ["P1"]
            else:
                data_key = ["P0"]
            data_type = "I_Q"
        else:
            data_key = ["Amp", "Phase"]
            data_type = "amp_phase"

        pyqlog.log(
            "EXP",
            f"theta_type: {self.experiment_options.theta_type}, "
            f"N: {self.experiment_options.N}",
        )

        # update experiment options
        self.set_experiment_options(data_type=data_type)

        # update analysis options
        self.set_analysis_options(
            result_parameters=result_parameters,
            data_key=data_key,
            x_label=x_label,
            sweep_name=sweep_name,
        )

        self.set_run_options(
            x_data=self.experiment_options.sweep_list, analysis_class=APEAnalysis
        )

    # Shq 2024/04/29
    # for async mode.
    @staticmethod
    def check_theta(label: str, theta_type: str):
        if theta_type == "Xpi":
            theta = np.pi
        elif theta_type == "Xpi/2":
            theta = np.pi / 2
        else:
            raise ExperimentOptionsError(
                label,
                msg="theta type only support Xpi and Xpi/2",
                key="theta_type",
                value=theta_type,
            )
        return theta


def _get_couple_of_ape_detune_pulse(
    qubit: Qubit, theta: float, phi_num: int, detune: float = None, f12: bool = False
):
    if theta == np.pi:
        ape_pulse_zero_phase = f12_pi_pulse(qubit) if f12 else pi_pulse(qubit)
        ape_pulse_zero_phase.phase = 0
        ape_pulse_pi_phase = f12_pi_pulse(qubit) if f12 else pi_pulse(qubit)
        ape_pulse_pi_phase.phase = np.pi * phi_num
    else:
        ape_pulse_zero_phase = half_f12_pi_pulse(qubit) if f12 else half_pi_pulse(qubit)
        ape_pulse_zero_phase.phase = 0
        ape_pulse_pi_phase = half_f12_pi_pulse(qubit) if f12 else half_pi_pulse(qubit)
        ape_pulse_pi_phase.phase = np.pi * phi_num

    if detune is not None:
        ape_pulse_zero_phase.detune = detune * 1e-3
        ape_pulse_pi_phase.detune = detune * 1e-3

    ape_pulse_group = ape_pulse_zero_phase() + ape_pulse_pi_phase()

    return ape_pulse_group


def _get_last_of_ape_phase_pulse(
    qubit: Qubit, theta: float, phase: float, f12: bool = False
):
    if theta == np.pi:
        ape_zero_pulse = f12_pi_pulse(qubit) if f12 else pi_pulse(qubit)
        ape_last_pulse = f12_pi_pulse(qubit) if f12 else pi_pulse(qubit)
    else:
        ape_zero_pulse = half_f12_pi_pulse(qubit) if f12 else half_pi_pulse(qubit)
        ape_last_pulse = half_f12_pi_pulse(qubit) if f12 else half_pi_pulse(qubit)

    ape_zero_pulse.phase = 0
    ape_last_pulse.phase = phase

    ape_pulse_group = ape_zero_pulse() + ape_last_pulse()

    return ape_pulse_group


class CouplerAPE(CouplerBaseExperiment, APE):
    """Coupler Amp Optimize Experiment"""

    @staticmethod
    def set_xy_pulses(self):
        xy_pulse_list = APE._get_xy_pulse(self, self.driveQ)
        self.compose_xy_pulses(xy_pulse_list)
