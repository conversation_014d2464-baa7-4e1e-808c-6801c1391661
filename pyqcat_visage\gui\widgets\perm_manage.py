# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/06/01
# __author:       <PERSON><PERSON>
from typing import TYPE_CHECKING

from PySide6.QtCore import Qt
from PySide6.QtGui import QColor
from PySide6.QtWidgets import QTreeWidgetItem

from pyqcat_visage.gui.widgets.manager.table_model_groups import QTableModelGroup
from pyqcat_visage.gui.widgets.manager.table_model_users import QTableModelUser
from pyqcat_visage.gui.widgets.manager.table_view_groups import QTableViewGroupWidget
from pyqcat_visage.gui.widgets.manager.table_view_users import QTableViewPermUserWidget
from pyqcat_visage.gui.widgets.title_window import TitleWindow
from pyqcat_visage.gui.permission_manage_ui import Ui_MainWindow

if TYPE_CHECKING:
    from pyqcat_visage.gui.main_window import VisageGUI


class PermissionManageWindow(TitleWindow):
    def __init__(self, gui: "VisageGUI" = None, parent=None):
        super().__init__(parent)
        self._ui = Ui_MainWindow()
        self._ui.setupUi(self)
        self.gui = gui

        self.group_perm = []
        self.group_list = []
        self.users = {}
        self.item = {}
        self.add = []
        self.delete = []
        self.cur_group = None
        self._create_platform_dialog = None

        self.perm_type = None
        self.choose_type = None
        self.platform_list = None
        self.user_list = None
        self.perms_list = None

        self.p_group_table_view = None
        self.p_user_table_view = None
        self.n_group_table_view = None
        self.n_user_table_view = None
        self.p_group_table_model = None
        self.p_user_table_model = None
        self.n_group_table_model = None
        self.n_user_table_model = None

        self.user = None
        self.cur_group = None
        self.is_platform = None
        self.check_state = {}

        self.check_flag = True
        self.color_flag = False
        self._setup_model()
        self._ui.p_treeWidget.itemChanged.connect(self.on_item_changed)
        self._ui.n_treeWidget.itemChanged.connect(self.on_item_changed)

    @property
    def ui(self):
        return self._ui

    def _setup_model(self):
        self._ui.tabWidget.setCurrentIndex(0)
        self._ui.p_radio_group.setChecked(True)

        self.load_model(index=0)

    def load_platform_combox(self):
        self.ui.p_plat_comb.clear()
        if self.platform_list is None:
            ret_data = self.gui.backend.db.query_platform()
            self.platform_list = []
            if ret_data.get("code") == 200:
                self.platform_list = [{
                    "ident": x.get("ident"),
                    "is_platform": x.get("is_platform"),
                    "name": x.get("name")} for x in ret_data["data"]
                ]
        self.ui.p_plat_comb.addItems([x.get("name") for x in self.platform_list])
        self.ui.p_plat_comb.setCurrentIndex(0)

    def tab_changed(self, index):
        self.perms_list = None
        # platform
        if index == 0:
            self._ui.p_radio_group.setChecked(True)
        # normal
        else:
            self._ui.n_radio_group.setChecked(True)
        self.load_model(index=index)

    def update_display(self, text):
        # platform
        if self.perm_type == "platform":
            self.p_user_table_view.update_display(text)
        # normal
        else:
            self.n_user_table_view.update_display(text)

    def load_permission_combox(self, platform_ident: str = ""):
        if self.perm_type == "platform":
            comb = self.ui.p_perm_comb
        else:
            comb = self.ui.n_perm_comb
        comb.clear()
        if self.perms_list is None:
            ret_data = self.gui.backend.db.query_perms_list(group="group", platform_ident=platform_ident)
            self.perms_list = []
            if ret_data.get("code") == 200:
                self.perms_list = [{"id": x.get("id"), "ident": x.get("ident"), "name": x.get("name")} for x in ret_data["data"]]
        comb.addItems([""] + [x.get("name") for x in self.perms_list])
        comb.setCurrentIndex(0)

    def hide_table(self):
        if self.p_group_table_view:
            self.group_list = []
            self.p_group_table_view.setVisible(False)
        if self.p_user_table_view:
            self.p_user_table_view.setVisible(False)
        if self.n_group_table_view:
            self.n_group_table_view.setVisible(False)
            self.group_list = []
        if self.n_user_table_view:
            self.n_user_table_view.setVisible(False)

    def table_model_show(self):
        self.hide_table()
        if self.perm_type == "platform":
            if self.choose_type == "group":
                self.ui.p_search.setVisible(False)
                if not self.p_group_table_view:
                    table_widget = self.ui.p_table_widget
                    self.p_group_table_view = QTableViewGroupWidget(table_widget)
                    vertical = self.ui.verticalLayout_3
                    vertical.addWidget(self.p_group_table_view)
                    self.p_group_table_view.choose_group_signal.connect(
                        self._change_group
                    )
                    self.p_group_table_model = QTableModelGroup(
                        self.gui, self, self.p_group_table_view
                    )
                    self.p_group_table_view.setModel(self.p_group_table_model)
                else:
                    self.p_group_table_view.setVisible(True)
                self.p_group_table_model.refresh_auto(check_count=False)
            else:
                self.ui.p_search.setVisible(True)
                if not self.p_user_table_view:
                    table_widget = self.ui.p_table_widget
                    self.p_user_table_view = QTableViewPermUserWidget(table_widget)
                    vertical = self.ui.verticalLayout_3
                    vertical.addWidget(self.p_user_table_view)
                    self.p_user_table_view.choose_user_signal.connect(
                        self._change_user
                    )
                    self.p_user_table_model = QTableModelUser(
                        self.gui, self, self.p_user_table_view
                    )
                    self.p_user_table_view.setModel(self.p_user_table_model)
                else:
                    self.p_user_table_view.setVisible(True)
                self.p_user_table_model.set_columns(["username", "groups", "is_super", "is_admin", "is_platform"])
                if self.is_super:
                    self.p_user_table_view.set_menu_status(True)
                self.p_user_table_model.refresh_auto(check_count=False)
        else:
            if self.choose_type == "group":
                self.ui.n_search.setVisible(False)
                if not self.n_group_table_view:
                    table_widget = self.ui.n_table_widget
                    self.n_group_table_view = QTableViewGroupWidget(table_widget)
                    vertical = self.ui.verticalLayout_7
                    vertical.addWidget(self.n_group_table_view)
                    self.n_group_table_view.choose_group_signal.connect(
                        self._change_group
                    )
                    self.n_group_table_model = QTableModelGroup(
                        self.gui, self, self.n_group_table_view
                    )
                    self.n_group_table_view.setModel(self.n_group_table_model)
                else:
                    self.n_group_table_view.setVisible(True)
                self.n_group_table_model.refresh_auto(check_count=False)
            else:
                self.ui.n_search.setVisible(True)
                if not self.n_user_table_view:
                    table_widget = self.ui.n_table_widget
                    self.n_user_table_view = QTableViewPermUserWidget(table_widget)
                    vertical = self.ui.verticalLayout_7
                    vertical.addWidget(self.n_user_table_view)
                    self.n_user_table_view.choose_user_signal.connect(
                        self._change_user
                    )
                    self.n_user_table_model = QTableModelUser(
                        self.gui, self, self.n_user_table_view
                    )
                    self.n_user_table_view.setModel(self.n_user_table_model)
                else:
                    self.n_user_table_view.setVisible(True)
                self.n_user_table_model.set_columns(["username", "groups", "is_super", "is_admin", "is_platform"])
                self.n_user_table_model.refresh_auto(check_count=False)

    def load_model(self, index):
        # platform permission
        if index == 0:
            self.perm_type = "platform"
            self.load_platform_combox()
            # group
            if self._ui.p_radio_group.isChecked():
                self.choose_type = "group"
            # user
            else:
                self.choose_type = "user"
                # self.load_user_combox()
            self.load_permission_combox("platform")
        # normal permission
        else:
            self.perm_type = "normal"
            # group
            if self._ui.n_radio_group.isChecked():
                self.choose_type = "group"
            # user
            else:
                self.choose_type = "user"
                # self.load_user_combox()
            self.load_permission_combox()
        self.table_model_show()
        self.tree_widget_init()

    def reset_window_layout(self):
        pass

    def p_radio_group(self, checked):
        self.load_model(index=0)

    def p_radio_user(self, checked):
        self.load_model(index=0)

    def n_radio_group(self, checked):
        self.load_model(index=1)

    def n_radio_user(self, checked):
        self.load_model(index=1)

    def show(self):
        # self.load_default_data()
        super().show()

    def platform_changed(self, index):
        self.ui.p_plat_comb.setCurrentText("")
        if index == -1:
            return
        self.is_platform = self.platform_list[index].get("is_platform")

    def tree_widget_init(self):
        if self.perm_type == "platform":
            tree_view = self.ui.p_treeWidget
        else:
            tree_view = self.ui.n_treeWidget
        tree_view.clear()
        self.delete = []
        self.add = []
        self.check_state = {}
        self.check_flag = True
        self.color_flag = False
        return tree_view

    def query_perm(self):
        platform_ident = ""
        if self.perm_type == "platform":
            if self.choose_type == "group":
                table_model = self.p_group_table_model
            else:
                table_model = self.p_user_table_model
            platform_ident = self.get_platform_ident()
            if not platform_ident:
                return
            perm_comb = self.ui.p_perm_comb
        else:
            if self.choose_type == "group":
                table_model = self.n_group_table_model
            else:
                table_model = self.n_user_table_model
            perm_comb = self.ui.n_perm_comb
        perm_ident = ""
        text = perm_comb.currentText()
        if text:
            for c in self.perms_list:
                if text == c.get("name"):
                    perm_ident = c.get("ident")
                    break
        if self.choose_type == "group":
            ret_data = self.gui.backend.db.query_all_groups(
                perm_type=self.perm_type,
                platform_ident=platform_ident,
                perms_ident=perm_ident
            )
            if ret_data.get("code") == 200:
                self.group_list = ret_data.get("data")
            else:
                self.group_list = []
        else:
            # 是 super 或者平台管理员才查询所有组用户权限
            if self.is_super or (self.perm_type == "platform" and self.is_platform):
                group_name = ""
            else:
                group_name = self.group_name
            ret_data = self.gui.backend.query_perms_user_list(
                perm_type=self.perm_type,
                platform_ident=platform_ident,
                perms_ident=perm_ident,
                group_name=group_name,
            )
            if ret_data.get("code") == 200:
                perms_user_list = ret_data.get("data")
                self.users["default"] = perms_user_list
                table_model.group_name = "default"
        table_model.refresh_auto(check_count=False)
        self.tree_widget_init()

    def save_perm(self):
        platform_ident = self.get_platform_ident()
        if self.choose_type == "group":
            if self.cur_group == "super":
                return
            if self.add or self.delete:
                ret_data = {}
                if self.add:
                    ret_data = self.gui.backend.db.add_perms_group(self.add, group=self.cur_group, platform_ident=platform_ident)
                if self.delete:
                    ret_data = self.gui.backend.db.del_perms_group(self.delete, group=self.cur_group, platform_ident=platform_ident)
                self.handler_ret_data(ret_data, show_suc=True)
                self._change_group(self.cur_group)
        else:
            if self.add or self.delete:
                username = self.user.get("username")
                ret_data = {}
                if self.add:
                    ret_data = self.gui.backend.db.add_perms_user(self.add, username=username,
                                                                  platform_ident=platform_ident)
                if self.delete:
                    ret_data = self.gui.backend.db.del_perms_user(self.delete, username=username,
                                                                  platform_ident=platform_ident)
                self.handler_ret_data(ret_data, show_suc=True)
                self._change_user(self.user)

    def check_status(self):
        if (self.is_super
                or (self.perm_type == "platform" and
                    (self.is_platform or (self.is_admin and self.choose_type == "user"))) or
                (self.perm_type == "normal" and self.is_admin and self.choose_type == "user")):
            return True
        return False

    def set_color(self, item, color):
        self.color_flag = True
        item.setForeground(0, color)
        self.color_flag = False

    def on_item_changed(self, item, column):
        self.check_flag = False
        if not self.check_status() or self.color_flag:
            self.color_flag = True
            return
        if self.add or self.delete:
            self._ui.p_save_button.setEnabled(True)
            self._ui.n_save_button.setEnabled(True)
        text = item.text(0)
        pre_item = self.item.get(text, {})
        if pre_item:
            pre_status = pre_item.get("is_check")
            id_ = pre_item.get("id")
            ident = pre_item.get("ident")
            if self.check_state.get(ident, 0) == 1:
                if item.checkState(column) == Qt.CheckState.Checked:
                    if not pre_status:
                        self.set_color(item, QColor(252, 157, 154))
                        if id_ not in self.add:
                            self.add.append(id_)
                    else:
                        self.set_color(item, QColor(255, 255, 255))
                        if id_ in self.delete:
                            self.delete.remove(id_)
                elif item.checkState(column) == Qt.CheckState.Unchecked:
                    if pre_status:
                        self.set_color(item, QColor(252, 157, 154))
                        if id_ not in self.delete:
                            self.delete.append(id_)
                    else:
                        self.set_color(item, QColor(255, 255, 255))
                        if id_ in self.add:
                            self.add.remove(id_)

    def on_tree_clicked(self, item, column):
        if self.check_status() and self.check_flag:
            text = item.text(column)
            pre_item = self.item.get(text, {})
            if pre_item:
                ident = pre_item.get("ident")
                if self.check_state.get(ident, 0) == 1:
                    # 反选复选框
                    if item.checkState(0) == Qt.CheckState.Unchecked:
                        state = Qt.CheckState.Checked
                    else:
                        state = Qt.CheckState.Unchecked
                    item.setCheckState(0, state)
        self.check_flag = True

    def load_tree(self, group: str):
        tree_view = self.tree_widget_init()

        root = QTreeWidgetItem(tree_view)
        root.setText(0, "{}权限".format(group))
        root.setToolTip(0, "Assign or view permissions.")
        for name, value in self.item.items():
            is_check = value.get("is_check", False)
            ident = value.get("ident")
            item = QTreeWidgetItem(root)
            item.setText(0, name)
            item.setToolTip(
                0,
                "A check box indicates that the permission can be changed, "
                "gray indicates that the permission is not available, "
                "and light red indicates that the modified permission is not submitted."
            )
            if self.choose_type != "group" and ident not in self.group_perm:
                self.set_color(item, QColor(128, 128, 128))
            else:
                if self.check_status():
                    self.check_state[ident] = 1
                    item.setCheckState(0, Qt.CheckState.Checked if is_check else Qt.CheckState.Unchecked)
                else:
                    if not is_check:
                        self.set_color(item, QColor(128, 128, 128))
        tree_view.expandAll()
        self.check_flag = True

    def get_platform_ident(self):
        platform_ident = ""
        if self.perm_type == "platform":
            index = self.ui.p_plat_comb.currentIndex()
            if index == -1:
                return
            platform_ident = self.platform_list[index].get("ident")
        return platform_ident

    def change_user_platform_admin(self, user):
        username = user.get("username")
        is_admin = not user.get("is_platform")
        platform_ident = self.get_platform_ident()
        if platform_ident:
            ret_data = self.gui.backend.db.change_platform_leader(
                username=username,
                platform_ident=platform_ident,
                is_admin=is_admin
            )
            self.handler_ret_data(ret_data, show_suc=True)
            self.query_perm()

    def _change_group(self, group_name: str = None):
        group_name = group_name or self.cur_group
        self.cur_group = group_name
        platform_ident = self.get_platform_ident()

        group_data = self.gui.backend.db.query_perms_group(group=group_name, platform_ident=platform_ident)
        group_perm = group_data.get("data")
        c = [x["ident"] for x in group_perm]
        self.item = {x["name"]: {"id": x["id"], "ident": x["ident"], "is_check": True if x["ident"] in c else False} for
                     x in self.perms_list}
        self.load_tree(group_name)

    def _change_user(self, user):
        self.user = user
        username = user.get("username")
        group_name = user.get("groups")

        platform_ident = self.get_platform_ident()

        group_data = self.gui.backend.db.query_perms_group(group=group_name, platform_ident=platform_ident)
        group_perm = group_data.get("data")
        self.group_perm = [x.get("ident") for x in group_perm]

        ret_data = self.gui.backend.db.query_perms_user(
            username=username,
            platform_ident=platform_ident
        )
        user_perm = ret_data.get("data", [])
        c = [x["ident"] for x in user_perm]
        self.item = {x["name"]: {"id": x["id"], "ident": x["ident"], "is_check": True if x["ident"] in c else False} for
                     x in self.perms_list}
        self.load_tree(username)
