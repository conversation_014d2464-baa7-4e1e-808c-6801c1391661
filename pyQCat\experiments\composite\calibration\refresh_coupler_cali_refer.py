# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/04
# __author:       <PERSON> Fang

"""
Refresh coupler sweep_point voltage calibrate information.

"""

from copy import deepcopy
from typing import Tuple

import numpy as np

from ....analysis.library.cali_refer_analysis import CaliReferAnalysis
from ....errors import ExperimentFieldError
from ....log import pyqlog
from ....structures import Options
from ....types import ExperimentRunMode, QualityDescribe
from ...composite_experiment import CompositeExperiment
from ...single import CouplerRamseyByZZShift


class RefreshCouplerCaliRefer(CompositeExperiment):
    """Refresh Coupler.cali_refer information."""

    _sub_experiment_class = CouplerRamseyByZZShift

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("iteration", int)
        options.set_validator("z_amp", float)
        options.set_validator("z_step", float)
        options.set_validator("min_delta_freq", float)
        options.set_validator("max_delta_freq", float)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.iteration = 5
        options.z_amp = 0.15
        options.z_step = 0
        options.min_delta_freq = 5  # MHz
        options.max_delta_freq = 20  # MHz

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options."""
        options = super()._default_analysis_options()

        options.quality_list = []
        options.min_delta_freq = 5  # MHz
        options.max_delta_freq = 20  # MHz
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default run options."""
        options = super()._default_run_options()

        options.ac_bias = 0.0
        options.z_amp = 0.0
        options.z_step = 0.0
        options.current_z_step = 0.0
        options.init_osc_freq = 0
        options.analysis_class = CaliReferAnalysis
        return options

    def _check_options(self):
        """Check options."""
        super()._check_options()
        z_amp = self.experiment_options.z_amp
        z_step = self.experiment_options.z_step
        coupler_obj = self.child_experiment.coupler

        result_name = coupler_obj.name
        ac_bias_keys = list(self.child_experiment.ac_bias.keys())
        if result_name not in ac_bias_keys:
            raise ExperimentFieldError(
                self.label,
                f"{result_name} not in current environment.\n" f"{ac_bias_keys}",
            )
        ac_bias = self.child_experiment.ac_bias.get(result_name)[1]

        if ac_bias > 0 and z_amp > 0:
            new_z_amp = -z_amp
        elif ac_bias < 0 and z_amp < 0:
            new_z_amp = -z_amp
        else:
            new_z_amp = z_amp

        if z_step:
            if z_step * new_z_amp < 0:
                new_z_step = -z_step
            else:
                new_z_step = z_step
        else:
            new_z_step = round(new_z_amp * 0.1, 6)

        pyqlog.info(
            f"{result_name} ac_bias: {ac_bias}, z_amp: {new_z_amp}, z_step: {new_z_step}"
        )

        self.set_run_options(ac_bias=ac_bias, z_amp=new_z_amp, z_step=new_z_step, current_z_step=new_z_amp)
        self.set_analysis_options(
            result_name=result_name,
            min_delta_freq=self.experiment_options.min_delta_freq,
            max_delta_freq=self.experiment_options.max_delta_freq,
        )

    def _judge_diff_freq(
        self, cur_z_amp: float, cur_osc_freq: float
    ) -> Tuple[float, bool]:
        """Judge iterate or not."""
        min_delta_freq = self.experiment_options.min_delta_freq
        max_delta_freq = self.experiment_options.max_delta_freq
        z_step = self.run_options.z_step
        init_osc_freq = self.run_options.init_osc_freq

        delta_freq = cur_osc_freq - init_osc_freq
        flag = True
        next_z_amp = 0.0
        if delta_freq < min_delta_freq:
            next_z_amp = cur_z_amp + z_step
        elif min_delta_freq <= delta_freq <= max_delta_freq:
            flag = False
        else:
            current_z_step = self.run_options.current_z_step
            new_z_step = current_z_step / 2
            next_z_amp = cur_z_amp - new_z_step
            self.run_options.current_z_step = new_z_step
        return next_z_amp, flag

    async def _sync_composite_run(self):
        """Run logic."""
        # super().run()
        iteration = self.experiment_options.iteration
        result_name = self.analysis_options.result_name
        z_amp = self.run_options.z_amp
        analysis_class = self.run_options.analysis_class

        z_amp_list = []
        osc_freq_list = []
        quality_list = []

        idx = 0
        idx_z_amp = 0.0
        run_flag = True
        while idx < iteration + 1 and run_flag is True:
            description = f"z_amp={idx_z_amp}"
            rc_exp = deepcopy(self.child_experiment)
            rc_exp.set_parent_file(self, description, idx)
            rc_exp.set_experiment_options(z_amp=idx_z_amp)
            self._check_simulator_data(rc_exp, idx)
            # rc_exp.run()
            # rc_exp.clear_params()
            await rc_exp.run_experiment()

            osc_freq = rc_exp.analysis.results.freq.value
            if rc_exp.analysis.quality:
                quality_str = rc_exp.analysis.quality.descriptor
            else:
                quality_str = QualityDescribe.bad
            z_amp_list.append(
                idx_z_amp
                if idx == 0
                else idx_z_amp + self.child_experiment.coupler.idle_point
            )
            osc_freq_list.append(osc_freq)
            quality_list.append(quality_str)
            rc_exp.analysis.provide_for_parent.update({"osc_freq": osc_freq})
            self._experiments.append(rc_exp)

            if idx == 0:
                self.run_options.init_osc_freq = osc_freq
                idx_z_amp = z_amp
            else:
                idx_z_amp, run_flag = self._judge_diff_freq(idx_z_amp, osc_freq)
            idx += 1

        file_name = f"{result_name}_refer_note"
        self.file.save_data(
            np.array(z_amp_list),
            np.array(osc_freq_list),
            np.array(quality_list),
            name=file_name,
            fmt="%s",
        )

        self.set_analysis_options(quality_list=quality_list)
        self._run_analysis(z_amp_list, analysis_class)
