# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/08/18
# __author:       <PERSON><PERSON><PERSON>

import numpy as np

from ...structures import Options
from ...types import QualityDescribe
from ..curve_fit_analysis import CurveFitAnalysis
from ..fit.fit_models import erf_fit_func, solve_x_from_erf_fit
from ..specification import FitModel, ParameterRepr


class SaturationPowerAnalysis(CurveFitAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.fit_model = FitModel(
            fit_func=erf_fit_func, model_description=r"-A * erf((x-a)/b) + B"
        )
        options.result_parameters = [
            ParameterRepr(
                name="saturation_power",
                repr="saturation_power",
                unit="db",
                param_path="",
            ),
        ]
        return options

    def _guess_fit_param(self, fit_opt, data):
        """
        Data preprocessing and initial parameter guessing for the fitter.
        """
        x = data.x
        y = data.y
        amp = (max(y) - min(y)) / 2
        a = np.median(x)
        b = 4
        B = np.mean(y)
        fit_opt.p0.set_if_empty(A=amp, a=a, b=b, B=B)
        fit_opt.bounds.set_if_empty(b=(1e-3, np.inf))
        return fit_opt

    def _extract_result(self):
        best_data_key = self.experiment_data.metadata.process_meta.get("best_data_key")
        if best_data_key and self._quality.descriptor in [
            QualityDescribe.perfect,
            QualityDescribe.normal,
        ]:
            analysis_data = self.analysis_datas[best_data_key]
            A, a, b, B = analysis_data.fit_data.popt
            low_power = a - (4 * b)
            goal_amp = erf_fit_func(low_power, A, a, b, B) - 1
            try:
                result = int(solve_x_from_erf_fit(goal_amp, A, a, b, B) - 10)
            except ValueError:
                result = None
            self.results.saturation_power.value = result
            if result is None or result < -55 or result > 10:
                self.quality.descriptor = QualityDescribe.bad
            else:
                pos = (result, round(erf_fit_func(result, A, a, b, B), 3))
                self.drawer.set_options(
                    text_pos=[pos], text_rp=[f"Xpi\n{pos}"], text_key=["amp"]
                )
