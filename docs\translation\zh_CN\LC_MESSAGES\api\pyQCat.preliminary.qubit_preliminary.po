# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.preliminary.qubit_preliminary.rst:2
msgid "pyQCat.preliminary.qubit\\_preliminary package"
msgstr ""

#: ../../source/api/pyQCat.preliminary.qubit_preliminary.rst:5
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.preliminary.qubit_preliminary.rst:8
msgid "pyQCat.preliminary.qubit\\_preliminary.cavity\\_flux\\_scan module"
msgstr ""

#: ../../source/api/pyQCat.preliminary.qubit_preliminary.rst:16
msgid "Module contents"
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.preliminary.preliminary_models.QubitPreliminary`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.preliminary.preliminary_models.QubitPreliminary`"
#~ msgstr ""

#~ msgid "Default Experiment Options."
#~ msgstr ""

#~ msgid "Experiment Options:"
#~ msgstr ""

#~ msgid ""
#~ "q_bits (List[int]): Test qubit number, "
#~ "normal `[ 0 ]`. c_bits (List[int]): "
#~ "Test coupler number, normal `[ 0 "
#~ "]`. dc_channel (int): Scan dc will "
#~ "set dc channel. add_dc_channels (List[int]):"
#~ " May be set constant dc to "
#~ "other dc channels. dc_list (List, "
#~ "np.ndarray): Scan dc range. freq_list "
#~ "(List, np.ndarray): Set scan net "
#~ "analyzer scan frequency range. net_IFBW "
#~ ": The IF bandwidth of network "
#~ "analyzer. net_power (float): The power "
#~ "of network analyzer. dynamic_plot (bool): "
#~ "If True stands for animated drawing "
#~ "fit_q (bool): If True, stands Q "
#~ "fitting. ATT : Plot mark ATT "
#~ "value. sample (str, BaseQubit): str or"
#~ " BaseQubit object. name (str): Optional."
#~ " Experiment name."
#~ msgstr ""

#~ msgid "Return type"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.structures.Options`"
#~ msgstr ""

#~ msgid "Default Analysis Options"
#~ msgstr ""

#~ msgid "Default Sub Once Analysis Options"
#~ msgstr ""

#~ msgid "Set experiment options."
#~ msgstr ""

#~ msgid "Set analysis options."
#~ msgstr ""

#~ msgid "Set subclass analysis options."
#~ msgstr ""

#~ msgid "Set metadata."
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.structures.MetaData`"
#~ msgstr ""

#~ msgid "Save CurveAnalysis plot figure."
#~ msgstr ""

#~ msgid "Process Net Analyzer Scan Once result data."
#~ msgstr ""

#~ msgid "Create and Run TunableAnalysis."
#~ msgstr ""

#~ msgid "Parameters"
#~ msgstr ""

#~ msgid "Scan dc range."
#~ msgstr ""

#~ msgid "Start cavity flux scan qubit_test."
#~ msgstr ""

#~ msgid "Qubit Preliminary."
#~ msgstr ""

