# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/05/16
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy

from ....analysis import ZZTimingOnceAnalysis
from ....pulse.pulse_function import stimulate_state_pulse, zero_pulse
from ....pulse.pulse_lib import Constant, FlatTopGaussian
from ....structures import MetaData, Options
from ....tools.utilities import qarange
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment
from .swap_once import (
    validate_data_key,
    validate_qubit_pair_cz_std,
    validate_two_qubit_exp_read_options,
)


class ZZTimingOnce(TopExperiment):
    """SwapOnce scan z line pulse width list."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("swap_state", ["10", "11", "01"])
        options.set_validator("const_delay", dict)
        options.set_validator("delays", list)
        options.set_validator("scan_bit", ["qh", "ql", "qc"])
        options.set_validator("z_pulse_params", dict)

        options.swap_state = "11"
        options.const_delay = {"ql": 20, "qh": 20, "qc": 20}
        options.delays = qarange(0, 40, 0.833)
        options.scan_bit = "qc"
        options.z_pulse_params = {
            "sigma": None,
            "buffer": None,
        }

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []
        options.support_context = [StandardContext.CGC]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("extract_mode", ["min_point", "fit_params"])
        options.set_validator(
            "fit_model_name",
            [
                "gauss_lorentzian",
                "skewed_lorentzian",
                "lorentzian",
                "bi_lorentz_tilt",
                "skewed_gauss_lorentz",
            ],
        )
        options.set_validator("filter", dict)
        options.set_validator("data_key", list)

        options.extract_mode = "fit_params"
        options.fit_model_name = "gauss_lorentzian"
        options.filter = {"window_length": 5, "polyorder": 3}
        options.const_delay = None
        options.data_key = None

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "scan_bit": self.experiment_options.scan_bit,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        validate_qubit_pair_cz_std(self, "zz")
        validate_two_qubit_exp_read_options(self)
        validate_data_key(self)

        # new feature: add pulse param
        gate_params = self.run_options.gate_params
        z_pulse_params = self.experiment_options.z_pulse_params
        for n in list(z_pulse_params.keys()):
            p = z_pulse_params.get(n)
            if p is not None:
                for k in list(gate_params.keys()):
                    gate_params[k][n] = p

        self.set_analysis_options(
            const_delay=self.experiment_options.const_delay.get(
                self.experiment_options.scan_bit
            )
        )

        self.file_describe = f"sweep-{self.experiment_options.scan_bit}"

        self.set_run_options(
            x_data=self.experiment_options.delays, analysis_class=ZZTimingOnceAnalysis
        )

    @staticmethod
    def set_xy_pulses(self):
        """Set SwapOnce experiment XY pulses."""
        eop = self.experiment_options
        rop = self.run_options
        pair = self.qubit_pair

        max_delay = max(eop.delays)
        zz_width = pair.width("zz")
        qubit_list = [rop.ql, rop.qh]
        qubit_list.extend(rop.parking_qubits)

        for qubit in qubit_list:
            if qubit == rop.qh:
                state = eop.swap_state[0]
            elif qubit == rop.ql:
                state = eop.swap_state[1]
            elif qubit.name.startswith("q"):
                state = "0"
            else:
                continue

            state_pulse = stimulate_state_pulse(state, qubit)
            new_state_pulse = deepcopy(state_pulse)
            delay_pulse = Constant(max_delay + zz_width, 0, name="XY")
            xy_pulse = new_state_pulse() + delay_pulse()
            xy_pulse_list = [deepcopy(xy_pulse) for _ in range(len(eop.delays))]

            self.play_pulse("XY", qubit, xy_pulse_list)

    @staticmethod
    def set_z_pulses(self):
        """Set SwapOnce experiment XY pulses."""
        eop = self.experiment_options
        rop = self.run_options

        scan_qubit = getattr(self.run_options, eop.scan_bit)
        const_delay_map = {}
        for bit in rop.env_bits:
            if bit == rop.ql:
                const_delay_map[bit] = eop.const_delay.get("ql")
            elif bit == rop.qh:
                const_delay_map[bit] = eop.const_delay.get("qh")
            elif bit == rop.qc:
                const_delay_map[bit] = eop.const_delay.get("qc")
            else:
                const_delay_map[bit] = eop.const_delay.get("ql")

        drag_time = zero_pulse(rop.qh, "Z").width
        q_assign_pulse = Constant(drag_time, 0)
        for qubit in rop.env_bits:
            pulse_param = rop.gate_params.get(qubit.name)
            z_pulse_list = []
            if qubit == scan_qubit:
                for delay in eop.delays:
                    p1 = deepcopy(q_assign_pulse)
                    p2 = Constant(delay, 0)
                    p3 = FlatTopGaussian(
                        time=rop.width,
                        amp=pulse_param.get("amp"),
                        sigma=pulse_param.get("sigma"),
                        buffer=pulse_param.get("buffer"),
                    )
                    p4 = Constant(max(eop.delays) - delay, 0)
                    cp = p1() + p2() + p3() + p4()
                    z_pulse_list.append(cp)
            else:
                cd = const_delay_map.get(qubit)
                rd = max(eop.delays) - cd
                new_q_assign_pulse = deepcopy(q_assign_pulse)
                target_pulse = FlatTopGaussian(
                    time=rop.width,
                    amp=pulse_param.get("amp"),
                    sigma=pulse_param.get("sigma"),
                    buffer=pulse_param.get("buffer"),
                )
                z_pulse = (
                    new_q_assign_pulse()
                    + Constant(cd, 0)()
                    + target_pulse()
                    + Constant(rd, 0)()
                )
                z_pulse_list = [deepcopy(z_pulse) for _ in range(len(eop.delays))]

            self.play_pulse("Z", qubit, z_pulse_list)
