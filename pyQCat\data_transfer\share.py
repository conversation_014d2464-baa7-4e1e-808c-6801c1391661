# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/06
# __author:       <PERSON><PERSON><PERSON>
from __future__ import annotations

import itertools
import json
import random
import uuid
from datetime import datetime
from typing import List, Optional

import numpy as np

from ..protobuf.v1.acquisition_pb2 import (
    AcqData,
    AcquisitionPackage,
    AmpPhaseData,
    IQData,
)
from ..tools.utilities import display_dict_as_table
from .log import LogLevel, logger
from .state import DataTypeEnum, TaskStatusEnum, TransferTaskStatusEnum
from .structure import CompileResult, Heart, TaskStruct
from .util import encode_msg, remove_ipc_file


class ShareArea:
    __slots__ = (
        "monster_program_queue",
        "transfer_task_collection",
        "tid_rid_map",
        "stop",
        "close",
        "heart",
        "transfer_addr_list",
        "process_name",
        "settings"
    )

    def __init__(self, process_name: str, settings):
        self.monster_program_queue = []
        self.transfer_task_collection = {}
        self.tid_rid_map = {}
        self.stop = False
        self.close = False
        self.heart = Heart()
        self.transfer_addr_list = []
        self.process_name = process_name
        self.settings = settings

    @property
    def information(self):
        message = "Share Information:\n"
        share_information = {
            "Program Queue": len(self.monster_program_queue),
            "Alive Task": len(self.transfer_task_collection),
            "TID-RID-Link": len(self.tid_rid_map),
            "Is Stop": self.stop,
            "Is Close": self.close,
        }
        share_table = display_dict_as_table(share_information)
        message += str(share_table)

        if self.transfer_task_collection:
            task_infos = [
                task.information() for task in self.transfer_task_collection.values()
            ]
            task_table = display_dict_as_table(task_infos)
            message += f"\nTask Information:\n{task_table}"
        return message

    def clear_ipc_file(self):
        for thread_token in self.transfer_addr_list:
            remove_ipc_file(f"transfer{thread_token}")
        remove_ipc_file(self.settings.link_url + self.process_name)
        remove_ipc_file(self.settings.log_url+ self.process_name)
        remove_ipc_file(self.settings.pub_url + self.process_name)

    def get_alive_task_ids(self):
        task_ids = []
        for task in self.transfer_task_collection.values():
            if task.task_id:
                task_ids.append(task.task_id)
        return task_ids

    def clear_resource(self):
        self.monster_program_queue.clear()
        self.transfer_task_collection.clear()
        self.tid_rid_map.clear()
        self.heart = Heart()

    def has_compile_result(self):
        return bool(self.monster_program_queue)

    def pop_compile_result(self):
        if self.monster_program_queue:
            return self.monster_program_queue.pop()

    def build_task_from_compile_result(self, compile_result: CompileResult):
        task = TaskStruct(
            require_id=compile_result.require_id,
            readout_channels=compile_result.readout_channels,
            loop=compile_result.loop,
            program=compile_result.program_buf,
            pointer=compile_result.program_pointer_buf,
            simulator=compile_result.simulator,
            data_type=DataTypeEnum.adapter_experiment_doc_status(
                compile_result.data_type
            ),
            shot=compile_result.shot,
            register_time=datetime.now()
        )
        task.init_result_struct()
        if task.simulator:
            task.task_id = str(uuid.uuid4())
            task.state = TransferTaskStatusEnum.SUC
            self.tid_rid_map[task.task_id] = task.require_id
        self.transfer_task_collection[compile_result.require_id] = task
        logger.log(LogLevel.DISPATCH, f"Register task {task}")

    def check_dispatcher_heart_state(self):
        if self.heart.state:
            logger.log(LogLevel.SYSTEM, "Dispatcher heart normal ...")
        else:
            logger.log(LogLevel.SYSTEM, "Dispatcher heart interruption ...")

    def send_dispatcher_heart_state(self):
        self.heart.state = 0

    def is_dispatcher_restart(self):
        return self.heart.is_restart

    def dispatcher_restart_recover(self):
        self.heart.is_restart = False

    def get_task_state(self, task: TaskStruct):
        return task.state if self.stop is False else TransferTaskStatusEnum.FAIL

    def get_task_from_id(self, task_id: str):
        if task_id in self.tid_rid_map:
            return self.transfer_task_collection.get(self.tid_rid_map[task_id])

    def get_task_from_require_id(self, require_id: str) -> TaskStruct | None:
        return self.transfer_task_collection.get(require_id)

    def bind_acq_data(self, buffer: bytes):
        package = AcquisitionPackage.FromString(buffer)
        task = self.get_task_from_id(package.task_uuid)
        if task:
            for acq_data in package.data:
                channel = str(acq_data.channel)
                if acq_data.HasField("iq"):
                    task.data_type = DataTypeEnum.IQ
                    task.result[channel][package.loop_index - 1] = (
                        acq_data.iq.SerializeToString()
                    )
                elif acq_data.HasField("amp_phase"):
                    task.data_type = DataTypeEnum.AP
                    task.result[channel][package.loop_index - 1] = (
                        acq_data.amp_phase.SerializeToString()
                    )
                elif acq_data.HasField("probability"):
                    task.data_type = DataTypeEnum.PO
                    task.result[channel][package.loop_index - 1] = acq_data.probability
                else:
                    logger.error(
                        f"band acq data, but data type error {package.task_uuid} {task}"
                    )
                task.acq_loops.add(package.loop_index - 1)
            task.count += 1
            return True
        else:
            logger.error(f"band acq data, but not found task {package.task_uuid}")
            return False

    def bind_task_error(self, json_str: str):
        error_info = json.loads(json_str)
        task_uuid = error_info.get("task_uuid")
        error_msg = error_info.get("error_msg")
        task = self.get_task_from_id(task_uuid)
        if task:
            task.state = TransferTaskStatusEnum.from_description(error_msg)
            task.error = error_msg
            return True
        else:
            logger.error(f"band task error info, but not found task {task_uuid}")
            return False

    def bind_task_id(self, json_str: str):
        info_id = json.loads(json_str)
        task_uuid = info_id.get("task_uuid")
        require_id = info_id.get("require_id")
        task = self.get_task_from_require_id(require_id)
        if task and task_uuid and require_id:
            if task.task_id:
                logger.warning(
                    f"Re establish ID mapping, RID({require_id}) | TID({task_uuid})"
                )
            self.tid_rid_map[task_uuid] = require_id
            task.task_id = task_uuid
            return True
        else:
            logger.error(f"band task id, but not found task {task_uuid}")
            return False

    def bind_task_state(self, json_str: str) -> bool:
        state_info = json.loads(json_str)
        task_uuid = state_info.get("task_uuid")
        task_status = state_info.get("task_status")
        state = TransferTaskStatusEnum.from_description(task_status)
        detail = state_info.get("details", "")
        if state == TransferTaskStatusEnum.FAIL:
            detail_msg = str(TaskStatusEnum.from_description(detail))
            if detail != detail_msg:
                detail = f"code({detail}) | {detail_msg}"
        task = self.get_task_from_id(task_uuid)
        if task:
            logger.log(
                LogLevel.DISPATCH,
                f"RID({task.require_id}) state to `{state.name}` | {detail}",
            )
            if state != TransferTaskStatusEnum.INIT:
                task.state = state
            if state == TransferTaskStatusEnum.RETRY:
                task.retry_start = datetime.now()
            return True
        else:
            logger.error(
                f"band task state `{state.name}`, but not found task {task_uuid}"
            )
            return False

    def recv_dispatcher_heart_state(self, token: bytes):
        his_token = self.heart.token
        if his_token and his_token != token:
            logger.warning("Dispatcher Restart, will resend some program ...")
            self.heart.is_restart = True
        self.heart.token = token
        self.heart.state = 1
        return True

    def get_acq_data(
        self, require_id: str, channels: List[str], index: Optional[int] = None
    ):
        task = self.get_task_from_require_id(require_id)
        if not task:
            logger.error(f"no find task RID({require_id})")
            return [encode_msg(str(TransferTaskStatusEnum.FAIL))]

        if task.simulator:
            result = [encode_msg(str(self.get_task_state(task)))]
            if index is None:
                for channel in channels:
                    result.extend(
                        [
                            generate_random_acq_data(
                                task.shot * task.readout_channels[channel],
                                task.data_type,
                            )
                            for _ in task.result[channel]
                        ]
                    )
            else:
                result.extend(
                    [
                        generate_random_acq_data(
                            task.shot * task.readout_channels[channel], task.data_type
                        )
                        for channel in channels
                    ]
                )
            return result

        if index is None:
            result = [None]
            for channel in channels:
                result.extend(task.result[channel])
        else:
            loops = [index]
            combinations = list(itertools.product(channels, loops))
            result: List = [None for _ in range(len(combinations) + 1)]

            for idx, (chl, lp) in enumerate(combinations):
                cur_buf = task.result[chl][lp]
                if cur_buf:
                    result[idx + 1] = cur_buf
                else:
                    state = self.get_task_state(task)
                    return [encode_msg(str(state))]
        result[0] = encode_msg(str(self.get_task_state(task)))
        return result

    def get_task_state_from_require_id(self, require_id: str) -> TransferTaskStatusEnum:
        task = self.get_task_from_require_id(require_id)
        if task:
            return self.get_task_state(task)
        else:
            logger.error(f"no find task RID({require_id})")
            return TransferTaskStatusEnum.FAIL

    def check_acquisition_start(self, require_id: str):
        task = self.get_task_from_require_id(require_id)
        if task:
            if task.count:
                return TransferTaskStatusEnum.ACQ_START
            return task.state
        else:
            logger.error(f"no find task RID({require_id})")
            return TransferTaskStatusEnum.FAIL

    def pop_task_from_require_id(self, require_id: str):
        return self.transfer_task_collection.pop(require_id, None)

    def clear_acq_data(self, require_id: str, channels: List[str]):
        task = self.get_task_from_require_id(require_id)
        if task:
            for channel in channels:
                task.result.pop(channel, None)
                task.remove_channels.add(channel)
            return True
        else:
            logger.error(f"no find task RID{require_id}")
            return False

    def clear_finish_task(self) -> List:
        clear_ids = []
        for task in list(self.transfer_task_collection.values()):
            if self.stop is True or task.is_finish():
                logger.log(LogLevel.TRANSFER, f"Clear {task}")
                self.transfer_task_collection.pop(task.require_id, None)
                if task.task_id:
                    self.tid_rid_map.pop(task.task_id, None)
                clear_ids.append(task.task_id)
        return clear_ids


def generate_random_acq_data(shots: int, data_type: DataTypeEnum):
    if data_type == DataTypeEnum.IQ:
        return generate_fake_iq(shots)
    elif data_type == DataTypeEnum.AP:
        return generate_fake_amp_phase()
    elif data_type == DataTypeEnum.TRACK:
        return generate_fake_track(shots)
    else:
        logger.error(f"No support data type {data_type}")
        return generate_fake_amp_phase()


def generate_fake_amp_phase() -> AcqData:
    amp_phase_data = AmpPhaseData()
    amp_phase_data.amp.append(random.random())
    amp_phase_data.phase.append(random.random())
    return amp_phase_data.SerializeToString()


def generate_fake_iq(shots: int) -> AcqData:
    iq_data = IQData()
    random_i = np.random.rand(shots) * 50
    random_q = np.random.rand(shots) * 50
    iq_data.i.extend(random_i)
    iq_data.q.extend(random_q)
    return iq_data.SerializeToString()


def generate_fake_track(shots: int) -> AcqData:
    fake_sample_width = 800
    fake_sample_rate = 3.2
    
    random_i = np.array([np.random.rand(int(fake_sample_width * fake_sample_rate)) * 50 for _ in range(shots)])
    random_q = np.array([np.random.rand(int(fake_sample_width * fake_sample_rate)) * 50 for _ in range(shots)])
    
    mean_i = np.mean(random_i, axis=0)
    std_i = np.std(random_i, axis=0)
    result_i = np.hstack((mean_i, std_i))
    
    mean_q = np.mean(random_q, axis=0)
    std_q = np.std(random_q, axis=0)
    result_q = np.hstack((mean_q, std_q))
    
    iq_data = IQData()
    iq_data.i.extend(result_i)
    iq_data.q.extend(result_q)
    
    return iq_data.SerializeToString()
