{"cells": [{"cell_type": "markdown", "id": "28a59423", "metadata": {}, "source": ["# CavityFreqSpectrum\n", "\n", "扫腔实验，用于粗测比特腔频"]}, {"cell_type": "code", "execution_count": 1, "id": "67e5a5c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\n"]}], "source": ["# 若用户未安装 pyqcat 包（monster），通过本地源码测试，需要执行下面操作\n", "# 若安装了 pyqcat 包（monster），跳过此步骤\n", "\n", "import os\n", "import sys\n", "\n", "\n", "package_root = \"../../../pyqcat-monster\"\n", "\n", "print(os.path.abspath(package_root))\n", "\n", "sys.path.insert(0, package_root)"]}, {"cell_type": "code", "execution_count": 2, "id": "fa251024", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:02:16\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1m\n", "|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |        tcp://************:8088         |\n", "|    point_label     |              person_point              |\n", "|       sample       |               test_chip                |\n", "|      env_name      |                D00_1011                |\n", "|      version       |                 0.1.6                  |\n", "|=============================================================|\n", "\u001b[0m\n"]}, {"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': 'a3502c597e99cbacdea0b959e09d8c00'},\n", " 'msg': 'success'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "\n", "from pyQCat.tools import qarange\n", "from pyQCat.context import ExperimentContext\n", "from pyQCat.invoker import Invoker\n", "from pyQCat.parameters import get_parameters\n", "\n", "\n", "%matplotlib inline\n", "pd.set_option('max_colwidth', None)\n", "\n", "conf_file = r'E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf'\n", "\n", "username = \"test_04\"\n", "password = \"123456\"\n", "\n", "context = ExperimentContext(conf_file)\n", "Invoker.verify_account(username=username, password=password)"]}, {"cell_type": "code", "execution_count": 3, "id": "229c261c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:02:17\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1mQubit_(bit=0) is already exist in current environment.\u001b[0m\n"]}], "source": ["# context 加载实验参数\n", "\n", "q_name_list = [\"q0\"]\n", "environment_elements = [\"q0\", \"q1\", \"q2\", \"q3\", \"c0\", \"c1\", \"c2\"]\n", "\n", "context.configure_inst()\n", "context.configure_qubits(q_name_list)\n", "context.minimize_compensate()\n", "# context.maximize_compensate()\n", "context.configure_environment(environment_elements)\n", "context.configure_crosstalk_dict()\n", "\n", "qubit = context.qubit"]}, {"cell_type": "code", "execution_count": 4, "id": "5820ee4c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.744732</td>\n", "      <td>0.236088</td>\n", "      <td>0.203233</td>\n", "      <td>0.525287</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          1         2         3         4    5    6    7\n", "0  0.744732  0.236088  0.203233  0.525287  0.0  0.0  0.0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看当前系统设置的DC \n", "\n", "pd.DataFrame(context.working_dc, index=[0])"]}, {"cell_type": "code", "execution_count": 5, "id": "370eafb3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>experiment option</td>\n", "      <td>show_result</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_shape</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_data_path</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>experiment option</td>\n", "      <td>repeat</td>\n", "      <td>1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>experiment option</td>\n", "      <td>register_pulse_save</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_flag</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_save</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_measure</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_type</td>\n", "      <td>envelop</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_measure</td>\n", "      <td>150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_real</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_index</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>experiment option</td>\n", "      <td>save_label</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>experiment option</td>\n", "      <td>is_dynamic</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>experiment option</td>\n", "      <td>fidelity_matrix</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>experiment option</td>\n", "      <td>ac_prepare_time</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>experiment option</td>\n", "      <td>readout_point_model</td>\n", "      <td>&lt;class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>experiment option</td>\n", "      <td>idle_qubits</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>experiment option</td>\n", "      <td>fc_list</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>experiment option</td>\n", "      <td>points</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>experiment option</td>\n", "      <td>readout_power</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>experiment option</td>\n", "      <td>amp</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>experiment option</td>\n", "      <td>is_opt</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>experiment option</td>\n", "      <td>scope</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>analysis option</td>\n", "      <td>is_plot</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>analysis option</td>\n", "      <td>figsize</td>\n", "      <td>(12, 8)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>analysis option</td>\n", "      <td>quality_bounds</td>\n", "      <td>[0.98, 0.95, 0.85]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 type                   name  \\\n", "0   experiment option            show_result   \n", "1   experiment option        simulator_shape   \n", "2   experiment option    simulator_data_path   \n", "3   experiment option                 repeat   \n", "4   experiment option    register_pulse_save   \n", "5   experiment option          schedule_flag   \n", "6   experiment option          schedule_save   \n", "7   experiment option       schedule_measure   \n", "8   experiment option          schedule_type   \n", "9   experiment option  schedule_show_measure   \n", "10  experiment option     schedule_show_real   \n", "11  experiment option         schedule_index   \n", "12  experiment option             save_label   \n", "13  experiment option             is_dynamic   \n", "14  experiment option        fidelity_matrix   \n", "15  experiment option        ac_prepare_time   \n", "16  experiment option    readout_point_model   \n", "17  experiment option            idle_qubits   \n", "18  experiment option                fc_list   \n", "19  experiment option                 points   \n", "20  experiment option          readout_power   \n", "21  experiment option                    amp   \n", "22  experiment option                 is_opt   \n", "23  experiment option                  scope   \n", "24    analysis option                is_plot   \n", "25    analysis option                figsize   \n", "26    analysis option         quality_bounds   \n", "\n", "                                               value  \n", "0                                               True  \n", "1                                               None  \n", "2                                               None  \n", "3                                               1000  \n", "4                                              False  \n", "5                                               True  \n", "6                                               True  \n", "7                                               True  \n", "8                                            envelop  \n", "9                                                150  \n", "10                                              True  \n", "11                                                -1  \n", "12                                              None  \n", "13                                                 1  \n", "14                                              None  \n", "15                                                 0  \n", "16  <class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'>  \n", "17                                                []  \n", "18                                              None  \n", "19                                                61  \n", "20                                              None  \n", "21                                              None  \n", "22                                             False  \n", "23                                                 3  \n", "24                                              True  \n", "25                                           (12, 8)  \n", "26                                [0.98, 0.95, 0.85]  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from pyQCat.experiments import CavityFreqSpectrum\n", "\n", "\n", "# 创建实验\n", "cs_exp = CavityFreqSpectrum.from_experiment_context(context)\n", "\n", "# 查看实验选项\n", "pd.DataFrame(cs_exp.options_table())"]}, {"cell_type": "markdown", "id": "07e2b8ca", "metadata": {}, "source": ["**experiment options**\n", "\n", "*辅助功能选型*\n", "- simulator_data_path (str): 模拟数据地址\n", "- simulator (bool): 是否开启模拟器\n", "- repeat (int): 小循环次数\n", "- fidelity_matrix (np.ndarray): 保真度矩阵\n", "- show_result (bool): 是否在日志中展示分析结果\n", "- save_lable (str): 采集数据存储标签，用于生成文件名，默认为空\n", "- is_dynamic (int): 是否进行动态绘图，默认为1，单比特读取动态绘图，0关闭动态绘图\n", "\n", "*脉冲时序选项*\n", "- schedule_flag (bool): 是否绘制脉冲时序图\n", "- schedule_save (bool): 是否存储脉冲时序图\n", "- schedule_measure (bool): 是否绘制测量波形\n", "- schedule_type (str): 脉冲时序图的类型，支持 squence 和 envelop 两种\n", "- schedule_index (int or list(int)): 绘制脉冲时序图的索引\n", "- register_pulse_save (bool): 波形存储数据\n", "\n", "*实验参数选项*\n", "\n", "- fc_list (list): 扫描读取频率列表，支持为空\n", "- points (int): 扫描点数\n", "- readout_power (float): 读取功率\n", "- amp (float): Z线施加幅值\n", "- is_opt (bool): XY线是否添加X门\n", "- scope (float): 当前腔频位置上下扫描的范围，单位 MHz\n", "\n", "**analysis options**\n", "- is_plot (bool): 是否绘制结果图\n", "- quality_bounds (str): 曲线拟合质量评估阈值"]}, {"cell_type": "code", "execution_count": 6, "id": "45bac007", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:02:17\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mCavityFreqSpectrum register success, id 634925996197ca12107415bf\u001b[0m\n", "\u001b[33m2022-10-14 17:02:17\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\test_chip\\CavityFreqSpectrum\\q0\\2022-10-14\\17.02.17\\\u001b[0m\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9144d4ea688a4ceebe2ed101b3313c4c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/61 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:02:18\u001b[0m | \u001b[32m RESULT \u001b[0m | \u001b[32mExperiment results as follow:\n", "==========================================================\n", "| name | describe |  value   | unit |      quality       | \n", "----------------------------------------------------------\n", "|  fr  |    fc    | 7380.301 | MHz  | R²=0.9975(perfect) | \n", "==========================================================\u001b[0m\n"]}], "source": ["# 设置 experiment_options 和 analysis_options\n", "\n", "\n", "cs_exp.set_experiment_options(\n", "    is_opt=False,\n", "    simulator_data_path='../../scripts/simulator/data/CavityFreqSpectrum/',\n", "    readout_power=-10,\n", ")\n", "\n", "cs_exp.set_analysis_options(\n", "    is_plot=True,\n", "    quality_bounds=[0.98, 0.95, 0.85]\n", ")\n", "\n", "cs_exp.run()"]}, {"cell_type": "code", "execution_count": 7, "id": "5d93a7c0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:02:18\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1mNo find schedule records!\u001b[0m\n"]}], "source": ["cs_exp.jupyter_schedule()"]}, {"cell_type": "code", "execution_count": 8, "id": "1a30624b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "      <th>unit</th>\n", "      <th>extra</th>\n", "      <th>quality</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>fc</td>\n", "      <td>7380.301</td>\n", "      <td>MHz</td>\n", "      <td>{}</td>\n", "      <td>R²=0.9975(perfect)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  name     value unit extra             quality\n", "0   fc  7380.301  MHz    {}  R²=0.9975(perfect)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(cs_exp.analysis.show_results())"]}, {"cell_type": "code", "execution_count": 9, "id": "c1cd74ab", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 2 Axes>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["cs_exp.analysis.drawer.figure"]}, {"cell_type": "code", "execution_count": 10, "id": "fbf2dfb7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>experiment option</td>\n", "      <td>show_result</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_shape</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_data_path</td>\n", "      <td>../../scripts/simulator/data/CavityFreqSpectrum/CavityFreqSpectrum(amp_phase).dat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>experiment option</td>\n", "      <td>repeat</td>\n", "      <td>1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>experiment option</td>\n", "      <td>register_pulse_save</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_flag</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_save</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_measure</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_type</td>\n", "      <td>envelop</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_measure</td>\n", "      <td>150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_real</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_index</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>experiment option</td>\n", "      <td>save_label</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>experiment option</td>\n", "      <td>is_dynamic</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>experiment option</td>\n", "      <td>fidelity_matrix</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>experiment option</td>\n", "      <td>ac_prepare_time</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>experiment option</td>\n", "      <td>readout_point_model</td>\n", "      <td>&lt;class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>experiment option</td>\n", "      <td>idle_qubits</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>experiment option</td>\n", "      <td>fc_list</td>\n", "      <td>[6210.0, 6210.1, 6210.2, 6210.3, 6210.4, 6210.5, 6210.6, 6210.7, 6210.8, 6210.9, 6211.0, 6211.1, 6211.2, 6211.3, 6211.4, 6211.5, 6211.6, 6211.7, 6211.8, 6211.9, 6212.0, 6212.1, 6212.2, 6212.3, 6212.4, 6212.5, 6212.6, 6212.7, 6212.8, 6212.9, 6213.0, 6213.1, 6213.2, 6213.3, 6213.4, 6213.5, 6213.6, 6213.7, 6213.8, 6213.9, 6214.0, 6214.1, 6214.2, 6214.3, 6214.4, 6214.5, 6214.6, 6214.7, 6214.8, 6214.9, 6215.0, 6215.1, 6215.2, 6215.3, 6215.4, 6215.5, 6215.6, 6215.7, 6215.8, 6215.9, 6216.0]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>experiment option</td>\n", "      <td>points</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>experiment option</td>\n", "      <td>readout_power</td>\n", "      <td>-10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>experiment option</td>\n", "      <td>amp</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>experiment option</td>\n", "      <td>is_opt</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>experiment option</td>\n", "      <td>scope</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>analysis option</td>\n", "      <td>is_plot</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>analysis option</td>\n", "      <td>figsize</td>\n", "      <td>(12, 8)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>analysis option</td>\n", "      <td>quality_bounds</td>\n", "      <td>[0.98, 0.95, 0.85]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 type                   name  \\\n", "0   experiment option            show_result   \n", "1   experiment option        simulator_shape   \n", "2   experiment option    simulator_data_path   \n", "3   experiment option                 repeat   \n", "4   experiment option    register_pulse_save   \n", "5   experiment option          schedule_flag   \n", "6   experiment option          schedule_save   \n", "7   experiment option       schedule_measure   \n", "8   experiment option          schedule_type   \n", "9   experiment option  schedule_show_measure   \n", "10  experiment option     schedule_show_real   \n", "11  experiment option         schedule_index   \n", "12  experiment option             save_label   \n", "13  experiment option             is_dynamic   \n", "14  experiment option        fidelity_matrix   \n", "15  experiment option        ac_prepare_time   \n", "16  experiment option    readout_point_model   \n", "17  experiment option            idle_qubits   \n", "18  experiment option                fc_list   \n", "19  experiment option                 points   \n", "20  experiment option          readout_power   \n", "21  experiment option                    amp   \n", "22  experiment option                 is_opt   \n", "23  experiment option                  scope   \n", "24    analysis option                is_plot   \n", "25    analysis option                figsize   \n", "26    analysis option         quality_bounds   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       value  \n", "0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       True  \n", "1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       None  \n", "2                                                                                                                                                                                                                                                                                                                                                                                                                          ../../scripts/simulator/data/CavityFreqSpectrum/CavityFreqSpectrum(amp_phase).dat  \n", "3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       1000  \n", "4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      False  \n", "5                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      False  \n", "6                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       True  \n", "7                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       True  \n", "8                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    envelop  \n", "9                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        150  \n", "10                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      True  \n", "11                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        -1  \n", "12                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      None  \n", "13                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         1  \n", "14                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      None  \n", "15                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         0  \n", "16                                                                                                                                                                                                                                                                                                                                                                                                                                                          <class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'>  \n", "17                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        []  \n", "18  [6210.0, 6210.1, 6210.2, 6210.3, 6210.4, 6210.5, 6210.6, 6210.7, 6210.8, 6210.9, 6211.0, 6211.1, 6211.2, 6211.3, 6211.4, 6211.5, 6211.6, 6211.7, 6211.8, 6211.9, 6212.0, 6212.1, 6212.2, 6212.3, 6212.4, 6212.5, 6212.6, 6212.7, 6212.8, 6212.9, 6213.0, 6213.1, 6213.2, 6213.3, 6213.4, 6213.5, 6213.6, 6213.7, 6213.8, 6213.9, 6214.0, 6214.1, 6214.2, 6214.3, 6214.4, 6214.5, 6214.6, 6214.7, 6214.8, 6214.9, 6215.0, 6215.1, 6215.2, 6215.3, 6215.4, 6215.5, 6215.6, 6215.7, 6215.8, 6215.9, 6216.0]  \n", "19                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        61  \n", "20                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       -10  \n", "21                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      None  \n", "22                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     False  \n", "23                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         3  \n", "24                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      True  \n", "25                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   (12, 8)  \n", "26                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        [0.98, 0.95, 0.85]  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(cs_exp.options_table())"]}, {"cell_type": "code", "execution_count": 11, "id": "4e368117", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:02:18\u001b[0m | \u001b[33m\u001b[1m UPDATE \u001b[0m | \u001b[33m\u001b[1mSave q0 to data service success.\u001b[0m\n"]}], "source": ["context.qubit.probe_freq = 7380.225\n", "context.qubit.save_data()"]}, {"cell_type": "code", "execution_count": 12, "id": "6c9ebc61", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>parameter</th>\n", "      <th>value</th>\n", "      <th>unit</th>\n", "      <th>update_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>probe_freq</td>\n", "      <td>7380.225</td>\n", "      <td>MHz</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    parameter     value unit update_time\n", "0  probe_freq  7380.225  MHz        None"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(context.base_qubit_table(context.qubit, parameters='probe_freq'))"]}, {"cell_type": "code", "execution_count": null, "id": "30e3eac2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.12"}}, "nbformat": 4, "nbformat_minor": 5}