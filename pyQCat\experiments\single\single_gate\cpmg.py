# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/02/06
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy
from enum import Enum

import numpy as np

from ....analysis import CPMGAnalysis
from ....structures import Options
from .ramsey import Constant, List, MetaData, Ramsey, half_pi_pulse, pi_pulse, qarange


class CPMGMode(str, Enum):
    CP = "CP"
    CPMG = "CPMG"


class CPMGExperiment(Ramsey):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("mode", [member.value for member in CPMGMode])
        options.set_validator("N", int)
        options.mode = "CP"
        options.N = 6
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("fft_freq", list)
        options.fft_freq = qarange(0.001, 40, 0.001)  # MHz
        options.data_key = ["P0", "P1"]
        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        self.set_run_options(analysis_class=CPMGAnalysis)
        self._label = self.experiment_options.mode

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta.update(dict(mode=self.experiment_options.mode, N=self.experiment_options.N))
        metadata.process_meta.update(
            dict(
                N=self.experiment_options.N,
                max_delay=max(self.experiment_options.delays),
                x_width=pi_pulse(self.qubit).width,
                x2_width=half_pi_pulse(self.qubit).width,
            )
        )
        return metadata

    @staticmethod
    def get_xy_pulse(qubit, delays: List, fringe: float, N: int, mode: CPMGMode):
        """Get XY line wave."""
        pulse_list = []
        for delay in delays:
            one_time = round(delay / (N * 2), 4)
            front_drag = half_pi_pulse(qubit)()
            side_delay = Constant(one_time, 0, "XY")()
            mid_delay = Constant(one_time * 2, 0, "XY")()
            mid_drag = pi_pulse(qubit)()
            if mode == CPMGMode.CPMG:
                mid_drag(phase=np.pi / 2)
            rear_drag = deepcopy(front_drag)
            exp_pulse = front_drag + side_delay

            for _ in range(N - 1):
                exp_pulse += deepcopy(mid_drag)
                exp_pulse += deepcopy(mid_delay)

            exp_pulse += deepcopy(mid_drag)
            exp_pulse += deepcopy(side_delay)
            exp_pulse += rear_drag(phase=2 * np.pi * fringe * 1e6 / 1e9 * delay)
            exp_pulse.bit = qubit.name
            exp_pulse.sweep = "sweep delay"
            pulse_list.append(exp_pulse)
        return pulse_list

    @staticmethod
    def get_z_pulse(qubit, delays: List, z_amp: float, N: int):
        z_pulse_list = []

        x_width = pi_pulse(qubit).width
        x2_width = half_pi_pulse(qubit).width

        for delay in delays:
            one_time = round(delay / (N * 2), 4)
            exp_pulse = Constant(x2_width, 0)() + Constant(one_time, z_amp)()

            for _ in range(N - 1):
                exp_pulse += Constant(x_width, 0)()
                exp_pulse += Constant(one_time * 2, z_amp)()

            exp_pulse += Constant(x_width, 0)()
            exp_pulse += Constant(one_time, z_amp)()
            exp_pulse += Constant(x2_width, 0)()
            exp_pulse.bit = qubit.name
            exp_pulse.sweep = "sweep delay"
            z_pulse_list.append(exp_pulse)

        return z_pulse_list

    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        xy_pulse_list = CPMGExperiment.get_xy_pulse(
            builder.qubit,
            builder.experiment_options.delays,
            builder.experiment_options.fringe,
            builder.experiment_options.N,
            builder.experiment_options.mode,
        )
        builder.play_pulse("XY", builder.qubit, xy_pulse_list)

    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""
        if builder.experiment_options.z_amp is not None:
            z_pulse_list = CPMGExperiment.get_z_pulse(
                builder.qubit,
                builder.experiment_options.delays,
                builder.experiment_options.z_amp,
                builder.experiment_options.N,
            )
            builder.play_pulse("Z", builder.qubit, z_pulse_list)
