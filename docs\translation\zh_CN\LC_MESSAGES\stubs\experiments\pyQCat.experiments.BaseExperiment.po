# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:2
msgid "pyQCat.experiments.BaseExperiment"
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment:1
msgid "Abstract base class for experiments."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
#: of pyQCat.experiments.base_experiment.BaseExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.BaseExperiment.__init__>`\\ "
"\\(\\[inst\\, qubits\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.BaseExperiment.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
#: of
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.BaseExperiment.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
#: of pyQCat.experiments.base_experiment.BaseExperiment.get_qubit_str:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.BaseExperiment.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.BaseExperiment.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
#: of pyQCat.experiments.base_experiment.BaseExperiment.set_analysis_options:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.BaseExperiment.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
#: of
#: pyQCat.experiments.base_experiment.BaseExperiment.set_experiment_options:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.BaseExperiment.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
#: of pyQCat.experiments.base_experiment.BaseExperiment.set_parent_file:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.BaseExperiment.set_run_options>`\\ \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:28:<autosummary>:1
#: of pyQCat.experiments.base_experiment.BaseExperiment.set_run_options:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.BaseExperiment.rst:30
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.BaseExperiment.analysis>`\\"
msgstr ""

#: of pyQCat.experiments.BaseExperiment.analysis:1
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.BaseExperiment.analysis_options>`\\"
msgstr ""

#: of pyQCat.experiments.BaseExperiment.analysis_options:1
#: pyQCat.experiments.BaseExperiment.experiment_options:1
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.BaseExperiment.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context:1:<autosummary>:1
msgid ":py:obj:`run_options <pyQCat.experiments.BaseExperiment.run_options>`\\"
msgstr ""

#: of pyQCat.experiments.BaseExperiment.run_options:1
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment._put_info_to_queue
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context
#: pyQCat.experiments.base_experiment.BaseExperiment.set_analysis_options
#: pyQCat.experiments.base_experiment.BaseExperiment.set_experiment_options
#: pyQCat.experiments.base_experiment.BaseExperiment.set_parent_file
#: pyQCat.experiments.base_experiment.BaseExperiment.set_run_options
msgid "Parameters"
msgstr ""

#: of
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context:4
msgid "The instance of `ExperimentContext`."
msgstr ""

#: of pyQCat.experiments.BaseExperiment.analysis
#: pyQCat.experiments.BaseExperiment.analysis_options
#: pyQCat.experiments.BaseExperiment.experiment_options
#: pyQCat.experiments.BaseExperiment.run_options
#: pyQCat.experiments.base_experiment.BaseExperiment._default_analysis_options
#: pyQCat.experiments.base_experiment.BaseExperiment._default_experiment_options
#: pyQCat.experiments.base_experiment.BaseExperiment._default_run_options
#: pyQCat.experiments.base_experiment.BaseExperiment._metadata
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context
#: pyQCat.experiments.base_experiment.BaseExperiment.get_qubit_str
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context:7
msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context
#: pyQCat.experiments.base_experiment.BaseExperiment.get_qubit_str
msgid "Returns"
msgstr ""

#: of
#: pyQCat.experiments.base_experiment.BaseExperiment.from_experiment_context:8
msgid "`BaseExperiment` object."
msgstr ""

#: of pyQCat.experiments.BaseExperiment.analysis:3
msgid ":py:data:`~typing.Optional`\\[:py:class:`~pyQCat.analysis.top_analysis.TopAnalysis`]"
msgstr ""

#: of pyQCat.experiments.BaseExperiment.analysis_options:3
#: pyQCat.experiments.BaseExperiment.experiment_options:3
#: pyQCat.experiments.BaseExperiment.run_options:3
#: pyQCat.experiments.base_experiment.BaseExperiment._default_analysis_options:4
#: pyQCat.experiments.base_experiment.BaseExperiment._default_experiment_options:5
#: pyQCat.experiments.base_experiment.BaseExperiment._default_run_options:4
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.base_experiment.BaseExperiment._default_analysis_options:1
msgid "Default kwarg options for analysis."
msgstr ""

#: of
#: pyQCat.experiments.base_experiment.BaseExperiment._default_experiment_options:1
msgid "Default kwarg options for experiment."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment._default_run_options:1
msgid "Default options values for the experiment :meth:`run` method."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment.set_analysis_options:3
#: pyQCat.experiments.base_experiment.BaseExperiment.set_experiment_options:3
#: pyQCat.experiments.base_experiment.BaseExperiment.set_run_options:3
msgid "The fields to update the options"
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment.set_analysis_options
#: pyQCat.experiments.base_experiment.BaseExperiment.set_experiment_options
msgid "Raises"
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment.set_analysis_options:5
#: pyQCat.experiments.base_experiment.BaseExperiment.set_experiment_options:5
msgid "If the field passed in is not a supported options"
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment.get_qubit_str:3
msgid "string, use to create save data path."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment._metadata:1
msgid "Return experiment metadata for ExperimentData."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment._metadata:3
msgid ""
"Subclasses can override this method to add custom experiment metadata to "
"the returned experiment result data."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment._metadata:7
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment._init_qubit_info:1
msgid "init qubit information."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment._config_file_sys:1
msgid ""
"Set the file path of experiment's analysis result. Support object storage"
" and file storage."
msgstr ""

#: of
#: pyQCat.experiments.base_experiment.BaseExperiment._save_curve_analysis_plot:1
msgid "Save CurveAnalysis plot figure."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment.set_parent_file:4
msgid "BaseFile class object."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment.set_parent_file:7
msgid "The description of the file."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment._check_options:1
msgid "Check Options."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment._validate_options:1
msgid "Validate Options."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment._put_info_to_queue:1
msgid "Put information data to data service."
msgstr ""

#: of pyQCat.experiments.base_experiment.BaseExperiment._put_info_to_queue:4
msgid "Put to Queue data,"
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.BaseExperiment.__init__>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.BaseExperiment.from_experiment_context>`\\ "
#~ "\\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.BaseExperiment.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.BaseExperiment.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.BaseExperiment.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.BaseExperiment.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.BaseExperiment.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":py:obj:`analysis <pyQCat.experiments.BaseExperiment.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.BaseExperiment.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.BaseExperiment.experiment_options>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`run_options <pyQCat.experiments.BaseExperiment.run_options>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.experiments.BaseExperiment.__init__>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.BaseExperiment.from_experiment_context>`\\ "
#~ "\\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.BaseExperiment.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.BaseExperiment.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.BaseExperiment.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.BaseExperiment.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.BaseExperiment.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.BaseExperiment.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.BaseExperiment.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.BaseExperiment.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.BaseExperiment.experiment_options>`\\"
#~ msgstr ""

#~ msgid ":obj:`run_options <pyQCat.experiments.BaseExperiment.run_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Put information data to Process Queue."
#~ " By `pyQCat.tools.utilities.save_queue_msg_to_store` "
#~ "api, save to DB server corresponding "
#~ "collection."
#~ msgstr ""

#~ msgid ""
#~ "Put to Queue data, data normal "
#~ "like: {     \"username\": \"username\",     "
#~ "\"save_type\": \"note_experiment\",     \"params\": "
#~ "{} }"
#~ msgstr ""

#~ msgid "Put to Queue data, data normal like: {"
#~ msgstr ""

#~ msgid ""
#~ "\"username\": \"username\", \"save_type\": "
#~ "\"note_experiment\", \"params\": {}"
#~ msgstr ""

#~ msgid "}"
#~ msgstr ""

#~ msgid "Put information data to database."
#~ msgstr ""

