# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:2
msgid "pyQCat.analysis.library.CavityAnalysis"
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.curve_analysis.CurveAnalysis`"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:1
msgid "Initialize the analysis object."
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._extract_result
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param
#: pyQCat.analysis.top_analysis.TopAnalysis.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:4
msgid "pyQCat Experiments Data container class."
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:7
msgid "Whether the experiment contains sub-experiments."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.library.CavityAnalysis.__init__>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`from_sub_analysis "
"<pyQCat.analysis.library.CavityAnalysis.from_sub_analysis>`\\ "
"\\(x\\_data\\, sub\\_analysis\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:25:<autosummary>:1
msgid "This interface is used for composite experiment."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`run_analysis "
"<pyQCat.analysis.library.CavityAnalysis.run_analysis>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:25:<autosummary>:1
msgid "Run analysis on experiment data."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`set_options "
"<pyQCat.analysis.library.CavityAnalysis.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:25:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`show_results "
"<pyQCat.analysis.library.CavityAnalysis.show_results>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:25:<autosummary>:1
msgid "Show analysis results"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.CavityAnalysis.rst:27
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_datas "
"<pyQCat.analysis.library.CavityAnalysis.analysis_datas>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis datas."
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`data_filter "
"<pyQCat.analysis.library.CavityAnalysis.data_filter>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid "The parameters provided for data filter functions."
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`drawer <pyQCat.analysis.library.CavityAnalysis.drawer>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid "A short-cut for curve drawer instance."
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_data "
"<pyQCat.analysis.library.CavityAnalysis.experiment_data>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid "Return the experiment data for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`has_child <pyQCat.analysis.library.CavityAnalysis.has_child>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis has_child value."
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.library.CavityAnalysis.options>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis options for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.library.CavityAnalysis.quality>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis data quality."
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`results <pyQCat.analysis.library.CavityAnalysis.results>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1:<autosummary>:1
msgid "Get the result datas."
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:1
msgid "Create the Analysis options, and set some fields."
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options
#: pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._default_options:4
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param:1
msgid "Guess initial fit parameters."
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param:4
msgid "Fit options filled with user provided guess and bounds."
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param:6
msgid "Formatted data collection to fit."
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param:8
msgid ""
":py:data:`~typing.Union`\\[:py:class:`~pyQCat.analysis.specification.FitOptions`,"
" "
":py:class:`~typing.List`\\[:py:class:`~pyQCat.analysis.specification.FitOptions`]]"
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._guess_fit_param:9
msgid "List of fit options that are passed to the fitter function."
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._extract_result:1
msgid "Extract cavity frequency from fitted data."
msgstr ""

#: of pyQCat.analysis.library.cavity_analysis.CavityAnalysis._extract_result:4
msgid "The basis for selecting data."
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.analysis.curve_analysis.CurveAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.analysis.library.CavityAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.CavityAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_analysis "
#~ "<pyQCat.analysis.library.CavityAnalysis.run_analysis>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_options "
#~ "<pyQCat.analysis.library.CavityAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_datas "
#~ "<pyQCat.analysis.library.CavityAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`data_filter "
#~ "<pyQCat.analysis.library.CavityAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`drawer <pyQCat.analysis.library.CavityAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_data "
#~ "<pyQCat.analysis.library.CavityAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`has_child "
#~ "<pyQCat.analysis.library.CavityAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`options <pyQCat.analysis.library.CavityAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`quality <pyQCat.analysis.library.CavityAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`results <pyQCat.analysis.library.CavityAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.analysis.curve_analysis.CurveAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.library.CavityAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.CavityAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_analysis "
#~ "<pyQCat.analysis.library.CavityAnalysis.run_analysis>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options "
#~ "<pyQCat.analysis.library.CavityAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_datas "
#~ "<pyQCat.analysis.library.CavityAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`data_filter "
#~ "<pyQCat.analysis.library.CavityAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid "The parameters provided for data filter funtions."
#~ msgstr ""

#~ msgid ":obj:`drawer <pyQCat.analysis.library.CavityAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_data "
#~ "<pyQCat.analysis.library.CavityAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ":obj:`has_child <pyQCat.analysis.library.CavityAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.library.CavityAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.library.CavityAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":obj:`results <pyQCat.analysis.library.CavityAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "Quality of fit outcome."
#~ msgstr ""

