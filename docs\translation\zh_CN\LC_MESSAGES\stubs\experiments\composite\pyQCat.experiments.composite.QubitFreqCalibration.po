# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:2
msgid "pyQCat.experiments.composite.QubitFreqCalibration"
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_freq_calibration.QubitFreqCalibration:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.experiments.composite.QubitFreqCalibration.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.composite.QubitFreqCalibration.component_experiment>`\\"
" \\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.composite.QubitFreqCalibration.from_experiment_context>`\\"
" \\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.composite.QubitFreqCalibration.get_qubit_str>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.composite.QubitFreqCalibration.options_table>`\\ "
"\\(\\[mode\\, detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid ""
":py:obj:`run <pyQCat.experiments.composite.QubitFreqCalibration.run>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
#: of
#: pyQCat.experiments.composite.qubit_freq_calibration.QubitFreqCalibration.run:1
msgid "Run composite experiment."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.composite.QubitFreqCalibration.set_analysis_options>`\\"
" \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.composite.QubitFreqCalibration.set_experiment_options>`\\"
" \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.composite.QubitFreqCalibration.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.composite.QubitFreqCalibration.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.QubitFreqCalibration.rst:32
msgid "Attributes"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid ""
":py:obj:`analysis "
"<pyQCat.experiments.composite.QubitFreqCalibration.analysis>`\\"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.composite.QubitFreqCalibration.analysis_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.composite.QubitFreqCalibration.child_experiment>`\\"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.composite.QubitFreqCalibration.experiment_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.composite.QubitFreqCalibration.run_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_freq_calibration.QubitFreqCalibration._default_experiment_options:1
msgid "Default kwarg options for experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_freq_calibration.QubitFreqCalibration._default_analysis_options
#: pyQCat.experiments.composite.qubit_freq_calibration.QubitFreqCalibration._default_experiment_options
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_freq_calibration.QubitFreqCalibration._default_analysis_options:4
#: pyQCat.experiments.composite.qubit_freq_calibration.QubitFreqCalibration._default_experiment_options:5
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_freq_calibration.QubitFreqCalibration._default_analysis_options:1
msgid "Default kwarg options for analysis."
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_freq_calibration.QubitFreqCalibration._check_options:1
msgid "Check Options."
msgstr ""

