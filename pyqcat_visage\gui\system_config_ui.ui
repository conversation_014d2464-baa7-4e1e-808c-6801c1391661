<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>566</width>
    <height>651</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>System Config</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout" stretch="7,1">
    <item>
     <widget class="QGroupBox" name="groupBox">
      <property name="title">
       <string>System Config</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignCenter</set>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>9</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QTreeViewConfig" name="tree_view_config">
         <property name="sortingEnabled">
          <bool>false</bool>
         </property>
         <property name="expandsOnDoubleClick">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="14,2,0,0">
      <item>
       <widget class="QLineEdit" name="file_edit">
        <property name="inputMask">
         <string/>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="placeholderText">
         <string>Load local conig file...</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="browse_button">
        <property name="text">
         <string>Import</string>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>_imgs/import.png</normaloff>_imgs/import.png</iconset>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="import_button">
        <property name="text">
         <string>Export</string>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>_imgs/save_as.png</normaloff>_imgs/save_as.png</iconset>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton">
        <property name="text">
         <string>Save</string>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>_imgs/ok.png</normaloff>_imgs/ok.png</iconset>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QTreeViewConfig</class>
   <extends>QTreeView</extends>
   <header>.widgets.config.tree_view_config</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>browse_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>import_config()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>268</x>
     <y>578</y>
    </hint>
    <hint type="destinationlabel">
     <x>322</x>
     <y>-11</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>import_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>export_config()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>355</x>
     <y>584</y>
    </hint>
    <hint type="destinationlabel">
     <x>491</x>
     <y>634</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>save_config()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>443</x>
     <y>584</y>
    </hint>
    <hint type="destinationlabel">
     <x>492</x>
     <y>575</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>export_config()</slot>
  <slot>save_config()</slot>
  <slot>import_config()</slot>
 </slots>
</ui>
