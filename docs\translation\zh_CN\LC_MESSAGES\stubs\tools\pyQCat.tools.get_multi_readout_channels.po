# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.get_multi_readout_channels.rst:2
msgid "pyQCat.tools.get\\_multi\\_readout\\_channels"
msgstr ""

#: of pyQCat.tools.utilities.get_multi_readout_channels:1
msgid "Get multiple qubits' readout channel."
msgstr ""

#: of pyQCat.tools.utilities.get_multi_readout_channels
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.utilities.get_multi_readout_channels:4
msgid "List of Qubit object."
msgstr ""

#: of pyQCat.tools.utilities.get_multi_readout_channels
msgid "Returns"
msgstr ""

#: of pyQCat.tools.utilities.get_multi_readout_channels:7
msgid "List of int, readout channel list."
msgstr ""

#: of pyQCat.tools.utilities.get_multi_readout_channels
msgid "Return type"
msgstr ""

