# -*- coding: utf-8 -*-

# This code is part of pyqcat.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/05/17
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

import dataclasses
import inspect
from typing import Any, Callable, Dict, Iterable, List, Optional, Tuple, Union

import numpy as np
from loguru import logger

from ..errors import AnalysisFitDataError, AnalysisFitValError
from ..structures import QDict
from .algorithms.iqprobability import IQdiscriminator
from .quality import GoodnessofFit, SingleShotQuality


@dataclasses.dataclass(frozen=True)
class FitModel:
    fit_func: Callable
    name: Optional[str] = None
    plot_color: str = "black"
    plot_symbol: str = "o"
    model_description: Optional[str] = None
    signature: Tuple[str, ...] = dataclasses.field(init=False)

    __annotations__ = {
        "fit_func": Callable,
        "name": Optional[str],
        "plot_color": str,
        "plot_symbol": str,
        "model_description": Optional[str],
        "signature": Tuple[str, ...],
    }

    def __post_init__(self):
        """Parse the fit function signature to extract the names of the variables.

        Fit functions take arguments F(x, p0, p1, p2, ...) thus the first value should be excluded.
        """
        signature = list(inspect.signature(self.fit_func).parameters.keys())
        fitparams = tuple(signature[1:])

        # Note that this dataclass is frozen
        object.__setattr__(self, "signature", fitparams)


@dataclasses.dataclass
class FitData:
    """A dataclass to store the outcome of the fitting.

    Attributes:
        popt: List of optimal parameter values with uncertainties if available.
        popt_keys: List of parameter names being fit.
        variance: The value of rmse.
        y_fit: Y-values got form the fitter.
    """

    popt: List[float]
    popt_keys: List[str]
    variance: float
    y_fit: np.ndarray
    goodness_of_fit: Optional[GoodnessofFit] = None

    __annotations__ = {
        "popt": List[float],
        "popt_keys": List[str],
        "variance": float,
        "y_fit": np.ndarray,
        "goodness_of_fit": GoodnessofFit,
    }

    def format(self, num: int = 8):
        for i, value in enumerate(self.popt):
            self.popt[i] = round(value, num)
        self.variance = round(self.variance, num)

    def fitval(self, key: str) -> float:
        """A helper method to get fit value object from parameter key name.

        Args:
            key: Name of parameters to extract.

        Returns:
            A float object which functions as a standard Python float object
            but with automatic error propagation.

        Raises:
            AnalysisFieldError: When specified parameter is not defined.
        """
        try:
            index = self.popt_keys.index(key)
            return self.popt[index]
        except ValueError as ex:
            raise AnalysisFitValError(key, self.popt_keys) from ex

    def to_dict(self):
        res = {}
        for index, key in enumerate(self.popt_keys):
            res[key] = self.popt[index]
        return res


@dataclasses.dataclass
class CurveAnalysisData:
    """A dataclass to store the process of the analysis.

    Attributes:
        x: X-values provided to the analysis.
        y: Y-values provided to the analysis.
        fit_data: A dataclass to store the outcome of the fitting.

    """

    x: np.ndarray = None
    y: np.ndarray = None
    fit_data: FitData = None

    __annotations__ = {"x": np.ndarray, "y": np.ndarray, "fit_data": FitData}


@dataclasses.dataclass
class SingleShotAnalysisData:
    """A dataclass to store the process of the analysis.

    Attributes:
        source_data: some bit IQ data, np.ndarray([I0, Q0, I1, Q1]).
        discriminator: IQdiscriminator object, after train.
        quality: SingleShotQuality object.

    """

    source_data: np.ndarray = None
    discriminator: IQdiscriminator = None
    quality: SingleShotQuality = None

    __annotations__ = {
        "source_data": np.ndarray,
        "discriminator": IQdiscriminator,
        "quality": SingleShotQuality,
    }


@dataclasses.dataclass
class TomographyAnalysis:
    # todo
    pass


@dataclasses.dataclass
class AnalysisResult:
    """Dataclass for experiment analysis results

    Attributes:
        name: Human-readable parameter name shown in the analysis result and in the figure.
        unit: Optional. Physical unit of this parameter if applicable.
        value: Optional.The value of result data.
    """

    name: str = ""
    unit: Optional[str] = None
    value: Any = None
    extra: Dict[str, Any] = dataclasses.field(
        default_factory=QDict, hash=False, compare=False
    )
    __annotations__ = {
        "name": str,
        "unit": Optional[str],
        "value": Any,
        "extra": Dict[str, Any],
    }

    def __str__(self):
        if self.unit:
            outcome = f"{self.name}={self.value}{self.unit} "
        else:
            outcome = f"{self.name}={self.value} "
        if self.extra and isinstance(self.extra, (dict, QDict)) and len(self.extra) > 2:
            outcome += f" extra({self.extra.__repr__()})"
        return outcome

    def __iter__(self):
        """Return iterator of data fields (attr, value)"""
        return iter(
            (field.name, getattr(self, field.name))
            for field in dataclasses.fields(self)
        )
        
    def __json_encode__(self):

        try:
            base_value = format_dict_for_json(self.value)
        except Exception as e:
            logger.error(e)
            base_value= str(self.value)

        return dict(
            name=self.name,
            unit=self.unit,
            value=base_value,
            extra=format_dict_for_json(self.extra)
        )


@dataclasses.dataclass
class ParameterRepr:
    """Detailed description of fitting parameter.

    Attributes:
        name: Original name of the fit parameter being defined in the fit model.
        repr: Optional. Human-readable parameter name shown in the analysis result and in the figure.
        unit: Optional. Physical unit of this parameter if applicable.
    """

    # Fitter argument name
    name: str

    # Unicode representation
    repr: Optional[str] = None

    # Unit
    unit: Optional[str] = None

    param_path: Optional[str] = None

    param_name: Optional[str] = None

    __annotations__ = {
        "name": str,
        "repr": Optional[str],
        "unit": Optional[str],
        "param_path": Optional[str],
        "param_name": Optional[str],
    }

    def __repr__(self):
        return f"{self.name}(repr: {self.repr}, unit: {self.unit})"


class OptionsDict(dict):
    """General extended dictionary for fit options.

    This dictionary provides several extra features.

    - A value setting method which validates the dict key and value.
    - Dictionary keys are limited to those specified in the constructor as ``parameters``.
    """

    def __init__(
        self,
        parameters: List[str],
        defaults: Optional[Union[Iterable[Any], Dict[str, Any]]] = None,
    ):
        """Create new dictionary.

        Args:
            parameters: List of parameter names used in the fit model.
            defaults: Default values.

        Raises:
            AnalysisDataError: When defaults is provided as array-like but the number of
                element doesn't match with the number of fit parameters.
        """
        if defaults is not None:
            if not isinstance(defaults, dict):
                if len(defaults) != len(parameters):
                    raise AnalysisFitDataError(
                        f"Default parameter {defaults} is provided with array-like "
                        "but the number of element doesn't match. "
                        f"This fit requires {len(parameters)} parameters."
                    )
                defaults = dict(zip(parameters, defaults))

            full_options = {p: self.format(defaults.get(p, None)) for p in parameters}
        else:
            full_options = {p: None for p in parameters}

        super().__init__(**full_options)

    def __setitem__(self, key, value):
        """Set value with validations.

        Raises:
            AnalysisDataError: When key is not previously defined.
        """
        if key not in self:
            raise AnalysisFitDataError(
                f"Parameter {key} is not defined in this fit model."
            )
        super().__setitem__(key, self.format(value))

    def __hash__(self):
        return hash(tuple(sorted(self.items())))

    def set_if_empty(self, **kwargs):
        """Set value to the dictionary if not assigned.

        Args:
              kwargs: Key and new value to assign.
        """
        for key, value in kwargs.items():
            if self.get(key) is None:
                self.__setitem__(key, value)

    @staticmethod
    def format(value: Any) -> Any:
        """Format dictionary value.

        Subcasses may override this method to provide their own validation.

        Args:
            value: New value to assign.

        Returns:
            Formatted value.
        """
        return value


class InitialGuesses(OptionsDict):
    """Dictionary providing a float validation for initial guesses."""

    @staticmethod
    def format(value: Any) -> Optional[float]:
        """Validate that value is float a float or None.

        Args:
            value: New value to assign.

        Returns:
            Formatted value.

        Raises:
            AnalysisDataError: When value is not a float or None.
        """
        if value is None:
            return None

        try:
            return float(value)
        except (TypeError, ValueError) as ex:
            raise AnalysisFitDataError(
                f"Input value {value} is not valid initial guess. "
            ) from ex


class Boundaries(OptionsDict):
    """Dictionary providing a validation for boundaries."""

    @staticmethod
    def format(value: Any) -> Optional[Tuple[float, float]]:
        """Validate if value is a min-max value tuple.

        Args:
            value: New value to assign.

        Returns:
            Formatted value.

        Raises:
            AnalysisDataError: When value is invalid format.
        """
        if value is None:
            return None

        try:
            minv, maxv = value
            if minv >= maxv:
                raise AnalysisFitDataError(
                    f"The first value is greater than the second value {minv} >= {maxv}."
                )
            return float(minv), float(maxv)
        except (TypeError, ValueError) as ex:
            raise AnalysisFitDataError(
                f"Input boundary {value} is not a min-max value tuple."
            ) from ex


# pylint: disable=invalid-name
class FitOptions:
    """Collection of fitting options.

    This class is initialized with a list of parameter names used in the fit model
    and corresponding default values provided by users.

    This class is hashable, and generates fitter keyword arguments.
    """

    def __init__(
        self,
        parameters: Union[List, Tuple],
        default_p0: Optional[Union[Iterable[float], Dict[str, float]]] = None,
        default_bounds: Optional[Union[Iterable[Tuple], Dict[str, Tuple]]] = None,
        **extra,
    ):
        # These are private members so that user cannot directly override values
        # without implicitly implemented validation logic. No setter will be provided.
        self.__p0 = InitialGuesses(list(parameters), default_p0)
        self.__bounds = Boundaries(list(parameters), default_bounds)
        self.__extra = extra

    def __hash__(self):
        return hash((self.__p0, self.__bounds, tuple(sorted(self.__extra.items()))))

    def __eq__(self, other):
        if isinstance(other, FitOptions):
            checks = [
                self.__p0 == other.__p0,
                self.__bounds == other.__bounds,
                self.__extra == other.__extra,
            ]
            return all(checks)
        return False

    def add_extra_options(self, **kwargs):
        """Add more fitter options."""
        self.__extra.update(kwargs)

    def copy(self):
        """Create copy of this option."""
        return FitOptions(
            parameters=list(self.__p0.keys()),
            default_p0=dict(self.__p0),
            default_bounds=dict(self.__bounds),
            **self.__extra,
        )

    @property
    def p0(self) -> InitialGuesses:
        """Return initial guess dictionary."""
        return self.__p0

    @property
    def bounds(self) -> Boundaries:
        """Return bounds dictionary."""
        return self.__bounds

    @property
    def options(self):
        """Generate keyword arguments of the curve fitter."""
        bounds = {
            k: v if v is not None else (-np.inf, np.inf)
            for k, v in self.__bounds.items()
        }
        return {"p0": dict(self.__p0), "bounds": bounds, **self.__extra}


class TitleOptions: ...


def format_dict_for_json(pv):
    if hasattr(pv, "to_dict"):
        rv = format_dict_for_json(pv.to_dict())
    elif isinstance(pv, np.ndarray):
        rv = [format_dict_for_json(_v) for _v in pv.tolist()]
    elif isinstance(pv, list):
        rv = [format_dict_for_json(_v) for _v in pv]
    elif isinstance(pv, (int, float)):
        return pv
    elif isinstance(pv, dict):
        rv = {}
        for _k, _v in pv.items():
            rv[_k] = format_dict_for_json(_v)
    else:
        rv = str(pv)
    return rv
