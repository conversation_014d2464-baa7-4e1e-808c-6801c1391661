# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/02/14
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

"""
Leakage Once Experiment.
"""

import json
from copy import deepcopy
from typing import List

import numpy as np

from ....analysis import LeakagePreAnalysis
from ....log import pyqlog
from ....parameters import options_wrapper
from ....structures import MetaData, Options
from ....types import ExperimentRunMode, QualityDescribe, StandardContext
from ...composite_experiment import CompositeExperiment
from ...single import LeakageOnce


@options_wrapper
class LeakagePre(CompositeExperiment):
    """LeakageOnce scan default name z amp list."""

    _sub_experiment_class = LeakageOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("iteration", int)
        options.set_validator("adjust_num", int)

        options.iteration = 10
        options.adjust_num = 5
        options.support_context = [StandardContext.CGC]
        options.adjust_num = 5
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.count = 3
        options.z_amp_list = None
        options.quality_value = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options.

        Analysis Options:
            data_key (List[str]): List of mark select data.

        """
        options = super()._default_analysis_options()

        # options.data_key = None
        options.quality_bounds = [0.8, 0.6, 0.5]
        options.perfect = False

        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        self.set_analysis_options(result_name=self.qubit_pair.name)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "scan_name": self.experiment_options.scan_name,
            "cz_num": self.experiment_options.cz_num,
        }
        metadata.process_meta = {"x_data": self.run_options.x_data}
        return metadata

    def _set_qc_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "fit_qc":
                result.extra["path"] = "QubitPair.metadata.std.process.fit_qc"
            elif key == "qc_min":
                des = "amp"
                if result.value > 1:
                    result.value = round(result.value, 3)
                    des = "freq"
                result.extra["path"] = (
                    f"QubitPair.metadata.std.cz.params.{self.qubit_pair.qc}.{des}"
                )

    def _adjust_z_amp(self, exp):
        peak_points = exp.analysis.results.peak_points.value or []
        data_key = exp.analysis.options.data_key
        y_data = exp.analysis.experiment_data.y_data.get(data_key[0])
        z_amp_list = exp.analysis.experiment_data.x_data
        if isinstance(z_amp_list, List):
            z_amp_list = np.array(z_amp_list)
        points = len(z_amp_list)
        if len(peak_points) > 1:
            self.set_run_options(count=len(peak_points))
            sorted_peak_points = sorted(peak_points, key=lambda point: point.x)
            if z_amp_list[0] > 10:
                point0 = sorted_peak_points[-1]
                point1 = sorted_peak_points[-2]
                left_idx = np.where(z_amp_list == point1.x)[0]
                right_idx = np.where(z_amp_list == point0.x)[0]
                left_y = y_data[left_idx[-1]: right_idx[-1]]
                right_y = y_data[right_idx[-1]:]
                left_y_idx = np.where(left_y == min(left_y))[0]
                right_y_idx = np.where(right_y == min(right_y))[0]

                z_start_idx = left_idx[-1] + left_y_idx[-1]
                z_end_idx = right_idx[-1] + right_y_idx[-1]

            elif z_amp_list[0] >= 0:
                point0 = sorted_peak_points[0]
                point1 = sorted_peak_points[1]
                left_idx = np.where(z_amp_list == point0.x)[0]
                right_idx = np.where(z_amp_list == point1.x)[0]

                right_y = y_data[left_idx[-1] : right_idx[-1]]
                left_y = y_data[: left_idx[-1]]
                left_y_idx = np.where(left_y == min(left_y))[0]
                right_y_idx = np.where(right_y == min(right_y))[0]
                z_start_idx = left_y_idx[-1]
                z_end_idx = left_idx[-1] + right_y_idx[-1]
            else:
                point0 = sorted_peak_points[-1]
                point1 = sorted_peak_points[-2]
                if z_amp_list[0] < z_amp_list[-1]:
                    left_idx = np.where(z_amp_list == point1.x)[0]
                    right_idx = np.where(z_amp_list == point0.x)[0]
                else:
                    left_idx = np.where(z_amp_list == point0.x)[0]
                    right_idx = np.where(z_amp_list == point1.x)[0]

                left_y = y_data[left_idx[-1] : right_idx[-1]]
                right_y = y_data[right_idx[-1] :]
                left_y_idx = np.where(left_y == min(left_y))[0]
                right_y_idx = np.where(right_y == min(right_y))[0]

                z_start_idx = left_idx[-1] + left_y_idx[-1]
                z_end_idx = right_idx[-1] + right_y_idx[-1]

        elif len(peak_points) > 0:
            point0 = peak_points[0]
            tp_idx = np.where(z_amp_list == point0.x)[0]
            left_y = y_data[: tp_idx[-1]]
            right_y = y_data[tp_idx[-1] : -1]
            left_y_idx = np.where(left_y == min(left_y))[0]
            right_y_idx = np.where(right_y == min(right_y))[0]
            z_start_idx = left_y_idx[-1]
            z_end_idx = tp_idx[-1] + right_y_idx[-1]
            self.set_run_options(count=0)
        else:
            adjust_num = self.experiment_options.adjust_num
            first_item = z_amp_list[0]
            common_diff = z_amp_list[1] - first_item
            if isinstance(z_amp_list, np.ndarray):
                z_amp_list = z_amp_list.tolist()
            z_amp_list = (
                [first_item - i * common_diff for i in range(adjust_num, 0, -1)]
                + z_amp_list
                + [z_amp_list[-1] + i * common_diff for i in range(1, adjust_num + 1)]
            )
            z_start_idx = 0
            z_end_idx = -1

        start = z_amp_list[z_start_idx]
        end = z_amp_list[z_end_idx]
        pyqlog.info(f"z_start_idx={z_start_idx}, z_end_idx={z_end_idx}")
        pyqlog.info(f"z_start={start}, z_end={end}")
        new_z_amp_list = np.linspace(start, end, points).tolist()

        self.set_run_options(z_amp_list=new_z_amp_list)

    async def _sync_composite_run(self):
        iteration_times = 0
        iteration = self.experiment_options.iteration

        gate_params = self.qubit_pair.gate_params(
            self.experiment_options.child_exp_options.label
        )
        json_str = json.dumps(gate_params, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name="gate_params", prefix=".json")

        for i in range(iteration):
            z_amp_list = self.run_options.z_amp_list
            exp = deepcopy(self.child_experiment)
            tail_name = f"iteration_times={iteration_times}"
            exp.set_parent_file(self, tail_name, iteration_times, len(range(iteration)))
            if z_amp_list is not None:
                if z_amp_list[0] > 10:
                    exp.set_experiment_options(freq_list=z_amp_list,
                                           )
                else:
                    exp.set_experiment_options(z_amp_list=z_amp_list,
                                               )
            self._check_simulator_data(exp, iteration_times)
            await exp.run_experiment()
            try:
                self._adjust_z_amp(exp)
            except Exception as e:
                pyqlog.warning(f"Unable to find the range of z_amp, error msg:{e}")
            if exp.analysis.quality:
                self.set_run_options(quality_value=exp.analysis.quality.descriptor)
            if exp.experiment_options.freq_list:
                exp.analysis.provide_for_parent.update(
                    {"fit_qc": exp.experiment_options.freq_list,
                     "qc_min": exp.analysis.results.fit_zc.value}
                )
            else:
                exp.analysis.provide_for_parent.update(
                    {"fit_qc": exp.experiment_options.z_amp_list,
                     "qc_min": exp.analysis.results.fit_zc.value}
                )
            self._experiments.append(exp)
            iteration_times += 1
            if iteration_times > iteration:
                break
            if (
                self.run_options.quality_value == QualityDescribe.perfect
                and self.run_options.count == 0
            ):
                self.set_analysis_options(perfect=True)
                break
        self.set_analysis_options(is_plot=False)
        self._run_analysis(
            x_data=[i for i in range(iteration_times)],
            analysis_class=LeakagePreAnalysis,
        )
        fit_qc = self.analysis.results.fit_qc.value
        self.file.save_data(fit_qc, name="fit_qc")
        if self.run_options.quality_value == QualityDescribe.perfect:
            self._set_qc_result_path()
