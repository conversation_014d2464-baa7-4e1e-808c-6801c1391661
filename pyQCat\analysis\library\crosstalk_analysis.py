# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/07
# __author:       HanQing Shi

import numpy as np
import matplotlib.pyplot as plt

from .crosstalk_fix_f_analysis import CrosstalkLinearAnalysis
from ..curve_analysis import CurveAnalysis
from ..fit.fit_models import linear_func
from ..specification import FitModel, ParameterRepr
from ...structures import Options


class CrosstalkAnalysis(CurveAnalysis):
    """An analysis class for getting crosstalk coefficients."""
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()
        # no bounds fitting.
        options.curve_fit_extra.update({
            "ftol": 1.49012e-8,
            "xtol": 1.49012e-8
        })

        options.fit_model = FitModel(
            fit_func=linear_func)

        options.p0 = {"k": 0, "b": 0}

        options.result_parameters = [ParameterRepr("k", "coefficient")]

        return options


class ZCrossDelayAnalysis(CrosstalkAnalysis):
    """Z Cross Delay analysis class."""

    def _pre_operation(self):
        """Prepare one more canvas axis."""
        sub_title = self.options.sub_title
        y_label = self.options.y_label
        new_sub_titles = ["Equivalent Target Zamp"]
        new_y_labels = list(self.experiment_data.y_data.keys())
        if isinstance(sub_title, list):
            new_sub_titles.extend(sub_title[:2])
        elif isinstance(sub_title, str):
            new_sub_titles.extend([sub_title, sub_title])

        if isinstance(y_label, list):
            new_y_labels.extend(y_label[:2])
        elif isinstance(y_label, str):
            new_y_labels.extend([y_label, y_label])

        self.set_options(
            y_label=new_y_labels,
            sub_title=new_sub_titles,
            subplots=(3, 1),
            figsize=(12, 12),
        )

    def _visualization(self):
        super()._visualization()
        base_ax_index = len(self.experiment_data.y_data.keys())
        analysis_params = self.experiment_data.metadata.process_meta.get("analysis_params")
        if analysis_params:
            self.drawer.draw_dynamic_data(
                np.array(analysis_params.bq_ac_list),
                np.asarray(analysis_params.tq_ac_array, dtype=object),
                np.asarray(analysis_params.z0_array, dtype=object),
                ax_index=base_ax_index,
            )
            self.drawer.draw_dynamic_data(
                np.array(analysis_params.bq_ac_list),
                np.asarray(analysis_params.tq_ac_array, dtype=object),
                np.asarray(analysis_params.z1_array, dtype=object),
                ax_index=base_ax_index + 1,
            )

            # Finalize plot.
            self.drawer.format_canvas()

    def run_analysis(self):
        """Run analysis on experiment data."""
        self._pre_operation()

        super().run_analysis()


class ZCrossSpinechoAnalysis(CrosstalkAnalysis):
    """Z Cross Delay analysis class."""

    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.pcolormesh_options = {
            "shading": "nearest",
            "cmap": plt.cm.get_cmap("viridis"),
        }

        return options

    def _pre_operation(self):
        """Prepare one more canvas axis."""
        sub_title = self.options.sub_title
        y_label = self.options.y_label
        new_sub_titles = ["Equivalent Target Zamp"]
        new_y_labels = list(self.experiment_data.y_data.keys())
        if isinstance(sub_title, list):
            new_sub_titles.extend(sub_title[:2])
        elif isinstance(sub_title, str):
            new_sub_titles.extend([sub_title, sub_title])

        if isinstance(y_label, list):
            new_y_labels.extend(y_label[:2])
        elif isinstance(y_label, str):
            new_y_labels.extend([y_label, y_label])

        self.set_options(
            y_label=new_y_labels,
            sub_title=new_sub_titles,
            subplots=(3, 1),
            figsize=(12, 12),
        )

    def _visualization(self):
        super()._visualization()
        base_ax_index = len(self.experiment_data.y_data.keys())
        analysis_params = self.experiment_data.metadata.process_meta.get("analysis_params")
        self.drawer.draw_color_map(
            np.array(analysis_params.bq_ac_list),
            np.asarray(analysis_params.tq_ac_array)[0],
            np.asarray(analysis_params.z0_array).T,
            ax_index=base_ax_index,
            **self.options.pcolormesh_options,

        )
        self.drawer.draw_color_map(
            np.array(analysis_params.bq_ac_list),
            np.asarray(analysis_params.tq_ac_array)[0],
            np.asarray(analysis_params.z1_array).T,
            ax_index=base_ax_index+1,
            **self.options.pcolormesh_options,
        )
            # Finalize plot.
        self.drawer.format_canvas()

    def run_analysis(self):
        """Run analysis on experiment data."""
        self._pre_operation()

        super().run_analysis()


class ZCrossSEPTMAnalysis(CrosstalkLinearAnalysis):
    def _visualization(self):
        self.drawer.set_options(title=self._description())

        if self.has_child is True:
            analysis_data = self.analysis_datas.bias_v
            self.drawer.draw_scatter_point(
                analysis_data.x,
                analysis_data.y,
                ax_index=0,
            )
            self.drawer.draw_fit_line(
                analysis_data.x,
                analysis_data.fit_data.y_fit,
                ax_index=0,
            )
        self.drawer.format_canvas()
