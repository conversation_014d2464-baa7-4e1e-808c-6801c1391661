# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:2
msgid "pyQCat.experiments.single.StateTomography"
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:1
msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:1
msgid "Quantum state tomography experiment."
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:9
msgid "# section: overview"
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:4
msgid ""
"Quantum state tomography (QST) is a method for experimentally "
"reconstructing the quantum state from measurement data."
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:7
msgid ""
"A QST experiment measures the state prepared by quantum circuit in "
"different measurement bases and post-processes the measurement data to "
"reconstruct the state."
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:12
msgid "# section: analysis_ref"
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:12
msgid ":py:class:`StateTomographyAnalysis`"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.__init__:1
msgid "Create a new experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.single.StateTomography.__init__>`\\"
" \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`acquire_pulse "
"<pyQCat.experiments.single.StateTomography.acquire_pulse>`\\ "
"\\(acq\\_pulse\\[\\, qubit\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Set measure pulse."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`cal_fidelity "
"<pyQCat.experiments.single.StateTomography.cal_fidelity>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Calculate the fidelity matrix."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`experiment_info "
"<pyQCat.experiments.single.StateTomography.experiment_info>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Show experiment informations."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.single.StateTomography.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.single.StateTomography.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`jupyter_schedule "
"<pyQCat.experiments.single.StateTomography.jupyter_schedule>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Show experiment pulses's schedules in Jupyter Notebook."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.single.StateTomography.options_table>`\\ "
"\\(\\[mode\\, detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`play_pulse "
"<pyQCat.experiments.single.StateTomography.play_pulse>`\\ \\(name\\, "
"base\\_qubit\\, pulse\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Play pulse for XY control and Z flux control."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`plot_schedule "
"<pyQCat.experiments.single.StateTomography.plot_schedule>`\\ "
"\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
"Plot the pulse schedule which is very important to confirm experiment "
"logical correction."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.single.StateTomography.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
#: of pyQCat.experiments.single.state_tomography.StateTomography.run:1
msgid "Run quantum state tomography experiment and perform analysis."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.single.StateTomography.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.single.StateTomography.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_multiple_IF "
"<pyQCat.experiments.single.StateTomography.set_multiple_IF>`\\ "
"\\(\\*IF\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Used for multiple qubits experiment, set mutiple IF."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_multiple_index "
"<pyQCat.experiments.single.StateTomography.set_multiple_index>`\\ "
"\\(\\*args\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
"Used for multiple qubits experiment, set mutiple index. :type args: "
":py:class:`list` :param args: Setting the start and end values :type "
"channel: :py:class:`int` :param channel: channel settings Usage::     "
">>>experiment = TopExperiment()     >>>experiment.set_multiple_index([0, "
"1500], [100, 900], channel=1)."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.single.StateTomography.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.single.StateTomography.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_sweep_order "
"<pyQCat.experiments.single.StateTomography.set_sweep_order>`\\ "
"\\(order\\_list\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid "Old interface."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:39:<autosummary>:1
msgid ""
":py:obj:`sweep_readout_trigger_delay "
"<pyQCat.experiments.single.StateTomography.sweep_readout_trigger_delay>`\\"
" \\(channel\\, sweep\\_delay\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.StateTomography.rst:41
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.single.StateTomography.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.single.StateTomography.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.single.StateTomography.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.single.StateTomography.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:1
msgid "Default kwarg options for (QST) experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:13
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:4
msgid ""
"pre_gate (List, optional): Initial gate state for performing QST. "
"qubit_nums (int): Qubit numbers. base_gate (List): QST aiming at "
"projection direction,"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:7
msgid ""
"I gate means Z-axis projection measurement, X/2 gate means Y-axis "
"projection measurement, Y/2 gate means X-axis projection measurement."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:10
msgid ""
"gate_map (Dict): Basic Gate Collection, for details, see "
"`pyQCat.gate.GateCollection` class. is_dynamic (bool): When collection "
"data, is or not showing result data dynamically. show_results (bool): The"
" result information is not printed by default."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_analysis_options
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options
#: pyQCat.experiments.single.state_tomography.StateTomography._default_run_options
#: pyQCat.experiments.single.state_tomography.StateTomography._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_analysis_options:8
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:15
#: pyQCat.experiments.single.state_tomography.StateTomography._default_run_options:11
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_run_options:1
msgid "Set run experiment operate options."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_run_options:9
msgid "Options:"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_run_options:4
msgid ""
"QL: Qubit object, which bit name is ql_name. QH: Qubit object, which bit "
"name is qh_name. parking_qubits (List): List of BaseQubit,"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_run_options:7
msgid "when name in parking_bits."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_analysis_options:1
msgid "Default kwarg options for (QST) analysis."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_analysis_options:6
msgid "Analysis options:"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_analysis_options:4
msgid ""
"use_mle (bool): Whether to compute the density matrix using maximum "
"likelihood estimation. is_plot (bool): Qubit numbers."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._set_xy_pulses:1
msgid "Set XY pulse."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._set_z_pulses:1
msgid "Set z pulse."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._set_measure_pulses:1
msgid "Set readout pulse."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._check_options:1
msgid "Check Options."
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography._metadata:1
msgid "Return experiment metadata for ExperimentData."
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography._metadata:3
msgid ""
"Subclasses can override this method to add custom experiment metadata to "
"the returned experiment result data."
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography._metadata:7
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

