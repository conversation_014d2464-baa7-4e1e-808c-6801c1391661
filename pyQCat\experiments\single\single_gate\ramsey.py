# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/23
# __author:       <PERSON><PERSON><PERSON> Shi
"""Ramsey experiment for calibrating qubit frequency."""

import copy
from typing import Dict, List, Optional, Union

import numpy as np

from ....analysis.algorithms import IQdiscriminator
from ....analysis.library import RamseyAnalysis, RamseyEGAnalysis, RamseySegmAnalysis, RamseyZampAnalysis
from ....errors import ExperimentOptionsError
from ....executor import ExperimentContext
from ....instrument import Instrument
from ....log import pyqlog
from ....parameters import options_wrapper
from ....pulse import Constant, PulseCorrection
from ....pulse.pulse_function import half_pi_pulse, pi_pulse, zero_pulse
from ....qaio_property import QAIO
from ....qubit import <PERSON>Qubit, BaseQubitsType, Co<PERSON><PERSON>, Qubit
from ....structures import MetaData, Options
from ....tools import (
    cz_flow_options_adapter,
    freq_to_amp,
    qarange,
)
from ....tools.utilities import ac_spectrum_transform
from ...coupler_experiment_v1 import CouplerBaseExperimentV1 as CouplerBaseExperiment
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


@options_wrapper
class Ramsey(TopExperiment):
    """Ramsey experiment to measure the frequency of a qubit."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            delays (List, np.ndarray): The list of delays that will
                                           be scanned in the experiment,
                                           in nanoseconds.
            fringe (float): A frequency shift in Hz that will be applied
                            by means of a virtual Z rotation to increase
                            the frequency of the measured oscillation.
            z_amp (float): A ac pulse amplitude.
        """
        options = super()._default_experiment_options()

        options.set_validator("delays", list, limit_null=True)
        options.set_validator("fringe", float)
        options.set_validator("z_amp", (-1, 1, 2))

        options.delays = qarange(0, 200, 5)
        options.fringe = 25  # MHz
        options.z_amp = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("factor", float)
        options.set_validator("fit_type", ["osc", "segm", "eg"])

        options.fit_type = "osc"
        options.factor = 3.5

        options.data_key = None
        options.quality_bounds = [0.98, 0.93, 0.81]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options.

        Options:
            analysis_class: Ramsey Analysis class.

        """
        options = super()._default_run_options()

        options.analysis_class = None

        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        if self.discriminator is not None:
            data_type = "I_Q"
        else:
            data_type = "amp_phase"

        if self.experiment_options.z_amp is not None:
            fig_size = (20, 11)
        else:
            fig_size = (12, 8)

        self.set_experiment_options(data_type=data_type)
        self.set_analysis_options(figsize=fig_size)

        # Normal T2Ramsey use define Analysis Class.
        fit_type = self.analysis_options.fit_type

        if not self.run_options.analysis_class:
            
            if self.experiment_options.z_amp is not None:
                new_anal_class = RamseyZampAnalysis

            elif fit_type == "segm":
                new_anal_class = RamseySegmAnalysis
            elif fit_type == "eg":
                new_anal_class = RamseyEGAnalysis
            else:
                new_anal_class = RamseyAnalysis
            self.set_run_options(analysis_class=new_anal_class)

        # Shq 2024/04/29
        # for async mode.
        self.set_run_options(x_data=self.experiment_options.delays)

    # Shq 2024/04/29
    # for async mode.
    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        xy_pulse_list = Ramsey.get_xy_pulse(
            builder.qubit,
            builder.experiment_options.delays,
            builder.experiment_options.fringe,
        )
        builder.play_pulse("XY", builder.qubit, xy_pulse_list)

    # Shq 2024/04/29
    # for async mode.
    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""
        if builder.experiment_options.z_amp is not None:
            z_pulse_list = Ramsey.get_z_pulse(
                builder.qubit,
                builder.experiment_options.delays,
                builder.experiment_options.z_amp,
            )
            builder.play_pulse("Z", builder.qubit, z_pulse_list)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"vol": (self.qubit.dc, "v")}
        z_amp = self.experiment_options.z_amp
        if z_amp is not None:
            metadata.draw_meta["z_amp"] = (z_amp, "v")
        return metadata

    # Shq 2024/04/29
    # for async mode.
    @staticmethod
    def update_instrument(builder):
        sweep_delay = builder._pulse_time_list[: len(builder.experiment_options.delays)]
        builder.sweep_readout_trigger_delay(builder.qubit.readout_channel, sweep_delay)

    @staticmethod
    def get_xy_pulse_pre(qubit, delays: List, fringe: Union[int, float]):
        """Get XY line wave"""
        sum_delay = delays[-1]
        pulse_list = []
        for delay in delays:
            front_delay = Constant(sum_delay - delay, 0, "XY")
            front_drag = half_pi_pulse(qubit)
            center_delay = Constant(delay, 0, "XY")
            rear_drag = copy.deepcopy(front_drag)

            ramsey_pulse = (
                front_delay() + front_drag() + center_delay() + rear_drag(phase=2 * np.pi * fringe * 1e6 / 1e9 * delay)
            )
            ramsey_pulse.bit = qubit.bit
            ramsey_pulse.sweep = "sweep delay"

            pulse_list.append(ramsey_pulse)

        return pulse_list

    @staticmethod
    def get_xy_pulse(qubit, delays: List, fringe: Union[int, float]):
        """Get XY line wave."""
        pulse_list = []
        for delay in delays:
            front_drag = half_pi_pulse(qubit)
            center_delay = Constant(delay, 0, "XY")
            rear_drag = copy.deepcopy(front_drag)

            ramsey_pulse = front_drag() + center_delay() + rear_drag(phase=2 * np.pi * fringe * 1e6 / 1e9 * delay)
            ramsey_pulse.bit = qubit.bit
            ramsey_pulse.sweep = "sweep delay"

            pulse_list.append(ramsey_pulse)

        return pulse_list

    @staticmethod
    def get_z_pulse_pre(
        qubit,
        delays: List,
        z_amp: Union[float, int],
        drag_time: Optional[Union[float, int]] = None,
    ):
        """Get Z line wave.

        The Z line in the middle of the two half pi pulses plus add input amp.
        """
        sum_delay = delays[-1]
        z_pulse_list = []
        if drag_time is None:
            drag = half_pi_pulse(qubit)
            drag_time = drag().width

        for delay in delays:
            front_delay = Constant(sum_delay - delay, 0)
            front_constant = Constant(drag_time, 0)
            center_delay = Constant(delay, z_amp)
            rear_constant = Constant(drag_time, 0)
            ramsey_z_pulse = front_delay() + front_constant() + center_delay() + rear_constant()
            ramsey_z_pulse.bit = qubit.name
            ramsey_z_pulse.sweep = "sweep delay"
            z_pulse_list.append(ramsey_z_pulse)

        return z_pulse_list

    @staticmethod
    def get_z_pulse(
        qubit,
        delays: List,
        z_amp: Union[float, int],
        drag_time: Optional[Union[int, float]] = None,
    ):
        """Get Z line wave"""
        z_pulse_list = []
        if drag_time is None:
            drag = half_pi_pulse(qubit)
            drag_time = drag().width

        for delay in delays:
            front_constant = Constant(drag_time, 0)
            center_delay = Constant(delay, z_amp)
            rear_constant = Constant(drag_time, 0)
            ramsey_z_pulse = front_constant() + center_delay() + rear_constant()
            ramsey_z_pulse.bit = qubit.name
            ramsey_z_pulse.sweep = "sweep delay"
            z_pulse_list.append(ramsey_z_pulse)

        return z_pulse_list


class RamseyCrosstalk(Ramsey):
    """Ramsey Experiments used for crosstalk."""

    def __init__(
        self,
        inst: Instrument,
        target_qubit: Qubit,
        bias_qubit: BaseQubitsType,
        compensates: Dict[BaseQubitsType, PulseCorrection] = None,
        discriminators: Union[IQdiscriminator, List[IQdiscriminator]] = None,
        working_dc: Dict[str, float] = None,
        ac_bias: Dict[str, float] = None,
    ):
        """Create a new ramsey experiment which used for testing crosstalk.
        Args:
            target_qubit (Qubit): The crosstalk coefficient needs to be
                                  measured on the target qubit.
            bias_qubit (Qubit): A qubit that causes crosstalk to the target
                                qubit.
        """
        if isinstance(bias_qubit, Qubit):
            qubits = [target_qubit, bias_qubit]
            couplers = None
        elif isinstance(bias_qubit, Coupler):
            qubits = target_qubit
            couplers = bias_qubit
        else:
            raise TypeError(f"bias qubit only support qubit or coupler!")

        super().__init__(
            inst=inst,
            qubits=qubits,
            couplers=couplers,
            compensates=compensates,
            discriminators=discriminators,
            working_dc=working_dc,
            ac_bias=ac_bias,
        )
        self.qubit = target_qubit
        self.bias_qubit = bias_qubit
        self.set_experiment_options(multi_readout_channels=[target_qubit.readout_channel])

    def get_bias_pulse(self, delays: Union[List, np.ndarray], v_bias: float):
        """Play pulse on bias qubit.

        Args:
            delays (List, np.ndarray): The list of delays that will
                                       be scanned in the experiment,
                                       in nanoseconds.
            v_bias (float): The bias qubit voltage.
        """
        bias_pulse_list = self.get_z_pulse(self.qubit, delays, v_bias)
        self.play_pulse("Z", self.bias_qubit, bias_pulse_list)

    def play_bias_pulse(self, v_bias: float, delays: Union[List, np.ndarray]):
        """:Note: use for new coupler ac crosstalk."""
        self.get_bias_pulse(delays, v_bias)

    def get_qubit_str(self):
        """Overwrite parent method."""
        qubit_str = f"q{self.qubit.bit}/"
        for q in self.qubits:
            qubit_str += "q" + str(q.bit) + ""
        return qubit_str


class CouplerRamsey(CouplerBaseExperiment, Ramsey):
    """Coupler ramsey experiment."""

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.support_context = [StandardContext.CC]
        return options

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulses."""
        xy_pulse_list = Ramsey.get_xy_pulse(self.driveQ, self.experiment_options.delays, self.experiment_options.fringe)
        self.compose_xy_pulses(xy_pulse_list)

    @staticmethod
    def set_z_pulses(self):
        """Set Z pulses."""
        if self.experiment_options.z_amp is not None:
            z_pulse_list = Ramsey.get_z_pulse(
                self.driveQ,
                self.experiment_options.delays,
                self.experiment_options.z_amp,
            )
            self.compose_z_pulses(z_pulse_list)

    @staticmethod
    def update_instrument(self):
        sweep_delay = self._pulse_time_list[: len(self.experiment_options.delays)]
        self.sweep_readout_trigger_delay(self.probeQ.readout_channel, sweep_delay)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super(Ramsey, self)._metadata()
        metadata.draw_meta = {"vol": (self.coupler.dc, "v")}
        z_amp = self.experiment_options.z_amp
        if z_amp is not None:
            metadata.draw_meta["z_amp"] = (z_amp, "v")
        return metadata


class CouplerRamseyCrosstalk(CouplerRamsey):
    def __init__(self, target_qubit: Coupler, bias_qubit: BaseQubit, context: ExperimentContext):
        drive_q, probe_q = context.qubits[0], context.qubits[1]

        if isinstance(bias_qubit, Coupler):
            context.couplers.append(bias_qubit)
        elif isinstance(bias_qubit, Qubit):
            if bias_qubit.bit == drive_q.bit:
                bias_qubit = drive_q
            elif bias_qubit.bit == probe_q.bit:
                bias_qubit = probe_q
            context.qubits.append(bias_qubit)

        super().__init__(
            inst=context.inst,
            qubits=context.qubits,
            couplers=context.couplers,
            compensates=context.compensates,
            discriminators=context.discriminators,
            working_dc=context.working_dc,
            ac_bias=context.ac_bias,
        )
        self.target_bit = target_qubit
        self.bias_bit = bias_qubit

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()

        options.set_validator("target_z_amp", (-1, 1, 2))
        options.set_validator("bias_z_amp", (-1, 1, 2))

        options.target_z_amp = 0.1
        options.bias_z_amp = 0.1

        return options

    @staticmethod
    def set_z_pulses(self):
        """Set Z pulses."""
        if self.experiment_options.target_zamp is not None:
            z_pulse_list = self.get_z_pulse(
                self.driveQ,
                self.experiment_options.delays,
                self.experiment_options.target_z_amp,
            )
            self.compose_z_pulses(z_pulse_list)


class RamseyZZ(Ramsey):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("drag_bit", str)
        options.set_validator("ramsey_bit", str)
        options.set_validator("amp_c_bit", str)
        options.set_validator("gate", ["I", "X"])
        options.set_validator("amp_bit", str)
        options.set_validator("bit_z_amp", float)
        options.amp_c_bit = "qc"
        options.drag_bit = "qh"
        options.ramsey_bit = "ql"
        options.amp_bit = None
        options.bit_z_amp = None
        options.gate = "I"
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.amp_coupler = None
        options.drag_qubit = None
        options.ramsey_qubit = None
        options.amp_qubit = None
        options.support_context = [StandardContext.CGC]

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super(Ramsey, self)._metadata()
        z_amp = self.experiment_options.z_amp
        if z_amp is not None:
            metadata.draw_meta = {"z_amp": (z_amp, "v")}
        return metadata

    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        ramsey_qubit = builder.run_options.ramsey_qubit
        ramsey_xy_pulses = Ramsey.get_xy_pulse(
            ramsey_qubit,
            builder.experiment_options.delays,
            builder.experiment_options.fringe,
        )
        builder.play_pulse("XY", ramsey_qubit, ramsey_xy_pulses)

        drag_qubit = builder.run_options.drag_qubit
        if builder.experiment_options.gate == "X":
            drag_pulse = pi_pulse(drag_qubit)()
        else:
            drag_pulse = zero_pulse(drag_qubit)()
        builder.play_pulse(
            "XY",
            drag_qubit,
            [
                copy.deepcopy(drag_pulse) + Constant(pulse.width - drag_pulse.width, 0, "XY")()
                for pulse in ramsey_xy_pulses
            ],
        )

        for qubit in builder.qubits:
            if qubit.name not in [ramsey_qubit.name, drag_qubit.name]:
                pulses = [Constant(pulse.width, 0, "XY")() for pulse in ramsey_xy_pulses]
                builder.play_pulse("XY", qubit, pulses)

    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""
        if builder.experiment_options.z_amp is not None:
            drag_time = pi_pulse(builder.run_options.drag_qubit)().width
            z_pulse_list = Ramsey.get_z_pulse(
                builder.run_options.amp_coupler,
                builder.experiment_options.delays,
                builder.experiment_options.z_amp,
                drag_time=drag_time,
            )
            builder.play_pulse("Z", builder.run_options.amp_coupler, z_pulse_list)

        if builder.experiment_options.bit_z_amp is not None:
            drag_time = pi_pulse(builder.run_options.drag_qubit)().width
            z_pulse_list2 = Ramsey.get_z_pulse(
                builder.run_options.amp_qubit,
                builder.experiment_options.delays,
                builder.experiment_options.bit_z_amp,
                drag_time=drag_time,
            )
            builder.play_pulse("Z", builder.run_options.amp_qubit, z_pulse_list2)

    def _check_options(self):
        super()._check_options()

        if isinstance(self.discriminator, IQdiscriminator):
            if self.discriminator.name == self.qubit_pair.qh:
                self.experiment_options.ramsey_bit = "qh"
                self.experiment_options.drag_bit = "ql"
            else:
                self.experiment_options.ramsey_bit = "ql"
                self.experiment_options.drag_bit = "qh"

        cz_flow_options_adapter(self)

        if self.experiment_options.amp_c_bit is None or self.experiment_options.drag_bit is None:
            raise ExperimentOptionsError(self.label, msg="`amp_c_bit` and `drag_bit` is both None!")

        drag_qubit = None
        ramsey_qubit = None
        amp_qubit = None
        for qubit in self.qubits:
            if qubit.name == self.experiment_options.drag_bit:
                drag_qubit = qubit
            elif qubit.name == self.experiment_options.ramsey_bit:
                ramsey_qubit = qubit

            if qubit.name == self.experiment_options.amp_bit:
                amp_qubit = qubit

        amp_coupler = None
        for coupler in self.couplers:
            if coupler.name == self.experiment_options.amp_c_bit:
                amp_coupler = coupler

        if drag_qubit is None:
            raise ExperimentOptionsError(
                self._label,
                key="drag_bit",
                value=self.experiment_options.drag_bit,
                msg="no find drag bit in context",
            )

        if amp_coupler is None:
            raise ExperimentOptionsError(
                self._label,
                key="amp_c_bit",
                value=self.experiment_options.amp_c_bit,
                msg="no find amp bit in context",
            )

        if ramsey_qubit is None:
            raise ExperimentOptionsError(
                self._label,
                key="ramsey_bit",
                value=self.experiment_options.ramsy_bit,
                msg="no find ramsey bit in context",
            )

        # two qubit experiment, must set readout channel
        multi_readout_channels = [ramsey_qubit.readout_channel]
        self.set_experiment_options(multi_readout_channels=multi_readout_channels)

        self.set_run_options(
            drag_qubit=drag_qubit,
            ramsey_qubit=ramsey_qubit,
            amp_coupler=amp_coupler,
            amp_qubit=amp_qubit,
            measure_qubits=[ramsey_qubit],
        )

        if isinstance(self.discriminator, List):
            for d in self.discriminator:
                if d.name == ramsey_qubit.name:
                    self.discriminator = d
                    break

    @staticmethod
    def update_instrument(builder):
        sweep_delay = builder.pulse_time_list[: len(builder.experiment_options.delays)]
        builder.sweep_readout_trigger_delay(builder.run_options.ramsey_qubit.readout_channel, sweep_delay)


class CouplerRamseyByZZShift(Ramsey):
    """Ramsey ZZShift."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()
        options.set_validator("fix_q_zamp", float)

        # 2024.07.09 Issue:
        # when fix_q_zamp is None, will default set `- self.qubit.idle_point`,
        # when fix_q_zamp is 0.0, no add `self.qubit` z line ramsey z_pulse.
        options.fix_q_zamp = 0.0

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.support_context = [StandardContext.CC]
        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        fringe = self.experiment_options.fringe
        fix_q_zamp = self.experiment_options.fix_q_zamp
        fd = self.qubit.drive_freq

        if fix_q_zamp is None:
            fix_q_zamp = -self.qubit.idle_point

        # when fix_q_zamp is 0.0, no operate.
        if fix_q_zamp:
            frequency = ac_spectrum_transform(self.qubit, fix_q_zamp, mode=2)
            df = frequency - fd
            if QAIO.type in [QAIO.qaio_8, QAIO.qaio_30]:
                fringe = round(abs(fringe) - df, 3)
            else:
                fringe = round(abs(fringe) + df, 3)

        pyqlog.info(f"{self.coupler.name} fix_q_zamp: {fix_q_zamp}, fringe: {fringe}")
        self.set_experiment_options(
            fix_q_zamp=fix_q_zamp,
            fringe=fringe,
        )

    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""
        qc = builder.couplers[0]
        qb = builder.qubit
        qc_x_width = qc.drive_XYwave.time + qc.drive_XYwave.offset * 2
        qb_x_width = qb.XYwave.time + qb.XYwave.offset * 2
        if qc_x_width > qb_x_width:
            qb = copy.deepcopy(qb)
            qb.XYwave.offset += round((qc_x_width - qb_x_width) / 2, 4)
        elif qb_x_width > qc_x_width:
            qc = copy.deepcopy(qc)
            qc.drive_XYwave.offset += round((qb_x_width - qc_x_width) / 2, 4)

        if builder.experiment_options.z_amp is not None:
            z_pulse_list = Ramsey.get_z_pulse(
                qc,
                builder.experiment_options.delays,
                builder.experiment_options.z_amp,
            )
            builder.play_pulse("Z", qc, z_pulse_list)

        # when fix_q_zamp is 0.0, no add z pulse.
        if builder.experiment_options.fix_q_zamp:
            z_pulse_list = Ramsey.get_z_pulse(
                qb,
                builder.experiment_options.delays,
                builder.experiment_options.fix_q_zamp,
            )
            builder.play_pulse("Z", builder.qubit, z_pulse_list)


@options_wrapper
class RamseyExtend(Ramsey):
    """RamseyExtend experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""

        options = super()._default_experiment_options()
        options.set_validator("tq_name", str)
        options.set_validator("bq_name_list", list)
        options.set_validator("bq_z_amp_list", list)
        options.set_validator("bq_freq_list", list)
        options.set_validator("auto_set_coupler_zamp", bool)
        options.set_validator("auto_set_label", ["cz", "zz"])

        options.tq_name = None
        options.bq_name_list = None
        options.bq_freq_list = None
        options.bq_z_amp_list = None

        # get z amp form qubit pair
        options.auto_set_coupler_zamp = False
        options.auto_set_label = "cz"

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.drag_width = 0.0
        options.bq_list = []
        options.bq_z_amp_map = {}
        options.tq_obj = None

        options.support_context = [
            StandardContext.CGC,
            StandardContext.URM,
        ]

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super(Ramsey, self)._metadata()
        metadata.draw_meta = {
            "tq": self.experiment_options.tq_name,
            "z_amp": self.experiment_options.z_amp,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        # function options
        tq_name = self.experiment_options.tq_name
        bq_name_list = self.experiment_options.bq_name_list or []
        bq_z_amp_list = self.experiment_options.bq_z_amp_list or []
        bq_freq_list = self.experiment_options.bq_freq_list or []

        # verify that the three lists of name/amp/freq are of equal length
        if bq_name_list:
            amp_list_length = len(bq_z_amp_list)
            freq_list_length = len(bq_freq_list)
            name_list_length = len(bq_name_list)
            if amp_list_length < name_list_length:
                bq_z_amp_list.extend([None for _ in range(name_list_length - amp_list_length)])
            if freq_list_length < name_list_length:
                bq_freq_list.extend([None for _ in range(name_list_length - freq_list_length)])

        # collect base qubit/coupler
        qubit_map = {qubit.name: qubit for qubit in self.qubits}
        coupler_map = {coupler.name: coupler for coupler in self.couplers}
        all_qc_map = {}
        all_qc_map.update(qubit_map)
        all_qc_map.update(coupler_map)
        all_qc_names = list(all_qc_map.keys())

        # obtain qubit object corresponding to tq_name
        if tq_name:
            if tq_name in ["ql", "qh"]:
                tq_name = getattr(self.qubit_pair, tq_name)
            tq_obj = qubit_map.get(tq_name)
        else:
            qubit_list = []
            for q_obj in self.qubits:
                if q_obj.name not in bq_name_list:
                    qubit_list.append(q_obj)
            if qubit_list:
                tq_obj = qubit_list[0]
            else:
                tq_obj = None

        # tq_obj assert not None
        if tq_obj is None:
            raise ExperimentOptionsError(
                self,
                f"Set target {tq_name} not in all bit names!",
                "tq_name",
                tq_name,
            )

        # discriminator filter only for tq object
        if self.discriminator:
            if not isinstance(self.discriminator, list):
                dcm_list = [self.discriminator]
            else:
                dcm_list = self.discriminator

            tq_dcm = None
            for dcm_obj in dcm_list:
                if dcm_obj.name == tq_obj.name:
                    tq_dcm = dcm_obj
                    break
            self.discriminator = tq_dcm

        # build bias qubit z amp map
        bq_list = []
        rp_bq_z_amp_map = {}
        for index, bq_name in enumerate(bq_name_list):
            if bq_name in ["qc", "qh", "ql"]:
                if bq_name == "qc" and self.experiment_options.auto_set_coupler_zamp:
                    # auto set qc z amp
                    bq_name = getattr(self.qubit_pair, bq_name)
                    gate_params = self.qubit_pair.gate_params(self.experiment_options.auto_set_label)
                    amp = gate_params.get(bq_name).amp
                    bq_z_amp_list[index] = amp
                else:
                    bq_name = getattr(self.qubit_pair, bq_name)

            if bq_name not in all_qc_names:
                pyqlog.warning(
                    f"{bq_name} not in all names: {all_qc_names}, so will remove {bq_name} from {bq_name_list} ! "
                )
            else:
                bq_obj = all_qc_map.get(bq_name)
                bq_freq = bq_freq_list[index]
                bq_z_amp = bq_z_amp_list[index]

                trans_z_amp = self._trans_freq_to_amp(bq_obj, bq_freq, bq_z_amp)
                pyqlog.debug(f"{bq_name} trans result z_amp: {trans_z_amp}")
                if trans_z_amp is not None and not np.isnan(trans_z_amp):
                    bq_list.append(bq_obj)
                    rp_bq_z_amp_map.update({bq_name: trans_z_amp})

        # check run options
        self.qubit = tq_obj
        multi_readout_channels = [tq_obj.readout_channel]
        drag_width = tq_obj.XYwave.time + 2 * tq_obj.XYwave.offset
        self.set_experiment_options(
            multi_readout_channels=multi_readout_channels,
        )
        self.set_run_options(
            drag_width=drag_width,
            bq_list=bq_list,
            bq_z_amp_map=rp_bq_z_amp_map,
            tq_obj=tq_obj,
            measure_qubits=[tq_obj],
        )
        pyqlog.info(f"bias_qubit_zamp:{rp_bq_z_amp_map}")

    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        tq_obj = builder.run_options.tq_obj
        delays = builder.experiment_options.delays
        fringe = builder.experiment_options.fringe
        xy_pulse_list = Ramsey.get_xy_pulse(tq_obj, delays, fringe)

        for qubit in builder.qubits:
            if qubit == tq_obj:
                builder.play_pulse("XY", qubit, xy_pulse_list)
            else:
                new_xy_pulse_list = [Constant(pulse_obj.width, 0, "XY")() for pulse_obj in xy_pulse_list]
                builder.play_pulse("XY", qubit, new_xy_pulse_list)

    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""
        delays = builder.experiment_options.delays
        z_amp = builder.experiment_options.z_amp
        drag_width = builder.run_options.drag_width
        bq_list = builder.run_options.bq_list
        bq_z_amp_map = builder.run_options.bq_z_amp_map
        tq_obj = builder.run_options.tq_obj

        if z_amp is not None:
            z_pulse_list = Ramsey.get_z_pulse(tq_obj, delays, z_amp, drag_width)
            builder.play_pulse("Z", tq_obj, z_pulse_list)

        for bq_obj in bq_list:
            bq_z_amp = bq_z_amp_map.get(bq_obj.name)
            z_pulse_list = Ramsey.get_z_pulse(bq_obj, delays, bq_z_amp, drag_width)
            builder.play_pulse("Z", bq_obj, z_pulse_list)

    @staticmethod
    def _trans_freq_to_amp(bq_obj: BaseQubit, freq=None, z_amp=None) -> float:
        """Trans frequency to z_amp."""
        bq_name = bq_obj.name
        idle_val = bq_obj.idle_point
        if z_amp is not None:
            rs_amp = z_amp
        elif freq is not None:
            if idle_val > 0:
                branch = "right"
            elif idle_val < 0:
                branch = "left"
            else:
                branch = "right"

            try:
                amp = freq_to_amp(physical_unit=bq_obj, freq=freq, branch=branch)
                rs_amp = amp - idle_val
            except Exception as err:
                rs_amp = None
                pyqlog.warning(f"{bq_name} frequency calculate amp error: {err}")
        else:
            rs_amp = None
            pyqlog.warning(f"{bq_name} provide frequency or z_amp at least one, so will remove {bq_name} ! ")
        return rs_amp

    @staticmethod
    def update_instrument(self):
        """Overwrite."""
        tq_obj = self.run_options.tq_obj
        sweep_delay = self._pulse_time_list[: len(self.experiment_options.delays)]
        self.sweep_readout_trigger_delay(tq_obj.readout_channel, sweep_delay)
