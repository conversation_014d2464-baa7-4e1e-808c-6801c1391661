# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/05
# __author:       ssfang

"""
APE Composite Analysis.

"""
import numpy as np
from matplotlib import pyplot as plt

from pyQCat.structures import Options
from ..curve_fit_analysis import CurveFitAnalysis


class DPhaseCompositeAnalysis(CurveFitAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Options:
            index_policy (str): Select fit target index policy.
            interaction_location (int): Select interaction point number.
        """
        options = super()._default_options()
        options.x_label = "delay (ns)"
        options.y_label = "phase"

        options.p_all_list = []

        options.result_parameters = []

        return options

    def _pre_operation(self):
        """Prepare one more canvas axis."""
        sub_title = []
        y_labels = []
        p_all_list = []

        child_data = self.experiment_data.child_data(index=0)
        for p_label in child_data.y_data.keys():
            p_all_list.append([])
            y_labels.append(self.options.y_label)
            sub_title.append(f"Depth {p_label}")

        self.set_options(
            y_label=y_labels,
            subplots=(2, 1),
            sub_title=sub_title,
            p_all_list=p_all_list,
        )

    def run_analysis(self):
        """Run analysis on experiment data."""
        super().run_analysis()

    def _visualization(self):
        super()._visualization()
        self.drawer.draw_raw_data(
            x_data=self.experiment_data.x_data, y_data=self.experiment_data.y_data["phase"], ax_index=2
        )
