# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/07
# __author:       <PERSON><PERSON><PERSON>

import json
import os
from pathlib import Path
from typing import Dict

import matplotlib.pyplot as plt
import numpy as np

from ...types import S3DataType
from .types import ExecutorState


class RecordData:
    def __init__(self, file=None):
        self.unit2exp = {}
        self.exp2unit = {}
        self.root_path = ""
        self.file = file

    def add_result(self, batch_records: Dict):
        for _, v in batch_records.items():
            self.add_single_record(v)

    def add_single_record(self, record: Dict):

        for unit in record.get("pass_units", []):
            self._add_unit_exp(unit, record.get("exp_name"), ExecutorState.SUC.value)

        for unit in record.get("bad_units", []):
            self._add_unit_exp(unit, record.get("exp_name"), ExecutorState.FAL.value)

    def _add_unit_exp(self, unit: str, exp: str, state: str):
        # for unit_exp_map
        self.update_map(unit, exp, state, self.unit2exp)

        # for exp_unit_map
        self.update_map(exp, unit, state, self.exp2unit)

    @staticmethod
    def update_map(a: str, b: str, c: str, data: Dict):
        if a not in data:
            data[a] = {}

        if b not in data[a]:
            data[a].update(
                {
                    b: {
                        ExecutorState.SUC.value: 0,
                        ExecutorState.FAL.value: 0,
                    }
                }
            )
        data[a][b][c] += 1

    @classmethod
    def from_json(cls, json_path: str, file=None):
        with open(json_path, "r") as f:
            data = json.load(f)
        record_data = cls(file=file)
        record_data.add_result(data)
        record_data.root_path = str(Path(json_path).parent)
        return record_data

    def unit_state_pic(self, description: str = "Batch"):
        
        analysis_result = {}
        for unit, unit_record in self.unit2exp.items():
            species = list(unit_record.keys())
            palette = {
                "SUC": "green",
                "FAIL": "red",
            }
            penguin_means = {
                "SUC": [v.get(ExecutorState.SUC.value) for v in unit_record.values()],
                "FAIL": [v.get(ExecutorState.FAL.value) for v in unit_record.values()],
            }
            max_count = max(max(penguin_means["SUC"]), max(penguin_means["FAIL"]))

            x = np.arange(len(species))  # the label locations
            width = 0.25  # the width of the bars
            multiplier = 0

            fig, ax = plt.subplots(layout="constrained")

            for attribute, measurement in penguin_means.items():
                offset = width * multiplier
                rect = ax.bar(
                    x + offset,
                    measurement,
                    width,
                    label=attribute,
                    color=palette[attribute],
                )
                ax.bar_label(rect, padding=3)
                multiplier += 1

            # Add some text for labels, title and custom x-axis tick labels, etc.
            ax.set_ylabel("Count")
            ax.set_title(f"{description} {unit.upper()} execute state")
            ax.set_xticks(x + width, species, rotation=90, size="small")
            ax.legend(loc="upper right", ncols=3)
            ax.set_ylim(0, int(max_count * 1.2) + 1)

            if self.root_path:
                save_path = str(
                    Path(
                        self.root_path,
                        "analysis",
                        f"{description}_{unit}_execute_state.png",
                    )
                )
            else:
                save_path = str(
                    Path(
                        Path.cwd(),
                        "analysis",
                        f"{description}_{unit}_execute_state.png",
                    )
                )

            os.makedirs(str(Path(save_path).parent), exist_ok=True)
            
            if self.file:
                name = self.file.put_data_to_s3(S3DataType.png, fig)
                if name:
                    analysis_result[unit] = name

            plt.savefig(save_path)
            plt.close()
        return analysis_result
