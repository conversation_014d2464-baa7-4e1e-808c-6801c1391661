# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/11/22
# __author:       <PERSON><PERSON><PERSON>

import numpy as np
from scipy.optimize import least_squares

from ..curve_analysis import CurveAnalysis
from ..quality import GoodnessofFit, BaseQuality
from ...types import QualityDescribe
from ..specification import FitData
from ...analysis.fit import comp_wq2q
from ...log import pyqlog
from ...structures import Options


class CrosstalkLinearAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the QubitSpectrum Analysis options, and set some fields."""
        options = super()._default_options()

        # default x label (Drive Frequency (MHz))
        options.x_label = "Bias Bit AC (V)"
        options.y_label = "Target Bit AC (V)"

        # default quality bounds
        options.quality_bounds = [0.8, 0.6, 0.5]

        # default result parameters (freq)
        options.result_parameters = ["coefficient", "popt"]

        options.sub_title = ["P0", "P1"]
        options.subplots = (2, 1)
        options.popt = [-4, 0, 10, -4, 0, 10, 0.01, 0.001]

        return options

    def _extract_result(self, data_key: str):
        analysis_data = self.analysis_datas[data_key]
        x = analysis_data.x
        y = analysis_data.y

        popt = np.polyfit(x, y, 1)

        fit_y = popt[0] * x + popt[1]

        r2 = GoodnessofFit(*self.options.quality_bounds)
        r2.evaluate(y, fit_y)
        self._quality = r2

        analysis_data.fit_data = FitData(
            popt=list(popt),
            popt_keys=["k", "b"],
            variance=r2.value,
            y_fit=fit_y,
            goodness_of_fit=r2,
        )

        self.results.coefficient.value = np.round(popt[0], 6)
        self.results.popt.value = list(popt)

    def _visualization(self):
        self.drawer.set_options(title=self._description())

        if self.has_child is True:
            analysis_params = self.experiment_data.metadata.process_meta.get(
                "analysis_params"
            )
            self.drawer.draw_dynamic_data(
                np.array(analysis_params.bq_ac_list),
                np.asarray(analysis_params.tq_ac_array, dtype=object),
                np.asarray(analysis_params.z0_array, dtype=object),
                ax_index=0,
            )
            self.drawer.draw_dynamic_data(
                np.array(analysis_params.bq_ac_list),
                np.asarray(analysis_params.tq_ac_array, dtype=object),
                np.asarray(analysis_params.z1_array, dtype=object),
                ax_index=1,
            )
            analysis_data = self.analysis_datas.bias_v
            self.drawer.draw_fit_line(
                analysis_data.x,
                analysis_data.fit_data.y_fit,
                ax_index=0,
            )
            self.drawer.draw_fit_line(
                analysis_data.x,
                analysis_data.fit_data.y_fit,
                ax_index=1,
            )
        self.drawer.format_canvas()


class CrosstalkCurveAnalysis(CrosstalkLinearAnalysis):
    def _extract_result(self, data_key: str):
        """Extract analysis results from important fit parameters.

        Args:
            data_key (str): The basis for selecting data.
        """
        analysis_data = self.analysis_datas[data_key]
        x = analysis_data.x
        y = analysis_data.y

        drive_freq = self.experiment_data.metadata.process_meta.get(
            "analysis_params"
        ).get("drive_freq")
        popt, wq2q_func = self.comp_wq2q_fit(x, y, eigen=drive_freq / 1000)
        bq_ac_fit_list = wq2q_func(x)

        r2 = GoodnessofFit(*self.options.quality_bounds)
        r2.evaluate(y, bq_ac_fit_list)
        self._quality = r2

        analysis_data.fit_data = FitData(
            popt=list(popt),
            popt_keys=[],
            variance=r2.value,
            y_fit=bq_ac_fit_list,
            goodness_of_fit=r2,
        )

        self.results.coefficient.value = round(popt[-2], 6)
        self.results.popt.value = list(popt)

    @staticmethod
    def comp_wq2q_fit(xqb_list, xqt_list, eigen: float = 4.0):
        popt = []

        def error(paras, *args):
            x, y = args
            return comp_wq2q(x, *paras) - y

        popt0 = [
            -4,
            0,
            10,
            4,
            0,
            7,
            0.02,
            0.001,
            eigen,
        ]
        # popt0.append(popt0)
        num = 0
        r2 = np.inf
        while r2 > 1e-5 and num < 500:
            res = least_squares(
                error,
                x0=popt0,
                args=(xqb_list, xqt_list),
                bounds=(
                    [-np.inf, -np.inf, 0, 0, -np.inf, -np.inf, 0, -1, eigen - 0.08],
                    [
                        0,
                        np.inf,
                        np.inf,
                        np.inf,
                        np.inf,
                        np.inf,
                        np.inf,
                        1,
                        eigen + 0.08,
                    ],
                ),
            )
            popt = res.x
            r2 = np.sqrt(np.sum(error(popt, *(xqb_list, xqt_list)) ** 2)) / len(
                xqt_list
            )
            num += 1
            popt0 = popt
        pyqlog.info(f"analytical fit: iter={num}, r2={r2}")
        pyqlog.info(
            f"\n------ analytical fit result ------\n"
            f"a0, b0, c0: {popt[0]:.3e}, {popt[1]:.3e}, {popt[2]:.3e}\n"
            f"a1, b1, c1: {popt[3]:.3e}, {popt[4]:.3e}, {popt[5]:.3e}\n"
            f"rho: {popt[6]:.6f}\n"
            f"crosstalk: {popt[7]:.6f}\n"
            f"working point: {popt[8]:.3f}\n"
            f"----------------------------------"
        )
        return popt, lambda xqb: comp_wq2q(xqb, *popt)


class QCShiftAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the QubitSpectrum Analysis options, and set some fields."""
        options = super()._default_options()

        # default x label (Drive Frequency (MHz))
        options.x_label = "QC AC (V)"
        options.y_label = ["Target Bit AC (V)"]

        # default result parameters (freq)
        options.result_parameters = ["qt_amp_list", "qb_amp_list"]
        options.raw_data_format = "plot"

        return options

    def _extract_result(self, data_key: str):
        """Extract analysis results from important fit parameters.

        Args:
            data_key (str): The basis for selecting data.
        """
        analysis_data = self.analysis_datas[data_key]
        self.results.qb_amp_list.value = list(analysis_data.x)
        self.results.qt_amp_list.value = list(analysis_data.y)
        self.results.qb_amp_list.extra.update({"out_flag": False})
        self.results.qt_amp_list.extra.update({"out_flag": False})
        self._quality = BaseQuality.instantiate(QualityDescribe.perfect)
    