# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
#
# QubitSpectrumScanPower: Scan different drive_power values at dc_max position,
# execute QubitSpectrum experiment, and automatically update drive_power based on results.

# __date:         2025/06/13
# __author:       <PERSON><PERSON><PERSON>

from pyQCat.log import pyqlog
from pyQCat.structures import MetaData, Options, Union
from pyQCat.analysis.specification import ParameterRepr
from pyQCat.types import ExperimentRunMode
from pyQCat.experiments.composite_experiment import CompositeExperiment
from pyQCat.experiments.single.single_gate.qubit_spectrum import QubitSpectrum
from pyQCat.analysis.library.qubit_spectrum_scan_power_analysis import QubitSpectrumScanPowerAnalysis



class QubitSpectrumScanPower(CompositeExperiment):
    """
    QubitSpectrumScanPower Experiment
    Perform a QubitSpectrum experiment by sweeping different drive_power values ​​at dc_max,
        and automatically updating drive_power based on the results.
    """
    _sub_experiment_class = QubitSpectrum

    @classmethod
    def _default_experiment_options(cls):
        """Default experimental parameter settings"""
        options = super()._default_experiment_options()

        options.set_validator("drive_power_list", list, limit_null=True)
        options.drive_power_list = [-30, -27, -24, -21, -18, -15]
        options.run_mode = ExperimentRunMode.async_mode
        return options

    @classmethod
    def _default_analysis_options(cls):
        """Default analysis parameter settings"""
        options = super()._default_analysis_options()
        options.plot_2d = True

        return options

    @classmethod
    def _default_run_options(cls):
        """Default operating parameter settings"""
        options = super()._default_run_options()

        return options

    def _check_options(self):
        """Check parameters and set up the experiment"""
        super()._check_options()
        self.set_analysis_options(result_name=self.qubit.name)
        drive_power_list = self.experiment_options.drive_power_list

        self.set_run_options(
            x_data=drive_power_list,
            analysis_class=QubitSpectrumScanPowerAnalysis,
        )

    def _setup_child_experiment(self, qs_exp: QubitSpectrum, index: int, power: Union[int, float]):
        """Set parameters for each subexperiment"""
        qs_exp.set_experiment_options(drive_power=power)
        qs_exp.run_options.index = index

        describe = f"drive_power={power}dBm"
        total = len(self.run_options.x_data)
        qs_exp.set_parent_file(self, describe, index, total)

        self._check_simulator_data(qs_exp, index)
