# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/20
# __author:       <PERSON><PERSON><PERSON>

#       ___           ___           ___           ___           ___           ___           ___           ___     
#      /\  \         /\  \         /\  \         /\__\         /\  \         /\  \         /\  \         /\  \    
#      \:\  \       /::\  \       /::\  \       /::|  |       /::\  \       /::\  \       /::\  \       /::\  \   
#       \:\  \     /:/\:\  \     /:/\:\  \     /:|:|  |      /:/\ \  \     /:/\:\  \     /:/\:\  \     /:/\:\  \  
#       /::\  \   /::\~\:\  \   /::\~\:\  \   /:/|:|  |__   _\:\~\ \  \   /::\~\:\  \   /::\~\:\  \   /::\~\:\  \ 
#      /:/\:\__\ /:/\:\ \:\__\ /:/\:\ \:\__\ /:/ |:| /\__\ /\ \:\ \ \__\ /:/\:\ \:\__\ /:/\:\ \:\__\ /:/\:\ \:\__\
#     /:/  \/__/ \/_|::\/:/  / \/__\:\/:/  / \/__|:|/:/  / \:\ \:\ \/__/ \/__\:\ \/__/ \:\~\:\ \/__/ \/_|::\/:/  /
#    /:/  /         |:|::/  /       \::/  /      |:/:/  /   \:\ \:\__\        \:\__\    \:\ \:\__\      |:|::/  / 
#    \/__/          |:|\/__/        /:/  /       |::/  /     \:\/:/  /         \/__/     \:\ \/__/      |:|\/__/  
#                   |:|  |         /:/  /        /:/  /       \::/  /                     \:\__\        |:|  |    
#                    \|__|         \/__/         \/__/         \/__/                       \/__/         \|__|    

"""
Transfer service is an independent data transfer process, with the following responsibilities:

1. Accept the experimental message body registered by Monster and forward the experimental
    information to the Naga dispatcher module.

2. Monitor the successful registration of the Naga dispatcher module's ID and establish a 
    mapping between the client ID (required ID) and the server ID (task ID).

3. Accept task status and collected data information returned by Naga Dispatcher for caching
    and recording, replacing the original MongoDB database transfer solution.

4. Accept Monster's requests, including task status, data collection, exception information, etc

5. Task management, logical handling of task completion, exceptions, interruptions, and other states

6. Dynamically clear useless resources to ensure memory security
"""