# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/07/17
# __author:       <PERSON>

"""
Floquet calibration 2q SQ phase experiment.
"""

from copy import deepcopy

import numpy as np

from ....analysis.library.floquet_cz_phase_analysis import FloquetCaliCZphaseAnalysis
from ....errors import ExperimentOptionsError
from ....log import pyqlog
from ....pulse.pulse_function import half_pi_pulse, pi_pulse
from ....pulse.pulse_lib import Constant
from ....pulse_adjust import params_to_pulse
from ....structures import MetaData, Options
from ....tools.utilities import qarange
from ....types import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment
from ..two_qubit_gate.swap_once import (
    validate_qubit_pair_cz_std,
    validate_two_qubit_exp_read_options,
)


class FloquetCalibrationCZphase(TopExperiment):
    """FloquetCalibrationCZphase experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Define experiment options."""
        options = super()._default_experiment_options()
        options.set_validator("ramsey_bit", str)
        options.set_validator("N_list", list)
        options.set_validator("changing_phase", float)

        options.ramsey_bit = "ql"
        options.N_list = qarange(1, 20, 1)
        options.changing_phase = 0.4 * np.pi

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Define run options."""
        options = super()._default_run_options()

        options.support_context = [StandardContext.CGC]
        options.x_data = []
        options.analysis_class = FloquetCaliCZphaseAnalysis

        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = 40.0  # CZ gate with
        options.env_bits = []

        options.ramsey_qubit = None
        options.x_width = 30.0  # X gate width

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Define analysis options."""
        options = super()._default_analysis_options()

        options.set_validator("padding_factor", int)
        options.set_validator("reduce_value", float)
        options.set_validator("dz", float)
        options.set_validator("dp", float)

        options.padding_factor = 200  # 增加分辨率
        options.reduce_value = 0.25  # 减去直流分量值
        options.dz = 0.8 * np.pi  # 猜测初值
        options.dp = 0.0  # 移动初值

        options.changing_phase = 0.2 * np.pi
        options.raw_data_format = "plot"

        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()

        ramsey_bit = self.experiment_options.ramsey_bit
        ramsey_qubit = self.run_options.ramsey_qubit
        q_name = ramsey_qubit.name if ramsey_qubit else ramsey_bit

        metadata.draw_meta = {
            "ramsey_bit": q_name,
        }
        return metadata

    def _check_options(self):
        """Check option."""
        super()._check_options()

        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)

        ramsey_bit = self.experiment_options.ramsey_bit
        N_list = self.experiment_options.N_list
        result_name = self.qubit_pair.name

        if ramsey_bit == "ql":
            ramsey_qubit = self.run_options.ql
        elif ramsey_bit == "qh":
            ramsey_qubit = self.run_options.qh
        else:
            for qubit_obj in self.qubits:
                if qubit_obj.name == ramsey_bit:
                    ramsey_qubit = qubit_obj
                    break
            else:
                raise ExperimentOptionsError(
                    self,
                    f"Set target {ramsey_bit} not in all bit names!",
                    "ramsey_bit",
                    ramsey_bit,
                )
        pyqlog.log(
            "EXP",
            f"{result_name} ramsey_bit: {ramsey_bit}, ramsey_qubit: {ramsey_qubit}",
        )

        x_width = ramsey_qubit.XYwave.time + 2 * ramsey_qubit.XYwave.offset

        self.set_run_options(
            ramsey_qubit=ramsey_qubit,
            x_width=x_width,
            x_data=N_list,
        )
        self.set_analysis_options(
            changing_phase=self.experiment_options.changing_phase,
            result_name=self.qubit_pair.name,
        )

    @staticmethod
    def set_xy_pulses(self):
        """Set xy pulses."""
        N_list = self.experiment_options.N_list
        ramsey_qubit = self.run_options.ramsey_qubit
        x_width = self.run_options.x_width
        cz_width = self.run_options.width
        ql = self.run_options.ql
        qh = self.run_options.qh

        changing_phase = self.experiment_options.changing_phase

        # half_pulse_obj = half_pi_pulse(ramsey_qubit)()
        zx_pulse_obj = Constant(x_width, 0.0, name="XY")()
        for qubit_obj in self.qubits:
            if qubit_obj in [ql, qh]:
                t_pulse = Constant(cz_width, 0.0, name="XY")() + pi_pulse(qubit_obj)()
            else:
                t_pulse = Constant(cz_width + x_width, 0.0, name="XY")()

            xy_pulse_list = []
            for N in N_list:
                if qubit_obj == ramsey_qubit:
                    half_pulse_obj_pre = half_pi_pulse(ramsey_qubit)()
                    half_pulse_obj_after = half_pi_pulse(ramsey_qubit)(
                        phase=N * changing_phase
                    )

                    pulse_obj = (
                        deepcopy(half_pulse_obj_pre)
                        + deepcopy(t_pulse) * N * 2
                        + deepcopy(half_pulse_obj_after)
                    )
                else:
                    pulse_obj = (
                        deepcopy(zx_pulse_obj)
                        + deepcopy(t_pulse) * N * 2
                        + deepcopy(zx_pulse_obj)
                    )
                xy_pulse_list.append(pulse_obj)
            self.play_pulse("XY", qubit_obj, xy_pulse_list)

    @staticmethod
    def set_z_pulses(self):
        """Set z pulses."""
        N_list = self.experiment_options.N_list
        x_width = self.run_options.x_width
        cz_width = self.run_options.width
        gate_params = self.run_options.gate_params

        base_qubits = []
        base_qubits.extend(self.qubits)
        base_qubits.extend(self.couplers)

        zx_pulse_obj = Constant(x_width, 0.0, name="Z")()
        for qc_obj in base_qubits:
            s_gate_params = gate_params.get(qc_obj.name, {})
            if s_gate_params:
                amp = s_gate_params.get("amp") or 0.0
                s_gate_params.update({"time": cz_width, "amp": amp})
                s_pulse = params_to_pulse(**s_gate_params)()
                t_pulse = s_pulse + Constant(x_width, 0.0, name="Z")()
            else:
                t_pulse = Constant(cz_width + x_width, 0.0, name="Z")()

            z_pulse_list = []
            for N in N_list:
                pulse_obj = (
                    deepcopy(zx_pulse_obj)
                    + deepcopy(t_pulse) * N * 2
                    + deepcopy(zx_pulse_obj)
                )
                z_pulse_list.append(pulse_obj)
            self.play_pulse("Z", qc_obj, z_pulse_list)

    @staticmethod
    def update_instrument(self):
        """Update some instrument parameters."""
        N_list = self.experiment_options.N_list

        length = len(N_list)
        for channel in self.experiment_options.multi_readout_channels:
            sweep_delay = self._pulse_time_list[:length]
            self.sweep_readout_trigger_delay(channel, sweep_delay)
