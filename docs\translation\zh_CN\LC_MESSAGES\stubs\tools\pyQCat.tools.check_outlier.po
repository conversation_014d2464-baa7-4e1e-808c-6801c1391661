# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.check_outlier.rst:2
msgid "pyQCat.tools.check\\_outlier"
msgstr ""

#: of pyQCat.tools.utilities.check_outlier:1
msgid "Detect outliers."
msgstr ""

#: of pyQCat.tools.utilities.check_outlier:3
msgid ""
"MAD: Median Absolute Deviation. mad = b * np.median(np.abs(amp - "
"np.median(data))). Deviation from median is larger than sigma * mad will "
"be detected to outlier. :type data: :py:class:`~numpy.ndarray` :param "
"data: data to be detected. :type data: list or np.asarray :type b: "
":py:class:`float` :param b: Fluctuation expansion factor, Default 1.4826."
" :type b: float :type sigma: :py:class:`float` :param sigma: :type sigma:"
" float"
msgstr ""

#: of pyQCat.tools.utilities.check_outlier
msgid "Returns"
msgstr ""

#: of pyQCat.tools.utilities.check_outlier:16
msgid ""
"Upper limit of data fluctuation. mad_lower: Lower limit of data "
"fluctuation."
msgstr ""

#: of pyQCat.tools.utilities.check_outlier
msgid "Return type"
msgstr ""

