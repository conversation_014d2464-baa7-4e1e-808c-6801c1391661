# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:2
msgid "pyQCat.experiments.single.AmpOptimize"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize:1
msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize:1
msgid "AmpOptimize experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.__init__:1
msgid "Create a new experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.single.AmpOptimize.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`acquire_pulse "
"<pyQCat.experiments.single.AmpOptimize.acquire_pulse>`\\ "
"\\(acq\\_pulse\\[\\, qubit\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Set measure pulse."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`cal_fidelity "
"<pyQCat.experiments.single.AmpOptimize.cal_fidelity>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Calculate the fidelity matrix."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`experiment_info "
"<pyQCat.experiments.single.AmpOptimize.experiment_info>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Show experiment informations."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.single.AmpOptimize.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.single.AmpOptimize.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`get_xy_pulse "
"<pyQCat.experiments.single.AmpOptimize.get_xy_pulse>`\\ \\(qubit\\, "
"amp\\_list\\, theta\\, N\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>
msgid "rtype"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ":py:class:`~typing.List`"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`jupyter_schedule "
"<pyQCat.experiments.single.AmpOptimize.jupyter_schedule>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Show experiment pulses's schedules in Jupyter Notebook."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.single.AmpOptimize.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`play_pulse <pyQCat.experiments.single.AmpOptimize.play_pulse>`\\"
" \\(name\\, base\\_qubit\\, pulse\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Play pulse for XY control and Z flux control."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`plot_schedule "
"<pyQCat.experiments.single.AmpOptimize.plot_schedule>`\\ "
"\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
"Plot the pulse schedule which is very important to confirm experiment "
"logical correction."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.single.AmpOptimize.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
#: of pyQCat.experiments.single.amp_optimize.AmpOptimize.run:1
msgid "Run amp optimize experiment and perform analysis."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.single.AmpOptimize.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.single.AmpOptimize.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_multiple_IF "
"<pyQCat.experiments.single.AmpOptimize.set_multiple_IF>`\\ \\(\\*IF\\[\\,"
" channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Used for multiple qubits experiment, set mutiple IF."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_multiple_index "
"<pyQCat.experiments.single.AmpOptimize.set_multiple_index>`\\ "
"\\(\\*args\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
"Used for multiple qubits experiment, set mutiple index. :type args: "
":py:class:`list` :param args: Setting the start and end values :type "
"channel: :py:class:`int` :param channel: channel settings Usage::     "
">>>experiment = TopExperiment()     >>>experiment.set_multiple_index([0, "
"1500], [100, 900], channel=1)."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.single.AmpOptimize.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.single.AmpOptimize.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`set_sweep_order "
"<pyQCat.experiments.single.AmpOptimize.set_sweep_order>`\\ "
"\\(order\\_list\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid "Old interface."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:40:<autosummary>:1
msgid ""
":py:obj:`sweep_readout_trigger_delay "
"<pyQCat.experiments.single.AmpOptimize.sweep_readout_trigger_delay>`\\ "
"\\(channel\\, sweep\\_delay\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.AmpOptimize.rst:42
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.single.AmpOptimize.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.single.AmpOptimize.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.single.AmpOptimize.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.single.AmpOptimize.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:10
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:4
msgid ""
"amp_list (List, np.ndarray): Scan amp list. amp_init (float): Scan amp "
"initial value, default none. threshold (Tuple): Set scan amp range. "
"points (int): Set scan points number. theta_type (str): Support `θ` type,"
" normal \"Xpi\" or \"Xpi/2\". N (int): Number of gates, π gate or π/2 "
"gate."
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options:10
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:12
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options:1
msgid "Default analysis options."
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options:8
msgid "Analysis Options:"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options:4
msgid "quality_bounds (Iterable[float]): The bounds value of the"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options:5
msgid "goodness of fit."
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options:6
msgid ""
"is_plot (bool): Plot or not analysis fit photo. data_key (List[str]): "
"List of mark select data."
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._set_xy_pulses:1
msgid "Set XY pulses."
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:1
msgid "N不可为0"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:2
msgid "当theta_type为Xpi/2时, N不可以为奇数;"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:3
msgid "如果amp_list为空,则使用amp_init构造;"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:4
msgid "如果amp_init为空,则使用qubit获取;"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:5
msgid "data_key根据实验类型决定"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:7
msgid ""
"When `amp_list` and `amp_init` is both none, get xpi value from qubit and"
" assign it to `amp_init`, according to `theta_type` adjusts the output "
"results and selects the analyzed data. When processing amplitude and "
"phase data, both are processed; At the same time, we did some exception "
"handling:"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:11
msgid ""
"① when `theta_type` is not xpi and xpi/2; ② When n is 0; ③ When "
"`theta_type` is xpi/2 and N is odd;"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._metadata:1
msgid "Set metadata."
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.single.AmpOptimize.__init__>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`acquire_pulse "
#~ "<pyQCat.experiments.single.AmpOptimize.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.single.AmpOptimize.from_experiment_context>`\\ "
#~ "\\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.single.AmpOptimize.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_xy_pulse "
#~ "<pyQCat.experiments.single.AmpOptimize.get_xy_pulse>`\\ "
#~ "\\(qubit\\, amp\\_list\\, theta\\, N\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`play_pulse "
#~ "<pyQCat.experiments.single.AmpOptimize.play_pulse>`\\ \\(name\\,"
#~ " base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot_schedule "
#~ "<pyQCat.experiments.single.AmpOptimize.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`run <pyQCat.experiments.single.AmpOptimize.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.single.AmpOptimize.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.single.AmpOptimize.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_IF "
#~ "<pyQCat.experiments.single.AmpOptimize.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_index "
#~ "<pyQCat.experiments.single.AmpOptimize.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.single.AmpOptimize.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.single.AmpOptimize.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_sweep_order "
#~ "<pyQCat.experiments.single.AmpOptimize.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":py:obj:`analysis <pyQCat.experiments.single.AmpOptimize.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.single.AmpOptimize.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.single.AmpOptimize.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.single.AmpOptimize.run_options>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.single.AmpOptimize.__init__>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`acquire_pulse "
#~ "<pyQCat.experiments.single.AmpOptimize.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_info "
#~ "<pyQCat.experiments.single.AmpOptimize.experiment_info>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.single.AmpOptimize.from_experiment_context>`\\ "
#~ "\\(context\\)"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.single.AmpOptimize.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_xy_pulse "
#~ "<pyQCat.experiments.single.AmpOptimize.get_xy_pulse>`\\ "
#~ "\\(qubit\\, amp\\_list\\, theta\\, N\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`jupyter_schedule "
#~ "<pyQCat.experiments.single.AmpOptimize.jupyter_schedule>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.single.AmpOptimize.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`play_pulse "
#~ "<pyQCat.experiments.single.AmpOptimize.play_pulse>`\\ \\(name\\,"
#~ " base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot_schedule "
#~ "<pyQCat.experiments.single.AmpOptimize.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`run <pyQCat.experiments.single.AmpOptimize.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.single.AmpOptimize.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.single.AmpOptimize.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_IF "
#~ "<pyQCat.experiments.single.AmpOptimize.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_index "
#~ "<pyQCat.experiments.single.AmpOptimize.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.single.AmpOptimize.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.single.AmpOptimize.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_sweep_order "
#~ "<pyQCat.experiments.single.AmpOptimize.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.single.AmpOptimize.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.single.AmpOptimize.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.single.AmpOptimize.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.single.AmpOptimize.run_options>`\\"
#~ msgstr ""

#~ msgid "data_keys根据实验类型决定"
#~ msgstr ""

