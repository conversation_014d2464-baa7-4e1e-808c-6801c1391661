# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/08/05
# __author:       SS Fang

"""
CZ Assist Analysis.
"""

from ..oscillation_analysis import OscillationAnalysis
from ...structures import Options


class CZAssistAnalysis(OscillationAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Options:
            readout_type (str): Readout type.

        """
        options = super()._default_options()

        options.set_validator("readout_type",
                              ["target", "control", "parking", "union"])

        options.readout_type = "target"

        options.x_label = "Phase"
        options.quality_bounds = [0.9, 0.65, 0.55]

        options.curve_fit_extra.update({"ftol": 1.49012e-8,
                                        "xtol": 1.49012e-8})

        options.result_parameters = ["phase"]
        options.merge_y_data = True

        return options

    def _extract_result(self, data_key: str, quality=None):
        """Extract Xpi from fitted data.

        Args:
            data_key (str): The basis for selecting data.
            quality : Quality of fit outcome.
        """

        analysis_data = self.analysis_datas[data_key]
        x = analysis_data.x
        y_fit = analysis_data.fit_data.y_fit
        amp, freq, phase, base = analysis_data.fit_data.popt

        if amp < 0:
            phase += 1 / freq / 2

        # bug solve: f(x) = A * cos(x + phase), if we want to find the max point,
        # the x must be equal -phase
        phase = -phase

        if data_key == 'P0':
            phase += 1 / freq / 4

        while phase <= x[0]:
            phase = phase + 1 / freq

        while phase >= x[-1]:
            phase = phase - 1 / freq

        self.results.phase.value = phase

        # todo, to optimize get target phase value.
        readout_type = self.options.readout_type

        if self.options.is_plot is True:
            pos = (phase, 0.5)
            self.drawer.set_options(text_pos=[pos],
                                    text_rp=[f"TargetPhase\n{pos}"],
                                    text_key=[data_key])

    def run_analysis(self):
        """Run analysis on experiment data."""
        super().run_analysis()
