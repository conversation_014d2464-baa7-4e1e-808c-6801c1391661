# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/23
# __author:       <PERSON><PERSON><PERSON>

import copy
import json
from copy import deepcopy
from pathlib import Path
from typing import Union

import numpy as np

from ....analysis import AnalysisResult, NMAnalysis, CMAAnalysis
from ....log import pyqlog
from ....qaio_property import QAIO
from ....structures import Options, QDict
from ....tools import cz_flow_options_adapter
from ....tools.calculator import qubit_pair_detune_prepare
from ....tools.NM_bnd import async_nm_minimize, cma_es
from ....tools.utilities import count_segments, restore_array
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import (
    RBMultiple,
    RBSingle,
    SingleShot,
    XEBMultiple,
    XEBSingle,
    XYCrosstalkRB,
)
from ..z_distortion.distortion_pole_optimization import ea, gea


class NMBase(CompositeExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("input_data", dict)
        options.set_validator("nm_params", dict)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.input_data = None
        options.nm_params = Options(
            ftarget=-1e8,
            maxiter=None,
            maxfev=None,
            disp=True,
            return_all=True,
            initial_simplex=None,
            xatol=1e-4,
            fatol=1e-4,
            adaptive=False,
        )

        options.set_validator("mode", ["NM", "DE"])
        options.mode = "DE"
        options.set_validator("de_params", dict)
        options.de_params = {
            "NIND": 5,
            "MAXGEN": 5,
            "mutF": 0.7,
            "XOVR": 0.7,
            "prophet_flag": True,
        }
        options.set_validator("bound_mode", ["base_init", "no_base"])
        options.bound_mode = "no_base"
        # options.final_check = True

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.optimize_result = None
        options.is_reversed = False
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.count = 0
        options.init_v = None
        options.opt_keys = None
        options.iter_step = None
        options.bound = None
        options.record_list = []
        options.nonzdelt = None
        options.index = 0

        return options

    async def _sync_composite_run(self):
        init_v = self.run_options.init_v
        iter_step = self.run_options.iter_step
        bound = self.run_options.bound
        opt_keys = self.run_options.opt_keys
        nonzdelt = self.run_options.nonzdelt
        
        if self.experiment_options.bound_mode == "base_init":
            new_bound = []
            for v, cb in zip(init_v, bound):
                try:
                    new_bound.append([v + cb[0], v + cb[1]])
                except Exception:
                    new_bound.append(cb)
            bound = new_bound
            
        if self.experiment_options.mode == "NM":
            pyqlog.log(
                "EXP",
                f"NM optimize base {self.child_experiment.__class__.__name__}:\n"
                f"Keys: {opt_keys}\n"
                f"Init V: {init_v}\n"
                f"Iter Step: {iter_step}\n"
                f"Bound: {bound}\n"
                f"nonzdelt: {nonzdelt}"
            )
            ftarget = self.experiment_options.nm_params.ftarget
            if isinstance(ftarget, str):
                ftarget = float(ftarget)
            res, sim = await async_nm_minimize(
                self.nm_opt_func,
                x0=init_v,
                ftarget=ftarget,
                nonzdelt=nonzdelt,
                maxiter=self.experiment_options.nm_params.maxiter,
                maxfev=self.experiment_options.nm_params.maxfev,
                disp=self.experiment_options.nm_params.disp,
                return_all=self.experiment_options.nm_params.return_all,
                xatol=self.experiment_options.nm_params.xatol,
                fatol=self.experiment_options.nm_params.fatol,
                adaptive=self.experiment_options.nm_params.adaptive,
                step=iter_step,
                bound=bound,
                callback=self._callback,
            )
        else:
            pyqlog.log(
                "EXP",
                f"DE optimize base {self.child_experiment.__class__.__name__}:\n"
                f"Keys: {opt_keys}\n"
                f"Init V: {init_v}\n"
                f"Iter Step: {iter_step}\n"
                f"Bound: {bound}\n"
                f"nonzdelt: {nonzdelt}"
            )
            opti_opts = self.experiment_options.de_params
            opti_dim = len(init_v)
            problem = ea.Problem(
                name="DE optimize",
                M=1,  # Initialize M (Target Dimension)
                maxormins=[1],
                Dim=opti_dim,
                varTypes=[0] * opti_dim,
                # varTypes=[1, 0, 0],
                lb=np.asarray([b[0] for b in bound]),
                ub=np.asarray([b[1] for b in bound]),
                evalVars=self.eval_vars,
            )
            algorithm = ea.soea_DE_best_1_bin_templet(
                problem,
                gea.Population(Encoding="RI", NIND=opti_opts["NIND"]),
                MAXGEN=opti_opts["MAXGEN"],
                logTras=1,
                outFunc=None,
                # trappedValue=1e-10,
                # maxTrappedCount=20
            )
            algorithm.mutOper.F = opti_opts["mutF"]  # Parameter F in differential evolution
            algorithm.recOper.XOVR = opti_opts["XOVR"]  # Cross-recombination probability

            prophet = np.array(init_v)
            res = await ea.optimize(
                algorithm,
                prophet=prophet,
                verbose=True,
                drawing=0,
                outputMsg=True,
                drawLog=False,
                saveFlag=True,
                dirName=str(Path(self.file.dirs)),
            )
            
            fsim_records = [rl[-1] for rl in self.run_options.record_list]
            x = res.get("Vars")
            fun = res.get("ObjV")[0]
            res.update({
                "fsim_records": fsim_records,
                "x": x[0],
                "fun": fun
            })
            res = QDict(**res)
            
        pyqlog.info(f"optimize result | {res}")
        self.set_analysis_options(optimize_result=res)
        self._run_analysis(x_data=list(range(self.run_options.count)), analysis_class=NMAnalysis)

    async def eval_vars(self, Vars):
        # Vars size=[number of individuals in the population, number of parameters],
        # even if the initial value is the same, the result here is not the same.
        cost = []
        for i in range(Vars.shape[0]):
            res = await self.nm_opt_func(Vars[i, :])
            cost.append(res)
        return np.vstack(cost)

    async def nm_opt_func(self, parameters, *args):
        res = await self._execute_exp(parameters)
        pyqlog.log("EXP", f"{self.experiment_options.mode} FUNC - {self.run_options.count}: {parameters} | {res}")
        record = copy.copy(list(parameters))
        record.insert(0, self.run_options.count)
        record.append(res)
        self.run_options.count += 1
        try:
            self.run_options.record_list.append(record)
            self.file.save_data(np.array(self.run_options.record_list).T, name="params")
        except Exception as e:
            pyqlog.warning(f"{e}, error occurred when saving parameters")
        return res

    async def _execute_exp(self, parameters) -> Union[int, float]:
        return 0

    async def _run_exp(self, exp):
        exp.set_parent_file(
            self,
            description=f"{self.experiment_options.mode}-{self.run_options.count}",
            index=self.run_options.index,
        )
        self.run_options.index += 1
        self._check_simulator_data(exp, self.run_options.count)
        await exp.run_experiment()
        self._experiments.append(exp)

    def _callback(self, fsim: float):
        pass

    def _check_options(self):
        super()._check_options()

        opt_keys = []
        bound = []
        iter_step = []
        nonzdelt = []
        for k, v in self.experiment_options.input_data.items():
            if v.get("is_opt") is True:
                opt_keys.append(k)
                iter_step.append(v.get("iter_step"))
                nonzdelt.append(v.get("nonzdelt"))

                b = v.get("bound")
                if b:
                    bound.append(b)
                else:
                    bound.append([float("-inf"), float("inf")])

        self.set_run_options(opt_keys=opt_keys, iter_step=iter_step, nonzdelt=nonzdelt, bound=bound)

        self._label = f"{self.experiment_options.mode}{type(self).__name__[2:]}"

    def _set_result_path(self):
        pass


class NMSingleShot(NMBase):
    _sub_experiment_class = SingleShot

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.input_data = {
            "probe_freq": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.001,
                "bound": [4000, 8000],
                "nonzdelt": 0.05,
            },
            "probe_power": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.1,
                "bound": [-40, -10],
                "nonzdelt": 0.05,
            },
            "sample_width": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 5,
                "bound": [1000, 3000],
                "nonzdelt": 0.05,
            },
            "sample_delay": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 5,
                "bound": [500, 1000],
                "nonzdelt": 0.05,
            },
            "Mwave.amp": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.0001,
                "bound": [0, 1],
                "nonzdelt": 0.05,
            },
        }

        return options

    async def _execute_exp(self, parameters) -> Union[int, float]:
        exp = deepcopy(self.child_experiment)

        opt_keys = self.run_options.opt_keys
        for i, p in enumerate(parameters):
            if opt_keys[i] == "Mwave.amp":
                exp.qubit.Mwave.amp = parameters[i]
            else:
                setattr(exp.qubit, opt_keys[i], parameters[i])

        exp.set_experiment_options(
            save_result=False,
            save_bin=False,
        )

        await self._run_exp(exp)

        f0, f1 = exp.discriminator.fidelity
        k_recommend = exp.discriminator.k_recommend
        if k_recommend == 3:
            f1 -= 0.2
        fidelity = np.mean([f0, f1])

        return -fidelity

    def _check_options(self):
        super()._check_options()
        self.set_analysis_options(is_reversed=True)

        init_v = []
        for k, v in self.experiment_options.input_data.items():
            if v.get("is_opt") is True:
                iv = v.get("init_v")
                if iv is not None:
                    init_v.append(iv)
                else:
                    if k == "Mwave.amp":
                        init_v.append(self.child_experiment.qubit.Mwave.amp)
                    else:
                        init_v.append(getattr(self.child_experiment.qubit, k))

        self.set_run_options(init_v=init_v)


class NMRBMultiple(NMBase):
    _sub_experiment_class = RBMultiple

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("scan_name", str)

        options.scan_name = "qh"
        options.input_data = {
            "ql_amp": {
                "is_opt": False,
                "init_v": None,
                "iter_step": 0.0001,
                "bound": [-0.5, 0.5],
                "nonzdelt": 0.05,
            },
            "qh_amp": {
                "is_opt": False,
                "init_v": None,
                "iter_step": 0.0001,
                "bound": [-0.5, 0.5],
                "nonzdelt": 0.05,
            },
            "ql_freq": {
                "is_opt": False,
                "init_v": None,
                "iter_step": 0.1,
                "bound": [4000, 8000],
                "nonzdelt": 0.05,
            },
            "qh_freq": {
                "is_opt": False,
                "init_v": None,
                "iter_step": 0.1,
                "bound": [4000, 8000],
                "nonzdelt": 0.05,
            },
            "qc_amp": {
                "is_opt": False,
                "init_v": None,
                "iter_step": 0.0001,
                "bound": [-0.5, 0.5],
                "nonzdelt": 0.05,
            },
            "qh_phase": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.0001,
                "bound": None,
                "nonzdelt": 0.05,
            },
            "ql_phase": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.0001,
                "bound": None,
                "nonzdelt": 0.05,
            },
            "detune": {
                "is_opt": True,
                "init_v": 0,
                "iter_step": 0.1,
                "bound": [-30, 30],
                "nonzdelt": 0.05,
            },
            "detune1": {
                "is_opt": False,
                "init_v": 0,
                "iter_step": 0.001,
                "bound": [-0.15, 0.15],
                "nonzdelt": 0.005,
            },
            "detune2": {
                "is_opt": False,
                "init_v": 0,
                "iter_step": 0.001,
                "bound": [-0.15, 0.15],
                "nonzdelt": 0.005,
            },
        }

        return options

    def _check_options(self):
        super()._check_options()

        qubit_pair = self.child_experiment.qubit_pair
        ql_name = qubit_pair.metadata.std.ql
        qh_name = qubit_pair.metadata.std.qh
        qc_name = qubit_pair.metadata.std.qc
        cz_flow_options_adapter(self)
        scan_name = self.experiment_options.scan_name

        init_v = []
        for k, v in self.experiment_options.input_data.items():
            if v.get("is_opt") is True:
                iv = v.get("init_v")
                if iv is not None:
                    init_v.append(iv)
                else:
                    if k == "ql_amp":
                        init_v.append(qubit_pair.cz_value(ql_name, "amp"))
                    elif k == "qh_amp":
                        init_v.append(qubit_pair.cz_value(qh_name, "amp"))
                    elif k == "qc_amp":
                        init_v.append(qubit_pair.cz_value(qc_name, "amp"))
                    elif k == "ql_phase":
                        init_v.append(qubit_pair.cz_value(ql_name, "phase"))
                    elif k == "qh_phase":
                        init_v.append(qubit_pair.cz_value(qh_name, "phase"))
                    elif k == "ql_freq":
                        init_v.append(qubit_pair.cz_value(ql_name, "freq"))
                    elif k == "qh_freq":
                        init_v.append(qubit_pair.cz_value(qh_name, "freq"))
                    elif k == "detune1":
                        init_v.append(qubit_pair.cz_value(scan_name, "detune1"))
                    elif k == "detune2":
                        init_v.append(qubit_pair.cz_value(scan_name, "detune2"))
                    elif k == "detune":
                        init_v.append(0)

        self.set_run_options(init_v=init_v)

    async def _execute_exp(self, parameters) -> Union[int, float]:
        qubit_pair = self.child_experiment.qubit_pair
        ql_name = qubit_pair.metadata.std.ql
        qh_name = qubit_pair.metadata.std.qh
        qc_name = qubit_pair.metadata.std.qc
        scan_name = self.experiment_options.scan_name
        exp = deepcopy(self.child_experiment)
        if self.run_options.count == 0:
            gate_params = qubit_pair.gate_params("cz")
            json_str = json.dumps(gate_params, ensure_ascii=False, indent=4)
            self.file.save_text(json_str, name="gate_params", prefix=".json")

        for i, key in enumerate(self.run_options.opt_keys):
            if key == "ql_amp":
                exp.qubit_pair.set_cz_value(ql_name, "amp", parameters[i])
                exp.qubit_pair.set_cz_value(ql_name, "freq", None)
            elif key == "qh_amp":
                exp.qubit_pair.set_cz_value(qh_name, "amp", parameters[i])
                exp.qubit_pair.set_cz_value(qh_name, "freq", None)
            elif key == "qc_amp":
                exp.qubit_pair.set_cz_value(qc_name, "amp", parameters[i])
                exp.qubit_pair.set_cz_value(qc_name, "freq", None)
            elif key == "qh_phase":
                exp.qubit_pair.set_cz_value(qh_name, "phase", parameters[i])
            elif key == "ql_phase":
                exp.qubit_pair.set_cz_value(ql_name, "phase", parameters[i])
            elif key == "qh_freq":
                exp.qubit_pair.set_cz_value(qh_name, "freq", parameters[i])
            elif key == "ql_freq":
                exp.qubit_pair.set_cz_value(ql_name, "freq", parameters[i])
            elif key == "detune1":
                exp.qubit_pair.set_cz_value(scan_name, "detune1", parameters[i])
            elif key == "detune2":
                exp.qubit_pair.set_cz_value(scan_name, "detune2", parameters[i])

            elif key == "detune":
                freq_map = qubit_pair_detune_prepare(
                    self.qubit_pair, self.qubits, label="cz", goal_detune=parameters[i]
                )
                msg = f"detune({parameters[i]})"
                for unit, freq in freq_map.items():
                    msg += f" {unit}({freq[0]})"
                    exp.qubit_pair.set_cz_value(unit, "freq", freq[0])
                pyqlog.info(f"NM detune: {msg}")

        await self._run_exp(exp)

        if self.__class__.__name__ == "NMRBMultiple":
            return -float(np.mean(exp.analysis.analysis_datas.P00.y))
        else:
            return -float(np.mean(exp.analysis.analysis_datas.F_xeb.y))

    def _set_result_path(self):
        sim = self.analysis.results.sim
        ql = self.qubit_pair.ql
        qh = self.qubit_pair.qh
        qc = self.qubit_pair.qc
        scan_name = self.experiment_options.scan_name

        his_lam_list = self.qubit_pair.cz_value(qc, "lam_list") or []
        lam_list = his_lam_list[:]
        lam_flag = False
        for i, key in enumerate(self.run_options.opt_keys):
            if key == "ql_amp":
                self.analysis.results["ql_amp"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{ql}.amp",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "qh_amp":
                self.analysis.results["qh_amp"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{qh}.amp",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "qc_amp":
                self.analysis.results["qc_amp"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{qc}.amp",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "qh_phase":
                self.analysis.results["qh_phase"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{qh}.phase",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "ql_phase":
                self.analysis.results["ql_phase"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{ql}.phase",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "qh_freq":
                self.analysis.results["qh_freq"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{qh}.freq",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "ql_freq":
                self.analysis.results["ql_freq"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{ql}.freq",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "detune1":
                self.analysis.results["detune1"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{scan_name}.detune1",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "detune2":
                self.analysis.results["detune2"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{scan_name}.detune2",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "detune":
                freq_map = qubit_pair_detune_prepare(
                    self.qubit_pair, self.qubits, goal_detune=sim.value[i]
                )
                for unit, freq in freq_map.items():
                    self.analysis.results[unit] = AnalysisResult(
                        name=unit,
                        value=freq[0],
                        extra={
                            "path": f"QubitPair.metadata.std.cz.params.{unit}.freq",
                            "name": self.qubit_pair.name,
                        },
                    )
            elif key.startswith("lam"):
                lam_flag = True
                lam_idx = int(key[3:]) - 1
                lam_length = len(lam_list)
                value = sim.value[i]
                if lam_idx < lam_length:
                    lam_list[lam_idx] = value
                else:
                    diff = [0.0] * (lam_idx - lam_length + 1)
                    diff[-1] = value
                    lam_list.extend(diff)

        if lam_flag is True:
            pyqlog.info(f"{self.qubit_pair.name} will set {qc} lam_list: {lam_list}")
            self.analysis.results["lam_list"] = AnalysisResult(
                name="lam_list",
                value=lam_list,
                extra={
                    "path": f"QubitPair.metadata.std.cz.params.{qc}.lam_list",
                    "name": self.qubit_pair.name,
                },
            )


class NMRBSingle(NMBase):
    _sub_experiment_class = RBSingle

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.input_data = {
            "Xpi": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.001,
                "bound": [-1, 1],
                "nonzdelt": 0.05,
            },
            "Xpi2": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.001,
                "bound": [-1, 1],
                "nonzdelt": 0.05,
            },
            "detune_pi": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.01,
                "bound": None,
                "nonzdelt": 0.05,
            },
            "detune_pi2": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.01,
                "bound": None,
                "nonzdelt": 0.05,
            },
            "alpha": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.001,
                "bound": [0, 1],
                "nonzdelt": 0.05,
            },
        }

        return options

    def _check_options(self):
        super()._check_options()
        init_v = []
        xy_wave = self.child_experiment.qubit.XYwave
        for k, v in self.experiment_options.input_data.items():
            if v.get("is_opt") is True:
                iv = v.get("init_v")
                if iv is not None:
                    init_v.append(iv)
                else:
                    init_v.append(getattr(xy_wave, k))

        self.set_run_options(init_v=init_v)

    async def _execute_exp(self, parameters) -> Union[int, float]:
        exp = deepcopy(self.child_experiment)

        for i, key in enumerate(self.run_options.opt_keys):
            setattr(exp.qubit.XYwave, key, parameters[i])

        await self._run_exp(exp)

        return -float(np.mean(exp.analysis.analysis_datas.P0.y))


class NMXEBSingle(NMRBSingle):
    _sub_experiment_class = XEBSingle

    async def _execute_exp(self, parameters) -> Union[int, float]:
        exp = deepcopy(self.child_experiment)

        for i, key in enumerate(self.run_options.opt_keys):
            setattr(exp.qubit.XYwave, key, parameters[i])

        await self._run_exp(exp)

        return -float(np.mean(exp.analysis.analysis_datas.F_xeb.y))


class NMXEBMultiple(NMRBMultiple):
    _sub_experiment_class = XEBMultiple

    # def _final_check(self, parameters):
    #     qubit_pair = self.child_experiment.qubit_pair
    #     ql_name = qubit_pair.metadata.std.ql
    #     qh_name = qubit_pair.metadata.std.qh
    #     qc_name = qubit_pair.metadata.std.qc
    #     exp = deepcopy(self.child_experiment)
    #     exp.set_experiment_options(is_amend=True,
    #                                depth1=qarange(2, 10, 2),
    #                                depth2=qarange(15, 60, 5),
    #                                depth3=None,
    #                                depth4=None,
    #                                times=30,
    #                                goal_gate="CZ",
    #                                open_seed=False,
    #                                )
    #
    #     for i, key in enumerate(self.run_options.opt_keys):
    #         if key == "ql_amp":
    #             exp.qubit_pair.set_cz_value(ql_name, "amp", parameters[i])
    #             exp.qubit_pair.set_cz_value(ql_name, "freq", None)
    #         elif key == "qh_amp":
    #             exp.qubit_pair.set_cz_value(qh_name, "amp", parameters[i])
    #             exp.qubit_pair.set_cz_value(qh_name, "freq", None)
    #         elif key == "qc_amp":
    #             exp.qubit_pair.set_cz_value(qc_name, "amp", parameters[i])
    #             exp.qubit_pair.set_cz_value(qc_name, "freq", None)
    #         elif key == "qh_phase":
    #             exp.qubit_pair.set_cz_value(qh_name, "phase", parameters[i])
    #         elif key == "ql_phase":
    #             exp.qubit_pair.set_cz_value(ql_name, "phase", parameters[i])
    #         elif key == "qh_freq":
    #             exp.qubit_pair.set_cz_value(qh_name, "freq", parameters[i])
    #         elif key == "ql_freq":
    #             exp.qubit_pair.set_cz_value(ql_name, "freq", parameters[i])
    #         elif key == "detune":
    #             freq_map = self.qubit_pair.detune_prepare(self.qubits, label="cz", goal_detune=parameters[i])
    #             msg = f"detune({parameters[i]})"
    #             for unit, freq in freq_map.items():
    #                 msg += f" {unit}({freq[0]})"
    #                 exp.qubit_pair.set_cz_value(unit, "freq", freq[0])
    #             pyqlog.info(f"NM detune: {msg}")
    #
    #     self._run_exp(exp)
    #     return exp.analysis.results.f_xeb.value


class NMXYCrosstalkRB(NMBase):
    """NM optimize XYCrosstalk RB."""

    _sub_experiment_class = XYCrosstalkRB

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options."""
        options = super()._default_experiment_options()

        options.input_data = {
            "amp_coe": {
                "is_opt": True,
                "init_v": 0.01,
                "iter_step": 0.005,
                "bound": [0.001, 0.5],
                "nonzdelt": 0.05,
            },
            "phase": {
                "is_opt": True,
                "init_v": 0.0,
                "iter_step": 0.05,
                "bound": [-3.1415, 3.1416],
                "nonzdelt": 0.05,
            },
        }

        return options

    def _check_options(self):
        """Check options."""
        super()._check_options()
        self.set_analysis_options(is_reversed=True)

        init_v = []
        for k, v in self.experiment_options.input_data.items():
            if v.get("is_opt") is True:
                iv = v.get("init_v")
                init_v.append(iv or 0.0)

        self.set_run_options(init_v=init_v)

    async def _execute_exp(self, parameters) -> Union[int, float]:
        """Run execute experiment part."""
        exp = deepcopy(self.child_experiment)

        exp_params = {}
        for i, key in enumerate(self.run_options.opt_keys):
            value = parameters[i]
            exp_params.update({key: value})
        exp.set_experiment_options(**exp_params)
        await self._run_exp(exp)

        as_dict = exp.analysis.analysis_datas
        analysis_data = as_dict.get("t-0(P00+P01)") or as_dict.get("P0")
        return -float(np.mean(analysis_data.y))


class NMSlepianLam(NMXEBMultiple):
    """NM optimize SlepianLam lam parametes."""

    _sub_experiment_class = XEBMultiple

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options."""
        options = super()._default_experiment_options()

        options.input_data.update(
            {
                "lam1": {
                    "is_opt": True,
                    "init_v": None,
                    "iter_step": 0.005,
                    "bound": [0.001, 0.5],
                    "nonzdelt": 0.1,
                },
                "lam2": {
                    "is_opt": True,
                    "init_v": None,
                    "iter_step": 0.005,
                    "bound": [0.0, 0.3],
                    "nonzdelt": 0.1,
                },
                "lam3": {
                    "is_opt": False,
                    "init_v": None,
                    "iter_step": 0.005,
                    "bound": [0.0, 0.1],
                    "nonzdelt": 0.05,
                },
                "lam4": {
                    "is_opt": False,
                    "init_v": None,
                    "iter_step": 0.005,
                    "bound": [0.0, 0.1],
                    "nonzdelt": 0.05,
                },
            }
        )

        return options

    def _check_options(self):
        """Check options."""
        super(NMRBMultiple, self)._check_options()

        qubit_pair = self.child_experiment.qubit_pair
        ql_name = qubit_pair.metadata.std.ql
        qh_name = qubit_pair.metadata.std.qh
        qc_name = qubit_pair.metadata.std.qc
        cz_flow_options_adapter(self)
        scan_name = self.experiment_options.scan_name

        init_v = []
        lam_list = qubit_pair.cz_value(qc_name, "lam_list") or []
        lam_length = len(lam_list)
        for k, v in self.experiment_options.input_data.items():
            if v.get("is_opt") is True:
                iv = v.get("init_v")
                if iv is not None:
                    init_v.append(iv)
                else:
                    if k == "ql_amp":
                        init_v.append(qubit_pair.cz_value(ql_name, "amp"))
                    elif k == "qh_amp":
                        init_v.append(qubit_pair.cz_value(qh_name, "amp"))
                    elif k == "qc_amp":
                        init_v.append(qubit_pair.cz_value(qc_name, "amp"))
                    elif k == "ql_phase":
                        init_v.append(qubit_pair.cz_value(ql_name, "phase"))
                    elif k == "qh_phase":
                        init_v.append(qubit_pair.cz_value(qh_name, "phase"))
                    elif k == "ql_freq":
                        init_v.append(qubit_pair.cz_value(ql_name, "freq"))
                    elif k == "qh_freq":
                        init_v.append(qubit_pair.cz_value(qh_name, "freq"))
                    elif k == "detune1":
                        init_v.append(qubit_pair.cz_value(scan_name, "detune1"))
                    elif k == "detune2":
                        init_v.append(qubit_pair.cz_value(scan_name, "detune2"))
                    elif k == "detune":
                        init_v.append(0)
                    elif k.startswith("lam"):
                        lam_idx = int(k[3:]) - 1
                        if lam_idx < lam_length:
                            kv = lam_list[lam_idx]
                        else:
                            kv = 0.0
                        init_v.append(kv)

        self.set_run_options(init_v=init_v)

    async def _execute_exp(self, parameters) -> Union[int, float]:
        """Run execute experiment part."""
        exp = deepcopy(self.child_experiment)

        qubit_pair = self.child_experiment.qubit_pair
        ql_name = qubit_pair.metadata.std.ql
        qh_name = qubit_pair.metadata.std.qh
        qc_name = qubit_pair.metadata.std.qc
        scan_name = self.experiment_options.scan_name

        if self.run_options.count == 0:
            gate_params = qubit_pair.gate_params("cz")
            json_str = json.dumps(gate_params, ensure_ascii=False, indent=4)
            self.file.save_text(json_str, name="gate_params", prefix=".json")

        his_lam_list = qubit_pair.cz_value(qc_name, "lam_list") or []
        lam_list = his_lam_list[:]
        for i, key in enumerate(self.run_options.opt_keys):
            if key == "ql_amp":
                exp.qubit_pair.set_cz_value(ql_name, "amp", parameters[i])
                exp.qubit_pair.set_cz_value(ql_name, "freq", None)
            elif key == "qh_amp":
                exp.qubit_pair.set_cz_value(qh_name, "amp", parameters[i])
                exp.qubit_pair.set_cz_value(qh_name, "freq", None)
            elif key == "qc_amp":
                exp.qubit_pair.set_cz_value(qc_name, "amp", parameters[i])
                exp.qubit_pair.set_cz_value(qc_name, "freq", None)
            elif key == "qh_phase":
                exp.qubit_pair.set_cz_value(qh_name, "phase", parameters[i])
            elif key == "ql_phase":
                exp.qubit_pair.set_cz_value(ql_name, "phase", parameters[i])
            elif key == "qh_freq":
                exp.qubit_pair.set_cz_value(qh_name, "freq", parameters[i])
            elif key == "ql_freq":
                exp.qubit_pair.set_cz_value(ql_name, "freq", parameters[i])
            elif key == "detune1":
                exp.qubit_pair.set_cz_value(scan_name, "detune1", parameters[i])
            elif key == "detune2":
                exp.qubit_pair.set_cz_value(scan_name, "detune2", parameters[i])

            elif key == "detune":
                freq_map = qubit_pair_detune_prepare(
                    self.qubit_pair, self.qubits, label="cz", goal_detune=parameters[i]
                )
                msg = f"detune({parameters[i]})"
                for unit, freq in freq_map.items():
                    msg += f" {unit}({freq[0]})"
                    exp.qubit_pair.set_cz_value(unit, "freq", freq[0])
                pyqlog.info(f"NM detune: {msg}")

            elif key.startswith("lam"):
                lam_idx = int(key[3:]) - 1
                lam_length = len(lam_list)
                value = parameters[i]
                if lam_idx < lam_length:
                    lam_list[lam_idx] = value
                else:
                    diff = [0.0] * (lam_idx - lam_length + 1)
                    diff[-1] = value
                    lam_list.extend(diff)

        exp.qubit_pair.set_cz_value(qc_name, "lam_list", lam_list)

        await self._run_exp(exp)

        as_dict = exp.analysis.analysis_datas
        analysis_data = as_dict.get("F_xeb")
        return -float(np.mean(analysis_data.y))


class NMComXEBMultiple(NMRBMultiple):
    _sub_experiment_class = XEBMultiple

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options."""
        options = super()._default_experiment_options()

        options.input_data.update(
            {
                "ql_Xpi2": {
                    "is_opt": False,
                    "init_v": None,
                    "iter_step": 0.001,
                    "bound": [0.1, 0.5],
                    "nonzdelt": 0.05,
                },
                "ql_detune_pi2": {
                    "is_opt": False,
                    "init_v": None,
                    "iter_step": 0.001,
                    "bound": [-30.0, 30.0],
                    "nonzdelt": 0.1,
                },
                "qh_Xpi2": {
                    "is_opt": False,
                    "init_v": None,
                    "iter_step": 0.001,
                    "bound": [0.1, 0.5],
                    "nonzdelt": 0.05,
                },
                "qh_detune_pi2": {
                    "is_opt": False,
                    "init_v": None,
                    "iter_step": 0.001,
                    "bound": [-30.0, 30.0],
                    "nonzdelt": 0.1,
                },
            }
        )

        return options

    def _check_options(self):
        """Check options."""
        super(NMRBMultiple, self)._check_options()

        qubit_pair = self.child_experiment.qubit_pair
        ql_name = qubit_pair.metadata.std.ql
        qh_name = qubit_pair.metadata.std.qh
        qc_name = qubit_pair.metadata.std.qc
        cz_flow_options_adapter(self)
        scan_name = self.experiment_options.scan_name

        ql_obj = None
        qh_obj = None
        for q_obj in self.child_experiment.qubits:
            q_name = q_obj.name
            if q_name == ql_name:
                ql_obj = q_obj
            elif q_name == qh_name:
                qh_obj = q_obj

        init_v = []
        for k, v in self.experiment_options.input_data.items():
            if v.get("is_opt") is True:
                iv = v.get("init_v")
                if iv is not None:
                    init_v.append(iv)
                else:
                    if k == "ql_amp":
                        init_v.append(qubit_pair.cz_value(ql_name, "amp"))
                    elif k == "qh_amp":
                        init_v.append(qubit_pair.cz_value(qh_name, "amp"))
                    elif k == "qc_amp":
                        init_v.append(qubit_pair.cz_value(qc_name, "amp"))
                    elif k == "ql_phase":
                        init_v.append(qubit_pair.cz_value(ql_name, "phase"))
                    elif k == "qh_phase":
                        init_v.append(qubit_pair.cz_value(qh_name, "phase"))
                    elif k == "ql_freq":
                        init_v.append(qubit_pair.cz_value(ql_name, "freq"))
                    elif k == "qh_freq":
                        init_v.append(qubit_pair.cz_value(qh_name, "freq"))
                    elif k == "detune1":
                        init_v.append(qubit_pair.cz_value(scan_name, "detune1"))
                    elif k == "detune2":
                        init_v.append(qubit_pair.cz_value(scan_name, "detune2"))
                    elif k == "detune":
                        init_v.append(0)
                    elif k == "ql_Xpi2":
                        init_v.append(ql_obj.XYwave.Xpi2)
                    elif k == "ql_detune_pi2":
                        init_v.append(ql_obj.XYwave.detune_pi2)
                    elif k == "qh_Xpi2":
                        init_v.append(qh_obj.XYwave.Xpi2)
                    elif k == "qh_detune_pi2":
                        init_v.append(qh_obj.XYwave.detune_pi2)

        self.set_run_options(init_v=init_v)

    async def _execute_exp(self, parameters) -> Union[int, float]:
        """Special execute experiment api."""

        qubit_pair = self.child_experiment.qubit_pair
        ql_name = qubit_pair.metadata.std.ql
        qh_name = qubit_pair.metadata.std.qh
        qc_name = qubit_pair.metadata.std.qc
        scan_name = self.experiment_options.scan_name
        if self.run_options.count == 0:
            gate_params = qubit_pair.gate_params("cz")
            json_str = json.dumps(gate_params, ensure_ascii=False, indent=4)
            self.file.save_text(json_str, name="gate_params", prefix=".json")

        exp = deepcopy(self.child_experiment)
        ql_obj = None
        qh_obj = None
        for q_obj in exp.qubits:
            q_name = q_obj.name
            if q_name == ql_name:
                ql_obj = q_obj
            elif q_name == qh_name:
                qh_obj = q_obj

        for i, key in enumerate(self.run_options.opt_keys):
            if key == "ql_amp":
                exp.qubit_pair.set_cz_value(ql_name, "amp", parameters[i])
                exp.qubit_pair.set_cz_value(ql_name, "freq", None)
            elif key == "qh_amp":
                exp.qubit_pair.set_cz_value(qh_name, "amp", parameters[i])
                exp.qubit_pair.set_cz_value(qh_name, "freq", None)
            elif key == "qc_amp":
                exp.qubit_pair.set_cz_value(qc_name, "amp", parameters[i])
                exp.qubit_pair.set_cz_value(qc_name, "freq", None)
            elif key == "qh_phase":
                exp.qubit_pair.set_cz_value(qh_name, "phase", parameters[i])
            elif key == "ql_phase":
                exp.qubit_pair.set_cz_value(ql_name, "phase", parameters[i])
            elif key == "qh_freq":
                exp.qubit_pair.set_cz_value(qh_name, "freq", parameters[i])
            elif key == "ql_freq":
                exp.qubit_pair.set_cz_value(ql_name, "freq", parameters[i])
            elif key == "detune1":
                exp.qubit_pair.set_cz_value(scan_name, "detune1", parameters[i])
            elif key == "detune2":
                exp.qubit_pair.set_cz_value(scan_name, "detune2", parameters[i])

            elif key == "detune":
                freq_map = qubit_pair_detune_prepare(
                    self.qubit_pair, self.qubits, label="cz", goal_detune=parameters[i]
                )
                msg = f"detune({parameters[i]})"
                for unit, freq in freq_map.items():
                    msg += f" {unit}({freq[0]})"
                    exp.qubit_pair.set_cz_value(unit, "freq", freq[0])
                pyqlog.info(f"NM detune: {msg}")

            elif key == "ql_Xpi2":
                ql_obj.XYwave.Xpi2 = parameters[i]
            elif key == "ql_detune_pi2":
                ql_obj.XYwave.detune_pi2 = parameters[i]
            elif key == "qh_Xpi2":
                qh_obj.XYwave.Xpi2 = parameters[i]
            elif key == "qh_detune_pi2":
                qh_obj.XYwave.detune_pi2 = parameters[i]

        await self._run_exp(exp)

        if self.__class__.__name__ == "NMRBMultiple":
            return -float(np.mean(exp.analysis.analysis_datas.P00.y))
        else:
            return -float(np.mean(exp.analysis.analysis_datas.F_xeb.y))

    def _set_result_path(self):
        sim = self.analysis.results.sim
        ql = self.qubit_pair.ql
        qh = self.qubit_pair.qh
        qc = self.qubit_pair.qc
        scan_name = self.experiment_options.scan_name

        for i, key in enumerate(self.run_options.opt_keys):
            if key == "ql_amp":
                self.analysis.results["ql_amp"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{ql}.amp",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "qh_amp":
                self.analysis.results["qh_amp"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{qh}.amp",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "qc_amp":
                self.analysis.results["qc_amp"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{qc}.amp",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "qh_phase":
                self.analysis.results["qh_phase"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{qh}.phase",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "ql_phase":
                self.analysis.results["ql_phase"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{ql}.phase",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "qh_freq":
                self.analysis.results["qh_freq"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{qh}.freq",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "ql_freq":
                self.analysis.results["ql_freq"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{ql}.freq",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "detune1":
                self.analysis.results["detune1"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{scan_name}.detune1",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "detune2":
                self.analysis.results["detune2"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{scan_name}.detune2",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "detune":
                freq_map = qubit_pair_detune_prepare(
                    self.qubit_pair, self.qubits, goal_detune=sim.value[i]
                )
                for unit, freq in freq_map.items():
                    self.analysis.results[unit] = AnalysisResult(
                        name=unit,
                        value=freq[0],
                        extra={
                            "path": f"QubitPair.metadata.std.cz.params.{unit}.freq",
                            "name": self.qubit_pair.name,
                        },
                    )
            elif key in ["ql_Xpi2", "ql_detune_pi2", "qh_Xpi2", "qh_detune_pi2"]:
                # Optimize X or X/2 gate parameters.
                q_mark, q_field = key.split("_", 1)
                q_name = ql if q_mark == "ql" else qh
                self.analysis.results[key] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"Qubit.XYwave.{q_field}",
                        "name": q_name,
                    },
                )


class CMAXEBMultiple(CompositeExperiment):
    _sub_experiment_class = XEBMultiple

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options."""
        options = super()._default_experiment_options()
        options.set_validator("num_segments", int)
        options.set_validator("cma_params", dict)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])
        options.set_validator("sigma", float)
        options.set_validator("input_data", dict)

        options.num_segments = 3
        options.cma_params = Options(
            bounds=[0.8, 1.2],
            maxiter=1000,
            popsize=20,
            tolx=1e-3,
            tolfun=1e-4
        )
        options.sigma = 0.01
        options.input_data = {
            "ql": {
                "is_opt": False
            },
            "qh": {
                "is_opt": True
            },
            "qc": {
                "is_opt": False
            },

        }

        return options

    def _check_options(self):
        """Check options."""
        super()._check_options()

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.optimize_result = None
        options.is_reversed = False
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.count = 0
        options.record_list = []
        options.optimize_point = None
        options.opt_count = 0
        options.points = 0

        return options

    def _get_optimize_params(self, q_name, params, bound):
        optimize_point = params.get(q_name).optimize_point
        if optimize_point:
            points = count_segments(optimize_point)
        else:
            points = self.experiment_options.num_segments
            optimize_point = [1, ] * points
        bounds = [np.ones(points) * bound[0], np.ones([points]) * bound[1]]
        self.run_options.opt_count += 1
        return optimize_point, bounds

    async def _sync_composite_run(self):
        sigma = self.experiment_options.sigma
        cma_params = self.experiment_options.cma_params
        input_data = self.experiment_options.input_data
        bound = cma_params.get("bounds")
        maxiter = cma_params.get("maxiter")
        popsize = cma_params.get("popsize")
        tolx = cma_params.get("tolx")
        tolfun = cma_params.get("tolfun")
        width = self.qubit_pair.width("cz")
        sample_rate = QAIO.awg_sample_rate
        points = int(width * sample_rate) + 1
        self.run_options.points = points
        params = self.qubit_pair.gate_params("cz")
        initial_guess = []
        lb = []
        ub = []

        for q_name, v in input_data.items():
            is_opt = v.get("is_opt", False)
            if q_name == "ql" and is_opt:
                q_name = self.qubit_pair.ql
                optimize_point, bounds = self._get_optimize_params(q_name, params, bound)
                lb.extend(bounds[0])
                ub.extend(bounds[1])
                initial_guess.extend(optimize_point)
            elif q_name == "qh" and is_opt:
                q_name = self.qubit_pair.qh
                optimize_point, bounds = self._get_optimize_params(q_name, params, bound)
                lb.extend(bounds[0])
                ub.extend(bounds[1])
                initial_guess.extend(optimize_point)
            elif q_name == "qc" and is_opt:
                q_name = self.qubit_pair.qc
                optimize_point, bounds = self._get_optimize_params(q_name, params, bound)
                lb.extend(bounds[0])
                ub.extend(bounds[1])
                initial_guess.extend(optimize_point)
        cma_params.update({"bounds": [lb, ub]})

        pyqlog.log(
            "EXP",
            f"CMA optimize base {self.child_experiment.__class__.__name__}:\n"
            f"tolfun: {tolfun}\n"
            f"tolx: {tolx}\n"
            f"popsize: {popsize}\n"
            f"Bound: {bound}",
            f"maxiter:{maxiter}",
        )

        res, cost = await cma_es(
            self.nm_opt_func,
            initial_guess,
            sigma,
            cma_params
        )

        self.set_analysis_options(optimize_result=cost)
        self._run_analysis(
            x_data=list(range(self.run_options.count)), analysis_class=CMAAnalysis
        )

    async def nm_opt_func(self, parameters, *args):
        num_segments = self.experiment_options.num_segments
        points = self.run_options.points
        if not self.run_options.opt_count:
            raise ValueError("No optimization of arbitrary bit waveforms!")

        new_parameters = []
        segm_parameters = np.array_split(parameters, self.run_options.opt_count)
        for i in range(self.run_options.opt_count):
            segm_value = restore_array(segm_parameters[i], num_segments, points)
            new_parameters.extend(segm_value)
        self.set_run_options(optimize_point=new_parameters)
        res = await self._execute_exp(new_parameters)
        pyqlog.log("EXP", f"CMA FUNC - {self.run_options.count}: {new_parameters} | {res}")
        pyqlog.log("EXP", f"CMA FUNC - {self.run_options.count}: {res}")
        record = copy.copy(list(new_parameters))
        record.insert(0, self.run_options.count)
        record.append(res)
        self.run_options.count += 1
        try:
            self.run_options.record_list.append(record)
            self.file.save_data(np.array(self.run_options.record_list).T, name="params")
        except Exception as e:
            pyqlog.warning(f"{e}, error occurred when saving parameters")
        return res

    async def _run_exp(self, exp):
        exp.set_parent_file(
            self,
            description=f"CMA-{self.run_options.count}",
            index=self.run_options.index,
        )
        self.run_options.index += 1
        self._check_simulator_data(exp, self.run_options.count)
        await exp.run_experiment()
        self._experiments.append(exp)

    def _callback(self, fsim: float):
        pass

    async def _execute_exp(self, parameters) -> Union[int, float]:
        """Run execute experiment part."""
        exp = deepcopy(self.child_experiment)

        qubit_pair = self.child_experiment.qubit_pair
        qh_name = qubit_pair.metadata.std.qh
        qc_name = qubit_pair.metadata.std.qc
        ql_name = qubit_pair.metadata.std.ql
        input_data = self.experiment_options.input_data

        if self.run_options.count == 0:
            gate_params = qubit_pair.gate_params("cz")
            json_str = json.dumps(gate_params, ensure_ascii=False, indent=4)
            self.file.save_text(json_str, name="gate_params", prefix=".json")

        opt_count = self.run_options.opt_count

        if opt_count > 0:
            parameters = np.array_split(parameters, opt_count)

        for idx, (q_name, v) in enumerate(input_data.items()):
            is_opt = v.get("is_opt", False)
            if q_name == "ql" and is_opt:
                exp.qubit_pair.set_cz_value(ql_name, "optimize_point", parameters[idx % opt_count])
            if q_name == "qh" and is_opt:
                exp.qubit_pair.set_cz_value(qh_name, "optimize_point", parameters[idx % opt_count])
            if q_name == "qc" and is_opt:
                exp.qubit_pair.set_cz_value(qc_name, "optimize_point", parameters[idx % opt_count])

        await self._run_exp(exp)

        as_dict = exp.analysis.analysis_datas
        analysis_data = as_dict.get("F_xeb")
        return -float(np.mean(analysis_data.y))

    def _set_result_path(self):
        optimize_point = self.run_options.optimize_point
        opt_count = self.run_options.opt_count
        qh = self.qubit_pair.qh
        ql = self.qubit_pair.ql
        qc = self.qubit_pair.qc
        input_data = self.experiment_options.input_data

        if optimize_point is not None:
            optimize_point = np.array_split(optimize_point, opt_count)

            for idx, (q_name, v) in enumerate(input_data.items()):
                is_opt = v.get("is_opt", False)
                if q_name == "ql" and is_opt:
                    pyqlog.info(
                        f"{self.qubit_pair.name} will set {ql} optimize_point: {optimize_point[idx % opt_count]}")
                    self.analysis.results[ql] = AnalysisResult(
                        name=ql,
                        value=optimize_point[idx % opt_count].tolist(),
                        extra={
                            "path": f"QubitPair.metadata.std.cz.params.{ql}.optimize_point",
                            "name": self.qubit_pair.name,
                        },
                    )
                elif q_name == "qh" and is_opt:
                    pyqlog.info(
                        f"{self.qubit_pair.name} will set {qh} optimize_point: {optimize_point[idx % opt_count]}")
                    self.analysis.results[qh] = AnalysisResult(
                        name=qh,
                        value=optimize_point[idx % opt_count].tolist(),
                        extra={
                            "path": f"QubitPair.metadata.std.cz.params.{qh}.optimize_point",
                            "name": self.qubit_pair.name,
                        },
                    )
                elif q_name == "qc" and is_opt:
                    pyqlog.info(
                        f"{self.qubit_pair.name} will set {qc} optimize_point: {optimize_point[idx % opt_count]}")
                    self.analysis.results[qc] = AnalysisResult(
                        name=qc,
                        value=optimize_point[idx % opt_count].tolist(),
                        extra={
                            "path": f"QubitPair.metadata.std.cz.params.{qc}.optimize_point",
                            "name": self.qubit_pair.name,
                        },
                    )
        print("update sucess")
