# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:2
msgid "pyQCat.analysis.library.RBAnalysis"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.curve_analysis.CurveAnalysis`"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis:1
msgid "A class to analyze general randomized benchmarking experiment."
msgstr "用来分析处理通用随机门标签的分析类"

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:1
msgid "Initialize the analysis object."
msgstr "初始化一个新的分析对象"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param
#: pyQCat.analysis.top_analysis.TopAnalysis.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:4
msgid "pyQCat Experiments Data container class."
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:7
msgid "Whether the experiment contains sub-experiments."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.library.RBAnalysis.__init__>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`from_sub_analysis "
"<pyQCat.analysis.library.RBAnalysis.from_sub_analysis>`\\ \\(x\\_data\\, "
"sub\\_analysis\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:25:<autosummary>:1
msgid "This interface is used for composite experiment."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`run_analysis "
"<pyQCat.analysis.library.RBAnalysis.run_analysis>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:25:<autosummary>:1
msgid "Run analysis on experiment data."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`set_options <pyQCat.analysis.library.RBAnalysis.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:25:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`show_results "
"<pyQCat.analysis.library.RBAnalysis.show_results>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:25:<autosummary>:1
msgid "Show analysis results"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RBAnalysis.rst:27
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_datas "
"<pyQCat.analysis.library.RBAnalysis.analysis_datas>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis datas."
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`data_filter <pyQCat.analysis.library.RBAnalysis.data_filter>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid "The parameters provided for data filter functions."
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`drawer <pyQCat.analysis.library.RBAnalysis.drawer>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid "A short-cut for curve drawer instance."
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_data "
"<pyQCat.analysis.library.RBAnalysis.experiment_data>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid "Return the experiment data for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`has_child <pyQCat.analysis.library.RBAnalysis.has_child>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis has_child value."
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.library.RBAnalysis.options>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis options for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.library.RBAnalysis.quality>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis data quality."
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`results <pyQCat.analysis.library.RBAnalysis.results>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1:<autosummary>:1
msgid "Get the result datas."
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:1
msgid "Create the Analysis options, and set some fields."
msgstr "创建分析选项，并设置一些属性"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:3
msgid "**Analysis Options**:"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:5
msgid "**fit_model (FitModel)** - Curve fitting model, the formula is as follows:"
msgstr "**fit_model (FitModel)** - 曲线拟合模型，拟合公式如下:"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:7
msgid ""
"y = {\\rm amp} \\cdot {\\rm base}^{\\left(x + {\\rm x0} \\right)} + {\\rm"
" baseline}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:10
msgid ""
"**x_label (str)** - The labels of the X-axis in the resulting graph, "
"default is `Number of cliffords`"
msgstr "**x_label (str)** - 结果图中的X轴标签，默认为 ``Number of cliffords``"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:13
msgid ""
"**quality_bounds (List)** - Fit quality evaluation metrics, default is "
"`[0.9, 0.85, 0.77]`"
msgstr "**quality_bounds (List)** - 拟合质量评估阈值, 默认为 ``[0.9, 0.85, 0.77]``"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:15
msgid "**depths (List)** - RB scan depth, determined by experiment"
msgstr "**depths (List)** - RB扫描深度，由实验决定"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:17
msgid ""
"**k (int)** - Random number of times at each depth, determined by "
"experiment."
msgstr "**k (int)** - 每个深度下的重复次数，由实验决定"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:19
msgid ""
"**result_parameters (List)** - Expected Extracted Experimental Results, "
"default is `['rc', 'rg', \"fidelity\"]`."
msgstr "**result_parameters (List)** - 期望提取的实验结果，默认为 ``[rc, rg, fidelity]``"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._create_analysis_data
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:21
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._create_analysis_data
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options
#: pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._default_options:22
msgid "RB experiment analysis options."
msgstr "RB实验分析选项"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:1
msgid "Create algorithmic guess with analysis options and curve data."
msgstr "创建曲线拟合初始值"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:3
msgid "We do as follows:"
msgstr "我们这样做的："

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:5
msgid ""
"Use the mean of the data excluding the first 5 points as the initial "
"`baseline`"
msgstr "剔除y值前5个点取均值作为 `baseline`"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:6
msgid ""
"The difference between the first y value and the baseline is used as the "
"initial `amp`"
msgstr "y值首个点与 `baseline` 差值作为初始 `amp`"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:7
msgid "`base` defaults to 0.9"
msgstr "初始 `base` 默认设置为 0.9"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:10
msgid "Fit options filled with user provided guess and bounds."
msgstr "拟合选型"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:12
msgid "Formatted data collection to fit."
msgstr "用于曲线拟合的标准数据"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:14
msgid ""
":py:data:`~typing.Union`\\[:py:class:`~pyQCat.analysis.specification.FitOptions`,"
" "
":py:class:`~typing.List`\\[:py:class:`~pyQCat.analysis.specification.FitOptions`]]"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._guess_fit_param:15
msgid "List of fit options that are passed to the fitter function."
msgstr "添加了拟合模型的拟合选型"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._create_analysis_data:1
msgid "Create Analysis data provided for detailed analyze."
msgstr "创建实验分析数据"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._create_analysis_data:5
msgid ""
"Since the RB experiment will repeat k times of experiments at a certain "
"depth m, it is necessary to reshape the experimental results."
msgstr "因为RB实现在每个深度m下重复执行k次实验，因此构造分析数据是吗，需要进行 ``reshape()`` 操作"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._create_analysis_data:8
msgid ":py:class:`~pyQCat.structures.QDict`"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._create_analysis_data:9
msgid ""
"A QDict object, key represents data type and value is CurveAnalysisData "
"object."
msgstr "一个 QDict 对象，键表示数据类值表为 CurveAnalyusisData 对象"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:1
msgid "Extract analysis results from important fit parameters."
msgstr "提取分析结果"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:3
msgid "The error rate of a clifford individual is:"
msgstr "一个cliffords个体的错误率"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:5
msgid "rc = \\frac{1 - base}{2}"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:9
msgid ""
"An average individual in a single-bit Clifford cluster contains 1.875 "
"single gates, so the single-bit gate error rate is calculated as follows:"
msgstr "每个单门clifford个体中平均有1.875的单门，因此单门平均错误率为："

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:12
msgid "rg = \\frac{rc}{1.875}"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:16
msgid "So the basic book fidelity is calculated as follows:"
msgstr "所以基本门保真度为："

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._extract_result:18
msgid "fidelity = 1 - rg"
msgstr ""

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._visualization:1
msgid "Visualization of experimental results"
msgstr "实验结果可视化"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._visualization:3
msgid "We will draw four graphs:"
msgstr "我们这样做的："

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._visualization:5
#, fuzzy
msgid "Plot a scatter plot of repeated experiments at each depth;"
msgstr "绘制了每个深度下的采集数据的散点图"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._visualization:6
msgid "Plot the frequency sampling results at each depth and fit;"
msgstr "绘制了每个深度下平均采样结果及拟合结果"

#: of pyQCat.analysis.library.rb_analysis.RBAnalysis._visualization:7
msgid "Plot Fidelity Calculation Results Title;"
msgstr "绘制了保真度计算结果值标题栏"

#~ msgid "Bases: :class:`pyQCat.analysis.curve_analysis.CurveAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.analysis.library.RBAnalysis.__init__>`\\"
#~ " \\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.RBAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_analysis "
#~ "<pyQCat.analysis.library.RBAnalysis.run_analysis>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options "
#~ "<pyQCat.analysis.library.RBAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_datas "
#~ "<pyQCat.analysis.library.RBAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ":obj:`data_filter <pyQCat.analysis.library.RBAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid "The parameters provided for data filter funtions."
#~ msgstr ""

#~ msgid ":obj:`drawer <pyQCat.analysis.library.RBAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_data "
#~ "<pyQCat.analysis.library.RBAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ":obj:`has_child <pyQCat.analysis.library.RBAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.library.RBAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.library.RBAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":obj:`results <pyQCat.analysis.library.RBAnalysis.results>`\\"
#~ msgstr ""

