﻿pyQCat.analysis.library.QubitSpectrumPreAnalysis
================================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: QubitSpectrumPreAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~QubitSpectrumPreAnalysis.__init__
      ~QubitSpectrumPreAnalysis.from_sub_analysis
      ~QubitSpectrumPreAnalysis.run_analysis
      ~QubitSpectrumPreAnalysis.set_options
      ~QubitSpectrumPreAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~QubitSpectrumPreAnalysis.analysis_datas
      ~QubitSpectrumPreAnalysis.data_filter
      ~QubitSpectrumPreAnalysis.drawer
      ~QubitSpectrumPreAnalysis.experiment_data
      ~QubitSpectrumPreAnalysis.has_child
      ~QubitSpectrumPreAnalysis.options
      ~QubitSpectrumPreAnalysis.quality
      ~QubitSpectrumPreAnalysis.results
   
   