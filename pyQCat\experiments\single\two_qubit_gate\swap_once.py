# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/27
# __author:       <PERSON>, <PERSON><PERSON><PERSON>

"""
SwapOnce Experiment: Sub experiment of Swap experiment,
scan control line frequency modulation width.
"""

from copy import deepcopy
from typing import List

from ....analysis.algorithms import IQdiscriminator
from ....analysis.library import SwapOnceAnalysis
from ....errors import <PERSON><PERSON>ield<PERSON>rror, ExperimentOptionsError
from ....log import pyqlog
from ....pulse.pulse_function import stimulate_state_pulse, zero_pulse
from ....pulse.pulse_lib import Constant
from ....pulse_adjust import params_to_pulse
from ....structures import MetaData, Options, QDict
from ....tools.calculator import qubit_pair_validate
from ....tools.utilities import get_multi_readout_channels, qarange
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment

READ_TYPE = [
    "qh-01",
    "qh-02",
    "qh-012",
    "ql-01",
    "ql-02",
    "ql-012",
    "union-01-01",
    "union-02-02",
    "union-012-01",
    "union-012-012",
]

DATE_KEY_MAP = {
    "10": {
        "ql-01": "P0",
        "qh-01": "P1",
        "union-01-01": "P10",
    },
    "01": {
        "ql-01": "P1",
        "qh-01": "P0",
        "union-01-01": "P01",
    },
    "11": {
        "ql-01": "P1",
        "qh-01": "P1",
        "union-01-01": "P11",
    },
}


class SwapOnce(TopExperiment):
    """SwapOnce scan z line pulse width list."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("swap_state", ["10", "11", "01"])
        options.set_validator("scan_buffer", bool)
        options.set_validator("time_list", list)
        options.set_validator("label", ["zz", "cz"])

        options.swap_state = "11"
        options.readout_type = "ql-01"
        options.scan_buffer = False
        options.time_list = qarange(20, 80, 2.5)
        options.label = "cz"

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []

        options.max_buffer = None
        options.support_context = [StandardContext.CGC]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("interaction_location", int)
        options.set_validator("data_key", list)
        options.set_validator("fit_model_name", ["swap_once_cosine", "cosine"])
        options.set_validator("cut_width", float)

        options.quality_bounds = [0.98, 0.95, 0.85]
        options.interaction_location = 1
        options.data_key = None
        options.label = "cz"
        options.fit_model_name = "swap_once_cosine"
        options.cut_width = 27.5

        options.x_label = "Width [ns]"

        return options

    @staticmethod
    def set_xy_pulses(self):
        """Set SwapOnce experiment XY pulses."""
        eop = self.experiment_options
        rop = self.run_options
        cz_width = rop.width

        for qubit in rop.env_bits:
            if qubit.name == rop.qh.name:
                state = eop.swap_state[0]
            elif qubit.name == rop.ql.name:
                state = eop.swap_state[1]
            elif qubit.name.startswith("q"):
                state = "0"
            else:
                continue

            state_pulse = stimulate_state_pulse(state, qubit)
            xy_pulse_list = []
            for width in eop.time_list:
                new_state_pulse = deepcopy(state_pulse)
                if eop.scan_buffer is False:
                    cz_width = width
                offset_pulse = Constant(cz_width, 0, name="XY")
                xy_pulse = new_state_pulse() + offset_pulse()
                xy_pulse_list.append(xy_pulse)

            self.play_pulse("XY", qubit, xy_pulse_list)

    @staticmethod
    def set_z_pulses(self):
        """Set SwapOnce experiment XY pulses."""
        eop = self.experiment_options
        rop = self.run_options
        pair = self.qubit_pair
        cz_width = rop.width
        zero_x = zero_pulse(rop.qh, "Z")

        for qubit in rop.env_bits:
            q_assign_pulse = deepcopy(zero_x)
            s_gate_params = deepcopy(rop.gate_params.get(qubit.name))

            z_pulse_list = []
            for time in eop.time_list:
                new_q_assign_pulse = deepcopy(q_assign_pulse)

                if eop.scan_buffer is True:
                    new_width = cz_width
                    default_buffer = pair.cz_value(qubit.name, "buffer", eop.label)
                    new_buffer = (
                        cz_width - (time + 2 * (rop.max_buffer - default_buffer))
                    ) / 2
                else:
                    new_width = time
                    new_buffer = pair.cz_value(qubit.name, "buffer", eop.label)

                s_gate_params.update(
                    {
                        "time": new_width,
                        "sigma": pair.cz_value(qubit.name, "sigma", eop.label),
                        "buffer": new_buffer,
                    }
                )

                target_pulse = params_to_pulse(**s_gate_params)
                z_pulse = new_q_assign_pulse() + target_pulse()
                z_pulse_list.append(z_pulse)

            self.play_pulse("Z", qubit, z_pulse_list)

    @staticmethod
    def update_instrument(self):
        """Update Instrument params."""
        if self.experiment_options.scan_buffer is False:
            for channel in self.experiment_options.multi_readout_channels:
                sweep_delay = self._pulse_time_list[
                    : len(self.experiment_options.time_list)
                ]
                self.sweep_readout_trigger_delay(channel, sweep_delay)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "swap_state": self.experiment_options.swap_state,
            "is_amend": self.experiment_options.is_amend,
            "label": self.experiment_options.label,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        validate_qubit_pair_cz_std(self, self.experiment_options.label)
        validate_two_qubit_exp_read_options(self)
        validate_data_key(self)
        self._check_pulse_params()
        self.set_run_options(
            x_data=self.experiment_options.time_list, analysis_class=SwapOnceAnalysis
        )

    def _check_pulse_params(self):
        eop = self.experiment_options
        pair = self.qubit_pair

        max_buffer = max(
            [pair.cz_value(bit, "buffer", eop.label) for bit in pair.env_bits]
        )

        if eop.scan_buffer:
            width = pair.width(eop.label)
            if width < max_buffer * 2 + max(eop.time_list):
                raise ExperimentOptionsError(
                    self.label, msg=f"CZ width does not meet the maximum tc of the scan"
                )
            self.set_run_options(max_buffer=max_buffer)
            self.set_analysis_options(x_label="Tc [ns]")
            pyqlog.debug(f"Scan buffer is {eop.scan_buffer}, time list scan tc!")
        else:
            if min(eop.time_list) < 2 * max_buffer:
                raise ExperimentOptionsError(
                    self.label,
                    msg=f"Min width is {min(eop.time_list)}, but max buffer is {max_buffer}",
                )
            pyqlog.debug(f"Scan buffer is {eop.scan_buffer}, time list scan width!")
            self.set_analysis_options(x_label="Width [ns]")

        self.set_experiment_options(swap_state=str(eop.swap_state))
        self.set_analysis_options(label=eop.label, result_name=self.qubit_pair.name)

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "zz_tc":
                path = f"QubitPair.metadata.std.{self.experiment_options.label}.width"
                result.extra["path"] = path


def set_measure_pulses(exp: TopExperiment):
    ql = exp.run_options.ql
    qh = exp.run_options.qh
    readout_type = exp.experiment_options.readout_type

    if readout_type in ["ql-01", "ql-02", "ql-012"]:
        exp._bind_probe_inst(ql)
        exp._set_single_readout_pulse(qubit=ql)
    elif readout_type in ["qh-01", "qh-02", "qh-012"]:
        exp._bind_probe_inst(qh)
        exp._set_single_readout_pulse(qubit=qh)
    else:
        exp._set_union_readout_pulse(qubits=[qh, ql])


def validate_qubit_pair_cz_std(exp, label: str = "cz"):
    if exp.qubit_pair is None:
        raise ExperimentFieldError(exp, "Qubit Pair is None!")

    qubit_pair = exp.qubit_pair
    base_bits = []
    base_bits.extend(exp.qubits)
    base_bits.extend(exp.couplers)

    parking_bits = qubit_pair.parking_bits
    gate_params = qubit_pair.gate_params(label)
    width = qubit_pair.width(label)

    ql, qh, qc = exp.ql, exp.qh, exp.qc
    parking_qubit_objs = []
    for qubit in base_bits:
        if qubit.name == qubit_pair.qc:
            parking_qubit_objs.append(qubit)
        elif qubit.name in parking_bits:
            parking_qubit_objs.append(qubit)

    if len(parking_qubit_objs) != len(parking_bits):
        raise ExperimentFieldError(exp, "Parking qubit error, please check!")

    if qh is None or ql is None:
        raise ExperimentFieldError(exp, "No find qh or ql, please check!")

    # bugfix 2023/12/26: qh / ql con not validate from drive freq
    # if qh.drive_freq <= ql.drive_freq:
    #     raise ExperimentFieldError(
    #         exp,
    #         f"qh {qh} frequency {qh.drive_freq}MHz < ql {ql} "
    #         f"frequency {ql.drive_freq}MHz, "
    #         f"please check ql_name and qh_name!",
    #     )

    # qubit_pair.validate(qubits=base_bits)
    qubit_pair_validate(qubit_pair, qubits=base_bits)
    env_bits = [qh, ql]
    env_bits.extend(parking_qubit_objs)

    options = QDict(
        qh=qh, ql=ql, qc=qc, gate_params=gate_params, width=width, env_bits=env_bits
    )

    try:
        exp.set_run_options(**options)
    except AttributeError as e:
        pyqlog.warning(e)
    finally:
        return options


def validate_two_qubit_exp_read_options(exp: TopExperiment):
    eop = exp.experiment_options
    rop = exp.run_options
    pair = exp.qubit_pair
    cz_bits = pair.env_bits

    def _check_dcm_exist():
        flag = True
        ry = None

        if exp.discriminator is None:
            flag = False
        elif isinstance(exp.discriminator, IQdiscriminator):
            qubit = exp.discriminator.name
            if qubit not in cz_bits:
                flag = False
            else:
                name = "ql" if qubit == pair.ql else "qh"
                ry = f"{name}-{exp.discriminator.level_str}"
        elif isinstance(exp.discriminator, List):
            if len(exp.discriminator) > 2:
                flag = False
            else:
                qh_level, ql_level = None, None
                for dcm in exp.discriminator:
                    if dcm.name == pair.ql:
                        ql_level = dcm.level_str
                    elif dcm.name == pair.qh:
                        qh_level = dcm.level_str
                    else:
                        flag = False
                ry = f"union-{qh_level}-{ql_level}"

        if not flag:
            raise ExperimentFieldError(
                exp, f"experiment context dcm ({exp.discriminator}) error!"
            )

        if hasattr(eop, "readout_type"):
            eop.readout_type = ry

        return ry

    readout_type = _check_dcm_exist()

    # get readout channel
    if readout_type in ["ql-01", "ql-012"]:
        multi_readout_channels = [rop.ql.readout_channel]
        measure_qubits = [rop.ql]
    elif readout_type in ["qh-01", "qh-012"]:
        multi_readout_channels = [rop.qh.readout_channel]
        measure_qubits = [rop.qh]
    else:
        multi_readout_channels = get_multi_readout_channels([rop.qh, rop.ql])
        measure_qubits = [rop.qh, rop.ql]

    # close dynamic
    if readout_type not in ["ql-01", "qh-01"]:
        eop.is_dynamic = 0

    exp.set_experiment_options(
        data_type="I_Q",
        multi_readout_channels=multi_readout_channels,
    )

    if eop.use_simulator:
        acquisition_key = "simulator"
    elif "2" in readout_type:
        acquisition_key = "weirdo"
    else:
        acquisition_key = "normal"
    exp.set_run_options(acquisition_key=acquisition_key, measure_qubits=measure_qubits)


def validate_data_key(exp: TopExperiment):
    swap_state = exp.experiment_options.swap_state
    exp.experiment_options.swap_state = str(swap_state)

    if exp.analysis_options.data_key is None:
        swap_state = exp.experiment_options.swap_state
        readout_type = exp.experiment_options.readout_type

        data_key = DATE_KEY_MAP.get(str(swap_state)).get(readout_type)
        if data_key:
            exp.analysis_options.data_key = [data_key]
