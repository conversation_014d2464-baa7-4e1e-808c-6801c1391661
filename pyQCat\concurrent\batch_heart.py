# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/12
# __author:       <PERSON><PERSON><PERSON>

import asyncio
import os
import threading
import time
from collections import deque
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Callable

import psutil
import zmq
from loguru import logger
from zmq.asyncio import Context, Poller

from pyQCat.structures import QDict, Singleton
from pyQCat.tools.app_data_collector import AppDataCollector
from pyQCat.types import MonsterServerOp

from .data_client import (
    CONCURRENT_CACHE,
    build_ipc_file,
    decode_msg,
    encode_msg,
    settings,
)


@dataclass
class AnalysisStruct:
    id: str
    function: Callable[[Any], Any]
    kwargs: dict = field(default_factory=dict)


class RecordMode(Enum):
    BS = 0x01
    BE = 0x02
    FS = 0x03
    FE = 0x04
    EE = 0x05
    RF = 0x06
    BA = 0x07  # Batch Analysis


class RecordThread(metaclass=Singleton):
    def __init__(
        self,
        task_id: str = "",
        api_addr: str = "",
        courier_addr: str = "",
        topic: str = "",
    ):
        self.task_id = task_id
        self.queue: deque = deque()
        self.running = True
        self.finish = False
        self.courier_client = AppDataCollector()
        self._func_map = {
            RecordMode.BS: self.courier_client.batch_start_signal,
            RecordMode.BE: self.courier_client.batch_end_signal,
            RecordMode.FS: self.courier_client.batch_flow_start_signal,
            RecordMode.FE: self.courier_client.batch_flow_end_signal,
            RecordMode.EE: self.courier_client.batch_exp_end_signal,
            RecordMode.RF: self._refresh_task,
            RecordMode.BA: self._batch_analysis,
        }

        self.task_id = task_id
        self.task_info = ""
        self.socket = None
        self.sub_socket = None
        self.sub_poller = None
        self.thread = None
        self._pid = os.getpid()

        self.transfer_sub_socket = Context.instance().socket(zmq.SUB)
        self.transfer_sub_socket.connect(
            build_ipc_file(settings.pub_url + CONCURRENT_CACHE["transfer_name"])
        )
        self.transfer_sub_socket.setsockopt_string(zmq.SUBSCRIBE, "")

        self.poller = Poller()
        self.poller.register(self.transfer_sub_socket, zmq.POLLIN)

        if api_addr:
            self.socket = Context.instance().socket(zmq.DEALER)
            self.socket.connect(api_addr)
            self.socket.setsockopt(zmq.IDENTITY, self.task_id.encode("utf-8"))
            self.poller.register(self.socket, zmq.POLLIN)

        if courier_addr:
            self.sub_socket = Context.instance().socket(zmq.SUB)
            self.sub_socket.setsockopt(zmq.SUBSCRIBE, topic.encode("utf-8"))
            self.sub_socket.connect(courier_addr)
            self.poller.register(self.sub_socket, zmq.POLLIN)

        self._batch_analysis_result = {}

        self.metadata = QDict(
            auto_refresh=True if courier_addr else False,
            pause=False,
            calibration=False,
            interval=0,
            units=[],
            loops=100,
            need_refresh=False,
            last_refresh_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            global_options=None,
        )

    def start(self):
        if self.thread is None:
            self.thread = threading.Thread(target=self.run, daemon=True)
            self.thread.start()

    def run(self):
        try:
            asyncio.run(self.async_run())
        except Exception as e:
            import traceback

            logger.error(f"Async record thread error | {e} | {traceback.format_exc()}")
        finally:
            logger.info("Async record thread finish ...")
            self.finish = True
            self.running = False

    async def async_run(self):
        await asyncio.gather(self.heart(), self.async_monitor(), self.refresh_signal())

    async def heart(self):
        while self.running and self.socket is not None:
            memory_info = psutil.Process(self._pid).memory_info()
            rss = memory_info.rss / 1024 / 1024 / 1024  # GB
            data = dict(
                memo=f"{round(rss, 3)} GB",
                last_refresh_time=self.metadata.last_refresh_time,
                interval=self.metadata.interval,
                units=self.metadata.units,
                loops=self.metadata.loops,
                pause=self.metadata.pause,
                auto_refresh=self.metadata.auto_refresh,
            )
            await self.socket.send_multipart(
                [
                    MonsterServerOp.HEART_TS.value,
                    self.task_id.encode("utf-8"),
                    encode_msg(data),
                ]
            )
            await asyncio.sleep(5)

    async def async_monitor(self):
        while self.running:
            if self.queue:
                func, data = self.queue.popleft()
                if func in self._func_map:
                    await self._func_map[func](data)
            else:
                await asyncio.sleep(0.5)

    async def refresh_signal(self):
        calibration_done_code = "calibration_done".encode("utf-8")
        calibration_start_code = "calibration_start".encode("utf-8")

        while self.running:
            socks = dict(await self.poller.poll(timeout=1000))

            if self.sub_socket and self.sub_socket in socks:
                _, operation, *_ = await self.sub_socket.recv_multipart()
                if operation == calibration_done_code:
                    logger.info(f"receive refresh signal | {operation}")
                    self.metadata.need_refresh = True
                    self.metadata.calibration = False
                elif operation == calibration_start_code:
                    self.metadata.calibration = True

            if self.transfer_sub_socket in socks:
                res = await self.transfer_sub_socket.recv_string()
                await AppDataCollector().task_time_out(res)
                logger.warning(f"receive transfer pub overdue task | {res}")

            if self.socket in socks:
                code, message = await self.socket.recv_multipart()
                logger.debug(f"receive api send message | {code}, {message}")
                if code == MonsterServerOp.SYSTEM_CONFIG.value:
                    self.metadata.global_options = decode_msg(message, parse_json=True)
                elif code == MonsterServerOp.UPDATE_TASK.value:
                    self.metadata.update(decode_msg(message, parse_json=True))

    async def _refresh_task(self, *data):
        if self.socket:
            await self.socket.send_multipart(
                [MonsterServerOp.REFRESH_TS.value, self.task_id.encode("utf-8")]
            )

    async def _batch_analysis(self, task: AnalysisStruct):
        async def _inner():
            from pyQCat.concurrent.calculate_resource import (
                BrokenProcessPool,
                ConcurrentCR,
            )

            logger.info("Start batch analysis ....")
            try:
                result = await ConcurrentCR().run_concurrent_job(
                    task.function, task.kwargs
                )
            except (BrokenProcessPool, Exception) as e:
                result = str(e)
                logger.error(f"async task analysis error {e}")
            self._batch_analysis_result[task.id] = result
            logger.info("End batch analysis ....")

        asyncio.create_task(_inner())

    def add_message(self, message):
        if self.running is False:
            logger.error("record thread is stop!")
        self.queue.append(message)

    def acquire_batch_analysis_result(self, task: AnalysisStruct):
        if task.id in self._batch_analysis_result:
            return self._batch_analysis_result.pop(task.id)

    def refresh_last_update_time(self):
        self.metadata.last_refresh_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def stop(self):
        while self.queue:
            time.sleep(0.1)
        self.running = False
        while self.finish is False:
            time.sleep(0.1)
