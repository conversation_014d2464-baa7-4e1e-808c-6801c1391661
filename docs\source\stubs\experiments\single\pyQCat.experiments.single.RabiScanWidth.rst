﻿pyQCat.experiments.single.RabiScanWidth
=======================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: RabiScanWidth

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~RabiScanWidth.__init__
      ~RabiScanWidth.acquire_pulse
      ~RabiScanWidth.cal_fidelity
      ~RabiScanWidth.experiment_info
      ~RabiScanWidth.from_experiment_context
      ~RabiScanWidth.get_qubit_str
      ~RabiScanWidth.get_xy_pulse
      ~RabiScanWidth.jupyter_schedule
      ~RabiScanWidth.options_table
      ~RabiScanWidth.play_pulse
      ~RabiScanWidth.plot_schedule
      ~RabiScanWidth.run
      ~RabiScanWidth.set_analysis_options
      ~RabiScanWidth.set_experiment_options
      ~RabiScanWidth.set_multiple_IF
      ~RabiScanWidth.set_multiple_index
      ~RabiScanWidth.set_parent_file
      ~RabiScanWidth.set_run_options
      ~RabiScanWidth.set_sweep_order
      ~RabiScanWidth.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~RabiScanWidth.analysis
      ~RabiScanWidth.analysis_options
      ~RabiScanWidth.experiment_options
      ~RabiScanWidth.run_options
   
   