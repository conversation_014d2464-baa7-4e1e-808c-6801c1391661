# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:2
msgid "pyQCat.analysis.library.RabiAmpAnalysis"
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis:1
msgid ""
"Bases: "
":py:class:`~pyQCat.analysis.oscillation_analysis.OscillationAnalysis`"
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis:1
msgid "An analysis class for getting pi-pulse amplitude."
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis:4
msgid "Notes"
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis:5
msgid "Get Xpi as pi-pulse amplitude."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:1
msgid "Initialize the analysis object."
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._extract_result
#: pyQCat.analysis.top_analysis.TopAnalysis.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:4
msgid "pyQCat Experiments Data container class."
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:7
msgid "Whether the experiment contains sub-experiments."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.library.RabiAmpAnalysis.__init__>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`from_sub_analysis "
"<pyQCat.analysis.library.RabiAmpAnalysis.from_sub_analysis>`\\ "
"\\(x\\_data\\, sub\\_analysis\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:25:<autosummary>:1
msgid "This interface is used for composite experiment."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`run_analysis "
"<pyQCat.analysis.library.RabiAmpAnalysis.run_analysis>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:25:<autosummary>:1
msgid "Run analysis on experiment data."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`set_options "
"<pyQCat.analysis.library.RabiAmpAnalysis.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:25:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`show_results "
"<pyQCat.analysis.library.RabiAmpAnalysis.show_results>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:25:<autosummary>:1
msgid "Show analysis results"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.RabiAmpAnalysis.rst:27
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_datas "
"<pyQCat.analysis.library.RabiAmpAnalysis.analysis_datas>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis datas."
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`data_filter "
"<pyQCat.analysis.library.RabiAmpAnalysis.data_filter>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid "The parameters provided for data filter functions."
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`drawer <pyQCat.analysis.library.RabiAmpAnalysis.drawer>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid "A short-cut for curve drawer instance."
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_data "
"<pyQCat.analysis.library.RabiAmpAnalysis.experiment_data>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid "Return the experiment data for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`has_child <pyQCat.analysis.library.RabiAmpAnalysis.has_child>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis has_child value."
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.library.RabiAmpAnalysis.options>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis options for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.library.RabiAmpAnalysis.quality>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis data quality."
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`results <pyQCat.analysis.library.RabiAmpAnalysis.results>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1:<autosummary>:1
msgid "Get the result datas."
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:1
msgid "Return the default analysis options."
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._default_options:4
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._extract_result:1
msgid ""
"Extract Xpi from fitted data. The map of amp-phase to P0-P1 depends on "
"the location of the cavity frequency."
msgstr ""

#: of pyQCat.analysis.library.rabi_analysis.RabiAmpAnalysis._extract_result:6
msgid "The basis for selecting data."
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.analysis.oscillation_analysis.OscillationAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_analysis "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.run_analysis>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_options "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_datas "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`data_filter "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`drawer <pyQCat.analysis.library.RabiAmpAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_data "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`has_child "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`options <pyQCat.analysis.library.RabiAmpAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`quality <pyQCat.analysis.library.RabiAmpAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`results <pyQCat.analysis.library.RabiAmpAnalysis.results>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.analysis.oscillation_analysis.OscillationAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_analysis "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.run_analysis>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_datas "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`data_filter "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid "The parameters provided for data filter funtions."
#~ msgstr ""

#~ msgid ":obj:`drawer <pyQCat.analysis.library.RabiAmpAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_data "
#~ "<pyQCat.analysis.library.RabiAmpAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ":obj:`has_child <pyQCat.analysis.library.RabiAmpAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.library.RabiAmpAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.library.RabiAmpAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":obj:`results <pyQCat.analysis.library.RabiAmpAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "Quality of fit outcome."
#~ msgstr ""

