﻿pyQCat.experiments.composite.DCCrosstalk
========================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: DCCrosstalk

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~DCCrosstalk.__init__
      ~DCCrosstalk.component_experiment
      ~DCCrosstalk.from_experiment_context
      ~DCCrosstalk.get_qubit_str
      ~DCCrosstalk.options_table
      ~DCCrosstalk.run
      ~DCCrosstalk.set_analysis_options
      ~DCCrosstalk.set_experiment_options
      ~DCCrosstalk.set_parent_file
      ~DCCrosstalk.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~DCCrosstalk.analysis
      ~DCCrosstalk.analysis_options
      ~DCCrosstalk.child_experiment
      ~DCCrosstalk.experiment_options
      ~DCCrosstalk.run_options
   
   