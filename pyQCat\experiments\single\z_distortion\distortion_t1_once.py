# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2021/08/24
# __author:       ssfang

"""
Distortion T1 Experiment Once.
"""

from ....analysis.algorithms import IQdiscriminator
from ....analysis.library import DistortionT1Analysis
from ....errors import ExperimentFieldError
from ....log import pyqlog
from ....parameters import options_wrapper
from ....pulse.pulse_function import pi_pulse
from ....pulse.pulse_lib import Constant, GaussianDown, GaussianUp
from ....structures import MetaData, Options
from ....tools import qarange
from ....types import StandardContext
from ...coupler_experiment_v1 import CouplerBaseExperimentV1 as CouplerBaseExperiment
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class DistortionT1(TopExperiment):
    """Once Distortion Test."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            z_amp (float): The const z amp of Z line.
            gauss_sigma (float): The sigma of GaussUP, GaussDown wave.
            gauss_width (float): The width of GaussUP, GaussDown wave.
            const_width (float): The width of Constant wave.
            ta (float): Set a width of Constant wave.
            tb (float): No need set, we will set in `self.check_options()`.
            add_tb_width (float): Set tb value will add width.
            z_offset_list (List, np.ndarray): Scan Z offset range.
            xy_delay (float): Set delay of XY pulse.

        """
        options = super()._default_experiment_options()

        options.set_validator("z_offset_list", list, limit_null=True)
        options.set_validator("gauss_sigma", float)
        options.set_validator("gauss_width", float)
        options.set_validator("const_width", float)
        options.set_validator("ta", float)
        # options.set_validator('tb', float)
        options.set_validator("add_tb_width", float)
        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("xy_delay", float)
        options.set_validator("run_mode", ["normal", "new_case"])

        options.z_amp = -0.5
        options.gauss_sigma = 5.0
        options.gauss_width = 15.0
        options.const_width = 100
        options.ta = 20000
        options.tb = 2000
        options.add_tb_width = 100
        options.z_offset_list = qarange(-0.15, 0.15, 0.002)
        options.xy_delay = 0

        options.run_mode = "normal"

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator(
            "fit_model_name",
            [
                "lorentzian",
                "bi_lorentz_tilt",
                "gauss_lorentzian",
                "skewed_lorentzian",
                "skewed_gauss_lorentz",
            ],
        )
        options.set_validator("cali_offset_method", ["direct", "undirect"])
        options.set_validator("cut_index", bool)
        options.set_validator("adjust_noise", bool)
        # options.set_validator("n_multiple", float)

        options.data_key = None
        options.quality_bounds = [0.95, 0.85, 0.75]
        options.fit_model_name = "lorentzian"
        options.cali_offset_method = "direct"
        options.p0_history = None
        options.cut_index = True

        options.adjust_noise = True
        options.n_multiple = 4.0

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.exp_qubit = None
        options.drag_qubit = None
        options.rdz_amp = None

        options.injection_func = ["get_xy_pulse", "get_z_pulse"]
        options.support_context = [
            StandardContext.QC,
            StandardContext.CPC,
        ]

        return options

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulse."""
        xy_pulse = self.get_xy_pulse(
            self.run_options.drag_qubit, self.experiment_options
        )
        self.play_pulse("XY", self.run_options.drag_qubit, xy_pulse)

    @staticmethod
    def set_z_pulses(self):
        """Set z pulse."""
        z_pulse_list = self.get_z_pulse(
            self.run_options.exp_qubit, self.experiment_options
        )
        self.play_pulse("Z", self.run_options.exp_qubit, z_pulse_list)

    def _check_options(self):
        """
        1. 如果没有IQ判据, 给出警告
        2. 如果是比特畸变, 分析P1, 如果是Coupler畸变, 分析P0
        """
        super()._check_options()
        if self.is_coupler_exp:
            data_key = ["P0"]
            drag_width = self.driveQ.XYwave.time + 2 * self.driveQ.XYwave.offset
            target_component = self.coupler
        elif self.is_coupler_exp is False and self.coupler:
            data_key = ["P1"]
            drag_width = self.qubits[0].XYwave.time + 2 * self.qubits[0].XYwave.offset
            target_component = self.coupler
        else:
            data_key = ["P1"]
            drag_width = self.qubit.XYwave.time + 2 * self.qubit.XYwave.offset
            target_component = self.qubit

        if self.discriminator is None:
            data_key = ["Amp", "Phase"]
            data_type = "amp_phase"
            pyqlog.warning("DistortionT1 experiment recommends IQ discriminator.")
        else:
            data_type = "I_Q"

        # adjust tb by xy_delay, to optimize readout
        xy_delay = self.experiment_options.xy_delay
        add_tb_width = self.experiment_options.add_tb_width
        new_tb = xy_delay + drag_width / 2 + add_tb_width

        # new case distortion, set ac, readout point
        z_amp = self.experiment_options.z_amp
        run_mode = self.experiment_options.run_mode

        # 2024.10.29 Bugfixed, register pulse not use self.compensates keys
        base_qubits = []
        base_qubits.extend(self.qubits or [])
        base_qubits.extend(self.couplers or [])

        # bugfixed, options_wrapper will run twice !!!
        rdz_amp = self.run_options.rdz_amp
        if run_mode == "new_case":
            for qc in base_qubits:
                if qc.name == target_component.name:
                    qc.ac = 0.0
                    old_rdz_amp = qc.readout_point.amp
                    new_rdz_amp = old_rdz_amp + z_amp
                    qc.readout_point_model = "Constant"
                    qc.readout_point = Options(amp=new_rdz_amp)
                    self.run_options.rdz_amp = new_rdz_amp

                    pyqlog.info(
                        f"target: {target_component}, run_mode: {run_mode}, "
                        f"ac: {qc.ac} V, "
                        f"readout_point_model: {qc.readout_point_model}, "
                        f"readout_point.amp: {qc.readout_point.amp} V"
                    )
            if self.ac_bias.get(target_component.name):
                old_awg_bias = self.ac_bias[target_component.name][1]
                # self.ac_bias[target_component.name][1] = old_awg_bias - z_amp

                # Adjust asyncio version, 2024.05.15
                new_awg_bias = old_awg_bias - z_amp
                self.run_options.ac_bias = {target_component.name: new_awg_bias}

        self.set_experiment_options(data_type=data_type, tb=new_tb)
        self.set_analysis_options(data_key=data_key)
        self.set_run_options(
            exp_qubit=self.qubit,
            drag_qubit=self.qubit,
            x_data=self.experiment_options.z_offset_list,
            analysis_class=DistortionT1Analysis,
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"XY-Delay": (self.experiment_options.xy_delay, "ns")}
        return metadata

    @staticmethod
    def get_xy_pulse(qubit, options: Options):
        gauss_width = options.gauss_width
        const_width = options.const_width
        ta = options.ta
        tb = options.tb
        xy_delay = options.xy_delay

        align_with = gauss_width + const_width + ta + xy_delay
        all_width = gauss_width + const_width + ta + tb + gauss_width

        drag_middle = pi_pulse(qubit)
        drag_width = drag_middle.width
        half_drag_width = drag_width / 2

        const_head = Constant(align_with - half_drag_width, 0, "XY")
        new_width = all_width - const_head.width - drag_width
        if new_width > 0:
            const_tail = Constant(new_width, 0, "XY")
            xy_pulse = const_head() + drag_middle() + const_tail()
        else:
            xy_pulse = const_head() + drag_middle()

        return [xy_pulse]

    @staticmethod
    def get_z_pulse(qubit, options: Options):
        """Get Z pulse list"""
        run_mode = options.run_mode
        z_amp = options.z_amp
        gauss_sigma = options.gauss_sigma
        gauss_width = options.gauss_width
        const_width = options.const_width
        ta = options.ta
        tb = options.tb
        z_offset_list = options.z_offset_list

        z_pulse_list = []
        for z_offset in z_offset_list:
            if run_mode == "normal":
                gauss_up = GaussianUp(gauss_width, z_offset, sigma=gauss_sigma)
                const_one = Constant(const_width, z_offset)
                # const_two = Constant(ta, z_amp + z_offset, 'Z')
                const_two = Constant(ta, z_amp)
                const_three = Constant(tb, z_offset)
                gauss_down = GaussianDown(gauss_width, z_offset, sigma=gauss_sigma)
                z_pulse = (
                    gauss_up()
                    + const_one()
                    + const_two()
                    + const_three()
                    + gauss_down()
                )
            else:
                gauss_up = GaussianUp(gauss_width, z_offset, sigma=gauss_sigma)
                const_one = Constant(const_width + ta, z_offset)
                const_two = Constant(tb + gauss_width, z_amp + z_offset)
                z_pulse = gauss_up() + const_one() + const_two()

            z_pulse.bit = qubit.name
            z_pulse.sweep = "sweep z offset"
            z_pulse_list.append(z_pulse)
        return z_pulse_list


class CouplerDistortionT1(CouplerBaseExperiment, DistortionT1):
    """Coupler Distortion T1 Once"""

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulse."""
        xy_pulse = self.get_xy_pulse(self.driveQ, self.experiment_options)
        self.compose_xy_pulses(xy_pulse)

    @staticmethod
    def set_z_pulses(self):
        """Set z pulse."""
        z_pulse_list = self.get_z_pulse(self.driveQ, self.experiment_options)
        self.compose_z_pulses(z_pulse_list)


class CouplerDistortionZZ(DistortionT1):
    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.support_context = [StandardContext.CC]
        return options

    def _check_options(self):
        if self.coupler is None:
            raise ExperimentFieldError(
                self.label, f"No coupler information is configured!"
            )

        if not isinstance(self.discriminator, IQdiscriminator):
            raise ExperimentFieldError(self.label, "No iq discriminator is configured!")

        super()._check_options()
        coupler = self.compensates.get(self.coupler)
        xy_delay = coupler.x_delay
        qubit = self.compensates.get(self.qubit)
        qubit.x_delay = xy_delay
        self.set_run_options(exp_qubit=self.coupler, drag_qubit=self.qubit)
