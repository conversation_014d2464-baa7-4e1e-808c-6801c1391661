# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/index.rst:7
msgid "Welcome to pyQCat-Monster's documentation!"
msgstr ""

#: ../../source/index.rst:19
msgid "Indices and tables"
msgstr ""

#: ../../source/index.rst:21
msgid ":ref:`genindex`"
msgstr ""

#: ../../source/index.rst:22
msgid ":ref:`modindex`"
msgstr ""

#: ../../source/index.rst:23
msgid ":ref:`search`"
msgstr ""

