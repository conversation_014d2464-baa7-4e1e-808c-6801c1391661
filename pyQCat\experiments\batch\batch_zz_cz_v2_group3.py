# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON>

import copy

import numpy as np

from ...log import pyqlog
from ...qaio_property import QAIO
from ...qubit import <PERSON><PERSON><PERSON>, <PERSON>ubi<PERSON>, QubitPair
from ...tools.utilities import amp_to_freq, qarange, validate_ac_spectrum
from ..batch_experiment import BatchExperiment


class BatchCZ_OnlineCali(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.pair_names = None
        options.zz_point_map = None
        options.cz_point_map = None
        options.iter_swap_freq = None
        options.extra_widths = None
        options.raw_flows = [
            "FixedPointCalibration_01_qh",
            "FixedPointCalibration_01_ql",
            "FixedSwapFreqCaliCoupler_01",
            "SwapOnce_01",
        ]

        options.zz_freq_cali_flows = [
            "FixedPointCalibration_zz_qh",
            "FixedPointCalibration_zz_ql",
        ]

        options.zz_swap_cali_flows = ["FixedSwapFreqCaliCoupler2_zz", "SwapOnce_zz"]

        options.zz_flows = ["ZZTimingComposite"]

        options.cz_freq_cali_flows = [
            # "FixedPointCalibration_cz_qh",
            # "FixedPointCalibration_cz_ql",
            # "FixedSwapFreqCaliCoupler2_cz"
        ]

        options.cz_check_flows = [
            "XEBMultiple_2",
        ]
        options.cz_optimize_flows = [
            "NMXEBMultiple",
            "XEBMultiple_2",
        ]
        options.cz_flows = [
            "Swap",
            "LeakagePre",
            "LeakageAmp",
            "CPhaseTMSE",
            # "ConditionalPhaseFixed",
            "SQPhaseTMSE",
            # "RBInterleavedMultiple",
            # "NMRBMulitple",
            # "XEBMultiple_1",
            # "NMXEBMultiple",
            "XEBMultiple_2",
        ]
        options.swap_state = "11"
        options.swap_state_map = {}
        options.cz_sweep_count = 10
        options.zz_sweep_count = 3
        options.swap_sweep_count = 4
        options.sweep_step = 20
        options.auto_set_interaction_freq = True
        options.fix_interaction_freq = False
        options.zz_threshold = 50
        options.cz_threshold = 400
        options.swap_threshold = 400
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.cz_fl_map = {}
        options.cz_fh_map = {}
        return options

    def _count_max_sweep_count(self, pair_collections, label):
        if self.experiment_options.fix_interaction_freq:
            return 1
        elif self.experiment_options.auto_set_interaction_freq:
            point_map = {}
            sweep_count = 0
            for pair in pair_collections:
                if label == "zz":
                    zz_inter_freq = self._get_interection_freq_for_zztiming(
                        pair,
                        step=self.experiment_options.sweep_step,
                        threshold=self.experiment_options.zz_threshold,
                    )
                    point_map.update({pair: zz_inter_freq})
                    self.experiment_options.zz_point_map = point_map
                    sweep_count = self.experiment_options.zz_sweep_count
                elif label == "cz":
                    cz_inter_freq = self._get_interection_freq_for_cz(
                        pair,
                        step=self.experiment_options.sweep_step,
                        threshold=self.experiment_options.cz_threshold,
                    )
                    point_map.update({pair: cz_inter_freq})
                    self.experiment_options.cz_point_map = point_map
                    sweep_count = self.experiment_options.cz_sweep_count
                else:
                    swap_inter_freq = self._get_interection_freq_for_01swap(
                        pair,
                        step=self.experiment_options.sweep_step,
                        threshold=self.experiment_options.swap_threshold,
                    )
                    point_map.update({pair: swap_inter_freq})
                    self.experiment_options.swap_point_map = point_map
                    sweep_count = self.experiment_options.swap_sweep_count
            return min([sweep_count, max([len(v) for v in point_map.values()])])
        else:
            if label == "zz":
                return max(
                    [len(v) for v in self.experiment_options.zz_point_map.values()]
                )
            elif label == "cz":
                return max(
                    [len(v) for v in self.experiment_options.cz_point_map.values()]
                )
            else:
                return max(
                    [len(v) for v in self.experiment_options.swap_point_map.values()]
                )

    def _init_coupler_zamp(self, pair_obj):
        qc: Coupler = self.context_manager.chip_data.cache_coupler.get(pair_obj.qc)
        ## 将coupler电压偏置到与它的idle_point同侧同分支的腰部
        idle_point = qc.idle_point
        dc_max = qc.dc_max
        dc_min = qc.dc_min
        mid_point = abs(dc_max - dc_min) / 3 * 2
        zamp = mid_point - abs(idle_point)
        if idle_point != 0:
            zamp = zamp if idle_point > 0 else -zamp
        else:
            zamp = zamp if dc_max < 0 else -zamp
        return zamp

    def _change_work_point(self, i: int, pair_collections: list, label: str = "zz"):
        working_units = []
        if label == "zz":
            point_map = self.experiment_options.zz_point_map
        elif label == "cz":
            point_map = self.experiment_options.cz_point_map
        else:
            point_map = self.experiment_options.swap_point_map
            # raise Exception(f"label:{label} should be either cz or zz ")
        if not self.experiment_options.fix_interaction_freq:
            for pair in pair_collections:
                if pair in self.experiment_options.swap_state_map.keys():
                    swap_state = self.experiment_options.swap_state_map[pair]
                else:
                    swap_state = self.experiment_options.swap_state

                if len(point_map.get(pair)) > i:
                    working_units.append(pair)
                    freq = point_map[pair][i]
                    pair_obj: QubitPair = (
                        self.context_manager.chip_data.cache_qubit_pair.get(pair)
                    )
                    qh: Qubit = self.context_manager.chip_data.cache_qubit.get(
                        pair_obj.qh
                    )

                    if label == "zz":
                        gate_params = pair_obj.gate_params("zz")
                        zamp = gate_params.get(pair_obj.qc).amp
                        if not zamp:
                            zamp = self._init_coupler_zamp(pair_obj)
                        gate_params.get(pair_obj.qc).amp = zamp

                    elif label == "cz":
                        gate_params = pair_obj.gate_params("cz")
                        zamp = gate_params.get(pair_obj.qc).amp
                        if not zamp:
                            zamp = self._init_coupler_zamp(pair_obj)
                        gate_params.get(pair_obj.qc).amp = zamp

                    else:
                        if pair in self.experiment_options.swap_state_map.keys():
                            swap_state = self.experiment_options.swap_state_map[pair]
                        else:
                            swap_state = "01"

                        gate_params = pair_obj.gate_params("cz")
                        zamp = self._init_coupler_zamp(pair_obj)
                        gate_params.get(pair_obj.qc).amp = zamp

                    if swap_state in ["01", "10"]:
                        gate_params.get(pair_obj.qh).freq = freq
                        gate_params.get(pair_obj.ql).freq = freq
                    elif swap_state == "11":
                        gate_params.get(pair_obj.qh).freq = freq - qh.anharmonicity
                        gate_params.get(pair_obj.ql).freq = freq
                    elif swap_state == "11-02":
                        ql: Qubit = self.context_manager.chip_data.cache_qubit.get(
                            pair_obj.ql
                        )
                        gate_params.get(pair_obj.qh).freq = freq
                        gate_params.get(pair_obj.ql).freq = freq - ql.anharmonicity
                    else:
                        raise NameError(f"swap_sate({swap_state}) error!")

                    pyqlog.log("EXP", f"{pair_obj} {label} point: qc zamp= ({zamp})V")
                    pyqlog.log(
                        "EXP", f"{pair_obj} swap {swap_state} {label} point ({freq})MHz"
                    )
            return working_units
        else:
            return pair_collections

    def _change_zz_time_width(
        self, swap_freq: float, pair_collections: list, extra: float = 0
    ):
        for pair in pair_collections:
            pair_obj: QubitPair = self.context_manager.chip_data.cache_qubit_pair.get(
                pair
            )
            gate_params = pair_obj.gate_params("zz")
            qh_tb = gate_params.get(pair_obj.qh).buffer
            ql_tb = gate_params.get(pair_obj.ql).buffer
            qc_tb = gate_params.get(pair_obj.qc).buffer
            tb = max([qh_tb, ql_tb, qc_tb])
            zz_width = 1000 / swap_freq / 4 + 2 * tb + extra
            zz_width = np.round(
                zz_width // (1 / QAIO.awg_sample_rate) * (1 / QAIO.awg_sample_rate), 3
            )
            pair_obj.metadata.std.zz.width = zz_width
            pyqlog.log(
                "FLOW", f"{pair_obj} swap_freq {swap_freq}MHz, zz_width {zz_width} ns"
            )

        return pair_collections

    def _run_batch(self):
        pass_group = {}
        qubit_bad_group = {}
        for index, pair_collections in enumerate(self.experiment_options.pair_names):
            qubit_bad_group.update({f"{index}": copy.copy(pair_collections)})
            working_units = self._run_cz_raw(pair_collections)
            if working_units:
                working_units = self._run_zz_flows(working_units)
            if working_units:
                pass_units = self._run_cz_flows(working_units)
                pass_group.update({f"{index}": pass_units})
                pyqlog.info(f"passed units in group {index}:{pass_units}")
                if pass_units:
                    for qubit in pass_units:
                        qubit_bad_group[f"{index}"].remove(qubit)
                pyqlog.info(
                    f"qubit failed units in group {index}:{qubit_bad_group[f'{index}']}"
                )
        pyqlog.info(f"qubit failed units:{qubit_bad_group}")

    def _run_cz_raw(self, pair_collections):
        if self.experiment_options.raw_flows:
            good_units = []
            sweep_count = self._count_max_sweep_count(pair_collections, "01")
            for i in range(sweep_count):
                working_units = self._change_work_point(i, pair_collections, "01")
                if working_units:
                    pass_units = self._run_flow(
                        flows=self.experiment_options.raw_flows,
                        physical_units=working_units,
                    )
                    if pass_units:
                        for unit in pass_units:
                            good_units.append(unit)
                            pair_collections.remove(unit)
            return good_units
        else:
            return pair_collections

    def _run_zz_flows(self, pair_collections):
        if self.experiment_options.zz_flows:
            good_units = []
            sweep_count = self._count_max_sweep_count(pair_collections, "zz")
            for i in range(sweep_count):
                working_units = self._change_work_point(i, pair_collections, "zz")
                if working_units:
                    pyqlog.log("FLOW", f"Change zz point count {i}")
                    if self.experiment_options.zz_freq_cali_flows:
                        working_units = self._run_flow(
                            flows=self.experiment_options.zz_freq_cali_flows,
                            physical_units=working_units,
                        )
                    for swap_freq in self.experiment_options.iter_swap_freq:
                        # update qc z amp for FixedSwapFreqCaliCoupler experiment
                        fsf = self.params_manager.exp_map.get(
                            "FixedSwapFreqCaliCoupler2_zz"
                        )
                        fsf.options_for_regular_exec["experiment_options"][
                            "frequency"
                        ] = swap_freq
                        pyqlog.log("FLOW", f"Change swap point {swap_freq}")
                        if working_units:
                            if self.experiment_options.zz_swap_cali_flows:
                                # run zz timing flow
                                zz_working_units = self._run_flow(
                                    flows=self.experiment_options.zz_swap_cali_flows,
                                    physical_units=working_units,
                                )
                            else:
                                zz_working_units = working_units
                            # if zz_working_units:
                            # run zz timing flow
                            for extra_width in self.experiment_options.extra_widths:
                                pyqlog.log(
                                    "FLOW", f"Change zz extra_width {extra_width}"
                                )
                                zz_working_units = self._change_zz_time_width(
                                    swap_freq, zz_working_units, extra_width
                                )
                                if zz_working_units:
                                    pass_units = self._run_flow(
                                        flows=self.experiment_options.zz_flows,
                                        physical_units=zz_working_units,
                                    )
                                    if pass_units:
                                        for unit in pass_units:
                                            good_units.append(unit)
                                            pair_collections.remove(unit)
                                            working_units.remove(unit)
                                            zz_working_units.remove(unit)
            return good_units
        else:
            return pair_collections

    def _run_cz_flows(self, pair_collections):
        good_units = []
        if self.experiment_options.cz_check_flows:
            pass_units = self._run_flow(
                flows=self.experiment_options.cz_check_flows,
                physical_units=pair_collections,
            )
            if pass_units:
                for unit in pass_units:
                    pair_collections.remove(unit)
                    good_units.append(unit)

        if self.experiment_options.cz_optimize_flows:
            pass_units = self._run_flow(
                flows=self.experiment_options.cz_optimize_flows,
                physical_units=pair_collections,
            )
            if pass_units:
                for unit in pass_units:
                    pair_collections.remove(unit)
                    good_units.append(unit)

        if self.experiment_options.cz_flows:
            sweep_count = self._count_max_sweep_count(pair_collections, "cz")
            for i in range(sweep_count):
                working_units = self._change_work_point(i, pair_collections, "cz")
                if working_units:
                    if self.experiment_options.cz_freq_cali_flows:
                        working_units = self._run_flow(
                            flows=self.experiment_options.cz_freq_cali_flows,
                            physical_units=working_units,
                        )
                    if working_units:
                        pass_units = self._run_flow(
                            flows=self.experiment_options.cz_flows,
                            physical_units=working_units,
                        )
                        if pass_units:
                            for unit in pass_units:
                                good_units.append(unit)
                                pair_collections.remove(unit)

        return good_units

    def _get_interection_freq_for_zztiming(
        self, pair: str, step: float = 10, threshold: float = 100
    ):
        pair_obj: QubitPair = self.context_manager.chip_data.cache_qubit_pair.get(pair)
        qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)
        ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
        fl0 = pair_obj.gate_params("zz").get(pair_obj.ql).freq
        fh0 = pair_obj.gate_params("zz").get(pair_obj.qh).freq
        fl = ql.drive_freq
        fh = qh.drive_freq
        alpha = qh.anharmonicity

        if ql.tunable:
            ql_params, fl_max = validate_ac_spectrum(ql.ac_spectrum, fl)
        else:
            fl_max = 0
        if qh.tunable:
            qh_params, fh_max = validate_ac_spectrum(qh.ac_spectrum, fh)
        else:
            fh_max = 0

        if fl_max == 0 and fh_max > 0:
            return [fl]
        if fh_max == 0 and fl_max > 0:
            return [fh + alpha]

        fl_max = fl_max - 2
        fh_max = fh_max - 2
        fl_min = (
            amp_to_freq(ql.ac_spectrum, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
        )
        fh_min = (
            amp_to_freq(qh.ac_spectrum, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
        )
        f_int_min = fl_min
        f_int_max = fl_max

        if fh_min > fl_min - alpha:
            f_int_min = fh_min + alpha
        if fh_max < fl_max - alpha:
            f_int_max = fh_max + alpha

        inter_freq_list = np.array(qarange(f_int_min, f_int_max, step))
        ql_distance = np.abs(inter_freq_list - fl)
        qh_distance = np.abs(inter_freq_list - alpha - fh)
        distance = ql_distance + qh_distance
        ql_filter = np.argwhere(ql_distance > threshold)
        ql_filter = ql_filter.flatten()
        qh_filter = np.argwhere(qh_distance > threshold)
        qh_filter = qh_filter.flatten()

        legal_index = list(set(ql_filter) & set(qh_filter))
        if legal_index:
            legal_inter_freq = inter_freq_list[legal_index]
            distance_filter = distance[legal_index]
            sort_index = distance_filter.argsort()
            sorted_legal_inter_freq = legal_inter_freq[sort_index]
            if fl0 and fh0:
                sorted_legal_inter_freq.tolist().insert(0, fl0)
            return sorted_legal_inter_freq

        elif fl_max > fh_max:
            # 交换qh, ql
            # pair_obj.metadata.std.qh = ql.name
            # pair_obj.metadata.std.ql = qh.name
            self.experiment_options.swap_state_map.update({pair: "11-02"})
            ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)
            qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
            fl = pair_obj.gate_params("zz").get(pair_obj.ql).freq or ql.drive_freq
            fh = pair_obj.gate_params("zz").get(pair_obj.qh).freq or qh.drive_freq
            alpha = qh.anharmonicity
            ql_params, fl_max = validate_ac_spectrum(ql.ac_spectrum, 0)
            qh_params, fh_max = validate_ac_spectrum(qh.ac_spectrum, 0)
            fl_max = fl_max - 2
            fh_max = fh_max - 2

            fl_min = (
                amp_to_freq(ql.ac_spectrum, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
            )
            fh_min = (
                amp_to_freq(qh.ac_spectrum, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
            )
            f_int_min = fl_min
            f_int_max = fl_max

            if fh_min > fl_min - alpha:
                f_int_min = fh_min + alpha
            if fh_max < fl_max - alpha:
                f_int_max = fh_max + alpha

            inter_freq_list = np.array(qarange(f_int_min, f_int_max, step))
            ql_distance = np.abs(inter_freq_list - fl)
            qh_distance = np.abs(inter_freq_list - alpha - fh)
            distance = ql_distance + qh_distance
            ql_filter = np.argwhere(ql_distance > threshold)
            ql_filter = ql_filter.flatten()
            qh_filter = np.argwhere(qh_distance > threshold)
            qh_filter = qh_filter.flatten()

            legal_index = list(set(ql_filter) & set(qh_filter))
            if legal_index:
                legal_inter_freq = inter_freq_list[legal_index]
                distance_filter = distance[legal_index]
                sort_index = distance_filter.argsort()
                sorted_legal_inter_freq = legal_inter_freq[sort_index]
                if fl0 and fh0:
                    sorted_legal_inter_freq.tolist().insert(0, fh0)
                return sorted_legal_inter_freq
            else:
                max_threshold = threshold
                while max_threshold > 0:
                    max_threshold -= step
                    ql_filter = np.argwhere(ql_distance > max_threshold)
                    ql_filter = ql_filter.flatten()
                    qh_filter = np.argwhere(qh_distance > max_threshold)
                    qh_filter = qh_filter.flatten()
                    legal_index = list(set(ql_filter) & set(qh_filter))
                    if legal_index:
                        legal_inter_freq = inter_freq_list[legal_index]
                        distance_filter = distance[legal_index]
                        sort_index = distance_filter.argsort()
                        sorted_legal_inter_freq = legal_inter_freq[sort_index]
                        pyqlog.warning(
                            f"Can't not find any interection frequnecy at the given threshold:{threshold}. "
                            f"Find {len(sorted_legal_inter_freq)} points of interection frequency, when change to the maximum possible threshold:{max_threshold}"
                        )
                        return sorted_legal_inter_freq

        else:
            max_threshold = threshold
            while max_threshold > 0:
                max_threshold -= step
                ql_filter = np.argwhere(ql_distance > max_threshold)
                ql_filter = ql_filter.flatten()
                qh_filter = np.argwhere(qh_distance > max_threshold)
                qh_filter = qh_filter.flatten()
                legal_index = list(set(ql_filter) & set(qh_filter))
                if legal_index:
                    legal_inter_freq = inter_freq_list[legal_index]
                    distance_filter = distance[legal_index]
                    sort_index = distance_filter.argsort()
                    sorted_legal_inter_freq = legal_inter_freq[sort_index]
                    pyqlog.warning(
                        f"{pair_obj} Can't not find any interection frequnecy at the given threshold:{threshold}. "
                        f"Find {len(sorted_legal_inter_freq)} points of interection frequency, when change to the maximum possible threshold:{max_threshold}"
                    )
                    return sorted_legal_inter_freq

            pyqlog.error(f" {pair_obj} can't not find any interection frequnecy")
            return []

    def _get_interection_freq_for_cz(
        self, pair: str, step: float = 10, threshold: float = 200
    ):
        pair_obj: QubitPair = self.context_manager.chip_data.cache_qubit_pair.get(pair)
        qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)
        ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
        if pair in self.run_options.cz_fl_map.keys():
            fl = self.run_options.cz_fl_map[pair]
            fh = self.run_options.cz_fh_map[pair]
            pyqlog.info(f"{pair}: origin_fl:{fl}, origin_fh:{fh}")
        else:
            fl = pair_obj.gate_params("cz").get(pair_obj.ql).freq or ql.drive_freq
            fh = pair_obj.gate_params("cz").get(pair_obj.qh).freq or qh.drive_freq
        alpha = qh.anharmonicity

        if ql.tunable:
            ql_params, fl_max = validate_ac_spectrum(ql.ac_spectrum, fl)
        else:
            fl_max = 0
        if qh.tunable:
            qh_params, fh_max = validate_ac_spectrum(qh.ac_spectrum, fh)
        else:
            fh_max = 0

        if fl_max == 0 and fh_max > 0:
            pyqlog.warning(f"{pair_obj} interaction set to fl:{fl}MHz")
            return [fl]
        if fh_max == 0 and fl_max > 0:
            pyqlog.warning(f"{pair_obj} interaction set to fh+alpha:{fh + alpha}MHz")
            return [fh + alpha]

        fl_max = fl_max - 2
        fh_max = fh_max - 2
        fl_min = (
            amp_to_freq(ql.ac_spectrum, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
        )
        fh_min = (
            amp_to_freq(qh.ac_spectrum, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
        )
        f_int_min = fl_min
        f_int_max = fl_max

        if fh_min > fl_min - alpha:
            f_int_min = fh_min + alpha
        if fh_max < fl_max - alpha:
            f_int_max = fh_max + alpha

        inter_freq_list = np.array(qarange(f_int_min, f_int_max, step))
        ql_distance = np.abs(inter_freq_list - fl)
        qh_distance = np.abs(inter_freq_list - alpha - fh)
        distance = ql_distance + qh_distance
        ql_filter = np.argwhere(ql_distance < threshold)
        ql_filter = ql_filter.flatten()
        qh_filter = np.argwhere(qh_distance < threshold)
        qh_filter = qh_filter.flatten()

        legal_index = list(set(ql_filter) & set(qh_filter))
        if legal_index:
            legal_inter_freq = inter_freq_list[legal_index]
            distance_filter = distance[legal_index]
            sort_index = distance_filter.argsort()
            sorted_legal_inter_freq = legal_inter_freq[sort_index]
            sorted_legal_inter_freq[0] = fl
            pyqlog.info(f"{pair_obj} {len(sorted_legal_inter_freq)} points find")
            return sorted_legal_inter_freq

        elif fl_max > fh_max:
            # 交换qh, ql
            pyqlog.warning(f"{pair}: change ql and qh")
            pair_obj.metadata.std.qh = ql.name
            pair_obj.metadata.std.ql = qh.name
            # self.experiment_options.swap_state_map.update({pair: '11-02'})
            qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)
            ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
            fl = pair_obj.gate_params("cz").get(pair_obj.ql).freq or ql.drive_freq
            fh = pair_obj.gate_params("cz").get(pair_obj.qh).freq or qh.drive_freq
            alpha = qh.anharmonicity

            fl_max = fl_max - 2
            fh_max = fh_max - 2

            fl_min = (
                amp_to_freq(ql.ac_spectrum, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
            )
            fh_min = (
                amp_to_freq(qh.ac_spectrum, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
            )
            f_int_min = fl_min
            f_int_max = fl_max

            if fh_min > fl_min - alpha:
                f_int_min = fh_min + alpha
            if fh_max < fl_max - alpha:
                f_int_max = fh_max + alpha

            inter_freq_list = np.array(qarange(f_int_min, f_int_max, step))
            ql_distance = np.abs(inter_freq_list - fl)
            qh_distance = np.abs(inter_freq_list - alpha - fh)
            distance = ql_distance + qh_distance
            ql_filter = np.argwhere(ql_distance < threshold)
            ql_filter = ql_filter.flatten()
            qh_filter = np.argwhere(qh_distance < threshold)
            qh_filter = qh_filter.flatten()

            if np.any(ql_filter) and np.any(qh_filter):
                legal_index = list(set(ql_filter) & set(qh_filter))
                legal_inter_freq = inter_freq_list[legal_index]
                distance_filter = distance[legal_index]
                sort_index = distance_filter.argsort()
                sorted_legal_inter_freq = legal_inter_freq[sort_index]
                pyqlog.info(f"{pair_obj} {len(sorted_legal_inter_freq)} points find")
                return sorted_legal_inter_freq
            else:
                min_threshold = threshold
                while min_threshold < 500:
                    min_threshold += step
                    ql_filter = np.argwhere(ql_distance < min_threshold)
                    ql_filter = ql_filter.flatten()
                    qh_filter = np.argwhere(qh_distance < min_threshold)
                    qh_filter = qh_filter.flatten()
                    if np.any(ql_filter) and np.any(qh_filter):
                        legal_index = list(set(ql_filter) & set(qh_filter))
                        legal_inter_freq = inter_freq_list[legal_index]
                        distance_filter = distance[legal_index]
                        sort_index = distance_filter.argsort()
                        sorted_legal_inter_freq = legal_inter_freq[sort_index]
                        pyqlog.warning(
                            f"Can't not find any interection frequnecy at the given threshold:{threshold}. "
                            f"Find {len(sorted_legal_inter_freq)} points of interection frequency, when change to the larger threshold:{min_threshold}"
                        )
                        return sorted_legal_inter_freq

        else:
            min_threshold = threshold
            while min_threshold < 500:
                min_threshold += step
                ql_filter = np.argwhere(ql_distance < min_threshold)
                ql_filter = ql_filter.flatten()
                qh_filter = np.argwhere(qh_distance < min_threshold)
                qh_filter = qh_filter.flatten()
                if np.any(ql_filter) and np.any(qh_filter):
                    legal_index = list(set(ql_filter) | set(qh_filter))
                    legal_inter_freq = inter_freq_list[legal_index]
                    distance_filter = distance[legal_index]
                    sort_index = distance_filter.argsort()
                    sorted_legal_inter_freq = legal_inter_freq[sort_index]
                    pyqlog.warning(
                        f"{pair_obj} Can't not find any interection frequnecy at the given threshold:{threshold}. "
                        f"Find {len(sorted_legal_inter_freq)} points of interection frequency, when change to the maximum possible threshold:{min_threshold}"
                    )
                    return sorted_legal_inter_freq

            pyqlog.error(f" {pair_obj} can't not find any interection frequnecy")
            return []

    def _get_interection_freq_for_01swap(
        self, pair: str, step: float = 10, threshold: float = 100
    ):
        pair_obj: QubitPair = self.context_manager.chip_data.cache_qubit_pair.get(pair)
        qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)
        ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
        self.run_options.cz_fl_map.update(
            {pair: pair_obj.gate_params("cz").get(pair_obj.ql).freq}
        )
        self.run_options.cz_fh_map.update(
            {pair: pair_obj.gate_params("cz").get(pair_obj.qh).freq}
        )
        fl = ql.drive_freq
        fh = qh.drive_freq
        if ql.tunable:
            ql_params, fl_max = validate_ac_spectrum(ql.ac_spectrum, fl)
        else:
            fl_max = 0
        if qh.tunable:
            qh_params, fh_max = validate_ac_spectrum(qh.ac_spectrum, fh)
        else:
            fh_max = 0
        if fl_max == 0 and fh_max > 0:
            fh_min = (
                amp_to_freq(qh.ac_spectrum, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
            )
            if fh_min > fl or fh_max < fl:
                pyqlog.warning(f"{pair}: swap state change to 11")
                self.experiment_options.swap_state_map.update({pair: "11"})
                return self._get_interection_freq_for_cz(pair, step, threshold)
            else:
                pyqlog.warning(f"{pair_obj} interaction set to fl:{fl}MHz")
                return [fl]
        if fh_max == 0 and fl_max > 0:
            fl_min = (
                amp_to_freq(qh.ac_spectrum, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
            )
            if fl_min > fh or fl_max < fh:
                pyqlog.warning(f"{pair}: swap state change to 11")
                self.experiment_options.swap_state_map.update({pair: "11"})
                return self._get_interection_freq_for_cz(pair, step, threshold)
            else:
                pyqlog.warning(f"{pair_obj} interaction set to ff:{fh}MHz")
                return [fh]

        fl_max = fl_max - 2
        fh_max = fh_max - 2
        fl_min = (
            amp_to_freq(ql.ac_spectrum, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
        )
        fh_min = (
            amp_to_freq(qh.ac_spectrum, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
        )
        f_int_min = max([fl_min, fh_min])
        f_int_max = min([fl_max, fh_max])

        if f_int_max < f_int_min:
            pyqlog.warning(f"{pair}: swap state change to 11")
            self.experiment_options.swap_state_map.update({pair: "11"})
            return self._get_interection_freq_for_cz(pair, step, threshold)

        inter_freq_list = np.array(qarange(f_int_min, f_int_max, step))
        ql_distance = np.abs(inter_freq_list - fl)
        qh_distance = np.abs(inter_freq_list - fh)
        distance = ql_distance + qh_distance
        ql_filter = np.argwhere(ql_distance < threshold)
        ql_filter = ql_filter.flatten()
        qh_filter = np.argwhere(qh_distance < threshold)
        qh_filter = qh_filter.flatten()

        legal_index = list(set(ql_filter) & set(qh_filter))
        if legal_index:
            legal_inter_freq = inter_freq_list[legal_index]
            distance_filter = distance[legal_index]
            sort_index = distance_filter.argsort()
            sorted_legal_inter_freq = legal_inter_freq[sort_index]
            pyqlog.info(f"{pair_obj} {len(sorted_legal_inter_freq)} points find")
            return sorted_legal_inter_freq
        else:
            max_threshold = threshold
            while max_threshold < 700:
                max_threshold += 10 * step
                ql_filter = np.argwhere(ql_distance < max_threshold)
                ql_filter = ql_filter.flatten()
                qh_filter = np.argwhere(qh_distance < max_threshold)
                qh_filter = qh_filter.flatten()
                legal_index = list(set(ql_filter) & set(qh_filter))
                if legal_index:
                    legal_inter_freq = inter_freq_list[legal_index]
                    distance_filter = distance[legal_index]
                    sort_index = distance_filter.argsort()
                    sorted_legal_inter_freq = legal_inter_freq[sort_index]
                    pyqlog.warning(
                        f"{pair_obj} Can't not find any interection frequnecy at the given threshold:{threshold}. "
                        f"Find {len(sorted_legal_inter_freq)} points of interection frequency, when change to the larger threshold:{max_threshold}"
                    )
                    return sorted_legal_inter_freq
            pyqlog.error(f" {pair_obj} can't not find any interection frequnecy")
            return []
