# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/12/14
# __author:       <PERSON><PERSON><PERSON>

import copy

from ..structures import ExperimentData
from .base_analysis import BaseAnalysis
from .standard_curve_analysis import StandardCurveAnalysis, StandardIQAnalysis


class SQMCAcquireAnalysis(BaseAnalysis):
    def _data_processing(self):
        data_type = self.experiment_data.metadata.process_meta["data_type"]
        if data_type == "I_Q":
            for idx, x in enumerate(self.experiment_data.x_data):
                metadata = copy.deepcopy(self.experiment_data.metadata)
                metadata.name += f"Loop-{idx}"
                metadata.draw_meta["x"] = x
                metadata
                cur_exp_data = ExperimentData(
                    x_data=[idx],
                    y_data={k: iq_data[idx] for k, iq_data in self.experiment_data.y_data.items()},
                    experiment_id=self.experiment_data.experiment_id,
                    metadata=metadata,
                )
                child_ana = StandardIQAnalysis(cur_exp_data)
                child_ana.run_analysis()
                self.options.sub_analysis.append(child_ana)
        else:
            child_ana = StandardCurveAnalysis(self.experiment_data)
            child_ana.run_analysis()
            self.options.sub_analysis.append(child_ana)
