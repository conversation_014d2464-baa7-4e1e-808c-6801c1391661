<svg width="20" height="20" xmlns="http://www.w3.org/2000/svg">
 <!-- Created with Method Draw - http://github.com/duopixel/Method-Draw/ -->
 <g>
  <title>background</title>
  <rect fill="#0000ff" id="canvas_background" height="22" width="22" y="-1" x="-1"/>
  <g display="none" overflow="visible" y="0" x="0" height="100%" width="100%" id="canvasGrid">
   <rect fill="url(#gridpattern)" stroke-width="0" y="0" x="0" height="100%" width="100%"/>
  </g>
 </g>
 <g>
  <title>Layer 1</title>
  <rect rx="0.05" ry="0.05" id="svg_15" height="16" width="16" y="2" x="2" fill-opacity="null" stroke-opacity="null" stroke-width="2" stroke="#ff0000" fill="none"/>
 </g>
</svg>