# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/07
# __author:       <PERSON><PERSON><PERSON> Shi

"""
Crosstalk experiment.
"""

import copy
import math
from typing import Union

import numpy as np

from ....analysis.fit.fit_models import freq2amp_formula
from ....log import pyqlog
from ....structures import Options
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import Ramsey, RamseyCrosstalk


class Crosstalk(CompositeExperiment):
    """Crosstalk experiment abstract class."""

    _sub_experiment_class = RamseyCrosstalk

    def _create_child_experiment(self) -> RamseyCrosstalk:
        """Create a child experiment object.

        Returns:
            BaseExperiment object.
        """
        if self.couplers:
            target_qubit = self.qubits[0]
            bias_qubit = self.couplers[0]
        else:
            target_qubit, bias_qubit, *_ = self.qubits

        child_exp = self._sub_experiment_class(
            inst=self.inst,
            target_qubit=target_qubit,
            bias_qubit=bias_qubit,
            compensates=self.compensates,
            discriminators=self.discriminator,
            working_dc=self.working_dc,
            ac_bias=self.ac_bias,
        )
        return child_exp

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for crosstalk experiment."""
        options = super()._default_analysis_options()
        options.set_validator("quality_bounds", list)
        options.quality_bounds = [0.995, 0.992, 0.990]
        options.data_key = None

        return options

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options for crosstalk experiment.

        Experiment options:
            delays (Union[List, np.ndarray]): Delay time scanned when performing Ramsey
                                              experiments.
            init_fringe (float): The initialization value of fringe.
            spectrum_params (list, np.ndarray): The fitted parameters of AC/DC spectrum.
            init_v_bias (float): The initialization value of bias voltage.
            v_bias_bound (float): The bias voltage value range of bias qubit.
            freq_offset (float): The offset value of qubit at sweet spot deviates
                                 from target qubit.
            freq_threshold (tuple): The frequency range of target qubit.
            scan_points (int): The bias voltage points to be scanned.
        """
        options = super()._default_experiment_options()

        options.set_validator("delays", list)
        options.set_validator("init_fringe", float)
        options.set_validator("spectrum_params", list)
        options.set_validator("init_v_bias", (-1, 1, 2))
        options.set_validator("v_bias_bound", (-1, 1, 2))
        options.set_validator("freq_offset", float)
        options.set_validator("freq_threshold", tuple)
        options.set_validator("scan_points", (2, 50, 0))
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.delays = None
        options.init_fringe = None
        options.spectrum_params = None
        options.init_v_bias = 0.15
        options.v_bias_bound = 0.7
        options.freq_offset = 50
        options.freq_threshold = (-20, 30)
        options.scan_points = 5

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()
        options.guess_coefficient = None
        options.v_background = 0
        options.v_bias_list = []
        options.v_real_list = []

        return options

    def _check_options(self):
        super()._check_options()
        target_qubit, bias_qubit = self._get_qubits()
        self.analysis_options.result_name = f"{target_qubit.name}.{bias_qubit.name}"

    def _get_qubits(self):
        """Gets the target qubit and the bias qubit."""
        if self.couplers:
            target_qubit = self.qubits[0]
            bias_qubit = self.couplers[0]
        else:
            target_qubit, bias_qubit = self.qubits

        prefix = f"bias-{bias_qubit.name} to target-{target_qubit.name}"
        self._label += prefix

        return target_qubit, bias_qubit

    def _get_runtime_parameters(self, target_qubit):
        """Get the crosstalk experiment run time parameters"""
        fd = target_qubit.drive_freq  # MHz
        # baseband_freq = target_qubit.XYwave.baseband_freq  # MHz
        baseband_freq = 0
        init_fringe = self.experiment_options.init_fringe
        fringe = baseband_freq - init_fringe + self.experiment_options.freq_offset

        return fd, baseband_freq, fringe

    async def _get_v_bias_list(
        self,
        v_target: float,
        fringe: float,
        fd: float,
        fq: float,
        baseband_freq: float,
        z_amp: float = None,
    ):
        """Get the bias voltage list.

        Args:
            v_target (float): The voltage which was applied to target qubit.
            fringe (float): Ramsey oscillation frequency which is used to amplify the difference
                            between the drive frequency and the qubit real frequency.
            fd (float): The drive frequency of qubit.
            fq (float): The real frequency of qubit.
            baseband_freq (float): The intermediate frequency.
            z_amp (float): The Z flux pulse amplitude which was applied to target qubit.
        """
        # Do ramsey experiment with initial bias voltage.
        init_v_bias = (
            self.experiment_options.init_v_bias + self.run_options.v_background
        )
        v_real = await self._run_ramsey(
            index=0,
            v_bias=init_v_bias,
            v_target=z_amp,
            fringe=fringe,
            fd=fd,
            baseband_freq=baseband_freq,
        )
        # remove this sub experiment because just used for guessing bias voltage.
        self._experiments.pop()

        # get bias voltage boundaries.
        v_min, v_max = self._validate_voltage_bounds(v_target, v_real, fq)

        self.run_options.v_bias_list = np.linspace(
            v_min, v_max, self.experiment_options.scan_points
        )

    async def _run_ramsey(
        self,
        index: int,
        v_bias: float,
        fringe: float,
        fd: float,
        baseband_freq: float,
        v_target: float = None,
    ) -> float:
        """Execute ramsey experiment to get the actual voltage applied to target qubit.

        Args:
            v_bias (float): The voltage which was applied to bias qubit.
            v_target (float): The voltage which was applied to target qubit.
            fringe (float): Ramsey oscillation frequency which is used to amplify the difference
                            between the drive frequency and the qubit real frequency.
            fd (float): The drive frequency of qubit.
            baseband_freq (float): The intermediate frequency of pulse.

        Returns:
            v_real (float): The real voltage on target qubit.
        """
        ramsey_exp = copy.deepcopy(self.child_experiment)
        await self._execute_child_experiment(
            ramsey_exp, fringe, v_target, v_bias, index
        )
        v_real = self._get_child_experiment_result(
            ramsey_exp, fringe, fd, baseband_freq
        )

        return v_real

    async def _execute_child_experiment(
        self,
        ramsey_exp: Union[RamseyCrosstalk, Ramsey],
        fringe: float,
        v_target: float,
        v_bias: float,
        index: int,
    ):
        """Run child ramsey experiment."""
        ramsey_exp.set_experiment_options(
            delays=self.experiment_options.delays, fringe=fringe
        )
        if isinstance(ramsey_exp, RamseyCrosstalk):
            ramsey_exp.set_experiment_options(zamp=v_target)
            ramsey_exp.get_bias_pulse(
                delays=self.experiment_options.delays, v_bias=v_bias
            )
        pyqlog.debug(f"vt={v_target} vb={v_bias} index={index}")
        ramsey_exp.set_parent_file(self, f"vt={v_target} vb={v_bias} index={index}")
        self._check_simulator_data(ramsey_exp, index)
        await ramsey_exp.run_experiment()
        # ramsey_exp.clear_params()

    def _get_child_experiment_result(
        self,
        ramsey_exp: Union[RamseyCrosstalk, Ramsey],
        fringe: float,
        fd: float,
        baseband_freq: float,
    ):
        """Get child Ramsey experiment results."""
        result = ramsey_exp.analysis.results
        osc_freq = result.freq.value
        f10 = fd + baseband_freq - fringe - osc_freq
        # Get the real voltage on target qubit by AC/DC spectrum formula.
        v_real = freq2amp_formula(f10, *self.experiment_options.spectrum_params)
        ramsey_exp.analysis.provide_for_parent["target_voltage"] = v_real

        # record experiment data during running.
        self._experiments.append(ramsey_exp)

        return v_real

    def _validate_voltage_bounds(
        self,
        v_target: float,
        v_real: float,
        fq: float,
    ):
        """Validate the bias voltage boundary.

        Use ac/dc spectrum fitted parameters to calculate the theoretical voltage
        boundary value. With the Target qubit frequency varying [+30, -20]MHz,
        calculate the required bias qubit voltage range (v_min~v_max), if bias qubit
        voltage is over 0.7V or less than -0.7V, take +-0.7.

        Args:
            v_target (float): The voltage of the target qubit by calculated.
            fq (float): The qubit frequency.
            v_real (float): The voltage of the target qubit by experiment.

        Returns:
            The boundary value of the bias voltage.
        """
        # Calculate the crosstalk coefficient with formula: y = k * x + b
        # y -> real voltage, x -> bias voltage, b -> target voltage
        k = (v_real - v_target) / self.experiment_options.init_v_bias
        self.set_run_options(guess_coefficient=k)

        pyqlog.info(f"C_TB init: k={k}")

        lb, rb = self.experiment_options.freq_threshold

        v_l = (
            freq2amp_formula(fq + lb, *self.experiment_options.spectrum_params)
            - v_target
        ) / k + self.run_options.v_background

        v_r = (
            freq2amp_formula(fq + rb, *self.experiment_options.spectrum_params)
            - v_target
        ) / k + self.run_options.v_background

        v_l = -1 if math.isnan(v_l) else v_l
        v_r = 1 if math.isnan(v_r) else v_r

        v_min = v_l if v_l < v_r else v_r
        v_max = v_l if v_l > v_r else v_r

        # In case the bias voltage out of the range, we limit the bias voltage range.
        if v_min < -self.experiment_options.v_bias_bound:
            if v_max < -self.experiment_options.v_bias_bound:
                v_min, v_max = -self.experiment_options.v_bias_bound, 0
            else:
                v_min = -self.experiment_options.v_bias_bound
        if v_max > self.experiment_options.v_bias_bound:
            if v_min > self.experiment_options.v_bias_bound:
                v_min, v_max = 0, self.experiment_options.v_bias_bound
            else:
                v_max = self.experiment_options.v_bias_bound

        return v_min, v_max
