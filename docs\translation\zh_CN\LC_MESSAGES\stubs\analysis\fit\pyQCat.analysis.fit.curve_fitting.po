# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/fit/pyQCat.analysis.fit.curve_fitting.rst:2
msgid "pyQCat.analysis.fit.curve\\_fitting"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:1
msgid "Curve fitting using least squares"
msgstr "最小二乘法进行曲线拟合"

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:3
msgid "We used the ``scipy.optimize.curve_fit`` implementation."
msgstr "我们使用 ``scipy.optimize.curve_fit`` 来实现"

#: of pyQCat.analysis.fit.curve_fit.curve_fitting
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:6
msgid "Curve fit to raw data, including x and y."
msgstr "曲线拟合的原始数据，包含了x和y信息"

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:8
msgid ""
"Fitting options, including initial values corresponding to the fitted "
"model, and fitting iteration parameter boundary conditions, etc."
msgstr ""
"拟合选项，包含了与拟合模型相关的拟合初始值，以及迭代和边界等条件"

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:11
msgid "Fit the model, support custom fitting formula."
msgstr "拟合模型，支持自定义公式"

#: of pyQCat.analysis.fit.curve_fit.curve_fitting
msgid "Raises"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:13
msgid "scipy.optimize.curve_fit failed with error."
msgstr "``scipy.optimize.curve_fit`` 拟合错误"

#: of pyQCat.analysis.fit.curve_fit.curve_fitting
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:15
msgid ":py:class:`~pyQCat.analysis.specification.FitData`"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.curve_fitting:16
msgid "Returns a structure of fit parameters, fitted y, and goodness of fit."
msgstr "返回一个有拟合参数，拟合y值以及拟合优度组成的结构体"

#~ msgid "todo"
#~ msgstr ""

