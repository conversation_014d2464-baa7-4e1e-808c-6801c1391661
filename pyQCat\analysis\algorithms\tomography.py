# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/20
# __author:       <PERSON><PERSON><PERSON>

import itertools
from functools import reduce
from typing import List, Dict, Tuple, Optional, Union

import cvxpy
import numpy as np
import qutip as qp
from qutip.qip.circuit import phasegate
from qutip.qobj import Qobj
from scipy import optimize
from scipy.optimize import minimize

from pyQCat.gate import GateBucket
from pyQCat.structures import QDict


def _dot3(A, B, C):
    """Compute the dot product of three matrices"""
    return np.dot(np.dot(A, B), C)


def _qpt_pointer(rhos, e_rhos, return_all=False):
    """Calculates the pointer-basis chi-matrix.

    rhos - array of input density matrices
    e_rhos - array of output density matrices.

    Uses linalg.lstsq to calculate the closest fit
    when the chi-matrix is overdetermined by the data.
    The return_all flag specifies whether to return
    all the parameters returned from linalg.lstsq, such
    as the residuals and the rank of the chi-matrix. By
    default (return_all=False) only chi is returned.
    """

    # the input and output density matrices can have different
    # dimensions, although this will rarely be the case for us.
    d_in = rhos[0].size
    d_out = e_rhos[0].size
    n = len(rhos)

    # reshape the input and output density matrices
    # each row of the resulting matrix has a flattened
    # density matrix (in or out, respectively)
    rhos_mat = np.asarray(rhos).reshape((n, d_in))
    e_rhos_mat = np.asarray(e_rhos).reshape((n, d_out))

    chi, resids, rank, s = np.linalg.lstsq(rhos_mat, e_rhos_mat, rcond=-1)
    if return_all:
        return chi, resids, rank, s
    else:
        return chi


def _transform_chi_pointer(chi_pointer, T, return_all=False):
    """Convert a chi matrix from the pointer basis into a different basis.

    transform_chi_pointer(chi_pointer, As) will transform the chi_pointer
    matrix from the pointer basis (as produced by qpt_pointer, for example)
    into the basis specified by operator elements in the cell array As.
    """
    d_in, d_out = chi_pointer.shape
    chi_flat, resids, rank, s = np.linalg.lstsq(T, chi_pointer.flatten(), rcond=-1)
    chi = chi_flat.reshape((d_out, d_out))
    if return_all:
        return chi, resids, rank, s
    else:
        return chi


def init_qst(us):
    """Initialize quantum state tomography for a set of unitaries.

    Args:
        us: a list of unitary operations that will be applied to the
            state before measuring the diagonal elements.  These unitaries
            should form a 'complete' set to allow the full density matrix
            to be determined, though this is not enforced.

    Returns:
        Returns a transformation matrix that should be passed to qst along
        with measurement data to perform the state tomography.
    """
    us = np.asarray(us)

    # Number of different rotations applied to the system prior
    # to measurement
    # 测量前应用于系统的不同旋转次数
    num_rotations = len(us)

    # Number of states. Normally this will be 2**n where n is the number
    # of qubits measured in the experiment. More generally this is the
    # number of diagonal elements that you measured.
    # For example, imagine that for a given set of rotations on a three qubit
    # system, we measure the following probabilities:
    # |ggg>, |geg>, |egg>, |eeg>
    # then numStates=4.
    # Note that in this example the measurements DO NOT completely determine
    # the three qubit density matrix, but they do determine the density
    # matrix for the subsystem consisting of the first two qubits.
    num_states = len(us[0])

    # we have to be a bit careful here, because things blow up
    # exponentially with the number of qubits.  The first method
    # uses direct indexing to generate the entire transform matrix
    # in one shot.  This is elegant and much faster than for-loop
    # iteration, but uses more memory and so only works for
    # smaller qubit numbers.
    if num_states <= 16:
        # 1-4 qubits
        # For explanation of this code see Matthew Neeley's thesis
        # page 144-146.
        # j = which rotation (U)
        # k = which state (diagonal element)
        def transform(kp, lp):
            _j, _k = divmod(kp, num_states)
            _m, _n = divmod(lp, num_states)
            return us[_j, _k, _m] * us[_j, _k, _n].conj()

        u = np.fromfunction(
            transform, (num_rotations * num_states, num_states**2), dtype=int
        )
    else:
        # 5+ qubits
        u = np.zeros((num_rotations * num_states, num_states**2), dtype=complex)
        for K in range(num_rotations * num_states):
            for L in range(num_states**2):
                j, k = divmod(K, num_states)
                m, n = divmod(L, num_states)
                u[K, L] = us[j, k, m] * us[j, k, n].conj()

    return u


def qst(measure_data, u_array):
    """Convert a set of diagonal measurements into a density matrix.

    Args:
        measure_data: Measured probabilities (diagonal elements) after acting
            on the state with each of the unitaries from the qst protocol.
        u_array: Transformation matrix from init_qst for this protocol, or
            key passed to init_qst under which the transformation was
            saved.
    """
    measure_data = np.asarray(measure_data)
    n = measure_data.shape[1]
    try:
        # Least squares for solving hyper-positive definite linear equations.
        rho_flat, resids, rank, s = np.linalg.lstsq(
            u_array, measure_data.flatten(), rcond=-1
        )
    except Exception:
        raise Exception("qst failed")
    return rho_flat.reshape((n, n)), resids, rank, s


def qst_mle(measure_data, u_array, F=None, rho0=None):
    """State tomography with maximum-likelihood estimation.

    Args:
        measure_data: a 2D array of measured probabilities. The first index indicates
            which operation from Us was applied, while the second index tells
            which measurement result this was (e.g. 000, 001, etc.).
        u_array: the unitary operations that were applied to the system before
            measuring.
        F: a 'fidelity' matrix, relating the actual or 'intrinsic'
            probabilities to the measured probabilities, via pms = dot(F, pis).
            If no fidelity matrix is given, the identity will be used.
        rho0: an initial guess for the density matrix, e.g. from linear
            tomography.
    """
    measure_data = np.array(measure_data)

    N = len(u_array[0])  # size of density matrix

    if F is None:
        F = np.eye(N)

    try:
        # 取矩阵左下角的索引
        indices_re = np.tril_indices(N)
        # 去矩阵左下角的索引，偏移一位
        indices_im = np.tril_indices(N, -1)
    except AttributeError:
        indices_re = (
            np.hstack([[k] * (k + 1) for k in range(N)]),
            np.hstack([list(range(k + 1)) for k in range(N)]),
        )
        indices_im = (
            np.hstack([[k + 1] * (k + 1) for k in range(N - 1)]),
            np.hstack([list(range(k + 1)) for k in range(N - 1)]),
        )
    n_re = len(indices_re[0])  # N*(N+1)/2
    n_im = len(indices_im[0])  # N*(N-1)/2

    def make_T(tis):
        """

        Args:
            tis:

        Returns:

        """
        t = np.zeros((N, N), dtype=complex)
        t[indices_re] = tis[:n_re]
        t[indices_im] += 1j * tis[n_re:]
        return t

    def unmake_T(m):
        """不完全转置, 获取转置参数

        Args:
            m: 待操作的矩阵

        Returns:
            矩阵下半区的实部加上偏移一位后的下半区虚部
        """
        return np.hstack((m[indices_re].real, m[indices_im].imag))

    def make_rho(ts):
        """计算密度矩阵

        Args:
            ts: 待计算的矩阵

        Returns:
            密度矩阵
        """
        t = make_T(ts)
        # 计算Hermite半正定阵
        tt = np.dot(t, t.conj().T)
        return tt / np.trace(tt)

    # make an initial guess using linear tomography
    if rho0 is None:
        u = init_qst(u_array)
        inverse = np.linalg.inv(F)
        pis_guess = np.array([np.dot(inverse, p) for p in measure_data])
        rho0, *_ = qst(pis_guess, u)
    # convert the initial guess into t vector
    # to do this we use a cholesky decomposition, which
    # only works if the matrix is positive and hermitian.
    # so, we diagonalize and fix up the eigenvalues before
    # attempting the cholesky decomp.
    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量
    d = d.real
    d = d * (d > 0) + 0.01
    d_fix = d / sum(d)
    rho0 = _dot3(v, np.diag(d_fix), v.conj().T)
    # 对矩阵进行cholesky分解, Cholesky分解是把一个对称正定的矩阵表示成
    # 一个下三角矩阵L和其转置的乘积的分解。它要求矩阵的所有特征值必须大于零
    t0 = np.linalg.cholesky(rho0)
    tis_guess = unmake_T(t0)

    # precompute conjugate transposes of matrices
    # 预先计算矩阵的共轭转置
    u_uds = [(U, U.conj().T) for U in u_array]

    def log(x):
        """Safe version of log that returns -Inf when x < 0, rather than NaN.

        This is good for our purposes since negative probabilities are
        infinitely unlikely.
        """
        return np.log(x.real * (x.real > 0))

    array = np.array

    dot = np.dot
    diag = np.diag

    def unlikelihood(tis):
        """negative of likelihood function

        Args:
            tis: cholesky分解后的值

        Returns:
            最大似然估计
        """
        rho = make_rho(tis)
        p_xis = array([dot(F, diag(_dot3(U, rho, Ud))) for U, Ud in u_uds])
        terms = measure_data * log(p_xis) + (1 - measure_data) * log(1 - p_xis)
        return -sum(terms.flat)

    # minimize
    tis_value = optimize.fmin(unlikelihood, tis_guess)

    # tis_value = optimize.fmin_bfgs (unlikelihood, tis_guess)
    return make_rho(tis_value)


def init_qpt(matrices):
    """Initialize quantum process tomography for an operator basis.

    Args:
        matrices: a list of matrices giving the basis in which to compute
            the chi matrix for process tomography.  These matrices
            should form a 'complete' set to allow the full chi matrix
            to be represented, though this is not enforced.

    Returns:
        Returns a transformation matrix that should be passed to qpt along
        with input and output density matrices to perform the process tomography.
    """
    matrices = np.asarray(matrices, dtype=complex)

    d_out, d_in = matrices[0].shape
    chi_size = d_out * d_in

    # we have to be a bit careful here, because things blow up
    # exponentially with the number of qubits.  The first method
    # uses direct indexing to generate the entire transform matrix
    # in one shot.  This is elegant and much faster than for-loop
    # iteration, but uses more memory and so only works for
    # smaller qubit numbers.
    if chi_size <= 16:
        # one or two qubits
        def transform(alpha, beta):
            """todo"""
            L, J = divmod(alpha, chi_size)
            M, N = divmod(beta, chi_size)
            i, j = divmod(J, d_out)
            k, l = divmod(L, d_in)
            return matrices[M, i, k] * matrices[N, j, l].conj()

        T = np.fromfunction(transform, (chi_size**2, chi_size**2), dtype=int)
    else:
        # three or more qubits
        T = np.zeros((chi_size**2, chi_size**2), dtype=complex)
        for _alpha in range(chi_size**2):
            for _beta in range(chi_size**2):
                L, J = divmod(_alpha, chi_size)
                M, N = divmod(_beta, chi_size)
                i, j = divmod(J, d_out)
                k, l = divmod(L, d_in)
                T[_alpha, _beta] = matrices[M, i, k] * matrices[N, j, l].conj()
    return T


def qpt(rhos, e_rhos, T, return_all=False):
    """Calculate the chi matrix of a quantum process.

    Args:
        rhos: array of input density matrices
        e_rhos: array of output density matrices
        T: transformation matrix from init_qpt for the desired operator
            basis, or key passed to init_qpt under which this basis was saved
    """
    chi_pointer = _qpt_pointer(rhos, e_rhos)
    return _transform_chi_pointer(chi_pointer, T, return_all)


def tensor(matrices):
    """Compute the tensor product of a list (or array) of matrices

    Args:
        matrices: matrices squence

    Returns:
        the result of `np.korn`
    """
    return reduce(np.kron, matrices)


def tensor_combinations(matrices, repeat):
    """Matrix tensor combinations

    Args:
        matrices: List of matrices to be merged
        repeat: Number of mergers

    .. code::

        Input:
            matrices = [A1, A2, A3, A4], repeat = 2
        Output:
            matrices = [
                A1*A1, A1*A2, A1*A3, A1*A4,
                A2*A1, A2*A2, A2*A3, A2*A4,
                A3*A1, A3*A2, A3*A3, A3*A4,
                A4*A1, A4*A2, A4*A3, A4*A4
            ]
    """
    return [tensor(ms) for ms in itertools.product(matrices, repeat=repeat)]


def gen_ideal_chi_matrix(unitary, qpt_ops, tomo_ops):
    """Generate Chi matrix for standard gate operations

    Args:
        unitary: Pauli matrix of the gate to be characterized.
        qpt_ops: Quantum process tomography for
            an operator basis `[I, X, Y, Z]`.
        tomo_ops: Base gate matrix list `["I", "Y", "Y/2", "-X/2"]`.

    Returns:
        Ideal chi matrix.
    """
    num_qubits = int(np.log2(len(unitary)))

    main_diag = [1] + (2 ** num_qubits - 1) * [0]
    rho0 = np.diag(main_diag)

    in_rhos = []
    out_rhos = []

    if len(tomo_ops) < 16:
        operations = tensor_combinations(tomo_ops, repeat=num_qubits)
    else:
        operations = tomo_ops

    for op in operations:
        in_rho = _dot3(op, rho0, op.conjugate().transpose())
        out_rho = _dot3(unitary, in_rho, unitary.conjugate().transpose())

        in_rhos.append(in_rho)
        out_rhos.append(out_rho)

    chi = qpt(in_rhos, out_rhos, qpt_ops)

    return chi, in_rhos, out_rhos


def qpt_mle(diags_out, rhos_in, Us, As, T, F=None, chi0=None):
    """State tomography with maximum-likelihood estimation.

    Args:
        diags_out: a list of 2D array of measured probabilities.  the list index
            indicates the # rho prepared and processed by Chi for the 2D
            array, (see qst_mle) The first index indicates which
            operation from Us was applied, while the second index tells
            which measurement result this was (e.g. 000, 001, etc.).
        Us: the unitary operations that were applied to the system before
            measuring.
        F: a 'fidelity' matrix, relating the actual or 'intrinsic' probabilities
            to the measured probabilities, via pms = dot(F, pis).  If no fidelity
            matrix is given, the identity will be used.
        chi0: an initial guess for the chi matrix, e.g. from linear qpt.
    """
    dim = len(Us[0])  # size of density matrix
    N = dim**2  # size of chi matrix
    if F is None:
        F = np.eye(dim)

    try:
        indices_re = np.tril_indices(N)
        indices_im = np.tril_indices(N, -1)
    except AttributeError:
        # tril_indices is new in numpy 1.4.0
        indices_re = (
            np.hstack([[k] * (k + 1) for k in range(N)]),
            np.hstack([list(range(k + 1)) for k in range(N)]),
        )
        indices_im = (
            np.hstack([[k + 1] * (k + 1) for k in range(N - 1)]),
            np.hstack([list(range(k + 1)) for k in range(N - 1)]),
        )
    n_re = len(indices_re[0])  # N*(N+1)/2
    n_im = len(indices_im[0])  # N*(N-1)/2

    def make_T(tis):
        T = np.zeros((N, N), dtype=complex)
        T[indices_re] = tis.ravel()[:n_re]
        T[indices_im] += 1j * tis.ravel()[n_re:]
        return T

    def unmake_T(T):
        return np.hstack((T[indices_re].real, T[indices_im].imag))

    def make_chi(ts):
        T = make_T(ts)
        chi = np.dot(T, T.conj().T)
        return chi

    # make an initial guess using linear qpt
    if chi0 is None:
        qst_T = init_qst(Us)
        Finv = np.linalg.inv(F)
        rhos_out = []
        for pxms in diags_out:
            pis_guess = np.array([np.dot(Finv, p) for p in pxms])
            rho_out, *_ = qst(pis_guess, qst_T)
            rhos_out.append(rho_out)
        chi0 = qpt(rhos_in, rhos_out, T)
    # lbls_list = [['I', 'X', 'Y', 'Z']] * 2
    # fig, _ = qp.qpt_plot_combined(chi0, lbls_list)
    # fig.show()
    # fig.canvas.draw()
    # print('fidelity:', qp.process_fidelity(qp.Qobj(chi0), qp.Qobj(chi)))
    # convert the initial guess into t vector
    # to do this we use a cholesky decomposition, which
    # only works if the matrix is positive and hermitian.
    # so, we diagonalize and fix up the eigenvalues before
    # attempting the cholesky decomp.
    d, V = np.linalg.eig(chi0)
    d = d.real
    d = d * (d > 0) + 1e-5
    # dfix = d / sum(d)
    dfix = d
    chi00 = _dot3(V, np.diag(dfix), V.conj().T)
    T0 = np.linalg.cholesky(chi00)
    tis_guess = unmake_T(T0)
    UUds = [(U, U.conj().T) for U in Us]

    # ABds = [(A, B.conj().T) for A in As for B in As]
    def log(x):
        """Safe version of log that returns -Inf when x < 0, rather than NaN.

        This is good for our purposes since negative probabilities are
        infinitely unlikely.
        """
        return np.log(x.real * (x.real > 0))

    array = np.array
    dot = np.dot
    diag = np.diag
    # sum = np.sum
    # for small qubits number
    Trkmn = [
        array(
            [
                [(_dot3(As[m], As[k], As[n].conj().T)).trace() for n in range(N)]
                for m in range(N)
            ]
        )
        for k in range(N)
    ]
    Trk = [As[k].trace() for k in range(N)]
    Ads = [A.conj().T for A in As]

    def ftarget(tis):
        lamd = 0.1  # Lagrange multiplier
        chi = make_chi(tis)
        # chi_vector = chi.flatten()
        terms = 0  # np.zeros_like(diags_out[0])
        for i in range(len(rhos_in)):
            rho_in = rhos_in[i]
            pxms = diags_out[i]
            rho_out = sum(
                chi[m, n] * _dot3(As[m], rho_in, Ads[n])
                for m in range(len(As))
                for n in range(len(As))
            )
            # rho_out = np.sum([chi_vector[ind]*dot3(A, rho_in, Bd)
            #                   for ind, (A, Bd) in enumerate(ABds)])
            pxis = array(
                [np.real(dot(F, diag(_dot3(U, rho_out, Ud)))) for U, Ud in UUds]
            )
            # pxis = pxis.astype(np.float)
            # terms += pxms * log(pxis) + (1 - pxms) * log(1 - pxis)
            dif = (pxms - pxis) ** 2
            terms += sum(dif.flat)
        unlikelihood = terms  # negative of likelihood function
        return unlikelihood
        # # completeness
        # distance = 0
        # # for k in range(N):
        # #     distance += (np.sum((chi*Trkmn[k]).flat)-Trk[k])**2
        # f = unlikelihood + lamd*distance
        # # print(f)
        # return f

    def con(tis):
        """todo"""
        chi = make_chi(tis)
        distance = 0
        for k in range(N):
            distance += (np.sum((chi * Trkmn[k]).flat) - Trk[k]) ** 2
        return distance

    # # minimize
    cons = {"type": "eq", "fun": lambda x: con(x)}
    res = optimize.minimize(ftarget, tis_guess, method="SLSQP", constraints=cons)
    tis = res.x
    # tis = optimize.fmin(ftarget, tis_guess)
    # tis = optimize.fmin_bfgs(ftarget, tis_guess)
    return make_chi(tis)


def phase_tomograph(p0_x, p1_x, p0_y, p1_y, hase_fix: bool = False, fidelity=None):
    if not hase_fix and fidelity is not None:
        px = np.c_[p0_x, p1_x]
        py = np.c_[p0_y, p1_y]
        f_inv = np.linalg.inv(fidelity)
        px = np.array([np.dot(f_inv, p) for p in px])
        py = np.array([np.dot(f_inv, p) for p in py])
        p1_x = px[:, 1][0]
        p1_y = py[:, 1][0]

    x = 1 - 2 * p1_x
    y = 1 - 2 * p1_y
    phase = np.angle(x + 1j * y)

    return x, y, phase


def change_phase(phase, level=np.pi):
    def _trans(p):
        while p > level + np.pi:
            p -= 2 * np.pi

        while p < level - np.pi:
            p += 2 * np.pi

        return p

    if isinstance(phase, float):
        return _trans(phase)

    new_phase = []
    for _p in phase:
        new_phase.append(_trans(_p))

    return np.array(new_phase)


# ########### for TomographyCalculator #############
def super_operator(bm: np.ndarray, bn: np.ndarray) -> np.ndarray:
    return np.kron(bn.T, bm)


def vec2mat(vec: np.ndarray) -> np.ndarray:
    ln = len(vec)
    dim = int(np.sqrt(ln))
    return vec.reshape(dim, dim).T


def rhovec2probs(rhovec: np.ndarray) -> np.ndarray:
    mat = vec2mat(rhovec)
    return mat.diagonal()


def dots(matrices):
    """Compute the dot product of a list (or array) of matrices"""
    return reduce(np.dot, matrices)


def mat2vec(mat: np.ndarray) -> np.ndarray:
    return np.ravel(mat, "F")


def get_completeness_sop(basis_list: List[np.ndarray]) -> np.ndarray:
    n_basis = len(basis_list)
    dim = basis_list[0].shape[0]  # N_basis == dim**2
    completeness_sop = np.zeros((dim**2, n_basis**2), dtype=complex)
    for n in range(n_basis):
        for m in range(n_basis):
            bnm_vec = mat2vec(np.dot(basis_list[n].conj().T, basis_list[m]))
            column_index = m * n_basis + n
            completeness_sop[:, column_index] = bnm_vec
    return completeness_sop


def get_basis_matrix(
    meas_op_list: List[np.ndarray],
    prep_op_list: List[np.ndarray] = None,
    basis_list: List[np.ndarray] = None,
) -> np.ndarray:
    n_meas = len(meas_op_list)  # number of measurement basis transformation
    dim = meas_op_list[0].shape[0]  # dimension of Hilbert space
    sop_meas_list = [super_operator(A, A.conj().T) for A in meas_op_list]
    if prep_op_list:  # QPT
        n_prep = len(prep_op_list)  # number of preparation operations
        rhovec0 = np.zeros(dim**2)
        rhovec0[0] = 1  # |0><0|
        sop_prep_list = [super_operator(A, A.conj().T) for A in prep_op_list]
        sop_chi_basis = [
            [super_operator(Bm, Bn.conj().T) for Bn in basis_list] for Bm in basis_list
        ]
        basis_matrix = np.zeros((dim * n_prep * n_meas, dim**4), dtype=complex)
        for n in range(dim**2):
            for m in range(dim**2):
                epsilon_mn = sop_chi_basis[m][n]
                column_index = dim**2 * n + m
                basis_matrix[:, column_index] = np.array(
                    [
                        [
                            rhovec2probs(
                                dots([epsilon_mea, epsilon_mn, epsilon_pre, rhovec0])
                            )
                            for epsilon_mea in sop_meas_list
                        ]
                        for epsilon_pre in sop_prep_list
                    ]
                ).reshape(-1)
                # fastest:dim, secondly faster: mea_list, slowest: prep_list
    else:  # QST
        row_index_slice = slice(
            0, dim**2, dim + 1
        )  # [k*(dim+1) for k in range(dim)]  # index_slice of diagonal elements of rhovec
        basis_matrix = np.zeros((n_meas * dim, dim**2), dtype=complex)
        for m in range(n_meas):
            row_index = slice(m * dim, (m + 1) * dim, 1)
            basis_matrix[row_index, :] = sop_meas_list[m][row_index_slice, :]
    return basis_matrix


def cvxpy_linear_lstsq(
    probability_data: np.ndarray,
    meas_op_list: List[np.ndarray],
    prep_op_list: List[np.ndarray] = None,
    basis_list: List[np.ndarray] = None,
    psd: bool = True,
    trace_preserving: bool = False,
    trace: Optional[float] = None,
    weights: Optional[np.ndarray] = None,
    **kwargs,
) -> Tuple[np.ndarray, Dict]:
    r"""Constrained weighted linear least-squares tomography fitter.

    Overview
        This fitter reconstructs the maximum-likelihood estimate by using
        ``cvxpy`` to minimize the constrained least-squares negative log
        likelihood function

        .. math::
            \hat{\rho}
                &= -\mbox{argmin }\log\mathcal{L}{\rho} \\
                &= \mbox{argmin }\sum_i w_i^2(\mbox{Tr}[E_j\rho] - \hat{p}_i)^2 \\
                &= \mbox{argmin }\|W(Ax - y) \|_2^2

        subject to

        - *Positive-semidefinite* (``psd=True``): :math:`\rho \gg 0` is constrained
          to be a postive-semidefinite matrix.
        - *Trace* (``trace=t``): :math:`\mbox{Tr}(\rho) = t` is constained to have
          the specified trace.
        - *Trace preserving* (``trace_preserving=True``): When performing process
          tomography the Choi-state :math:`\rho` represents is constained to be
          trace preserving.

        where

        - :math:`A` is the matrix of measurement operators
          :math:`A = \sum_i |i\rangle\!\langle\!\langle M_i|`
        - :math:`y` is the vector of expectation value data for each projector
          corresponding to estimates of :math:`b_i = Tr[M_i \cdot x]`.
        - :math:`x` is the vectorized density matrix (or Choi-matrix) to be fitted
          :math:`x = |\rho\rangle\\!\rangle`.

    .. note:

        Various solvers can be called in CVXPY using the `solver` keyword
        argument. When ``psd=True`` the optimization problem is a case of a
        *semidefinite program* (SDP) and requires SDP compatible solver
        for CVXPY. CVXPY includes an SDP compatible solver `SCS`` but it
        is recommended to install the open-source ``CVXOPT`` solver
        or one of the supported commercial solvers. See the `CVXPY
        documentation
        <https://www.cvxpy.org/tutorial/advanced/index.html#solve-method-options>`_
        for more information on solvers.

    .. note::

        Linear least-squares constructs the full basis matrix :math:`A` as a dense
        numpy array so should not be used for than 5 or 6 qubits. For larger number
        of qubits try the
        :func:`~qiskit_experiments.library.tomography.fitters.linear_inversion`
        fitter function.

    Args:
        probability_data: list of outcome frequency data.
        meas_op_list: basis measurement total shot data.
        prep_op_list: measurement basis indice data.
        basis_list: preparation basis indice data.
        psd: If True rescale the eigenvalues of fitted matrix to be positive
             semidefinite (default: True)
        trace_preserving: Enforce the fitted matrix to be
            trace preserving when fitting a Choi-matrix in quantum process
            tomography (default: False).
        trace: trace constraint for the fitted matrix (default: None).
        weights: Optional array of weights for least squares objective.
        kwargs: kwargs for cvxpy solver.

    Raises:
        QiskitError: If CVXPy is not installed on the current system.
        AnalysisError: If analysis fails.

    Returns:
        The fitted matrix rho that maximizes the least-squares likelihood function.
    """
    basis_matrix = get_basis_matrix(meas_op_list, prep_op_list, basis_list)
    if weights is not None:
        basis_matrix = weights[:, None] * basis_matrix
        probability_data = weights * probability_data

    # SDP VARIABLES

    # Since CVXPY only works with real variables we must specify the real
    # and imaginary parts of rho seperately: rho = rho_r + 1j * rho_i

    dim = int(np.sqrt(basis_matrix.shape[1]))
    rho_r = cvxpy.Variable((dim, dim), symmetric=True)
    rho_i = cvxpy.Variable((dim, dim))

    # CONSTRAINTS

    cons = [rho_i == -rho_i.T]

    # Trace constraint: note this should not be used at the same
    # time as the trace preserving constraint.
    if trace is not None:
        cons.append(cvxpy.trace(rho_r) == trace)

    # Since we can only work with real matrices in CVXPY we can specify
    # a complex PSD constraint as
    #   rho >> 0 iff [[rho_r, -rho_i], [rho_i, rho_r]] >> 0
    if psd is True:
        rho = cvxpy.bmat([[rho_r, -rho_i], [rho_i, rho_r]])
        cons.append(rho >> 0)

    # Trace preserving constraint when fitting Choi-matrices for
    # quantum process tomography. Note that this adds an implicity
    # trace constraint of trace(rho) = sqrt(len(rho)) = dim
    # if a different trace constraint is specified above this will
    # cause the fitter to fail.

    if trace_preserving is True:
        sdim = int(np.sqrt(dim))
        # ptr = partial_trace_super(sdim, sdim)
        ptr = get_completeness_sop(basis_list)
        ptr_r = np.real(ptr)
        ptr_i = np.imag(ptr)
        # cons.append(ptr @ cvxpy.vec(rho_r) == np.identity(sdim).ravel())
        # cons.append(ptr @ cvxpy.vec(rho_i) == np.zeros(sdim * sdim))
        cons.append(
            (ptr_r @ cvxpy.vec(rho_r) - ptr_i @ cvxpy.vec(rho_i))
            == np.identity(sdim).ravel()
        )
        cons.append(
            (ptr_r @ cvxpy.vec(rho_i) + ptr_i @ cvxpy.vec(rho_r)) == np.zeros(dim)
        )

    # OBJECTIVE FUNCTION
    # where we drop the imaginary part since the expectation value is real

    bm_r = np.real(basis_matrix)
    bm_i = np.imag(basis_matrix)

    # SDP objective function
    arg = bm_r @ cvxpy.vec(rho_r) - bm_i @ cvxpy.vec(rho_i) - probability_data
    obj = cvxpy.Minimize(cvxpy.norm(arg, p=2))

    # Solve SDP
    prob = cvxpy.Problem(obj, cons)
    iters = 5000
    max_iters = kwargs.get("max_iters", 20000)
    # Set default solver if none is specified
    if "solver" not in kwargs:
        if "CVXOPT" in cvxpy.installed_solvers():
            kwargs["solver"] = "CVXOPT"
        elif "MOSEK" in cvxpy.installed_solvers():
            kwargs["solver"] = "MOSEK"

    problem_solved = False
    while not problem_solved:
        kwargs["max_iters"] = iters
        prob.solve(**kwargs)
        if prob.status in ["optimal_inaccurate", "optimal"]:
            problem_solved = True
        elif prob.status == "unbounded_inaccurate":
            if iters < max_iters:
                iters *= 2
            else:
                raise Exception(
                    "CVXPY fit failed, probably not enough iterations for the " "solver"
                )
        elif prob.status in ["infeasible", "unbounded"]:
            raise Exception(
                "CVXPY fit failed, problem status {} which should not "
                "happen".format(prob.status)
            )
        else:
            raise Exception("CVXPY fit failed, reason unknown")

    rho_fit = rho_r.value + 1j * rho_i.value
    metadata = {
        "cvxpy_solver": prob.solver_stats.solver_name,
        "cvxpy_status": prob.status,
    }
    return rho_fit, metadata


def cvxpy_linear_lstsq_rho_in_out(
    in_probability_data: np.ndarray,  # for QPT,its length is N_prep, the shape of each element is (dim*N_meas,1)
    out_probability_data: np.ndarray,  # for QPT,its shape is (dim*N_meas*N_prep,1)
    meas_op_list: List[np.ndarray],
    basis_list: List[np.ndarray] = None,
    psd: bool = True,
    trace_preserving: bool = False,
    trace: Optional[float] = None,
    **kwargs,
) -> Tuple[np.ndarray, Dict]:
    rhos_in = []
    for in_probability in in_probability_data:
        rho_in, _ = cvxpy_linear_lstsq(in_probability, meas_op_list, psd=True, trace=1)
        # rho_in,_ = cvxpy_linear_lstsq(in_probability,meas_op_list,psd=False)
        # rho_in,_ = scipy_linear_lstsq(in_probability,meas_op_list)
        rhos_in.append(rho_in)

    dim = meas_op_list[0].shape[0]
    n_meas = len(meas_op_list)
    n_prep = len(in_probability_data)
    sop_meas_list = [super_operator(A, A.conj().T) for A in meas_op_list]
    basis_matrix = np.zeros((dim * n_prep * n_meas, dim**4), dtype=complex)
    sop_chi_basis = [
        [super_operator(Bm, Bn.conj().T) for Bn in basis_list] for Bm in basis_list
    ]
    for n in range(dim**2):
        for m in range(dim**2):
            epsilon_mn = sop_chi_basis[m][n]
            column_index = dim**2 * n + m
            basis_matrix[:, column_index] = np.array(
                [
                    [
                        rhovec2probs(
                            dots([sop_meas_list[m1], epsilon_mn, mat2vec(rhos_in[m2])])
                        )
                        for m1 in range(n_meas)
                    ]
                    for m2 in range(n_prep)
                ]
            ).reshape(-1)

    # SDP VARIABLES

    # Since CVXPY only works with real variables we must specify the real
    # and imaginary parts of rho seperately: rho = rho_r + 1j * rho_i

    dim = int(np.sqrt(basis_matrix.shape[1]))
    rho_r = cvxpy.Variable((dim, dim), symmetric=True)
    rho_i = cvxpy.Variable((dim, dim))

    # CONSTRAINTS
    cons = [rho_i == -rho_i.T]

    # Trace constraint: note this should not be used at the same
    # time as the trace preserving constraint.
    if trace is not None:
        cons.append(cvxpy.trace(rho_r) == trace)
    # Since we can only work with real matrices in CVXPY we can specify
    # a complex PSD constraint as
    #   rho >> 0 iff [[rho_r, -rho_i], [rho_i, rho_r]] >> 0

    if psd is True:
        rho = cvxpy.bmat([[rho_r, -rho_i], [rho_i, rho_r]])
        cons.append(rho >> 0)

    # Trace preserving constraint when fitting Choi-matrices for
    # quantum process tomography. Note that this adds an implicity
    # trace constraint of trace(rho) = sqrt(len(rho)) = dim
    # if a different trace constraint is specified above this will
    # cause the fitter to fail.

    if trace_preserving is True:
        sdim = int(np.sqrt(dim))
        # ptr = partial_trace_super(sdim, sdim)
        ptr = get_completeness_sop(basis_list)
        ptr_r = np.real(ptr)
        ptr_i = np.imag(ptr)
        # cons.append(ptr @ cvxpy.vec(rho_r) == np.identity(sdim).ravel())
        # cons.append(ptr @ cvxpy.vec(rho_i) == np.zeros(sdim * sdim))
        cons.append(
            (ptr_r @ cvxpy.vec(rho_r) - ptr_i @ cvxpy.vec(rho_i))
            == np.identity(sdim).ravel()
        )
        cons.append(
            (ptr_r @ cvxpy.vec(rho_i) + ptr_i @ cvxpy.vec(rho_r)) == np.zeros(dim)
        )

    # OBJECTIVE FUNCTION

    # The function we wish to minimize is || arg ||_2 where
    #   arg =  bm * vec(rho) - data
    # Since we are working with real matrices in CVXPY we expand this as
    #   bm * vec(rho) = (bm_r + 1j * bm_i) * vec(rho_r + 1j * rho_i)
    #                 = bm_r * vec(rho_r) - bm_i * vec(rho_i)
    #                   + 1j * (bm_r * vec(rho_i) + bm_i * vec(rho_r))
    #                 = bm_r * vec(rho_r) - bm_i * vec(rho_i)
    # where we drop the imaginary part since the expectation value is real

    bm_r = np.real(basis_matrix)
    bm_i = np.imag(basis_matrix)

    # SDP objective function
    arg = bm_r @ cvxpy.vec(rho_r) - bm_i @ cvxpy.vec(rho_i) - out_probability_data
    obj = cvxpy.Minimize(cvxpy.norm(arg, p=2))

    # Solve SDP
    prob = cvxpy.Problem(obj, cons)
    iters = 5000
    max_iters = kwargs.get("max_iters", 20000)
    # Set default solver if none is specified
    if "solver" not in kwargs:
        if "CVXOPT" in cvxpy.installed_solvers():
            kwargs["solver"] = "CVXOPT"
        elif "MOSEK" in cvxpy.installed_solvers():
            kwargs["solver"] = "MOSEK"

    problem_solved = False
    while not problem_solved:
        kwargs["max_iters"] = iters
        prob.solve(**kwargs)
        if prob.status in ["optimal_inaccurate", "optimal"]:
            problem_solved = True
        elif prob.status == "unbounded_inaccurate":
            if iters < max_iters:
                iters *= 2
            else:
                raise Exception(
                    "CVXPY fit failed, probably not enough iterations for the " "solver"
                )
        elif prob.status in ["infeasible", "unbounded"]:
            raise Exception(
                "CVXPY fit failed, problem status {} which should not "
                "happen".format(prob.status)
            )
        else:
            raise Exception("CVXPY fit failed, reason unknown")

    rho_fit = rho_r.value + 1j * rho_i.value
    metadata = {
        "cvxpy_solver": prob.solver_stats.solver_name,
        "cvxpy_status": prob.status,
    }
    return rho_fit, metadata


class TomographyCalculator:
    def __init__(
        self,
        mea_base_gates: List[str] = None,
        prep_base_gates: List[str] = None,
    ):
        self.gate_bucket = GateBucket()
        self.mea_base_gates = mea_base_gates or ["I", "X/2", "Y/2"]
        self.prep_base_gates = prep_base_gates or ["I", "Y", "Y/2", "-X/2"]
        self.analysis_options = QDict(
            mode="qpt",
            cal_diag_in=True,
            neglect_mode="neglect_chi_prep",
            goal_gate="I",
            phase_opt=True,
        )
        self.run_options = QDict()
        self.result = QDict(
            process_fidelity=None,
            chi_fidelity=None,
            chi_err_rate=None,
            chi_exp=None,
            chi_exp_err=None,
            chi_err=None,
            chi_err_diag=None,
            phase=None,
        )
        self.qst_result = QDict(rho_exp=None, rho_ideal=None, fidelity=None)

    def calculate(self, probability: Union[List, np.ndarray]):
        """

        Args:
            probability: Acquisition results obtained after executing quantum circuits.
                Consider diag_in:
                    1q = [
                        [a1, a2, ..., a24],  # p0
                        [b1, b2, ..., b24]   # p1
                    ]
                    2q = [
                        [a1, a2, ..., a288],  # p00
                        [b1, b2, ..., b288],  # p01
                        [b1, b2, ..., b288],  # p10
                        [b1, b2, ..., b288]   # p11
                    ]
                No consider diag_in:
                    1q = [
                        [a1, a2, ..., a12],  # p0
                        [b1, b2, ..., b12]   # p1
                    ]
                    2q = [
                        [a1, a2, ..., a144],  # p00
                        [b1, b2, ..., b144],  # p01
                        [b1, b2, ..., b144],  # p10
                        [b1, b2, ..., b144]   # p11
                    ]

        Returns:

        """
        self._cal_common_var(np.array(probability))
        if self.analysis_options.mode == "qpt":
            self._cal_qpt_fidelity()
        else:
            self._cal_qst_fidelity(np.array(probability))

    def _cal_common_var(self, probability: np.ndarray) -> None:
        qubit_num = int(np.log2(probability.shape[0]))

        if qubit_num not in [1, 2]:
            raise ValueError(f"Currently only 1q and 2q!")

        self.run_options.qubit_num = qubit_num

        # calculator mea_ops/prep_ops/sigma_ops
        sigma_basis = tensor_combinations(
            list(self.gate_bucket.pauli_matrix.values()), qubit_num
        )
        self.run_options.mea_ops = tensor_combinations(
            [
                np.asarray(self.gate_bucket.get_matrix(gate))
                for gate in self.mea_base_gates
            ],
            qubit_num,
        )
        self.run_options.prep_ops = tensor_combinations(
            [
                np.asarray(self.gate_bucket.get_matrix(gate))
                for gate in self.prep_base_gates
            ],
            qubit_num,
        )
        self.run_options.basis_ops = sigma_basis
        self.run_options.sigma_ops = init_qpt(sigma_basis)

        # check diag out/in
        if qubit_num == 1:
            if probability.shape[1] == 24:
                diag = probability.T.reshape(8, 6)
                self.run_options.diag_in = diag[:4, :]
                self.run_options.diag_out = diag[4:, :]
            elif probability.shape[1] == 12:
                self.run_options.diag_in = None
                self.run_options.diag_out = probability.T.reshape(4, 6)
            else:
                raise ValueError(f"1q shape1 only in [12, 24")
        else:
            if probability.shape[1] == 288:
                diag = probability.T.reshape(32, 36)
                self.run_options.diag_in = diag[:16, :]
                self.run_options.diag_out = diag[16:, :]
            elif probability.shape[1] == 144:
                self.run_options.diag_in = None
                self.run_options.diag_out = probability.T.reshape(32, 36)
            # else:
                # raise ValueError(f"1q shape1 only in [12, 24")

        # init e set
        if self.analysis_options.mode == 'qpt':
            uui = qp.sprepost(qp.qeye(2 ** qubit_num), qp.qeye(2 ** qubit_num).dag())
            self.run_options.e_set = [
                qp.Qobj(v)
                for v in tensor_combinations(
                    [qp.qeye(2), qp.sigmax(), qp.sigmay(), qp.sigmaz()], qubit_num
                )
            ]
            self.run_options.chi_i = qp.qpt(
                uui, [[qp.qeye(2), qp.sigmax(), qp.sigmay(), qp.sigmaz()]] * qubit_num
            )

    def _cal_qpt_fidelity(self):
        if self.analysis_options.cal_diag_in is True:
            # chi_exp calculated from experiment data
            chi_exp = self.exp_chi_mle(
                self.run_options.diag_out.flatten(),
                diag_in=self.run_options.diag_in,
            )

            # chi_exp_I calculated from experiment data
            chi_exp_i = self.exp_chi_mle(
                diag_out=self.run_options.diag_in.flatten(), diag_in=None
            )

            # get idle u matrix
            s_phase, phase = self._get_idle_u(chi_exp)

            if self.analysis_options.neglect_mode == "neglect_chi_prep":
                chi_exp_err = self.calc_chi_err(s_phase, chi_exp)
                chi_exp_i_err = self.calc_chi_err(qp.qeye(2 ** self.run_options.qubit_num), chi_exp_i)
            else:
                chi_exp_err = self.calc_chi_err_td(s_phase, chi_exp)
                chi_exp_i_err = self.calc_chi_err_td(qp.qeye(2 ** self.run_options.qubit_num), chi_exp_i)

            process_fidelity = np.abs(
                np.trace(
                    chi_exp_err
                    @ gen_ideal_chi_matrix(
                        np.eye(2 ** self.run_options.qubit_num),
                        self.run_options.sigma_ops,
                        self.run_options.prep_ops,
                    )[0]
                )
            )

            chi_err = chi_exp_err - (chi_exp_i_err - self.run_options.chi_i)
            chi_fidelity = abs(chi_err[0, 0])

            chi_err_diag, _ = np.linalg.eig(chi_err)
            chi_err_diag = np.diag(chi_err_diag)
            chi_err_rate = 1 - abs(chi_err_diag[0, 0])

        else:
            chi_exp = self.exp_chi_mle(self.run_options.diag_out.flatten())
            s_phase, phase = self._get_idle_u(chi_exp)
            chi_exp_err = self.calc_chi_err(s_phase, chi_exp)
            process_fidelity = np.abs(
                np.trace(
                    chi_exp_err
                    @ gen_ideal_chi_matrix(
                        np.eye(2 ** self.run_options.qubit_num),
                        self.run_options.sigma_ops,
                        self.run_options.prep_ops,
                    )[0]
                )
            )

            chi_err = chi_exp_err
            chi_fidelity = abs(chi_err[0, 0])

            chi_err_diag, _ = np.linalg.eig(chi_err)
            chi_err_diag = np.diag(chi_err_diag)
            chi_err_rate = 1 - abs(chi_err_diag[0, 0])

        # collect result
        self.result.chi_exp = chi_exp
        self.result.chi_exp_err = chi_exp_err
        self.result.chi_err = chi_err
        self.result.chi_err_diag = chi_err_diag
        self.result.process_fidelity = round(process_fidelity, 5)
        self.result.chi_fidelity = round(chi_fidelity, 5)
        self.result.chi_err_rate = round(chi_err_rate, 5)
        self.result.phase = phase

    def calc_chi_err(self, u, chi):
        v = np.zeros(
            [4**self.run_options.qubit_num, 4**self.run_options.qubit_num],
            dtype=complex,
        )
        for m, Em in enumerate(self.run_options.e_set):
            for n, En in enumerate(self.run_options.e_set):
                v[m, n] = (Em.dag() * En * u.dag()).tr() / 2 ** self.run_options.qubit_num
        v = qp.Qobj(v)

        return v * chi * v.dag()

    def calc_chi_err_td(self, u, chi):
        vtd = np.zeros(
            [4**self.run_options.qubit_num, 4**self.run_options.qubit_num],
            dtype=complex,
        )
        for m, Em in enumerate(self.run_options.e_set):
            for n, En in enumerate(self.run_options.e_set):
                vtd[m, n] = (Em.dag() * u.dag() * En).tr() / 2 ** self.run_options.qubit_num
        vtd = qp.Qobj(vtd)

        return vtd * chi * vtd.dag()

    def exp_chi_mle(self, diag_out, diag_in: np.ndarray = None):
        if diag_in is not None:
            chi_exp, _status = cvxpy_linear_lstsq_rho_in_out(
                diag_in, diag_out, self.run_options.mea_ops, self.run_options.basis_ops
            )
        else:
            chi_exp, _status = cvxpy_linear_lstsq(
                diag_out,
                self.run_options.mea_ops,
                self.run_options.prep_ops,
                self.run_options.basis_ops,
            )
        return chi_exp

    def _get_idle_u(self, chi_exp):
        if self.analysis_options.phase_opt is True:
            res = minimize(
                self.error_chi,
                x0=np.array([0]),
                args=(chi_exp,),
                method="SLSQP",
                bounds=((-np.pi, np.pi),),
                options={"disp": True},
            )
            phase = res.x[0]
            s_phase = phasegate(phase)
        else:
            s_phase = self.gate_bucket.get_matrix(self.analysis_options.goal_gate)
            s_phase = Qobj(s_phase)
            phase = 0
        return s_phase, phase

    def error_chi(self, paras, chi_exp):
        matrix_guess = self.gate_bucket.get_matrix(
            self.analysis_options.goal_gate, *paras
        )
        chi_guess, *_ = gen_ideal_chi_matrix(
            matrix_guess, self.run_options.sigma_ops, self.run_options.prep_ops
        )
        return np.sum(np.abs(chi_guess - chi_exp) ** 2)

    def calc_ideal_rho(self, pre_gate_list: List):
        """Calculate pre_gate ideal matrix."""
        q_nums = self.run_options.qubit_num
        ideal_r_arr = np.zeros((2**q_nums, 2**q_nums))

        ideal_r_arr[0][0] = 1
        ideal_u = None

        if q_nums == 1:
            ideal_u = np.eye(2)
            for gate in pre_gate_list:
                ideal_u = np.matmul(self.gate_bucket.get_matrix(gate), ideal_u)
        else:
            for l_gate, r_gate in zip(*pre_gate_list):
                l_mat = self.gate_bucket.get_matrix(l_gate)
                r_mat = self.gate_bucket.get_matrix(r_gate)
                if l_gate == "CZ" or r_gate == "CZ":
                    s_mat = l_mat
                else:
                    s_mat = np.kron(l_mat, r_mat)
                if ideal_u is None:
                    ideal_u = s_mat
                else:
                    ideal_u = np.matmul(s_mat, ideal_u)

        try:
            conj_ideal_u = np.transpose(np.conj(ideal_u))
            ideal_matrix = np.matmul(
                np.matmul(ideal_u, ideal_r_arr),
                conj_ideal_u,
            )
        except Exception as err:
            ideal_matrix = None
        return ideal_matrix

    def _cal_qst_fidelity(self, probability: np.ndarray):
        diag_list = probability.T.flatten()
        rho_exp, _status = cvxpy_linear_lstsq(
            diag_list,
            self.run_options.mea_ops,
        )
        if self.analysis_options.pre_gate_list:
            rho_ideal = self.calc_ideal_rho(self.analysis_options.pre_gate_list)
            fidelity = qp.fidelity(qp.Qobj(rho_exp), qp.Qobj(rho_ideal))
            self.qst_result.fidelity = fidelity
            self.qst_result.rho_ideal = rho_ideal

        self.qst_result.rho_exp = np.matrix(rho_exp)

