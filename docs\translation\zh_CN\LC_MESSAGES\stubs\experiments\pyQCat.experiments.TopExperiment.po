# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:2
msgid "pyQCat.experiments.TopExperiment"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment:1
msgid "Bases: :py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment:1
msgid "Abstract quantum experiment base class."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment:3
msgid ""
"The top-level interface of the experiment contains the properties and "
"methods necessary for the qubit qubit_test experiment. If you want to "
"implement a new experiment, it is recommended that you inherit this class"
" and rewrite the ``run()`` method to implement specific operations."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.__init__:1
msgid "Create a new experiment."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.TopExperiment.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`acquire_pulse "
"<pyQCat.experiments.TopExperiment.acquire_pulse>`\\ \\(acq\\_pulse\\[\\, "
"qubit\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment._set_measure_pulses:1
#: pyQCat.experiments.top_experiment.TopExperiment.acquire_pulse:1
msgid "Set measure pulse."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`cal_fidelity <pyQCat.experiments.TopExperiment.cal_fidelity>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.cal_fidelity:1
msgid "Calculate the fidelity matrix."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`experiment_info "
"<pyQCat.experiments.TopExperiment.experiment_info>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.experiment_info:1
msgid "Show experiment informations."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.TopExperiment.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.TopExperiment.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`jupyter_schedule "
"<pyQCat.experiments.TopExperiment.jupyter_schedule>`\\ \\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.jupyter_schedule:1
msgid "Show experiment pulses's schedules in Jupyter Notebook."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.TopExperiment.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`play_pulse <pyQCat.experiments.TopExperiment.play_pulse>`\\ "
"\\(name\\, base\\_qubit\\, pulse\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.play_pulse:1
msgid "Play pulse for XY control and Z flux control."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`plot_schedule "
"<pyQCat.experiments.TopExperiment.plot_schedule>`\\ "
"\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.plot_schedule:1
msgid ""
"Plot the pulse schedule which is very important to confirm experiment "
"logical correction."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`run <pyQCat.experiments.TopExperiment.run>`\\ \\(\\*args\\, "
"\\*\\*kwargs\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.run:1
msgid "Run experiment"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.TopExperiment.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.TopExperiment.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_multiple_IF "
"<pyQCat.experiments.TopExperiment.set_multiple_IF>`\\ \\(\\*IF\\[\\, "
"channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.set_multiple_IF:1
msgid "Used for multiple qubits experiment, set mutiple IF."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_multiple_index "
"<pyQCat.experiments.TopExperiment.set_multiple_index>`\\ \\(\\*args\\[\\,"
" channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
"Used for multiple qubits experiment, set mutiple index. :type args: "
":py:class:`list` :param args: Setting the start and end values :type "
"channel: :py:class:`int` :param channel: channel settings Usage::     "
">>>experiment = TopExperiment()     >>>experiment.set_multiple_index([0, "
"1500], [100, 900], channel=1)."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.TopExperiment.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.TopExperiment.set_run_options>`\\ \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_sweep_order "
"<pyQCat.experiments.TopExperiment.set_sweep_order>`\\ \\(order\\_list\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.set_sweep_order:1
msgid "Old interface."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:39:<autosummary>:1
msgid ""
":py:obj:`sweep_readout_trigger_delay "
"<pyQCat.experiments.TopExperiment.sweep_readout_trigger_delay>`\\ "
"\\(channel\\, sweep\\_delay\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.TopExperiment.rst:41
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._initialize_experiment:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.TopExperiment.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._initialize_experiment:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._initialize_experiment:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.TopExperiment.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._initialize_experiment:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._initialize_experiment:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.TopExperiment.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._initialize_experiment:1:<autosummary>:1
msgid ":py:obj:`run_options <pyQCat.experiments.TopExperiment.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._initialize_experiment:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._initialize_experiment:1
msgid "initialize experiment settings."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._create_experiment_data
#: pyQCat.experiments.top_experiment.TopExperiment._data_collection
#: pyQCat.experiments.top_experiment.TopExperiment._run_analysis
#: pyQCat.experiments.top_experiment.TopExperiment._save_source_data
#: pyQCat.experiments.top_experiment.TopExperiment.acquire_pulse
#: pyQCat.experiments.top_experiment.TopExperiment.jupyter_schedule
#: pyQCat.experiments.top_experiment.TopExperiment.play_pulse
#: pyQCat.experiments.top_experiment.TopExperiment.plot_schedule
#: pyQCat.experiments.top_experiment.TopExperiment.set_multiple_IF
msgid "Parameters"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.play_pulse:4
msgid "Only can be set as \"XY\" or \"Z\"."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.play_pulse:7
msgid "indicate the pulse is used for which qubit or."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.play_pulse:15
msgid "pulse object must be called `get_pulse` method before calling this method."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._set_acquisition_type
#: pyQCat.experiments.top_experiment.TopExperiment.jupyter_schedule
#: pyQCat.experiments.top_experiment.TopExperiment.play_pulse
msgid "Returns"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.play_pulse:17
msgid "None"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._register
#: pyQCat.experiments.top_experiment.TopExperiment.play_pulse
msgid "Raises"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.play_pulse:19
msgid "when name is not set as \"XY\" or \"Z\"."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.play_pulse:20
msgid "when pulse is not the instance of classe `pyQCat.PulseComponent`."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.acquire_pulse:4
msgid "Pulse object to generate measure pulse."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.acquire_pulse:7
msgid "The Qubit object."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.set_multiple_IF:3
msgid "intermediate frequency."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.set_multiple_IF:5
msgid "readout control channel."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.set_multiple_index:1
msgid ""
"Used for multiple qubits experiment, set mutiple index. :type args: "
":py:class:`list` :param args: Setting the start and end values :type "
"channel: :py:class:`int` :param channel: channel settings Usage::"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._register:1
msgid ""
"Register all datas to database which the data include instrument settings"
" and pulse settings."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._register:4
msgid "If experiment data saved to database failed."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._register:5
msgid "If user set `data_type` out of namespace."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._plot_pulses_schedule:1
msgid "Plot pulse schedule."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.plot_schedule:5
msgid "Plot pulse index."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.plot_schedule:8
msgid "If set true, the pulse schedule will be saved."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.plot_schedule:11
msgid "Determines whether the measurement pulse is displayed."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.plot_schedule:14
msgid "Pulse timing diagram drawing method, `sequence` or `envelop`."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._bind_probe_inst:1
msgid "Bind qubit information to instrument."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._validate_parameters:1
msgid "Validate parameters."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._init_instrument:1
msgid "Initialize instrument settings."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._bind_qubits_inst:1
msgid "Bind qubit parameters to instruments."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._bind_qubit_drive:1
msgid "Bind qubit drive parameters to instruments."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._bind_qubit_probe:1
msgid "Only single qubit experiment need bind qubit probe & instrument"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._bind_qubit_dc:1
msgid "Bind qubit voltage to instrument."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._register_pulse:1
msgid "Register pulse and do pulse correction."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._auto_set_readout_delay:1
msgid "Auto set readout delay according to XY and Z pulse length."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._open_zcompensate:1
msgid ""
"Open AWG channel for experiment to eliminate AWG channel compensation. "
"Set Z_flux moudle's pulse according to XY moudle's pulse. Must be sure "
"call this method before call set_pulse(\"Z\") method. since z pulse time "
"is equal to xy pulse time, don not need to append to "
"self._pulse_time_list."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._open_zcompensate:7
msgid "Multiple qubits experiment needs add AC crosstalk."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._set_single_readout_pulse:1
msgid "Set single bit readout pulse."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._set_union_readout_pulse:1
msgid "Set union readout pulse."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._get_union_readout_pulse_utility:1
msgid "Set readout pulse with parameters."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._create_experiment_data:1
msgid "Create ExperimentData to Analysis. Update self.experiment_data value."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._create_experiment_data:5
#: pyQCat.experiments.top_experiment.TopExperiment._data_collection:5
msgid "data type of register to AIO"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._create_experiment_data:8
#: pyQCat.experiments.top_experiment.TopExperiment._data_collection:8
msgid "scan parameter range"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._create_experiment_data:11
#: pyQCat.experiments.top_experiment.TopExperiment._data_collection:21
msgid "mark field, SingleShot experiment use loop_num to adjust repeat > 10000"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._save_source_data:1
msgid "Save source data from ExperimentData x_data and y_data."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._data_collection:25
#: pyQCat.experiments.top_experiment.TopExperiment._save_source_data:4
msgid "save file name some mark information"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._data_collection:28
#: pyQCat.experiments.top_experiment.TopExperiment._save_source_data:7
msgid "save data is or not iq like SingleShot"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._data_collection:1
msgid ""
"Data collection contains acquisition data, create experiment data, and "
"save source data."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._data_collection:11
msgid "plot dynamic is or not, default 1, means do."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._data_collection:14
msgid "fidelity matrix, correct the measure result"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._data_collection:18
msgid "measure bit list"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:1
msgid "Default kwarg options for experiment."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:40
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:4
msgid "bind_dc (bool): Whether to bind the initial DC of the current qubit"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:5
msgid "to the inst."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:6
msgid "bind_drive (bool): Whether to bind the drive frenquency of the"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:7
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:9
msgid "current qubit to the inst."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:8
msgid "bind_probe (bool): Whether to bind the probe frenquency of the"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:10
msgid "file_flag (int): OriginQAIO collection source data type,"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:11
msgid "and save or not."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:13
msgid "multi_readout_channels (List, optional): Multiple readout channels,"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:13
msgid ""
"list of int, normal used to appoint the readout channel, or multiple bits"
" experiment, readout channel not same."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:15
msgid "repeat (int): Repeat number."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:18
msgid "data_type (str): The data type determines how the collected data"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:18
msgid "is processed. Now only support \"amp_phase\", \"I_Q\" and \"track\"."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:20
msgid ""
"enable_one_sweep (bool):The flag bit of a single loop experiment. "
"register_pulse_save (bool): Save or not register pulse data."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:23
msgid "schedule_flag (bool): Plot or not schedule, normal cavity frequency"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:24
msgid "experiment XY or Z line no pulse, so no plot."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:25
msgid ""
"schedule_index (Union[int, List]): Select a pulse schedule to plot. "
"schedule_save (bool): Save or not, schedule plot figure. schedule_measure"
" (bool): Plot schedule, show or not measure pulse. schedule_type (str, "
"optional): Pulse timing diagram drawing method,"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:29
msgid "`sequence` or `envelop`."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:31
msgid ""
"is_dynamic (int): When collect data, dynamic plot or not. fidelity_matrix"
" (array): Fidelity amend matrix. measure_bits (List): When multiple bits "
"experiment,"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:34
msgid "set measure bits, list of int."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:35
msgid "loop_num (int): Mark field, SingleShot experiment use loop_num"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:36
msgid "to adjust repeat > 10000."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:37
msgid ""
"save_label (str): Save source data, file name mark information. iq_flag "
"(bool): Save source data, data like SingleShot iq or not."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options
#: pyQCat.experiments.top_experiment.TopExperiment._set_acquisition_type
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._default_experiment_options:42
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._set_acquisition_type:1
msgid "Set acquisition data type."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._set_acquisition_type:3
msgid ":py:class:`str`"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._set_acquisition_type:4
msgid "I_Q or amp_phase."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._run_analysis:1
msgid "Default experiment running flow."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._run_analysis:4
msgid "The list of variables that will be scanned in the experiment."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._run_analysis:8
msgid "The class or subclass of TopAnalysis."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._set_xy_pulses:1
msgid "Set XY pulse."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._set_z_pulses:1
msgid "Set z pulse."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._update_instrument:1
msgid "Update instrument parameters before running."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._build_z_compensate_pulse:1
msgid "build z line pulse."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.jupyter_schedule:4
msgid "The index of the schedules sequences."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment.jupyter_schedule:7
msgid "PIL.Image object."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._connect_z_pulse:1
msgid ""
"connect 3 pulse. 1st is AC prepare z pulse. 2nd is experimental z pulse. "
"3rd is readout work point z pulse."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._fill_z_experimental_pulse:1
msgid "Fill experiment Z line pulse."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment._fill_z_prepare_pulse:1
msgid ""
"If use AC instead DC, Z line needs play z pulse before experiment starts "
"to reduce distortion."
msgstr ""

#: of
#: pyQCat.experiments.top_experiment.TopExperiment._fill_readout_work_point_pulse:1
msgid "Fill readout point Z line pulse."
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.experiments.TopExperiment.__init__>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`acquire_pulse "
#~ "<pyQCat.experiments.TopExperiment.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.TopExperiment.from_experiment_context>`\\ "
#~ "\\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.TopExperiment.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`play_pulse "
#~ "<pyQCat.experiments.TopExperiment.play_pulse>`\\ \\(name\\, "
#~ "base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot_schedule "
#~ "<pyQCat.experiments.TopExperiment.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run <pyQCat.experiments.TopExperiment.run>`\\ "
#~ "\\(\\*args\\, \\*\\*kwargs\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.TopExperiment.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.TopExperiment.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_IF "
#~ "<pyQCat.experiments.TopExperiment.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_index "
#~ "<pyQCat.experiments.TopExperiment.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.TopExperiment.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.TopExperiment.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_sweep_order "
#~ "<pyQCat.experiments.TopExperiment.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":py:obj:`analysis <pyQCat.experiments.TopExperiment.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.TopExperiment.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.TopExperiment.experiment_options>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`run_options <pyQCat.experiments.TopExperiment.run_options>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.experiments.TopExperiment.__init__>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`acquire_pulse "
#~ "<pyQCat.experiments.TopExperiment.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_info "
#~ "<pyQCat.experiments.TopExperiment.experiment_info>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.TopExperiment.from_experiment_context>`\\ "
#~ "\\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.TopExperiment.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`jupyter_schedule "
#~ "<pyQCat.experiments.TopExperiment.jupyter_schedule>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.TopExperiment.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`play_pulse "
#~ "<pyQCat.experiments.TopExperiment.play_pulse>`\\ \\(name\\, "
#~ "base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot_schedule "
#~ "<pyQCat.experiments.TopExperiment.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run <pyQCat.experiments.TopExperiment.run>`\\ "
#~ "\\(\\*args\\, \\*\\*kwargs\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.TopExperiment.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.TopExperiment.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_IF "
#~ "<pyQCat.experiments.TopExperiment.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_index "
#~ "<pyQCat.experiments.TopExperiment.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.TopExperiment.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.TopExperiment.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_sweep_order "
#~ "<pyQCat.experiments.TopExperiment.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.TopExperiment.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.TopExperiment.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.TopExperiment.experiment_options>`\\"
#~ msgstr ""

#~ msgid ":obj:`run_options <pyQCat.experiments.TopExperiment.run_options>`\\"
#~ msgstr ""

#~ msgid "qubits validation"
#~ msgstr ""

#~ msgid "Sweep hierarchy."
#~ msgstr ""

#~ msgid "Sweep combination"
#~ msgstr ""

#~ msgid "Multiple qubits experiment needs add AC ctrosstalk."
#~ msgstr ""

#~ msgid "The list of variables that willbe scanned in the experiment."
#~ msgstr ""

#~ msgid "The class or sub-class of TopAnalysis."
#~ msgstr ""

