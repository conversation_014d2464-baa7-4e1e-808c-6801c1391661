# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.qubit.rst:2
msgid "pyQCat.qubit package"
msgstr ""

#: ../../source/api/pyQCat.qubit.rst:5
msgid "Module contents"
msgstr ""

#: of pyQCat.qubit:3
msgid "Base Qubit (:mod:`pyQCat.qubit`)"
msgstr ""

#: of pyQCat.qubit:5
msgid "Initial qubit model."
msgstr ""

#: of pyQCat.qubit:8
msgid "Base Classes"
msgstr ""

#: of pyQCat.qubit:16:<autosummary>:1
msgid ":py:obj:`BaseQubit <pyQCat.qubit.BaseQubit>`\\ \\(name\\)"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit:1 pyQCat.qubit:16:<autosummary>:1
msgid "BaseQubit class, depends on different chip structure."
msgstr ""

#: of pyQCat.qubit:16:<autosummary>:1
msgid ":py:obj:`Qubit <pyQCat.qubit.Qubit>`\\ \\(bit\\)"
msgstr ""

#: of pyQCat.qubit.qubit.Qubit:1 pyQCat.qubit:16:<autosummary>:1
msgid "Quantum bit entity class."
msgstr ""

#: of pyQCat.qubit:16:<autosummary>:1
msgid ":py:obj:`Coupler <pyQCat.qubit.Coupler>`\\ \\(bit\\)"
msgstr ""

#: of pyQCat.qubit.qubit.Coupler:1 pyQCat.qubit:16:<autosummary>:1
msgid "Coupler bit entity class."
msgstr ""

#: of pyQCat.qubit:16:<autosummary>:1
msgid ":py:obj:`QubitPair <pyQCat.qubit.QubitPair>`\\ \\(name\\)"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair:1 pyQCat.qubit:16:<autosummary>:1
msgid "Qubit Pair."
msgstr ""

#: of pyQCat.qubit.qubit.Coupler:1 pyQCat.qubit.qubit.Qubit:1
msgid "Bases: :py:class:`~pyQCat.qubit.qubit.BaseQubit`"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.anno:1 pyQCat.qubit.qubit.Coupler.anno:1
#: pyQCat.qubit.qubit.Qubit.anno:1
msgid "Normal used to describe."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit:1 pyQCat.qubit.qubit_pair.QubitPair:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit:5
msgid "BaseQubit use row-major ordering:"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit:4
msgid "BaseQubit(0, 0) < BaseQubit(0, 1) < BaseQubit(1, 0) < BaseQubit(1, 1)"
msgstr ""

#: of pyQCat.qubit.BaseQubit.row:1
msgid "coordinate row."
msgstr ""

#: of pyQCat.qubit.BaseQubit.col:1
msgid "coordinate col."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.set_coords:1
msgid "Set coords."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.is_adjacent:1
msgid "Determines if two qubits are adjacent qubits."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit._comparison_key
#: pyQCat.qubit.qubit.BaseQubit.from_dict
#: pyQCat.qubit.qubit.BaseQubit.from_file
#: pyQCat.qubit.qubit.BaseQubit.is_adjacent
#: pyQCat.qubit.qubit.BaseQubit.to_dict
#: pyQCat.qubit.qubit_pair.QubitPair.from_dict
#: pyQCat.qubit.qubit_pair.QubitPair.to_dict
msgid "Return type"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.is_adjacent:4
msgid ":py:class:`bool`"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit._comparison_key:1
msgid "key to judge two qubits equal."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit._comparison_key:4
msgid ":py:data:`~typing.Any`"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.from_dict:1
msgid "Load qubit information from dict."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.ac_instead_dc
#: pyQCat.qubit.qubit.BaseQubit.from_dict
#: pyQCat.qubit.qubit.BaseQubit.from_file pyQCat.qubit.qubit.BaseQubit.to_file
#: pyQCat.qubit.qubit_pair.QubitPair.from_dict
#: pyQCat.qubit.qubit_pair.QubitPair.from_file
#: pyQCat.qubit.qubit_pair.QubitPair.to_file
msgid "Parameters"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.from_dict:4
msgid "Dict of qubit information."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.from_dict:8
#: pyQCat.qubit.qubit.BaseQubit.from_file:11
msgid ":py:class:`~pyQCat.qubit.qubit.BaseQubit`"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.from_file:1
#: pyQCat.qubit.qubit_pair.QubitPair.from_file:1
msgid "Load qubit information from file."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.from_file:4
#: pyQCat.qubit.qubit_pair.QubitPair.from_file:4
msgid "From file path name."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.from_file:7
#: pyQCat.qubit.qubit_pair.QubitPair.from_file:7
msgid "From file type, support `yaml` and `json`."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.to_dict:1
#: pyQCat.qubit.qubit_pair.QubitPair.to_dict:1
msgid "Convert object to dict structure."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.to_dict
#: pyQCat.qubit.qubit_pair.QubitPair.from_dict
#: pyQCat.qubit.qubit_pair.QubitPair.to_dict
msgid "Returns"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.to_dict:3
#: pyQCat.qubit.qubit_pair.QubitPair.to_dict:3
msgid "Target data."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.to_file:1
#: pyQCat.qubit.qubit_pair.QubitPair.to_file:1
msgid "Export the object to a `yaml` or `json` file."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.to_file:4
#: pyQCat.qubit.qubit_pair.QubitPair.to_file:4
msgid "Export file path, default `conf/bit_data'."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.to_file:7
#: pyQCat.qubit.qubit_pair.QubitPair.to_file:7
msgid "Export file type, support `yaml` and `json`."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.save_database:1
#: pyQCat.qubit.qubit_pair.QubitPair.save_database:1
msgid "Save database hook. Push the information to database."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.reset:1
msgid "Reset fields."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.put_sweet_point:1
msgid "Put Qubit/Coupler on sweet point."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.ac_instead_dc:1
msgid "Use AWG instead DC module to provide voltage."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.ac_instead_dc:4
msgid "Value to provide static work point."
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict:1
msgid "Create QubitPair object using Dict."
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict:4
msgid "Dict of two qubits information."
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict:7
msgid ":py:class:`~pyQCat.qubit.qubit_pair.json.QubitPair`"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict:8
msgid "`QubitPair` object."
msgstr ""

#~ msgid ""
#~ ":py:obj:`BaseQubit <pyQCat.qubit.BaseQubit>`\\ "
#~ "\\(name\\[\\, username\\, label\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`Qubit <pyQCat.qubit.Qubit>`\\ \\(bit\\[\\, "
#~ "username\\, label\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`Coupler <pyQCat.qubit.Coupler>`\\ \\(bit\\[\\,"
#~ " username\\, label\\]\\)"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.qubit.qubit.BaseQubit`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`BaseQubit <pyQCat.qubit.BaseQubit>`\\ \\(name\\[\\,"
#~ " username\\, label\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`Qubit <pyQCat.qubit.Qubit>`\\ \\(bit\\[\\, "
#~ "username\\, label\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`Coupler <pyQCat.qubit.Coupler>`\\ \\(bit\\[\\, "
#~ "username\\, label\\]\\)"
#~ msgstr ""

#~ msgid "Load qubit information from json file or database."
#~ msgstr ""

#~ msgid "json file path. Defaults to None."
#~ msgstr ""

#~ msgid "According to time get Qubit attribute value from Store."
#~ msgstr ""

#~ msgid "str or datetime.datetime, Examples, 2021-03-26 15:23:38.442"
#~ msgstr ""

#~ msgid "qubit/coupler number, default self.bit"
#~ msgstr ""

#~ msgid "Export qubit info to a yaml file."
#~ msgstr ""

#~ msgid "export qubit data path, default `config/qubit_data'"
#~ msgstr ""

#~ msgid "Log message to file, write qubit running state and some info."
#~ msgstr ""

#~ msgid "message"
#~ msgstr ""

#~ msgid "log path"
#~ msgstr ""

#~ msgid ""
#~ "Compare the current attribute value with"
#~ " the attribute value in the database."
#~ " If it is inconsistent, modify the"
#~ " corresponding attribute value in the "
#~ "database and save it to the "
#~ "database again."
#~ msgstr ""

#~ msgid "Save {}.yaml to MongoDB Store."
#~ msgstr ""

#~ msgid "{}.yaml path, like `{path}/config/q0.yaml`"
#~ msgstr ""

#~ msgid "Update fields."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`BaseQubit <pyQCat.qubit.BaseQubit>`\\ \\(name\\[\\,"
#~ " sample\\, username\\, point\\_label\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`Qubit <pyQCat.qubit.Qubit>`\\ \\(bit\\[\\, "
#~ "sample\\, username\\, point\\_label\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`Coupler <pyQCat.qubit.Coupler>`\\ \\(bit\\[\\, "
#~ "sample\\, username\\, point\\_label\\]\\)"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.qubit.qubit.BaseQubit`"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ "Save database hook. Push the information"
#~ " to the process queue, and then "
#~ "save it by other processes."
#~ msgstr ""

