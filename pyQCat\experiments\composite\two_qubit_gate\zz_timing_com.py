# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/07/07
# __author:       <PERSON><PERSON><PERSON>

import json
from copy import deepcopy

from ....analysis import ZZTimingCompositeAnalysis
from ....log import pyqlog
from ....qaio_property import QAIO
from ....structures import MetaData, Options
from ....types import ExperimentRunMode, QualityDescribe
from ...composite_experiment import CompositeExperiment
from ...single import ZZTimingOnce


class ZZTimingComposite(CompositeExperiment):
    _sub_experiment_class = ZZTimingOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """
                delay (float): refresh child experiment (ZZTimingOnce) keep same const delay
        `       iteration (int): maximum number of iterations
                mode (str):
                    - z3: iter qc, qh, ql, default mode
                    - z1: only iter qc, when the delay of the Z-line of qh and ql has been
                        determined, and their relative delay has been determined, iterative
                        testing can be conducted only for qc
        """
        options = super()._default_experiment_options()

        options.set_validator("delay", float)
        options.set_validator("iteration", int)
        options.set_validator("iter_names", list)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.delay = 20
        options.iteration = 1
        options.mode = "All"
        options.iter_names = ["ql", "qh", "qc"]
        options.threshold = 1

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.iter_state = 0
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.init_offset_map = None
        options.final_offset_map = None
        options.bit_map = None
        options.iter_names = None

        return options

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.process_meta = {
            "bit_map": self.run_options.bit_map,
            "final_offset_map": self.run_options.final_offset_map,
            "init_offset_map": self.run_options.init_offset_map,
        }
        return metadata

    def _check_options(self):
        super()._check_options()
        bit_map = {
            "qh": self.qubit_pair.qh,
            "ql": self.qubit_pair.ql,
            "qc": self.qubit_pair.qc,
        }
        iter_names = self.experiment_options.iter_names
        qc_map = {qubit.name: qubit for qubit in self.qubits}
        qc_map.update({coupler.name: coupler for coupler in self.couplers})
        for name in iter_names:
            qc_name = getattr(self.qubit_pair, name)
            qc_obj = qc_map.get(qc_name)
            if not qc_obj.tunable:
                iter_names.remove(name)
        # validate_qubit_pair_cz_std(self, "zz")
        # gate_params = self.qubit_pair.gate_params("zz")
        # for name in iter_names:
        #     qc = getattr(self.qubit_pair, name)
        #     amp = gate_params.get(qc).amp
        #     if amp == 0:
        #         iter_names.remove(name)
        self.set_run_options(bit_map=bit_map, iter_names=iter_names)
        self.set_run_options(init_offset_map=self.record_offset())
        self.set_analysis_options(result_name=self.qubit_pair.name)

    async def _sync_composite_run(self):
        threshold = self.experiment_options.threshold
        self.reset()
        index, iter_num = 0, 0
        cd = self.child_experiment
        iter_const_delay = deepcopy(cd.experiment_options.const_delay)
        record_map = {}
        exp: ZZTimingOnce = None
        iter_fail = False

        while True:
            minimize_offset = True
            record_map.update({f"iter-{iter_num}": {}})

            for scan_bit in self.run_options.iter_names:
                exp = deepcopy(cd)
                exp.set_experiment_options(
                    scan_bit=scan_bit, const_delay=iter_const_delay
                )
                exp.set_parent_file(
                    self, index=index, description=f"iter-{iter_num}-sweep-{scan_bit}"
                )
                self._check_simulator_data(exp, index)
                await exp.run_experiment()
                self._experiments.append(exp)
                index += 1

                res = exp.analysis.results.offset.value

                record_map[f"iter-{iter_num}"].update({scan_bit: res})

                # when the offset of qc,ql,qh are all
                # less than one sample period, skip the iteration.
                if minimize_offset:
                    minimize_offset = abs(res) < threshold / QAIO.awg_sample_rate

                iter_const_delay[scan_bit] += res

                # bugfix 2024/05/09 zyc: When const delay is less than 0,
                # it will cause waveform compilation errors. This further
                # leads to the failure of parallel merging
                if iter_const_delay[scan_bit] < 0 or iter_const_delay[scan_bit] > max(
                    exp.experiment_options.delays
                ):
                    iter_fail = True
                    pyqlog.error(
                        f"iter const delay {scan_bit}({iter_const_delay[scan_bit]}) overflow, break iteration!"
                    )
                    break

                exp.analysis.provide_for_parent["offset"] = res
                pyqlog.log(
                    "RESULT",
                    f"{self.qubit_pair.name} Current Const Delay: {iter_const_delay}",
                )
            iter_num += 1

            if iter_fail is True:
                break

            if minimize_offset:
                self.analysis_options.iter_state = 1
                pyqlog.log(
                    "EXP",
                    f"All offset of qc,ql,qh are less than {threshold} sample period, skip the iteration.",
                )
                break

            if iter_num >= self.experiment_options.iteration:
                self.analysis_options.iter_state = 0
                pyqlog.log("EXP", "Reached maximum number of iterations.")
                break

        if iter_fail is True:
            self._analysis = ZZTimingCompositeAnalysis.empty_analysis(
                QualityDescribe.bad
            )
            return

        self.update_pulse_compensate(iter_const_delay)

        self.analysis_options.link_channel = [
            exp.run_options.qh.z_flux_channel,
            exp.run_options.ql.z_flux_channel,
            exp.run_options.qc.z_flux_channel,
        ]

        self._run_analysis(
            x_data=list(range(index)), analysis_class=ZZTimingCompositeAnalysis
        )

        if self.analysis:
            record_map.update(
                {
                    "result": {
                        "qc": {
                            "name": exp.run_options.qc.name,
                            "z_flux_channel": exp.run_options.qc.z_flux_channel,
                            "relative_delay": self.analysis.results.link.value[-1],
                        },
                        "qh": {
                            "name": exp.run_options.qh.name,
                            "z_flux_channel": exp.run_options.qh.z_flux_channel,
                            "relative_delay": self.analysis.results.link.value[-3],
                        },
                        "ql": {
                            "name": exp.run_options.ql.name,
                            "z_flux_channel": exp.run_options.ql.z_flux_channel,
                            "relative_delay": self.analysis.results.link.value[-2],
                        },
                        "process": {
                            "before": {
                                "qh-xy-z": self.analysis.results.qh_pre.value,
                                "ql-xy-z": self.analysis.results.ql_pre.value,
                                "qc-xy-z": self.analysis.results.qc_pre.value,
                            },
                            "current": {
                                "qh-xy-z": self.analysis.results.qh.value,
                                "ql-xy-z": self.analysis.results.ql.value,
                                "qc-xy-z": self.analysis.results.qc.value,
                            },
                        },
                    }
                }
            )
            mark_info = f"{self.qubit_pair.name}_zz_iter"
            json_str = json.dumps(record_map, ensure_ascii=False, indent=4)
            self.file.save_text(json_str, name=mark_info, prefix=".json")
            pyqlog.log("RESULT", f"{mark_info}:\n{json_str}")

            # 2025.01.16 Issue (ZS)
            # Trans `record_map` adjust to `hardware_offset.json` data format.
            trans_map = self.trans_record_map(exp.run_options.qc, record_map)
            trans_mark = f"{exp.run_options.qc.name}_zz_delay"
            trans_json_str = json.dumps(trans_map, ensure_ascii=False, indent=4)
            self.file.save_text(trans_json_str, name=trans_mark, prefix=".json")
            pyqlog.log("RESULT", f"{trans_mark}:\n{trans_json_str}")

    def reset(self):
        self.child_experiment.experiment_options.const_delay = {
            "qc": self.experiment_options.delay,
            "qh": self.experiment_options.delay,
            "ql": self.experiment_options.delay,
        }

    def record_offset(self):
        offset_map = {}
        msg = ""
        for name, bit_name in self.run_options.bit_map.items():
            compensate = None
            for qubit, com in self.child_experiment.compensates.items():
                if bit_name == qubit.name:
                    compensate = com
                    break
            msg += f"\nCurrent-{name}/{bit_name}-delay(xy/z): {[compensate.x_delay, compensate.z_delay]}"
            offset_map[bit_name] = [compensate.x_delay, compensate.z_delay]
        pyqlog.log("EXP", f"Current hardware offset:{msg}")
        return offset_map

    def update_pulse_compensate(self, delay_map):
        init_offset_map = deepcopy(self.run_options.init_offset_map)
        bit_map = self.run_options.bit_map
        final_offset_map = {}

        min_delay = min(list(delay_map.values()))
        for key in list(delay_map.keys()):
            bit_name = bit_map.get(key)
            delay = round(delay_map[key] - min_delay, 5)
            # xd = round(round(delay * QAIO.dac_sample_rate) / QAIO.dac_sample_rate, 4)
            # zd = round(round(delay * QAIO.awg_sample_rate) / QAIO.awg_sample_rate, 4)
            # if zd < 0.1:
            #     xd = zd
            # pyqlog.info(
            #     f"{key}/{bit_name} actual offset {delay} ns | round xy delay {xd} ns | round z delay {zd} ns!"
            # )

            x, z = init_offset_map.get(bit_name)
            # final_offset_map[bit_name] = [x + xd, z + zd]
            final_offset_map[bit_name] = [x + delay, z + delay]

        self.set_run_options(final_offset_map=final_offset_map)

    @staticmethod
    def trans_record_map(coupler, record_map):
        """Trans record_map dict."""
        zc_name = coupler.name
        zp_name = f"q{coupler.probe_bit}"
        zd_name = f"q{coupler.drive_bit}"

        trans_dict = {}
        result_dict = record_map.get("result", {})
        for field in ["qc", "qh", "ql"]:
            f_dict = result_dict.get(field, {})
            if f_dict:
                name = f_dict.get("name", "")
                t_dict = {
                    "channel": f_dict.get("z_flux_channel") or 0,
                    "delay": f_dict.get("relative_delay") or 0.0,
                }
                if name == zc_name:
                    trans_dict["zc"] = t_dict
                elif name == zp_name:
                    trans_dict["zp"] = t_dict
                elif name == zd_name:
                    trans_dict["zd"] = t_dict

        trans_result = {zc_name: trans_dict}
        return trans_result
