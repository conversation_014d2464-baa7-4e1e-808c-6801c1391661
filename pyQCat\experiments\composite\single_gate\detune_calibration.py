# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/09/16
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy

import numpy as np

from ....analysis.library import DetuneCalibrationAnalysis
from ....analysis.specification import ParameterRepr
from ....structures import MetaData, Options
from ....tools import qarange
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from .ape_composite import APEComposite, CouplerAPEComposite


class DetuneCalibration(CompositeExperiment):
    _sub_experiment_class = APEComposite

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("detune_list", list)
        options.set_validator("rough_n_list", list)
        options.set_validator("fine_n_list", list)
        options.set_validator("theta_type", ["Xpi", "Xpi/2"])
        options.set_validator("fine_precision", float)
        options.set_validator("simulator_shape", list)
        options.set_validator("f12_opt", bool)

        options.detune_list = qarange(-25, 10, 1)
        options.rough_n_list = [6, 7, 8]
        options.fine_n_list = [7, 9]
        options.theta_type = "Xpi"
        options.fine_precision = 0.1
        options.simulator_shape = [3, 2]
        options.f12_opt = False

        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.text_pos = None
        options.text_rp = None
        options.plot_key = None
        options.y_label = None
        options.result_parameters = []

        return options

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.draw_meta = {
            "theta_type": self.experiment_options.theta_type,
        }
        return metadata

    def _check_options(self):
        """Check options."""
        super()._check_options()

        if self.experiment_options.theta_type == "Xpi":
            result_parameters = [
                ParameterRepr(name="detune", repr="detune", unit="MHz"),
                ParameterRepr(name="detune2", repr="detune2", unit="MHz"),
            ]
        else:
            result_parameters = [
                ParameterRepr(name="detune2", repr="detune2", unit="MHz")
            ]

        cc_exp = self.child_experiment.child_experiment
        result_name, plot_key, y_label = self.child_experiment.check_key(cc_exp)

        self.set_analysis_options(
            result_name=result_name,
            plot_key=plot_key,
            y_label=y_label,
            result_parameters=result_parameters,
        )

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        cc_exp = self.child_experiment.child_experiment

        if self.experiment_options.f12_opt:
            path_pre = "Qubit.f12_options"
        elif cc_exp.is_coupler_exp:
            path_pre = "Coupler.drive_XYwave"
        elif cc_exp.coupler:
            path_pre = "Coupler.probe_XYwave"
        else:
            path_pre = "Qubit.XYwave"

        for key, result in self.analysis.results.items():
            if key == "detune":
                result.extra["path"] = f"{path_pre}.detune_pi"
            else:
                result.extra["path"] = f"{path_pre}.detune_pi2"

    async def _sync_composite_run(self):
        # super().run()

        x_data = ["rough", "fine"]
        rough_n_list = self.experiment_options.rough_n_list
        fine_n_list = self.experiment_options.fine_n_list
        detune_list = self.experiment_options.detune_list
        theta_type = self.experiment_options.theta_type
        fine_precision = self.experiment_options.fine_precision
        f12_opt = self.experiment_options.f12_opt

        coincident_point = None
        text_pos = {}
        text_rp = {}

        for index, scan_type in enumerate(x_data):
            child_ape_composite_exp: APEComposite = deepcopy(self.child_experiment)

            n_list = rough_n_list if index == 0 else fine_n_list
            label = "RoughScan" if index == 0 else "FineScan"
            if index == 1:
                detune_list = np.linspace(
                    coincident_point - 15 * fine_precision,
                    coincident_point + 15 * fine_precision,
                    31,
                ).tolist()

            child_ape_composite_exp.set_parent_file(self, label, index, len(x_data))
            child_ape_composite_exp.set_experiment_options(
                n_list=n_list,
                detune_list=detune_list,
                theta_type=theta_type,
                scan_type=scan_type,
                f12_opt=f12_opt,
            )
            self._check_simulator_data(child_ape_composite_exp, index)
            await child_ape_composite_exp.run_experiment()
            child_ape_composite_exp.analysis.provide_for_parent.update(
                {"analysis": child_ape_composite_exp.analysis}
            )
            coincident_point = child_ape_composite_exp.analysis.results.detune.value
            self._experiments.append(child_ape_composite_exp)

            # shq 2024/04/30
            # fixed bug: AttributeError: 'NoneType' object has no attribute 'options'
            # due to: TopAnalysis
            #     def __reduce__(self):
            #         self.options.curve_drawer = None
            #         return super().__reduce__()
            text_pos[index] = child_ape_composite_exp.analysis.options.text_pos
            text_rp[index] = child_ape_composite_exp.analysis.options.text_rp

        self.set_analysis_options(text_pos=text_pos, text_rp=text_rp)

        self._run_analysis(x_data=x_data, analysis_class=DetuneCalibrationAnalysis)


class CouplerDetuneCalibration(DetuneCalibration):
    _sub_experiment_class = CouplerAPEComposite
