﻿pyQCat.analysis.library.CrosstalkAnalysis
=========================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: CrosstalkAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CrosstalkAnalysis.__init__
      ~CrosstalkAnalysis.from_sub_analysis
      ~CrosstalkAnalysis.run_analysis
      ~CrosstalkAnalysis.set_options
      ~CrosstalkAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CrosstalkAnalysis.analysis_datas
      ~CrosstalkAnalysis.data_filter
      ~CrosstalkAnalysis.drawer
      ~CrosstalkAnalysis.experiment_data
      ~CrosstalkAnalysis.has_child
      ~CrosstalkAnalysis.options
      ~CrosstalkAnalysis.quality
      ~CrosstalkAnalysis.results
   
   