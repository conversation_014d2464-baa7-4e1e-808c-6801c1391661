# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/06/19
# __author:       <PERSON><PERSON><PERSON>


"""-
SecondMeasureWrapper:

1. Check if second measurement extensions are required.
2. Perform two measurement task protocol extensions
"""

import math

from pyQCat.structures import CommonDict

from ...structures import QDict
from ...log import pyqlog
from ...proto_utils import ExperimentFile as Experiment

from ...pulse import Constant
from ...qm_protocol import SweepControl


class PrepareMeasureWrapper:
    def __init__(
        self,
        experiment: Experiment,
        offset: float = 300,
        readout_raw_delay_amp: CommonDict = None,
    ):
        self._experiment = experiment
        self._offset = offset
        self._readout_raw_delay_amp = readout_raw_delay_amp or {}

        self._validator_records = QDict(has_sweep_measure_delay=False, exp_pulse_widths=None)

    def run(self):
        if self._validator():
            self._work()

    def _validator(self):
        """
        校验任务是否可以进行 2 次测量扩展

        校验逻辑:
        1. MeasureAIO 中的各读取模块，不存在 2 次测量任务格式，如 trigger_delay、sample_delay 列表长度必须为 1
        2. SweepControl 中不存在涉及 2 次测量的读取通道
        """
        readout_control = self._experiment.measure_aio.Readout_control
        sweep_control = self._experiment.sweep_control

        # check measure aio readout control
        for readout_control in readout_control:
            count = len(readout_control.sampling_delay)
            if count != 1:
                pyqlog.warning(f"Origin task protocol measure count is {count}, no support second measure wrapper!")
                return False

        # checkout sweep control
        delete_control = []
        for control in sweep_control:
            split_results = control.func.split(":")

            if split_results[1] == "measure_delay":
                self._validator_records.has_sweep_measure_delay = True

            if split_results[1] in ["measure_delay", "sampling_delay"]:
                delete_control.append(control)
                if split_results[-1] != "0":
                    pyqlog.warning(
                        f"Origin task protocol check sweep {control.func}, no support second measure wrapper!"
                    )
                    return False

        for control in delete_control:
            sweep_control.remove(control)

        return True

    def _work(self):
        # extract max readout width, 默认所有比特的读取波形长度均一致
        readout_control = self._experiment.measure_aio.Readout_control[0]
        measure_width = readout_control.measure_width[0]

        # auto set xy module measure delay (`TRIGGER DELAY`)
        for control in self._experiment.measure_aio.XY_control:
            pre_measure_delay = control.measure_delay[0]
            control.measure_delay = [pre_measure_delay + measure_width + self._offset]

        # auto wrap z flux readout point pulse
        for control in self._experiment.measure_aio.Z_flux_control:
            self._wrap_z_flux_pulse(control)
        for control in self._experiment.sweep_control:
            if control.func.startswith("Z_flux_control:waveform"):
                self._wrap_z_flux_pulse(control)

        # extract experiment width
        has_warp_readout_control = False
        for control in self._experiment.sweep_control:
            if control.func.startswith("XY_control:waveform"):
                self._wrap_xy_control(control)
                has_warp_readout_control = True
                break
        if has_warp_readout_control is False:
            for control in self._experiment.measure_aio.XY_control:
                self._wrap_xy_control(control)
                break

        # auto wrap readout control
        self._wrap_readout_control()

    def _wrap_z_flux_pulse(self, control):
        if control.waveform:
            for pulse in control.waveform:
                readout_point_pulse = pulse.pulse[-1]
                pulse.fake_pulse_order.insert(0, pulse.fake_pulse_order[-1])
                pulse += Constant(self._offset, 0)()
                pulse.fake_pulse_order.insert(1, pulse.fake_pulse_order[-1])
                pulse.fake_pulse_order.pop()
                pulse.width += readout_point_pulse.width

    def _wrap_xy_control(self, control):
        pulse_width_list = []
        for pulse in control.waveform:
            pulse_width_list.append(pulse.width)
        self._validator_records.exp_pulse_widths = pulse_width_list

    def _wrap_readout_control(self):
        pulse_width_list = self._validator_records.exp_pulse_widths

        pulse_width = pulse_width_list[0]
        for control in self._experiment.measure_aio.Readout_control:
            sampling_delay = control.sampling_delay[0]
            sampling_width = control.sampling_width[0]
            measure_width = control.measure_width[0]

            raw_delay = self._readout_raw_delay_amp.get(control.channel, 50)
            measure_delay2 = round_up_to_50ns(self._offset + pulse_width)
            sampling_delay2 = measure_width - sampling_width + measure_delay2

            control.measure_delay = [raw_delay, measure_delay2]
            control.sampling_delay = [sampling_delay + raw_delay, sampling_delay2]
            control.sampling_width = [sampling_width, sampling_width]
            control.measure_width = [measure_width, measure_width]
            control.measure_index = [0, 0]
            control.sampling_count = 2

            if len(set(pulse_width_list)) > 1:
                measure_delay2_list = []
                sampling_delay2_list = []
                for pulse_width in pulse_width_list:
                    measure_delay2 = round_up_to_50ns(self._offset + pulse_width)
                    measure_delay2_list.append(measure_delay2)
                    sampling_delay2_list.append(measure_width - sampling_width + measure_delay2)
                self._experiment.sweep_control.append(
                    SweepControl(
                        channel=control.channel,
                        func="Readout_control:measure_delay:1",
                        points=measure_delay2_list,
                    )
                )
                self._experiment.sweep_control.append(
                    SweepControl(
                        channel=control.channel,
                        func="Readout_control:sampling_delay:1",
                        points=sampling_delay2_list,
                    )
                )


class PrepareMeasureWrapperV2:
    """
    兼容 F12 读取优化
    """
    def __init__(
        self,
        experiment: Experiment,
        offset: float = 300,
        readout_raw_delay_amp: CommonDict = None,
    ):
        self._experiment = experiment
        self._offset = offset
        self._readout_raw_delay_amp = readout_raw_delay_amp or {}

        self._validator_records = QDict(has_sweep_measure_delay=False, exp_pulse_widths=None)
        self._tail_f12_width = 0
        self._head_f12_width = 0
        self._head_pulse = QDict()
        self._z_offset_pulse = Constant(offset, 0)()

    def run(self):
        if self._validator():
            self._work()

    def _validator(self):
        """
        校验任务是否可以进行 2 次测量扩展

        校验逻辑:
        1. MeasureAIO 中的各读取模块，不存在 2 次测量任务格式，如 trigger_delay、sample_delay 列表长度必须为 1
        2. SweepControl 中不存在涉及 2 次测量的读取通道
        """
        readout_control = self._experiment.measure_aio.Readout_control
        sweep_control = self._experiment.sweep_control

        # check measure aio readout control
        for readout_control in readout_control:
            count = len(readout_control.sampling_delay)
            if count != 1:
                pyqlog.warning(f"Origin task protocol measure count is {count}, no support second measure wrapper!")
                return False

        # checkout sweep control
        delete_control = []
        for control in sweep_control:
            split_results = control.func.split(":")

            if split_results[1] == "measure_delay":
                self._validator_records.has_sweep_measure_delay = True

            if split_results[1] in ["measure_delay", "sampling_delay"]:
                delete_control.append(control)
                if split_results[-1] != "0":
                    pyqlog.warning(
                        f"Origin task protocol check sweep {control.func}, no support second measure wrapper!"
                    )
                    return False

        for control in delete_control:
            sweep_control.remove(control)

        return True

    def _work(self):
        # extract max readout width, 默认所有比特的读取波形长度均一致

        # auto set xy module measure delay (`TRIGGER DELAY`)
        # for control in self._experiment.measure_aio.XY_control:
        #     pre_measure_delay = control.measure_delay[0]
        #     control.measure_delay = [pre_measure_delay + measure_width + self._offset]

        # check f12 pi pulse
        self._check_f12_pi_pulse()
        self._build_f12_head_pulse()

        # auto wrap z flux readout point pulse
        for control in self._experiment.measure_aio.Z_flux_control:
            self._wrap_z_flux_pulse(control)
        for control in self._experiment.sweep_control:
            if control.func.startswith("Z_flux_control:waveform"):
                self._wrap_z_flux_pulse(control)

        # extract experiment width
        has_warp_readout_control = False
        for control in self._experiment.sweep_control:
            if control.func.startswith("XY_control:waveform"):
                self._wrap_xy_control(control)
                has_warp_readout_control = True
                break
        if has_warp_readout_control is False:
            for control in self._experiment.measure_aio.XY_control:
                self._wrap_xy_control(control)
                break

        # auto wrap readout control
        self._wrap_readout_control()

    def _check_f12_pi_pulse(self):
        for control in self._experiment.sweep_control:
            if control.func.startswith("XY_control:waveform"):
                pulse = control.waveform[0]
                self._tail_f12_width = pulse.pulse[pulse.fake_pulse_order[-1]].width
                return
        for control in self._experiment.measure_aio.XY_control:
            pulse = control.waveform[0]
            self._tail_f12_width = pulse.pulse[pulse.fake_pulse_order[-1]].width
            return

    def _build_f12_head_pulse(self):
        readout_control = self._experiment.measure_aio.Readout_control[0]
        measure_width = readout_control.measure_width[0]
        self._head_f12_width = round_up_to_50ns(self._tail_f12_width)
        tail_width = round(self._head_f12_width - self._tail_f12_width, 1)
        self._head_pulse.xy = Constant(tail_width + measure_width + self._offset, 0, "XY")()
        self._head_pulse.z = Constant(tail_width, 0)()

    def _wrap_z_flux_pulse(self, control):
        if control.waveform:
            for pulse in control.waveform:
                readout_point_pulse_order = pulse.fake_pulse_order[-1]
                f12_extend_pulse_order = pulse.fake_pulse_order[-3]
                pulse.pulse.append(self._z_offset_pulse)
                z_offset_order = len(pulse.pulse) - 1
                pulse.pulse.append(self._head_pulse.z)
                head_pulse_order = len(pulse.pulse) - 1
                
                extend_width = 0
                for idx, order in enumerate([f12_extend_pulse_order, head_pulse_order, readout_point_pulse_order, z_offset_order]):
                    pulse.fake_pulse_order.insert(idx, order)
                    extend_width += pulse.pulse[order].width

                pulse.width += extend_width

    def _wrap_xy_control(self, control):
        pulse_width_list = []
        for pulse in control.waveform:
            pulse_width_list.append(pulse.width)
            f12_extend_pulse_order = pulse.fake_pulse_order[-1]
            pulse.pulse.append(self._head_pulse.xy)
            head_pulse_order = len(pulse.pulse) - 1
            extend_width = 0
            for idx, order in enumerate([f12_extend_pulse_order, head_pulse_order]):
                pulse.fake_pulse_order.insert(idx, order)
                extend_width += pulse.pulse[order].width
                if pulse.virtual_z_phase:
                    phase = 0 if idx else pulse.virtual_z_phase[-1]
                    pulse.virtual_z_phase.insert(idx, phase)
            pulse.width += extend_width
        self._validator_records.exp_pulse_widths = pulse_width_list

    def _wrap_readout_control(self):
        pulse_width_list = self._validator_records.exp_pulse_widths

        pulse_width = pulse_width_list[0]
        for control in self._experiment.measure_aio.Readout_control:
            sampling_delay = control.sampling_delay[0]
            sampling_width = control.sampling_width[0]
            measure_width = control.measure_width[0]

            raw_delay = self._readout_raw_delay_amp.get(control.channel, 50)
            measure_delay2 = round_up_to_50ns(self._offset + pulse_width)
            sampling_delay2 = measure_width - sampling_width + measure_delay2

            control.measure_delay = [raw_delay + self._head_f12_width, measure_delay2]
            control.sampling_delay = [sampling_delay + raw_delay + self._head_f12_width, sampling_delay2]
            control.sampling_width = [sampling_width, sampling_width]
            control.measure_width = [measure_width, measure_width]
            control.measure_index = [0, 0]
            control.sampling_count = 2

            if len(set(pulse_width_list)) > 1:
                measure_delay2_list = []
                sampling_delay2_list = []
                for pulse_width in pulse_width_list:
                    measure_delay2 = round_up_to_50ns(self._offset + pulse_width)
                    measure_delay2_list.append(measure_delay2)
                    sampling_delay2_list.append(measure_width - sampling_width + measure_delay2)
                self._experiment.sweep_control.append(
                    SweepControl(
                        channel=control.channel,
                        func="Readout_control:measure_delay:1",
                        points=measure_delay2_list,
                    )
                )
                self._experiment.sweep_control.append(
                    SweepControl(
                        channel=control.channel,
                        func="Readout_control:sampling_delay:1",
                        points=sampling_delay2_list,
                    )
                )


def round_up_to_50ns(nanosecond):
    """
    将给定的纳秒数向上取整到最接近的50ns的整数倍。

    参数:
    nanosecond (int or float): 以纳秒为单位的时间值。

    返回:
    int: 向上取整到50ns整数倍后的纳秒数。
    """
    # 向上取整到50ns的倍数
    rounded_nanosecond = math.ceil(nanosecond / 50) * 50
    return rounded_nanosecond
