# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/02
# __author:       <PERSON><PERSON><PERSON>
# __corporation:  OriginQuantum

from .amp_optimize import AmpOptimize, CouplerAmpOptimize
from .ape_once import APE, CouplerAPE
from .cpmg import CPMGExperiment
from .f12_rabi import RabiScanAmpF12, RabiScanWidthF12
from .f12_ramsey import RamseyF12
from .f12_spectrum import QubitSpectrumF12
from .f12_spectrum_2d import QubitSpectrumF12_2D
from .floquet_cali_single_once import FloquetCalibrationSingleOnce
from .floquet_xyz_timing_once import FloquetXYZTimingOnce
from .qubit_spectrum import CouplerSpectrum, NewCouplerSpectrum, QubitSpectrum
from .rabi import (
    CouplerRabiScanAmp,
    CouplerRabiScanWidth,
    CouplerRabiScanWidthDetune,
    RabiScanAmp,
    RabiScanWidth,
    RabiScanWidthAmp,
    RabiScanWidthDetune,
    XYCrossRabiWidthOnce,
)
from .ramsey import (
    CouplerRamsey,
    CouplerRamseyByZZShift,
    CouplerRamseyCrosstalk,
    Ramsey,
    RamseyCrosstalk,
    RamseyExtend,
    RamseyZZ,
)
from .spin_echo import SpinEcho, SpinEchoZZ
from .xyz_timing import CouplerXYZTiming, CouplerXYZTimingByZZShift, XYZTiming
