# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/06/11
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy

import numpy as np

from ....analysis.library.cz_detune_analysis import (
    SweepDetuneComAnalysis,
    SweepQCAndDetuneAnalysis,
)
from ....analysis.specification import CurveAnalysisData
from ....parameters import options_wrapper
from ....structures import MetaData, Options
from ....tools import cz_flow_options_adapter, judge_exp_failed
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import CPhaseTMSE, SweepDetune


@options_wrapper
class SweepDetuneComPhase(CompositeExperiment):
    _sub_experiment_class = CPhaseTMSE

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("detune_list", list)
        options.set_validator("scan_name", str)
        options.set_validator("detune1_scope", float)
        options.set_validator("detune1_points", int)
        options.set_validator("detune2_scope", float)
        options.set_validator("detune2_points", int)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])
        options.detune_list = None
        options.scan_name = None
        options.detune1_scope = 30
        options.detune1_points = 31
        options.detune2_scope = 30
        options.detune2_points = 31
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.x_label = None
        options.y_label = None
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.detune1_freq_list = []
        options.detune2_freq_list = []
        return options

    def _check_options(self):
        super()._check_options()
        cz_flow_options_adapter(self)
        eop = self.experiment_options

        if not eop.detune_list and not eop.detune_freq_list:
            detune1 = self.qubit_pair.cz_value(eop.scan_name, "freq", "cz")
            scope1 = eop.detune1_scope
            points1 = eop.detune1_points
            detune_freq_list = list(
                np.linspace(detune1 - scope1, detune1 + scope1, points1)
            )
            eop.detune_freq_list = detune_freq_list
            self.run_options.detune1_freq_list = detune_freq_list

            scope2 = eop.detune2_scope
            points2 = eop.detune2_points
            detune2_freq_list = list(
                np.linspace(detune1 - scope2, detune1 + scope2, points2)
            )
            self.run_options.detune2_freq_list = detune2_freq_list

        self.set_run_options(
            x_data=eop.detune_list, analysis_class=SweepQCAndDetuneAnalysis
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "scan_name": self.experiment_options.scan_name,
        }
        metadata.process_meta = {
            "x_scan": self.experiment_options.scan_name,
            "y_scan": self.experiment_options.child_exp_options.scan_name,
            "x_data": self.experiment_options.get("detune_list"),
            "y_data": self._experiments[
                0
            ].analysis.experiment_data.metadata.process_meta.get("x_data"),
            "child_scan_freq": self._experiments[0].experiment_options.get(
                "detune_freq_list"
            )
            or self._experiments[0].analysis.experiment_data.metadata.process_meta.get(
                "x_data"
            ),
            "scan_freq": self.experiment_options.detune_freq_list,
        }
        return metadata

    async def _sync_composite_run(self):
        detune_list = self.experiment_options.detune_list
        detune2_freq_list = self.run_options.detune2_freq_list
        self.set_analysis_options(x_label="Detune1 Z Amp", y_label="Detune2 Z Amp")
        data_key = self.child_experiment.analysis_options.data_key
        scan_name = self.experiment_options.scan_name
        delta_phase_arr = None
        self.child_experiment.set_experiment_options(analysis_mode="detune_phase")

        for idx1, detune1 in enumerate(detune_list):
            # exp = None
            phases = []
            # for idx2, detune2 in enumerate(detune2_list):
            exp = deepcopy(self.child_experiment)
            exp.qubit_pair.set_cz_value(scan_name, "detune1", detune1)
            # exp.qubit_pair.set_cz_value(scan_name, "detune2", detune2)
            exp.set_parent_file(self, f"detune-{detune1}v", idx1, len(detune_list))
            exp.set_experiment_options(mode="SE-TM")
            if detune2_freq_list:
                exp.set_experiment_options(detune_freq_list=detune2_freq_list)
            self._check_simulator_data(exp, idx1)
            await exp.run_experiment()
            delta_phase = exp.analysis.analysis_datas.get(data_key[0]).y
            # phases.append(exp.analysis.results.phase.value)
            phases.append(delta_phase)

            exp.experiment_data._x_data = np.array(detune_list)
            exp.experiment_data._y_data = {"phase": np.array(phases)}
            exp.analysis._analysis_data_dict = CurveAnalysisData(
                x=np.array(detune_list), y=np.array(phases)
            )
            exp.analysis.provide_for_parent.update(
                {"ac_scan": exp.analysis.results.ac_scan.value}
            )
            self._experiments.append(exp)
            if delta_phase_arr is None:
                delta_phase_arr = np.array(phases)
            else:
                delta_phase_arr = np.vstack((delta_phase_arr, np.array(phases)))

            if not judge_exp_failed(exp.analysis.quality):
                exp.analysis.provide_for_parent.update({"is_pass": 1})
            else:
                exp.analysis.provide_for_parent.update({"is_pass": 0})

            #     detune1 = self.experiment_options.detune_freq_list[idx1]
            #     detune2 = exp.analysis.results.ac_scan.value
            #     pyqlog.info(f"Quality normal, record freq | detune1({detune1}) | detune2({detune2})")
            #     self.run_options.detune1_freq_list.append(detune1)
            #     self.run_options.detune2_freq_list.append(detune2)

        self.file.save_data(
            self.run_options.detune1_freq_list,
            self.run_options.detune2_freq_list,
            name=self._label,
        )
        self.file.save_data(delta_phase_arr, name="Phase_data")

        self._run_analysis(x_data=detune_list, analysis_class=SweepQCAndDetuneAnalysis)
        self._set_result_path()

    def _set_result_path(self):
        self.file.save_data(
            self.run_options.detune1_freq_list,
            self.run_options.detune2_freq_list,
            name=self._label,
        )

        for k, result in self.analysis.results.items():
            result.extra.update(
                {
                    "path": f"QubitPair.metadata.std.process.max_phase_point.{k}",
                    "name": self.qubit_pair.name,
                }
            )


@options_wrapper
class SweepDetuneCom(CompositeExperiment):
    _sub_experiment_class = SweepDetune

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("detune_list", list)
        options.set_validator("scan_name", str)
        options.set_validator("scope", dict)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.detune_list = None
        options.scan_name = "qh"
        options.scope = {"l": 30, "r": 30, "p": 31}

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.x_label = None
        options.y_label = None
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        return options

    def _check_options(self):
        super()._check_options()
        cz_flow_options_adapter(self)

        eop = self.experiment_options
        if not eop.detune_list and not eop.detune_freq_list:
            l, r, p = eop.scope.get("l"), eop.scope.get("r"), eop.scope.get("p")
            detune = self.qubit_pair.cz_value(eop.scan_name, "freq", "cz")
            sweeps = list(np.linspace(detune - l, detune + r, p))
            eop.detune_freq_list = sweeps

        self.set_run_options(
            x_data=self.experiment_options.detune_list,
            analysis_class=SweepDetuneComAnalysis,
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "scan_name": self.experiment_options.scan_name,
        }
        metadata.process_meta = {
            "x_scan": self.experiment_options.scan_name,
            "y_scan": self.experiment_options.child_exp_options.scan_name,
            "x_data": self.experiment_options.get("detune_list"),
            "y_data": self._experiments[
                0
            ].analysis.experiment_data.metadata.process_meta.get("x_data"),
            "child_scan_freq": self._experiments[0].experiment_options.get(
                "detune_freq_list"
            ),
            "scan_freq": self.experiment_options.detune_freq_list,
        }
        return metadata

    async def _sync_composite_run(self):
        detune_list = self.experiment_options.detune_list
        scan_name = self.experiment_options.child_exp_options.scan_name
        label = self.experiment_options.child_exp_options.label
        scan_bit = self.qubit_pair.qh if scan_name == "qh" else self.qubit_pair.ql
        detune_name = "detune2" if label == "detune1" else "detune1"
        self.set_analysis_options(x_label=detune_name, y_label=label)

        for index, detune in enumerate(detune_list):
            exp = deepcopy(self.child_experiment)
            exp.experiment_options.detune_freq_list = (
                self.experiment_options.detune_freq_list
            )
            exp.qubit_pair.set_cz_value(scan_bit, detune_name, detune)
            exp.set_parent_file(self, f"detune-{detune}v", index, len(detune_list))
            self._check_simulator_data(exp, index)
            await exp.run_experiment()
            self._experiments.append(exp)

        self._run_analysis(x_data=detune_list, analysis_class=SweepDetuneComAnalysis)

    # def _setup_child_experiment(
    #     self, child_exp: SweepDetune, index: int, detune: float
    # ):
    #     child_exp.run_options.index = index
    #     total = len(self.run_options.x_data)
    #     describe = f"detune-{detune}v"
    #     scan_name = self.experiment_options.scan_name
    #     label = self.experiment_options.child_exp_options.label
    #     detune_name = "detune2" if label == "detune1" else "detune1"
    #     child_exp.qubit_pair.set_cz_value(scan_name, detune_name, detune)
    #     child_exp.set_parent_file(self, describe, index, total)
    #     child_exp.set_experiment_options(
    #         detune_freq_list=self.experiment_options.detune_freq_list,
    #     )
    #     self._check_simulator_data(child_exp, index)
