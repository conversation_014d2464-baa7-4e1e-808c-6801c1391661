# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/25
# __author:       SS Fang

import json
import re
import time
from typing import TYPE_CHECKING, Dict

import zmq
from loguru import logger

from pyQCat.executor.structures import WorkingType, DivideType
from pyqcat_visage.gui.online_update_ui import Ui_MainWindow
from pyqcat_visage.gui.widgets.title_window import TitleWindow
from pyqcat_visage.protocol import PROXY_PUB_PORT

if TYPE_CHECKING:
    from pyqcat_visage.gui.main_window import VisageGUI


class OnlineUpdateWindow(TitleWindow):
    def __init__(self, gui: "VisageGUI", parent=None):
        super().__init__(parent)
        self._ui = Ui_MainWindow()
        self._ui.setupUi(self)
        self.gui = gui

        self._username = ""
        self._topic = ""
        self._operation = "online_update"
        self._point_label = ""
        self._point_labels = []
        self._env_bits = []
        self._bits = []
        self._couplers = []
        self._pairs = []

        self._first_set = True
        self._working_types = [val.value for val in WorkingType.__members__.values()]
        self._divide_types = [val.value for val in DivideType.__members__.values()]

        self._context = zmq.Context.instance()
        self._pub_url = ""
        self._pub_client = None

    @property
    def ui(self):
        return self._ui

    def _create_pub_sock(self, pub_url: str):
        sock: zmq.Socket = self._context.socket(zmq.PUB)
        sock.connect(pub_url)
        time.sleep(0.005)
        logger.info(f"Create publish {pub_url} sock complete.")
        self._pub_url = pub_url
        self._pub_client = sock

    def _pub_message(self, topic: str, operation: str, message: Dict):
        topic_bytes = topic.encode(encoding="utf-8")
        operation_bytes = operation.encode(encoding="utf-8")
        body_bytes = json.dumps(message).encode(encoding="utf-8")

        msg_parts = [topic_bytes, operation_bytes, body_bytes]
        self._pub_client.send_multipart(msg_parts)
        logger.info(
            f"Publish {self._pub_url} topic {topic} operation {operation}, message: {message}"
        )

    def _initial(self):
        sample = self.gui.backend.config.system.sample
        env_name = self.gui.backend.config.system.env_name
        point_label = self.gui.backend.config.system.point_label
        invoker_addr = self.gui.backend.config.system.invoker_addr
        username = self.gui.backend.username
        chip_data = self.gui.backend.context_builder.chip_data

        addr_list = invoker_addr.rsplit(":", 1)
        # pub_port = int(addr_list[1]) + 2
        pub_url = f"{addr_list[0].replace('http', 'tcp')}:{PROXY_PUB_PORT}"
        if pub_url != self._pub_url or self._pub_client is None:
            self._create_pub_sock(pub_url)

        self._bits = list(chip_data.cache_qubit.keys())
        self._couplers = list(chip_data.cache_coupler.keys())
        self._pairs = list(chip_data.cache_qubit_pair.keys())
        self._env_bits.clear()
        self._env_bits.extend(self._bits)
        self._env_bits.extend(self._couplers)

        self._username = username
        self._topic = f"{sample}_|_{env_name}"
        self._point_label = point_label

        point_res = self.gui.backend.db.query_point_label_list(
            username, sample, env_name
        )
        # logger.info(f"Query point result: {point_res}")
        if point_res.get("code") == 200:
            p_labels = point_res.get("data", [])
            self._point_labels.clear()
            self._point_labels.extend(p_labels)

    def load_all_data(self):
        self._initial()

        self.ui.PointLabelBox.clear()
        self.ui.PointLabelBox.addItems(self._point_labels)
        self.ui.PointLabelBox.setCurrentText(self._point_label)
        self.ui.EnvBox.set_units(self._env_bits)
        self.ui.MaxUnitsBox.set_units(self._env_bits)
        self.ui.OptBox.set_units(self._bits)
        self.ui.OnlineBitlBox.set_units(self._bits)
        self.ui.OnlinePairBox.set_units(self._pairs)

        if self._first_set is True:
            self.ui.WorkingTypeBox.addItems(self._working_types)
            self.ui.WorkingTypeBox.setCurrentText(WorkingType.ac_bias.value)
            self.ui.DivideTypeBox.addItems(self._divide_types)
            self.ui.DivideTypeBox.setCurrentText(DivideType.CHA.value)
            self._first_set = False

    def update_online_params(self):
        point_label = self.ui.PointLabelBox.currentText()
        if not self.ask_ok(
            f"Are you sure to <strong style='color:red'>Update</strong> "
            f"the point <strong style='color:red'>{point_label}</strong> online parameters?",
            "OnlineUpdate Message",
        ):
            return

        all_env_bits = self.ui.EnvBox.currentText()
        max_point_units = self.ui.MaxUnitsBox.currentText()
        f12_opt_bits = self.ui.OptBox.currentText()
        online_bits = self.ui.OnlineBitlBox.currentText()
        online_pairs = self.ui.OnlinePairBox.currentText()
        working_type = self.ui.WorkingTypeBox.currentText()
        divide_type = self.ui.DivideTypeBox.currentText()

        online_flag = self.ui.OnlineCheck.isChecked()
        ac_crosstalk_flag = self.ui.CrosstalkCheck.isChecked()
        xy_crosstalk_flag = self.ui.XYCrosstalkCheck.isChecked()

        env_bits = []
        env_couplers = []
        for qc_name in all_env_bits:
            if qc_name.startswith("q"):
                env_bits.append(qc_name)
            elif qc_name.startswith("c"):
                env_couplers.append(qc_name)

        # check
        err_info = ""
        for qc_name in max_point_units:
            if qc_name not in all_env_bits:
                err_info += f"max_point {qc_name} not in env_bits\n"
        for q_name in f12_opt_bits:
            if q_name not in env_bits:
                err_info += f"f12_opt {q_name} not in env_bits\n"
        for q_name in online_bits:
            if q_name not in env_bits:
                err_info += f"online_bit {q_name} not in env_bits\n"

        online_couplers = []
        pattern = re.compile(r"(\d+)")
        for qp_name in online_pairs:
            res_list = pattern.findall(qp_name)
            c_name = f"c{res_list[0]}-{res_list[1]}"
            online_couplers.append(c_name)

            if c_name not in env_couplers:
                err_info += f"online_pair {qp_name} but {c_name} not in env_bits\n"
            for q_name in [f"q{res_list[0]}", f"q{res_list[1]}"]:
                if q_name not in online_bits:
                    err_info += f"online_pair {qp_name} but {q_name} not in online_bits\n"

        online_params = {
            "User": self._username,
            "DateTime": time.strftime("%Y-%m-%d %H:%M:%S"),
            "PointLabel": point_label,
            "Parameters": {
                "env_bits": env_bits,
                "env_couplers": env_couplers,
                "max_point_units": max_point_units,
                "f12_opt_bits": f12_opt_bits,
                "online_bits": online_bits,
                "online_couplers": online_couplers,
                "working_type": working_type,
                "divide_type": divide_type,
                "online": online_flag,
                "crosstalk": ac_crosstalk_flag,
                "xy_crosstalk": xy_crosstalk_flag,
            },
        }
        if err_info:
            logger.error(f"UpdateOnline parameters set error: \n{err_info}")
        else:
            self._pub_message(self._topic, self._operation, online_params)
