# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/01/14
# __author:       <PERSON><PERSON><PERSON>

import copy
import json
from pathlib import Path

from loguru import logger

from pyQCat.executor.batch.tools import divide_cz_parallel_group
from pyQCat.experiments.batch_experiment import BatchExperiment
from pyQCat.structures import QDict
from pyQCat.tools.find import ResonanceFrequency
from pyQCat.tools.utilities import get_bound_ac_spectrum, qarange


class BatchPuritySpectrum(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.set_validator("freq_map", dict)
        options.freq_map = {}
        options.shot_flows = ["SingleShot"]
        options.flows = ["Swap", "PurityXEBMultiple"]  # customer set
        options.step = 20
        options.use_resonance = False
        options.threshold = 0  # 共振点距离各比特当前 drive_freq 的距离下限
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.freq_map = {}
        options.xeb_records = {}
        options.current_point_map = {}
        options.auto_divide = ResonanceFrequency()
        return options

    def _batch_up(self):
        super()._batch_up()
        outer_freq_map = self.experiment_options.freq_map
        actual_freq_map = {}
        for unit in self.experiment_options.physical_units:
            pair_struct = self.context_manager.chip_data.get_std_pair_struct(unit)

            if self.experiment_options.use_resonance is True:
                freq_list = self.run_options.auto_divide.get_intersect_freq11(
                    pair_struct.pair.name,
                    self.context_manager.chip_data,
                    step=self.experiment_options.step,
                    threshold=self.experiment_options.threshold,
                )
                down_freq, up_freq = (min(freq_list), max(freq_list))
                freq_list = qarange(
                    up_freq, down_freq, -abs(self.experiment_options.step)
                )
            else:
                ql_freq_min, ql_freq_max = get_bound_ac_spectrum(pair_struct.ql)
                qh_freq_min, qh_freq_max = get_bound_ac_spectrum(pair_struct.qh)
                down_freq, up_freq = (
                    max(ql_freq_min, qh_freq_min + pair_struct.qh.anharmonicity),
                    min(ql_freq_max, qh_freq_max + pair_struct.qh.anharmonicity),
                )
                if down_freq >= up_freq:
                    raise ValueError(
                        f"{unit} freq gao calculate error | {down_freq, up_freq}"
                    )

                freq_list = qarange(down_freq, up_freq, self.experiment_options.step)

            logger.log("EXP", f"{unit}, {down_freq}, {up_freq}")

            if unit in outer_freq_map:
                freq_list = [
                    v for v in outer_freq_map[unit] if down_freq <= v <= up_freq
                ]
                if not freq_list:
                    raise ValueError(f"{unit} freq gao calculate error | {freq_list}")

            actual_freq_map[unit] = freq_list

            point_map = {}
            for freq in freq_list:
                point_map.update(
                    {
                        str(freq): {
                            "is_pass": False,
                            "xeb_fidelity": None,
                            "params": None,
                            "error_reason": None,
                            "error_exp": None,
                        }
                    }
                )
            self.run_options.xeb_records[unit] = QDict(**point_map)

        self.run_options.freq_map = actual_freq_map

    def _run_batch(self):
        max_point = max([len(v) for v in self.run_options.freq_map.values()])
        for loop in range(max_point):
            pair_name_list = []

            for unit, freq_list in self.run_options.freq_map.items():
                if loop >= len(freq_list):
                    continue
                else:
                    pair_name_list.append(unit)
                    freq = freq_list[loop]

                    pair_struct = self.context_manager.chip_data.get_std_pair_struct(
                        unit
                    )
                    pair_struct.pair.metadata.std.cz.params[pair_struct.ql_name][
                        "freq"
                    ] = freq
                    pair_struct.pair.metadata.std.cz.params[pair_struct.qh_name][
                        "freq"
                    ] = freq - pair_struct.qh.anharmonicity
                    self.run_options.current_point_map[unit] = str(freq)
                    self.run_options.dir_describe[unit] = f"sweep-{freq}"
                    logger.log(
                        "EXP",
                        f"{unit} | ql freq ({freq}) | qh freq ({freq - pair_struct.qh.anharmonicity}) ",
                    )

            # divide parallel group and IF divide
            group_list = divide_cz_parallel_group(
                pair_name_list,
                self.context_manager.chip_data,
                # **self.backend.system.parallel_divide,
            )

            for idx, group in enumerate(group_list):
                qubit_list = []
                for pair_name in group_list[group]:
                    pair = self.context_manager.chip_data.cache_qubit_pair.get(
                        pair_name
                    )
                    qubit_list.extend([pair.qh, pair.ql])
                process_bar = f"Group-{idx} ({loop + 1}/{max_point})"
                self._run_flow(
                    flows=self.experiment_options.shot_flows,
                    physical_units=qubit_list,
                    name=f"Refresh Dcm {process_bar}",
                )
                pass_unit = self._run_flow(
                    flows=self.experiment_options.flows,
                    physical_units=group_list[group],
                    name=f"CZ Flow {process_bar}",
                )
                logger.info(f"Suc {pass_unit}")
                self._record_point_state()

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)
        if "PurityXEBMultiple" in exp.label and record:
            for unit in record.analysis_data.keys():
                cur_point = self.run_options.current_point_map[unit]
                point_sate = self.run_options.xeb_records.get(unit).get(cur_point)
                if point_sate and unit in record.pass_units:
                    point_sate.is_pass = True
                    point_sate.xeb_fidelity = (
                        record.analysis_data.get(unit).get("result").get("fidelity")
                    )
                else:
                    point_sate.error_reason = record.fail_reason
                    point_sate.error_exp = exp.label
                point_sate.params = copy.deepcopy(
                    self.context_manager.chip_data.cache_qubit_pair.get(unit).to_dict()
                )
        return record

    def _record_point_state(self):
        record_path = str(
            Path(Path(self.run_options.record_path).parent, "xeb_records_.json")
        )
        with open(record_path, mode="w", encoding="utf-8") as fp:
            json.dump(self.run_options.xeb_records, fp, indent=4, ensure_ascii=False)
