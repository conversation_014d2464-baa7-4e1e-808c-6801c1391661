# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/23
# __author:       ssfang

"""
Distortion T1 Analysis.

"""

from typing import List, Union

import numpy as np
from scipy.interpolate import InterpolatedUnivariateSpline
from scipy.signal import find_peaks, savgol_filter

from ..algorithms.distortion import adjust_noise_points
from ..curve_analysis import CurveAnalysis
from ..fit.fit_models import (
    lorentzian,
    bi_lorentz_tilt,
    gauss_lorentzian,
    skewed_lorentzian,
    skewed_gauss_lorentz,
)
from ..specification import CurveAnalysisData, FitModel, FitOptions, ParameterRepr
from ...log import pyqlog
from ...structures import Options, QDict


class DistortionT1Analysis(CurveAnalysis):
    """DistortionT1Once Analysis class."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""

        options = super()._default_options()

        options.fit_model_name = "lorentzian"
        options.fit_model = FitModel(fit_func=lorentzian)

        options.cali_offset_method = "direct"
        options.p0_history = None
        options.cut_index = True
        options.adjust_noise = True
        options.n_multiple = 4.0

        options.width = 0.0
        options.x_data = np.array([])
        options.y_data = {}

        options.filter = {"window_length": 5, "polyorder": 3}
        options.quality_bounds = [0.95, 0.85, 0.75]

        options.x_label = "Z offset [V]"
        options.result_parameters = [
            ParameterRepr(name="t_offset", repr="TargetOffset", unit="V"),
            ParameterRepr(name="width", repr="FWHM", unit="V"),
        ]
        return options

    def _pre_operation(self):
        """Do some special operation."""
        fit_model_name = self.options.fit_model_name
        adjust_noise = self.options.adjust_noise
        n_multiple = self.options.n_multiple

        # According to fit_model_name adjust fit_model.
        if fit_model_name == "lorentzian":
            fit_model = FitModel(fit_func=lorentzian)
            param_repr = ParameterRepr(name="f0", repr="FitParams", unit="V")
        elif fit_model_name == "bi_lorentz_tilt":
            fit_model = FitModel(fit_func=bi_lorentz_tilt)
            param_repr = ParameterRepr(name="b", repr="FitParams", unit="V")
        elif fit_model_name == "gauss_lorentzian":
            fit_model = FitModel(fit_func=gauss_lorentzian)
            param_repr = ParameterRepr(name="b", repr="FitParams", unit="V")
        elif fit_model_name == "skewed_lorentzian":
            fit_model = FitModel(fit_func=skewed_lorentzian)
            param_repr = ParameterRepr(name="fr", repr="FitParams", unit="V")
        elif fit_model_name == "skewed_gauss_lorentz":
            fit_model = FitModel(fit_func=skewed_gauss_lorentz)
            param_repr = ParameterRepr(name="b", repr="FitParams", unit="V")
        else:
            raise ValueError(f"Set fit_model_name {fit_model_name} is not supported.")

        # Get fit original x, y data.
        x_data = self.experiment_data.x_data
        y_data = self.experiment_data.y_data
        op_key = list(self.options.get("data_key", None) or y_data.keys())[0]
        x_arr = np.asarray(x_data)
        y_arr = np.asarray(y_data.get(op_key))

        # Adjust noise points
        if adjust_noise is True:
            *_, y_arr = adjust_noise_points(y_arr, n_multiple=n_multiple)

        # Only fit max height peak part.
        x_step = round(abs(x_arr[1] - x_arr[0]), 6)
        y_max = np.max(y_arr)
        y_mean = np.mean(y_arr)
        height = (y_max - y_mean) / 5 + y_mean

        s_y_arr = savgol_filter(y_arr, **self.data_filter)
        peaks, properties = find_peaks(s_y_arr, height=height, width=x_step / 2)

        if peaks.size > 0:
            peak_value_list = properties.get("peak_heights")
            peak_width_list = properties.get("widths")
            peak_start_list = properties.get("left_bases")
            peak_end_list = properties.get("right_bases")

            max_idx = np.argmax(peak_value_list)
            start_idx = peak_start_list[max_idx]
            end_idx = peak_end_list[max_idx]

            if self.options.cut_index is False:
                start_idx = 0
                end_idx = len(y_arr)
            else:
                if end_idx - start_idx < 10:
                    start_idx = 0
                    end_idx = len(y_arr)

            width = round(peak_width_list[max_idx] * x_step, 6)
        else:
            pyqlog.warning("Not find peaks, reset fit_model is None!")
            start_idx = 0
            end_idx = len(y_arr)
            width = 0
            fit_model = None

        new_x_arr = x_arr[start_idx:end_idx]
        new_y_data = {}
        for key, value in y_data.items():
            if key == op_key:
                new_y_arr = y_arr[start_idx:end_idx]
            else:
                new_y_arr = np.asarray(value)[start_idx:end_idx]
            new_y_data.update({key: new_y_arr})

        self.options.result_parameters.append(param_repr)
        self.set_options(
            width=width, x_data=new_x_arr, y_data=new_y_data, fit_model=fit_model
        )

    def _create_analysis_data(self) -> QDict:
        """Create Analysis data provided for detailed analyze.

        Returns:
            A QDict object, key represents data type and value is
            CurveAnalysisData object.
        """
        analysis_data_dict = QDict()
        x_data = self.options.x_data
        y_data = self.options.y_data

        data_key = list(self.options.get("data_key", None) or y_data.keys())
        for key in data_key:
            if key in y_data:
                analysis_data = CurveAnalysisData(
                    x=np.copy(x_data),
                    y=np.copy(y_data.get(key)),
                )
                analysis_data_dict[key] = analysis_data
        return analysis_data_dict

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Guess initial fit parameters.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        x = data.x
        y = data.y

        x_step = round(abs(x[1] - x[0]), 6)

        y_mean = np.mean(y)
        y_max = np.max(y)
        height = (y_max - y_mean) / 5 + y_mean

        peaks, properties = find_peaks(y, height=height, width=x_step / 2)

        if peaks.size != 0:
            peak_value_list = properties.get("peak_heights")
            peak_width_list = properties.get("widths")
            max_index = np.argmax(peak_value_list)
        else:
            peaks = [np.argmax(y)]
            peak_width_list = [0.5]
            peak_value_list = [0.5]
            max_index = 0

        a = y[peaks[max_index]] - np.mean(y)
        offset = np.mean(y)
        b = x[peaks[max_index]]

        c = peak_width_list[max_index] * abs(x_step) / 2
        coe = 0.01

        e = 0.5
        # coe1, coe2 = 0, 0
        a1 = peak_value_list[max_index]
        a2 = np.min(y)

        Ql = abs(b / c)

        # Set the initial values for each fitted model specifically.
        fit_opt_list = []
        fit_model_name = self.options.fit_model_name
        if fit_model_name == "lorentzian":
            fit_opt.p0.set_if_empty(A=0.01, offset=y_mean, f0=b, kappa=0.01)
            fit_opt_list = [fit_opt]
        elif fit_model_name == "bi_lorentz_tilt":
            fit_opt.p0.set_if_empty(offset=offset, b=b, cl=c, cr=c, coe=coe)
            for i in range(1, 10):
                opt = fit_opt.copy()
                opt.p0.set_if_empty(a=a * i * 0.1)
                fit_opt_list.append(opt)
        elif fit_model_name == "gauss_lorentzian":
            fit_opt.p0.set_if_empty(offset=offset, a=a, b=b, c=c, e=e)
            fit_opt_list = [fit_opt]
        elif fit_model_name == "skewed_lorentzian":
            fit_opt.p0.set_if_empty(A1=a2, A2=0, A3=a1, A4=0, fr=b, Ql=Ql)
            fit_opt_list = [fit_opt]
        elif fit_model_name == "skewed_gauss_lorentz":
            fit_opt.p0.set_if_empty(b=b, c=c, e=e, coe1=coe, a1=a1, coe2=coe, a2=a2)
            fit_opt_list = [fit_opt]

        if self.options.p0_history is not None:
            new_fit_opt = FitOptions(
                parameters=self.options.fit_model.signature,
                default_p0=self.options.p0_history,
                default_bounds=self.options.bounds,
                **self.options.curve_fit_extra,
            )
            fit_opt_list.append(new_fit_opt)

        return fit_opt_list

    def _extract_result(self, data_key: str):
        """Extract the Analysis results from fitted data.

        Args:
            data_key (str): The basis for selecting data.

        """
        width = self.options.width
        fit_model = self.options.fit_model
        fit_model_name = self.options.fit_model_name
        cali_offset_method = self.options.cali_offset_method

        analysis_data = self.analysis_datas[data_key]
        x = analysis_data.x
        y = analysis_data.y

        self.results.width.value = width
        if fit_model is None:
            self.results.t_offset.value = x[np.argmax(y)]
            return

        super()._extract_result(data_key)

        popt = analysis_data.fit_data.popt
        popt_keys = analysis_data.fit_data.popt_keys

        pyqlog.debug(
            f"fit_model_name: {fit_model_name}, "
            f"fit params: {list(zip(popt_keys, popt))}"
        )

        b_idx = 0
        if fit_model_name == "lorentzian":
            b_idx = popt_keys.index("f0")
        elif fit_model_name == "skewed_lorentzian":
            b_idx = popt_keys.index("fr")
        elif fit_model_name in [
            "bi_lorentz_tilt",
            "gauss_lorentzian",
            "skewed_gauss_lorentz",
        ]:
            b_idx = popt_keys.index("b")

        target_offset = None
        if fit_model_name == "lorentzian":
            target_offset = popt[b_idx]
        elif fit_model_name == "gauss_lorentzian":
            target_offset = popt[b_idx]
        elif fit_model_name in [
            "bi_lorentz_tilt",
            "skewed_lorentzian",
            "skewed_gauss_lorentz",
        ]:
            fit_func = self.options.fit_model.fit_func
            interp_x_arr = np.arange(x[0], x[-1] + 1e-5, 1e-5)
            interp_fit_y_arr = fit_func(interp_x_arr, *popt)

            if cali_offset_method == "direct":
                target_offset = interp_x_arr[np.argmax(interp_fit_y_arr)]
            else:
                fit_interp = InterpolatedUnivariateSpline(
                    interp_x_arr, interp_fit_y_arr, k=4
                )
                roots = fit_interp.derivative().roots()
                distance = np.abs(roots - popt[b_idx])
                target_offset = roots[np.argmin(distance)]

        self.results.t_offset.value = target_offset

    def run_analysis(self):
        """Run analysis on experiment data."""
        self._pre_operation()
        super().run_analysis()
