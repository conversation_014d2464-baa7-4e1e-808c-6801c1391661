# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/14
# __author:       <PERSON> Fang

"""
Create Base SingleShot Composite Optimize One Field of Qubit.

"""

from collections import defaultdict

import numpy as np

from ....errors import ExperimentOptionsError
from ....structures import MetaData, Options
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import SingleShot


class SingleShotComposite(CompositeExperiment):
    """Optimize One Field of Qubit."""

    _sub_experiment_class = SingleShot

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            optimize_field (str): Give the target optimize field.
            sweep_list (List, np.ndarray): Scan optimize field list.

        """
        options = super()._default_experiment_options()

        options.set_validator("sweep_list", list)

        options.optimize_field = ""
        options.sweep_list = []

        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for optimize experiment.

        Options:
            fidelity_threshold (List): Threshold of F0, F1.
            outlier (float): Set outlier threshold value.
            distance_flag (bool): True means use center-distance
                                  to optimize target field.

        """
        options = super()._default_analysis_options()
        options.data_key = ["Pacify"]
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()

        options.sweep_list = []
        options.k_recommend_list = []
        options.F_dict = defaultdict(list)
        options.outlier_list = []
        options.center_dist_list = []
        options.child_quality_list = []
        options.link_child_exp = True

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {"quality": self.run_options.child_quality_list}
        return metadata

    def _check_options(self):
        """Check options."""
        super()._check_options()
        sweep_list = self.experiment_options.sweep_list
        if self.coupler and self.child_experiment.is_coupler_exp is False:
            result_name = self.child_experiment.coupler.name
        else:
            result_name = self.child_experiment.qubit.name
        self.set_analysis_options(result_name=result_name)
        self.set_run_options(x_data=sweep_list)

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        coupler_probe_flag = False
        if self.coupler and self.child_experiment.is_coupler_exp is False:
            coupler_probe_flag = True
        for key, result in self.analysis.results.items():
            if key == "optimize_field":
                optimize_field = self.experiment_options.optimize_field
                if coupler_probe_flag is True:
                    result.extra["path"] = f"Coupler.{optimize_field}"
                else:
                    result.extra["path"] = f"Qubit.{optimize_field}"

    def _alone_save_result(self):
        """Save some result data."""
        q_name = self.child_experiment.qubit.name
        optimize_field = self.experiment_options.optimize_field

        f_list = []
        for _, f_ in self.run_options.F_dict.items():
            f_list.append(np.array(f_))

        self.file.save_data(
            np.array(self.run_options.sweep_list),
            np.array(self.run_options.k_recommend_list),
            *f_list,
            np.array(self.run_options.outlier_list),
            np.array(self.run_options.center_dist_list),
            name=f"{self}({q_name}_{optimize_field}_opt_run_options)",
        )

    def _setup_child_experiment(self, exp: "SingleShot", index: int, value: float):
        """Set child_experiment some options."""
        single_shot_exp = exp
        single_shot_exp.run_options.index = index
        total = len(self.run_options.x_data)

        optimize_field = self.experiment_options.optimize_field
        if hasattr(single_shot_exp.qubit, optimize_field):
            setattr(single_shot_exp.qubit, optimize_field, value)
            if optimize_field in ["probe_freq"]:
                single_shot_exp.qubit.check_lo()

        elif optimize_field == "Mwave.amp":
            single_shot_exp.qubit.Mwave.amp = value
        else:
            raise ExperimentOptionsError(
                self,
                key="optimize_field",
                value=optimize_field,
                msg=f"'{single_shot_exp.qubit}' object has no attribute '{optimize_field}'",
            )

        single_shot_exp.set_parent_file(self, f"{optimize_field}={value}", index, total)
        single_shot_exp.set_experiment_options(
            save_result=False,
        )
        self._check_simulator_data(single_shot_exp, index)

    def _handle_child_result(self, exp: "SingleShot"):
        # collect child experiment result and provide it for parent.
        single_shot_exp = exp

        idx = single_shot_exp.run_options.index
        value = self.run_options.x_data[idx]
        provide_field = self.analysis_options.data_key[0]

        # if single_shot_exp.discriminator is None:
        # bugfix: single shot result name update dcm
        # bit_name = single_shot_exp.qubit.name
        dcm = single_shot_exp.analysis.results.get("dcm").value
        single_shot_exp.discriminator = dcm

        k_recommend = single_shot_exp.discriminator.k_recommend
        fidelity = single_shot_exp.discriminator.fidelity
        level_str = single_shot_exp.discriminator.level_str
        outlier = single_shot_exp.discriminator.outlier
        mean_center_dist = single_shot_exp.discriminator.mean_center_distance()

        single_shot_exp.analysis.provide_for_parent.update(
            {provide_field: [k_recommend, *fidelity, outlier, mean_center_dist]}
        )

        for j, label in enumerate(level_str):
            self.run_options.F_dict[label].append(fidelity[j])
        self.run_options.sweep_list.append(value)
        self.run_options.k_recommend_list.append(k_recommend)
        self.run_options.outlier_list.append(outlier)
        self.run_options.center_dist_list.append(mean_center_dist)

        if not self.run_options.child_quality_list:
            self.run_options.child_quality_list = [
                0 for _ in self.run_options.x_data
            ]
        self.run_options.child_quality_list[idx] = single_shot_exp.analysis.quality
