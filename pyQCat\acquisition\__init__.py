# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/20
# __author:       ssfang

"""
===========================================================
Data Acquisition (:mod:`pyQCat.acquisition`)
===========================================================

Base Classes
===========================================================

.. autosummary::
    :toctree: ../stubs/acquisition/

    DataAcquisition
"""

from .acquisition import DataAcquisition, SimulateAcquisition, WeirdoAcquisition

__all__ = ["DataAcquisition", "SimulateAcquisition", "WeirdoAcquisition"]
