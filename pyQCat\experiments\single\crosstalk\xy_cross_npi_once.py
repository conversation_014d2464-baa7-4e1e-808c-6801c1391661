# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/08/10
# __author:       SS Fang

"""
Rough test XY Crosstalk base experiment.
"""

from copy import deepcopy
from typing import List, Tuple, Union

import numpy as np

from ....analysis.library.ape_analysis import APEAnalysis
from ....analysis.specification import ParameterRepr
from ....errors import Experiment<PERSON>ieldError, ExperimentOptionsError
from ....log import pyqlog
from ....pulse.base_pulse import PulseComponent
from ....pulse.pulse_function import pi_pulse
from ....structures import MetaData, Options
from ....types import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class XYCrosstalkNpiOnce(TopExperiment):
    """XY Crosstalk Once experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options.

        Experiment Options:
            target_name (str): Target bit name.

        """
        options = super()._default_experiment_options()

        options.set_validator("target_name", str)
        options.set_validator("amp_coe", float)
        options.set_validator("phase", float)
        options.set_validator("num", int)

        options.set_validator("sweep_name", ["amp_coe", "phase"])
        options.set_validator("sweep_list", list)

        options.target_name = None
        options.amp_coe = 0.1
        options.phase = 0.0
        options.num = 1

        options.sweep_name = "amp_coe"
        options.sweep_list = []

        options.fake_pulse = False
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default run options.

        Run Options:
            target_qubit (Qubit): Target Qubit object.
            bias_qubit (Qubit): Crosstalk Bias Qubit object.

        """
        options = super()._default_run_options()

        options.target_qubit = None
        options.bias_qubit = None

        options.amp_coe_list = []
        options.phase_list = []
        options.fixed_name = ""
        options.fixed_value = 0.0

        options.injection_func = ["cross_xy_pulses"]
        options.support_context = [StandardContext.CM]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options.

        Analysis Options:
            is_plot (bool): Plot or not analysis fit photo.
            data_key (List[str]): List of mark select data.

        """
        options = super()._default_analysis_options()

        options.set_validator("prominence_divisor", float)

        options.is_plot = True
        options.data_key = ["P0"]
        options.x_label = "amp_coe"

        options.prominence_divisor = 5.0

        options.result_parameters = [
            ParameterRepr(name="points_0", repr="points_0"),
            ParameterRepr(name="fit_points_0", repr="fit_points_0"),
        ]

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()

        sweep_name = self.experiment_options.sweep_name
        num = self.experiment_options.num
        fixed_name = self.run_options.fixed_name
        fixed_value = self.run_options.fixed_value

        metadata.draw_meta = {
            "target": self.run_options.target_qubit.name,
            "bias": self.run_options.bias_qubit.name,
            "sweep": sweep_name,
            fixed_name: fixed_value,
            "num": num,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        target_name = self.experiment_options.target_name
        amp_coe = self.experiment_options.amp_coe
        phase = self.experiment_options.phase
        sweep_name = self.experiment_options.sweep_name
        sweep_list = self.experiment_options.sweep_list
        fake_pulse = self.experiment_options.fake_pulse

        qubit_map = {qubit.name: qubit for qubit in self.qubits}
        q_names = list(qubit_map.keys())
        if len(self.qubits) < 2:
            raise ExperimentFieldError(
                self,
                f"XY Crosstalk at least two Qubit ! "
                f"But the qubits length {len(self.qubits)} !",
            )
        if target_name not in q_names:
            raise ExperimentOptionsError(
                self,
                f"Set target {target_name} not in all bit names!",
                "target_name",
                target_name,
            )

        target_qubit = qubit_map.get(target_name)
        qubit_map.pop(target_name)
        bias_qubit = list(qubit_map.values())[0]

        # adjust target and bias bit X width same.
        bias_qubit.XYwave.time = target_qubit.XYwave.time
        bias_qubit.XYwave.offset = target_qubit.XYwave.offset

        amp_coe_list = []
        phase_list = []
        fixed_name = ""
        fixed_value = 0.0

        length = len(sweep_list)
        if sweep_name == "amp_coe":
            amp_coe_list = sweep_list
            phase_list = [phase] * length
            fixed_name = "phase"
            fixed_value = phase
        elif sweep_name == "phase":
            amp_coe_list = [amp_coe] * length
            phase_list = sweep_list
            fixed_name = "amp_coe"
            fixed_value = amp_coe

        multi_readout_channels = [target_qubit.readout_channel]

        if fake_pulse is True:
            pyqlog.warning(f"{self.label} must be set `fake_pulse` False!")
            fake_pulse = False

        # adjust to new version, 2024.05.20
        pre_xy_cross = {
            "target_name": target_name,
            "bias_name": bias_qubit.name,
            "amp_coe_list": amp_coe_list,
            "phase_list": phase_list,
        }

        self.set_experiment_options(
            multi_readout_channels=multi_readout_channels,
            data_type="I_Q",
            fake_pulse=fake_pulse,
        )

        self.set_run_options(
            target_qubit=target_qubit,
            bias_qubit=bias_qubit,
            amp_coe_list=amp_coe_list,
            phase_list=phase_list,
            fixed_name=fixed_name,
            fixed_value=fixed_value,
            measure_qubits=[target_qubit],
            x_data=sweep_list,
            analysis_class=APEAnalysis,
            pre_xy_cross=pre_xy_cross,
        )

        self.set_analysis_options(x_label=sweep_name)

    @staticmethod
    def set_xy_pulses(self):
        """Set experiment XY pulses."""
        num = self.experiment_options.num

        target_qubit = self.run_options.target_qubit
        bias_qubit = self.run_options.bias_qubit
        amp_coe_list = self.run_options.amp_coe_list
        phase_list = self.run_options.phase_list

        t_x_pulse = pi_pulse(target_qubit)
        b_x_pulse = pi_pulse(bias_qubit)

        t_pulse_list = []
        b_pulse_list = []
        length = len(amp_coe_list)
        for idx in range(length):
            t_pulse_once = (
                deepcopy(t_x_pulse)(phase=0) + deepcopy(t_x_pulse)(phase=np.pi)
            ) * num

            b_pulse_once = (
                deepcopy(b_x_pulse)(phase=0) + deepcopy(b_x_pulse)(phase=np.pi)
            ) * num

            t_pulse_list.append(t_pulse_once)
            b_pulse_list.append(deepcopy(b_pulse_once))

        self.play_pulse("XY", target_qubit, t_pulse_list)
        self.play_pulse("XY", bias_qubit, b_pulse_list)

        # cr_args_list = list(zip(amp_coe_list, phase_list))
        # new_t_pulse_list = self.cross_xy_pulses(
        #     self.xy_pulses[target_qubit], self.xy_pulses[bias_qubit], cr_args_list
        # )
        # self.xy_pulses.update({target_qubit: new_t_pulse_list})

    @staticmethod
    def cross_xy_pulses(
        t_pulses: List[PulseComponent],
        b_pulses: List[PulseComponent],
        cr_args_list: List[Union[List, Tuple]],
    ):
        """Optimize cross xy pulse list.

        Notice: Must be real pulse data.
        """
        new_t_pulses = deepcopy(t_pulses)
        for idx, t_pulse in enumerate(new_t_pulses):
            cr_args = cr_args_list[idx]
            amp_coe, phase, *_ = cr_args

            b_pulse = b_pulses[idx]

            t_pulse_complex = t_pulse.pulse_complexes[0]
            b_pulse_complex = b_pulse.pulse_complexes[0]

            b_cross_pulse_complex = b_pulse_complex * amp_coe * np.exp(1j * phase)
            new_t_pulse_complex = t_pulse_complex + b_cross_pulse_complex
            new_t_pulse_arr = np.real(new_t_pulse_complex)

            t_pulse.pulse_complexes = [new_t_pulse_complex]
            t_pulse.pulse = new_t_pulse_arr

        return new_t_pulses

    @staticmethod
    def update_instrument(self):
        """Update instrument."""
        target_qubit = self.run_options.target_qubit
        self._bind_probe_inst(target_qubit)
