# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/15
# __author:       <PERSON><PERSON><PERSON>

from enum import <PERSON><PERSON>
from typing import List

import numpy as np

from ...log import pyqlog
from ...tools import get_bound_ac_spectrum, qarange
from ..batch_experiment import BatchExperiment


class SpectrumType(str, Enum):
    AC = "ACSpectrum"
    T1 = "T1Spectrum"


class BatchACT1Spectrum(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.max_cali_flows = ["SweetPointCalibration", "SweetPointCalibrationVMin"]
        options.ac_spec_flows = [SpectrumType.AC]
        options.t1_spec_flows = [SpectrumType.T1]
        options.gap_points = 30  # Scanning points between the highest and lowest point
        options.band_points = (
            5  # Number of external extension points for boundary point
        )
        options.t1_step = 20
        options.t1_points = 20
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.group_map = None
        options.ppt_template.update(
            dict(
                shape=(2, 3),
                split_by_unit=False,
            )
        )
        return options

    def _check_options(self):
        super()._check_options()
        self.run_options.ppt_template.exps = (
            self.experiment_options.max_cali_flows
            + self.experiment_options.ac_spec_flows
            + self.experiment_options.t1_spec_flows
        )

    def _auto_divide_amp_for_ac_spectrum(self, working_unit: List[str]):
        # auto calculate ac spectrum sweep amp list
        parallel_units = []
        for unit in working_unit:
            qubit = self.context_manager.chip_data.get_physical_unit(unit)
            if qubit.tunable is True:
                parallel_units.append(unit)
                max_point = qubit.dc_max
                min_point = qubit.dc_min
                idle_point = qubit.idle_point

                step = round(
                    (max_point - min_point) / self.experiment_options.gap_points, 5
                )
                mid_amp_list = qarange(max_point, min_point, -step)

                band_scope = step * self.experiment_options.band_points
                left_point, right_point = mid_amp_list[0], mid_amp_list[-1]
                max_band_amp_list = qarange(
                    left_point + step, left_point + band_scope, step
                )
                min_band_amp_list = qarange(
                    right_point - step, right_point - band_scope, -step
                )

                expect_amp_list = mid_amp_list + max_band_amp_list + min_band_amp_list
                expect_amp_list = [amp for amp in expect_amp_list if abs(amp) < 0.48]
                actual_amp_list = [
                    round(amp - idle_point - max_point, 5) for amp in expect_amp_list
                ]

                pyqlog.info(
                    f"{unit} scope info: max({max(expect_amp_list)}) min({min(expect_amp_list)})"
                    f" step({step}) points({len(expect_amp_list)})"
                )

                self.change_parallel_exec_exp_options(
                    exp_name=SpectrumType.AC, unit=unit, z_amps=actual_amp_list
                )
            else:
                pyqlog.warning(f"{unit} tunable is false")

        # divide parallel group and IF divide
        group_map = self.parallel_allocator_for_qc(parallel_units)
        self.run_options.group_map = group_map

    def _auto_divide_freq_for_t1_spectrum(self, working_unit: List[str]):
        # calculate freq list
        exp_name = self.experiment_options.t1_spec_flows[0]
        for unit in working_unit:
            qubit = self.context_manager.chip_data.get_physical_unit(unit)
            freq_max, freq_min = get_bound_ac_spectrum(qubit)

            # bugfix 2024/01/18: round maybe cause bound freq out of range
            if self.experiment_options.t1_points:
                points = self.experiment_options.t1_points
                freq_list = np.linspace(
                    freq_min + 0.001, freq_max - 0.001, points
                ).tolist()
            else:
                t1_step = abs(self.experiment_options.t1_step) or 20
                freq_list = qarange(freq_min + 0.001, freq_max - 0.001, t1_step)

            self.change_parallel_exec_exp_options(
                exp_name=exp_name, unit=unit, freq_list=freq_list
            )

            pyqlog.info(
                f"{unit} scope info: max({freq_max}) min({freq_min}) points({len(freq_list)})"
            )

        # divide parallel group and IF divide
        group_map = self.parallel_allocator_for_qc(
            working_unit,
        )
        self.run_options.group_map = group_map

    def _run_batch(self):
        # run dc max/min calibrate
        if self.experiment_options.max_cali_flows:
            # divide parallel group and IF divide
            group_map = self.parallel_allocator_for_qc(
                self.experiment_options.physical_units
            )

            # run flow
            working_units = []
            for group_name, group in group_map.items():
                pass_units = self._run_flow(
                    flows=self.experiment_options.max_cali_flows,
                    physical_units=group,
                    name=f"{group_name} VMax Cali",
                )
                if pass_units:
                    working_units.extend(pass_units)
        else:
            working_units = self.experiment_options.physical_units

        fail_units = None

        # run ac spectrum
        if working_units:
            working_units, fail_units = self._common_run_func(
                SpectrumType.AC, working_units
            )

        # run t1 spectrum
        if working_units:
            self._common_run_func(SpectrumType.T1, working_units)

        self._show_batch_execute_result(all_units=working_units, bad_units=fail_units)
        self.bind_pass_units(working_units)
        if self.experiment_options.save_db is True:
            self.backend.update_context_from_hot_data(self.record_id)

    def _common_run_func(self, name: str, working_units: List[str]):
        if name == SpectrumType.AC:
            flows = self.experiment_options.ac_spec_flows
            if flows:
                self._auto_divide_amp_for_ac_spectrum(working_units)
        else:
            flows = self.experiment_options.t1_spec_flows
            if flows:
                self._auto_divide_freq_for_t1_spectrum(working_units)

        if flows:
            pass_units = []
            for group_name, cur_group_unit in self.run_options.group_map.items():
                cur_pass_units = self._run_flow(
                    flows=flows,
                    physical_units=cur_group_unit,
                    name=f"{group_name} Search {name}",
                )
                pass_units.extend(cur_pass_units)
            fail_units = [unit for unit in working_units if unit not in pass_units]
            pyqlog.log(
                "RESULT",
                f"{name} Result:\npass units: {pass_units}\nfail units: {fail_units}",
            )
            return pass_units, fail_units

        return working_units, []
