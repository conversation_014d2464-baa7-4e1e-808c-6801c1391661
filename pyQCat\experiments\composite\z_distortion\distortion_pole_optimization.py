# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/02/14
# __author:       xw

"""
Optimize the parameters in the pole model using the fidelity of the RB as a cost function.
"""

import json
import os
from copy import deepcopy
from typing import Union

import matplotlib.pyplot as plt
import numpy as np
from cma.fitness_transformations import ScaleCoordinates
from scipy import signal
from scipy.optimize._constraints import Bounds

import pyQCat.tools.de_optimize as ea

from ....analysis.algorithms.distortion import (
    _minimize_neldermead,
    generate_ab_from_poles_params_dict,
    get_poles_bounds_p0,
)
from ....analysis.fit.fit_models import arrange_poles_params
from ....analysis.library import OptimizationAnalysis
from ....errors import Experiment<PERSON>ield<PERSON>rror, ExperimentOptionsError
from ....log import pyqlog
from ....structures import Options
from ....tools.de_optimize.import_ea import ea as gea
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import Distortion_RB


class DistortionPolesOpt(CompositeExperiment):
    """Optimize the parameters in the pole model using the fidelity of the RB as a cost function."""

    _sub_experiment_class = Distortion_RB

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()

        options.set_validator("z_pulse_params", dict)
        options.set_validator("poles_obj", dict)
        options.set_validator("nm_options", dict)
        options.set_validator("de_options", dict)
        options.set_validator("poles_num_list", list)
        options.set_validator("poles_bounds_path", str)
        options.set_validator("opti_method", ["NM", "DE"])
        options.set_validator("fixed_m", int)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.z_pulse_params = {
            "t_head": 50,
            "t_bottom": 100,
            "t_wait": 30,
            "amp_bottom": 0,
            "amp_tail": 0,
        }
        options.poles_obj = None
        options.nm_options = {
            "nonzdelt": 0.04,
            "maxiter": 100,
            "maxfev": 70,
            "fatol": 0.005,
            "xatol": 1e-2,
            "adaptive": False,
            "return_all": True,
            "disp": True,
        }

        options.de_options = {
            "NIND": 30,  # The number of individuals in the population, MATLAB gave 15 at the time.
            # Take a look at the npj single-bit gate and give it a little more,
            # because the single-bit DAG may not run through and there will be losses.
            "MAXGEN": 20,  # Maximum number of genetic generations
            "mutF": 0.7,  # Parameter F in differential evolution
            "XOVR": 0.7,  # Cross-recombination probability
            "init_population_path": None,
        }  # mutF=0.5/0.7, XOVR=0.7

        options.poles_num_list = [[3, 0]]
        options.step_res_normalization = True
        options.awg_ts = 1 / 1.2
        options.check_ab = True
        options.temp_mode = "low_temp"
        options.response_type = "rise"
        options.poles_bounds_path = None
        options.opti_method = "DE"
        options.fixed_m = 50
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default Analysis Options"""
        options = super()._default_analysis_options()

        options.goal_function = None

        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        file_name = self.experiment_options.poles_bounds_path
        try:
            with open(file_name, mode="r", encoding="utf-8") as fp:
                poles_obj = json.load(fp)
        except Exception:
            raise ExperimentOptionsError(
                self.label,
                key="poles_bounds_path",
                value=file_name,
                msg=f"{file_name} does not exist!",
            )
        self.set_experiment_options(poles_obj=poles_obj)
        # z_pulse_params
        z_pulse_params = self.experiment_options.z_pulse_params
        if z_pulse_params["amp_bottom"] is None:
            # choose +0.4 or -0.4 according to idle point voltage
            idle_voltage = self.qubits[0].dc_max + self.qubits[0].idle_point
            if idle_voltage > 0:
                z_pulse_params["amp_bottom"] = -0.4
            else:
                z_pulse_params["amp_bottom"] = 0.4
            self.set_experiment_options(z_pulse_params=z_pulse_params)

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.goal_list = []
        options.poles_model_params_list = []
        options.sos_filter = []
        options.count = 0
        return options

    def poles_model_params_2_sos_filter(self, poles_model_params):
        poles_model_params_dic = arrange_poles_params(
            poles_model_params,
            self.real_poles_num,
            self.complex_poles_num,
            self.step_response_normalization,
            self.experiment_options.awg_ts,
            self.experiment_options.response_type,
        )
        a, b = generate_ab_from_poles_params_dict(
            poles_model_params_dic,
            self.real_poles_num,
            self.complex_poles_num,
            self.experiment_options.check_ab,
            self.experiment_options.awg_ts,
        )
        zeros, poles, k = signal.tf2zpk(b, a)
        sos_digital_filter = signal.zpk2sos(poles, zeros, 1 / k)
        return sos_digital_filter

    async def single_length_RB_for_poles_opt(self, poles_model_params):
        """
        cost function used to optimize the parameters of the poles model
        :param poles_model_params:
        :return:
        """
        pyqlog.info(f"poles_model_params:{poles_model_params}")
        RB_exp = deepcopy(self.child_experiment)
        RB_exp.set_parent_file(self, f"rb_exp-{self.run_options.count}")

        sos_digital_filter = self.poles_model_params_2_sos_filter(poles_model_params)

        RB_exp.compensates[self.qubits[0]].z_distortion_type = "sos"
        compensate = RB_exp.compensates[self.qubits[0]]
        compensate.z_distortion_sos["Low_temperature_IIR_sos_filter"] = [
            sos_digital_filter.tolist()
        ]
        # sos_dict = compensate.z_distortion_sos
        # compensate.z_distortion_sos = {}
        try:
            RB_exp.set_experiment_options(
                depth1=[self.experiment_options.fixed_m],
                depth2=None,
                depth3=None,
                depth4=None,
                simulator_data_path=self.experiment_options.simulator_data_path,
                z_pulse_params=self.experiment_options.z_pulse_params,
            )
            await RB_exp.run_experiment()
            self._experiments.append(RB_exp)
            # get P0 and p1
            P1_list = RB_exp.analysis.analysis_datas["P1"].y
            goal = np.mean(P1_list) + np.std(P1_list, ddof=1)  # fidelity and std
            RB_exp.analysis.provide_for_parent.update({"goal": goal})
        except Exception as e:
            pyqlog.warning(f"Error msg:{e}")
            goal = 0.5

        self.run_options.goal_list.append(goal)
        self.run_options.poles_model_params_list.append(poles_model_params)
        self.run_options.count += 1
        return goal

    def single_length_RB_for_1qgate_opt(self, params_1q_gate):
        """
        cost function used to optimize single qubit gate
        :param params_1q_gate:
        :return:
        """

        RB_exp = deepcopy(self.child_experiment)
        RB_exp.set_parent_file(self, f"rb_exp-{self.run_options.count}")
        if len(params_1q_gate) == 2:
            detune, amp_pi_2 = params_1q_gate
        elif len(params_1q_gate) == 3:
            alpha, detune, amp_pi_2 = params_1q_gate
            RB_exp.qubit.XYwave.alpha = alpha
        # detune, amp_pi_2
        RB_exp.qubit.XYwave.detune_pi2 = detune
        RB_exp.qubit.XYwave.Xpi2 = amp_pi_2

        try:
            RB_exp.set_experiment_options(
                depths=self.experiment_options.depths,
                times=self.experiment_options.times,
                gate_split=self.experiment_options.gate_split,
                interleaved_gate=self.experiment_options.interleaved_gate,
                simulator_data_path=self.experiment_options.simulator_data_path,
            )
            RB_exp.run()
            RB_exp.clear_params()
            # get P0 and p1
            P1_list = RB_exp.analysis.analysis_datas["P1"].y
            goal = np.mean(P1_list)
        except Exception as e:
            pyqlog.warning(f"Error msg:{e}")
            goal = 0.5
        return goal, goal

    def fct_to_min(self, input_parameters: Union[np.ndarray]) -> Union[np.ndarray]:
        """
        Wrapper for the goal function.

        Parameters
        ----------
        input_parameters : [np.array, tf.constant]
            Vector of parameters in the optimizer friendly way.

        Returns
        -------
        [np.ndarray]
            Value of the goal function. Float if input is np.array
        """
        goal = self.eval_func(input_parameters)
        return goal

    async def cma_opt(self, init, bounds, nm_options):
        self.eval_func = self.single_length_RB_for_poles_opt
        lb = np.asarray(bounds[0])
        ub = np.asarray(bounds[1])
        multipliers = ub - lb
        zeros = -lb / (ub - lb)
        fun = ScaleCoordinates(self.fct_to_min, multipliers, zeros)
        lb = fun.inverse(lb)
        ub = fun.inverse(ub)
        init = fun.inverse(init)
        # bounds = np.column_stack((lb, ub))
        bounds = Bounds(lb, ub)
        # x, fval = minimize_nelder_mead(fun, init, bounds=bounds, **nm_options)
        res = await _minimize_neldermead(fun, init, bounds=bounds, **nm_options)
        x = res.x
        xbest = fun.scale_and_offset(x)
        return xbest

    def mediate_save(self):
        # Each generation is saved at the end of the run
        # optimization process
        exp_index = list(range(self.run_options.count))

        data_save = np.column_stack(
            (
                np.array(exp_index),
                np.array(self.run_options.poles_model_params_list),
                np.array(self.run_options.goal_list),
            )
        )
        self.file.save_data(data_save.T, name=f"optimization_process")
        # current best
        current_best_index = np.argmin(np.array(self.run_options.goal_list))
        current_best = data_save[current_best_index, :]
        self.file.save_data(current_best, name=f"current_best")
        # digital filter corresponding to current best
        sos_digital_filter = self.poles_model_params_2_sos_filter(
            self.run_options.poles_model_params_list[current_best_index]
        )
        self.file.save_data(
            sos_digital_filter.T,
            name=f"{self.qubits[0].name}_Low_temp_IIR_sos_filter",
            fmt="%.16f",
        )
        # best 10
        if data_save.shape[0] > 9:
            best10 = data_save[np.argsort(data_save, axis=0)[:, -1]][:10, :]
            self.file.save_data(best10.T, name=f"best10")

        # trace plot
        de_options = self.experiment_options.de_options
        NIND = de_options["NIND"]  # Number of individuals in the population
        GEN = int(np.floor(self.run_options.count / NIND))
        goal_function = np.array(self.run_options.goal_list)
        goal_function = goal_function.reshape(GEN, NIND).tolist()
        self.data_plot(goal_function)

    async def evalVars(self, Vars):
        # Vars size=[number of individuals in the population, number of parameters],
        # even if the initial value is the same, the result here is not the same.
        cost = []
        for i in range(Vars.shape[0]):
            costi = await self.single_length_RB_for_poles_opt(Vars[i, :])
            cost.append(costi)
        self.mediate_save()
        return np.vstack(cost)

    async def opti_DE(self, init, bounds, opti_opts):
        opti_dim = len(init)

        problem = ea.Problem(
            name="soea Z distortion compensation",
            M=1,  # Initialize M (Target Dimension)
            maxormins=[1],
            Dim=opti_dim,
            varTypes=[0] * opti_dim,
            # varTypes=[1, 0, 0],
            lb=np.asarray(bounds[0]),
            ub=np.asarray(bounds[1]),
            evalVars=self.evalVars,
        )

        algorithm = ea.soea_DE_best_1_bin_templet(
            problem,
            gea.Population(Encoding="RI", NIND=opti_opts["NIND"]),
            MAXGEN=opti_opts["MAXGEN"],
            logTras=1,
            outFunc=None,
            # trappedValue=1e-10,
            # maxTrappedCount=20
        )
        algorithm.mutOper.F = opti_opts["mutF"]  # Parameter F in differential evolution
        algorithm.recOper.XOVR = opti_opts["XOVR"]  # Cross-recombination probability

        self.save_path = self.file.dirs + "soea_DE result"
        os.makedirs(self.save_path)
        if opti_opts.get("init_population_path"):
            # Insert prior knowledge and make a check for legitimacy.
            prophet = np.genfromtxt(opti_opts.get("init_population_path"))
            if prophet.shape[1] != opti_dim:
                raise ValueError(
                    f"the dimension of prophet is different from the dimension of parameters to be optimized."
                )
        else:
            prophet = np.array(init)
        res = await ea.optimize(
            algorithm,
            prophet=prophet,
            verbose=True,
            drawing=0,
            outputMsg=True,
            drawLog=False,
            saveFlag=True,
            dirName=self.save_path,
        )
        return res

    async def single_poles_num_run(self, init, bounds):
        """
        run with a pair of [real_poles_num, complex_poles_num]
        :param init:
        :param bounds:
        :return:
        """
        opti_method = self.experiment_options.opti_method
        if opti_method == "NM":
            nm_options = self.experiment_options.nm_options
            xbest = await self.cma_opt(init, bounds, nm_options)
        elif opti_method == "DE":
            de_options = self.experiment_options.de_options
            res = await self.opti_DE(init, bounds, de_options)
            optPop = res["optPop"]

            # print('The best objective value is: %s' % optPop.ObjV[0][0])
            # print('The best variables are: ')
            # for i in range(optPop.Phen.shape[1]):
            #     print(optPop.Phen[0, i], end='\t')
            xbest = optPop.Phen[0, :]
        else:
            raise ValueError(f"opti_method {opti_method} is not supported now.")

        sos_digital_filter = self.poles_model_params_2_sos_filter(xbest)
        self.compensates[self.qubits[0]].z_distortion_sos[
            "Low_temperature_IIR_sos_filter"
        ] = [sos_digital_filter.tolist()]
        self.run_options.sos_filter = sos_digital_filter

    async def optimal_poles_num_determination(self):
        """
        determine the best [real_poles_num, complex_poles_num]
        :return:
        """
        # RB distortion envelope set
        poles_num_list = self.experiment_options.poles_num_list
        self.real_poles_num = poles_num_list[0][0]
        self.complex_poles_num = poles_num_list[0][1]
        self.step_response_normalization = (
            self.experiment_options.step_res_normalization
        )

        # initial point
        params, lb, ub, p0 = get_poles_bounds_p0(
            temp_mode=self.experiment_options.temp_mode,
            real_poles_num=self.real_poles_num,
            complex_poles_num=self.complex_poles_num,
            data=self.experiment_options.poles_obj,
            response_type=self.experiment_options.response_type,
        )
        if len(p0) != len(lb):
            raise ExperimentFieldError(
                self.label, "Dimension of initial point is wrong"
            )
        # # strict bound constraints,[0.5,1.5],the range is 1
        # for index_bound in list(range(len(p0))):
        #     if p0[index_bound] > 0:
        #         lb[index_bound] = p0[index_bound] * 0.5
        #         ub[index_bound] = p0[index_bound] * 1.5
        #     else:
        #         lb[index_bound] = p0[index_bound] * 1.5
        #         ub[index_bound] = p0[index_bound] * 0.5

        bounds = [lb, ub]

        for index, poles_pair in enumerate(poles_num_list):
            self.real_poles_num, self.complex_poles_num = poles_pair
            await self.single_poles_num_run(p0, bounds)

    async def _sync_composite_run(self):
        await self.optimal_poles_num_determination()
        sos_digital_filter = self.run_options.sos_filter
        self.file.save_data(
            sos_digital_filter.T,
            name=f"{self.qubits[0].name}_Low_temp_IIR_sos_filter",
            fmt="%.16f",
        )

        exp_index = list(range(self.run_options.count))

        data_save = np.column_stack(
            (
                np.array(exp_index),
                np.array(self.run_options.poles_model_params_list),
                np.array(self.run_options.goal_list),
            )
        )

        self.file.save_data(data_save.T, name=f"optimization_process")

        opti_method = self.experiment_options.opti_method
        if opti_method == "DE":
            de_options = self.experiment_options.de_options
            MAXGEN = de_options["MAXGEN"]  # Maximum number of genetic generations
            NIND = de_options["NIND"]  # Number of individuals in the population
        elif opti_method == "NM":
            MAXGEN = self.run_options.count
            NIND = 1
        else:
            raise ValueError(f"opti_method {opti_method} is not supported now.")

        goal_function = np.array(self.run_options.goal_list)
        goal_function = goal_function.reshape(MAXGEN, NIND).tolist()

        x_data = range(1, len(goal_function) + 1)
        self.set_analysis_options(goal_function=goal_function)
        self._run_analysis(np.asarray(x_data).repeat(NIND), OptimizationAnalysis)

    def data_plot(self, goal_function):
        fig, ax = plt.subplots(figsize=(12, 8))
        # means = []
        bests = []
        for ii in range(len(goal_function)):
            # means.append(np.mean(np.array(goal_function[ii])))
            bests.append(np.min(np.array(goal_function[ii])))
            for pt in goal_function[ii]:
                ax.plot(
                    ii + 1,
                    pt,
                    color="tab:blue",
                    marker="D",
                    markersize=2.5,
                    linewidth=0,
                )

        # ax.xaxis.set_major_locator(MaxNLocator(integer=True))
        ax.plot(
            range(1, len(goal_function) + 1),
            bests,
            color="tab:red",
            marker="D",
            markersize=5.5,
            linewidth=0,
            fillstyle="full",
        )
        times = self.child_experiment.experiment_options.times
        z_pulse_params = self.experiment_options.z_pulse_params
        de_options = deepcopy(self.experiment_options.de_options)
        poles_num_list = self.experiment_options.poles_num_list
        m = self.experiment_options.fixed_m
        del de_options["mutF"]
        del de_options["XOVR"]
        del de_options["init_population_path"]
        fontsize = 20
        ax.set_title(
            f"{self.qubits[0].name}, m={m}, k={times}, de_options={de_options}, \n"
            f"z_pulse_params={z_pulse_params},\n "
            f"poles_num = {poles_num_list}, "
            f"best_goal={np.round(np.min(np.array(self.run_options.goal_list)), 4)}",
            fontsize=18,
        )

        ax.set_ylabel("goal", fontsize=fontsize)
        ax.set_xlabel("Iterations", fontsize=fontsize)
        plt.tick_params(labelsize=fontsize)
        ax.grid()
        fig.savefig(self.save_path + "\Trace_Plot.png")
