# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/10
# __author:       <PERSON><PERSON><PERSON>


from ...log import pyqlog
from ..batch_experiment import BatchExperiment, List


class BatchRunner(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.loop = 1
        return options

    def _batch_up(self):
        super()._batch_up()

        if not self.experiment_options.flows:
            self.experiment_options.flows = self.params_manager.flows

    def _run_batch(self):
        self._run_flow()

    def _batch_down(self):
        super()._batch_down()
        if self.run_options.error_records:
            err_experiments = "\n".join(list(self.run_options.error_records))
            pyqlog.error(f"Error Experiment as Flow:\n{err_experiments}")

    def _run_flow(
        self, flows: List[str] = None, physical_units: List[str] = None
    ) -> List[str]:
        if not flows:
            flows = self.experiment_options.flows

        loop = self.experiment_options.loop
        for i in range(loop):
            result = super()._run_flow(flows, physical_units, f"Base Flow ({i + 1}/{loop})")

        return result
