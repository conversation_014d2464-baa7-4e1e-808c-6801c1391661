<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1079</width>
    <height>406</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Login</string>
  </property>
  <property name="windowIcon">
   <iconset resource="_imgs/_imgs.qrc">
    <normaloff>:/logo.png</normaloff>:/logo.png</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <spacer name="horizontalSpacer_12">
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
      <property name="sizeType">
       <enum>QSizePolicy::Policy::Preferred</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>40</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_7">
        <item>
         <spacer name="horizontalSpacer_11">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="logo_label">
          <property name="text">
           <string/>
          </property>
          <property name="textFormat">
           <enum>Qt::TextFormat::AutoText</enum>
          </property>
          <property name="pixmap">
           <pixmap resource="_imgs/_imgs.qrc">:/login-logo.png</pixmap>
          </property>
          <property name="wordWrap">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_10">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="title_label">
          <property name="font">
           <font>
            <family>Calibri</family>
            <pointsize>18</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="text">
           <string>Calibrate quantum chip.
Right at your fingertips.</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
          </property>
          <property name="wordWrap">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_9">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="note_label">
        <property name="text">
         <string>Origin Quantum offers pyQCat series software packages and applications for users to do measure, characterization and calibtration on quantum chip.

If you encounter any problems or have some good suggestions, please contact our development team. 

Mail: <EMAIL></string>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignTop</set>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer_2">
        <property name="orientation">
         <enum>Qt::Orientation::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </item>
    <item>
     <spacer name="horizontalSpacer_14">
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
      <property name="sizeType">
       <enum>QSizePolicy::Policy::Fixed</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>40</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QStackedWidget" name="stackedWidget">
      <property name="frameShape">
       <enum>QFrame::Shape::Box</enum>
      </property>
      <property name="currentIndex">
       <number>1</number>
      </property>
      <widget class="QWidget" name="addr_page">
       <layout class="QVBoxLayout" name="verticalLayout_2" stretch="10,5,1,1,1,1,1">
        <item>
         <widget class="QLabel" name="test_conntext_label">
          <property name="palette">
           <palette>
            <active>
             <colorrole role="Button">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>181</red>
                <green>181</green>
                <blue>181</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Light">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>223</red>
                <green>214</green>
                <blue>207</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Midlight">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>186</red>
                <green>178</green>
                <blue>172</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Dark">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>74</red>
                <green>71</green>
                <blue>69</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Mid">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>99</red>
                <green>95</green>
                <blue>92</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Base">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>181</red>
                <green>181</green>
                <blue>181</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Window">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>181</red>
                <green>181</green>
                <blue>181</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Shadow">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="AlternateBase">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>202</red>
                <green>199</green>
                <blue>196</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="PlaceholderText">
              <brush brushstyle="SolidPattern">
               <color alpha="127">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </active>
            <inactive>
             <colorrole role="Button">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>181</red>
                <green>181</green>
                <blue>181</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Light">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>255</green>
                <blue>255</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Midlight">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>227</red>
                <green>227</green>
                <blue>227</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Dark">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>160</red>
                <green>160</green>
                <blue>160</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Mid">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>160</red>
                <green>160</green>
                <blue>160</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Base">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>181</red>
                <green>181</green>
                <blue>181</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Window">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>181</red>
                <green>181</green>
                <blue>181</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Shadow">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>105</red>
                <green>105</green>
                <blue>105</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="AlternateBase">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>245</red>
                <green>245</green>
                <blue>245</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="PlaceholderText">
              <brush brushstyle="SolidPattern">
               <color alpha="128">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </inactive>
            <disabled>
             <colorrole role="Button">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>181</red>
                <green>181</green>
                <blue>181</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Light">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>223</red>
                <green>214</green>
                <blue>207</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Midlight">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>186</red>
                <green>178</green>
                <blue>172</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Dark">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>74</red>
                <green>71</green>
                <blue>69</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Mid">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>99</red>
                <green>95</green>
                <blue>92</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Base">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>181</red>
                <green>181</green>
                <blue>181</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Window">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>181</red>
                <green>181</green>
                <blue>181</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="Shadow">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="AlternateBase">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>245</red>
                <green>245</green>
                <blue>245</blue>
               </color>
              </brush>
             </colorrole>
             <colorrole role="PlaceholderText">
              <brush brushstyle="SolidPattern">
               <color alpha="128">
                <red>0</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </disabled>
           </palette>
          </property>
          <property name="font">
           <font>
            <pointsize>11</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(181, 181, 181);</string>
          </property>
          <property name="text">
           <string>User Data Serive Test Connect !</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_6">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>30</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QWidget" name="widget" native="true">
          <layout class="QHBoxLayout" name="horizontalLayout_13" stretch="2,2,6,3">
           <item>
            <spacer name="horizontalSpacer_27">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="ip_label">
             <property name="text">
              <string>IP</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="ip_edit"/>
           </item>
           <item>
            <spacer name="horizontalSpacer_28">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>79</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="widget_2" native="true">
          <layout class="QHBoxLayout" name="horizontalLayout_14" stretch="2,2,6,3">
           <item>
            <spacer name="horizontalSpacer_29">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>51</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="port_label">
             <property name="text">
              <string>PORT</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="port_edit"/>
           </item>
           <item>
            <spacer name="horizontalSpacer_30">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_7">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>30</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_15" stretch="2,1,3,1,3,2">
          <item>
           <spacer name="horizontalSpacer_34">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="state_label">
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="test_connect_button">
            <property name="text">
             <string>TestConnect</string>
            </property>
            <property name="icon">
             <iconset resource="_imgs/_imgs.qrc">
              <normaloff>:/wifi-line.png</normaloff>:/wifi-line.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_35">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="connect_button">
            <property name="font">
             <font>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>Connect</string>
            </property>
            <property name="icon">
             <iconset resource="_imgs/_imgs.qrc">
              <normaloff>:/connect.png</normaloff>:/connect.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_33">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer_8">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>30</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="login_page">
       <layout class="QGridLayout" name="gridLayout" rowstretch="2,1,2,2,2,2,2,5,2">
        <item row="0" column="0">
         <widget class="QLabel" name="label">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="font">
           <font>
            <pointsize>11</pointsize>
            <bold>true</bold>
            <underline>false</underline>
            <strikeout>false</strikeout>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color:rgb(181, 181, 181)</string>
          </property>
          <property name="frameShape">
           <enum>QFrame::Shape::Panel</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Shadow::Raised</enum>
          </property>
          <property name="text">
           <string>Login to pyQCat !</string>
          </property>
          <property name="textFormat">
           <enum>Qt::TextFormat::AutoText</enum>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <spacer name="verticalSpacer_3">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Policy::Minimum</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="2" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="0,1,2,0">
          <item>
           <spacer name="horizontalSpacer_5">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="user_name_label">
            <property name="text">
             <string>User Name</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="user_name_box">
            <property name="editable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_6">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item row="3" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="0,1,2,0">
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="password_label">
            <property name="text">
             <string>Password</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="pwd_linedit_login">
            <property name="text">
             <string/>
            </property>
            <property name="echoMode">
             <enum>QLineEdit::EchoMode::Password</enum>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item row="4" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_22" stretch="0,1,1,1,0">
          <item>
           <spacer name="horizontalSpacer_31">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="user_name_label_2">
            <property name="text">
             <string>Multi-Window</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="cache_flag">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="editable">
             <bool>false</bool>
            </property>
            <item>
             <property name="text">
              <string>0</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>1</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>2</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>3</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>4</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>5</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>6</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>7</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>9</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>10</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="select_cache_flag">
            <property name="text">
             <string>Select Cache</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_32">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item row="5" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="0,2,2,0">
          <item>
           <spacer name="horizontalSpacer_8">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QCheckBox" name="is_remember_box">
            <property name="text">
             <string>Remember me</string>
            </property>
            <property name="checked">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="login_pushButton">
            <property name="palette">
             <palette>
              <active>
               <colorrole role="ButtonText">
                <brush brushstyle="SolidPattern">
                 <color alpha="255">
                  <red>50</red>
                  <green>206</green>
                  <blue>42</blue>
                 </color>
                </brush>
               </colorrole>
              </active>
              <inactive>
               <colorrole role="ButtonText">
                <brush brushstyle="SolidPattern">
                 <color alpha="255">
                  <red>0</red>
                  <green>0</green>
                  <blue>0</blue>
                 </color>
                </brush>
               </colorrole>
              </inactive>
              <disabled>
               <colorrole role="Button">
                <brush brushstyle="SolidPattern">
                 <color alpha="77">
                  <red>249</red>
                  <green>249</green>
                  <blue>249</blue>
                 </color>
                </brush>
               </colorrole>
               <colorrole role="ButtonText">
                <brush brushstyle="SolidPattern">
                 <color alpha="92">
                  <red>0</red>
                  <green>0</green>
                  <blue>0</blue>
                 </color>
                </brush>
               </colorrole>
              </disabled>
             </palette>
            </property>
            <property name="font">
             <font>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>Login</string>
            </property>
            <property name="icon">
             <iconset resource="_imgs/_imgs.qrc">
              <normaloff>:/login.png</normaloff>:/login.png</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>24</width>
              <height>24</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item row="6" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0,2,2,0">
          <item>
           <spacer name="horizontalSpacer_7">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="new_user_label">
            <property name="palette">
             <palette>
              <active>
               <colorrole role="WindowText">
                <brush brushstyle="SolidPattern">
                 <color alpha="255">
                  <red>0</red>
                  <green>0</green>
                  <blue>212</blue>
                 </color>
                </brush>
               </colorrole>
              </active>
              <inactive>
               <colorrole role="WindowText">
                <brush brushstyle="SolidPattern">
                 <color alpha="255">
                  <red>0</red>
                  <green>0</green>
                  <blue>0</blue>
                 </color>
                </brush>
               </colorrole>
              </inactive>
              <disabled>
               <colorrole role="WindowText">
                <brush brushstyle="SolidPattern">
                 <color alpha="255">
                  <red>120</red>
                  <green>120</green>
                  <blue>120</blue>
                 </color>
                </brush>
               </colorrole>
              </disabled>
             </palette>
            </property>
            <property name="font">
             <font>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>New to pyQCat ?</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="create_pushButton">
            <property name="palette">
             <palette>
              <active>
               <colorrole role="ButtonText">
                <brush brushstyle="SolidPattern">
                 <color alpha="255">
                  <red>0</red>
                  <green>0</green>
                  <blue>218</blue>
                 </color>
                </brush>
               </colorrole>
              </active>
              <inactive>
               <colorrole role="ButtonText">
                <brush brushstyle="SolidPattern">
                 <color alpha="255">
                  <red>0</red>
                  <green>0</green>
                  <blue>0</blue>
                 </color>
                </brush>
               </colorrole>
              </inactive>
              <disabled>
               <colorrole role="Button">
                <brush brushstyle="SolidPattern">
                 <color alpha="77">
                  <red>249</red>
                  <green>249</green>
                  <blue>249</blue>
                 </color>
                </brush>
               </colorrole>
               <colorrole role="ButtonText">
                <brush brushstyle="SolidPattern">
                 <color alpha="92">
                  <red>0</red>
                  <green>0</green>
                  <blue>0</blue>
                 </color>
                </brush>
               </colorrole>
              </disabled>
             </palette>
            </property>
            <property name="statusTip">
             <string>Create a pyQCat account.</string>
            </property>
            <property name="whatsThis">
             <string>Create a pyQCat account.</string>
            </property>
            <property name="text">
             <string>Create an account.</string>
            </property>
            <property name="icon">
             <iconset resource="_imgs/_imgs.qrc">
              <normaloff>:/register-an-account.png</normaloff>:/register-an-account.png</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>24</width>
              <height>24</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item row="7" column="0">
         <spacer name="verticalSpacer_5">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Policy::Preferred</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="8" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="0,3,1,0">
          <item>
           <spacer name="horizontalSpacer_15">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Policy::Preferred</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="forget_button">
            <property name="palette">
             <palette>
              <active>
               <colorrole role="Button">
                <brush brushstyle="SolidPattern">
                 <color alpha="255">
                  <red>243</red>
                  <green>243</green>
                  <blue>243</blue>
                 </color>
                </brush>
               </colorrole>
               <colorrole role="ButtonText">
                <brush brushstyle="SolidPattern">
                 <color alpha="255">
                  <red>255</red>
                  <green>0</green>
                  <blue>0</blue>
                 </color>
                </brush>
               </colorrole>
              </active>
              <inactive>
               <colorrole role="ButtonText">
                <brush brushstyle="SolidPattern">
                 <color alpha="255">
                  <red>0</red>
                  <green>0</green>
                  <blue>0</blue>
                 </color>
                </brush>
               </colorrole>
              </inactive>
              <disabled>
               <colorrole role="Button">
                <brush brushstyle="SolidPattern">
                 <color alpha="77">
                  <red>249</red>
                  <green>249</green>
                  <blue>249</blue>
                 </color>
                </brush>
               </colorrole>
               <colorrole role="ButtonText">
                <brush brushstyle="SolidPattern">
                 <color alpha="92">
                  <red>0</red>
                  <green>0</green>
                  <blue>0</blue>
                 </color>
                </brush>
               </colorrole>
              </disabled>
             </palette>
            </property>
            <property name="font">
             <font>
              <italic>true</italic>
              <bold>true</bold>
              <underline>true</underline>
             </font>
            </property>
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="text">
             <string>Forget your password or accout ?</string>
            </property>
            <property name="flat">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="back_connect_putton">
            <property name="text">
             <string>Back</string>
            </property>
            <property name="icon">
             <iconset resource="_imgs/_imgs.qrc">
              <normaloff>:/cancel.png</normaloff>:/cancel.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_16">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Policy::Expanding</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="sign_page">
       <layout class="QVBoxLayout" name="verticalLayout_3" stretch="0,0,0,0,0,0,0,0">
        <item>
         <widget class="QLabel" name="title_label_register">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="font">
           <font>
            <pointsize>11</pointsize>
            <bold>true</bold>
            <underline>false</underline>
            <strikeout>false</strikeout>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color:rgb(181, 181, 181)</string>
          </property>
          <property name="frameShape">
           <enum>QFrame::Shape::Panel</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Shadow::Raised</enum>
          </property>
          <property name="text">
           <string>Create a pyQCat account !</string>
          </property>
          <property name="textFormat">
           <enum>Qt::TextFormat::AutoText</enum>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_4">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Policy::Minimum</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_21" stretch="0,1,2,0">
          <item>
           <spacer name="horizontalSpacer_46">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="user_name_label_register_3">
            <property name="text">
             <string>Group</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="SearchComboBox" name="GroupComboBox"/>
          </item>
          <item>
           <spacer name="horizontalSpacer_47">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_11" stretch="0,1,2,0">
          <item>
           <spacer name="horizontalSpacer_17">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="user_name_label_register">
            <property name="text">
             <string>User Name</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="user_name_lineEdit_register"/>
          </item>
          <item>
           <spacer name="horizontalSpacer_18">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="0,1,2,0">
          <item>
           <spacer name="horizontalSpacer_19">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="pwd_label_register">
            <property name="text">
             <string>Password</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="pwd_lineEdit_register">
            <property name="echoMode">
             <enum>QLineEdit::EchoMode::Password</enum>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_20">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="0,1,2,0">
          <item>
           <spacer name="horizontalSpacer_21">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="rpwd_label_register">
            <property name="text">
             <string>Repeat </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="rpwd_lineEdit">
            <property name="echoMode">
             <enum>QLineEdit::EchoMode::Password</enum>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_22">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="0,1,2,0">
          <item>
           <spacer name="horizontalSpacer_23">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="mail_label_register">
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">image: url(:/label_mail-register.png);</string>
            </property>
            <property name="text">
             <string>E-mail</string>
            </property>
            <property name="textFormat">
             <enum>Qt::TextFormat::AutoText</enum>
            </property>
            <property name="scaledContents">
             <bool>false</bool>
            </property>
            <property name="wordWrap">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="mail_lineEdit_register"/>
          </item>
          <item>
           <spacer name="horizontalSpacer_24">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_12">
          <item>
           <spacer name="horizontalSpacer_25">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Policy::Expanding</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="back_putton">
            <property name="text">
             <string>Back</string>
            </property>
            <property name="icon">
             <iconset resource="_imgs/_imgs.qrc">
              <normaloff>:/cancel.png</normaloff>:/cancel.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="create_account_putton">
            <property name="text">
             <string>Create</string>
            </property>
            <property name="icon">
             <iconset resource="_imgs/_imgs.qrc">
              <normaloff>:/register-an-account.png</normaloff>:/register-an-account.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_26">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Policy::Minimum</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="cache_page">
       <layout class="QHBoxLayout" name="horizontalLayout_23">
        <item>
         <widget class="QTableViewCacheFlag" name="cacheFlagTableView"/>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="find_page">
       <layout class="QGridLayout" name="gridLayout_3">
        <item row="0" column="0">
         <widget class="QLabel" name="title_label_register_2">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="font">
           <font>
            <pointsize>11</pointsize>
            <bold>true</bold>
            <underline>false</underline>
            <strikeout>false</strikeout>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color:rgb(181, 181, 181)</string>
          </property>
          <property name="frameShape">
           <enum>QFrame::Shape::Panel</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Shadow::Raised</enum>
          </property>
          <property name="text">
           <string>Find a pyQCat account !</string>
          </property>
          <property name="textFormat">
           <enum>Qt::TextFormat::AutoText</enum>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <spacer name="verticalSpacer_9">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Policy::Minimum</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>41</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="2" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_16" stretch="0,1,2,0">
          <item>
           <spacer name="horizontalSpacer_42">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="user_name_label_register_2">
            <property name="text">
             <string>User Name</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="user_name_lineEdit_find"/>
          </item>
          <item>
           <spacer name="horizontalSpacer_43">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Policy::Minimum</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item row="3" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_17" stretch="0,1,2,0">
          <item>
           <spacer name="horizontalSpacer_40">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Policy::Expanding</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="pwd_label_register_2">
            <property name="text">
             <string>Pre Password</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="pre_pwd_edit">
            <property name="echoMode">
             <enum>QLineEdit::EchoMode::Password</enum>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_41">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item row="4" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_18" stretch="0,1,2,0">
          <item>
           <spacer name="horizontalSpacer_37">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="mail_label_register_2">
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">image: url(:/label_mail-register.png);</string>
            </property>
            <property name="text">
             <string>E-mail</string>
            </property>
            <property name="textFormat">
             <enum>Qt::TextFormat::AutoText</enum>
            </property>
            <property name="scaledContents">
             <bool>false</bool>
            </property>
            <property name="wordWrap">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="mail_lineEdit_find"/>
          </item>
          <item>
           <spacer name="horizontalSpacer_36">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item row="5" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_20" stretch="0,1,2,0">
          <item>
           <spacer name="horizontalSpacer_44">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>New Password</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="new_pwd_edit"/>
          </item>
          <item>
           <spacer name="horizontalSpacer_45">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item row="6" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_19" stretch="3,1,1,0">
          <item>
           <spacer name="horizontalSpacer_39">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="back_putton_find">
            <property name="text">
             <string>Back</string>
            </property>
            <property name="icon">
             <iconset resource="_imgs/_imgs.qrc">
              <normaloff>:/cancel.png</normaloff>:/cancel.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="rebuild_putton">
            <property name="text">
             <string>Rebuild</string>
            </property>
            <property name="icon">
             <iconset resource="_imgs/_imgs.qrc">
              <normaloff>:/refresh.png</normaloff>:/refresh.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_38">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item>
     <spacer name="horizontalSpacer_13">
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
      <property name="sizeType">
       <enum>QSizePolicy::Policy::Preferred</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>40</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SearchComboBox</class>
   <extends>QComboBox</extends>
   <header>.widgets.combox_custom.combox_search</header>
  </customwidget>
  <customwidget>
   <class>QTableViewCacheFlag</class>
   <extends>QTableView</extends>
   <header>.widgets.context.table_view_cache_flag</header>
  </customwidget>
 </customwidgets>
 <tabstops>
  <tabstop>pwd_linedit_login</tabstop>
  <tabstop>is_remember_box</tabstop>
  <tabstop>login_pushButton</tabstop>
  <tabstop>create_pushButton</tabstop>
  <tabstop>pwd_lineEdit_register</tabstop>
  <tabstop>create_account_putton</tabstop>
  <tabstop>user_name_lineEdit_register</tabstop>
  <tabstop>rpwd_lineEdit</tabstop>
  <tabstop>mail_lineEdit_register</tabstop>
 </tabstops>
 <resources>
  <include location="_imgs/_imgs.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>create_pushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>show_create_account()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>886</x>
     <y>212</y>
    </hint>
    <hint type="destinationlabel">
     <x>1055</x>
     <y>262</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>login_pushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>login_to_system()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>909</x>
     <y>177</y>
    </hint>
    <hint type="destinationlabel">
     <x>1063</x>
     <y>170</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>back_connect_putton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>back_up_page()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>913</x>
     <y>302</y>
    </hint>
    <hint type="destinationlabel">
     <x>539</x>
     <y>167</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>user_name_box</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>choose_user()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>813</x>
     <y>122</y>
    </hint>
    <hint type="destinationlabel">
     <x>810</x>
     <y>-10</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>back_putton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>back_up_page()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>885</x>
     <y>314</y>
    </hint>
    <hint type="destinationlabel">
     <x>539</x>
     <y>167</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>create_account_putton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>create_account()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>966</x>
     <y>314</y>
    </hint>
    <hint type="destinationlabel">
     <x>1048</x>
     <y>253</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>test_connect_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>test_connect()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>773</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>561</x>
     <y>85</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>back_putton_find</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>back_up_page()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>848</x>
     <y>299</y>
    </hint>
    <hint type="destinationlabel">
     <x>539</x>
     <y>167</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>forget_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>forget_password_link()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>778</x>
     <y>302</y>
    </hint>
    <hint type="destinationlabel">
     <x>539</x>
     <y>167</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>rebuild_putton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>find_account()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>933</x>
     <y>298</y>
    </hint>
    <hint type="destinationlabel">
     <x>818</x>
     <y>-11</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>connect_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>take_connect()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>906</x>
     <y>257</y>
    </hint>
    <hint type="destinationlabel">
     <x>504</x>
     <y>-17</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>cache_flag</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>change_cache_flag()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>870</x>
     <y>165</y>
    </hint>
    <hint type="destinationlabel">
     <x>539</x>
     <y>167</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>select_cache_flag</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>selector_cache_flag()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>919</x>
     <y>181</y>
    </hint>
    <hint type="destinationlabel">
     <x>539</x>
     <y>202</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>show_create_account()</slot>
  <slot>login_to_system()</slot>
  <slot>create_account()</slot>
  <slot>test_connect()</slot>
  <slot>back_up_page()</slot>
  <slot>choose_user()</slot>
  <slot>forget_password_link()</slot>
  <slot>find_account()</slot>
  <slot>take_connect()</slot>
  <slot>change_cache_flag()</slot>
  <slot>selector_cache_flag()</slot>
 </slots>
</ui>
