# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/05
# __author:       <PERSON><PERSON><PERSON>

import numpy as np
from ....analysis.standard_curve_analysis import StandardCurveAnalysis
from ....structures import Options
from ....tools import qarange
from ...composite_experiment import CompositeExperiment
from ...single import CPMGExperiment


class CPMGCurveAnalysis(StandardCurveAnalysis):
    
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.a1 = 0.5
        options.x_label = ["N", "N", "N", r"$\omega (MHZ)$ "]
        options.y_label = [r"$\tau (us)$", r"$\omega (MHz)$ ", "An", r"$S(\omega) (MHZ)$"]
        return options
    
    def _data_processing(self):
        y_data = self.experiment_data.y_data
        tau_n1 = y_data["tau"][0]
        omega_n1 = y_data["omega"][0]
        omega = self.experiment_data.y_data["omega"][1:]
        tau = self.experiment_data.y_data["tau"][1:]
        a1 = self.options.a1
        A = ((a1 * np.array(omega) * tau_n1 ** 2) / (omega_n1 * np.array(tau) ** 2))
        S = A / omega
        A = np.insert(A, 0, a1)
        S = np.insert(S, 0, a1 / omega_n1)
        
        self.experiment_data.y_data["A"] = A
        self.experiment_data.y_data["S"] = S
        self.experiment_data.replace_x_data = {"S": self.experiment_data.y_data["omega"]}
        

class CPMGComposite(CompositeExperiment):
    _sub_experiment_class = CPMGExperiment

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("n_list", list)
        options.n_list = qarange(6, 20, 2)
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("a1", float)
        options.a1 = 0.5
        options.raw_data_format = "plot"
        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        if 1 not in self.experiment_options.n_list:
            self.experiment_options.n_list.insert(0, 1)
        self.set_run_options(x_data=self.experiment_options.n_list, analysis_class=CPMGCurveAnalysis)

    def _setup_child_experiment(self, exp: "CPMGExperiment", index: int, n: int):
        """Set child_experiment some options."""
        exp.run_options.index = index
        total = len(self.run_options.x_data)

        exp.set_parent_file(self, f"N={n}dBm", index, total)
        exp.set_experiment_options(N=n)
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp: "CPMGExperiment"):
        exp.analysis.provide_for_parent.update(
            dict(tau=exp.analysis.results.tau.value, omega=exp.analysis.results.omega.value)
        )
