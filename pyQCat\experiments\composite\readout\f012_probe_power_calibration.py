# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/11/11
# __author:       <PERSON><PERSON><PERSON>

"""
Readout power calibrate node composite experiment.
"""

from ....analysis import ReadoutPowerF012CaliAnalysis
from ....structures import Options
from ....tools import qarange
from ...single import SingleShotF02
from .single_shot_composite import SingleShotComposite


class SingleShotF02Composite(SingleShotComposite):
    """Optimize One Field of Qubit."""

    _sub_experiment_class = SingleShotF02


class ReadoutPowerF02Calibrate(SingleShotF02Composite):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            optimize_field (str): Give target optimize field.
            sweep_list (List, np.ndarray): <PERSON>an optimize field list.

        """
        options = super()._default_experiment_options()

        options.set_validator("optimize_field", ["probe_power"])
        options.set_validator("sweep_list", list)

        options.optimize_field = "probe_power"
        options.sweep_list = qarange(-35, -15, 1)

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()

        options.analysis_class = ReadoutPowerF012CaliAnalysis
        return options

    def _set_result_path(self):
        # bugfix: bugfix: Inheritance can cause probe power error updates
        pass
