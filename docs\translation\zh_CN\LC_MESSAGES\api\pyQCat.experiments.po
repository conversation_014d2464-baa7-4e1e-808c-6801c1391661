# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.experiments.rst:2
msgid "pyQCat.experiments package"
msgstr ""

#: ../../source/api/pyQCat.experiments.rst:5
msgid "Module contents"
msgstr ""

#: of pyQCat.experiments:3
msgid "Experiment Library (:mod:`pyQCat.experiments`)"
msgstr ""

#: of pyQCat.experiments:5
msgid "pyQCat Experiments modules."
msgstr ""

#: of pyQCat.experiments:9
msgid "Base Classes"
msgstr ""

#: of pyQCat.experiments:18:<autosummary>:1
msgid ""
":py:obj:`BaseExperiment <pyQCat.experiments.BaseExperiment>`\\ "
"\\(\\[inst\\, qubits\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments:18:<autosummary>:1
msgid "Abstract base class for experiments."
msgstr ""

#: of pyQCat.experiments:18:<autosummary>:1
msgid ""
":py:obj:`CompositeExperiment <pyQCat.experiments.CompositeExperiment>`\\ "
"\\(inst\\, qubits\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments:18:<autosummary>:1
msgid "Composite Experiment base class"
msgstr ""

#: of pyQCat.experiments:18:<autosummary>:1
msgid ""
":py:obj:`TopExperiment <pyQCat.experiments.TopExperiment>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments:18:<autosummary>:1
msgid "Abstract quantum experiment base class."
msgstr ""

#: of pyQCat.experiments:18:<autosummary>:1
msgid ""
":py:obj:`CouplerBaseExperiment "
"<pyQCat.experiments.CouplerBaseExperiment>`\\ \\(inst\\, qubits\\, "
"couplers\\)"
msgstr ""

#: of pyQCat.experiments:20
msgid "Modules"
msgstr ""

#: of pyQCat.experiments:24
msgid ":mod:`~pyQCat.experiments.single`"
msgstr ""

#: of pyQCat.experiments:25
msgid "单循环实验"
msgstr ""

#: of pyQCat.experiments:26
msgid ":mod:`~pyQCat.experiments.composite`"
msgstr ""

#: of pyQCat.experiments:27
msgid "复合实验"
msgstr ""

#~ msgid ""
#~ ":py:obj:`BaseExperiment <pyQCat.experiments.BaseExperiment>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CompositeExperiment "
#~ "<pyQCat.experiments.CompositeExperiment>`\\ \\(inst\\, "
#~ "qubits\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`TopExperiment <pyQCat.experiments.TopExperiment>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CouplerBaseExperiment "
#~ "<pyQCat.experiments.CouplerBaseExperiment>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`BaseExperiment <pyQCat.experiments.BaseExperiment>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CompositeExperiment "
#~ "<pyQCat.experiments.CompositeExperiment>`\\ \\(inst\\, "
#~ "qubits\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`TopExperiment <pyQCat.experiments.TopExperiment>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CouplerBaseExperiment "
#~ "<pyQCat.experiments.CouplerBaseExperiment>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\)"
#~ msgstr ""

