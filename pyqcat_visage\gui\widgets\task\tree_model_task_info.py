# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/08/10
# __author:       <PERSON><PERSON><PERSON>

from typing import TYPE_CHECKING

from PySide6.QtCore import QModelIndex, Qt
from PySide6.QtWidgets import QTreeView, QWidget

from pyqcat_visage.gui.widgets.base.tree_structure import QTreeModelBase

if TYPE_CHECKING:
    from ...main_window import Visa<PERSON><PERSON><PERSON>
    from .tree_view_task_info import QTreeViewTaskInfoWidget
    from .task_manage import DagManagerWindow


class QTreeModelTaskInfo(QTreeModelBase):

    def __init__(self, parent: 'DagManagerWindow', gui: 'VisageG<PERSON>', view: 'QTreeViewTaskInfoWidget'):
        """Editable table with drop-down rows for component options. Organized
        as a tree model where child nodes are more specific properties of a
        given parent node.

        Args:
            parent (QWidget): The parent widget
            gui (MetalGUI): The main user interface
            view (QTreeView): View corresponding to a tree structure
        """
        super().__init__(parent, gui, view)
        self.load()

    @property
    def data_dict(self):
        return self.task_info

    @property
    def task_info(self):
        data = self._parent_widget.task_info
        return data

    @task_info.setter
    def task_info(self, task_info):
        self._parent_widget._task_info = task_info

    def flags(self, index: QModelIndex):
        """Determine how user may interact with each cell in the table.

        Returns:
            list: List of flags
        """
        return Qt.ItemFlag.ItemIsSelectable | Qt.ItemFlag.ItemIsEnabled




