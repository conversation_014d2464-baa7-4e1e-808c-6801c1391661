# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:2
msgid "pyQCat.analysis.CurveAnalysis"
msgstr ""

#: of pyQCat.analysis.curve_analysis.CurveAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.top_analysis.TopAnalysis`"
msgstr ""

#: of pyQCat.analysis.curve_analysis.CurveAnalysis:1
msgid "Abstract superclass of curve analysis base classes."
msgstr "曲线分析类的抽象基类。"

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:1
msgid "Initialize the analysis object."
msgstr "同父类 **TopAnalysis** 的初始化方法"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._extract_result
#: pyQCat.analysis.curve_analysis.CurveAnalysis._guess_fit_param
#: pyQCat.analysis.top_analysis.TopAnalysis.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:4
msgid "pyQCat Experiments Data container class."
msgstr "用于描述实验数据结构的类。"

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:7
msgid "Whether the experiment contains sub-experiments."
msgstr "是否具有子实验的标志位，默认为False。"

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:13
msgid "Methods"
msgstr "方法"

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.CurveAnalysis.__init__>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`from_sub_analysis "
"<pyQCat.analysis.CurveAnalysis.from_sub_analysis>`\\ \\(x\\_data\\, "
"sub\\_analysis\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:25:<autosummary>:1
msgid "This interface is used for composite experiment."
msgstr "该接口用于构建复合实验的分析类"

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`run_analysis <pyQCat.analysis.CurveAnalysis.run_analysis>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.curve_analysis.CurveAnalysis.run_analysis:1
msgid "Run analysis on experiment data."
msgstr "执行对实验数据的分析"

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`set_options <pyQCat.analysis.CurveAnalysis.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:25:<autosummary>:1
msgid "Set the analysis options."
msgstr "设置数据分析的配置选项"

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`show_results <pyQCat.analysis.CurveAnalysis.show_results>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:25:<autosummary>:1
#, fuzzy
msgid "Show analysis results"
msgstr "用于绘制分析结果的曲线画图实例。每个曲线分析类在运行时都会实例化:py:class:`~BaseCurveDrawer` 对象。"

#: ../../source/stubs/analysis/pyQCat.analysis.CurveAnalysis.rst:27
msgid "Attributes"
msgstr "属性"

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`analysis_datas <pyQCat.analysis.CurveAnalysis.analysis_datas>`\\"
msgstr ""

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid "Get the analysis datas."
msgstr "获取分析数据的数据结构，默认为QDict类型"

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`data_filter <pyQCat.analysis.CurveAnalysis.data_filter>`\\"
msgstr ""

#: of pyQCat.analysis.CurveAnalysis.data_filter:1
#: pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
#, fuzzy
msgid "The parameters provided for data filter functions."
msgstr "对实验数据做预处理的函数参数。例如，平滑处理函数的 *window_length* 和 *polyorder* "

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`drawer <pyQCat.analysis.CurveAnalysis.drawer>`\\"
msgstr ""

#: of pyQCat.analysis.CurveAnalysis.drawer:1
#: pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid "A short-cut for curve drawer instance."
msgstr "获取CurveDrawer对象的捷径"

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid ""
":py:obj:`experiment_data "
"<pyQCat.analysis.CurveAnalysis.experiment_data>`\\"
msgstr ""

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid "Return the experiment data for :meth:`run` method."
msgstr "实验数据结构对象"

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`has_child <pyQCat.analysis.CurveAnalysis.has_child>`\\"
msgstr ""

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid "Return the analysis has_child value."
msgstr "返回has_child的值"

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.CurveAnalysis.options>`\\"
msgstr ""

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid "Return the analysis options for :meth:`run` method."
msgstr "返回数据分析的选项参数"

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.CurveAnalysis.quality>`\\"
msgstr ""

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid "Get the analysis data quality."
msgstr "获取分析数据的质量评估结果"

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`results <pyQCat.analysis.CurveAnalysis.results>`\\"
msgstr ""

#: of pyQCat.analysis.CurveAnalysis.drawer:1:<autosummary>:1
msgid "Get the result datas."
msgstr "数据分析类的结果"

#: of pyQCat.analysis.CurveAnalysis.data_filter
#: pyQCat.analysis.CurveAnalysis.drawer
#: pyQCat.analysis.curve_analysis.CurveAnalysis._create_analysis_data
#: pyQCat.analysis.curve_analysis.CurveAnalysis._default_options
#: pyQCat.analysis.curve_analysis.CurveAnalysis._evaluate_quality
#: pyQCat.analysis.curve_analysis.CurveAnalysis._guess_fit_param
#: pyQCat.analysis.curve_analysis.CurveAnalysis._run_fitting
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.CurveAnalysis.drawer:3
msgid ":py:class:`~pyQCat.analysis.visualization.curve_drawer.CurveDrawer`"
msgstr ""

#: of pyQCat.analysis.CurveAnalysis.data_filter:3
msgid ":py:class:`~typing.Dict`"
msgstr ""

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:1
msgid "Return default analysis options."
msgstr "返回默认的分析选项。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:21
msgid "Analysis Options:"
msgstr "分析选项"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:4
msgid "p0 (Dict[str, float]): Initial guesses for the fit parameters."
msgstr "p0 (Dict[str, float]): "

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:5
msgid "The dictionary is keyed on the fit parameter names."
msgstr "拟合参数的初始值猜测。字典的键代表拟合参数的名称，值代表猜测的初始值。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:7
msgid "bounds (Dict[str, Tuple[float, float]]): Boundary of fit parameters."
msgstr "bounds (Dict[str, Tuple[float, float]]): "

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:7
msgid ""
"The dictionary is keyed on the fit parameter names and values are the "
"tuples of (min, max) of each parameter."
msgstr "拟合参数的边界条件。字典的键值是fit参数名，值是每个参数(min, max)的元组。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:9
msgid "curve_fit_extra (Dict[str, Any]) Options that are passed to the"
msgstr "curve_fit_extra (Dict[str, Any]):"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:10
msgid ""
"scipy curve fit which performs the least square fitting on the experiment"
" results."
msgstr ""
"传递给Scipy曲线拟合，对实验结果进行最小二乘拟合的额外参数选项。只要是 *scipy.optimize.curve_fit* "
"接口中的参数均可以由 *curve_fit_extra* 传递"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:11
msgid "curve_drawer (BaseCurveDrawer): A curve drawer instance to visualize"
msgstr "curve_drawer (BaseCurveDrawer): "

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:12
msgid "the analysis result."
msgstr "用于绘制分析结果的曲线画图实例。每个曲线分析类在运行时都会实例化:py:class:`~BaseCurveDrawer` 对象。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:13
msgid "is_plot (bool): Set ``True`` to create figure for fit result."
msgstr "is_plot (bool): "

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:14
msgid "This is ``True`` by default."
msgstr "是否需要绘图的标志位。如果设置成 ``True`` 将会为拟合结果绘制图像。默认是值是 ``True`` 。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:15
msgid "plot_raw_data (bool): Set ``True`` to draw processed data points,"
msgstr "plot_raw_data (bool): "

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:16
msgid "dataset without formatting, on canvas. This is ``False`` by default."
msgstr "是否需要绘制原始数据图的标志位。如果设置成 ``True`` 将绘制没有经过任何处理的原始数据图。默认是值是 ``False`` 。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:17
msgid ""
"plot_style (Dict[str, Any]): A stylesheet for curve analysis figure. "
"plot_iq_data (bool): Set ``True`` to draw processed IQ data points."
msgstr "plot_style (Dict[str, Any]): 用于定义绘图风格的数据结构类"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:19
msgid "This is ``False`` by default."
msgstr "默认值是 ``False`` 。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:20
msgid "quality_bounds (Tuple): Boundary of fit qulity."
msgstr "quality_bounds (Tuple): 质量评估的边界条件。通常将一个曲线拟合的结果分成：完美，正常，不太正常和异常四个层次"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._default_options:23
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._create_analysis_data:1
msgid "Create Analysis data provided for detailed analyze."
msgstr "创建分析数据，提供详细分析。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._create_analysis_data:3
msgid ":py:class:`~pyQCat.structures.QDict`"
msgstr ""

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._create_analysis_data
#: pyQCat.analysis.curve_analysis.CurveAnalysis._evaluate_quality
#: pyQCat.analysis.curve_analysis.CurveAnalysis._guess_fit_param
#: pyQCat.analysis.curve_analysis.CurveAnalysis._run_fitting
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._create_analysis_data:4
msgid ""
"A QDict object, key represents data type and value is CurveAnalysisData "
"object."
msgstr ""
"一个QDict对象，键代表数据类型，值为CurveAnalysisData对象。数据类型包括我们通常熟悉的 *amp* "
"，*phase*，*P0*，*P1* 等。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._guess_fit_param:1
msgid "Create algorithmic guess with analysis options and curve data."
msgstr "使用分析选项和曲线数据创建算法猜测的初始值。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._guess_fit_param:4
msgid "Fit options filled with user provided guess and bounds."
msgstr "FitOptions对象，包含了用户提供的关于拟合参数的初始值以及边界值。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._guess_fit_param:6
msgid "Formatted data collection to fit."
msgstr "CurveAnalysisData对象，存储了格式化后的拟合数据。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._guess_fit_param:8
msgid ""
":py:data:`~typing.Union`\\[:py:class:`~pyQCat.analysis.specification.FitOptions`,"
" "
":py:class:`~typing.List`\\[:py:class:`~pyQCat.analysis.specification.FitOptions`]]"
msgstr ""

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._guess_fit_param:9
msgid "List of fit options that are passed to the fitter function."
msgstr "传递给拟合函数的匹配选项（FitOptions对象）列表。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._evaluate_quality:1
msgid "Evaluates the quality of the fit based on the fit result."
msgstr "根据拟合结果评价拟合质量。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._evaluate_quality:3
msgid ":py:data:`~typing.Tuple`\\[:py:class:`str`, :py:data:`~typing.Any`]"
msgstr ""

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._evaluate_quality:4
msgid "The goodness of fit."
msgstr "拟合优度。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._initialize:1
msgid "Initialize curve analysis with experiment data."
msgstr "用实验数据初始化曲线分析。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._data_filter:1
#, fuzzy
msgid "Pre-processing for raw data."
msgstr "对原始数据进行预处理。例如：平滑，寻峰等。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._run_fitting:1
msgid "Perform curve fitting on given data collection and fit models."
msgstr "对给定的数据进行曲线拟合，并拟合模型。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._run_fitting:3
msgid ":py:class:`bool`"
msgstr ""

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._run_fitting:4
msgid "fitting success or failed."
msgstr "拟合成功或者拟合失败。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._description:1
msgid "A description of the important information in the figure."
msgstr ""
"对绘制结果图中的重要信息进行描述。这些信息将展示在最终的结果图上。这里主要是将 ``metadata`` 的数据信息和拟合结果 "
"``result`` 以及 ``quality`` 中的相关信息进行整合，让用户从图中可以直观获取一些有用的信息。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._visualization:1
msgid ""
"Draw all data, including raw data and fit data and so on. Some composite "
"experiments may need to override this function."
msgstr "可视化分析结果的接口。将从数据分析的结果中获取原始数据和拟合数据，进行绘制。一些复合实验可能需要重写这个函数。用户也可以根据需求重写这个接口。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._extract_result:1
msgid "Extract analysis results from important fit parameters."
msgstr "从拟合参数中提取一些重要的参数信息。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._extract_result:4
msgid "The basis for selecting data."
msgstr "选择要提取的参数的依据。通常默认是根据数据拟合的质量评估来选择。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis.run_analysis:4
msgid "Notes"
msgstr ""

#: of pyQCat.analysis.curve_analysis.CurveAnalysis.run_analysis:5
msgid "Sub analysis classes may need to override this method."
msgstr "继承这个类的子类可能要重写这个方法。"

#: of pyQCat.analysis.curve_analysis.CurveAnalysis._initialize_canvas:1
msgid "Initialize matplotlib canvas."
msgstr "初始化 ``matplotlib`` 画布。"

#~ msgid "Bases: :py:class:`~pyQCat.analysis.top_analysis.TopAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.analysis.CurveAnalysis.__init__>`\\"
#~ " \\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_sub_analysis "
#~ "<pyQCat.analysis.CurveAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_analysis "
#~ "<pyQCat.analysis.CurveAnalysis.run_analysis>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_options "
#~ "<pyQCat.analysis.CurveAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_datas "
#~ "<pyQCat.analysis.CurveAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`data_filter <pyQCat.analysis.CurveAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`drawer <pyQCat.analysis.CurveAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_data "
#~ "<pyQCat.analysis.CurveAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`has_child <pyQCat.analysis.CurveAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`options <pyQCat.analysis.CurveAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`quality <pyQCat.analysis.CurveAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`results <pyQCat.analysis.CurveAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.analysis.top_analysis.TopAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.analysis.CurveAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_sub_analysis "
#~ "<pyQCat.analysis.CurveAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_analysis "
#~ "<pyQCat.analysis.CurveAnalysis.run_analysis>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options <pyQCat.analysis.CurveAnalysis.set_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis_datas <pyQCat.analysis.CurveAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ":obj:`data_filter <pyQCat.analysis.CurveAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid ":obj:`drawer <pyQCat.analysis.CurveAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_data "
#~ "<pyQCat.analysis.CurveAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ":obj:`has_child <pyQCat.analysis.CurveAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.CurveAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.CurveAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":obj:`results <pyQCat.analysis.CurveAnalysis.results>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Create Analysis result data structure to"
#~ " save analysis results. The AnalysisResult"
#~ " object will get more attributes "
#~ "after analysis."
#~ msgstr "创建分析结果数据结构，保存分析结果。AnalysisResult对象在执行完分析过程后将获得更多属性。"

#~ msgid ""
#~ "A QDict object, key represents result"
#~ " type and value is AnalysisResult "
#~ "object."
#~ msgstr "一个QDict对象，键代表结果类型，值为AnalysisResult对象。结果类型来自于:meth:`_create_analysis_data`方法中返回的数据类型。"

#~ msgid "Quality of fit outcome."
#~ msgstr "拟合结果的质量。"

