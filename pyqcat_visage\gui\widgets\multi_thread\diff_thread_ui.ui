<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>883</width>
    <height>346</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QSplitter" name="splitter">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QWidget" name="task_widget" native="true">
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,0,3,1,0,3,1">
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="label">
            <property name="text">
             <string>page</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QSpinBox" name="spinBoxpage">
            <property name="minimum">
             <number>1</number>
            </property>
            <property name="maximum">
             <number>999</number>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_8">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>volume</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QSpinBox" name="spinBoxVolume">
            <property name="minimum">
             <number>5</number>
            </property>
            <property name="maximum">
             <number>40</number>
            </property>
            <property name="value">
             <number>8</number>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_7">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <widget class="NearTaskView" name="tableView"/>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="diff_widget" native="true">
       <layout class="QVBoxLayout" name="verticalLayout_2" stretch="2,1,4">
        <item>
         <layout class="QGridLayout" name="gridLayout_2" columnstretch="2,1,10" columnminimumwidth="0,0,0">
          <item row="1" column="2">
           <widget class="QLineEdit" name="task2_input"/>
          </item>
          <item row="0" column="2">
           <widget class="QLineEdit" name="task1_input"/>
          </item>
          <item row="1" column="1">
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="lb_task1">
            <property name="text">
             <string>task1</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="lb_task2">
            <property name="text">
             <string>task2</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,4,1,2,1">
          <item>
           <spacer name="horizontalSpacer_6">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="diff_btn">
            <property name="text">
             <string>diff</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="diff_status">
            <property name="font">
             <font>
              <pointsize>13</pointsize>
              <italic>false</italic>
              <bold>true</bold>
              <underline>false</underline>
              <strikeout>false</strikeout>
              <kerning>true</kerning>
             </font>
            </property>
            <property name="text">
             <string>O</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_5">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QTextEdit" name="diff_text">
          <property name="lineWrapMode">
           <enum>QTextEdit::WidgetWidth</enum>
          </property>
          <property name="readOnly">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>883</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>NearTaskView</class>
   <extends>QTableView</extends>
   <header>.near_task_view</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>spinBoxpage</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>query_task_list()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>110</x>
     <y>51</y>
    </hint>
    <hint type="destinationlabel">
     <x>319</x>
     <y>179</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBoxVolume</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>query_task_list()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>250</x>
     <y>51</y>
    </hint>
    <hint type="destinationlabel">
     <x>319</x>
     <y>179</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>diff_btn</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>query_task_diff()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>425</x>
     <y>141</y>
    </hint>
    <hint type="destinationlabel">
     <x>319</x>
     <y>179</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>query_task_list()</slot>
  <slot>query_task_diff()</slot>
 </slots>
</ui>
