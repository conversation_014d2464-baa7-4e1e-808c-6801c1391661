# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'online_update_ui.ui'
##
## Created by: Qt User Interface Compiler version 6.8.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QB<PERSON>, QColor, QC<PERSON>alGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QCheckBox, QComboBox, QGridLayout,
    QGroupBox, QHBoxLayout, QLabel, QMainWindow,
    QPushButton, QSizePolicy, QSpacerItem, QVBoxLayout,
    QWidget)

from .widgets.combox_custom.combox_multi import QMultiComboBox

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(826, 568)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout_2 = QVBoxLayout(self.centralwidget)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.groupBox = QGroupBox(self.centralwidget)
        self.groupBox.setObjectName(u"groupBox")
        self.gridLayout = QGridLayout(self.groupBox)
        self.gridLayout.setObjectName(u"gridLayout")
        self.widget_4 = QWidget(self.groupBox)
        self.widget_4.setObjectName(u"widget_4")
        self.horizontalLayout_4 = QHBoxLayout(self.widget_4)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.label_4 = QLabel(self.widget_4)
        self.label_4.setObjectName(u"label_4")

        self.horizontalLayout_4.addWidget(self.label_4)

        self.OptBox = QMultiComboBox(self.widget_4)
        self.OptBox.setObjectName(u"OptBox")

        self.horizontalLayout_4.addWidget(self.OptBox)

        self.horizontalLayout_4.setStretch(0, 3)
        self.horizontalLayout_4.setStretch(1, 16)

        self.gridLayout.addWidget(self.widget_4, 3, 0, 1, 1)

        self.widget_7 = QWidget(self.groupBox)
        self.widget_7.setObjectName(u"widget_7")
        self.horizontalLayout_7 = QHBoxLayout(self.widget_7)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.label_7 = QLabel(self.widget_7)
        self.label_7.setObjectName(u"label_7")

        self.horizontalLayout_7.addWidget(self.label_7)

        self.WorkingTypeBox = QComboBox(self.widget_7)
        self.WorkingTypeBox.setObjectName(u"WorkingTypeBox")

        self.horizontalLayout_7.addWidget(self.WorkingTypeBox)

        self.horizontalLayout_7.setStretch(0, 3)
        self.horizontalLayout_7.setStretch(1, 16)

        self.gridLayout.addWidget(self.widget_7, 6, 0, 1, 1)

        self.widget_8 = QWidget(self.groupBox)
        self.widget_8.setObjectName(u"widget_8")
        self.horizontalLayout_8 = QHBoxLayout(self.widget_8)
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.label_8 = QLabel(self.widget_8)
        self.label_8.setObjectName(u"label_8")

        self.horizontalLayout_8.addWidget(self.label_8)

        self.DivideTypeBox = QComboBox(self.widget_8)
        self.DivideTypeBox.setObjectName(u"DivideTypeBox")

        self.horizontalLayout_8.addWidget(self.DivideTypeBox)

        self.horizontalLayout_8.setStretch(0, 3)
        self.horizontalLayout_8.setStretch(1, 16)

        self.gridLayout.addWidget(self.widget_8, 7, 0, 1, 1)

        self.widget_u = QWidget(self.groupBox)
        self.widget_u.setObjectName(u"widget_u")
        self.horizontalLayout_u = QHBoxLayout(self.widget_u)
        self.horizontalLayout_u.setObjectName(u"horizontalLayout_u")
        self.horizontalSpacer_1 = QSpacerItem(249, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_u.addItem(self.horizontalSpacer_1)

        self.UpdateButton = QPushButton(self.widget_u)
        self.UpdateButton.setObjectName(u"UpdateButton")

        self.horizontalLayout_u.addWidget(self.UpdateButton)

        self.horizontalSpacer_2 = QSpacerItem(249, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_u.addItem(self.horizontalSpacer_2)


        self.gridLayout.addWidget(self.widget_u, 10, 0, 1, 1)

        self.widget_5 = QWidget(self.groupBox)
        self.widget_5.setObjectName(u"widget_5")
        self.horizontalLayout_5 = QHBoxLayout(self.widget_5)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.label_5 = QLabel(self.widget_5)
        self.label_5.setObjectName(u"label_5")

        self.horizontalLayout_5.addWidget(self.label_5)

        self.OnlineBitlBox = QMultiComboBox(self.widget_5)
        self.OnlineBitlBox.setObjectName(u"OnlineBitlBox")

        self.horizontalLayout_5.addWidget(self.OnlineBitlBox)

        self.horizontalLayout_5.setStretch(0, 3)
        self.horizontalLayout_5.setStretch(1, 16)

        self.gridLayout.addWidget(self.widget_5, 4, 0, 1, 1)

        self.widget_3 = QWidget(self.groupBox)
        self.widget_3.setObjectName(u"widget_3")
        self.horizontalLayout_3 = QHBoxLayout(self.widget_3)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.label_3 = QLabel(self.widget_3)
        self.label_3.setObjectName(u"label_3")

        self.horizontalLayout_3.addWidget(self.label_3)

        self.MaxUnitsBox = QMultiComboBox(self.widget_3)
        self.MaxUnitsBox.setObjectName(u"MaxUnitsBox")

        self.horizontalLayout_3.addWidget(self.MaxUnitsBox)

        self.horizontalLayout_3.setStretch(0, 3)
        self.horizontalLayout_3.setStretch(1, 16)

        self.gridLayout.addWidget(self.widget_3, 2, 0, 1, 1)

        self.verticalSpacer_1 = QSpacerItem(20, 0, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.gridLayout.addItem(self.verticalSpacer_1, 9, 0, 1, 1)

        self.widget_6 = QWidget(self.groupBox)
        self.widget_6.setObjectName(u"widget_6")
        self.horizontalLayout_6 = QHBoxLayout(self.widget_6)
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.horizontalLayout_6.setContentsMargins(-1, -1, 9, -1)
        self.label_6 = QLabel(self.widget_6)
        self.label_6.setObjectName(u"label_6")

        self.horizontalLayout_6.addWidget(self.label_6)

        self.OnlinePairBox = QMultiComboBox(self.widget_6)
        self.OnlinePairBox.setObjectName(u"OnlinePairBox")

        self.horizontalLayout_6.addWidget(self.OnlinePairBox)

        self.horizontalLayout_6.setStretch(0, 3)
        self.horizontalLayout_6.setStretch(1, 16)

        self.gridLayout.addWidget(self.widget_6, 5, 0, 1, 1)

        self.widget_1 = QWidget(self.groupBox)
        self.widget_1.setObjectName(u"widget_1")
        self.horizontalLayout_1 = QHBoxLayout(self.widget_1)
        self.horizontalLayout_1.setObjectName(u"horizontalLayout_1")
        self.label_1 = QLabel(self.widget_1)
        self.label_1.setObjectName(u"label_1")

        self.horizontalLayout_1.addWidget(self.label_1)

        self.PointLabelBox = QComboBox(self.widget_1)
        self.PointLabelBox.setObjectName(u"PointLabelBox")

        self.horizontalLayout_1.addWidget(self.PointLabelBox)

        self.horizontalLayout_1.setStretch(0, 3)
        self.horizontalLayout_1.setStretch(1, 16)

        self.gridLayout.addWidget(self.widget_1, 0, 0, 1, 1)

        self.verticalSpacer_2 = QSpacerItem(20, 0, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.gridLayout.addItem(self.verticalSpacer_2, 11, 0, 1, 1)

        self.widget_2 = QWidget(self.groupBox)
        self.widget_2.setObjectName(u"widget_2")
        self.horizontalLayout_2 = QHBoxLayout(self.widget_2)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.label_2 = QLabel(self.widget_2)
        self.label_2.setObjectName(u"label_2")

        self.horizontalLayout_2.addWidget(self.label_2)

        self.EnvBox = QMultiComboBox(self.widget_2)
        self.EnvBox.setObjectName(u"EnvBox")
        self.EnvBox.setEnabled(True)

        self.horizontalLayout_2.addWidget(self.EnvBox)

        self.horizontalLayout_2.setStretch(0, 3)
        self.horizontalLayout_2.setStretch(1, 16)

        self.gridLayout.addWidget(self.widget_2, 1, 0, 1, 1)

        self.widget_c = QWidget(self.groupBox)
        self.widget_c.setObjectName(u"widget_c")
        self.horizontalLayout_c = QHBoxLayout(self.widget_c)
        self.horizontalLayout_c.setObjectName(u"horizontalLayout_c")
        self.OnlineCheck = QCheckBox(self.widget_c)
        self.OnlineCheck.setObjectName(u"OnlineCheck")

        self.horizontalLayout_c.addWidget(self.OnlineCheck)

        self.CrosstalkCheck = QCheckBox(self.widget_c)
        self.CrosstalkCheck.setObjectName(u"CrosstalkCheck")
        self.CrosstalkCheck.setChecked(True)

        self.horizontalLayout_c.addWidget(self.CrosstalkCheck)

        self.XYCrosstalkCheck = QCheckBox(self.widget_c)
        self.XYCrosstalkCheck.setObjectName(u"XYCrosstalkCheck")
        self.XYCrosstalkCheck.setChecked(True)

        self.horizontalLayout_c.addWidget(self.XYCrosstalkCheck)


        self.gridLayout.addWidget(self.widget_c, 8, 0, 1, 1)


        self.verticalLayout_2.addWidget(self.groupBox)

        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)
        self.UpdateButton.clicked.connect(MainWindow.update_online_params)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"OnlineUpdate", None))
        self.groupBox.setTitle(QCoreApplication.translate("MainWindow", u"Update Online One PointLabel Parameters", None))
        self.label_4.setText(QCoreApplication.translate("MainWindow", u"f12_opt_bits", None))
        self.label_7.setText(QCoreApplication.translate("MainWindow", u"working_type", None))
        self.label_8.setText(QCoreApplication.translate("MainWindow", u"divide_type", None))
        self.UpdateButton.setText(QCoreApplication.translate("MainWindow", u"Update", None))
        self.label_5.setText(QCoreApplication.translate("MainWindow", u"online_bits", None))
        self.label_3.setText(QCoreApplication.translate("MainWindow", u"max_point_units", None))
        self.label_6.setText(QCoreApplication.translate("MainWindow", u"online_pairs", None))
        self.label_1.setText(QCoreApplication.translate("MainWindow", u"point_label", None))
        self.label_2.setText(QCoreApplication.translate("MainWindow", u"env_bits", None))
        self.OnlineCheck.setText(QCoreApplication.translate("MainWindow", u"online", None))
        self.CrosstalkCheck.setText(QCoreApplication.translate("MainWindow", u"crosstalk", None))
        self.XYCrosstalkCheck.setText(QCoreApplication.translate("MainWindow", u"xy_crosstalk", None))
    # retranslateUi

