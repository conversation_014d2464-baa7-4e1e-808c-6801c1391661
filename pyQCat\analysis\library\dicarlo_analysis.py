# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/12/21
# __author:       xw

"""
"""
import numpy as np
import matplotlib.pyplot as plt

from scipy.fftpack import fft
from scipy.signal import savgol_filter
from sklearn.metrics import mean_squared_error

from ..curve_analysis import CurveAnalysis
from ..fit.fit_models import linear_func, fitdata
from ..quality import BaseQuality
from ...qaio_property import QAIO
from ...structures import Options, QDict
from ..specification import ParameterRepr, CurveAnalysisData
from ...log import pyqlog
from ...types import QualityDescribe
from ...analysis.algorithms.smooth import smooth3rd


class DicarloAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.subplots = (2, 2)
        options.x_label = ["sigma_x", "Frequency (MHz)", "width (ns)", "width (ns)"]
        options.y_label = ["sigma_y", "Probability", "Frequency (MHz)", "Phase (rad)"]

        options.pcolormesh_options = {"cmap": plt.cm.get_cmap("viridis")}
        options.savgol_win_length = 21
        options.smooth_win_length = 11
        options.freq_switch = False
        options.amp2freq_func = None
        options.freq2amp_func = None
        options.fringe = None
        options.drive_freq = None
        options.cali_offset = True
        options.start_index = None
        options.result_parameters = []

        return options

    def _create_analysis_data(self) -> QDict:
        analysis_data_dict = QDict()
        self._quality = BaseQuality()
        sigma_x = self.experiment_data.metadata.process_meta.get("sigma_x")
        sigma_y = self.experiment_data.metadata.process_meta.get("sigma_y")
        # width_list = analysis_datas["width_list"][0]
        width_list = self.experiment_data.metadata.process_meta.get("width_list")

        N = len(sigma_x)
        N_FFT = 2 * N
        dt = width_list[1] - width_list[0]

        # Calculate the sampling rate 'FS' of  experiment according to the width_list (equal interval).
        Fs = 1 / abs(dt) * 1e3
        # Calculate FFT frequency list.
        f = Fs / 2 * np.linspace(0, 1, N + 1)

        x_f = fft(sigma_x, N_FFT)
        # Take the FFT single side band, and do the normalization.
        x_f_ss = x_f[0 : N + 1] / N
        x_f_ss[0 : N + 1] = 2 * x_f_ss[0 : N + 1]
        x_f_amp = np.abs(x_f_ss[0 : N + 1])

        index = int(np.argmax(x_f_amp[5:]) + 5)
        self._quality.descriptor = QualityDescribe.perfect
        if x_f_amp[index] < 0.5:  # 震荡很差
            self._quality.descriptor = QualityDescribe.bad
        fosci = f[index]
        if self.options.freq_switch:
            fosci = abs(self.options.freq_switch * Fs + fosci)

        fringe = self.options.fringe
        z_amp = self.options.z_amp
        func = self.options.amp2freq_func
        fd = self.options.drive_freq
        expected_detune = fd - func(z_amp)

        if QAIO.type in [QAIO.qaio_8, QAIO.qaio_30]:
            sign = -1
        else:
            sign = 1

        expected_fosci = expected_detune + fringe * sign
        if expected_fosci < 0:
            fosci = -abs(fosci)
        detune = fosci - fringe * sign  ## detune

        pyqlog.info(
            f"fft_IF:{fosci}, detune:{detune},fringe:{self.options.fringe}, expect detune:{fd - func(z_amp)}\n"
        )
        # Calculate phase by sigma_x and sigma_y.
        phase = np.angle(sigma_x + 1j * sigma_y * sign)

        # Phase down convertion.
        # Subtract a phase that oscillates with the fosci frequency.
        down_conversion_phase = 2 * np.pi * fosci * 1e-3 * width_list
        delta_phase = phase - down_conversion_phase
        extend_phase = np.unwrap(delta_phase, np.pi)
        # Calculate differential phase by using savgol filter.
        # The order of the polynomial used to fit the samples is 2.
        dphi = savgol_filter(
            extend_phase, self.options.savgol_win_length, 2, deriv=1, mode="nearest"
        )
        detune_list = detune + dphi / dt / 2 / np.pi * 1e3
        smooth_length = self.options.smooth_win_length
        smooth_detune_list = smooth3rd(detune_list, smooth_length)
        # Calculate amp by ac spectrum params.
        # Get the amplitude response.
        z_amp = self.options.z_amp
        z_amp_list = self.options.freq2amp_func(self.options.drive_freq - detune_list)
        smooth_z_amp_list = self.options.freq2amp_func(
            self.options.drive_freq - smooth_detune_list
        )

        if self.options.cali_offset:
            normalize_amp = self.options.freq2amp_func(self.options.drive_freq - detune)
        else:
            normalize_amp = z_amp

        amp_response_list = smooth_z_amp_list / normalize_amp

        analysis_data_dict["f"] = f
        analysis_data_dict["phi"] = extend_phase
        analysis_data_dict["x_f_amp"] = x_f_amp
        analysis_data_dict["detune"] = detune
        analysis_data_dict["z_amp_list"] = z_amp_list
        analysis_data_dict["smooth_z_amp_list"] = smooth_z_amp_list
        analysis_data_dict["detune_list"] = detune_list
        analysis_data_dict["smooth_detune_list"] = smooth_detune_list
        analysis_data_dict["amp_response_list"] = amp_response_list

        start_index = self.options.start_index
        new_width_list = width_list[start_index:] - width_list[start_index]
        new_amp_response_list = amp_response_list[start_index:]
        index_list = np.logical_and(
            new_amp_response_list < 1.3, new_amp_response_list > 0.7
        )
        abnormal_count = len(new_amp_response_list) - np.sum(index_list)
        new_width_list = new_width_list[index_list]
        if abnormal_count > 3 or new_width_list[0] != 0:
            # analysis_data_dict['quality'] = 'bad'
            self._quality.descriptor = QualityDescribe.bad

        return analysis_data_dict

    def _initialize_canvas(self):
        """Initialize matplotlib canvas."""
        self.drawer.set_options(
            subplots=self.options.get("subplots"),
            xlabel=self.options.get("x_label"),
            ylabel=self.options.get("y_label"),
            sub_title=self.options.sub_title,
            figsize=self.options.figsize,
            raw_data_format=self.options.get("raw_data_format"),
        )
        self.drawer.initialize_canvas()

    def _visualization(self):
        self.drawer.set_options(title=self._description())
        self.drawer.set_options(raw_data_format="plot")
        draw_ops = {
            "markersize": 5,
            "marker": "o",
            "alpha": 0.8,
            "label": f"rate",
            "linewidth": 2.5,
            "color": None,
        }

        analysis_datas = self.experiment_data.y_data
        width_list = self.experiment_data.metadata.process_meta.get("width_list")
        base_index = 0
        sigma_x = self.experiment_data.metadata.process_meta.get("sigma_x")
        sigma_y = self.experiment_data.metadata.process_meta.get("sigma_y")

        if self.options.plot_raw_data:
            # plot scatter xy points
            self.options.pcolormesh_options.update({"c": width_list, "s": 20})
            self.drawer.draw_xy_point(
                x_data=sigma_x,
                y_data=sigma_y,
                ax_index=base_index,
                **self.options.pcolormesh_options,
            )

            # plot and save fft result.
            draw_ops.update(
                {"linewidth": 2.5, "marker": None, "color": None, "label": "ideal"}
            )

            base_index += 1
            self.drawer.draw_raw_data(
                x_data=self.analysis_datas["f"],
                y_data=self.analysis_datas["x_f_amp"],
                ax_index=base_index,
                **draw_ops,
            )

            # plot and save time-freq.
            line_label = ["raw-detune", "smooth-detune"]
            y_datas = [
                self.analysis_datas["detune_list"],
                self.analysis_datas["smooth_detune_list"],
            ]
            base_index += 1
            for i, y_data in enumerate(y_datas):
                draw_ops.update(
                    {
                        "linewidth": 2.5,
                        "marker": None,
                        "color": None,
                        "label": line_label[i],
                    }
                )
                self.drawer.draw_raw_data(
                    x_data=width_list, y_data=y_data, ax_index=base_index, **draw_ops
                )
        draw_ops.update({"label": "phase"})
        base_index += 1
        self.drawer.draw_raw_data(
            x_data=width_list,
            y_data=self.analysis_datas["phi"],
            ax_index=base_index,
            **draw_ops,
        )

        self.drawer.format_canvas()


class Dicarlo2Analysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.subplots = (2, 2)
        options.x_label = ["width (ns)", "width (ns)", "Time [ns]", "Time [ns]"]
        options.y_label = [
            "Amplitude (V)",
            "Smooth Amplitude (V)",
            "Normalized Zbias",
            "Normalized response",
        ]

        options.pcolormesh_options = {"cmap": plt.cm.get_cmap("viridis")}
        options.sigma_x = None
        options.sigma_y = None
        options.savgol_win_length = 5
        options.smooth_win_length = 0.08
        options.freq_switch = False
        options.result_parameters = [
            ParameterRepr(name="max_error", repr="", unit="", param_path=""),
            ParameterRepr(name="fir_length", repr="", unit="", param_path=""),
        ]
        options.sample_rate = 1.2

        options.time_point = 60
        options.avg_window = 5
        options.target_value = 1
        options.before_time = 10
        options.percentage = 0.01
        options.threshold = 0.01
        options.cal_fir_length = True
        options.k = 1.2

        return options

    def _create_analysis_data(self) -> QDict:
        """Create Analysis data provided for detailed analyze.

        Returns:
            A QDict object, key represents data type and value is
            CurveAnalysisData object.
        """
        analysis_data_dict = QDict()
        analysis_data_dict["z_amp_list"] = (
            self.experiment_data.metadata.process_meta.get("z_amp_list")
        )
        analysis_data_dict["smooth_z_amp_list"] = (
            self.experiment_data.metadata.process_meta.get("smooth_z_amp_list")
        )
        analysis_data_dict["amp_response_list"] = (
            self.experiment_data.metadata.process_meta.get("amp_response_list")
        )
        return analysis_data_dict

    def _cal_fir_length(self, y_data, width_list):
        sample_rate = self.options.sample_rate
        ponit_num = int(20 * sample_rate)

        y_data = y_data[:-ponit_num]
        width_list = width_list[:-ponit_num]
        a = np.mean(y_data[-ponit_num:])
        d_to_mean = np.abs(y_data - a)
        alpha = d_to_mean[-ponit_num:].max()
        k = self.options.k
        max_threshold = max([k * alpha, 0.003])
        indices_greater_than_threshold = np.where(
            d_to_mean[:-ponit_num] > max_threshold
        )[0]
        if len(indices_greater_than_threshold) > 0:
            max_time_index = indices_greater_than_threshold[
                np.argmax(width_list[indices_greater_than_threshold])
            ]
            max_time = width_list[max_time_index]
            self.results.fir_length.value = max_time
        else:
            pyqlog.error(
                f"Can not find fir length! fir_length use default value 30 ns!"
            )
            self.results.fir_length.value = 30

    def _check_convergence(self, width_list, y_data):

        time_point = self.options.time_point
        avg_window = self.options.avg_window
        target_value = self.options.target_value
        threshold = self.options.threshold
        before_time = self.options.before_time
        percentage = self.options.percentage

        # 计算时间点之后的数据的移动平均
        index = np.where(width_list > time_point)
        after_time_data = y_data[index]

        after_time_avg = np.mean(after_time_data[-avg_window:])

        # 检查时间点之后的收敛情况
        after_time_converged = abs(after_time_avg - target_value) <= threshold

        # 检查时间点之前的全程收敛情况
        before_index = np.where(width_list < before_time)
        before_time_data = y_data[before_index]
        lower_bound = target_value * (1 - percentage)
        upper_bound = target_value * (1 + percentage)
        before_time_converged = all(
            lower_bound <= value <= upper_bound for value in before_time_data
        )

        return after_time_converged, before_time_converged

    def _check_convergence_v2(self, width_list, y_data):
        target_value = self.options.target_value
        percentage = self.options.percentage
        sample_rate = self.options.sample_rate
        ponit_num = int(20 * sample_rate)

        aft_10_idx = np.where(width_list > 10)

        aft_10_y_data = y_data[aft_10_idx]

        m_data = aft_10_y_data[:-ponit_num]
        max_y = np.max(m_data)
        min_y = np.min(m_data)

        lower_bound = target_value * (1 - percentage)
        upper_bound = target_value * (1 + percentage)
        if min_y > lower_bound and max_y < upper_bound:
            after_converged, before_converged = True, True
        else:
            after_converged, before_converged = False, False
        pyqlog.info(
            f"min_y={min_y}, lower_bound={lower_bound}, max_y={max_y}, upper_bound={upper_bound}"
        )
        return after_converged, before_converged

    def _extract_result(self, data_key: str):
        width_list = self.experiment_data.metadata.process_meta.get("width_list")
        y_data = self.analysis_datas["amp_response_list"]
        self._quality = BaseQuality()
        if self.options.cal_fir_length:
            self._cal_fir_length(y_data, width_list)
        after_converged, before_converged = self._check_convergence_v2(
            width_list, y_data
        )

        if after_converged and before_converged:
            self._quality.descriptor = QualityDescribe.perfect
        else:
            self._quality.descriptor = QualityDescribe.bad

    def _visualization(self):
        self.drawer.set_options(title=self._description())
        self.drawer.set_options(raw_data_format="plot")
        draw_ops = {
            "markersize": 5,
            "marker": "o",
            "alpha": 0.8,
            "label": "rate",
            "linewidth": 2.5,
            "color": None,
        }

        width_list = self.experiment_data.metadata.process_meta.get("width_list")
        base_index = 0

        if self.options.plot_raw_data:
            # plot scatter xy points
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=self.analysis_datas["z_amp_list"],
                ax_index=base_index,
                **draw_ops,
            )
            base_index += 1
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=self.analysis_datas["smooth_z_amp_list"],
                ax_index=base_index,
                **draw_ops,
            )

            base_index += 1
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=self.analysis_datas["amp_response_list"],
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops = {"linewidth": 0.5, "color": "b", "marker": None, "label": "0.01"}
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=1.01 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=0.99 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops = {
                "linewidth": 0.2,
                "color": "g",
                "marker": None,
                "label": "0.005",
            }
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=1.005 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=0.995 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )

            ## start_index
            start_index = self.options.start_index
            base_index += 1
            draw_ops = {
                "markersize": 3,
                "marker": "o",
                "alpha": 0.8,
                "label": None,
                "linewidth": 1,
                "color": None,
            }
            self.drawer.draw_raw_data(
                x_data=width_list[start_index:],
                y_data=self.analysis_datas["amp_response_list"][start_index:],
                ax_index=base_index,
                **draw_ops,
            )

            draw_ops = {"linewidth": 0.5, "color": "b", "marker": None, "label": "0.01"}
            width_list = width_list[start_index:]
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=1.01 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=0.99 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops = {
                "linewidth": 0.2,
                "color": "g",
                "marker": None,
                "label": "0.005",
            }
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=1.005 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=0.995 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
        self.drawer.format_canvas()


class AvgDicarloAnalysis(Dicarlo2Analysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.subplots = (1, 1)
        options.x_label = ["width (ns)"]
        options.y_label = [
            "Avg Amplitude (V)",
        ]

        options.pcolormesh_options = {"cmap": plt.cm.get_cmap("viridis")}
        options.result_parameters = [
            ParameterRepr(name="max_error", repr="", unit="", param_path=""),
            ParameterRepr(name="fir_length", repr="", unit="", param_path=""),
        ]
        options.sample_rate = 1.2

        options.time_point = 60
        options.avg_window = 5
        options.target_value = 1
        options.before_time = 10
        options.percentage = 0.01
        options.threshold = 0.01
        options.cal_fir_length = True
        options.k = 1.2

        return options

    def _create_analysis_data(self) -> QDict:
        """Create Analysis data provided for detailed analyze.

        Returns:
            A QDict object, key represents data type and value is
            CurveAnalysisData object.
        """
        analysis_data_dict = QDict()
        x_data = self.experiment_data.metadata.process_meta.get("width_list")
        y_data = self.experiment_data.metadata.process_meta.get("amp_avg_list")

        analysis_data = CurveAnalysisData(
            x=np.copy(x_data),
            y=np.copy(y_data),
        )
        analysis_data_dict["amp_avg_list"] = analysis_data
        return analysis_data_dict

    def _cal_fir_length(self, y_data, width_list):
        sample_rate = self.options.sample_rate
        ponit_num = int(20 * sample_rate)

        y_data = y_data[:-ponit_num]
        width_list = width_list[:-ponit_num]
        a = np.mean(y_data[-ponit_num:])
        d_to_mean = np.abs(y_data - a)
        alpha = d_to_mean[-ponit_num:].max()
        k = self.options.k
        max_threshold = max([k * alpha, 0.003])
        indices_greater_than_threshold = np.where(
            d_to_mean[:-ponit_num] > max_threshold
        )[0]
        if len(indices_greater_than_threshold) > 0:
            max_time_index = indices_greater_than_threshold[
                np.argmax(width_list[indices_greater_than_threshold])
            ]
            max_time = width_list[max_time_index]
            self.results.fir_length.value = max_time
        else:
            pyqlog.error(
                f"Can not find fir length! fir_length use default value 30 ns!"
            )
            self.results.fir_length.value = 30

    def _extract_result(self, data_key: str):
        width_list = self.experiment_data.metadata.process_meta.get("width_list")
        y_data = self.experiment_data.metadata.process_meta.get("amp_avg_list")
        self._quality = BaseQuality()
        if self.options.cal_fir_length:
            self._cal_fir_length(y_data, width_list)
        after_converged, before_converged = self._check_convergence_v2(width_list, y_data)

        if after_converged and before_converged:
            self._quality.descriptor = QualityDescribe.perfect
        else:
            self._quality.descriptor = QualityDescribe.bad

    def _visualization(self):
        self.drawer.set_options(title=self._description())
        self.drawer.set_options(raw_data_format="plot")
        draw_ops = {
            "markersize": 5,
            "marker": "o",
            "alpha": 0.8,
            "label": "rate",
            "linewidth": 2.5,
            "color": None,
        }

        width_list = self.experiment_data.metadata.process_meta.get("width_list")
        base_index = 0

        if self.options.plot_raw_data:
            # plot scatter xy points
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=self.experiment_data.metadata.process_meta.get("amp_avg_list"),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops = {"linewidth": 0.5, "color": "b", "marker": None, "label": "0.01"}
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=1.01 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=0.99 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops = {
                "linewidth": 0.2,
                "color": "g",
                "marker": None,
                "label": "0.005",
            }
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=1.005 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=0.995 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
        self.drawer.format_canvas()


class OldDicarloAnalysis(DicarloAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.subplots = (2, 2)
        options.x_label = ["width (ns)", "width (ns)", "Time [ns]", "Time [ns]"]
        options.y_label = [
            "Amplitude (V)",
            "Smooth Amplitude (V)",
            "Normalized Zbias",
            "Normalized response",
        ]

        options.pcolormesh_options = {"cmap": plt.cm.get_cmap("viridis")}
        options.sigma_x = None
        options.sigma_y = None
        options.savgol_win_length = 5
        options.smooth_win_length = 0.08
        options.freq_switch = False
        options.result_parameters = [
            ParameterRepr(name="max_error", repr="", unit="", param_path=""),
            ParameterRepr(name="fir_length", repr="", unit="", param_path=""),
        ]
        options.sample_rate = 1.2
        options.error_threshold = 1 / 100
        options.k = 1.2

        return options

    def _extract_result(self, data_key: str):
        analysis_datas = self.experiment_data.y_data
        width_list = analysis_datas["width_list"][0]
        y_data = self.analysis_datas["amp_response_list"]
        num = 30
        sample_rate = self.options.sample_rate
        ponit_num = int(20 * sample_rate)
        af_10_ponit_num = int(10 * sample_rate)
        last_time_ponit_num = int(num * sample_rate)

        y_data = y_data[:-ponit_num]
        width_list = width_list[:-ponit_num]

        last_time_y_data = y_data[-last_time_ponit_num:]
        expected_time_y_data = np.ones(last_time_ponit_num)

        last_time_errors = np.abs(last_time_y_data - expected_time_y_data)
        last_time_max_error = last_time_errors.max()

        pyqlog.info(f"lase {num} points max error={last_time_max_error}")
        af_10_y_data = y_data[af_10_ponit_num:]
        expected_af_10_data = np.ones(len(af_10_y_data))
        af_10_errors = np.abs(af_10_y_data - expected_af_10_data)
        af_10_max_error = af_10_errors.max()
        self.results.max_error.value = af_10_max_error
        self._quality = BaseQuality()
        if last_time_max_error <= self.options.error_threshold:
            a = np.mean(y_data[-ponit_num:])
            d_to_mean = np.abs(y_data - a)
            alpha = d_to_mean[-ponit_num:].max()
            k = self.options.k
            max_threshold = max([k * alpha, 0.003])
            indices_greater_than_threshold = np.where(
                d_to_mean[:-ponit_num] > max_threshold
            )[0]
            if len(indices_greater_than_threshold) > 0:
                max_time_index = indices_greater_than_threshold[
                    np.argmax(width_list[indices_greater_than_threshold])
                ]
                max_time = width_list[max_time_index]
                self.results.fir_length.value = max_time
                self._quality.descriptor = QualityDescribe.perfect
            else:
                pyqlog.error(
                    f"Can not find fir length! indices_greater_than_threshold={indices_greater_than_threshold}"
                )
                self._quality.descriptor = QualityDescribe.bad
        else:
            self._quality.descriptor = QualityDescribe.bad

    def _visualization(self):
        self.drawer.set_options(title=self._description())
        self.drawer.set_options(raw_data_format="plot")
        draw_ops = {
            "markersize": 5,
            "marker": "o",
            "alpha": 0.8,
            "label": "rate",
            "linewidth": 2.5,
            "color": None,
        }

        analysis_datas = self.experiment_data.y_data
        width_list = analysis_datas["width_list"][0]
        base_index = 0

        if self.options.plot_raw_data:
            # plot scatter xy points
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=self.analysis_datas["z_amp_list"],
                ax_index=base_index,
                **draw_ops,
            )
            base_index = base_index + 1
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=self.analysis_datas["smooth_z_amp_list"],
                ax_index=base_index,
                **draw_ops,
            )

            base_index = base_index + 1
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=self.analysis_datas["amp_response_list"],
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops = {"linewidth": 0.5, "color": "b", "marker": None, "label": "0.01"}
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=1.01 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=0.99 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops = {
                "linewidth": 0.2,
                "color": "g",
                "marker": None,
                "label": "0.005",
            }
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=1.005 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=0.995 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )

            ## start_index
            start_index = self.options.start_index
            base_index = base_index + 1
            draw_ops = {
                "markersize": 3,
                "marker": "o",
                "alpha": 0.8,
                "label": None,
                "linewidth": 1,
                "color": None,
            }
            self.drawer.draw_raw_data(
                x_data=width_list[start_index:],
                y_data=self.analysis_datas["amp_response_list"][start_index:],
                ax_index=base_index,
                **draw_ops,
            )

            draw_ops = {"linewidth": 0.5, "color": "b", "marker": None, "label": "0.01"}
            width_list = width_list[start_index:]
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=1.01 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=0.99 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops = {
                "linewidth": 0.2,
                "color": "g",
                "marker": None,
                "label": "0.005",
            }
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=1.005 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
            draw_ops.update({"label": None})
            self.drawer.draw_raw_data(
                x_data=width_list,
                y_data=0.995 * np.ones_like(width_list),
                ax_index=base_index,
                **draw_ops,
            )
        self.drawer.format_canvas()


class DicarloAnalysisNM(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.subplots = (2, 2)
        options.x_label = ["sigma_x", "Frequency (MHz)", "width (ns)", "width (ns)"]
        options.y_label = ["sigma_y", "Probability", "Phase (rad)", "Phase (rad)"]

        options.pcolormesh_options = {"cmap": plt.cm.get_cmap("viridis")}
        options.sigma_x = None
        options.sigma_y = None
        options.freq_switch = False
        options.fringe = None
        options.drive_freq = None
        options.cali_offset = True
        options.start_index = None
        options.result_parameters = [
            ParameterRepr(
                name="distortion_sos", param_path="Compensate.z_distortion_sos"
            ),
        ]
        return options

    def _create_analysis_data(self) -> QDict:
        analysis_data_dict = QDict()
        analysis_datas = self.experiment_data.y_data
        for i, v in enumerate(self.experiment_data.x_data):
            if v == 0:
                P1y = self.experiment_data.child_data(i).y_data["P1"]
                self.options.sigma_y = 1 - 2 * P1y
            else:
                P1x = self.experiment_data.child_data(i).y_data["P1"]
                self.options.sigma_x = 1 - 2 * P1x

        width_list = analysis_datas["width_list"][0]

        N = len(self.options.sigma_x)
        N_FFT = 2 * N
        dt = width_list[1] - width_list[0]

        # Calculate the sampling rate 'FS' of  experiment according to the width_list (equal interval).
        Fs = 1 / abs(dt) * 1e3
        # Calculate FFT frequency list.
        f = Fs / 2 * np.linspace(0, 1, N + 1)

        x_f = fft(self.options.sigma_x, N_FFT)
        # Take the FFT single side band, and do the normalization.
        x_f_ss = x_f[0 : N + 1] / N
        x_f_ss[0 : N + 1] = 2 * x_f_ss[0 : N + 1]
        x_f_amp = np.abs(x_f_ss[0 : N + 1])

        index = int(np.argmax(x_f_amp[5:]) + 5)  ##
        fosci = f[index]
        if self.options.freq_switch:
            # fosci = Fs - fosci
            fosci = abs(self.options.freq_switch * Fs + fosci)

        if QAIO.type in [QAIO.qaio_8, QAIO.qaio_30]:
            sign = -1
        else:
            sign = 1
        pyqlog.info(f"fft_IF:{fosci},fringe:{self.options.fringe}\n")

        # Calculate phase by sigma_x and sigma_y.
        phase = np.angle(self.options.sigma_x + 1j * self.options.sigma_y * sign)

        # Phase down convertion.
        # Subtract a phase that oscillates with the fosci frequency.
        down_conversion_phase = 2 * np.pi * fosci * 1e-3 * width_list
        delta_phase = phase - down_conversion_phase
        extend_phase = np.unwrap(delta_phase, np.pi)
        # Calculate differential phase by using savgol filter.
        # The order of the polynomial used to fit the samples is 2.
        start_index = self.options.start_index
        width_ref = width_list[start_index:]
        phase_ref = extend_phase[start_index:]

        F = fitdata(linear_func, width_ref, phase_ref, [0, 0])
        rmse = mean_squared_error(phase_ref, linear_func(width_ref, *F.p))
        df_ref = F.p[0] / 2 / np.pi
        pyqlog.log("EXP", f"dicarlo scheme: df={df_ref}GHz, rmse={rmse}")

        phase_net = extend_phase - linear_func(width_list, *F.p)
        cost = np.std(phase_net[start_index:])
        if x_f_amp[index] < 0.5:
            cost += 100 * (0.5 - x_f_amp[index])
        analysis_data_dict["f"] = f
        analysis_data_dict["phi"] = phase_net
        analysis_data_dict["extend_phi"] = extend_phase
        analysis_data_dict["original_phi"] = phase
        analysis_data_dict["x_f_amp"] = x_f_amp
        analysis_data_dict["cost"] = cost

        return analysis_data_dict

    def _initialize_canvas(self):
        """Initialize matplotlib canvas."""
        self.drawer.set_options(
            subplots=self.options.get("subplots"),
            xlabel=self.options.get("x_label"),
            ylabel=self.options.get("y_label"),
            sub_title=self.options.sub_title,
            figsize=self.options.figsize,
            raw_data_format=self.options.get("raw_data_format"),
        )
        self.drawer.initialize_canvas()

    def _visualization(self):
        self.drawer.set_options(title=self._description())
        self.drawer.set_options(raw_data_format="plot")
        draw_ops = {
            "markersize": 5,
            "marker": "o",
            "alpha": 0.8,
            "label": "rate",
            "linewidth": 2.5,
            "color": None,
        }

        analysis_datas = self.experiment_data.y_data
        width_list = analysis_datas["width_list"][0]
        start_index = self.options.start_index
        base_index = 0

        if self.options.plot_raw_data:
            # plot scatter xy points
            self.options.pcolormesh_options.update({"c": width_list, "s": 20})
            self.drawer.draw_xy_point(
                x_data=self.options.sigma_x,
                y_data=self.options.sigma_y,
                ax_index=base_index,
                **self.options.pcolormesh_options,
            )

            # plot and save fft result.
            draw_ops.update(
                {"linewidth": 2.5, "marker": None, "color": None, "label": "ideal"}
            )

            base_index = base_index + 1
            self.drawer.draw_raw_data(
                x_data=self.analysis_datas["f"],
                y_data=self.analysis_datas["x_f_amp"],
                ax_index=base_index,
                **draw_ops,
            )

        draw_ops.update({"label": "phase"})
        base_index = base_index + 1
        self.drawer.draw_raw_data(
            x_data=width_list,
            y_data=self.analysis_datas["extend_phi"],
            ax_index=base_index,
            **draw_ops,
        )

        base_index = base_index + 1
        draw_ops.update({"label": f"phase-{self.analysis_datas['cost']}"})
        self.drawer.draw_raw_data(
            x_data=width_list[start_index:],
            y_data=self.analysis_datas["phi"][start_index:],
            ax_index=base_index,
            **draw_ops,
        )

        self.drawer.format_canvas()
