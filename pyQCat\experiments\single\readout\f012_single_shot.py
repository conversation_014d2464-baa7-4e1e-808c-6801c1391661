# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/11/10
# __author:       <PERSON><PERSON><PERSON>

from .single_shot import SingleShot


class SingleShotF012(SingleShot):
    @classmethod
    def _default_analysis_options(cls):
        options = super()._default_analysis_options()
        options.quality_bounds = [3, 0.85, 0.7, 0.6, 0.011]
        return options

    def _check_options(self):
        self.experiment_options.level_str = "012"
        super()._check_options()


class SingleShotF02(SingleShot):
    def _check_options(self):
        self.experiment_options.level_str = "02"
        super()._check_options()
