<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>826</width>
    <height>568</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>OnlineUpdate</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_2">
    <item>
     <widget class="QGroupBox" name="groupBox">
      <property name="title">
       <string>Update Online One PointLabel Parameters</string>
      </property>
      <layout class="QGridLayout" name="gridLayout">
       <item row="3" column="0">
        <widget class="QWidget" name="widget_4" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="3,16">
          <item>
           <widget class="QLabel" name="label_4">
            <property name="text">
             <string>f12_opt_bits</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QMultiComboBox" name="OptBox"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QWidget" name="widget_7" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="3,16">
          <item>
           <widget class="QLabel" name="label_7">
            <property name="text">
             <string>working_type</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="WorkingTypeBox"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="7" column="0">
        <widget class="QWidget" name="widget_8" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="3,16">
          <item>
           <widget class="QLabel" name="label_8">
            <property name="text">
             <string>divide_type</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="DivideTypeBox"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="10" column="0">
        <widget class="QWidget" name="widget_u" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_u">
          <item>
           <spacer name="horizontalSpacer_1">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>249</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="UpdateButton">
            <property name="text">
             <string>Update</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>249</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QWidget" name="widget_5" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="3,16">
          <item>
           <widget class="QLabel" name="label_5">
            <property name="text">
             <string>online_bits</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QMultiComboBox" name="OnlineBitlBox"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QWidget" name="widget_3" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="3,16">
          <item>
           <widget class="QLabel" name="label_3">
            <property name="text">
             <string>max_point_units</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QMultiComboBox" name="MaxUnitsBox"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="9" column="0">
        <spacer name="verticalSpacer_1">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>0</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="5" column="0">
        <widget class="QWidget" name="widget_6" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="3,16">
          <property name="rightMargin">
           <number>9</number>
          </property>
          <item>
           <widget class="QLabel" name="label_6">
            <property name="text">
             <string>online_pairs</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QMultiComboBox" name="OnlinePairBox"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QWidget" name="widget_1" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_1" stretch="3,16">
          <item>
           <widget class="QLabel" name="label_1">
            <property name="text">
             <string>point_label</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="PointLabelBox"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="11" column="0">
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>0</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="1" column="0">
        <widget class="QWidget" name="widget_2" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="3,16">
          <item>
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>env_bits</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QMultiComboBox" name="EnvBox">
            <property name="enabled">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="8" column="0">
        <widget class="QWidget" name="widget_c" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_c">
          <item>
           <widget class="QCheckBox" name="OnlineCheck">
            <property name="text">
             <string>online</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="CrosstalkCheck">
            <property name="text">
             <string>crosstalk</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="XYCrosstalkCheck">
            <property name="text">
             <string>xy_crosstalk</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QMultiComboBox</class>
   <extends>QComboBox</extends>
   <header>.widgets.combox_custom.combox_multi</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>UpdateButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>update_online_params()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>413</x>
     <y>497</y>
    </hint>
    <hint type="destinationlabel">
     <x>412</x>
     <y>283</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>update_online_params()</slot>
 </slots>
</ui>
