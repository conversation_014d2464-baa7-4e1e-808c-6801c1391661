﻿pyQCat.qubit.BaseQubit
======================

.. currentmodule:: pyQCat.qubit

.. autoclass:: BaseQubit

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~BaseQubit.__init__
      ~BaseQubit.ac_instead_dc
      ~BaseQubit.anno
      ~BaseQubit.from_dict
      ~BaseQubit.from_file
      ~BaseQubit.is_adjacent
      ~BaseQubit.put_sweet_point
      ~BaseQubit.reset
      ~BaseQubit.save_database
      ~BaseQubit.set_coords
      ~BaseQubit.to_dict
      ~BaseQubit.to_file
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~BaseQubit.col
      ~BaseQubit.composite_attrs
      ~BaseQubit.options_attrs
      ~BaseQubit.row
      ~BaseQubit.unit_map
   
   