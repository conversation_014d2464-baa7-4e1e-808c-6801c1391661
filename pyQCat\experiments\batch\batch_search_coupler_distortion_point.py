# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/02/01
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

"""
BatchSearchCouplerDistortionPoint:
    Search for suitable Coupler distortion calibration work points
"""

import copy
import json
from typing import Dict, List

from ...analysis.fit.fit_models import freq2amp_formula
from ...log import pyqlog
from ...structures import QDict
from ..batch_experiment import BatchExperiment
from .batch_coupler_distortion import BatchCouplerDistortionT1New


class BatchSearchCouplerDistortionPoint(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.qc2qh_ac_spectrum_flows = None
        options.point_check_flows = None
        options.distortion_cali_flows = None
        options.qh_test_detune_list = None
        options.distortion_zamp = None
        options.distortion_xy_delay = None
        options.run_ac_spec_flag = True
        options.ac_spec_json_path = None
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.qp2qc_map = {}
        options.qc2qh_map = {}
        options.qc2ql_map = {}
        options.qc_test_amp_list_map = {}
        options.qh_ac_spec_by_qc = {}
        options.cur_distortion_xy_delay = None
        options.cur_distortion_z_amp = None
        options.cur_qc_idle_map = {}
        options.qc_distortion_point_record = {}
        options.dir_describe = {}
        return options

    def _count_max_sweep_count(self):
        return max([len(v) for v in self.run_options.qc_test_amp_list_map.values()])

    def _set_qubit_idle(self):
        qc2qh_ac_spec_map = {}

        # load local ac spec data
        if self.experiment_options.ac_spec_json_path:
            try:
                with open(
                    self.experiment_options.ac_spec_json_path,
                    mode="r",
                    encoding="utf-8",
                ) as fp:
                    content = fp.read()
                    qc2qh_ac_spec_map = json.loads(content)
            except Exception as e:
                pyqlog.error(
                    f"Load ac spec from {self.experiment_options.ac_spec_json_path} error {e}!"
                )

        # init qh_ac_spec_by_qc dict for every coupler
        for coupler_name in self.experiment_options.physical_units:
            # get coupler structure
            coupler_struct = self.context_manager.chip_data.get_coupler_struct(
                coupler_name
            )
            qh = coupler_struct.qh
            ql = coupler_struct.ql

            # set the env bit working voltage
            ql_max_freq, ql_min_freq = (
                BatchCouplerDistortionT1New.refresh_working_point(ql, qh)
            )

            # write run env record json
            self.run_options.qh_ac_spec_by_qc[coupler_name] = {
                "qh": {qh.name: {"idle_freq": qh.drive_freq}},
                "ql": {
                    ql.name: {
                        "idle_freq": ql.drive_freq,
                        "max_freq": round(ql_max_freq),
                        "min_freq": round(ql_min_freq),
                        "idle_amp": round(ql.dc_min - ql.dc_max, 3),
                    }
                },
                "params": qc2qh_ac_spec_map.get(coupler_name, {}).get("params"),
            }

    def _change_work_point(self, i: int, physical_units: List[str]):
        working_units = []
        for c_name in physical_units:
            qc_test_amp_list = self.run_options.qc_test_amp_list_map.get(c_name)
            if i < len(qc_test_amp_list):
                vol = qc_test_amp_list[i]

                distortion_detune = self.experiment_options.qh_test_detune_list[i]
                coupler = self.context_manager.chip_data.cache_coupler.get(c_name)
                qh_name = self.run_options.qc2qh_map.get(c_name)
                qh = self.context_manager.chip_data.cache_qubit.get(qh_name)

                coupler.dc_max += vol
                qh.drive_freq -= distortion_detune
                qh.XYwave.detune_pi = 0
                qh.XYwave.detune_pi2 = 0
                qh.XYwave.time = 200

                self.run_options.cur_qc_idle_map[c_name] = str(vol)
                self.run_options.dir_describe[qh_name] = f"idle={vol}"
                self.run_options.dir_describe[c_name] = f"idle={vol}"
                working_units.append(qh_name)

                pyqlog.log(
                    "EXP", f"{c_name} | idle point ({vol}) | qh bit {qh_name} | "
                )
        return working_units

    def _batch_up(self):
        super()._batch_up()
        self._pre_run_batch()

    def _run_batch(self):
        # set ql dc min, keep the max frequency detune from qh
        self._set_qubit_idle()

        # generate the coupler shift ac spectrum
        if (
            self.experiment_options.run_ac_spec_flag
            and self.experiment_options.qc2qh_ac_spectrum_flows
        ):
            acs_pass_units = self._run_flow(
                flows=self.experiment_options.qc2qh_ac_spectrum_flows,
                physical_units=self.experiment_options.physical_units,
            )
        else:
            acs_pass_units = []
            for unit, data in self.run_options.qh_ac_spec_by_qc.items():
                if data.get("params"):
                    acs_pass_units.append(unit)
        self._save_qc2qh_ac_spec_data(acs_pass_units)

        # run coupler distortion working point check flow
        # the max sweep coupler idle point times
        sweep_count = self._count_max_sweep_count()
        for i in range(sweep_count):
            if acs_pass_units:
                # change working point
                working_units = self._change_work_point(i, acs_pass_units)
                if not working_units:
                    continue

                # run point check flow
                pass_units = self._run_flow(
                    flows=self.experiment_options.point_check_flows,
                    physical_units=working_units,
                )
                if not pass_units:
                    continue
                pass_units = [
                    self.run_options.qp2qc_map.get(unit) for unit in pass_units
                ]

                # check point flow
                next_units = copy.deepcopy(pass_units)
                all_delay_pass_units = []
                # exp = self.params_manager.exp_map.get(self.experiment_options.distortion_cali_flows[0])
                exp_name = self.experiment_options.distortion_cali_flows[0]
                for zamp in self.experiment_options.distortion_zamp:
                    cur_pass_units = copy.deepcopy(next_units)
                    self.change_regular_exec_exp_options(exp_name=exp_name, z_amp=zamp)
                    self.run_options.cur_distortion_z_amp = zamp
                    pyqlog.log("FLOW", f"The current distortion zamp {zamp} v!")
                    for xy_delay in self.experiment_options.distortion_xy_delay:
                        self.change_regular_exec_exp_options(
                            exp_name=exp_name, xy_delay=xy_delay
                        )
                        pyqlog.log(
                            "FLOW",
                            f"The current distortion zamp {zamp} v | distortion xy_delay {xy_delay} ns!",
                        )
                        self.run_options.cur_distortion_xy_delay = xy_delay
                        cur_pass_units = self._run_flow(
                            self.experiment_options.distortion_cali_flows,
                            cur_pass_units,
                        )

                    if cur_pass_units:
                        all_delay_pass_units.extend(cur_pass_units)
                        next_units = [
                            unit for unit in next_units if unit not in cur_pass_units
                        ]

                # record working point state
                self._save_qc_distortion_point_record()

                # remove the qubit in next loop when have find good working point
                for unit in all_delay_pass_units:
                    acs_pass_units.remove(unit)

            else:
                if i == 0:
                    pyqlog.warning(
                        "Please Check ACSpectrumByCoupler Exp, No Qubit Pass For Next Exp!!!"
                    )
                else:
                    pyqlog.log(
                        "FLOW",
                        "All Executing Coupler Have Find Suitable Distortion Working Point!",
                    )
                    break

    def _record_experiment(self, exp_name, exp, physical_units, err: Exception = None):
        # record the exp result and information after each one exp have executed
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        # record the shift ac spectrum data in json
        if "ACSpectrumByCoupler" in exp.label:
            for c_name in record.pass_units:
                res = record.analysis_data.get(c_name)
                self.run_options.qh_ac_spec_by_qc[c_name].update(
                    {"params": res.get("result").get("params")}
                )

        # record qubit freq, drag amp and power in json
        elif (
            ("QubitSpectrum" in exp.label)
            or ("XpiDetection" in exp.label)
            or ("RabiScanAmp" in exp.label)
        ):
            for q_name, res in record.analysis_data.items():
                c_name = self.return_key_from_value(q_name, self.run_options.qc2qh_map)
                cur_idle = self.run_options.cur_qc_idle_map.get(c_name)
                point_sate = self.run_options.qc_distortion_point_record.get(
                    c_name
                ).get(cur_idle)
                if q_name in record.pass_units:
                    if "QubitSpectrum" in exp.label:
                        point_sate.drive_freq = res.get("result").get("freq")
                    elif ("XpiDetection" in exp.label) or ("RabiScanAmp" in exp.label):
                        point_sate.drive_power = res.get("result").get("drive_power")
                        point_sate.Xpi = res.get("result").get("Xpi")
                        point_sate.Xpi2 = res.get("result").get("Xpi2")
                else:
                    point_sate.error_reason = record.fail_reason
                    point_sate.error_exp = exp.label

        # record coupler distortion working point information in json
        elif "CouplerDistortionZZ" in exp.label:
            for unit_index, unit in enumerate(record.analysis_data.keys()):
                c_name = "c" + unit.split("c")[-1]
                cur_idle = self.run_options.cur_qc_idle_map[c_name]
                point_sate = self.run_options.qc_distortion_point_record.get(
                    c_name
                ).get(cur_idle)
                cur_zamp = self.run_options.cur_distortion_z_amp
                xy_delay = self.run_options.cur_distortion_xy_delay
                if len(record.analysis_data.keys()) > 1:
                    point_sate.distortion_quality.update(
                        {cur_zamp: {xy_delay: record.analysis_data[unit]["quality"]}}
                    )
                else:
                    point_sate.distortion_quality.update(
                        {cur_zamp: {xy_delay: record.analysis_data[unit]["quality"]}}
                    )

                if c_name in record.pass_units:
                    point_sate.is_pass = True
                else:
                    point_sate.is_pass = False

        # record the bad units information in json
        else:
            for unit in record.bad_units:
                if unit.startwidth("q"):
                    c_name = self.run_options.qp2qc_map.get(unit)
                else:
                    c_name = unit
                cur_idle = self.run_options.cur_qc_idle_map[c_name]
                point_sate = self.run_options.qc_distortion_point_record.get(
                    c_name
                ).get(cur_idle)
                point_sate.error_reason = record.fail_reason
                point_sate.error_exp = exp.label

        return record

    def _save_qc_distortion_point_record(self):
        self._save_data_to_json(
            self.run_options.qc_distortion_point_record, "qc_distortion_point_record"
        )

    def _save_qc2qh_ac_spec_data(self, acs_pass_units: List[str]):
        for c_name in acs_pass_units:
            qh_ac_spec_by_qc = self.run_options.qh_ac_spec_by_qc[c_name]
            qh = self.context_manager.chip_data.cache_qubit.get(
                self.run_options.qc2qh_map.get(c_name)
            )
            drive_freq = qh.drive_freq
            fq_max, fc, m, offset, d, w, g, *_ = qh_ac_spec_by_qc.get("params")
            self.run_options.qc_test_amp_list_map[c_name] = [
                round(
                    freq2amp_formula(
                        drive_freq - detune,
                        float(fq_max),
                        float(fc),
                        float(m),
                        float(offset),
                        float(d),
                        "left",
                        float(w),
                        float(g),
                        "top",
                    ),
                    6,
                )
                for detune in self.experiment_options.qh_test_detune_list
            ]
        self._generate_record_json(acs_pass_units)
        self._save_data_to_json(self.run_options.qh_ac_spec_by_qc, "qh_ac_spec_by_qc")

    def _pre_run_batch(self):
        # generate the qp2qc, qc2qh, qc2ql dict in run options
        for c_name in self.experiment_options.physical_units:
            coupler_struct = self.context_manager.chip_data.get_coupler_struct(c_name)
            self.run_options.qp2qc_map.update({coupler_struct.qp.name: c_name})
            self.run_options.qc2qh_map.update({c_name: coupler_struct.qh.name})
            self.run_options.qc2ql_map.update({c_name: coupler_struct.ql.name})

    def _generate_record_json(self, acs_pass_units: List[str]):
        for c_name in acs_pass_units:
            idle_list = self.run_options.qc_test_amp_list_map.get(c_name)
            point_map = {}
            for idle in idle_list:
                point_map.update(
                    {
                        str(idle): {
                            "is_pass": False,
                            "drive_freq": None,
                            "drive_power": None,
                            "Xpi": None,
                            "Xpi2": None,
                            "distortion_quality": {},
                            "error_reason": None,
                            "error_exp": None,
                        }
                    }
                )
            self.run_options.qc_distortion_point_record[c_name] = QDict(**point_map)

    @staticmethod
    def return_key_from_value(value=None, dic=None):
        # get the key from value
        if isinstance(dic, Dict):
            if value in dic.values():
                for key, val in dic.items():
                    if val == value:
                        return key
            else:
                raise ValueError(f"Value {value} not in dict {dic}")
        else:
            raise ValueError(f"{dic} is not a dict !")
