# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.frequency.rst:2
msgid "pyQCat.analysis.algorithms.frequency"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency:1
msgid "Get frequency of oscillating signal."
msgstr "获取振荡信号的频率"

#: of pyQCat.analysis.algorithms.guess.frequency:3
msgid ""
"First this tries FFT. If the true value is likely below or near the "
"frequency resolution, the function tries low frequency fit with"
msgstr "首先这会尝试 FFT。 如果真值可能低于或接近频率分辨率，则函数尝试低频拟合"

#: of pyQCat.analysis.algorithms.guess.frequency:6
msgid ""
"f_{\\rm est} = \\frac{1}{2\\pi {\\rm max}\\left| y \\right|}\n"
"    {\\rm max} \\left| \\frac{dy}{dx} \\right|"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency:11
msgid ""
"given :math:`y = A \\cos (2\\pi f x + phi)`. In this mode, y data points "
"are smoothed by a Savitzky-Golay filter to protect against outlier "
"points."
msgstr ""
"给定 :math:`y = A \\cos (2\\pi f x + phi)` 模型下，y 数据点由 ``Savitzky-Golay`` 滤"
"波器平滑，以防止异常点。"

#: of pyQCat.analysis.algorithms.guess.frequency:16
msgid ""
"This function returns always positive frequency. This function is "
"sensitive to the DC offset. This function assumes sorted, no-overlapping "
"x values."
msgstr ""
"此函数始终返回正频率。 此功能对直流偏移敏感。 此函数假定已排序、不重叠的 x 值。"

#: of pyQCat.analysis.algorithms.guess.frequency
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency:21
msgid "Array of x values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency:23
msgid "Array of y values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency:25
msgid "Window size of Savitzky-Golay filter. This should be odd number."
msgstr "``Savitzky-Golay`` 滤波器的窗口大小。 它应该是奇数。"

#: of pyQCat.analysis.algorithms.guess.frequency:27
msgid "Dimension of Savitzky-Golay filter."
msgstr "``Savitzky-Golay`` 滤波器的维度"

#: of pyQCat.analysis.algorithms.guess.frequency
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency:29
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.frequency:30
msgid "Frequency estimation of oscillation signal."
msgstr "振荡信号的预计频率。"

