# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/29
# __author:       <PERSON>

from __future__ import annotations

import datetime
import os
import re
import time

import matplotlib

matplotlib.use("Agg")
import matplotlib.dates as mdates
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

from ...concurrent.batch_heart import RecordMode
from ...log import pyqlog
from ...qubit import NAME_PATTERN
from ...structures import CommonDict
from ...tools.utilities import display_dict_as_table
from ..batch_experiment import BatchExperiment


class BatchStability(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()

        options.loops = 3
        options.interval = 10  # Unit: second
        options.filter_params = {}
        options.y_limit_params = {}
        options.is_statistics = False
        options.record_length_limit = 100

        return options

    @classmethod
    def _default_analysis_options(cls):
        options = super()._default_analysis_options()

        # The name of the `base` option is inspired by the parameter names of
        # `matplotlib.ticker.MultipleLocator.__init__`.
        # With `matplotlib.ticker.MultipleLocator`, we can set a tick on each integer multiple of a **base** within
        # the view interval.
        options.base = 1
        options.rotation = 45
        # The name of the `left` and `bottom` option are inspired by the parameter names of
        # `matplotlib.pyplot.subplots_adjust(*args, **kwargs)`.
        # https://matplotlib.org/2.0.2/api/pyplot_api.html#matplotlib.pyplot.subplots_adjust
        options.left = 0.15  # The left side of the subplots of the figure
        options.bottom = 0.2  # The bottom of the subplots of the figure
        options.interval_of_analysis = 1

        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        # If `is_finished = True`, then BatchStability (the current batch experiment)
        # may have been finished successfully (no error is raised), **or vice versa**.
        options.is_finished = False
        options.path_of_temp_fig_folder = ""
        options.group_map = None
        options.history_limit = None
        return options

    def _batch_up(self):
        super()._batch_up()

        if not self.experiment_options.flows:
            self.experiment_options.flows = self.params_manager.flows

        self.experiment_options.exp_retry = 0
        pyqlog.info("`exp_retry` (an experiment option) is forced to 0.")
        self.experiment_options.record_batch = True
        pyqlog.info("`record_batch` (an experiment option) is forced to True.")
        self.experiment_options.quality_filter = False
        pyqlog.info("`quality_filter` (an experiment option) is forced to False.")

        if self.analysis_options.base is None:
            pyqlog.info(
                f"The value of `base` (an analysis option) is {self.analysis_options.base}. "
                f"Now this option is set to 1."
            )
            self.analysis_options.base = 1
        if self.analysis_options.rotation is None:
            pyqlog.info(
                f"The value of `rotation` (an analysis option) is {self.analysis_options.rotation}. "
                f"Now this option is set to 45."
            )
            self.analysis_options.rotation = 45
        if self.analysis_options.left is None:
            pyqlog.info(
                f"The value of `left` (an analysis option) is {self.analysis_options.left}. "
                f"Now this option is set to 0.15."
            )
            self.analysis_options.left = 0.15
        if self.analysis_options.bottom is None:
            pyqlog.info(
                f"The value of `bottom` (an analysis option) is {self.analysis_options.bottom}. "
                f"Now this option is set to 0.2."
            )
            self.analysis_options.bottom = 0.2

        bit = self.experiment_options.physical_units[0]
        if re.match(NAME_PATTERN.qubit, bit):
            group_map = self.parallel_allocator_for_qc(
                self.experiment_options.physical_units
            )
        elif re.match(NAME_PATTERN.qubit_pair, bit):
            group_map = self.parallel_allocator_for_cgc(
                self.experiment_options.physical_units
            )
        else:
            group_map = {"Group-1": self.experiment_options.physical_units}
        self.run_options.group_map = group_map

        # extend record length limit
        record_length_limit = self.experiment_options.record_length_limit
        self.run_options.history_limit = record_length_limit
        self.experiment_options.record_length_limit = (
            record_length_limit * len(group_map) * len(self.experiment_options.flows)
        )

    def _run_batch(self):
        loops = self.experiment_options.loops
        interval = self.experiment_options.interval
        # In this batch experiment, `self.experiment_options.flows` is a one-dimensional list.
        flows = self.experiment_options.flows

        # `path_of_temp_fig_folder` is an absolute path.
        path_of_temp_fig_folder = os.path.join(
            self.context_manager.config.system.local_root,
            self.context_manager.config.system.sample,
            self.run_options.dir_prefix,
            "Temp",
        )
        self.run_options.path_of_temp_fig_folder = path_of_temp_fig_folder

        if self.analysis_options.interval_of_analysis == -1:
            pyqlog.info(
                f"The value of `interval_of_analysis` (an analysis option) is "
                f"{self.analysis_options.interval_of_analysis}, "
                f"so Monster will **not** do phase analysis. "
                f"For more information, refer to https://document.qpanda.cn/docs/R13j8MzM1OTjD8k5#anchor-oYtJ"
            )
            should_do_phase_analysis: bool = False
        else:
            should_do_phase_analysis: bool = True
            os.makedirs(path_of_temp_fig_folder, exist_ok=True)

        self.run_options.begin_datetime = datetime.datetime.now()
        history_limit = self.run_options.history_limit

        try:
            for i in range(loops):
                if (i + 1) % history_limit == 0:
                    self.record_meta.execute_meta.sync_history_results()
                    pyqlog.info("Sync history results ...")
                for group_name, group in self.run_options.group_map.items():
                    self._run_flow(
                        flows=flows,
                        physical_units=group,
                        name=f"{group_name} Stability ({i + 1}/{loops})",
                    )
                if should_do_phase_analysis and (
                    i % self.analysis_options.interval_of_analysis == 0
                ):
                    for name in os.listdir(path_of_temp_fig_folder):
                        path = os.path.join(path_of_temp_fig_folder, name)
                        try:
                            if os.path.isfile(path):
                                # `remove` **permanently** deletes the file.
                                os.remove(path)
                        except Exception as e:
                            print(f"Failed to delete {path}. Reason: {e}")
                    self._run_analysis(i)
                    self.record_thread.add_message((RecordMode.RF, ""))
                if i != loops - 1:
                    pyqlog.info(
                        f"{i + 1} loop(s) finished, {loops - (i + 1)} loop(s) left"
                    )
                    pyqlog.info(f"Waiting for {interval} second(s)...")
                    time.sleep(interval)
        except Exception as ex:
            pyqlog.warning("An exceptions is thrown when running experiments.")
            pyqlog.warning(ex)
            pyqlog.info(
                "The results of the batch experiment are saved by Monster in real time. "
                "Monster will analyse these saved experiment results. "
                f"The path of saved experiment results: {self.run_options.record_path}"
            )
        else:
            pyqlog.info("No exceptions are thrown when running experiments.")
            pyqlog.info(
                "Monster will analyse the experiment results. "
                f"The path of experiment results: {self.run_options.record_path}"
            )
        self.run_options.is_finished = True
        self.run_options.end_datetime = datetime.datetime.now()

    def _run_analysis(self, index: int = None):
        if index is None:
            index = self.experiment_options.loops - 1

        def convert_data(data_to_plot: CommonDict, exp_names: set[str]):
            converted_data = {}

            for exp_name in exp_names:
                converted_data[exp_name] = {}
                # for name_of_physical_unit in name_of_physical_units:
                #     converted_data[exp_name][name_of_physical_unit] = {}

            for exp_name, data_of_exp in data_to_plot.items():
                for name_of_result, value_of_result in data_of_exp.items():
                    for name_of_physical_unit, data_list in value_of_result.items():
                        if converted_data[exp_name].get(name_of_physical_unit) is None:
                            converted_data[exp_name][name_of_physical_unit] = {}
                        converted_data[exp_name][name_of_physical_unit][
                            name_of_result
                        ] = data_list

            return converted_data

        records = self.batch_records
        is_filter_params = bool(self.experiment_options.filter_params)

        x_data_map = {}
        data_to_plot = {}
        exp_names = set()

        for name_of_record, value_of_record in records.items():
            exp_name = value_of_record["exp_name"]

            if (
                is_filter_params
                and exp_name not in self.experiment_options.filter_params
            ):
                continue

            plot_params = self.experiment_options.filter_params.get(exp_name)
            exp_names.add(exp_name)

            data_of_exp = data_to_plot.get(exp_name)
            if data_of_exp is None:
                data_of_exp = {}

            for (
                name_of_physical_unit,
                analysis_data_of_current_physical_unit,
            ) in value_of_record["analysis_data"].items():
                result = analysis_data_of_current_physical_unit.get("result")
                experiment_data = analysis_data_of_current_physical_unit.get(
                    "experiment_data", {}
                )

                x_data = experiment_data.get("x_data")
                if x_data and not x_data_map.get(exp_name, {}).get(
                    name_of_physical_unit
                ):
                    x_unit = x_data_map.setdefault(exp_name, {})
                    x_unit.update({name_of_physical_unit: x_data})

                if not result and not experiment_data:
                    pyqlog.info(
                        f"There is no analysis data of {name_of_physical_unit} to plot "
                        f"in the current record (`{name_of_record}`). "
                        f"The path of experiment results: {self.run_options.record_path}."
                    )
                    continue

                if exp_name == "SingleShot":
                    names_of_results = ["fidelity0", "fidelity1"]
                    for name_of_result in names_of_results:
                        if plot_params and name_of_result not in plot_params:
                            continue
                        if data_of_exp.get(name_of_result) is None:
                            data_of_exp[name_of_result] = {}
                        if (
                            data_of_exp[name_of_result].get(name_of_physical_unit)
                            is None
                        ):
                            data_of_exp[name_of_result][name_of_physical_unit] = []
                        data_of_exp[name_of_result][name_of_physical_unit].append(
                            [
                                value_of_record["timestamp"],
                                result["dcm"]["fidelity"][int(name_of_result[-1])],
                            ]
                        )
                else:
                    for name_of_data, value_of_data in experiment_data.items():
                        if plot_params and name_of_data not in plot_params:
                            continue
                        cur_field_data = data_of_exp.setdefault(
                            name_of_data, {name_of_physical_unit: []}
                        )
                        cur_data = cur_field_data.setdefault(name_of_physical_unit, [])
                        cur_data.append((value_of_record["timestamp"], value_of_data))
                    for name_of_result, value_of_result in result.items():
                        if plot_params and name_of_result not in plot_params:
                            continue
                        if isinstance(value_of_result, (int, float)) and not isinstance(
                            value_of_result, bool
                        ):
                            if np.isfinite(value_of_result):
                                if data_of_exp.get(name_of_result) is None:
                                    data_of_exp[name_of_result] = {}
                                if (
                                    data_of_exp[name_of_result].get(
                                        name_of_physical_unit
                                    )
                                    is None
                                ):
                                    data_of_exp[name_of_result][
                                        name_of_physical_unit
                                    ] = []
                                data_of_exp[name_of_result][
                                    name_of_physical_unit
                                ].append(
                                    (
                                        value_of_record["timestamp"],
                                        value_of_result,
                                    )
                                )
                            else:
                                pyqlog.warning(
                                    f"{value_of_result} (found in {name_of_record} - {name_of_physical_unit} - "
                                    f"{name_of_result}) is not finite, so this piece of data will not be plotted. "
                                    f"The path of experiment results: {self.run_options.record_path}"
                                )

            if data_of_exp:
                data_to_plot[exp_name] = data_of_exp
            else:
                pyqlog.info(
                    f"There is no data to plot in the current record (`{name_of_record}`). "
                    f"The path of experiment results: {self.run_options.record_path}"
                )
        converted_data = convert_data(data_to_plot, exp_names)
        self._save_data_to_json(converted_data, "origin_data")
        """
        # One example of `data_to_plot`:
        data_to_plot = {
            "SingleShot": {
                "fidelity_0": {
                    "q1": [
                        ("2024-02-06 22:39:43.736768", 0.9417),
                        ("2024-02-06 22:39:47.996599", 0.9417),
                        ("2024-02-06 22:39:52.391141", 0.9417),
                    ],
                    "q2": [
                        ("2024-02-06 22:39:43.736768", 0.9417),
                        ("2024-02-06 22:39:47.996599", 0.9417),
                        ("2024-02-06 22:39:52.391141", 0.9417),
                    ],
                }
            },
            "T1": {
                "p0": {
                    "q1": [
                        ("2024-02-06 22:39:45.923143", [...]),
                        ("2024-02-06 22:39:50.275533", [...]),
                        ("2024-02-06 22:39:54.694212", [...]),
                    ],
                    "q2": [
                        ("2024-02-06 22:39:45.923143", [...]),
                        ("2024-02-06 22:39:50.275533", [...]),
                        ("2024-02-06 22:39:54.694212", [...]),
                    ],
                }
                "tau": {
                    "q1": [
                        ("2024-02-06 22:39:45.923143", 11.7788),
                        ("2024-02-06 22:39:50.275533", 11.7788),
                        ("2024-02-06 22:39:54.694212", 11.7788),
                    ],
                    "q2": [
                        ("2024-02-06 22:39:45.923143", 11.7788),
                        ("2024-02-06 22:39:50.275533", 11.7788),
                        ("2024-02-06 22:39:54.694212", 11.7788),
                    ],
                },
                "rate": {
                    "q1": [
                        ("2024-02-06 22:39:45.923143", 0.295),
                        ("2024-02-06 22:39:50.275533", 0.295),
                        ("2024-02-06 22:39:54.694212", 0.295),
                    ],
                    "q2": [
                        ("2024-02-06 22:39:45.923143", 0.295),
                        ("2024-02-06 22:39:50.275533", 0.295),
                        ("2024-02-06 22:39:54.694212", 0.295),
                    ],
                },
            },
        }
        # One example of `converted_data`:
        converted_data = {
            "SingleShot": {
                "q1": {
                    "fidelity_0": [
                        ("2024-02-06 22:39:43.736768", 0.9417),
                        ("2024-02-06 22:39:47.996599", 0.9417),
                        ("2024-02-06 22:39:52.391141", 0.9417),
                    ]
                },
                "q2": {
                    "fidelity_0": [
                        ("2024-02-06 22:39:43.736768", 0.9417),
                        ("2024-02-06 22:39:47.996599", 0.9417),
                        ("2024-02-06 22:39:52.391141", 0.9417),
                    ]
                },
            },
            "T1": {
                "q1": {
                    "tau": [
                        ("2024-02-06 22:39:45.923143", 11.7788),
                        ("2024-02-06 22:39:50.275533", 11.7788),
                        ("2024-02-06 22:39:54.694212", 11.7788),
                    ],
                    "rate": [
                        ("2024-02-06 22:39:45.923143", 0.295),
                        ("2024-02-06 22:39:50.275533", 0.295),
                        ("2024-02-06 22:39:54.694212", 0.295),
                    ],
                },
                "q2": {
                    "tau": [
                        ("2024-02-06 22:39:45.923143", 11.7788),
                        ("2024-02-06 22:39:50.275533", 11.7788),
                        ("2024-02-06 22:39:54.694212", 11.7788),
                    ],
                    "rate": [
                        ("2024-02-06 22:39:45.923143", 0.295),
                        ("2024-02-06 22:39:50.275533", 0.295),
                        ("2024-02-06 22:39:54.694212", 0.295),
                    ],
                },
            },
        }
        """

        if self.experiment_options.is_statistics is True:
            self._data_analysis(converted_data, index)

        begin_datetime = self.run_options.begin_datetime
        end_datetime = self.run_options.end_datetime
        for exp_name, data_of_exp in converted_data.items():
            for name_of_physical_unit, value_of_result in data_of_exp.items():
                title = f"{exp_name} ({name_of_physical_unit}) "
                if self.run_options.is_finished:
                    title += (
                        f"{begin_datetime.month}-{begin_datetime.day} {begin_datetime.strftime('%H:%M')}"
                        f" -> {end_datetime.month}-{end_datetime.day} {end_datetime.strftime('%H:%M')}"
                    )
                else:
                    now = datetime.datetime.now()
                    title += (
                        f"Begin at {begin_datetime.month}-{begin_datetime.day} {begin_datetime.strftime('%H:%M')}, "
                        f"Last updated at {now.month}-{now.day} {now.strftime('%H:%M:%S')}"
                    )

                y_limit_map = self.experiment_options.y_limit_params.get(exp_name, {})
                self._save_fig(
                    exp_name,
                    name_of_physical_unit,
                    value_of_result,
                    title,
                    self.analysis_options.base,
                    self.analysis_options.rotation,
                    self.analysis_options.left,
                    self.analysis_options.bottom,
                    y_limit_map,
                    origin_x_data=x_data_map.get(exp_name, {}).get(
                        name_of_physical_unit
                    ),
                )

        self.sync_batch_information()

    def _data_analysis(self, converted_data, index):
        if converted_data:
            records = []
            for exp_name, unit_records in converted_data.items():
                for unit, exp_records in unit_records.items():
                    for key, vs in exp_records.items():
                        try:
                            data_list = [v[1] for v in vs]
                            cs = dict(
                                exp=exp_name,
                                unit=unit,
                                key=key,
                                max=np.max(data_list),
                                min=np.min(data_list),
                                mean_v=np.mean(data_list),
                                median_v=np.median(data_list),
                            )
                            records.append(cs)
                        except Exception:
                            pass
            table = display_dict_as_table(records)
            pyqlog.info(f"Index-{index} Statistics records: \n{table}")
            # self._save_data_to_json(
            #     dict(total=self.experiment_options.loops, interval=self.experiment_options.interval, data=records),
            #     "statistics",
            # )

    def _save_fig(
        self,
        exp_name: str,
        physical_units: str,
        value_of_result: CommonDict,
        title: str,
        base: int,
        rotation: int,
        left: float,
        bottom: float,
        y_limit_map: dict,
        origin_x_data=None,
    ):
        value_of_result.pop("x_data", None)
        # Create a figure and axis object
        num_of_results = len(value_of_result)
        # If num_of_results == 1,
        # then type(ax) == <class 'matplotlib.axes._subplots.AxesSubplot'>, type(fig) == <class 'matplotlib.figure.Figure'>
        # If num_of_results > 1,
        # then type(ax) == <class 'numpy.ndarray'>, type(fig) == <class 'matplotlib.figure.Figure'>
        fig, ax = plt.subplots(num_of_results, 1, sharex=True)

        # Calculate the appropriate figure size based on the number of timestamps
        sum_of_len = 0  # Sum of the length of `data_list` (X data)
        for name_of_result, data_list in value_of_result.items():
            sum_of_len = sum_of_len + len(data_list)
        average_len_of_x_data = sum_of_len / num_of_results
        extended_width = 0
        if average_len_of_x_data <= 40:
            # Range of `extended_width` in this case: [0, 0.1 * 40]
            extended_width = 0.1 * average_len_of_x_data
        elif average_len_of_x_data <= 80:
            # Range of `extended_width` in this case: (0.1 * 40, 0.1 * 40 + 0.05 * 40]
            extended_width = 0.1 * 40 + 0.05 * (average_len_of_x_data - 40)
        elif average_len_of_x_data <= 120:
            # Range of `extended_width` in this case: (0.1 * 40 + 0.05 * 40, 0.1 * 40 + 0.05 * 40 + 0.0001 * (average_len_of_x_data - 80)]
            extended_width = (
                0.1 * 40 + 0.05 * 40 + 0.0001 * (average_len_of_x_data - 80)
            )
        # `matplotlib.figure.Figure.set_size_inches` sets the figure size in inches.
        # `matplotlib.figure.Figure.get_figwidth` returns the figure width in inches.
        # `matplotlib.figure.Figure.get_figheight` returns the figure height in inches.
        fig.set_size_inches(
            # `6.4` is the default width of the figure.
            fig.get_figwidth() + extended_width,
            fig.get_figheight() * (1 + 0.5 * (num_of_results - 1)),
        )

        all_x_data: list[str] = []
        all_timestamps = []

        index_of_ax = 0
        for name_of_result, data_list in value_of_result.items():
            x_data: list[str] = [data_tuple[0] for data_tuple in data_list]
            y_data: list[int | float | list] = [
                data_tuple[1] for data_tuple in data_list
            ]

            # Convert timestamps to datetime objects
            timestamps = [pd.to_datetime(timestamp_str) for timestamp_str in x_data]

            all_x_data = list(set(all_x_data + x_data))
            all_timestamps = list(set(all_timestamps + timestamps))

            # Plot the data
            if num_of_results > 1:
                axis = ax[index_of_ax]
                plot_axis(
                    timestamps,
                    y_data,
                    axis,
                    fig,
                    name_of_result,
                    *y_limit_map.get(name_of_result, (None, None)),
                    origin_x_data,
                )
                index_of_ax = index_of_ax + 1
            elif num_of_results == 1:
                plot_axis(
                    timestamps,
                    y_data,
                    ax,
                    fig,
                    name_of_result,
                    *y_limit_map.get(name_of_result, (None, None)),
                    origin_x_data,
                )
                # ax.plot(timestamps, y_data, label=name_of_result, marker="o")
                # ax.set_ylabel(name_of_result)

        # Set X-axis ticks to show only the values from the timestamps list
        all_x_data.sort()
        all_timestamps.sort()
        if not self.run_options.is_finished:
            base = int(len(all_timestamps) / 5)
            if base == 0:
                base = 1
        # plt.xticks(
        #     all_timestamps[::base],
        #     [timestamp_str[11:19] for timestamp_str in all_x_data][::base],
        #     rotation=rotation,
        # )  # Rotate X-axis labels for better readability.

        # plt.title(title)  # Don't set the title of the plot in this way. When there is more than one subplot, this
        # way will not guarantee to display the title **above** all subplots.
        fig.suptitle(title, fontsize=13, wrap=True)
        plt.xlabel("timestamps")
        # plt.legend()
        # plt.grid(True)
        plt.subplots_adjust(left=left, bottom=bottom)
        plt.tight_layout()

        # Save the figure
        # plt.savefig(title)  # The plot will be saved in the current working directory.
        # Basically, the title of the figure will also be the filename of the figure,
        # but since `:` is not allowed in the filename, `_` is used instead.
        filename = title.replace(":", "_").replace(">", "")

        if self.run_options.is_finished:
            path_of_fig = os.path.join(
                self.context_manager.config.system.local_root,
                self.context_manager.config.system.sample,
                self.run_options.dir_prefix,
                f"{filename}.png",
            )
        else:
            path_of_fig = os.path.join(
                self.context_manager.config.system.local_root,
                self.context_manager.config.system.sample,
                self.run_options.dir_prefix,
                "Temp",
                f"{filename}.png",
            )

        self.put_png_to_s3(fig, [exp_name, physical_units])
        plt.savefig(f"{path_of_fig}")

        # Show the plot
        # plt.show()  # Remember to save the plot before calling `plt.show()`, as it clears the plot.

        plt.close()


def plot_axis(
    x_data, y_data, axis, fig, name_of_result, ymax=None, ymin=None, origin_x_data=None
):
    if isinstance(y_data[0], list):
        # new_x_data = list(range(len(x_data)))
        mesh_x, mesh_y = np.meshgrid(
            x_data, origin_x_data if origin_x_data else list(range(len(y_data[0])))
        )
        z_data = np.array(y_data).T
        map_z = np.reshape(z_data, (len(y_data[0]), len(x_data)), order="F")
        draw_ops = {
            "cmap": plt.cm.get_cmap("viridis"),
            "shading": "nearest",
        }
        mesh = axis.pcolormesh(mesh_x, mesh_y, map_z, **draw_ops)
        fig.colorbar(mesh, ax=axis, extend="both")
    else:
        if ymax is not None:
            axis.set_ylim(ymax=ymax)
        if ymin is not None:
            axis.set_ylim(ymin=ymin)
        axis.plot(x_data, y_data, label=name_of_result, marker="o")
        axis.grid(True)
    axis.set_ylabel(name_of_result)
    axis.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d %H:%M:%S"))
    axis.xaxis.set_major_locator(mdates.AutoDateLocator())
    plt.setp(axis.get_xticklabels(), rotation=45)
