# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/fit/pyQCat.analysis.fit.lorentzian.rst:2
msgid "pyQCat.analysis.fit.lorentzian"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:1
msgid "Single lorentz with background."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:3
msgid ""
"y = offset + \\frac{A \\cdot kappa}{\\pi \\cdot ((f - f0)^2 + {kappa}^2)}"
"\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:7
msgid "frequency sweep points in Hz"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:9
msgid "amplitude of the tallest/deepest Lorentzian structure in the data"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:11
msgid "the offset value of the tallest/deepest Lorentzian structure in the data."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:14
msgid "frequency of the tallest/deepest Lorentzian structure in the data."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.lorentzian:17
msgid "kappa (FWHM) of the tallest/deepest Lorentzian structure in the data."
msgstr ""

