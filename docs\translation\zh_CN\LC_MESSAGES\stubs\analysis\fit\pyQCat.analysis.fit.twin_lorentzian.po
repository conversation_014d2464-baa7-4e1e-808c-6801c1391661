# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/fit/pyQCat.analysis.fit.twin_lorentzian.rst:2
msgid "pyQCat.analysis.fit.twin\\_lorentzian"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:1
msgid "Twin lorentz with background."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:3
msgid ""
"y = \\frac{A_2\\cdot kappa_2}{\\pi\\cdot ((f-f_2)^2 + {kappa_2}^2)} +\n"
" \\frac{A\\cdot kappa}{\\pi\\cdot ((f-f_0)^2 + {kappa}^2)} + background\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:8
msgid "frequency sweep points in Hz."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:10
msgid "amplitude of the tallest/deepest Lorentzian structure in the data."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:13
msgid ""
"amplitude of the other Lorentzian structure in the data; since this "
"function is used for high power qubit spectroscopy, this parameter refers"
" to the Lorentzian structure corresponding to the gf/2 transition."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:18
msgid "frequency of the tallest/deepest Lorentzian structure in the data."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:21
msgid ""
"frequency of the other Lorentzian structure in the data; since this "
"function is used for high power qubit spectroscopy, this parameter refers"
" to the Lorentzian structure corresponding to the gf/2 transition."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:26
msgid "kappa (FWHM) of the tallest/deepest Lorentzian structure in the data."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:29
msgid ""
"kappa (FWHM) of the other Lorentzian structure in the data; since this "
"function is used for high power qubit spectroscopy, this parameter refers"
" to the Lorentzian structure corresponding to the gf/2 transition."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.twin_lorentzian:34
msgid "background offset."
msgstr ""

#~ msgid ""
#~ "Args: f (float):          frequency sweep "
#~ "points in Hz A (float):          "
#~ "amplitude of the tallest/deepest Lorentzian"
#~ " structure"
#~ msgstr ""

#~ msgid "in the data"
#~ msgstr ""

#~ msgid ""
#~ "A_gf_over_2 (float):    amplitude of the "
#~ "other Lorentzian structure in the"
#~ msgstr ""

#~ msgid ""
#~ "data; since this function is used "
#~ "for high power qubit spectroscopy, this"
#~ " parameter refers to the Lorentzian "
#~ "structure corresponding to the gf/2 "
#~ "transition"
#~ msgstr ""

#~ msgid ""
#~ "f0 (float):         frequency of the "
#~ "tallest/deepest Lorentzian structure"
#~ msgstr ""

#~ msgid ""
#~ "f0_gf_over_2 (float):   frequency of the "
#~ "other Lorentzian structure in the"
#~ msgstr ""

#~ msgid ""
#~ "kappa (float):      kappa (FWHM) of the"
#~ " tallest/deepest Lorentzian structure"
#~ msgstr ""

#~ msgid ""
#~ "kappa_gf_over_2 (float): kappa (FWHM) of "
#~ "the other Lorentzian structure in"
#~ msgstr ""

#~ msgid ""
#~ "the data; since this function is "
#~ "used for high power qubit spectroscopy,"
#~ " this parameter refers to the "
#~ "Lorentzian structure corresponding to the "
#~ "gf/2 transition"
#~ msgstr ""

#~ msgid "background (float):     background offset"
#~ msgstr ""

