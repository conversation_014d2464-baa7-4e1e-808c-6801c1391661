# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/06/15
# __author:       xw

from copy import deepcopy

import numpy as np

from ....errors import ExperimentOptionsError
from ....structures import Options
from ....tools import freq_to_amp, qarange, validate_ac_spectrum
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from .detune_width import SweepDetuneRabiWidth


class SweepDetuneRabiZamp(CompositeExperiment):
    _sub_experiment_class = SweepDetuneRabiWidth

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("d_start", float)
        options.set_validator("d_end", float)
        options.set_validator("d_step", int)
        options.set_validator("ac_branch", str)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.d_start = 5
        options.d_end = 25
        options.d_step = -5
        options.ac_branch = "right"

        return options

    async def _sync_composite_run(self):
        drive_freq = self.qubits[0].drive_freq

        d_start = self.experiment_options.d_start
        d_end = self.experiment_options.d_end
        d_step = self.experiment_options.d_step

        ac_spectrum, freq_max = validate_ac_spectrum(
            ac_spectrum=self.qubits[0].ac_spectrum,
            freq=drive_freq,
        )

        idle_point = self.qubits[0].idle_point
        if not idle_point:
            freq = np.floor(ac_spectrum[0] * 1000) / 1000
            self.qubits[0].drive_freq = freq
            self.qubits[0]._check_lo()
        else:
            freq = np.floor(drive_freq * 1000) / 1000
        rough_freq_list = qarange(freq - d_start, freq - d_end, d_step)

        z_amp_list = []
        for rough_freq in rough_freq_list:
            amp = freq_to_amp(
                physical_unit=self.qubits[0],
                freq=rough_freq,
                branch=self.experiment_options.ac_branch,
            )
            z_amp = amp - idle_point
            z_amp_list.append(z_amp)

        has_nan = np.isnan(z_amp_list).any()
        if has_nan:
            raise ExperimentOptionsError(
                self.label,
                key="z_amp_list",
                msg=f"{z_amp_list} has a Nan value, please check ac_spectrum or drive freq!",
            )

        target_qubit = self.qubits[0]
        for i, z_amp in enumerate(z_amp_list):
            base_ac_bias = deepcopy(self.ac_bias)
            z_amp = round(z_amp, 6)
            once_exp = deepcopy(self.child_experiment)
            once_child_exp = once_exp.child_experiment

            for qubit, _ in once_child_exp.compensates.items():
                if qubit.name == target_qubit.name:
                    readout_point = Options(amp=-z_amp, sigma=1.25, buffer=5)
                    qubit.readout_point = readout_point

            if base_ac_bias.get(target_qubit.name):
                old_awg_bias = base_ac_bias[target_qubit.name][1]
                base_ac_bias[target_qubit.name][1] = old_awg_bias + z_amp
                once_child_exp.ac_bias = base_ac_bias

            target_qubit.drive_freq = rough_freq_list[i]
            once_child_exp.qubit = target_qubit
            once_child_exp.qubits = [target_qubit]

            self._check_simulator_data(once_exp, i)

            once_exp.set_run_options(
                z_amp=z_amp, freq=rough_freq_list[i], idle_point=target_qubit.idle_point
            )
            once_exp.set_parent_file(
                self, f"z_amp={z_amp}-freq={rough_freq_list[i]}", i, len(z_amp_list)
            )
            await once_exp.run_experiment()
            if once_exp is not None and hasattr(once_exp, "analysis"):
                self._analysis = once_exp.analysis
                self._save_curve_analysis_plot(
                    save_mark=f"z_amp={z_amp}-freq={rough_freq_list[i]}"
                )
