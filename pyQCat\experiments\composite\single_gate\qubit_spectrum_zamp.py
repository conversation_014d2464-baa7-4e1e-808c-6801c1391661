# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/12/20
# __author:       xw

"""
QubitSpectrumZAmp experiment.
"""

import numpy as np

from ....analysis.library import QubitSpectrumZAmpAnalysis
from ....errors import ExperimentFlowError
from ....structures import MetaData, Options
from ....tools.utilities import qarange
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment, pyqlog
from ...single import CouplerSpectrum, QubitSpectrum


class QubitSpectrumZAmp(CompositeExperiment):
    _sub_experiment_class = QubitSpectrum

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("z_amp_list", list)
        options.z_amp_list = qarange(-0.4, 0.4, 0.015)

        options.run_mode = ExperimentRunMode.async_mode
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.sub_data_type = "amp_phase"
        options.set_validator("clear_base", bool)
        options.set_validator("diff_ratio", float)
        options.set_validator("point_nums", int)

        options.clear_base = False
        options.freq_list = None
        options.diff_ratio = 0.5
        options.point_nums = 5
        return options

    def _check_options(self):
        super()._check_options()
        if self.discriminator:
            self.analysis_options.sub_data_type = "I_Q"

        z_amp_list = self.experiment_options.z_amp_list
        result_name = self.analysis_options.result_name
        cd_exp = self.child_experiment
        qc_obj = cd_exp.qubits[0]
        if cd_exp.coupler and cd_exp.is_coupler_exp is False:
            result_name = cd_exp.coupler.name
            qc_obj = cd_exp.coupler
        elif cd_exp.is_coupler_exp is True and cd_exp.coupler:
            result_name = cd_exp.coupler.name
            qc_obj = cd_exp.coupler
        elif cd_exp.qubit:
            result_name = cd_exp.qubit.name
            qc_obj = cd_exp.qubit

        if not z_amp_list:
            min_val = min([qc_obj.dc_min, qc_obj.dc_max])
            max_val = max([qc_obj.dc_min, qc_obj.dc_max])
            z_amp_arr = np.round(
                np.arange(min_val - 0.01, max_val + 0.01, 0.01) - qc_obj.idle_point, 6
            )
            z_amp_list = z_amp_arr.tolist()
            self.set_experiment_options(z_amp_list=z_amp_list)

        self.set_analysis_options(result_name=result_name)

        self.set_run_options(
            x_data=z_amp_list,
            analysis_class=QubitSpectrumZAmpAnalysis,
        )

    def _metadata(self) -> MetaData:
        """Return experiment metadata for ExperimentData."""
        metadata = super()._metadata()
        drive_power = self.child_experiment.experiment_options.drive_power
        use_square = self.child_experiment.experiment_options.use_square
        band_width = self.child_experiment.experiment_options.band_width
        fine_flag = self.child_experiment.experiment_options.fine_flag

        metadata.draw_meta = {
            "drive_power": drive_power,
            "use_square": use_square,
            "band_width": band_width,
            "fine_flag": fine_flag,
        }
        return metadata

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        cd_exp = self.child_experiment
        for key, result in self.analysis.results.items():
            if key == "params":
                if cd_exp.coupler and cd_exp.is_coupler_exp is False:
                    result.extra["path"] = "Coupler.ac_spectrum.standard"
                elif cd_exp.is_coupler_exp is True and cd_exp.coupler:
                    result.extra["path"] = "Coupler.ac_spectrum.standard"
                else:
                    result.extra["path"] = "Qubit.ac_spectrum.standard"
                if result.value:
                    self.file.save_data(result.value, name="ac_params")

    def _setup_child_experiment(self, exp: "QubitSpectrum", index: int, value: float):
        """Set child_experiment some options."""
        child_exp = exp
        child_exp.run_options.index = index
        total = len(self.run_options.x_data)

        child_exp.set_parent_file(self, f"z_amp={value}", index, total)
        child_exp.set_experiment_options(z_amp=value)
        self._check_simulator_data(child_exp, index)

    def _handle_child_result(self, exp: "QubitSpectrum"):
        # collect child experiment result and provide it for parent.
        child_exp = exp
        idx = child_exp.run_options.index

        if child_exp.analysis is None:
            raise ExperimentFlowError(child_exp, "Analysis is None!")

        if self.analysis_options.freq_list is None:
            self.set_analysis_options(
                freq_list=child_exp.analysis.experiment_data.x_data
            )

    def _alone_save_result(self):
        """Save analysis extract z_amp & frequency values."""
        try:
            if self.analysis:
                better_data_key = self.analysis.options.better_data_key
                better_data = self.analysis.analysis_datas.get(better_data_key)
                x_data = better_data.x
                y_data = better_data.y
                self.file.save_data(x_data, y_data, name=f"{self}(z_amp-freq)")
        except Exception as e:
            pyqlog.error(f"Save result Error: {e}")


class CouplerSpectrumZAmp(QubitSpectrumZAmp):
    """CouplerSpectrumZAmp class."""

    _sub_experiment_class = CouplerSpectrum

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Define analysis options."""
        options = super()._default_analysis_options()

        options.sub_data_type = "I_Q"
        options.y_label = ["P0-freq [MHz]", "P1-freq [MHz]"]

        return options
