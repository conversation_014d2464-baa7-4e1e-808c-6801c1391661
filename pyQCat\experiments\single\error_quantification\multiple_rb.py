# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/27
# __author:       <PERSON><PERSON><PERSON>

"""
Standard RB Experiment class.
"""

import itertools
import pathlib
import time
from copy import deepcopy
from typing import List

import numpy as np

from ....analysis import ParameterRepr, RBAnalysis
from ....errors import ExperimentFieldError, ExperimentOptionsError, FileTypeError
from ....gate.notable_gate import (
    CLIFFORD_GATE_SET,
    GATE_2Q,
    S1_SET,
    S1_X2_SET,
    S1_Y2_SET,
    GateBucket,
    GateCollection,
)
from ....log import pyqlog
from ....structures import MetaData, Options
from ....tools import RandomType, qarange, seed_randomer
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment
from ..two_qubit_gate.swap_once import (
    validate_qubit_pair_cz_std,
    validate_two_qubit_exp_read_options,
)
from .single_rb import _CLIFFORD_DATA, clifford_random, rb_generator


class RBMultiple(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        interleaved_gate = ["None"]
        interleaved_gate.extend(GateCollection.double_gate_infos())

        options.set_validator("depth1", list)
        options.set_validator("depth2", list)
        options.set_validator("depth3", list)
        options.set_validator("depth4", list)
        options.set_validator("times", (1, 50))
        options.set_validator("interleaved_gate", interleaved_gate)
        options.set_validator("gate_split", bool)
        options.set_validator("open_seed", bool)
        options.set_validator("seed", str)
        options.set_validator("mode", ["dynamic", "cpp"])

        options.gate_split = True
        options.depth1 = qarange(2, 10, 2)
        options.depth2 = None
        options.depth3 = None
        options.depth4 = None
        options.times = 30
        options.interleaved_gate = None
        options.open_seed = False
        options.seed = None
        options.mode = "cpp"
        options.check_matrix = False

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.clifford = None
        options.clifford_matrix_list = None
        options.matrix_map = None
        options.quantum_circuit_list = None
        options.experiment_pulses = None
        options.gate_bucket = GateBucket()
        options.depths = []

        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []
        options.seed = 0

        options.gate_2q = GATE_2Q

        options.injection_func = [
            "_bind_cz_gate",
            "_generate_matrix_map",
            "_generate_clifford",
            "split_gate",
            "_gate_assemble",
            "_align_gate",
            "_generate_clifford_matrix",
            "_clifford_divide",
            "_check_interleaved",
            "_get_quantum_circuits",
            "_get_quantum_circuits2",
            "_add_gates",
            "_get_against_gate",
            "_absorb_i_gate",
            "_flatten_list",
            "_generate_pulse",
            "_gate_split",
            "_check_cpp_matrix",
        ]
        options.support_context = [StandardContext.CGC]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)

        options.depths = None
        options.k = None

        options.data_key = ["P00"]
        options.quality_bounds = [0.9, 0.85, 0.77]
        options.result_parameters = [
            ParameterRepr(name="p", repr="base"),
            ParameterRepr(name="r", repr="average error rate"),
            ParameterRepr(name="f", repr="average fidelity"),
            ParameterRepr(name="depth", repr="Max Depth"),
            ParameterRepr(name="std", repr="standard deviation"),
        ]

        return options

    @staticmethod
    def update_instrument(self):
        ql = self.run_options.ql
        qh = self.run_options.qh

        for channel in list({ql.readout_channel, qh.readout_channel}):
            sweep_delay = self._pulse_time_list[
                : len(self.run_options.experiment_pulses[ql]["XY"])
            ]
            self.sweep_readout_trigger_delay(channel, sweep_delay)

    def _check_options(self):
        super()._check_options()

        # set run depths
        depths = []
        depth1 = self.experiment_options.depth1
        depth2 = self.experiment_options.depth2
        depth3 = self.experiment_options.depth3
        depth4 = self.experiment_options.depth4
        for ds in [depth1, depth2, depth3, depth4]:
            if ds:
                depths.extend(ds)

        seed = self.experiment_options.seed or int(time.time())
        self.set_run_options(depths=depths, seed=int(seed))

        if rb_generator is None and self.experiment_options.mode == "cpp":
            self.experiment_options.mode = "dynamic"

        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)

        # bind base cz gate
        self._bind_cz_gate()

        # gate split check
        if self.experiment_options.gate_split:
            self._gate_split()

        if self.experiment_options.mode == "dynamic":
            # constructing two bit gate matrix mapper based on gate map
            self._generate_matrix_map()

            # construct a two bit clifford cluster
            self._generate_clifford()

            # construct matrix form of two bit clifford
            self._generate_clifford_matrix()

            # check interleaved gate, cz alignment
            self._check_interleaved()

            # get line gate operation set
            self._get_quantum_circuits()
        else:
            self._get_quantum_circuits2()

        # compile pulse
        self._generate_pulse()

        # set experiment options
        self.set_experiment_options(data_type="I_Q", is_dynamic=0, seed=str(seed))

        # set analysis options
        self.set_analysis_options(
            depths=self.run_options.depths,
            k=self.experiment_options.times,
            figsize=(12, 12),
        )

        # set run options
        self.set_run_options(
            x_data=np.asarray(depths).repeat(self.experiment_options.times),
            analysis_class=RBAnalysis,
            measure_qubits=[self.run_options.qh, self.run_options.ql],
        )

    @staticmethod
    def set_xy_pulses(self):
        experiment_pulses = self.run_options.experiment_pulses
        for qubit, pulses in experiment_pulses.items():
            if "XY" in pulses:
                self.play_pulse("XY", qubit, pulses.get("XY"))

    @staticmethod
    def set_z_pulses(self):
        experiment_pulses = self.run_options.experiment_pulses
        for qubit, pulses in experiment_pulses.items():
            self.play_pulse("Z", qubit, pulses.get("Z"))

    def _metadata(self) -> MetaData:
        """Set RB experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"K": self.experiment_options.times}
        if self.experiment_options.open_seed:
            metadata.draw_meta = {"Seed": self.experiment_options.seed}
        return metadata

    def _bind_cz_gate(self):
        gate_bucket = self.run_options.gate_bucket
        ql = self.run_options.ql
        qh = self.run_options.qh

        gate_bucket.bind_single_gates(ql)
        gate_bucket.bind_single_gates(qh)
        units = []
        units.extend(self.qubits)
        units.extend(self.couplers)
        gate_bucket.bind_cz_gates(self.qubit_pair, units)

    def _generate_matrix_map(self):
        """Generate the matrix form corresponding to the two bit gates

        Like:
            (I, I) = np.korn(I.matrix, I.matrix)
            (I, X) = np.korn(I.matrix, X.matrix)
        """
        matrix_map = {}

        # base gate matrix
        gate_bucket = self.run_options.gate_bucket

        single_gates_name = list(gate_bucket.gate_collector.single_gate_map.keys())
        two_qubit_gates_name = list(
            itertools.product(single_gates_name, single_gates_name)
        )
        for gate in two_qubit_gates_name:
            matrix_map[gate] = np.kron(
                gate_bucket.get_matrix(gate[0]), gate_bucket.get_matrix(gate[1])
            )

        # cz gate matrix
        matrix_map[("CZ", "CZ")] = gate_bucket.get_matrix("CZ")

        self.set_run_options(matrix_map=matrix_map)

    def _generate_clifford(self):
        clifford_gate_set = CLIFFORD_GATE_SET
        s1_y2_set = S1_Y2_SET
        s1_x2_set = S1_X2_SET
        s1_set = S1_SET

        if self.experiment_options.gate_split:
            clifford_gate_set = self.split_gate(clifford_gate_set)
            s1_y2_set = self.split_gate(s1_y2_set)
            s1_x2_set = self.split_gate(s1_x2_set)
            s1_set = self.split_gate(s1_set)

        clifford = []

        # c1_set and c1_set assemble (24 * 24 = 576)
        c1_c1_iter = itertools.product(
            deepcopy(clifford_gate_set), deepcopy(clifford_gate_set)
        )
        c1_c1_gates = [list(t) for t in c1_c1_iter]
        clifford.extend(c1_c1_gates)

        # c1, cz, s1 and c1, cz, y2 assemble (576 * 3 * 3 = 5184) c-not-like class
        s1_y2_iter = itertools.product(deepcopy(s1_set), deepcopy(s1_y2_set))
        s1_y2_gates = [list(t) for t in s1_y2_iter]
        mid_part_c_not = [["CZ"], ["CZ"]]
        c_not = self._gate_assemble(
            deepcopy(c1_c1_gates), deepcopy(mid_part_c_not), deepcopy(s1_y2_gates)
        )
        clifford.extend(c_not)

        # assemble (576 * 3 * 3 = 5184) i-swap-like class
        y2_x2_iter = itertools.product(deepcopy(s1_y2_set), deepcopy(s1_x2_set))
        y2_x2_gates = [list(t) for t in y2_x2_iter]
        mid_part_i_swap = [["CZ", "Y/2", "CZ"], ["CZ", "-X/2", "CZ"]]
        i_swap = self._gate_assemble(
            deepcopy(c1_c1_gates), deepcopy(mid_part_i_swap), deepcopy(y2_x2_gates)
        )
        clifford.extend(i_swap)

        # assemble 576 swap-like classes
        mid_part_swap = [
            ["CZ", "-Y/2", "CZ", "Y/2", "CZ"],
            ["CZ", "Y/2", "CZ", "-Y/2", "CZ", "Y/2"],
        ]
        swap = self._gate_assemble(deepcopy(c1_c1_gates), deepcopy(mid_part_swap))
        clifford.extend(swap)

        # CZ door alignment
        final_clifford = []
        for gates in clifford:
            gates_l, gates_r = self._align_gate(gates)
            final_clifford.append([gates_l, gates_r])

        self.set_run_options(clifford=final_clifford)

    @staticmethod
    def split_gate(clifford):
        new_clifford = []
        for gates in clifford:
            new_gates = []
            for gate in gates:
                if gate == "X":
                    new_gates.extend(["X/2", "X/2"])
                elif gate == "-X":
                    new_gates.extend(["-X/2", "-X/2"])
                elif gate == "Y":
                    new_gates.extend(["Y/2", "Y/2"])
                elif gate == "-Y":
                    new_gates.extend(["-Y/2", "-Y/2"])
                else:
                    new_gates.append(gate)
            new_clifford.append(new_gates)

        return new_clifford

    @staticmethod
    def _gate_assemble(head_gate: List, mid_gate: List, tail_gate: List = None):
        """gate assemble

        Assume we have:
            head_gate: [[[X, I], [Y, X]], [[I], [X, Y]]]
            mid_gate: [[CZ], [CZ]]
            tail_gate: [[[X, I], [Y, X]], [[I], [X, Y]]]

        gate assemble result as follows:
            1. [[X, I, CZ, X, I], [Y, X, CZ, Y, X]]
            2. [[X, I, CZ, I], [Y, X, CZ, X, Y]]
            3. [[I, CZ, X, I], [X, Y, CZ, Y, X]]
            4. [[I, CZ, I], [X, Y, CZ, X, Y]]

        Total length is len(head_gate) * len(tail)
        """
        gate_set = [
            [gate[0] + mid_gate[0], gate[1] + mid_gate[1]] for gate in head_gate
        ]
        if tail_gate:
            gate_res = []
            for tail in tail_gate:
                gate_res.extend(
                    [[gate[0] + tail[0], gate[1] + tail[1]] for gate in gate_set]
                )
            return gate_res
        return gate_set

    @staticmethod
    def _align_gate(clifford_gate: List):
        """CZ gate alignment operation

        In the clifford cluster of two qubit RB, the alignment of the first CZ
        gate must be ensured, and the length of the two gate operation lists
        must be equal.  The operation length between the two CZ gates in the
        two-bit Clifford cluster must be equal, so we only need to slave before
        the first CZ gate After the last CZ gate,  and fill I gate in the two
        bit door operation list. Let's say that our set of gates is:

        [X/2, Y/2, CZ, I, X/2, CZ, Y/2, Y/2]
        [CZ, I, I, CZ, Y/2]

        After alignment is:

        [X/2, Y/2, CZ, I, X/2, CZ, Y/2, Y/2]
        [  I,   I, CZ, I,   I, CZ, Y/2,   I]
        """
        gate_list = deepcopy(clifford_gate)
        num_c_zs = gate_list[0].count("CZ")
        if num_c_zs != gate_list[1].count("CZ"):
            raise Exception("dissimilar number of two qubit CZ gates")
        if num_c_zs > 0:
            gap = gate_list[0].index("CZ") - gate_list[1].index("CZ")
            if gap < 0:
                gate_list[0] = ["I" for _ in range(-gap)] + gate_list[0]
            elif gap > 0:
                gate_list[1] = ["I" for _ in range(gap)] + gate_list[1]
        len0 = len(gate_list[0])
        len1 = len(gate_list[1])
        if len0 < len1:
            gate_list[0].extend(["I" for _ in range(len1 - len0)])
        elif len0 > len1:
            gate_list[1].extend(["I" for _ in range(len0 - len1)])
        return gate_list[0], gate_list[1]

    def _generate_clifford_matrix(self):
        """Generate a matrix form for each individual in the clifford cluster

        The matrix form of the two qubit clifford cluster individuals is
        a 4 * 4 vector.
        """
        matrix_map = self.run_options.matrix_map
        clifford = self.run_options.clifford

        clifford_matrix_list = []
        for clifford_list in clifford:
            out = self._clifford_divide(clifford_list)
            matrix_temp = np.eye(4)
            for op in out:
                if op in matrix_map:
                    matrix_temp = np.dot(matrix_map[tuple(op)], matrix_temp)
                else:
                    raise ExperimentFieldError(
                        self.label, f"no find clifford gate {op}!"
                    )
            clifford_matrix_list.append(matrix_temp)

        self.set_run_options(clifford_matrix_list=clifford_matrix_list)

    @staticmethod
    def _clifford_divide(clifford_list):
        """After alignment, the two-bit computing Clifford cluster is
        reconstructed into a one-to-one corresponding form

        Gate operation: [[X, Y, I], [Y, I, X]]
        After the transformed: [(X, Y), (Y, I), (I, X)]
        """
        out = []
        for el0, el1 in zip(clifford_list[0], clifford_list[1]):
            out.append((el0, el1))
        return out

    def _check_interleaved(self):
        """Obtain Clifford cluster of two qubit RB experiment

        - First, 11520 two-qubit clifford clusters are generated by crossing;
        - Add the interleaved gate if it's not none;
        - CZ gate alignment operation.
        """
        interleaved_gate = self.experiment_options.interleaved_gate
        if interleaved_gate == "None":
            interleaved_gate = None

        pyqlog.log("EXP", f"interleaved gate = {interleaved_gate}")
        clifford = self.run_options.clifford
        clifford_matrix_list = self.run_options.clifford_matrix_list
        matrix_map = self.run_options.matrix_map

        if interleaved_gate:
            if interleaved_gate == "CZ":
                interleaved_gate_list = [["CZ"], ["CZ"]]
            else:
                raise ExperimentOptionsError(
                    self.label,
                    key="interleaved_gate",
                    value=interleaved_gate,
                    msg=f"Now only support CZ gate, but your input is {interleaved_gate}",
                )

            update_clifford = []
            int_gate_l, int_gate_r = self._align_gate(interleaved_gate_list)

            interleaved_matrix = np.eye(4)
            for gate_l, gate_r in zip(int_gate_l, int_gate_r):
                interleaved_matrix = np.dot(
                    matrix_map.get((gate_l, gate_r)), interleaved_matrix
                )

            for index, gates in enumerate(clifford):
                gates[0].extend(int_gate_l)
                gates[1].extend(int_gate_r)
                update_clifford.append(gates)
                matrix = clifford_matrix_list[index]
                clifford_matrix_list[index] = np.dot(interleaved_matrix, matrix)

            self.set_run_options(
                clifford=update_clifford, clifford_matrix_list=clifford_matrix_list
            )

    def _get_quantum_circuits(self):
        """Generate a list of gate operations

        - M individuals were randomly selected from clifford cluster to form a
            gate operation list;
        - Find its inverse gate in Clifford cluster, the matrix multiplication
            is approximately the identity matrix;
        - Eliminate redundant I gate, reduce the length of the spliced
            waveform.
        """
        clifford = self.run_options.clifford
        clifford_matrix_list = self.run_options.clifford_matrix_list
        depths = self.run_options.depths
        random_times = self.experiment_options.times

        random_gate_max_value = len(clifford) - 1
        quantum_circuit_list = []
        for circuit_depth in depths:
            for _ in range(random_times):
                quantum_circuit_cache = None
                quantum_circuit_matrix_cache = np.eye(4)
                for __ in range(circuit_depth):
                    seed = None
                    if self.experiment_options.open_seed is True:
                        seed = self.run_options.seed
                        self.run_options.seed += 1
                    random_gate_index = seed_randomer(
                        style=RandomType.randint, right=random_gate_max_value, seed=seed
                    )

                    random_gate = clifford[random_gate_index]
                    random_gate_matrix = clifford_matrix_list[random_gate_index]
                    quantum_circuit_matrix_cache = np.dot(
                        random_gate_matrix, quantum_circuit_matrix_cache
                    )
                    if quantum_circuit_cache is None:
                        quantum_circuit_cache = random_gate
                    else:
                        quantum_circuit_cache = self._add_gates(
                            quantum_circuit_cache, random_gate
                        )

                # Calculate the matrix form after the gate operation and get (consume: (47 - 12) / 47 = 74%)
                # the against gate (Matrix products are identity matrices).
                against_gate = self._get_against_gate(quantum_circuit_matrix_cache)

                # Add against gate
                quantum_circuit_cache = self._add_gates(
                    quantum_circuit_cache, against_gate
                )

                # Eliminate redundant I gates
                quantum_circuit_cache = self._absorb_i_gate(quantum_circuit_cache)

                # Save to list
                quantum_circuit_list.append(quantum_circuit_cache)

        self.set_run_options(quantum_circuit_list=quantum_circuit_list)

    def _get_quantum_circuits2(self):
        times = self.experiment_options.times

        rb44 = rb_generator.RB44()
        if not rb44.load_from_file(str(pathlib.Path(_CLIFFORD_DATA, "rb44.dat"))):
            raise FileTypeError("load rb44 data error!")

        quantum_circuit_list = []
        for circuit_depth in self.run_options.depths:
            for _ in range(times):
                cur_circuit = [[], []]

                # rb44 inverse gate
                sequence = clifford_random(
                    rb44,
                    circuit_depth,
                    self.run_options.seed,
                    self.experiment_options.interleaved_gate,
                )
                self.run_options.seed += 1

                # build quantum circuit
                for cir in sequence:
                    for i in range(2):
                        cur_circuit[i].extend(self.run_options.gate_2q[cir][i])

                # check matrix
                if self.experiment_options.check_matrix:
                    self._check_cpp_matrix(cur_circuit)

                quantum_circuit_cache = self._absorb_i_gate(cur_circuit)
                quantum_circuit_list.append(quantum_circuit_cache)

        self.set_run_options(quantum_circuit_list=quantum_circuit_list)

    @staticmethod
    def _add_gates(gate_list0, gate_list1):
        """Merge the two gate operation lists"""
        gate_list = deepcopy(gate_list0)
        gate_list[0].extend(gate_list1[0])
        gate_list[1].extend(gate_list1[1])
        return gate_list

    def _get_against_gate(self, gate_matrix):
        """Get against gate"""
        clifford_matrix_list = self.run_options.clifford_matrix_list
        clifford = self.run_options.clifford

        against_gate = None
        for i, m in enumerate(clifford_matrix_list):
            if abs(np.trace(np.dot(m, gate_matrix))) / 4 > 0.999:
                against_gate = clifford[i]
                break
        if against_gate is None:
            raise Exception(
                "Final gate could not be found "
                "(when interleaving: use a Clifford generator)."
            )
        return against_gate

    def _absorb_i_gate(self, quantum_circuit_cache):
        """Eliminate redundant I gates"""
        gate_list0, gate_list1 = quantum_circuit_cache
        n = len(gate_list0)
        cz_index0 = [ind for ind in range(len(gate_list0)) if gate_list0[ind] == "CZ"]
        cz_index1 = [ind for ind in range(len(gate_list1)) if gate_list1[ind] == "CZ"]
        if cz_index0 != cz_index1:
            raise Exception("dissimilar number of two qubit gates")
        cz_index = cz_index0

        if len(cz_index) == 0:
            return gate_list0, gate_list1
        else:
            gate_between_cz_list0 = []
            gate_between_cz_list1 = []
            gate_list_out0 = []
            gate_list_out1 = []
            if cz_index[0] != 0:
                gate_between_cz_list0.append(gate_list0[0 : cz_index[0]])
                gate_between_cz_list1.append(gate_list1[0 : cz_index[0]])
            for ind in range(len(cz_index) - 1):
                if cz_index[ind] + 1 < cz_index[ind + 1]:
                    gate_between_cz_list0.append(
                        gate_list0[cz_index[ind] + 1 : cz_index[ind + 1]]
                    )
                    gate_between_cz_list1.append(
                        gate_list1[cz_index[ind] + 1 : cz_index[ind + 1]]
                    )
            if cz_index[-1] != (n - 1):
                gate_between_cz_list0.append(gate_list0[cz_index[-1] + 1 :])
                gate_between_cz_list1.append(gate_list1[cz_index[-1] + 1 :])
            for gate_between_cz0, gate_between_cz1 in zip(
                gate_between_cz_list0, gate_between_cz_list1
            ):
                num_i0 = gate_between_cz0.count("I")
                num_i1 = gate_between_cz1.count("I")
                if (num_i0 + num_i1) > 1:
                    if num_i1 > num_i0:
                        for ind in range(num_i0):
                            gate_between_cz0.pop(gate_between_cz0.index("I"))
                            gate_between_cz1.pop(gate_between_cz1.index("I"))
                    else:
                        for ind in range(num_i1):
                            gate_between_cz0.pop(gate_between_cz0.index("I"))
                            gate_between_cz1.pop(gate_between_cz1.index("I"))
                if gate_between_cz0:
                    # sometimes gates between cz include only 'I',
                    # and would become an empty list
                    gate_list_out0.append(gate_between_cz0)
                    gate_list_out1.append(gate_between_cz1)
                gate_list_out0.append(["CZ"])
                gate_list_out1.append(["CZ"])
            if cz_index[0] == 0:
                gate_list_out0.insert(0, ["CZ"])
                gate_list_out1.insert(0, ["CZ"])
            if cz_index[-1] != n - 1:
                gate_list_out0.pop(-1)
                gate_list_out1.pop(-1)
            gate_list0 = self._flatten_list(gate_list_out0)
            gate_list1 = self._flatten_list(gate_list_out1)
            quantum_circuit_cache = [gate_list0, gate_list1]
            return quantum_circuit_cache

    @staticmethod
    def _flatten_list(gate_list):
        """Flatten the list of door actions

        Before flatten: [[X, Y], [I, I], [X/2, Y/2]]

        After flatten: [X, Y, I, I, X/2, Y/2]
        """
        return eval("[%s]" % repr(gate_list).replace("[", "").replace("]", ""))

    def _generate_pulse(self):
        experiment_pulses = {}

        quantum_circuits = self.run_options.quantum_circuit_list
        gate_bucket = self.run_options.gate_bucket
        qh = self.run_options.qh
        ql = self.run_options.ql

        for qubit in [qh, ql]:
            experiment_pulses[qubit] = {"XY": [], "Z": []}

        # play parking qubit pylse
        base_qubit_list = []
        base_qubit_list.extend(self.qubits)
        base_qubit_list.extend(self.couplers)
        parking_qubits = []

        for bit_name in gate_bucket.cz_gate.parking_qubits:
            for qubit in base_qubit_list:
                if qubit.name == bit_name:
                    parking_qubits.append(qubit)
                    experiment_pulses[qubit] = {"Z": []}
                    break

        for index, circuit in enumerate(quantum_circuits):
            qh_circuit, ql_circuit = circuit
            experiment_pulses[qh]["XY"].append(gate_bucket.get_xy_pulse(qh, qh_circuit))
            experiment_pulses[qh]["Z"].append(gate_bucket.get_z_pulse(qh, qh_circuit))
            experiment_pulses[ql]["XY"].append(gate_bucket.get_xy_pulse(ql, ql_circuit))
            experiment_pulses[ql]["Z"].append(gate_bucket.get_z_pulse(ql, ql_circuit))
            for qubit in parking_qubits:
                experiment_pulses[qubit]["Z"].append(
                    gate_bucket.get_z_pulse(qubit, qh_circuit)
                )

        self.set_run_options(experiment_pulses=experiment_pulses)

    def _gate_split(self):
        gate_2q = self.run_options.gate_2q
        new_gate_2q = []
        for gate2 in gate_2q:
            if ["X"] in gate2 or ["Y"] in gate2:
                c = []
                for g in gate2:
                    if "X" in g:
                        c.append(["X/2", "X/2"])
                    elif "Y" in g:
                        c.append(["Y/2", "Y/2"])
                    else:
                        c.append([g[0], "I"])
                new_gate_2q.append(c)
            else:
                new_gate_2q.append(gate2)
        self.run_options.gate_2q = new_gate_2q

    def _check_cpp_matrix(self, cur_circuit: List):
        assert len(cur_circuit[0]) == len(cur_circuit[1])
        matrix = np.eye(4)
        for i in range(len(cur_circuit[0])):
            c1 = cur_circuit[0][i]
            c2 = cur_circuit[1][i]
            if c1 == "CZ":
                cm = self.run_options.gate_bucket.get_matrix(c1)
            else:
                cm = np.kron(
                    self.run_options.gate_bucket.get_matrix(c1),
                    self.run_options.gate_bucket.get_matrix(c2),
                )
            matrix = np.dot(cm, matrix)
        assert round(abs(np.trace(matrix)) / 4, 8) == 1.0
