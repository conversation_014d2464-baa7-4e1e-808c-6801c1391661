# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.calculate_distortion.rst:2
msgid "pyQCat.analysis.algorithms.calculate\\_distortion"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion:1
msgid "Calculate distortion delay array, response array."
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion:4
msgid "List of delay array."
msgstr "畸变迭代测试得到 delay array 列表"

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion:7
msgid "List of response array."
msgstr "畸变迭代测试得到 response array 列表"

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion:10
msgid "Sample rate, unit: GHz."
msgstr "测试比特 Z ac线的采样率, 默认1.6, 单位 GHz"

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion:13
msgid "Tuple object, (delay_arr, response_arr)."
msgstr "计算后的 delay_arr, response_arr构成的两元素元组 (delay_arr, response_arr)"

#: of pyQCat.analysis.algorithms.distortion.calculate_distortion
msgid "Return type"
msgstr ""

