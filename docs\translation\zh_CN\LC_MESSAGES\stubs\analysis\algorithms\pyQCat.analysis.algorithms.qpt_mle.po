# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.qpt_mle.rst:2
msgid "pyQCat.analysis.algorithms.qpt\\_mle"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt_mle:1
msgid "State tomography with maximum-likelihood estimation."
msgstr "最大似然估计进行量子过程层析"

#: of pyQCat.analysis.algorithms.tomography.qpt_mle
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt_mle:3
msgid ""
"a list of 2D array of measured probabilites.  the list index indicates "
"the # rho prepared and processed by Chi for the 2D array, (see qst_mle) "
"The first index indicates which operation from Us was applied, while the "
"second index tells which measurement result this was (e.g. 000, 001, "
"etc.)."
msgstr ""
"测量概率的二维数组列表。 列表索引表示 Chi 为 2D 数组准备和处理的密度矩阵，"
"（参见 qst_mle）第一个索引表示应用了来自 Us 的哪个操作，而第二个索引表示这"
"是哪个测量结果（例如 000、001 等。 ）。"

#: of pyQCat.analysis.algorithms.tomography.qpt_mle:8
msgid "the unitary operations that were applied to the system before measuring."
msgstr "在测量之前应用于系统的酉运算。"

#: of pyQCat.analysis.algorithms.tomography.qpt_mle:10
msgid ""
"a 'fidelity' matrix, relating the actual or 'intrinsic' probabilities to "
"the measured probabilites, via pms = dot(F, pis).  If no fidelity matrix "
"is given, the identity will be used."
msgstr ""
"一个“保真度”矩阵，通过 pms = dot(F, pis) 将实际或“内在”概率与测量概率相关联。"
" 如果没有给出保真度矩阵，则使用恒等式。"

#: of pyQCat.analysis.algorithms.tomography.qpt_mle:13
msgid "an initial guess for the chi matrix, e.g. from linear qpt."
msgstr "对 chi 矩阵的初始猜测，例如从线性qpt。"

#~ msgid ""
#~ "diags_out - a list of 2D array "
#~ "of measured probabilites.  the list "
#~ "index"
#~ msgstr ""

#~ msgid ""
#~ "indicates the # rho prepared and "
#~ "processed by Chi for the 2D array,"
#~ " (see qst_mle) The first index "
#~ "indicates which operation from Us was"
#~ " applied, while the second index "
#~ "tells which measurement result this was"
#~ " (e.g. 000, 001, etc.)."
#~ msgstr ""

#~ msgid "Us - the unitary operations that were applied to the system before"
#~ msgstr ""

#~ msgid "measuring."
#~ msgstr ""

#~ msgid ""
#~ "F - a 'fidelity' matrix, relating "
#~ "the actual or 'intrinsic' probabilities"
#~ msgstr ""

#~ msgid ""
#~ "to the measured probabilites, via pms"
#~ " = dot(F, pis).  If no fidelity "
#~ "matrix is given, the identity will "
#~ "be used."
#~ msgstr ""

#~ msgid "chi0 - an initial guess for the chi matrix, e.g. from linear qpt."
#~ msgstr ""

