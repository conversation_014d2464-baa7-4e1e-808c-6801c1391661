# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.experiments.single.rst:2
msgid "pyQCat.experiments.single package"
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:5
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:8
msgid "pyQCat.experiments.single.amp\\_optimize module"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize:1
#: pyQCat.experiments.single.amp_optimize.AmpOptimize:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "AmpOptimize experiment."
msgstr ""

#: of pyQCat.experiments.single.amp_optimize:3
msgid ""
"Amplitude Optimize Experiment is used to calibrate the amplitude of the "
"single gate XY line waveform."
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize:1
#: pyQCat.experiments.single.ape_once.APE:1
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum:1
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1:1
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum:1
#: pyQCat.experiments.single.rabi.RabiScanAmp:1
#: pyQCat.experiments.single.rabi.RabiScanWidth:1
#: pyQCat.experiments.single.ramsey.Ramsey:1
#: pyQCat.experiments.single.single_rb.RBSingle:1
#: pyQCat.experiments.single.single_shot.SingleShot:1
#: pyQCat.experiments.single.state_tomography.StateTomography:1
#: pyQCat.experiments.single.t1.T1:1
#: pyQCat.experiments.single.xyz_timing.XYZTiming:1
msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:1
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:1
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum._default_experiment_options:1
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:1
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options:1
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:1
#: pyQCat.experiments.single.rabi.RabiScanWidth._default_experiment_options:1
#: pyQCat.experiments.single.ramsey.CouplerRamseyCrosstalk._default_experiment_options:1
#: pyQCat.experiments.single.ramsey.Ramsey._default_experiment_options:1
#: pyQCat.experiments.single.single_rb.RBSingle._default_experiment_options:1
#: pyQCat.experiments.single.single_shot.NMSingleShot._default_experiment_options:1
#: pyQCat.experiments.single.single_shot.SingleShot._default_experiment_options:1
#: pyQCat.experiments.single.t1.T1._default_experiment_options:1
#: pyQCat.experiments.single.t2.T2Ramsey._default_experiment_options:1
#: pyQCat.experiments.single.union_readout.UnionReadout._default_experiment_options:1
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:10
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:10
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum._default_experiment_options:9
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:13
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options:11
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:10
#: pyQCat.experiments.single.rabi.RabiScanWidth._default_experiment_options:6
#: pyQCat.experiments.single.ramsey.Ramsey._default_experiment_options:11
#: pyQCat.experiments.single.single_rb.RBSingle._default_experiment_options:11
#: pyQCat.experiments.single.single_shot.NMSingleShot._default_experiment_options:7
#: pyQCat.experiments.single.single_shot.SingleShot._default_experiment_options:8
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:13
#: pyQCat.experiments.single.t1.T1._default_experiment_options:8
#: pyQCat.experiments.single.t2.T2Ramsey._default_experiment_options:11
#: pyQCat.experiments.single.union_readout.UnionReadout._default_experiment_options:13
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:9
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:4
msgid ""
"amp_list (List, np.ndarray): Scan amp list. amp_init (float): Scan amp "
"initial value, default none. threshold (Tuple): Set scan amp range. "
"points (int): Set scan points number. theta_type (str): Support `θ` type,"
" normal \"Xpi\" or \"Xpi/2\". N (int): Number of gates, π gate or π/2 "
"gate."
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._metadata
#: pyQCat.experiments.single.amp_optimize.AmpOptimize.get_xy_pulse
#: pyQCat.experiments.single.ape_once.APE._default_analysis_options
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options
#: pyQCat.experiments.single.ape_once.APE._metadata
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum._default_analysis_options
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum._default_experiment_options
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum._metadata
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_analysis_options
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._metadata
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_analysis_options
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._metadata
#: pyQCat.experiments.single.rabi.CouplerRabiScanWidth._metadata
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_analysis_options
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options
#: pyQCat.experiments.single.rabi.RabiScanAmp._metadata
#: pyQCat.experiments.single.rabi.RabiScanWidth._default_analysis_options
#: pyQCat.experiments.single.rabi.RabiScanWidth._default_experiment_options
#: pyQCat.experiments.single.rabi.RabiScanWidth._metadata
#: pyQCat.experiments.single.ramsey.CouplerRamsey._metadata
#: pyQCat.experiments.single.ramsey.CouplerRamseyCrosstalk._default_experiment_options
#: pyQCat.experiments.single.ramsey.Ramsey._default_analysis_options
#: pyQCat.experiments.single.ramsey.Ramsey._default_experiment_options
#: pyQCat.experiments.single.ramsey.Ramsey._metadata
#: pyQCat.experiments.single.single_rb.RBSingle._default_analysis_options
#: pyQCat.experiments.single.single_rb.RBSingle._default_experiment_options
#: pyQCat.experiments.single.single_rb.RBSingle._default_run_options
#: pyQCat.experiments.single.single_rb.RBSingle._metadata
#: pyQCat.experiments.single.single_shot.NMSingleShot._default_experiment_options
#: pyQCat.experiments.single.single_shot.SingleShot._default_analysis_options
#: pyQCat.experiments.single.single_shot.SingleShot._default_experiment_options
#: pyQCat.experiments.single.single_shot.SingleShot._metadata
#: pyQCat.experiments.single.state_tomography.StateTomography._default_analysis_options
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options
#: pyQCat.experiments.single.state_tomography.StateTomography._default_run_options
#: pyQCat.experiments.single.state_tomography.StateTomography._metadata
#: pyQCat.experiments.single.t1.T1._default_analysis_options
#: pyQCat.experiments.single.t1.T1._default_experiment_options
#: pyQCat.experiments.single.t1.T1._metadata
#: pyQCat.experiments.single.t2.T2Ramsey._cal_rate_gap
#: pyQCat.experiments.single.t2.T2Ramsey._default_analysis_options
#: pyQCat.experiments.single.t2.T2Ramsey._default_experiment_options
#: pyQCat.experiments.single.union_readout.UnionReadout._default_experiment_options
#: pyQCat.experiments.single.union_readout.UnionReadout._default_run_options
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_run_options
#: pyQCat.experiments.single.xyz_timing.XYZTiming._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options:10
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_experiment_options:12
#: pyQCat.experiments.single.ape_once.APE._default_analysis_options:4
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:12
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum._default_analysis_options:4
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum._default_experiment_options:11
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_analysis_options:4
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:15
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_analysis_options:4
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options:23
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_analysis_options:4
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:12
#: pyQCat.experiments.single.rabi.RabiScanWidth._default_analysis_options:4
#: pyQCat.experiments.single.rabi.RabiScanWidth._default_experiment_options:8
#: pyQCat.experiments.single.ramsey.CouplerRamseyCrosstalk._default_experiment_options:4
#: pyQCat.experiments.single.ramsey.Ramsey._default_analysis_options:4
#: pyQCat.experiments.single.ramsey.Ramsey._default_experiment_options:13
#: pyQCat.experiments.single.single_rb.RBSingle._default_analysis_options:4
#: pyQCat.experiments.single.single_rb.RBSingle._default_experiment_options:13
#: pyQCat.experiments.single.single_rb.RBSingle._default_run_options:4
#: pyQCat.experiments.single.single_shot.NMSingleShot._default_experiment_options:9
#: pyQCat.experiments.single.single_shot.SingleShot._default_analysis_options:15
#: pyQCat.experiments.single.single_shot.SingleShot._default_experiment_options:10
#: pyQCat.experiments.single.state_tomography.StateTomography._default_analysis_options:8
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:15
#: pyQCat.experiments.single.state_tomography.StateTomography._default_run_options:11
#: pyQCat.experiments.single.t1.T1._default_analysis_options:11
#: pyQCat.experiments.single.t1.T1._default_experiment_options:10
#: pyQCat.experiments.single.t2.T2Ramsey._default_analysis_options:13
#: pyQCat.experiments.single.t2.T2Ramsey._default_experiment_options:13
#: pyQCat.experiments.single.union_readout.UnionReadout._default_experiment_options:15
#: pyQCat.experiments.single.union_readout.UnionReadout._default_run_options:8
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options:11
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:11
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_run_options:4
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options:1
#: pyQCat.experiments.single.single_shot.SingleShot._default_analysis_options:1
#: pyQCat.experiments.single.t1.T1._default_analysis_options:1
#: pyQCat.experiments.single.t2.T2Ramsey._default_analysis_options:1
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options:1
msgid "Default analysis options."
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options:8
#: pyQCat.experiments.single.single_shot.SingleShot._default_analysis_options:13
#: pyQCat.experiments.single.t1.T1._default_analysis_options:9
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options:9
msgid "Analysis Options:"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options:4
#: pyQCat.experiments.single.t1.T1._default_analysis_options:4
#: pyQCat.experiments.single.t2.T2Ramsey._default_analysis_options:4
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options:4
msgid "quality_bounds (Iterable[float]): The bounds value of the"
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options:5
#: pyQCat.experiments.single.single_shot.SingleShot._default_analysis_options:11
#: pyQCat.experiments.single.t1.T1._default_analysis_options:5
#: pyQCat.experiments.single.t2.T2Ramsey._default_analysis_options:5
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options:5
msgid "goodness of fit."
msgstr ""

#: of
#: pyQCat.experiments.single.amp_optimize.AmpOptimize._default_analysis_options:6
msgid ""
"is_plot (bool): Plot or not analysis fit photo. data_key (List[str]): "
"List of mark select data."
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._set_xy_pulses:1
#: pyQCat.experiments.single.amp_optimize.CouplerAmpOptimize._set_xy_pulses:1
#: pyQCat.experiments.single.ape_once.APE._set_xy_pulses:1
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum._set_xy_pulses:1
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._set_xy_pulses:1
#: pyQCat.experiments.single.ramsey.CouplerRamsey._set_xy_pulses:1
#: pyQCat.experiments.single.ramsey.Ramsey._set_xy_pulses:1
#: pyQCat.experiments.single.t1.CouplerT1._set_xy_pulses:1
#: pyQCat.experiments.single.t1.T1._set_xy_pulses:1
#: pyQCat.experiments.single.xyz_timing.XYZTiming._set_xy_pulses:1
msgid "Set XY pulses."
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:1
msgid "N不可为0"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:2
msgid "当theta_type为Xpi/2时, N不可以为奇数;"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:3
msgid "如果amp_list为空,则使用amp_init构造;"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:4
msgid "如果amp_init为空,则使用qubit获取;"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:5
msgid "data_key根据实验类型决定"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:7
msgid ""
"When `amp_list` and `amp_init` is both none, get xpi value from qubit and"
" assign it to `amp_init`, according to `theta_type` adjusts the output "
"results and selects the analyzed data. When processing amplitude and "
"phase data, both are processed; At the same time, we did some exception "
"handling:"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._check_options:11
msgid ""
"① when `theta_type` is not xpi and xpi/2; ② When n is 0; ③ When "
"`theta_type` is xpi/2 and N is odd;"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._metadata:1
#: pyQCat.experiments.single.ape_once.APE._metadata:1
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum._metadata:1
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._metadata:1
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._metadata:1
#: pyQCat.experiments.single.ramsey.CouplerRamsey._metadata:1
#: pyQCat.experiments.single.ramsey.Ramsey._metadata:1
#: pyQCat.experiments.single.single_shot.SingleShot._metadata:1
#: pyQCat.experiments.single.t1.T1._metadata:1
#: pyQCat.experiments.single.xyz_timing.XYZTiming._metadata:1
msgid "Set metadata."
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize._metadata:4
#: pyQCat.experiments.single.ape_once.APE._metadata:4
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum._metadata:4
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._metadata:4
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._metadata:4
#: pyQCat.experiments.single.rabi.CouplerRabiScanWidth._metadata:4
#: pyQCat.experiments.single.rabi.RabiScanAmp._metadata:4
#: pyQCat.experiments.single.rabi.RabiScanWidth._metadata:4
#: pyQCat.experiments.single.ramsey.CouplerRamsey._metadata:4
#: pyQCat.experiments.single.ramsey.Ramsey._metadata:4
#: pyQCat.experiments.single.single_rb.RBSingle._metadata:4
#: pyQCat.experiments.single.single_shot.SingleShot._metadata:4
#: pyQCat.experiments.single.state_tomography.StateTomography._metadata:7
#: pyQCat.experiments.single.t1.T1._metadata:4
#: pyQCat.experiments.single.xyz_timing.XYZTiming._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize.run:1
msgid "Run amp optimize experiment and perform analysis."
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.AmpOptimize.get_xy_pulse:2
msgid ":py:class:`~typing.List`"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.CouplerAmpOptimize:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
" :py:class:`~pyQCat.experiments.single.amp_optimize.AmpOptimize`"
msgstr ""

#: of pyQCat.experiments.single.amp_optimize.CouplerAmpOptimize:1
#: pyQCat.experiments.single:50:<autosummary>:1
msgid "Coupler Amp Optimize Experiment"
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:16
msgid "pyQCat.experiments.single.ape\\_once module"
msgstr ""

#: of pyQCat.experiments.single.ape_once:1
#: pyQCat.experiments.single.ape_once.APE:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "APE experiment."
msgstr ""

#: of pyQCat.experiments.single.ape_once:3
msgid ""
"The APE experiment is used to calibrate the detune parameters of the "
"single-gate XY line waveform."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._default_experiment_options:4
msgid ""
"sweep_name (str): Experiment purpose, \"detune\" or \"phase\". sweep_list"
" (List, np.ndarray): Scan detune or phase list. phi_num (int): Multiples "
"phase of π or π/2 pulse. theta_type (str): Support `θ` type, normal "
"\"Xpi\" or \"Xpi/2\"."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._default_experiment_options:8
msgid "Calibrate π gate or π/2 gate."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._default_experiment_options:9
msgid "N (int): Repeat times of pairing drag pulse."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._default_analysis_options:1
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum._default_analysis_options:1
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_analysis_options:1
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_analysis_options:1
#: pyQCat.experiments.single.rabi.RabiScanAmp._default_analysis_options:1
#: pyQCat.experiments.single.rabi.RabiScanWidth._default_analysis_options:1
#: pyQCat.experiments.single.ramsey.Ramsey._default_analysis_options:1
#: pyQCat.experiments.single.single_rb.RBSingle._default_analysis_options:1
msgid "Default kwarg options for analysis."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._check_options:1
msgid "sweep_name仅支持detune和phase两种类型;"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._check_options:2
msgid "theta_type仅支持Xpi和 Xpi/2两种类型;"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._check_options:3
msgid "如果用判据, 分析P0数据, 否则幅值相位数据均处理;"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._check_options:4
msgid "输出结果名称由sweep_name决定"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._check_options:5
msgid "x_lable由sweep_name决定"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._check_options:7
msgid ""
"`sweep_name` only supports two types: detune and phase; `theta_type` only"
" supports two types: xpi and xpi/2; If the criterion is used, process P0 "
"data, otherwise the amplitude and phase data are processed; The output "
"result name is determined by sweep name."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE.run:1
msgid "Run the APE once experiment."
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:24
msgid "pyQCat.experiments.single.cavity module"
msgstr ""

#: of pyQCat.experiments.single.cavity:1
msgid "CavityFreqSpectrum and CavityPowerOptimize experiment."
msgstr ""

#: of pyQCat.experiments.single.cavity:3
msgid ""
"CavityFreqSpectrum experiment is used to calibrate the qubit cavity "
"frequency. CavityPowerOptimize experiment is used to calibrate the read "
"power of the qubit."
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "CavityFreqSpectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.cavity.CavityFreqSpectrum._default_experiment_options:4
msgid ""
"fc_list (List, np.ndarray): Scan cavity frequency list. readout_power "
"(float): Set readout channel power. is_opt (bool): True or False, True "
"means set XY pulse. amp (int): When is_opt is True, XY drag pulse amp "
"value."
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum._check_options:1
msgid "Set the default fc_list"
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum._check_options:2
msgid "Set the default read power"
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum._check_options:4
msgid "If the cavity frequency is not measured when the X gate is added,"
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum._check_options:4
msgid ""
"the drive parameters will not be bound, and the pulse timing diagram will"
" not be drawn."
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum._check_options:7
msgid "If the IQ criterion is passed in the current experiment, a warning"
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum._check_options:7
msgid ""
"will be given, which is not recommended for the current experiment, and "
"the IQ criterion will be automatically blanked"
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum._update_instrument:1
#: pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._update_instrument:1
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._update_instrument:1
#: pyQCat.experiments.single.rabi.CouplerRabiScanWidth._update_instrument:1
#: pyQCat.experiments.single.rabi.RabiScanWidth._update_instrument:1
#: pyQCat.experiments.single.ramsey.CouplerRamsey._update_instrument:1
#: pyQCat.experiments.single.ramsey.Ramsey._update_instrument:1
#: pyQCat.experiments.single.single_rb.RBSingle._update_instrument:1
#: pyQCat.experiments.single.single_shot.NMSingleShot._update_instrument:1
#: pyQCat.experiments.single.t1.CouplerT1._update_instrument:1
#: pyQCat.experiments.single.t1.T1._update_instrument:1
#: pyQCat.experiments.single.union_readout.UnionReadout._update_instrument:1
msgid "Update instrument parameters before running."
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum.run:1
msgid "Run Cavity experiment."
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:32
msgid "pyQCat.experiments.single.distortion\\_t1\\_once module"
msgstr ""

#: of pyQCat.experiments.single.distortion_t1_once:1
msgid "Distortion T1 Experiment Once."
msgstr ""

#: of pyQCat.experiments.single.distortion_t1_once.DistortionT1:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "Once Distortion Test."
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:4
msgid ""
"z_amp (float): The const Zamp of Z line. gauss_sigma (float): The sigma "
"of GaussUP, GaussDown wave. gauss_width (float): The width of GaussUP, "
"GaussDown wave. const_width (float): The width of Constant wave. ta "
"(float): Set a width of Constant wave. tb (float): Set a width of "
"Constant wave. z_offset_list (List, np.ndarray): Scan Z offset range. "
"xy_delay (float): Set delay of XY pulse."
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.CouplerDistortionT1._set_xy_pulses:1
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._set_xy_pulses:1
#: pyQCat.experiments.single.single_rb.RBSingle._set_xy_pulses:1
#: pyQCat.experiments.single.single_shot.SingleShot._set_xy_pulses:1
#: pyQCat.experiments.single.state_tomography.StateTomography._set_xy_pulses:1
#: pyQCat.experiments.single.union_readout.UnionReadout._set_xy_pulses:1
msgid "Set XY pulse."
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.CouplerDistortionT1._set_z_pulses:1
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._set_z_pulses:1
#: pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._set_z_pulses:1
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._set_z_pulses:1
#: pyQCat.experiments.single.state_tomography.StateTomography._set_z_pulses:1
msgid "Set z pulse."
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._check_options:1
msgid "如果没有IQ判据, 给出警告"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._check_options:2
msgid "如果是比特畸变, 分析P1, 如果是Coupler畸变, 分析P0"
msgstr ""

#: of pyQCat.experiments.single.distortion_t1_once.DistortionT1.get_z_pulse:1
#: pyQCat.experiments.single.t1.T1.get_z_pulse:1
msgid "Get Z pulse list"
msgstr ""

#: of pyQCat.experiments.single.distortion_t1_once.DistortionT1.run:1
msgid "Run DistortionT1 Once experiment."
msgstr ""

#: of pyQCat.experiments.single.distortion_t1_once.CouplerDistortionT1:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
" :py:class:`~pyQCat.experiments.single.distortion_t1_once.DistortionT1`"
msgstr ""

#: of pyQCat.experiments.single.distortion_t1_once.CouplerDistortionT1:1
#: pyQCat.experiments.single:50:<autosummary>:1
msgid "Coupler Distortion T1 Once"
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:40
msgid "pyQCat.experiments.single.qubit\\_spectrum module"
msgstr ""

#: of pyQCat.experiments.single.qubit_spectrum:1
msgid ""
"QubitSpectrum experiment. QubitSpectrum is used to find qubit drive "
"frequency."
msgstr ""

#: of pyQCat.experiments.single.qubit_spectrum.QubitSpectrum:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "QubitSpectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options:4
msgid ""
"freq_list (List): Scan frequency list. drive_power (float): Set driver "
"power value. z_amp (float): Set Z line pulse amp. use_square (bool): XY "
"line pulse model,"
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options:8
msgid "True use square, False use chirp."
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options:9
msgid "band_width (float): When use_square is False,"
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options:10
msgid "set chirp pulse band width."
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options:11
msgid "fine_flag (bool): Fine scan or not, select analysis not same."
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options:15
msgid "Rough scan analysis options:"
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options:14
msgid ""
"rough_window_length (int): Set window_length for rough scan. "
"rough_freq_distance (float): Set freq_distance for rough scan."
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options:21
msgid "Fine scan analysis options:"
msgstr ""

#: of
#: pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._default_experiment_options:18
msgid ""
"fine_window_length (int): Set window_length for fine scan. "
"fine_freq_distance (float): Set freq_distance for fine scan."
msgstr ""

#: of pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._check_options:1
msgid "校验freq_list"
msgstr ""

#: of pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._check_options:2
msgid "校验drive_power"
msgstr ""

#: of pyQCat.experiments.single.qubit_spectrum.QubitSpectrum._check_options:3
msgid "根据是否细扫修改窗口大小"
msgstr ""

#: of pyQCat.experiments.single.qubit_spectrum.QubitSpectrum.run:1
msgid "Run QubitSpectrum experiment and perform analysis."
msgstr ""

#: of pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
" :py:class:`~pyQCat.experiments.single.qubit_spectrum.QubitSpectrum`"
msgstr ""

#: of pyQCat.experiments.single.qubit_spectrum.CouplerSpectrum._set_xy_pulses:1
#: pyQCat.experiments.single.rabi.CouplerRabiScanAmp._set_xy_pulses:1
#: pyQCat.experiments.single.rabi.CouplerRabiScanWidth._set_xy_pulses:1
#: pyQCat.experiments.single.rabi.RabiScanWidth._set_xy_pulses:1
#: pyQCat.experiments.single.xyz_timing.CouplerXYZTiming._set_xy_pulses:1
#: pyQCat.experiments.single.xyz_timing.CouplerXYZTiming._set_z_pulses:1
msgid "Set RabiScanWidth experiment XY pulses."
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:48
msgid "pyQCat.experiments.single.rabi module"
msgstr ""

#: of pyQCat.experiments.single.rabi:1
msgid "Rabi experiment for calibrating qubit xy pulse amplitude."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "Rabi experiment scan XY pulse amplitude."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:5
msgid "name (str): Indicates which type of pulse is selected"
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:5
msgid "to perform the experimentOnly 'Xpi' and 'Xpi/2' supported."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:7
msgid "amps (list, np.ndarray): The list of XY pulse amplitudes that"
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:8
#: pyQCat.experiments.single.rabi.RabiScanWidth._default_experiment_options:5
msgid "will be scanned in the experiment."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._default_experiment_options:9
msgid "drive_power (float): The driver power of qubit."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._set_xy_pulses:1
msgid "Set RabiScanAmp experiment XY pulses."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._metadata:1
msgid "Set RabiScanAmp experiment metadata."
msgstr ""

#: of pyQCat.experiments.single.rabi.CouplerRabiScanAmp._update_instrument:1
#: pyQCat.experiments.single.rabi.RabiScanAmp._update_instrument:1
msgid "Update drive power if setting."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp._check_options:1
#: pyQCat.experiments.single.rabi.RabiScanWidth._check_options:1
#: pyQCat.experiments.single.ramsey.Ramsey._check_options:1
#: pyQCat.experiments.single.single_rb.RBSingle._check_options:1
#: pyQCat.experiments.single.state_tomography.StateTomography._check_options:1
#: pyQCat.experiments.single.t1.T1._check_options:1
#: pyQCat.experiments.single.t2.T2Ramsey._check_options:1
msgid "Check Options."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanAmp.run:1
msgid "Run RabiScanAmp experiment and perform analysis."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanWidth:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "Rabi experiment scan XY pulse width."
msgstr ""

#: of
#: pyQCat.experiments.single.rabi.RabiScanWidth._default_experiment_options:6
msgid "widths (list, np.ndarray): The list of XY pulse widths that"
msgstr ""

#: of pyQCat.experiments.single.rabi.CouplerRabiScanWidth._metadata:1
#: pyQCat.experiments.single.rabi.RabiScanWidth._metadata:1
msgid "Set RabiScanWidth experiment metadata."
msgstr ""

#: of pyQCat.experiments.single.rabi.RabiScanWidth.run:1
msgid "Run RabiScanWidth experiment and perform analysis."
msgstr ""

#: of pyQCat.experiments.single.rabi.CouplerRabiScanAmp:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
" :py:class:`~pyQCat.experiments.single.rabi.RabiScanAmp`"
msgstr ""

#: of pyQCat.experiments.single.rabi.CouplerRabiScanAmp:1
#: pyQCat.experiments.single:50:<autosummary>:1
msgid "Coupler Rabi Scan Amp Experiment."
msgstr ""

#: of pyQCat.experiments.single.rabi.CouplerRabiScanWidth:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
" :py:class:`~pyQCat.experiments.single.rabi.RabiScanWidth`"
msgstr ""

#: of pyQCat.experiments.single.rabi.CouplerRabiScanWidth:1
#: pyQCat.experiments.single:50:<autosummary>:1
msgid "Coupler Rabi Scan Width Experiment."
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:56
msgid "pyQCat.experiments.single.ramsey module"
msgstr ""

#: of pyQCat.experiments.single.ramsey:1
msgid "Ramsey experiment for calibrating qubit frequency."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "Ramsey experiment to measure the frequency of a qubit."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey._default_experiment_options:5
#: pyQCat.experiments.single.single_rb.RBSingle._default_experiment_options:5
#: pyQCat.experiments.single.t2.T2Ramsey._default_experiment_options:5
msgid "delays (List, np.ndarray): The list of delays that will"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey._default_experiment_options:5
#: pyQCat.experiments.single.single_rb.RBSingle._default_experiment_options:5
#: pyQCat.experiments.single.t1.T1._default_experiment_options:5
#: pyQCat.experiments.single.t2.T2Ramsey._default_experiment_options:5
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:5
msgid "be scanned in the experiment, in nanoseconds."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey._default_experiment_options:8
#: pyQCat.experiments.single.single_rb.RBSingle._default_experiment_options:8
#: pyQCat.experiments.single.t2.T2Ramsey._default_experiment_options:8
msgid "fringe (float): A frequency shift in Hz that will be applied"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey._default_experiment_options:8
#: pyQCat.experiments.single.single_rb.RBSingle._default_experiment_options:8
#: pyQCat.experiments.single.t2.T2Ramsey._default_experiment_options:8
msgid ""
"by means of a virtual Z rotation to increase the frequency of the "
"measured oscillation."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey._default_experiment_options:10
#: pyQCat.experiments.single.single_rb.RBSingle._default_experiment_options:10
#: pyQCat.experiments.single.t2.T2Ramsey._default_experiment_options:10
msgid "zamp (float): A ac pulse amplitude."
msgstr ""

#: of pyQCat.experiments.single.ramsey.CouplerRamsey._set_z_pulses:1
#: pyQCat.experiments.single.ramsey.CouplerRamseyCrosstalk._set_z_pulses:1
#: pyQCat.experiments.single.ramsey.Ramsey._set_z_pulses:1
#: pyQCat.experiments.single.t1.CouplerT1._set_z_pulses:1
#: pyQCat.experiments.single.t1.T1._set_z_pulses:1
#: pyQCat.experiments.single.xyz_timing.XYZTiming._set_z_pulses:1
msgid "Set Z pulses."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey.run:1
msgid "Run ramsey experiment and perform analysis."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey.get_xy_pulse_pre:1
msgid "Get XY line wave"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey.get_xy_pulse:1
msgid "Get XY line wave."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey.get_z_pulse_pre:1
msgid "Get Z line wave."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey.get_z_pulse_pre:3
msgid "The Z line in the middle of the two half pi pulses plus add input amp."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey.get_z_pulse:1
msgid "Get Z line wave"
msgstr ""

#: of pyQCat.experiments.single.ramsey.RamseyCrosstalk:1
#: pyQCat.experiments.single.t2.T2Ramsey:1
msgid "Bases: :py:class:`~pyQCat.experiments.single.ramsey.Ramsey`"
msgstr ""

#: of pyQCat.experiments.single.ramsey.RamseyCrosstalk:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "Ramsey Experiments used for crosstalk."
msgstr ""

#: of pyQCat.experiments.single.ramsey.CouplerRamseyCrosstalk.play_bias_pulse:1
#: pyQCat.experiments.single.ramsey.RamseyCrosstalk.get_bias_pulse:1
msgid "Play pulse on bias qubit."
msgstr ""

#: of pyQCat.experiments.single.ramsey.RamseyCrosstalk.get_bias_pulse
msgid "Parameters"
msgstr ""

#: of pyQCat.experiments.single.ramsey.RamseyCrosstalk.get_bias_pulse:4
msgid "The list of delays that will be scanned in the experiment, in nanoseconds."
msgstr ""

#: of pyQCat.experiments.single.ramsey.RamseyCrosstalk.get_bias_pulse:9
msgid "The bias qubit voltage."
msgstr ""

#: of pyQCat.experiments.single.ramsey.RamseyCrosstalk.play_bias_pulse
msgid "Note"
msgstr ""

#: of pyQCat.experiments.single.ramsey.RamseyCrosstalk.play_bias_pulse:1
msgid "use for new coupler ac crosstalk."
msgstr ""

#: of pyQCat.experiments.single.ramsey.RamseyCrosstalk.get_qubit_str:1
msgid "Over write parent method."
msgstr ""

#: of pyQCat.experiments.single.ramsey.CouplerRamsey:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
" :py:class:`~pyQCat.experiments.single.ramsey.Ramsey`"
msgstr ""

#: of pyQCat.experiments.single.ramsey.CouplerRamsey:1
#: pyQCat.experiments.single:50:<autosummary>:1
msgid "Coupler ramsey experiment."
msgstr ""

#: of pyQCat.experiments.single.ramsey.CouplerRamseyCrosstalk:1
msgid "Bases: :py:class:`~pyQCat.experiments.single.ramsey.CouplerRamsey`"
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:64
msgid "pyQCat.experiments.single.single\\_rb module"
msgstr ""

#: of pyQCat.experiments.single.single_rb:1
msgid "Standard RB Experiment class."
msgstr ""

#: of pyQCat.experiments.single.single_rb.RBSingle:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "Standard single qubit randomized benchmarking experiment."
msgstr ""

#: of pyQCat.experiments.single.single_rb.RBSingle._default_run_options:1
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_run_options:1
msgid "Default options values for the experiment :meth:`run` method."
msgstr ""

#: of pyQCat.experiments.single.single_rb.RBSingle.run:1
msgid "Initialize a standard randomized benchmarking experiment."
msgstr ""

#: of pyQCat.experiments.single.single_rb.RBSingle._metadata:1
msgid "Set RB experiment metadata."
msgstr ""

#: of pyQCat.experiments.single.single_rb.RBSingle._get_quantum_circuits:1
msgid "todo"
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:72
msgid "pyQCat.experiments.single.single\\_shot module"
msgstr ""

#: of pyQCat.experiments.single.single_shot:1
msgid ""
"SingleShot is used to get the probability that the qubit is in state 0 "
"and state 1."
msgstr ""

#: of pyQCat.experiments.single.single_shot.SingleShot:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid ""
"In quantum bits, usually only read |0> or |1> probability. And this is "
"called projection measurement."
msgstr ""

#: of
#: pyQCat.experiments.single.single_shot.SingleShot._default_experiment_options:4
msgid ""
"is_check (bool): Is or not check discriminator quality. save_bin (bool): "
"Save bin file or not. save_mark (str): When plot, save png name add name "
"mark."
msgstr ""

#: of
#: pyQCat.experiments.single.single_shot.SingleShot._default_analysis_options:4
msgid ""
"n_clusters (int): Preset clustering number. method (str): classifier "
"model name, `GMM` or `KMeans` n_multiple (float): Calculate cluster "
"radius,"
msgstr ""

#: of
#: pyQCat.experiments.single.single_shot.SingleShot._default_analysis_options:7
msgid "multiple of standard deviation. Default set `3.0`."
msgstr ""

#: of
#: pyQCat.experiments.single.single_shot.SingleShot._default_analysis_options:9
msgid ""
"is_plot (bool): Set ``True`` to create figure for fit result. "
"quality_bounds (Iterable[float]): The bounds value of the"
msgstr ""

#: of
#: pyQCat.experiments.single.single_shot.SingleShot._default_analysis_options:12
msgid "result_parameters (List): result data key list."
msgstr ""

#: of pyQCat.experiments.single.single_shot.SingleShot._update_loop_num:1
msgid "Update experiment repeat and loop_num."
msgstr ""

#: of pyQCat.experiments.single.single_shot.NMSingleShot._check_options:1
#: pyQCat.experiments.single.single_shot.SingleShot._check_options:1
msgid "Before run, some operations."
msgstr ""

#: of pyQCat.experiments.single.single_shot.SingleShot.run:1
msgid "Run SingleShot experiment."
msgstr ""

#: of pyQCat.experiments.single.single_shot.SingleShot.save_bin_file:1
msgid "Save discriminator."
msgstr ""

#: of pyQCat.experiments.single.single_shot.NMSingleShot:1
#: pyQCat.experiments.single.union_readout.UnionReadout:1
msgid "Bases: :py:class:`~pyQCat.experiments.single.single_shot.SingleShot`"
msgstr ""

#: of pyQCat.experiments.single.single_shot.NMSingleShot:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "NM SingleShot Experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.single_shot.NMSingleShot._default_experiment_options:4
msgid ""
"sample_width (float): Sample width. readout_power (float): Readout power."
" readout_freq (float): Readout frequency."
msgstr ""

#: of pyQCat.experiments.single.single_shot.NMSingleShot.run:1
msgid "Run NM SingleShot experiment."
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:80
msgid "pyQCat.experiments.single.state\\_tomography module"
msgstr ""

#: of pyQCat.experiments.single.state_tomography:1
msgid "Quantum State Tomography experiment"
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "Quantum state tomography experiment."
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:9
msgid "# section: overview"
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:4
msgid ""
"Quantum state tomography (QST) is a method for experimentally "
"reconstructing the quantum state from measurement data."
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:7
msgid ""
"A QST experiment measures the state prepared by quantum circuit in "
"different measurement bases and post-processes the measurement data to "
"reconstruct the state."
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:13
msgid "# section: analysis_ref"
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography:12
msgid ":py:class:`StateTomographyAnalysis`"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:1
msgid "Default kwarg options for (QST) experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:4
msgid ""
"pre_gate (List, optional): Initial gate state for performing QST. "
"qubit_nums (int): Qubit numbers. base_gate (List): QST aiming at "
"projection direction,"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:7
msgid ""
"I gate means Z-axis projection measurement, X/2 gate means Y-axis "
"projection measurement, Y/2 gate means X-axis projection measurement."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_experiment_options:10
msgid ""
"gate_map (Dict): Basic Gate Collection, for details, see "
"`pyQCat.gate.GateCollection` class. is_dynamic (bool): When collection "
"data, is or not showing result data dynamically. show_results (bool): The"
" result information is not printed by default."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_run_options:1
msgid "Set run experiment operate options."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_run_options:9
#: pyQCat.experiments.single.union_readout.UnionReadout._default_run_options:6
msgid "Options:"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_run_options:4
msgid ""
"QL: Qubit object, which bit name is ql_name. QH: Qubit object, which bit "
"name is qh_name. parking_qubits (List): List of BaseQubit,"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_run_options:7
msgid "when name in parking_bits."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_analysis_options:1
msgid "Default kwarg options for (QST) analysis."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_analysis_options:6
#: pyQCat.experiments.single.t2.T2Ramsey._default_analysis_options:11
msgid "Analysis options:"
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._default_analysis_options:4
msgid ""
"use_mle (bool): Whether to compute the density matrix using maximum "
"likelihood estimation. is_plot (bool): Qubit numbers."
msgstr ""

#: of
#: pyQCat.experiments.single.state_tomography.StateTomography._set_measure_pulses:1
msgid "Set readout pulse."
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography._metadata:1
msgid "Return experiment metadata for ExperimentData."
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography._metadata:3
msgid ""
"Subclasses can override this method to add custom experiment metadata to "
"the returned experiment result data."
msgstr ""

#: of pyQCat.experiments.single.state_tomography.StateTomography.run:1
msgid "Run quantum state tomography experiment and perform analysis."
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:88
msgid "pyQCat.experiments.single.t1 module"
msgstr ""

#: of pyQCat.experiments.single.t1:1
msgid "T1 Experiment is used to get the qubit energy relaxation time."
msgstr ""

#: of pyQCat.experiments.single.t1.T1:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "T1 Experiment, get the qubit energy relaxation time."
msgstr ""

#: of pyQCat.experiments.single.t1.T1._default_experiment_options:5
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:5
msgid "delay_list (List, np.ndarray): The list of delays that will"
msgstr ""

#: of pyQCat.experiments.single.t1.T1._default_experiment_options:7
msgid "z_amp (float): A ac pulse amplitude."
msgstr ""

#: of pyQCat.experiments.single.t1.T1._default_analysis_options:6
msgid ""
"is_plot (bool): Plot or not analysis fit photo. data_key (List[str]): "
"List of mark select data. p0 (Dict): Initial value of fitting parameters."
msgstr ""

#: of pyQCat.experiments.single.t1.T1.run:1
msgid "Run T1 experiment."
msgstr ""

#: of pyQCat.experiments.single.t1.CouplerT1:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
" :py:class:`~pyQCat.experiments.single.t1.T1`"
msgstr ""

#: of pyQCat.experiments.single.t1.CouplerT1:1
#: pyQCat.experiments.single:50:<autosummary>:1
msgid "Coupler T1 Experiment"
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:96
msgid "pyQCat.experiments.single.t2 module"
msgstr ""

#: of pyQCat.experiments.single.t2:1
msgid "T2 experiment is used to get the qubit decoherence time."
msgstr ""

#: of pyQCat.experiments.single.t2.T2Ramsey:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "T2 Ramsey experiment."
msgstr ""

#: of pyQCat.experiments.single.t2.T2Ramsey._default_analysis_options:6
msgid "is_plot (bool): Set ``True`` to create figure for fit result."
msgstr ""

#: of pyQCat.experiments.single.t2.T2Ramsey._default_analysis_options:7
msgid "This is ``True`` by default."
msgstr ""

#: of pyQCat.experiments.single.t2.T2Ramsey._default_analysis_options:11
msgid "data_key (list): Select the data to be processed from the raw data."
msgstr ""

#: of pyQCat.experiments.single.t2.T2Ramsey._default_analysis_options:9
msgid "This is ``None`` by default which means all raw data need to be processed."
msgstr ""

#: of pyQCat.experiments.single.t2.T2Ramsey._modify_file_dirs:1
msgid "Modify file dirs."
msgstr ""

#: of pyQCat.experiments.single.t2.T2Ramsey.run:1
msgid "Run precise qubit_test T2."
msgstr ""

#: of pyQCat.experiments.single.t2.T2Ramsey.run_once:1
msgid "Run t2 ramsey experiment and perform analysis."
msgstr ""

#: of pyQCat.experiments.single.t2.T2Ramsey._guess_delays:1
msgid "Guess delay, fringe of T2Ramsey Experiment."
msgstr ""

#: of pyQCat.experiments.single.t2.T2Ramsey._cal_rate_gap:1
msgid "Calculate the min distance t2_rate to rate_threshold."
msgstr ""

#: of pyQCat.experiments.single.t2.T2Ramsey._cal_rate_gap:4
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.experiments.single.t2.CouplerT2Ramsey:1
msgid ""
"Bases: :py:class:`~pyQCat.experiments.single.ramsey.CouplerRamsey`, "
":py:class:`~pyQCat.experiments.single.t2.T2Ramsey`"
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:104
msgid "pyQCat.experiments.single.union\\_readout module"
msgstr ""

#: of pyQCat.experiments.single.union_readout:1
#: pyQCat.experiments.single.union_readout.UnionReadout:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "Union Readout Experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.union_readout.UnionReadout._default_experiment_options:4
msgid "adjust_opt (bool): Is or not optimize, or just refresh union dcm."
msgstr ""

#: of
#: pyQCat.experiments.single.union_readout.UnionReadout._default_experiment_options:7
msgid "union_readout_data (dict): When adjust_opt is True, according to"
msgstr ""

#: of
#: pyQCat.experiments.single.union_readout.UnionReadout._default_experiment_options:7
msgid "union_readout_data optimize."
msgstr ""

#: of
#: pyQCat.experiments.single.union_readout.UnionReadout._default_experiment_options:9
msgid "update_readout_args (bool): When adjust_opt is True,"
msgstr ""

#: of
#: pyQCat.experiments.single.union_readout.UnionReadout._default_experiment_options:10
msgid "update or not union_readout_data."
msgstr ""

#: of
#: pyQCat.experiments.single.union_readout.UnionReadout._default_experiment_options:11
msgid ""
"sample_rate (float): Sample rate of readout line. cali_amp_flag (bool): "
"Is or not, calculate amp of bit args."
msgstr ""

#: of
#: pyQCat.experiments.single.union_readout.UnionReadout._default_run_options:1
msgid "Default set run options of experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.union_readout.UnionReadout._default_run_options:4
msgid "rd_ch_bit_map (Dict): Readout channel and Qubit list map."
msgstr ""

#: of
#: pyQCat.experiments.single.union_readout.UnionReadout._set_measure_pulses:1
msgid "Set measure pulse."
msgstr ""

#: of pyQCat.experiments.single.union_readout.UnionReadout._check_options:1
msgid "Before run, should be some operation."
msgstr ""

#: of pyQCat.experiments.single.union_readout.UnionReadout.run:1
msgid "Run Union Readout experiment."
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:112
msgid "pyQCat.experiments.single.xyz\\_timing module"
msgstr ""

#: of pyQCat.experiments.single.xyz_timing:1
msgid "XYZTiming Experiment. Get bit the XY line relative delay with the Z line."
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.XYZTiming:1
#: pyQCat.experiments.single:34:<autosummary>:1
msgid "XYZTiming Experiment, get the qubit xy or z line delay value."
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:7
msgid ""
"const_delay (float): Set XY line const delay. z_amp (float): A ac pulse "
"amplitude."
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options:6
msgid ""
"is_plot (bool): Plot or not analysis fit photo. sample_rate (float): "
"Sample rate, unit GHz. data_key (List[str]): List of mark select data."
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.CouplerXYZTiming._check_options:1
#: pyQCat.experiments.single.xyz_timing.XYZTiming._check_options:1
msgid "Check options."
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.XYZTiming.run:1
msgid "Run XYZTiming experiment."
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.CouplerXYZTiming:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
" :py:class:`~pyQCat.experiments.single.xyz_timing.XYZTiming`"
msgstr ""

#: ../../source/api/pyQCat.experiments.single.rst:120
msgid "Module contents"
msgstr ""

#: of pyQCat.experiments.single:3
msgid "Single Experiment Library (:mod:`pyQCat.experiments.single`)"
msgstr ""

#: of pyQCat.experiments.single:5
msgid ""
"pyQCat single scan parameter experiments, and related SingleShot "
"experiments."
msgstr ""

#: of pyQCat.experiments.single:10
msgid "Single Qubit Experiment Classes"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`AmpOptimize <pyQCat.experiments.single.AmpOptimize>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`APE <pyQCat.experiments.single.APE>`\\ \\(inst\\, qubits\\[\\, "
"couplers\\, compensates\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`CavityFreqSpectrum "
"<pyQCat.experiments.single.CavityFreqSpectrum>`\\ \\(inst\\, qubits\\[\\,"
" couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`DistortionT1 <pyQCat.experiments.single.DistortionT1>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`QubitSpectrum <pyQCat.experiments.single.QubitSpectrum>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`RabiScanAmp <pyQCat.experiments.single.RabiScanAmp>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`RabiScanWidth <pyQCat.experiments.single.RabiScanWidth>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`Ramsey <pyQCat.experiments.single.Ramsey>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`RamseyCrosstalk <pyQCat.experiments.single.RamseyCrosstalk>`\\ "
"\\(inst\\, target\\_qubit\\, bias\\_qubit\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`RBSingle <pyQCat.experiments.single.RBSingle>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`SingleShot <pyQCat.experiments.single.SingleShot>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`NMSingleShot <pyQCat.experiments.single.NMSingleShot>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`T1 <pyQCat.experiments.single.T1>`\\ \\(inst\\, qubits\\[\\, "
"couplers\\, compensates\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`T2Ramsey <pyQCat.experiments.single.T2Ramsey>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`UnionReadout <pyQCat.experiments.single.UnionReadout>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`XYZTiming <pyQCat.experiments.single.XYZTiming>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`CZAssist <pyQCat.experiments.single.CZAssist>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`StateTomography <pyQCat.experiments.single.StateTomography>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid ""
":py:obj:`SwapOnce <pyQCat.experiments.single.SwapOnce>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:34:<autosummary>:1
msgid "SwapOnce scan z line pulse width list."
msgstr ""

#: of pyQCat.experiments.single:36
msgid "Single Coupler Experiment Classes"
msgstr ""

#: of pyQCat.experiments.single:50:<autosummary>:1
msgid ""
":py:obj:`CouplerAmpOptimize "
"<pyQCat.experiments.single.CouplerAmpOptimize>`\\ \\(inst\\, qubits\\, "
"couplers\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:50:<autosummary>:1
msgid ""
":py:obj:`CouplerDistortionT1 "
"<pyQCat.experiments.single.CouplerDistortionT1>`\\ \\(inst\\, qubits\\, "
"couplers\\)"
msgstr ""

#: of pyQCat.experiments.single:50:<autosummary>:1
msgid ""
":py:obj:`CouplerSpectrum <pyQCat.experiments.single.CouplerSpectrum>`\\ "
"\\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:50:<autosummary>:1
msgid ""
":py:obj:`CouplerRabiScanAmp "
"<pyQCat.experiments.single.CouplerRabiScanAmp>`\\ \\(inst\\, qubits\\, "
"couplers\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:50:<autosummary>:1
msgid ""
":py:obj:`CouplerRabiScanWidth "
"<pyQCat.experiments.single.CouplerRabiScanWidth>`\\ \\(inst\\, qubits\\, "
"couplers\\)"
msgstr ""

#: of pyQCat.experiments.single:50:<autosummary>:1
msgid ""
":py:obj:`CouplerRamsey <pyQCat.experiments.single.CouplerRamsey>`\\ "
"\\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:50:<autosummary>:1
msgid ""
":py:obj:`CouplerRamseyCrosstalk "
"<pyQCat.experiments.single.CouplerRamseyCrosstalk>`\\ "
"\\(target\\_qubit\\, ...\\)"
msgstr ""

#: of pyQCat.experiments.single:50:<autosummary>:1
msgid ""
":py:obj:`CouplerT1 <pyQCat.experiments.single.CouplerT1>`\\ \\(inst\\, "
"qubits\\, couplers\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:50:<autosummary>:1
msgid ""
":py:obj:`CouplerT2Ramsey <pyQCat.experiments.single.CouplerT2Ramsey>`\\ "
"\\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.single:50:<autosummary>:1
msgid ""
":py:obj:`CouplerXYZTiming <pyQCat.experiments.single.CouplerXYZTiming>`\\"
" \\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :py:class:`~pyQCat.experiments.single.amp_optimize.AmpOptimize`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " "
#~ ":py:class:`~pyQCat.experiments.single.distortion_t1_once.DistortionT1`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " "
#~ ":py:class:`~pyQCat.experiments.single.qubit_spectrum.QubitSpectrum`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :py:class:`~pyQCat.experiments.single.rabi.RabiScanAmp`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :py:class:`~pyQCat.experiments.single.rabi.RabiScanWidth`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.single.ramsey.Ramsey`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :py:class:`~pyQCat.experiments.single.ramsey.Ramsey`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.single.ramsey.CouplerRamsey`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.single.single_shot.SingleShot`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :py:class:`~pyQCat.experiments.single.t1.T1`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.single.ramsey.CouplerRamsey`, "
#~ ":py:class:`~pyQCat.experiments.single.t2.T2Ramsey`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :py:class:`~pyQCat.experiments.single.xyz_timing.XYZTiming`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`AmpOptimize <pyQCat.experiments.single.AmpOptimize>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`APE <pyQCat.experiments.single.APE>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, compensates\\, "
#~ "...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CavityFreqSpectrum "
#~ "<pyQCat.experiments.single.CavityFreqSpectrum>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`DistortionT1 "
#~ "<pyQCat.experiments.single.DistortionT1>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`QubitSpectrum "
#~ "<pyQCat.experiments.single.QubitSpectrum>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`RabiScanAmp <pyQCat.experiments.single.RabiScanAmp>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`RabiScanWidth "
#~ "<pyQCat.experiments.single.RabiScanWidth>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`Ramsey <pyQCat.experiments.single.Ramsey>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`RamseyCrosstalk "
#~ "<pyQCat.experiments.single.RamseyCrosstalk>`\\ "
#~ "\\(target\\_qubit\\, bias\\_qubit\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`RBSingle <pyQCat.experiments.single.RBSingle>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`SingleShot <pyQCat.experiments.single.SingleShot>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`NMSingleShot "
#~ "<pyQCat.experiments.single.NMSingleShot>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`T1 <pyQCat.experiments.single.T1>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, compensates\\, "
#~ "...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`T2Ramsey <pyQCat.experiments.single.T2Ramsey>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`UnionReadout "
#~ "<pyQCat.experiments.single.UnionReadout>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`XYZTiming <pyQCat.experiments.single.XYZTiming>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CouplerAmpOptimize "
#~ "<pyQCat.experiments.single.CouplerAmpOptimize>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CouplerDistortionT1 "
#~ "<pyQCat.experiments.single.CouplerDistortionT1>`\\ \\(inst\\,"
#~ " qubits\\, couplers\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CouplerSpectrum "
#~ "<pyQCat.experiments.single.CouplerSpectrum>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CouplerRabiScanAmp "
#~ "<pyQCat.experiments.single.CouplerRabiScanAmp>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CouplerRabiScanWidth "
#~ "<pyQCat.experiments.single.CouplerRabiScanWidth>`\\ \\(inst\\,"
#~ " qubits\\, couplers\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CouplerRamsey "
#~ "<pyQCat.experiments.single.CouplerRamsey>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CouplerRamseyCrosstalk "
#~ "<pyQCat.experiments.single.CouplerRamseyCrosstalk>`\\ "
#~ "\\(target\\_qubit\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CouplerT1 <pyQCat.experiments.single.CouplerT1>`\\ "
#~ "\\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CouplerT2Ramsey "
#~ "<pyQCat.experiments.single.CouplerT2Ramsey>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CouplerXYZTiming "
#~ "<pyQCat.experiments.single.CouplerXYZTiming>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid "data_keys根据实验类型决定"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :class:`pyQCat.experiments.single.amp_optimize.AmpOptimize`"
#~ msgstr ""

#~ msgid "设置默认的fc_list"
#~ msgstr ""

#~ msgid "设置默认的读取功率"
#~ msgstr ""

#~ msgid "如果不测量加X门时的腔频, 则不绑定驱动参数, 不绘制脉冲时序图"
#~ msgstr ""

#~ msgid "如果当前实验传入了IQ判据, 则给出警告, 当前实验不建议给, 并自动将IQ判据置空"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :class:`pyQCat.experiments.single.distortion_t1_once.DistortionT1`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :class:`pyQCat.experiments.single.qubit_spectrum.QubitSpectrum`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :class:`pyQCat.experiments.single.rabi.RabiScanAmp`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :class:`pyQCat.experiments.single.rabi.RabiScanWidth`"
#~ msgstr ""

#~ msgid ""
#~ "Constan pulse, half pi pulse, constant"
#~ " pulse(sweep delay), half pi pulse(phase"
#~ " change)."
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.single.ramsey.Ramsey`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :class:`pyQCat.experiments.single.ramsey.Ramsey`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.single.ramsey.CouplerRamsey`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.single.single_shot.SingleShot`"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :class:`pyQCat.experiments.single.t1.T1`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: :class:`pyQCat.experiments.single.ramsey.CouplerRamsey`,"
#~ " :class:`pyQCat.experiments.single.t2.T2Ramsey`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.coupler_experiment.CouplerBaseExperiment`,"
#~ " :class:`pyQCat.experiments.single.xyz_timing.XYZTiming`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`AmpOptimize <pyQCat.experiments.single.AmpOptimize>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`APE <pyQCat.experiments.single.APE>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, compensates\\, "
#~ "...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CavityFreqSpectrum "
#~ "<pyQCat.experiments.single.CavityFreqSpectrum>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`DistortionT1 <pyQCat.experiments.single.DistortionT1>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`QubitSpectrum <pyQCat.experiments.single.QubitSpectrum>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`RabiScanAmp <pyQCat.experiments.single.RabiScanAmp>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`RabiScanWidth <pyQCat.experiments.single.RabiScanWidth>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`Ramsey <pyQCat.experiments.single.Ramsey>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`RamseyCrosstalk "
#~ "<pyQCat.experiments.single.RamseyCrosstalk>`\\ "
#~ "\\(target\\_qubit\\, bias\\_qubit\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`RBSingle <pyQCat.experiments.single.RBSingle>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`SingleShot <pyQCat.experiments.single.SingleShot>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`NMSingleShot <pyQCat.experiments.single.NMSingleShot>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`T1 <pyQCat.experiments.single.T1>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, compensates\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`T2Ramsey <pyQCat.experiments.single.T2Ramsey>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`UnionReadout <pyQCat.experiments.single.UnionReadout>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`XYZTiming <pyQCat.experiments.single.XYZTiming>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CouplerAmpOptimize "
#~ "<pyQCat.experiments.single.CouplerAmpOptimize>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CouplerDistortionT1 "
#~ "<pyQCat.experiments.single.CouplerDistortionT1>`\\ \\(inst\\,"
#~ " qubits\\, couplers\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CouplerSpectrum "
#~ "<pyQCat.experiments.single.CouplerSpectrum>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CouplerRabiScanAmp "
#~ "<pyQCat.experiments.single.CouplerRabiScanAmp>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CouplerRabiScanWidth "
#~ "<pyQCat.experiments.single.CouplerRabiScanWidth>`\\ \\(inst\\,"
#~ " qubits\\, couplers\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CouplerRamsey <pyQCat.experiments.single.CouplerRamsey>`\\"
#~ " \\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CouplerRamseyCrosstalk "
#~ "<pyQCat.experiments.single.CouplerRamseyCrosstalk>`\\ "
#~ "\\(target\\_qubit\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CouplerT1 <pyQCat.experiments.single.CouplerT1>`\\ "
#~ "\\(inst\\, qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CouplerT2Ramsey "
#~ "<pyQCat.experiments.single.CouplerT2Ramsey>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CouplerXYZTiming "
#~ "<pyQCat.experiments.single.CouplerXYZTiming>`\\ \\(inst\\, "
#~ "qubits\\, couplers\\[\\, ...\\]\\)"
#~ msgstr ""

