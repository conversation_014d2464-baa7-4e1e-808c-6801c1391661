# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/17
# __author:       <PERSON><PERSON><PERSON>

import numpy as np

from pyQCat.analysis import OscillationAnalysis, ParameterRepr, FitOptions, CurveAnalysisData
from pyQCat.analysis.specification import FitModel
from pyQCat.structures import Options
from typing import List, Union


class DistortionPhaseScanAnalysis(OscillationAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.x_label = "phase (°)"
        options.data_key = ["P1"]
        options.correct_read = True
        options.fidelity_matrix = None
        options.result_parameters = [ParameterRepr(name='point', repr="max_p1_phase", unit="°")]
        options.quality_bounds = [0.98, 0.95, 0.85]

        options.fit_model = FitModel(
            fit_func=cos_phi,
            model_description=
            r"{\rm amp} \cos\left(x "
            r"- {\rm phase}\right) + {\rm base}",
        )
        return options

    def _guess_fit_param(
            self,
            fit_opt: FitOptions,
            data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Use fft guess initial fit parameters.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        y = data.y

        fit_opt.p0.set_if_empty(amp=(max(y) - min(y)) / 2, phase=0, base=(max(y) + min(y)) / 2)
        # set fit bounds.
        fit_opt.bounds.set_if_empty(
            amp=(0, np.inf),
        )

        return [fit_opt]

    def _extract_result(self, data_key: str, quality=None):
        analysis_data = self.analysis_datas[data_key]
        phase = analysis_data.fit_data.fitval("phase")
        self.results.point.value = phase

    def run_analysis(self):
        """Run analysis on experiment data.

        Notes:
            Sub analysis classes may need to override this method.
        """
        # Prepare for fitting
        self._initialize()

        # Run data fitting.
        _ = self._run_fitting()

        # Create figure and result data
        if self.analysis_datas:
            better_data_key, _ = self._evaluate_quality()
            self._extract_result(better_data_key)
        else:
            p0s = self.experiment_data.y_data['P0']
            p1s = self.experiment_data.y_data['P1']
            x, y, phase = self.phase_tomograph(
                p0s[0], p1s[0], p0s[1], p1s[1],
                self.options.correct_read, self.options.fidelity_matrix
            )
            self.results.x.value = round(x, 4)
            self.results.y.value = round(y, 4)
            self.results.phase.value = round(phase, 4)
        
        if self.options.is_plot:
            self._visualization()

    @staticmethod
    def phase_tomograph(p0_x, p1_x, p0_y, p1_y, hase_fix: bool = False, fidelity=None):
        if not hase_fix and fidelity is not None:
            px = np.c_[p0_x, p1_x]
            py = np.c_[p0_y, p1_y]
            f_inv = np.linalg.inv(fidelity)
            px = np.array([np.dot(f_inv, p) for p in px])
            py = np.array([np.dot(f_inv, p) for p in py])
            p1_x = px[:, 1][0]
            p1_y = py[:, 1][0]

        x = 1 - 2 * p1_x
        y = 1 - 2 * p1_y
        phase = np.angle(x + 1j * y)

        return x, y, phase


def cos_phi(x, amp, phase, base):
    # 与标准形式略有差异，主要是phase前面为负号
    return amp * np.cos(x - phase) + base
