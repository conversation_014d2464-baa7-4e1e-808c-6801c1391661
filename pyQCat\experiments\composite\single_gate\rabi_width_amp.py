# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/05/09
# __author:       <PERSON><PERSON><PERSON>

from ....analysis.library.detune_width_analysis import (
    CheckFreqRabiWidthAnalysis,
    SweepAmpWidthAnalysis,
)
from ....structures import MetaData, Options
from ....tools import qarange
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import RabiScanWidth, RabiScanWidthAmp


class SweepAmpRabiWidth(CompositeExperiment):
    """Experiment SweepAmpRabiWidth class."""

    _sub_experiment_class = RabiScanWidthAmp

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options."""
        options = super()._default_experiment_options()

        options.set_validator("amp_list", list)
        options.amp_list = qarange(0, 1, 0.01)
        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options."""
        options = super()._default_analysis_options()
        options.x_label = "Amp (V)"
        options.figsize = [12, 12]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default run options."""
        options = super()._default_run_options()
        options.power = None
        options.freq_list = []
        return options

    def _metadata(self) -> MetaData:
        """Set RabiScanWidth experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "power": (self.run_options.power, "dBm"),
            "alpha": (self.run_options.alpha, " "),
        }
        return metadata

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.amp_list,
            analysis_class=SweepAmpWidthAnalysis,
        )

    def _setup_child_experiment(self, exp: RabiScanWidthAmp, index: int, amp: float):
        exp.run_options.index = index
        total = len(self.run_options.x_data)
        describe = f"Amp={amp}"
        exp.set_parent_file(self, describe, index, total)
        exp.set_experiment_options(amp=amp)
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp: RabiScanWidthAmp):
        freq = exp.analysis.results.freq.value
        exp.analysis.provide_for_parent.update({"freq": freq})


class CheckFreqRabiWidth(CompositeExperiment):
    _sub_experiment_class = RabiScanWidth

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options."""
        options = super()._default_experiment_options()

        options.set_validator("freq_list", list)
        options.freq_list = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.subplots = (2, 2)
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.pass_freq = []
        options.osc_freq = []
        return options

    def _check_options(self):
        self.set_analysis_options(
            subplots=(len(self.experiment_options.freq_list), 2),
            result_name=self.child_experiment.qubit.name,
        )
        # Shq 2024/04/29
        # for async mode.
        self.run_options.x_data = self.experiment_options.freq_list
        self.run_options.analysis_class = CheckFreqRabiWidthAnalysis

    def _metadata(self) -> MetaData:
        """Set RabiScanWidth experiment metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "pass_freq": self.run_options.pass_freq,
            "osc_freq": self.run_options.osc_freq,
        }
        return metadata

    # Shq 2024/04/29
    # for async mode.
    def _setup_child_experiment(
        self, child_exp: "RabiScanWidth", index: int, freq: float
    ):
        child_exp.run_options.index = index
        size = len(self.run_options.x_data)
        child_exp.set_parent_file(self, f"fq={freq}", index, size)
        child_exp.set_experiment_options(drive_freq=freq)
        self._check_simulator_data(child_exp, index)

    # Shq 2024/04/29
    # for async mode.
    def _handle_child_result(self, child_exp: "RabiScanWidth"):
        if child_exp.analysis and child_exp.analysis.results.oscillating.value is True:
            index = child_exp.run_options.index
            freq = self.run_options.x_data[index]
            self.run_options.pass_freq.append(freq)
            self.run_options.osc_freq.append(
                child_exp.analysis_options.results.freq.value
            )
