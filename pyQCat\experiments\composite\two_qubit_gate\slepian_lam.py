# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/18
# __author:       <PERSON>

"""
SlepianLam Experiment.
"""

from typing import List, Union

import numpy as np

from ....analysis.library.slepian_lam_anlysis import (
    SlepianLamAnalysis,
    SlepianLamNumAnalysis,
)
from ....errors import ExperimentFlowError
from ....log import pyqlog
from ....structures import MetaData, Options
from ....tools.utilities import qarange
from ....types import ExperimentRunMode, QualityDescribe, StandardContext
from ...composite_experiment import CompositeExperiment
from ...single import SlepianLamNumOnce, SlepianLamOnce


class SlepianLam(CompositeExperiment):
    """Slepian Rough measure lam1, lam2 value."""

    _sub_experiment_class = SlepianLamOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("lam2_list", list)

        options.lam2_list = qarange(0.0, 0.2, 0.01)
        options.run_mode = ExperimentRunMode.async_mode
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default Analysis Options."""
        options = super()._default_analysis_options()

        options.set_validator("percent", float)
        options.set_validator("leak_threshold", float)

        options.x_label = "lam2"
        options.y_label = "lam1"
        options.percent = 0.2
        options.leak_threshold = 0.3
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default run options."""
        options = super()._default_run_options()

        options.select_lam1 = []
        options.select_lam2 = []
        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "child_x_data": self._experiments[0].run_options.x_data,
            "sub_key": self._experiments[0].analysis_options.data_key[0],
            "select_lam1": self.run_options.select_lam1,
            "select_lam2": self.run_options.select_lam2,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        result_name = self.analysis_options.result_name
        if self.qubit_pair:
            result_name = self.qubit_pair.name
        self.set_analysis_options(result_name=result_name)
        self.run_options.x_data = self.experiment_options.lam2_list
        self.run_options.analysis_class = SlepianLamAnalysis

    def _setup_child_experiment(
        self, s_exp: SlepianLamNumOnce, index: int, lam2: float
    ):
        s_exp.run_options.index = index

        total = len(self.run_options.x_data)
        describe = f"lam2={lam2}"
        s_exp.set_parent_file(self, describe, index, total)
        s_exp.set_experiment_options(
            lam2=lam2,
        )
        self._check_simulator_data(s_exp, index)

    def _handle_child_result(self, s_exp: SlepianLamNumOnce):
        if s_exp.analysis is None:
            raise ExperimentFlowError(
                self.label,
                "child exp without analysis object, maybe child exp error!",
            )

        elif s_exp.analysis.analysis_state("result"):
            t_point = s_exp.analysis.results.t_point.value
            target_lam1, target_p = t_point

        else:
            # replace with previous result
            target_lam1, target_p = [0, 0]
            pyqlog.log(
                "child exp without analysis result object, maybe child exp error!"
            )

        if s_exp.analysis.quality.descriptor in [
            QualityDescribe.perfect,
            QualityDescribe.normal
        ]:
            lam2 = s_exp.experiment_options.lam2
            self.run_options.select_lam1.append(target_lam1)
            self.run_options.select_lam2.append(lam2)
        s_exp.analysis.provide_for_parent.update({"lam1": target_lam1, "p": target_p})

    def _run_analysis(
        self, x_data: Union[List, np.ndarray], analysis_class: type(SlepianLamAnalysis)
    ):
        super()._run_analysis(x_data, analysis_class)
        file_name = f"{self.qubit_pairs[0].name}_select_lam1_lam2"
        self.file.save_data(
            np.asarray(self.run_options.select_lam1),
            np.asarray(self.run_options.select_lam2),
            name=file_name,
        )


class SlepianLamNum(CompositeExperiment):
    _sub_experiment_class = SlepianLamNumOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("cz_num_list", list)

        options.cz_num_list = None
        options.run_mode = ExperimentRunMode.async_mode
        options.support_context = [StandardContext.CGC]

        return options

    def _check_options(self):
        super()._check_options()
        self.run_options.x_data = self.experiment_options.cz_num_list
        self.run_options.analysis_class = SlepianLamNumAnalysis

    def _setup_child_experiment(
        self, s_exp: SlepianLamNumOnce, index: int, cz_num: int
    ):
        s_exp.run_options.index = index

        total = len(self.run_options.x_data)
        describe = f"cz_num={cz_num}"
        s_exp.set_parent_file(self, describe, index, total)
        s_exp.set_experiment_options(
            cz_num=cz_num,
        )
        self._check_simulator_data(s_exp, index)

    def _handle_child_result(self, s_exp: SlepianLamNumOnce):
        provide_field = s_exp.analysis_options.data_key[0]
        # bugfix: Failure to fit sub experiments will result in experimental termination
        if s_exp.analysis is None:
            raise ExperimentFlowError(
                self.label,
                "child exp without analysis object, maybe child exp error!",
            )
        else:
            # replace with previous result
            max_p = max(s_exp.analysis.analysis_datas.get(provide_field).y)
        s_exp.analysis.provide_for_parent.update({"max_p": max_p})
