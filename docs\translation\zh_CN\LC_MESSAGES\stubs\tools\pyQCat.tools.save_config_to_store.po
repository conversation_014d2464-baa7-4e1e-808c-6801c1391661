# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-29 13:55+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.save_config_to_store.rst:2
msgid "pyQCat.tools.save\\_config\\_to\\_store"
msgstr ""

#~ msgid ""
#~ "Save batch config file to MongoDB "
#~ "ConfigStore. Support the file postfix "
#~ "with dat, json, bin. Just save one"
#~ " object about file name to "
#~ "ConfigStore. First, query by file name,"
#~ " if exist update, else create a "
#~ "new object."
#~ msgstr ""

#~ msgid "Parameters"
#~ msgstr ""

#~ msgid "dir name of config files"
#~ msgstr ""

#~ msgid "Belong to some user data."
#~ msgstr ""

