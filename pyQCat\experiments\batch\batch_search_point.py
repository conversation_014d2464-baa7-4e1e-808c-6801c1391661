# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/19
# __author:       <PERSON><PERSON><PERSON>

import json
from pathlib import Path
from typing import List

import numpy as np

from ...log import pyqlog
from ...qubit import Qubit
from ...tools import qarange
from ..batch_experiment import EXP_TYPE, BatchExperiment


class BatchSearchPoint(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()

        # for idle point search
        options.idle_qs_flows = [
            "CavityFreqSpectrum",
            "QubitSpectrum",
        ]
        options.idle_check_flows = [
            "XpiDetection",
            "QubitFreqCalibration",
            "XpiDetection",
        ]
        options.idle_step = 0.01
        options.idle_points = 30

        # for read point search
        options.read_flows = [
            "CavityFreqSpectrum",
            "ReadoutFreqCalibrate",
            "ReadoutAmpCalibration",
            "SampleWidthOptimize",
            "SingleShot",
        ]
        options.points = 30
        options.amp_map = {}
        options.auto_set_readout_point = True

        # for xyz timing
        options.timing_flows = [
            "XYZTimingComposite",
        ]
        options.z_amp_list = [0.1, -0.1, 0.2, -0.2]

        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.idle_point_map = {}
        options.read_point_map = {}
        options.max_idle_length = 0
        options.max_read_length = 0
        options.qs_freq_map = {}
        options.pass_unit_f01_map = {}
        options.pass_unit_osc_freq_map = {}
        return options

    def _record_experiment(
        self, exp_name: str, exp: EXP_TYPE, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if exp_name == "QubitSpectrum":
            for unit in record.pass_units:
                self.run_options.qs_freq_map[unit] = [
                    float(v) for v in record.analysis_data.get(unit)["result"]["peaks"]
                ]

        if exp_name == "RabiScanWidth":
            for unit in record.pass_units:
                pre_osc_freq = self.run_options.pass_unit_osc_freq_map.get(unit, 0)
                cur_osc_freq = float(record.analysis_data.get(unit)["result"]["freq"])

                if cur_osc_freq > pre_osc_freq:
                    qubit = self.context_manager.chip_data.get_physical_unit(unit)
                    self.run_options.pass_unit_osc_freq_map[unit] = max(
                        pre_osc_freq, cur_osc_freq
                    )
                    self.run_options.pass_unit_f01_map[unit] = qubit.drive_freq

        return record

    def _divide_idle_point(self, parallel_unit):
        for unit in parallel_unit:
            qubit_obj = self.context_manager.chip_data.cache_qubit.get(unit)

            # init drive freq and baseband freq
            qubit_obj.drive_freq = 4500
            qubit_obj.XYwave.baseband_freq = 850

            if qubit_obj.tunable is False:
                pyqlog.log("EXP", f"{unit} tunable false, idle point set 0")
                point_list = [0]
            else:
                max_point = qubit_obj.dc_max
                min_point = qubit_obj.dc_min

                if self.experiment_options.idle_points:
                    point_list = np.linspace(
                        max_point, min_point, self.experiment_options.idle_points
                    ).tolist()
                else:
                    step = abs(self.experiment_options.idle_step) or 0.01
                    if max_point > min_point:
                        step = -step
                    point_list = qarange(max_point, min_point, step)

            self.run_options.idle_point_map[unit] = point_list
            self.run_options.max_idle_length = max(
                len(point_list), self.run_options.max_idle_length
            )

    def _change_idle_point(self, idle_index: int, units: List[str]):
        ok_units = []
        for unit in units:
            point_list = self.run_options.idle_point_map.get(unit)
            if idle_index < len(point_list):
                idle_point = point_list[idle_index]
                ok_units.append(unit)
                qubit = self.context_manager.chip_data.cache_qubit.get(unit)
                qubit.drive_freq = 4500
                qubit.XYwave.baseband_freq = 850
                qubit.inst.xy_gap = round(4500 - 850, 3)
                qubit.idle_point = idle_point - qubit.dc_max
                pyqlog.info(
                    f"[{self.label}]: Change {unit} idle point {qubit.idle_point}"
                )

        # bugfix: Switching idle points requires clearing pass_unit_f01_map
        self.run_options.pass_unit_f01_map.clear()
        self.run_options.pass_unit_osc_freq_map.clear()

        return ok_units

    def _divide_read_point(self, units: List):
        for unit in units:
            qubit: Qubit = self.context_manager.chip_data.cache_qubit.get(unit)

            # auto scope max and min
            if qubit.tunable:
                max_point = qubit.dc_max
                min_point = qubit.dc_min
                idle_point = qubit.idle_point

                step = round(
                    (max_point - min_point) / self.experiment_options.points, 5
                )
                mid_amp_list = qarange(max_point, min_point, -step)
                expect_amp_list = mid_amp_list
                expect_amp_list = [amp for amp in expect_amp_list if abs(amp) < 0.48]
                amp_list = [
                    round(amp - idle_point - max_point, 5) for amp in expect_amp_list
                ]
                distance = abs(np.array(amp_list) - qubit.readout_point.amp)
                sort_index = distance.argsort()
                amp_list = np.array(amp_list)[sort_index]
            else:
                amp_list = [0]

            self.run_options.read_point_map[unit] = amp_list
            self.run_options.max_read_length = max(
                len(amp_list), self.run_options.max_read_length
            )

    def _change_read_point(self, read_index: int, units: List[str]):
        working_units = []
        amp_map = self.run_options.read_point_map
        for unit in units:
            if read_index < len(amp_map.get(unit)):
                read_point = amp_map.get(unit)[read_index]
                qubit = self.context_manager.chip_data.cache_qubit.get(unit)
                qubit.Mwave.amp = 0.08
                qubit.readout_point.amp = read_point
                working_units.append(unit)
                pyqlog.info(f"Change {unit} readout point {read_point}")
        return working_units

    def _run_batch(self):
        all_units = self.experiment_options.physical_units
        self.context_manager.global_options.max_point_unit = all_units
        self._divide_idle_point(all_units)

        pass_units, fail_units = [], []

        # sweep idle point
        for idle_idx in range(self.run_options.max_idle_length):
            # set idle point
            qs_para_units = self._change_idle_point(idle_idx, all_units)
            if not qs_para_units:
                continue

            # run qs flows: CavityFreqSpectrum | QubitSpectrum
            qs_pass_units = self._run_flow(
                flows=self.experiment_options.idle_qs_flows,
                physical_units=qs_para_units,
            )
            if not qs_pass_units:
                continue

            # RabiScanWidth Check f01
            rw_pass_units = self._rabi_check(qs_pass_units)
            if not rw_pass_units:
                continue

            # auto generate readout point scan list
            self._divide_read_point(rw_pass_units)

            # parallel divide group
            idle_group_map = self.parallel_allocator_for_qc(rw_pass_units)

            # sweep group run idle check flows
            for group_name, idle_group in idle_group_map.items():
                # run idle flow: XpiDetection | QubitFreqCalibration | XpiDetection
                idle_pass_units = self._run_flow(
                    self.experiment_options.idle_check_flows,
                    physical_units=idle_group,
                    name=f"{group_name} idle check",
                )
                if not idle_pass_units:
                    continue

                # run readout search flow
                self._read_flow_prepare(idle_pass_units, "before")
                read_pass_units = self._run_flow(
                    self.experiment_options.read_flows,
                    physical_units=idle_pass_units,
                    name=f"{group_name} read check",
                )

                # sweep z amp to run time flows
                time_final_pass_units = []
                for zamp in self.experiment_options.z_amp_list:
                    if not read_pass_units:
                        continue

                    # update z amp options
                    self.change_regular_exec_exp_options(
                        exp_name="XYZTimingComposite",
                        options={"child_exp_options.z_pulse_params.amp": zamp},
                    )

                    # run XYZ Timing
                    time_cur_pass_units = self._run_flow(
                        self.experiment_options.timing_flows,
                        physical_units=read_pass_units,
                        name=f"{group_name} time check",
                    )

                    # record pass unit
                    if time_cur_pass_units:
                        time_final_pass_units.extend(time_cur_pass_units)
                        for unit in time_cur_pass_units:
                            read_pass_units.remove(unit)
                if not time_final_pass_units:
                    continue

                # sweep readout point after timing flows
                self._read_flow_prepare(time_final_pass_units, "after")
                for read_idx in range(self.run_options.max_read_length):
                    # change readout point
                    working_units = self._change_read_point(
                        read_idx, time_final_pass_units
                    )
                    if not working_units:
                        continue

                    # run readout point flow
                    read_pass_units = self._run_flow(
                        self.experiment_options.read_flows,
                        physical_units=working_units,
                        name=f"{group_name} read check",
                    )

                    # remove pass unit
                    if read_pass_units:
                        self._record_unit_status(read_pass_units)
                        for unit in read_pass_units:
                            time_final_pass_units.remove(unit)
                            all_units.remove(unit)
                            pass_units.append(unit)

        fail_units = all_units
        pyqlog.info(f"Batch end:\npass units: {pass_units}\nfail units: {fail_units}")

    def _record_unit_status(self, pass_units):
        for unit in pass_units:
            qubit = self.context_manager.chip_data.get_physical_unit(unit)
            with open(
                str(
                    Path(
                        Path(self.run_options.record_path).parent,
                        f"{qubit.name}_Idle({qubit.idle_point})_Read({qubit.readout_point.amp}).json",
                    )
                ),
                mode="w",
                encoding="utf-8",
            ) as fp:
                data = qubit.to_dict()
                json.dump(data, fp, indent=4, ensure_ascii=False)

    def _rabi_check(self, units):
        max_iter = 0

        # count max freq list
        for unit in units:
            max_iter = max(max_iter, len(self.run_options.qs_freq_map[unit]))

        # sweep rabi scan width
        for i in range(max_iter):
            p_units = []

            # Set possible qubit frequencies
            for unit in units:
                qs_freq_list = self.run_options.qs_freq_map[unit]
                if i < len(qs_freq_list):
                    qubit = self.context_manager.chip_data.get_physical_unit(unit)
                    qubit.drive_freq = qs_freq_list[i]
                    p_units.append(unit)

            # group and baseband_freq divide
            group_map = self.parallel_allocator_for_qc(p_units)

            # run all group rabi width
            for gn, group in group_map.items():
                self._run_flow(["RabiScanWidth"], group, name=f"{gn} rw")

        # collect pass units after rabi width
        pass_units = []
        for unit, f01 in self.run_options.pass_unit_f01_map.items():
            if unit in units:
                qubit = self.context_manager.chip_data.get_physical_unit(unit)
                qubit.drive_freq = f01
                pass_units.append(unit)

        return pass_units

    def _read_flow_prepare(self, units: List[str], mode: str):
        self.run_options.read_flow_state = mode

        if mode == "before":
            for unit in units:
                qubit: Qubit = self.context_manager.chip_data.get_physical_unit(unit)
                qubit.readout_point.amp = 0

            # update z amp options before xyz timing
            ss = self.params_manager.exp_map.get("SingleShot")
            ss.options_for_regular_exec["analysis_options"]["quality_bounds"] = [
                2,
                0.6,
                0.55,
                0.02,
            ]
        else:
            # update z amp options after xyz timing
            ss = self.params_manager.exp_map.get("SingleShot")
            ss.options_for_regular_exec["analysis_options"]["quality_bounds"] = [
                2,
                0.9,
                0.80,
                0.02,
            ]
