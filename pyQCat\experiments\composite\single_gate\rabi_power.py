# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/04/15
# __author:       <PERSON><PERSON>


import numpy as np

from ....analysis import RabiScanAmpPowerAnalysis, RabiScanAmpPowerFreqAnalysis
from ....structures import MetaData, Options
from ....tools import qarange
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import RabiScanAmp


class RabiScanAmpPower(CompositeExperiment):
    """Optimize One Field of Qubit."""

    _sub_experiment_class = RabiScanAmp

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()

        options.set_validator("powers", list)
        options.powers = qarange(-40, -10, 1)

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for optimize experiment."""
        options = super()._default_analysis_options()
        options.data_key = ["P0 mean"]
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()
        return options

    def _check_options(self):
        """Check options."""
        super()._check_options()
        powers = self.experiment_options.powers
        self.set_run_options(
            x_data=powers,
            analysis_class=RabiScanAmpPowerAnalysis,
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"qubit freq": (self.qubit.drive_freq, "MHz")}
        return metadata

    def _setup_child_experiment(self, exp: "RabiScanAmp", index: int, value: float):
        """Set child_experiment some options."""
        rabi_exp = exp
        rabi_exp.run_options.index = index
        total = len(self.run_options.x_data)

        rabi_exp.set_experiment_options(drive_power=value)

        rabi_exp.set_parent_file(self, f"drive_power={value}", index, total)
        self._check_simulator_data(rabi_exp, index)

    def _handle_child_result(self, exp: "RabiScanAmp"):
        # collect child experiment result and provide it for parent.
        rabi_exp = exp

        provide_field = self.analysis_options.data_key[0]
        P0_mean = np.mean(rabi_exp.experiment_data.y_data["P0"])

        rabi_exp.analysis.provide_for_parent.update({provide_field: P0_mean})


class RabiScanAmpPowerFreq(CompositeExperiment):
    """Optimize One Field of Qubit."""

    _sub_experiment_class = RabiScanAmpPower

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()

        options.set_validator("freq_list", list)
        options.freq_list = qarange(4000, 4200, 1)

        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for optimize experiment."""
        options = super()._default_analysis_options()
        options.data_key = ["P0 power mean"]
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()
        return options

    def _check_options(self):
        """Check options."""
        super()._check_options()
        freq_list = self.experiment_options.freq_list
        self.set_run_options(
            x_data=freq_list,
            analysis_class=RabiScanAmpPowerFreqAnalysis,
        )

    def _setup_child_experiment(
        self, exp: "RabiScanAmpPower", index: int, value: float
    ):
        """Set child_experiment some options."""
        rabi_exp = exp
        rabi_exp.run_options.index = index
        total = len(self.run_options.x_data)

        rabi_exp.qubit.inst.xy_gap = value - 900
        rabi_exp.qubit.drive_freq = value

        rabi_exp.set_parent_file(self, f"drive_freq={value} MHz", index, total)
        self._check_simulator_data(rabi_exp, index)

    def _handle_child_result(self, exp: "RabiScanAmpPower"):
        # collect child experiment result and provide it for parent.
        rabi_exp = exp

        provide_field = self.analysis_options.data_key[0]
        P0_power_mean = np.mean(rabi_exp.experiment_data.y_data["P0 mean"])

        rabi_exp.analysis.provide_for_parent.update({provide_field: P0_power_mean})
