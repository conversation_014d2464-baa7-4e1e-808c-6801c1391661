# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/09/15
# __author:       <PERSON><PERSON><PERSON>

from collections import defaultdict
from copy import deepcopy
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional, Union

from prettytable import PrettyTable

from ..analysis.algorithms.iqprobability import IQdiscriminator
from ..environment import Environment
from ..errors import ExperimentContextError, RunOptionsError
from ..log import pyqlog
from ..parameters import transform_coupler_to_qubits
from ..processor.chip_data import ChipConfigField, ChipData
from ..processor.hardware_manager import HardwareOffsetManager
from ..qubit import NAME_PATTERN, BaseQubitsType
from ..structures import INST_SELECT, CommonDict, Options, Singleton
from ..tools.utilities import (
    ac_spectrum_transform,
    connect_server,
    display_dict_as_table,
)
from .structures import (
    CCEReadoutType,
    Coupler,
    DivideType,
    EnvironmentBitResource,
    ExperimentContext,
    Instrument,
    PulseCorrection,
    PyqcatConfig,
    QDict,
    Qubit,
    StandardContext,
    WorkingType,
    generate_default_context_data,
    re,
)

if TYPE_CHECKING:
    from pyQCat.analysis.specification import AnalysisResult


class BaseContextManager:
    def __init__(
        self,
        config_data: Union[str, CommonDict, PyqcatConfig] = None,
        chip_data=None,
    ):
        self._config_data = None
        self._chip_data: ChipData = chip_data or ChipData()
        self._link_mongo = False
        self._context = None

        self.global_options = self.default_global_options()
        self.run_options = self.default_run_options()

        self._update_records = {}

        if config_data:
            self.config = config_data

        self.token = ""

    @property
    def chip_data(self):
        return self._chip_data

    @property
    def config(self):
        return self._config_data

    @config.setter
    def config(self, data: Union[str, CommonDict, PyqcatConfig]):
        if isinstance(data, PyqcatConfig):
            config = QDict(**data.to_dict())
        elif isinstance(data, Dict):
            config = QDict(**data)
        elif isinstance(data, str):
            config = QDict(**PyqcatConfig(filename=data).to_dict())
        else:
            raise ExperimentContextError(
                f"config data type ({type(data)}), no support!"
            )

        self._config_data = config

    @property
    def context(self):
        return self._context

    @property
    def update_records(self):
        return self._update_records

    @property
    def global_table(self):
        table = PrettyTable()
        table.field_names = ["Key", "Value"]
        for k, v in self.global_options.items():
            if k != "context_data":
                table.add_row([str(k), str(v)])
        table.max_width = 150
        return table

    @classmethod
    def default_global_options(cls) -> Options:
        options = Options()
        options.env_bits = []
        options.working_type = "awg_bias"
        options.divide_type = "character_idle_point"
        options.max_point_unit = []
        options.crosstalk = True
        options.xy_crosstalk = True
        options.online = False
        options.custom = False
        options.online_unit = []
        options.f12_opt_bits = []
        options.context_data = generate_default_context_data()
        options.custom_points = {}
        options.add_read_env_bits = []
        options.id = None
        return options

    @classmethod
    def default_run_options(cls) -> Options:
        options = Options()
        options.env_bit_objs = {}
        options.parallel = False
        options.idle_units = set()
        return options

    def set_global_options(self, **fields):
        for f in fields:
            if f not in self.global_options:
                raise RunOptionsError(
                    self.__class__.__name__,
                    key=f,
                    value=fields.get(f),
                    msg=f"field {f} option must be defined in advance!",
                )
        self.global_options.update(**fields)

        # feature 2024/03/06 zyc
        # If the env bits change, check the system environment and remove the bits
        # with goodness false from the environment bits
        if "env_bits" in fields:
            self._check_env_bits()

    def _check_env_bits(self):
        """Remove the bits with goodness false from the environment bits."""
        if self.chip_data.is_ready:
            env_bits = self.global_options.env_bits
            new_env_bits = []
            for bit in env_bits:
                bit_obj = self.chip_data.get_physical_unit(bit)
                if bit_obj:
                    if bit_obj.goodness is True:
                        new_env_bits.append(bit)
                    else:
                        pyqlog.info(f"{bit} goodness is False, remove from env bits!")
            self.global_options.env_bits = new_env_bits

    def refresh_chip_data(self, chip_data: CommonDict):
        """Called when refreshing chip data, initializing, or updating interface parameters."""
        if self._chip_data is None:
            self._chip_data = ChipData()

        if chip_data:
            if isinstance(chip_data, QDict):
                chip_data = chip_data.to_dict()

            for key in chip_data.keys():
                if key in self._update_records:
                    pyqlog.info(f"Refresh chip data, clear {key} update record!")
                    self._update_records.pop(key)

        self._chip_data.refresh(chip_data)

        # feature 2024/03/06 zyc
        # If the chip data change, check the system environment and remove the bits
        # with goodness false from the environment bits
        self._check_env_bits()

    def clear_cache(self):
        if self._chip_data is not None:
            self._chip_data.clear_cache()

    def update_single_result_to_context(self, result: "AnalysisResult") -> CommonDict:
        """Refresh chip data based on experimental results.

        Args:
            result (AnalysisResult): `BaseExperiment` result

            ```
            result.value = Any  # Any possible experimental results
            result.extra = {
                "name": "Physical unit name of influence of experimental results,
                    such as `q0`, `c0-1`, `q0q1`, `q0.c0-1`",
                "path": "Specific update path for experimental results"
            }
            ```

            Such as:

            1. For Qubit
                {
                    "name": "q1"
                    "path": "Qubit.probe_freq"
                }

            2. For Coupler
                {
                    "name": "c1-2"
                    "path": "Coupler.probe_XYwave.Xpi"
                }

            3. For QubitPair
                {
                    "name": "q1q2"
                    "path": "QubitPair.metadata.std.cz.params.q1.freq"
                }

            4. For Crosstalk
                {
                    "name": "q1.q2"  # q1 is target unit, q2 is bias unit
                    "path": "Crosstalk.ac_crosstalk"
                }
                {
                    "name": "c2-3.q2"  # c2-3 is target unit, q2 is bias unit
                    "path": "Crosstalk.dc_crosstalk"
                }

            5. For Hardware Offset
                {
                    "name": "q1" or "c1-2"  # XYZTiming Result,
                    "path": "Hardware.xyz_timing"
                }
                {
                    "name": "q1q2"  # ZZTiming Result,
                    "path": "Hardware.zz_timing"
                }

            6. For Character
                {
                    # Will be deprecated
                    "name": "q1" or "c1-2",
                    "path": "Compensate.(x_delay/z_delay)"
                }
                {
                    "name": "q1" or "c1-2",
                    "path": "Compensate.(z_compensate/z_distortion_type/z_distortion_width/z_distortion_ab/
                            z_distortion_sos/z_distortion_solist/z_distortion_tlist)"
                }

            7. For IQdiscriminator
                {
                    "name": "q0" or "c1-2",
                    "path": "IQdiscriminator.(01/02/012)"
                }

            8. The repetition of a certain situation above
                # value: [6400, 6500]
                {
                    "name" ["q0", "q1"],
                    "path": "Qubit.probe_freq"
                }

        Returns:
            Dict: _description_
        """

        def parse_path(extra_path: str):
            """Parse the extra path of experiment result.

            Args:
                extra_path (str): All possibilities described in the annotations
                    in method `update_single_result_to_context`

            Returns:
                Tuple[str, List]:
                    Path Type: Qubit, Coupler, QubitPair, Crosstalk, Hardware, Compensate, IQdiscriminator.
                    Path Line: All content after the path type.
            """
            if extra_path:
                res = extra_path.split(".")
                return res[0], res[1:]
            else:
                return None, []

        def parse_qubit(param_path: List, value: Any, unit_name: str):
            qubit = self.chip_data.cache_qubit.get(unit_name)

            if qubit:
                old_value = deepen_set_param_value(qubit, param_path, value)
                path_text = ".".join(param_path)
                qubit.check_lo()
                return {unit_name: {path_text: [old_value, value]}}

        def parse_coupler(param_path: List, value: Any, unit_name: str):
            coupler = self.chip_data.cache_coupler.get(unit_name)

            if coupler:
                old_value = deepen_set_param_value(coupler, param_path, value)
                path_text = ".".join(param_path)
                return {unit_name: {path_text: [old_value, value]}}

        def parse_qubit_pair(param_path: List, value: Any, unit_name: str):
            pair = self.chip_data.cache_qubit_pair.get(unit_name)

            if pair:
                old_value = deepen_set_param_value(pair, param_path, value)
                path_text = ".".join(param_path)
                return {unit_name: {path_text: [old_value, value]}}

        def parse_crosstalk(param_path: List, value: Any, unit_name: str):
            crosstalk = self.chip_data.cache_config.get(ChipConfigField.crosstalk)

            name_splits = unit_name.split(".")
            if len(name_splits) == 2:
                target_unit, source_unit = name_splits
                location_x = crosstalk["infos"].index(target_unit)
                location_y = crosstalk["infos"].index(source_unit)
                if param_path[0] == "ac_crosstalk":
                    old_value = crosstalk["ac_crosstalk"][location_x][location_y]
                    crosstalk["ac_crosstalk"][location_x][location_y] = value
                elif param_path[0] == "dc_crosstalk":
                    old_value = crosstalk["dc_crosstalk"][location_x][location_y]
                    crosstalk["dc_crosstalk"][location_x][location_y] = value
                else:
                    pyqlog.warning(f"converter crosstalk {param_path[0]} format error!")
                    return
                return {param_path[0]: {unit_name: [old_value, value]}}
            else:
                pyqlog.warning(
                    f"converter crosstalk unit_name {unit_name} format error!"
                )

        def parse_compensate(param_path: List, value: Any, unit_name: str):
            _path_type = param_path[0]
            character_data = self.chip_data.cache_config.get(ChipConfigField.character)
            hardware_offset_data = self.chip_data.cache_config.get(
                ChipConfigField.hardware_offset
            )

            if _path_type == "x_delay":
                if hardware_offset_data.get("apply") is True:
                    return
                old_value = character_data[unit_name]["hardware_offset"][0]
                character_data[unit_name]["hardware_offset"][0] = value
            elif _path_type == "z_delay":
                if hardware_offset_data.get("apply") is True:
                    return
                old_value = character_data[unit_name]["hardware_offset"][1]
                character_data[unit_name]["hardware_offset"][1] = value
            elif _path_type == "z_compensate":
                old_value = character_data[unit_name]["hardware_offset"][2]
                character_data[unit_name]["hardware_offset"][2] = value
            elif _path_type == "z_distortion_type":
                old_value = character_data[unit_name]["distortion_type"]
                character_data[unit_name]["distortion_type"] = value
            elif _path_type == "z_distortion_width":
                old_value = character_data[unit_name]["distortion_width"]
                character_data[unit_name]["distortion_width"] = value
            elif _path_type == "z_distortion_ab":
                old_value = character_data[unit_name]["distortion_ab"]
                character_data[unit_name]["distortion_ab"] = value
            elif _path_type == "z_distortion_sos":
                old_value = {}
                for key, cv in value.items():
                    old_value[key] = (
                        character_data[unit_name].get("distortion_sos").get(key)
                    )
                    character_data[unit_name]["distortion_sos"][key] = cv
            elif _path_type in ["z_distortion_solist", "z_distortion_tlist"]:
                pyqlog.warning(
                    "z_distortion_solist and z_distortion_tlist has been  deprecated!"
                )
                return
            else:
                pyqlog.error(f"no support {_path_type}!")
                return

            return {"Character": {f"{unit_name}.{_path_type}": [old_value, value]}}

        def parse_hardware(param_path: List, value: Any, unit_name: str):
            manager = HardwareOffsetManager.from_data(
                self.chip_data.cache_config.get(ChipConfigField.hardware_offset)
            )

            if manager.apply is False:
                return

            if param_path[0] == "zz_timing":
                manager.insert_zz_timing(*value)
            elif param_path[0] == "xyz_timing":
                manager.insert_xyz_timing(*value)
            else:
                pyqlog.error(f"Can't format {param_path} !")
                return

            self.chip_data.cache_config[ChipConfigField.hardware_offset] = (
                manager.to_origin_data()
            )

            return {"Hardware": {f"{unit_name}.offset.{param_path[0]}": value}}

        def parse_dcm(param_path: List, value: Any, unit_name: str):
            bin_name = f"{unit_name}_{param_path[0]}.bin"
            old_value = self.chip_data.cache_dcm.get(bin_name)
            if isinstance(value, IQdiscriminator):
                self.chip_data.cache_dcm[bin_name] = value
                return {"IQdiscriminator": {bin_name: [old_value, value]}}

        def parse_empty(*args, **kwargs):
            pyqlog.debug(f"converter path is None, {result} | {args} | {kwargs}")

        convertor = {
            "Qubit": parse_qubit,
            "Coupler": parse_coupler,
            "QubitPair": parse_qubit_pair,
            "Crosstalk": parse_crosstalk,
            "Compensate": parse_compensate,
            "Hardware": parse_hardware,
            "IQdiscriminator": parse_dcm,
            None: parse_empty,
            "None": parse_empty,
        }

        path_type, path_line = parse_path(result.extra.get("path"))
        extra_name = result.extra.get("name")

        update_info = dict()
        if not path_line:
            return update_info

        if isinstance(extra_name, list):
            for i, p_name in enumerate(extra_name):
                res_info = convertor[path_type](
                    param_path=path_line,
                    value=result.value[i],
                    unit_name=p_name,
                    # result=result,
                )
                if res_info:
                    update_info.update(res_info)
        else:
            update_info = convertor[path_type](
                param_path=path_line,
                value=result.value,
                unit_name=extra_name,
                # result=result,
            )

        if update_info:
            for k, v in update_info.items():
                if k in self._update_records:
                    self._update_records[k].update(v)
                else:
                    self._update_records[k] = v

        return update_info

    def update_chip_records(self, records: CommonDict, index: int = -1):
        """_summary_

        Args:
            records (Dict):
                1. Qubit
                    eg: {"q0": {"drive_freq": [6800, 6900]}}

                2. Coupler
                    eg: {"c0-1": {"drive_power": [-30, -21]}}

                3. QubitPair
                    eg: {"q0q1": {"metadata.std.cz.params.q0.freq": [None, 5200]}}

                4. Crosstalk:
                    eg: {"dc_crosstalk": {"q0.q1": [0, 0.1]}}

                5. Hardware:
                    eg: {
                        "Hardware": {
                            "q0.offset.xyz_timing": [1, 1, 10.125],
                            "q0q1.offset.zz_timing": [1, 2, 3, 0, 10, 20]
                        }
                    }

                6. Character
                    eg: {
                        "Character": {
                            "q0.x_delay": [0, 2],
                            "q1.z_delay": [0, 3],
                            "q2.z_distortion_sos": {
                                "Gaussian_filter_order": [[], [***]],
                                "Room_temperature_sos_filter": [[], []],
                            }
                        }
                    }

                7. IQdiscriminator
                    eg: {
                        "IQdiscriminator": {
                            "q0_01.bin": [None, dcm_obj]
                        }
                    }

            index (int, optional): _description_. Defaults to -1.
                -1: update
                0: backtrack
        """
        try:
            if not records:
                return
            for name, value in records.items():
                base_qubit = None
                if re.match(NAME_PATTERN.qubit, name):
                    base_qubit = self.chip_data.cache_qubit.get(name)
                elif re.match(NAME_PATTERN.coupler, name):
                    base_qubit = self.chip_data.cache_coupler.get(name)
                elif re.match(NAME_PATTERN.qubit_pair, name):
                    base_qubit = self.chip_data.cache_qubit_pair.get(name)

                if base_qubit:
                    for k, v in value.items():
                        deepen_set_param_value(base_qubit, k.split("."), v[index])
                        if isinstance(base_qubit, Qubit):
                            base_qubit.check_lo()
                elif name == "IQdiscriminator":
                    for k, v in value.items():
                        self.chip_data.cache_dcm[k] = v[index]
                elif name == "Character":
                    for k, v in value.items():
                        unit, field_name = k.split(".")
                        character_obj = self.chip_data.cache_config.get(
                            ChipConfigField.character
                        ).get(unit)
                        if field_name == "x_delay":
                            character_obj["hardware_offset"][0] = v[index]
                        elif field_name == "z_delay":
                            character_obj["hardware_offset"][1] = v[index]
                        elif field_name == "z_compensate":
                            character_obj["hardware_offset"][2] = v[index]
                        elif field_name == "z_distortion_type":
                            character_obj["distortion_type"] = v[index]
                        elif field_name == "z_distortion_width":
                            character_obj["distortion_width"] = v[index]
                        elif field_name == "z_distortion_ab":
                            character_obj["distortion_ab"] = v[index]
                        elif field_name == "z_distortion_sos":
                            for ck, cv in v[index].items():
                                character_obj["distortion_sos"][ck] = cv
                elif name == "Hardware":
                    hardware_offset_data = self.chip_data.cache_config.get(
                        ChipConfigField.hardware_offset
                    )
                    manager = HardwareOffsetManager.from_data(hardware_offset_data)
                    for k, v in value.items():
                        if k.endswith("xyz_timing"):
                            if index == -1:
                                manager.insert_xyz_timing(*v)
                            else:
                                manager.backtrack()
                        else:
                            if index == -1:
                                manager.insert_zz_timing(*v)
                            else:
                                manager.backtrack()
                                manager.backtrack()
                    self.chip_data.cache_config[ChipConfigField.hardware_offset] = (
                        manager.to_origin_data()
                    )

        except Exception as e:
            pyqlog.error(f"Refresh context error, because {e}!")

    def extract_hot_data(self, record_id: str):
        """Extract modified hot spot data."""

        hot_config_data = {}

        for unit, value in self._update_records.items():
            if re.match(NAME_PATTERN.qubit, unit):
                qubit = self.chip_data.cache_qubit.get(unit)
                update_param_list = list(value.keys())
                qubit.save_data(update_list=update_param_list, record_id=record_id)
            elif re.match(NAME_PATTERN.coupler, unit):
                coupler = self.chip_data.cache_coupler.get(unit)
                update_param_list = list(value.keys())
                coupler.save_data(update_list=update_param_list, record_id=record_id)
            elif re.match(NAME_PATTERN.qubit_pair, unit):
                pair = self.chip_data.cache_qubit_pair.get(unit)
                update_param_list = list(value.keys())
                pair.save_data(update_list=update_param_list, record_id=record_id)
            elif unit == "Crosstalk":
                hot_config_data[ChipConfigField.crosstalk.value] = (
                    self.chip_data.cache_config.get(ChipConfigField.crosstalk)
                )
            elif unit == "Hardware":
                hot_config_data[ChipConfigField.hardware_offset.value] = (
                    self.chip_data.cache_config.get(ChipConfigField.hardware_offset)
                )
            elif unit == "Character":
                hot_config_data[ChipConfigField.character.value] = (
                    self.chip_data.cache_config.get(ChipConfigField.character)
                )
            else:
                pass

        # self._update_records.clear()

        return hot_config_data

    def rollback(self, record_type: str, record: CommonDict):
        """_summary_

        Args:

            record_type (str):
                - Qubit
                - Coupler
                - QubitPair
                - Crosstalk
                - Hardware
                - Compensate
                - IQdiscriminator

            record (Dict): _description_

            {
                "q0": {
                    "probe_freq": [6000, 6010]
                },
                "c0-1": {
                    "probe_power": [-30, -31]
                },
                "q1q2": {
                    "metadata.std.cz.params.q1.freq": [4500, 4600]
                },
                "q0": {
                    "q0_01.bin": [IQdiscriminator, IQdiscriminator],
                },
                "Hardware": {
                    "q0.offset.xyz_timing": (1, 1, 10),
                    "q0q1.offset.zz_timing": (1, 2, 3, 0, 5, 10)
                },
                "ac_crosstalk": {
                    "q0.q1": [0, 0.1]
                },
                "dc_crosstalk": {
                    "q1.c0-1": [0, 0.2]
                }
                "character": {
                    "q0.x_delay": [0, 10],  # 待弃用
                    "q0.z_delay": [0, 10],  # 待弃用
                    "q0.z_compensate": [0, 10],  # 未启用
                    "q0.z_distortion_type": ["width", "sos"],
                    "q0.z_distortion_width": [0, 2000],
                    "q0.z_distortion_ab": [[], []],
                    "q0.z_distortion_sos": [[], []],
                    "q0.z_distortion_solist": [[], []],
                    "q0.z_distortion_tlist": [[], []],
                }
            }
        """

        def deep_record_dict_param(target, param_path: Union[str, List[str]], old_key):
            if isinstance(param_path, str):
                setattr(target, param_path, old_key)
            elif isinstance(param_path, list) and len(param_path) >= 1:
                if len(param_path) == 1:
                    setattr(target, param_path[0], old_key)
                else:
                    deep_record_dict_param(
                        getattr(target, param_path[0]), param_path[1:], old_key
                    )

        def rollback_physical_unit(record_type_, record_):
            unit_obj = self.chip_data.get_physical_unit(record_type_)
            for pre_path in record_:
                if "spectrum" in pre_path:
                    spectrum_type, label, *_ = pre_path.split(".")
                    getattr(unit_obj, spectrum_type)[label] = record_[pre_path][0]
                else:
                    deep_record_dict_param(
                        target=unit_obj,
                        param_path=pre_path.split("."),
                        old_key=record_[pre_path][0],
                    )

        def rollback_qubit_pair(record_type_, record_):
            qubit_pair = self.chip_data.cache_qubit_pair.get(record_type_)
            for pre_path in record_:
                deep_record_dict_param(
                    target=qubit_pair,
                    param_path=pre_path.split("."),
                    old_key=record_[pre_path][0],
                )

        def rollback_crosstalk(record_type_, record_):
            for param_name, val_list in record_.items():
                name_res = param_name.split(".")
                if len(name_res) == 2:
                    target_qubit, source_qubit = name_res
                    crosstalk_dict = self.chip_data.cache_config.get(
                        ChipConfigField.crosstalk
                    )
                    location_x = crosstalk_dict["infos"].index(target_qubit)
                    location_y = crosstalk_dict["infos"].index(source_qubit)
                    if record_type_ == "ac_crosstalk":
                        crosstalk_dict["ac_crosstalk"][location_x][location_y] = (
                            val_list[0]
                        )
                    elif record_type_ == "dc_crosstalk":
                        crosstalk_dict["dc_crosstalk"][location_x][location_y] = (
                            val_list[0]
                        )

        def rollback_character(record_):
            # for key, val_list in record_.items():
            #     param_name, path_type = key.split(".")
            #     setattr(self.compensates[param_name], path_type, val_list[0])
            # todo: rollback character
            pyqlog.warning(f"Current no sup roll back character | {record_}")
            pass

        def rollback_hardware(record_):
            hardware_manager = HardwareOffsetManager.from_data(
                self.chip_data.get(ChipConfigField.hardware_offset)
            )
            for key in record_.keys():
                if key.endswith("xyz_timing"):
                    hardware_manager.backtrack()
                elif key.endswith("zz_timing"):
                    hardware_manager.backtrack()
                    hardware_manager.backtrack()
            self.chip_data.cache_config[ChipConfigField.hardware_offset] = (
                hardware_manager.to_origin_data()
            )

        if re.match(NAME_PATTERN.qubit, record_type):
            rollback_physical_unit(record_type, record)
        elif re.match(NAME_PATTERN.coupler, record_type):
            rollback_physical_unit(record_type, record)
        elif re.match(NAME_PATTERN.qubit_pair, record_type):
            rollback_qubit_pair(record_type, record)
        elif record_type.endswith("crosstalk"):
            rollback_crosstalk(record_type, record)
        elif record_type == "character":
            rollback_character(record)
        elif record_type == "Hardware":
            rollback_hardware(record)

    def open_mongo_env(self):
        """Initialize the mongo environment, it establishes the
        MongoDB service connection of the data collector and defines
        the database collection name set by the user.
        """
        inst_ip = self._config_data.mongo.inst_host
        inst_port = self._config_data.mongo.inst_port
        connect_server(inst_ip, inst_port)
        self._link_mongo = True

    def generate_context(
        self,
        name: str,
        physical_unit: Optional[Union[str, List]] = None,
        readout_type: Optional[str] = None,
        use_parallel: bool = False,
        **kwargs,
    ) -> "ExperimentContext":
        """Generate experiment context using cached data.

        Args:
            name (str): _description_
            physical_unit (Union[str, List], optional): _description_. Defaults to None.
            readout_type (str, optional): _description_. Defaults to None.
            use_parallel (bool, optional): _description_. Defaults to None.

        Returns:
            ExperimentContext: _description_
        """
        if name == StandardContext.SQMC:
            self._sqmc_context(name)
            return self._context
        else:
            if self.chip_data.is_ready:
                if not physical_unit or physical_unit == "":
                    raise ExperimentContextError(
                        "physical unit is not exist, can't run experiment!"
                    )

                if isinstance(physical_unit, str):
                    physical_unit = physical_unit.split(",")
                    physical_unit = [unit.strip() for unit in physical_unit]

                self.run_options = self.default_run_options()
                self.run_options.parallel = use_parallel

                func = getattr(self, f"_{name}_context")
                if not func:
                    raise ExperimentContextError(f"Unknown context {name} !")

                # assemble standard context
                self._init_context(name)
                self._assemble_context(func, physical_unit, readout_type)
                self._divide_working_point()
                self._configure_environment()
                self._online_extend()
                self._f12_opt_extend()
                self._validate_context()
                return self._context
            else:
                raise ExperimentContextError(
                    f"Chip Data validator fail, does not support run experiment!\n{self.chip_data.validate_fail_msg}"
                )

    def _init_context(self, context_name: str):
        """Create a new experiment context and assemble it with instrument and crosstalk."""
        context = ExperimentContext(config=self._config_data, token=self.token)
        context.context_name = context_name

        # assemble instrument
        qaio_type = self._config_data.system.qaio_type
        context.inst = Instrument(qaio_type)
        if INST_SELECT is True:
            context.inst.load(self.chip_data.get(ChipConfigField.instrument))

        # assemble crosstalk
        if self.global_options.crosstalk:
            context.crosstalk_dict = self.chip_data.get(ChipConfigField.crosstalk)

        if self.global_options.xy_crosstalk:
            context.xy_crosstalk_dict = self.chip_data.get(
                ChipConfigField.xy_crosstalk,
                default={},
            )
        # bind context
        self._context = context

    def _divide_working_point(self):
        """_summary_

        Raises:
            ExperimentContextError: _description_
        """
        env_bits = self.global_options.env_bits
        working_type = self.global_options.working_type
        divide_type = self.global_options.divide_type
        max_point_unit = self.global_options.max_point_unit
        idle_units = list(self.run_options.idle_units)
        context_unit_map = self._context.physical_unit_map

        # feat: The environment bit does not allow z channel repetition
        z_flux_channel_map = defaultdict(list)
        for bit in env_bits:
            bit_obj = self.chip_data.get_physical_unit(bit)
            if bit_obj.z_flux_channel:
                z_flux_channel_map[bit_obj.z_flux_channel].append(bit_obj.name)
        repeat_channel_map = {
            k: (",".join(v) if len(v) < 10 else f"{','.join(v[:10])}...({len(v)})")
            for k, v in z_flux_channel_map.items()
            if len(v) > 1
        }
        if repeat_channel_map:
            validate_result = (
                f"Env bit have repeat z flux channel, suggest setting useless channels to 0:\n"
                f"{display_dict_as_table(repeat_channel_map)}\n"
            )
            raise ExperimentContextError(validate_result)

        for bit in env_bits:
            # bugfix: context physical unit maybe copy object
            bit_obj = context_unit_map.get(bit, self.chip_data.get_physical_unit(bit))

            if bit_obj is None:
                raise ExperimentContextError(f"Chip data no find {bit}")

            # check working type
            if working_type == WorkingType.ac:
                bit_obj.dc = None
                bit_obj.awg_bias = 0
            elif working_type == WorkingType.dc:
                bit_obj.ac = 0
                bit_obj.awg_bias = 0
            elif working_type == WorkingType.ac_bias:
                bit_obj.ac = 0
                bit_obj.dc = None
            else:
                raise ExperimentContextError(f"Unknown working type | {working_type}")

            # divide working point
            if divide_type == DivideType.CHA:
                if (
                    self.global_options.custom is True
                    and bit in self.global_options.custom_points
                ):
                    point = self.global_options.custom_points.get(bit)
                    if -1 < point < 1:
                        setattr(
                            bit_obj,
                            working_type,
                            point + bit_obj.dc_max + bit_obj.idle_point,
                        )
                    elif 4000 < point < 8000:
                        new_point = ac_spectrum_transform(bit_obj, point, 1)
                        setattr(
                            bit_obj,
                            working_type,
                            new_point + bit_obj.dc_max + bit_obj.idle_point,
                        )
                elif bit in max_point_unit or bit in idle_units:
                    setattr(bit_obj, working_type, bit_obj.dc_max + bit_obj.idle_point)
                elif isinstance(bit_obj, Coupler):
                    setattr(bit_obj, working_type, bit_obj.dc_max)
                else:
                    setattr(bit_obj, working_type, bit_obj.dc_min)
            elif divide_type == DivideType.CAL:
                setattr(bit_obj, working_type, bit_obj.dc_max + bit_obj.idle_point)
            else:
                raise ExperimentContextError(f"Unknown divide type | {divide_type}")

            self.run_options.env_bit_objs[bit] = bit_obj

    def _configure_environment(self):
        env_bit_objs = self.run_options.env_bit_objs
        environment = Environment()
        context: ExperimentContext = self._context
        # context.env_bit_dict = env_bit_objs

        # load cache config data
        character_data = self.chip_data.get(ChipConfigField.character)
        hardware_offset_data = self.chip_data.get(ChipConfigField.hardware_offset)
        hardware_manager = HardwareOffsetManager.from_data(hardware_offset_data)

        # collect parallel unit to father context
        context_unit_map = context.physical_unit_map

        # set dc/ac/awg_bias/compensate
        for bit_name, bit_obj in env_bit_objs.items():
            if (
                self.global_options.working_type == WorkingType.ac
                or bit_name in context_unit_map
            ):
                distortion_data = self.chip_data.cache_distortion_dat.get(bit_name, [])
                hardware_data = character_data.get(bit_name)
                if hardware_manager.apply:
                    if re.match(NAME_PATTERN.qubit, bit_name):
                        hardware_data["hardware_offset"][0] = hardware_manager.xy_delay(
                            int(bit_obj.xy_channel)
                        )
                    hardware_data["hardware_offset"][1] = hardware_manager.z_delay(
                        int(bit_obj.z_flux_channel)
                    )
                compensate = PulseCorrection(bit_name, distortion_data, hardware_data)
                environment.add(bit_obj, compensate)
            else:
                environment.add(bit_obj)

        # add readout point env bit | todo
        # read_env_bits = []
        # for bit in self.global_options.add_read_env_bits:
        #     pyqlog.info(f"add read env_bit: {bit}")
        #     bit_obj = self.chip_data.get_physical_unit(bit)
        #     if bit_obj.name not in context_unit_map:
        #         read_env_bits.append(bit_obj)
        #         if bit_obj not in environment.compensates:
        #             distortion_data = self.chip_data.cache_distortion_dat.get(bit, [])
        #             hardware_data = character_data.get(bit)
        #             if hardware_manager.apply:
        #                 hardware_data["hardware_offset"][1] = hardware_manager.z_delay(int(bit_obj.z_flux_channel))
        #             compensate = PulseCorrection(bit, distortion_data, hardware_data)
        #             environment.add(bit_obj, compensate)
        # if read_env_bits:
        #     if context.is_parallel:
        #         child_context = list(context.parallel_component.values())[0]
        #         child_context.read_env_bits = read_env_bits
        #     else:
        #         context.read_env_bits = read_env_bits
        # context.read_env_bits = read_env_bits

        # set working dc or ac bias
        if self.global_options.working_type == WorkingType.dc:
            working_dc = environment.get_system_dc()
            context.working_dc = working_dc
        elif self.global_options.working_type == WorkingType.ac_bias:
            ac_bias = environment.get_ac_bias()
            context.ac_bias = ac_bias

        # set pulse compensates
        context.compensates = environment.compensates
        context.to_ready()

    def _build_pulse_correction(self, unit: Union[str, BaseQubitsType]):
        if isinstance(unit, str):
            bit_obj = self.chip_data.get_physical_unit(unit)
        else:
            bit_obj = unit
            unit = bit_obj.name

        if bit_obj:
            hardware_offset_data = self.chip_data.get(ChipConfigField.hardware_offset)
            distortion_data = self.chip_data.cache_distortion_dat.get(unit, [])
            hardware_data = self.chip_data.get(ChipConfigField.character).get(unit)
            if hardware_offset_data["apply"]:
                if re.match(NAME_PATTERN.qubit, unit):
                    hardware_data["hardware_offset"][0] = hardware_offset_data[
                        "delay_map"
                    ].get(f"xy{bit_obj.xy_channel}", 0)
                hardware_data["hardware_offset"][1] = hardware_offset_data[
                    "delay_map"
                ].get(f"xy{bit_obj.z_flux_channel}", 0)
            return PulseCorrection(unit, distortion_data, hardware_data)
        else:
            raise ExperimentContextError(f"Chip data no find {unit}")

    def _assemble_context(
        self,
        core_logic: Callable,
        physical_names: Union[str, List],
        readout_type: Optional[str] = None,
    ):
        """Parallel experimental environment partitioning tool.

        Args:
            core_logic: The core configuration method of the experimental environment.
            physical_names: Physical Work Unit.

        Returns:
            ExpContext
        """
        special_ctx_names = ["_union_read_measure_context"]
        if not self.run_options.parallel:
            if core_logic.__name__ in special_ctx_names:
                pass
            elif isinstance(physical_names, List):
                if len(physical_names) != 1:
                    raise ExperimentContextError(
                        "No selected parallel mode but find parallel components"
                        " in the experimental environment.\n\nPhysical unit must"
                        " be single-selected, please check the Context parameter!"
                    )
                physical_names = physical_names[0]
            elif physical_names is None:
                core_logic(readout_type, self._context)
                return
            core_logic(physical_names, readout_type, self._context)
        else:
            child_contexts = [
                deepcopy(self._context) for _ in range(len(physical_names))
            ]
            for i, pn in enumerate(physical_names):
                self._context.parallel_component[pn] = core_logic(
                    pn, readout_type, child_contexts[i]
                )

    def _online_extend(self):
        if self.global_options.online and len(self.global_options.online_unit) > 1:
            online_bit_objs = []
            online_dcm_objs = []
            online_com_objs = {}
            for bit in self.global_options.online_unit:
                # add online bits
                bit_obj = self.chip_data.get_physical_unit(bit)
                if bit_obj is None:
                    raise ValueError(
                        f"Online {bit} not in env bits, continue normal context!"
                    )
                online_bit_objs.append(bit_obj)

                # add online dcm
                if bit.startswith("q"):
                    dcm_obj = self.chip_data.cache_dcm.get(f"{bit}_01.bin")
                    if dcm_obj is None:
                        raise ValueError(
                            f"Online {bit} not union readout dcm, continue normal context!"
                        )
                    online_dcm_objs.append(dcm_obj)

                # add online compensate
                compensate = self._context.compensates.get(bit_obj)
                if compensate is None:
                    compensate = self._build_pulse_correction(bit_obj)
                online_com_objs[bit_obj] = compensate

            self._context.online_bits = online_bit_objs
            self._context.online_dcms = online_dcm_objs
            self._context.online_coms = online_com_objs

    def _f12_opt_extend(self):
        for qubit in self._context.qubits:
            if qubit.name in self.global_options.f12_opt_bits:
                qubit.f12_options.switch = True
            else:
                qubit.f12_options.switch = False

    def _qubit_calibration_context(
        self,
        unit: str,
        readout_type: str = "",
        ctx: "ExperimentContext" = None,
    ) -> "ExperimentContext":
        qubit = self.chip_data.check_physical_unit_exist(unit)
        self.run_options.idle_units.add(unit)
        ctx.qubits = [qubit]
        ctx.unit_map.qubit = ctx.qubits[0]

        if readout_type:
            dcm = self.chip_data.cache_dcm.get(f"{unit}_{readout_type}.bin")
            ctx.discriminators = dcm
        return ctx

    def _coupler_probe_calibration_context(
        self,
        unit: str,
        readout_type: str = None,
        ctx: "ExperimentContext" = None,
    ) -> "ExperimentContext":
        coupler = self.chip_data.check_physical_unit_exist(unit)

        # feature: coupler probe cali need set coupler to readout point
        if coupler.readout_point.amp:
            coupler.idle_point += coupler.readout_point.amp
            coupler.readout_point.amp = 0

        self.run_options.idle_units.add(f"q{coupler.probe_bit}")
        self.run_options.idle_units.add(coupler.name)

        _, probe_q = transform_coupler_to_qubits(coupler, self.chip_data.cache_qubit)
        ctx.qubits = [probe_q]
        ctx.couplers = [coupler]

        ctx.unit_map.update(dict(qubit=probe_q, coupler=coupler))

        if readout_type:
            dcm = self.chip_data.cache_dcm.get(f"{unit}_{readout_type}.bin")
            if dcm:
                ctx.discriminators = dcm

        return ctx

    def _coupler_calibration_context(
        self,
        unit: str,
        readout_type: str = None,
        ctx: "ExperimentContext" = None,
    ) -> "ExperimentContext":
        coupler = self.chip_data.check_physical_unit_exist(unit)
        ctx.unit_map.coupler = coupler
        pq, dq = f"q{coupler.probe_bit}", f"q{coupler.drive_bit}"
        qh, ql = None, None
        self.run_options.idle_units.add(coupler.name)
        if readout_type == CCEReadoutType.QD:
            self.run_options.idle_units.add(dq)
        elif readout_type in [CCEReadoutType.QP, CCEReadoutType.QC]:
            self.run_options.idle_units.add(pq)
        else:
            pq_obj = self.chip_data.cache_qubit.get(pq)
            dq_obj = self.chip_data.cache_qubit.get(dq)
            qh = pq_obj if pq_obj.drive_freq > dq_obj.drive_freq else dq_obj
            ql = dq_obj if qh == pq_obj else pq_obj
            if readout_type == CCEReadoutType.QH:
                self.run_options.idle_units.add(qh.name)
            else:
                self.run_options.idle_units.add(ql.name)

        ctx.couplers = [coupler]
        if readout_type == CCEReadoutType.QC:
            drive_q, probe_q = transform_coupler_to_qubits(
                coupler, self.chip_data.cache_qubit
            )
            ctx.qubits = [drive_q, probe_q]
            ctx.discriminators = self.chip_data.cache_dcm.get(f"{coupler.name}_01.bin")
            ctx.unit_map.update(dict(probeQ=probe_q, driveQ=drive_q))
            return ctx
        elif readout_type == CCEReadoutType.QP:
            qubit = self.chip_data.cache_qubit.get(pq)
        elif readout_type == CCEReadoutType.QD:
            qubit = self.chip_data.cache_qubit.get(dq)
        elif readout_type == CCEReadoutType.QH:
            qubit = qh
        else:
            qubit = ql

        ctx.unit_map.qubit = qubit
        ctx.qubits = [qubit]
        ctx.discriminators = self.chip_data.cache_dcm.get(f"{qubit.name}_01.bin")
        return ctx

    def _cz_gate_calibration_context(
        self,
        unit: str,
        readout_type: str = None,
        ctx: "ExperimentContext" = None,
    ) -> "ExperimentContext":
        pair = self.chip_data.check_physical_unit_exist(unit)
        ql_name = pair.metadata.std.ql
        qh_name = pair.metadata.std.qh
        ql = self.chip_data.cache_qubit.get(ql_name)
        qh = self.chip_data.cache_qubit.get(qh_name)
        parking_bits = pair.metadata.std.parking_bits
        if pair:
            self.run_options.idle_units.add(ql_name)
            self.run_options.idle_units.add(qh_name)
            for u in parking_bits:
                self.run_options.idle_units.add(u)
        else:
            raise ExperimentContextError(f"No find {pair}")

        ctx.qubit_pair = [pair]
        c_list = []
        q_list = [ql, qh]
        for name in parking_bits:
            if name.startswith("q"):
                qb = self.chip_data.cache_qubit.get(name)
                q_list.append(qb)
            elif name.startswith("c"):
                cb = self.chip_data.cache_coupler.get(name)
                c_list.append(cb)

        ctx.unit_map.update(
            dict(
                qubit_pair=pair,
                ql=ql,
                qh=qh,
                qc=self.chip_data.cache_coupler.get(pair.qc),
            )
        )
        ctx.qubits = q_list
        ctx.couplers = c_list

        rts = readout_type.split("-")
        if len(rts) == 2:
            dcm_name = qh_name if rts[0] == "qh" else ql_name
            dcm = self.chip_data.cache_dcm.get(f"{dcm_name}_{rts[1]}.bin")
        else:
            dcm = [
                self.chip_data.cache_dcm.get(f"{qh_name}_{rts[1]}.bin"),
                self.chip_data.cache_dcm.get(f"{ql_name}_{rts[2]}.bin"),
            ]

        ctx.discriminators = dcm
        return ctx

    def _crosstalk_measure_context(
        self,
        unit: str,
        readout_type: str = None,
        ctx: "ExperimentContext" = None,
    ) -> "ExperimentContext":
        tq, bq = get_tq_bq_from_group_name(unit)
        self.chip_data.check_physical_unit_exist([tq, bq])

        c_list = []
        q_list = []

        if re.match(NAME_PATTERN.coupler, tq):
            coupler = self.chip_data.cache_coupler.get(tq)
            drive_q, probe_q = transform_coupler_to_qubits(
                coupler, self.chip_data.cache_qubit
            )
            ctx.unit_map.update(
                dict(driveQ=drive_q, probeQ=probe_q, target_unit=coupler)
            )
            if bq == probe_q.name:
                q_list = [probe_q, drive_q]
            else:
                q_list = [drive_q, probe_q]
            c_list = [coupler]
        elif re.match(NAME_PATTERN.qubit, tq):
            qubit = self.chip_data.cache_qubit.get(tq)
            q_list = [qubit]
            ctx.unit_map.target_unit = qubit
        else:
            raise ExperimentContextError(msg=f"tq({tq}) not qubit pr coupler!")

        if bq not in [u.name for u in q_list] and bq not in [u.name for u in c_list]:
            if re.match(NAME_PATTERN.qubit, bq):
                qb = self.chip_data.cache_qubit.get(bq)
                q_list.append(deepcopy(qb))
                ctx.unit_map.bias_unit = q_list[-1]
            elif re.match(NAME_PATTERN.coupler, bq):
                cb = self.chip_data.cache_coupler.get(bq)
                c_list.append(cb)
                ctx.unit_map.bias_unit = cb
            else:
                raise ExperimentContextError(msg=f"bq({bq}) not qubit pr coupler!")
        else:
            ctx.unit_map.bias_unit = self.chip_data.get_physical_unit(bq)

        ctx.qubits = q_list
        ctx.couplers = c_list

        if len(c_list) == 1:
            ctx.unit_map.coupler = c_list[0]

        if re.match(NAME_PATTERN.qubit, tq):
            self.run_options.idle_units.add(tq)
        else:
            coupler = self.chip_data.cache_coupler.get(tq)
            self.run_options.idle_units.add(f"q{coupler.probe_bit}")

        if readout_type:
            if "union" in readout_type:
                # like: `union-01`, `union-012`, ...
                *_, rd_type = readout_type.split("-")
                dcm_list = []
                for q_name in [tq, bq]:
                    dcm_name = f"{q_name}_{rd_type}.bin"
                    dcm_obj = self.chip_data.cache_dcm.get(dcm_name)
                    if dcm_obj:
                        dcm_list.append(dcm_obj)
                    else:
                        pyqlog.warning(f"No match {dcm_name} dcm!")
                ctx.discriminators = dcm_list
            else:
                # bugfix: 2024/02/28 coupler bin can not find
                # if re.match(NAME_PATTERN.qubit, tq):
                dcm_name = f"{tq}_{readout_type}.bin"
                dcm_obj = self.chip_data.cache_dcm.get(dcm_name)
                if dcm_obj:
                    ctx.discriminators = dcm_obj
                else:
                    pyqlog.warning(f"No match {dcm_name} dcm!")

        return ctx

    def _union_read_measure_context(
        self,
        units: List[str],
        readout_type: str = None,
        ctx: "ExperimentContext" = None,
    ) -> "ExperimentContext":
        """UnionReadout or CloudLine context."""

        check_units = []
        for unit in units:
            if unit.startswith("bus"):
                check_units.extend(
                    list(self.chip_data.get_qubits_from_bus(unit).keys())
                )
                ctx.unit_map.bus = unit
            elif "," in unit:
                check_units.extend(unit.split(","))
            else:
                check_units.append(unit)
        units = check_units

        self.chip_data.check_physical_unit_exist(units)

        q_obj_list = []
        c_obj_list = []
        qp_obj_list = []
        dcm_obj_list = []

        for unit_str in units:
            if re.match(NAME_PATTERN.qubit_pair, unit_str):
                qcp_obj = self.chip_data.cache_qubit_pair.get(unit_str)
                qp_obj_list.append(qcp_obj)
            elif re.match(NAME_PATTERN.qubit, unit_str):
                qcp_obj = self.chip_data.cache_qubit.get(unit_str)
                q_obj_list.append(qcp_obj)
            elif re.match(NAME_PATTERN.coupler, unit_str):
                qcp_obj = self.chip_data.cache_coupler.get(unit_str)
                c_obj_list.append(qcp_obj)

        for qp_obj in qp_obj_list:
            ql_name = qp_obj.metadata.std.ql
            qh_name = qp_obj.metadata.std.qh
            parking_bits = qp_obj.metadata.std.parking_bits

            base_qubit_names = [qh_name, ql_name]
            base_qubit_names.extend(parking_bits)
            for bc_name in base_qubit_names:
                if re.match(NAME_PATTERN.qubit, bc_name):
                    q_obj = self.chip_data.cache_qubit.get(bc_name)
                    if q_obj not in q_obj_list:
                        q_obj_list.append(q_obj)
                elif re.match(NAME_PATTERN.coupler, bc_name):
                    c_obj = self.chip_data.cache_coupler.get(bc_name)
                    if c_obj not in c_obj_list:
                        c_obj_list.append(c_obj)

        if readout_type:
            for q_obj in q_obj_list:
                dcm_name = f"{q_obj.name}_{readout_type}.bin"
                dcm_obj = self.chip_data.cache_dcm.get(dcm_name)
                if dcm_obj:
                    dcm_obj_list.append(dcm_obj)
                else:
                    pyqlog.warning(f"No match {dcm_name} dcm!")

        for q_obj in q_obj_list:
            self.run_options.idle_units.add(q_obj.name)

        ctx.qubits = q_obj_list
        ctx.couplers = c_obj_list
        ctx.qubit_pair = qp_obj_list
        ctx.discriminators = dcm_obj_list

        if len(q_obj_list) == 1:
            ctx.unit_map.qubit = q_obj_list[0]

        if len(c_obj_list) == 1:
            ctx.unit_map.coupler = c_obj_list[0]

        if len(qp_obj_list) == 1:
            ctx.unit_map.qubit_pair = qp_obj_list[0]

        return ctx

    def _quantum_circuit_context(
        self,
        physical_units: None,
        readout_type: str = None,
        ctx: "ExperimentContext" = None,
    ):
        qubits, couplers = [], []
        for bit in self.global_options.env_bits:
            if bit.startswith("q"):
                qubits.append(self.chip_data.cache_qubit.get(bit))
            else:
                couplers.append(self.chip_data.cache_coupler.get(bit))

        if qubits:
            ctx.qubits = qubits

        if couplers:
            ctx.couplers = couplers

        ctx.qubit_pair = list(self.chip_data.cache_qubit_pair.values())

        if readout_type:
            dcm = []
            for qubit in ctx.qubits:
                d = self.chip_data.cache_dcm.get(f"{qubit.name}_{readout_type}.bin")
                if d:
                    dcm.append(d)
            ctx.discriminators = dcm
        return ctx

    def _sqmc_context(self, context_name: str):
        context = ExperimentContext(config=self._config_data, token=self.token)
        context.context_name = context_name
        context.to_ready()
        self._context = context

    def _validate_context(self):
        if self._context.context_name == StandardContext.CM:
            if self._context.is_parallel:
                context_list = list(self._context.parallel_component.values())
            else:
                context_list = [self._context]

            target_qubits = []
            bias_qubits = []

            for ctx in context_list:
                if not ctx.couplers:
                    target_qubits.append(ctx.qubits[0])
                    bias_qubits.append(ctx.qubits[1])
                elif len(ctx.qubits) == 1:
                    target_qubits.append(ctx.qubits[0])
                elif len(ctx.qubits) == 2:
                    target_qubits.extend(ctx.qubits)
                elif len(ctx.qubits) == 3:
                    target_qubits.extend(ctx.qubits[:2])
                    bias_qubits.append(ctx.qubits[2])

            for bq in bias_qubits:
                for tq in target_qubits:
                    if bq.inst.xy_lo == tq.inst.xy_lo:
                        pyqlog.warning(f"Bias {bq} auto change IF!")
                        bq.drive_freq = tq.drive_freq
                        bq.XYwave.baseband_freq = tq.XYwave.baseband_freq
                        bq.inst.xy_gap = tq.inst.xy_gap
                        break

        self._context.env_bit_resource = EnvironmentBitResource.from_chip_data(
            self.chip_data.cache_config.get(ChipConfigField.chip_line),
            self.global_options.env_bits,
            self.chip_data.topology,
        )

        # bugfix: parallel ac mode experiment need bind env bits for first child exp
        # if self._context.parallel_component and self.global_options.working_type == WorkingType.ac:
        #     env_bits = list(self._context.compensates.keys())

        #     for child_context in self._context.parallel_component.values():
        #         for bit in child_context.compensates:
        #             if bit in env_bits:
        #                 env_bits.remove(bit)

        #     first_child_context = list(self._context.parallel_component.values())[0]
        #     first_child_context.read_env_bits = env_bits


class MonsterContextManager(BaseContextManager, metaclass=Singleton):
    pass


def deepen_set_param_value(source_obj, key: Union[str, list], value):
    old_value = None
    if isinstance(key, str):
        if "." not in key:
            old_value = getattr(source_obj, key, None)
            setattr(source_obj, key, value)
        else:
            return deepen_set_param_value(source_obj, key.split("."), value)
    elif isinstance(key, list) and len(key) >= 1:
        if len(key) == 1:
            old_value = getattr(source_obj, key[0], None)
            setattr(source_obj, key[0], value)
        else:
            old_value = deepen_set_param_value(
                getattr(source_obj, key[0]), key[1:], value
            )

    return old_value


def get_tq_bq_from_group_name(name: str):
    q_names = re.findall(r"q\d+", name)
    c_names = re.findall(r"c\d+-\d+", name)
    if len(q_names) == 2:
        tq_name, bq_name = q_names
    elif len(q_names) == 0:
        tq_name, bq_name = c_names
    else:
        if name.startswith("q"):
            tq_name, bq_name = q_names[0], c_names[0]
        else:
            tq_name, bq_name = c_names[0], q_names[0]

    return tq_name, bq_name


def get_extend_coupler(cache_coupler, env_bits, qubit_names):
    if isinstance(qubit_names, str):
        qubit_names = [qubit_names]
    c_bit_dict = {key: value for key, value in cache_coupler.items() if key in env_bits}
    coupler_list = []
    for c_name, c_bit_obj in c_bit_dict.items():
        c_q_name = [f"q{c_bit_obj.probe_bit}", f"q{c_bit_obj.drive_bit}"]
        for qubit_name in qubit_names:
            if qubit_name in c_q_name:
                coupler_list.append(c_bit_obj)
    return coupler_list
