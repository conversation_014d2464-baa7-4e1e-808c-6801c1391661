{"name": "q0q1", "interaction_point": 0.0, "accumulation_phase": 0.0, "metadata": {"std": {"ql": "q1", "qh": "q0", "parking_bits": ["q2", "c0-1"], "gate_params": {"q0": {"width": 60.0, "amp": 0.05, "sigma": 1.25, "buffer": 5, "phase": 0.0, "drag_assign_amp": 0.0, "is_control": true}, "q1": {"width": 60.0, "amp": 0.1, "sigma": 1.25, "buffer": 5, "phase": 0.0, "drag_assign_amp": 0.0, "is_control": true}, "q2": {"width": 60.0, "amp": 0.15, "sigma": 1.25, "buffer": 5, "phase": 0.0, "drag_assign_amp": 0.0, "is_control": true}, "c0-1": {"width": 60.0, "amp": 0.2, "sigma": 1.25, "buffer": 5, "phase": 0.0, "drag_assign_amp": 0.0, "is_control": true}}}}}