# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2022-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/01/08
# __author:       <PERSON>

from copy import deepcopy

from ....analysis.library_v2 import ZExpAnalysis
from ....log import pyqlog
from ....pulse.pulse_lib import Constant, SquareEnvelop
from ....structures import MetaData, Options
from ....types import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class ZExp(TopExperiment):
    """Used setting z line pulse to test room temperature distortion & delay."""

    # 使用实验模式输出Z波形，用于Z畸变和时序采集。注意一次打开的通道数不要超过48个，
    # 否则可能超出一体机服务器的内存，能打开的最多通道数视一体机服务器的配置。

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""

        options = super()._default_experiment_options()
        options.set_validator("z_pulse_params", dict)
        options.set_validator("z_channels", list)
        options.set_validator("xy_pulse_params", dict)
        options.set_validator("xy_channels", list)
        options.set_validator("xy_power", float)
        options.set_validator("loop", int)

        # z pulse parameters
        options.z_pulse_params = {
            "time": 65000,
            "amp": 0.2,
        }
        options.xy_pulse_params = {
            "time": 65000,
            "amp": 0.8,
            "freq": 1050,
            "offset": 100,
            "detune": 0
        }

        options.z_channels = []  # 若不设置，则 self.qubits, self.couplers 全给
        options.xy_channels = []  # 若不设置，则 self.qubits 全给
        options.xy_power = -30.0  # 设置 xy 通道功率
        options.loop = 100  # 设置实验多少 loop

        # default options
        options.repeat = 5000
        options.period = 400

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default set run options of experiment."""
        options = super()._default_run_options()

        options.set_xy_names = []
        options.set_z_names = []
        options.qc_map = {}

        options.support_context = [StandardContext.URM.value]
        options.x_data = []
        options.analysis_class = ZExpAnalysis
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options."""
        options = super()._default_analysis_options()

        return options

    def _check_options(self):
        super()._check_options()

        z_channels = self.experiment_options.z_channels
        xy_channels = self.experiment_options.xy_channels
        loop = self.experiment_options.loop

        base_qubits = []
        base_qubits.extend(self.qubits)
        base_qubits.extend(self.couplers)

        qc_map = {}
        channel_map = {}
        default_channels = []
        xy_channel_map = {}
        default_xy_channels = []
        for qc_obj in base_qubits:
            qc_name = qc_obj.name
            qc_channel = qc_obj.z_flux_channel

            qc_map[qc_name] = qc_obj
            if qc_channel not in channel_map:
                channel_map[qc_channel] = qc_name
                default_channels.append(qc_channel)
            else:
                pyqlog.warning(
                    f"{qc_name} {qc_channel} is exist, {channel_map.get(qc_channel)}"
                )

            if qc_name.startswith("q"):
                qc_xy_channel = qc_obj.xy_channel
                if qc_xy_channel not in xy_channel_map:
                    xy_channel_map[qc_xy_channel] = qc_name
                    default_xy_channels.append(qc_xy_channel)
                else:
                    pyqlog.warning(
                        f"{qc_name} {qc_xy_channel} is exist, {xy_channel_map.get(qc_xy_channel)}"
                    )

            # dc_max, dc_min, idle_point, readout point 置 0
            qc_obj.dc_max = 0.0
            qc_obj.dc_min = 0.0
            qc_obj.idle_point = 0.0
            qc_obj.readout_point_model = "Constant"
            qc_obj.readout_point = Options(amp=0.0)

        if z_channels:
            set_z_names = []
            set_z_channels = z_channels
            for z_ch in z_channels:
                z_name = channel_map.get(z_ch, "")
                if z_name:
                    set_z_names.append(z_name)
                else:
                    pyqlog.warning(
                        f"Can't match z {z_ch} from self.qubits, self.couplers!"
                    )
        else:
            set_z_names = list(qc_map.keys())
            set_z_channels = default_channels

        if xy_channels:
            set_xy_names = []
            set_xy_channels = xy_channels
            for xy_ch in xy_channels:
                x_name = xy_channel_map.get(xy_ch, "")
                if x_name:
                    set_xy_names.append(x_name)
                else:
                    pyqlog.warning(
                        f"Can't match x {xy_ch} from self.qubits, self.couplers!"
                    )
        else:
            set_xy_names = [q_obj.name for q_obj in self.qubits]
            set_xy_channels = default_xy_channels

        pyqlog.info(f"set_z_names: {set_z_names}")
        pyqlog.info(f"set_z_channels: {set_z_channels}")
        pyqlog.info(f"set_xy_names: {set_xy_names}")
        pyqlog.info(f"set_xy_channels: {set_xy_channels}")

        # 清除 ac_bias 幅值
        z_pulse_params = self.experiment_options.z_pulse_params
        for qc_name, bias_val in self.ac_bias.items():
            bias_val[-1] = -z_pulse_params["amp"] / 2
            # bias_val[-1] = 0.0

        # 清除 delay 数据，以及 z 线校准数据
        for qc, comp_obj in self.compensates.items():
            comp_obj.x_delay = 0.0
            comp_obj.z_delay = 0.0
            comp_obj.z_compensate = 0.0
            comp_obj.z_distortion_type = "ab"
            comp_obj.z_distortion_ab = []

        self.discriminator = None
        self.set_experiment_options(
            multi_readout_channels=self.qubits[0].readout_channel,
            data_type="amp_phase",
        )

        new_loop = loop if loop > 1 else 1
        self.set_run_options(
            set_z_names=set_z_names,
            set_xy_names=set_xy_names,
            qc_map=qc_map,
            x_data=list(range(new_loop)),
            measure_qubits=[self.qubits[0]],
            custom_unit_describe=f"{base_qubits[0].name}~{base_qubits[-1].name}",
        )

    def _metadata(self) -> MetaData:
        """Set RabiScanAmp experiment metadata."""
        metadata = super()._metadata()
        metadata.qubits = [self.qubits[0].name] if self.qubits else []
        metadata.couplers = [self.couplers[0].name] if self.couplers else []
        return metadata

    @staticmethod
    def set_xy_pulses(self):
        """Set Z pulse."""
        xy_pulse_params = self.experiment_options.xy_pulse_params
        xy_power = self.experiment_options.xy_power
        set_xy_names = self.run_options.set_xy_names
        qc_map = self.run_options.qc_map
        x_data = self.run_options.x_data

        xy_pulse_params.update({"name": "XY"})
        xy_pulse_list = [SquareEnvelop(**xy_pulse_params)() for _ in x_data]

        for xy_name in set_xy_names:
            qc_obj = qc_map.get(xy_name)
            if qc_obj:
                self._bind_qubit_drive(qc_obj)
                qc_xy_pulse_list = deepcopy(xy_pulse_list)
                self.play_pulse("XY", qc_obj, qc_xy_pulse_list)
                self.inst.set_output_freq(
                    module="XY_control",
                    channel=qc_obj.xy_channel,
                    value=4500
                )
                self.inst.set_intermediate_freq(
                    module="XY_control",
                    channel=qc_obj.xy_channel,
                    value=1050,
                )
                self.inst.set_power(
                    module="XY_control",
                    channel=qc_obj.xy_channel,
                    value=xy_power,
                )

    @staticmethod
    def set_z_pulses(self):
        """Set Z pulse."""
        z_pulse_params = self.experiment_options.z_pulse_params
        set_z_names = self.run_options.set_z_names
        qc_map = self.run_options.qc_map
        x_data = self.run_options.x_data

        z_pulse_params.update({"name": "Z"})
        z_pulse = Constant(**z_pulse_params)()
        for z_name in set_z_names[:-1]:
            qc_obj = qc_map.get(z_name)
            if qc_obj:
                qc_z_pulse = deepcopy(z_pulse)
                self.play_pulse("Z", qc_obj, qc_z_pulse)

        # set sweep
        z_pulse_list = [Constant(**z_pulse_params)() for _ in x_data]
        qc_obj = qc_map.get(set_z_names[-1])
        self.play_pulse("Z", qc_obj, z_pulse_list)
