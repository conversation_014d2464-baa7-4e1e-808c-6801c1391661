# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/22
# __author:       <PERSON><PERSON><PERSON>

"""
Experiment Transform Utils:

1. Combine experimental message bodies
2. Convert fake pulse into actual pulse
"""

from typing import Dict, List

import numpy as np

from ...pulse import CorrectPulse, PulseComponent
from ...pulse.utilities import ac_crosstalk_correct, compile_pulse
from ...pulse.xy_compensate import xy_cross_compensate
from ...qm_protocol import CommonMessage, ExperimentFile, XYControl


class ExperimentTransformer:
    def __init__(
        self,
        experiment: ExperimentFile,
        common: CommonMessage,
        channel_bit_map: Dict[str, str],
    ):
        self.experiment = experiment
        self.common = common
        self.channel_bit_map = channel_bit_map
        self._ac_crosstalk_inv = None

        # BugFixed, get compensate object use `Qubit/Coupler` name
        self.compensate_dict = {
            unit.name: compensate
            for unit, compensate in self.common.compensate_dict.items()
        }

        PulseComponent.fake = True

    @property
    def measure_aio(self):
        return self.experiment.measure_aio

    def _correct_ac_bias(self):
        """Correct awg bias with ac crosstalk matrix."""

        # correct ac crosstalk for ac bias
        crosstalk_names = list(self.common.ac_bias.keys())
        awg_before_crosstalk = np.asarray(
            [ac[-1] for ac in self.common.ac_bias.values()]
        )
        ac_crosstalk_inv = TransformToolAPI.get_ac_crosstalk(
            self.common.crosstalk_dict, crosstalk_names
        )
        awg_after_crosstalk = np.dot(ac_crosstalk_inv, awg_before_crosstalk)

        for i, key in enumerate(self.common.ac_bias):
            bias = self.common.ac_bias[key]
            bias[-1] = float(awg_after_crosstalk[i])

    def _compensate_ac_bias(self):
        for ctrl in self.measure_aio.Z_flux_control:
            if ctrl.waveform:
                for pulse in ctrl.waveform:
                    pulse.base += self.common.ac_bias[pulse.bit][-1]

        for ctrl in self.experiment.sweep_control:
            if ctrl.func == "Z_flux_control:waveform":
                for pulse in ctrl.waveform:
                    pulse.base += self.common.ac_bias[pulse.bit][-1]

    def _get_ctrl_ac_crosstalk_inv(self):
        unit_names = []

        for ctrl in self.experiment.sweep_control:
            if ctrl.func == "Z_flux_control:waveform":
                unit_name = self.channel_bit_map.get(f"z-{ctrl.channel}")
                if unit_name not in unit_names:
                    unit_names.append(unit_name)

        if not unit_names:
            for ctrl in self.experiment.measure_aio.Z_flux_control:
                if ctrl.waveform:
                    unit_name = self.channel_bit_map.get(f"z-{ctrl.channel}")
                    if unit_name not in unit_names:
                        unit_names.append(unit_name)

        self._ac_crosstalk_inv = TransformToolAPI.get_ac_crosstalk(
            self.common.crosstalk_dict, unit_names
        )

    def _rf_control_trans(self, ctrl_list: List):
        """XY_control or Read_out_control custom wave tran fake to real."""
        xy_custom_wave_map = {}
        for idx, ctrl_doc in enumerate(ctrl_list):
            if ctrl_doc.waveform:
                if isinstance(ctrl_doc, XYControl):
                    xy_custom_wave_map.update({idx: ctrl_doc.waveform})
                else:
                    new_waveform = self._normal_custom_wave_trans(ctrl_doc.waveform)
                    ctrl_doc.waveform = new_waveform

        if xy_custom_wave_map:
            xy_cw_map = self._compensate_xy_custom_wave(xy_custom_wave_map)
            for idx, cw_doc in xy_cw_map.items():
                ctrl_list[idx].waveform = cw_doc

    def _normal_custom_wave_trans(self, custom_wave: List):
        """Trans fake pulse CustomWave to real pulse CustomWave."""
        real_pulse_list = TransformToolAPI.trans_custom_wave_pulses(custom_wave)
        rp_data_list = []
        comp_obj = self.compensate_dict.get(real_pulse_list[0].bit)
        for pulse_obj in real_pulse_list:
            if pulse_obj.type != "M" and comp_obj:
                correct_obj = CorrectPulse(pulse_obj, comp_obj)
                correct_obj.correct()
                pulse_obj = correct_obj.wave
            rp_data_list.append(pulse_obj.pulse)
        return rp_data_list

    def _compensate_xy_custom_wave(self, custom_wave_map: Dict) -> Dict:
        """Compensate XY custom wave."""
        idx_cw_map = {}
        name_pulses_map = {}
        idx_name_map = {}
        for idx, cw_doc in custom_wave_map.items():
            r_pulse_list = TransformToolAPI.trans_custom_wave_pulses(cw_doc)
            name = r_pulse_list[0].bit
            name_pulses_map.update({name: r_pulse_list})
            idx_name_map.update({idx: name})

        # Adjust some special experiments alone correct xy_pulses.
        pre_xy_cross_list = self.experiment.extra.get("pre_xy_cross_list", [])
        for pre_xy_cross in pre_xy_cross_list:
            t_name = pre_xy_cross.get("target_name", "")
            b_name = pre_xy_cross.get("bias_name", "")
            amp_coe_list = pre_xy_cross.get("amp_coe_list", [])
            phase_list = pre_xy_cross.get("phase_list", [])

            t_pulse_list = name_pulses_map.get(t_name, [])
            b_pulse_list = name_pulses_map.get(b_name, [])
            t_power = self.experiment.xy_power_dict.get(t_name, -38.0)
            b_power = self.experiment.xy_power_dict.get(b_name, -38.0)
            power_coe = 10 ** ((b_power - t_power) / 20)

            if t_pulse_list and b_pulse_list and amp_coe_list and phase_list:
                new_t_pulses = TransformToolAPI.cross_xy_pulses(
                    t_pulse_list, b_pulse_list, amp_coe_list, phase_list, power_coe
                )
                name_pulses_map[t_name] = new_t_pulses

        new_name_pulses_map = xy_cross_compensate(
            name_pulses_map,
            self.experiment.xy_power_dict,
            self.common.xy_crosstalk_dict or {},
        )

        for idx, name in idx_name_map.items():
            pulse_list = new_name_pulses_map.get(name)
            pulse_data_list = []
            comp_obj = self.compensate_dict.get(pulse_list[0].bit)
            for pulse_obj in pulse_list:
                if comp_obj:
                    correct_obj = CorrectPulse(pulse_obj, comp_obj)
                    correct_obj.correct()
                    pulse_obj = correct_obj.wave
                pulse_data_list.append(pulse_obj.pulse)
            idx_cw_map.update({idx: pulse_data_list})
        return idx_cw_map

    def _ac_control_trans(self, ctrl_list: List):
        """Z_flux_control custom wave tran fake to real."""
        custom_wave_map = {}
        for idx, ctrl_doc in enumerate(ctrl_list):
            if ctrl_doc.waveform:
                custom_wave_map.update({idx: ctrl_doc.waveform})

        if custom_wave_map:
            af_cw_map = self._map_custom_wave_trans(custom_wave_map)
            for idx, cw_doc in af_cw_map.items():
                ctrl_list[idx].waveform = cw_doc

    def _map_custom_wave_trans(self, custom_wave_map: Dict) -> Dict:
        """Consider all Z line CustomDoc trans."""
        ac_ck_inv = self._ac_crosstalk_inv
        idx_pulses_map = {}
        for idx, cw_doc in custom_wave_map.items():
            r_pulse_list = TransformToolAPI.trans_custom_wave_pulses(cw_doc)
            idx_pulses_map.update({idx: r_pulse_list})

        pulse_loop_set = {len(v) for v in idx_pulses_map.values()}
        pulse_loop = list(pulse_loop_set)[0]
        idx_list = list(idx_pulses_map.keys())
        af_idx_pulses_map = {idx: list(range(pulse_loop)) for idx in idx_list}
        for loop, pulse_tup in enumerate(zip(*idx_pulses_map.values())):
            cr_pulse_list = ac_crosstalk_correct(ac_ck_inv, list(pulse_tup))
            for idx, cr_pulse in zip(idx_list, cr_pulse_list):
                comp_obj = self.compensate_dict.get(cr_pulse.bit)
                if comp_obj:
                    correct_obj = CorrectPulse(cr_pulse, comp_obj)
                    correct_obj.correct()
                    cr_pulse = correct_obj.wave

                if cr_pulse.base is not None:
                    cr_pulse += cr_pulse.base
                af_idx_pulses_map[idx][loop] = cr_pulse.pulse

        return af_idx_pulses_map

    def _sweep_control_trans(self, ctrl_list: List):
        """sweep_control custom wave tran fake to real."""
        ac_custom_wave_map = {}
        xy_custom_wave_map = {}
        for idx, sweep_doc in enumerate(ctrl_list):
            func_name = sweep_doc.func
            cw_doc = sweep_doc.waveform
            if "waveform" in func_name:
                if "Z_flux_control" in func_name:
                    ac_custom_wave_map.update({idx: cw_doc})
                elif "XY_control" in func_name:
                    xy_custom_wave_map.update({idx: cw_doc})
                else:
                    new_cw_doc = self._normal_custom_wave_trans(cw_doc)
                    sweep_doc.waveform = new_cw_doc

        if ac_custom_wave_map:
            af_cw_map = self._map_custom_wave_trans(ac_custom_wave_map)
            for idx, cw_doc in af_cw_map.items():
                ctrl_list[idx].waveform = cw_doc

        if xy_custom_wave_map:
            xy_cw_map = self._compensate_xy_custom_wave(xy_custom_wave_map)
            for idx, cw_doc in xy_cw_map.items():
                ctrl_list[idx].waveform = cw_doc

    def _adapt_actual_pulse(self):
        xy_control = self.measure_aio.XY_control
        z_flux_control = self.measure_aio.Z_flux_control
        read_out_control = self.measure_aio.Readout_control
        sweep_control = self.experiment.sweep_control

        self._rf_control_trans(xy_control)
        self._ac_control_trans(z_flux_control)
        self._rf_control_trans(read_out_control)
        self._sweep_control_trans(sweep_control)

        self.experiment.fake_pulse = False

    def _adapt_fake_pulse(self):
        # set compensate
        self.experiment.compensate_dict = {
            unit.name: compensate.to_dict()
            for unit, compensate in self.common.compensate_dict.items()
        }

        # set ac crosstalk
        self.experiment.ac_crosstalk = self._ac_crosstalk_inv.tolist()

        # set xy crosstalk
        xy_length = len(self.experiment.measure_aio.XY_control)
        if xy_length > 1:
            xy_cross_params = self.common.xy_crosstalk_dict or {}
            self.experiment.xy_crosstalk = xy_cross_params

    def run(self):
        if self.common.ac_bias:
            self._correct_ac_bias()
            self._compensate_ac_bias()
        self._get_ctrl_ac_crosstalk_inv()

        if self.common.fake_pulse is False:
            self._adapt_actual_pulse()
        else:
            self._adapt_fake_pulse()


class TransformToolAPI:
    @staticmethod
    def patch_fake_pulse(pulse_list: List["PulseComponent"]) -> List["PulseComponent"]:
        """Trans pulse to fake pulse."""
        new_pulse_list = []
        for pulse_obj in pulse_list:
            pulse_obj.fake = True
            new_pulse_list.append(pulse_obj)
        return new_pulse_list

    @staticmethod
    def trans_custom_wave_pulses(
        custom_wave: List[PulseComponent],
    ) -> List[PulseComponent]:
        """Trans fake pulse CustomWave to real pulse CustomWave."""
        # custom_wave = TransformToolAPI.patch_fake_pulse(custom_wave)
        real_pulse_list = [compile_pulse(pulse_obj) for pulse_obj in custom_wave]
        return real_pulse_list

    @staticmethod
    def get_ac_crosstalk(crosstalk_dict, crosstalk_names: List[str]):
        """Get ac crosstalk matrix."""
        if crosstalk_dict and isinstance(crosstalk_dict, Dict):
            infos = crosstalk_dict.get("infos")
            ac_crosstalk = np.array(crosstalk_dict.get("ac_crosstalk"))
            index_list = [infos.index(q) for q in crosstalk_names]
            ac_crosstalk = ac_crosstalk[index_list, :]
            ac_crosstalk = ac_crosstalk[:, index_list]
        else:
            ac_crosstalk = np.eye(len(crosstalk_names))

        ac_crosstalk_inv = np.linalg.inv(ac_crosstalk)
        return ac_crosstalk_inv

    @staticmethod
    def cross_xy_pulses(
        t_pulses: List[PulseComponent],
        b_pulses: List[PulseComponent],
        amp_coe_list: List[float],
        phase_list: List[float],
        power_coe: float = 1.0,
    ):
        """Optimize cross xy pulse list.

        Notice: Must be real pulse data.
        """
        if len(amp_coe_list) != len(phase_list):
            # The amp_coe_list and phase must be a same length.
            return t_pulses

        if len(t_pulses) != len(b_pulses):
            # The t_pulses and b_pulses must be a same length.
            return t_pulses

        use_once = True
        if len(t_pulses) == len(amp_coe_list):
            use_once = False

        amp_coe, phase = amp_coe_list[0], phase_list[0]
        for idx, t_pulse in enumerate(t_pulses):
            b_pulse = b_pulses[idx]
            if use_once is False:
                amp_coe, phase = amp_coe_list[idx], phase_list[idx]

            t_pulse_complex = t_pulse.pulse_complexes[0]
            b_pulse_complex = b_pulse.pulse_complexes[0]

            b_cross_pulse_complex = (
                b_pulse_complex * amp_coe * np.exp(1j * phase) * power_coe
            )
            new_t_pulse_complex = t_pulse_complex + b_cross_pulse_complex
            new_t_pulse_arr = np.real(new_t_pulse_complex)

            t_pulse.pulse_complexes = [new_t_pulse_complex]
            t_pulse.pulse = new_t_pulse_arr

        return t_pulses
