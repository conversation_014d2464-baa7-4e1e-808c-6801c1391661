# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/15
# __author:       <PERSON>

"""
FloquetConditionalPhaseFixed analysis.
"""

import numpy as np
from scipy.interpolate import interp1d

from ...log import pyqlog
from ...structures import Options, QDict
from ...parameters import analysis_options_wrapper
from ...analysis.specification import ParameterRepr, FitModel, CurveAnalysisData
from ...analysis.curve_fit_analysis import CurveFitAnalysis
from ...analysis.fit.fit_models import swap_formula, linear


@analysis_options_wrapper(x_label="Freq [MHz]")
class FloquetConditionalPhaseAnalysis(CurveFitAnalysis):
    """FloquetConditionalPhaseFixed analysis."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.x_label = "Z Amp (V)"
        options.fit_points = 10
        options.accumulation_phase = np.pi
        options.swap_fit_args = []
        options.adapter_amp_list = []

        options.fit_model = FitModel(fit_func=linear)
        options.p0 = {"k": 0, "baseline": 0}
        options.curve_fit_extra.update({"ftol": 1.49012e-8, "xtol": 1.49012e-8})
        options.quality_bounds = [0.99, 0.9, 0.8]

        options.result_parameters = [
            ParameterRepr(name="ac_scan", repr="Amp", unit="V"),
            ParameterRepr(name="ac_adapter", repr="Amp", unit="V"),
            ParameterRepr(name="tc", repr="Tc", unit="ns"),
        ]

        return options

    def _create_analysis_data(self) -> QDict:
        """Create Analysis data provided for detailed analyze.

        Returns:
            A QDict object, key represents data type and value is
            CurveAnalysisData object.
        """
        accumulation_phase = self.options.accumulation_phase
        fit_points = self.options.fit_points
        half_fit_points = int(fit_points / 2)

        analysis_data_dict = QDict()
        data_key = list(
            self.options.get("data_key", None) or self._experiment_data.y_data.keys()
        )
        for key in data_key:
            if key in self.experiment_data.y_data:
                if key == "phase":
                    x_arr = self._experiment_data.x_data
                    y_arr = self.experiment_data.y_data.get(key, np.array([]))
                    length = len(y_arr)
                    select_idx = np.argmin(np.abs(y_arr - accumulation_phase))
                    left_idx = select_idx - half_fit_points
                    right_idx = select_idx + half_fit_points
                    left_idx = left_idx if left_idx > 0 else 0
                    right_idx = right_idx if right_idx < length else length -1

                    analysis_data = CurveAnalysisData(
                        x=x_arr[left_idx: right_idx],
                        y=y_arr[left_idx: right_idx],
                    )
                else:
                    analysis_data = CurveAnalysisData(
                        x=np.copy(self._experiment_data.x_data),
                        y=np.copy(self.experiment_data.y_data.get(key, [])),
                    )
                analysis_data_dict[key] = analysis_data
        return analysis_data_dict

    def _extract_result(self):
        """Extract Xpi from fitted data."""
        data_key = self.experiment_data.metadata.process_meta.get("best_data_key")
        analysis_data = self.analysis_datas[data_key]
        k, b = analysis_data.fit_data.popt

        z_amp_ret = self.results.ac_scan
        tc_ret = self.results.tc

        z_amp = (self.options.accumulation_phase - b) / k
        z_amp_ret.value = z_amp

        if self.options.adapter_amp_list or self.options.adapter_freq_list:
            x_data = self.experiment_data.x_data
            if self.options.adapter_freq_list:
                f = interp1d(
                    np.array(x_data),
                    np.array(self.options.adapter_freq_list),
                    kind="cubic",
                )
            else:
                f = interp1d(
                    np.array(x_data),
                    np.array(self.options.adapter_amp_list),
                    kind="cubic",
                )

            try:
                ac_adapter = f(z_amp)
                self.results.ac_adapter.value = float(ac_adapter)
            except Exception as e:
                pyqlog.error(f"interpolate error, because {e}")

        swap_fit_args = self.options.swap_fit_args
        if swap_fit_args:
            tc = 1 / swap_formula(z_amp, *swap_fit_args)
            tc_ret.value = tc
        else:
            pyqlog.warning(
                f"The swap_fit_args is {swap_fit_args}, " f"can't calculate tc value!"
            )

        if self.options.is_plot is True:
            pos = (round(z_amp, 6), round(self.options.accumulation_phase, 6))
            self.drawer.set_options(
                text_pos=[pos], text_rp=[f"Target\n{pos}"], text_key=[data_key]
            )
