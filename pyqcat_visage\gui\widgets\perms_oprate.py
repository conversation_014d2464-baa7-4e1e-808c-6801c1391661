# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/02/18
# __author:       <PERSON><PERSON><PERSON>

from typing import TYPE_CHECKING, Dict, List, Union

from PySide6.QtWidgets import QCheckBox, QVBoxLayout, QMessageBox, QSpacerItem, QSizePolicy

from pyqcat_visage.gui.widgets.title_window import TitleWindow
from pyqcat_visage.gui.permission_operate_ui import Ui_MainWindow

if TYPE_CHECKING:
    from pyqcat_visage.gui.main_window import VisageGUI


class PermsOperateWindow(TitleWindow):
    def __init__(self, gui: "VisageGUI", parent=None, user_flag: bool = True):
        super().__init__(parent)
        self._ui = Ui_MainWindow()
        self._ui.setupUi(self)
        self.user_flag = user_flag
        self.gui = gui
        self.widgets = [self._ui.ListBox1, self._ui.ListBox2]
        self.layouts: Dict[int, QVBoxLayout] = {}
        self.checkboxes: List[QCheckBox] = []
        self.origin_perms_ids = []
        self.init_widget()

    @property
    def ui(self):
        return self._ui

    def reset_window_layout(self):
        pass

    def load_default_data(self):
        pass

    def clear_children(self):
        self.checkboxes.clear()
        self.origin_perms_ids.clear()
        for widget in self.widgets:
            layout = widget.layout()
            if layout:
                while layout.count():
                    child = layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()
        self.ui.groupBox_2.update()

    def init_widget(self):
        if self.user_flag:
            self.ui.targetName.setText("username")
            if self.is_super:
                ret_data = self.gui.backend.db.query_usernames()
            else:
                ret_data = self.gui.backend.db.query_usernames(self.group_name)
        else:
            self.ui.targetName.setText("group")
            ret_data = self.gui.backend.db.query_group_names(all_group=True)
        self.ui.targetBox.clear()
        self.clear_children()
        if ret_data.get("code") == 200:
            if ret_data.get("data"):
                self.ui.targetBox.addItems(ret_data["data"])

    def query_info(self):
        name = self.ui.targetBox.currentText()
        if self.user_flag:
            ret_data = self.gui.backend.db.query_perms_list(username=name)
            ret_data2 = self.gui.backend.db.query_perms_user(username=name)
        else:
            ret_data = self.gui.backend.db.query_perms_list(group=name)
            ret_data2 = self.gui.backend.db.query_perms_group(group=name)
        perms_map = [x["id"] for x in ret_data2.get("data")]
        self.clear_children()
        self.origin_perms_ids = perms_map
        for num, item in enumerate(ret_data.get("data")):
            perms_id = item["id"]
            index = num % len(self.widgets)
            layout = self.layouts.get(index)
            if not layout:
                self.layouts[index] = QVBoxLayout(self.widgets[index])

            checkbox = QCheckBox(item["name"])
            checkbox.setToolTip(perms_id)
            if perms_id in perms_map:
                checkbox.setChecked(True)
            # if item["type"] == 2:
            #     checkbox.setStyleSheet("color: red;")
            self.checkboxes.append(checkbox)
            self.layouts[index].addWidget(checkbox)
            self.layouts[index].setStretch(0, 1)
        vertical_spacer = QSpacerItem(10, 10, QSizePolicy.Minimum, QSizePolicy.Expanding)
        self.layouts[0].addItem(vertical_spacer)
        if len(self.layouts) > 1:
            self.layouts[1].addItem(vertical_spacer)
        self.ui.groupBox_2.update()

    @staticmethod
    def compare_diff_list(old_list: List, new_list: List):
        """
        Compare the two lists and calculate the difference set
        Args:
            old_list: origin data
            new_list: new data

        Returns(tuple):
                (add_list, del_list)
        """
        if not old_list and not new_list:
            return [], []
        elif not old_list:
            return new_list, []
        elif not new_list:
            return [], old_list
        add_list = list(set(new_list) - set(old_list))
        del_list = list(set(old_list) - set(new_list))
        return add_list, del_list

    def save_info(self):
        new_perms_ids = []
        for checkbox in self.checkboxes:
            if checkbox.isChecked():
                new_perms_ids.append(checkbox.toolTip())
        name = self.ui.targetBox.currentText()
        add_perms, del_perms = self.compare_diff_list(self.origin_perms_ids, new_perms_ids)
        if not add_perms and not del_perms:
            QMessageBox().warning(self.ui.groupBox_2, "warning", "no change permissions!")
            return
        if add_perms:
            add_func = self.gui.backend.db.add_perms_user if self.user_flag else self.gui.backend.db.add_perms_group
            ret_data = add_func(add_perms, name)
            self.handler_ret_data(ret_data, show_suc=True)

        if del_perms:
            del_func = self.gui.backend.db.del_perms_user if self.user_flag else self.gui.backend.db.del_perms_group
            ret_data = del_func(del_perms, name)
            self.handler_ret_data(ret_data, show_suc=True)
        self.origin_perms_ids = new_perms_ids
        return


