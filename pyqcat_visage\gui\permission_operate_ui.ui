<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>960</width>
    <height>664</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Permission Manage</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item row="0" column="0">
     <widget class="QWidget" name="widget_main" native="true">
      <layout class="QVBoxLayout" name="verticalLayout" stretch="1,4,1">
       <item>
        <widget class="QWidget" name="widget" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,3,4,2,1">
          <item>
           <widget class="QLabel" name="targetName">
            <property name="text">
             <string>组</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="SearchComboBox" name="targetBox"/>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>268</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="queryButton">
            <property name="text">
             <string>query</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>267</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_2">
         <property name="title">
          <string>Permission Manage</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <widget class="QWidget" name="ListBox1" native="true"/>
          </item>
          <item>
           <widget class="QWidget" name="ListBox2" native="true"/>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="widget_3" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>314</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="saveButton">
            <property name="text">
             <string>Save</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>314</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SearchComboBox</class>
   <extends>QComboBox</extends>
   <header>.widgets.combox_custom.combox_search</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>queryButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>query_info()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>603</x>
     <y>60</y>
    </hint>
    <hint type="destinationlabel">
     <x>388</x>
     <y>336</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>saveButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>save_info()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>388</x>
     <y>592</y>
    </hint>
    <hint type="destinationlabel">
     <x>388</x>
     <y>336</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>query_info()</slot>
  <slot>save_info()</slot>
 </slots>
</ui>
