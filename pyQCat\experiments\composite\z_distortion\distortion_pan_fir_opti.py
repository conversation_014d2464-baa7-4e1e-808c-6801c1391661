# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2021 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/04/28
# __author:       <PERSON><PERSON><PERSON><PERSON>

from copy import deepcopy

import numpy as np
from scipy.interpolate import interp1d
from scipy.optimize import Bounds, minimize
from sklearn.metrics import mean_squared_error

from ....analysis.library.distortion_pan_opti_analysis import DistortionOptiAnalysis
from ....log import pyqlog
from ....qaio_property import QAIO
from ....structures import MetaData, Options
from ....tools.de_optimize.import_ea import ea
from ...composite_experiment import CompositeExperiment
from ...single import DistortionPhaseTomo


class DistortionFIROpti(CompositeExperiment):
    _sub_experiment_class = DistortionPhaseTomo

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("FIR0", list)
        options.set_validator("FIR_time", list, limit_null=True)
        options.set_validator("opti_method", ["NM", "DE"])
        options.set_validator("avg_num", int)
        options.set_validator("bounds", list)
        options.set_validator("opti_opts", dict)
        options.set_validator("ref_time", float)
        options.set_validator("ref_update", bool)
        options.set_validator("distortion_type", ["sos", "width"])

        options.FIR0 = None
        options.FIR_time = None
        options.opti_method = "NM"
        options.avg_num = 5
        options.bounds = None
        options.opti_opts = None
        options.ref_time = 0
        options.ref_update = False
        options.distortion_type = "sos"
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.delays = None
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.iter_num = 0
        options.response_arr0 = None
        options.FIR_num = None
        options.FIR_width = None
        options.ts_list = None
        options.cost_trace = []
        options.phase_ref_iter0 = None
        options.ref_alter_idx = 1
        return options

    def _check_options(self):
        super()._check_options()

        if self.experiment_options.opti_method == "NM":
            self._label = "DistortionFIROpti_NM"
            if self.experiment_options.opti_opts is None:
                self.set_experiment_options(
                    opti_opts={
                        "maxiter": 150,
                        "maxfev": 150,
                        "xatol": 3e-5,
                        "return_all": True,
                        "disp": True,
                    }
                )
        elif self.experiment_options.opti_method == "DE":
            self._label = "DistortionFIROpti_DE"
            if self.experiment_options.opti_opts is None:
                self.set_experiment_options(
                    opti_opts={"NIND": 5, "MAXGEN": 200, "mutF": 0.7, "XOVR": 0.7}
                )
        else:
            raise ValueError(
                f"opti_method {self.experiment_options.opti_method} is not supported now."
            )

        if self.experiment_options.bounds is None:
            FIR0 = self.experiment_options.FIR0
            FIR_range = np.max(FIR0) - np.min(FIR0)
            lb = FIR0 - 1.5 * FIR_range
            ub = FIR0 + 1.5 * FIR_range
            self.set_experiment_options(bounds=(lb, ub))
            pyqlog.log("EXP", f"bounds: {self.experiment_options.bounds}")

        self.set_analysis_options(
            delays=self.experiment_options.child_exp_options.delays
        )

        FIR_time_raw = self.experiment_options.FIR_time
        self.experiment_options.FIR_time = [
            round(round(t * QAIO.awg_sample_rate) / QAIO.awg_sample_rate, 6)
            for t in FIR_time_raw
        ]

        FIR_num = round(self.experiment_options.FIR_time[-1] * QAIO.awg_sample_rate) + 1
        FIR_width = (FIR_num - 1) / QAIO.awg_sample_rate
        ts_list = [round(t, 6) for t in np.linspace(0, FIR_width, FIR_num)]
        exp = deepcopy(self.child_experiment)
        exp.compensates[self.qubits[0]].z_distortion_type = "width"
        exp_compensates = exp.compensates[self.qubits[0]]
        self.set_run_options(
            # delay_arr=exp_compensates.z_distortion_tlist,
            response_arr0=exp_compensates.z_distortion_solist,
            FIR_num=FIR_num,
            FIR_width=FIR_width,
            ts_list=ts_list,
        )

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.draw_meta = {
            "FIR_num": self.run_options.FIR_num,
            "FIR_width": self.run_options.FIR_width,
        }
        return metadata

    def cost_fun(self, FIR: list):
        FIR_interp = interp1d(self.experiment_options.FIR_time, FIR, kind="cubic")
        FIR_list = FIR_interp(self.run_options.ts_list).tolist()

        pyqlog.info(f"FIR: \n{list(zip(self.experiment_options.FIR_time, FIR))}")

        exp = deepcopy(self.child_experiment)
        exp.set_parent_file(self, f"iter{self.run_options.iter_num}")

        distortion_type = self.experiment_options.distortion_type
        if distortion_type == "width":
            response_arr0 = self.run_options.response_arr0
            exp.compensates[self.qubits[0]].z_distortion_type = "width"
            exp_compensates = exp.compensates[self.qubits[0]]
            exp_compensates.z_distortion_solist[: self.run_options.FIR_num] = (
                response_arr0[: self.run_options.FIR_num] + FIR_list
            )
        elif distortion_type == "sos":
            h = np.diff(np.hstack([0, FIR_list]))
            h /= sum(h)
            pyqlog.info(f"h_list: \n{list(zip(self.experiment_options.FIR_time, h))}")

            exp.compensates[self.qubits[0]].z_distortion_type = "sos"
            exp_compensates = exp.compensates[self.qubits[0]]
            exp_compensates.z_distortion_sos.update(
                {"Low_temperature_FIR_tf_filter": [h.tolist()]}
            )
        else:
            raise ValueError(f"distortion_type {distortion_type} is not supported.")

        avg_num = self.experiment_options.avg_num
        if self.run_options.iter_num == 0:
            avg_num *= 1
        # exp.set_experiment_options(
        #     scheme=self.experiment_options.scheme,
        #     drag_gap=self.experiment_options.drag_gap,
        #     t1=self.experiment_options.t1,
        #     t2=self.experiment_options.t2,
        #     delays=np.tile(self.experiment_options.delays, avg_num),
        #     # correct_read=self.experiment_options.correct_read,
        #     z_amp=self.experiment_options.z_amp,
        #     fidelity_correct_type=self.experiment_options.fidelity_correct_type,
        # )
        exp.set_analysis_options(
            dicarlo_ref_time=self.experiment_options.ref_time, avg_num=avg_num
        )
        exp.run()
        exp.clear_params()

        ecop = self.experiment_options.child_exp_options
        if ecop.scheme == "pan":
            phases = exp.analysis.results.phase_unwrap.value
        elif ecop.scheme == "dicarlo":
            phases = exp.analysis.results.phase_net.value
        else:
            raise ValueError(f"scheme {ecop.scheme} is not supported.")

        phase_set = np.reshape(phases, (avg_num, len(ecop.delays)))

        phase_unwrap_avg = np.mean(phase_set, axis=0)

        if self.run_options.iter_num == 0:
            self.run_options.phase_ref_iter0 = np.mean(
                phase_unwrap_avg[
                    np.array(ecop.delays) >= self.experiment_options.ref_time
                ]
            )

        if self.experiment_options.ref_update:
            phase_unwrap_ref = np.mean(
                phase_unwrap_avg[
                    np.array(ecop.delays) >= self.experiment_options.ref_time
                ]
            )
            ref_lb = (
                self.run_options.phase_ref_iter0
                - abs(self.run_options.phase_ref_iter0) * 0.075
            )
            ref_ub = (
                self.run_options.phase_ref_iter0
                + abs(self.run_options.phase_ref_iter0) * 0.075
            )
            ref_lb_alter = np.array([-2 * np.pi, 0, 2 * np.pi]) + ref_lb
            ref_ub_alter = np.array([-2 * np.pi, 0, 2 * np.pi]) + ref_ub
            ref_outlier_flag = [
                (phase_unwrap_ref < ref_lbi) or (phase_unwrap_ref > ref_ubi)
                for ref_lbi, ref_ubi in list(zip(ref_lb_alter, ref_ub_alter))
            ]
            if np.all(ref_outlier_flag):
                pyqlog.info(
                    f"phase_ref {phase_unwrap_ref} outside of ref_bounds: ({ref_lb}, {ref_ub})"
                )
                phase_unwrap_ref = self.run_options.phase_ref_iter0
            else:
                self.run_options.ref_alter_idx = ref_outlier_flag.index(False)
        else:
            phase_unwrap_ref = self.run_options.phase_ref_iter0

        phase_unwrap_ref_alter = np.array([-2 * np.pi, 0, 2 * np.pi]) + phase_unwrap_ref
        phase_unwrap_std_alter = [
            mean_squared_error(phase_unwrap_avg, [refi] * len(phase_unwrap_avg))
            for refi in phase_unwrap_ref_alter
        ]
        phase_unwrap_std = np.min(phase_unwrap_std_alter)
        pyqlog.log("EXP", f"ref: {phase_unwrap_ref}, rmse: {phase_unwrap_std_alter}")

        if np.argmin(phase_unwrap_std_alter) != 1:
            self.run_options.ref_alter_idx = np.argmin(phase_unwrap_std_alter)

        if self.run_options.ref_alter_idx == 0:
            phase_unwrap_avg += 2 * np.pi
        elif self.run_options.ref_alter_idx == 2:
            phase_unwrap_avg -= 2 * np.pi

        exp.analysis.provide_for_parent["phase_unwrap_avg"] = phase_unwrap_avg
        exp.analysis.provide_for_parent["phase_unwrap_std"] = phase_unwrap_std
        self._experiments.append(exp)

        # 为了避免tomo半径超过1的情形(通常是由于参数偏差过大导致无法正常激发比特)，增加与tomo半径相关的惩罚项
        r = np.mean(
            np.sqrt(
                exp.analysis.results.sigma_x.value**2
                + exp.analysis.results.sigma_y.value**2
            )
        )
        punish = (1 - r) ** 2 / (1.1 * np.sqrt(2) - r) ** 2
        cost = phase_unwrap_std + punish
        pyqlog.info(f"std: {phase_unwrap_std}\npunish: {punish}\ncost: {cost}")

        self.run_options.cost_trace.append(cost)

        self.file.save_data(
            self.experiment_options.FIR_time,
            FIR,
            name=f"FIR opti_iter{self.run_options.iter_num}_cost={cost:.2e}_std={phase_unwrap_std:.2e}_punish={punish:.2e}",
        )

        self.run_options.iter_num += 1
        if self.run_options.iter_num % 3 == 0:
            self._run_analysis(
                x_data=list(range(self.run_options.iter_num)),
                analysis_class=DistortionOptiAnalysis,
            )
            self.file.save_data(
                list(range(self.run_options.iter_num)),
                self.run_options.cost_trace,
                name=f"cost_trace",
            )
        return cost

    def evalVars(self, Vars):
        cost = []
        for i in range(Vars.shape[0]):
            costi = self.cost_fun(Vars[i, :])
            cost.append(costi)
        return np.vstack(cost)

    def opti_NM(self):
        res = minimize(
            self.cost_fun,
            x0=self.experiment_options.FIR0,
            method="Nelder-Mead",
            bounds=Bounds(*self.experiment_options.bounds),
            options=self.experiment_options.opti_opts,
        )
        return res

    def opti_DE(self):
        opti_opts = self.experiment_options.opti_opts
        opti_dim = len(self.experiment_options.FIR_time)

        problem = ea.Problem(
            name="soea FIR fit",
            M=1,  # 初始化M（目标维数）
            maxormins=[
                1
            ],  # 初始化maxormins（目标最小最大化标记列表，1：最小化该目标；-1：最大化该目标）
            Dim=opti_dim,  # 决策变量维数
            varTypes=[0] * opti_dim,  # 决策变量的类型列表，0：实数；1：整数
            lb=self.experiment_options.bounds[0],  # 决策变量下界
            ub=self.experiment_options.bounds[1],  # 决策变量上界
            evalVars=self.evalVars,
        )

        algorithm = ea.soea_DE_best_1_bin_templet(
            problem,
            ea.Population(Encoding="RI", NIND=opti_opts["NIND"]),
            MAXGEN=opti_opts["MAXGEN"],
            logTras=1,
            outFunc=None,
            # trappedValue=1e-10,
            # maxTrappedCount=20
        )
        algorithm.mutOper.F = opti_opts["mutF"]
        algorithm.recOper.XOVR = opti_opts["XOVR"]

        prophet = (
            None
            if self.experiment_options.FIR0 is None
            else np.array(self.experiment_options.FIR0)
        )
        res = ea.optimize(
            algorithm,
            prophet=prophet,
            verbose=True,
            drawing=1,
            outputMsg=True,
            drawLog=False,
            saveFlag=True,
            dirName=f"{self.file.dirs}soea_DE result",
        )
        return res

    def run(self):
        super().run()
        if self.experiment_options.opti_method == "NM":
            res = self.opti_NM()
        elif self.experiment_options.opti_method == "DE":
            res = self.opti_DE()
        else:
            raise ValueError(
                f"opti_method {self.experiment_options.opti_method} is not supported now."
            )

        self._run_analysis(
            x_data=list(range(self.run_options.iter_num)),
            analysis_class=DistortionOptiAnalysis,
        )

        pyqlog.log("EXP", f"optimize result: \n{res}")
