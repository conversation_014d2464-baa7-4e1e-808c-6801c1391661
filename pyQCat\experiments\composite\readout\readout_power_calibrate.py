# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/04
# __author:       <PERSON> <PERSON>

"""
Readout power calibrate node composite experiment.

"""

import numpy as np

from ....analysis.library import ReadoutPowerCaliAnalysis
from ....structures import Options
from ....tools import qarange
from .single_shot_composite import SingleShotComposite, ExperimentRunMode


class ReadoutPowerCalibrate(SingleShotComposite):
    """Optimize Readout Power of Qubit."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            optimize_field (str): Give target optimize field.
            sweep_list (List, np.ndarray): Scan optimize field list.

        """
        options = super()._default_experiment_options()

        options.optimize_field = "probe_power"
        options.sweep_list = qarange(-35, -15, 1)
        options.run_mode = ExperimentRunMode.sync_mode

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()

        options.analysis_class = ReadoutPowerCaliAnalysis
        return options


class ReadoutAmpCalibration(ReadoutPowerCalibrate):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            optimize_field (str): Give target optimize field.
            sweep_list (List, np.ndarray): Scan optimize field list.

        """
        options = super()._default_experiment_options()

        options.set_validator("left_rate", float)
        options.set_validator("right_rate", float)
        options.set_validator("point", int)

        options.optimize_field = "Mwave.amp"
        options.sweep_list = None
        options.left_rate = 0.1
        options.right_rate = 0.1
        options.point = 30

        return options

    def _check_options(self):
        """Check options."""
        super()._check_options()

        eop = self.experiment_options

        if eop.sweep_list is None:
            amp = self.child_experiment.qubit.Mwave.amp or 0.15
            la = amp * (1 - eop.left_rate)
            ra = amp * (1 + eop.right_rate)
            sweep_list = list(np.round(np.linspace(la, ra, eop.point), 4))
            eop.sweep_list = sweep_list
            self.set_run_options(x_data=sweep_list)
