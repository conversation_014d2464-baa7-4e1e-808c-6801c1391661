﻿pyQCat.experiments.single.CouplerT1
===================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: CouplerT1

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CouplerT1.__init__
      ~CouplerT1.acquire_pulse
      ~CouplerT1.cal_fidelity
      ~CouplerT1.experiment_info
      ~CouplerT1.from_experiment_context
      ~CouplerT1.get_qubit_str
      ~CouplerT1.get_xy_pulse
      ~CouplerT1.get_z_pulse
      ~CouplerT1.jupyter_schedule
      ~CouplerT1.options_table
      ~CouplerT1.play_pulse
      ~CouplerT1.plot_schedule
      ~CouplerT1.run
      ~CouplerT1.set_analysis_options
      ~CouplerT1.set_experiment_options
      ~CouplerT1.set_multiple_IF
      ~CouplerT1.set_multiple_index
      ~CouplerT1.set_parent_file
      ~CouplerT1.set_run_options
      ~CouplerT1.set_sweep_order
      ~CouplerT1.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CouplerT1.analysis
      ~CouplerT1.analysis_options
      ~CouplerT1.experiment_options
      ~CouplerT1.run_options
   
   