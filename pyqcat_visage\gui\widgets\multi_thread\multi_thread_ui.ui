<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>mainWindow</class>
 <widget class="QMainWindow" name="mainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1433</width>
    <height>686</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_8">
    <item>
     <widget class="QSplitter" name="splitter">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QWidget" name="widget" native="true">
       <layout class="QVBoxLayout" name="verticalLayout" stretch="1,10">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,10,1,1,2,1,2,1,3,1">
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="sample_name">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <bold>true</bold>
             </font>
            </property>
            <property name="focusPolicy">
             <enum>Qt::NoFocus</enum>
            </property>
            <property name="text">
             <string>sample</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLCDNumber" name="thread_count_lcd"/>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="control_table">
            <property name="text">
             <string>hidden tabel</string>
            </property>
            <property name="icon">
             <iconset resource="../../_imgs/_imgs.qrc">
              <normaloff>:/refresh.png</normaloff>:/refresh.png</iconset>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
            <property name="checked">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="shot_btn">
            <property name="text">
             <string>shot</string>
            </property>
            <property name="icon">
             <iconset resource="../../_imgs/_imgs.qrc">
              <normaloff>:/screenshot.png</normaloff>:/screenshot.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_5">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QComboBox" name="show_type"/>
          </item>
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <widget class="MultiTopologyWidget" name="chip_widget" native="true">
          <property name="enabled">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="widget_2" native="true">
       <layout class="QVBoxLayout" name="verticalLayout_7" stretch="2,1,30">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,4,2,3,4,2,1">
          <item>
           <spacer name="horizontalSpacer_8">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="label">
            <property name="text">
             <string>Scheduler Queue</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLCDNumber" name="lcd_scheduler"/>
          </item>
          <item>
           <spacer name="horizontalSpacer_6">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>Wait Queue</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLCDNumber" name="lcd_wait"/>
          </item>
          <item>
           <spacer name="horizontalSpacer_7">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer_2">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>54</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QSplitter" name="splitter_table">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <widget class="MultiTableView" name="tr_table_view">
           <property name="enabled">
            <bool>true</bool>
           </property>
          </widget>
          <widget class="QWidget" name="">
           <layout class="QVBoxLayout" name="verticalLayout_2" stretch="1,20">
            <item>
             <spacer name="verticalSpacer">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QTabWidget" name="tabWidget">
              <property name="currentIndex">
               <number>2</number>
              </property>
              <widget class="QWidget" name="tab">
               <attribute name="title">
                <string>Scheduler_cache</string>
               </attribute>
               <layout class="QVBoxLayout" name="verticalLayout_3">
                <item>
                 <widget class="QTableView" name="task_list_view"/>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="tab_5">
               <attribute name="title">
                <string>Nomal_task_list</string>
               </attribute>
               <layout class="QVBoxLayout" name="verticalLayout_6">
                <item>
                 <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="10,3,1,0">
                  <item>
                   <spacer name="horizontalSpacer_13">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="QLabel" name="label_3">
                    <property name="text">
                     <string>normal_task_list</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <spacer name="horizontalSpacer_9">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="QLCDNumber" name="lcd_len_normal"/>
                  </item>
                 </layout>
                </item>
                <item>
                 <widget class="QTableView" name="view_list_normal"/>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="tab_3">
               <attribute name="title">
                <string>Low_priority_list</string>
               </attribute>
               <layout class="QVBoxLayout" name="verticalLayout_5">
                <item>
                 <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="10,3,1,0">
                  <item>
                   <spacer name="horizontalSpacer_12">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="QLabel" name="label_5">
                    <property name="text">
                     <string>low_priority_list</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <spacer name="horizontalSpacer_10">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="QLCDNumber" name="lcd_len_low_priority"/>
                  </item>
                 </layout>
                </item>
                <item>
                 <widget class="QTableView" name="view_list_low"/>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="tab_2">
               <attribute name="title">
                <string>Details</string>
               </attribute>
               <layout class="QVBoxLayout" name="verticalLayout_4">
                <item>
                 <widget class="QTextEdit" name="detail_text">
                  <property name="enabled">
                   <bool>true</bool>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                  <property name="html">
                   <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;task details&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MultiTopologyWidget</class>
   <extends>QWidget</extends>
   <header>.multi_topology_widget</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MultiTableView</class>
   <extends>QTableView</extends>
   <header>.threads_view</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../_imgs/_imgs.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>control_table</sender>
   <signal>clicked()</signal>
   <receiver>mainWindow</receiver>
   <slot>change_tabel_hidden_status()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>624</x>
     <y>45</y>
    </hint>
    <hint type="destinationlabel">
     <x>655</x>
     <y>342</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>show_type</sender>
   <signal>currentTextChanged(QString)</signal>
   <receiver>mainWindow</receiver>
   <slot>change_show_table()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>652</x>
     <y>45</y>
    </hint>
    <hint type="destinationlabel">
     <x>655</x>
     <y>342</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>shot_btn</sender>
   <signal>clicked()</signal>
   <receiver>mainWindow</receiver>
   <slot>screen_shot()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>556</x>
     <y>45</y>
    </hint>
    <hint type="destinationlabel">
     <x>655</x>
     <y>342</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>change_tabel_hidden_status()</slot>
  <slot>change_show_table()</slot>
  <slot>screen_shot()</slot>
 </slots>
</ui>
