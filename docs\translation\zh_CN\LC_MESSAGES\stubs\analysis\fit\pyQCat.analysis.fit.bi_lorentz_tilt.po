# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/fit/pyQCat.analysis.fit.bi_lorentz_tilt.rst:2
msgid "pyQCat.analysis.fit.bi\\_lorentz\\_tilt"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:1
msgid "Bia Lorentz fit formula."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:3
msgid ""
"amp = (coe\\cdot x + a) \\cdot \\left [ \\frac{1}{1 + (\\frac{x-b}{c} "
")^2} + offset \\right]\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:7
msgid "independent variable"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:10
msgid "peak height"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:13
msgid "base offset"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:16
msgid "peek position x0 value"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:19
msgid "left peek width"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:22
msgid "right peek width"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:25
msgid "slope coefficient"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt:28
msgid "strain variable"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.bi_lorentz_tilt
msgid "Return type"
msgstr ""

