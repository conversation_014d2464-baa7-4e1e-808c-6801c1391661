# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/06
# __author:       <PERSON> Fang

"""
Distortion T1 Composite Analysis.

"""
from typing import List, Union, Tuple, Any

import matplotlib.pyplot as plt
import numpy as np

from ..algorithms import (
    calculate_offset_arr,
    calculate_distortion,
    calculate_lfilter_paras,
    create_ideal_wave,
    calculate_pre_distortion_wave,
    calculate_simulation_wave
)
from ..curve_analysis import CurveAnalysis
from ..fit.fit_models import complex_pole
from ..quality.goodness_of_fit import GoodnessofFit, BaseQuality, QualityDescribe
from ..specification import (
    ParameterRepr,
    FitModel,
    FitOptions,
    CurveAnalysisData
)
from ...structures import Options


class DistortionT1CompositeAnalysis(CurveAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

            Options:
                z_amp (float): When run experiment,set Z amp of Z line pulse.
                dac_sample_rate (float): XY Line sample rate, used to calculate delay and response.
                sample_rate (float): Z Line sample rate, used to poles model fit.
                lfilter_flag (float): Use or not pole model.
                iteration_time (int): Iteration time distortion composite.
                cal_response_mode (str): Calculate response mode, `add` or `response`.

        """
        options = super()._default_options()
        options.z_amp = -0.5
        options.dac_sample_rate = 3.2
        options.sample_rate = 1.2
        options.lfilter_flag = True
        options.iteration_time = 0
        options.cal_response_mode = "add"
        options.expect_width = None

        options.dt_list = []
        options.so_list = []

        options.quality_bounds = [0.9999, 0.999, 0.9]
        options.sub_key = "P1"

        options.skip_width = 20
        options.dash_band = 0.005
        options.error_rate = 0.006
        options.hit_rate = None

        options.origin_delay_arr = None
        options.origin_response_arr = None
        options.offset_arr = None

        options.ideal_t_arr = None
        options.ideal_wave = None
        options.pre_distortion_wave = None
        options.simulation_wave = None
        options.ylim = [0.95, 1.02]

        options.subplots = (2, 2)
        options.curve_drawer.set_options(figsize=(20, 32))
        options.x_label = "Time [ns]"
        options.y_label = [
            "Deal Response",
            "Origin Response",
            "Amp [v]",
            "Z offset [v]"
        ]
        options.pcolormesh_options = {
            "shading": "auto",
            "cmap": plt.cm.get_cmap('plasma'),
            "vmin": None,
            "vmax": None
        }
        options.curve_fit_extra.update({"ftol": 1.49012e-8,
                                        "xtol": 1.49012e-8})

        options.result_parameters = [
            ParameterRepr(name="distortion_width", repr="width", unit="ns", param_path="Compensate.z_distortion_width"),
            ParameterRepr(name="distortion_ab", param_path="Compensate.z_distortion_ab"),
            ParameterRepr(name="delay_arr", param_path="Compensate.z_distortion_tlist"),
            ParameterRepr(name="response_arr", param_path="Compensate.z_distortion_solist"),
            "limit_flag"
        ]

        return options

    def _pre_operation(self):
        """When not the first iteration qubit_test,
        change experiment data, about experiment_data.x_data, y_data.
        """
        target_field = self.options.data_key[0]
        cal_response_mode = self.options.cal_response_mode

        origin_delay_arr = self.experiment_data.x_data
        origin_response_arr = self.experiment_data.y_data.get(target_field)
        offset_arr = calculate_offset_arr(
            origin_response_arr,
            z_amp=self.options.z_amp,
            cal_response_mode=cal_response_mode
        )

        self.set_options(origin_delay_arr=origin_delay_arr,
                         origin_response_arr=origin_response_arr,
                         offset_arr=offset_arr)

        if self.options.iteration_time != 0 or len(self.options.dt_list) > 1:
            if self.options.dt_list and self.options.so_list:
                delay_arr, response_arr = calculate_distortion(
                    self.options.dt_list,
                    self.options.so_list,
                    sample_rate=self.options.dac_sample_rate
                )
                self.experiment_data._x_data = delay_arr
                self.experiment_data._y_data.update(
                    {self.options.data_key[0]: response_arr}
                )

        if self.options.lfilter_flag is True:
            ts = 1 / self.options.sample_rate
            fit_model = FitModel(fit_func=complex_pole(ts))
            self.set_options(fit_model=fit_model)

            lb_list = [
                0, 0.5, 0, 0.5,
                0, 0.9, np.pi * 2 * ts / 2000, 0,
                0, 0.9, np.pi * 2 * ts / 2000, 0
            ]
            ub_list = [
                0.3, 1, 0.3, 1,
                0.3, 1, np.pi * 2 * ts / 50, 2 * np.pi,
                0.3, 1, np.pi * 2 * ts / 1.25, 2 * np.pi
            ]

            for para, lb, ub in zip(fit_model.signature, lb_list, ub_list):
                self.options.bounds.update({para: [lb, ub]})

    def _guess_fit_param(self, fit_opt: FitOptions,
                         data: CurveAnalysisData,
                         ) -> Union[FitOptions, List[FitOptions]]:
        """Guess initial fit parameters.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        ts = 1 / self.options.sample_rate
        p0_list = [
            0.005, 0.97, 0.003, 0.9983,
            0.015, 0.9987, np.pi * 2 * ts / 400, 0.473 * np.pi,
            0.0012, 0.9993, np.pi * 2 * ts / 70, 0.413 * np.pi
        ]

        p0 = {}
        for para, p in zip(self.options.fit_model.signature, p0_list):
            p0.update({para: p})

        fit_opt.p0.set_if_empty(**p0)
        return fit_opt

    def _evaluate_quality(self) -> Tuple[str, Any]:
        """Evaluates the quality of the fit or other.

        Returns:
            The goodness of fit.
        """
        target_key = self.options.data_key[0]

        # create ideal wave data, to verify distortion
        num = 2
        ts = round(1 / self.options.sample_rate, 4)
        amp = 0.5
        t_arr, ideal_wave = create_ideal_wave(num, ts, amp)

        analysis_data = self.analysis_datas[target_key]
        delay_arr = analysis_data.x
        cali_response_arr = analysis_data.y

        if self.options.get("fit_model"):
            response_arr = analysis_data.fit_data.y_fit
        else:
            response_arr = analysis_data.y

        pre_distortion_wave = calculate_pre_distortion_wave(
            delay_arr, response_arr, ideal_wave,
            sample_rate=self.options.sample_rate
        )
        simulation_wave = calculate_simulation_wave(pre_distortion_wave,
                                                    cali_response_arr)

        quality = GoodnessofFit(*self.options.quality_bounds)
        quality.evaluate(ideal_wave, simulation_wave)
        self._quality = quality

        if self.options.expect_width is not None:
            distortion_width = int(np.max(delay_arr))
            if distortion_width < self.options.expect_width:
                if not isinstance(self._quality, BaseQuality):
                    self._quality = BaseQuality()
                self._quality.descriptor = QualityDescribe.bad

        self.set_options(ideal_t_arr=t_arr,
                         ideal_wave=ideal_wave,
                         pre_distortion_wave=pre_distortion_wave,
                         simulation_wave=simulation_wave)
        return target_key, self._quality

    def _calculate_response_hit_rate(self):
        skip_width = self.options.skip_width
        dash_band = self.options.dash_band
        origin_delay_arr = self.options.origin_delay_arr
        origin_response_arr = self.options.origin_response_arr

        start_index = np.argmin(np.abs(skip_width - origin_delay_arr))
        new_response_arr = origin_response_arr[start_index:]
        length = len(new_response_arr)

        less_index_arr = np.where((new_response_arr <= (1 + dash_band)))[0]
        less_arr = new_response_arr[less_index_arr]
        target_index_arr = np.where(less_arr >= (1 - dash_band))[0]
        target_length = len(target_index_arr)

        hit_rate = target_length / length
        self.set_options(
            hit_rate=round(hit_rate, 4)
        )
        return hit_rate

    def _extract_result(self, data_key: str, quality=None):
        """Extract analysis result.

        Args:
            data_key (str): The basis for selecting data.
            quality : Quality of fit outcome.
        """
        for p_name, result in self.results.items():
            if p_name in ["distortion_ab", "delay_arr", "response_arr"]:
                result.extra.update({'out_flag': False})

        delay_arr = self.experiment_data.x_data
        response_arr = self.experiment_data.y_data.get(data_key)
        distortion_width = int(np.max(delay_arr))

        self.results.delay_arr.value = delay_arr
        self.results.response_arr.value = response_arr
        self.results.distortion_width.value = distortion_width

        if self.options.get("fit_model"):
            analysis_data = self.analysis_datas[data_key]
            a, b = calculate_lfilter_paras(*analysis_data.fit_data.popt)
            distortion_ab = [a.tolist(), b.tolist()]
        else:
            distortion_ab = []
        self.results.distortion_ab.value = distortion_ab

        hit_rate = self._calculate_response_hit_rate()
        if (1 - hit_rate) <= self.options.error_rate:
            self.results.limit_flag.value = True
        else:
            self.results.limit_flag.value = False

    def _child_depth_plot(self):
        """Plot child experiment depth."""
        origin_delay_arr = self.options.origin_delay_arr
        y_arr = None
        p_arr = []
        for i, _ in enumerate(origin_delay_arr):
            child_data = self.experiment_data.child_data(index=i)
            if y_arr is None:
                y_arr = child_data.x_data
            p_arr.append(child_data.y_data.get(self.options.sub_key))

        self.drawer.draw_color_map(origin_delay_arr, y_arr, np.array(p_arr).T,
                                   ax_index=3,
                                   **self.options.pcolormesh_options)

    def _visualization(self):
        """Plot visualization."""
        super()._visualization()

        self.drawer.set_options(raw_data_format="plot")

        # ax_index = 1, plot origin response
        origin_delay_arr = self.options.origin_delay_arr
        origin_response_arr = self.options.origin_response_arr
        dash_line = np.ones(len(origin_delay_arr))

        ylim = self.options.ylim
        y_min, y_max = ylim
        self.drawer._get_axis(0).set_ylim(ymin=y_min, ymax=y_max)
        self.drawer._get_axis(1).set_ylim(ymin=y_min, ymax=y_max)

        draw_ops = {
            "markersize": 5,
            "marker": 'o',
            "alpha": 0.8,
            "label": f"rate={self.options.hit_rate}",
            "linewidth": 2.5,
            "color": None,
        }

        self.drawer.draw_raw_data(x_data=origin_delay_arr,
                                  y_data=origin_response_arr,
                                  ax_index=1,
                                  **draw_ops)

        draw_ops.update({
            "linewidth": 0.8,
            "marker": "_",
            "color": "blue",
            "label": None
        })
        self.drawer.draw_raw_data(x_data=origin_delay_arr,
                                  y_data=1.001 * dash_line,
                                  ax_index=1,
                                  **draw_ops)

        self.drawer.draw_raw_data(x_data=origin_delay_arr,
                                  y_data=0.999 * dash_line,
                                  ax_index=1,
                                  **draw_ops)

        draw_ops.update({"color": "red"})
        self.drawer.draw_raw_data(x_data=origin_delay_arr,
                                  y_data=1.005 * dash_line,
                                  ax_index=1,
                                  **draw_ops)
        self.drawer.draw_raw_data(x_data=origin_delay_arr,
                                  y_data=0.995 * dash_line,
                                  ax_index=1,
                                  **draw_ops)

        x_arr = self.options.ideal_t_arr
        ideal_wave = self.options.ideal_wave
        pre_distortion_wave = self.options.pre_distortion_wave
        simulation_wave = self.options.simulation_wave

        draw_ops.update({
            "linewidth": 2.5,
            "marker": None,
            "color": None,
            "label": "ideal"
        })
        self.drawer.draw_raw_data(x_data=x_arr,
                                  y_data=ideal_wave,
                                  ax_index=2,
                                  **draw_ops)
        draw_ops.update({"label": "pre_distortion"})
        self.drawer.draw_raw_data(x_data=x_arr,
                                  y_data=pre_distortion_wave,
                                  ax_index=2,
                                  **draw_ops)
        draw_ops.update({"label": "simulation"})
        self.drawer.draw_raw_data(x_data=x_arr,
                                  y_data=simulation_wave,
                                  ax_index=2,
                                  **draw_ops)

        offset_arr = self.options.offset_arr

        draw_ops.update({"label": None, "color": "red"})
        self.drawer.draw_raw_data(x_data=origin_delay_arr,
                                  y_data=offset_arr,
                                  ax_index=3,
                                  **draw_ops)

        if self.has_child is True:
            self._child_depth_plot()

        # Finalize plot.
        self.drawer.format_canvas()

    def run_analysis(self):
        """Run analysis on experiment data."""
        self._pre_operation()

        super().run_analysis()


class DistortionT1CompositeNewAnalysis(DistortionT1CompositeAnalysis):
    """DistortionT1CompositeNewAnalysis class."""

    def _child_depth_plot(self):
        """Plot child experiment depth."""
        origin_delay_arr = self.options.origin_delay_arr
        sub_key = self.options.sub_key

        y_arr_list = []
        p_arr_list = []
        for i, _ in enumerate(origin_delay_arr):
            child_data = self.experiment_data.child_data(index=i)
            y_arr_list.append(child_data.x_data)
            p_arr = child_data.y_data.get(sub_key)
            p_arr_list.append(p_arr)

        self.drawer.draw_dynamic_data(
            np.array(origin_delay_arr),
            np.asarray(y_arr_list, dtype=object),
            np.asarray(p_arr_list, dtype=object),
            ax_index=3,
        )
