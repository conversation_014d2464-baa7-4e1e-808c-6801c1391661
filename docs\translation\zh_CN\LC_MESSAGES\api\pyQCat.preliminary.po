# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.preliminary.rst:2
msgid "pyQCat.preliminary package"
msgstr ""

#: ../../source/api/pyQCat.preliminary.rst:5
msgid "Module contents"
msgstr ""

#: of pyQCat.preliminary:3
msgid "Qubit Preliminary (:mod:`pyQCat.preliminary`)"
msgstr ""

#: of pyQCat.preliminary:5
msgid "pyqcat-monster preliminary, qubit or coupler."
msgstr ""

#: of pyQCat.preliminary:11:<autosummary>:1
msgid ""
":py:obj:`CavityFluxScan <pyQCat.preliminary.CavityFluxScan>`\\ "
"\\(\\[qubit\\, coupler\\]\\)"
msgstr ""

#: of pyQCat.preliminary:11:<autosummary>:1
msgid ""
":py:obj:`FindBusCavityFreq <pyQCat.preliminary.FindBusCavityFreq>`\\ "
"\\(\\[qubit\\, coupler\\]\\)"
msgstr ""

#~ msgid ""
#~ ":py:obj:`CavityFluxScan <pyQCat.preliminary.CavityFluxScan>`\\"
#~ " \\(\\[config\\, network\\_analyzer\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CavityFluxScan <pyQCat.preliminary.CavityFluxScan>`\\ "
#~ "\\(\\[config\\, network\\_analyzer\\, ...\\]\\)"
#~ msgstr ""

