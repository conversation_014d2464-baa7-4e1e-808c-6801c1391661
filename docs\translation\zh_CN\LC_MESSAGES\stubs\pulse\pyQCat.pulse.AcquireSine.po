# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:2
msgid "pyQCat.pulse.AcquireSine"
msgstr ""

#: of pyQCat.pulse.pulse_lib.AcquireSine:1
msgid "Bases: :py:class:`~pyQCat.pulse.base_pulse.PulseComponent`"
msgstr ""

#: of pyQCat.pulse.pulse_lib.AcquireSine:1
msgid "Acquisition Waveform Generator."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1 of
#: pyQCat.pulse.pulse_lib.AcquireSine.__init__:1
msgid "Create a new acquisition Waveform."
msgstr ""

#: of pyQCat.pulse.pulse_lib.AcquireSine.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.pulse.pulse_lib.AcquireSine.__init__:4
msgid "Wave width."
msgstr ""

#: of pyQCat.pulse.pulse_lib.AcquireSine.__init__:7
msgid "The list of amp, support union readout."
msgstr ""

#: of pyQCat.pulse.pulse_lib.AcquireSine.__init__:10
msgid "The list of IF, support union readout."
msgstr ""

#: of pyQCat.pulse.pulse_lib.AcquireSine.__init__:12
msgid "The name of wave, defaults to \"M\"."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.pulse.AcquireSine.__init__>`\\ \\(time\\, "
"amp\\_list\\, IF\\_list\\[\\, name\\]\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid ""
":py:obj:`correct_ac_crosstalk "
"<pyQCat.pulse.AcquireSine.correct_ac_crosstalk>`\\ "
"\\(after\\_ac\\_corsstalk\\_pulse\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid "Correct crosstalk caused by electronics device."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid ""
":py:obj:`correct_compensate "
"<pyQCat.pulse.AcquireSine.correct_compensate>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid "Correct compensate caused by electronics device."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid ""
":py:obj:`correct_delay <pyQCat.pulse.AcquireSine.correct_delay>`\\ "
"\\(delay\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid "Correct delay time caused by electronics device."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid ""
":py:obj:`correct_distortion "
"<pyQCat.pulse.AcquireSine.correct_distortion>`\\ \\(\\[plot\\]\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid "Correct distortion caused by electronics device."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid ":py:obj:`correct_pulse <pyQCat.pulse.AcquireSine.correct_pulse>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid "Correct XY or Z pulse"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid ""
":py:obj:`envelop2sequence <pyQCat.pulse.AcquireSine.envelop2sequence>`\\ "
"\\(envelop\\[\\, IF\\]\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid ":py:obj:`get_pulse <pyQCat.pulse.AcquireSine.get_pulse>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1 of
#: pyQCat.pulse.pulse_lib.AcquireSine.get_pulse:1
msgid "Calculate pulse sequence."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid ":py:obj:`get_raw_pulse <pyQCat.pulse.AcquireSine.get_raw_pulse>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid "Get pulse raw data."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid ""
":py:obj:`plot <pyQCat.pulse.AcquireSine.plot>`\\ \\(file\\_name\\[\\, "
"use\\_points\\]\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid "Plot Pulse's sequence use matplotlib."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid ":py:obj:`step <pyQCat.pulse.AcquireSine.step>`\\ \\(width\\, amp\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid ""
":py:obj:`validate_parameters "
"<pyQCat.pulse.AcquireSine.validate_parameters>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:32:<autosummary>:1
msgid "Validate parameters."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.AcquireSine.rst:34
msgid "Attributes"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid ":py:obj:`attach <pyQCat.pulse.AcquireSine.attach>`\\"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid "Flag of Pulse instance excuted '+' or '+=' operation."
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid ":py:obj:`bit <pyQCat.pulse.AcquireSine.bit>`\\"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid "Get pulse on which qubit."
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid ":py:obj:`delay <pyQCat.pulse.AcquireSine.delay>`\\"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid "Get pulse delay."
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid ":py:obj:`envelop <pyQCat.pulse.AcquireSine.envelop>`\\"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid "Get the envelop of the waveform."
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid ":py:obj:`id <pyQCat.pulse.AcquireSine.id>`\\"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid "Unique identifier for this pulse."
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid ":py:obj:`parameters <pyQCat.pulse.AcquireSine.parameters>`\\"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1
#: pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid "Return a dictionary containing the pulse's parameters."
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid ":py:obj:`pulse <pyQCat.pulse.AcquireSine.pulse>`\\"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid "Data sequence for this pulse."
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid ":py:obj:`raw_pulse <pyQCat.pulse.AcquireSine.raw_pulse>`\\"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid "Get uncorrected waveform raw data."
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid ":py:obj:`sweep <pyQCat.pulse.AcquireSine.sweep>`\\"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid "Get description of pulse sweeped parameters."
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid ":py:obj:`type <pyQCat.pulse.AcquireSine.type>`\\"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid "Get pulse type."
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid ":py:obj:`width <pyQCat.pulse.AcquireSine.width>`\\"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:1:<autosummary>:1
msgid "Return the time unit of the pulse."
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters
#: pyQCat.pulse.pulse_lib.AcquireSine.validate_parameters
msgid "Return type"
msgstr ""

#: of pyQCat.pulse.AcquireSine.parameters:3
msgid ":py:class:`~typing.Dict`\\[:py:class:`str`, :py:data:`~typing.Any`]"
msgstr ""

#: of pyQCat.pulse.pulse_lib.AcquireSine.validate_parameters:1
msgid ""
"Validate parameters. :raises PulseError: If the parameters passed are not"
" valid."
msgstr ""

#: of pyQCat.pulse.pulse_lib.AcquireSine.validate_parameters:5
msgid ":py:obj:`None`"
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.pulse.base_pulse.PulseComponent`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.pulse.AcquireSine.__init__>`\\ "
#~ "\\(time\\, amp\\_list\\, IF\\_list\\[\\, name\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_ac_crosstalk "
#~ "<pyQCat.pulse.AcquireSine.correct_ac_crosstalk>`\\ "
#~ "\\(after\\_ac\\_corsstalk\\_pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_compensate "
#~ "<pyQCat.pulse.AcquireSine.correct_compensate>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_delay "
#~ "<pyQCat.pulse.AcquireSine.correct_delay>`\\ \\(delay\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_distortion "
#~ "<pyQCat.pulse.AcquireSine.correct_distortion>`\\ "
#~ "\\(\\[plot\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`correct_pulse "
#~ "<pyQCat.pulse.AcquireSine.correct_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`envelop2sequence "
#~ "<pyQCat.pulse.AcquireSine.envelop2sequence>`\\ \\(envelop\\[\\,"
#~ " IF\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`get_pulse <pyQCat.pulse.AcquireSine.get_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_raw_pulse "
#~ "<pyQCat.pulse.AcquireSine.get_raw_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot <pyQCat.pulse.AcquireSine.plot>`\\ "
#~ "\\(file\\_name\\[\\, use\\_points\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`step <pyQCat.pulse.AcquireSine.step>`\\ \\(width\\, amp\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`validate_parameters "
#~ "<pyQCat.pulse.AcquireSine.validate_parameters>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`attach <pyQCat.pulse.AcquireSine.attach>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`bit <pyQCat.pulse.AcquireSine.bit>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`delay <pyQCat.pulse.AcquireSine.delay>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`envelop <pyQCat.pulse.AcquireSine.envelop>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`id <pyQCat.pulse.AcquireSine.id>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`parameters <pyQCat.pulse.AcquireSine.parameters>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`pulse <pyQCat.pulse.AcquireSine.pulse>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`raw_pulse <pyQCat.pulse.AcquireSine.raw_pulse>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`sweep <pyQCat.pulse.AcquireSine.sweep>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`type <pyQCat.pulse.AcquireSine.type>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`width <pyQCat.pulse.AcquireSine.width>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.pulse.base_pulse.PulseComponent`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.pulse.AcquireSine.__init__>`\\ "
#~ "\\(time\\, amp\\_list\\, IF\\_list\\[\\, name\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_ac_crosstalk "
#~ "<pyQCat.pulse.AcquireSine.correct_ac_crosstalk>`\\ "
#~ "\\(after\\_ac\\_corsstalk\\_pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_compensate "
#~ "<pyQCat.pulse.AcquireSine.correct_compensate>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_delay <pyQCat.pulse.AcquireSine.correct_delay>`\\"
#~ " \\(delay\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`correct_distortion "
#~ "<pyQCat.pulse.AcquireSine.correct_distortion>`\\ "
#~ "\\(\\[plot\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`correct_pulse <pyQCat.pulse.AcquireSine.correct_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`envelop2sequence "
#~ "<pyQCat.pulse.AcquireSine.envelop2sequence>`\\ \\(envelop\\[\\,"
#~ " IF\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`get_pulse <pyQCat.pulse.AcquireSine.get_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`get_raw_pulse <pyQCat.pulse.AcquireSine.get_raw_pulse>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot <pyQCat.pulse.AcquireSine.plot>`\\ "
#~ "\\(file\\_name\\[\\, use\\_points\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`step <pyQCat.pulse.AcquireSine.step>`\\ \\(width\\, amp\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`validate_parameters "
#~ "<pyQCat.pulse.AcquireSine.validate_parameters>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`attach <pyQCat.pulse.AcquireSine.attach>`\\"
#~ msgstr ""

#~ msgid ":obj:`bit <pyQCat.pulse.AcquireSine.bit>`\\"
#~ msgstr ""

#~ msgid ":obj:`delay <pyQCat.pulse.AcquireSine.delay>`\\"
#~ msgstr ""

#~ msgid ":obj:`envelop <pyQCat.pulse.AcquireSine.envelop>`\\"
#~ msgstr ""

#~ msgid ":obj:`id <pyQCat.pulse.AcquireSine.id>`\\"
#~ msgstr ""

#~ msgid ":obj:`parameters <pyQCat.pulse.AcquireSine.parameters>`\\"
#~ msgstr ""

#~ msgid ":obj:`pulse <pyQCat.pulse.AcquireSine.pulse>`\\"
#~ msgstr ""

#~ msgid ":obj:`raw_pulse <pyQCat.pulse.AcquireSine.raw_pulse>`\\"
#~ msgstr ""

#~ msgid ":obj:`sweep <pyQCat.pulse.AcquireSine.sweep>`\\"
#~ msgstr ""

#~ msgid ":obj:`type <pyQCat.pulse.AcquireSine.type>`\\"
#~ msgstr ""

#~ msgid ":obj:`width <pyQCat.pulse.AcquireSine.width>`\\"
#~ msgstr ""

