# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/11/06
# __author:       SSFang

"""
Use rabi width optimize drive power composite experiment.
"""

from copy import deepcopy

import numpy as np

from ....log import pyqlog
from ....structures import Options
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import RabiScanWidth


class RabiWidthComposite(CompositeExperiment):
    """Optimize drive power use rabi width experiment."""

    _sub_experiment_class = RabiScanWidth

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            scope (float): The rabi_width recommend drive_power diff set drive_power.
            max_count (int): The max iteration times.

        """
        options = super()._default_experiment_options()

        options.set_validator("diff", float)
        options.set_validator("max_count", int)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.diff = 0.5  # unit: dB
        options.max_count = 3

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options."""
        options = super()._default_run_options()

        options.set_power_list = []
        options.osc_freq_list = []
        options.rec_power_list = []

        return options

    async def _sync_composite_run(self):
        """Run logic."""
        # super().run()

        diff = self.experiment_options.diff
        max_count = self.experiment_options.max_count
        set_power_list = self.run_options.set_power_list
        osc_freq_list = self.run_options.osc_freq_list
        rec_power_list = self.run_options.rec_power_list

        drive_power = self.child_experiment.experiment_options.drive_power

        count = 0
        run_flag = True
        rabi_width_exp = None
        result_name = None
        power_path = None
        while count < max_count and run_flag is True:
            rabi_width_exp = deepcopy(self.child_experiment)
            description = f"count={count}-drive_power={drive_power}"

            rabi_width_exp.set_parent_file(self, description, count)
            rabi_width_exp.set_experiment_options(drive_power=drive_power)
            self._check_simulator_data(rabi_width_exp, count)
            # rabi_width_exp.run()
            # rabi_width_exp.clear_params()
            await rabi_width_exp.run_experiment()

            if result_name is None:
                result_name = rabi_width_exp.analysis_options.result_name
            if power_path is None:
                power_path = rabi_width_exp.run_options.power_path
            set_power = rabi_width_exp.experiment_options.drive_power
            osc_freq = rabi_width_exp.analysis.results.freq.value
            rec_power = rabi_width_exp.run_options.rec_drive_power

            if isinstance(rec_power, (int, float)):
                set_power_list.append(set_power)
                osc_freq_list.append(osc_freq)
                rec_power_list.append(rec_power)
                if abs(rec_power - set_power) < diff:
                    run_flag = False
                else:
                    drive_power = rec_power
            else:
                run_flag = False
                pyqlog.warning(
                    f"Recommend drive power: {rec_power}, "
                    f"maybe not oscillate, maybe drive frequency not right, "
                    f"please check!"
                )

            self._experiments.append(rabi_width_exp)
            count += 1

        save_file = f"{result_name}_rabi_width_run_options"
        self.file.save_data(
            np.asarray(self.run_options.set_power_list),
            np.asarray(self.run_options.osc_freq_list),
            np.asarray(self.run_options.rec_power_list),
            name=save_file,
        )

        if rabi_width_exp is not None and hasattr(rabi_width_exp, "analysis"):
            self._analysis = rabi_width_exp.analysis
            self._analysis.results.drive_power.extra["path"] = power_path
            self._save_curve_analysis_plot()
