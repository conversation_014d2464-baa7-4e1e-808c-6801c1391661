# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# __date:         2023/09/28
# __author:       xw
# __corporation:  OriginQuantum

from copy import deepcopy

from ....analysis.library import PopulationLossOnceAnalysis
from ....parameters import options_wrapper
from ....pulse import Constant
from ....pulse.pulse_function import half_pi_pulse, pi_pulse
from ....structures import Options
from ....tools.utilities import qarange
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


@options_wrapper
class PopulationLossOnce(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.z_amp_list = qarange(0.30, 0.40, 0.005)
        options.set_validator("z_amp_list", list, limit_null=True)

        options.delay = 30
        options.set_validator("delay", float)
        
        options.freq_range_mode = "normal"
        options.set_validator("freq_range_mode", ["auto", "normal"])

        options.point = 61
        options.set_validator("point", int)

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.data_key = None
        return options

    def _check_options(self):
        super()._check_options()

        if self.discriminator is not None:
            data_type = "I_Q"
            data_key = ["P1"]
        else:
            data_type = "amp_phase"
            data_key = ["Phase"]
        self.set_experiment_options(data_type=data_type)
        self.set_analysis_options(data_key=data_key)
        self.run_options.x_data = self.experiment_options.z_amp_list
        self.run_options.analysis_class = PopulationLossOnceAnalysis

    @staticmethod
    def set_xy_pulses(self):
        # bugfix: pi pulse not call make compiler error
        xy_pulse = pi_pulse(self.qubit)()
        xy_pulse_list = [
            deepcopy(xy_pulse) for _ in range(len(self.experiment_options.z_amp_list))
        ]
        self.play_pulse("XY", self.qubit, xy_pulse_list)

    @staticmethod
    def set_z_pulses(self):
        z_pulse_list = PopulationLossOnce.get_z_pulse(
            self.qubit,
            self.experiment_options.z_amp_list,
            self.experiment_options.delay,
        )
        self.play_pulse("Z", self.qubit, z_pulse_list)
        return z_pulse_list

    @staticmethod
    def get_z_pulse(qubit, z_amp_list: list, delay: float) -> list:
        z_pulse_list = []
        drag_time = pi_pulse(qubit).width

        for z_amp in z_amp_list:
            front_constant = Constant(drag_time, 0)
            center_delay = Constant(delay, z_amp)

            z_pulse = front_constant() + center_delay()
            z_pulse.bit = qubit.bit
            z_pulse.sweep = "sweep delay"

            z_pulse_list.append(z_pulse)

        return z_pulse_list
