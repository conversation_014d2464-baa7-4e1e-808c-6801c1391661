# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.init_qst.rst:2
msgid "pyQCat.analysis.algorithms.init\\_qst"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.init_qst:1
msgid "Initialize quantum state tomography for a set of unitaries."
msgstr "初始化一组量子状态层析的酉矩阵"

#: of pyQCat.analysis.algorithms.tomography.init_qst
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.init_qst:3
msgid ""
"a list of unitary operations that will be applied to the state before "
"measuring the diagonal elements.  These unitaries should form a "
"'complete' set to allow the full density matrix to be determined, though "
"this is not enforced."
msgstr ""
"在测量对角元素之前将应用于状态的酉操作列表。 这些酉应该形成一个“完整的”集合，"
"以允许确定完整的密度矩阵，尽管这不是强制的。"

#: of pyQCat.analysis.algorithms.tomography.init_qst
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.init_qst:8
msgid ""
"Returns a transformation matrix that should be passed to qst along with "
"measurement data to perform the state tomography."
msgstr ""
"返回应与测量数据一起传递给 qst 以执行状态断层扫描的转换矩阵。"

#~ msgid "us - a list of unitary operations that will be applied to the"
#~ msgstr ""

#~ msgid ""
#~ "state before measuring the diagonal "
#~ "elements.  These unitaries should form a"
#~ " 'complete' set to allow the full "
#~ "density matrix to be determined, though"
#~ " this is not enforced."
#~ msgstr ""

