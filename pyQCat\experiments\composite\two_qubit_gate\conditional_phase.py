# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/27
# __author:       <PERSON>, <PERSON><PERSON><PERSON>

"""
Standard ConditionalPhase Experiment.
"""

import json

import numpy as np

from ....analysis import (
    AnalysisResult,
    ConditionalPhaseAdjustAnalysis,
    ConditionalPhaseAnalysis,
)
from ....analysis.specification import ParameterRepr
from ....parameters import freq_list_to_amp, get_physical_bit, options_wrapper
from ....structures import MetaData, Options
from ....tools.calculator import qubit_pair_detune_prepare
from ....tools.utilities import cz_flow_options_adapter, qarange
from ...composite_experiment import CompositeExperiment, ExperimentRunMode
from ...single import CPhaseTMSE, CZAssist


@options_wrapper
class ConditionalPhaseFixed(CompositeExperiment):
    _sub_experiment_class = CZAssist

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("z_amp_list", list)
        options.set_validator("scan_name", str)
        options.set_validator("adapter_name", str)
        options.set_validator("adapter_amp_list", list)
        options.set_validator("leakage_mode", ["fit", "max"])

        options.z_amp_list = None
        options.scan_name = None
        options.adapter_name = "qc"
        options.adapter_amp_list = None
        options.leakage_mode = "fit"
        options.run_mode = ExperimentRunMode.async_mode
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.phase_i_list = []
        options.phase_x_list = []
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)

        options.accumulation_phase = np.pi
        options.swap_fit_args = []
        options.quality_bounds = [0.99, 0.9, 0.85]
        options.result_parameters = [
            ParameterRepr(name="ac_scan", repr="Amp", unit="V"),
            ParameterRepr(name="ac_adapter", repr="Amp", unit="V"),
            ParameterRepr(name="tc", repr="Tc", unit="ns"),
        ]
        options.adapter_amp_list = None
        options.data_key = ["delta_phase"]

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "accumulation_phase": self.analysis_options.accumulation_phase,
        }
        metadata.process_meta = {"scan_name": self.experiment_options.scan_name}
        return metadata

    def _check_options(self):
        super()._check_options()
        cz_flow_options_adapter(self)
        eop = self.experiment_options

        if self.qubit_pair.metadata.std.process.min_leakage_point.fit.detune:
            eop.leakage_mode = "fit"
        elif self.qubit_pair.metadata.std.process.min_leakage_point.max.detune:
            eop.leakage_mode = "max"

        leakage_point = self.qubit_pair.metadata.std.process.min_leakage_point.get(
            eop.leakage_mode
        )

        is_retry = False

        if not eop.z_amp_list and not eop.freq_list:
            if leakage_point.detune:
                self.run_options.x_data = leakage_point.detune
                self.experiment_options.scan_name = None
                freq_map = qubit_pair_detune_prepare(
                    self.qubit_pair, self.qubits, goal_detune=leakage_point.detune
                )
                self.run_options.use_detune = True
                self.run_options.detune_point = leakage_point.detune
                for unit, freq_list in freq_map.items():
                    physical_bit, branch = get_physical_bit(self, unit)
                    amp_list = freq_list_to_amp(freq_list, physical_bit, branch)
                    self.run_options.scan_map.update(
                        {
                            unit: {
                                "freq": freq_list,
                                "amp": amp_list,
                            }
                        }
                    )
            else:
                al = (
                    leakage_point.qh
                    if eop.scan_name.startswith("q")
                    else leakage_point.qc
                )
                if al[0] > 1:
                    eop.freq_list = al
                else:
                    eop.z_amp_list = al
                is_retry = True
        if not eop.adapter_amp_list and not eop.adapter_freq_list:
            bl = leakage_point.qc
            if bl[0] > 1:
                eop.adapter_freq_list = bl
                is_retry = True
            else:
                eop.adapter_amp_list = bl

        if is_retry:
            self._check_options()
            return

        self.analysis_options.result_parameters[0].repr = eop.scan_name
        self.analysis_options.result_parameters[1].repr = eop.adapter_name
        self.set_analysis_options(adapter_amp_list=eop.adapter_amp_list)

        self._prepare_scan_map()
        self.set_run_options(
            x_data=np.repeat(np.asarray(self.run_options.x_data), 2),
            analysis_class=ConditionalPhaseAnalysis,
        )

    def _prepare_scan_map(self):
        if self.experiment_options.z_amp_list:
            self.run_options.x_data = self.experiment_options.z_amp_list
            self.run_options.scan_map.update(
                {
                    self.experiment_options.scan_name: {
                        "amp": self.experiment_options.adapter_amp_list
                    }
                }
            )

        self.run_options.scan_map.update(
            {self.qubit_pair.qc: {"amp": self.experiment_options.adapter_amp_list}}
        )

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        phase = self.analysis.results.phase
        if phase and phase.value:
            if self.run_options.use_detune:
                scope = {
                    "l": abs(self.run_options.detune_point[0]),
                    "r": abs(self.run_options.detune_point[-1]),
                    "p": 30,
                }
                freq_map = qubit_pair_detune_prepare(
                    self.qubit_pair, self.qubits, goal_detune=phase.value, **scope
                )
                for unit, params in freq_map.items():
                    self.analysis.results[unit] = AnalysisResult(
                        name=unit,
                        value=round(params[0], 3),
                        extra={
                            "name": self.qubit_pair.name,
                            "path": f"QubitPair.metadata.std.cz.params.{unit}.freq",
                        },
                    )

    def run(self):
        super().run()
        gate_params = self.qubit_pair.metadata.std.cz["params"]
        json_str = json.dumps(gate_params, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name="gate_params", prefix=".json")

    def _setup_child_experiment(self, exp: CZAssist, index: int, val: float):
        exp.run_options.index = index
        scan_name = self.experiment_options.scan_name

        total = len(self.run_options.x_data)
        control_gate = "I" if index % 2 == 0 else "X"
        describe = f"{scan_name} {control_gate} amp = {val}v"

        exp.set_parent_file(self, describe, index, total)
        exp.set_experiment_options(add_cz=True)

        for unit, params in self.run_options.scan_map.items():
            exp.qubit_pair.metadata.std.cz["params"][unit]["amp"] = params["amp"][
                index // 2
            ]
            exp.qubit_pair.metadata.std.cz["params"][unit]["freq"] = 0

        exp.set_experiment_options(control_gate=control_gate)
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp: CZAssist):
        phase = exp.analysis.results.phase.value
        exp.analysis.provide_for_parent.update({"phase": phase})

    def _create_composite_experiment_data(self, x_data):
        experiment_data = super()._create_composite_experiment_data(x_data)
        phase = experiment_data.y_data.get("phase")

        length = len(x_data)
        if length % 2 != 0:
            raise ValueError("X data must be an even number, because I/X")

        actual_x_data = []
        phase_i, phase_x = [], []
        for idx in range(length // 2):
            if x_data[idx * 2] != x_data[idx * 2 + 1]:
                raise ValueError("The data of I X is inconsistent")
            actual_x_data.append(x_data[idx * 2])
            phase_i.append(phase[idx * 2])
            phase_x.append(phase[idx * 2 + 1])

        experiment_data._x_data = actual_x_data
        experiment_data.y_data = {"phase_I": phase_i, "phase_X": phase_x}

        return experiment_data


@options_wrapper
class ConditionalPhaseAdjust(CompositeExperiment):
    _sub_experiment_class = ConditionalPhaseFixed

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("z_amp_list", list)
        options.set_validator("scan_name", str)

        options.z_amp_list = qarange(0.30, 0.40, 0.005)
        options.scan_name = None
        options.run_mode = ExperimentRunMode.async_mode

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "x_scan": self.experiment_options.child_exp_options.get("scan_name"),
            "y_scan": self.experiment_options.scan_name,
            "y_data": self._experiments[0].experiment_options.get("z_amp_list"),
        }
        return metadata

    def _check_options(self):
        super()._check_options()
        cz_flow_options_adapter(self)
        if self.experiment_options.child_exp_options.freq_list:
            child_exp_count = (
                len(self.experiment_options.child_exp_options.freq_list) * 2
            )
        else:
            child_exp_count = (
                len(self.experiment_options.child_exp_options.z_amp_list) * 2
            )

        self.set_experiment_options(
            simulator_shape=[
                child_exp_count for _ in range(len(self.experiment_options.z_amp_list))
            ]
        )

        self.set_run_options(
            x_data=np.asarray(self.experiment_options.z_amp_list),
            analysis_class=ConditionalPhaseAdjustAnalysis,
        )

    def _setup_child_experiment(
        self, exp: ConditionalPhaseFixed, index: int, vol: float
    ):
        exp.run_options.index = index
        scan_name = self.experiment_options.scan_name

        total = len(self.run_options.x_data)
        describe = f"{scan_name}-z_amp={round(vol, 4)}"
        exp.set_parent_file(self, describe, index, total)
        exp.qubit_pair.metadata.std["gate_params"][scan_name]["amp"] = vol
        self._check_simulator_data(exp, index)


class ConditionalPhaseAdjustNGate(CompositeExperiment):
    _sub_experiment_class = ConditionalPhaseFixed

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("gate_nums", list)
        options.gate_nums = qarange(1, 8, 1)
        options.run_mode = ExperimentRunMode.async_mode

        return options

    def _check_options(self):
        super()._check_options()
        cz_flow_options_adapter(self)
        child_exp_count = len(self.experiment_options.child_exp_options.z_amp_list) * 2

        self.set_experiment_options(
            simulator_shape=[
                child_exp_count for _ in range(len(self.experiment_options.gate_nums))
            ]
        )
        self.set_run_options(
            x_data=np.asarray(self.experiment_options.gate_nums),
            analysis_class=ConditionalPhaseAdjustAnalysis,
        )

    def _setup_child_experiment(self, exp: ConditionalPhaseFixed, index: int, num: int):
        exp.run_options.index = index
        total = len(self.run_options.x_data)
        describe = f"cz num = {num}"
        exp.set_parent_file(self, describe, index, total)
        exp.set_child_exp_options(cz_num=int(num))
        self._check_simulator_data(exp, index)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "x_scan": self.experiment_options.child_exp_options.get("scan_name"),
            "y_scan": "",
            "y_data": self._experiments[0].experiment_options.get("z_amp_list"),
            "child_scan_freq": self._experiments[0].experiment_options.get("freq_list"),
        }
        return metadata


@options_wrapper
class ConditionalPhaseTMSE(CompositeExperiment):
    _sub_experiment_class = CPhaseTMSE

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("z_amp_list", list)
        options.set_validator("scan_name", str, limit_null=True)

        options.z_amp_list = qarange(0.30, 0.40, 0.005)
        options.scan_name = None
        options.run_mode = ExperimentRunMode.async_mode

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "x_scan": self.experiment_options.child_exp_options.get("scan_name"),
            "y_scan": self.experiment_options.scan_name,
            "y_data": self._experiments[0].experiment_options.get("z_amp_list"),
        }
        return metadata

    def _check_options(self):
        super()._check_options()
        cz_flow_options_adapter(self)
        self.set_run_options(
            x_data=np.asarray(self.experiment_options.z_amp_list),
            analysis_class=ConditionalPhaseAdjustAnalysis,
        )

    def _setup_child_experiment(self, exp: CPhaseTMSE, index: int, val: float):
        exp.run_options.index = index
        scan_name = self.experiment_options.scan_name

        total = len(self.run_options.x_data)
        describe = f"{scan_name}-z_amp={round(val, 4)}"

        exp.set_parent_file(self, describe, index, total)
        exp.qubit_pair.metadata.std.cz["params"][scan_name]["amp"] = val
        self._check_simulator_data(exp, index)

    def run(self):
        super().run()
        gate_params = self.qubit_pair.metadata.std.cz["params"]
        json_str = json.dumps(gate_params, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name="gate_params", prefix=".json")


class ConditionalPhaseTMSENGate(CompositeExperiment):
    _sub_experiment_class = CPhaseTMSE

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("gate_nums", list)
        options.gate_nums = qarange(1, 8, 1)
        options.run_mode = ExperimentRunMode.async_mode

        return options

    def _setup_child_experiment(self, exp: CPhaseTMSE, index: int, num: int):
        exp.run_options.index = index
        total = len(self.run_options.x_data)
        describe = f"cz num = {num}"
        exp.set_parent_file(self, describe, index, total)
        exp.set_experiment_options(cz_num=int(num))
        self._check_simulator_data(exp, index)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "x_scan": self.experiment_options.child_exp_options.get("scan_name"),
            "y_scan": "",
            "y_data": self._experiments[0].experiment_options.get("z_amp_list"),
            "child_scan_freq": self._experiments[0].experiment_options.get("freq_list"),
        }
        return metadata

    def _check_options(self):
        super()._check_options()
        cz_flow_options_adapter(self)
        self.set_run_options(
            x_data=np.asarray(self.experiment_options.gate_nums),
            analysis_class=ConditionalPhaseAdjustAnalysis,
        )
