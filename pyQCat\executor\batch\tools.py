# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/25
# __author:       <PERSON><PERSON><PERSON>

import json
import os
import re
from collections import defaultdict
from pathlib import Path
from typing import List, Union

from ...log import pyqlog
from ...processor.chip_data import ChipData
from ...qubit import NAME_PATTERN, Qubit
from ...tools.allocation import (
    IntermediateFreqAllocation,
    ParallelAllocationCGC,
    ParallelAllocationQC,
    ParallelAllocationCC
)


def generate_qubit_name_from_num(
    num_qubits: Union[Union[int, str], List[Union[int, str]]],
):
    if isinstance(num_qubits, int):
        return f"q{num_qubits}"

    if isinstance(num_qubits, str):
        return num_qubits

    return [f"q{num}" if isinstance(num, int) else num for num in num_qubits]


def auto_build_parallel_qubit(batch_exp):
    parallel_unit = batch_exp.experiment_options.parallel_unit
    if parallel_unit:
        parallel_unit = generate_qubit_name_from_num(parallel_unit)
    else:
        parallel_unit = []
        env_bits = batch_exp.context_manager.global_options.env_bits
        for bit in env_bits:
            if re.match(NAME_PATTERN.qubit, bit):
                parallel_unit.append(bit)
    batch_exp.experiment_options.parallel_unit = parallel_unit


def divide_qubit_parallel_group(
    parallel_units: List[str], chip_data: ChipData, **kwargs
):
    """Divide parallel group for qubit"""
    aller = ParallelAllocationQC(chip_data.to_middle_chip_data())
    aller.set_allocation_options(**kwargs)
    aller.allocate(parallel_units)
    return list(aller.run_options.parallel_name_map.values())


def divide_same_lo_baseband_freq(
    parallel_qubits: List[Union[str, Qubit]],
    chip_data: ChipData,
    style: str = "xy",
    **kwarg,
):
    """A set of parallel qubits may contain different lo configurations"""
    aller = IntermediateFreqAllocation(chip_data.to_middle_chip_data())
    aller.set_allocation_options(mode=style, **kwarg)

    q_names = []
    for unit in parallel_qubits:
        if isinstance(unit, Qubit):
            q_names.append(unit.name)
        elif isinstance(unit, str):
            q_names.append(unit)
    aller.allocate(q_names)


def divide_cz_parallel_group(parallel_units: List[str], chip_data: ChipData, **kwargs):
    """Divide parallel group for qubit pair"""
    aller = ParallelAllocationCGC(chip_data.to_middle_chip_data())
    kwargs.pop("mode", None)
    aller.set_allocation_options(**kwargs)
    aller.allocate(parallel_units)
    return aller.run_options.parallel_name_map


def divide_coupler_calibration_parallel_group(
    parallel_units: List[str], chip_data: ChipData, **kwargs
) -> List[List[str]]:
    init_coupler_groups = defaultdict(list)
    qubit_temp_groups = defaultdict(list)
    max_group = 0

    # check probe/drive bit
    for unit in parallel_units:
        qc = chip_data.get_physical_unit(unit)
        drive_bit = int(qc.drive_bit)
        probe_bit = int(qc.probe_bit)
        qp_name = f"q{qc.probe_bit}"

        find_group = False
        for group_idx in range(max_group):
            cur_qubit_group = qubit_temp_groups.get(f"G-{group_idx}")
            if drive_bit not in cur_qubit_group and probe_bit not in cur_qubit_group:
                cur_qubit_group.extend([probe_bit, drive_bit])
                init_coupler_groups[f"G-{group_idx}"].append((qp_name, unit))
                find_group = True
                break

        if find_group is False:
            qubit_temp_groups[f"G-{max_group}"].extend([probe_bit, drive_bit])
            init_coupler_groups[f"G-{max_group}"].append((qp_name, unit))
            max_group += 1

    # base qubit parallel divide
    parallel_coupler_groups = []
    for group_name, group_items in init_coupler_groups.items():
        qp_list = [qp_qc_tuple[0] for qp_qc_tuple in group_items]
        cur_qubit_groups = divide_qubit_parallel_group(qp_list, chip_data, **kwargs)
        for cur_group in cur_qubit_groups:
            cur_coupler_names = []
            for qubit_name in cur_group:
                for item in group_items:
                    if item[0] == qubit_name:
                        cur_coupler_names.append(item[1])
            parallel_coupler_groups.append(cur_coupler_names)

    for i, group in enumerate(parallel_coupler_groups):
        pyqlog.info(f"Parallel Coupler Divide Group: Region-{i} | {group}")

    return parallel_coupler_groups


def check_coupler_drive_probe_bit(parallel_units: List[str], chip_data: ChipData):
    for unit in parallel_units:
        if re.match(NAME_PATTERN.coupler, unit):
            qc = chip_data.get_physical_unit(unit)
            drive_bit = int(qc.drive_bit)
            probe_bit = int(qc.probe_bit)
            dq: Qubit = chip_data.get_physical_unit(f"q{drive_bit}")
            pq: Qubit = chip_data.get_physical_unit(f"q{probe_bit}")
            if dq and dq.goodness and dq.drive_freq > pq.drive_freq:
                qc.probe_bit = drive_bit
                qc.drive_bit = probe_bit
            else:
                qc.probe_bit = probe_bit
                qc.drive_bit = drive_bit


def update_physical_unit(_backend, f_path: str):
    bit_name = Path(f_path).name.split("_")[0]
    with open(f_path, "r") as fp:
        data = json.load(fp)
        data.pop("bit", None)
        data.pop("qid", None)
        qubit_obj = _backend.context_manager.chip_data.get_physical_unit(bit_name)
        qubit_obj.update(qubit_obj, data)
        qubit_obj.save_data()
        print(f"Update {bit_name} from {f_path}!")


def import_idle_point(_backend, idle_point_path: str):
    def search_json_files(folder):
        for root, dirs, files in os.walk(folder):
            for file in files:
                if file.endswith(".json") and file.startswith("q"):
                    yield file

    for f in search_json_files(idle_point_path):
        update_physical_unit(_backend, str(Path(idle_point_path, f)))


def import_readout_point(_backend, readout_point_path: str):
    best_amp_path = Path(readout_point_path, "best_amp.json")

    if best_amp_path.exists():
        with open(str(best_amp_path), "r") as fp:
            best_amp = json.load(fp)

        for unit, params in best_amp.items():
            text = f"{unit}_{params.get('amp')}.json"
            qubit_data_path = Path(readout_point_path, text)
            if qubit_data_path.exists():
                update_physical_unit(_backend, str(qubit_data_path))


def divide_coupler_parallel_group(parallel_units: List[str], chip_data: ChipData, **kwargs):
    aller = ParallelAllocationCC(chip_data.to_middle_chip_data())
    aller.set_allocation_options(**kwargs)
    aller.allocate(parallel_units)
    return aller.run_options.parallel_name_map
