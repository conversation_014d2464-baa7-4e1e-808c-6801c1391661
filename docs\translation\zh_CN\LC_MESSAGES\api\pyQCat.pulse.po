# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.pulse.rst:2
msgid "pyQCat.pulse package"
msgstr ""

#: ../../source/api/pyQCat.pulse.rst:6
msgid "Module contents"
msgstr ""

#: of pyQCat.pulse:3
msgid "Pulse Library (:mod:`pyQCat.pulse`)"
msgstr ""

#: of pyQCat.pulse:5
msgid "Pulses modules."
msgstr ""

#: of pyQCat.pulse:8
msgid "Base Classes"
msgstr ""

#: of pyQCat.pulse:16:<autosummary>:1
msgid ""
":py:obj:`PulseComponent <pyQCat.pulse.PulseComponent>`\\ \\(time\\, "
"type\\_\\)"
msgstr ""

#: of pyQCat.pulse:16:<autosummary>:1
msgid "The abstract superclass for pulses."
msgstr ""

#: of pyQCat.pulse:16:<autosummary>:1
msgid ""
":py:obj:`PulseCorrection <pyQCat.pulse.PulseCorrection>`\\ \\(name\\[\\, "
"distortion\\_data\\, ...\\]\\)"
msgstr ""

#: of pyQCat.pulse:16:<autosummary>:1
msgid "The PulseCorrection class is used for pulse compensation calibration."
msgstr ""

#: of pyQCat.pulse:16:<autosummary>:1
msgid ""
":py:obj:`Schedule <pyQCat.pulse.Schedule>`\\ \\(name\\, save\\_file\\, "
"fixed\\_delay\\, ...\\)"
msgstr ""

#: of pyQCat.pulse:16:<autosummary>:1
msgid "Pulse schedule plotter."
msgstr ""

#: of pyQCat.pulse:18
msgid "Pulse Lirbrary"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid ""
":py:obj:`GaussianSquare <pyQCat.pulse.GaussianSquare>`\\ \\(time\\, "
"amp\\[\\, sigma\\, fast\\_m\\, name\\]\\)"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid ""
":py:obj:`Drag <pyQCat.pulse.Drag>`\\ \\(time\\, offset\\, amp\\, "
"detune\\, freq\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid ""
":py:obj:`FlatTopGaussian <pyQCat.pulse.FlatTopGaussian>`\\ \\(time\\[\\, "
"amp\\, sigma\\, buffer\\, ...\\]\\)"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid ""
":py:obj:`AcquireSine <pyQCat.pulse.AcquireSine>`\\ \\(time\\, "
"amp\\_list\\, IF\\_list\\[\\, name\\]\\)"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid "Acquisition Waveform Generator."
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid ""
":py:obj:`Constant <pyQCat.pulse.Constant>`\\ \\(time\\, amp\\[\\, "
"name\\]\\)"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid "A simple constant pulse, with an amplitude value and a duration:"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid ""
":py:obj:`SquareEnvelop <pyQCat.pulse.SquareEnvelop>`\\ \\(time\\, "
"offset\\, amp\\, detune\\, freq\\)"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid ""
":py:obj:`VarFreqEnvelop <pyQCat.pulse.VarFreqEnvelop>`\\ \\(time\\, "
"offset\\, amp\\, detune\\, freq\\)"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid ":py:obj:`pi_pulse <pyQCat.pulse.pi_pulse>`\\ \\(qubit\\)"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>
msgid "rtype"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid ":py:class:`~pyQCat.pulse.pulse_lib.Drag`"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid ":py:obj:`half_pi_pulse <pyQCat.pulse.half_pi_pulse>`\\ \\(qubit\\)"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid ":py:obj:`zero_pulse <pyQCat.pulse.zero_pulse>`\\ \\(qubit\\[\\, name\\]\\)"
msgstr ""

#: of pyQCat.pulse:33:<autosummary>:1
msgid ":py:class:`~pyQCat.pulse.pulse_lib.Constant`"
msgstr ""

#: of pyQCat.pulse:35
msgid "Pulse Generator"
msgstr ""

#: of pyQCat.pulse:42:<autosummary>:1
msgid ""
":py:obj:`generate <pyQCat.pulse.generate>`\\ \\(wave\\_struct\\, "
"wave\\_list\\)"
msgstr ""

#: of pyQCat.pulse:42:<autosummary>:1
msgid ""
":py:obj:`WaveStruct <pyQCat.pulse.WaveStruct>`\\ \\(\\[device\\_code\\, "
"channel\\, ...\\]\\)"
msgstr ""

#: of pyQCat.pulse:42:<autosummary>:1
msgid ""
":py:obj:`WaveGenerator <pyQCat.pulse.WaveGenerator>`\\ "
"\\(\\[sample\\_rate\\]\\)"
msgstr ""

#~ msgid ""
#~ ":py:obj:`PulseComponent <pyQCat.pulse.PulseComponent>`\\ "
#~ "\\(time\\, type\\_\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`PulseCorrection <pyQCat.pulse.PulseCorrection>`\\ "
#~ "\\(name\\, hardware\\_dir\\[\\, username\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`Schedule <pyQCat.pulse.Schedule>`\\ \\(name\\, save\\_file\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`GaussianSquare <pyQCat.pulse.GaussianSquare>`\\ "
#~ "\\(time\\, amp\\[\\, sigma\\, fast\\_m\\, "
#~ "name\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`Drag <pyQCat.pulse.Drag>`\\ \\(time\\, "
#~ "offset\\, amp\\, detune\\, freq\\[\\, "
#~ "...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`FlatTopGaussian <pyQCat.pulse.FlatTopGaussian>`\\ "
#~ "\\(time\\, amp\\[\\, sigma\\, buffer\\, "
#~ "...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`AcquireSine <pyQCat.pulse.AcquireSine>`\\ "
#~ "\\(time\\, amp\\_list\\, IF\\_list\\[\\, name\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`Constant <pyQCat.pulse.Constant>`\\ \\(time\\,"
#~ " amp\\[\\, name\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`SquareEnvelop <pyQCat.pulse.SquareEnvelop>`\\ "
#~ "\\(time\\, offset\\, amp\\, detune\\, freq\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`VarFreqEnvelop <pyQCat.pulse.VarFreqEnvelop>`\\ "
#~ "\\(time\\, offset\\, amp\\, detune\\, freq\\)"
#~ msgstr ""

#~ msgid ":py:obj:`pi_pulse <pyQCat.pulse.pi_pulse>`\\ \\(qubit\\)"
#~ msgstr ""

#~ msgid ":py:obj:`half_pi_pulse <pyQCat.pulse.half_pi_pulse>`\\ \\(qubit\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`zero_pulse <pyQCat.pulse.zero_pulse>`\\ "
#~ "\\(qubit\\[\\, name\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`generate <pyQCat.pulse.generate>`\\ "
#~ "\\(wave\\_struct\\, wave\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`WaveStruct <pyQCat.pulse.WaveStruct>`\\ "
#~ "\\(\\[device\\_code\\, channel\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`WaveGenerator <pyQCat.pulse.WaveGenerator>`\\ "
#~ "\\(\\[sample\\_rate\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`PulseCorrection <pyQCat.pulse.PulseCorrection>`\\ "
#~ "\\(name\\, hardware\\_dir\\[\\, username\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`PulseComponent <pyQCat.pulse.PulseComponent>`\\ "
#~ "\\(time\\, type\\_\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`PulseCorrection <pyQCat.pulse.PulseCorrection>`\\ "
#~ "\\(name\\[\\, distortion\\_data\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`Schedule <pyQCat.pulse.Schedule>`\\ \\(name\\, save\\_file\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`GaussianSquare <pyQCat.pulse.GaussianSquare>`\\ "
#~ "\\(time\\, amp\\[\\, sigma\\, fast\\_m\\, "
#~ "name\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`Drag <pyQCat.pulse.Drag>`\\ \\(time\\, "
#~ "offset\\, amp\\, detune\\, freq\\[\\, "
#~ "...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`FlatTopGaussian <pyQCat.pulse.FlatTopGaussian>`\\ "
#~ "\\(time\\, amp\\[\\, sigma\\, buffer\\, "
#~ "...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`AcquireSine <pyQCat.pulse.AcquireSine>`\\ \\(time\\,"
#~ " amp\\_list\\, IF\\_list\\[\\, name\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`Constant <pyQCat.pulse.Constant>`\\ \\(time\\, "
#~ "amp\\[\\, name\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`SquareEnvelop <pyQCat.pulse.SquareEnvelop>`\\ "
#~ "\\(time\\, offset\\, amp\\, detune\\, freq\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`VarFreqEnvelop <pyQCat.pulse.VarFreqEnvelop>`\\ "
#~ "\\(time\\, offset\\, amp\\, detune\\, freq\\)"
#~ msgstr ""

#~ msgid ":obj:`pi_pulse <pyQCat.pulse.pi_pulse>`\\ \\(qubit\\)"
#~ msgstr ""

#~ msgid ":obj:`half_pi_pulse <pyQCat.pulse.half_pi_pulse>`\\ \\(qubit\\)"
#~ msgstr ""

#~ msgid ":obj:`zero_pulse <pyQCat.pulse.zero_pulse>`\\ \\(qubit\\[\\, name\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`generate <pyQCat.pulse.generate>`\\ "
#~ "\\(wave\\_struct\\, wave\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`WaveStruct <pyQCat.pulse.WaveStruct>`\\ "
#~ "\\(\\[device\\_code\\, channel\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`WaveGenerator <pyQCat.pulse.WaveGenerator>`\\ "
#~ "\\(\\[sample\\_rate\\]\\)"
#~ msgstr ""

