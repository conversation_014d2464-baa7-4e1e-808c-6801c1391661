﻿pyQCat.qubit.Coupler
====================

.. currentmodule:: pyQCat.qubit

.. autoclass:: Coupler

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~Coupler.__init__
      ~Coupler.ac_instead_dc
      ~Coupler.anno
      ~Coupler.from_dict
      ~Coupler.from_file
      ~Coupler.is_adjacent
      ~Coupler.put_sweet_point
      ~Coupler.reset
      ~Coupler.save_database
      ~Coupler.set_coords
      ~Coupler.to_dict
      ~Coupler.to_file
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~Coupler.col
      ~Coupler.composite_attrs
      ~Coupler.options_attrs
      ~Coupler.row
      ~Coupler.unit_map
   
   