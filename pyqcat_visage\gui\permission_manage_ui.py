# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'permission_manage_ui.ui'
##
## Created by: Qt User Interface Compiler version 6.8.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (Q<PERSON><PERSON>, QColor, QC<PERSON>al<PERSON>radient, QCursor,
    QF<PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QFrame, QGridLayout,
    QHBoxLayout, QHeaderView, QLabel, QLineEdit,
    QMainWindow, QPushButton, QRadioButton, QSizePolicy,
    QSpacerItem, QSplitter, QTabWidget, QTableWidget,
    QTableWidgetItem, QTreeWidget, QTreeWidgetItem, QVBoxLayout,
    QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1000, 700)
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(MainWindow.sizePolicy().hasHeightForWidth())
        MainWindow.setSizePolicy(sizePolicy)
        MainWindow.setMinimumSize(QSize(800, 500))
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        sizePolicy.setHeightForWidth(self.centralwidget.sizePolicy().hasHeightForWidth())
        self.centralwidget.setSizePolicy(sizePolicy)
        self.centralwidget.setMinimumSize(QSize(800, 500))
        self.horizontalLayout = QHBoxLayout(self.centralwidget)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.main_widget = QWidget(self.centralwidget)
        self.main_widget.setObjectName(u"main_widget")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.main_widget.sizePolicy().hasHeightForWidth())
        self.main_widget.setSizePolicy(sizePolicy1)
        self.main_widget.setMinimumSize(QSize(600, 459))
        self.horizontalLayout_3 = QHBoxLayout(self.main_widget)
        self.horizontalLayout_3.setSpacing(0)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.tabWidget = QTabWidget(self.main_widget)
        self.tabWidget.setObjectName(u"tabWidget")
        sizePolicy.setHeightForWidth(self.tabWidget.sizePolicy().hasHeightForWidth())
        self.tabWidget.setSizePolicy(sizePolicy)
        self.tabWidget.setMinimumSize(QSize(600, 459))
        self.tab_platform = QWidget()
        self.tab_platform.setObjectName(u"tab_platform")
        self.tab_platform.setMinimumSize(QSize(600, 459))
        self.horizontalLayout_5 = QHBoxLayout(self.tab_platform)
        self.horizontalLayout_5.setSpacing(0)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.widget = QWidget(self.tab_platform)
        self.widget.setObjectName(u"widget")
        self.widget.setMinimumSize(QSize(600, 459))
        self.horizontalLayout_6 = QHBoxLayout(self.widget)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.p_main_splitter = QSplitter(self.widget)
        self.p_main_splitter.setObjectName(u"p_main_splitter")
        sizePolicy.setHeightForWidth(self.p_main_splitter.sizePolicy().hasHeightForWidth())
        self.p_main_splitter.setSizePolicy(sizePolicy)
        self.p_main_splitter.setMinimumSize(QSize(600, 459))
        self.p_main_splitter.setFrameShadow(QFrame.Shadow.Plain)
        self.p_main_splitter.setMidLineWidth(0)
        self.p_main_splitter.setOrientation(Qt.Orientation.Horizontal)
        self.p_left = QWidget(self.p_main_splitter)
        self.p_left.setObjectName(u"p_left")
        sizePolicy.setHeightForWidth(self.p_left.sizePolicy().hasHeightForWidth())
        self.p_left.setSizePolicy(sizePolicy)
        self.p_left.setMinimumSize(QSize(400, 459))
        self.verticalLayout_2 = QVBoxLayout(self.p_left)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.p_splitter = QSplitter(self.p_left)
        self.p_splitter.setObjectName(u"p_splitter")
        sizePolicy.setHeightForWidth(self.p_splitter.sizePolicy().hasHeightForWidth())
        self.p_splitter.setSizePolicy(sizePolicy)
        self.p_splitter.setMinimumSize(QSize(400, 459))
        self.p_splitter.setOrientation(Qt.Orientation.Vertical)
        self.p_condion_widget = QWidget(self.p_splitter)
        self.p_condion_widget.setObjectName(u"p_condion_widget")
        self.p_condion_widget.setMinimumSize(QSize(400, 100))
        self.p_condion_widget.setMaximumSize(QSize(16777215, 200))
        self.horizontalLayout_4 = QHBoxLayout(self.p_condion_widget)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.p_condition_context = QWidget(self.p_condion_widget)
        self.p_condition_context.setObjectName(u"p_condition_context")
        self.gridLayout = QGridLayout(self.p_condition_context)
        self.gridLayout.setObjectName(u"gridLayout")
        self.p_radio_group = QRadioButton(self.p_condition_context)
        self.p_radio_group.setObjectName(u"p_radio_group")

        self.gridLayout.addWidget(self.p_radio_group, 0, 0, 1, 1)

        self.p_plat_comb = QComboBox(self.p_condition_context)
        self.p_plat_comb.setObjectName(u"p_plat_comb")
        self.p_plat_comb.setMaximumSize(QSize(16777215, 16777215))

        self.gridLayout.addWidget(self.p_plat_comb, 3, 1, 1, 1)

        self.p_perm_lab = QLabel(self.p_condition_context)
        self.p_perm_lab.setObjectName(u"p_perm_lab")

        self.gridLayout.addWidget(self.p_perm_lab, 5, 0, 1, 1)

        self.p_perm_comb = QComboBox(self.p_condition_context)
        self.p_perm_comb.setObjectName(u"p_perm_comb")

        self.gridLayout.addWidget(self.p_perm_comb, 5, 1, 1, 1)

        self.p_user_comb = QComboBox(self.p_condition_context)
        self.p_user_comb.setObjectName(u"p_user_comb")

        self.gridLayout.addWidget(self.p_user_comb, 4, 1, 1, 1)

        self.p_user_lab = QLabel(self.p_condition_context)
        self.p_user_lab.setObjectName(u"p_user_lab")

        self.gridLayout.addWidget(self.p_user_lab, 4, 0, 1, 1)

        self.p_plat_lab = QLabel(self.p_condition_context)
        self.p_plat_lab.setObjectName(u"p_plat_lab")

        self.gridLayout.addWidget(self.p_plat_lab, 3, 0, 1, 1)

        self.p_radio_user = QRadioButton(self.p_condition_context)
        self.p_radio_user.setObjectName(u"p_radio_user")

        self.gridLayout.addWidget(self.p_radio_user, 0, 1, 1, 1)

        self.gridLayout.setColumnStretch(0, 1)
        self.gridLayout.setColumnStretch(1, 5)

        self.horizontalLayout_4.addWidget(self.p_condition_context)

        self.p_query_widget = QWidget(self.p_condion_widget)
        self.p_query_widget.setObjectName(u"p_query_widget")
        self.verticalLayout_4 = QVBoxLayout(self.p_query_widget)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_4.addItem(self.verticalSpacer)

        self.p_query_button = QPushButton(self.p_query_widget)
        self.p_query_button.setObjectName(u"p_query_button")
        sizePolicy.setHeightForWidth(self.p_query_button.sizePolicy().hasHeightForWidth())
        self.p_query_button.setSizePolicy(sizePolicy)
        self.p_query_button.setMinimumSize(QSize(100, 30))
        self.p_query_button.setMaximumSize(QSize(16777215, 30))

        self.verticalLayout_4.addWidget(self.p_query_button)

        self.verticalSpacer_2 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_4.addItem(self.verticalSpacer_2)


        self.horizontalLayout_4.addWidget(self.p_query_widget)

        self.horizontalLayout_4.setStretch(0, 4)
        self.horizontalLayout_4.setStretch(1, 1)
        self.p_splitter.addWidget(self.p_condion_widget)
        self.p_table_widget = QWidget(self.p_splitter)
        self.p_table_widget.setObjectName(u"p_table_widget")
        self.p_table_widget.setMinimumSize(QSize(400, 300))
        self.verticalLayout_3 = QVBoxLayout(self.p_table_widget)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.p_search = QLineEdit(self.p_table_widget)
        self.p_search.setObjectName(u"p_search")

        self.verticalLayout_3.addWidget(self.p_search)

        self.p_tableWidget = QTableWidget(self.p_table_widget)
        self.p_tableWidget.setObjectName(u"p_tableWidget")
        sizePolicy.setHeightForWidth(self.p_tableWidget.sizePolicy().hasHeightForWidth())
        self.p_tableWidget.setSizePolicy(sizePolicy)

        self.verticalLayout_3.addWidget(self.p_tableWidget)

        self.p_splitter.addWidget(self.p_table_widget)

        self.verticalLayout_2.addWidget(self.p_splitter)

        self.p_main_splitter.addWidget(self.p_left)
        self.p_right = QWidget(self.p_main_splitter)
        self.p_right.setObjectName(u"p_right")
        sizePolicy.setHeightForWidth(self.p_right.sizePolicy().hasHeightForWidth())
        self.p_right.setSizePolicy(sizePolicy)
        self.p_right.setMinimumSize(QSize(200, 459))
        self.verticalLayout = QVBoxLayout(self.p_right)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.p_treeWidget = QTreeWidget(self.p_right)
        __qtreewidgetitem = QTreeWidgetItem()
        __qtreewidgetitem.setText(0, u"1");
        self.p_treeWidget.setHeaderItem(__qtreewidgetitem)
        self.p_treeWidget.setObjectName(u"p_treeWidget")
        sizePolicy.setHeightForWidth(self.p_treeWidget.sizePolicy().hasHeightForWidth())
        self.p_treeWidget.setSizePolicy(sizePolicy)
        self.p_treeWidget.setMinimumSize(QSize(200, 400))

        self.verticalLayout.addWidget(self.p_treeWidget)

        self.p_save_widget = QWidget(self.p_right)
        self.p_save_widget.setObjectName(u"p_save_widget")
        self.p_save_widget.setMinimumSize(QSize(200, 0))
        self.horizontalLayout_2 = QHBoxLayout(self.p_save_widget)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.p_save_button = QPushButton(self.p_save_widget)
        self.p_save_button.setObjectName(u"p_save_button")
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
        sizePolicy2.setHorizontalStretch(2)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.p_save_button.sizePolicy().hasHeightForWidth())
        self.p_save_button.setSizePolicy(sizePolicy2)
        self.p_save_button.setMaximumSize(QSize(100, 30))
        self.p_save_button.setContextMenuPolicy(Qt.ContextMenuPolicy.DefaultContextMenu)
        self.p_save_button.setLayoutDirection(Qt.LayoutDirection.LeftToRight)

        self.horizontalLayout_2.addWidget(self.p_save_button)


        self.verticalLayout.addWidget(self.p_save_widget)

        self.verticalLayout.setStretch(0, 8)
        self.verticalLayout.setStretch(1, 1)
        self.p_main_splitter.addWidget(self.p_right)

        self.horizontalLayout_6.addWidget(self.p_main_splitter)


        self.horizontalLayout_5.addWidget(self.widget)

        self.tabWidget.addTab(self.tab_platform, "")
        self.tab_normal = QWidget()
        self.tab_normal.setObjectName(u"tab_normal")
        self.tab_normal.setMinimumSize(QSize(600, 459))
        self.horizontalLayout_7 = QHBoxLayout(self.tab_normal)
        self.horizontalLayout_7.setSpacing(0)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.widget_2 = QWidget(self.tab_normal)
        self.widget_2.setObjectName(u"widget_2")
        self.horizontalLayout_10 = QHBoxLayout(self.widget_2)
        self.horizontalLayout_10.setSpacing(0)
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.n_main_splitter = QSplitter(self.widget_2)
        self.n_main_splitter.setObjectName(u"n_main_splitter")
        sizePolicy.setHeightForWidth(self.n_main_splitter.sizePolicy().hasHeightForWidth())
        self.n_main_splitter.setSizePolicy(sizePolicy)
        self.n_main_splitter.setMinimumSize(QSize(600, 459))
        self.n_main_splitter.setFrameShadow(QFrame.Shadow.Plain)
        self.n_main_splitter.setMidLineWidth(0)
        self.n_main_splitter.setOrientation(Qt.Orientation.Horizontal)
        self.n_left = QWidget(self.n_main_splitter)
        self.n_left.setObjectName(u"n_left")
        sizePolicy.setHeightForWidth(self.n_left.sizePolicy().hasHeightForWidth())
        self.n_left.setSizePolicy(sizePolicy)
        self.n_left.setMinimumSize(QSize(400, 459))
        self.verticalLayout_5 = QVBoxLayout(self.n_left)
        self.verticalLayout_5.setSpacing(0)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.verticalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.n_splitter = QSplitter(self.n_left)
        self.n_splitter.setObjectName(u"n_splitter")
        sizePolicy.setHeightForWidth(self.n_splitter.sizePolicy().hasHeightForWidth())
        self.n_splitter.setSizePolicy(sizePolicy)
        self.n_splitter.setMinimumSize(QSize(400, 459))
        self.n_splitter.setOrientation(Qt.Orientation.Vertical)
        self.n_condion_widget = QWidget(self.n_splitter)
        self.n_condion_widget.setObjectName(u"n_condion_widget")
        self.n_condion_widget.setMinimumSize(QSize(400, 100))
        self.n_condion_widget.setMaximumSize(QSize(16777215, 200))
        self.horizontalLayout_8 = QHBoxLayout(self.n_condion_widget)
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.n_condition_context = QWidget(self.n_condion_widget)
        self.n_condition_context.setObjectName(u"n_condition_context")
        self.gridLayout_2 = QGridLayout(self.n_condition_context)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.n_radio_group = QRadioButton(self.n_condition_context)
        self.n_radio_group.setObjectName(u"n_radio_group")

        self.gridLayout_2.addWidget(self.n_radio_group, 0, 0, 1, 1)

        self.n_radio_user = QRadioButton(self.n_condition_context)
        self.n_radio_user.setObjectName(u"n_radio_user")

        self.gridLayout_2.addWidget(self.n_radio_user, 0, 1, 1, 1)

        self.n_perm_lab = QLabel(self.n_condition_context)
        self.n_perm_lab.setObjectName(u"n_perm_lab")

        self.gridLayout_2.addWidget(self.n_perm_lab, 4, 0, 1, 1)

        self.n_user_comb = QComboBox(self.n_condition_context)
        self.n_user_comb.setObjectName(u"n_user_comb")

        self.gridLayout_2.addWidget(self.n_user_comb, 3, 1, 1, 1)

        self.n_user_lab = QLabel(self.n_condition_context)
        self.n_user_lab.setObjectName(u"n_user_lab")

        self.gridLayout_2.addWidget(self.n_user_lab, 3, 0, 1, 1)

        self.n_perm_comb = QComboBox(self.n_condition_context)
        self.n_perm_comb.setObjectName(u"n_perm_comb")

        self.gridLayout_2.addWidget(self.n_perm_comb, 4, 1, 1, 1)

        self.gridLayout_2.setColumnStretch(0, 1)
        self.gridLayout_2.setColumnStretch(1, 5)

        self.horizontalLayout_8.addWidget(self.n_condition_context)

        self.n_query_widget = QWidget(self.n_condion_widget)
        self.n_query_widget.setObjectName(u"n_query_widget")
        self.verticalLayout_6 = QVBoxLayout(self.n_query_widget)
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.verticalSpacer_3 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_6.addItem(self.verticalSpacer_3)

        self.n_query_button = QPushButton(self.n_query_widget)
        self.n_query_button.setObjectName(u"n_query_button")
        sizePolicy.setHeightForWidth(self.n_query_button.sizePolicy().hasHeightForWidth())
        self.n_query_button.setSizePolicy(sizePolicy)
        self.n_query_button.setMinimumSize(QSize(100, 30))
        self.n_query_button.setMaximumSize(QSize(16777215, 30))

        self.verticalLayout_6.addWidget(self.n_query_button)

        self.verticalSpacer_4 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_6.addItem(self.verticalSpacer_4)


        self.horizontalLayout_8.addWidget(self.n_query_widget)

        self.horizontalLayout_8.setStretch(0, 4)
        self.horizontalLayout_8.setStretch(1, 1)
        self.n_splitter.addWidget(self.n_condion_widget)
        self.n_table_widget = QWidget(self.n_splitter)
        self.n_table_widget.setObjectName(u"n_table_widget")
        self.n_table_widget.setMinimumSize(QSize(400, 300))
        self.verticalLayout_7 = QVBoxLayout(self.n_table_widget)
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.n_search = QLineEdit(self.n_table_widget)
        self.n_search.setObjectName(u"n_search")

        self.verticalLayout_7.addWidget(self.n_search)

        self.n_tableWidget = QTableWidget(self.n_table_widget)
        self.n_tableWidget.setObjectName(u"n_tableWidget")
        sizePolicy.setHeightForWidth(self.n_tableWidget.sizePolicy().hasHeightForWidth())
        self.n_tableWidget.setSizePolicy(sizePolicy)

        self.verticalLayout_7.addWidget(self.n_tableWidget)

        self.n_splitter.addWidget(self.n_table_widget)

        self.verticalLayout_5.addWidget(self.n_splitter)

        self.n_main_splitter.addWidget(self.n_left)
        self.n_right = QWidget(self.n_main_splitter)
        self.n_right.setObjectName(u"n_right")
        sizePolicy.setHeightForWidth(self.n_right.sizePolicy().hasHeightForWidth())
        self.n_right.setSizePolicy(sizePolicy)
        self.n_right.setMinimumSize(QSize(200, 459))
        self.verticalLayout_8 = QVBoxLayout(self.n_right)
        self.verticalLayout_8.setSpacing(0)
        self.verticalLayout_8.setObjectName(u"verticalLayout_8")
        self.verticalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.n_treeWidget = QTreeWidget(self.n_right)
        __qtreewidgetitem1 = QTreeWidgetItem()
        __qtreewidgetitem1.setText(0, u"1");
        self.n_treeWidget.setHeaderItem(__qtreewidgetitem1)
        self.n_treeWidget.setObjectName(u"n_treeWidget")
        sizePolicy.setHeightForWidth(self.n_treeWidget.sizePolicy().hasHeightForWidth())
        self.n_treeWidget.setSizePolicy(sizePolicy)
        self.n_treeWidget.setMinimumSize(QSize(200, 400))

        self.verticalLayout_8.addWidget(self.n_treeWidget)

        self.n_save_widget = QWidget(self.n_right)
        self.n_save_widget.setObjectName(u"n_save_widget")
        self.n_save_widget.setMinimumSize(QSize(200, 0))
        self.horizontalLayout_9 = QHBoxLayout(self.n_save_widget)
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.n_save_button = QPushButton(self.n_save_widget)
        self.n_save_button.setObjectName(u"n_save_button")
        sizePolicy2.setHeightForWidth(self.n_save_button.sizePolicy().hasHeightForWidth())
        self.n_save_button.setSizePolicy(sizePolicy2)
        self.n_save_button.setMaximumSize(QSize(100, 30))
        self.n_save_button.setContextMenuPolicy(Qt.ContextMenuPolicy.DefaultContextMenu)
        self.n_save_button.setLayoutDirection(Qt.LayoutDirection.LeftToRight)

        self.horizontalLayout_9.addWidget(self.n_save_button)


        self.verticalLayout_8.addWidget(self.n_save_widget)

        self.verticalLayout_8.setStretch(0, 8)
        self.verticalLayout_8.setStretch(1, 1)
        self.n_main_splitter.addWidget(self.n_right)

        self.horizontalLayout_10.addWidget(self.n_main_splitter)


        self.horizontalLayout_7.addWidget(self.widget_2)

        self.tabWidget.addTab(self.tab_normal, "")

        self.horizontalLayout_3.addWidget(self.tabWidget)


        self.horizontalLayout.addWidget(self.main_widget)

        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)

        self.tabWidget.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"Permission Manage", None))
#if QT_CONFIG(tooltip)
        self.tabWidget.setToolTip(QCoreApplication.translate("MainWindow", u"Platform Manage", None))
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(whatsthis)
        self.tabWidget.setWhatsThis(QCoreApplication.translate("MainWindow", u"<html><head/><body><p>Platform Manage</p></body></html>", None))
#endif // QT_CONFIG(whatsthis)
        self.p_radio_group.setText(QCoreApplication.translate("MainWindow", u"Group", None))
        self.p_perm_lab.setText(QCoreApplication.translate("MainWindow", u"Permission", None))
        self.p_user_lab.setText(QCoreApplication.translate("MainWindow", u"User", None))
        self.p_plat_lab.setText(QCoreApplication.translate("MainWindow", u"Platform", None))
        self.p_radio_user.setText(QCoreApplication.translate("MainWindow", u"User", None))
        self.p_query_button.setText(QCoreApplication.translate("MainWindow", u"Query", None))
        self.p_save_button.setText(QCoreApplication.translate("MainWindow", u"Save", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_platform), QCoreApplication.translate("MainWindow", u"Platform Permission", None))
        self.n_radio_group.setText(QCoreApplication.translate("MainWindow", u"Group", None))
        self.n_radio_user.setText(QCoreApplication.translate("MainWindow", u"User", None))
        self.n_perm_lab.setText(QCoreApplication.translate("MainWindow", u"Permission", None))
        self.n_user_lab.setText(QCoreApplication.translate("MainWindow", u"User", None))
        self.n_query_button.setText(QCoreApplication.translate("MainWindow", u"Query", None))
        self.n_save_button.setText(QCoreApplication.translate("MainWindow", u"Save", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_normal), QCoreApplication.translate("MainWindow", u"Normal Permission", None))
    # retranslateUi

