# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/05/17
# __author:       <PERSON><PERSON><PERSON> Shi
"""Recovering the frequency of the bits by voltage calibration."""

from ....analysis import OneStepCalibrationAnalysis
from ....structures import Options
from ..single_gate import Ramsey


class VoltageDriftOneStepCalibration(Ramsey):
    """Recovery to the original operating point by voltage calibration rather than
    frequency calibration in the case of small changes in external flux.
    """

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("vol_type", ["bias", "ac"])
        options.set_validator("ac_spectrum_type", str)

        options.vol_type = "bias"
        options.ac_spectrum_type = "standard"

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_analysis_options()
        options.ac_spectrum_type = "standard"
        options.z_new = 0.0
        options.ac_spectrum_params = [0.0, 0.0, 0.0, 0.0, 0.0]
        options.drive_freq = 5600.0
        options.fringe = 10.0

        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        if self.experiment_options.vol_type == "bias":
            z_new = self.qubit.awg_bias
        else:
            z_new = self.qubit.ac

        self.set_analysis_options(
            ac_spectrum_type=self.experiment_options.ac_spectrum_type,
            z_new=z_new,
            ac_spectrum_params=self.qubit.ac_spectrum[
                self.experiment_options.ac_spectrum_type
            ],
            drive_freq=self.qubit.drive_freq,
            fringe=self.experiment_options.fringe,
        )
        self.set_run_options(analysis_class=OneStepCalibrationAnalysis)
