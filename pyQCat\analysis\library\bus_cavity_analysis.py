# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/08/30
# __author:       <PERSON><PERSON><PERSON>

import numpy as np

from pyQCat.analysis.algorithms.find_peak import find_peaks_with_prominence
from pyQCat.analysis.curve_analysis import CurveAnalysis
from pyQCat.analysis.quality import BaseQuality
from pyQCat.analysis.specification import ParameterRepr
from pyQCat.log import pyqlog
from pyQCat.structures import Options
from pyQCat.types import QualityDescribe


class BusCavityAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.data_key = ["Amp"]
        options.x_label = "Cavity Frequency (MHz)"
        options.cavity_count = 6
        options.distance = 20
        options.figsize = (12, 12)
        options.fc_list = None
        options.fit_q = False
        options.raw_data_format = "plot"
        options.result_parameters = [
            ParameterRepr(name="new_fc_list", repr="new_fc_list", unit="MHz"),
            ParameterRepr(name="cavity_count", repr="cavity_count", unit=""),
        ]

        return options

    def _extract_result(self, data_key: str, quality=None):
        analysis_data = self.analysis_datas[data_key]
        x = analysis_data.x
        y = analysis_data.y
        cavity_count = self.options.cavity_count
        distance = self.options.distance
        f_index, _ = find_peaks_with_prominence(
            y, num_peak=cavity_count, distance=distance
        )

        fc_list = [x[i] for i in f_index]
        amp_list = [y[i] for i in f_index]

        mean_amp = np.mean(y)

        index = [i for i, amp in enumerate(amp_list) if amp < mean_amp]
        new_fc_list = [round(fc_list[i], 3) for i in index]

        self.results.cavity_count.value = len(new_fc_list)
        self.results.new_fc_list.value = new_fc_list

        if self.options.cavity_count != len(new_fc_list):
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.perfect)
            if cavity_count == 1:
                self.results.new_fc_list.value = new_fc_list[0]
        if self.options.fit_q:
            # If `out_flag` is `True`, then the data is shown in the title of the figure.
            self.results.q_int.extra.update({"out_flag": False})
            self.results.q_ext.extra.update({"out_flag": False})

        self.options.fc_list = fc_list

        pos_list = []
        for fc in fc_list:
            closest_idx = np.argmin(np.abs(x - fc))
            pos_list.append((x[closest_idx], y[closest_idx]))

        rp_list = [f"[{round(pos[0], 3)}, {round(pos[1], 2)}]" for pos in pos_list]

        self.drawer.set_options(text_pos=pos_list, text_rp=rp_list, text_key=[data_key])

    def run_analysis(self):
        """Bus Cavity run analysis."""

        self.drawer.set_options(marker="")
        super().run_analysis()

    def _description(self, width: int = 70):

        metadata = self.experiment_data.metadata

        if "Sample" in metadata.draw_meta:
            sample_value = str(metadata.draw_meta["Sample"])
            has_chinese = False
            for char in sample_value:
                if "\u4e00" <= char <= "\u9fa5":
                    has_chinese = True
                    break

            if has_chinese:
                del metadata.draw_meta["Sample"]

        return super()._description(width)

    def _visualization(self):

        self.drawer.set_options(title=self._description())

        exp_keys = list(self.experiment_data.y_data.keys())

        for i, key in enumerate(exp_keys):
            if self.options.plot_raw_data:
                x_data = self.experiment_data.x_data
                y_data = self.experiment_data.y_data[key]

                self._draw_segmented_data(x_data, y_data, ax_index=i, data_key=key)

                if (
                    self.drawer.options.text_pos
                    and self.drawer.options.text_rp
                    and key in self.drawer.options.text_key
                ):
                    self.drawer.draw_text(ax_index=i)

        self.drawer.format_canvas()

    def _draw_segmented_data(self, x_data, y_data, ax_index, data_key):

        data_length = len(x_data)
        segment_length = data_length // 6

        for i in range(5):
            start_idx = i * segment_length
            end_idx = (i + 1) * segment_length

            x_segment = x_data[start_idx:end_idx]
            y_segment = y_data[start_idx:end_idx]

            self.drawer.draw_raw_data(
                x_data=x_segment,
                y_data=y_segment,
                ax_index=ax_index,
                label="",
                linewidth=2
            )

        start_idx = 5 * segment_length
        x_segment = x_data[start_idx:]
        y_segment = y_data[start_idx:]

        self.drawer.draw_raw_data(
            x_data=x_segment,
            y_data=y_segment,
            ax_index=ax_index,
            label="",
            linewidth=2
        )
