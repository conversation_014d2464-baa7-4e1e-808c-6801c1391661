# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.GoodnessofFit.rst:2
msgid "pyQCat.analysis.quality.GoodnessofFit"
msgstr ""

#: of pyQCat.analysis.quality.goodness_of_fit.GoodnessofFit:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.GoodnessofFit.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.GoodnessofFit.rst:22:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.quality.GoodnessofFit.__init__>`\\ "
"\\(perfect\\, normal\\, abnormal\\)"
msgstr ""

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.GoodnessofFit.rst:22:<autosummary>:1
msgid ""
":py:obj:`evaluate <pyQCat.analysis.quality.GoodnessofFit.evaluate>`\\ "
"\\(y\\, y\\_fit\\)"
msgstr ""

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.GoodnessofFit.rst:24
msgid "Attributes"
msgstr ""

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.GoodnessofFit.rst:28:<autosummary>:1
msgid ":py:obj:`descriptor <pyQCat.analysis.quality.GoodnessofFit.descriptor>`\\"
msgstr ""

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.GoodnessofFit.rst:28:<autosummary>:1
msgid ":py:obj:`value <pyQCat.analysis.quality.GoodnessofFit.value>`\\"
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.analysis.quality.GoodnessofFit.__init__>`\\ "
#~ "\\(perfect\\, normal\\, abnormal\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`evaluate "
#~ "<pyQCat.analysis.quality.GoodnessofFit.evaluate>`\\ \\(y\\, "
#~ "y\\_fit\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`descriptor "
#~ "<pyQCat.analysis.quality.GoodnessofFit.descriptor>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`value <pyQCat.analysis.quality.GoodnessofFit.value>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.quality.GoodnessofFit.__init__>`\\ "
#~ "\\(perfect\\, normal\\, abnormal\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`evaluate "
#~ "<pyQCat.analysis.quality.GoodnessofFit.evaluate>`\\ \\(y\\, "
#~ "y\\_fit\\)"
#~ msgstr ""

#~ msgid ":obj:`descriptor <pyQCat.analysis.quality.GoodnessofFit.descriptor>`\\"
#~ msgstr ""

#~ msgid ":obj:`value <pyQCat.analysis.quality.GoodnessofFit.value>`\\"
#~ msgstr ""

