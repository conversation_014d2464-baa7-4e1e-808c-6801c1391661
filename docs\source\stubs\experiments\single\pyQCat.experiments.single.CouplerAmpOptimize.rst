﻿pyQCat.experiments.single.CouplerAmpOptimize
============================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: CouplerAmpOptimize

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CouplerAmpOptimize.__init__
      ~CouplerAmpOptimize.acquire_pulse
      ~CouplerAmpOptimize.cal_fidelity
      ~CouplerAmpOptimize.experiment_info
      ~CouplerAmpOptimize.from_experiment_context
      ~CouplerAmpOptimize.get_qubit_str
      ~CouplerAmpOptimize.get_xy_pulse
      ~CouplerAmpOptimize.jupyter_schedule
      ~CouplerAmpOptimize.options_table
      ~CouplerAmpOptimize.play_pulse
      ~CouplerAmpOptimize.plot_schedule
      ~CouplerAmpOptimize.run
      ~CouplerAmpOptimize.set_analysis_options
      ~CouplerAmpOptimize.set_experiment_options
      ~CouplerAmpOptimize.set_multiple_IF
      ~CouplerAmpOptimize.set_multiple_index
      ~CouplerAmpOptimize.set_parent_file
      ~CouplerAmpOptimize.set_run_options
      ~CouplerAmpOptimize.set_sweep_order
      ~CouplerAmpOptimize.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CouplerAmpOptimize.analysis
      ~CouplerAmpOptimize.analysis_options
      ~CouplerAmpOptimize.experiment_options
      ~CouplerAmpOptimize.run_options
   
   