# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/30
# __author:       <PERSON><PERSON><PERSON>

import numpy as np

from ...structures import ExperimentData, MetaData, QDict
from ..algorithms.find_peak import (
    find_peaks,
    judge_peak_dip,
    peak_prominences,
)
from ..algorithms.s21_q_fit import q_fit
from ..quality.goodness_of_fit import GoodnessofFit
from ..specification import ParameterRepr
from ..standard_curve_analysis import Options, StandardCurveAnalysis


class BusS21Analysis(StandardCurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.x_label = "Frequency (MHz)"
        options.y_label = ["Amp", "Amp", "Amp"]
        options.sub_title = [
            "High Power",
            "High Power",
            "Low Power",
        ]
        options.result_parameters = [
            ParameterRepr(name="low_power_cavity", repr="low power cavity", unit="MHz"),
            ParameterRepr(
                name="high_power_cavity", repr="high power cavity", unit="MHz"
            ),
            ParameterRepr(
                name="cavity_shift", repr="low high power cavity shift", unit="MHz"
            ),
            ParameterRepr(name="s21_quality", repr="evaluate", unit=""),
        ]
        options.distance = 10
        options.cavity_count = 6

        return options

    def _initialize_canvas(self):
        # Set Canvas Options
        self.drawer.set_options(
            subplots=(3, 2),
            xlabel=[
                "Freq (MHz)",
                "Freq (MHz)",
                "Freq (MHz)",
                "Freq (MHz)",
                "Peak",
                "Peak",
            ],
            ylabel=["Amp", "Amp", "Amp", "Amp", "Prominence", "Prominence"],
            sub_title=[
                "High Power Large",
                "L/H Power Compare",
                "High Power Small",
                "Low Power Small",
                "High Power Prominence",
                "Low Power Prominence",
            ],
            figsize=(21, 14),
            raw_data_format="plot",
            marker="",
        )

        # CurveDrawer initialize canvas
        self.drawer.initialize_canvas()

    def _extract_result(self):
        cavity_count = self.options.cavity_count
        x = self.experiment_data.replace_x_data.get("hp_small_amp")
        distance = self.options.distance // (x[1] - x[0])
        hd = self.experiment_data.y_data.get("hp_small_amp")
        ld = self.experiment_data.y_data.get("lp_small_amp")
        hf_index, h_prominence, h_limit = find_cavity_peaks(
            hd, num_peak=cavity_count, distance=distance
        )
        lf_index, l_prominence, l_limit = find_cavity_peaks(
            ld, num_peak=cavity_count, distance=distance
        )

        if hf_index is not None and lf_index is not None:
            h_fc_list = [x[i] for i in hf_index]
            h_amp_list = [hd[i] for i in hf_index]
            l_fc_list = [x[i] for i in lf_index]
            l_amp_list = [ld[i] for i in lf_index]

            h_pos = [(fc, amp) for fc, amp in zip(h_fc_list, h_amp_list)]
            l_pos = [(fc, amp) for fc, amp in zip(l_fc_list, l_amp_list)]
            text_pos = {2: h_pos, 3: l_pos}
            text_rp = {2: [str(p[0]) for p in h_pos], 3: [str(p[0]) for p in l_pos]}

            self.drawer.set_options(
                text_pos=text_pos,
                text_rp=text_rp,
                text_key=["hp_small_amp", "lp_small_amp"],
            )

            if len(l_fc_list) == len(h_fc_list):
                chi = [abs(hc - lc) for hc, lc in zip(h_fc_list, l_fc_list)]
                self.results.cavity_shift.value = chi
                if (
                    max(chi) > self.options.distance * 1.5
                    or len(l_fc_list) != cavity_count
                ):
                    self.quality.set_bad()
            else:
                self.quality.set_bad()

            self.results.s21_quality.value = str(self.quality)
            self.results.low_power_cavity.value = l_fc_list
            self.results.high_power_cavity.value = h_fc_list
            self.experiment_data.metadata.process_meta["prominence"] = [
                h_prominence,
                l_prominence,
                h_limit,
                l_limit,
            ]
        else:
            self.quality.set_bad()

    def _visualization(self) -> None:
        """Draw all data, including raw data and fit data and so on.
        Some composite experiments may need to override this function.
        """

        # Set plot title.
        self.drawer.set_options(title=self._description())

        self.drawer.draw_raw_data(
            x_data=self.experiment_data.replace_x_data["hp_wide_amp"],
            y_data=self.experiment_data.y_data["hp_wide_amp"],
            ax_index=0,
            label="",
            linewidth=2,
        )
        self.drawer.draw_raw_data(
            x_data=self.experiment_data.replace_x_data["hp_small_amp"],
            y_data=self.experiment_data.y_data["hp_small_amp"],
            ax_index=1,
            label="HP",
            color="maroon",
            linewidth=2,
        )
        self.drawer.draw_raw_data(
            x_data=self.experiment_data.replace_x_data["lp_small_amp"],
            y_data=self.experiment_data.y_data["lp_small_amp"],
            ax_index=1,
            label="LP",
            color="forestgreen",
            linewidth=2,
        )
        self.drawer.draw_raw_data(
            x_data=self.experiment_data.replace_x_data["hp_small_amp"],
            y_data=self.experiment_data.y_data["hp_small_amp"],
            ax_index=2,
            label="",
            linewidth=2,
        )
        self.drawer.draw_raw_data(
            x_data=self.experiment_data.replace_x_data["lp_small_amp"],
            y_data=self.experiment_data.y_data["lp_small_amp"],
            ax_index=3,
            label="",
            linewidth=2,
        )
        prominence = self.experiment_data.metadata.process_meta.pop("prominence")
        if prominence:
            h_prominence, l_prominence, h_limit, l_limit = prominence
            self.drawer.draw_raw_data(
                x_data=list(range(len(h_prominence))),
                y_data=h_prominence,
                ax_index=4,
                marker="*",
                label="",
            )
            self.drawer.draw_raw_data(
                x_data=list(range(len(l_prominence))),
                y_data=l_prominence,
                ax_index=5,
                marker="*",
                label="",
            )
            self.drawer.draw_axh_line(h_limit, ax_index=4)
            self.drawer.draw_axh_line(l_limit, ax_index=5)
            self.drawer.draw_text(ax_index=2)
            self.drawer.draw_text(ax_index=3)


class CavityQAnalysis(StandardCurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.ATT = -80
        options.net_power = -45
        options.quality_bounds = [0.98, 0.95, 0.85]
        options.result_parameters = [
            ParameterRepr(name="fit_result", repr="q fit result", unit=""),
        ]
        return options

    def _data_processing(self):
        freq = self.experiment_data.x_data
        amp = self.experiment_data.y_data.get("amp")
        phase = self.experiment_data.y_data.get("phase")
        port = q_fit(amp, freq, phase)
        self.experiment_data.metadata.process_meta["port"] = port

    def _extract_result(self):
        decay = self.options.ATT + self.options.net_power
        port = self.experiment_data.metadata.process_meta["port"]
        result = {}
        result.update(port.fit_result)
        result.update(
            dict(
                single_photon_limit=port.get_single_photon_limit(),
                photon=port.get_photons_in_resonator(decay),
                ATT=self.options.ATT,
                net_power=self.options.net_power,
            )
        )
        fmt_result = {}
        for k, v in result.items():
            if isinstance(v, np.float64) and np.isnan(v):
                fmt_result[k] = None
            else:
                fmt_result[k] = v
        self.results.fit_result.value = fmt_result
        if fmt_result["Qi_dia_corr"]:
            self.experiment_data.metadata.draw_meta["q_int"] = round(
                fmt_result["Qi_dia_corr"] / 1e4, 3
            )
        if fmt_result["Qc_dia_corr"]:
            self.experiment_data.metadata.draw_meta["q_ext"] = round(
                fmt_result["Qc_dia_corr"] / 1e4, 3
            )
        if fmt_result["photon"]:
            self.experiment_data.metadata.draw_meta["photon"] = round(
                fmt_result["photon"], 2
            )

    def _initialize_canvas(self):
        # Set Canvas Options
        self.drawer.set_options(
            subplots=(2, 2),
            xlabel=[
                "Re(S21)",
                "Re(S21)",
                "Freq(GHz)",
                "Freq(GHz)",
            ],
            ylabel=["Im(S21)", "Im(S21)", "|S21|", "arg(|S21|)"],
            figsize=(12, 8),
            raw_data_format="plot",
            marker="",
        )

        # CurveDrawer initialize canvas
        self.drawer.initialize_canvas()

    def _evaluate_quality(self):
        port = self.experiment_data.metadata.process_meta["port"]
        self._quality = GoodnessofFit(*self.options.quality_bounds)
        self._quality.evaluate(
            np.absolute(port.z_data), np.absolute(port.z_data_sim_cali)
        )
        self.results.fit_result.extra["out_flag"] = False

    def _visualization(self):
        port = self.experiment_data.metadata.process_meta["port"]

        self.drawer.set_options(title=self._description())
        self.drawer.draw_scatter_point(
            x_data=port.z_data.real,
            y_data=port.z_data.imag,
            label="cali_data",
            ax_index=0,
            color="blue",
        )
        self.drawer.draw_raw_data(
            x_data=port.z_data_sim_cali.real,
            y_data=port.z_data_sim_cali.imag,
            label="fit",
            ax_index=0,
            color="red",
            linewidth=3,
        )
        self.drawer.draw_scatter_point(
            x_data=port.z_data_raw.real,
            y_data=port.z_data_raw.imag,
            label="raw_data",
            ax_index=1,
            color="blue",
        )
        self.drawer.draw_raw_data(
            x_data=port.z_data_sim.real,
            y_data=port.z_data_sim.imag,
            label="fit",
            ax_index=1,
            color="red",
            linewidth=3,
        )
        self.drawer.draw_scatter_point(
            x_data=port.f_data * 1e-9,
            y_data=np.absolute(port.z_data),
            label="absolute",
            ax_index=2,
            color="blue",
        )
        self.drawer.draw_raw_data(
            x_data=port.f_data * 1e-9,
            y_data=np.absolute(port.z_data_sim_cali),
            label="fit",
            ax_index=2,
            color="red",
            linewidth=3,
        )
        self.drawer.draw_scatter_point(
            x_data=port.f_data * 1e-9,
            y_data=np.angle(port.z_data),
            label="angle",
            ax_index=3,
            color="blue",
        )
        self.drawer.draw_raw_data(
            x_data=port.f_data * 1e-9,
            y_data=np.angle(port.z_data_sim_cali),
            label="fit",
            ax_index=3,
            color="red",
            linewidth=3,
        )


class BusQAnalysis(StandardCurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.quality_bounds = [0.98, 0.95, 0.85]
        options.result_parameters = [
            ParameterRepr(name="data", repr="q data", unit=""),
        ]
        return options

    def _data_processing(self):
        acq_data = self._experiment_data.y_data
        exp_options = self._experiment_data.metadata.process_meta.get(
            "experiment_options"
        )
        ATT = exp_options.ATT
        high_power_cavity = exp_options.high_power_cavity
        low_power_cavity = exp_options.low_power_cavity
        cavity_map = {"high_power": high_power_cavity, "low_power": low_power_cavity}
        figures = []
        result_data = {}
        for key, data in acq_data.items():
            net_power = getattr(exp_options, key)
            result_data[key] = {}
            init_cavity = cavity_map[key]
            freq_segments = np.array_split(data["freq"], len(init_cavity))
            amp_segments = np.array_split(data["amp"], len(init_cavity))
            phase_segments = np.array_split(data["phase"], len(init_cavity))
            for idx, cavity in enumerate(cavity_map[key]):
                epd = ExperimentData(
                    experiment_id=f"{key}-{idx}-{cavity}",
                    x_data=freq_segments[idx],
                    y_data=QDict(amp=amp_segments[idx], phase=phase_segments[idx]),
                    metadata=MetaData(
                        name=f"{key} cavity {cavity}",
                        draw_meta=dict(ATT=ATT, net_power=net_power),
                    ),
                )
                analysis = CavityQAnalysis(epd)
                analysis.set_options(
                    ATT=ATT,
                    net_power=net_power,
                    quality_bounds=self.options.quality_bounds,
                )
                analysis.run_analysis()
                figures.append(analysis.drawer.figure)
                result_data[key][cavity] = analysis.results.fit_result.value
                result_data[key][cavity]["quality"] = str(analysis.quality)
                self.options.sub_figure_names.append(epd.metadata.name)
        self.drawer._figure = figures
        self.results.data.value = result_data

    def _initialize_canvas(self): ...

    def _visualization(self): ...

    def _format_canvas(self): ...


def find_cavity_peaks(xdata, num_peak: int, distance: float = None):
    """Adaptive peak-seeking algorithm based on protrusion value.

    .. note::
        This method adds some preprocessing of peak-finding, hoping to make the peak-finding
        operation easier. The details of the algorithm are as follows:

        - First, it will be determined by the data distribution characteristics that the data
          is more suitable for peak or valley search;
        - We use the `scipy.signal.find_peaks` method with all default parameters to find all
          possible peaks, and then calculate the prominence of each peak, denoted as `prominence`;
        - Select the maximum degree of protrusion as the peak-seeking condition for the next peak-seeking;
        - Finally, the index and value corresponding to the peak are returned

    Most of the small peaks can be filtered in this way.

    Args:
        xdata (Union[List, np.ndarray]): Input signals.
        num_peak (int, optional): The number of peaks expected to be found, the default is 1,
            and the peak with the most prominence.
        distance (float, optional): Required minimal horizontal distance (>= 1) in samples
            between neighbouring peaks. Smaller peaks are removed first until the condition
            is fulfilled for all remaining peaks.

    Returns:
        Tuple(List, List): The index and peak value corresponding to the peak.
    """
    try:
        process_data = np.array(xdata)

        # judge peak or dip
        key = judge_peak_dip(process_data)
        if key == "dips":
            process_data = -process_data

        # search excepted peak prominence
        peaks, _ = find_peaks(process_data, distance=distance)
        prominence = peak_prominences(process_data, peaks)
        sort_prominence = sorted(prominence[0], reverse=True)
        expect_prominence = (np.max(process_data) - np.min(process_data)) / 10

        # find new peaks
        new_peaks, _ = find_peaks(
            process_data, prominence=expect_prominence, distance=distance
        )
        if len(new_peaks) > num_peak + 1:
            expect_prominence = sort_prominence[num_peak - 1]
            new_peaks, _ = find_peaks(
                process_data, prominence=expect_prominence, distance=distance
            )

        return new_peaks, sort_prominence, expect_prominence
    except Exception as e:
        print(f"No suitable peak was found, because {e}")
        return None, None, None
