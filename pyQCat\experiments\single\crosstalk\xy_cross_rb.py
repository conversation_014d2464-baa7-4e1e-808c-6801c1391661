# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/08/23
# __author:       <PERSON>


from copy import deepcopy
from typing import List

import numpy as np

from ....analysis.fit.fit_models import rb_exponential_decay_func
from ....analysis.library.rb_analysis import RBAnalysis
from ....analysis.library.xy_cross_rb_analysis import XY<PERSON>rosstalkRBAnalysis
from ....analysis.specification import FitModel
from ....errors import <PERSON><PERSON>ieldError, ExperimentOptionsError, QubitError
from ....log import pyqlog
from ....pulse.base_pulse import PulseComponent
from ....structures import Options
from ....tools.utilities import get_multi_readout_channels
from ....types import StandardContext
from ..error_quantification import RBSingle


class XYCrosstalkRB(RBSingle):
    """XY Crosstalk RB, test bias effect target cross."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()

        options.set_validator("target_name", str)
        options.set_validator("amp_coe", float)
        options.set_validator("phase", float)
        options.set_validator("only_readout_target", bool)

        options.target_name = None
        options.amp_coe = 0.01  # bias effect target, amp coefficient
        options.phase = 0.0  # bias effect target, phase
        options.only_readout_target = True

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set define Run Options."""
        options = super()._default_run_options()

        options.target_qubit = None
        options.bias_qubit = None
        options.xy_cross_params = {}

        options.injection_func.append("cross_xy_pulses")
        options.support_context = [StandardContext.CM]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Define some Analysis Options."""
        options = super()._default_analysis_options()

        options.figsize = (12, 12)
        options.fit_model = FitModel(
            fit_func=rb_exponential_decay_func,
            model_description=r"amp \exp(x) + baseline",
        )

        options.data_key = None
        options.result_parameters = ["depth"]
        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        target_name = self.experiment_options.target_name
        only_readout_target = self.experiment_options.only_readout_target
        depths = self.analysis_options.depths
        fit_model = self.analysis_options.fit_model
        result_parameters = self.analysis_options.result_parameters

        # get target_qubit, bias_qubit
        qubit_map = {qubit.name: qubit for qubit in self.qubits}
        q_names = list(qubit_map.keys())
        if len(self.qubits) < 2:
            raise ExperimentFieldError(
                self,
                f"XY Crosstalk at least two Qubit ! "
                f"But the qubits length {len(self.qubits)} !",
            )
        if target_name not in q_names:
            raise ExperimentOptionsError(
                self,
                f"Set target {target_name} not in all bit names!",
                "target_name",
                target_name,
            )

        target_qubit = qubit_map.get(target_name)
        qubit_map.pop(target_name)
        bias_qubit = list(qubit_map.values())[0]

        # validate target_qubit, bias_qubit X gate width
        t_x_width = round(target_qubit.XYwave.time + 2 * target_qubit.XYwave.offset, 3)
        b_x_width = round(bias_qubit.XYwave.time + 2 * bias_qubit.XYwave.offset, 3)
        if t_x_width != b_x_width:
            raise QubitError(
                f"X gate width not equal, "
                f"target {target_qubit} X: {t_x_width}, "
                f"bias {bias_qubit} X: {b_x_width}"
            )

        if only_readout_target is True:
            multi_readout_channels = [target_qubit.readout_channel]
            measure_qubits = [target_qubit]
            data_key = ["P0", "P1"]
            t_dcm = None
            for dcm in self.discriminator:
                if dcm.name == target_name:
                    t_dcm = dcm
            self.discriminator = t_dcm
            analysis_class = RBAnalysis
        else:
            multi_readout_channels = get_multi_readout_channels(
                [target_qubit, bias_qubit]
            )
            measure_qubits = [target_qubit, bias_qubit]
            measure_namess = [target_name, bias_qubit.name]
            data_key = [
                "t-0(P00+P01)",
                "t-1(P10+P11)",
                "b-0(P00+P10)",
                "b-1(P01+P11)",
            ]

            dcm_list = []
            for name in measure_namess:
                for dcm in self.discriminator:
                    if dcm.name == name:
                        dcm_list.append(dcm)
                        break
            self.discriminator = dcm_list

            if len(depths) >= 5:
                extra = [
                    "t_rc",
                    "t_rg",
                    "t_fidelity",
                    "b_rc",
                    "b_rg",
                    "b_fidelity",
                ]
                result_parameters.extend(extra)
            else:
                fit_model = None
            analysis_class = XYCrosstalkRBAnalysis

        fake_pulse = self.experiment_options.fake_pulse
        if fake_pulse is True:
            pyqlog.warning(f"{self.label} must be set `fake_pulse` False!")
            fake_pulse = False

        # adjust to new version, 2024.05.20
        amp_coe = self.experiment_options.amp_coe
        phase = self.experiment_options.phase
        pre_xy_cross = {
            "target_name": target_name,
            "bias_name": bias_qubit.name,
            "amp_coe_list": [amp_coe],
            "phase_list": [phase],
        }

        self.set_experiment_options(
            fake_pulse=fake_pulse,
            multi_readout_channels=multi_readout_channels,
        )

        self.set_run_options(
            target_qubit=target_qubit,
            bias_qubit=bias_qubit,
            measure_qubits=measure_qubits,
            analysis_class=analysis_class,
            pre_xy_cross=pre_xy_cross,
        )

        self.set_analysis_options(
            fit_model=fit_model,
            data_key=data_key,
            result_parameters=result_parameters,
        )

    @staticmethod
    def set_xy_pulses(self):
        """Set experiment XY pulses."""

        target_qubit = self.run_options.target_qubit
        bias_qubit = self.run_options.bias_qubit
        gate_bucket = self.run_options.gate_bucket

        def adjust_gates(gates: List[str], gate_num: int) -> List[str]:
            """According to gate number to adjust gates."""
            length = len(gates)
            if length < gate_num:
                offset_gates = ["I"] * (gate_num - length)
                gates.extend(offset_gates)
            return gates

        clifford_matrix_set = self._get_clifford_matrix_set()
        t_quantum_circuits = self._get_quantum_circuits(clifford_matrix_set)
        b_quantum_circuits = self._get_quantum_circuits(clifford_matrix_set)

        gate_bucket.bind_single_gates(target_qubit)
        gate_bucket.bind_single_gates(bias_qubit)

        t_pulse_list = []
        b_pulse_list = []
        for t_gate_list, b_gate_list in zip(t_quantum_circuits, b_quantum_circuits):
            max_length = max([len(t_gate_list), len(b_gate_list)])
            new_t_gates = adjust_gates(t_gate_list, max_length)
            new_b_gates = adjust_gates(b_gate_list, max_length)

            t_pulse_once = gate_bucket.get_xy_pulse(target_qubit, new_t_gates)
            b_pulse_once = gate_bucket.get_xy_pulse(bias_qubit, new_b_gates)

            t_pulse_list.append(t_pulse_once)
            b_pulse_list.append(b_pulse_once)

        self.play_pulse("XY", target_qubit, t_pulse_list)
        self.play_pulse("XY", bias_qubit, b_pulse_list)

        # new_t_pulse_list = self.cross_xy_pulses(
        #     self.xy_pulses[target_qubit], self.xy_pulses[bias_qubit]
        # )
        # self.xy_pulses.update({target_qubit: new_t_pulse_list})

    def cross_xy_pulses(
        self,
        t_pulses: List[PulseComponent],
        b_pulses: List[PulseComponent],
    ):
        """Optimize cross xy pulse list.

        Notice: Must be real pulse data.
        """
        amp_coe = self.experiment_options.amp_coe
        phase = self.experiment_options.phase
        target_qubit = self.run_options.target_qubit
        bias_qubit = self.run_options.bias_qubit

        power_coe = 10 ** ((bias_qubit.drive_power - target_qubit.drive_power) / 20)
        new_t_pulses = deepcopy(t_pulses)
        for idx, t_pulse in enumerate(new_t_pulses):
            b_pulse = b_pulses[idx]

            t_pulse_complex = t_pulse.pulse_complexes[0]
            b_pulse_complex = b_pulse.pulse_complexes[0]

            b_cross_pulse_complex = (
                b_pulse_complex * amp_coe * np.exp(1j * phase) * power_coe
            )
            new_t_pulse_complex = t_pulse_complex + b_cross_pulse_complex
            new_t_pulse_arr = np.real(new_t_pulse_complex)

            t_pulse.pulse_complexes = [new_t_pulse_complex]
            t_pulse.pulse = new_t_pulse_arr

        return new_t_pulses

    @staticmethod
    def update_instrument(self):
        """Update instrument parameters."""
        only_readout_target = self.experiment_options.only_readout_target
        target_qubit = self.run_options.target_qubit
        rd_channels = self.experiment_options.multi_readout_channels
        times = self.experiment_options.times
        depths = self.run_options.depths

        if only_readout_target is True:
            self._bind_probe_inst(target_qubit)

        new_depths = len(depths) * times
        for channel in rd_channels:
            sweep_delay = self._pulse_time_list[:new_depths]
            self.sweep_readout_trigger_delay(channel, sweep_delay)

    def _set_result_path(self):
        # bugfix: Inheritance can cause RB depth result error updates
        pass
