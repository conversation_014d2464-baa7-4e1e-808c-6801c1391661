# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/31
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>


from typing import List, Union

import numpy as np
from scipy.signal import find_peaks

from ...structures import Options, QDict
from ...types import QualityDescribe
from ..curve_fit_analysis import CurveFitAnalysis
from ..fit.fit_models import linear, typecast_float
from ..library import DistortionT1Analysis
from ..specification import (
    CurveAnalysisData,
    FitModel,
    FitOptions,
    ParameterRepr,
)


class dephase_ramsey_model:
    def __init__(self, kappa, chi, delta):
        self.kappa = kappa
        self.chi = chi
        self.delta = delta

    def _fit_model(self, t, n0, phi0, gamma, A, B):
        return self._dephase_ramsey_oscillation(
            t=t,
            n0=n0,
            phi0=phi0,
            gamma=gamma,
            A=A,
            B=B,
            delta=self.delta,
            kappa=self.kappa,
            chi=self.chi,
        )

    @staticmethod
    def _dephase_ramsey_oscillation(t, n0, phi0, gamma, A, B, delta, kappa, chi):
        tau = (1 - np.exp(-(kappa + 2 * chi * 1j) * t)) / (kappa + 2 * chi * 1j)

        y = np.exp(-(gamma + delta * 1j) * t + (phi0 - 2 * n0 * chi * tau) * 1j)

        return A * np.imag(y) + B


class square_rise_model:
    def __init__(self, kappa, chi_eff):
        self.kappa = kappa
        self.chi_eff = chi_eff

    def _fit_model(self, x, coeff, offset, x0):
        return self._square_rise_func(
            x=x,
            coeff=coeff,
            offset=offset,
            kappa=self.kappa,
            chi_eff=self.chi_eff,
            x0=x0,
        )

    @staticmethod
    def _square_rise_func(
        x: np.ndarray,
        coeff: float,
        offset: float,
        kappa: float,
        chi_eff: float,
        x0: float,
    ):
        return (coeff * (x - x0)) ** 2 / (chi_eff**2 + kappa**2 / 4) + offset


class cpk_lorenz_fit_model:
    def __init__(self, wq0, wrm, sign):
        self.wq0 = wq0
        self.wrm = wrm
        self.sign = sign

    def _fit_model(self, wdr, kappa, chi, A):
        return self._lorenz_func(
            x=wdr, wq0=self.wq0, wrm=self.wrm, kappa=kappa, chi=chi, A=A, sign=self.sign
        )

    @staticmethod
    def _lorenz_func(
        x: np.ndarray,
        wq0: float,
        wrm: float,
        kappa: float,
        chi: float,
        A: float,
        sign: int,
    ):
        denominator = (x - (wrm + (sign * chi))) ** 2 + (kappa / 2) ** 2
        omega_dq_star = wq0 + 2 * chi * (kappa * A**2) / denominator / (2 * np.pi)
        return omega_dq_star


@typecast_float
def exponential_decay(
    t: np.ndarray, n0: float, kappa: float, offset: float
) -> np.ndarray:
    return n0 * np.exp(-2 * np.pi * 1e-3 * kappa * t) + offset


class DePhaseRamseyAnalysis(CurveFitAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.set_validator("kappa", float)
        options.set_validator("chi", float)
        options.set_validator("t2echo", float)

        options.kappa = 1.198
        options.chi = 0.35
        options.t2echo = 14

        options.fit_model = None
        options.quality_bounds = [0.9, 0.8, 0.75]

        options.result_parameters = [
            ParameterRepr(name="n0", repr="n0", unit="1"),
            ParameterRepr(name="phi0", repr="phi0", unit="rad"),
        ]

        return options

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,  # pylint: disable=unused-argument
    ) -> Union[FitOptions, List[FitOptions]]:
        t2echo = self.options.t2echo
        data_key = self.options.data_key[0]
        analysis_datas = self.analysis_datas[data_key]
        y_data = analysis_datas.y
        max_min_delta = np.ptp(y_data)
        mean_y_data = np.mean(y_data)
        popt = {
            "n0": 3,
            "phi0": np.pi / 2,
            "gamma": 1 / t2echo * 1e-3,
            "A": -0.5 * max_min_delta,
            "B": mean_y_data,
        }
        fit_opt.p0.set_if_empty(
            n0=popt.get("n0"),
            phi0=popt.get("phi0"),
            gamma=popt.get("gamma"),
            A=popt.get("A"),
            B=popt.get("B"),
        )

        gamma_bounds = (
            1 / (self.options.t2echo + 2) * 1e-3,
            1 / (self.options.t2echo - 2) * 1e-3,
        )
        fit_opt.bounds.set_if_empty(
            n0=(0, 20),
            phi0=(-np.pi, np.pi),
            gamma=gamma_bounds,
            A=(-0.52, -0.5 * max_min_delta),
            B=(0.1, 0.9),
        )

        return fit_opt

    def _check_options(self):
        kappa = self.options.kappa * 1e-3  # GHz * 2 *pi
        chi = self.options.chi * 1e-3  # GHz * 2 *pi
        delta = (
            -self.experiment_data.metadata.process_meta.get("fringe") * 1e-3 * 2 * np.pi
        )  # GHz * 2 *pi

        fit_model = dephase_ramsey_model(
            kappa=kappa,
            chi=chi,
            delta=delta,
        )
        self.options.fit_model = FitModel(
            fit_func=fit_model._fit_model,
            model_description=r"\frac{1}{2}\left[1-\mathrm{Im}\{\exp[-(\Gamma_{2}+\Delta i)"
            r"t_{R}+(\phi_{0}-2n_{0}\chi * (\frac{1-e^{-(\kappa+2\chi i)"
            r"t_{R}}}{\kappa+2\chi i}))i]\}\right]",
        )


class DephaseRamseyCompAnalysis(CurveFitAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.x_label = "t_relax (ns)"
        options.result_parameters = [
            ParameterRepr(name="n0", repr="n0", unit="1"),
            ParameterRepr(name="kappa", repr="kappa", unit="MHz"),
            ParameterRepr(name="offset", repr="offset", unit="1"),
        ]

        options.fit_model = FitModel(fit_func=exponential_decay)
        return options

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        fit_opt.p0.set_if_empty(n0=20, kappa=1.0, offset=0.0)

        fit_opt.bounds.set_if_empty(offset=(0, 10))

        return fit_opt


class PhotonScanReadoutFreqAnalysis(CurveFitAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.x_label = "Probe Freq [MHz]"
        options.y_label = "delta_n"
        options.fit_model = FitModel(fit_func=linear)
        options.p0 = {"k": 0, "baseline": 0}
        options.result_parameters = [
            ParameterRepr(name="target_freq", repr="target_freq", unit="MHz"),
        ]

        return options

    def _create_analysis_data(self) -> QDict:
        sweep_list = self.experiment_data.metadata.process_meta["sweep_list"]
        photon_num_list = self.experiment_data.y_data["photon_num"]
        delta = []
        for idx in range(len(sweep_list)):
            delta.append(photon_num_list[idx * 2] - photon_num_list[idx * 2 + 1])
        self.experiment_data.y_data.clear()
        self.experiment_data.y_data["delta"] = delta
        self.experiment_data._x_data = sweep_list

        return super()._create_analysis_data()

    def _extract_result(self):
        best_data_key = self.experiment_data.metadata.process_meta.get("best_data_key")
        analysis_data = self.analysis_datas[best_data_key]
        k, b = analysis_data.fit_data.popt
        target_freq = round(-b / k, 3)
        self.results.target_freq.value = target_freq


class PhotonScanReadoutFreqV2Analysis(CurveFitAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.x_label = "Probe Freq [MHz]"
        options.fit_model = FitModel(fit_func=linear)
        options.p0 = {"k": 0, "baseline": 0}
        options.result_parameters = [
            ParameterRepr(name="target_freq", repr="target_freq", unit="MHz"),
        ]
        options.data_key = ["photon_gap"]
        return options

    def _create_analysis_data(self) -> QDict:
        """Create Analysis data provided for detailed analyze.

        Returns:
            A QDict object, key represents data type and value is
            CurveAnalysisData object.
        """
        photons = self.experiment_data.y_data.get("photon")
        photon_0 = photons[::2]
        photon_1 = photons[1::2]
        photon_gap = np.array(photon_1 - photon_0)
        x_data = self.experiment_data.x_data[::2]
        self.experiment_data._x_data = x_data
        self.experiment_data._y_data = dict(
            photon_0=photon_0, photon_1=photon_1, photon_gap=photon_gap
        )
        return super()._create_analysis_data()

    def _extract_result(self):
        photon_gap = self.experiment_data.y_data.get("photon_gap")
        if np.all(np.abs(photon_gap) < 1):
            # 当 0 1 光子数维持在一个较小的范围下，直接使用均值返回
            self.quality.descriptor = QualityDescribe.normal
            self.results.target_freq.value = round(
                np.mean(self.experiment_data.x_data), 3
            )
        else:
            best_data_key = self.experiment_data.metadata.process_meta.get(
                "best_data_key"
            )
            analysis_data = self.analysis_datas[best_data_key]
            k, b = analysis_data.fit_data.popt
            target_freq = round(-b / k, 3)
            self.results.target_freq.value = target_freq


class PhotonNumMeasAnalysis(DistortionT1Analysis):
    """Same with DistortionT1Analysis"""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()
        options.x_label = "Freq offset [MHz]"
        options.effective_chi = -0.5
        options.result_parameters = [
            ParameterRepr(name="t_offset", repr="TargetOffset", unit="MHz"),
            ParameterRepr(name="width", repr="FWHM", unit="MHz"),
            ParameterRepr(name="photon", repr="photon num", unit=""),
        ]
        return options

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Guess initial fit parameters.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        x = data.x
        y = data.y

        x_step = round(abs(x[1] - x[0]), 6)

        y_mean = np.mean(y)
        y_max = np.max(y)
        height = (y_max - y_mean) / 5 + y_mean

        peaks, properties = find_peaks(y, height=height, width=x_step / 2)

        if peaks.size != 0:
            peak_value_list = properties.get("peak_heights")
            peak_width_list = properties.get("widths")
            max_index = np.argmax(peak_value_list)
        else:
            peaks = [np.argmax(y)]
            peak_width_list = [0.5]
            peak_value_list = [0.5]
            max_index = 0

        a = y[peaks[max_index]] - np.mean(y)
        offset = np.mean(y)
        b = x[peaks[max_index]]

        c = peak_width_list[max_index] * abs(x_step) / 2
        coe = 0.01

        e = 0.5
        # coe1, coe2 = 0, 0
        a1 = peak_value_list[max_index]
        a2 = np.min(y)

        Ql = abs(b / c)

        # Set the initial values for each fitted model specifically.
        fit_opt_list = []
        fit_model_name = self.options.fit_model_name
        if fit_model_name == "lorentzian":
            fit_opt.p0.set_if_empty(A=1, offset=y_mean, f0=b, kappa=10)
            fit_opt_list = [fit_opt]
        elif fit_model_name == "bi_lorentz_tilt":
            fit_opt.p0.set_if_empty(offset=offset, b=b, cl=c, cr=c, coe=coe)
            for i in range(1, 10):
                opt = fit_opt.copy()
                opt.p0.set_if_empty(a=a * i * 0.1)
                fit_opt_list.append(opt)
        elif fit_model_name == "gauss_lorentzian":
            fit_opt.p0.set_if_empty(offset=offset, a=a, b=b, c=c, e=e)
            fit_opt_list = [fit_opt]
        elif fit_model_name == "skewed_lorentzian":
            fit_opt.p0.set_if_empty(A1=a2, A2=0, A3=a1, A4=0, fr=b, Ql=Ql)
            fit_opt_list = [fit_opt]
        elif fit_model_name == "skewed_gauss_lorentz":
            fit_opt.p0.set_if_empty(b=b, c=c, e=e, coe1=coe, a1=a1, coe2=coe, a2=a2)
            fit_opt_list = [fit_opt]

        if self.options.p0_history is not None:
            new_fit_opt = FitOptions(
                parameters=self.options.fit_model.signature,
                default_p0=self.options.p0_history,
                default_bounds=self.options.bounds,
                **self.options.curve_fit_extra,
            )
            fit_opt_list.append(new_fit_opt)

        return fit_opt_list

    def _extract_result(self, data_key: str):
        super()._extract_result(data_key)
        t_offset = self.results.t_offset.value
        self.results.photon.value = abs(
            2 * np.pi * t_offset / (2 * self.options.effective_chi)
        )
        for k in ["b", "f0", "fr"]:
            self.results.pop(k, None)


class PhotonNumMeasVsAmpAnalysis(CurveFitAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.curve_fit_extra.update({"ftol": 1.49012e-8, "xtol": 1.49012e-8})
        options.subplots = (2, 2)
        options.x_label = "Amp [V]"
        options.y_label = ["photon_num", "offset-freq", "offset-freq"]
        options.pcolormesh_options = {
            "shading": "auto",
        }
        options.result_parameters = [
            ParameterRepr(name="coeff", repr="coeff", unit="rad/(us*V)"),
            ParameterRepr(name="x0", repr="x0", unit="V"),
            ParameterRepr(name="offset", repr="offset", unit="1"),
            ParameterRepr(name="n0", repr="readout photon", unit="1"),
        ]
        options.figsize = (12, 6)
        options.effective_chi = -0.5

        return options

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        fit_opt.p0.set_if_empty(coeff=100, offset=0.0, x0=0.04)
        return fit_opt

    def _check_options(self):
        kappa = self.options.kappa  # MHz * 2pi
        effective_chi = self.options.effective_chi  # MHz * 2pi
        fit_model_cls = square_rise_model(
            kappa=kappa,
            chi_eff=effective_chi,
        )
        self.options.fit_model = FitModel(
            fit_func=fit_model_cls._fit_model,
        )

    def _extract_result(self):
        super()._extract_result()
        readout_amp = self.experiment_data.metadata.process_meta.get("readout_amp")
        n0 = self.options.fit_model.fit_func(
            x=readout_amp,
            coeff=self.results.coeff.value,
            offset=self.results.offset.value,
            x0=self.results.x0.value,
        )
        self.results.n0.value = n0


class PhotonNumMeasVsFreqAnalysis(CurveFitAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.curve_fit_extra.update({"ftol": 1.49012e-8, "xtol": 1.49012e-8})
        options.subplots = (2, 2)
        options.x_label = "FC (MHz)"
        options.y_label = [
            "FD (MHz)",
            "FD (MHz)",
            "FD (MHz)",
        ]
        options.pcolormesh_options = {
            "shading": "auto",
        }
        options.result_parameters = ["kappa", "chi", "A"]
        options.wq0 = 4500
        options.wrm = 6500
        options.plot_2d = True
        options.kappa = 0.5
        options.chi = 0.5
        options.A = 0.04
        return options

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        fit_opt.p0.set_if_empty(
            kappa=self.options.kappa, chi=self.options.chi, A=self.options.A
        )
        return fit_opt

    def _check_options(self):
        wq0 = self.options.wq0
        wrm = self.options.wrm
        init_state = self.experiment_data.metadata.draw_meta.get("init_state", 0)
        sign = 1 if str(init_state) == "0" else -1
        fit_model_cls = cpk_lorenz_fit_model(wq0=wq0, wrm=wrm, sign=sign)
        self.options.fit_model = FitModel(fit_func=fit_model_cls._fit_model)


class AmpToPhotonAnalysis(PhotonNumMeasVsAmpAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super(PhotonNumMeasVsAmpAnalysis, cls)._default_options()

        options.curve_fit_extra.update({"ftol": 1.49012e-8, "xtol": 1.49012e-8})
        options.subplots = (2, 2)
        options.x_label = "Amp [V]"
        options.y_label = ["photon_num", "Delays", "Delays"]
        options.pcolormesh_options = {
            "shading": "auto",
        }
        options.result_parameters = [
            ParameterRepr(name="coeff", repr="coeff", unit="rad/(us*V)"),
            ParameterRepr(name="x0", repr="x0", unit="V"),
            ParameterRepr(name="offset", repr="offset", unit="1"),
        ]
        options.figsize = (12, 6)

        return options
