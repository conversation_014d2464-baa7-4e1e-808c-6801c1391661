# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON><PERSON>

import json
from collections import defaultdict
from pathlib import Path
from typing import List

import numpy as np

from ...executor.batch import import_idle_point
from ...log import pyqlog
from ...tools import qarange
from ..batch_experiment import (
    EXP_TYPE,
    BatchExperiment,
)


def blame9(bit_nums=103):
    result = []
    for i in [1, 2, 3, 7, 8, 9, 13, 14, 15]:
        cur_v = []
        a = i
        while a < bit_nums:
            cur_v.append(a)
            a += 18
        append_v = [b + 3 for b in cur_v]
        cur_v.extend(append_v)
        result.append(sorted(cur_v))
    return result


class BatchSearchIdlePoint(BatchExperiment):
    std_blame9 = blame9()

    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.qs_flows = [
            "CavityFreqSpectrum",
            "QubitSpectrum",
        ]
        options.idle_flows = [
            "XpiDetection",
            "QubitFreqCalibration",
            "XpiDetection",
        ]
        options.idle_step = 0.01
        options.idle_points = 30
        options.is_qs_divide = False
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.idle_point_map = {}
        options.read_point_map = {}
        options.max_idle_length = 0
        options.max_read_length = 0
        options.qs_freq_map = {}
        options.idle_state_record = {
            "pass_units": defaultdict(list),
        }
        options.pass_unit_f01_map = {}
        options.pass_unit_osc_freq_map = {}
        options.ppt_template.update(
            dict(
                shape=(2, 3),
                split_by_unit=True,
                filter_repeat=True
            )
        )
        return options

    def _check_options(self):
        super()._check_options()
        self.run_options.ppt_template.exps = (
            self.experiment_options.qs_flows
            + self.experiment_options.idle_flows
            + ["RabiScanWidth"]
        )

    def _record_experiment(
        self, exp_name: str, exp: EXP_TYPE, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "QubitSpectrum" in exp_name:
            for unit in record.pass_units:
                # optimize: first check max peak
                peak_value = record.analysis_data.get(unit)["result"]["peaks"]
                if peak_value == "None":
                    peak_value = []
                peaks = [float(v) for v in peak_value]
                self.run_options.qs_freq_map[unit] = sorted(peaks, reverse=True)

        if "RabiScanWidth" in exp_name:
            for unit in record.pass_units:
                pre_osc_freq = self.run_options.pass_unit_osc_freq_map.get(unit, 0)
                cur_osc_freq = float(record.analysis_data.get(unit)["result"]["freq"])

                if cur_osc_freq > pre_osc_freq:
                    qubit = self.context_manager.chip_data.get_physical_unit(unit)
                    self.run_options.pass_unit_osc_freq_map[unit] = max(
                        pre_osc_freq, cur_osc_freq
                    )
                    self.run_options.pass_unit_f01_map[unit] = qubit.drive_freq

        self._record_idle_state_record()

        return record

    def _divide_idle_point(self, physical_units: List[str]):
        for unit in physical_units:
            qubit_obj = self.context_manager.chip_data.cache_qubit.get(unit)

            # init drive freq and baseband freq
            qubit_obj.drive_freq = 4500
            qubit_obj.XYwave.baseband_freq = 850

            if qubit_obj.tunable is False:
                pyqlog.log("EXP", f"{unit} tunable false, idle point set 0")
                point_list = [0]
            else:
                max_point = qubit_obj.dc_max
                min_point = qubit_obj.dc_min

                if self.experiment_options.idle_points:
                    point_list = np.linspace(
                        max_point, min_point, self.experiment_options.idle_points
                    ).tolist()
                else:
                    step = abs(self.experiment_options.idle_step) or 0.01
                    if max_point > min_point:
                        step = -step
                    point_list = qarange(max_point, min_point, step)

            self.run_options.idle_point_map[unit] = point_list
            self.run_options.max_idle_length = max(
                len(point_list), self.run_options.max_idle_length
            )

    def _change_idle_point(self, idle_index: int, units: List[str]):
        ok_units = []
        for unit in units:
            point_list = self.run_options.idle_point_map.get(unit)
            if idle_index < len(point_list):
                idle_point = point_list[idle_index]
                ok_units.append(unit)
                qubit = self.context_manager.chip_data.cache_qubit.get(unit)
                qubit.drive_freq = 4500
                qubit.XYwave.baseband_freq = 850
                qubit.inst.xy_gap = round(4500 - 850, 3)
                qubit.idle_point = idle_point - qubit.dc_max
                pyqlog.info(f"Change {unit} idle point {qubit.idle_point}")

        # bugfix: Switching idle points requires clearing pass_unit_f01_map
        self.run_options.pass_unit_f01_map.clear()
        self.run_options.pass_unit_osc_freq_map.clear()

        return ok_units

    def _divide_qs_group(self, working_unit: List[str]):
        group_dict = defaultdict(list)
        for unit in working_unit:
            bit = int(unit[1:])
            for index, bit_list in enumerate(self.std_blame9):
                if bit in bit_list:
                    group_dict[str(index)].append(unit)
        return list(group_dict.values())

    def _run_batch(self):
        all_units = self.experiment_options.physical_units

        if self.experiment_options.qs_flows:
            self.context_manager.global_options.max_point_unit = all_units
            self._divide_idle_point(all_units)

            # sweep idle point
            for idle_idx in range(self.run_options.max_idle_length):
                process_bar = f"{idle_idx + 1}/{self.run_options.max_idle_length}"

                # set idle point
                qs_para_units = self._change_idle_point(idle_idx, all_units)

                # run qs flows: CavityFreqSpectrum | QubitSpectrum
                if qs_para_units:
                    if self.experiment_options.is_qs_divide is True:
                        group_list = self._divide_qs_group(qs_para_units)
                        qs_pass_units = []
                        for ci, group in enumerate(group_list):
                            cur_pass_units = self._run_flow(
                                flows=self.experiment_options.qs_flows,
                                physical_units=group,
                                name=f"Blame9-G{ci + 1} rough find f01 ({process_bar})",
                            )
                            if cur_pass_units:
                                qs_pass_units.extend(cur_pass_units)
                    else:
                        qs_pass_units = self._run_flow(
                            flows=self.experiment_options.qs_flows,
                            physical_units=qs_para_units,
                            name=f"All rough find f01 ({process_bar})",
                        )

                    # RabiScanWidth Check f01
                    rw_pass_units = self._rabi_check(qs_pass_units, process_bar)

                    # run idle flow: XpiDetection | QubitFreqCalibration | XpiDetection
                    if self.experiment_options.idle_flows:
                        # parallel divide group
                        if rw_pass_units:
                            idle_group_map = self.parallel_allocator_for_qc(rw_pass_units)

                            # sweep group run idle flows
                            for group_name, idle_group in idle_group_map.items():
                                idle_pass_units = self._run_flow(
                                    self.experiment_options.idle_flows,
                                    physical_units=idle_group,
                                    name=f"{group_name} idle search flow ({process_bar})",
                                )
                                self._record_unit_status(idle_pass_units)
                                if idle_pass_units:
                                    for unit in idle_pass_units:
                                        all_units.remove(unit)

    def _record_unit_status(self, pass_units):
        for unit in pass_units:
            qubit = self.context_manager.chip_data.get_physical_unit(unit)
            self.run_options.idle_state_record["pass_units"][unit].append(
                qubit.idle_point
            )
            with open(
                str(
                    Path(
                        Path(self.run_options.record_path).parent,
                        f"{qubit.name}_{qubit.idle_point}.json",
                    )
                ),
                mode="w",
                encoding="utf-8",
            ) as fp:
                data = qubit.to_dict()
                json.dump(data, fp, indent=4, ensure_ascii=False)

    def _rabi_check(self, units, process_bar: str):
        max_iter = 0

        # count max freq list
        for unit in units:
            max_iter = max(max_iter, len(self.run_options.qs_freq_map[unit]))

        # sweep rabi scan width
        for i in range(max_iter):
            p_units = []
            for unit in units:
                qs_freq_list = self.run_options.qs_freq_map[unit]
                if i < len(qs_freq_list):
                    qubit = self.context_manager.chip_data.get_physical_unit(unit)
                    qubit.drive_freq = qs_freq_list[i]
                    p_units.append(unit)

            # parallel group and baseband_freq divide
            group_map = self.parallel_allocator_for_qc(p_units)

            for group_name, group in group_map.items():
                self._run_flow(
                    ["RabiScanWidth"],
                    group,
                    name=f"{group_name} rabi check f01 ({process_bar})",
                )

        # collect pass units
        pass_units = []
        for unit, f01 in self.run_options.pass_unit_f01_map.items():
            if unit in units:
                qubit = self.context_manager.chip_data.get_physical_unit(unit)
                qubit.drive_freq = f01
                pass_units.append(unit)

        return pass_units

    def _record_idle_state_record(self):
        with open(
            str(Path(Path(self.run_options.record_path).parent, "status.json")),
            mode="w",
            encoding="utf-8",
        ) as fp:
            json.dump(
                self.run_options.idle_state_record, fp, indent=4, ensure_ascii=False
            )

    def _batch_down(self):

        pass_units = list(self.run_options.idle_state_record.get("pass_units").keys())
        self.bind_pass_units(pass_units)
        self.run_options.idle_state_record["fail_units"] = (
            self.record_meta.execute_meta.result.fail_units
        )
        self._record_idle_state_record()

        if self.experiment_options.save_db:
            import_idle_point(
                self.backend, str(Path(self.run_options.record_path).parent)
            )

        super()._batch_down()
