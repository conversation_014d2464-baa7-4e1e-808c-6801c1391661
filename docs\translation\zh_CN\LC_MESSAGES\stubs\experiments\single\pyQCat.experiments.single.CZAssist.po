# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:2
msgid "pyQCat.experiments.single.CZAssist"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1
msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.__init__:1
msgid "Create a new experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.single.CZAssist.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`acquire_pulse "
"<pyQCat.experiments.single.CZAssist.acquire_pulse>`\\ "
"\\(acq\\_pulse\\[\\, qubit\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Set measure pulse."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`cal_fidelity "
"<pyQCat.experiments.single.CZAssist.cal_fidelity>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Calculate the fidelity matrix."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`experiment_info "
"<pyQCat.experiments.single.CZAssist.experiment_info>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Show experiment informations."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.single.CZAssist.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.single.CZAssist.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`jupyter_schedule "
"<pyQCat.experiments.single.CZAssist.jupyter_schedule>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Show experiment pulses's schedules in Jupyter Notebook."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.single.CZAssist.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`play_pulse <pyQCat.experiments.single.CZAssist.play_pulse>`\\ "
"\\(name\\, base\\_qubit\\, pulse\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Play pulse for XY control and Z flux control."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`plot_schedule "
"<pyQCat.experiments.single.CZAssist.plot_schedule>`\\ "
"\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
"Plot the pulse schedule which is very important to confirm experiment "
"logical correction."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.single.CZAssist.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
#: of pyQCat.experiments.single.cz_assist.CZAssist.run:1
msgid "Run SwapOnce experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.single.CZAssist.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.single.CZAssist.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_multiple_IF "
"<pyQCat.experiments.single.CZAssist.set_multiple_IF>`\\ \\(\\*IF\\[\\, "
"channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Used for multiple qubits experiment, set mutiple IF."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_multiple_index "
"<pyQCat.experiments.single.CZAssist.set_multiple_index>`\\ "
"\\(\\*args\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
"Used for multiple qubits experiment, set mutiple index. :type args: "
":py:class:`list` :param args: Setting the start and end values :type "
"channel: :py:class:`int` :param channel: channel settings Usage::     "
">>>experiment = TopExperiment()     >>>experiment.set_multiple_index([0, "
"1500], [100, 900], channel=1)."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.single.CZAssist.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.single.CZAssist.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_sweep_order "
"<pyQCat.experiments.single.CZAssist.set_sweep_order>`\\ "
"\\(order\\_list\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid "Old interface."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:39:<autosummary>:1
msgid ""
":py:obj:`sweep_readout_trigger_delay "
"<pyQCat.experiments.single.CZAssist.sweep_readout_trigger_delay>`\\ "
"\\(channel\\, sweep\\_delay\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.CZAssist.rst:41
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.single.CZAssist.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.single.CZAssist.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.single.CZAssist.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`run_options <pyQCat.experiments.single.CZAssist.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:38
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:4
msgid ""
"control_gate (str): Control gate, will add QC XY line. readout_type "
"(str): Readout type. is_amend (bool): True means use fidelity_matrix "
"amend result. cz_num (int): Two X/2 center add number CZ pulse, default "
"1."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:9
msgid ""
"qt_name (str): The target bit name. qc_name (str): The control bit name. "
"cali_pk_name (str): One parking bit name, that is bit not coupler."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:12
msgid "When SingleQubitPhase simulator parking bit phase, will set."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:15
msgid "scan_high_bit (bool): Is or not scan high frequency bit."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:15
msgid "Normal scan control bit, will set z_amp to the bit Z line."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:17
msgid ""
"const_z_amp (float): The set z_amp of no-scan-bit. z_amp (float): The "
"z_amp of scan-bit."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:20
msgid "cz_width (float): The value is Flattop Gaussian Pulse width."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:21
msgid "CZ pulse width, that normal determined by Swap experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:22
msgid ""
"tc (float): The value is Flattop Gaussian Pulse tc. sigma (float): The "
"value is Flattop Gaussian Pulse sigma."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:25
msgid "phase_list (List, array): When simulator bit, last X/2 scan phase list."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:27
msgid "parking_bits (List(str)): List of parking name,"
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:28
msgid "normal like: [\"q2\", \"c0\", ...]"
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:38
msgid "parking_param_dict (dict):"
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:30
msgid "The parking bits parameter of Flattop Gaussian Pulse. normal like: {"
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:33
msgid "\"q2\": {\"amp\": 0.4}, \"c0\": {\"amp\": 0.2}, ..."
msgstr ""

#: of
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:36
msgid "}"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._default_analysis_options
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options
#: pyQCat.experiments.single.cz_assist.CZAssist._default_run_options
#: pyQCat.experiments.single.cz_assist.CZAssist._metadata
msgid "Return type"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._default_analysis_options:12
#: pyQCat.experiments.single.cz_assist.CZAssist._default_experiment_options:40
#: pyQCat.experiments.single.cz_assist.CZAssist._default_run_options:12
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._default_run_options:1
msgid "Set run experiment operate options."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._default_run_options:10
msgid "Options:"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._default_run_options:4
msgid ""
"QT: Qubit object, which bit name is qt_name. QC: Qubit object, which bit "
"name is qc_name. cali_pk_qubit: Qubit object, which bit name is "
"cali_pk_name. parking_qubits (List): List of BaseQubit,"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._default_run_options:8
msgid "when name in parking_bits."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._default_analysis_options:1
msgid "Default analysis options."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._default_analysis_options:10
msgid "Analysis Options:"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._default_analysis_options:4
msgid "readout_type (str): Readout type."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._default_analysis_options:6
msgid "quality_bounds (Iterable[float]):"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._default_analysis_options:7
msgid "The bounds value of the goodness of fit."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._default_analysis_options:8
msgid ""
"is_plot (bool): Plot or not analysis fit photo. data_key (List[str]): "
"List of mark select data."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._metadata:1
msgid "Set metadata."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._check_options:1
msgid "Check Options."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._set_xy_pulses:1
msgid "Set experiment XY pulses."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._set_z_pulses:1
msgid "Set experiment Z pulses."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._set_measure_pulses:1
msgid "Set readout pulse."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist._save_cz_assist_json:1
msgid "Save cz assist json."
msgstr ""

