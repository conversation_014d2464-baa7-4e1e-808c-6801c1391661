# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:2
msgid "pyQCat.experiments.CompositeExperiment"
msgstr ""

#: of pyQCat.experiments.composite_experiment.CompositeExperiment:1
msgid "Bases: :py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
msgstr ""

#: of pyQCat.experiments.composite_experiment.CompositeExperiment:1
msgid "Composite Experiment base class"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.CompositeExperiment.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.CompositeExperiment.component_experiment>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
#: of
#: pyQCat.experiments.composite_experiment.CompositeExperiment.component_experiment:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.CompositeExperiment.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.CompositeExperiment.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.CompositeExperiment.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.CompositeExperiment.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.run:1
msgid "Run composite experiment."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.CompositeExperiment.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.CompositeExperiment.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.CompositeExperiment.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.CompositeExperiment.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/pyQCat.experiments.CompositeExperiment.rst:32
msgid "Attributes"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.CompositeExperiment.analysis>`\\"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.CompositeExperiment.analysis_options>`\\"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.CompositeExperiment.child_experiment>`\\"
msgstr ""

#: of pyQCat.experiments.CompositeExperiment.child_experiment:1
#: pyQCat.experiments.top_experiment.TopExperiment:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.CompositeExperiment.experiment_options>`\\"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.CompositeExperiment.run_options>`\\"
msgstr ""

#: of pyQCat.experiments.top_experiment.TopExperiment:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite_experiment.CompositeExperiment._run_analysis:1
msgid "Run composite analysis."
msgstr ""

#: of pyQCat.experiments.composite_experiment.CompositeExperiment._run_analysis
#: pyQCat.experiments.composite_experiment.CompositeExperiment.component_experiment
msgid "Parameters"
msgstr ""

#: of
#: pyQCat.experiments.composite_experiment.CompositeExperiment._run_analysis:4
msgid "The value of the x data."
msgstr ""

#: of
#: pyQCat.experiments.composite_experiment.CompositeExperiment._run_analysis:7
msgid "analysis class."
msgstr ""

#: of
#: pyQCat.experiments.composite_experiment.CompositeExperiment._create_child_experiment:1
msgid "Create a child experiment object."
msgstr ""

#: of
#: pyQCat.experiments.composite_experiment.CompositeExperiment._create_child_experiment
#: pyQCat.experiments.composite_experiment.CompositeExperiment.component_experiment
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite_experiment.CompositeExperiment._create_child_experiment:3
msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
msgstr ""

#: of
#: pyQCat.experiments.composite_experiment.CompositeExperiment._create_child_experiment
#: pyQCat.experiments.composite_experiment.CompositeExperiment.component_experiment
msgid "Returns"
msgstr ""

#: of
#: pyQCat.experiments.composite_experiment.CompositeExperiment._create_child_experiment:4
msgid "BaseExperiment object."
msgstr ""

#: of
#: pyQCat.experiments.composite_experiment.CompositeExperiment.component_experiment:3
msgid "Experiment index, or ``None`` if all experiments are to be returned."
msgstr ""

#: of
#: pyQCat.experiments.composite_experiment.CompositeExperiment.component_experiment:6
msgid "The component experiment(s)."
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.CompositeExperiment.__init__>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`component_experiment "
#~ "<pyQCat.experiments.CompositeExperiment.component_experiment>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.CompositeExperiment.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.CompositeExperiment.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`run <pyQCat.experiments.CompositeExperiment.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.CompositeExperiment.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.CompositeExperiment.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.CompositeExperiment.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.CompositeExperiment.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":py:obj:`analysis <pyQCat.experiments.CompositeExperiment.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.CompositeExperiment.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`child_experiment "
#~ "<pyQCat.experiments.CompositeExperiment.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.CompositeExperiment.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.CompositeExperiment.run_options>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.CompositeExperiment.__init__>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`component_experiment "
#~ "<pyQCat.experiments.CompositeExperiment.component_experiment>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.CompositeExperiment.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.CompositeExperiment.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.CompositeExperiment.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`run <pyQCat.experiments.CompositeExperiment.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.CompositeExperiment.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.CompositeExperiment.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.CompositeExperiment.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.CompositeExperiment.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.CompositeExperiment.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.CompositeExperiment.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`child_experiment "
#~ "<pyQCat.experiments.CompositeExperiment.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.CompositeExperiment.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.CompositeExperiment.run_options>`\\"
#~ msgstr ""

