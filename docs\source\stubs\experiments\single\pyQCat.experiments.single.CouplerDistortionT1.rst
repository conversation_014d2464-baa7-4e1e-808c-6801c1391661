﻿pyQCat.experiments.single.CouplerDistortionT1
=============================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: CouplerDistortionT1

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CouplerDistortionT1.__init__
      ~CouplerDistortionT1.acquire_pulse
      ~CouplerDistortionT1.cal_fidelity
      ~CouplerDistortionT1.experiment_info
      ~CouplerDistortionT1.from_experiment_context
      ~CouplerDistortionT1.get_qubit_str
      ~CouplerDistortionT1.get_xy_pulse
      ~CouplerDistortionT1.get_z_pulse
      ~CouplerDistortionT1.jupyter_schedule
      ~CouplerDistortionT1.options_table
      ~CouplerDistortionT1.play_pulse
      ~CouplerDistortionT1.plot_schedule
      ~CouplerDistortionT1.run
      ~CouplerDistortionT1.set_analysis_options
      ~CouplerDistortionT1.set_experiment_options
      ~CouplerDistortionT1.set_multiple_IF
      ~CouplerDistortionT1.set_multiple_index
      ~CouplerDistortionT1.set_parent_file
      ~CouplerDistortionT1.set_run_options
      ~CouplerDistortionT1.set_sweep_order
      ~CouplerDistortionT1.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CouplerDistortionT1.analysis
      ~CouplerDistortionT1.analysis_options
      ~CouplerDistortionT1.experiment_options
      ~CouplerDistortionT1.run_options
   
   