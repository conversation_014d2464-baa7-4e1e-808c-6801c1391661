﻿pyQCat.experiments.single.SwapOnce
==================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: SwapOnce

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~SwapOnce.__init__
      ~SwapOnce.acquire_pulse
      ~SwapOnce.cal_fidelity
      ~SwapOnce.experiment_info
      ~SwapOnce.from_experiment_context
      ~SwapOnce.get_qubit_str
      ~SwapOnce.jupyter_schedule
      ~SwapOnce.options_table
      ~SwapOnce.play_pulse
      ~SwapOnce.plot_schedule
      ~SwapOnce.run
      ~SwapOnce.set_analysis_options
      ~SwapOnce.set_experiment_options
      ~SwapOnce.set_multiple_IF
      ~SwapOnce.set_multiple_index
      ~SwapOnce.set_parent_file
      ~SwapOnce.set_run_options
      ~SwapOnce.set_sweep_order
      ~SwapOnce.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~SwapOnce.analysis
      ~SwapOnce.analysis_options
      ~SwapOnce.experiment_options
      ~SwapOnce.flattop_gaussian_paras
      ~SwapOnce.run_options
   
   