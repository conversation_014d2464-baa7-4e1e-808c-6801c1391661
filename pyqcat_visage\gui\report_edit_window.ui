<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>494</width>
    <height>304</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Report Setting</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QFrame" name="frame_2">
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="3,1">
       <item>
        <widget class="QGroupBox" name="groupBox">
         <property name="title">
          <string>Report Informations</string>
         </property>
         <layout class="QGridLayout" name="gridLayout">
          <item row="4" column="1">
           <widget class="QComboBox" name="detail">
            <item>
             <property name="text">
              <string>detailed</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>simple</string>
             </property>
            </item>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QCheckBox" name="is_report">
            <property name="text">
             <string>enable report</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QComboBox" name="language">
            <item>
             <property name="text">
              <string>cn</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>en</string>
             </property>
            </item>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QComboBox" name="save_type">
            <item>
             <property name="text">
              <string>pdf</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>html</string>
             </property>
            </item>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>theme</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_3">
            <property name="text">
             <string>save type</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QComboBox" name="theme">
            <item>
             <property name="text">
              <string>sync os</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>dark</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>white</string>
             </property>
            </item>
           </widget>
          </item>
          <item row="4" column="0">
           <widget class="QLabel" name="label_5">
            <property name="text">
             <string>detail</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="label_4">
            <property name="text">
             <string>language</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame">
         <property name="frameShape">
          <enum>QFrame::Shape::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Shadow::Raised</enum>
         </property>
         <layout class="QGridLayout" name="gridLayout_2">
          <item row="0" column="0">
           <widget class="QPushButton" name="ok_button">
            <property name="text">
             <string>OK</string>
            </property>
            <property name="icon">
             <iconset>
              <normaloff>_imgs/ok.png</normaloff>_imgs/ok.png</iconset>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QPushButton" name="cancel_button">
            <property name="text">
             <string>Cancel</string>
            </property>
            <property name="icon">
             <iconset>
              <normaloff>_imgs/cancel.png</normaloff>_imgs/cancel.png</iconset>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>file path</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="lineEdit"/>
      </item>
      <item>
       <widget class="QPushButton" name="choose_button">
        <property name="text">
         <string>Choose</string>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>_imgs/import.png</normaloff>_imgs/import.png</iconset>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>ok_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>update_report()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>153</x>
     <y>312</y>
    </hint>
    <hint type="destinationlabel">
     <x>356</x>
     <y>330</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>cancel_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>cancel()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>418</x>
     <y>148</y>
    </hint>
    <hint type="destinationlabel">
     <x>246</x>
     <y>151</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>choose_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>choose_path()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>446</x>
     <y>250</y>
    </hint>
    <hint type="destinationlabel">
     <x>246</x>
     <y>151</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>update_report()</slot>
  <slot>flash_path()</slot>
  <slot>choose_path()</slot>
  <slot>cancel()</slot>
 </slots>
</ui>
