# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/09
# __author:       ssfang

"""
Cavity Analysis.

"""

from typing import List, Union

import numpy as np
from scipy.optimize import minimize
from scipy.signal import argrelmin

from ...structures import Options
from ..curve_fit_analysis import CurveFitAnalysis
from ..fit.fit_models import skewed_lorentzian
from ..specification import (CurveAnalysisData, FitModel, FitOptions,
                             ParameterRepr)


class CavityAnalysisV2(CurveFitAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        # set fit model (skewed_lorentzian)
        options.fit_model = FitModel(fit_func=skewed_lorentzian)

        # set fitting initial value
        options.p0 = {"A2": 0, "A4": 0, "Ql": 5000}

        # set fitting iteration parameters
        options.curve_fit_extra.update({"ftol": 1.49012e-8, "xtol": 1.49012e-8})

        # default date key, analysis amp data
        options.data_key = ["Amp"]

        # default x label (Cavity Frequency (MHz))
        options.x_label = "Cavity Frequency (MHz)"

        # default quality bounds
        options.quality_bounds = [0.98, 0.95, 0.85]

        # default result parameters
        options.result_parameters = [
            ParameterRepr(
                name="fr", repr="fc", unit="MHz", param_path="Qubit.probe_freq"
            ),
            ParameterRepr(
                name="power",
                repr="probe power",
                unit="db",
                param_path="Qubit.probe_power",
            ),
            "Ql",
        ]

        return options

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Guess initial fit parameters.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        x = data.x
        y = data.y

        A1 = min(y[0], y[-1])
        A3 = -max(y)
        fr = x[np.argmin(y)]

        fit_opt.p0.set_if_empty(A1=A1, A3=A3, fr=fr)
        return fit_opt

    def _extract_result(self):
        """Extract cavity frequency from fitted data.

        Args:
            data_key (str): The basis for selecting data.
        """
        data_key = self.experiment_data.metadata.process_meta.get("best_data_key")
        analysis_data = self.analysis_datas[data_key]
        x = analysis_data.x
        y_fit = analysis_data.fit_data.y_fit
        A1, A2, A3, A4, fr, Q1 = analysis_data.fit_data.popt

        fit_minimal = argrelmin(y_fit)
        if fit_minimal[0].any():
            min_index = np.argmin(y_fit[fit_minimal])
            min_fr = x[fit_minimal[0][min_index]]
            result = minimize(
                skewed_lorentzian,
                np.array([min_fr]),
                args=(A1, A2, A3, A4, fr, Q1),
                method="BFGS",
                tol=1e-9,
            )

            fit_fr = float(result.x)
        else:
            fit_fr = fr

        if fit_fr > 10000:
            fit_fr = round(fit_fr / 1e6, 3)
        else:
            fit_fr = round(fit_fr, 3)

        self.results.fr.value = fit_fr

        readout_power, _ = self.experiment_data.metadata.draw_meta.get(
            "readout_power", (None, "db")
        )
        self.results.power.value = readout_power
        self.results.Ql.value = Q1
