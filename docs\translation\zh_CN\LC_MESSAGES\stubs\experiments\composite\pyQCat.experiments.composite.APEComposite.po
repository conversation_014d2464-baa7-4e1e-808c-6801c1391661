# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:2
msgid "pyQCat.experiments.composite.APEComposite"
msgstr ""

#: of pyQCat.experiments.composite.ape_composite.APEComposite:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.composite.APEComposite.__init__>`\\"
" \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.composite.APEComposite.component_experiment>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.composite.APEComposite.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.composite.APEComposite.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.composite.APEComposite.options_table>`\\ "
"\\(\\[mode\\, detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.composite.APEComposite.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite.ape_composite.APEComposite.run:1
msgid "Run composite experiment."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.composite.APEComposite.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.composite.APEComposite.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.composite.APEComposite.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.composite.APEComposite.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.APEComposite.rst:32
msgid "Attributes"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.composite.APEComposite.analysis>`\\"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.composite.APEComposite.analysis_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.composite.APEComposite.child_experiment>`\\"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.composite.APEComposite.experiment_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.composite.APEComposite.run_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.ape_composite.APEComposite._default_experiment_options:1
msgid "Default kwarg options for experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.ape_composite.APEComposite._default_analysis_options
#: pyQCat.experiments.composite.ape_composite.APEComposite._default_experiment_options
#: pyQCat.experiments.composite.ape_composite.APEComposite._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite.ape_composite.APEComposite._default_analysis_options:4
#: pyQCat.experiments.composite.ape_composite.APEComposite._default_experiment_options:5
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.composite.ape_composite.APEComposite._default_analysis_options:1
msgid "Default kwarg options for analysis."
msgstr ""

#: of pyQCat.experiments.composite.ape_composite.APEComposite._check_options:1
msgid "Check Options."
msgstr ""

#: of pyQCat.experiments.composite.ape_composite.APEComposite._metadata:1
msgid "Return experiment metadata for ExperimentData."
msgstr ""

#: of pyQCat.experiments.composite.ape_composite.APEComposite._metadata:3
msgid ""
"Subclasses can override this method to add custom experiment metadata to "
"the returned experiment result data."
msgstr ""

#: of pyQCat.experiments.composite.ape_composite.APEComposite._metadata:7
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.composite.APEComposite.__init__>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`component_experiment "
#~ "<pyQCat.experiments.composite.APEComposite.component_experiment>`\\"
#~ " \\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.APEComposite.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.APEComposite.get_qubit_str>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`run <pyQCat.experiments.composite.APEComposite.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.APEComposite.set_analysis_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.APEComposite.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.APEComposite.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.composite.APEComposite.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis "
#~ "<pyQCat.experiments.composite.APEComposite.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.composite.APEComposite.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`child_experiment "
#~ "<pyQCat.experiments.composite.APEComposite.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.composite.APEComposite.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.composite.APEComposite.run_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid "APE node, rough scan detune and fine scan detune."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.composite.APEComposite.__init__>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`component_experiment "
#~ "<pyQCat.experiments.composite.APEComposite.component_experiment>`\\"
#~ " \\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.APEComposite.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.APEComposite.get_qubit_str>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.composite.APEComposite.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`run <pyQCat.experiments.composite.APEComposite.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ "The rough scan detune get a rough"
#~ " detune value, the fine scan detune"
#~ " improve accuracy."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.APEComposite.set_analysis_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.APEComposite.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.APEComposite.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.composite.APEComposite.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.composite.APEComposite.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.composite.APEComposite.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`child_experiment "
#~ "<pyQCat.experiments.composite.APEComposite.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.composite.APEComposite.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.composite.APEComposite.run_options>`\\"
#~ msgstr ""

#~ msgid "Default Experiment Options"
#~ msgstr ""

#~ msgid "Experiment options:"
#~ msgstr ""

#~ msgid ""
#~ "detune_list (Union[List, np.ndarray]):  rough "
#~ "scan detune range. rough_n_list (List[int]):"
#~ " when rough scan, N list, normal "
#~ "`[ 7, 9, 13 ]`. fine_n_list "
#~ "List[int]: when fine scan, N list, "
#~ "normal `[ 7, 11 ]` theta_type "
#~ "(str): run ape theta type, \"Xpi\" "
#~ "or \"Xpi/2\". fine_precision (float): "
#~ "precision of fine scan detune. scan_type"
#~ " (str): Normal 'rough' or 'fine'."
#~ msgstr ""

#~ msgid "Default analysis options for Readout frequency calibrate experiment."
#~ msgstr ""

#~ msgid "Options:"
#~ msgstr ""

#~ msgid "diff_threshold (float): Twice fine scan results difference."
#~ msgstr ""

#~ msgid "Default options values for the experiment :meth:`run` method."
#~ msgstr ""

#~ msgid "Set metadata."
#~ msgstr ""

#~ msgid "Execute multiple time child experiment."
#~ msgstr ""

