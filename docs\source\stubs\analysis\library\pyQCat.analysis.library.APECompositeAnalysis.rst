﻿pyQCat.analysis.library.APECompositeAnalysis
============================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: APECompositeAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~APECompositeAnalysis.__init__
      ~APECompositeAnalysis.from_sub_analysis
      ~APECompositeAnalysis.run_analysis
      ~APECompositeAnalysis.set_options
      ~APECompositeAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~APECompositeAnalysis.analysis_datas
      ~APECompositeAnalysis.data_filter
      ~APECompositeAnalysis.drawer
      ~APECompositeAnalysis.experiment_data
      ~APECompositeAnalysis.has_child
      ~APECompositeAnalysis.options
      ~APECompositeAnalysis.quality
      ~APECompositeAnalysis.results
   
   