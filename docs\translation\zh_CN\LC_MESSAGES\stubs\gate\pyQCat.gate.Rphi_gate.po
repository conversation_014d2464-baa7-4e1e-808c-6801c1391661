# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/gate/pyQCat.gate.Rphi_gate.rst:2
msgid "pyQCat.gate.Rphi\\_gate"
msgstr ""

#: of pyQCat.gate.notable_gate.Rphi_gate:1
msgid "Bases: :py:class:`~pyQCat.gate.notable_gate.SingleQgate`"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.Rphi_gate.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.Rphi_gate.rst:22:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.gate.Rphi_gate.__init__>`\\ \\(phase\\[\\, "
"theta\\]\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.Rphi_gate.rst:22:<autosummary>:1
msgid ":py:obj:`to_pulse <pyQCat.gate.Rphi_gate.to_pulse>`\\ \\(qubit\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.Rphi_gate.rst:22:<autosummary>:1 of
#: pyQCat.gate.notable_gate.Rphi_gate.to_pulse:1
msgid "Convert quantum logic gate to pulse Object."
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.Rphi_gate.rst:24
msgid "Attributes"
msgstr ""

#: of pyQCat.gate.notable_gate.Rphi_gate.to_pulse:1:<autosummary>:1
msgid ":py:obj:`matrix <pyQCat.gate.Rphi_gate.matrix>`\\"
msgstr ""

#: of pyQCat.gate.notable_gate.Rphi_gate.to_pulse:1:<autosummary>:1
msgid ":py:obj:`name <pyQCat.gate.Rphi_gate.name>`\\"
msgstr ""

#: of pyQCat.gate.notable_gate.Rphi_gate.to_pulse:1:<autosummary>:1
msgid ":py:obj:`theta <pyQCat.gate.Rphi_gate.theta>`\\"
msgstr ""

#: of pyQCat.gate.notable_gate.Rphi_gate.to_pulse
msgid "Return type"
msgstr ""

#: of pyQCat.gate.notable_gate.Rphi_gate.to_pulse:4
msgid ":py:class:`~pyQCat.pulse.base_pulse.PulseComponent`"
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.gate.notable_gate.SingleQgate`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.gate.Rphi_gate.__init__>`\\ "
#~ "\\(phase\\[\\, theta\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`to_pulse <pyQCat.gate.Rphi_gate.to_pulse>`\\ \\(qubit\\)"
#~ msgstr ""

#~ msgid ":py:obj:`matrix <pyQCat.gate.Rphi_gate.matrix>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`name <pyQCat.gate.Rphi_gate.name>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`theta <pyQCat.gate.Rphi_gate.theta>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.gate.notable_gate.SingleQgate`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.gate.Rphi_gate.__init__>`\\ "
#~ "\\(phase\\[\\, theta\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`to_pulse <pyQCat.gate.Rphi_gate.to_pulse>`\\ \\(qubit\\)"
#~ msgstr ""

#~ msgid ":obj:`matrix <pyQCat.gate.Rphi_gate.matrix>`\\"
#~ msgstr ""

#~ msgid ":obj:`name <pyQCat.gate.Rphi_gate.name>`\\"
#~ msgstr ""

#~ msgid ":obj:`theta <pyQCat.gate.Rphi_gate.theta>`\\"
#~ msgstr ""

