# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/02/07
# __author:       <PERSON><PERSON><PERSON>

from ....analysis.tomography_analysis import ProcessTomographyAnalysisV2
from ....errors import ExperimentFieldError
from ....gate import GateBucket, GateCollection
from ....structures import MetaData, Options
from ...top_experiment_v1 import StandardContext, pyqlog
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class ProcessTomographyV2(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("goal_gate", GateCollection.gate_infos())
        options.set_validator("use_diag_in", bool)
        options.use_diag_in = True
        options.goal_gate = "I"
        options.mea_base_gates = ["I", "X/2", "Y/2"]
        options.prep_base_gates = ["I", "Y", "Y/2", "-X/2"]
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.gate_bucket = GateBucket()
        options.x_data = None
        options.qubit_num = None
        options.injection_func = [
            "_set_single_gate_pulse",
            "_set_double_gate_pulse",
        ]
        options.support_context = [StandardContext.CGC, StandardContext.QC]
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("neglect_mode", ["neglect_chi_prep", "neglect_chi_meas"])
        options.set_validator("phase_opt", bool)
        options.set_validator("cal_diag_in", bool)
        options.cal_diag_in = True
        options.neglect_mode = "neglect_chi_prep"
        options.phase_opt = False
        return options

    @staticmethod
    def set_xy_pulses(self):
        if self.qubit_pair is None:
            pulse_map = self._set_single_gate_pulse()
        else:
            pulse_map = self._set_double_gate_pulse()

        for qubit, pulses in pulse_map.items():
            self.play_pulse("XY", qubit, pulses)

    def _set_z_pulses(self):
        if self.qubit_pair is None:
            pulse_map = self._set_single_gate_pulse("z")
        else:
            pulse_map = self._set_double_gate_pulse("z")

        for qubit, pulses in pulse_map.items():
            self.play_pulse("Z", qubit, pulses)

    def _set_single_gate_pulse(self, mode: str = "xy"):
        pulse_map = {}
        goal_gate = self.experiment_options.goal_gate
        mea_base_gates = self.experiment_options.mea_base_gates
        prep_base_gates = self.experiment_options.prep_base_gates
        use_diag_in = self.experiment_options.use_diag_in
        gate_bucket = self.run_options.gate_bucket

        gate_list = []
        if use_diag_in:
            for p_gate in prep_base_gates:
                for m_gate in mea_base_gates:
                    gate_list.append([p_gate, m_gate])
        for p_gate in prep_base_gates:
            for m_gate in mea_base_gates:
                gate_list.append([p_gate, goal_gate, m_gate])

        pulse_list = []
        for gates in gate_list:
            pulse = getattr(gate_bucket, f"get_{mode.lower()}_pulse")(self.qubit, gates)
            pulse_list.append(pulse)

        pulse_map[self.qubit] = pulse_list
        return pulse_map

    def _set_double_gate_pulse(self, mode: str = "xy"):
        pyqlog.warning(f"{self.experiment_options.goal_gate} todo support double gate pulse {mode}")
        return {}

    def _check_options(self):
        super()._check_options()

        # check qubit
        if self.qubit:
            # one qubit qst
            self.run_options.gate_bucket.bind_single_gates(self.qubit)
            self.run_options.x_data = list(range(3 * 4 * 2))
            self.run_options.qubit_num = 1
        else:
            # todo: for 2q
            self.run_options.x_data = list(range((3**2) * (4**2) * 2))
            self.run_options.qubit_num = 2

        # check use diag in
        if self.experiment_options.use_diag_in is False:
            self.analysis_options.cal_diag_in = False

        # check IQ discriminator
        if not self.discriminator:
            raise ExperimentFieldError(self, msg="IQ discriminator is empty!")

        self.set_experiment_options(data_type="I_Q")
        self.set_run_options(x_data=self.run_options.x_data, analysis_class=ProcessTomographyAnalysisV2)

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.draw_meta = {
            "use_diag_in": self.experiment_options.use_diag_in,
            "goal_gate": self.experiment_options.goal_gate,
        }
        metadata.process_meta = {
            "qubit_num": self.run_options.qubit_num,
            "mea_base_gates": self.experiment_options.mea_base_gates,
            "prep_base_gates": self.experiment_options.prep_base_gates,
            "goal_gate": self.experiment_options.goal_gate,
        }
        return metadata

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        if self.run_options.qubit_num == 2:
            for key, result in self.analysis.results.items():
                if key == "chi_fidelity":
                    result.extra["path"] = "QubitPair.metadata.std.fidelity.qpt_chi_fidelity"
                elif key == "process_fidelity":
                    result.extra["path"] = "QubitPair.metadata.std.fidelity.qpt_process_fidelity"
        else:
            for key, result in self.analysis.results.items():
                if key == "chi_fidelity":
                    result.extra["path"] = "Qubit.qpt_chi_fidelity"
                elif key == "process_fidelity":
                    result.extra["path"] = "Qubit.qpt_process_fidelity"
