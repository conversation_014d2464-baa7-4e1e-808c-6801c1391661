# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.correct_crosstalk.rst:2
msgid "pyQCat.tools.correct\\_crosstalk"
msgstr ""

#: of pyQCat.tools.utilities.correct_crosstalk:1
msgid "Calibration data with crosstalk matrix."
msgstr ""

#: of pyQCat.tools.utilities.correct_crosstalk
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.utilities.correct_crosstalk:4
msgid "Data before crosstalk calibration."
msgstr ""

#: of pyQCat.tools.utilities.correct_crosstalk:7
msgid "Crosstalk matrix."
msgstr ""

#: of pyQCat.tools.utilities.correct_crosstalk
msgid "Return type"
msgstr ""

#: of pyQCat.tools.utilities.correct_crosstalk:10
msgid ":py:class:`~numpy.ndarray`"
msgstr ""

#: of pyQCat.tools.utilities.correct_crosstalk
msgid "Returns"
msgstr ""

#: of pyQCat.tools.utilities.correct_crosstalk:11
msgid "Data after crosstalk calibration."
msgstr ""

