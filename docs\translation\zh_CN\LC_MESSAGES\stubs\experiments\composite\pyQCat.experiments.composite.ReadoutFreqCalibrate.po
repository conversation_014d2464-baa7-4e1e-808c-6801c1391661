# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:2
msgid "pyQCat.experiments.composite.ReadoutFreqCalibrate"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate:1
msgid "Optimize Readout Frequency."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.component_experiment>`\\"
" \\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.from_experiment_context>`\\"
" \\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.get_qubit_str>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.options_table>`\\ "
"\\(\\[mode\\, detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`run <pyQCat.experiments.composite.ReadoutFreqCalibrate.run>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate.run:1
msgid ""
"The first XY line pulse amp is 0, the second XY line pulse amp is "
"`qubit.XYwave.Xpi`."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.set_analysis_options>`\\"
" \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.set_experiment_options>`\\"
" \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ReadoutFreqCalibrate.rst:32
msgid "Attributes"
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum:1:<autosummary>:1
msgid ""
":py:obj:`analysis "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.analysis>`\\"
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.analysis_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.child_experiment>`\\"
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.experiment_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate.run_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.cavity.CavityFreqSpectrum:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_experiment_options:7
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_experiment_options:4
msgid ""
"fc_list (List, np.ndarray): Scan cavity frequency list. readout_power "
"(float): Set readout channel power."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_analysis_options
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_experiment_options
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_run_options
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_analysis_options:8
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_experiment_options:9
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_run_options:4
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_analysis_options:1
msgid "Default analysis options for Readout frequency calibrate experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_analysis_options:6
msgid "Options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_analysis_options:4
msgid ""
"distance_flag (bool): True means use distance opt probe freq. "
"diff_threshold (float): Twice cavity frequency difference."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_run_options:1
msgid "Default options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._metadata:1
msgid "Set metadata."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.__init__>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`component_experiment "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.component_experiment>`\\"
#~ " \\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.get_qubit_str>`\\"
#~ " \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.run>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.set_analysis_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.set_parent_file>`\\"
#~ " \\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.set_run_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`child_experiment "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.run_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.__init__>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`component_experiment "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.component_experiment>`\\"
#~ " \\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.get_qubit_str>`\\"
#~ " \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.options_table>`\\"
#~ " \\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.run>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.set_analysis_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.set_parent_file>`\\"
#~ " \\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.set_run_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`child_experiment "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate.run_options>`\\"
#~ msgstr ""

