# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/07
# __author:       <PERSON><PERSON><PERSON>

from __future__ import annotations

import copy
from dataclasses import asdict, dataclass, field
from datetime import datetime
from typing import Dict, List

import pyQCat.experiments as monster_exp_library
import pyQCat.preliminary as preliminary_exp_library
from pyQCat.experiments.top_experiment_v1 import TopExperimentV1, pyqlog

from ... import __version__
from ...errors import PyQCatError
from ...executor.structures import ContextOptions
from ...experiments.parallel_experiment import ParallelExperiment
from ...pulse import STANDARD_PULSE_MODELS
from ...structures import CommonDict, QDict
from ...tools import qarange
from ...types import CONTEXT_FILTER, EXP_FILTER, UNADAPTED_EXPS

PulseMap = {value.__class__.__name__: value for value in STANDARD_PULSE_MODELS}


@dataclass()
class ExpCollection:
    meta: Dict
    context_options: dict
    options_for_regular_exec: dict
    options_for_parallel_exec: dict
    _cache_data: dict = field(default_factory=dict)
    single_mode: bool = False

    __annotations__ = {
        "meta": Dict,
        "context_options": dict,
        "options_for_regular_exec": dict,
        "options_for_parallel_exec": dict,
        "_cache_data": dict,
        "single_mode": bool
    }

    @classmethod
    def from_dict(cls, exp_name: str, exp_data: CommonDict) -> "ExpCollection":
        """Python dict -> instance of `ExpCollection`"""
        exp_data = cls.trans_local_exp_params(exp_data)

        if "meta" not in exp_data:
            exp_data.update(
                {
                    "meta": {
                        "username": "",
                        "visage_version": "",
                        "monster_version": __version__,
                        "chip": "",
                        "exp_class_name": exp_name.split("_")[0],
                        "export_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "description": "",
                    }
                }
            )

        if "options_for_regular_exec" not in exp_data:
            exp_data.update(
                {
                    "options_for_regular_exec": {
                        "experiment_options": exp_data.get("experiment_options", {}),
                        "analysis_options": exp_data.get("analysis_options", {}),
                    }
                }
            )
            exp_data.pop("experiment_options", None)
            exp_data.pop("analysis_options", None)

        if "options_for_parallel_exec" not in exp_data:
            exp_data.update({"options_for_parallel_exec": {}})

        return cls(**exp_data)

    @classmethod
    def from_db_data(cls, db_data: Dict) -> "ExpCollection":
        def _db_data_validate():
            # todo
            return QDict(**db_data)

        db_data = _db_data_validate()

        meta = {
            "username": db_data.username,
            "exp_class_name": db_data.exp_name,
            "create_time": db_data.create_time,
            "monster_version": db_data.version,
            "exp_type": db_data.exp_type,
        }
        exp_params = cls.trans_db_exp_params(db_data.exp_params)
        options_for_regular_exec = {
            "experiment_options": exp_params.get("experiment_options"),
            "analysis_options": exp_params.get("analysis_options"),
        }
        options_for_parallel_exec = {
            "experiment_options": exp_params.get("parallel_options", {}).get(
                "model_exp_options", {}
            ),
            "analysis_options": exp_params.get("parallel_options", {}).get(
                "model_ana_options", {}
            ),
        }
        context_options = exp_params.get("context_options")

        return cls(
            meta=meta,
            context_options=context_options,
            options_for_regular_exec=options_for_regular_exec,
            options_for_parallel_exec=options_for_parallel_exec,
        )

    @staticmethod
    def trans_db_exp_params(exp_params: Dict):
        std_params = {}

        if exp_params and isinstance(exp_params, Dict):
            # list type
            if len(exp_params) == 6 and "describe" in exp_params:
                describe = exp_params.get("describe")[0]
                cv = describe.split(" | ")
                if len(cv) == 3:
                    cv = cv[1:]
                if cv[0] == "normal":
                    range_data = eval(cv[1])
                elif cv[0] == "qarange":
                    range_data = qarange(*eval(cv[1]))
                else:
                    raise PyQCatError(f"mode only in `normal` or `qarange`")
                return range_data

            # for dict data
            for key, value in exp_params.items():
                # validator field pass
                if key == "validator":
                    pass

                # adapter readout_point_model
                elif key == "readout_point_model":
                    if value in PulseMap:
                        std_params.update({key: PulseMap[value[0]]})

                # Dict trans again
                elif isinstance(value, Dict):
                    std_params[key] = ExpCollection.trans_db_exp_params(value)

                # for three list value
                elif (
                    isinstance(value, list)
                    and len(value) == 3
                    and isinstance(value[-1], bool)
                ):
                    std_params[key] = value[0]

                # bad adapter
                else:
                    std_params[key] = value

        return std_params

    @staticmethod
    def trans_local_exp_params(exp_params: Dict):
        std_params = {}

        if exp_params and isinstance(exp_params, Dict):
            # for dict data
            for key, value in exp_params.items():
                # validator field pass
                if isinstance(value, str) and value.startswith("Points("):
                    cv = value.split(" | ")[1:]
                    if cv[0] == "normal":
                        range_data = eval(cv[1])
                    elif cv[0] == "qarange":
                        range_data = qarange(*eval(cv[1]))
                    else:
                        raise PyQCatError(f"mode only in `normal` or `qarange`")
                    std_params[key] = range_data

                elif key.endswith("*qarange"):
                    std_params[key.split("*")[0]] = qarange(*value)

                elif isinstance(value, dict):
                    std_params[key] = ExpCollection.trans_local_exp_params(value)

                else:
                    std_params[key] = value

        return std_params

    def _extract_unit_options(self, physical_unit):
        if self.options_for_parallel_exec:
            exp_options = self._extract_options(
                str(physical_unit), self._get_options(), self._get_options(is_parallel=True)
            )
            ana_options = self._extract_options(
                str(physical_unit),
                self._get_options(mode="ana"),
                self._get_options(mode="ana", is_parallel=True),
            )
        else:
            exp_options = self._get_options()
            ana_options = self._get_options(mode="ana")

        return exp_options, ana_options

    def _get_options(self, mode: str = "exp", is_parallel: bool = False):
        options = (
            self.options_for_regular_exec
            if is_parallel is False
            else self.options_for_parallel_exec
        )
        result = "experiment_options" if mode == "exp" else "analysis_options"
        return options.get(result, {})

    @staticmethod
    def _extract_options(physical_unit: str, options: CommonDict, parallel_options: CommonDict):
        _special_fields = ["child_exp_options", "child_ana_options"]

        # bugfix: parallel composite experiment can't set child options
        if parallel_options is None:
            parallel_options = {}

        new_options = {}
        for key, val in options.items():
            if key in _special_fields:
                new_options[key] = ExpCollection._extract_options(
                    physical_unit, options.get(key, {}), parallel_options.get(key, {})
                )
            elif key in parallel_options and isinstance(parallel_options[key], dict):
                new_options[key] = parallel_options.get(key).get(physical_unit, val)
            else:
                new_options[key] = val
        return new_options

    def to_dict(self) -> dict:
        """instance of `OptionsJson` -> Python dict"""
        return asdict(self)

    def to_exp(
        self, context_manager, **kwargs
    ) -> tuple[ParallelExperiment | None | PyQCatError, List, List]:
        try:
            exp_name = self.meta.get("exp_class_name")

            if exp_name in UNADAPTED_EXPS:
                raise PyQCatError(f"Experiment {exp_name} currently does not support asynchronous solutions!")
            
            context_name = self.context_options.get("name")
            if CONTEXT_FILTER and context_name not in CONTEXT_FILTER:
                raise PyQCatError(f"Experiment {exp_name} currently does in {CONTEXT_FILTER}!")

            # If the experiment is a `PreliminaryExperiment`, then `getattr(monster_exp_library, exp_name)`
            # will raise an `AttributeError` (e.g., `AttributeError: module 'pyQCat.experiments' has no
            # attribute 'FindBusCavityFreq'`). To solve this problem, we need to pass a default value when
            # calling `getattr`.
            # > If the named attribute does not exist, default is returned if provided, otherwise AttributeError is raised.
            # > https://docs.python.org/3/library/functions.html#getattr
            exp_cls = getattr(monster_exp_library, exp_name, None) or getattr(
                preliminary_exp_library, exp_name, None
            )
            
            if EXP_FILTER and exp_cls.EXP_TYPE not in EXP_FILTER:
                raise PyQCatError(f"Experiment {exp_name} currently does in {EXP_FILTER}!")

            use_simulator = kwargs.get("use_simulator", False)
            if exp_cls:
                ctx_options = copy.deepcopy(self.context_options)
                for k, v in kwargs.items():
                    if v is not None and k in ctx_options:
                        ctx_options[k] = v
                pre_exp = kwargs.get("pre_exp", False)
                ctx_options.update({"pre_exp": pre_exp})
                context_options = ContextOptions.from_dict(ctx_options)
                context_options_dict = context_options.to_dict()
                context = context_manager.generate_context(**context_options_dict)
                if context_options.use_parallel is False:
                    exp = exp_cls.from_experiment_context(context)
                    exp_options, ana_options = self._extract_unit_options(
                        context_options.physical_unit
                    )
                    exp_options["use_simulator"] = use_simulator
                    exp.set_experiment_options(**exp_options)
                    exp.set_analysis_options(**ana_options)
                    if self.single_mode and not isinstance(exp, TopExperimentV1):
                        raise PyQCatError("Test TopExperimentV1")
                else:
                    exp_list = []
                    for physical_unit, ctx in context.parallel_component.items():
                        exp_options, ana_options = self._extract_unit_options(physical_unit)
                        child_exp = exp_cls.from_experiment_context(ctx)
                        exp_options["use_simulator"] = use_simulator
                        child_exp.set_experiment_options(**exp_options)
                        child_exp.set_analysis_options(**ana_options)
                        exp_list.append(child_exp)
                    if self.single_mode and not isinstance(exp_list[-1], TopExperimentV1):
                        raise PyQCatError("Test TopExperimentV1")
                    exp = ParallelExperiment(exp_list, context.compensates)
                    exp.env_bit_resource = context.env_bit_resource
                exp, parallel_units = exp, context_options_dict.get("physical_unit")
                return self._env_bit_check(exp, parallel_units, context_manager)
            else:
                raise PyQCatError(f"No find {exp_name} in experiment library!")
        except Exception as error:
            import traceback
            pyqlog.debug(f"build exp error \n{traceback.format_exc()}")
            return error, [], []

    def _env_bit_check(
        self, exp, parallel_units, context_manager
    ) -> tuple[ParallelExperiment | None, List, List]:
        not_env_units = []

        if isinstance(parallel_units, str):
            parallel_units = [parallel_units]

        if isinstance(exp, ParallelExperiment):
            remove_index_list = []
            for index, child_exp in enumerate(exp.experiments):
                if not self._exp_env_bit_check(child_exp, context_manager):
                    remove_index_list.append(index)
                    not_env_units.append(parallel_units[index])
            new_child_exp = [
                child_exp
                for index, child_exp in enumerate(exp.experiments)
                if index not in remove_index_list
            ]
            if new_child_exp:
                exp._experiments = new_child_exp
            else:
                exp = None
            parallel_units = [
                unit for unit in parallel_units if unit not in not_env_units
            ]
        elif not self._exp_env_bit_check(exp, context_manager):
            return None, not_env_units, parallel_units

        return exp, parallel_units, not_env_units

    @staticmethod
    def _exp_env_bit_check(exp, context_manager) -> bool:
        for qubit in exp.qubits:
            if qubit.name not in context_manager.global_options.env_bits:
                return False

        for coupler in exp.couplers:
            if coupler.name not in context_manager.global_options.env_bits:
                return False

        return True
