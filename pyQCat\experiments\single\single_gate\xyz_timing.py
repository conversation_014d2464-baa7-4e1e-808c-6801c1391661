# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2021/04/22
# __author:       ssfang

"""
XYZTiming Experiment.
Get bit the XY line relative delay with the Z line.
"""

from typing import Dict

from ....analysis.library.xyztiming_analysis import XYZTimingAnalysis
from ....errors import ExperimentFieldError
from ....pulse import Constant, FlatTopGaussian
from ....pulse.pulse_function import pi_pulse
from ....qubit import Qubit
from ....structures import MetaData, Options
from ....tools import qarange
from ...coupler_experiment_v1 import CouplerBaseExperimentV1 as CouplerBaseExperiment
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class XYZTiming(TopExperiment):
    """XYZTiming Experiment, get the qubit xy or z line delay value."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            delays (List, np.ndarray): The list of delays that will
                                           be scanned in the experiment,
                                           in nanoseconds.
            const_delay (float): Set XY line const delay.
            z_amp (float): A ac pulse amplitude.
        """
        options = super()._default_experiment_options()

        options.set_validator("delays", list, limit_null=True)
        options.set_validator("const_delay", float)
        options.set_validator("z_pulse_params", dict)

        options.delays = qarange(0, 80, 0.625)
        options.const_delay = 50
        options.z_pulse_params = {"time": 50, "amp": 0.5, "sigma": 2.5, "buffer": 10}

        # No set, adjust XYZTimingComposite experiment.
        options.xy_delay = 0.0
        options.z_delay = 0.0

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options.

        Analysis Options:
            quality_bounds (Iterable[float]): The bounds value of the
                                              goodness of fit.
            is_plot (bool): Plot or not analysis fit photo.
            sample_rate (float): Sample rate, unit GHz.
            data_key (List[str]): List of mark select data.
        """
        options = super()._default_analysis_options()

        options.set_validator("extract_mode", ["fit_params", "min_point"])
        options.set_validator("quality_bounds", list)
        options.set_validator("peak_limit", float)
        options.set_validator(
            "fit_model_name",
            [
                "gauss_lorentzian",
                "skewed_lorentzian",
                "lorentzian",
                "bi_lorentz_tilt",
                "skewed_gauss_lorentz",
            ],
        )

        # options.sample_rate = 1.6  # GHZ
        options.extract_mode = "fit_params"
        options.const_delay = 50
        options.quality_bounds = [0.95, 0.9, 0.75]
        options.data_key = None
        options.link_channel = None
        options.old_z_delay = 0.0
        options.peak_limit = 0.4

        # fit_model_name: skewed_lorentzian or gauss_lorentzian
        options.fit_model_name = "gauss_lorentzian"

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.exp_qubit = None
        options.pi_qubit = None
        options.max_delay = 50
        return options

    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""

        const_delay = builder.experiment_options.const_delay
        z_time = builder.experiment_options.z_pulse_params.get("time")
        max_delay = float(builder.run_options.max_delay)

        xy_pulse = XYZTiming.get_xy_pulse(
            builder.run_options.pi_qubit, const_delay, max_delay, z_time
        )

        if not builder.is_coupler_exp:
            builder.play_pulse("XY", builder.qubit, xy_pulse)
        return xy_pulse

    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""
        pulse = pi_pulse(builder.run_options.pi_qubit)

        delay_list = builder.experiment_options.delays
        z_flux_params = builder.experiment_options.z_pulse_params
        max_delay = float(builder.run_options.max_delay)

        z_pulse_list = XYZTiming.get_z_pulse(
            builder.run_options.pi_qubit,
            pulse.width,
            z_flux_params,
            delay_list,
            max_delay,
        )

        if not builder.is_coupler_exp:
            builder.play_pulse("Z", builder.run_options.exp_qubit, z_pulse_list)

        return z_pulse_list

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()

        const_delay = self.experiment_options.const_delay
        z_amp = self.experiment_options.z_pulse_params.get("amp")
        metadata.draw_meta = {"Const Delay": (const_delay, "ns"), "Z Amp": (z_amp, "V")}
        return metadata

    def _check_options(self):
        """Check options."""
        super()._check_options()
        if self.discriminator is None:
            raise ExperimentFieldError(self.label, 'must select the "discriminator"')

        const_delay = self.experiment_options.const_delay
        delay_list = self.experiment_options.delays

        if delay_list[-1] < const_delay:
            max_delay = const_delay
        else:
            max_delay = delay_list[-1]
        max_delay = ((max_delay // 5) + 1) * 5

        old_z_delay = self.analysis_options.old_z_delay
        result_name = self.analysis_options.result_name
        for compensate in self.compensates.values():
            if compensate.name == result_name:
                old_z_delay = compensate.z_delay

        self.set_experiment_options(data_type="I_Q", save_result=False)
        self.set_run_options(
            exp_qubit=self.qubit,
            pi_qubit=self.qubit,
            max_delay=max_delay,
            x_data=self.experiment_options.delays,
            analysis_class=XYZTimingAnalysis,
        )
        self.set_analysis_options(
            data_key=["P1"],
            const_delay=const_delay,
            link_channel={
                "xy": self.qubit.xy_channel if self.qubit else None,
                "z": self.qubit.z_flux_channel if self.qubit else None,
            },
            old_z_delay=old_z_delay,
        )

        if self.run_options.exp_qubit:
            for compensate in self.compensates.values():
                if compensate.name == self.run_options.exp_qubit.name:
                    compensate.x_delay = self.experiment_options.xy_delay
                    compensate.z_delay = self.experiment_options.z_delay

    @staticmethod
    def get_xy_pulse(qubit, const_delay: float, max_delay: float, z_time: float):
        const_pulse = Constant(const_delay, 0, "XY")
        behind_pulse = pi_pulse(qubit)
        if z_time > behind_pulse.width:
            lp = round((z_time - behind_pulse.width) / 2, 5)
            rp = round(z_time - behind_pulse.width - lp, 5)
            behind_pulse = (
                Constant(lp, 0, "XY")() + behind_pulse() + Constant(rp, 0, "XY")()
            )
        else:
            behind_pulse()

        tail_pulse = Constant(max_delay - const_delay, 0, "XY")

        xy_pulse = const_pulse() + behind_pulse + tail_pulse()

        return xy_pulse

    @staticmethod
    def get_z_pulse(
        qubit: Qubit,
        xy_drag_time: float,
        z_flux_params: Dict,
        delay_list: list,
        max_delay: float,
    ):
        z_flux_time = z_flux_params.get("time")
        z_amp = z_flux_params.get("amp")
        z_pulse_list = []
        diff_time = (xy_drag_time - z_flux_time) / 2
        if diff_time < 0:
            diff_time = 0

        for delay in delay_list:
            front_constant = Constant(delay + diff_time, 0)
            center_delay = FlatTopGaussian(
                time=z_flux_time,
                amp=z_amp,
                sigma=z_flux_params.get("sigma"),
                buffer=z_flux_params.get("buffer"),
            )
            tail_delay = max_delay - delay + diff_time
            tail_constant = Constant(tail_delay, 0)

            z_pulse = front_constant() + center_delay() + tail_constant()
            z_pulse.bit = qubit.name
            z_pulse.sweep = "sweep delay"
            z_pulse_list.append(z_pulse)
        return z_pulse_list


class CouplerXYZTiming(CouplerBaseExperiment, XYZTiming):
    def _check_options(self):
        super()._check_options()
        for compensate in self.compensates.values():
            if compensate.name == self.driveQ.name:
                compensate.x_delay = self.experiment_options.xy_delay
                compensate.z_delay = self.experiment_options.z_delay
        self.set_run_options(pi_qubit=self.driveQ, exp_qubit=self.coupler)
        self.set_analysis_options(
            data_key=["P0"],
            link_channel={
                "xy": self.driveQ.xy_channel,
                "z": self.coupler.z_flux_channel,
            },
        )

    @staticmethod
    def set_xy_pulses(builder):
        pulse_list = XYZTiming.set_xy_pulses(builder)
        builder.compose_xy_pulses(pulse_list)

    @staticmethod
    def set_z_pulses(builder):
        z_pulse_list = XYZTiming.set_z_pulses(builder)
        builder.compose_z_pulses(z_pulse_list)


class CouplerXYZTimingByZZShift(XYZTiming):
    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.support_context = [StandardContext.CC]
        return options

    def _check_options(self):
        """Check options."""

        super()._check_options()

        self.set_run_options(exp_qubit=self.coupler, pi_qubit=self.qubit)
        self.set_analysis_options(
            link_channel={"xy": self.qubit.xy_channel, "z": self.coupler.z_flux_channel}
        )

        for compensate in self.compensates.values():
            compensate.x_delay = 0
            compensate.z_delay = 0
        for compensate in self.compensates.values():
            if compensate.name == self.qubit.name:
                compensate.x_delay = self.experiment_options.xy_delay
            elif compensate.name == self.coupler.name:
                compensate.z_delay = self.experiment_options.z_delay
