# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/15
# __author:       <PERSON><PERSON><PERSON>

import copy
from typing import List

from ...log import pyqlog
from ..batch_experiment import BatchExperiment


class BatchSearchF12(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.freq_gap_limit = (1100, 1250)  # MHz
        options.flows = ["QubitSpectrumF12", "RabiScanWidthF12", "F12Calibration"]
        options.affect_next_node = False
        options.xpi_rate = []
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.ppt_template.update(
            dict(shape=(2, 2), split_by_unit=True, filter_repeat=True)
        )
        options.pass_units = []
        return options

    def _run_batch(self):
        if self.experiment_options.xpi_rate:
            physical_units = copy.deepcopy(self.experiment_options.physical_units)
            for rate in self.experiment_options.xpi_rate:
                self._sync_f12_xpi(physical_units, rate)
                self._run_once_xpi(physical_units)
                physical_units = [
                    unit
                    for unit in physical_units
                    if unit not in self.run_options.pass_units
                ]
        else:
            self._run_once_xpi(self.experiment_options.physical_units)

        self.bind_pass_units(self.run_options.pass_units)
        if self.experiment_options.save_db:
            self.backend.save_chip_data_to_db(self.run_options.pass_units)
        self.run_options.pass_units = []

    def _run_once_xpi(self, physical_units: List[str]):
        left, right = self.experiment_options.freq_gap_limit
        divide_options = copy.deepcopy(
            self.backend.system.parallel_divide.ParallelAllocationQC
        )
        divide_options.max_limit = int(right - left)
        divide_options.intermediate_freq_allocate_options.expect_intermediate_freq = (
            left + right
        ) // 2
        divide_options.intermediate_freq_allocate_options.is_force = True
        group_map = self.parallel_allocator_for_qc(physical_units)
        for gn, group in group_map.items():
            working_qubits = self._check_baseband_freq(group)
            if working_qubits:
                self._run_flow(
                    physical_units=working_qubits,
                    flows=self.experiment_options.flows,
                    name=f"{gn} search f12",
                )

    def _check_baseband_freq(self, units: List[str]):
        working_qubits = []
        lf, rg = self.experiment_options.freq_gap_limit
        for qubit_name in units:
            qubit = self.backend.context_manager.chip_data.cache_qubit.get(qubit_name)
            baseband_freq = qubit.XYwave.baseband_freq
            if lf < baseband_freq < rg:
                working_qubits.append(qubit_name)
            else:
                pyqlog.warning(
                    f"{qubit_name} baseband freq {baseband_freq} not in limit ({lf}, {rg})"
                )
        return working_qubits

    def _sync_f12_xpi(self, physical_units: List[str], rate: float):
        for unit in physical_units:
            qubit = self.backend.chip_data.cache_qubit.get(unit)
            qubit.f12_options.Xpi = qubit.XYwave.Xpi * rate

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "QubitSpectrumF12" == exp_name and record.pass_units:
            self.run_options.pass_units.extend(record.pass_units)

        return record
