﻿pyQCat.experiments.composite.ACCrosstalk
========================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: ACCrosstalk

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~ACCrosstalk.__init__
      ~ACCrosstalk.component_experiment
      ~ACCrosstalk.from_experiment_context
      ~ACCrosstalk.get_qubit_str
      ~ACCrosstalk.options_table
      ~ACCrosstalk.run
      ~ACCrosstalk.set_analysis_options
      ~ACCrosstalk.set_experiment_options
      ~ACCrosstalk.set_parent_file
      ~<PERSON>rosstalk.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~<PERSON>rosstalk.analysis
      ~ACCrosstalk.analysis_options
      ~ACCrosstalk.child_experiment
      ~ACCrosstalk.experiment_options
      ~ACCrosstalk.run_options
   
   