# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/10/16
# __author:       <PERSON> Fang

"""
Sweep MicSource frequency and power, Ramsey Spectrum.
"""

from copy import deepcopy

import numpy as np

from ....analysis.library import RamseySpectrumAnalysis
from ....config import PyqcatConfig
from ....instrument.INSTRUMENT import INSTRUMENT
from ....instrument.STFSH9004 import STFSH9004
from ....log import pyqlog
from ....structures import Options
from ....tools.utilities import qarange
from ...composite_experiment import CompositeExperiment
from ...single import Ramsey


class InstConnect:
    def __init__(self, instrument_model, config_path=None):
        # print('current_inst={}'.format(instrument_model))
        self.unit_type = instrument_model
        self.inst = INSTRUMENT(config_path)[instrument_model]

    def close(self):
        del self.inst


class MicSourceRamseySpectrum(CompositeExperiment):
    """MicSourceRamseySpectrum class."""

    _sub_experiment_class = Ramsey

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()

        options.set_validator("mic_source_name", str)
        options.set_validator("mic_source_channel", int)
        options.set_validator("ip", str)
        options.set_validator("port", int)
        options.set_validator("divice_id", str)
        options.set_validator("freq_list", list)
        options.set_validator("power_min", float)
        options.set_validator("power_max", float)

        options.mic_source_name = "N5173B"
        options.mic_source_channel = None
        options.ip = "*************"
        options.port = 6001
        options.divice_id = "0x23240004"

        options.freq_list = qarange(4000, 6000, 5)  # MHz
        options.power_min = None
        options.power_max = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default Analysis Options"""
        options = super()._default_analysis_options()

        options.x_label = "MicSourceFrequency (MHz)"
        options.y_label = ["Delay (ns)", "RamseyOscFrequency (MHz)"]
        options.data_key = ["RamseyOscFrequency (MHz)"]
        options.sub_key = "P1"

        options.raw_data_format = "plot"
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.config_path = None
        options.max_freq = None
        options.drive_freq = None

        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        mic_source_name = self.experiment_options.mic_source_name
        mic_source_channel = self.experiment_options.mic_source_channel
        ip_addr = self.experiment_options.ip
        port = self.experiment_options.port
        divice_id = self.experiment_options.divice_id
        freq_list = self.experiment_options.freq_list
        config: PyqcatConfig = self.run_options.config

        config_path = config.get_system("config_path")
        result_name = self.child_experiment.qubit.name
        drive_freq = self.child_experiment.qubit.drive_freq or 4700  # MHz
        max_freq = max(freq_list)

        if self.discriminator:
            sub_key = "P1"
        else:
            sub_key = "Amp"

        if mic_source_name == "STFSH9004":
            ps_msg = {
                "channel": mic_source_channel,
                "ip": ip_addr,
                "port": port,
                "divice_id": divice_id,
            }
        else:
            ps_msg = {
                "channel": mic_source_channel,
                "config": f"{config_path}/config.ini [{mic_source_name}] params",
            }
        pyqlog.info(f"PS: {mic_source_name} check parameters: {ps_msg}")

        pyqlog.info(
            f"Set max_freq: {max_freq} MHZ, "
            f"{result_name} drive_freq: {drive_freq} MHz"
        )
        self.set_analysis_options(sub_key=sub_key, result_name=result_name)
        self.set_run_options(
            config_path=config_path,
            max_freq=max_freq,
            drive_freq=drive_freq,
        )

    def _freq_to_power(self, freq: float) -> float:
        """Calculate power by frequency."""
        power_max = self.experiment_options.power_max
        power_min = self.experiment_options.power_min
        max_freq = self.run_options.max_freq
        drive_freq = self.run_options.drive_freq or 4700  # MHz

        # power = (
        #     (power_max - power_min) * abs(freq - 4700) / abs(max_freq - 4700)
        # ) + power_min
        power = (
            (power_max - power_min)
            * abs(freq - drive_freq)
            / abs(max_freq - drive_freq)
        ) + power_min
        return round(power, 3)

    async def _sync_composite_run(self):
        """Run logic."""
        # super().run()

        use_simulator = self.experiment_options.use_simulator
        mic_source_name = self.experiment_options.mic_source_name
        mic_source_channel = self.experiment_options.mic_source_channel
        freq_list = self.experiment_options.freq_list
        provide_field = self.analysis_options.data_key[0]
        result_name = self.analysis_options.result_name
        config_path = self.run_options.config_path

        if use_simulator is False:
            if mic_source_name == "STFSH9004":
                ip_addr = self.experiment_options.ip
                port = self.experiment_options.port
                device_id = int(self.experiment_options.divice_id, base=16)
                mic_source_obj = STFSH9004(
                    ip=ip_addr, port=port, device_id=device_id, once=True
                )
                mic_source_obj.output_on(mic_source_channel)
            elif mic_source_name in ["N5173B"]:
                mic_source_obj = InstConnect(mic_source_name, config_path)
                mic_source_obj.inst.output_on()
            else:
                mic_source_obj = InstConnect(mic_source_name, config_path)
                mic_source_obj.inst.output_on(mic_source_channel)
        else:
            mic_source_obj = None

        length = len(freq_list)
        power_list = []
        osci_freq_list = []
        for idx, freq in enumerate(freq_list):
            power = self._freq_to_power(freq)
            power_list.append(power)
            freq_new = freq * 1e6  # Hz
            if use_simulator is False:
                if mic_source_name == "STFSH9004":
                    mic_source_obj.set_freq_power(mic_source_channel, freq_new, power)
                elif mic_source_name in ["N5173B"]:
                    mic_source_obj.inst.set_power(power)
                    mic_source_obj.inst.set_freq(freq_new)
                else:
                    mic_source_obj.inst.set_power(mic_source_channel, power)
                    mic_source_obj.inst.set_freq(mic_source_channel, freq_new)

            description = f"power={power}dB freq={freq}MHz"
            ramsey_exp = deepcopy(self.child_experiment)
            ramsey_exp.set_parent_file(self, description, idx, length)
            self._check_simulator_data(ramsey_exp, idx)
            # ramsey_exp.run()
            # ramsey_exp.clear_params()
            await ramsey_exp.run_experiment()

            osci_freq = ramsey_exp.analysis.results.freq.value
            ramsey_exp.analysis.provide_for_parent.update({provide_field: osci_freq})
            osci_freq_list.append(osci_freq)
            self._experiments.append(ramsey_exp)

        if use_simulator is False:
            if mic_source_name == "STFSH9004":
                mic_source_obj.output_off(mic_source_channel)
            elif mic_source_name in ["N5173B"]:
                mic_source_obj.inst.output_off()
                mic_source_obj.close()
            else:
                mic_source_obj.inst.output_off(mic_source_channel)
                mic_source_obj.close()

        save_file = f"{result_name}_mic_ramsey_spectrum"
        self.file.save_data(
            np.asarray(freq_list),
            np.asarray(power_list),
            np.asarray(osci_freq_list),
            name=save_file,
        )

        self._run_analysis(freq_list, RamseySpectrumAnalysis)
