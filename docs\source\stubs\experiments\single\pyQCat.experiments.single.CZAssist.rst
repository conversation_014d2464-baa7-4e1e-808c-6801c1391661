﻿pyQCat.experiments.single.CZAssist
==================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: CZAssist

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CZAssist.__init__
      ~CZAssist.acquire_pulse
      ~CZAssist.cal_fidelity
      ~CZAssist.experiment_info
      ~CZAssist.from_experiment_context
      ~CZAssist.get_qubit_str
      ~CZAssist.jupyter_schedule
      ~CZAssist.options_table
      ~CZAssist.play_pulse
      ~CZAssist.plot_schedule
      ~CZAssist.run
      ~CZAssist.set_analysis_options
      ~CZAssist.set_experiment_options
      ~CZAssist.set_multiple_IF
      ~CZAssist.set_multiple_index
      ~CZAssist.set_parent_file
      ~CZAssist.set_run_options
      ~CZAssist.set_sweep_order
      ~CZAssist.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CZAssist.analysis
      ~CZAssist.analysis_options
      ~CZAssist.experiment_options
      ~CZAssist.run_options
   
   