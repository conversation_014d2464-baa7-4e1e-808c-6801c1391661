# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/26
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy

import numpy as np

from ....acquisition.acq_tackle import QubitMeasureMode
from ....analysis.library.mcm_analysis import (
    CoherentPumpAnalysis,
    CoherentPumpAnalysisV2,
    McmQubitDePhaseAnalysis,
    McmQubitPopulationAnalysis,
    McmSpectatorAnalysis,
    SingleShotExtendAnalysis,
)
from ....analysis.library_v2.t1_analysis import T1AnalysisV2
from ....errors import PulseError
from ....pulse.pulse_function import half_pi_pulse, pi_pulse, zero_pulse
from ....pulse.pulse_lib import <PERSON><PERSON><PERSON><PERSON><PERSON>r, <PERSON>cquirePhase, <PERSON><PERSON>quire<PERSON><PERSON>, Constant
from ....qubit import Qubit
from ....structures import Dict, MetaData, Options, QDict
from ....tools import qarange
from ....types import StandardContext
from ...top_experiment_v1 import TopExperimentV1
from ..error_quantification import RBSingle
from .single_shot import SingleShot


def generate_measure_pulse(
        qubit: Qubit,
        acq_pulse_type: str,
        acq_pulse_params: Dict = None,
        scale_coef: float = 1.0,
):
    if acq_pulse_type == "Clear":
        scale_amp = qubit.Mwave.amp * scale_coef
        scale_amp1 = acq_pulse_params[acq_pulse_type]["amp1"] * scale_coef
        scale_amp2 = acq_pulse_params[acq_pulse_type]["amp2"] * scale_coef
        max_abs_amp = np.max(np.abs([scale_amp, scale_amp1, scale_amp2]))
        if max_abs_amp > 1.0:
            raise PulseError(
                f"The amplitude norm must be <= 1, found: {abs(max_abs_amp)}"
            )
        acq_pulse = AcquireClear(
            rd_time=qubit.Mwave.width,
            amp_list=scale_amp,
            baseband_freq=qubit.Mwave.baseband_freq,
            amp1=scale_amp1,
            amp2=scale_amp2,
            tkick=acq_pulse_params[acq_pulse_type]["tkick"],
        )()
    elif acq_pulse_type == "Phase":
        scale_rd_amp = qubit.Mwave.amp * scale_coef
        scale_reset_amp = acq_pulse_params[acq_pulse_type]["amp"] * scale_coef
        max_abs_amp = np.max(np.abs([scale_rd_amp, scale_reset_amp]))
        if max_abs_amp > 1.0:
            raise PulseError(
                f"The amplitude norm must be <= 1, found: {abs(max_abs_amp)}"
            )
        acq_pulse = AcquirePhase(
            rd_time=qubit.Mwave.width,
            amp_list=scale_rd_amp,
            baseband_freq=qubit.Mwave.baseband_freq,
            amp=scale_reset_amp,
            phase=acq_pulse_params[acq_pulse_type]["phase"],
            tkick=acq_pulse_params[acq_pulse_type]["tkick"],
        )()
    else:
        if np.abs(qubit.Mwave.amp * scale_coef) > 1.0:
            raise PulseError(
                f"The amplitude norm must be <= 1, found: {np.abs(qubit.Mwave.amp * scale_coef)}"
            )
        acq_pulse = AcquireSine(
            time=qubit.Mwave.width,
            amp_list=[qubit.Mwave.amp * scale_coef],
            baseband_freq_list=[qubit.Mwave.baseband_freq],
        )()

    acq_pulse.bit = qubit.name
    return acq_pulse


class McmQubitPopulation(TopExperimentV1):
    """"""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("trigger_delay_list", list)
        options.set_validator("init_state", int)
        options.set_validator("acq_pulse_params", dict)
        options.set_validator("acq_pulse_type", ["Clear", "Phase", "Square"])

        options.trigger_delay_list = [0, 0, 0]
        options.init_state = 0
        options.acq_pulse_params = {
            "Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100},
            "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100},
        }
        options.acq_pulse_type = "Square"
        options.resource_filter = False

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.multiple_measure_options = None
        return options

    def _check_options(self):
        super()._check_options()

        if not self.discriminator:
            raise ValueError("No find dcm!")

        self.set_experiment_options(data_type="I_Q")
        self.set_experiment_options(enable_one_sweep=True)

        sample_count = len(self.experiment_options.trigger_delay_list)
        self.run_options.x_data = [1]
        self.run_options.measure_modes = QubitMeasureMode(
            name=self.qubit.name,
            measure_total=sample_count,
            measure_num_list=list(range(sample_count)),
            base_label_list=["" for _ in range(sample_count)],
        )
        self.run_options.analysis_class = McmQubitPopulationAnalysis

        self._custom_readout()

    def _custom_readout(self):
        acq_pulse_params = self.experiment_options.acq_pulse_params
        acq_pulse_type = self.experiment_options.acq_pulse_type
        trigger_delay_list = self.experiment_options.trigger_delay_list

        acq_pulse = generate_measure_pulse(self.qubit, acq_pulse_type, acq_pulse_params)

        self.run_options.multiple_measure_options = {
            self.qubit: QDict(
                measure_delay=trigger_delay_list,
                waveform=[acq_pulse],
            )
        }

    @staticmethod
    def set_xy_pulses(builder):
        init_state = builder.experiment_options.init_state
        if init_state:
            pulse = pi_pulse(builder.qubit)()
        else:
            pulse = zero_pulse(builder.qubit)()
        builder.play_pulse("XY", builder.qubit, pulse)

    def _metadata(self):
        meta_data = super()._metadata()
        meta_data.process_meta["trigger_delay_list"] = (
            self.experiment_options.trigger_delay_list
        )
        return meta_data

    def _alone_save_result(self):
        experiment_data = self.analysis.experiment_data
        self.file.save_data(
            experiment_data.x_data,
            *list(experiment_data.y_data.values()),
            name=f"{self}(x_td_p0_p1)",
        )


class McmQubitDePhase(TopExperimentV1):
    """"""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()
        options.set_validator("first_trigger_delay", int)
        options.set_validator("phase_list", list)
        options.set_validator("delay", float)
        options.set_validator("scale_coef", float)
        options.set_validator("acq_pulse_params", dict)
        options.set_validator("acq_pulse_type", ["Clear", "Phase", "Square"])

        options.acq_pulse_params = {
            "Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100},
            "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100},
        }
        options.acq_pulse_type = "Square"
        options.first_trigger_delay = 100
        options.phase_list = qarange(0, 4 * np.pi, 0.2)
        options.delay = 5000
        options.scale_coef = 1.0

        return options

    @staticmethod
    def set_xy_pulses(builder):
        qubit = builder.qubit
        delay = builder.experiment_options.delay
        phase_list = builder.experiment_options.phase_list

        xp2 = half_pi_pulse(qubit)()
        pulse2 = Constant(delay, 0, "XY")()
        pulse3 = half_pi_pulse(qubit)()
        pulse_list = []
        for phase in phase_list:
            xy_pulse = deepcopy(xp2) + pulse2 + deepcopy(pulse3)(phase=phase)
            pulse_list.append(xy_pulse)

        builder.play_pulse("XY", qubit, pulse_list)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        scale = self.experiment_options.scale_coef
        metadata.draw_meta = {"scale": scale}
        return metadata

    def _check_options(self):
        super()._check_options()

        if not self.discriminator:
            raise ValueError("No find dcm!")

        self.set_experiment_options(data_type="I_Q")

        self.run_options.x_data = self.experiment_options.phase_list
        self.run_options.measure_modes = QubitMeasureMode(
            name=self.qubit.name,
            measure_total=2,
            measure_num_list=[1],
            base_label_list=[""],
        )
        self.run_options.analysis_class = McmQubitDePhaseAnalysis

        self._custom_readout()

    def _custom_readout(self):
        acq_pulse_params = self.experiment_options.acq_pulse_params
        acq_pulse_type = self.experiment_options.acq_pulse_type
        first_trigger_delay = self.experiment_options.first_trigger_delay
        delay = self.experiment_options.delay
        half_pi_pulse_width = half_pi_pulse(self.qubit).width
        scale_coef = self.experiment_options.scale_coef

        acq_pulse = generate_measure_pulse(
            self.qubit, acq_pulse_type, acq_pulse_params, scale_coef
        )
        acq_pulse2 = generate_measure_pulse(self.qubit, "Square", acq_pulse_params)
        measure_delay2 = (
                delay + half_pi_pulse_width * 2 - first_trigger_delay - acq_pulse.width
        )
        if measure_delay2 < 0:
            raise ValueError("measure delay must > 0")

        self.run_options.multiple_measure_options = {
            self.qubit: QDict(
                measure_index=[0, 1],
                measure_delay=[first_trigger_delay, measure_delay2],
                waveform=[acq_pulse, acq_pulse2],
            )
        }


class McmSpectator(TopExperimentV1):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("trigger_delay_list", list)
        options.set_validator("target_init_state", int)
        options.set_validator("bias_acq_pulse_params", dict)
        options.set_validator("bias_acq_pulse_type", ["Clear", "Phase", "Square"])
        options.set_validator("add_delay", float)
        options.set_validator("bias_init_state", int)

        options.trigger_delay_list = [0, 0, 0]
        options.target_init_state = 0
        options.bias_acq_pulse_params = {
            "Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100},
            "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100},
        }
        options.bias_acq_pulse_type = "Square"
        options.resource_filter = False
        options.add_delay = 100
        options.bias_init_state = 0

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.multiple_measure_options = None
        options.support_context = [StandardContext.CM]
        options.target_tail_time = 0
        return options

    def _check_options(self):
        super()._check_options()

        if not self.discriminator:
            raise ValueError("No find dcm!")

        self.set_experiment_options(data_type="I_Q")
        self.set_experiment_options(enable_one_sweep=True)

        self.run_options.x_data = [1]
        self.run_options.measure_modes = [
            QubitMeasureMode(
                name=self.target_unit.name,
                measure_total=1,
                measure_num_list=[0],
                base_label_list=[""],
            )
        ]
        self.run_options.analysis_class = McmSpectatorAnalysis

        self._custom_readout()

    def _custom_readout(self):
        bias_acq_pulse_params = self.experiment_options.bias_acq_pulse_params
        bias_acq_pulse_type = self.experiment_options.bias_acq_pulse_type
        trigger_delay_list = self.experiment_options.trigger_delay_list
        add_delay = self.experiment_options.add_delay
        self.run_options.multiple_measure_options = {}
        target_qubit = self.target_unit
        bias_qubit = self.bias_unit

        # set multiple qubit measure
        self.experiment_options.multi_readout_channels = [
            target_qubit.readout_channel,
            bias_qubit.readout_channel,
        ]

        # for bias qubit
        bias_acq_pulse = generate_measure_pulse(
            bias_qubit, bias_acq_pulse_type, bias_acq_pulse_params, 1.0
        )
        self.run_options.multiple_measure_options.update(
            {
                bias_qubit: QDict(
                    measure_delay=trigger_delay_list,
                    waveform=[bias_acq_pulse],
                    acq_data=False,
                )
            }
        )

        # for target qubit
        pi_width = pi_pulse(target_qubit).width
        self.run_options.target_tail_time = (
                bias_acq_pulse.width * len(trigger_delay_list)
                + sum(trigger_delay_list)
                - pi_width
        )
        target_acq_pulse = generate_measure_pulse(target_qubit, "Square")
        self.run_options.multiple_measure_options.update(
            {
                target_qubit: QDict(
                    measure_delay=[
                        pi_width + add_delay + self.run_options.target_tail_time
                    ],
                    waveform=[target_acq_pulse],
                )
            }
        )

    @staticmethod
    def set_xy_pulses(builder):
        target_init_state = builder.experiment_options.target_init_state
        bias_init_state = builder.experiment_options.bias_init_state
        add_delay = builder.experiment_options.add_delay
        target_tail_time = builder.run_options.target_tail_time
        target_qubit = builder.target_unit
        bias_qubit = builder.bias_unit

        if target_init_state:
            pulse = pi_pulse(target_qubit)()
            pulse += Constant(add_delay + target_tail_time, 0, "XY")()
            builder.play_pulse("XY", target_qubit, pulse)

        if bias_init_state:
            pulse = pi_pulse(bias_qubit)()
            builder.play_pulse("XY", bias_qubit, pulse)


class McmSpectatorDePhase(TopExperimentV1):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("add_bq_readout", bool)
        options.set_validator("delay", float)
        options.set_validator("phase_list", list)
        options.set_validator("bias_acq_pulse_params", dict)
        options.set_validator("bias_acq_pulse_type", ["Clear", "Phase", "Square"])
        options.set_validator("bias_trigger_delay", float)
        options.set_validator("scale_coef", float)
        options.set_validator("bias_init_state", int)

        options.add_bq_readout = True
        options.delay = 1500
        options.phase_list = np.linspace(0, 4 * np.pi, 51).tolist()
        options.bias_acq_pulse_params = {
            "Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100},
            "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100},
        }
        options.bias_acq_pulse_type = "Square"
        options.bias_trigger_delay = 100
        options.scale_coef = 1.0
        options.bias_init_state = 0

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.multiple_measure_options = None
        options.support_context = [StandardContext.CM]
        return options

    def _check_options(self):
        super()._check_options()

        if not self.discriminator:
            raise ValueError("No find dcm!")

        self.set_experiment_options(data_type="I_Q")
        self.set_experiment_options(enable_one_sweep=True)

        self.run_options.x_data = self.experiment_options.phase_list
        self.run_options.measure_modes = QubitMeasureMode(
            name=self.target_unit.name,
            measure_total=1,
            measure_num_list=[0],
            base_label_list=[""],
        )
        self.run_options.analysis_class = McmQubitDePhaseAnalysis

        self._custom_readout()

    def _custom_readout(self):
        add_bq_readout = self.experiment_options.add_bq_readout
        scale_coef = self.experiment_options.scale_coef
        bias_acq_pulse_params = self.experiment_options.bias_acq_pulse_params
        bias_acq_pulse_type = self.experiment_options.bias_acq_pulse_type
        bias_trigger_delay = self.experiment_options.bias_trigger_delay
        delay = self.experiment_options.delay
        self.run_options.multiple_measure_options = {}
        target_qubit = self.target_unit
        bias_qubit = self.bias_unit

        # set multiple qubit measure
        if add_bq_readout is True:
            self.experiment_options.multi_readout_channels = [
                target_qubit.readout_channel,
                bias_qubit.readout_channel,
            ]
            bias_acq_pulse = generate_measure_pulse(
                bias_qubit, bias_acq_pulse_type, bias_acq_pulse_params, scale_coef
            )
            self.run_options.multiple_measure_options.update(
                {
                    bias_qubit: QDict(
                        measure_delay=[bias_trigger_delay],
                        waveform=[bias_acq_pulse],
                        acq_data=False,
                    )
                }
            )
        else:
            self.experiment_options.multi_readout_channels = [
                target_qubit.readout_channel
            ]

        # for target qubit
        pi_width = half_pi_pulse(target_qubit).width
        target_acq_pulse = generate_measure_pulse(target_qubit, "Square")
        self.run_options.multiple_measure_options.update(
            {
                target_qubit: QDict(
                    measure_delay=[pi_width * 2 + delay],
                    waveform=[target_acq_pulse],
                )
            }
        )

    @staticmethod
    def set_xy_pulses(builder):
        qubit = builder.target_unit
        delay = builder.experiment_options.delay
        phase_list = builder.experiment_options.phase_list
        bias_init_state = builder.experiment_options.bias_init_state

        xp2 = half_pi_pulse(qubit)()
        pulse2 = Constant(delay, 0, "XY")()
        pulse3 = half_pi_pulse(qubit)()
        pulse_list = []
        for phase in phase_list:
            xy_pulse = deepcopy(xp2) + pulse2 + deepcopy(pulse3)(phase=phase)
            pulse_list.append(xy_pulse)

        builder.play_pulse("XY", qubit, pulse_list)

        if bias_init_state:
            builder.play_pulse("XY", builder.bias_unit, pi_pulse(builder.bias_unit))

    def _metadata(self):
        metadata = super()._metadata()
        metadata.draw_meta.update(
            {
                "add_bq_readout": self.experiment_options.add_bq_readout,
                "scale_coef": self.experiment_options.scale_coef,
            }
        )
        return metadata


class McmRBSingle(RBSingle):
    """TODO"""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("add_bq", bool)
        options.set_validator("acq_pulse_params", dict)
        options.set_validator("acq_pulse_type", ["Clear", "Phase", "Square"])
        options.set_validator("first_trigger_delay", float)

        options.add_bq = False
        options.acq_pulse_params = {
            "Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100},
            "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100},
        }
        options.acq_pulse_type = "Square"
        options.first_trigger_delay = 100

        return options


class SingleShotExtend(SingleShot):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("acq_pulse_params", dict)
        options.set_validator("acq_pulse_type", ["Clear", "Phase", "Square"])
        options.set_validator("scale_coef", float)

        options.acq_pulse_params = {
            "Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100},
            "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100},
        }
        options.acq_pulse_type = "Clear"
        options.scale_coef = 1.0
        options.save_result = False

        return options

    def _check_options(self):
        super()._check_options()
        acq_pulse_params = self.experiment_options.acq_pulse_params
        acq_pulse_type = self.experiment_options.acq_pulse_type
        scale_coef = self.experiment_options.scale_coef
        acq_pulse = generate_measure_pulse(
            self.qubit, acq_pulse_type, acq_pulse_params, scale_coef
        )
        self.run_options.acq_pulse = acq_pulse
        self.run_options.analysis_class = SingleShotExtendAnalysis
        self.analysis_options.result_parameters.append("snr")

    def _alone_save_result(self):
        super()._alone_save_result()
        snr = self.analysis.results.snr.value
        self.file.save_data(np.array([snr]), name=f"{self}-snr")


class SingleShotSnr(SingleShotExtend):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("sample_width", float)
        options.sample_width = 100
        return options

    @staticmethod
    def update_instrument(builder):
        sample_width = builder.experiment_options.sample_width
        builder.inst.set_sample_width(builder.qubit.readout_channel, sample_width)


class CoherentPumpExperiment(TopExperimentV1):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            delays (List, np.ndarray): The list of delays that will
                                           be scanned in the experiment,
                                           in nanoseconds.
            z_amp (float): A ac pulse amplitude.
        """
        options = super()._default_experiment_options()

        options.set_validator("delays", list, limit_null=True)
        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("n", int)
        options.set_validator("delta_t", float)
        options.set_validator("amp_coff", float)

        options.delays = qarange(0, 24000, 200)
        options.z_amp = None
        options.n = 3
        options.delta_t = 30000
        options.amp_coff = 1.0

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.multiple_measure_options = None
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.delta_t = None
        return options

    def _check_options(self):
        super()._check_options()

        m_time = self.qubit.Mwave.width
        amp = self.qubit.Mwave.amp
        baseband_freq = self.qubit.Mwave.baseband_freq
        acq_pulse = AcquireSine(
            time=m_time, amp_list=[amp], baseband_freq_list=[baseband_freq]
        )()

        if not self.discriminator:
            raise ValueError("No find dcm!")
        self.set_experiment_options(data_type="I_Q")

        pi_width = pi_pulse(self.qubit).width
        delays = self.experiment_options.delays
        fist_measure_delay_delay = [round(pi_width + delay, 3) for delay in delays]
        measure_delay = [fist_measure_delay_delay]
        for i in range(self.experiment_options.n - 1):
            measure_delay.append(
                [
                    (i + 1) * self.experiment_options.delta_t + delay
                    for delay in fist_measure_delay_delay
                ]
            )

        self.set_run_options(
            x_data=self.experiment_options.delays,
            analysis_class=CoherentPumpAnalysis,
            multiple_measure_options={
                self.qubit: QDict(
                    measure_delay=measure_delay,
                    waveform=[acq_pulse],
                )
            },
            measure_modes=QubitMeasureMode(
                name=self.qubit.name,
                measure_total=self.experiment_options.n,
                measure_num_list=list(range(self.experiment_options.n)),
                base_label_list=["" for _ in range(self.experiment_options.n)],
            ),
        )

    def _metadata(self) -> MetaData:
        meta_data = super()._metadata()
        meta_data.process_meta.update(
            dict(delta_t=self.experiment_options.delta_t, n=self.experiment_options.n)
        )
        return meta_data

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulses."""
        pulses = CoherentPumpExperiment.get_xy_pulse(self.qubit, self.experiment_options)
        self.play_pulse("XY", self.qubit, pulses)

    @staticmethod
    def get_xy_pulse(qubit, experiment_options):
        """Get XY pulse list."""
        pulse = pi_pulse(qubit)()
        rate_pi_pulse = pi_pulse(qubit)(amp=qubit.XYwave.Xpi * experiment_options.amp_coff)
        pi_width = pulse.width

        delta_t = experiment_options.delta_t
        n = experiment_options.n
        tail = round(delta_t - pi_width, 3)

        xy_pulse = Constant(0, 0, "XY")()
        for i in range(n):
            xy_pulse += pulse if not i else rate_pi_pulse
            xy_pulse += Constant(tail, 0, "XY")()

        return xy_pulse


class CoherentPumpExperimentV2(TopExperimentV1):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            delays (List, np.ndarray): The list of delays that will
                                           be scanned in the experiment,
                                           in nanoseconds.
            z_amp (float): A ac pulse amplitude.
        """
        options = super()._default_experiment_options()

        options.set_validator("delays", list, limit_null=True)
        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("n", int)
        options.set_validator("delta_t", float)
        options.set_validator("amp_coff", float)

        options.delays = qarange(0, 24000, 200)
        options.z_amp = None
        options.n = 3
        options.delta_t = 30000
        options.amp_coff = 1.0

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.multiple_measure_options = None
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.delta_t = None
        return options

    def _check_options(self):
        super()._check_options()

        if not self.discriminator:
            raise ValueError("No find dcm!")
        self.set_experiment_options(data_type="I_Q")

        delta_t = self.experiment_options.delta_t
        delays = self.experiment_options.delays
        n = self.experiment_options.n
        x_data = [round(delta_t * i + delay, 3) for i in range(n) for delay in delays]

        self.set_run_options(
            x_data=x_data,
            analysis_class=CoherentPumpAnalysisV2,
        )

    @staticmethod
    def update_instrument(self):
        self.sweep_readout_trigger_delay(self.qubit.readout_channel, self.run_options.widths)

    def _metadata(self) -> MetaData:
        meta_data = super()._metadata()
        meta_data.process_meta.update(
            dict(delta_t=self.experiment_options.delta_t, n=self.experiment_options.n)
        )
        return meta_data

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulses."""
        pulses, widths = CoherentPumpExperimentV2.get_xy_pulse(self.qubit, self.experiment_options)
        self.run_options.widths = widths
        self.play_pulse("XY", self.qubit, pulses)

    @staticmethod
    def get_xy_pulse(qubit, experiment_options):
        """Get XY pulse list."""
        pulse = pi_pulse(qubit)()
        rate_pi_pulse = pi_pulse(qubit)(amp=qubit.XYwave.Xpi * experiment_options.amp_coff)
        pi_width = pulse.width

        delays = experiment_options.delays
        delta_t = experiment_options.delta_t
        n = experiment_options.n
        tail = round(delta_t - pi_width, 3)
        
        xy_pulse_list = []
        widths = []
        for i in range(n):
            head_pulse = deepcopy(pulse)
            if i > 0:
                for j in range(i):
                    head_pulse += Constant(tail, 0, "XY")()
                    head_pulse += rate_pi_pulse
            for delay in delays:
                xy_pulse = deepcopy(head_pulse)
                xy_pulse += Constant(delay, 0, "XY")()
                xy_pulse_list.append(xy_pulse)
                widths.append(xy_pulse.width)

        return xy_pulse_list, widths


class CoherentPumpT1(TopExperimentV1):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            delays (List, np.ndarray): The list of delays that will
                                           be scanned in the experiment,
                                           in nanoseconds.
        """
        options = super()._default_experiment_options()

        options.set_validator("delays", list, limit_null=True)
        options.set_validator("n", int)
        options.set_validator("delta_t", float)
        options.set_validator("amp_coff", float)
        options.set_validator("offset", float)

        options.delays = qarange(0, 24000, 200)
        options.n = 3
        options.delta_t = 30000
        options.amp_coff = 1.0
        options.offset = 1000

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.multiple_measure_options = None
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("quality_bounds", list)
        options.quality_bounds = [0.9, 0.85, 0.77]
        return options

    def _check_options(self):
        super()._check_options()

        if not self.discriminator:
            raise ValueError("No find dcm!")
        self.set_experiment_options(data_type="I_Q")

        self.set_run_options(
            x_data=self.experiment_options.delays,
            analysis_class=T1AnalysisV2,
        )

    @staticmethod
    def update_instrument(self):
        self.sweep_readout_trigger_delay(self.qubit.readout_channel, self.run_options.widths)

    def _metadata(self) -> MetaData:
        meta_data = super()._metadata()
        meta_data.process_meta.update(
            dict(delta_t=self.experiment_options.delta_t, n=self.experiment_options.n)
        )
        return meta_data

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulses."""
        pulses, widths = CoherentPumpT1.get_xy_pulse(self.qubit, self.experiment_options)
        self.run_options.widths = widths
        self.play_pulse("XY", self.qubit, pulses)

    @staticmethod
    def get_xy_pulse(qubit, experiment_options):
        """Get XY pulse list."""
        std_pi_pulse = pi_pulse(qubit)()
        rate_pi_pulse = pi_pulse(qubit)(
            amp=qubit.XYwave.Xpi * experiment_options.amp_coff
        )

        delays = experiment_options.delays
        delta_t = experiment_options.delta_t
        n = experiment_options.n

        xy_pulse_list = []
        widths = []

        head_pulse = Constant(0, 0, "XY")()
        for i in range(n):
            head_pulse += std_pi_pulse if i == 0 else rate_pi_pulse
            head_pulse += Constant(experiment_options.offset, 0, "XY")() if i == n -1 else Constant(delta_t, 0, "XY")()
        head_pulse += std_pi_pulse

        for delay in delays:
            xy_pulse = deepcopy(head_pulse)
            xy_pulse += Constant(delay, 0, "XY")()
            xy_pulse_list.append(xy_pulse)
            widths.append(xy_pulse.width)

        return xy_pulse_list, widths
