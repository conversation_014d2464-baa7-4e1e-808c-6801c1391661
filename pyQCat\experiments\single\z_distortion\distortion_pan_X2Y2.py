# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/17
# __author:       <PERSON><PERSON><PERSON>


from copy import deepcopy

import numpy as np

from ....analysis.library.distortion_pan_X2Y2_analysis import (
    DistortionPhaseTomoAnalysis,
)
from ....concurrent.worker import ExperimentProtocolBuilder
from ....log import pyqlog
from ....pulse import Constant
from ....pulse.pulse_function import half_pi_pulse
from ....structures import MetaData, Options
from ....tools.find import cal_fidelity
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class DistortionPhaseTomo(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("drag_gap", float)
        options.set_validator("t1", float)
        options.set_validator("t2", float)
        options.set_validator("delays", list)
        options.set_validator("z_amp", float)
        options.set_validator("scheme", ["pan", "dicarlo"])
        options.set_validator("exp_mode", ["half_square", "full_square"])
        options.set_validator("diy_readout_point", bool)
        options.set_validator("base_num", int)

        options.drag_gap = 100
        options.t1 = 1000
        options.t2 = 1000
        options.delays = None
        options.z_amp = 0
        # options.show_result = False
        options.scheme = "pan"
        options.diy_readout_point = False
        options.exp_mode = "half_square"
        options.base_num = 2
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        # options.correct_read = True
        options.data_key = ["None"]
        options.fidelity_matrix = None
        options.ana_scheme = "pan"
        options.dicarlo_ref_time = 100
        options.avg_num = 1
        options.base_num = 2
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.phases = [0, np.pi / 2]
        # options.phases = [np.pi / 2, np.pi]
        options.injection_func = [
            "get_xy_pulse",
            "get_xy_dicarlo_pulse",
            "get_z_pulse",
            "get_z_dicarlo_pulse",
        ]
        return options

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.draw_meta = {
            "drag_gap": (self.experiment_options.drag_gap, "ns"),
            "drag_width": (self.qubit.XYwave.time, "ns"),
            "z_amp": (self.experiment_options.z_amp, "V"),
            "exp_mode": self.experiment_options.exp_mode,
        }
        return metadata

    @staticmethod
    def set_xy_pulses(builder: ExperimentProtocolBuilder):
        if builder.experiment_options.scheme == "pan":
            xy_pulse = builder.get_xy_pulse(builder.qubit)
        elif builder.experiment_options.scheme == "dicarlo":
            xy_pulse = builder.get_xy_dicarlo_pulse(builder.qubit)
        else:
            raise ValueError(
                f"scheme {builder.experiment_options.scheme} is not supported."
            )
        builder.play_pulse("XY", builder.qubit, xy_pulse)

    @staticmethod
    def set_z_pulses(builder: ExperimentProtocolBuilder):
        if builder.experiment_options.scheme == "pan":
            z_pulse = builder.get_z_pulse(builder.qubit)
        elif builder.experiment_options.scheme == "dicarlo":
            z_pulse = builder.get_z_dicarlo_pulse(builder.qubit)
        else:
            raise ValueError(
                f"scheme {builder.experiment_options.scheme} is not supported."
            )
        builder.play_pulse("Z", builder.qubit, z_pulse)

    @staticmethod
    def update_instrument(builder: ExperimentProtocolBuilder):
        if builder.experiment_options.scheme == "pan":
            sweep_delay = builder._pulse_time_list[
                : len(builder.experiment_options.delays)
                * builder.experiment_options.base_num
            ]
            builder.sweep_readout_trigger_delay(
                builder.qubit.readout_channel, sweep_delay
            )

    def set_data_analysis_class(self):
        self.set_run_options(
            x_data=np.repeat(
                self.experiment_options.delays, self.experiment_options.base_num
            ),
            analysis_class=DistortionPhaseTomoAnalysis,
        )

    def _check_options(self):
        super()._check_options()

        self.qubit.XYwave.offset = 0
        pyqlog.warning(f"XYwave.offset is forced to set to zero.")

        if self.discriminator is None:
            raise ValueError(f"Must use dcm to get p0/p1")

        base_num = self.experiment_options.base_num
        assert base_num % 2 == 0, "Only support even base_num."
        phases = [0, np.pi / 2, np.pi, -np.pi / 2][:base_num]
        self.set_run_options(phases=phases)
        self.set_analysis_options(base_num=base_num)

        fidelity_matrix = cal_fidelity(self.discriminator)
        pyqlog.info(f"{self.qubit.name} fidelity_matrix:\n{fidelity_matrix}")
        # if self.experiment_options.correct_read:
        #     self.set_run_options(fidelity_matrix=fidelity_matrix)
        self.set_analysis_options(
            fidelity_matrix=fidelity_matrix,
            ana_scheme=self.experiment_options.scheme,
        )
        if self.analysis_options.ac_spectrum is None:
            self.analysis_options.ac_spectrum = self.qubit.ac_spectrum.standard

        # self.set_analysis_options(correct_read=self.experiment_options.correct_read)
        self.set_experiment_options(data_type="I_Q")

        if self.experiment_options.scheme == "pan":
            self._label = f"DistortionX2Y2"
            z_amp = self.experiment_options.z_amp
            diy_readout_point = self.experiment_options.diy_readout_point

            if self.experiment_options.exp_mode == "half_square":
                for qc in self.compensates.keys():
                    if qc.name == self.qubit.name:
                        qc.ac = 0.0
                        if diy_readout_point is False:
                            qc.readout_point_model = "Constant"
                            qc.readout_point = Options(amp=z_amp)
                        else:
                            qc.readout_point_model = "Constant"
                            qc.readout_point = Options(
                                amp=qc.readout_point.amp + self.experiment_options.z_amp
                            )

                        pyqlog.info(
                            f"target: {self.qubit},"
                            f"diy_readout_point: {diy_readout_point}, "
                            f"ac: {qc.ac} V, "
                            f"readout_point_model: {qc.readout_point_model}, "
                            f"readout_point.amp: {qc.readout_point.amp} V"
                        )

                if self.ac_bias.get(self.qubit.name):
                    old_awg_bias = self.ac_bias[self.qubit.name][1]
                    self.ac_bias[self.qubit.name][1] = old_awg_bias - z_amp
        elif self.experiment_options.scheme == "dicarlo":
            self._label = f"DistortionX2Y2Dicarlo"
        else:
            raise ValueError(
                f"scheme {self.experiment_options.scheme} is not supported."
            )
        self.set_data_analysis_class()

    def get_xy_pulse(self, qubit):
        t1 = self.experiment_options.t1
        delays = self.experiment_options.delays
        drag_gap = self.experiment_options.drag_gap
        t2 = self.experiment_options.t2
        phases = self.run_options.phases
        exp_mode = self.experiment_options.exp_mode

        pulses = []
        for delay in delays:
            for phase in phases:
                pulse0 = Constant(t1, 0, name="XY")
                pulse1 = Constant(delay, 0, name="XY")
                pulse2 = half_pi_pulse(qubit)
                pulse3 = Constant(drag_gap, 0, name="XY")
                # pulse4 = Rphi_gate(phase=phase).to_pulse(self.qubit)
                pulse4 = deepcopy(pulse2)
                pulse5 = Constant(t2, 0, name="XY")

                if exp_mode == "half_square":
                    pulse = (
                        pulse0()
                        + pulse1()
                        + pulse2()
                        + pulse3()
                        + pulse4(phase=phase)
                        + pulse5()
                    )
                elif exp_mode == "full_square":
                    pulse = (
                        deepcopy(pulse0())
                        + pulse5()
                        + pulse1()
                        + pulse2()
                        + pulse3()
                        + pulse4(phase=phase)
                        + pulse0()
                    )
                else:
                    raise ValueError(f"exp_mode {exp_mode} is not supported.")

                pulses.append(pulse)

        return pulses

    def get_z_pulse(self, qubit):
        t1 = self.experiment_options.t1
        delays = self.experiment_options.delays
        drag_gap = self.experiment_options.drag_gap
        t2 = self.experiment_options.t2
        drag_time = half_pi_pulse(qubit).width
        z_amp = self.experiment_options.z_amp
        exp_mode = self.experiment_options.exp_mode
        phases = self.run_options.phases

        pulses = []
        delays_repeat = np.repeat(delays, len(phases))
        for delay in delays_repeat:
            pulse0 = Constant(t1, 0)()
            if exp_mode == "half_square":
                pulse = Constant(delay + drag_time * 2 + drag_gap + t2, z_amp)()
                zpulse = deepcopy(pulse0) + pulse
            elif exp_mode == "full_square":
                pulse1 = Constant(t2, z_amp)()
                pulse2 = Constant(delay + drag_time * 2 + drag_gap, 0)()
                zpulse = deepcopy(pulse0) + pulse1 + pulse2 + pulse0
            else:
                raise ValueError(f"exp_mode {exp_mode} is not supported.")

            pulses.append(zpulse)
        return pulses

    def get_xy_dicarlo_pulse(self, qubit):
        t1 = self.experiment_options.t1
        delays = self.experiment_options.delays
        drag_gap = self.experiment_options.drag_gap
        t2 = self.experiment_options.t2
        phases = self.run_options.phases

        pulses = []
        for phase in phases:
            pulse0 = Constant(t1, 0, name="XY")
            pulse1 = half_pi_pulse(qubit)
            pulse2 = Constant(drag_gap, 0, name="XY")
            pulse3 = deepcopy(pulse1)
            pulse4 = Constant(t2, 0, name="XY")

            pulse = pulse0() + pulse1() + pulse2() + pulse3(phase=phase) + pulse4()
            pulses.extend([deepcopy(pulse) for _ in range(len(delays))])

        return pulses

    def get_z_dicarlo_pulse(self, qubit):
        t1 = self.experiment_options.t1
        delays = self.experiment_options.delays
        drag_gap = self.experiment_options.drag_gap
        t2 = self.experiment_options.t2
        drag_time = half_pi_pulse(qubit).width
        z_amp = self.experiment_options.z_amp

        pulses = []
        delays_double = np.tile(delays, 2)
        for delay in delays_double:
            pulse0 = Constant(t1 + drag_time, 0)()
            pulse1 = Constant(delay, z_amp)()
            pulse2 = Constant(drag_gap - delay + drag_time + t2, 0)()

            pulse = pulse0 + pulse1 + pulse2
            pulses.append(pulse)

        return pulses

    def _alone_save_result(self):
        results = self.analysis.results

        if self.experiment_options.scheme == "pan":
            self.file.save_data(
                self.experiment_options.delays,
                *results.sigma_I.value,
                *results.sigma_Q.value,
                *results.phase.value,
                results.phase_unwrap.value,
                results.phase_std.value,
                name=f"{self}(distortion_delay_sigmaI_sigmaQ_phase_phase unwrap_phase_std)",
            )

        if self.experiment_options.scheme == "dicarlo":
            self.file.save_data(
                self.experiment_options.delays,
                results.sigma_x.value,
                results.sigma_y.value,
                results.phase.value,
                results.phase_unwrap.value,
                results.phase_net.value,
                name=f"{self}(distortion_delay_sigmax_sigmay_phase_phase unwrap_phase net)",
            )

            self.file.save_data(
                results.df_ref.value,
                results.offset_ref.value,
                name=f"{self}(df_offset_reference)",
            )


class DistortionDetunePhaseTomo(DistortionPhaseTomo):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("detune_list", list)
        options.set_validator("delay", float)
        options.detune_list = None
        options.delay = 100
        options.pop("delays")
        return options

    def _check_options(self):
        self.set_experiment_options(base_num=2)
        super()._check_options()
        self._label = "DistortionDetunePhaseTomo"
        self.qubit.XYwave.offset = 0
        pyqlog.warning("XYwave.offset is forced to set to zero.")

    def set_data_analysis_class(self):
        self.set_run_options(
            x_data=np.tile(
                self.experiment_options.detune_list, self.experiment_options.base_num
            ),
            analysis_class=DistortionPhaseTomoAnalysis,
        )

    @staticmethod
    def update_instrument(builder: ExperimentProtocolBuilder):
        pass

    def get_xy_pulse(self, qubit):
        t1 = self.experiment_options.t1
        delay = self.experiment_options.delay
        detune_list = self.experiment_options.detune_list
        drag_gap = self.experiment_options.drag_gap
        t2 = self.experiment_options.t2
        phases = self.run_options.phases
        exp_mode = self.experiment_options.exp_mode

        pulses = []
        for detune in detune_list:
            for phase in phases:
                # detune += qubit.XYwave.detune_pi2
                detune *= 1e-3
                # qubit.XYwave.detune_pi2 = detune
                pulse0 = Constant(t1, 0, name="XY")
                pulse1 = Constant(delay, 0, name="XY")
                pulse2 = half_pi_pulse(qubit)
                pulse3 = Constant(drag_gap, 0, name="XY")
                # pulse4 = Rphi_gate(phase=phase).to_pulse(self.qubit)
                pulse4 = deepcopy(pulse2)
                pulse5 = Constant(t2, 0, name="XY")

                if exp_mode == "half_square":
                    pulse = (
                        pulse0()
                        + pulse1()
                        + pulse2(detune=detune)
                        + pulse3()
                        + pulse4(phase=phase)
                        + pulse5()
                    )
                elif exp_mode == "full_square":
                    pulse = (
                        deepcopy(pulse0())
                        + pulse5()
                        + pulse1()
                        + pulse2(detune=detune)
                        + pulse3()
                        + pulse4(phase=phase)
                        + pulse0()
                    )
                else:
                    raise ValueError(f"exp_mode {exp_mode} is not supported.")

                pulses.append(pulse)

        return pulses

    def get_z_pulse(self, qubit):
        t1 = self.experiment_options.t1
        delay = self.experiment_options.delay
        drag_gap = self.experiment_options.drag_gap
        t2 = self.experiment_options.t2
        drag_time = half_pi_pulse(qubit).width
        z_amp = self.experiment_options.z_amp
        exp_mode = self.experiment_options.exp_mode

        pulse0 = Constant(t1, 0)()

        if exp_mode == "half_square":
            pulse = Constant(delay + drag_time * 2 + drag_gap + t2, z_amp)()
            zpulse = deepcopy(pulse0) + pulse
        elif exp_mode == "full_square":
            pulse1 = Constant(t2, z_amp)()
            pulse2 = Constant(delay + drag_time * 2 + drag_gap, 0)()
            zpulse = deepcopy(pulse0) + pulse1 + pulse2 + pulse0
        else:
            raise ValueError(f"exp_mode {exp_mode} is not supported.")

        return zpulse

    def _alone_save_result(self):
        results = self.analysis.results

        if self.experiment_options.scheme == "pan":
            self.file.save_data(
                self.experiment_options.detune_list,
                results.sigma_I.value,
                results.sigma_Q.value,
                results.phase.value,
                results.phase_unwrap.value,
                name=f"{self}(distortion_delay_sigmax_sigmay_phase_phase unwrap)",
            )

        if self.experiment_options.scheme == "dicarlo":
            self.file.save_data(
                self.experiment_options.detune_list,
                results.sigma_I.value,
                results.sigma_Q.value,
                results.phase.value,
                results.phase_unwrap.value,
                results.phase_net.value,
                name=f"{self}(distortion_delay_sigmax_sigmay_phase_phase unwrap_phase net)",
            )

            self.file.save_data(
                results.df_ref.value,
                results.offset_ref.value,
                name=f"{self}(df_offset_reference)",
            )
