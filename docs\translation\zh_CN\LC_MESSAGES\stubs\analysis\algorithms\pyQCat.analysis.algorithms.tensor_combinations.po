# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 19:06+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.tensor_combinations.rst:2
msgid "pyQCat.analysis.algorithms.tensor\\_combinations"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.tensor_combinations:1
msgid "Matrix tensor combinations"
msgstr "矩阵张量组合"

#: of pyQCat.analysis.algorithms.tomography.tensor_combinations
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.tensor_combinations:3
msgid "List of matrices to be merged"
msgstr "矩阵列表"

#: of pyQCat.analysis.algorithms.tomography.tensor_combinations:4
msgid "Number of mergers"
msgstr "重复次数"

#~ msgid "Example"
#~ msgstr ""

#~ msgid "Input:"
#~ msgstr ""

#~ msgid "matrices = [A1, A2, A3, A4], repeat = 2"
#~ msgstr ""

#~ msgid "Output:"
#~ msgstr ""

#~ msgid "matrices = ["
#~ msgstr ""

#~ msgid ""
#~ "A1*A1, A1*A2, A1*A3, A1*A4, A2*A1, "
#~ "A2*A2, A2*A3, A2*A4, A3*A1, A3*A2, "
#~ "A3*A3, A3*A4, A4*A1, A4*A2, A4*A3, A4*A4"
#~ msgstr ""

#~ msgid "]"
#~ msgstr ""

