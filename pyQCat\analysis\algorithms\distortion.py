# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/05
# __author:       SS Fang

"""
Distortion data process function.

"""

import traceback
import warnings
from copy import deepcopy
from typing import List, Tuple
from pyQCat.structures import CommonDict


import numpy as np
import scipy.signal as ss
from numpy import asfarray
from scipy.fftpack import fft, ifft
from scipy.interpolate import interp1d
from scipy.optimize import OptimizeResult
from scipy.optimize import curve_fit

try:
    from scipy.optimize.optimize import (
        _check_unknown_options,
        OptimizeWarning,
        _status_message,
    )
except ImportError:

    class OptimizeWarning(UserWarning):
        pass

    def _check_unknown_options(unknown_options):
        if unknown_options:
            msg = ", ".join(map(str, unknown_options.keys()))
            # Stack level 4: this is called from _minimize_*, which is
            # called from another function in SciPy. Level 4 is the first
            # level in user code.
            warnings.warn("Unknown solver options: %s" % msg, OptimizeWarning, 4)

    # standard status messages of optimizers
    _status_message = {
        "success": "Optimization terminated successfully.",
        "maxfev": "Maximum number of function evaluations has " "been exceeded.",
        "maxiter": "Maximum number of iterations has been " "exceeded.",
        "pr_loss": "Desired error not necessarily achieved due " "to precision loss.",
        "nan": "NaN result encountered.",
        "out_of_bounds": "The result is outside of the provided " "bounds.",
    }


from ...errors import AnalysisFieldError
from ...log import pyqlog
from ...pulse import FlatTopGaussian, Constant, PulseComponent
from ...tools.NM_bnd import wrap_function


def calculate_offset_arr(
    response_arr: np.ndarray, z_amp: float = -0.5, cal_response_mode: str = "add"
) -> np.ndarray:
    """Calculate z offset array, by response array.

    Args:
        response_arr (np.array): Array of response, distortion origin response.
        z_amp (float): When set z line amp value.
        cal_response_mode (str): Calculate response mode, `add` or `response`.

    Returns:
        offset_arr (np.ndarray): Array of z offset.

    """
    if cal_response_mode == "add":
        normal_offset_arr = response_arr - 1
    elif cal_response_mode == "reduce":
        normal_offset_arr = 1 - response_arr
    else:
        raise AnalysisFieldError(
            "cal_response_mode", cal_response_mode, expect=["add", "response"]
        )

    if z_amp == 0:
        offset_arr = normal_offset_arr
    else:
        offset_arr = normal_offset_arr * z_amp
    return offset_arr


def calculate_distortion(
    dt_list: List[np.ndarray], so_list: List[np.ndarray], sample_rate: float = 1.6
) -> Tuple:
    """Calculate distortion delay array, response array.

    Args:
        dt_list (List): List of delay array.
        so_list (List): List of response array.
        sample_rate (float): Sample rate, unit: GHz.

    Returns:
        Tuple: Tuple object, (delay_arr, response_arr).
    """
    Width2 = np.max(np.hstack(dt_list))
    Width1 = 100
    N1 = int(Width1 * sample_rate) + 1
    N2 = int(Width2 * sample_rate) + 1
    N = N1 + N2
    NFFT = 2 * N
    Si = np.ones(
        N2,
    )
    S0 = np.zeros(
        N1,
    )
    h_f = np.ones(NFFT, dtype="complex128")

    for t_seq, amp_seq in zip(dt_list, so_list):
        func = interp1d(t_seq, amp_seq, kind="cubic", fill_value="extrapolate")
        t_min = np.min(t_seq)
        t_max = np.max(t_seq)
        t_width = t_max - t_min
        t2 = np.linspace(t_min, t_max, int(t_width * sample_rate) + 1)
        So = func(t2)
        N3 = len(So)
        if N3 < N2:
            So = np.hstack([np.zeros(N1), So, So[-1] * np.ones(N2 - N3)])
        else:
            So = np.hstack([np.zeros(N1), So])
        h = np.hstack([0, np.diff(So)])
        h = h / np.sum(h)
        h_f *= fft(h, NFFT)

    m = len(dt_list) - 1
    h = ifft(h_f, NFFT)
    h = np.real(h[m * N1 : N + m * N1])
    h = h / np.sum(h)
    So = np.convolve(h, np.hstack([S0, Si]))
    So = So[N1 : N1 + N]

    response_arr = So[N1:]
    delay_arr = np.arange(0, Width2 + 1 / sample_rate, 1 / sample_rate)
    return delay_arr, response_arr


def calculate_lfilter_paras(*parameters) -> Tuple:
    """Calculate paras of lfilter,
    by pole model(complex_pole_temp) fit parameters.

    Args:
        *parameters: Pole model fit parameters.

    Returns:
        (a, b): Paras of lfilter.
    """
    (
        C1_1,
        p1_1,
        C1_2,
        p1_2,
        C2_1,
        p2_1,
        phip_1,
        phir_1,
        C2_2,
        p2_2,
        phip_2,
        phir_2,
    ) = parameters

    C0 = 1 - (C1_1 + C1_2 + C2_1 + C2_2)
    C_list = [C0]

    P_list = [
        p1_1,
        p1_2,
        p2_1 * np.exp(1j * phip_1),
        p2_1 * np.exp(-1j * phip_1),
        p2_2 * np.exp(1j * phip_2),
        p2_2 * np.exp(-1j * phip_2),
    ]

    rl_list = []
    for p2, phip, phir in [(p2_1, phip_1, phir_1), (p2_2, phip_2, phir_2)]:
        rl = (1 + p2**2 - 2 * p2 * np.cos(phip)) / (
            2 * np.cos(phir) - 2 * p2 * np.cos(phir - phip)
        )
        rl_list.append(rl)

    rl_1, rl_2 = rl_list

    R_list = [
        (1 - p1_1) * C1_1,
        (1 - p1_2) * C1_2,
        rl_1 * np.exp(1j * phir_1) * C2_1,
        rl_1 * np.exp(-1j * phir_1) * C2_1,
        rl_2 * np.exp(1j * phir_2) * C2_2,
        rl_2 * np.exp(-1j * phir_2) * C2_2,
    ]

    b, a = ss.invres(R_list, P_list, C_list)
    a = np.real(a)
    b = np.real(b)
    return a, b


def create_verify_pulse(num: int = 10, sample_rate: float = 1.6) -> PulseComponent:
    """Create an ideal pulse, may be used to verify distortion.

    Args:
        num (int): Model pulse repeat times.
        sample_rate (float): Sample rate, unit: GHz.

    Returns:
        target_pulse (PulseComponent): PulseComponent object.
    """
    head_pulse = Constant(time=30, amp=0)
    center_pulse = FlatTopGaussian(time=60, amp=0.5)
    tail_pulse = Constant(time=30, amp=0)

    for pulse in [head_pulse, center_pulse, tail_pulse]:
        pulse.sample_rate = sample_rate
        pulse._lost_width = pulse.width % (1 / pulse.sample_rate)
    model_pulse = head_pulse() + center_pulse() + tail_pulse()

    target_pulse = model_pulse * num
    return target_pulse


def calculate_pre_distortion_wave(
    delay_arr: np.ndarray,
    response_arr: np.ndarray,
    ideal_wave: np.ndarray,
    sample_rate: float = 1.6,
) -> np.ndarray:
    """Calculate pre distortion wave, by response and ideal wave.

    Args:
        delay_arr (np.ndarray): Collect distortion data delay array.
        response_arr (np.ndarray): Collect distortion data response array.
        ideal_wave (np.ndarray): Ideal wave array.
        sample_rate (float): Sample rate.

    Returns:
        pre_distortion_wave (np.ndarray): Array of pre distortion wave.

    """
    func = interp1d(delay_arr, response_arr, kind="cubic", fill_value="extrapolate")

    width1 = 100
    width2 = np.max(delay_arr)
    width3 = np.min(delay_arr)
    width = width2 - width3

    N1 = int(width1 * sample_rate) + 1
    N2 = int(width2 * sample_rate) + 1
    t2 = np.linspace(width3, width2, int(width * sample_rate) + 1)
    Si = np.hstack([np.zeros(N1), np.ones(N2)])
    N = len(Si)
    NFFT = 2 * N
    Si_f = fft(Si, NFFT)
    So = np.hstack([np.zeros(N1), func(t2)])
    h = np.hstack([0, np.diff(So)])
    h_f = fft(h, NFFT)

    y = ideal_wave
    N3 = len(y)

    if N3 < N2:
        So_ideal = np.hstack([np.zeros(N1), y, np.zeros(N2 - N3)])
    else:
        h = np.hstack([h, np.zeros(N3 - N2)])
        N2 = N3
        N = N1 + N2
        NFFT = 2 * N
        So_ideal = np.hstack([np.zeros(N1), y])
        Si = np.hstack([np.zeros(N1), np.ones(N2)])
        Si_f = fft(Si, NFFT)
        h_f = fft(h, NFFT)

    h_ideal = np.hstack([0, np.diff(So_ideal)])
    h_f_ideal = fft(h_ideal, NFFT)
    h_inv_f = h_f_ideal / h_f
    Si1_f = Si_f * h_inv_f
    Si1 = ifft(Si1_f, NFFT)
    Si1 = np.real(Si1[0:N])

    distortion_wave = Si1[N1 : N1 + N3]
    return distortion_wave


def calculate_simulation_wave(
    pre_distortion_wave: np.ndarray, response_arr: np.ndarray
) -> np.ndarray:
    """Calculate simulation wave of system output, by distortion response.

    Args:
        pre_distortion_wave (np.ndarray): Array of pre distortion wave.
        response_arr (np.ndarray): Distortion data response array.

    Returns:
        simulation_wave (np.ndarray): Array of system output simulation.
    """
    N1 = 100
    N = len(pre_distortion_wave)

    So = np.hstack([np.zeros(N1), response_arr])
    h = np.hstack([0, np.diff(So)])

    simulation_wave = np.convolve(h, pre_distortion_wave)
    simulation_wave = simulation_wave[N1 : N1 + N]
    return simulation_wave


def get_poles_bounds_p0(
    temp_mode: str,
    real_poles_num: int,
    complex_poles_num: int,
    data: CommonDict,
    response_type: str = "rise",
) -> Tuple:
    """Get poles model bounds, and fit initial parameters p0.

    Args:
        temp_mode (str): Support temperature mode, `room_temp` or `low_temp`.
        real_poles_num (int): Real pole model number.
        complex_poles_num (int): Complex pole model number.
        data (dict): Get bounds and p0 from data.
            data format normal like:
            >>>{
            >>>    "room_temp": {
            >>>        "C0": [0.0, 1.0, 0.96],
            >>>        "real_poles": [
            >>>            {
            >>>                "Cr": [],
            >>>                "tao": [0.01, 30000.0, 2.0]
            >>>            },
            >>>            {
            >>>                "Cr": [-0.05, 0.06, 0.02],
            >>>                "tao": [0.01, 30000.0, 2.0]
            >>>            },
            >>>            ...
            >>>        ],
            >>>        "complex_poles": [
            >>>            {
            >>>                "Cc": [0.0, 0.06, 0.02],
            >>>                "phir": [0.0, 6.2832, 1.57],
            >>>                "tao": [0.01, 30000.0, 80.0],
            >>>                "tos": [0.01, 30000.0, 300.0]
            >>>            },
            >>>            ...
            >>>        ],
            >>>    },
            >>>    "low_temp": {}  # similar `room_temp` field
            >>>}

        response_type (str): The response type, `rise` or `rise_and_fall` .

    Returns:
        tuple: (params, lb, ub, p0)

    """
    if temp_mode not in ["room_temp", "low_temp"]:
        raise AnalysisFieldError(
            "temp_mode", temp_mode, expect=["room_temp", "low_temp"]
        )

    target_data = data.get(temp_mode)
    c0_lb, c0_ub, c0_p0 = target_data.get("C0")
    real_poles = target_data.get("real_poles")
    complex_poles = target_data.get("complex_poles")

    if real_poles_num < 0 or complex_poles_num < 0:
        raise AnalysisFieldError(
            key="real/complex poles num",
            value=(real_poles_num, complex_poles_num),
            msg=f"Error: Pole model number error! "
            f"Real or Complex number must be a natural number, "
            f"the real n {real_poles_num} complex n {complex_poles_num} ! ",
        )
    elif real_poles_num > len(real_poles):
        raise AnalysisFieldError(
            key="real_poles_num",
            value=real_poles_num,
            msg=f"max real_poles {len(real_poles)}, but set real_num {real_poles_num}!",
        )
    elif complex_poles_num > len(complex_poles):
        raise AnalysisFieldError(
            key="complex_poles_num",
            value=complex_poles_num,
            msg=f"max complex_poles {len(complex_poles)}, but set complex_num {complex_poles_num}!",
        )

    params = ["C0"]
    lb = [c0_lb]
    ub = [c0_ub]
    p0 = [c0_p0]
    for i in range(real_poles_num):
        one_real_params = real_poles[i]
        for key, value in one_real_params.items():
            # normal, the first real poles no `Cr` value
            if i == 0 and key == "Cr":
                continue
            r_lb, r_ub, r_p0 = value
            lb.append(r_lb)
            ub.append(r_ub)
            p0.append(r_p0)
            params.append(f"R_{key}_{i}")

    for j in range(complex_poles_num):
        one_complex_params = complex_poles[j]
        for key, value in one_complex_params.items():
            c_lb, c_ub, c_p0 = value
            lb.append(c_lb)
            ub.append(c_ub)
            p0.append(c_p0)
            params.append(f"C_{key}_{j}")

    if response_type == "rise_and_fall":
        # when response rise_and_fall, add normalization factor
        n_lb, n_ub, n_p0 = [0.9, 1.0, 0.999]
        lb.append(n_lb)
        ub.append(n_ub)
        p0.append(n_p0)
        params.append(f"normalization")

    return params, lb, ub, p0


def generate_ab_from_poles_params_dict(
    poles_params_dict: CommonDict,
    real_poles_num: int,
    complex_poles_num: int,
    check_ab: bool = True,
    ts: float = 0.625,
) -> Tuple:
    """Calculate system a, b value by poles model params dict.

    Args:
        poles_params_dict (dict): Poles model params dict.
        real_poles_num (int): Real pole model number.
        complex_poles_num (int): Complex pole model number.
        check_ab (bool): Is or not check a, b .
        ts (float): The response data sample rate, aio or oscilloscope.

    Returns:
        (a, b): The system IIR a, b value.

    """
    from pyQCat.analysis.fit.fit_models import compute_r, get_from_equation

    # compute a,b from poles_model_params_dic
    C0 = poles_params_dict["C0"][0]
    Clist = [C0]
    Rlist = []
    plist = []
    for i in list(range(real_poles_num)):
        Creal, pk = poles_params_dict["real_poles"][i]
        plist.append(pk)
        Rlist.append((1 - pk) * Creal)
    for j in list(range(complex_poles_num)):
        Ccomp, phir, p, phip = poles_params_dict["complex_poles"][j]
        r = compute_r(p, phip, phir)
        plist.append(p * np.exp(1j * phip))
        Rlist.append(r * np.exp(1j * phir) * Ccomp)
        plist.append(p * np.exp(-1j * phip))
        Rlist.append(r * np.exp(-1j * phir) * Ccomp)

    b, a = ss.invresz(Rlist, plist, Clist, tol=1e-10)
    a = np.real(a)
    b = np.real(b)

    pyqlog.info(
        "sum(b)={:.8f},sum(a)={:.8f},sum(b)/sum(a)={:.8f}".format(
            np.sum(b) * 1e9, np.sum(a) * 1e9, np.sum(b) / np.sum(a)
        )
    )

    if check_ab is True:
        step_response_tmax = 30000
        N = int(step_response_tmax / ts) + 1
        x = np.ones(N)
        step_response_t = np.arange(0, N, 1) * ts
        y_ab = ss.lfilter(b, a, x)
        y_equation = get_from_equation(step_response_t, poles_params_dict, ts)
        pyqlog.info(f"Error max is {np.max(np.abs(y_ab - y_equation))}")
        if np.max(np.abs(y_ab - y_equation)) > 1e-5:
            pyqlog.warning("Exist error when compute ab")

    return a, b


def get_ab_full(
    a_iir: np.ndarray, b_iir: np.ndarray, fir_residual: np.ndarray
) -> Tuple:
    """Calculate complex system a, b of IIR and FIR.

    NOTICE:
        The sum of a IIR system and a FIR system
        Clist_FIR=[deltaC00,C01,C02,C03,C04,C05,C06,……C032]
        The last point of fir_residual must be zero

    Args:
        a_iir (array): IIR a value.
        b_iir (array): IIR b value.
        fir_residual (array): Residual of FIR response.

    Returns:
        (a_full, b_full): a, b value.

    """
    Clist_FIR = np.diff(fir_residual)
    residues, poles, klist = ss.residuez(b_iir, a_iir, tol=1e-10)

    if len(klist) > len(Clist_FIR):
        Clist = deepcopy(klist)
        Clist[: len(Clist_FIR)] = Clist[: len(Clist_FIR)] + Clist_FIR
    else:
        Clist = deepcopy(Clist_FIR)
        Clist[: len(klist)] = Clist[: len(klist)] + klist

    b, a = ss.invresz(residues, poles, Clist, tol=1e-10)
    a_full = np.real(a)
    b_full = np.real(b)

    return a_full, b_full


def real_list2complex_list(real_list, complex_pairs):
    complex_list = np.complex128(real_list)
    for i in list(range(complex_pairs)):
        real = real_list[-i * 2 - 2]
        imag = real_list[-i * 2 - 1]
        complex_list[-i * 2 - 1] = real + 1j * imag
        complex_list[-i * 2 - 2] = real - 1j * imag
    return complex_list


def complex_list2_real_list(complex_list):
    """
    Complex numbers appear in pairs in the form of conjugate complex numbers.
    """
    complex_list = np.complex128(complex_list)
    real_part = np.real(complex_list)
    imag_part = np.imag(complex_list)

    index_sort = np.argsort(np.abs(imag_part))
    real_part_sort = real_part[index_sort]
    imag_part_sort = imag_part[index_sort]
    # the number of complex number
    complex_number = np.sum((np.abs(imag_part) > 1e-6))
    complex_pairs = int(np.round(complex_number / 2))
    real_list = deepcopy(real_part_sort)

    for i in list(range(complex_pairs)):
        if (real_part_sort[-i * 2 - 1] == real_part_sort[-i * 2 - 2]) & (
            imag_part_sort[-i * 2 - 1] + imag_part_sort[-i * 2 - 2] == 0
        ):
            real_list[-i * 2 - 2] = real_part_sort[-i * 2 - 1]  # real
            real_list[-i * 2 - 1] = np.abs(imag_part_sort[-i * 2 - 1])  # imag
        else:
            raise AnalysisFieldError(
                key="method(complex_list2_real_list)",
                value="***",
                msg="Complex numbers don't appear in pairs in the form of conjugate complex numbers",
            )
    return real_list, complex_pairs


def RMSE(x, y1, y2):
    variances = list(map(lambda x, y: (x - y) ** 2, y1, y2))
    variance = np.sum(variances)
    rmse = np.sqrt(variance / len(x))
    # print(variance)
    return rmse


def curve_fit_step(x, y, p0, func, num, bounds):
    popt, rmse, fit_y = None, None, None
    p = p0
    for _ in range(num):
        popt, pocv = curve_fit(func, x, y, p, bounds=bounds, maxfev=100000)
        p = popt
        fit_y = func(x, *popt)
        rmse = RMSE(x, y, fit_y)
        if rmse <= 1e-3:
            break
    return popt, rmse, fit_y


def step_response_fit_using_zpk(
    zeros_num, poles_num, zeros_complex_pairs, poles_complex_pairs, amplification_factor
):
    def fit_function(x, *zpk):
        k = zpk[-1]
        zeros_real_list = zpk[0:zeros_num]
        poles_real_list = zpk[zeros_num : zeros_num + poles_num]
        zeros = real_list2complex_list(zeros_real_list, zeros_complex_pairs)
        poles = real_list2complex_list(poles_real_list, poles_complex_pairs)
        sos = ss.zpk2sos(zeros, poles, k)
        y = ss.sosfilt(sos, np.ones_like(x)) * amplification_factor
        return y

    return fit_function


def sosfilter_design_by_ZPK_optimization(
    t_arr: np.ndarray,
    response_arr: np.ndarray,
    a_full: np.ndarray,
    b_full: np.ndarray,
    amplification_factor: float,
):
    """ZPK Optimize."""
    # initial value
    zeros0, poles0, k0 = ss.tf2zpk(b_full, a_full)
    zeros_num = len(zeros0)
    poles_num = len(poles0)
    zeros0_real_list, zeros_complex_pairs = complex_list2_real_list(zeros0)
    poles0_real_list, poles_complex_pairs = complex_list2_real_list(poles0)

    # data fit
    start_point = np.hstack((zeros0_real_list, poles0_real_list, [k0]))
    lb = np.ones_like(start_point) * -1
    ub = np.ones_like(start_point)
    if k0 > 0:
        lb[-1] = k0 * 0.5
        ub[-1] = k0 * 2
    else:
        lb[-1] = k0 * 2
        ub[-1] = k0 * 0.5

    new_resp_arr = response_arr * amplification_factor
    fit_p = curve_fit_step(
        t_arr,
        new_resp_arr,
        start_point,
        step_response_fit_using_zpk(
            zeros_num,
            poles_num,
            zeros_complex_pairs,
            poles_complex_pairs,
            amplification_factor,
        ),
        10,
        bounds=(lb, ub),
    )
    zpk = fit_p[0]
    k = zpk[-1]
    zeros_real_list = zpk[0:zeros_num]
    poles_real_list = zpk[zeros_num : zeros_num + poles_num]
    zeros = real_list2complex_list(zeros_real_list, zeros_complex_pairs)
    poles = real_list2complex_list(poles_real_list, poles_complex_pairs)

    return zeros, poles, k


def sosfilter_design_using_ab(
    t_arr: np.ndarray,
    response_arr: np.ndarray,
    a_full: np.ndarray,
    b_full: np.ndarray,
    amplification_factor: float,
) -> Tuple:
    """If there is any error in the calculation of ab,
    it is corrected by optimization here, and finally the sos representation
    of the system and the predistortion digital filter is obtained.

    Args:
        t_arr (array): Sampling width t array about awg sample rate.
        response_arr (array): Fit array about t_arr.
        a_full (array): System a_full value of IIR and FIR.
        b_full (array): System b_full value of IIR and FIR.
        amplification_factor (float): The amplification coefficient value.

    Returns:
        (sos_digital_filter, sos_system): sos_digital_filter, sos_system

    """
    # transform directly and verify accuracy
    zeros, poles, k = ss.tf2zpk(b_full, a_full)
    sos_system = ss.zpk2sos(zeros, poles, k)
    x = np.ones_like(t_arr)
    error_max = np.max(np.abs(ss.sosfilt(sos_system, x) - response_arr))
    pyqlog.info(f"Error max before ZPK optimization is {error_max}")

    if error_max > 1e-5:
        pyqlog.warning("Exist error when compute sos of system directly")
        try:
            # error correction through optimization
            zeros, poles, k = sosfilter_design_by_ZPK_optimization(
                t_arr, response_arr, a_full, b_full, amplification_factor
            )
            sos_system = ss.zpk2sos(zeros, poles, k)
            error_max = np.max(np.abs(ss.sosfilt(sos_system, x) - response_arr))
            pyqlog.info(f"Error max after ZPK optimization is {error_max}")
            if error_max > 1e-5:
                pyqlog.warning("Failed to design sos filter after optimization")
        except Exception as err:
            err_msg = traceback.format_exc()
            pyqlog.warning(
                f"Design sos filter optimization error: {err}!\n"
                f"Detailed error message: {err_msg}"
            )
    else:
        pyqlog.info(f"Do not need to do ZPK optimization")

    sos_digital_filter = ss.zpk2sos(poles, zeros, 1 / k)

    return sos_digital_filter, sos_system


def create_ideal_wave(num: int = 10, ts: float = 0.625, amp: float = 0.5) -> Tuple:
    """Create an ideal wave, may be used to verify distortion.

    Args:
        num (int): Model pulse repeat times.
        ts (float): Sample period, unit: ns .
        amp (float): Center part pulse amp value.

    Returns:
        (t_arr, amp_arr): Pulse data, x: t_arr, y: amp_arr .

    """
    head_width = 15
    center_width = 30
    tail_width = 15

    head_amp_arr = np.zeros(int(head_width / ts) + 1)
    center_amp_arr = np.ones(int(center_width / ts) + 1) * amp
    tail_amp_arr = np.zeros(int(tail_width / ts) + 1)
    model_amp_arr = np.hstack((head_amp_arr[0:], center_amp_arr[1:], tail_amp_arr[1:]))

    amp_arr = np.array([])
    for n in range(num):
        if n == 0:
            amp_arr = np.hstack((amp_arr, model_amp_arr))
        else:
            amp_arr = np.hstack((amp_arr, model_amp_arr[1:]))

    all_width = num * (head_width + center_width + tail_width)
    all_points = int(all_width / ts) + 1
    t_arr = np.linspace(0, all_width, all_points)

    return t_arr, amp_arr


def gaussian_fir_low_pass_filter(order: int = 5, ts: float = 0.625) -> np.ndarray:
    """Get h of Gaussian filter.

    Args:
        order (int): The order of the Filter
        ts (float): Sample period, unit: ns .

    Returns:
        array: h value.

    """
    bw = 1 / ts / order  # 3 dB cut-off frequency
    pyqlog.info(f"order={order}, bandwidth is {bw * 1000} MHz")

    t = np.linspace(-1 / 2, 1 / 2, order + 1)
    alpha = np.sqrt(np.log(2) / 2)
    h = (np.sqrt(np.pi) / alpha) * np.exp(-((t * np.pi / alpha) ** 2))
    # Normalize coefficients
    h = h / sum(h)
    return h


async def _minimize_neldermead(
    func,
    x0,
    args=(),
    callback=None,
    nonzdelt=0.05,
    maxiter=None,
    maxfev=None,
    disp=False,
    return_all=False,
    initial_simplex=None,
    xatol=1e-4,
    fatol=1e-4,
    adaptive=False,
    bounds=None,
    **unknown_options,
):
    """
    Minimization of scalar function of one or more variables using the
    Nelder-Mead algorithm.

    Options
    -------
    disp : bool
        Set to True to print convergence messages.
    maxiter, maxfev : int
        Maximum allowed number of iterations and function evaluations.
        Will default to ``N*200``, where ``N`` is the number of
        variables, if neither `maxiter` or `maxfev` is set. If both
        `maxiter` and `maxfev` are set, minimization will stop at the
        first reached.
    return_all : bool, optional
        Set to True to return a list of the best solution at each of the
        iterations.
    initial_simplex : array_like of shape (N + 1, N)
        Initial simplex. If given, overrides `x0`.
        ``initial_simplex[j,:]`` should contain the coordinates of
        the jth vertex of the ``N+1`` vertices in the simplex, where
        ``N`` is the dimension.
    xatol : float, optional
        Absolute error in xopt between iterations that is acceptable for
        convergence.
    fatol : number, optional
        Absolute error in func(xopt) between iterations that is acceptable for
        convergence.
    adaptive : bool, optional
        Adapt algorithm parameters to dimensionality of problem. Useful for
        high-dimensional minimization [1]_.
    bounds : sequence or `Bounds`, optional
        Bounds on variables. There are two ways to specify the bounds:

            1. Instance of `Bounds` class.
            2. Sequence of ``(min, max)`` pairs for each element in `x`. None
               is used to specify no bound.

        Note that this just clips all vertices in simplex based on
        the bounds.

    References
    ----------
    .. [1] Gao, F. and Han, L.
       Implementing the Nelder-Mead simplex algorithm with adaptive
       parameters. 2012. Computational Optimization and Applications.
       51:1, pp. 259-277

    """
    if "ftol" in unknown_options:
        warnings.warn(
            "ftol is deprecated for Nelder-Mead,"
            " use fatol instead. If you specified both, only"
            " fatol is used.",
            DeprecationWarning,
        )
        if np.isclose(fatol, 1e-4) and not np.isclose(unknown_options["ftol"], 1e-4):
            # only ftol was probably specified, use it.
            fatol = unknown_options["ftol"]
        unknown_options.pop("ftol")
    if "xtol" in unknown_options:
        warnings.warn(
            "xtol is deprecated for Nelder-Mead,"
            " use xatol instead. If you specified both, only"
            " xatol is used.",
            DeprecationWarning,
        )
        if np.isclose(xatol, 1e-4) and not np.isclose(unknown_options["xtol"], 1e-4):
            # only xtol was probably specified, use it.
            xatol = unknown_options["xtol"]
        unknown_options.pop("xtol")

    _check_unknown_options(unknown_options)
    maxfun = maxfev
    retall = return_all

    fcalls, func = wrap_function(func, args)

    if adaptive:
        dim = float(len(x0))
        rho = 1
        chi = 1 + 2 / dim
        psi = 0.75 - 1 / (2 * dim)
        sigma = 1 - 1 / dim
    else:
        rho = 1
        chi = 2
        psi = 0.5
        sigma = 0.5

    zdelt = 0.00025
    x0 = asfarray(x0).flatten()

    if bounds is not None:
        lower_bound, upper_bound = bounds.lb, bounds.ub
        # check bounds
        if (lower_bound > upper_bound).any():
            raise AnalysisFieldError(
                "bound",
                (lower_bound, upper_bound),
                msg="Nelder Mead - one of the lower bounds is greater than an upper bound.",
            )
        if np.any(lower_bound > x0) or np.any(x0 > upper_bound):
            warnings.warn(
                "Initial guess is not within the specified bounds", OptimizeWarning, 3
            )

    if bounds is not None:
        x0 = np.clip(x0, lower_bound, upper_bound)

    if initial_simplex is None:
        N = len(x0)

        sim = np.empty((N + 1, N), dtype=x0.dtype)  # sim: simplex
        sim[0] = x0
        for k in range(N):
            y = np.array(x0, copy=True)
            if y[k] != 0:
                y[k] = (1 + nonzdelt) * y[k]
            else:
                y[k] = zdelt
            sim[k + 1] = y
    else:
        sim = np.asfarray(initial_simplex).copy()
        if sim.ndim != 2 or sim.shape[0] != sim.shape[1] + 1:
            raise AnalysisFieldError(
                "initial_simplex shape", sim.shape, expect="(N+1, N)"
            )
        if len(x0) != sim.shape[1]:
            raise AnalysisFieldError("x0", x0, expect=[sim.shape[1]])
        N = sim.shape[1]

    # If neither are set, then set both to default
    if maxiter is None and maxfun is None:
        maxiter = N * 200
        maxfun = N * 200
    elif maxiter is None:
        # Convert remaining Nones, to np.inf, unless the other is np.inf, in
        # which case use the default to avoid unbounded iteration
        if maxfun == np.inf:
            maxiter = N * 200
        else:
            maxiter = np.inf
    elif maxfun is None:
        if maxiter == np.inf:
            maxfun = N * 200
        else:
            maxfun = np.inf

    if bounds is not None:
        sim = np.clip(sim, lower_bound, upper_bound)

    one2np1 = list(range(1, N + 1))
    fsim = np.empty((N + 1,), float)

    for k in range(N + 1):
        fsim[k] = await func(sim[k])

    ind = np.argsort(fsim)
    fsim = np.take(fsim, ind, 0)
    # sort so sim[0,:] has the lowest function value
    sim = np.take(sim, ind, 0)
    if retall:
        allvecs = [sim[0]]
        fbest_each_iteration = [fsim[0]]

    iterations = 1

    while fcalls[0] < maxfun and iterations < maxiter:
        if (
            np.max(np.ravel(np.abs(sim[1:] - sim[0]))) <= xatol
            and np.max(np.abs(fsim[0] - fsim[1:])) <= fatol
        ):
            break

        xbar = np.add.reduce(sim[:-1], 0) / N
        xr = (1 + rho) * xbar - rho * sim[-1]
        if bounds is not None:
            xr = np.clip(xr, lower_bound, upper_bound)
        fxr = await func(xr)  # r: reflect
        doshrink = 0

        if fxr < fsim[0]:
            xe = (1 + rho * chi) * xbar - rho * chi * sim[-1]
            if bounds is not None:
                xe = np.clip(xe, lower_bound, upper_bound)
            fxe = await func(xe)

            if fxe < fxr:
                sim[-1] = xe
                fsim[-1] = fxe
            else:
                sim[-1] = xr
                fsim[-1] = fxr
        else:  # fsim[0] <= fxr
            if fxr < fsim[-2]:
                sim[-1] = xr
                fsim[-1] = fxr
            else:  # fxr >= fsim[-2]
                # Perform contraction
                if fxr < fsim[-1]:
                    xc = (1 + psi * rho) * xbar - psi * rho * sim[-1]
                    if bounds is not None:
                        xc = np.clip(xc, lower_bound, upper_bound)
                    fxc = await func(xc)

                    if fxc <= fxr:
                        sim[-1] = xc
                        fsim[-1] = fxc
                    else:
                        doshrink = 1
                else:
                    # Perform an inside contraction
                    xcc = (1 - psi) * xbar + psi * sim[-1]
                    if bounds is not None:
                        xcc = np.clip(xcc, lower_bound, upper_bound)
                    fxcc = await func(xcc)

                    if fxcc < fsim[-1]:
                        sim[-1] = xcc
                        fsim[-1] = fxcc
                    else:
                        doshrink = 1

                if doshrink:
                    for j in one2np1:
                        sim[j] = sim[0] + sigma * (sim[j] - sim[0])
                        if bounds is not None:
                            sim[j] = np.clip(sim[j], lower_bound, upper_bound)
                        fsim[j] = await func(sim[j])

        ind = np.argsort(fsim)
        sim = np.take(sim, ind, 0)
        fsim = np.take(fsim, ind, 0)
        if callback is not None:
            callback(sim[0])
        iterations += 1
        if retall:
            allvecs.append(sim[0])
            fbest_each_iteration.append(fsim[0])

    x = sim[0]
    fval = np.min(fsim)
    warnflag = 0

    if fcalls[0] >= maxfun:
        warnflag = 1
        msg = _status_message["maxfev"]
        if disp:
            print("Warning: " + msg)
    elif iterations >= maxiter:
        warnflag = 2
        msg = _status_message["maxiter"]
        if disp:
            print("Warning: " + msg)
    else:
        msg = _status_message["success"]
        if disp:
            print(msg)
            print("         Current function value: %f" % fval)
            print("         Iterations: %d" % iterations)
            print("         Function evaluations: %d" % fcalls[0])

    result = OptimizeResult(
        fun=fval,
        nit=iterations,
        nfev=fcalls[0],
        status=warnflag,
        success=(warnflag == 0),
        message=msg,
        x=x,
        final_simplex=(sim, fsim),
    )
    if retall:
        result["allvecs"] = allvecs
        result["fbest_each_iteration"] = fbest_each_iteration

    return result


def sample_response(
    t_arr: np.ndarray,
    response_arr: np.ndarray,
    sample_rate: float = 1.2,
    t_min=None,
    t_max=None,
) -> Tuple:
    """According to the sample_rate, get an new response array."""
    ts = 1 / sample_rate
    func = interp1d(t_arr, response_arr, kind="cubic", fill_value="extrapolate")
    if t_min is None:
        t_min = t_arr[0]
    if t_max is None:
        t_max = t_arr[-1] - ts
    new_t_arr = np.arange(t_min, t_max, ts)

    diff = new_t_arr[-1] - t_arr[-1]
    if 0 < diff < ts:
        new_t_arr[-1] = t_arr[-1]
    new_resp_arr = func(new_t_arr)
    return new_t_arr, new_resp_arr


def calculate_fir_h_arr(
    t_arr: np.ndarray,
    response_arr: np.ndarray,
    sample_rate: float = 1.2,
    t_min=None,
    t_max=None,
) -> np.ndarray:
    """Calculate FIR h array.

    Args:
        t_arr (array): Array of time.
        response_arr (array): Array of response.
        sample_rate (float): Calculate use line sample rate.
        t_min (float): Start FIR time value.
        t_max (float): End FIR time value.

    Returns:
        array: FIR response h array.

    """
    new_t, new_resp = sample_response(t_arr, response_arr, sample_rate, t_min, t_max)
    So = np.hstack([0, new_resp])
    h = np.diff(So)
    h = h / sum(h)
    return h


def iterate_fir_h_arr(h_list: List[np.ndarray], sample_rate: float = 1.2) -> np.ndarray:
    """Iterate calculate FIR h array.

    Args:
        h_list (list): List of h array, h array is FIR response diff.
        sample_rate (float): Calculate use line sample rate.

    Returns:
        array: Many h array recalculate FIR response h array.

    """
    ts = 1 / sample_rate
    dt_list = []
    so_list = []
    for h_arr in h_list:
        new_h_arr = np.append(h_arr, 0)
        Si = np.ones_like(new_h_arr)
        So = ss.lfilter(new_h_arr, 1, Si)

        t_max = (len(So) - 1) * ts + 0.1
        dt_arr = np.arange(0, t_max, ts)
        dt_list.append(dt_arr)
        so_list.append(So)
    t_arr, resp_arr = calculate_distortion(dt_list, so_list, sample_rate)

    final_So = np.hstack([0, resp_arr])
    h = np.diff(final_So)
    h = h[:-1]
    h_composite = h / sum(h)
    return h_composite


def calculate_fir_width(
    t_arr: np.ndarray,
    response_arr: np.ndarray,
    sample_rate: float = 1.2,
    std: float = 0.00015,
) -> float:
    """Calculate FIR max width value.

    Args:
        t_arr (array): Array of time.
        response_arr (array): Array of response.
        sample_rate (float): Calculate use line sample rate.
        std (float): Set target standard sigma, judge data stable.

    Returns:
        float: FIR width.

    """
    new_t, new_resp = sample_response(t_arr, response_arr, sample_rate)

    count = 0
    arrow_err = 2 * (std / 100)
    length = len(new_resp)
    start_idx = int(length / 2)
    start_idx_list = []

    while start_idx > 1 and count < 20:
        start_idx_list.append(start_idx)
        new_arr = new_resp[start_idx:]
        new_std = np.std(new_arr)
        # print(f"start_idx: {start_idx}, std: {new_std}")

        if new_std < std - arrow_err:
            start_idx = int(start_idx / 2)
        elif new_std > std + arrow_err:
            t_idx = length
            for idx in start_idx_list[::-1]:
                if idx > start_idx:
                    t_idx = idx
                    break
            start_idx = int((start_idx + t_idx) / 2)
        else:
            break
        count += 1
    fir_width = (new_t[start_idx] // 2.5) * 2.5
    return fir_width


def calculate_inline_width(
    t_arr: np.ndarray,
    response_arr: np.ndarray,
    sample_rate: float = 1.2,
    rate: float = 0.9527,
) -> float:
    """Calculate inline width.

    Args:
        t_arr (array): Array of time.
        response_arr (array): Array of response.
        sample_rate (float): Calculate use line sample rate.
        rate (float): Point in line rate.

    Returns:
        float: Inline width.

    """
    new_t, new_resp = sample_response(t_arr, response_arr, sample_rate)

    count = 0
    arrow_err = 2 * (rate / 100)
    dash_band = 0.001
    length = len(new_resp)
    start_idx = int(length / 2)
    start_idx_list = []

    while start_idx > 1 and count < 20:
        start_idx_list.append(start_idx)
        new_arr = new_resp[start_idx:]
        new_length = len(new_arr)

        less_index_arr = np.where((new_arr <= (1 + dash_band)))[0]
        less_arr = new_arr[less_index_arr]
        target_index_arr = np.where(less_arr >= (1 - dash_band))[0]
        target_length = len(target_index_arr)
        hit_rate = target_length / new_length

        if hit_rate > rate:
            start_idx = int(start_idx / 2)
        elif hit_rate < rate - arrow_err:
            t_idx = length
            for idx in start_idx_list[::-1]:
                if idx > start_idx:
                    t_idx = idx
                    break
            start_idx = int((start_idx + t_idx) / 2)
        else:
            break
        count += 1

    inline_width = (new_t[start_idx] // 2.5) * 2.5
    return inline_width


def adjust_noise_points(
    arr: np.ndarray, n_multiple: float = 2.0, max_count: int = 5
) -> Tuple:
    """Get array noise point index list, and after adjust noise array.

    Args:
        arr (array): One dimensional array.
        n_multiple (float): Multiple of diff mean, used to check noise points.
        max_count (int): Max iteration times.

    Returns:
        (idx_list, c_arr):
            idx_list (list): Search noise points index list.
            c_arr (array): After adjust noise points array.

    """
    idx_list = []
    c_arr = np.asarray(arr.flatten())
    c_length = len(c_arr)
    special_idx = c_length - 2

    count = 0
    run_flag = True
    while count < max_count and run_flag is True:
        normal_diff = np.diff(c_arr)
        abs_diff = np.abs(normal_diff)
        diff_mean = np.mean(abs_diff)

        index_info = np.where(abs_diff > diff_mean * n_multiple)
        index_arr = index_info[0]
        neighbour_index_info = np.where(np.diff(index_arr) == 1)
        neighbour_index_arr = neighbour_index_info[0]
        t_index_arr = index_arr[neighbour_index_arr]

        t_indexes = t_index_arr.tolist()
        if special_idx in index_arr.tolist():
            t_indexes.append(special_idx)
        if t_indexes:
            for t_idx in t_indexes:
                s_idx = t_idx + 1
                if s_idx < c_length - 1:
                    c_arr[s_idx] = np.mean([c_arr[s_idx - 1], c_arr[s_idx + 1]])
                else:
                    # the end point
                    t_diff = normal_diff[t_idx]
                    c_arr[s_idx] -= t_diff / 2

                if s_idx != 0 and s_idx not in idx_list:
                    idx_list.append(s_idx)
        else:
            run_flag = False
        count += 1

    idx_list.sort()
    return idx_list, c_arr
