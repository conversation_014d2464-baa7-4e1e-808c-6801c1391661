# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/08
# __author:       xw

"""
Purity XEB Experiment class.
"""

import itertools
from collections import defaultdict
from copy import deepcopy

import numpy as np

from ..two_qubit_gate.swap_once import validate_qubit_pair_cz_std, validate_two_qubit_exp_read_options
from ....analysis import ParameterRepr
from ....analysis.algorithms.tomography import tensor_combinations
from ....analysis.library import PurityXEBAnalysis
from ....gate import Rphi_gate
from ....structures import MetaData, Options, QDict
from ....tools import RandomType
from ....types import StandardContext
from .xeb import XEBMultiple, XEBSingle


class PurityXEBSingle(XEBSingle):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()
        options.set_validator("base_gate", list)
        options.base_gate = ["I", "X/2", "Y/2"]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.base_ops = None
        options.use_mle = True
        options.N = 1
        options.data_key = ["trace_rho"]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.support_context = [StandardContext.QC]

        return options

    def _check_options(self):
        super()._check_options()
        gate_bucket = self.run_options.gate_bucket
        # check base ops and base gate
        base_ops = self.analysis_options.base_ops
        base_gate = self.experiment_options.base_gate
        if base_ops is None:
            base_ops = [gate_bucket.get_matrix(gate) for gate in base_gate]
            if self.qubit_pair is not None:
                self.set_analysis_options(N=2)
                base_ops = tensor_combinations(base_ops, repeat=2)
            self.set_analysis_options(base_ops=base_ops)
        depths = self.run_options.depths
        k = self.experiment_options.times
        counts = 3 ** (2 if self.qubit_pairs else 1)
        x_data = np.asarray(depths).repeat(k * counts)
        self.set_run_options(x_data=x_data, analysis_class=PurityXEBAnalysis)

    def _metadata(self) -> MetaData:
        """Set RB experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"K": self.experiment_options.times}
        return metadata

    def _transform_pulse(self):
        temp_struct = self.run_options.temp_struct
        pulse_list = []
        for struct in temp_struct:
            pulse = self.pulse_from_struct(struct, self.qubit)
            for gate in self.experiment_options.base_gate:
                base_pulse = self.run_options.gate_bucket.get_xy_pulse(self.qubit, gate)
                com_pulse = deepcopy(pulse) + base_pulse
                # com_pulse.virtual_z_phase.append(getattr(base_pulse, "phase", 0))
                pulse_list.append(com_pulse)
        self.run_options.xy_pulse_map[self.qubit] = pulse_list

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "fidelity":
                result.extra["path"] = "Qubit.xeb_purity_fidelity"


class PurityXEBMultiple(XEBMultiple):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()
        options.set_validator("base_gate", list)

        options.base_gate = ["I", "X/2", "Y/2"]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)

        options.depths = None
        options.k = None
        options.quality_bounds = [0.9, 0.85, 0.77]
        options.base_ops = None
        options.use_mle = True
        options.N = 1
        options.data_key = ["trace_rho"]

        options.result_parameters = [
            ParameterRepr(
                name="fidelity", repr="average fidelity", unit="", param_path=""
            ),
            ParameterRepr(name="A", repr="A", unit="", param_path=""),
            ParameterRepr(name="B", repr="B", unit="", param_path=""),
            ParameterRepr(name="U", repr="u", unit="", param_path=""),
        ]

        return options

    def _metadata(self) -> MetaData:
        """Set RB experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"K": self.experiment_options.times}
        return metadata

    def _check_options(self):

        # bind high frequency qubit and low frequency qubit
        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)

        # bind base cz gate
        self._bind_cz_gate()
        self.set_experiment_options(is_dynamic=0)

        self.set_analysis_options(result_name=self.qubit_pair.name)

        # check base ops and base gate
        gate_bucket = self.run_options.gate_bucket
        base_ops = self.analysis_options.base_ops
        base_gate = self.experiment_options.base_gate
        if base_ops is None:
            base_ops = [gate_bucket.get_matrix(gate) for gate in base_gate]
            if self.qubit_pair is not None:
                self.set_analysis_options(N=2)
                base_ops = tensor_combinations(base_ops, repeat=2)
            self.set_analysis_options(base_ops=base_ops)
        if self.qubit_pair is not None and isinstance(base_gate[0], str):
            base_gate = list(itertools.product(base_gate, repeat=2))
            self.set_experiment_options(base_gate=base_gate)

        super(XEBMultiple, self)._check_options()

        depths = self.run_options.depths
        k = self.experiment_options.times
        counts = 3 ** (2 if self.qubit_pair else 1)
        x_data = np.asarray(depths).repeat(k * counts)

        # set run options
        self.set_run_options(x_data=x_data, analysis_class=PurityXEBAnalysis)

    def random_sample_struct(self, depth: int):
        goal_gate = self.experiment_options.goal_gate
        gate_bucket = self.run_options.gate_bucket
        gate_names = gate_bucket.gate_collector.single_x2_gate_infos()
        final_gates = []
        rgp = []

        for _ in range(depth):
            random_index = self._random_function(RandomType.randint)
            gate = gate_names[random_index]
            final_gates.append(gate)
            rgp.append("-")
            if goal_gate:
                final_gates.append(goal_gate)
                rgp.append("-")

        return QDict(gates=final_gates, rgp=rgp, matrix=np.eye(2))

    def _transform_pulse(self):
        temp_struct = self.run_options.temp_struct

        ql = self.run_options.ql
        qh = self.run_options.qh
        qc = self.couplers[0]

        parking_bits = []
        for bit in self.qubit_pair.metadata.std.parking_bits:
            if bit != self.qubit_pair.qc:
                if bit.startswith("q"):
                    for qubit in self.qubits:
                        if qubit.name == bit:
                            parking_bits.append(qubit)
                            break
                else:
                    for coupler in self.couplers:
                        if coupler.name == bit:
                            parking_bits.append(coupler)
                            break

        qh_xy_pulse_list = []
        ql_xy_pulse_list = []
        qh_z_pulse_list = []
        ql_z_pulse_list = []
        qc_z_pulse_list = []
        parking_z_pulse_dict = defaultdict(list)

        for struct in temp_struct:
            qh_xy_pulse_list.extend(self.pulse_from_struct(struct.qh_struct, qh))
            ql_xy_pulse_list.extend(self.pulse_from_struct(struct.ql_struct, ql))
            qh_z_pulse_list.extend(
                self.pulse_from_struct(struct.qh_struct, qh, mode="Z")
            )
            ql_z_pulse_list.extend(
                self.pulse_from_struct(struct.ql_struct, ql, mode="Z")
            )
            qc_z_pulse_list.extend(
                self.pulse_from_struct(struct.ql_struct, qc, mode="Z")
            )
            for bit_obj in parking_bits:
                parking_z_pulse_dict[bit_obj].extend(
                    self.pulse_from_struct(struct.ql_struct, bit_obj, mode="Z")
                )

        self.run_options.xy_pulse_map[qh] = qh_xy_pulse_list
        self.run_options.xy_pulse_map[ql] = ql_xy_pulse_list
        self.run_options.z_pulse_map[qh] = qh_z_pulse_list
        self.run_options.z_pulse_map[ql] = ql_z_pulse_list
        self.run_options.z_pulse_map[qc] = qc_z_pulse_list
        self.run_options.z_pulse_map.update(parking_z_pulse_dict)

    def pulse_from_struct(self, struct, qubit, mode: str = "XY"):
        gate_bucket = self.run_options.gate_bucket
        pre_gates, aft_gates = struct.gates[:-2], struct.gates[-2:]
        if mode == "XY":
            pulse = gate_bucket.get_xy_pulse(qubit, pre_gates)
        else:
            pulse = gate_bucket.get_z_pulse(qubit, pre_gates)
        for i, gate in enumerate(aft_gates):
            if mode == "XY":
                rgp = struct.rgp[len(pre_gates) + i]
                p = Rphi_gate(phase=rgp[0], theta=rgp[1])
                pulse += p.to_pulse(qubit)()
                if pulse.virtual_z_phase:
                    pulse.virtual_z_phase.append(rgp[0])
            else:
                pulse += gate_bucket.get_z_pulse(qubit, gate)
        pulse_list = []
        for bg in self.experiment_options.base_gate:
            if mode == "XY":
                base_pulse = gate_bucket.get_xy_pulse(qubit, bg)
                com_pulse = deepcopy(pulse) + base_pulse
                if com_pulse.virtual_z_phase:
                    com_pulse.virtual_z_phase.append(getattr(base_pulse, "phase", 0))
                pulse_list.append(com_pulse)
            else:
                base_pulse = gate_bucket.get_z_pulse(qubit, bg)
                com_pulse = deepcopy(pulse) + base_pulse
                pulse_list.append(com_pulse)
        return pulse_list

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""

        for key, result in self.analysis.results.items():
            if key == "fidelity" and self.experiment_options.goal_gate == "CZ":
                result.extra["path"] = "QubitPair.metadata.std.fidelity.xeb_purity_fidelity"

    def _alone_save_result(self):
        pass
