# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/07/27
# __author:       SS Fang

import json
from copy import deepcopy

import pandas as pd

from ....analysis.library import XYZTimingCompositeAnalysis
from ....log import pyqlog
from ....qaio_property import QAIO
from ....structures import Options
from ....tools.utilities import qarange
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import CouplerXYZTiming, CouplerXYZTimingByZZShift, XYZTiming


class XYZTimingComposite(CompositeExperiment):
    """XYZTiming composite experiment."""

    _sub_experiment_class = XYZTiming

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("max_count", int)
        options.max_count = 2
        
        options.set_validator("policy", ["normal", "shrink"])
        options.policy = "shrink"

        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for experiment."""
        options = super()._default_analysis_options()

        options.set_validator("force", bool)
        options.delay_map = {}
        options.link_channel = None
        options.force = False

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default run options values for the experiment."""
        options = super()._default_run_options()

        options.delay_map = {}

        return options

    def _check_options(self):
        """Check options."""
        super()._check_options()

        # set analysis result name
        result_name = self.analysis_options.result_name
        child_exp = self.child_experiment
        if self.coupler and child_exp.is_coupler_exp is False:
            result_name = child_exp.coupler.name
        elif child_exp.is_coupler_exp is True and self.coupler:
            result_name = child_exp.coupler.name
        elif child_exp.qubit:
            result_name = child_exp.qubit.name

        delay_map = self.run_options.delay_map
        for compensate in self.compensates.values():
            if compensate.name == result_name:
                delay_map.update({
                    "old_xy_delay": compensate.x_delay,
                    "old_z_delay": compensate.z_delay,
                })
        self.set_analysis_options(result_name=result_name)

    def _alone_save_result(self):
        """Alone save some special result."""
        delay_map = self.run_options.delay_map
        result_name = self.analysis_options.result_name

        mark_info = f"{self}({result_name}_xyz_iter)"
        json_str = json.dumps(delay_map, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=mark_info, prefix=".json")

        delay_map.pop("old_xy_delay")
        delay_map.pop("old_z_delay")
        index_list = list(list(delay_map.values())[0].keys())
        delay_df = pd.DataFrame(data=delay_map, index=index_list)
        pyqlog.log("RESULT", f"{mark_info}:\n{delay_df}")

    async def _sync_composite_run(self):
        """XYZTimingComposite run logic."""
        # super().run()

        max_count = self.experiment_options.max_count
        delay_map = self.run_options.delay_map
        result_name = self.analysis_options.result_name

        count = 0
        run_flag = True
        xy_delay = 0.0
        z_delay = 0.0
        while count < max_count and run_flag is True:
            once_delay_map = {"set_xy_delay": xy_delay, "set_z_delay": z_delay}
            xyz_exp = deepcopy(self.child_experiment)
            description = f"count-{count}-xy_delay-{xy_delay}-z_delay-{z_delay}"

            xyz_exp.set_parent_file(self, description, count)
            xyz_exp.set_experiment_options(xy_delay=xy_delay, z_delay=z_delay)
            self._check_simulator_data(xyz_exp, count)
            await xyz_exp.run_experiment()
            self.set_analysis_options(link_channel=xyz_exp.analysis_options.link_channel)
            self._experiments.append(xyz_exp)

            line = xyz_exp.analysis.results.line.value
            offset = xyz_exp.analysis.results.offset.value
            quality = xyz_exp.analysis.quality.descriptor

            if line == "XY-line":
                if offset <= 1 / QAIO.dac_sample_rate:
                    run_flag = False
                else:
                    xy_delay += offset
            elif line == "Z-line":
                if offset <= 1 / QAIO.awg_sample_rate:
                    run_flag = False
                else:
                    z_delay += offset
            pyqlog.info(
                f"{result_name} execute count {count} result line: {line}, offset: {offset}, run_flag: {run_flag}"
            )

            min_value = min([xy_delay, z_delay])
            xy_delay = xy_delay - min_value
            z_delay = z_delay - min_value

            once_delay_map.update({
                "quality": quality,
                "line": line,
                "offset": offset,
                "result_xy_delay": xy_delay,
                "result_z_delay": z_delay,
            })
            delay_map.update({str(count): once_delay_map})

            print(f"count={count}, old_xy_delay={delay_map.get('old_xy_delay')}")
            count += 1

            if self.experiment_options.policy == "shrink":
                pre_delays = xyz_exp.experiment_options.delays
                pre_const_delay = xyz_exp.experiment_options.const_delay
                pre_step = pre_delays[1] - pre_delays[0]
                new_delays = qarange(pre_const_delay - pre_step * 30, pre_const_delay + pre_step * 30, pre_step)
                self.child_experiment.experiment_options.delays = new_delays

        self.set_analysis_options(delay_map=delay_map)
        # self._alone_save_result()
        self._run_analysis(list(range(count)), XYZTimingCompositeAnalysis)


class CouplerXYZTimingComposite(XYZTimingComposite):
    """Coupler XYZTimingComposite experiment."""

    _sub_experiment_class = CouplerXYZTiming


class CouplerXYZTimingZZShiftComposite(XYZTimingComposite):
    """Coupler ZZShift XYZTimingComposite experiment."""

    _sub_experiment_class = CouplerXYZTimingByZZShift
