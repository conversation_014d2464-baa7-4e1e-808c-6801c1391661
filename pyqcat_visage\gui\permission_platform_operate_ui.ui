<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>927</width>
    <height>674</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Platform Permission</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item row="0" column="0">
     <widget class="QWidget" name="widget_main" native="true">
      <layout class="QVBoxLayout" name="verticalLayout" stretch="1,5,1">
       <item>
        <widget class="QWidget" name="widget" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="5,1">
          <item>
           <widget class="QWidget" name="widget_2" native="true">
            <layout class="QVBoxLayout" name="verticalLayout_2">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="2,1,10,1">
               <item>
                <widget class="QLabel" name="targetName">
                 <property name="text">
                  <string>platform</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>268</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item>
                <widget class="SearchComboBox" name="PlatformBox"/>
               </item>
               <item>
                <spacer name="horizontalSpacer_2">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="2,1,5,6">
               <item>
                <widget class="QLabel" name="label">
                 <property name="text">
                  <string>type</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_5">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item>
                <widget class="QComboBox" name="TypeBox"/>
               </item>
               <item>
                <spacer name="horizontalSpacer_6">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_4" native="true">
            <layout class="QGridLayout" name="gridLayout_2">
             <item row="0" column="0">
              <widget class="QPushButton" name="queryButton">
               <property name="text">
                <string>query</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_2">
         <property name="title">
          <string>Platfrom Permission Manage</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <widget class="QWidget" name="ListBox1" native="true"/>
          </item>
          <item>
           <widget class="QWidget" name="ListBox2" native="true"/>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="widget_3" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout" stretch="3,1,2,0">
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>314</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="saveButton">
            <property name="text">
             <string>Save</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>314</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="ResetButton">
            <property name="text">
             <string>reset</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SearchComboBox</class>
   <extends>QComboBox</extends>
   <header>.widgets.combox_custom.combox_search</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>queryButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>query_info()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>603</x>
     <y>60</y>
    </hint>
    <hint type="destinationlabel">
     <x>388</x>
     <y>336</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>saveButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>save_info()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>388</x>
     <y>592</y>
    </hint>
    <hint type="destinationlabel">
     <x>388</x>
     <y>336</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>ResetButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>reset()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>712</x>
     <y>599</y>
    </hint>
    <hint type="destinationlabel">
     <x>388</x>
     <y>336</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>query_info()</slot>
  <slot>save_info()</slot>
  <slot>reset()</slot>
 </slots>
</ui>
