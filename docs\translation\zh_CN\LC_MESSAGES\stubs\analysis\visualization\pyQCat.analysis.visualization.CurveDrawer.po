# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:2
msgid "pyQCat.analysis.visualization.CurveDrawer"
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer:1
msgid ""
"Bases: "
":py:class:`~pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer`"
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer:1
msgid "Curve drawer for MatplotLib backend."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.visualization.CurveDrawer.__init__>`\\"
" \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`draw_color_map "
"<pyQCat.analysis.visualization.CurveDrawer.draw_color_map>`\\ "
"\\(x\\_data\\, y\\_data\\, z\\_data\\[\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_color_map:1
msgid ""
"using norm to map colormaps onto data in non-linear ways :type x_data: "
":py:class:`~typing.Sequence`\\[:py:class:`float`] :param x_data: :type "
"y_data: :py:class:`~typing.Sequence`\\[:py:class:`float`] :param y_data: "
":type z_data: :py:class:`~typing.Sequence`\\[:py:class:`float`] :param "
"z_data: :type ax_index: :py:data:`~typing.Optional`\\[:py:class:`int`] "
":param ax_index: Index of canvas if multiple inset axis exist."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`draw_filter_data "
"<pyQCat.analysis.visualization.CurveDrawer.draw_filter_data>`\\ "
"\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_filter_data:1
msgid "Draw smooth data"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`draw_fit_line "
"<pyQCat.analysis.visualization.CurveDrawer.draw_fit_line>`\\ "
"\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_fit_line:1
msgid "Draw fit line."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`draw_raw_data "
"<pyQCat.analysis.visualization.CurveDrawer.draw_raw_data>`\\ "
"\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_raw_data:1
msgid "Draw raw data."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`draw_text "
"<pyQCat.analysis.visualization.CurveDrawer.draw_text>`\\ "
"\\(\\[ax\\_index\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
msgid "Add text to the axes."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`draw_vlines "
"<pyQCat.analysis.visualization.CurveDrawer.draw_vlines>`\\ \\(x\\_data\\,"
" y\\_data\\[\\, y\\_min\\, ax\\_index\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines:1
msgid "Draw vline."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`format_canvas "
"<pyQCat.analysis.visualization.CurveDrawer.format_canvas>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.format_canvas:1
msgid "Final cleanup for the canvas appearance."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`initialize_canvas "
"<pyQCat.analysis.visualization.CurveDrawer.initialize_canvas>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
#: of
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.initialize_canvas:1
msgid "Initialize the drawing canvas."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_options "
"<pyQCat.analysis.visualization.CurveDrawer.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:30:<autosummary>:1
msgid "Set the drawing options."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.CurveDrawer.rst:32
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.initialize_canvas:1:<autosummary>:1
msgid ":py:obj:`figure <pyQCat.analysis.visualization.CurveDrawer.figure>`\\"
msgstr ""

#: of
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.initialize_canvas:1:<autosummary>:1
msgid "Return figure object handler to be saved in the database."
msgstr ""

#: of
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.initialize_canvas:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.visualization.CurveDrawer.options>`\\"
msgstr ""

#: of
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.initialize_canvas:1:<autosummary>:1
msgid "Return the drawing options."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis:1
msgid ""
"A helper method to get inset axis. :type index: "
":py:data:`~typing.Optional`\\[:py:class:`int`] :param index: Index of "
"inset axis. If nothing is provided, it returns the entire axis."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis:5
msgid ":py:class:`~matplotlib.axes._axes.Axes`"
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis:6
msgid "Corresponding axis object."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis
msgid "Raises"
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer._get_axis:8
msgid "When axis index is specified but no inset axis is found."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_filter_data
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_fit_line
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_raw_data
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_text
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_filter_data:4
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_fit_line:4
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_raw_data:4
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines:4
msgid "X values."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_filter_data:6
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_raw_data:6
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines:6
msgid "Y values."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_filter_data:8
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_fit_line:8
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_raw_data:8
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_text:5
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines:11
msgid "Index of canvas if multiple inset axis exist."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_filter_data:9
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_fit_line:9
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_raw_data:9
#: pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines:12
msgid "Valid options for the drawer backend API."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_fit_line:6
msgid "Fit Y values."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_text:1
msgid ""
"Add text to the axes. Add the text *s* to the axes at location *x*, *y* "
"in data coordinates."
msgstr ""

#: of pyQCat.analysis.visualization.curve_drawer.CurveDrawer.draw_vlines:8
msgid ""
"Respective beginning and end of each line. If scalars are provided, all "
"lines will have same length."
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.analysis.visualization.CurveDrawer.__init__>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`draw_color_map "
#~ "<pyQCat.analysis.visualization.CurveDrawer.draw_color_map>`\\ "
#~ "\\(x\\_data\\, y\\_data\\, z\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`draw_filter_data "
#~ "<pyQCat.analysis.visualization.CurveDrawer.draw_filter_data>`\\ "
#~ "\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`draw_fit_line "
#~ "<pyQCat.analysis.visualization.CurveDrawer.draw_fit_line>`\\ "
#~ "\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`draw_raw_data "
#~ "<pyQCat.analysis.visualization.CurveDrawer.draw_raw_data>`\\ "
#~ "\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`draw_text "
#~ "<pyQCat.analysis.visualization.CurveDrawer.draw_text>`\\ "
#~ "\\(\\[ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`draw_vlines "
#~ "<pyQCat.analysis.visualization.CurveDrawer.draw_vlines>`\\ "
#~ "\\(x\\_data\\, y\\_data\\[\\, y\\_min\\, "
#~ "ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`format_canvas "
#~ "<pyQCat.analysis.visualization.CurveDrawer.format_canvas>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`initialize_canvas "
#~ "<pyQCat.analysis.visualization.CurveDrawer.initialize_canvas>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_options "
#~ "<pyQCat.analysis.visualization.CurveDrawer.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":py:obj:`figure <pyQCat.analysis.visualization.CurveDrawer.figure>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`options <pyQCat.analysis.visualization.CurveDrawer.options>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.visualization.CurveDrawer.__init__>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`draw_color_map "
#~ "<pyQCat.analysis.visualization.CurveDrawer.draw_color_map>`\\ "
#~ "\\(x\\_data\\, y\\_data\\, z\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`draw_filter_data "
#~ "<pyQCat.analysis.visualization.CurveDrawer.draw_filter_data>`\\ "
#~ "\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`draw_fit_line "
#~ "<pyQCat.analysis.visualization.CurveDrawer.draw_fit_line>`\\ "
#~ "\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`draw_raw_data "
#~ "<pyQCat.analysis.visualization.CurveDrawer.draw_raw_data>`\\ "
#~ "\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`draw_text "
#~ "<pyQCat.analysis.visualization.CurveDrawer.draw_text>`\\ "
#~ "\\(\\[ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`draw_vlines "
#~ "<pyQCat.analysis.visualization.CurveDrawer.draw_vlines>`\\ "
#~ "\\(x\\_data\\, y\\_data\\[\\, y\\_min\\, "
#~ "ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`format_canvas "
#~ "<pyQCat.analysis.visualization.CurveDrawer.format_canvas>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`initialize_canvas "
#~ "<pyQCat.analysis.visualization.CurveDrawer.initialize_canvas>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options "
#~ "<pyQCat.analysis.visualization.CurveDrawer.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":obj:`figure <pyQCat.analysis.visualization.CurveDrawer.figure>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.visualization.CurveDrawer.options>`\\"
#~ msgstr ""

