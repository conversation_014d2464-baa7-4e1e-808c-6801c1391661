<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>910</width>
    <height>682</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Storm Manage</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item row="0" column="0">
     <widget class="QGroupBox" name="groupBox">
      <property name="title">
       <string>Storm</string>
      </property>
      <layout class="QGridLayout" name="gridLayout_2" rowstretch="1,10">
       <item row="0" column="0">
        <widget class="QWidget" name="widget" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="5,1">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QWidget" name="widget_11" native="true">
            <layout class="QGridLayout" name="gridLayout_3">
             <item row="0" column="0">
              <widget class="QWidget" name="widget_3" native="true">
               <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="2,10,3">
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="label">
                  <property name="text">
                   <string>sample</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="SearchComboBox" name="SampleContent"/>
                </item>
                <item>
                 <spacer name="horizontalSpacer">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>95</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QWidget" name="widget_4" native="true">
               <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,10,3">
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="label_2">
                  <property name="text">
                   <string>env_name</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="SearchComboBox" name="EnvContent"/>
                </item>
                <item>
                 <spacer name="horizontalSpacer_2">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>95</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_10" native="true">
            <layout class="QVBoxLayout" name="verticalLayout_3">
             <item>
              <spacer name="verticalSpacer">
               <property name="orientation">
                <enum>Qt::Orientation::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>18</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="queryButton">
               <property name="text">
                <string>query</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_2">
               <property name="orientation">
                <enum>Qt::Orientation::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>18</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QTableViewStormWidget" name="tableStormView"/>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionCreateStom"/>
   <addaction name="separator"/>
   <addaction name="actionRefresh"/>
  </widget>
  <action name="actionCreateStom">
   <property name="text">
    <string>Create Storm</string>
   </property>
  </action>
  <action name="actionRefresh">
   <property name="text">
    <string>refresh</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QTableViewStormWidget</class>
   <extends>QTableView</extends>
   <header>.widgets.chip_manage_files.table_view_storm</header>
  </customwidget>
  <customwidget>
   <class>SearchComboBox</class>
   <extends>QComboBox</extends>
   <header>.widgets.combox_custom.combox_search</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>actionCreateStom</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>create_storm()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>414</x>
     <y>314</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>queryButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>query_storm()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>881</x>
     <y>120</y>
    </hint>
    <hint type="destinationlabel">
     <x>513</x>
     <y>323</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionRefresh</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>refresh()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>541</x>
     <y>335</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>SampleContent</sender>
   <signal>currentTextChanged(QString)</signal>
   <receiver>MainWindow</receiver>
   <slot>sample_change()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>206</x>
     <y>73</y>
    </hint>
    <hint type="destinationlabel">
     <x>541</x>
     <y>335</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>create_storm()</slot>
  <slot>query_storm()</slot>
  <slot>refresh()</slot>
  <slot>sample_change()</slot>
 </slots>
</ui>
