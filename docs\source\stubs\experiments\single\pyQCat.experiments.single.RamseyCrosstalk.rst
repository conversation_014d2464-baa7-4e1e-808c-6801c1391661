﻿pyQCat.experiments.single.Ramsey<PERSON>rosstalk
=========================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: RamseyCrosstalk

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~RamseyCrosstalk.__init__
      ~RamseyCrosstalk.acquire_pulse
      ~RamseyCrosstalk.cal_fidelity
      ~<PERSON><PERSON>rosstalk.experiment_info
      ~RamseyCrosstalk.from_experiment_context
      ~Ramsey<PERSON>rosstalk.get_bias_pulse
      ~RamseyCrosstalk.get_qubit_str
      ~<PERSON><PERSON>rosstalk.get_xy_pulse
      ~RamseyCrosstalk.get_xy_pulse_pre
      ~Ramsey<PERSON>rosstalk.get_z_pulse
      ~Ramsey<PERSON>rosstalk.get_z_pulse_pre
      ~RamseyCrosstalk.jupyter_schedule
      ~<PERSON><PERSON><PERSON>talk.options_table
      ~<PERSON><PERSON>rosstalk.play_bias_pulse
      ~RamseyCrosstalk.play_pulse
      ~<PERSON><PERSON><PERSON>talk.plot_schedule
      ~<PERSON><PERSON>rosstalk.run
      ~<PERSON><PERSON>rosstalk.set_analysis_options
      ~RamseyCrosstalk.set_experiment_options
      ~RamseyCrosstalk.set_multiple_IF
      ~RamseyCrosstalk.set_multiple_index
      ~RamseyCrosstalk.set_parent_file
      ~RamseyCrosstalk.set_run_options
      ~RamseyCrosstalk.set_sweep_order
      ~<PERSON>Crosstalk.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~RamseyCrosstalk.analysis
      ~RamseyCrosstalk.analysis_options
      ~RamseyCrosstalk.experiment_options
      ~RamseyCrosstalk.run_options
   
   