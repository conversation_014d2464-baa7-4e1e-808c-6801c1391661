# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/12
# __author:       <PERSON><PERSON><PERSON>
# __refs:         https://document.qpanda.cn/presentation/1lq7MwEVB2tVVQAe

from collections import defaultdict
from typing import List

import numpy as np
from pyQCat.experiments.batch_experiment import BatchExperiment, BatchPhysicalUnitType, EXP_TYPE
from pyQCat.log import pyqlog
from pyQCat.structures import QDict
from pyQCat.tools import get_bound_ac_spectrum, qarange


class BatchCouplerProbeCalibration(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.set_validator("probe_bit_mode", ["high", "low"])
        options.set_validator("coupler_idle_mode", ["max", "middle"])
        options.coupler_idle_mode = "middle"
        options.probe_bit_mode = "high"
        options.set_global_qc = True
        options.probe_bit_black_list = []
        options.probe_qubit_calibration_flows = [
            "CavityFreqSpectrum",
            "QubitSpectrum",
            "XpiDetection",
            "SingleShot",
            "QubitFreqCalibration",
            "ReadoutFreqCalibration",
            "SingleShot",
        ]
        options.large_width_flows = ["RabiScanAmp_400"]
        options.coupler_calibration_flows = [
            "CouplerSpectrum",
            "CouplerSpectrumZAmp",
            "CouplerRabiScanAmp_1"
            "CouplerFreqCalibration"
            "CouplerRabiScanAmp_2"
            "CouplerDetuneCalibration"
            "CouplerAmpComposite_0"
            "CouplerAmpComposite_1",
        ]
        options.probe_x_width = [30, 400]
        options.v_scope = 0.1
        options.quality_block_exp = ["CouplerSpectrumZAmp"]
        options.physical_unit_type = BatchPhysicalUnitType.COUPLER
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.probe_qubit_list = []
        options.probe_qubit_dict = defaultdict(list)
        return options

    def _batch_up(self):
        super()._batch_up()
        self._divide_probe_drive()
        self._set_coupler_idle_point()
        self._set_coupler_z_amp_list()

    def _divide_probe_drive(self):
        """Allocate probe bit and drive bit"""
        if self.experiment_options.physical_units is None:
            self.experiment_options.physical_units = list(
                self.backend.chip_data.cache_coupler.keys()
            )

        probe_qubit_dict = self.run_options.probe_qubit_dict
        probe_qubit_list = self.run_options.probe_qubit_list
        probe_bit_black_list = self.experiment_options.probe_bit_black_list

        for unit in self.experiment_options.physical_units:
            # check probe bit
            cs = self.backend.chip_data.get_coupler_struct(unit, check_freq=False)

            if cs.qp.name in probe_qubit_list:
                probe_qubit_dict[cs.qp.name].append(unit)
            elif not cs.qc.check_drive_bit_in_near():
                if cs.qp.name in probe_bit_black_list:
                    pyqlog.warning(
                        f"{unit} probe bit {cs.qp.name} in black_list and drive bit {cs.qd.name} Non neighbor"
                    )
                    continue
                else:
                    probe_qubit_list.append(cs.qp.name)
                    probe_qubit_dict[cs.qp.name].append(unit)
            elif cs.qd.name in probe_qubit_list:
                probe_qubit_dict[cs.qd.name].append(unit)
                cs.qc.revert_probe_drive()
            elif (
                cs.qp.name in probe_bit_black_list
                and cs.qd.name in probe_bit_black_list
            ):
                pyqlog.warning(f"{unit} probe and qubit both in black_list")
                continue
            else:
                cs = self.backend.chip_data.get_coupler_struct(unit)
                if cs.qp.name in probe_bit_black_list:
                    cs.qc.revert_probe_drive()
                    cs = self.backend.chip_data.get_coupler_struct(
                        unit, check_freq=False
                    )
                probe_qubit_list.append(cs.qp.name)
                probe_qubit_dict[cs.qp.name].append(unit)

            # check drive bit
            if cs.qp.inst.xy_lo == cs.qd.inst.xy_lo:
                has_found = self._search_nearest_drive_bit(cs)
                if has_found is False:
                    probe_qubit_dict[cs.qp.name].remove(unit)

    def _search_nearest_drive_bit(self, cs: QDict):
        topology = self.backend.chip_data.topology
        if not topology:
            return False

        has_found_bit = set([cs.qd.name, cs.qp.name])
        start_bits = set([cs.qd.name, cs.qp.name])
        new_start_bits = set()
        drive_qubit = None

        while not drive_qubit and start_bits:
            for start_bit in start_bits:
                neighbor_bits = topology.bit_neighbor(start_bit)
                for bit in neighbor_bits:
                    if bit not in has_found_bit:
                        qubit = self.backend.chip_data.cache_qubit.get(bit)
                        if qubit and qubit.inst.xy_lo != cs.qp.inst.xy_lo:
                            cs.qc.drive_bit = int(bit[1:])
                            drive_qubit = bit
                            break
                        has_found_bit.add(bit)
                        new_start_bits.add(bit)
                if drive_qubit:
                    break
            start_bits = new_start_bits
            new_start_bits = set()

        if drive_qubit:
            distance = min(
                topology.bit_distance(cs.qp.name, drive_qubit),
                topology.bit_distance(cs.qd.name, drive_qubit),
            )
            drive_power = -30 + 10 * distance
            if drive_power > -10:
                pyqlog.warning(
                    f"{cs.qc.name} found drive bit q{cs.qc.drive_bit}, but distance is {distance}!"
                )
                return False
            else:
                pyqlog.info(f"{cs.qc.name} found drive bit q{cs.qc.drive_bit}!")
                cs.qc.drive_power = -30 + 10 * distance
                return True
        else:
            pyqlog.warning(f"{cs.qc.name} not found drive bit!")
            return False

    def _set_coupler_idle_point(self):
        probe_qubit_dict = self.run_options.probe_qubit_dict
        physical_units = []
        for coupler_list in probe_qubit_dict.values():
            physical_units.extend(coupler_list)
        set_coupler_idle_point(self, physical_units)

    def _set_coupler_z_amp_list(self):
        if (
            "CouplerSpectrumZAmp"
            not in self.experiment_options.coupler_calibration_flows
        ):
            return
        v_scope = self.experiment_options.v_scope
        for coupler in self.experiment_options.physical_units:
            qc = self.backend.chip_data.cache_coupler.get(coupler)
            v_max, v_min, idle = qc.dc_max, qc.dc_min, qc.idle_point
            l1 = (1 + v_scope) * v_min - v_scope * v_max
            l2 = (1 + v_scope) * v_max - v_scope * v_min
            l1 = l1 if abs(l1) < 0.5 else (0.5 if l1 > 0 else -0.5)
            l2 = l2 if abs(l2) < 0.5 else (0.5 if l2 > 0 else -0.5)
            z_amp_list = list(np.linspace(l1 - v_max - idle, l2 - v_max - idle, 50))
            self.change_parallel_exec_exp_options(
                exp_name="CouplerSpectrumZAmp", unit=coupler, z_amp_list=z_amp_list
            )

    def _update_qubit_x_width(self, group: List[str], index: int = 0):
        for unit in group:
            qubit = self.backend.chip_data.cache_qubit.get(unit)
            if qubit:
                if index:
                    qubit.XYwave.detune_pi = 0
                    qubit.XYwave.detune_pi2 = 0
                qubit.XYwave.time = round(
                    self.experiment_options.probe_x_width[index]
                    - qubit.XYwave.offset * 2,
                    1,
                )

    def _update_rabi_amp_list(self, group: List[str]):
        w1, w2 = self.experiment_options.probe_x_width
        for unit in group:
            qubit = self.backend.chip_data.cache_qubit.get(unit)
            if qubit:
                new_amp = qubit.XYwave.Xpi * w1 * 1.5 / w2
                new_amp_list = np.linspace(0, new_amp, 50).tolist()
                pyqlog.info(f"Update {unit} amp list {new_amp_list}")
                self.change_parallel_exec_exp_options(
                    exp_name=self.experiment_options.large_width_flows[0],
                    unit=unit,
                    amps=new_amp_list,
                )

    def _coupler_parallel_group_allocate(self, physical_units: List[str]):
        coupler_list = []
        for pb in physical_units:
            coupler_list.extend(self.run_options.probe_qubit_dict[pb])

        group_map = self.parallel_allocator_for_cc(coupler_list)
        return list(group_map.values())

    def _run_batch(self):
        # probe qubit calibration
        success_probe_qubit = []

        group_map = self.parallel_allocator_for_qc(self.run_options.probe_qubit_list)
        for index, group in enumerate(group_map.values()):
            progress = f"Group({index + 1}/{len(group_map)})"
            self._update_qubit_x_width(group, index=0)
            pass_units = self._run_flow(
                flows=self.experiment_options.probe_qubit_calibration_flows,
                physical_units=group,
                name=f"{progress} Probe Qubit Calibration",
            )
            if pass_units:
                self._update_qubit_x_width(pass_units, index=1)
                self._update_rabi_amp_list(pass_units)
                pass_units = self._run_flow(
                    flows=self.experiment_options.large_width_flows,
                    physical_units=pass_units,
                    name=f"{progress} Probe Qubit Extend X Width",
                )
                if pass_units:
                    success_probe_qubit.extend(pass_units)

        # update probe qubit to db
        # self.backend.save_chip_data_to_db(success_probe_qubit)

        # coupler spectrum test
        success_couplers = []
        coupler_groups = self._coupler_parallel_group_allocate(success_probe_qubit)
        for index, group in enumerate(coupler_groups):
            progress = f"Group({index + 1}/{len(coupler_groups)})"
            self.backend.chip_data.sync_coupler_probe_qubit(group)
            pass_units = self._run_flow(
                flows=self.experiment_options.coupler_calibration_flows,
                physical_units=group,
                name=f"{progress} Coupler Calibration",
            )
            success_couplers.extend(group)
        fail_couplers = [
            unit
            for unit in self.experiment_options.physical_units
            if unit not in success_couplers
        ]
        pyqlog.info(f"SUC Coupler: {success_couplers}\nFail Coupler: {fail_couplers}")

        # update coupler to db
        self.backend.save_chip_data_to_db(success_couplers)


class BatchCouplerIdleCalibration(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.cs_flows = [
            "SingleShot_QC",
            "CouplerSpectrum",
        ]
        options.flows = [
            "CouplerRabiScanAmp_1",
            "CouplerFreqCalibration",
            "CouplerRabiScanAmp_2",
            "CouplerDetuneCalibration",
            "CouplerAmpComposite_Xpi",
            "CouplerAmpComposite_Xpi2",
        ]
        options.idle_step = 0.01
        options.idle_points = 30
        options.pass_to_db = False
        options.set_global_qc = True
        options.set_validator("coupler_idle_mode", ["max", "middle"])
        options.coupler_idle_mode = "middle"
        options.physical_unit_type = BatchPhysicalUnitType.COUPLER
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.idle_point_map = {}
        options.max_idle_length = 0
        options.cs_freq_map = {}
        options.pass_unit_f01_map = {}
        options.pass_unit_osc_freq_map = {}
        options.idle_state_record = {
            "pass_units": defaultdict(list),
        }
        return options

    def _divide_idle_point(self, physical_units: List[str]):
        for unit in physical_units:
            coupler_obj = self.context_manager.chip_data.cache_coupler.get(unit)

            # init drive freq and baseband freq
            coupler_obj.drive_freq = 5000
            coupler_obj.drive_XYwave.baseband_freq = 1050

            if coupler_obj.tunable is False:
                pyqlog.log("EXP", f"{unit} tunable false, idle point set 0")
                point_list = [0]
            else:
                max_point = coupler_obj.dc_max
                min_point = coupler_obj.dc_min

                if self.experiment_options.idle_points:
                    point_list = np.linspace(
                        max_point, min_point, self.experiment_options.idle_points
                    ).tolist()
                else:
                    step = abs(self.experiment_options.idle_step) or 0.01
                    if max_point > min_point:
                        step = -step
                    point_list = qarange(max_point, min_point, step)

            self.run_options.idle_point_map[unit] = point_list
            self.run_options.max_idle_length = max(
                len(point_list), self.run_options.max_idle_length
            )

    def _change_idle_point(self, idle_index: int, units: List[str]):
        ok_units = []
        for unit in units:
            point_list = self.run_options.idle_point_map.get(unit)
            if idle_index < len(point_list):
                idle_point = point_list[idle_index]
                ok_units.append(unit)
                coupler = self.context_manager.chip_data.cache_coupler.get(unit)
                coupler.drive_freq = 5000
                coupler.drive_XYwave.baseband_freq = 1050
                # coupler.inst.xy_gap = round(5000 - 1050, 3)
                coupler.idle_point = idle_point - coupler.dc_max
                pyqlog.info(f"Change {unit} idle point {coupler.idle_point}")

        self.run_options.pass_unit_f01_map.clear()
        self.run_options.pass_unit_osc_freq_map.clear()

        return ok_units

    def _record_experiment(
        self, exp_name: str, exp: EXP_TYPE, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if exp_name == "CouplerSpectrum":
            for unit in record.pass_units:
                # optimize: first check max peak
                peak_value = record.analysis_data.get(unit)["result"]["peaks"]
                if peak_value == "None":
                    peak_value = []
                peaks = [float(v) for v in peak_value]
                self.run_options.cs_freq_map[unit] = sorted(peaks, reverse=True)

        if exp_name == "CouplerRabiScanWidth":
            for unit in record.pass_units:
                pre_osc_freq = self.run_options.pass_unit_osc_freq_map.get(unit, 0)
                cur_osc_freq = float(record.analysis_data.get(unit)["result"]["freq"])

                if cur_osc_freq > pre_osc_freq:
                    coupler = self.context_manager.chip_data.get_physical_unit(unit)
                    self.run_options.pass_unit_osc_freq_map[unit] = max(
                        pre_osc_freq, cur_osc_freq
                    )
                    self.run_options.pass_unit_f01_map[unit] = coupler.drive_freq

        # self._save_data_to_json(self.run_options.idle_state_record, "status")

        return record

    def _rabi_check(self, units, process_bar: str):
        max_iter = 0

        # count max freq list
        for unit in units:
            max_iter = max(max_iter, len(self.run_options.cs_freq_map[unit]))

        # sweep rabi scan width
        for i in range(max_iter):
            p_units = []
            for unit in units:
                qs_freq_list = self.run_options.cs_freq_map[unit]
                if i < len(qs_freq_list):
                    coupler = self.context_manager.chip_data.get_physical_unit(unit)
                    coupler.drive_freq = qs_freq_list[i]
                    p_units.append(unit)

            self._run_flow(
                ["CouplerRabiScanWidth"],
                p_units,
                name=f"rabi check f01 ({process_bar})",
            )

            # # parallel group and baseband_freq divide
            # group_list = divide_qubit_parallel_group(
            #     p_units,
            #     self.context_manager.chip_data,
            #     **self.backend.system.parallel_divide,
            # )

            # for idx, group in enumerate(group_list):
            #     self._run_flow(
            #         ["RabiScanWidth"],
            #         group,
            #         name=f"Group-{idx + 1} rabi check f01 ({process_bar})",
            #     )

        # collect pass units
        pass_units = []
        for unit, f01 in self.run_options.pass_unit_f01_map.items():
            if unit in units:
                coupler = self.context_manager.chip_data.get_physical_unit(unit)
                coupler.drive_freq = f01
                pass_units.append(unit)

        return pass_units

    def _run_batch(self):
        all_units = self.experiment_options.physical_units

        set_coupler_idle_point(self, all_units)

        all_pass_units = []
        self._divide_idle_point(all_units)

        # sweep idle point
        for idle_idx in range(self.run_options.max_idle_length):
            process_bar = f"{idle_idx + 1}/{self.run_options.max_idle_length}"

            # set idle point
            if all_units:
                ready_units = self._change_idle_point(idle_idx, all_units)
                group_map = self.parallel_allocator_for_cc(ready_units)

                for gn, group in group_map.items():
                    pass_units = self._run_flow(
                        flows=self.experiment_options.cs_flows,
                        physical_units=group,
                        name=f"{process_bar}-{gn}-CS Find Peak",
                    )
                    if not pass_units:
                        continue

                    pass_units = self._rabi_check(pass_units, process_bar)

                    if pass_units:
                        pass_units = self._run_flow(
                            flows=self.experiment_options.flows,
                            physical_units=pass_units,
                            name=f"{process_bar}-{gn}-Coupler Idle Calibration",
                        )

                    if pass_units:
                        all_pass_units.extend(pass_units)
                        all_units = [
                            unit for unit in all_units if unit not in pass_units
                        ]

        pyqlog.log("FLOW", f"SUC Units: {all_pass_units}")
        self.backend.save_chip_data_to_db(all_pass_units)


class BatchCouplerACT1Calibration(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.ac_flows = [
            "CouplerT1",
            "CouplerT2RamseyCouplerACSpectrum",
        ]
        options.t1_flows = ["CouplerT1Spectrum"]
        options.gap_points = 30  # Scanning points between the highest and lowest point
        options.band_points = (
            5  # Number of external extension points for boundary point
        )
        options.t1_step = 20
        options.t1_points = 20
        options.set_global_qc = True
        options.set_validator("coupler_idle_mode", ["max", "middle"])
        options.coupler_idle_mode = "middle"
        options.physical_unit_type = BatchPhysicalUnitType.COUPLER
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.group_list = None
        return options

    def _batch_up(self):
        super()._batch_up()
        set_coupler_idle_point(self, self.experiment_options.physical_unit)

    def _auto_divide_amp_for_ac_spectrum(self, working_unit: List[str]):
        # auto calculate ac spectrum sweep amp list
        parallel_units = []
        for unit in working_unit:
            coupler = self.context_manager.chip_data.get_physical_unit(unit)
            if coupler.tunable is True:
                parallel_units.append(unit)
                max_point = coupler.dc_max
                min_point = coupler.dc_min
                idle_point = coupler.idle_point

                step = round(
                    (max_point - min_point) / self.experiment_options.gap_points, 5
                )
                mid_amp_list = qarange(max_point, min_point, -step)

                band_scope = step * self.experiment_options.band_points
                left_point, right_point = mid_amp_list[0], mid_amp_list[-1]
                max_band_amp_list = qarange(
                    left_point + step, left_point + band_scope, step
                )
                min_band_amp_list = qarange(
                    right_point - step, right_point - band_scope, -step
                )

                expect_amp_list = mid_amp_list + max_band_amp_list + min_band_amp_list
                expect_amp_list = [amp for amp in expect_amp_list if abs(amp) < 0.48]
                actual_amp_list = [
                    round(amp - idle_point - max_point, 5) for amp in expect_amp_list
                ]

                pyqlog.info(
                    f"{unit} scope info: max({max(expect_amp_list)}) min({min(expect_amp_list)})"
                    f" step({step}) points({len(expect_amp_list)})"
                )

                self.change_parallel_exec_exp_options(
                    exp_name="CouplerACSpectrum", unit=unit, z_amps=actual_amp_list
                )
            else:
                pyqlog.warning(f"{unit} tunable is false")

    def _auto_divide_freq_for_t1_spectrum(self, working_unit: List[str]):
        # calculate freq list
        for unit in working_unit:
            coupler = self.context_manager.chip_data.get_physical_unit(unit)
            freq_max, freq_min = get_bound_ac_spectrum(coupler)

            # bugfix 2024/01/18: round maybe cause bound freq out of range
            if self.experiment_options.t1_points:
                points = self.experiment_options.t1_points
                freq_list = np.linspace(
                    freq_min + 0.001, freq_max - 0.001, points
                ).tolist()
            else:
                t1_step = abs(self.experiment_options.t1_step) or 20
                freq_list = qarange(freq_min + 0.001, freq_max - 0.001, t1_step)

            self.change_parallel_exec_exp_options(
                exp_name="CouplerT1Spectrum", unit=unit, freq_list=freq_list
            )

            pyqlog.info(
                f"{unit} scope info: max({freq_max}) min({freq_min}) points({len(freq_list)})"
            )

    def _run_batch(self):
        physical_units = self.experiment_options.physical_units
        self._auto_divide_amp_for_ac_spectrum(physical_units)
        group_map = self.parallel_allocator_for_cc(physical_units)

        success_units = []

        for g, group in group_map.items():
            pass_units = self._run_flow(
                self.experiment_options.ac_flows,
                physical_units=group,
                name=f"{g}-CouplerACSpectrum Flow",
            )
            if pass_units:
                success_units.extend(pass_units)

        if success_units:
            self._auto_divide_freq_for_t1_spectrum(success_units)
            group_map = self.parallel_allocator_for_cc(physical_units)
            for g, group in group_map.items():
                pass_units = self._run_flow(
                    self.experiment_options.t1_flows,
                    physical_units=group,
                    name=f"{g}-CouplerT1Spectrum Flow",
                )

        pyqlog.info(f"SUC Unit: {success_units}")

        if success_units:
            self.backend.save_chip_data_to_db(success_units)


def set_coupler_idle_point(batch, physical_units: List[str]):
    coupler_idle_mode = batch.experiment_options.coupler_idle_mode
    max_point_unit = []

    for coupler in physical_units:
        obj = batch.backend.chip_data.cache_coupler.get(coupler)
        if not obj:
            continue
        max_point_unit.append(coupler)
        if coupler_idle_mode == "middle":
            obj.idle_point = (obj.dc_min - obj.dc_max) / 2
        else:
            obj.idle_point = 0

    if batch.experiment_options.set_global_qc is True:
        batch.backend.context_manager.global_options.max_point_unit = max_point_unit
