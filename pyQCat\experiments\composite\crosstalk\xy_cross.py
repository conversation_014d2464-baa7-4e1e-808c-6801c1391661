# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/08/09
# __author:       <PERSON> Fang

"""
Rough test XY Crosstalk composite experiment.
"""

import numpy as np

from ....analysis.library.xy_cross_analysis import XYCrosstalkAnalysis
from ....structures import MetaData, Options
from ....tools.utilities import qarange
from ....types import ExperimentRunMode, StandardContext
from ...composite_experiment import CompositeExperiment
from ...single import XYCrosstalkOnce


class XYCrosstalk(CompositeExperiment):
    """XY Crosstalk composite experiment."""

    _sub_experiment_class = XYCrosstalkOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment Options:
            target_name (str): Sub experiment target bit name.
            sweep_name(str): Scan field name.
            sweep_list(list): Scan field parameters value list.

        """
        options = super()._default_experiment_options()

        options.set_validator("target_name", str)
        options.set_validator(
            "sweep_name",
            [
                "amp_coe",
                "b_amp",
                "b_drive_power",
                "b_freq",
                "x_num",
                "x_width",
                "delay",
            ],
        )
        options.set_validator("sweep_list", list)

        options.target_name = ""
        options.sweep_name = "amp_coe"
        options.sweep_list = qarange(0.30, 0.40, 0.005)

        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options."""
        options = super()._default_run_options()

        options.phase_list = []
        options.support_context = [StandardContext.CM]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options."""
        options = super()._default_analysis_options()

        options.sub_key = "P1"
        options.x_label = "Amp (V)"
        options.y_label = "Phase"

        options.is_plot = True
        options.data_key = ["Phase", "omega1", "omega2", "x0"]
        return options

    def get_qubit_str(self):
        """Get qubit str, an args qubit for SaveFile class.

        Returns:
            qubit_str: string, use to create save data path.
        """
        target_name = self.experiment_options.target_name
        return target_name

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"sweep": self.experiment_options.sweep_name}
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        sweep_name = self.experiment_options.sweep_name

        self.set_analysis_options(x_label=sweep_name)
        self.set_run_options(
            x_data=self.experiment_options.sweep_list,
            analysis_class=XYCrosstalkAnalysis,
        )

    def _alone_save_result(self):
        """Alone save some special result."""
        sweep_name = self.experiment_options.sweep_name

        self.file.save_data(
            np.array(self.experiment_options.sweep_list),
            np.array(self.run_options.phase_list),
            name=f"{self}(row_{sweep_name}_phase_arr)",
        )

        self.file.save_data(
            np.array([self.analysis.results.target_amp.value]),
            np.array(self.analysis.results.target_phase.value),
            name=f"{self}(target_{sweep_name}_phase_arr)",
        )

    def _setup_child_experiment(self, exp: "XYCrosstalkOnce", index: int, value: float):
        """Set child_experiment some options."""
        target_name = self.experiment_options.target_name
        sweep_name = self.experiment_options.sweep_name

        length = len(self.experiment_options.sweep_list)
        once_options = {"target_name": target_name, sweep_name: value}

        xy_exp = exp
        xy_exp.run_options.index = index
        xy_exp.set_parent_file(self, f"{sweep_name}={value}", index, length)
        xy_exp.set_experiment_options(**once_options)
        self._check_simulator_data(xy_exp, index)

    def _handle_child_result(self, exp: "XYCrosstalkOnce"):
        # collect child experiment result and provide it for parent.
        xy_exp = exp

        # phase = xy_exp.analysis.results.t_value.value
        omega1 = xy_exp.analysis.results.omega1.value
        omega2 = xy_exp.analysis.results.omega2.value
        x0 = xy_exp.analysis.results.x0.value
        xy_exp.analysis.provide_for_parent.update(
            {"omega1": omega1, "omega2": omega2, "x0": x0}
        )

        # self.run_options.phase_list.append(phase)
        self.run_options.phase_list.append(x0)
