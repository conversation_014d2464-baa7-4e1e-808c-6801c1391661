# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/10/30
# __author:       <PERSON>

"""
Floquet calibration Single and Amp composite experiments.
"""

from copy import deepcopy

import numpy as np

from ....analysis.library_v2 import FloquetCaliSingleOnceAnalysis
from ....log import pyqlog
from ....pulse.pulse_function import half_pi_pulse
from ....pulse.pulse_lib import Constant
from ....structures import MetaData, Options
from ....tools.utilities import qarange
from ....types import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class FloquetCalibrationSingleOnce(TopExperiment):
    """FloquetCalibrationSingle once experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Define experiment options."""
        options = super()._default_experiment_options()
        options.set_validator("N_list", list)
        options.set_validator("changing_phase", float)
        options.set_validator("amp", float)

        options.N_list = qarange(1, 20, 1)
        options.changing_phase = 0.2 * np.pi
        options.amp = None

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Define run options."""
        options = super()._default_run_options()

        options.x_data = []
        options.analysis_class = FloquetCaliSingleOnceAnalysis
        options.support_context = [StandardContext.QC]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Define analysis options."""
        options = super()._default_analysis_options()

        options.set_validator("padding_factor", int)
        options.set_validator("region", int)
        options.set_validator("degree", int)

        options.padding_factor = 30  # 增加分辨率
        options.region = 10  # 多项式插值 选择峰值左右的点数
        options.degree = 4  # 多项式拟合的度

        options.figsize = (12, 12)
        options.raw_data_format = "plot"
        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "changing_phase": self.experiment_options.changing_phase,
            "amp": self.experiment_options.amp,
        }
        return metadata

    def _check_options(self):
        """Check option."""
        super()._check_options()
        N_list = self.experiment_options.N_list

        if not self.discriminator:
            pyqlog.warning(f"Please check `context` no discriminator: {self.discriminator}")

        self.set_experiment_options(data_type="I_Q")
        self.set_run_options(x_data=N_list)
        self.set_analysis_options(result_name=self.qubit.name)

    @staticmethod
    def set_xy_pulses(self):
        """Set xy pulses."""
        N_list = self.experiment_options.N_list
        amp = self.experiment_options.amp
        changing_phase = self.experiment_options.changing_phase

        xy_pulse_list = []

        x_pulse = half_pi_pulse(self.qubit)
        for N in N_list:
            pulse_obj = Constant(5.0, 0.0, name="XY")()
            for i in range(N):
                if amp is not None:
                    pulse_obj += deepcopy(x_pulse(amp=amp, phase=i * changing_phase))
                else:
                    pulse_obj += deepcopy(x_pulse(phase=i * changing_phase))
            xy_pulse_list.append(pulse_obj)

        qubit_obj = self.qubit
        self.play_pulse("XY", qubit_obj, xy_pulse_list)

    @staticmethod
    def set_z_pulses(self):
        """Set z pulses."""
        N_list = self.experiment_options.N_list

        z_pulse_list = []

        x_pulse = half_pi_pulse(self.qubit)
        x_width = x_pulse.width

        z_pulse = Constant(x_width, 0.0, name="Z")
        for N in N_list:
            pulse_obj = Constant(5.0, 0.0, name="Z")()
            for i in range(N):
                pulse_obj += deepcopy(z_pulse())
            z_pulse_list.append(pulse_obj)

        qubit_obj = self.qubit
        self.play_pulse("Z", qubit_obj, z_pulse_list)

    @staticmethod
    def update_instrument(self):
        """Update some instrument parameters."""
        N_list = self.experiment_options.N_list

        length = len(N_list)
        sweep_delay = self._pulse_time_list[:length]
        channel = self.qubit.readout_channel
        self.sweep_readout_trigger_delay(channel, sweep_delay)
