﻿pyQCat.analysis.library.ReadoutFreqCaliAnalysis
===============================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: ReadoutFreqCaliAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~ReadoutFreqCaliAnalysis.__init__
      ~ReadoutFreqCaliAnalysis.from_sub_analysis
      ~ReadoutFreqCaliAnalysis.run_analysis
      ~ReadoutFreqCaliAnalysis.set_options
      ~ReadoutFreqCaliAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~ReadoutFreqCaliAnalysis.analysis_datas
      ~ReadoutFreqCaliAnalysis.data_filter
      ~ReadoutFreqCaliAnalysis.drawer
      ~ReadoutFreqCaliAnalysis.experiment_data
      ~ReadoutFreqCaliAnalysis.has_child
      ~ReadoutFreqCaliAnalysis.options
      ~ReadoutFreqCaliAnalysis.quality
      ~ReadoutFreqCaliAnalysis.results
   
   