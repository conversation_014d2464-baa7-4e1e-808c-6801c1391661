﻿pyQCat.experiments.single.RabiScanAmp
=====================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: RabiScanAmp

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~RabiScanAmp.__init__
      ~RabiScanAmp.acquire_pulse
      ~RabiScanAmp.cal_fidelity
      ~RabiScanAmp.experiment_info
      ~RabiScanAmp.from_experiment_context
      ~RabiScanAmp.get_qubit_str
      ~RabiScanAmp.get_xy_pulse
      ~RabiScanAmp.jupyter_schedule
      ~RabiScanAmp.options_table
      ~RabiScanAmp.play_pulse
      ~RabiScanAmp.plot_schedule
      ~RabiScanAmp.run
      ~RabiScanAmp.set_analysis_options
      ~RabiScanAmp.set_experiment_options
      ~RabiScanAmp.set_multiple_IF
      ~RabiScanAmp.set_multiple_index
      ~RabiScanAmp.set_parent_file
      ~RabiScanAmp.set_run_options
      ~RabiScanAmp.set_sweep_order
      ~RabiScanAmp.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~RabiScanAmp.analysis
      ~RabiScanAmp.analysis_options
      ~RabiScanAmp.experiment_options
      ~RabiScanAmp.run_options
   
   