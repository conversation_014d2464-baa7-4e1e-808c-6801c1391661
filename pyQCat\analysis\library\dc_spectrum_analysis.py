# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/16
# __author:       SS Fang

"""
DC Spectrum analysis.
"""

import matplotlib.pyplot as plt
import numpy as np

from .ac_spectrum_analysis import ACSpectrumAnalysis
from ...structures import Options


class DCSpectrumAnalysis(ACSpectrumAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        """
        options = super()._default_options()

        options.subplots = (3, 1)
        options.curve_drawer.set_options(figsize=(20, 24))
        options.x_label = "DC [v]"
        options.y_label = [
            "Frequency [MHz]",
            "Amp",
            "Phase"
        ]

        options.pcolormesh_options = {
            "shading": "nearest",
            "cmap": plt.cm.get_cmap('viridis')
        }

        options.figsize = (12, 12)

        return options

    def _visualization(self):
        """Plot dc and frequency relationship."""
        super()._visualization()

        if self.has_child is True:
            x_arr = self.experiment_data.x_data
            y_arr = None
            amp_arr = []
            phase_arr = []
            for i, _ in enumerate(x_arr):
                child_data = self.experiment_data.child_data(index=i)
                if y_arr is None:
                    y_arr = child_data.x_data
                amp_arr.append(child_data.y_data.get("Amp"))
                phase_arr.append(child_data.y_data.get("Phase"))

            self.drawer.draw_color_map(x_arr, y_arr, np.array(amp_arr).T,
                                       ax_index=1,
                                       **self.options.pcolormesh_options)
            self.drawer.draw_color_map(x_arr, y_arr, np.array(phase_arr).T,
                                       ax_index=2,
                                       **self.options.pcolormesh_options)
