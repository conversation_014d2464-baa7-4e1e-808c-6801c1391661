# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.analysis.rst:2
msgid "pyQCat.analysis package"
msgstr ""

#: ../../source/api/pyQCat.analysis.rst:6
msgid "Module contents"
msgstr ""

#: of pyQCat.analysis:3
msgid "Experiment Analysis (:mod:`pyQCat.analysis`)"
msgstr ""

#: of pyQCat.analysis:5
msgid "Analysis modules, experiment data analysis related modules."
msgstr ""

#: of pyQCat.analysis:8
msgid "Modules"
msgstr ""

#: of pyQCat.analysis:12
msgid ":mod:`~pyQCat.analysis.algorithms`"
msgstr ""

#: of pyQCat.analysis:13
msgid "algorithms"
msgstr ""

#: of pyQCat.analysis:14
msgid ":mod:`~pyQCat.analysis.fit`"
msgstr ""

#: of pyQCat.analysis:15
msgid "curve fit"
msgstr ""

#: of pyQCat.analysis:16
msgid ":mod:`~pyQCat.analysis.library`"
msgstr ""

#: of pyQCat.analysis:17
msgid "Basic library for experiment data analysis"
msgstr ""

#: of pyQCat.analysis:18
msgid ":mod:`~pyQCat.analysis.visualization`"
msgstr ""

#: of pyQCat.analysis:19
msgid "visualization"
msgstr ""

#: of pyQCat.analysis:23
msgid "Base Classes"
msgstr ""

#: of pyQCat.analysis:29:<autosummary>:1
msgid ""
":py:obj:`TopAnalysis <pyQCat.analysis.TopAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis:29:<autosummary>:1
msgid "Abstract base class for analyzing Experiment data."
msgstr ""

#: of pyQCat.analysis:31
msgid "Standard Analysis Library"
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid ""
":py:obj:`CurveAnalysis <pyQCat.analysis.CurveAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid "Abstract superclass of curve analysis base classes."
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid ""
":py:obj:`TomographyAnalysis <pyQCat.analysis.TomographyAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid "Base analysis for state and process tomography experiments."
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid ""
":py:obj:`DecayAnalysis <pyQCat.analysis.DecayAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid "A class to analyze general exponential decay curve."
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid ""
":py:obj:`OscillationAnalysis <pyQCat.analysis.OscillationAnalysis>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid ""
"Oscillation analysis class based on a fit of the data to a cosine "
"function."
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid ""
":py:obj:`DumpedOscillationAnalysis "
"<pyQCat.analysis.DumpedOscillationAnalysis>`\\ \\(experiment\\_data\\[\\,"
" ...\\]\\)"
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid ""
"A class to analyze general exponential decay curve with sinusoidal "
"oscillation."
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid ""
":py:obj:`StateTomographyAnalysis "
"<pyQCat.analysis.StateTomographyAnalysis>`\\ \\(experiment\\_data\\[\\, "
"...\\]\\)"
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid "Analysis for state tomography experiments."
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid ""
":py:obj:`ProcessTomographyAnalysis "
"<pyQCat.analysis.ProcessTomographyAnalysis>`\\ \\(experiment\\_data\\[\\,"
" ...\\]\\)"
msgstr ""

#: of pyQCat.analysis:43:<autosummary>:1
msgid "Analysis for process tomography experiments."
msgstr ""

#: of pyQCat.analysis:45
msgid "Standard Analysis Specification"
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid ""
":py:obj:`FitModel <pyQCat.analysis.FitModel>`\\ \\(fit\\_func\\[\\, "
"name\\, plot\\_color\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid ""
":py:obj:`FitData <pyQCat.analysis.FitData>`\\ \\(popt\\, popt\\_keys\\, "
"variance\\, y\\_fit\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid "A dataclass to store the outcome of the fitting."
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid ""
":py:obj:`CurveAnalysisData <pyQCat.analysis.CurveAnalysisData>`\\ "
"\\(\\[x\\, y\\, fit\\_data\\]\\)"
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid "A dataclass to store the process of the analysis."
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid ""
":py:obj:`SingleShotAnalysisData "
"<pyQCat.analysis.SingleShotAnalysisData>`\\ \\(\\[source\\_data\\, "
"...\\]\\)"
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid ""
":py:obj:`AnalysisResult <pyQCat.analysis.AnalysisResult>`\\ \\(\\[name\\,"
" unit\\, value\\, extra\\]\\)"
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid "Dataclass for experiment analysis results"
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid ""
":py:obj:`ParameterRepr <pyQCat.analysis.ParameterRepr>`\\ \\(name\\[\\, "
"repr\\, unit\\]\\)"
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid "Detailed description of fitting parameter."
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid ""
":py:obj:`OptionsDict <pyQCat.analysis.OptionsDict>`\\ \\(parameters\\[\\,"
" defaults\\]\\)"
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid "General extended dictionary for fit options."
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid ""
":py:obj:`InitialGuesses <pyQCat.analysis.InitialGuesses>`\\ "
"\\(parameters\\[\\, defaults\\]\\)"
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid "Dictionary providing a float validation for initial guesses."
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid ""
":py:obj:`Boundaries <pyQCat.analysis.Boundaries>`\\ \\(parameters\\[\\, "
"defaults\\]\\)"
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid "Dictionary providing a validation for boundaries."
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid ""
":py:obj:`FitOptions <pyQCat.analysis.FitOptions>`\\ \\(parameters\\[\\, "
"default\\_p0\\, ...\\]\\)"
msgstr ""

#: of pyQCat.analysis:59:<autosummary>:1
msgid "Collection of fitting options."
msgstr ""

#~ msgid ""
#~ ":py:obj:`TopAnalysis <pyQCat.analysis.TopAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`CurveAnalysis <pyQCat.analysis.CurveAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`TomographyAnalysis "
#~ "<pyQCat.analysis.TomographyAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`DecayAnalysis <pyQCat.analysis.DecayAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`OscillationAnalysis "
#~ "<pyQCat.analysis.OscillationAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`DumpedOscillationAnalysis "
#~ "<pyQCat.analysis.DumpedOscillationAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`TopAnalysis <pyQCat.analysis.TopAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CurveAnalysis <pyQCat.analysis.CurveAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`TomographyAnalysis <pyQCat.analysis.TomographyAnalysis>`\\"
#~ " \\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`DecayAnalysis <pyQCat.analysis.DecayAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`OscillationAnalysis "
#~ "<pyQCat.analysis.OscillationAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`DumpedOscillationAnalysis "
#~ "<pyQCat.analysis.DumpedOscillationAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`StateTomographyAnalysis "
#~ "<pyQCat.analysis.StateTomographyAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`ProcessTomographyAnalysis "
#~ "<pyQCat.analysis.ProcessTomographyAnalysis>`\\ "
#~ "\\(experiment\\_data\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`FitModel <pyQCat.analysis.FitModel>`\\ "
#~ "\\(fit\\_func\\[\\, name\\, plot\\_color\\, "
#~ "...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`FitData <pyQCat.analysis.FitData>`\\ \\(popt\\, "
#~ "popt\\_keys\\, variance\\, y\\_fit\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`CurveAnalysisData <pyQCat.analysis.CurveAnalysisData>`\\"
#~ " \\(\\[x\\, y\\, fit\\_data\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`SingleShotAnalysisData "
#~ "<pyQCat.analysis.SingleShotAnalysisData>`\\ "
#~ "\\(\\[source\\_data\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`AnalysisResult <pyQCat.analysis.AnalysisResult>`\\ "
#~ "\\(\\[name\\, unit\\, value\\, extra\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`ParameterRepr <pyQCat.analysis.ParameterRepr>`\\ "
#~ "\\(name\\[\\, repr\\, unit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`OptionsDict <pyQCat.analysis.OptionsDict>`\\ "
#~ "\\(parameters\\[\\, defaults\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`InitialGuesses <pyQCat.analysis.InitialGuesses>`\\ "
#~ "\\(parameters\\[\\, defaults\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`Boundaries <pyQCat.analysis.Boundaries>`\\ "
#~ "\\(parameters\\[\\, defaults\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`FitOptions <pyQCat.analysis.FitOptions>`\\ "
#~ "\\(parameters\\[\\, default\\_p0\\, ...\\]\\)"
#~ msgstr ""

