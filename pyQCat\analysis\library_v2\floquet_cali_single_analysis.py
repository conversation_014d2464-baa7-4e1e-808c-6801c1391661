# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/10/30
# __author:       <PERSON> Fang

"""
FloquetCondition single q gate analysis.
"""

from typing import Union, List

import numpy as np

from scipy.fft import fft, fftfreq
from scipy.optimize import minimize_scalar
from scipy.signal import find_peaks

from ...analysis.curve_fit_analysis import CurveFitAnalysis
from ...analysis.fit.fit_models import floquet_fit_func, linear
from ...analysis.specification import ParameterRepr, FitModel, CurveAnalysisData, FitOptions
from ..standard_curve_analysis import StandardCurveAnalysis
from ...log import pyqlog
from ...structures import Options


class FloquetCaliSingleOnceAnalysis(StandardCurveAnalysis):
    """FloquetCondition single q gate once analysis."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.subplots = (3, 1)
        options.figsize = (12, 12)
        options.x_label = ["N", "Frequency (Hz)", "Frequency (Hz)"]
        options.y_label = ["P1", "Amplitude", "Amplitude"]
        options.sub_title = ["Origin data", "FFT part data", "Polynomial fit data"]

        options.padding_factor = 30  # 增加分辨率
        options.region = 10  # 多项式插值 选择峰值左右的点数
        options.degree = 4  # 多项式拟合的度

        options.extra_dict = {}  # no set
        options.result_parameters = [
            ParameterRepr(name="ou", repr="Ω", unit="", param_path=""),
        ]

        return options

    def _data_processing(self):
        """FFT process function."""
        padding_factor = self.options.padding_factor
        region = self.options.region
        degree = self.options.degree

        p_label = "P1"
        x_arr = self.experiment_data.x_data
        y_arr = self.experiment_data.y_data.get(p_label)
        N = len(y_arr) # 数据点数
        T = x_arr[1] - x_arr[0] # 采样间隔

        # 零填充以提高频率分辨率
        arr_padded = np.pad(y_arr, (0, N * (padding_factor - 1)), "constant")
        length = len(arr_padded)
        yf_padded = fft(arr_padded)
        xf_padded = fftfreq(length, T)

        select_idx = length // 2
        xf_arr = xf_padded[: select_idx]
        yf_arr = yf_padded[: select_idx]

        # find peaks
        peaks, _ = find_peaks(np.abs(yf_arr))
        peak_values = yf_arr[peaks]
        peaks_with_heights = [(np.abs(height), idx) for idx, height in zip(peaks, peak_values)]
        sorted_peaks = sorted(peaks_with_heights, key=lambda x: (-x[0], x[1]))
        top_two_peaks_original_indices = [idx for _, idx in sorted_peaks[:3]]
        k = top_two_peaks_original_indices[0]

        # polynomial fit
        new_yf_arr = 2.0 / length * np.abs(yf_arr)
        x_data = xf_arr[k - region:k + region]
        y_data = new_yf_arr[k - region:k + region]

        coefficients = np.polyfit(x_data, y_data, degree)
        polynomial = np.poly1d(coefficients)

        x_fit = np.linspace(min(x_data), max(x_data), 100)
        y_fit = polynomial(x_fit)

        xmin = x_data[0]
        xmax = x_data[-1]
        result = minimize_scalar(-polynomial, bounds=(xmin, xmax), method="bounded")

        self.results.ou.value = result.x
        self.options.extra_dict = {
            "xf_arr": xf_arr,
            "yf_arr": new_yf_arr,
            "x_data": x_data,
            "y_data": y_data,
            "x_fit": x_fit,
            "y_fit": y_fit,
        }

    def _visualization(self):
        """Plot."""
        # Set plot title.
        self.drawer.set_options(title=self._description())

        p_label = "P1"
        x_arr = self.experiment_data.x_data
        y_arr = self.experiment_data.y_data.get(p_label)

        extra_dict = self.options.extra_dict
        ou = self.results.ou.value

        # plot population
        self.drawer.draw_raw_data(x_arr, y_arr, ax_index=0)

        # plot fft part data
        self.drawer.draw_raw_data(
            x_data=extra_dict.get("xf_arr"),
            y_data=extra_dict.get("yf_arr"),
            ax_index=1,
        )
        self.drawer.draw_axv_line(x=ou, ax_index=1, c="g", linestyle="--")

        # plot polynomial fit data
        self.drawer.draw_scatter_point(
            x_data=extra_dict.get("x_data"),
            y_data=extra_dict.get("y_data"),
            ax_index=2,
        )
        self.drawer.draw_fit_line(
            x_data=extra_dict.get("x_fit"),
            y_data=extra_dict.get("y_fit"),
            ax_index=2,
        )
        self.drawer.draw_axv_line(x=ou, ax_index=2, c="g", linestyle="--")


class FloquetCaliSingleAnalysis(CurveFitAnalysis):
    """Floquet calibrate single Analysis."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.theta = 0.5  # 拟合参数 theta 初始值
        options.zeta0 = 0.0  # 拟合参数 zeta0 初始值
        options.x_label = "changing_phase"
        options.quality_bounds = [0.95, 0.90, 0.7]

        options.fit_model = FitModel(fit_func=floquet_fit_func)
        options.result_parameters = [
            ParameterRepr(name="theta", repr="theta", unit="", param_path=""),
            ParameterRepr(name="zeta0", repr="zeta0", unit="", param_path=""),
        ]

        return options

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,  # pylint: disable=unused-argument
    ) -> Union[FitOptions, List[FitOptions]]:
        """Create algorithmic guess with analysis options and curve data.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        # fit_params = {"theta": 0.0, "zeta0": 0.0}
        fit_params = {
            "theta": self.options.theta,
            "zeta0": self.options.zeta0,
        }
        fit_opt.p0.set_if_empty(**fit_params)
        return fit_opt

    def _extract_result(self):
        """Extract analysis results from important fit parameters."""
        super()._extract_result()
        result_name = self.options.result_name
        theta = self.results.theta.value
        zeta0 = self.results.zeta0.value

        new_theta = theta / np.pi
        new_zeta0 = zeta0 / np.pi
        pyqlog.info(f"{result_name} theta: {new_theta}, zeta0: {new_zeta0}")
        self.results.theta.value = new_theta
        self.results.zeta0.value = new_zeta0


class FloquetAmpOptimizeAnalysis(CurveFitAnalysis):
    """Floquet optimize X/2 amp Analysis."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.x_label = "X/2 Amp(V)"
        options.quality_bounds = [0.95, 0.90, 0.7]
        options.theta = 0.5

        options.fit_model = FitModel(fit_func=linear)
        options.result_parameters = [
            ParameterRepr(name="amp", repr="X/2 amp", unit="V", param_path="Qubit.XYwave.Xpi2"),
            ParameterRepr(name="zeta", repr="target zeta", unit="", param_path="Qubit.zeta"),
        ]

        return options

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,  # pylint: disable=unused-argument
    ) -> Union[FitOptions, List[FitOptions]]:
        """Create algorithmic guess with analysis options and curve data.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        fit_params = {"k": 0.0, "baseline": 0.0}
        fit_opt.p0.set_if_empty(**fit_params)
        return fit_opt

    def _extract_result(self):
        """Extract analysis results from important fit parameters.

        Args:
            data_key (str): The basis for selecting data.
        """
        theta = self.options.theta
        result_name = self.options.result_name

        theta_analysis_data = self.analysis_datas["theta"]
        theta_popt = theta_analysis_data.fit_data.popt
        theta_k, theta_b = theta_popt[0], theta_popt[1]
        amp = (theta - theta_b) / theta_k

        zeta_analysis_data = self.analysis_datas["zeta"]
        zeta_popt = zeta_analysis_data.fit_data.popt
        zeta_k, zeta_b = zeta_popt[0], zeta_popt[1]
        zeta = zeta_k * amp + zeta_b

        pyqlog.info(f"{result_name} set theta: {theta}, so amp: {amp}, zeta: {zeta}")
        self.results.amp.value = amp
        self.results.zeta.value = zeta

    def _visualization(self):
        """Plot."""
        super()._visualization()
        amp = self.results.amp.value

        # get experiment keys.
        exp_keys = list(self.experiment_data.y_data.keys())
        for data_key, analysis_data in self.analysis_datas.items():
            draw_index = exp_keys.index(data_key)
            self.drawer.draw_axv_line(
                x=amp, ax_index=draw_index, c="g", linestyle="--"
            )
