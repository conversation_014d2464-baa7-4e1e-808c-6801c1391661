# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.parse_yaml_args.rst:2
msgid "pyQCat.tools.parse\\_yaml\\_args"
msgstr ""

#: of pyQCat.tools.parse_yaml.parse_yaml_args:1
msgid ""
"Parse yaml config info. :param yaml_path: yaml file path, default "
"`config/exp.yaml`"
msgstr ""

#: of pyQCat.tools.parse_yaml.parse_yaml_args
msgid "Returns"
msgstr ""

#: of pyQCat.tools.parse_yaml.parse_yaml_args:4
msgid "ExpArgs object"
msgstr ""

#: of pyQCat.tools.parse_yaml.parse_yaml_args
msgid "Return type"
msgstr ""

