# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/23
# __author:       <PERSON><PERSON><PERSON>
"""RamseyF12 experiment for calibrating qubit f02 frequency."""

import copy
from copy import deepcopy
from typing import List

import numpy as np

from ....pulse import Constant
from ....pulse.pulse_function import half_f12_pi_pulse, pi_pulse
from .ramsey import Ramsey


class RamseyF12(Ramsey):
    """Ramsey experiment to measure the frequency of a qubit."""

    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        xy_pulse_list = RamseyF12.get_xy_pulse(
            builder.qubit,
            builder.experiment_options.delays,
            builder.experiment_options.fringe,
        )
        builder.play_pulse("XY", builder.qubit, xy_pulse_list)

    @staticmethod
    def set_z_pulse(builder):
        # todo f12 z pulse
        pass

    @staticmethod
    def get_xy_pulse(qubit, delays: List, fringe: float):
        """Get XY line wave."""
        f01_x = pi_pulse(qubit)()

        pulse_list = []
        for delay in delays:
            front_drag = half_f12_pi_pulse(qubit)
            center_delay = Constant(delay, 0, "XY")
            rear_drag = copy.deepcopy(front_drag)

            ramsey_pulse = (
                deepcopy(f01_x)
                + front_drag()
                + center_delay()
                + rear_drag(phase=2 * np.pi * fringe * 1e6 / 1e9 * delay)
                + deepcopy(f01_x)
            )

            ramsey_pulse.bit = qubit.bit
            ramsey_pulse.sweep = "sweep delay"

            pulse_list.append(ramsey_pulse)

        return pulse_list

    def _check_options(self):
        if self.discriminator and "2" in self.discriminator.level_str:
            self.set_run_options(acquisition_key="weirdo")
            self.set_experiment_options(is_dynamic=0)

        super()._check_options()
