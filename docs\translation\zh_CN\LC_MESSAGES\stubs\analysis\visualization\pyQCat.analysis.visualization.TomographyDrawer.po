# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:2
msgid "pyQCat.analysis.visualization.TomographyDrawer"
msgstr ""

#: of pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer:1
msgid ""
"Bases: "
":py:class:`~pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer`"
msgstr ""

#: of pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer:1
msgid "Tomography drawer for MatplotLib backend."
msgstr "量子层析绘图器"

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.analysis.visualization.TomographyDrawer.__init__>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`draw_color_map "
"<pyQCat.analysis.visualization.TomographyDrawer.draw_color_map>`\\ "
"\\(x\\_data\\, y\\_data\\, z\\_data\\, \\*\\*options\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid ""
"using norm to map colormaps onto data in non-linear ways :type x_data: "
":py:class:`~typing.Sequence`\\[:py:class:`float`] :param x_data: :type "
"y_data: :py:class:`~typing.Sequence`\\[:py:class:`float`] :param y_data: "
":type z_data: :py:class:`~typing.Sequence`\\[:py:class:`float`] :param "
"z_data:"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`draw_filter_data "
"<pyQCat.analysis.visualization.TomographyDrawer.draw_filter_data>`\\ "
"\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid "Draw smooth data"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`draw_fit_line "
"<pyQCat.analysis.visualization.TomographyDrawer.draw_fit_line>`\\ "
"\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid "Draw fit line."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`draw_matrix "
"<pyQCat.analysis.visualization.TomographyDrawer.draw_matrix>`\\ "
"\\(matrix\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.draw_matrix:1
msgid "Draw matrix."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`draw_raw_data "
"<pyQCat.analysis.visualization.TomographyDrawer.draw_raw_data>`\\ "
"\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid "Draw raw data."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`draw_text "
"<pyQCat.analysis.visualization.TomographyDrawer.draw_text>`\\ "
"\\(\\[ax\\_index\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid "Add text to the axes."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`format_canvas "
"<pyQCat.analysis.visualization.TomographyDrawer.format_canvas>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.format_canvas:1
msgid "Add a title to the canvas."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`initialize_canvas "
"<pyQCat.analysis.visualization.TomographyDrawer.initialize_canvas>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.initialize_canvas:1
msgid "Initialize the drawing canvas."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_options "
"<pyQCat.analysis.visualization.TomographyDrawer.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:30:<autosummary>:1
msgid "Set the drawing options."
msgstr ""

#: ../../source/stubs/analysis/visualization/pyQCat.analysis.visualization.TomographyDrawer.rst:32
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:1:<autosummary>:1
msgid ":py:obj:`figure <pyQCat.analysis.visualization.TomographyDrawer.figure>`\\"
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:1:<autosummary>:1
msgid "Return figure object handler to be saved in the database."
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:1:<autosummary>:1
msgid ""
":py:obj:`options "
"<pyQCat.analysis.visualization.TomographyDrawer.options>`\\"
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:1:<autosummary>:1
msgid "Return the drawing options."
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:1
msgid "Create the Analysis options, and set some fields."
msgstr "创建绘图选型，并设置一些属性"

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:3
msgid "**Analysis Options**:"
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:5
msgid "**labels (List)** - Subplot title, default is ``[real, image]``."
msgstr "**labels (List)** - 子图标题，默认为 ``[real, image]``."

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:7
msgid "**figsize (Tuple)** - Canvas size, defaultn is ``(14, 7)``"
msgstr "**figsize (Tuple)** - 画布尺寸，默认为 ``(14, 7)``"

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:9
msgid "**sup_title (str)** - Top-level canvas title, ``QPT`` or ``QST``"
msgstr "**sup_title (str)** - 顶层画布标题, ``QPT`` or ``QST``"

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options
msgid "Return type"
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:11
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options
msgid "Returns"
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer._default_options:12
msgid "Tomography drawer options."
msgstr "层析画布选项"

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.draw_matrix
msgid "Parameters"
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.draw_matrix:4
msgid "goal maxtrix."
msgstr "目标矩阵"

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.draw_matrix
msgid "Raises"
msgstr ""

#: of
#: pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer.draw_matrix:7
msgid "wrong dimension"
msgstr "维度错误，必须为方阵"

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.__init__>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`draw_color_map "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.draw_color_map>`\\ "
#~ "\\(x\\_data\\, y\\_data\\, z\\_data\\, "
#~ "\\*\\*options\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`draw_filter_data "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.draw_filter_data>`\\"
#~ " \\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`draw_fit_line "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.draw_fit_line>`\\ "
#~ "\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`draw_matrix "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.draw_matrix>`\\ "
#~ "\\(matrix\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`draw_raw_data "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.draw_raw_data>`\\ "
#~ "\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`draw_text "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.draw_text>`\\ "
#~ "\\(\\[ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`format_canvas "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.format_canvas>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid "Final cleanup for the canvas appearance."
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`initialize_canvas "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.initialize_canvas>`\\"
#~ " \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_options "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`figure "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.figure>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`options "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.options>`\\"
#~ msgstr ""

#~ msgid "Return default draw options."
#~ msgstr ""

#~ msgid "Draw Options:"
#~ msgstr ""

#~ msgid ""
#~ "axis (Any): Arbitrary object that can"
#~ " be used as a drawing canvas. "
#~ "subplots (Tuple[int, int]): Number of "
#~ "rows and columns when the experimental"
#~ msgstr ""

#~ msgid "result is drawn in the multiple windows."
#~ msgstr ""

#~ msgid ""
#~ "xlabel (Union[str, List[str]]): X-axis label"
#~ " string of the output figure."
#~ msgstr ""

#~ msgid ""
#~ "If there are multiple columns in "
#~ "the canvas, this could be a list"
#~ " of labels."
#~ msgstr ""

#~ msgid ""
#~ "ylabel (Union[str, List[str]]): Y-axis label"
#~ " string of the output figure."
#~ msgstr ""

#~ msgid ""
#~ "If there are multiple rows in the"
#~ " canvas, this could be a list "
#~ "of labels."
#~ msgstr ""

#~ msgid "xlim (Tuple[float, float]): Min and max value of the horizontal axis."
#~ msgstr ""

#~ msgid ""
#~ "If not provided, it is automatically "
#~ "scaled based on the input data "
#~ "points."
#~ msgstr ""

#~ msgid "ylim (Tuple[float, float]): Min and max value of the vertical axis."
#~ msgstr ""

#~ msgid "xval_unit (str): SI unit of x values. No prefix is needed here."
#~ msgstr ""

#~ msgid ""
#~ "For example, when the x values "
#~ "represent time, this option will be "
#~ "just \"s\" rather than \"ms\". In "
#~ "the output figure, the prefix is "
#~ "automatically selected based on the "
#~ "maximum value in this axis. If "
#~ "your x values are in [1e-3, 1e-4],"
#~ " they are displayed as [1 ms, "
#~ "10 ms]. This option is likely "
#~ "provided by the analysis class rather"
#~ " than end-users. However, users can"
#~ " still override if they need "
#~ "different unit notation. By default, "
#~ "this option is set to ``None``, "
#~ "and no scaling is applied. If "
#~ "nothing is provided, the axis numbers"
#~ " will be displayed in the scientific"
#~ " notation."
#~ msgstr ""

#~ msgid ""
#~ "yval_unit (str): Unit of y values. "
#~ "See ``xval_unit`` for details. figsize "
#~ "(Tuple[int, int]): A tuple of two "
#~ "numbers representing the size of"
#~ msgstr ""

#~ msgid ""
#~ "the output figure (width, height). Note"
#~ " that this is applicable only when"
#~ " ``axis`` object is not provided. If"
#~ " any canvas object is provided, the"
#~ " figure size associated with the axis"
#~ " is preferentially applied."
#~ msgstr ""

#~ msgid ""
#~ "legend_loc (str): Vertical and horizontal "
#~ "location of the curve legend window "
#~ "in"
#~ msgstr ""

#~ msgid ""
#~ "a single string separated by a "
#~ "space. This defaults to ``center "
#~ "right``. Vertical position can be "
#~ "``upper``, ``center``, ``lower``. Horizontal "
#~ "position can be ``right``, ``center``, "
#~ "``left``."
#~ msgstr ""

#~ msgid ""
#~ "tick_label_size (int): Size of text "
#~ "representing the axis tick numbers. "
#~ "axis_label_size (int): Size of text "
#~ "representing the axis label. fit_report_rpos"
#~ " (Tuple[int, int]): A tuple of "
#~ "numbers showing the location of"
#~ msgstr ""

#~ msgid ""
#~ "the fit report window. These numbers "
#~ "are horizontal and vertical position of"
#~ " the top left corner of the "
#~ "window in the relative coordinate on "
#~ "the output figure, i.e. ``[0, 1]``. "
#~ "The fit report window shows the "
#~ "selected fit parameters and the reduced"
#~ " chi-squared value."
#~ msgstr ""

#~ msgid ""
#~ "fit_report_text_size (int): Size of text "
#~ "in the fit report window. plot_sigma "
#~ "(List[Tuple[float, float]]): A list of "
#~ "two number tuples"
#~ msgstr ""

#~ msgid ""
#~ "showing the configuration to write "
#~ "confidence intervals for the fit curve."
#~ " The first argument is the relative"
#~ " sigma (n_sigma), and the second "
#~ "argument is the transparency of the "
#~ "interval plot in ``[0, 1]``. Multiple"
#~ " n_sigma intervals can be drawn for"
#~ " the single curve."
#~ msgstr ""

#~ msgid ""
#~ "text_pos (List[Tuple]): The position of "
#~ "the annotations to place on the "
#~ "figure. text_rp (List[str]): The contents "
#~ "of the annotations to place on the"
#~ " figure. text_key (List[str]): Indicates "
#~ "the data key to annotate."
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.analysis.visualization.base_drawer.BaseCurveDrawer`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.__init__>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`draw_color_map "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.draw_color_map>`\\ "
#~ "\\(x\\_data\\, y\\_data\\, z\\_data\\, "
#~ "\\*\\*options\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`draw_filter_data "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.draw_filter_data>`\\"
#~ " \\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`draw_fit_line "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.draw_fit_line>`\\ "
#~ "\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`draw_matrix "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.draw_matrix>`\\ "
#~ "\\(matrix\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`draw_raw_data "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.draw_raw_data>`\\ "
#~ "\\(x\\_data\\, y\\_data\\[\\, ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`draw_text "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.draw_text>`\\ "
#~ "\\(\\[ax\\_index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`format_canvas "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.format_canvas>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`initialize_canvas "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.initialize_canvas>`\\"
#~ " \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":obj:`figure <pyQCat.analysis.visualization.TomographyDrawer.figure>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options "
#~ "<pyQCat.analysis.visualization.TomographyDrawer.options>`\\"
#~ msgstr ""

