<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>700</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>800</width>
    <height>500</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Permission Manage</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>800</width>
     <height>500</height>
    </size>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QWidget" name="main_widget" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>600</width>
        <height>459</height>
       </size>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QTabWidget" name="tabWidget">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>600</width>
           <height>459</height>
          </size>
         </property>
         <property name="toolTip">
          <string>Platform Manage</string>
         </property>
         <property name="whatsThis">
          <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Platform Manage&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
         </property>
         <property name="currentIndex">
          <number>0</number>
         </property>
         <widget class="QWidget" name="tab_platform">
          <property name="minimumSize">
           <size>
            <width>600</width>
            <height>459</height>
           </size>
          </property>
          <attribute name="title">
           <string>Platform Permission</string>
          </attribute>
          <layout class="QHBoxLayout" name="horizontalLayout_5">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QWidget" name="widget" native="true">
             <property name="minimumSize">
              <size>
               <width>600</width>
               <height>459</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_6">
              <property name="spacing">
               <number>0</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QSplitter" name="p_main_splitter">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>600</width>
                  <height>459</height>
                 </size>
                </property>
                <property name="frameShadow">
                 <enum>QFrame::Shadow::Plain</enum>
                </property>
                <property name="midLineWidth">
                 <number>0</number>
                </property>
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <widget class="QWidget" name="p_left" native="true">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>400</width>
                   <height>459</height>
                  </size>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_2">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <widget class="QSplitter" name="p_splitter">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>400</width>
                      <height>459</height>
                     </size>
                    </property>
                    <property name="orientation">
                     <enum>Qt::Orientation::Vertical</enum>
                    </property>
                    <widget class="QWidget" name="p_condion_widget" native="true">
                     <property name="minimumSize">
                      <size>
                       <width>400</width>
                       <height>100</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>200</height>
                      </size>
                     </property>
                     <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="4,1">
                      <item>
                       <widget class="QWidget" name="p_condition_context" native="true">
                        <layout class="QGridLayout" name="gridLayout" columnstretch="1,5">
                         <item row="0" column="0">
                          <widget class="QRadioButton" name="p_radio_group">
                           <property name="text">
                            <string>Group</string>
                           </property>
                          </widget>
                         </item>
                         <item row="3" column="1">
                          <widget class="QComboBox" name="p_plat_comb">
                           <property name="maximumSize">
                            <size>
                             <width>16777215</width>
                             <height>16777215</height>
                            </size>
                           </property>
                          </widget>
                         </item>
                         <item row="5" column="0">
                          <widget class="QLabel" name="p_perm_lab">
                           <property name="text">
                            <string>Permission</string>
                           </property>
                          </widget>
                         </item>
                         <item row="5" column="1">
                          <widget class="QComboBox" name="p_perm_comb"/>
                         </item>
                         <item row="4" column="1">
                          <widget class="QComboBox" name="p_user_comb"/>
                         </item>
                         <item row="4" column="0">
                          <widget class="QLabel" name="p_user_lab">
                           <property name="text">
                            <string>User</string>
                           </property>
                          </widget>
                         </item>
                         <item row="3" column="0">
                          <widget class="QLabel" name="p_plat_lab">
                           <property name="text">
                            <string>Platform</string>
                           </property>
                          </widget>
                         </item>
                         <item row="0" column="1">
                          <widget class="QRadioButton" name="p_radio_user">
                           <property name="text">
                            <string>User</string>
                           </property>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QWidget" name="p_query_widget" native="true">
                        <layout class="QVBoxLayout" name="verticalLayout_4">
                         <item>
                          <spacer name="verticalSpacer">
                           <property name="orientation">
                            <enum>Qt::Orientation::Vertical</enum>
                           </property>
                           <property name="sizeHint" stdset="0">
                            <size>
                             <width>20</width>
                             <height>40</height>
                            </size>
                           </property>
                          </spacer>
                         </item>
                         <item>
                          <widget class="QPushButton" name="p_query_button">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>100</width>
                             <height>30</height>
                            </size>
                           </property>
                           <property name="maximumSize">
                            <size>
                             <width>16777215</width>
                             <height>30</height>
                            </size>
                           </property>
                           <property name="text">
                            <string>Query</string>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <spacer name="verticalSpacer_2">
                           <property name="orientation">
                            <enum>Qt::Orientation::Vertical</enum>
                           </property>
                           <property name="sizeHint" stdset="0">
                            <size>
                             <width>20</width>
                             <height>40</height>
                            </size>
                           </property>
                          </spacer>
                         </item>
                        </layout>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                    <widget class="QWidget" name="p_table_widget" native="true">
                     <property name="minimumSize">
                      <size>
                       <width>400</width>
                       <height>300</height>
                      </size>
                     </property>
                     <layout class="QVBoxLayout" name="verticalLayout_3">
                      <item>
                       <widget class="QLineEdit" name="p_search"/>
                      </item>
                      <item>
                       <widget class="QTableWidget" name="p_tableWidget">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                   </widget>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="p_right" native="true">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>200</width>
                   <height>459</height>
                  </size>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout" stretch="8,1">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <widget class="QTreeWidget" name="p_treeWidget">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>200</width>
                      <height>400</height>
                     </size>
                    </property>
                    <column>
                     <property name="text">
                      <string notr="true">1</string>
                     </property>
                    </column>
                   </widget>
                  </item>
                  <item>
                   <widget class="QWidget" name="p_save_widget" native="true">
                    <property name="minimumSize">
                     <size>
                      <width>200</width>
                      <height>0</height>
                     </size>
                    </property>
                    <layout class="QHBoxLayout" name="horizontalLayout_2">
                     <item>
                      <widget class="QPushButton" name="p_save_button">
                       <property name="sizePolicy">
                        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                         <horstretch>2</horstretch>
                         <verstretch>0</verstretch>
                        </sizepolicy>
                       </property>
                       <property name="maximumSize">
                        <size>
                         <width>100</width>
                         <height>30</height>
                        </size>
                       </property>
                       <property name="contextMenuPolicy">
                        <enum>Qt::ContextMenuPolicy::DefaultContextMenu</enum>
                       </property>
                       <property name="layoutDirection">
                        <enum>Qt::LayoutDirection::LeftToRight</enum>
                       </property>
                       <property name="text">
                        <string>Save</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="tab_normal">
          <property name="minimumSize">
           <size>
            <width>600</width>
            <height>459</height>
           </size>
          </property>
          <attribute name="title">
           <string>Normal Permission</string>
          </attribute>
          <layout class="QHBoxLayout" name="horizontalLayout_7">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QWidget" name="widget_2" native="true">
             <layout class="QHBoxLayout" name="horizontalLayout_10">
              <property name="spacing">
               <number>0</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QSplitter" name="n_main_splitter">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>600</width>
                  <height>459</height>
                 </size>
                </property>
                <property name="frameShadow">
                 <enum>QFrame::Shadow::Plain</enum>
                </property>
                <property name="midLineWidth">
                 <number>0</number>
                </property>
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <widget class="QWidget" name="n_left" native="true">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>400</width>
                   <height>459</height>
                  </size>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_5">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <widget class="QSplitter" name="n_splitter">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>400</width>
                      <height>459</height>
                     </size>
                    </property>
                    <property name="orientation">
                     <enum>Qt::Orientation::Vertical</enum>
                    </property>
                    <widget class="QWidget" name="n_condion_widget" native="true">
                     <property name="minimumSize">
                      <size>
                       <width>400</width>
                       <height>100</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>200</height>
                      </size>
                     </property>
                     <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="4,1">
                      <item>
                       <widget class="QWidget" name="n_condition_context" native="true">
                        <layout class="QGridLayout" name="gridLayout_2" columnstretch="1,5">
                         <item row="0" column="0">
                          <widget class="QRadioButton" name="n_radio_group">
                           <property name="text">
                            <string>Group</string>
                           </property>
                          </widget>
                         </item>
                         <item row="0" column="1">
                          <widget class="QRadioButton" name="n_radio_user">
                           <property name="text">
                            <string>User</string>
                           </property>
                          </widget>
                         </item>
                         <item row="4" column="0">
                          <widget class="QLabel" name="n_perm_lab">
                           <property name="text">
                            <string>Permission</string>
                           </property>
                          </widget>
                         </item>
                         <item row="3" column="1">
                          <widget class="QComboBox" name="n_user_comb"/>
                         </item>
                         <item row="3" column="0">
                          <widget class="QLabel" name="n_user_lab">
                           <property name="text">
                            <string>User</string>
                           </property>
                          </widget>
                         </item>
                         <item row="4" column="1">
                          <widget class="QComboBox" name="n_perm_comb"/>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QWidget" name="n_query_widget" native="true">
                        <layout class="QVBoxLayout" name="verticalLayout_6">
                         <item>
                          <spacer name="verticalSpacer_3">
                           <property name="orientation">
                            <enum>Qt::Orientation::Vertical</enum>
                           </property>
                           <property name="sizeHint" stdset="0">
                            <size>
                             <width>20</width>
                             <height>40</height>
                            </size>
                           </property>
                          </spacer>
                         </item>
                         <item>
                          <widget class="QPushButton" name="n_query_button">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>100</width>
                             <height>30</height>
                            </size>
                           </property>
                           <property name="maximumSize">
                            <size>
                             <width>16777215</width>
                             <height>30</height>
                            </size>
                           </property>
                           <property name="text">
                            <string>Query</string>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <spacer name="verticalSpacer_4">
                           <property name="orientation">
                            <enum>Qt::Orientation::Vertical</enum>
                           </property>
                           <property name="sizeHint" stdset="0">
                            <size>
                             <width>20</width>
                             <height>40</height>
                            </size>
                           </property>
                          </spacer>
                         </item>
                        </layout>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                    <widget class="QWidget" name="n_table_widget" native="true">
                     <property name="minimumSize">
                      <size>
                       <width>400</width>
                       <height>300</height>
                      </size>
                     </property>
                     <layout class="QVBoxLayout" name="verticalLayout_7">
                      <item>
                       <widget class="QLineEdit" name="n_search"/>
                      </item>
                      <item>
                       <widget class="QTableWidget" name="n_tableWidget">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                   </widget>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="n_right" native="true">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>200</width>
                   <height>459</height>
                  </size>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_8" stretch="8,1">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <widget class="QTreeWidget" name="n_treeWidget">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>200</width>
                      <height>400</height>
                     </size>
                    </property>
                    <column>
                     <property name="text">
                      <string notr="true">1</string>
                     </property>
                    </column>
                   </widget>
                  </item>
                  <item>
                   <widget class="QWidget" name="n_save_widget" native="true">
                    <property name="minimumSize">
                     <size>
                      <width>200</width>
                      <height>0</height>
                     </size>
                    </property>
                    <layout class="QHBoxLayout" name="horizontalLayout_9">
                     <item>
                      <widget class="QPushButton" name="n_save_button">
                       <property name="sizePolicy">
                        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                         <horstretch>2</horstretch>
                         <verstretch>0</verstretch>
                        </sizepolicy>
                       </property>
                       <property name="maximumSize">
                        <size>
                         <width>100</width>
                         <height>30</height>
                        </size>
                       </property>
                       <property name="contextMenuPolicy">
                        <enum>Qt::ContextMenuPolicy::DefaultContextMenu</enum>
                       </property>
                       <property name="layoutDirection">
                        <enum>Qt::LayoutDirection::LeftToRight</enum>
                       </property>
                       <property name="text">
                        <string>Save</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
