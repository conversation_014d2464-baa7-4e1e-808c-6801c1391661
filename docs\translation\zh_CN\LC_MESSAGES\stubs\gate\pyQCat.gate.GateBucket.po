# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:58+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/gate/pyQCat.gate.GateBucket.rst:2
msgid "pyQCat.gate.GateBucket"
msgstr ""

#: of pyQCat.gate.notable_gate.GateBucket:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateBucket.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateBucket.rst:26:<autosummary>:1
msgid ":py:obj:`__init__ <pyQCat.gate.GateBucket.__init__>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateBucket.rst:26:<autosummary>:1
msgid ""
":py:obj:`bind_cz_gates <pyQCat.gate.GateBucket.bind_cz_gates>`\\ "
"\\(cz\\_obj\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateBucket.rst:26:<autosummary>:1
msgid ""
":py:obj:`bind_single_gates <pyQCat.gate.GateBucket.bind_single_gates>`\\ "
"\\(qubit\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateBucket.rst:26:<autosummary>:1
msgid ""
":py:obj:`get_matrix <pyQCat.gate.GateBucket.get_matrix>`\\ "
"\\(gate\\_name\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateBucket.rst:26:<autosummary>:1
msgid ""
":py:obj:`get_xy_pulse <pyQCat.gate.GateBucket.get_xy_pulse>`\\ "
"\\(qubit\\, gate\\_name\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateBucket.rst:26:<autosummary>:1
msgid ""
":py:obj:`get_z_pulse <pyQCat.gate.GateBucket.get_z_pulse>`\\ \\(qubit\\, "
"gate\\_name\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateBucket.rst:28
msgid "Attributes"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateBucket.rst:31:<autosummary>:1
msgid ":py:obj:`pauli_matrix <pyQCat.gate.GateBucket.pauli_matrix>`\\"
msgstr ""

