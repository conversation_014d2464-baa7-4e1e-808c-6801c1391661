# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:2
msgid "pyQCat.qubit.Coupler"
msgstr ""

#: of pyQCat.qubit.qubit.Coupler:1
msgid "Bases: :py:class:`~pyQCat.qubit.qubit.BaseQubit`"
msgstr ""

#: of pyQCat.qubit.qubit.Coupler:1
msgid "Coupler bit entity class."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.Coupler.__init__:1
msgid "Create a new coupler."
msgstr ""

#: of pyQCat.qubit.qubit.Coupler.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.qubit.qubit.Coupler.__init__:4
msgid "Coupler number."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid ":py:obj:`__init__ <pyQCat.qubit.Coupler.__init__>`\\ \\(bit\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid ":py:obj:`ac_instead_dc <pyQCat.qubit.Coupler.ac_instead_dc>`\\ \\(value\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid "Use AWG instead DC module to provide voltage."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid ":py:obj:`anno <pyQCat.qubit.Coupler.anno>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.Coupler.anno:1
msgid "Normal used to describe."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid ":py:obj:`from_dict <pyQCat.qubit.Coupler.from_dict>`\\ \\(data\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid "Load qubit information from dict."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid ""
":py:obj:`from_file <pyQCat.qubit.Coupler.from_file>`\\ \\(file\\[\\, "
"fmt\\]\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid "Load qubit information from file."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid ":py:obj:`is_adjacent <pyQCat.qubit.Coupler.is_adjacent>`\\ \\(other\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid "Determines if two qubits are adjacent qubits."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid ":py:obj:`put_sweet_point <pyQCat.qubit.Coupler.put_sweet_point>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid "Put Qubit/Coupler on sweet point."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid ":py:obj:`reset <pyQCat.qubit.Coupler.reset>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid "Reset fields."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid ":py:obj:`save_database <pyQCat.qubit.Coupler.save_database>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid "Save database hook."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid ":py:obj:`set_coords <pyQCat.qubit.Coupler.set_coords>`\\ \\(row\\, col\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid "Set coords."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid ":py:obj:`to_dict <pyQCat.qubit.Coupler.to_dict>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid "Convert object to dict structure."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid ""
":py:obj:`to_file <pyQCat.qubit.Coupler.to_file>`\\ \\(\\[export\\_path\\,"
" fmt\\]\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:32:<autosummary>:1
msgid "Export the object to a `yaml` or `json` file."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.Coupler.rst:34
msgid "Attributes"
msgstr ""

#: of pyQCat.qubit.qubit.Coupler.anno:1:<autosummary>:1
msgid ":py:obj:`col <pyQCat.qubit.Coupler.col>`\\"
msgstr ""

#: of pyQCat.qubit.qubit.Coupler.anno:1:<autosummary>:1
msgid "coordinate col."
msgstr ""

#: of pyQCat.qubit.qubit.Coupler.anno:1:<autosummary>:1
msgid ":py:obj:`composite_attrs <pyQCat.qubit.Coupler.composite_attrs>`\\"
msgstr ""

#: of pyQCat.qubit.qubit.Coupler.anno:1:<autosummary>:1
msgid ":py:obj:`options_attrs <pyQCat.qubit.Coupler.options_attrs>`\\"
msgstr ""

#: of pyQCat.qubit.qubit.Coupler.anno:1:<autosummary>:1
msgid ":py:obj:`row <pyQCat.qubit.Coupler.row>`\\"
msgstr ""

#: of pyQCat.qubit.qubit.Coupler.anno:1:<autosummary>:1
msgid "coordinate row."
msgstr ""

#: of pyQCat.qubit.qubit.Coupler.anno:1:<autosummary>:1
msgid ":py:obj:`unit_map <pyQCat.qubit.Coupler.unit_map>`\\"
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.qubit.qubit.BaseQubit`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.qubit.Coupler.__init__>`\\ "
#~ "\\(bit\\[\\, username\\, label\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`anno <pyQCat.qubit.Coupler.anno>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`export <pyQCat.qubit.Coupler.export>`\\ "
#~ "\\(\\[export\\_path\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`load <pyQCat.qubit.Coupler.load>`\\ \\(\\[json\\_file\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`load_by_time <pyQCat.qubit.Coupler.load_by_time>`\\ "
#~ "\\(create\\_time\\[\\, bit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`log_info <pyQCat.qubit.Coupler.log_info>`\\ "
#~ "\\(msg\\[\\, log\\_path\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`reset <pyQCat.qubit.Coupler.reset>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`save_by_yaml <pyQCat.qubit.Coupler.save_by_yaml>`\\ "
#~ "\\(yaml\\_path\\)"
#~ msgstr ""

#~ msgid ":py:obj:`save_database <pyQCat.qubit.Coupler.save_database>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.qubit.Coupler.__init__>`\\ "
#~ "\\(bit\\[\\, username\\, label\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`export <pyQCat.qubit.Coupler.export>`\\ \\(\\[export\\_path\\]\\)"
#~ msgstr ""

#~ msgid "Export qubit info to a yaml file."
#~ msgstr ""

#~ msgid ":obj:`load <pyQCat.qubit.Coupler.load>`\\ \\(\\[json\\_file\\]\\)"
#~ msgstr ""

#~ msgid "Load qubit information from json file or database."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`load_by_time <pyQCat.qubit.Coupler.load_by_time>`\\ "
#~ "\\(create\\_time\\[\\, bit\\]\\)"
#~ msgstr ""

#~ msgid "According to time get Qubit attribute value from Store."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`log_info <pyQCat.qubit.Coupler.log_info>`\\ "
#~ "\\(msg\\[\\, log\\_path\\]\\)"
#~ msgstr ""

#~ msgid "Log message to file, write qubit running state and some info."
#~ msgstr ""

#~ msgid "Update fields."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`save_by_yaml <pyQCat.qubit.Coupler.save_by_yaml>`\\ "
#~ "\\(yaml\\_path\\)"
#~ msgstr ""

#~ msgid "Save {}.yaml to MongoDB Store."
#~ msgstr ""

#~ msgid ""
#~ "Compare the current attribute value with"
#~ " the attribute value in the database."
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.qubit.qubit.BaseQubit`"
#~ msgstr ""

#~ msgid "Mark which sample name, relate to the parameters."
#~ msgstr ""

#~ msgid "Belong to user's username."
#~ msgstr ""

#~ msgid "Point description."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.qubit.Coupler.__init__>`\\ "
#~ "\\(bit\\[\\, sample\\, username\\, "
#~ "point\\_label\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`anno <pyQCat.qubit.Coupler.anno>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_file <pyQCat.qubit.Coupler.from_file>`\\ "
#~ "\\(file\\[\\, fmt\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`load <pyQCat.qubit.Coupler.load>`\\ \\(data\\)"
#~ msgstr ""

#~ msgid ":obj:`reset <pyQCat.qubit.Coupler.reset>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`save_database <pyQCat.qubit.Coupler.save_database>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`to_dict <pyQCat.qubit.Coupler.to_dict>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`to_file <pyQCat.qubit.Coupler.to_file>`\\ "
#~ "\\(\\[export\\_path\\, fmt\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`composite_attrs <pyQCat.qubit.Coupler.composite_attrs>`\\"
#~ msgstr ""

