# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/11/10
# __author:       <PERSON><PERSON><PERSON>

import copy
from typing import List

import numpy as np

from ....analysis.base_analysis import BaseAnalysis, QualityDescribe
from ....analysis.library.crosstalk_fix_f_analysis import (
    CrosstalkCurveAnalysis,
    CrosstalkLinearAnalysis,
    QCShiftAnalysis,
)
from ....log import pyqlog
from ....structures import MetaD<PERSON>, Options, QDict
from ....tools import cz_flow_options_adapter, qarange, solve_equations
from ....tools.utilities import (
    copy_file,
    freq_to_amp,
    judge_exp_failed,
    recursion_dir_all_file,
    reshape_data,
)
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import (
    ACCrosstalkOnce,
    CouplerACCrosstalkOnce,
    QCZShiftOnce,
)
from ..calibration.voltage_drift_calibration import FixedPointCalibration


class ACCrosstalkFixF(CompositeExperiment):
    _sub_experiment_class = ACCrosstalkOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("drive_freq", float)
        options.set_validator("bq_ac_list", list, limit_null=True)

        # new requirement: https://document.qpanda.cn/presentation/913JVW9l2KH4Z3E6
        options.set_validator("init_tq_ac_center", list)
        options.set_validator("init_tq_scan_range", float)
        options.set_validator("init_tq_scan_gap", float)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.drive_freq = None
        options.bq_ac_list = None

        # new requirement: https://document.qpanda.cn/presentation/913JVW9l2KH4Z3E6
        options.init_tq_ac_center = [0.1, 0.2, 0.3]
        options.init_tq_scan_range = 0.05
        options.init_tq_scan_gap = 0.005

        # feature: 0.4.9 add qc shift qc
        options.set_validator("fix_tq_ac", bool)
        options.fix_tq_ac = True

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("fit_type", ["linear", "anal"])
        options.set_validator("popt", list)

        options.fit_type = "linear"
        options.x_label = None
        options.y_label = None
        options.sub_title = None

        # feature: 0.4.10 add qc shift exp
        options.popt = [-4, 0, 10, 4, 0, 7, 0.01, 0.001]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.analysis_params = None
        return options

    def _check_options(self):
        super()._check_options()
        self._auto_checkout_tq_bq()
        sub_title = ["P0", "P1"] if self.discriminator else ["Amp", "Phase"]
        self.set_analysis_options(sub_title=sub_title)

    def _auto_checkout_tq_bq(self):
        tq, bq = None, None
        if len(self.qubits) == 2:
            tq, bq = self.qubits
        else:
            tq = self.qubits[0]
            bq = self.couplers[0]
        self.child_experiment.experiment_options.tq_name = tq.name
        self.child_experiment.experiment_options.bq_name = bq.name
        self.run_options.custom_unit_describe = f"bq({bq.name})-tq({tq.name})"
        # self._label += f"-bq({bq.name})-tq({tq.name})"
        self.run_options.collect_qubits = False
        x_label = f"Bias {bq.name} AC (V)"
        y_label = f"Target {tq.name} AC (V)"
        self.set_analysis_options(x_label=x_label, y_label=y_label)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        analysis_params = self.run_options.analysis_params
        metadata.process_meta = {"analysis_params": analysis_params}
        metadata.draw_meta = {
            "tq": analysis_params.tq_name,
            "bq": analysis_params.bq_name,
        }
        return metadata

    def _pre_tc_list(self, center_ac: float = None, index: int = None):
        if center_ac is None:
            center_ac = self.experiment_options.init_tq_ac_center[index]

        ac_range = self.experiment_options.init_tq_scan_range
        ac_gap = self.experiment_options.init_tq_scan_gap
        return qarange(center_ac - ac_range, center_ac + ac_range, ac_gap)

    async def _sync_composite_run(self):
        bq_ac_list = self.experiment_options.bq_ac_list
        sub_title = self.analysis_options.sub_title

        x_data = []
        tq_ac_fit_list = []
        tq_ac_array = []
        z0_array = []
        z1_array = []
        exp = None
        for i, bq_ac in enumerate(bq_ac_list):
            if self.experiment_options.fix_tq_ac:
                tq_ac_list = self.child_experiment.experiment_options.tq_ac_list
            else:
                if i < 3:
                    tq_ac_list = self._pre_tc_list(index=i)
                else:
                    arr = bq_ac_list[i - 3 : i]
                    y = tq_ac_fit_list[i - 3 : i]
                    a, b, c = solve_equations(arr, y)
                    guess_tq_center = a * bq_ac**2 + b * bq_ac + c
                    if guess_tq_center > 1:
                        guess_tq_center = 1
                    elif guess_tq_center < -1:
                        guess_tq_center = -1
                    pyqlog.log(
                        "EXP", f"ac tq central guess from 2nd poly: {guess_tq_center}"
                    )
                    tq_ac_list = self._pre_tc_list(center_ac=guess_tq_center)

            tq_ac_array.append(tq_ac_list)
            exp = copy.deepcopy(self.child_experiment)
            exp.set_experiment_options(bq_ac=bq_ac, tq_ac_list=tq_ac_list)
            self._check_simulator_data(exp, i)
            exp.set_parent_file(self, f"bq_ac={bq_ac}", i, len(bq_ac_list))
            await exp.run_experiment()
            self._experiments.append(exp)

            quality_obj = exp.analysis.quality
            if judge_exp_failed(quality_obj) is False:
                if "t_offset" in exp.analysis.results:
                    best_ac = exp.analysis.results.t_offset.value
                else:
                    best_ac = exp.analysis.results.b.value
                x_data.append(bq_ac)
            else:
                pyqlog.warning(
                    f"quality: {quality_obj}, use max index select best_ac value."
                )
                x = list(exp.analysis.analysis_datas.values())[0].x
                y = list(exp.analysis.analysis_datas.values())[0].y
                y_max_index = np.argmax(y)
                max_x = x[y_max_index]
                max_y = y[y_max_index]

                if max_y - np.mean(np.array(y)) < 0.2:
                    pyqlog.warning("the gap of mean and max less 0.2, break flow!")
                    break
                else:
                    best_ac = max_x
                    x_data.append(bq_ac)

            exp.analysis.provide_for_parent["bias_v"] = best_ac
            tq_ac_fit_list.append(best_ac)

            z0_array.append(exp.experiment_data.y_data[sub_title[0]])
            z1_array.append(exp.experiment_data.y_data[sub_title[1]])
            new_tq = reshape_data(tq_ac_fit_list, i, len(bq_ac_list))
            self.file.save_data(bq_ac_list, new_tq, name="bqv-tqv")
        # self.file.save_data(bq_ac_list, tq_ac_fit_list, name='bqv-tqv')
        path = self.file.dirs
        file_path_list = recursion_dir_all_file(path)
        copy_file(file_path_list, path)
        analysis_params = QDict()
        analysis_params.tq_name = (
            self.experiment_options.tq_name or exp.run_options.tq.name
        )
        analysis_params.bq_name = (
            self.experiment_options.bq_name or exp.run_options.bq.name
        )
        analysis_params.bq_ac_list = x_data
        analysis_params.tq_ac_array = tq_ac_array
        analysis_params.tq_ac_fit_list = tq_ac_fit_list
        analysis_params.z0_array = z0_array
        analysis_params.z1_array = z1_array
        analysis_params.drive_freq = (
            self.experiment_options.drive_freq or exp.run_options.tq.drive_freq
        )

        self.set_run_options(analysis_params=analysis_params)
        
        if not x_data:
            self._analysis = BaseAnalysis.empty_analysis(QualityDescribe.normal)
            return

        ana_cls = (
            CrosstalkLinearAnalysis
            if self.analysis_options.fit_type == "linear"
            else CrosstalkCurveAnalysis
        )

        self._run_analysis(x_data=x_data, analysis_class=ana_cls)


class CouplerACCrosstalkFixF(ACCrosstalkFixF):
    _sub_experiment_class = CouplerACCrosstalkOnce

    def _auto_checkout_tq_bq(self):
        tq, bq = None, None
        if len(self.couplers) == 2:
            tq, bq = self.couplers
        elif len(self.qubits) == 3:
            tq = self.couplers[0]
            bq = self.qubits[-1]
        else:
            tq = self.couplers[0]
            bq = self.qubits[0]
        self.child_experiment.experiment_options.tq_name = tq.name
        self.child_experiment.experiment_options.bq_name = bq.name
        self.run_options.custom_unit_describe = f"bq({bq.name})-tq({tq.name})"
        # self._label += f"-bq({bq.name})-tq({tq.name})"
        self.run_options.collect_qubits = False
        x_label = f"Bias {bq.name} AC (V)"
        y_label = f"Target {tq.name} AC (V)"
        self.set_analysis_options(x_label=x_label, y_label=y_label)


class QCZShift(ACCrosstalkFixF):
    _sub_experiment_class = QCZShiftOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("bq_ac_point", int)
        options.bq_ac_point = 25
        return options

    def _check_options(self):
        super()._check_options()
        self.set_analysis_options(result_name=self.qubit_pair.name)

        if not self.experiment_options.bq_ac_list:
            self.experiment_options.bq_ac_list = auto_generate_qc_points(
                self.qubit_pair, self.couplers[0], self.experiment_options.bq_ac_point
            )

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "popt":
                tq = self.child_experiment.experiment_options.tq_name
                result.extra["path"] = f"QubitPair.metadata.std.process.qc_shift.{tq}"


class QCZShiftFixPointCalibration(CompositeExperiment):
    _sub_experiment_class = FixedPointCalibration

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("qc_ac_list", list, limit_null=True)
        options.set_validator("qc_ac_point", int)
        options.set_validator("qc_ac_bound", float)
        options.set_validator("points_density", float)
        options.set_validator("qt_freq", float)
        options.set_validator("tq_name", str)
        options.set_validator("bq_name", str)
        options.set_validator("bq_amp", float)

        options.qc_ac_list = None
        options.qc_ac_point = 25
        options.quality_filter = True
        options.qc_ac_bound = 0.05
        options.points_density = -0.8
        options.qt_freq = None
        options.tq_name = "ql"
        options.bq_name = "qh"
        options.bq_amp = 0

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.qt_amp = None
        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "qt": self.experiment_options.tq_name,
            "qs": self.experiment_options.bq_name,
            "qt_freq": self.experiment_options.qt_freq,
            "qb_amp": self.experiment_options.bq_amp,
        }
        return metadata

    def _check_options(self):
        super()._check_options()
        cz_flow_options_adapter(self)
        tq_name = self.experiment_options.tq_name

        if isinstance(self.discriminator, List):
            for dcm in self.discriminator:
                if dcm.name == tq_name:
                    self.discriminator = dcm
                    break

        if not self.experiment_options.qc_ac_list:
            self.experiment_options.qc_ac_list = auto_generate_qc_points(
                self.qubit_pair,
                self.couplers[0],
                self.experiment_options.qc_ac_point,
                self.experiment_options.qc_ac_bound,
                self.experiment_options.points_density,
            )
        self.set_run_options(
            x_data=self.experiment_options.qc_ac_list,
            analysis_class=QCShiftAnalysis,
        )
        self.set_analysis_options(result_name=self.qubit_pair.name)

        # calculate qt amp
        physical_units_map = self.physical_units_map()
        tq = physical_units_map.get(tq_name)
        z_amp = (
            freq_to_amp(
                physical_unit=tq,
                freq=self.experiment_options.qt_freq,
            )
            - tq.idle_point
        )
        self.set_run_options(qt_amp=z_amp)
        # self.set_child_exp_options(frequency=self.experiment_options.qt_freq)

    def _setup_child_experiment(
        self, exp: "FixedPointCalibration", index: int, value: float
    ):
        """Set child_experiment some options."""
        exp.run_options.index = index
        total = len(self.run_options.x_data)
        exp.experiment_options.frequency = self.experiment_options.qt_freq
        exp.set_child_exp_options(
            tq_name=self.experiment_options.tq_name,
            bq_z_amp_list=[value, self.experiment_options.bq_amp],
            bq_name_list=["qc", self.experiment_options.bq_name],
        )
        exp.set_parent_file(self, f"qc={value}v", index, total)
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp: "FixedPointCalibration"):
        # collect child experiment result and provide it for parent.
        origin_vol = self.run_options.qt_amp
        if exp.analysis:
            if exp.analysis.results:
                vol = exp.analysis.results.vol.value  # temp
            else:
                vol = origin_vol
            exp.analysis.provide_for_parent.update({"bias_v": vol})
            pyqlog.log(
                "RESULT",
                f"QC({self.run_options.x_data[exp.run_options.index]}) | " f"QT({vol})",
            )


def var_step_arr(start: float, end: float, point: int, coef: float = -0.8):
    # Initialize the sequence, with the first value being 0 and the last value being 1
    sequence = np.zeros(point)
    sequence[-1] = 1

    # Generate inverse square root weights (this is just one of many possible methods)
    weights = np.arange(1, point, dtype=np.float64) ** coef

    # Accumulate weights and normalize to ensure that the last item in the sequence is 1
    cumulative_weights = weights.cumsum() / weights.sum()

    # Map cumulative weights between 0 and 1
    sequence[1:-1] = cumulative_weights[:-1]

    gap = end - start

    sequence *= gap
    sequence += start

    return sequence


def auto_generate_qc_points(qubit_pair, coupler, point, bound, coef: float = -0.8):
    qc_ac = qubit_pair.cz_value(coupler.name, "amp")
    if qc_ac:
        qc_ac = qc_ac - abs(bound) if qc_ac < 0 else qc_ac + abs(bound)
        if qc_ac > 0.5:
            qc_ac = 0.45
        elif qc_ac < -0.5:
            qc_ac = -0.45
        qc_points = var_step_arr(0, qc_ac, point, coef)
    else:
        idle_point, dc_max, dc_min = coupler.idle_point, coupler.dc_max, coupler.dc_min
        mid_point = abs(dc_max - dc_min) / 3 * 2
        zamp = mid_point - abs(idle_point)
        if idle_point != 0:
            zamp = zamp if idle_point > 0 else -zamp
        else:
            zamp = zamp if dc_max < 0 else -zamp
        qc_points = list(np.linspace(0, zamp, point))

    pyqlog.info(
        f"auto generate qc points: {point}, density: {coef}, bound: {bound}\n{qc_points}"
    )
    return qc_points
