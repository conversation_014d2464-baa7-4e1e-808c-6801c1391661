# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/14
# __author:       <PERSON> <PERSON>
"""
Sample width optimize composite experiment.

"""

from ....analysis.library import SampleWidthOptimizeAnalysis
from ....structures import Options
from ....tools import qarange
from .single_shot_composite import SingleShotComposite


class SampleWidthOptimize(SingleShotComposite):
    """Optimize Sample Width."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            optimize_field (str): Give target optimize field.
            sweep_list (List, np.ndarray): Scan optimize field list.

        """
        options = super()._default_experiment_options()

        options.optimize_field = "sample_width"
        options.sweep_list = qarange(600, 2200, 100)

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()

        options.analysis_class = SampleWidthOptimizeAnalysis
        return options
