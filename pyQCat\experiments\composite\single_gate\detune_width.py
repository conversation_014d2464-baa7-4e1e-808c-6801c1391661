# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/05/09
# __author:       <PERSON><PERSON><PERSON>

from ....analysis import SweepDetuneAnalysis
from ....structures import MetaData, Options
from ....tools import qarange
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import CouplerRabiScanWidthDetune, RabiScanWidthDetune


class SweepDetuneRabiWidth(CompositeExperiment):
    _sub_experiment_class = RabiScanWidthDetune

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("detune_list", list)
        options.detune_list = qarange(-40, 40, 2)
        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()
        options.freq = None
        options.z_amp = None
        options.idle_point = None
        options.driver_power = None
        return options

    def _metadata(self) -> MetaData:
        """Set RabiScanWidth experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "freq": (self.run_options.freq, "MHz"),
            "z_amp": (self.run_options.z_amp, "V"),
            "idle_point": (self.run_options.idle_point, "V"),
            "driver_power": (self.run_options.driver_power, "db")
        }
        return metadata

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.detune_list,
            analysis_class=SweepDetuneAnalysis,
        )
        if not self.run_options.parent_label:
            self.run_options.z_amp = 0
            unit = self.couplers[0] if self.couplers else self.qubits[0]
            self.run_options.freq = unit.drive_freq
            self.run_options.idle_point = unit.idle_point
            self.run_options.driver_power = unit.drive_power

    def _setup_child_experiment(
        self, exp: RabiScanWidthDetune, index: int, detune: float
    ):
        exp.run_options.index = index
        total = len(self.run_options.x_data)
        describe = f"Detune={detune}"
        exp.set_parent_file(self, describe, index, total)
        exp.set_experiment_options(detune=detune)
        self._check_simulator_data(exp, index)


class CouplerSweepDetuneRabiWidth(SweepDetuneRabiWidth):
    _sub_experiment_class = CouplerRabiScanWidthDetune
