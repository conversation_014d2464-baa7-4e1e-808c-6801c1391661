# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/05/20
# __author:       <PERSON><PERSON><PERSON>

import json
import time
from collections import defaultdict

import numpy as np

from ....analysis import AnalysisResult, XEBAnalysis
from ....gate import GateBucket, GateCollection, Rphi_gate
from ....log import pyqlog
from ....pulse import Constant
from ....structures import MetaData, Options, QDict, Union
from ....tools import RandomType, qarange, seed_randomer
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment
from ..two_qubit_gate.swap_once import (
    validate_qubit_pair_cz_std,
    validate_two_qubit_exp_read_options,
)


class XEBBase(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        gate_list = GateCollection.gate_infos()
        gate_list.append(None)

        options.set_validator("depth1", list)
        options.set_validator("depth2", list)
        options.set_validator("depth3", list)
        options.set_validator("depth4", list)
        options.set_validator("times", int)
        options.set_validator("goal_gate", gate_list)
        options.set_validator("open_seed", bool)
        options.set_validator("seed", str)

        options.depth1 = qarange(2, 10, 2)
        options.depth2 = qarange(15, 50, 5)
        options.depth3 = qarange(60, 100, 10)
        options.depth4 = qarange(120, 200, 20)
        options.times = 30
        options.goal_gate = None
        options.open_seed = False
        options.goal_matrix = None
        options.seed = None
        options.readout_type = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("fidelity_threshold", float)

        options.matrix_list = []
        options.depths = None
        options.k = None
        options.num = 1
        options.quality_bounds = [0.9, 0.85, 0.77]
        options.fidelity_threshold = 0.95

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.depths = []
        options.gate_bucket = GateBucket()
        options.xy_pulse_map = {}
        options.z_pulse_map = {}
        options.temp_struct = []
        options.x_data = None
        options.seed = 0

        options.injection_func = [
            "_random_sample",
            "_random_su2",
            "_transform_pulse",
            "random_sample_struct",
            "pulse_from_struct",
            "_random_function",
            "one_random_su2",
        ]

        return options

    def _metadata(self) -> MetaData:
        """Set XEB experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"K": self.experiment_options.times}
        if self.experiment_options.goal_gate:
            metadata.draw_meta = {"Gate": self.experiment_options.goal_gate}
        if self.experiment_options.open_seed:
            metadata.draw_meta = {"Seed": self.experiment_options.seed}
        return metadata

    @staticmethod
    def set_xy_pulses(self):
        for bit, pulses in self.run_options.xy_pulse_map.items():
            self.play_pulse("XY", bit, pulses)

    @staticmethod
    def set_z_pulses(self):
        for bit, pulses in self.run_options.z_pulse_map.items():
            self.play_pulse("Z", bit, pulses)

    def _check_options(self):
        super()._check_options()

        depths = []
        depth1 = self.experiment_options.depth1
        depth2 = self.experiment_options.depth2
        depth3 = self.experiment_options.depth3
        depth4 = self.experiment_options.depth4
        for ds in [depth1, depth2, depth3, depth4]:
            if ds:
                depths.extend(ds)

        x_data = np.asarray(depths).repeat(self.experiment_options.times)
        seed = self.experiment_options.seed or int(time.time())
        self.set_experiment_options(data_type="I_Q", seed=str(seed))
        self.set_run_options(
            x_data=x_data, seed=int(seed), depths=depths, analysis_class=XEBAnalysis
        )

        if self.qubit_pair is None:
            self.set_analysis_options(num=1)
        else:
            self.set_analysis_options(num=2)

        self.run_options.temp_struct.clear()

        for depth in depths:
            for _ in range(self.experiment_options.times):
                self._random_sample(depth)

        self._random_su2()
        self._transform_pulse()

        matrix_list = [struct.matrix for struct in self.run_options.temp_struct]
        self.set_analysis_options(
            matrix_list=matrix_list,
            depths=self.run_options.depths,
            k=self.experiment_options.times,
        )

    def _random_sample(self, depth: int):
        pass

    def _random_su2(self):
        pass

    def _transform_pulse(self):
        pass

    def random_sample_struct(self, depth: int):
        gate_bucket = self.run_options.gate_bucket
        gate_names = gate_bucket.gate_collector.single_x2_gate_infos()
        final_gates = []
        final_matrix = np.eye(2)
        rgp = []

        for _ in range(depth):
            random_index = self._random_function(RandomType.randint)
            gate = gate_names[random_index]
            gate_matrix = gate_bucket.get_matrix(gate)
            final_gates.append(gate)
            rgp.append("-")
            final_matrix = np.dot(gate_matrix, final_matrix)

            if self.experiment_options.goal_gate:
                mg = self.experiment_options.goal_gate
                final_gates.append(mg)
                rgp.append("-")
                goal_matrix = gate_bucket.get_matrix(mg)
                if self.experiment_options.goal_matrix is not None:
                    goal_matrix = goal_matrix
                final_matrix = np.dot(goal_matrix, final_matrix)

        return QDict(gates=final_gates, rgp=rgp, matrix=final_matrix)

    def pulse_from_struct(self, struct, qubit, mode: str = "XY"):
        gate_bucket = self.run_options.gate_bucket
        pulse = Constant(0, 0, mode)()
        for i, gate in enumerate(struct.gates):
            if gate == "XR":
                if mode == "XY":
                    rgp = struct.rgp[i]
                    p = Rphi_gate(phase=rgp[0], theta=rgp[1])
                    pulse += p.to_pulse(qubit)()
                else:
                    pulse += gate_bucket.get_z_pulse(qubit, gate)
            else:
                if mode == "XY":
                    c_pulse = gate_bucket.get_xy_pulse(qubit, gate)
                else:
                    c_pulse = gate_bucket.get_z_pulse(qubit, gate)

                pulse += c_pulse

        return pulse

    def _set_result_path(self):
        """"""

    def _random_function(self, style: Union[int, RandomType]):
        seed = None

        if self.experiment_options.open_seed is True:
            seed = self.run_options.seed
            self.run_options.seed += 1

        if style == RandomType.randint:
            return seed_randomer(style=style, right=7, seed=seed)
        else:
            return seed_randomer(style=style, seed=seed)

    def one_random_su2(self, struct, phase: float = 0.0):
        """Build random su2 gate

        Args:
            struct: A special gate information dictionary, like:
                {
                    gates: [-, -, -];
                    rgp: [-, -, -];
                    matrix: np.Matrix
                }
            phase: CZ single qubit phase

        Returns:
            A new special gate information dictionary
        """

        def vz_transform(key_list, phase_list, theta_list):
            """Virtual Z-Gate Conversion

            Args:
                key_list: Gate name list
                phase_list: Gate phase list
                theta_list: Gate theta list

            Returns:
                new key, phase and theta only for Rphi_gate
            """
            length = len(key_list)
            del_index = []
            for index, key in enumerate(key_list):
                if key == "Z":
                    tt = theta_list[index]
                    for j in range(index + 1, length):
                        if key_list[j] == "XR":
                            phase_list[j] -= tt
                    del_index.append(index)

            key_list = [
                key_list[idx] for idx in range(len(key_list)) if (idx not in del_index)
            ]
            phase_list = [
                phase_list[idx]
                for idx in range(len(phase_list))
                if (idx not in del_index)
            ]
            theta_list = [
                theta_list[idx]
                for idx in range(len(theta_list))
                if (idx not in del_index)
            ]

            return key_list, phase_list, theta_list

        # generate random theta, phase and lamda
        theta = self._random_function(RandomType.random) * 2 * np.pi
        phi = self._random_function(RandomType.random) * 2 * np.pi
        lamda = self._random_function(RandomType.random) * 2 * np.pi

        # calculate the su2 matrix
        u00 = np.cos(theta / 2)
        u01 = -1j * np.sin(theta / 2) * np.exp(1j * lamda)
        u10 = -1j * np.sin(theta / 2) * np.exp(1j * phi)
        u11 = np.cos(theta / 2) * np.exp(1j * (lamda + phi))
        u_matrix = np.mat([[u00, u01], [u10, u11]])

        # standard Su2 waveform parameter generation method
        envelop_key = ["Z", "XR", "Z", "XR", "Z"]
        envelop_phase = [0, 0, 0, 0, 0]
        envelop_theta = [
            lamda + np.pi / 2,
            np.pi / 2,
            theta - np.pi,
            np.pi / 2,
            phi + np.pi / 2,
        ]
        keys, phases, thetas = vz_transform(envelop_key, envelop_phase, envelop_theta)
        phases = [p - phase for p in phases]

        # update structure
        e_rgp = []
        for i in range(len(keys)):
            e_rgp.append([phases[i], thetas[i]])
        new_matrix = np.dot(u_matrix, struct.matrix)

        struct.gates.extend(keys)
        struct.rgp.extend(e_rgp)
        struct.matrix = new_matrix
        struct.su2_matrix = u_matrix

        return u_matrix

    def _alone_save_result(self):
        x_data = self.analysis.analysis_datas.F_xeb.x
        f_xeb = self.analysis.analysis_datas.F_xeb.y
        f_purity = self.analysis.analysis_datas.F_purity.y

        try:
            self.file.save_data(x_data, np.array(f_xeb), np.array(f_purity), name=f"{self}(Depth-XEB-Purity)")
        except Exception as e:
            pyqlog.warning(e)


class XEBSingle(XEBBase):
    def _random_sample(self, depth: int):
        self.run_options.temp_struct.append(self.random_sample_struct(depth))

    def _random_su2(self):
        for struct in self.run_options.temp_struct:
            self.one_random_su2(struct)

    def _transform_pulse(self):
        temp_struct = self.run_options.temp_struct

        pulse_list = []
        for struct in temp_struct:
            pulse = self.pulse_from_struct(struct, self.qubit)
            pulse_list.append(pulse)

        self.run_options.xy_pulse_map[self.qubit] = pulse_list

    def _check_options(self):
        self.run_options.gate_bucket.bind_single_x2_gates(self.qubit)
        self.set_analysis_options(result_name=self.qubit.name)
        super()._check_options()

    @staticmethod
    def update_instrument(self):
        self.sweep_readout_trigger_delay(
            self.qubit.readout_channel, self._pulse_time_list
        )

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""

        for key, result in self.analysis.results.items():
            if key == "f_xeb":
                result.extra["path"] = "Qubit.xeb_fidelity"
            elif key == "depth":
                result.extra["path"] = "Qubit.xeb_depth"


class XEBMultiple(XEBBase):
    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []
        options.support_context = [StandardContext.CGC]

        return options

    def random_sample_struct(self, depth: int):
        goal_gate = self.experiment_options.goal_gate
        gate_bucket = self.run_options.gate_bucket
        gate_names = gate_bucket.gate_collector.single_x2_gate_infos()
        final_gates = []
        rgp = []

        for _ in range(depth):
            random_index = self._random_function(RandomType.randint)
            gate = gate_names[random_index]
            final_gates.append(gate)
            rgp.append("-")
            if goal_gate:
                final_gates.append(goal_gate)
                rgp.append("-")

        return QDict(gates=final_gates, rgp=rgp, matrix=np.eye(2))

    def _random_sample(self, depth: int):
        qh_gate_struct = self.random_sample_struct(depth)
        ql_gate_struct = self.random_sample_struct(depth)

        self.run_options.temp_struct.append(
            QDict(qh_struct=qh_gate_struct, ql_struct=ql_gate_struct, matrix=np.eye(4))
        )

    def _random_su2(self):
        goal_gate = self.experiment_options.goal_gate
        gate_bucket = self.run_options.gate_bucket
        ql = self.run_options.ql
        qh = self.run_options.qh
        qh_phase = gate_bucket.cz_gate.phase_map.get(qh.name)
        ql_phase = gate_bucket.cz_gate.phase_map.get(ql.name)
        depths = self.run_options.depths
        x_data = np.asarray(depths).repeat(self.experiment_options.times)

        def get_matrix(com_struct):
            cur_matrix = np.eye(4)
            qh_struct_gates = com_struct.qh_struct.gates
            ql_struct_gates = com_struct.ql_struct.gates

            for i in range(len(qh_struct_gates)):
                qh_gate = qh_struct_gates[i]
                ql_gate = ql_struct_gates[i]
                if qh_gate == "CZ":
                    goal_matrix = gate_bucket.get_matrix(qh_gate)
                    if self.experiment_options.goal_matrix is not None:
                        goal_matrix = self.experiment_options.goal_matrix
                    cur_matrix = np.dot(goal_matrix, cur_matrix)
                else:
                    cm = np.kron(
                        gate_bucket.get_matrix(qh_gate), gate_bucket.get_matrix(ql_gate)
                    )
                    cur_matrix = np.dot(cm, cur_matrix)

            return cur_matrix

        for idx, struct in enumerate(self.run_options.temp_struct):
            cz_num = 0
            if goal_gate == "CZ":
                cz_num = x_data[idx]
            pre_matrix = get_matrix(struct)
            matrix1 = self.one_random_su2(struct.qh_struct, qh_phase * cz_num)
            matrix2 = self.one_random_su2(struct.ql_struct, ql_phase * cz_num)
            aft_matrix = np.kron(matrix1, matrix2)
            final_matrix = np.dot(aft_matrix, pre_matrix)
            struct.matrix = final_matrix

    def _transform_pulse(self):
        temp_struct = self.run_options.temp_struct

        ql = self.run_options.ql
        qh = self.run_options.qh
        qc = self.couplers[0]

        parking_bits = []
        for bit in self.qubit_pair.metadata.std.parking_bits:
            if bit != self.qubit_pair.qc:
                if bit.startswith("q"):
                    for qubit in self.qubits:
                        if qubit.name == bit:
                            parking_bits.append(qubit)
                            break
                else:
                    for coupler in self.couplers:
                        if coupler.name == bit:
                            parking_bits.append(coupler)
                            break

        qh_xy_pulse_list = []
        ql_xy_pulse_list = []
        qh_z_pulse_list = []
        ql_z_pulse_list = []
        qc_z_pulse_list = []
        parking_z_pulse_dict = defaultdict(list)

        for struct in temp_struct:
            qh_xy_pulse_list.append(self.pulse_from_struct(struct.qh_struct, qh))
            ql_xy_pulse_list.append(self.pulse_from_struct(struct.ql_struct, ql))
            qh_z_pulse_list.append(
                self.pulse_from_struct(struct.qh_struct, qh, mode="Z")
            )
            ql_z_pulse_list.append(
                self.pulse_from_struct(struct.ql_struct, ql, mode="Z")
            )
            qc_z_pulse_list.append(
                self.pulse_from_struct(struct.ql_struct, qc, mode="Z")
            )
            for bit_obj in parking_bits:
                parking_z_pulse_dict[bit_obj].append(
                    self.pulse_from_struct(struct.ql_struct, bit_obj, mode="Z")
                )

        self.run_options.xy_pulse_map[qh] = qh_xy_pulse_list
        self.run_options.xy_pulse_map[ql] = ql_xy_pulse_list
        self.run_options.z_pulse_map[qh] = qh_z_pulse_list
        self.run_options.z_pulse_map[ql] = ql_z_pulse_list
        self.run_options.z_pulse_map[qc] = qc_z_pulse_list
        self.run_options.z_pulse_map.update(parking_z_pulse_dict)

    def pulse_from_struct(self, struct, qubit, mode: str = "XY"):
        gate_bucket = self.run_options.gate_bucket
        pre_gates, aft_gates = struct.gates[:-2], struct.gates[-2:]
        if mode == "XY":
            pulse = gate_bucket.get_xy_pulse(qubit, pre_gates)
        else:
            pulse = gate_bucket.get_z_pulse(qubit, pre_gates)
        for i, gate in enumerate(aft_gates):
            if mode == "XY":
                rgp = struct.rgp[len(pre_gates) + i]
                p = Rphi_gate(phase=rgp[0], theta=rgp[1])
                pulse += p.to_pulse(qubit)()
                if pulse.virtual_z_phase:
                    pulse.virtual_z_phase.append(rgp[0])
            else:
                pulse += gate_bucket.get_z_pulse(qubit, gate)

        return pulse

    def _check_options(self):
        # bind high frequency qubit and low frequency qubit
        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)

        # bind base cz gate
        self._bind_cz_gate()
        self.set_experiment_options(is_dynamic=0)

        super()._check_options()
        self.set_analysis_options(result_name=self.qubit_pair.name)

    def _bind_cz_gate(self):
        gate_bucket = self.run_options.gate_bucket
        ql = self.run_options.ql
        qh = self.run_options.qh

        gate_bucket.bind_single_x2_gates(ql)
        gate_bucket.bind_single_x2_gates(qh)
        units = []
        units.extend(self.qubits)
        units.extend(self.couplers)
        gate_bucket.bind_cz_gates(self.qubit_pair, units)

    @staticmethod
    def update_instrument(self):
        ql = self.run_options.ql
        qh = self.run_options.qh

        for channel in list({ql.readout_channel, qh.readout_channel}):
            sweep_delay = self._pulse_time_list[
                : len(self.run_options.xy_pulse_map[ql])
            ]
            self.sweep_readout_trigger_delay(channel, sweep_delay)

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""

        try:
            gate_params = self.qubit_pair.metadata.std.cz["params"]
            json_str = json.dumps(gate_params, ensure_ascii=False, indent=4)
            self.file.save_text(json_str, name="gate_params", prefix=".json")
        except Exception as e:
            pyqlog.warning(f"{e} error occur when saving gate_params")

        for key, result in self.analysis.results.items():
            if self.experiment_options.goal_gate == "CZ":
                if key == "f_xeb":
                    result.extra["path"] = "QubitPair.metadata.std.fidelity.xeb_fidelity"
                if key == "f_spb_purity":
                    result.extra["path"] = "QubitPair.metadata.std.fidelity.xeb_spb_purity_fidelity"
                elif key == "depth":
                    result.extra["path"] = "QubitPair.metadata.std.fidelity.xeb_depth"

        if self.analysis.results.f_xeb.value:
            qh_xeb = self.qh.xeb_fidelity
            ql_xeb = self.ql.xeb_fidelity
            if qh_xeb and ql_xeb:
                v = round(self.analysis.results.f_xeb.value + 2 - (ql_xeb + qh_xeb), 4)
                self.analysis.results["xeb_relative_fidelity"] = AnalysisResult(
                    name="xeb_relative_fidelity",
                    value=v,
                    extra={
                        "path": "QubitPair.metadata.std.fidelity.xeb_relative_fidelity",
                        "name": self.qubit_pair.name,
                    },
                )
