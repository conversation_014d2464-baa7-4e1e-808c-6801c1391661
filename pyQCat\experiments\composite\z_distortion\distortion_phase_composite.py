# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/12/31
# __author:       xw

"""
APE Composite.
"""

import json

from ....analysis.library import DPhaseCompositeAnalysis
from ....log import logger
from ....structures import MetaData, Options
from ....tools import qarange
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import DistortionAssistPhase


class DistortionPhaseComposite(CompositeExperiment):
    _sub_experiment_class = DistortionAssistPhase

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("delay_list", list)
        options.set_validator("point_config", str)

        options.delay_list = qarange(0, 100, 2)
        options.point_config = ""

        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("diff_threshold", (0, 1, 2))

        options.diff_threshold = 0.2
        options.data_key = ["phase"]
        options.fine = None
        options.step = None
        options.plot_key = None
        options.y_label = None
        options.plot_2d = True

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.point_data = None
        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        cc_exp = self.child_experiment
        result_name, plot_key, y_label = self.check_key(cc_exp)

        self.set_analysis_options(
            result_name=result_name,
            plot_key=plot_key,
        )

        # config point
        point_config = self.experiment_options.point_config
        point_data = {}
        try:
            with open(point_config, "r", encoding="utf-8") as file:
                point_data = json.load(file)
        except FileNotFoundError:
            logger.error(f"file {point_config} no find!")
        except json.JSONDecodeError:
            logger.error(f"file {point_config} is not a valid JSON format!")
        self.run_options.point_data = point_data

        # Shq 2024/04/29
        # for async mode.
        self.run_options.x_data = self.experiment_options.delay_list
        self.run_options.analysis_class = DPhaseCompositeAnalysis

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        # metadata.draw_meta = {
        #     "theta_type": self.experiment_options.theta_type,
        #     "scan_type": self.experiment_options.scan_type,
        # }
        return metadata

    @staticmethod
    def check_key(cc_exp):
        if cc_exp.is_coupler_exp:
            result_name = cc_exp.coupler.name
            plot_key = "P1"
            y_label = "P1"
        elif cc_exp.coupler:
            result_name = cc_exp.coupler.name
            plot_key = "P0"
            y_label = "P0"
        else:
            result_name = cc_exp.qubit.name
            plot_key = "P0"
            y_label = "P0"

        return result_name, plot_key, y_label

    def _setup_child_experiment(self, child_exp: DistortionAssistPhase, index: int, delay: float):
        delay_list = self.experiment_options.delay_list

        point = self.run_options.point_data.get(self.qubit.name, {})
        z_amp = point.get("amp")
        if z_amp is None:
            z_amp = (self.qubit.dc_min - self.qubit.dc_max) / 2
        z_amp = round(z_amp, 4)

        child_exp.set_parent_file(self, f"delay={delay} amp={z_amp}", index, len(delay_list))
        child_exp.set_experiment_options(
            delay=round(delay, 4),
            z_amp=z_amp
        )
        self._check_simulator_data(child_exp, index)

    def _handle_child_result(self, child_exp: DistortionAssistPhase):
        provide_field = self.analysis_options.data_key[0]
        points = child_exp.analysis.results.phase.value

        child_exp.analysis.provide_for_parent.update({provide_field: points})
