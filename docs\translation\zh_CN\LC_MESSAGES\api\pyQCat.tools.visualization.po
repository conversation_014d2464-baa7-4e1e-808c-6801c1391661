# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/api/pyQCat.tools.visualization.rst:2
msgid "pyQCat.tools.visualization package"
msgstr ""

#: ../../source/api/pyQCat.tools.visualization.rst:5
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.tools.visualization.rst:8
msgid "pyQCat.tools.visualization.base\\_plot module"
msgstr ""

#: ../../source/api/pyQCat.tools.visualization.rst:16
msgid "pyQCat.tools.visualization.plot\\_styles module"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:1
msgid "Multiple figure painting and pcolor depth photos."
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic
#: pyQCat.tools.visualization.plot_styles.plot_multiple
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:4
msgid "x axis data"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:5
msgid "other data"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:7
msgid "x axis label"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:9
msgid "y axis label"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:11
msgid "line label, List object"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:13
msgid "figure title"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:15
msgid "mark points-x axis"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:17
msgid "save path"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:19
msgid "pcolor flag"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:21
msgid "show flag"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:23
msgid "mark the serial number"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:25
msgid "out text string"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:27
msgid "out text position"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_multiple:29
msgid "Returns:"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:1
msgid "plot dynamic results"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:3
msgid "Experiment sweep parameter, length is 2."
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:4
msgid "Amp or P0, length is 2."
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:5
msgid "Usually the same as x1 list, length is 2."
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:6
msgid "Phase or P1, length is 2."
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:8
msgid "Plot tool."
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:9
msgid "Ax1 x label."
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:10
msgid "Ax1 y label."
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:11
msgid "Ax2 x label."
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:12
msgid "Ax2 y label."
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:13
msgid "Ax1 title."
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:14
msgid "Ax2 title."
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic
msgid "Raises"
msgstr ""

#: of pyQCat.tools.visualization.plot_styles.plot_double_dynamic:16
msgid "figures is error, must be form `plt.pyplot()`"
msgstr ""

#: ../../source/api/pyQCat.tools.visualization.rst:24
msgid "Module contents"
msgstr ""

#: of pyQCat.tools.visualization:1
msgid "pyQCat visualization related functions."
msgstr ""

