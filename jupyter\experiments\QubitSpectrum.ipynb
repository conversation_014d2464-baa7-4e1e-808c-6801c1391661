{"cells": [{"cell_type": "markdown", "id": "a49074d5", "metadata": {}, "source": ["# QubitSpecturm\n", "\n", "用于测量比特频率\n", "\n", "### 初始化实验环境"]}, {"cell_type": "code", "execution_count": 1, "id": "77a0fc9c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\n"]}], "source": ["# 若用户未安装 pyqcat 包（monster），通过本地源码测试，需要执行下面操作\n", "# 若安装了 pyqcat 包（monster），跳过此步骤\n", "\n", "import os\n", "import sys\n", "\n", "\n", "package_root = \"../../../pyqcat-monster\"\n", "\n", "print(os.path.abspath(package_root))\n", "\n", "sys.path.insert(0, package_root)"]}, {"cell_type": "code", "execution_count": 2, "id": "51cb5d56", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:18:33\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1m\n", "|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |        tcp://************:8088         |\n", "|    point_label     |              person_point              |\n", "|       sample       |               test_chip                |\n", "|      env_name      |                D00_1011                |\n", "|      version       |                 0.1.6                  |\n", "|=============================================================|\n", "\u001b[0m\n"]}, {"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': 'f43db7cc4c5e5a85e2f100ac14f2d613'},\n", " 'msg': 'success'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "\n", "from pyQCat.tools import qarange\n", "from pyQCat.context import ExperimentContext\n", "from pyQCat.invoker import Invoker\n", "from pyQCat.parameters import get_parameters\n", "\n", "\n", "%matplotlib inline\n", "pd.set_option('max_colwidth', None)\n", "\n", "conf_file = r'E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf'\n", "\n", "username = \"test_04\"\n", "password = \"123456\"\n", "\n", "context = ExperimentContext(conf_file)\n", "Invoker.verify_account(username=username, password=password)"]}, {"cell_type": "markdown", "id": "9d95d25f", "metadata": {}, "source": ["### 配置实验参数"]}, {"cell_type": "code", "execution_count": 3, "id": "cb44f405", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:18:34\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1mQubit_(bit=0) is already exist in current environment.\u001b[0m\n"]}], "source": ["# context 加载实验参数\n", "\n", "q_name_list = [\"q0\"]\n", "environment_elements = [\"q0\", \"q1\", \"q2\", \"q3\", \"c0\", \"c1\", \"c2\"]\n", "\n", "context.configure_inst()\n", "context.configure_qubits(q_name_list)\n", "context.minimize_compensate()\n", "# context.maximize_compensate()\n", "context.configure_environment(environment_elements)\n", "context.configure_crosstalk_dict()\n", "\n", "qubit = context.qubit"]}, {"cell_type": "code", "execution_count": 4, "id": "44792e49", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'1': 0.744732,\n", " '2': 0.236088,\n", " '3': 0.203233,\n", " '4': 0.525287,\n", " '5': 0.0,\n", " '6': 0.0,\n", " '7': 0.0}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["context.working_dc"]}, {"cell_type": "code", "execution_count": 5, "id": "3da1fd56", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>parameter</th>\n", "      <th>object</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>config</td>\n", "      <td>E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>qubits</td>\n", "      <td>[Qubit_(bit=0)]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>couplers</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>discriminators</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>compensates</td>\n", "      <td>{Qubit_(bit=0): PulseCorrectionQ0}</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>working_dc</td>\n", "      <td>True</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>crosstalk_dict</td>\n", "      <td>True</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        parameter  \\\n", "0          config   \n", "1          qubits   \n", "2        couplers   \n", "3  discriminators   \n", "4     compensates   \n", "5      working_dc   \n", "6  crosstalk_dict   \n", "\n", "                                                                  object count  \n", "0  E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf        \n", "1                                                        [Qubit_(bit=0)]     1  \n", "2                                                                     []     0  \n", "3                                                                   None     0  \n", "4                                     {Qubit_(bit=0): PulseCorrectionQ0}     1  \n", "5                                                                   True        \n", "6                                                                   True        "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(context.context_table())"]}, {"cell_type": "markdown", "id": "879816fa", "metadata": {}, "source": ["## 能谱粗扫\n", "\n", "### 创建实验"]}, {"cell_type": "code", "execution_count": 6, "id": "38b05200", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>experiment option</td>\n", "      <td>show_result</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_shape</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_data_path</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>experiment option</td>\n", "      <td>repeat</td>\n", "      <td>1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>experiment option</td>\n", "      <td>register_pulse_save</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_flag</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_save</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_measure</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_type</td>\n", "      <td>envelop</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_measure</td>\n", "      <td>150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_real</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_index</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>experiment option</td>\n", "      <td>save_label</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>experiment option</td>\n", "      <td>is_dynamic</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>experiment option</td>\n", "      <td>fidelity_matrix</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>experiment option</td>\n", "      <td>ac_prepare_time</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>experiment option</td>\n", "      <td>readout_point_model</td>\n", "      <td>&lt;class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>experiment option</td>\n", "      <td>idle_qubits</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>experiment option</td>\n", "      <td>freq_list</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>experiment option</td>\n", "      <td>drive_power</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>experiment option</td>\n", "      <td>z_amp</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>experiment option</td>\n", "      <td>use_square</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>experiment option</td>\n", "      <td>band_width</td>\n", "      <td>50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>experiment option</td>\n", "      <td>fine_flag</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>experiment option</td>\n", "      <td>rough_window_length</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>experiment option</td>\n", "      <td>rough_freq_distance</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>experiment option</td>\n", "      <td>fine_window_length</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>experiment option</td>\n", "      <td>fine_freq_distance</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>experiment option</td>\n", "      <td>xpulse_params</td>\n", "      <td>{'time': 5000, 'offset': 15, 'amp': 1.0, 'detune': 0, 'freq': 466.667}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>experiment option</td>\n", "      <td>zpulse_params</td>\n", "      <td>{'time': 5100, 'amp': 0.0, 'sigma': 3, 'fast_m': True}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>analysis option</td>\n", "      <td>is_plot</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>analysis option</td>\n", "      <td>figsize</td>\n", "      <td>(12, 8)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>analysis option</td>\n", "      <td>window_length</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>analysis option</td>\n", "      <td>freq_distance</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>analysis option</td>\n", "      <td>snr_bounds</td>\n", "      <td>1.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>analysis option</td>\n", "      <td>data_key</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>analysis option</td>\n", "      <td>quality_bounds</td>\n", "      <td>[0.8, 0.6, 0.5]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 type                   name  \\\n", "0   experiment option            show_result   \n", "1   experiment option        simulator_shape   \n", "2   experiment option    simulator_data_path   \n", "3   experiment option                 repeat   \n", "4   experiment option    register_pulse_save   \n", "5   experiment option          schedule_flag   \n", "6   experiment option          schedule_save   \n", "7   experiment option       schedule_measure   \n", "8   experiment option          schedule_type   \n", "9   experiment option  schedule_show_measure   \n", "10  experiment option     schedule_show_real   \n", "11  experiment option         schedule_index   \n", "12  experiment option             save_label   \n", "13  experiment option             is_dynamic   \n", "14  experiment option        fidelity_matrix   \n", "15  experiment option        ac_prepare_time   \n", "16  experiment option    readout_point_model   \n", "17  experiment option            idle_qubits   \n", "18  experiment option              freq_list   \n", "19  experiment option            drive_power   \n", "20  experiment option                  z_amp   \n", "21  experiment option             use_square   \n", "22  experiment option             band_width   \n", "23  experiment option              fine_flag   \n", "24  experiment option    rough_window_length   \n", "25  experiment option    rough_freq_distance   \n", "26  experiment option     fine_window_length   \n", "27  experiment option     fine_freq_distance   \n", "28  experiment option          xpulse_params   \n", "29  experiment option          zpulse_params   \n", "30    analysis option                is_plot   \n", "31    analysis option                figsize   \n", "32    analysis option          window_length   \n", "33    analysis option          freq_distance   \n", "34    analysis option             snr_bounds   \n", "35    analysis option               data_key   \n", "36    analysis option         quality_bounds   \n", "\n", "                                                                     value  \n", "0                                                                     True  \n", "1                                                                     None  \n", "2                                                                     None  \n", "3                                                                     1000  \n", "4                                                                    False  \n", "5                                                                     True  \n", "6                                                                     True  \n", "7                                                                     True  \n", "8                                                                  envelop  \n", "9                                                                      150  \n", "10                                                                    True  \n", "11                                                                      -1  \n", "12                                                                    None  \n", "13                                                                       1  \n", "14                                                                    None  \n", "15                                                                       0  \n", "16                        <class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'>  \n", "17                                                                      []  \n", "18                                                                    None  \n", "19                                                                    None  \n", "20                                                                    None  \n", "21                                                                    True  \n", "22                                                                      50  \n", "23                                                                   False  \n", "24                                                                       7  \n", "25                                                                      70  \n", "26                                                                      11  \n", "27                                                                      80  \n", "28  {'time': 5000, 'offset': 15, 'amp': 1.0, 'detune': 0, 'freq': 466.667}  \n", "29                  {'time': 5100, 'amp': 0.0, 'sigma': 3, 'fast_m': True}  \n", "30                                                                    True  \n", "31                                                                 (12, 8)  \n", "32                                                                      11  \n", "33                                                                      80  \n", "34                                                                     1.5  \n", "35                                                                    None  \n", "36                                                         [0.8, 0.6, 0.5]  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from pyQCat.experiments import QubitSpectrum\n", "\n", "qs_exp = QubitSpectrum.from_experiment_context(context)\n", "\n", "pd.DataFrame(qs_exp.options_table())"]}, {"cell_type": "markdown", "id": "33b90b03", "metadata": {}, "source": ["**experiment options**\n", "\n", "*辅助功能选型*\n", "- simulator_data_path (str): 模拟数据地址\n", "- simulator (bool): 是否开启模拟器\n", "- repeat (int): 小循环次数\n", "- fidelity_matrix (np.ndarray): 保真度矩阵，支持外部传入\n", "- show_result (bool): 是否在日志中展示分析结果\n", "- save_lable (str): 采集数据存储标签，用于生成文件名，默认为空\n", "- is_dynamic (int): 是否进行动态绘图，默认为1，单比特读取动态绘图，0关闭动态绘图\n", "\n", "*脉冲时序选项*\n", "- schedule_flag (bool): 是否绘制脉冲时序图\n", "- schedule_save (bool): 是否存储脉冲时序图\n", "- schedule_measure (bool): 是否绘制测量波形\n", "- schedule_type (str): 脉冲时序图的类型，支持 squence 和 envelop 两种\n", "- schedule_index (int or list(int)): 绘制脉冲时序图的索引\n", "- register_pulse_save (bool): 波形存储数据\n", "\n", "*实验参数选项*\n", "\n", "- freq_list (list): 扫描驱动频率列表，支持为空\n", "- use_square (bool): 是否使用方波\n", "- band_width (float): 使用 chirp 方案时的中频带宽\n", "- fine_flag (bool): 粗扫和细扫的区别标签\n", "- drive_power (float): 驱动功率\n", "- z_amp (float): Z线施加幅值\n", "- is_opt (bool): XY线是否添加X门\n", "- scope (float): 当前腔频位置上下扫描的范围，单位 MHz\n", "\n", "**analysis options**\n", "- window_length (int): 寻峰窗口长度，默认为11\n", "- freq_distance (float): 峰值间距限制，默认为80\n", "- snr_bounds (float): 信噪比限制\n", "- data_key (list): 分析信号\n", "- quality_bounds (list)：拟合优度评估指标"]}, {"cell_type": "code", "execution_count": 7, "id": "e069dfa2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:18:34\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mQubitSpectrum register success, id 6349296a405c47fe304863f3\u001b[0m\n", "\u001b[33m2022-10-14 17:18:34\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\test_chip\\QubitSpectrum\\q0\\2022-10-14\\17.18.34\\\u001b[0m\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "647e975f726a4966a58eb0a13a0be7b0", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/501 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:18:42\u001b[0m | \u001b[32m RESULT \u001b[0m | \u001b[32mExperiment results as follow:\n", "===========================================================\n", "| name  | describe |  value   | unit |      quality       | \n", "-----------------------------------------------------------\n", "| peaks |  peaks   | [5000.0] | MHz  | SNR=2.132(perfect) | \n", "===========================================================\u001b[0m\n"]}], "source": ["\n", "qs_exp.set_experiment_options(\n", "    freq_list=qarange(4500, 5000, 2),\n", "    drive_power=-20,\n", "    z_amp=None,\n", "    use_square=False,\n", "    band_width=10,\n", "    fine_flag=False,\n", "    simulator_data_path='../../scripts/simulator/data/QubitSpectrum/5000Mhz/rough/',\n", ")\n", "\n", "qs_exp.set_analysis_options(\n", "    snr_bounds=1.5,\n", "    quality_bounds=[0.8, 0.6, 0.5],\n", "    is_plot=True,\n", ")\n", "\n", "qs_exp.run()"]}, {"cell_type": "code", "execution_count": 8, "id": "da37e41e", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["qs_exp.jupyter_schedule()"]}, {"cell_type": "code", "execution_count": 9, "id": "b07dc956", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "      <th>unit</th>\n", "      <th>extra</th>\n", "      <th>quality</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>peaks</td>\n", "      <td>[5000.0]</td>\n", "      <td>MHz</td>\n", "      <td>{}</td>\n", "      <td>SNR=2.132(perfect)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    name     value unit extra             quality\n", "0  peaks  [5000.0]  MHz    {}  SNR=2.132(perfect)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(qs_exp.analysis.show_results())"]}, {"cell_type": "code", "execution_count": 10, "id": "f294d329", "metadata": {}, "outputs": [{"data": {"text/plain": ["SNR=2.132(perfect)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["qs_exp.analysis.quality"]}, {"cell_type": "code", "execution_count": 11, "id": "3c9d1cef", "metadata": {}, "outputs": [{"data": {"image/png": "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***********************************************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\n", "text/plain": ["<Figure size 864x576 with 2 Axes>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["qs_exp.analysis.drawer.figure"]}, {"cell_type": "markdown", "id": "feddeb7d", "metadata": {}, "source": ["## 能谱细扫"]}, {"cell_type": "code", "execution_count": 12, "id": "a8771230", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:18:43\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mQubitSpectrum register success, id 63492973405c47fe304863f5\u001b[0m\n", "\u001b[33m2022-10-14 17:18:43\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\test_chip\\QubitSpectrum\\q0\\2022-10-14\\17.18.43\\\u001b[0m\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f3efcd8b4cc74e8d8cf152b0bc9d40a9", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/101 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:18:45\u001b[0m | \u001b[32m RESULT \u001b[0m | \u001b[32mExperiment results as follow:\n", "==========================================================\n", "| name | describe |  value   | unit |      quality       | \n", "----------------------------------------------------------\n", "| freq |   f01    | 5000.324 | MHz  | R²=0.8252(perfect) | \n", "==========================================================\u001b[0m\n"]}], "source": ["qs_exp2 = QubitSpectrum.from_experiment_context(context)\n", "\n", "qs_exp2.set_experiment_options(\n", "    freq_list=qarange(4900, 5100, 2),\n", "    drive_power=-20,\n", "    z_amp=None,\n", "    use_square=True,\n", "    band_width=10,\n", "    fine_flag=True,\n", "    simulator_data_path='../../scripts/simulator/data/QubitSpectrum/5000Mhz/fine/'\n", ")\n", "\n", "qs_exp2.set_analysis_options(\n", "    snr_bounds=1.5,\n", "    quality_bounds=[0.8, 0.6, 0.5],\n", "    is_plot=True,\n", ")\n", "\n", "qs_exp2.run()"]}, {"cell_type": "code", "execution_count": 13, "id": "e188985e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "      <th>unit</th>\n", "      <th>extra</th>\n", "      <th>quality</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>f01</td>\n", "      <td>5000.324</td>\n", "      <td>MHz</td>\n", "      <td>{}</td>\n", "      <td>R²=0.8252(perfect)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  name     value unit extra             quality\n", "0  f01  5000.324  MHz    {}  R²=0.8252(perfect)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(qs_exp2.analysis.show_results())"]}, {"cell_type": "code", "execution_count": 14, "id": "d288d8d1", "metadata": {}, "outputs": [{"data": {"text/plain": ["R²=0.8252(perfect)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["qs_exp2.analysis.quality"]}, {"cell_type": "code", "execution_count": 15, "id": "6f3e291a", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 2 Axes>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["qs_exp2.analysis.drawer.figure"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}