# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/19
# __author:       <PERSON><PERSON><PERSON>

"""
Core data transfer service.
"""

import asyncio
from datetime import datetime, timedelta

import zmq
from zmq.asyncio import Poller

from .log import LogLevel, logger
from .message import DispatchSocket
from .share import ShareArea
from .util import heart_msg
from .web_client import WebClient


class DispatcherManager:
    """
    DataService contains two main asynchronous sockets, namely DispatcherSocket and
    TransferSocket. The DispatcherSocket composite communicates with the Naga Kernel
    Dispatcher module to send Monster tasks, receive task collection information,
    task status, and other messages. The TransferSocket service accepts task requests
    from <PERSON>'s internal threads and processes, such as querying task status and
    requesting message collection. The Transfer service was built into a completely
    asynchronous task transfer service using DispatcherSocket and TransferSocket,
    enabling the Monster asynchronous parallel framework to operate efficiently and
    smoothly.
    """

    def __init__(
        self,
        dispatcher_url: str,
        dispatcher_id: str,
        token: str,
        web_url: str,
        share: ShareArea,
    ):
        self.share = share
        self._dispatcher_sock = DispatchSocket(dispatcher_url, dispatcher_id, self.share)
        self.poller = Poller()
        self.poller.register(self._dispatcher_sock.socket, zmq.POLLIN)
        self._heartbeat_interval = 120
        self._heartbeat_time = None
        self._running = True
        self._dispatcher_url = dispatcher_url

        self.web_client = WebClient(token=token, web_url=web_url)

    def __repr__(self) -> str:
        return f"{self.__class__.__name__} | {self._dispatcher_url}"

    def close(self):
        self._running = False

    def close_sock(self):
        """
        Disconnect all socket connections
        """
        self._dispatcher_sock.socket.close()

    def _heartbeat_monitor(self) -> bool:
        """Heartbeat monitoring between Monster and Naga. Monster sends a heartbeat
        to naga every 60 seconds. If there is a heartbeat reply, it indicates a normal
        connection. Otherwise, it prompts a disconnection; Every time the identity
        information is received, if the identity information changes, it means that
        naga has restarted and Monster needs to resume the task

        Returns:
            bool: Has it reached the time to send heartbeat
        """
        current_time = datetime.now()
        if self._heartbeat_time is None or (
            current_time >= (self._heartbeat_time + timedelta(seconds=self._heartbeat_interval))
        ):
            logger.log(LogLevel.SYSTEM, self.share.information)
            if self._heartbeat_time:
                self.share.check_dispatcher_heart_state()
            self._heartbeat_time = current_time
            return True
        return False

    async def listening_program(self):
        """
        Asynchronous monster program listener for the transfer.

        This coroutine will poll whether the listening shared area is a compiled
        Monster task. If there is, pop it up from the shared area and send it to
        Naga Kernel; If not, switch to other coroutines
        """
        logger.log(LogLevel.SYSTEM, "Transfer-listening_program start...")
        while self.share.close is False and self._running:
            # Check if heartbeat needs to be sent
            if self._heartbeat_monitor():
                await self._send_heart()

            compile_result = self.share.pop_compile_result()
            if compile_result:
                # Check for new tasks
                self.share.build_task_from_compile_result(compile_result)
                if compile_result.simulator is False:
                    await self._send_program(compile_result)
            elif self.share.is_dispatcher_restart():
                # Dispatcher restart, check and resend
                # stop history task and resend
                self._stop_task()
                await asyncio.sleep(10)
                for task in self.share.transfer_task_collection.values():
                    logger.warning(f"{task} resend ...")
                    await self._send_program(task.to_program(), task.task_id)
                self.share.dispatcher_restart_recover()
            else:
                await asyncio.sleep(0.01)
                for task in self.share.transfer_task_collection.values():
                    if task.is_resend():
                        logger.warning(f"{task} resend ...")
                        await self._send_program(task.to_program(), task.task_id)

        logger.log(LogLevel.SYSTEM, "Transfer-listening_program end...")

    async def listening_result(self):
        """
        Asynchronous naga response listener for the transfer.

        This coroutine will poll and listen to DispatcherSocket and TransferSocket
        to see if there are any feedback messages. Accept different socket messages
        and distribute them into different message processing flows
        """
        logger.log(LogLevel.SYSTEM, "Transfer-listening_result start...")
        while self.share.close is False and self._running:
            res_ = dict(await self.poller.poll(100))
            if self._dispatcher_sock.socket in res_:
                await self._dispatcher_sock.recv()
        logger.log(LogLevel.SYSTEM, "Transfer-listening_result end...")

    async def listening_resource(self):
        """
        Asynchronous system resource listener for the transfer.

        This coroutine will poll for tasks in the listening area. If the task status
        is FINISH, the task resources will be cleared and memory will be released.
        """
        logger.log(LogLevel.SYSTEM, "Transfer-listening_resource start...")
        while self.share.close is False and self._running:
            self.share.clear_finish_task()
            await asyncio.sleep(2)
        logger.log(LogLevel.SYSTEM, "Transfer-listening_resource end...")

    async def _send_program(self, compile_result, task_id: str = ""):
        """Send monster program to naga kernel

        Args:
            compile_result (CompileResult): Monster experiment compiler result.
            task_id (str): Task note task_id.

        """
        task_id_bytes = task_id.encode(encoding="utf-8") if task_id else b""
        await self._dispatcher_sock.send(
            [compile_result.program_pointer_buf, compile_result.program_buf, task_id_bytes]
        )

    async def _send_heart(self):
        """Send monster heart to naga kernel"""
        await self._dispatcher_sock.send([heart_msg(), b""])
        self.share.send_dispatcher_heart_state()

    def _stop_task(self):
        """
        Stop all unfinished tasks
        """
        clear_ids = self.share.get_alive_task_ids()
        if clear_ids:
            for task_id in clear_ids:
                self.share.tid_rid_map.pop(task_id, None)
            res = self.web_client.delete_task(clear_ids)
            logger.log(LogLevel.TRANSFER, f"Stop ID {res}")

    async def run(self):
        try:
            logger.log(LogLevel.SYSTEM, f"{self} start ...")
            await asyncio.gather(
                self.listening_program(),
                self.listening_result(),
                self.listening_resource(),
            )
        except Exception:
            import traceback

            error_msg = traceback.format_exc()
            logger.error(f"{self} crash. {error_msg}")
        finally:
            self.share.stop = True
            if self._running is True:
                self.share.clear_ipc_file()
            self._stop_task()
            self.close_sock()
            logger.log(LogLevel.SYSTEM, f"{self} close ...")
