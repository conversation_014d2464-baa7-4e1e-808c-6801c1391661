# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/07/17
# __author:       <PERSON> Fang

"""
Floquet calibration 2q SQ phase composite experiment.
"""

from ....analysis.library.floquet_sq_phase_analysis import (
    FloquetSQphaseCompositeAnalysis,
)
from ....analysis.specification import ParameterRepr
from ....structures import Options
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import FloquetCalibrationSQphase


class FloquetSQphaseComposite(CompositeExperiment):
    """Floquet SQ phase Composite experiment."""

    _sub_experiment_class = FloquetCalibrationSQphase

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Define run options."""
        options = super()._default_run_options()

        options.x_data = []
        options.analysis_class = FloquetSQphaseCompositeAnalysis

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Define analysis options."""
        options = super()._default_analysis_options()

        options.result_parameters = []

        return options

    def _check_options(self):
        """Check option."""
        super()._check_options()

        x_data = []
        result_parameters = []
        for qubit in self.qubits:
            param_repr = ParameterRepr(
                name=f"{qubit.name}_phase",
                repr=f"{qubit.name}_phase",
                unit="",
                param_path=f"QubitPair.metadata.std.cz.params.{qubit.name}.phase",
            )
            x_data.append(int(qubit.name[1:]))
            result_parameters.append(param_repr)

        self.set_run_options(x_data=x_data)
        self.set_analysis_options(
            result_name=self.qubit_pair.name,
            result_parameters=result_parameters,
        )

    def _setup_child_experiment(self, exp, index: int, value: int):
        """Set child_experiment some options."""
        exp.run_options.index = index
        total = len(self.run_options.x_data)
        q_name = f"q{int(value)}"

        describe = f"ramsey {q_name}"
        exp.set_parent_file(self, describe, index, total)
        exp.set_experiment_options(ramsey_bit=q_name)
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp):
        """Collect child experiment result and provide it for parent."""
        phase = exp.analysis.results.phase.value
        exp.analysis.provide_for_parent.update({"phase": phase})
