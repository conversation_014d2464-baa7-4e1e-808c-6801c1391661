# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/05/17
# __author:       <PERSON><PERSON><PERSON>

from pyQCat.analysis.library.single_shot_composite_analysis import (
    ReadoutSampleDelayCaliAnalysis,
)
from pyQCat.errors import ExperimentOptionsError
from pyQCat.experiments.composite.readout.single_shot_composite import (
    SingleShotComposite,
)
from pyQCat.structures import Options
from pyQCat.tools import qarange


class ReadoutSampleDelayCalibrate(SingleShotComposite):
    """This experiment is used to optimize the sample delay of a qubit."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.optimize_field = "sample_delay"
        options.sweep_list = qarange(600, 1000, 50)  # Unit: ns
        options.set_validator("sweep_list", list)

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()

        options.analysis_class = ReadoutSampleDelayCaliAnalysis
        return options

    def _validate_options(self):
        super()._validate_options()
        for value in self.experiment_options.sweep_list:
            if value % 50 != 0:
                raise ExperimentOptionsError(
                    "Every value in `sweep_list` should be a multiple of 50.",
                    key="sweep_list",
                    value=self.experiment_options.sweep_list,
                )
