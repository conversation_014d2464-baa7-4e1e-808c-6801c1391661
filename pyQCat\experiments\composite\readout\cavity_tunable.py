# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/11/01
# __author:       <PERSON><PERSON><PERSON>

from typing import List, Union

import numpy as np

from ....analysis import (
    CavityCheckAnalysis,
    CavityCheckOnceAnalysis,
    CouplerTunableAnalysis,
    CouplerTunableAnalysisV2,
    CouplerTunableAnalysisV3,
    TunableAnalysis,
)
from ....analysis.specification import AnalysisResult
from ....errors import (
    ExperimentFieldError,
    ExperimentFlowError,
    ExperimentOptionsError,
    PyQCatError,
)
from ....structures import Options
from ....types import ExperimentRunMode, QualityDescribe, StandardContext
from ...composite_experiment import CompositeExperiment
from ...single import CavityFreqSpectrum, QubitSpectrum
from ...top_experiment_v1 import TopExperimentV1


class CavityTunable(CompositeExperiment):
    _sub_experiment_class = CavityFreqSpectrum

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("flux_list", list)
        options.set_validator("scan_name", ["dc", "ac", "ac_bias"])

        options.flux_list = None
        options.scan_name = "ac_bias"

        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("diff_threshold", (0, 1, 2))
        options.set_validator(
            "tackle_type", ["Qubit", "Coupler", "CouplerV1", "CouplerV2"]
        )
        options.set_validator("quality_bounds", list)

        options.data_key = ["fc"]
        options.is_plot = True
        options.figsize = (18, 12)

        options.diff_threshold = 0.1
        options.tackle_type = "Qubit"
        options.quality_bounds = [0.98, 0.95, 0.90]
        options.threshold = (-0.5, 0.5)

        return options

    def _check_options(self):
        super()._check_options()
        if self.experiment_options.scan_name == "ac":
            self._label = "ACTunable"
            self.analysis_options.threshold = (-0.5, 0.5)
            bit_name = self.coupler.name if self.coupler else self.qubit.name
            if self.ac_bias:
                self.ac_bias[bit_name][-1] = 0
                self.child_experiment.ac_bias[bit_name][-1] = 0
            if self.working_dc:
                self.working_dc[bit_name][-1] = 0
                self.child_experiment.working_dc[bit_name][-1] = 0
        elif self.experiment_options.scan_name == "ac_bias":
            if not self.ac_bias:
                raise ExperimentFieldError(
                    self.label, "not find ac bias, please check working type"
                )
            self._label = "ACBiasTunable"
            self.analysis_options.threshold = (-0.5, 0.5)
        else:
            if not self.working_dc:
                raise ExperimentFieldError(
                    self.label, "not find working dc, please check working type"
                )
            self._label = "DCTunable"
            self.analysis_options.threshold = (-5, 5)

        if len(self.qubits) == 1:
            self.run_options.exp_qubit = self.qubits[0]
        else:
            raise ExperimentFieldError(self.label, "The length of self.qubits is > 1.")

        if self.couplers:
            self.run_options.coupler = self.couplers[0]
            self.run_options.bit_type = "coupler"
        else:
            self.run_options.bit_type = "qubit"

        # bugfix for YangChao Zhao: 2024/02/21 CavityTunable must set readout point to 0.
        for qubit in self.qubits:
            qubit.readout_point.freq = None
            qubit.readout_point.amp = 0

        for coupler in self.couplers:
            coupler.readout_point.freq = None
            coupler.readout_point.amp = 0

        self.run_options.x_data = self.experiment_options.flux_list

        ana_map = {
            "Coupler": CouplerTunableAnalysis,
            "CouplerV1": CouplerTunableAnalysisV3,
            "CouplerV2": CouplerTunableAnalysisV2,
            "Qubit": TunableAnalysis,
        }
        self.run_options.analysis_class = ana_map[self.analysis_options.tackle_type]

        # feature: CavityTunable set pulse period default is 20 ns
        self.child_experiment.experiment_options.period = 20

    def _setup_child_experiment(
        self, cs_exp: CavityFreqSpectrum, index: int, flux: float
    ):
        cs_exp.run_options.index = index
        scan_name = self.experiment_options.scan_name

        total = len(self.run_options.x_data)
        describe = f"{scan_name} = {flux}v"
        cs_exp.set_parent_file(self, describe, index, total)

        if scan_name == "ac":
            if self.run_options.bit_type == "qubit":
                cs_exp.qubit.ac = flux
            else:
                cs_exp.coupler.ac = flux
        elif scan_name == "ac_bias":
            if self.run_options.bit_type == "qubit":
                base_qubit = self.child_experiment.qubit
            else:
                base_qubit = self.child_experiment.coupler

            # awg_channel = str(base_qubit.z_flux_channel)
            # cs_exp.ac_bias.update({base_qubit.name: [awg_channel, flux]})
            cs_exp.run_options.ac_bias = {base_qubit.name: flux}
        else:
            if self.run_options.bit_type == "qubit":
                base_qubit = self.child_experiment.qubit
            else:
                base_qubit = self.child_experiment.coupler
            dc_channel = str(base_qubit.z_dc_channel)
            cs_exp.working_dc.update({base_qubit.name: [dc_channel, flux]})

        self._check_simulator_data(cs_exp, index)

    def _handle_child_result(self, cs_exp: CavityFreqSpectrum):
        provide_field = self.analysis_options.data_key[0]
        # bugfix: Failure to fit sub experiments will result in experimental termination
        if cs_exp.analysis is None:
            raise ExperimentFlowError(
                self.label,
                "child exp without analysis object, maybe child exp error!",
            )
        elif cs_exp.analysis.analysis_state("result"):
            fr = cs_exp.analysis.results.fr.value
            ql = cs_exp.analysis.results.Ql.value
        elif cs_exp.run_options.index == 0 or not self._experiments[-1].analysis:
            # first fitting failed, using x_data mean substitution
            fr = np.mean(cs_exp.experiment_data.x_data)
            ql = 3000.0
        else:
            # replace with previous result
            fr = self._experiments[-1].analysis.provide_for_parent.get(
                provide_field
            ) or np.mean(cs_exp.experiment_data.x_data)
            ql = self._experiments[-1].analysis.provide_for_parent.get("ql") or 3000.0

        cs_exp.analysis.provide_for_parent.update({provide_field: fr * 1e-3, "ql": ql})

    def _set_result_path_by_type(self, qname: str, result_type: Union[str, List[str]]):
        update_dict = {
            "dc_max": "{}.dc_max",
            "dc_min": "{}.dc_min",
            "probe_freq": "{}.probe_freq",
            "fc_max": "{}.fc_max",
            "fc_min": "{}.fc_min",
            "tunable": "{}.tunable",
        }

        if type(result_type) is str:
            result_type = [result_type]
        for single_result in result_type:
            if (
                single_result in self.analysis.results
                and isinstance(self.analysis.results[single_result], AnalysisResult)
                and single_result in update_dict
            ):
                temp_path = None
                if qname.startswith("c"):
                    temp_path = update_dict[single_result].format("Coupler")
                elif qname.startswith("q"):
                    temp_path = update_dict[single_result].format("Qubit")
                if temp_path:
                    self.analysis.results[single_result].extra.update(
                        {"path": temp_path, "name": qname}
                    )

    def _set_result_path(self):
        if self.analysis.analysis_datas.fc:
            x = self.analysis.analysis_datas.fc.x
            y = self.analysis.analysis_datas.fc.y
            self.file.save_data(x, y, name="cavity_tunable")

        if self.couplers:
            for x in ["dc_max", "dc_min", "tunable"]:
                self._set_result_path_by_type(self.couplers[0].name, x)
        elif self.qubits:
            for x in ["dc_max", "dc_min", "probe_freq", "fc_max", "fc_min", "tunable"]:
                self._set_result_path_by_type(self.qubits[0].name, x)

            if self.experiment_options.scan_name == "ac":
                self.analysis.results["ac"].extra.update(
                    {"path": "Qubit.ac", "name": self.qubits[0].name}
                )
                self.analysis.results["ac"].value = self.analysis.results[
                    "dc_max"
                ].value


class CouplerTunableByQS(CompositeExperiment):
    _sub_experiment_class = QubitSpectrum

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("flux_list", list)
        options.set_validator("scan_name", ["dc", "ac", "ac_bias", "amp"])

        options.flux_list = None
        options.scan_name = "dc"

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.support_context = [StandardContext.CC]
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.is_plot = True
        options.subplots = (2, 1)
        options.figsize = (12, 12)
        options.tackle_type = "rough"
        options.fit_model = None
        options.plot_raw_data = False
        options.data_mode = "amp_phase"

        return options

    def _check_options(self):
        super()._check_options()

        if self.experiment_options.scan_name == "ac":
            self._label = "CouplerACTunable"
        elif self.experiment_options.scan_name == "ac_bias":
            self._label = "CouplerACBiasTunable"
        elif self.experiment_options.scan_name == "amp":
            self._label = "CouplerZAmpTunable"
        else:
            self._label = "CouplerDCTunable"

        if self.discriminator:
            self.analysis_options.data_mode = "I_Q"

        self.run_options.x_data = self.experiment_options.flux_list
        self.run_options.analysis_class = TunableAnalysis

    def _setup_child_experiment(self, qs_exp: QubitSpectrum, index: int, flux: float):
        qs_exp.run_options.index = index
        scan_name = self.experiment_options.scan_name

        total = len(self.run_options.x_data)
        describe = f"{scan_name} = {flux}v"
        qs_exp.set_parent_file(self, describe, index, total)

        if scan_name == "ac":
            self.coupler.ac = flux
        elif scan_name == "ac_bias":
            qs_exp.run_options.ac_bias = {self.coupler.name: flux}
        elif scan_name == "amp":
            qs_exp.set_experiment_options(z_amp=flux)
        else:
            dc_channel = str(self.coupler.z_dc_channel)
            qs_exp.working_dc.update({self.coupler.name: [dc_channel, flux]})

        self._check_simulator_data(qs_exp, index)


class CavityFreqOnce(TopExperimentV1):
    """CavityFreqSpectrum experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("fc_list", list, limit_null=True)
        options.set_validator("index", int)
        options.set_validator("cavity_count", int)
        options.set_validator("readout_power", (-40, -10, 1))

        options.fc_list = None
        options.index = 0
        options.cavity_count = 6
        options.readout_power = -30

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("quality_bounds", list)
        options.quality_bounds = [0.98, 0.95, 0.85]
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.support_context = [StandardContext.URM]
        options.qubit = None
        return options

    def _check_options(self):
        self.discriminator = None
        super()._check_options()
        if self.experiment_options.index >= len(self.qubits):
            raise ExperimentOptionsError(
                self,
                msg="index > qubit length",
                key="index",
                value=self.experiment_options.index,
            )

        self.set_experiment_options(
            data_type="amp_phase",
            multi_readout_channels=[
                self.qubits[self.experiment_options.index].readout_channel
            ],
        )
        self.set_run_options(
            x_data=self.experiment_options.fc_list,
            analysis_class=CavityCheckOnceAnalysis,
            qubit=self.qubits[self.experiment_options.index],
            custom_unit_describe=f"bus-{self.qubits[0].inst.bus}",
            measure_qubits=[self.qubits[self.experiment_options.index]],
        )

    def _metadata(self):
        metadata = super()._metadata()
        metadata.draw_meta.update(
            dict(
                qubit=self.run_options.qubit.name,
                cavity_count=self.experiment_options.cavity_count,
            )
        )
        return metadata

    @staticmethod
    def update_instrument(builder):
        fc_list = builder.experiment_options.fc_list
        readout_power = builder.experiment_options.readout_power
        builder.inst.set_power(
            "Readout_control", builder.run_options.qubit.readout_channel, readout_power
        )
        builder.inst.sweep_freq(
            "Readout_control",
            builder.run_options.qubit.readout_channel,
            points=fc_list,
            repeat=builder.experiment_options.repeat,
        )
        
    async def _async_analysis(self, require_id):
        try:
            return await super()._async_analysis(require_id)
        except PyQCatError:
            return self.analysis.empty_analysis(QualityDescribe.bad)


class CavityCheck(CompositeExperiment):
    _sub_experiment_class = CavityFreqOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("flux_list", list, limit_null=True)
        options.set_validator("init_cavity", list, limit_null=True)
        options.set_validator("scan_name", ["dc", "ac", "ac_bias"])
        options.set_validator("points", int)
        options.set_validator("scope", float)

        options.flux_list = None
        options.init_cavity = None
        options.scan_name = "ac_bias"
        options.points = 51
        options.scope = 3
        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = super()._default_run_options()
        options.support_context = [StandardContext.URM]
        return options

    def _check_options(self):
        super()._check_options()
        if self.experiment_options.scan_name == "ac":
            self.analysis_options.threshold = (-0.5, 0.5)
            for qubit in self.qubits:
                bit_name = qubit
                if self.ac_bias:
                    self.ac_bias[bit_name][-1] = 0
                    self.child_experiment.ac_bias[bit_name][-1] = 0
                if self.working_dc:
                    self.working_dc[bit_name][-1] = 0
                    self.child_experiment.working_dc[bit_name][-1] = 0
        elif self.experiment_options.scan_name == "ac_bias":
            if not self.ac_bias:
                raise ExperimentFieldError(
                    self.label, "not find ac bias, please check working type"
                )
            self.analysis_options.threshold = (-0.5, 0.5)
        else:
            if not self.working_dc:
                raise ExperimentFieldError(
                    self.label, "not find working dc, please check working type"
                )
            self.analysis_options.threshold = (-5, 5)

        if not self.experiment_options.init_cavity:
            self.experiment_options.init_cavity = [
                qubit.probe_freq or 6000 for qubit in self.qubits
            ]

        self.set_run_options(
            x_data=[
                flux
                for _ in range(len(self.qubits))
                for flux in self.experiment_options.flux_list
            ],
            analysis_class=CavityCheckAnalysis,
            custom_unit_describe=f"bus-{self.qubits[0].inst.bus}",
        )

        points = self.experiment_options.points
        scope = self.experiment_options.scope
        fc_list = []
        for cavity in self.experiment_options.init_cavity:
            fc_list.extend(
                np.round(
                    np.linspace(cavity - scope, cavity + scope, points), 3
                ).tolist()
            )
        self.child_experiment.set_experiment_options(
            fc_list=fc_list,
            cavity_count=len(self.experiment_options.init_cavity),
        )

    def _metadata(self):
        metadata = super()._metadata()
        metadata.process_meta.update(
            dict(
                qubits=[qubit.name for qubit in self.qubits],
                init_cavity=self.experiment_options.init_cavity,
            )
        )
        return metadata

    def _set_flux(self, cs_exp: CavityFreqOnce, qubit_name: str, flux: float):
        qubit = cs_exp.physical_units_map().get(qubit_name)
        scan_name = self.experiment_options.scan_name
        if scan_name == "ac":
            qubit.ac = flux
        elif scan_name == "ac_bias":
            cs_exp.run_options.ac_bias = {qubit.name: flux}
        else:
            cs_exp.working_dc.update({qubit.name: [str(qubit.z_dc_channel), flux]})

    def _setup_child_experiment(self, cs_exp: CavityFreqOnce, index: int, flux: float):
        cs_exp.run_options.index = index
        scan_name = self.experiment_options.scan_name
        flux_length = len(self.experiment_options.flux_list)
        q_index = index // flux_length
        qubit = self.qubits[q_index]
        total = len(self.run_options.x_data)
        describe = f"{scan_name} {qubit.name} = {flux}v"
        cs_exp.set_parent_file(self, describe, index, total)
        cs_exp.set_experiment_options(index=q_index)
        self._set_flux(cs_exp, qubit.name, flux)
        self._check_simulator_data(cs_exp, index)

    def _handle_child_result(self, cs_exp: CavityFreqOnce):
        try:
            fc = cs_exp.analysis.results.fc.value
        except Exception:
            fc = [None for _ in range(len(self.experiment_options.init_cavity))]
        cs_exp.analysis.provide_for_parent.update({"fc": fc})

    def _set_result_path(self):
        pass
