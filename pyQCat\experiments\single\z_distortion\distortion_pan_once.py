# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/17
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy

import numpy as np

from ....analysis import ParameterRepr
from ....analysis.library.distortion_pan_once_analysis import (
    DistortionPhaseScanAnalysis,
)
from ....concurrent.worker import ExperimentProtocolBuilder
from ....log import pyqlog
from ....pulse import Constant
from ....pulse.pulse_function import half_pi_pulse
from ....structures import MetaData, Options
from ....tools.find import cal_fidelity
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class DistortionPhaseScan(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("drag_gap", float)
        options.set_validator("t1", float)
        options.set_validator("t2", float)
        options.set_validator("z_amp", float)
        options.set_validator("delay", float)
        options.set_validator("exp_mode", ["half_square", "full_square"])
        options.set_validator("diy_readout_point", bool)

        options.drag_gap = 100
        options.t1 = 1000
        options.t2 = 1000
        options.delay = 5000
        options.phases = [0, np.pi / 2]
        # options.phases = qarange(0, 8, 0.261799)
        print(options.phases)
        options.correct_read = True
        options.z_amp = 0
        options.diy_readout_point = False
        options.exp_mode = "half_square"
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.result_parameters = []
        options.correct_read = True
        options.data_key = ["P1"]
        options.fidelity_matrix = None

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        return options

    @staticmethod
    def set_xy_pulses(builder: ExperimentProtocolBuilder):
        t1 = builder.experiment_options.t1
        delay = builder.experiment_options.delay
        drag_gap = builder.experiment_options.drag_gap
        t2 = builder.experiment_options.t2
        phases = builder.experiment_options.phases
        exp_mode = builder.experiment_options.exp_mode

        pulses = []
        for phase in phases:
            pulse0 = Constant(t1, 0, name="XY")
            pulse1 = Constant(delay, 0, name="XY")
            pulse2 = half_pi_pulse(builder.qubit)
            pulse3 = Constant(drag_gap, 0, name="XY")
            # pulse4 = Rphi_gate(phase=phase).to_pulse(builder.qubit)
            pulse4 = deepcopy(pulse2)
            pulse5 = Constant(t2, 0, name="XY")

            if exp_mode == "half_square":
                pulse = (
                    deepcopy(pulse0())
                    + pulse1()
                    + pulse2()
                    + pulse3()
                    + pulse4(phase=phase)
                    + pulse5()
                )
            elif exp_mode == "full_square":
                pulse = (
                    deepcopy(pulse0())
                    + pulse5()
                    + pulse1()
                    + pulse2()
                    + pulse3()
                    + pulse4(phase=phase)
                    + pulse0()
                )
            else:
                raise ValueError(f"exp_mode {exp_mode} is not supported.")
            pulses.append(pulse)

        builder.play_pulse("XY", builder.qubit, pulses)

    @staticmethod
    def set_z_pulses(builder):
        t1 = builder.experiment_options.t1
        delay = builder.experiment_options.delay
        drag_gap = builder.experiment_options.drag_gap
        t2 = builder.experiment_options.t2
        drag_time = half_pi_pulse(builder.qubit).width
        phases = builder.experiment_options.phases
        z_amp = builder.experiment_options.z_amp
        exp_mode = builder.experiment_options.exp_mode

        pulse0 = Constant(t1, 0)()

        if exp_mode == "half_square":
            pulse = Constant(delay + drag_time * 2 + drag_gap + t2, z_amp)()
            pulses = [deepcopy(pulse0) + pulse for _ in range(len(phases))]
        elif exp_mode == "full_square":
            pulse1 = Constant(t2, z_amp)()
            pulse2 = Constant(delay + drag_time * 2 + drag_gap, 0)()
            pulses = [deepcopy(pulse0) + pulse1 + pulse2 for _ in range(len(phases))]
        else:
            raise ValueError(f"exp_mode {exp_mode} is not supported.")
        builder.play_pulse("Z", builder.qubit, pulses)

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.draw_meta = {
            "drag_gap": (self.experiment_options.drag_gap, "ns"),
            "drag_width": (self.qubit.XYwave.time, "ns"),
            "drag_offset": (self.qubit.XYwave.offset, "ns"),
            "z_amp": (self.experiment_options.z_amp, "V"),
            "exp_mode": self.experiment_options.exp_mode,
        }
        return metadata

    def _check_options(self):
        super()._check_options()

        self.qubit.XYwave.offset = 0
        pyqlog.warning(f"XYwave.offset is forced to set to zero.")

        z_amp = self.experiment_options.z_amp
        diy_readout_point = self.experiment_options.diy_readout_point

        if self.experiment_options.exp_mode == "half_square":
            for qc in self.compensates.keys():
                if qc.name == self.qubit.name:
                    qc.ac = 0.0
                    if diy_readout_point is False:
                        qc.readout_point_model = "Constant"
                        qc.readout_point = Options(amp=z_amp)
                    else:
                        qc.readout_point_model = "Constant"
                        qc.readout_point = Options(
                            amp=qc.readout_point.amp + self.experiment_options.z_amp
                        )

                    pyqlog.info(
                        f"target: {self.qubit},"
                        f"diy_readout_point: {diy_readout_point}, "
                        f"ac: {qc.ac} V, "
                        f"readout_point_model: {qc.readout_point_model}, "
                        f"readout_point.amp: {qc.readout_point.amp} V"
                    )

            if self.ac_bias.get(self.qubit.name):
                old_awg_bias = self.ac_bias[self.qubit.name][1]
                self.ac_bias[self.qubit.name][1] = old_awg_bias - z_amp

        if self.discriminator is None:
            raise ValueError(f"Must use dcm to get p0/p1")

        fidelity_matrix = cal_fidelity(self.discriminator)
        if self.experiment_options.correct_read:
            self.set_run_options(fidelity_matrix=fidelity_matrix)
        else:
            self.set_analysis_options(fidelity_matrix=fidelity_matrix)

        if len(self.experiment_options.phases) == 2:
            self._label = f"DistortionX2Y2Once"
            self.set_analysis_options(data_key=["No"])
            self.set_analysis_options(
                result_parameters=[
                    ParameterRepr(name="x", repr="x"),
                    ParameterRepr(name="y", repr="y"),
                    ParameterRepr(name="phase", repr="phase", unit="°"),
                ]
            )
        else:
            self._label = f"DistortionSweepPhaseOnce"
            self.set_analysis_options(
                result_parameters=[
                    ParameterRepr(name="point", repr="max_p1_phase", unit="°")
                ]
            )

        # if self.experiment_options.detune is not None:
        #     pyqlog.log("EXP", f"Update detune pi2 {self.experiment_options.detune}")
        #     self.qubit.XYwave.detune_pi2 += self.experiment_options.detune

        self.set_analysis_options(correct_read=self.experiment_options.correct_read)
        self.set_experiment_options(data_type="I_Q")
        self.set_run_options(
            x_data=self.experiment_options.phases,
            analysis_class=DistortionPhaseScanAnalysis,
        )


class DistortionPhaseDetuneScan(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("t1", float)
        options.set_validator("t2", float)
        options.set_validator("detune", float)
        options.set_validator("z_amp", float)
        options.set_validator("delay", float)
        options.set_validator("diy_readout_point", bool)
        options.set_validator("phases", list)

        options.t1 = 1000
        options.t2 = 1000
        options.delay = 5000
        options.phases = [0, np.pi / 2]
        options.correct_read = True
        options.z_amp = 0
        options.detune = None
        options.diy_readout_point = False
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.result_parameters = []
        options.correct_read = True
        options.data_key = ["P1"]
        options.fidelity_matrix = None

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        return options

    @staticmethod
    def set_xy_pulses(builder):
        t1 = builder.experiment_options.t1
        delay = builder.experiment_options.delay
        t2 = builder.experiment_options.t2
        phases = builder.experiment_options.phases

        if builder.experiment_options.detune is not None:
            detune = builder.experiment_options.detune * 1e-3
        else:
            detune = builder.qubit.XYwave.detune_pi2 * 1e-3

        pulses = []
        for phase in phases:
            pulse0 = Constant(t1, 0, name="XY")
            pulse1 = Constant(delay, 0, name="XY")
            pulse2 = half_pi_pulse(builder.qubit)
            pulse3 = deepcopy(pulse2)
            pulse4 = Constant(t2, 0, name="XY")

            pulse = (
                pulse0()
                + pulse1()
                + pulse2(detune=detune)
                + pulse3(phase=phase)
                + pulse4()
            )
            pulses.append(pulse)

        builder.play_pulse("XY", builder.qubit, pulses)

    @staticmethod
    def set_z_pulses(builder):
        t1 = builder.experiment_options.t1
        delay = builder.experiment_options.delay
        t2 = builder.experiment_options.t2
        drag_time = half_pi_pulse(builder.qubit).width
        phases = builder.experiment_options.phases
        z_amp = builder.experiment_options.z_amp

        pulse0 = Constant(t1, 0)()

        pulse = Constant(delay + drag_time * 2 + t2, z_amp)()
        pulses = [deepcopy(pulse0) + pulse for _ in range(len(phases))]
        builder.play_pulse("Z", builder.qubit, pulses)

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.draw_meta = {
            "drag_gap": (self.experiment_options.drag_gap, "ns"),
            "drag_width": (self.qubit.XYwave.time, "ns"),
            "drag_offset": (self.qubit.XYwave.offset, "ns"),
            "z_amp": (self.experiment_options.z_amp, "V"),
            "exp_mode": self.experiment_options.exp_mode,
        }
        return metadata

    def _check_options(self):
        super()._check_options()

        self.qubit.XYwave.offset = 0
        pyqlog.warning(f"XYwave.offset is forced to set to zero.")

        if self.discriminator is None:
            raise ValueError(f"Must use dcm to get p0/p1")

        z_amp = self.experiment_options.z_amp
        diy_readout_point = self.experiment_options.diy_readout_point
        for qc in self.compensates.keys():
            if qc.name == self.qubit.name:
                qc.ac = 0.0
                if diy_readout_point is False:
                    qc.readout_point_model = "Constant"
                    qc.readout_point = Options(amp=z_amp)
                else:
                    qc.readout_point_model = "Constant"
                    qc.readout_point = Options(amp=qc.readout_point.amp)

                pyqlog.info(
                    f"target: {self.qubit},"
                    f"diy_readout_point: {diy_readout_point}, "
                    f"ac: {qc.ac} V, "
                    f"readout_point_model: {qc.readout_point_model}, "
                    f"readout_point.amp: {qc.readout_point.amp} V"
                )

        fidelity_matrix = cal_fidelity(self.discriminator)
        if self.experiment_options.correct_read:
            self.set_run_options(fidelity_matrix=fidelity_matrix)
        else:
            self.set_analysis_options(fidelity_matrix=fidelity_matrix)

        if len(self.experiment_options.phases) == 2:
            self._label = f"DistortionDetuneX2Y2Once"
            self.set_analysis_options(data_key=["No"])
            self.set_analysis_options(
                result_parameters=[
                    ParameterRepr(name="x", repr="x"),
                    ParameterRepr(name="y", repr="y"),
                    ParameterRepr(name="phase", repr="phase", unit="°"),
                ]
            )
        else:
            self._label = f"DistortionDetuneSweepPhaseOnce"
            self.set_analysis_options(
                result_parameters=[
                    ParameterRepr(name="point", repr="max_p1_phase", unit="°")
                ]
            )

        # if self.experiment_options.detune is not None:
        #     pyqlog.log("EXP", f"Update detune pi2 {self.experiment_options.detune}")
        #     self.qubit.XYwave.detune_pi2 += self.experiment_options.detune

        self.set_analysis_options(correct_read=self.experiment_options.correct_read)
        self.set_experiment_options(data_type="I_Q")
        self.set_run_options(
            x_data=self.experiment_options.phases,
            analysis_class=DistortionPhaseScanAnalysis,
        )
