# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/07/30
# __author:       <PERSON><PERSON><PERSON>

import textwrap
from abc import ABC

from ..structures import Options, QDict
from ..types import QualityDescribe
from .quality import BaseQuality
from .top_analysis import TopAnalysis


class BaseAnalysis(TopAnalysis, ABC):
    @classmethod
    def _default_options(cls) -> Options:
        """Default analysis options common to all analyzes."""
        # Raw data selections.
        options = super()._default_options()

        # Result options.
        options.result_parameters = []
        options.sub_analysis = []
        options.sub_figure_names = []

        return options

    def _check_options(self):
        pass

    def _create_analysis_data(self) -> QDict:
        return QDict()

    def _data_processing(self):
        pass

    def _evaluate_quality(self) -> None:
        self._quality = BaseQuality.instantiate(QualityDescribe.normal)

    def _extract_result(self):
        pass

    def _initialize_canvas(self):
        pass

    def _visualization(self):
        pass

    def _format_canvas(self):
        pass

    def _description(self, width: int = 70):
        """Returns the description of the analysis result, which is often used as the title of the experiment result
        plot.

        Args:
            width: The width of the description. If width is `30`, then each line of the description has 30 characters.
            If set to `None`, then no wrap will be performed.
            The default value of `figsize` (set in `pyQCat.experiments.base_experiment.BaseExperiment.
            _default_analysis_options`) is `(12, 8)`, in which case `52` can be an appropriate value for width.

        Returns:
            The description of the analysis result.

        Raises:
            ValueError: If width <= 0, this error is raised.
        """
        if width and width <= 0:
            raise ValueError(f"The width ({width}) is invalid (must be > 0).")

        metadata = self.experiment_data.metadata
        custom_unit_describe = metadata.process_meta.get("custom_unit_describe")
    
        if not metadata:
            return f"{self.__class__.__name__}"

        str_list = []

        if metadata.name:
            str_list.append(metadata.name)

        if custom_unit_describe:
            str_list.append(custom_unit_describe)
        elif metadata.qubits or metadata.couplers:
            list_of_units = []
            if metadata.qubits:
                list_of_units.extend(metadata.qubits)
            if metadata.couplers:
                list_of_units.extend(metadata.couplers)
            unit_description = "("
            unit_description += ", ".join(list_of_units)
            unit_description += ")"
            str_list.append(unit_description)

        if metadata.draw_meta:
            list_of_draw_meta = []
            for k, v in metadata.draw_meta.items():
                if isinstance(v, tuple):
                    list_of_draw_meta.append(f"{k}={v[0]}{v[1]}")
                else:
                    list_of_draw_meta.append(f"{k}={v}")
            str_list.append(", ".join(list_of_draw_meta))

        if self.results is not None and len(self.results) > 0:
            for result in self.results.values():
                if result.extra.get("out_flag") is not False:
                    str_list.append(result.__str__())

        if self.quality:
            if isinstance(self.quality, str):
                str_list.append(f"quality={self.quality}")
            else:
                str_list.append(self.quality.__repr__())

        description = " ".join(str_list)
        if width:
            description = "\n".join(textwrap.wrap(description, width=width))

        return description

    def run_analysis(self):
        # Used for checking some initial options
        # Default empty implementation, subclasses can be overridden
        self._check_options()

        # Create analysis data from experiment data
        # Default empty implementation, subclasses can be overridden
        self._analysis_data_dict = self._create_analysis_data()

        # Create analysis result templates for analysis classes
        # Implemented by TopAnalysis
        self._results = self._create_analysis_result()

        # Specific data processing tasks, such as fitting, smoothing,
        # peak searching, tomography, etc
        # Implemented by subclass, default empty
        self._data_processing()

        # Analysis Quality evaluate, usually used to set the `quality`` attribute
        # Implemented by subclass, abstract method, subclass must be rewritten
        self._evaluate_quality()

        # Extract results and provide them to the initial Analysis Result template
        # Default empty implementation, subclasses can be overridden
        self._extract_result()

        if self.options.is_plot:
            # Initialize the drawing canvas
            # Default empty implementation, subclasses can be overridden
            self._initialize_canvas()

            # Specific drawing logic can be used to draw raw data, fitted data,
            # spectrograms, scatter plots, and so on
            # Default empty implementation, subclasses can be overridden
            self._visualization()

            # Format the drawing and beautify the output
            # Default implementation, generally not rewritten
            self._format_canvas()
