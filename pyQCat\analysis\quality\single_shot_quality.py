# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/16
# __author:       ssfang

from typing import Tuple, Union

import numpy as np

from .base_quality import BaseQuality, QualityDescribe


class SingleShotQuality(BaseQuality):
    """SingleShot analysis quality evaluation standard."""

    def __init__(
        self,
        k_recommend: int,
        *fidelity: float,
        outlier: float = None,
        quality_bounds: Union[Tuple, list] = None,
        proportion: float = None,
    ):
        """Initial SingleShot Quality object.

        Args:
            k_recommend (int): Model ideal clusters.
            fidelity (float): The `0`, '1', '2' state fidelity.
            outlier (outlier): The dcm outlier value.
            quality_bounds (Tuple): The bounds of quality judge standard.
                normal like [2, 0.85, 0.7, 0.011] or [3, 0.85, 0.7, 0.7, 0.011]
            proportion (proportion): The dcm proportion value.
        """
        super().__init__()
        self._k_recommend = k_recommend
        self._fidelity = fidelity
        self._outlier = outlier
        self._quality_bounds = quality_bounds
        self._proportion = proportion

        self._err_msg = None

    def __str__(self):
        msg = f"Classification {self._quality.value}."
        if self._err_msg is not None:
            msg += "\n"
            msg += self._err_msg
        return msg

    def evaluate(self, check_k: bool = False):
        """Calculate quality value."""
        if check_k is True and self._quality_bounds[0] != self._k_recommend:
            self._quality = QualityDescribe.bad
            self._err_msg = f"Expect k is `{self._quality_bounds[0]}`, but k is `{self._k_recommend}` !"
            return

        if self._proportion is None:
            _, *fidelity, outlier = self._quality_bounds
            if self._outlier <= outlier:
                # if len(self._fidelity) == len(fidelity) == k_recommend:
                if len(self._fidelity) == len(fidelity):
                    diff_arr = np.array(self._fidelity) - np.array(fidelity)
                    new_arr = np.where(diff_arr >= 0, 1, 0)
                    if new_arr.all():
                        self._quality = QualityDescribe.normal
                    else:
                        self._quality = QualityDescribe.bad
                        self._err_msg = (
                            "Readout Fidelity is not achieved!"
                            " Please check the readout parameters and "
                            "the drive parameters!"
                        )
                else:
                    self._quality = QualityDescribe.bad
                    self._err_msg = f"quality_bounds: {self._quality_bounds}, but fidelity: {fidelity} length error!"
            else:
                self._quality = QualityDescribe.bad
                self._err_msg = f"Outlier is {self._outlier}."
        else:
            _, *fidelity, outlier, proportion = self._quality_bounds
            if self._proportion <= proportion:
                # if len(self._fidelity) == len(fidelity) == k_recommend:
                if len(self._fidelity) == len(fidelity):
                    diff_arr = np.array(self._fidelity) - np.array(fidelity)
                    new_arr = np.where(diff_arr >= 0, 1, 0)
                    if new_arr.all():
                        if self._outlier <= outlier:
                            self._quality = QualityDescribe.normal
                        else:
                            self._quality = QualityDescribe.bad
                            self._err_msg = "Error, [2 states] Too many, please adjust the power parameters! "
                    else:
                        self._quality = QualityDescribe.bad
                        self._err_msg = (
                            "Readout Fidelity is not achieved!"
                            " Please check the readout parameters and "
                            "the drive parameters!"
                        )
                else:
                    self._quality = QualityDescribe.bad
                    self._err_msg = f"open proportion | quality_bounds: {self._quality_bounds}, but fidelity: {fidelity} length error!"
            else:
                self._quality = QualityDescribe.bad
                self._err_msg = f"Proportion is {self._proportion}."
