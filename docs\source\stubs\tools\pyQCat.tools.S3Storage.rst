﻿pyQCat.tools.S3Storage
======================

.. currentmodule:: pyQCat.tools

.. autoclass:: S3Storage

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~S3Storage.__init__
      ~S3Storage.bucket_exist
      ~S3Storage.compose_object
      ~S3Storage.get_object
      ~S3Storage.list_buckets
      ~S3Storage.list_objects
      ~S3Storage.make_bucket
      ~S3Storage.put_figure
      ~S3Storage.put_object
      ~S3Storage.remove_bucket
      ~S3Storage.remove_object
   
   

   
   
   