# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.oscillation_exp_decay.rst:2
msgid "pyQCat.analysis.algorithms.oscillation\\_exp\\_decay"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:1
msgid "Get exponential decay parameter from oscillating signal."
msgstr "从振荡信号中获取指数衰减参数。"

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:3
msgid "This assumes following function form."
msgstr "假设有下面的函数模型："

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:5
msgid "y(x) = e^{\\alpha x} F(x),"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:9
msgid ""
"where :math:`F(x)` is arbitrary oscillation function oscillating at "
"``freq_guess``. This function first applies a Savitzky-Golay filter to y "
"value, then run scipy peak search to extract peak positions. If "
"``freq_guess`` is provided, the search function will be robust to fake "
"peaks due to noise. This function calls :py:func:`exp_decay` function for"
" extracted x and y values at peaks."
msgstr ""
"其中 :math:`F(x)` 是频率为 ``freq_guess`` 的任意振荡函数，此函数首先"
"将 ``Savitzky-Golay`` 过滤器应用于 y 值，然后运行 ``scipy peak search`` 以"
"提取峰值位置。 如果提供了 ``freq_guess``，则搜索功能将对由于噪声造成的假峰值"
"具有鲁棒性。 此函数调用 ``exp_decay()`` 函数以提取峰值处的 x 和 y 值。"

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:17
msgid ""
"y values should contain more than one cycle of oscillation to use this "
"guess approach."
msgstr "y 值应该包含一个以上的振荡周期才能使用这种猜测方法。"

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:20
msgid "Array of x values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:22
msgid "Array of y values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:24
msgid "Window size of Savitzky-Golay filter. This should be odd number."
msgstr "``Savitzky-Golay`` 平滑器的窗口大小，应该为奇数。"

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:26
msgid "Dimension of Savitzky-Golay filter."
msgstr "``Savitzky-Golay`` 平滑器的维度"

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:28
msgid "Optional. Initial frequency guess of :math:`F(x)`."
msgstr ":math:`F(x)` 的初始猜测频率"

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:30
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.oscillation_exp_decay:31
msgid "Decay rate of signal."
msgstr "信号的衰减率"

