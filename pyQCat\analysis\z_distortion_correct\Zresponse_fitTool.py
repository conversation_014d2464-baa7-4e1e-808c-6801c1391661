# -*- coding: UTF-8 -*-
"""
@Project ：
@File description：a new scheme to characterize and correct z line distortion based on poles model proposed by ll guo
<AUTHOR> <PERSON>
@Date    ：7/27/22 9:58 AM
"""
import copy
from pathlib import Path
import numpy as np
from typing import List
from scipy.optimize import curve_fit
from scipy import signal
import matplotlib.pyplot as plt
import time

# no_name = Constant(30, 0)
# Ts = 1/no_name.sample_rate
# Ts = 0.625


def RMSE(x, y1, y2):
    variances = list(map(lambda x, y: (x - y) ** 2, y1, y2))
    variance = np.sum(variances)
    rmse = np.sqrt(variance / len(x))
    # print(variance)
    return rmse


def curve_fit_step(x, y, p0, func, num, bounds):
    popt, rmse, fit_y = None, None, None
    p = p0
    for _ in range(num):
        popt, pocv = curve_fit(func, x, y, p, bounds=bounds, maxfev=100000)
        p = popt
        fit_y = func(x, *popt)
        rmse = RMSE(x, y, fit_y)
        if rmse <= 1e-3:
            break
    return popt, rmse, fit_y


def generate_poles_model_params_automatically(
    real_poles_num, complex_poles_num, temperature
):
    """
    [C0, 𝜏_(real, 1), 𝐶_(𝑟𝑒𝑎𝑙, 2), 𝜏_(real, 2)……𝐶_(𝑟𝑒𝑎𝑙, real_poles_num),𝜏_(real, real_poles_num),
    𝐶_(𝑐𝑜𝑚𝑝, 1), 𝜑_(𝑟, 1), 𝜏_(comp, 1), Tos_(1)……
    𝐶_(𝑐𝑜𝑚𝑝, complex_poles_num),  𝜑_(𝑟, complex_poles_num),𝜏_(comp, complex_poles_num), Tos_(complex_poles_num)]
    :return:
    """
    # temperature = "room_temperature"  # "low_temperature"
    if temperature == "room_temperature":
        real_pole_chart = np.loadtxt(
            r"real_pole_params_fit_room_temperature.csv", skiprows=1, delimiter=","
        )
        complex_pole_chart = np.loadtxt(
            r"complex_pole_params_fit_room_temperature.csv", skiprows=1, delimiter=","
        )
    elif temperature == "low_temperature":
        real_csv_path = str(
            Path(str(Path(__file__).parent), "real_pole_params_fit_low_temperature.csv")
        )
        complex_csv_path = str(
            Path(
                str(Path(__file__).parent),
                "complex_pole_params_fit_low_temperature.csv",
            )
        )
        real_pole_chart = np.loadtxt(real_csv_path, skiprows=1, delimiter=",")
        complex_pole_chart = np.loadtxt(complex_csv_path, skiprows=1, delimiter=",")

    real_poles_paras = real_pole_chart[:, : 2 * real_poles_num]
    complex_poles_paras = complex_pole_chart[:, : 4 * complex_poles_num]
    paras = np.column_stack((real_poles_paras, complex_poles_paras))
    poles_model_params = paras[0, :]
    lb = paras[1, :]
    ub = paras[2, :]
    return poles_model_params, lb, ub


def data_fit_using_poles_model(
    real_poles_num,
    complex_poles_num,
    step_response_normalization,
    filepath=None,
    filename=None,
    start_time=0,
    Ts=0.625,
    square_width=0,
    response_type="rise",
    temperature="room_temperature",
):
    data = np.genfromtxt(filepath + filename)
    Time = data[:, 0]
    Zresponse = data[:, 1]
    # start_index = int(start_time / (Time[1]-Time[0]))
    start_index = np.where(Time >= start_time)[0]
    start_point, lb, ub = generate_poles_model_params_automatically(real_poles_num, complex_poles_num, temperature)
    start_point = np.hstack((start_point, [0.999]))
    lb = np.hstack((lb, [0.9]))
    ub = np.hstack((ub, [1.1]))
    # start_point[0] = Zresponse[0]
    print(f"when fit the unit step response, sampling rate is {Ts} ns")
    amplification_factor = 1e4
    fit_p = curve_fit_step(Time[start_index], Zresponse[start_index]*amplification_factor, start_point,
                           step_response_fit(real_poles_num, complex_poles_num, step_response_normalization,
                                             Ts, square_width, response_type, amplification_factor),
                           10, bounds=(lb, ub))
    fit_result = fit_p
    # C0 = fit_result[0]

    # # plot
    datestr = time.strftime("%Y-%m-%d %H.%M.%S", time.localtime())
    new_response_png = (
        datestr
        + f"_poles_model_fit_{real_poles_num}real_poles_{complex_poles_num}complex_poles_start_fit_at{start_time}ns.png"
    )
    # new_response_title = plot_title(fit_p)
    fit_complex_plot(
        real_poles_num,
        complex_poles_num,
        step_response_normalization,
        fit_p,
        Time,
        Zresponse,
        title=None,
        png_name=filepath + new_response_png,
        Ts=Ts,
        square_width=square_width,
        response_type=response_type,
    )
    np.savetxt(
        filepath
        + datestr
        + f"_poles_model_fit_result_{real_poles_num}real_poles_{complex_poles_num}complex_poles_start_fit_at{start_time}ns.dat",
        fit_result[0],
    )
    return fit_result


def step_response_fit(
    real_poles_num,
    complex_poles_num,
    step_response_normalization,
    Ts,
    square_width,
    response_type,
    amplification_factor,
):
    def fit_function(x, *poles_model_params):
        poles_model_params_dic = arrange_poles_model_params(
            poles_model_params,
            real_poles_num,
            complex_poles_num,
            step_response_normalization,
            Ts,
            response_type,
        )
        y = (
            get_from_equation(
                x, poles_model_params_dic, Ts, square_width, response_type
            )
            * amplification_factor
        )
        return y

    return fit_function


def fit_complex_plot(
    real_poles_num,
    complex_poles_num,
    step_response_normalization,
    fit_p,
    time_data,
    zersponse,
    title,
    png_name,
    Ts,
    square_width,
    response_type,
):
    fig, ax = plt.subplots(figsize=(16, 9))
    ax.scatter(time_data, zersponse, label="Experiment", s=6, c="b")
    poles_model_params_dic = arrange_poles_model_params(
        fit_p[0],
        real_poles_num,
        complex_poles_num,
        step_response_normalization,
        Ts,
        response_type,
    )
    y_equation = get_from_equation(
        time_data, poles_model_params_dic, Ts, square_width, response_type
    )
    ax.plot(time_data, y_equation, c="r", label="fit R2 = {:.6f}".format(fit_p[1]))
    # save time_data, y_equation
    ax.legend(loc="lower right", fontsize=16)
    ax.set_title(title, fontsize=20)
    ax.set_xlabel("Time(ns)", fontsize=28)
    ax.set_ylabel("Zresponse", fontsize=28)
    plt.tick_params(labelsize=20)
    ax.grid()
    different = zersponse - y_equation
    ax2 = ax.twinx()
    ax2.plot(time_data, different, c="k")
    ax2.plot(time_data, np.ones(len(time_data)) * -0.001, c="k", linestyle="--")
    ax2.plot(time_data, np.ones(len(time_data)) * 0.001, c="k", linestyle="--")
    ax2.set_ylim(-0.005, 0.005)
    # ax2.set_xlim(-2, 20)
    plt.tick_params(labelsize=20)
    # plt.show()
    fig.savefig(png_name, dpi=300)
    # plt.close(fig)


def get_from_equation(
    x, poles_model_params_dic, Ts, square_width=0, response_type="rise"
):
    if response_type == "rise":
        return get_from_equation_rise(x, poles_model_params_dic, Ts)
    elif response_type == "rise_and_fall":
        return get_from_equation_rise_and_fall(
            x, poles_model_params_dic, Ts, square_width
        )


def get_from_equation_rise_and_fall(x, poles_model_params_dic, Ts, square_width):
    y_rise = get_from_equation_rise(x, poles_model_params_dic, Ts, x0=0)
    y_fall = get_from_equation_rise(x, poles_model_params_dic, Ts, x0=square_width)
    if x[-1] > square_width:
        index_square_width = np.argmin(np.diff(np.hstack(([1], x < square_width))))
        y_fall[:index_square_width] = 0  # casual system
    else:
        y_fall = 0
    y = y_rise - y_fall
    return y


def get_from_equation_rise(x, poles_model_params_dic, Ts, x0=0):
    x = x - x0
    C0 = poles_model_params_dic["C0"][0]
    y = C0
    real_poles_num = poles_model_params_dic["real_poles"].shape[0]
    complex_poles_num = poles_model_params_dic["complex_poles"].shape[0]
    for i in list(range(real_poles_num)):
        Creal, pk = poles_model_params_dic["real_poles"][i]
        tao = -1 / np.log(pk) * Ts
        yun_real = -1 * pk * np.exp(-x / tao) + 1
        y = y + yun_real * Creal
    for j in list(range(complex_poles_num)):
        Ccomp, phir, p, phip = poles_model_params_dic["complex_poles"][j]
        r = compute_r(p, phip, phir)
        m3 = 1
        m_imag = -((2 * r * np.cos(phir) - m3) * np.cos(phip) + m3 * p) / (
            2 * np.sin(phip)
        )
        m1 = r * np.cos(phir) - m3 / 2 + 1j * m_imag

        A12 = abs(m1)
        phi12 = np.angle(m1)

        tao2 = -1 / np.log(p) * Ts
        yun_complex = 2 * A12 * np.exp(-x / tao2) * np.cos(phip / Ts * x + phi12) + m3
        y = y + yun_complex * Ccomp
    return y * poles_model_params_dic["normalization_coefficient"]


def arrange_poles_model_params(
    poles_model_params,
    real_poles_num,
    complex_poles_num,
    step_response_normalization=True,
    Ts=0.625,
    response_type="rise",
):
    """
    :param poles_model_params:
    before transformation: [C0, 𝜏_(real,1),𝐶_(𝑟𝑒𝑎𝑙,2), 𝜏_(real,2)……𝐶_(𝑟𝑒𝑎𝑙,real_poles_num),
    𝜏_(real,real_poles_num),𝐶_(𝑐𝑜𝑚𝑝,1),𝜑_(𝑟,1),𝜏_(comp,1),Tos_(1)……𝐶_(𝑐𝑜𝑚𝑝,complex_poles_num),𝜑_(𝑟,complex_poles_num),
    𝜏_(comp,complex_poles_num), Tos_(complex_poles_num)]

    after transformation: [C0, 𝐶_(𝑟𝑒𝑎𝑙,1), 𝑝_(𝑘,1),𝐶_(𝑟𝑒𝑎𝑙,2), 𝑝_(𝑘,2)……𝐶_(𝑟𝑒𝑎𝑙,real_poles_num),
    𝑝_(𝑘,real_poles_num),𝐶_(𝑐𝑜𝑚𝑝,1),𝜑_(𝑟,1),p1,𝜑_(p,1)……𝐶_(𝑐𝑜𝑚𝑝,complex_poles_num),𝜑_(𝑟,complex_poles_num),
    p_(complex_poles_num), 𝜑_(p,complex_poles_num)],

    𝑝_(𝑘,real_poles_num) determine the decay rate of exp response,
    p_(complex_poles_num) determine the decay rate of oscillation response,
    𝜑_(p,complex_poles_num) determine the period of oscillation response,
    rearmost real pole has the longest exp response,
    rearmost complex pole has the shortest oscillation period,
    add new real pole from the head of the list
    add new complex pole from the end of the list
    :param real_poles_num:
    :param complex_poles_num:
    :param step_response_normalization:
    :return:
    """
    # arrange poles_model_params
    if not isinstance(poles_model_params, List):
        poles_model_params = list(poles_model_params)
    if step_response_normalization:
        poles_model_params.insert(1, 0)
    C0 = poles_model_params[0]
    poles_model_params_dic = {"C0": [C0], "real_poles": [], "complex_poles": []}
    for i in list(range(real_poles_num)):
        Creal = poles_model_params[1 + 2 * i]
        pk = tao2p(poles_model_params[2 + 2 * i], Ts)
        poles_model_params_dic["real_poles"].append([Creal, pk])
    for j in list(range(complex_poles_num)):
        Ccomp = poles_model_params[1 + 4 * j + 2 * real_poles_num]
        phir = poles_model_params[2 + 4 * j + 2 * real_poles_num]
        p = tao2p(poles_model_params[3 + 4 * j + 2 * real_poles_num], Ts)
        phip = tos2phip(poles_model_params[4 + 4 * j + 2 * real_poles_num], Ts)
        poles_model_params_dic["complex_poles"].append([Ccomp, phir, p, phip])
    poles_model_params_dic["real_poles"] = np.array(
        poles_model_params_dic["real_poles"]
    )
    poles_model_params_dic["complex_poles"] = np.array(
        poles_model_params_dic["complex_poles"]
    )
    if step_response_normalization:
        # compute 𝐶_(𝑟𝑒𝑎𝑙,1) according to normalization constraint
        if complex_poles_num:
            poles_model_params_dic["real_poles"][0, 0] = (
                1
                - np.sum(poles_model_params_dic["real_poles"][:, 0])
                - np.sum(poles_model_params_dic["complex_poles"][:, 0])
                - C0
            )

        else:
            poles_model_params_dic["real_poles"][0, 0] = (
                1 - np.sum(poles_model_params_dic["real_poles"][:, 0]) - C0
            )

    if len(poles_model_params) == 2 * real_poles_num + 4 * complex_poles_num + 2:
        poles_model_params_dic["normalization_coefficient"] = poles_model_params[-1]
    return poles_model_params_dic


def generate_ab_from_poles_model_params(
    poles_model_params,
    real_poles_num,
    complex_poles_num,
    step_response_normalization=True,
    check_ab=True,
    Ts=0.625,
):
    print(f"when compute ab, sampling rate is {Ts} ns")
    poles_model_params_dic = arrange_poles_model_params(
        poles_model_params,
        real_poles_num,
        complex_poles_num,
        step_response_normalization,
        Ts,
    )
    # compute a,b from poles_model_params_dic
    C0 = poles_model_params_dic["C0"][0]
    Clist = [C0]
    Rlist = []
    plist = []
    for i in list(range(real_poles_num)):
        Creal, pk = poles_model_params_dic["real_poles"][i]
        plist.append(pk)
        Rlist.append((1 - pk) * Creal)
    for j in list(range(complex_poles_num)):
        Ccomp, phir, p, phip = poles_model_params_dic["complex_poles"][j]
        r = compute_r(p, phip, phir)
        plist.append(p * np.exp(1j * phip))
        Rlist.append(r * np.exp(1j * phir) * Ccomp)
        plist.append(p * np.exp(-1j * phip))
        Rlist.append(r * np.exp(-1j * phir) * Ccomp)

    b, a = signal.invresz(Rlist, plist, Clist, tol=1e-10)
    print(
        "sum(b)={:.8f},sum(a)={:.8f},sum(b)/sum(a)={:.8f}".format(
            np.sum(b) * 1e9, np.sum(a) * 1e9, np.sum(b) / np.sum(a)
        )
    )
    a = np.real(a)
    b = np.real(b)
    if check_ab:
        step_response_tmax = 70000
        N = int(step_response_tmax / Ts) + 1
        x = np.ones(N)
        step_response_t = np.arange(0, N, 1) * Ts
        y_ab = signal.lfilter(b, a, x)
        y_equation = (
            get_from_equation(step_response_t, poles_model_params_dic, Ts)
            / poles_model_params_dic["normalization_coefficient"]
        )
        print(f"Error max is {np.max(np.abs(y_ab-y_equation))}")
        if np.max(np.abs(y_ab - y_equation)) > 1e-5:
            print("Exist error when compute ab")
    return a, b


def sosfilter_design_using_ab(filepath=None):
    # 有一个数据集 time_data_sampling,y_equation_sampling。与此同时经过转换，我们也有b_full,a_full
    # data import
    data = np.genfromtxt(filepath + "_unit_step_response_after_sampling_for_fit.dat")
    time_data_sampling = data[:, 0]
    y_equation_sampling = data[:, 1]
    b_full = np.genfromtxt(filepath + "bfull.dat")
    a_full = np.genfromtxt(filepath + "afull.dat")
    # transform directly and verify accuracy
    zeros, poles, k = signal.tf2zpk(b_full, a_full)
    sos_system = signal.zpk2sos(zeros, poles, k)
    x = np.ones_like(time_data_sampling)
    error_max = np.max(np.abs(signal.sosfilt(sos_system, x) - y_equation_sampling))
    print(f"Error max before ZPK optimization is {error_max}")
    if error_max > 1e-5:
        print("Exist error when compute sos of system directly")
        #
        zeros, poles, k = sosfilter_design_by_ZPK_optimization(filepath)
        sos_system = signal.zpk2sos(zeros, poles, k)
        error_max = np.max(np.abs(signal.sosfilt(sos_system, x) - y_equation_sampling))
        print(f"Error max after ZPK optimization is {error_max}")
        if error_max > 1e-5:
            print("Failed to design sos filter after optimization")
    else:
        print(f"Do not need to do ZPK optimization")
    sos_digital_filter = signal.zpk2sos(poles, zeros, 1 / k)
    # return sos_digital_filter, sos_system, zeros, poles, k
    return sos_digital_filter, sos_system, error_max


def complex_list2_real_list(complex_list):
    """
    Complex numbers appear in pairs in the form of conjugate complex numbers
    :param complex_list:
    :return:
    """
    complex_list = np.complex128(complex_list)
    real_part = np.real(complex_list)
    imag_part = np.imag(complex_list)

    index_sort = np.argsort(np.abs(imag_part))  # 从小到大排序的元素的索引
    real_part_sort = real_part[index_sort]
    imag_part_sort = imag_part[index_sort]
    # the number of complex number
    complex_number = np.sum((np.abs(imag_part) > 1e-6))
    complex_pairs = int(np.round(complex_number / 2))
    real_list = copy.deepcopy(real_part_sort)

    for i in list(range(complex_pairs)):
        if (real_part_sort[-i * 2 - 1] == real_part_sort[-i * 2 - 2]) & (
            imag_part_sort[-i * 2 - 1] + imag_part_sort[-i * 2 - 2] == 0
        ):
            real_list[-i * 2 - 2] = real_part_sort[-i * 2 - 1]  # real
            real_list[-i * 2 - 1] = np.abs(imag_part_sort[-i * 2 - 1])  # imag
        else:
            raise ValueError(
                "Complex numbers don't appear in pairs in the form of conjugate complex numbers"
            )
    return real_list, complex_pairs


def real_list2complex_list(real_list, complex_pairs):
    complex_list = np.complex128(real_list)
    for i in list(range(complex_pairs)):
        real = real_list[-i * 2 - 2]
        imag = real_list[-i * 2 - 1]
        complex_list[-i * 2 - 1] = real + 1j * imag
        complex_list[-i * 2 - 2] = real - 1j * imag
    return complex_list


def sosfilter_design_by_ZPK_optimization(filepath=None):
    # data import
    data = np.genfromtxt(filepath + "_unit_step_response_after_sampling_for_fit.dat")
    time_data_sampling = data[:, 0]
    y_equation_sampling = data[:, 1]
    b_full = np.genfromtxt(filepath + "bfull.dat")
    a_full = np.genfromtxt(filepath + "afull.dat")
    zeros0, poles0, k0 = signal.tf2zpk(b_full, a_full)  # initial value
    zeros_num = len(zeros0)
    poles_num = len(poles0)
    zeros0_real_list, zeros_complex_pairs = complex_list2_real_list(zeros0)
    poles0_real_list, poles_complex_pairs = complex_list2_real_list(poles0)
    ## data fit
    start_point = np.hstack((zeros0_real_list, poles0_real_list, [k0]))
    lb = np.ones_like(start_point) * -1
    ub = np.ones_like(start_point) * 1
    if k0 > 0:
        lb[-1] = k0 * 0.5
        ub[-1] = k0 * 2
    else:
        lb[-1] = k0 * 2
        ub[-1] = k0 * 0.5

    amplification_factor = 1e4
    fit_p = curve_fit_step(
        time_data_sampling,
        y_equation_sampling * amplification_factor,
        start_point,
        step_response_fit_using_zpk(
            zeros_num,
            poles_num,
            zeros_complex_pairs,
            poles_complex_pairs,
            amplification_factor,
        ),
        10,
        bounds=(lb, ub),
    )
    zpk = fit_p[0]
    k = zpk[-1]
    zeros_real_list = zpk[0:zeros_num]
    poles_real_list = zpk[zeros_num : zeros_num + poles_num]
    zeros = real_list2complex_list(zeros_real_list, zeros_complex_pairs)
    poles = real_list2complex_list(poles_real_list, poles_complex_pairs)

    return zeros, poles, k


def step_response_fit_using_zpk(
    zeros_num, poles_num, zeros_complex_pairs, poles_complex_pairs, amplification_factor
):
    def fit_function(x, *zpk):
        k = zpk[-1]
        zeros_real_list = zpk[0:zeros_num]
        poles_real_list = zpk[zeros_num : zeros_num + poles_num]
        zeros = real_list2complex_list(zeros_real_list, zeros_complex_pairs)
        poles = real_list2complex_list(poles_real_list, poles_complex_pairs)
        sos = signal.zpk2sos(zeros, poles, k)
        y = signal.sosfilt(sos, np.ones_like(x)) * amplification_factor
        return y

    return fit_function


def generate_init_from_last_optimization(
    last_optization_result, poles_pair_last_time, poles_pair_this_time
):
    """
    generate initial poles model parameters when using poles_pair_this_time,according to the optimization result
    when using poles_pair_last_time
    :param last_optization_result: optimization result when using poles_pair_last_time
    :param poles_pair_this_time: [real_poles_num, complex_poles_num]
    :param poles_pair_last_time: [real_poles_num, complex_poles_num]
    :return: initial poles model params this time
    """
    # generate
    init, lb, ub = generate_poles_model_params_automatically(
        poles_pair_this_time[0], poles_pair_this_time[1]
    )
    # replace
    init[: 2 * poles_pair_last_time[0]] = last_optization_result[
        : 2 * poles_pair_last_time[0]
    ]
    init[
        2 * poles_pair_this_time[0] : 2 * poles_pair_this_time[0]
        + 4 * poles_pair_last_time[1]
    ] = last_optization_result[2 * poles_pair_last_time[0] :]
    return init, lb, ub


def tao2p(tao, Ts=0.625):
    p = np.exp(-Ts / tao, dtype=np.float64)
    return p


def tos2phip(tos, Ts=0.625):
    phip = 2 * np.pi * Ts / tos
    return phip


def compute_r(p, phip, phir):
    r = (1 + p**2 - 2 * p * np.cos(phip)) / (
        2 * np.cos(phir) - 2 * p * np.cos(phir - phip)
    )
    return r


def fullrun(
    start_time,
    real_poles_num,
    complex_poles_num,
    step_response_normalization,
    path,
    filename,
    temperature,
):
    poles_model_params = data_fit_using_poles_model(
        real_poles_num,
        complex_poles_num,
        step_response_normalization,
        filepath=path,
        filename=filename,
        start_time=start_time,
        temperature=temperature,
    )
    a, b = generate_ab_from_poles_model_params(
        poles_model_params[0],
        real_poles_num,
        complex_poles_num,
        step_response_normalization=step_response_normalization,
        check_ab=True,
    )
    # save ab
    datestr = time.strftime("%Y-%m-%d %H.%M.%S", time.localtime())
    np.savetxt(
        path
        + datestr
        + f"_ab_poles_model_fit_result_{real_poles_num}real_poles_{complex_poles_num}complex_poles_start_fit_at{start_time}ns.dat",
        np.column_stack((a, b)),
    )
    return poles_model_params[0], a, b


if __name__ == "__main__":
    path = r"E:\GLL\72bit_A2\code\room_temprocess\Z_distortion_correction\data\low_temperature\\"
    filename = "response.txt"

    step_response_normalization = True
    start_time = 10
    response_type = "rise"  # 'rise_and_fall'
    temperature = "low_temperature"  # "low_temperature", "room_temperature"

    real_poles_num = 2
    complex_poles_num = 1
    poles_model_params1 = data_fit_using_poles_model(
        real_poles_num,
        complex_poles_num,
        step_response_normalization,
        filepath=path,
        filename=filename,
        start_time=start_time,
        Ts=1 / 1.2,
        square_width=100e3,
        response_type=response_type,
        temperature=temperature,
    )

    # poles_model_params, a, b = fullrun(start_time, real_poles_num, complex_poles_num, step_response_normalization, path,
    #                                    filename, temperature=temperature)

    # poles_model_params = data_fit_using_poles_model(real_poles_num, complex_poles_num, step_response_normalization,
    #                                                 filepath=path, filename=filename, start_time=start_time)
    #
    # a, b = generate_ab_from_poles_model_params(poles_model_params,
    #                                            real_poles_num, complex_poles_num,
    #                                            step_response_normalization=step_response_normalization,
    #                                            check_ab=True, Ts=0.625)
