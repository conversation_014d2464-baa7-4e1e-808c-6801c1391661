# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:2
msgid "pyQCat.experiments.single.DistortionT1"
msgstr ""

#: of pyQCat.experiments.single.distortion_t1_once.DistortionT1:1
msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
msgstr ""

#: of pyQCat.experiments.single.distortion_t1_once.DistortionT1:1
msgid "Once Distortion Test."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.__init__:1
msgid "Create a new experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.single.DistortionT1.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`acquire_pulse "
"<pyQCat.experiments.single.DistortionT1.acquire_pulse>`\\ "
"\\(acq\\_pulse\\[\\, qubit\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Set measure pulse."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`cal_fidelity "
"<pyQCat.experiments.single.DistortionT1.cal_fidelity>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Calculate the fidelity matrix."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`experiment_info "
"<pyQCat.experiments.single.DistortionT1.experiment_info>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Show experiment informations."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.single.DistortionT1.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.single.DistortionT1.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`get_xy_pulse "
"<pyQCat.experiments.single.DistortionT1.get_xy_pulse>`\\ \\(qubit\\, "
"options\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`get_z_pulse "
"<pyQCat.experiments.single.DistortionT1.get_z_pulse>`\\ \\(qubit\\, "
"options\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
#: of pyQCat.experiments.single.distortion_t1_once.DistortionT1.get_z_pulse:1
msgid "Get Z pulse list"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`jupyter_schedule "
"<pyQCat.experiments.single.DistortionT1.jupyter_schedule>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Show experiment pulses's schedules in Jupyter Notebook."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.single.DistortionT1.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`play_pulse "
"<pyQCat.experiments.single.DistortionT1.play_pulse>`\\ \\(name\\, "
"base\\_qubit\\, pulse\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Play pulse for XY control and Z flux control."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`plot_schedule "
"<pyQCat.experiments.single.DistortionT1.plot_schedule>`\\ "
"\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
"Plot the pulse schedule which is very important to confirm experiment "
"logical correction."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.single.DistortionT1.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
#: of pyQCat.experiments.single.distortion_t1_once.DistortionT1.run:1
msgid "Run DistortionT1 Once experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.single.DistortionT1.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.single.DistortionT1.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_multiple_IF "
"<pyQCat.experiments.single.DistortionT1.set_multiple_IF>`\\ "
"\\(\\*IF\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Used for multiple qubits experiment, set mutiple IF."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_multiple_index "
"<pyQCat.experiments.single.DistortionT1.set_multiple_index>`\\ "
"\\(\\*args\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
"Used for multiple qubits experiment, set mutiple index. :type args: "
":py:class:`list` :param args: Setting the start and end values :type "
"channel: :py:class:`int` :param channel: channel settings Usage::     "
">>>experiment = TopExperiment()     >>>experiment.set_multiple_index([0, "
"1500], [100, 900], channel=1)."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.single.DistortionT1.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.single.DistortionT1.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_sweep_order "
"<pyQCat.experiments.single.DistortionT1.set_sweep_order>`\\ "
"\\(order\\_list\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid "Old interface."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:41:<autosummary>:1
msgid ""
":py:obj:`sweep_readout_trigger_delay "
"<pyQCat.experiments.single.DistortionT1.sweep_readout_trigger_delay>`\\ "
"\\(channel\\, sweep\\_delay\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.DistortionT1.rst:43
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.single.DistortionT1.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.single.DistortionT1.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.single.DistortionT1.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.single.DistortionT1.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:13
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:4
msgid ""
"z_amp (float): The const Zamp of Z line. gauss_sigma (float): The sigma "
"of GaussUP, GaussDown wave. gauss_width (float): The width of GaussUP, "
"GaussDown wave. const_width (float): The width of Constant wave. ta "
"(float): Set a width of Constant wave. tb (float): Set a width of "
"Constant wave. z_offset_list (List, np.ndarray): Scan Z offset range. "
"xy_delay (float): Set delay of XY pulse."
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_analysis_options
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_analysis_options:4
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_experiment_options:15
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._default_analysis_options:1
msgid "Default kwarg options for analysis."
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._set_xy_pulses:1
msgid "Set XY pulse."
msgstr ""

#: of pyQCat.experiments.single.distortion_t1_once.DistortionT1._set_z_pulses:1
msgid "Set z pulse."
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._check_options:1
msgid "如果没有IQ判据, 给出警告"
msgstr ""

#: of
#: pyQCat.experiments.single.distortion_t1_once.DistortionT1._check_options:2
msgid "如果是比特畸变, 分析P1, 如果是Coupler畸变, 分析P0"
msgstr ""

#: of pyQCat.experiments.single.distortion_t1_once.DistortionT1._metadata:1
msgid "Set metadata."
msgstr ""

#: of pyQCat.experiments.single.distortion_t1_once.DistortionT1._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.single.DistortionT1.__init__>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`acquire_pulse "
#~ "<pyQCat.experiments.single.DistortionT1.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.single.DistortionT1.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.single.DistortionT1.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_xy_pulse "
#~ "<pyQCat.experiments.single.DistortionT1.get_xy_pulse>`\\ "
#~ "\\(qubit\\, options\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_z_pulse "
#~ "<pyQCat.experiments.single.DistortionT1.get_z_pulse>`\\ "
#~ "\\(qubit\\, options\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`play_pulse "
#~ "<pyQCat.experiments.single.DistortionT1.play_pulse>`\\ "
#~ "\\(name\\, base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot_schedule "
#~ "<pyQCat.experiments.single.DistortionT1.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`run <pyQCat.experiments.single.DistortionT1.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.single.DistortionT1.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.single.DistortionT1.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_IF "
#~ "<pyQCat.experiments.single.DistortionT1.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_index "
#~ "<pyQCat.experiments.single.DistortionT1.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.single.DistortionT1.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.single.DistortionT1.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_sweep_order "
#~ "<pyQCat.experiments.single.DistortionT1.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":py:obj:`analysis <pyQCat.experiments.single.DistortionT1.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.single.DistortionT1.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.single.DistortionT1.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.single.DistortionT1.run_options>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.single.DistortionT1.__init__>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`acquire_pulse "
#~ "<pyQCat.experiments.single.DistortionT1.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_info "
#~ "<pyQCat.experiments.single.DistortionT1.experiment_info>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.single.DistortionT1.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.single.DistortionT1.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_xy_pulse "
#~ "<pyQCat.experiments.single.DistortionT1.get_xy_pulse>`\\ "
#~ "\\(qubit\\, options\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_z_pulse "
#~ "<pyQCat.experiments.single.DistortionT1.get_z_pulse>`\\ "
#~ "\\(qubit\\, options\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`jupyter_schedule "
#~ "<pyQCat.experiments.single.DistortionT1.jupyter_schedule>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.single.DistortionT1.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`play_pulse "
#~ "<pyQCat.experiments.single.DistortionT1.play_pulse>`\\ "
#~ "\\(name\\, base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot_schedule "
#~ "<pyQCat.experiments.single.DistortionT1.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`run <pyQCat.experiments.single.DistortionT1.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.single.DistortionT1.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.single.DistortionT1.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_IF "
#~ "<pyQCat.experiments.single.DistortionT1.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_index "
#~ "<pyQCat.experiments.single.DistortionT1.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.single.DistortionT1.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.single.DistortionT1.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_sweep_order "
#~ "<pyQCat.experiments.single.DistortionT1.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.single.DistortionT1.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.single.DistortionT1.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.single.DistortionT1.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.single.DistortionT1.run_options>`\\"
#~ msgstr ""

