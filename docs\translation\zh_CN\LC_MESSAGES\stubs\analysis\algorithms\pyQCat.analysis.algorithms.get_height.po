# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.get_height.rst:2
msgid "pyQCat.analysis.algorithms.get\\_height"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.get_height:1
msgid "Get specific value of y curve defined by a callback and its index."
msgstr "获取由回调及其索引定义的 y 曲线的特定值。"

#: of pyQCat.analysis.algorithms.guess.get_height
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.get_height:4
msgid "Array of y values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.get_height:6
msgid "A callback to find preferred y value."
msgstr "寻峰y值百分位的回调函数"

#: of pyQCat.analysis.algorithms.guess.get_height:8
msgid "Use absolute y value."
msgstr "是否对y使用绝对值"

#: of pyQCat.analysis.algorithms.guess.get_height
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.get_height:10
msgid ":py:data:`~typing.Tuple`\\[:py:class:`float`, :py:class:`int`]"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.get_height
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.get_height:11
msgid "The target y value and index."
msgstr "目标y值及其索引"

