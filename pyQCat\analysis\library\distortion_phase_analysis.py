# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/14
# __author:       ssfang
"""
APE Analysis.

"""

import numpy as np
from loguru import logger

from ...structures import Options
from ..oscillation_analysis import List, OscillationAnalysis, guess
from ..specification import CurveAnalysisData, FitOptions, ParameterRepr


class DistortionPhaseAnalysis(OscillationAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.result_parameters = [ParameterRepr(name="phase", repr="phase")]
        options.data_key = ["P0"]
        options.quality_bounds = [0.95, 0.9, 0.8]
        return options

    def _extract_result(self, data_key: str, quality=None):
        """Extract Xpi from fitted data.

        Args:
            data_key (str): The basis for selecting data.
            quality : Quality of fit outcome.
        """

        analysis_data = self.analysis_datas[data_key]
        x = analysis_data.x
        amp, freq, phase, _ = analysis_data.fit_data.popt

        if amp < 0:
            phase += 1 / freq / 2

        # bug solve: f(x) = A * cos(x + phase), if we want to find the max point,
        # the x must be equal -phase
        phase = -phase

        if data_key == "P0":
            phase += 1 / freq / 4

        while phase <= x[0]:
            phase = phase + 1 / freq

        while phase >= x[-1]:
            phase = phase - 1 / freq

        phase = phase % (2 * np.pi)
        self.results.phase.value = phase

        if self.options.is_plot is True:
            pos = (phase, 0.5)
            self.drawer.set_options(
                text_pos=[pos], text_rp=[f"TargetPhase\n{pos}"], text_key=[data_key]
            )

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ):
        x = data.x
        y = data.y
        try:
            init_phase = -data.x[np.argmax(data.y)]
            init_phase = init_phase % (2 * np.pi)
        except Exception:
            init_phase = np.pi
            logger.warning("Guess init phase error, use np.pi")

        freq = 1 / (2 * np.pi)
        amp = (np.max(y) - np.min(y)) / 2
        baseline = (np.max(y) + np.min(y)) / 2

        if not isinstance(freq, List):
            freq = [freq]

        fit_opt_list = []
        for f in freq:
            for p_amp in [amp]:
                fo = fit_opt.copy()
                fo.p0.set_if_empty(amp=amp, freq=f, phase=init_phase, base=baseline)
                fo.bounds.set_if_empty(
                    amp=(0, 1),
                    freq=(0.8 * f, 1.2 * f),
                    phase=(0, 2 * np.pi),
                    base=(0, 1),
                )
                fit_opt_list.append(fo)

        return fit_opt_list
