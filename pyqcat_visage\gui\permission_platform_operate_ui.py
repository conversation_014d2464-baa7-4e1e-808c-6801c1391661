# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'permission_platform_operate_ui.ui'
##
## Created by: Qt User Interface Compiler version 6.8.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QGridLayout, QGroupBox,
    QHBoxLayout, QLabel, QMainWindow, QPushButton,
    QSizePolicy, QSpacerItem, QStatusBar, QVBoxLayout,
    QWidget)

from .widgets.combox_custom.combox_search import SearchComboBox

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(927, 674)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.gridLayout = QGridLayout(self.centralwidget)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setContentsMargins(-1, 0, -1, 0)
        self.widget_main = QWidget(self.centralwidget)
        self.widget_main.setObjectName(u"widget_main")
        self.verticalLayout = QVBoxLayout(self.widget_main)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.widget = QWidget(self.widget_main)
        self.widget.setObjectName(u"widget")
        self.horizontalLayout_5 = QHBoxLayout(self.widget)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.widget_2 = QWidget(self.widget)
        self.widget_2.setObjectName(u"widget_2")
        self.verticalLayout_2 = QVBoxLayout(self.widget_2)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.targetName = QLabel(self.widget_2)
        self.targetName.setObjectName(u"targetName")

        self.horizontalLayout_2.addWidget(self.targetName)

        self.horizontalSpacer = QSpacerItem(268, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer)

        self.PlatformBox = SearchComboBox(self.widget_2)
        self.PlatformBox.setObjectName(u"PlatformBox")

        self.horizontalLayout_2.addWidget(self.PlatformBox)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_2)

        self.horizontalLayout_2.setStretch(0, 2)
        self.horizontalLayout_2.setStretch(1, 1)
        self.horizontalLayout_2.setStretch(2, 10)
        self.horizontalLayout_2.setStretch(3, 1)

        self.verticalLayout_2.addLayout(self.horizontalLayout_2)

        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.label = QLabel(self.widget_2)
        self.label.setObjectName(u"label")

        self.horizontalLayout_4.addWidget(self.label)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_4.addItem(self.horizontalSpacer_5)

        self.TypeBox = QComboBox(self.widget_2)
        self.TypeBox.setObjectName(u"TypeBox")

        self.horizontalLayout_4.addWidget(self.TypeBox)

        self.horizontalSpacer_6 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_4.addItem(self.horizontalSpacer_6)

        self.horizontalLayout_4.setStretch(0, 2)
        self.horizontalLayout_4.setStretch(1, 1)
        self.horizontalLayout_4.setStretch(2, 5)
        self.horizontalLayout_4.setStretch(3, 6)

        self.verticalLayout_2.addLayout(self.horizontalLayout_4)


        self.horizontalLayout_5.addWidget(self.widget_2)

        self.widget_4 = QWidget(self.widget)
        self.widget_4.setObjectName(u"widget_4")
        self.gridLayout_2 = QGridLayout(self.widget_4)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.queryButton = QPushButton(self.widget_4)
        self.queryButton.setObjectName(u"queryButton")

        self.gridLayout_2.addWidget(self.queryButton, 0, 0, 1, 1)


        self.horizontalLayout_5.addWidget(self.widget_4)

        self.horizontalLayout_5.setStretch(0, 5)
        self.horizontalLayout_5.setStretch(1, 1)

        self.verticalLayout.addWidget(self.widget)

        self.groupBox_2 = QGroupBox(self.widget_main)
        self.groupBox_2.setObjectName(u"groupBox_2")
        self.horizontalLayout_3 = QHBoxLayout(self.groupBox_2)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.ListBox1 = QWidget(self.groupBox_2)
        self.ListBox1.setObjectName(u"ListBox1")

        self.horizontalLayout_3.addWidget(self.ListBox1)

        self.ListBox2 = QWidget(self.groupBox_2)
        self.ListBox2.setObjectName(u"ListBox2")

        self.horizontalLayout_3.addWidget(self.ListBox2)


        self.verticalLayout.addWidget(self.groupBox_2)

        self.widget_3 = QWidget(self.widget_main)
        self.widget_3.setObjectName(u"widget_3")
        self.horizontalLayout = QHBoxLayout(self.widget_3)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalSpacer_3 = QSpacerItem(314, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_3)

        self.saveButton = QPushButton(self.widget_3)
        self.saveButton.setObjectName(u"saveButton")

        self.horizontalLayout.addWidget(self.saveButton)

        self.horizontalSpacer_4 = QSpacerItem(314, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_4)

        self.ResetButton = QPushButton(self.widget_3)
        self.ResetButton.setObjectName(u"ResetButton")

        self.horizontalLayout.addWidget(self.ResetButton)

        self.horizontalLayout.setStretch(0, 3)
        self.horizontalLayout.setStretch(1, 1)
        self.horizontalLayout.setStretch(2, 2)

        self.verticalLayout.addWidget(self.widget_3)

        self.verticalLayout.setStretch(0, 1)
        self.verticalLayout.setStretch(1, 5)
        self.verticalLayout.setStretch(2, 1)

        self.gridLayout.addWidget(self.widget_main, 0, 0, 1, 1)

        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)
        self.queryButton.clicked.connect(MainWindow.query_info)
        self.saveButton.clicked.connect(MainWindow.save_info)
        self.ResetButton.clicked.connect(MainWindow.reset)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"Platform Permission", None))
        self.targetName.setText(QCoreApplication.translate("MainWindow", u"platform", None))
        self.label.setText(QCoreApplication.translate("MainWindow", u"type", None))
        self.queryButton.setText(QCoreApplication.translate("MainWindow", u"query", None))
        self.groupBox_2.setTitle(QCoreApplication.translate("MainWindow", u"Platfrom Permission Manage", None))
        self.saveButton.setText(QCoreApplication.translate("MainWindow", u"Save", None))
        self.ResetButton.setText(QCoreApplication.translate("MainWindow", u"reset", None))
    # retranslateUi

