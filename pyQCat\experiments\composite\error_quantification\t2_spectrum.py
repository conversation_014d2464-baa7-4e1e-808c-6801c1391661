# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/14
# __author:       <PERSON> <PERSON>

"""
T2 Spectrum composite experiment.

"""

from copy import deepcopy

import numpy as np

from ....analysis.library import T2SpectrumAnalysis
from ....log import pyqlog
from ....parameters import options_wrapper
from ....structures import MetaData, Options
from ....tools import qarange
from ....types import ExperimentRunMode, QualityDescribe
from ...composite_experiment import CompositeExperiment
from .t2 import T2<PERSON><PERSON>ey, T2SpinEcho


class T2SpectrumBase(CompositeExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            delays (List, np.ndarray): T1 experiment scan delay.
            fringe (float): A frequency shift in Hz that will be applied
                            by means of a virtual Z rotation to increase
                            the frequency of the measured oscillation.
            rate_threshold (Tuple): Set threshold of rate, optimize qubit_test T2.
            max_loops (int): T2Ramsey optimize qubit_test T2 times.

            freq_list (List, np.ndarray): Scan bit frequency list.
            freq_range_map (dict): Calculate freq_list l_gap, r_gap, step value.

            z_amp_list (List, np.ndarray): Scan Z line amp list.
            ac_spectrum_paras (List): The target qubit ac spectrum
                                      fit parameters, default None.
            pattern_flag (bool): Select run t2 spectrum model,
                                 False means like T1 Spectrum,
                                 True means precise qubit_test T2.

        """
        options = super()._default_experiment_options()

        options.set_validator("delays", list)
        options.set_validator("fringe", float)
        options.set_validator("rate_down", (0, 1, 2))
        options.set_validator("rate_up", (0, 1, 2))
        options.set_validator("max_loops", [1, 2, 3, 4, 5])
        # options.set_validator('freq_list', list)
        options.set_validator("freq_range_map", dict)
        options.set_validator("z_amp_list", list)
        # options.set_validator('ac_spectrum_paras', list)
        options.set_validator("pattern_flag", bool)
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        # T2Ramsey experiment options
        options.delays = qarange(0, 30000, 300)
        options.fringe = -0.5
        options.rate_down = 0.3
        options.rate_up = 0.5
        options.max_loops = 3

        # either freq_list or freq_range_map
        # options.freq_list = None
        options.freq_range_map = {
            "l": 200,
            "r": 20,
            "s": 10,
        }

        # directly provided z_amp_list
        options.z_amp_list = []

        # if not provided z_amp_list, ac_spectrum_paras must be given
        # options.ac_spectrum_paras = None

        # experiment model, brainless operation or precise qubit_test T2
        options.pattern_flag = False

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for T2Spectrum experiment.

        Options:
            freq_list (List, np.ndarray): The frequency calculate
                by ac spectrum paras and z amp.
                If no ac spectrum paras, this is None.
        """
        options = super()._default_analysis_options()

        options.set_validator("r_square_threshold", (0, 1, 2))
        options.set_validator("rate_threshold", (0, 1, 2))
        options.set_validator("ymax", float)

        options.freq_list = None
        options.r_square_threshold = 0.7
        options.rate_threshold = 0.38
        options.ymax = 40

        options.data_key = ["tau"]
        options.figsize = (18, 8)

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method.

        Options:
            z_amp_list (List): List of z_amp, run T2Ramsey get target value.
            tau_list (List): List of t2 value, scan z_amp_list run T2Ramsey.
            freq_list (array): The frequency calculate
                by ac spectrum paras and z amp.
                If no ac spectrum paras, this is None.
                The run_options.freq_list not necessarily with
                experiment_options.freq_list, it is possible to calculate
                z_amp through frequency and ac spectrum paras,
                if the z_amp is nan, that will remove the frequency.
        """
        options = super()._default_run_options()
        options.tau_list = []
        options.r_square_list = []
        options.rate_list = []
        options.freq_list = []

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()

        return metadata

    async def _sync_composite_run(self):
        """Run T2 Spectrum Composite Experiment."""
        # super().run()

        provide_field = self.analysis_options.data_key[0]
        pattern_flag = self.experiment_options.pattern_flag

        fringe = self.experiment_options.fringe
        rate_down = self.experiment_options.rate_down
        rate_up = self.experiment_options.rate_up
        max_loops = self.experiment_options.max_loops

        if pattern_flag is False:
            max_loops = 1
            pyqlog.log(
                "EXP",
                f"pattern_flag: {pattern_flag} not optimize T2, "
                f"so change max_loops {max_loops}",
            )

        qubit = self.child_experiment.qubits[0]
        q_name = qubit.name

        tau_list = []
        rate_list = []
        r_square_list = []
        best_max_delay = None
        freq_list = self.experiment_options.freq_list
        for i, z_amp in enumerate(self.experiment_options.z_amp_list):
            t2_exp = deepcopy(self.child_experiment)
            title = f"z_amp={np.round(z_amp, 5)}"
            if freq_list:
                title += f" freq={np.round(freq_list[i])}"
                freq = freq_list[i]
                t2_exp.run_options.frequency = freq

            t2_exp.set_parent_file(
                self, title, i, len(self.experiment_options.z_amp_list)
            )
            pyqlog.log("EXP", title)

            if best_max_delay is None:
                delays = self.experiment_options.delays
            else:
                fringe, delays = t2_exp.guess_delays(0, best_max_delay)

            t2_exp.set_experiment_options(
                delays=delays,
                fringe=fringe,
                z_amp=z_amp,
                rate_down=rate_down,
                rate_up=rate_up,
                max_loops=max_loops,
            )
            self._check_simulator_data(t2_exp, i)
            await t2_exp.run_experiment()
            new_best_max_delay = t2_exp.run_options.best_max_delay
            if pattern_flag:
                best_max_delay = new_best_max_delay

            tau = t2_exp.analysis.results.tau.value
            rate = t2_exp.analysis.results.t2_rate.value
            r_square = t2_exp.analysis.quality.value
            quality_str = t2_exp.analysis.quality.descriptor

            if self.experiment_options.pattern_flag is True:
                if quality_str in [
                    QualityDescribe.bad,
                    QualityDescribe.abnormal,
                ] or not (rate_down <= rate <= rate_up):
                    pyqlog.warning(
                        f"z_amp: {z_amp}, T2Ramsey quality: {quality_str}, rate: {rate}"
                    )
                    # continue

            tau_list.append(tau)
            rate_list.append(rate)
            r_square_list.append(r_square)

            t2_exp.analysis.provide_for_parent.update(
                {provide_field: [tau, rate, r_square]}
            )
            self._experiments.append(t2_exp)

        self.set_run_options(
            tau_list=tau_list,
            rate_list=rate_list,
            r_square_list=r_square_list,
            freq_list=freq_list,
        )

        z_amp_arr = np.array(self.experiment_options.z_amp_list)
        save_file = f"{q_name}_t2_spectrum_run_options"
        if self.run_options.freq_list:
            self.file.save_data(
                z_amp_arr,
                np.array(self.run_options.freq_list),
                np.array(self.run_options.tau_list),
                np.array(self.run_options.rate_list),
                np.array(self.run_options.r_square_list),
                name=save_file,
            )
            self.set_analysis_options(freq_list=np.array(self.run_options.freq_list))
        else:
            self.file.save_data(
                z_amp_arr,
                np.array(self.run_options.tau_list),
                np.array(self.run_options.rate_list),
                np.array(self.run_options.r_square_list),
                name=save_file,
            )

        self._run_analysis(x_data=z_amp_arr, analysis_class=T2SpectrumAnalysis)


@options_wrapper
class T2Spectrum(T2SpectrumBase):
    """T2 Spectrum Experiment base ramsey."""

    _sub_experiment_class = T2Ramsey


@options_wrapper
class T2SpinEchoSpectrum(T2SpectrumBase):
    """T2 Spectrum Experiment base spin echo."""

    _sub_experiment_class = T2SpinEcho
