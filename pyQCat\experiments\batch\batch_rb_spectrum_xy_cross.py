# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/22
# __author:       <PERSON> Fang

"""
BatchRBSpectrum extend XYCrossRabiWidth flows.
"""

import os
import re
import json
import traceback
from copy import deepcopy
from pathlib import Path
from typing import List, Dict, Optional

import numpy as np

from ...log import logger
from ...qubit import Qubit
from ...structures import Options, MetaData, ExperimentData
from ...tools.utilities import qarange
from ...tools.savefile import create_file
from ...analysis.library.xy_cross_rw_analysis import XYCrossRwAnalysis
from ...concurrent.worker.analysis_interface import run_analysis_process
from ..base_experiment import BaseExperiment
from .batch_rb_spectrum import BatchRBSpectrum
from .batch_xy_cross_rabi_width import NoteParams


class BatchRBSpectrumXYCross(BatchRBSpectrum):
    """Batch RBSpectrum extend, after one point execute xy_cross flows."""

    @classmethod
    def _default_experiment_options(cls):
        """Define some default experiment options."""
        options = super()._default_experiment_options()

        options.run_xy_cross = True
        options.target_bias_map = {}

        options.cali_freq_flag = True
        options.update_target_strength = True
        options.select_coe_threshold = 0.001

        options.bias_widths_1 = qarange(5, 10000, 250)
        options.bias_widths_2 = qarange(5, 2000, 25)
        options.bias_expect_1 = [0.1, 1.0]
        options.bias_expect_2 = [0.5, 10]

        options.cali_freq_flows = ["QubitFreqCalibration"]
        options.strength_flows = ["RabiScanWidth"]
        options.xy_cross_flows = ["XYCrossRabiWidthOnce"]

        return options

    @classmethod
    def _default_run_options(cls):
        """Define some default run options."""
        options = super()._default_run_options()

        options.xy_cross_index_path = ""
        options.t_names = []
        options.target_bias_map = {}
        options.cross_coe_map = {}
        options.cross_trust_map = {}

        return options

    def _config_file_path(self, unit: str, exp: Optional["BaseExperiment"]):
        """Configure the parent directory for each experiment executed in the BatchExperiment.

        Args:
            unit (str): Physical working unit.
            exp (BaseExperiment) :
        """
        if self.experiment_options.unified_dir is True and exp:
            dir_describe_map = self.run_options.dir_describe or {}
            exp.run_options.collect_qubits = False

            q_pattern = re.compile(r"(q\d+)")
            res_list = q_pattern.findall(unit)
            if len(res_list) > 1:
                q_name = res_list[0]
            else:
                q_name = unit
            exp.dir_describe = str(
                Path(self.run_options.dir_prefix, q_name, dir_describe_map.get(unit, ""))
            )

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        dir_path = Path(self.run_options.record_path).parent
        dir_describe_map = self.run_options.dir_describe or {}

        if "RabiScanWidth" in exp_name:
            if isinstance(exp, BaseExperiment):
                exps = [exp]
            else:
                exps = exp.experiments

            for idx, exp_obj in enumerate(exps):
                t_name = physical_units[idx]
                cur_point = self.run_options.current_point_map[t_name]
                point_sate = self.run_options.rb_records.get(t_name).get(cur_point)
                save_path = str(Path(dir_path, t_name, dir_describe_map.get(t_name, "")))
                t_note_obj = NoteParams(**point_sate.xy_cross_note)

                try:
                    analysis_obj = exp_obj.analysis
                    trust = analysis_obj.results.oscillating.value
                    osci_freq = analysis_obj.results.freq.value
                    error = ""
                except Exception as err:
                    logger.warning(f"{t_name} {exp_name} error: {err}")
                    trust = False
                    osci_freq = t_note_obj.osci_freq
                    error = str(err)

                t_note_obj.drive_freq = exp_obj.experiment_options.drive_freq
                t_note_obj.drive_power = exp_obj.experiment_options.drive_power
                t_note_obj.xy_wave = exp_obj.qubits[0].XYwave
                t_note_obj.trust = trust
                t_note_obj.osci_freq = osci_freq
                t_note_obj.current = "After Strength RabiWidth"
                t_note_obj.error = error

                t_note_dict = t_note_obj.to_dict()
                point_sate.xy_cross_note = t_note_dict
                self._save_data(save_path, t_note_dict, "note_params")

        elif "XYCrossRabiWidthOnce" in exp_name:
            if isinstance(exp, BaseExperiment):
                exps = [exp]
            else:
                exps = exp.experiments

            for idx, exp_obj in enumerate(exps):
                if self.run_options.config is None:
                    self.run_options.config = exp_obj.run_options.config

                t_name = exp_obj.experiment_options.rd_name
                b_name = exp_obj.experiment_options.xy_name
                cur_point = self.run_options.current_point_map[t_name]
                point_sate = self.run_options.rb_records.get(t_name).get(cur_point)
                save_path = str(Path(dir_path, t_name, dir_describe_map.get(t_name, "")))
                t_note_obj = NoteParams(**point_sate.xy_cross_note)

                try:
                    analysis_obj = exp_obj.analysis
                    trust = analysis_obj.results.oscillating.value
                    osci_freq = analysis_obj.results.freq.value
                    error = ""
                except Exception as err:
                    logger.warning(
                        f"target {t_name} bias {b_name} {exp_name} error: {err}"
                    )
                    trust = False
                    osci_freq = 0.0
                    error = str(err)
                one_bias_dict = {
                    "drive_freq": exp_obj.experiment_options.drive_freq,
                    "drive_power": exp_obj.experiment_options.drive_power,
                    "xy_wave": exp_obj.run_options.xy_qubit.XYwave.__dict__,
                    "trust": trust,
                    "osci_freq": osci_freq,
                    "error": error,
                }
                t_note_obj.current = (
                    f"After target {t_name} bias {b_name} XYCrossRabiWidthOnce"
                )
                t_note_obj.bias_cross_dict.update({b_name: one_bias_dict})

                t_note_dict = t_note_obj.to_dict()
                point_sate.xy_cross_note = t_note_dict
                self._save_data(save_path, t_note_dict, "note_params")

        return record

    @staticmethod
    def _save_data(save_path: str, data: Dict, name: str):
        """Save data to json file."""
        if not name.endswith("json"):
            name += ".json"
        save_file = str(Path(save_path, name))
        with open(save_file, mode="w", encoding="utf-8") as fp:
            json.dump(data, fp, indent=4, ensure_ascii=False)

    def _cali_target_freq(self):
        """Calibrate target bit frequency."""
        cali_freq_flag = self.experiment_options.cali_freq_flag
        cali_freq_flows = self.experiment_options.cali_freq_flows
        t_names = self.run_options.t_names
        dir_path = Path(self.run_options.record_path).parent
        dir_describe_map = self.run_options.dir_describe or {}

        if cali_freq_flag is True:
            self._run_flow(cali_freq_flows, t_names)

        # Note target bit some base parameters.
        chip_data = self.context_manager.chip_data
        cache_qubit = chip_data.cache_qubit

        for t_name in t_names:
            cur_point = self.run_options.current_point_map[t_name]
            point_sate = self.run_options.rb_records.get(t_name).get(cur_point)
            save_path = str(Path(dir_path, t_name, dir_describe_map.get(t_name, "")))

            t_qubit: "Qubit" = cache_qubit.get(t_name)
            t_note_obj = NoteParams(
                drive_freq=t_qubit.drive_freq,
                drive_power=t_qubit.drive_power,
                xy_wave=t_qubit.XYwave,
                osci_freq=round(1000 / (t_qubit.XYwave.time * 2), 3),
                current="Before Strength RabiWidth",
            )
            t_note_dict = t_note_obj.to_dict()
            point_sate.xy_cross_note = t_note_dict
            self._save_data(save_path, t_note_dict, "note_params")

    def _strength_target(self):
        """Strength target bit RabiWidth."""
        update_target_strength = self.experiment_options.update_target_strength
        strength_flows = self.experiment_options.strength_flows
        t_names = self.run_options.t_names

        if update_target_strength is True:
            self._run_flow(strength_flows, t_names)

    def _xy_cross_bias(self):
        """Bias XYCrossRabiWidthOnce."""
        xy_cross_flows = self.experiment_options.xy_cross_flows
        target_bias_map: dict = self.run_options.target_bias_map

        bias_widths_list = [
            self.experiment_options.bias_widths_1,
            self.experiment_options.bias_widths_2,
        ]
        bias_expect_list = [
            self.experiment_options.bias_expect_1,
            self.experiment_options.bias_expect_2,
        ]

        exp_name = xy_cross_flows[0] if xy_cross_flows else "XYCrossRabiWidthOnce"
        for t_name, bias_names in target_bias_map.items():
            logger.info(f"target: {t_name}, bias_names: {bias_names}")
            cur_point = self.run_options.current_point_map[t_name]
            point_sate = self.run_options.rb_records.get(t_name).get(cur_point)
            t_note_obj = NoteParams(**point_sate.xy_cross_note)

            for b_name in bias_names:
                if b_name == t_name:
                    continue
                unit_str = f"{t_name}{b_name}"
                logger.info(
                    f"target {t_name} bias {b_name} execute {exp_name} start ..."
                )
                for widths, expect_range in zip(bias_widths_list, bias_expect_list):
                    self.change_regular_exec_exp_options(
                        exp_name=exp_name,
                        widths=widths,
                        drive_power=t_note_obj.drive_power,
                        drive_freq=t_note_obj.drive_freq,
                        xy_name=b_name,
                        rd_name=t_name,
                        direct_execute=True,
                    )
                    self._run_flow([exp_name], [unit_str])

                    # judge is not execute next
                    t_min, t_max = min(expect_range), max(expect_range)
                    one_bias_dict: dict = t_note_obj.bias_cross_dict.get(b_name, {})
                    osci_freq = one_bias_dict.get("osci_freq", 0.0)

                    if osci_freq < t_min:
                        one_bias_dict.update({"osci_freq": t_min})
                    elif t_min <= osci_freq <= t_max:
                        break
                    elif osci_freq > t_max:
                        one_bias_dict.update({"osci_freq": t_max})

                logger.info(f"target {t_name} bias {b_name} execute {exp_name} end.")

    def _special_save_xy_cross_data(self):
        """Save some data."""
        select_coe_threshold = self.experiment_options.select_coe_threshold
        cross_coe_dict: dict = self.run_options.cross_coe_map
        cross_trust_dict: dict = self.run_options.cross_trust_map
        t_names = self.run_options.t_names

        dir_path = Path(self.run_options.record_path).parent
        dir_describe_map = self.run_options.dir_describe or {}

        cross_coe_dict.clear()
        cross_trust_dict.clear()
        note_params_dict = {}
        select_cross_coe_dict = {}

        for t_name in t_names:
            cur_point = self.run_options.current_point_map[t_name]
            point_sate = self.run_options.rb_records.get(t_name).get(cur_point)
            save_path = str(Path(dir_path, t_name, dir_describe_map.get(t_name, "")))
            t_note_obj = NoteParams(**point_sate.xy_cross_note)

            coe_dict = {}
            trust_dict = {}
            t_osci_freq = t_note_obj.osci_freq
            for b_name, one_bias_dict in t_note_obj.bias_cross_dict.items():
                b_osci_freq = one_bias_dict.get("osci_freq", 0.0)
                trust = one_bias_dict.get("trust", False)
                coefficient = round(b_osci_freq / t_osci_freq, 4)
                coe_dict[b_name] = coefficient
                trust_dict[b_name] = trust

            t_dict = {
                f"{t_name}-coefficient": coe_dict,
                f"{t_name}-trust": trust_dict,
            }
            self._save_data(save_path, t_dict, "coefficient")

            note_params_dict[t_name] = t_note_obj.to_dict()
            cross_coe_dict[t_name] = coe_dict
            cross_trust_dict[t_name] = trust_dict

        # Save cross coefficient when large than threshold and the oscillation is true.
        for t_name, coe_dict in cross_coe_dict.items():
            new_coe_dict = {}
            trust_dict = cross_trust_dict.get(t_name, {})
            for b_name, coe_val in coe_dict.items():
                trust_flag = trust_dict.get(b_name, False)
                if trust_flag is True and coe_val > select_coe_threshold:
                    new_coe_dict[b_name] = coe_val
            if new_coe_dict:
                sort_coe_dict = dict(
                    sorted(new_coe_dict.items(), key=lambda x: x[1], reverse=True)
                )
                select_cross_coe_dict[t_name] = sort_coe_dict

                save_path = str(
                    Path(dir_path, t_name, dir_describe_map.get(t_name, ""))
                )
                st_dict = {f"{t_name}-coefficient": sort_coe_dict}
                self._save_data(save_path, st_dict, "select_coefficient")

        # Save data to `xy_cross_index_path`
        idx_save_path = self.run_options.xy_cross_index_path
        self._save_data(idx_save_path, note_params_dict, "note_params")
        self._save_data(idx_save_path, cross_coe_dict, "cross_coefficient")
        self._save_data(idx_save_path, cross_trust_dict, "cross_trust")
        self._save_data(
            idx_save_path, select_cross_coe_dict, "select_cross_coefficient"
        )

    def _once_xy_cross_analysis(
        self,
        t_names: List[str],
        cross_coe_map: Dict,
        cross_trust_map: Dict,
        save_path: str,
    ):
        """Once run XY cross analysis."""
        all_bias_names = []
        for t_name, coe_dict in cross_coe_map.items():
            for b_name in coe_dict.keys():
                if b_name not in all_bias_names:
                    all_bias_names.append(b_name)

        all_bias_names.sort(key=lambda x: int(x[1:]))
        cross_coe_dict = {}
        cross_trust_dict = {}
        default_coe_dict = {b_name: np.nan for b_name in all_bias_names}
        default_trust_dict = {b_name: False for b_name in all_bias_names}
        for t_name in t_names:
            coe_dict = deepcopy(default_coe_dict)
            trust_dict = deepcopy(default_trust_dict)
            coe_dict.update(cross_coe_map.get(t_name, {}))
            trust_dict.update(cross_trust_map.get(t_name, {}))
            cross_coe_dict[t_name] = coe_dict
            cross_trust_dict[t_name] = trust_dict

        base_len = 10
        q_length = len(all_bias_names)
        if q_length > base_len:
            ratio = q_length / base_len
            figsize = [int(12 * ratio), int(8 * ratio)]
        else:
            figsize = [12, 8]
        logger.info(f"Set analysis figsize: {figsize}")

        exp_file_obj = create_file(self.run_options.config, self._label, "")
        exp_file_obj._dirs = save_path
        exp_data = ExperimentData(
            x_data=np.array([]),
            y_data={},
            experiment_id=self.id,
            metadata=MetaData(name="XYCrossFlows", save_location=save_path),
        )
        analysis_options = Options(
            is_plot=True,
            figsize=figsize,
            pure_exp_mode=False,
            cross_coe_map=cross_coe_dict,
            cross_trust_map=cross_trust_dict,
            n_multiple=100,
        )

        run_analysis_process(
            XYCrossRwAnalysis, exp_data, analysis_options, exp_file_obj
        )

    def _special_xy_cross_analysis(self):
        """Analysis logic."""
        t_names = self.run_options.t_names
        cross_coe_map: dict = self.run_options.cross_coe_map
        cross_trust_map: dict = self.run_options.cross_trust_map

        dir_path = Path(self.run_options.record_path).parent
        dir_describe_map = self.run_options.dir_describe or {}
        idx_save_path = self.run_options.xy_cross_index_path

        self._once_xy_cross_analysis(
            t_names, cross_coe_map, cross_trust_map, idx_save_path
        )

        for t_name in t_names:
            save_path = str(Path(dir_path, t_name, dir_describe_map.get(t_name, "")))
            coe_dict = cross_coe_map.get(t_name, {})
            trust_dict = cross_trust_map.get(t_name, {})
            if coe_dict and trust_dict:
                self._once_xy_cross_analysis(
                    [t_name], {t_name: coe_dict}, {t_name: trust_dict}, save_path
                )

    def _xy_cross_flows(self, gdx:int, sdx: int, target_names: List[str]):
        """Run xy_cross flows logic."""
        target_bias_dict: dict = self.experiment_options.target_bias_map
        target_bias_map: dict = self.run_options.target_bias_map
        t_names: list = self.run_options.t_names
        dir_path = Path(self.run_options.record_path).parent
        target_bias_map.clear()
        t_names.clear()
        mark = f"Group index {gdx} Sweep index {sdx}"

        # Check target & bias_names
        for t_name in target_names:
            his_describe = self.run_options.dir_describe[t_name]
            dir_describe = f"{his_describe}{os.sep}XYCrossFlows"
            t_path = str(Path(dir_path, t_name, dir_describe))
            if not os.path.exists(t_path):
                os.makedirs(t_path, exist_ok=True)

            self.run_options.dir_describe[t_name] = dir_describe

            t_names.append(t_name)
            bias_names = target_bias_dict.get(t_name, [])
            if bias_names:
                target_bias_map[t_name] = bias_names
                for b_name in bias_names:
                    unit_str = f"{t_name}{b_name}"
                    self.run_options.dir_describe[unit_str] = dir_describe
            else:
                logger.warning(f"{mark} get target {t_name} bias names: {bias_names}")

        logger.info(f"{mark} target_names: {target_names} execute xy_cross_flows.")
        if t_names and target_bias_map:
            idx_save_path = str(Path(dir_path, "xy_cross_result", f"group{gdx:02}", f"sweep{sdx:02}"))
            os.makedirs(idx_save_path, exist_ok=True)
            logger.info(f"{mark} xy_cross save_path: {idx_save_path}")

            self.run_options.xy_cross_index_path = idx_save_path

            # 1. 校准 target 比特频率
            self._cali_target_freq()

            # 2. target 比特 RabiWidth
            self._strength_target()

            # 3. bias 比特 XYCrossRabiWidthOnce
            self._xy_cross_bias()

            # 4. 保存 XY 串扰相关数据
            self._special_save_xy_cross_data()

            # 5. 进行 XYCrossRwAnalysis 绘图
            self._special_xy_cross_analysis()
        else:
            logger.warning(
                f"{mark} according target_names: {target_names}, "
                f"select target_bias_map: {target_bias_map}"
            )

    def _run_batch(self):
        group_list = self.run_options.group_list
        for gdx, group in enumerate(group_list):
            sweep_count = self._count_max_sweep_count(group)

            for i in range(sweep_count):
                # change working point
                working_units = self._change_work_point(i, group)

                # run exp flow
                pass_units = self._run_flow(
                    physical_units=working_units, flows=self.experiment_options.flows
                )

                # execute xy_cross flows or not.
                if self.experiment_options.run_xy_cross is True and pass_units:
                    try:
                        self._xy_cross_flows(gdx, i, pass_units)
                    except Exception as err:
                        logger.warning(
                            f"Group index {gdx} Sweep index {i}, pass names: {pass_units} "
                            f"execute xy_flows error: {err} \n {traceback.format_exc()}"
                        )

                # record working point state
                self._record_point_state()

        self._run_analysis()

        # self.run_options.batch_fail_units = self._check_need_retry_units()

        if self.experiment_options.plot_distribution_map is True:
            self._plot_distribution()
