﻿pyQCat.experiments.single.UnionReadout
======================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: UnionReadout

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~UnionReadout.__init__
      ~UnionReadout.acquire_pulse
      ~UnionReadout.cal_fidelity
      ~UnionReadout.experiment_info
      ~UnionReadout.from_experiment_context
      ~UnionReadout.get_qubit_str
      ~UnionReadout.jupyter_schedule
      ~UnionReadout.options_table
      ~UnionReadout.play_pulse
      ~UnionReadout.plot_schedule
      ~UnionReadout.run
      ~UnionReadout.save_bin_file
      ~UnionReadout.set_analysis_options
      ~UnionReadout.set_experiment_options
      ~UnionReadout.set_multiple_IF
      ~UnionReadout.set_multiple_index
      ~UnionReadout.set_parent_file
      ~UnionReadout.set_run_options
      ~UnionReadout.set_sweep_order
      ~UnionReadout.stimulate_state_one
      ~UnionReadout.stimulate_state_zero
      ~UnionReadout.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~UnionReadout.analysis
      ~UnionReadout.analysis_options
      ~UnionReadout.experiment_options
      ~UnionReadout.run_options
   
   