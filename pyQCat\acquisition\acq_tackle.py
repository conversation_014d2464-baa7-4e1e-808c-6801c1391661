# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/30
# __author:       <PERSON><PERSON><PERSON>

import csv
import itertools
import os
from collections import Counter, defaultdict
from dataclasses import dataclass, field
from enum import Enum
from functools import reduce
from operator import mul
from pathlib import Path
from typing import Dict, List, Union

import numpy as np
from prettytable import PrettyTable

from ..analysis.algorithms import IQdiscriminator, correct_fidelity
from ..analysis.visualization.scatter_drawer import iq_scatter_drawer
from ..log import pyqlog
from ..tools import time_cal


class UnionMode(str, Enum):
    U = "union"
    NU = "not union"
    AU = "all union"


@dataclass
class QubitMeasureMode:
    name: str
    measure_total: int
    measure_num_list: list = field(default_factory=list)
    base_label_list: list = field(default_factory=list)

    __annotations__ = {
        "name": str,
        "measure_total": int,
        "measure_num_list": list,
        "base_label_list": list,
    }

    def divide(self):
        if len(self.measure_num_list) != len(self.base_label_list):
            raise ValueError("measure_num 和 base_label 长度不一致！")

        result = []
        for index, num in enumerate(self.measure_num_list):
            once = OnceQubitMeasureMode(
                name=self.name,
                measure_total=self.measure_total,
                measure_num=num,
                base_label=self.base_label_list[index],
            )
            once.validator()
            result.append(once)

        return result


@dataclass
class OnceQubitMeasureMode:
    name: str
    measure_total: int
    measure_num: int
    base_label: str = ""
    remove_index: list = field(default_factory=list)
    goal_label: list = field(default_factory=list)

    __annotations__ = {
        "name": str,
        "measure_total": int,
        "measure_num": int,
        "base_label": str,
        "remove_index": list,
        "goal_label": list,
    }

    def __repr__(self):
        return f"{self.name}-M({self.measure_num})-Base({self.base_label})"

    def validator(self):
        if self.measure_num == -1:
            self.measure_num = self.measure_total - 1

        self.remove_index = [self.measure_num]
        self.goal_label = []

        for idx, label in enumerate(self.base_label):
            if label == "x":
                self.remove_index.append(idx if idx < self.measure_num else idx + 1)
            else:
                self.goal_label.append(int(label))

    @property
    def need_filter(self):
        return self.measure_total > 1 and self.base_label


class MultipleMeasureWrapper:
    def __init__(
        self,
        repeat: int,
        dcms: Union[IQdiscriminator, List[IQdiscriminator]],
        measure_modes: Union[QubitMeasureMode, List[QubitMeasureMode]],
        union_mode: UnionMode = UnionMode.U,
        save_path: str = "",
        label: str = "",
    ):
        self._repeat = repeat
        self._union_mode = union_mode
        self._save_path = save_path
        self._label = label
        self._filter_log = Path(save_path) / "filter.txt" 

        if not isinstance(dcms, list):
            dcms = [dcms]

        if not isinstance(measure_modes, list):
            measure_modes = [measure_modes]

        if len(dcms) != len(measure_modes):
            raise ValueError("判据个数和处理类型个数不匹配")

        tackle_type_map: Dict[str, List[OnceQubitMeasureMode]] = {}

        for tt in measure_modes:
            tackle_type_map[tt.name] = tt.divide()

        for dcm in dcms:
            if dcm.name not in tackle_type_map:
                raise ValueError("无法找到此判据的结果处理类型")

        self._dcm_list = dcms
        self._measure_mode_map: Dict[str, List[OnceQubitMeasureMode]] = tackle_type_map

        self._data_i = None
        self._data_q = None
        self._label_map = None

        # cache iq order
        self._cache_std_index = {}

        # record data
        self._record_data = defaultdict(list)
        self._origin_record_data = defaultdict(list)

        self.result_labels = self.get_data_labels()

        # extend plot iq function
        self.plot_iq = False
        self.iq_path = ""

        # for prepare single shot
        self.prepare_single_shot = False

        # readout fidelity correct
        self.fidelity_correct_type = ""

        self._cache_modes = None
        self._loop = 0

    @property
    def save_path(self):
        return self._save_path

    @save_path.setter
    def save_path(self, path: str):
        self._save_path = path

    @property
    def qubit_num(self):
        return len(self._dcm_list)

    @property
    def record_data(self):
        return self._record_data

    # @time_cal
    def tackle(self, measure_result: List, loop: int):
        """

        Args:
            measure_result:
            loop:

        Returns:

        """
        label_map = {}
        self._loop = loop

        for idx, once_result in enumerate(measure_result):
            dcm = self._dcm_list[idx]
            measure_mode = self._measure_mode_map.get(dcm.name)[0]

            # bugfix: predict label swap base dcm label
            labels = list(dcm.get_predict_label(once_result.I, once_result.Q))
            for i, v in enumerate(labels):
                labels[i] = dcm.label[v]

            labels = np.array(labels).reshape(
                (measure_mode.measure_total, self._repeat)
            )
            label_map[dcm.name] = labels

            if self.plot_iq is True:
                data_i = np.array(once_result.I).reshape(
                    (measure_mode.measure_total, self._repeat)
                )
                data_q = np.array(once_result.Q).reshape(
                    (measure_mode.measure_total, self._repeat)
                )
                iq_scatter_drawer(
                    list(data_i),
                    list(data_q),
                    [dcm for _ in range(measure_mode.measure_total)],
                    loop,
                    save_path=str(Path(self.iq_path, f"{dcm.name}-loop{loop}.png")),
                    plot_idx=True,
                )
                np.savetxt(
                    str(Path(self.iq_path, f"{dcm.name}-loop{loop}.dat")),
                    labels.T,
                    fmt="%d",
                )
                iq_arr = np.hstack((data_i.T, data_q.T))  # 前面 N 列均为 I, 后面 N 列均为 Q, N 为多次采集次数
                np.savetxt(str(Path(self.iq_path, f"{dcm.name}-loop{loop}_iq.dat")), iq_arr, fmt="%.6f")

        self._label_map = label_map

        result_labels = []
        result_values = []
        indexes = None
        tackles = [self._measure_mode_map.get(dcm.name) for dcm in self._dcm_list]

        if self._union_mode == UnionMode.U:
            for once_modes in itertools.product(*tackles):
                describe = " | ".join([str(one) for one in once_modes])
                result_labels.append(describe)
                result, indexes = self._once_run(list(once_modes))
                result_values.append(result)
        elif self._union_mode == UnionMode.AU:
            once_modes = []
            for child_modes in tackles:
                once_modes.extend(child_modes)
            result, indexes = self._once_run(list(once_modes))
            result_values.append(result)
        else:
            for idx, qubit_tackles in enumerate(tackles):
                dcm = self._dcm_list[idx]
                for tackle in qubit_tackles:
                    result, indexes = self._once_run([tackle], [dcm])
                    result_values.append(result)
                    result_labels.append(str(tackle))

        # use for Prepare SingleShot
        if self.prepare_single_shot is True:
            for idx, once_result in enumerate(measure_result):
                dcm = self._dcm_list[idx]
                measure_mode = self._measure_mode_map.get(dcm.name)[0]
                filter_i = np.array(once_result.I).reshape(
                    (measure_mode.measure_total, self._repeat)
                )[-1][indexes]
                filter_q = np.array(once_result.Q).reshape(
                    (measure_mode.measure_total, self._repeat)
                )[-1][indexes]
                return "PrepareSingleShot", [[filter_i, filter_q]]

        return result_labels, result_values

    def _once_run(
        self,
        once_mode_list: List[OnceQubitMeasureMode],
        dcms: List[IQdiscriminator] = None,
    ):
        repeat_indexes = None
        label_map = {}
        dcm_list = dcms or self._dcm_list

        for idx, tackle_type in enumerate(once_mode_list):
            dcm = dcm_list[idx]
            labels = self._label_map.get(dcm.name)
            label_map[dcm.name] = labels

            ok_indexes = self._filter_index(tackle_type, labels)

            if repeat_indexes is None:
                repeat_indexes = ok_indexes
            else:
                repeat_indexes = np.intersect1d(repeat_indexes, ok_indexes)

        with self._filter_log.open("a", encoding="utf-8") as f:
            f.write(
                f"Title: {' | '.join([str(mode) for mode in once_mode_list])} | "
                f"Filter({len(repeat_indexes)}) | Repeat({self._repeat}) | "
                f"COE({round(1 - len(repeat_indexes) / self._repeat, 4)})\n",
            )

        final_label = None
        for idx, dcm in enumerate(dcm_list):
            labels = label_map.get(dcm.name)
            tackle_type = once_mode_list[idx]
            labels = labels[tackle_type.measure_num, :][list(repeat_indexes)]
            if final_label is None:
                final_label = labels
            else:
                final_label = np.vstack((final_label, labels))
        total = final_label.shape[-1] or 1

        qubit_num = len(dcm_list)
        prob_status = np.zeros(1 << qubit_num)
        label_matrix = final_label.T
        if qubit_num == 1:
            for status_array in label_matrix:
                prob_status[int(status_array)] += 1
        else:
            for status_array in label_matrix:
                new_array = [str(int(v)) for v in status_array]
                prob_status[int("".join(new_array), 2)] += 1
        result = np.round(prob_status / total, 4)

        unit_key = "-".join([str(mode) for mode in once_mode_list])
        for idx, v in enumerate(result):
            self._origin_record_data[f"{unit_key}-P{idx}"].append(v)

        # add fidelity correct
        if self.fidelity_correct_type:
            aft_result = readout_fidelity_correct(
                origin_result=result,
                dcm_list=dcm_list,
                fidelity_correct_type=self.fidelity_correct_type,
            )
            # pyqlog.info(f"Origin ({result}) | Correct ({aft_result})")
            result = aft_result
            for idx, v in enumerate(result):
                self._record_data[f"{unit_key}-P{idx}"].append(v)
        else:
            for idx, v in enumerate(result):
                map_key = f"{unit_key}-P{idx}"
                self._record_data[map_key] = self._origin_record_data[map_key]
                # self._origin_record_data.pop(map_key)

        return result, repeat_indexes

    def _filter_index(self, tackle_type: OnceQubitMeasureMode, labels):
        if tackle_type.need_filter:
            new_labels = np.delete(labels, tuple(tackle_type.remove_index), axis=0)
            if new_labels.shape[0] == 0:
                return np.arange(self._repeat)
            else:
                return np.where(
                    (new_labels.T == tuple(tackle_type.goal_label)).all(axis=1)
                )[0]
        else:
            return np.arange(self._repeat)

    def _cache_dcm_std_index(self, dcms: List[IQdiscriminator]):
        name = "".join([dcm.name for dcm in dcms])
        if name not in self._cache_std_index and dcms[0].label is not None:
            pre_order = list(itertools.product(*[dcm.label for dcm in dcms]))
            sort_order = sorted(pre_order, key=lambda item: sum(item))
            if pre_order == sort_order:
                self._cache_std_index[name] = None
            else:
                am = {}
                for k in pre_order:
                    am[k] = sort_order.index(k)
                std_index = [am[k] for k in pre_order]
                self._cache_std_index[name] = std_index

        return self._cache_std_index.get(name)

    def save_record_data(self, x_data=None):
        if not self.save_path:
            return

        def _save_data(data, data_path: str):
            csv_data = []
            if x_data is not None:
                csv_data.append(["X-Data"] + list(x_data))
            for key, value in data.items():
                csv_row = [key]
                csv_row.extend(value)
                csv_data.append(csv_row)

            os.makedirs(self.save_path, exist_ok=True)
            with open(
                str(Path(self.save_path, f"{self._label}-{data_path}")),
                mode="w",
                newline="",
                encoding="utf-8",
            ) as f:
                writer = csv.writer(f)
                for row in csv_data:
                    writer.writerow(row)

        if self._record_data:
            _save_data(self._record_data, "result_data.csv")

        if self._origin_record_data and self.fidelity_correct_type:
            _save_data(self._origin_record_data, "origin_result_data.csv")

        if self._cache_modes:
            with open(
                str(Path(self.save_path, f"{self._label}-label.txt")),
                "w",
                encoding="utf-8",
            ) as file:
                for index, mode in enumerate(self._cache_modes):
                    file.write(f"{index}: {mode}\n")

    def get_data_labels(self):
        tackles = [self._measure_mode_map.get(dcm.name) for dcm in self._dcm_list]
        dcm_map = {dcm.name: dcm for dcm in self._dcm_list}
        result_labels = []
        if self._union_mode == UnionMode.U:
            for once_modes in itertools.product(*tackles):
                label_nums = [dcm_map.get(one.name).n_clusters for one in once_modes]
                total = reduce(mul, label_nums, 1)
                describe = "-".join([str(one) for one in once_modes])
                result_labels.extend([f"{describe}-P{i}" for i in range(total)])
        elif self._union_mode == UnionMode.AU:
            modes = []
            for child_modes in tackles:
                modes.extend(child_modes)
            label_nums = [2 for _ in range(len(modes))]
            total = reduce(mul, label_nums, 1)
            describe = "-".join([str(one) for one in modes])
            result_labels = generate_binary_strings(len(modes))
        else:
            for idx, qubit_tackles in enumerate(tackles):
                dcm = self._dcm_list[idx]
                for tackle in qubit_tackles:
                    result_labels.extend(
                        [f"{str(tackle)}-P{i}" for i in range(dcm.n_clusters)]
                    )

        # table = PrettyTable()
        # table.field_names = ["Index", "Label"]
        # for index, label in enumerate(result_labels):
        #     table.add_row([str(index), label])
        # pyqlog.log("EXP", f"Multiple Measure Label Results: \n{table}")

        return result_labels


class MultipleMeasureWrapperAU(MultipleMeasureWrapper):
    def get_data_labels(self):
        # add fidelity correct
        self.fidelity_correct_type = None
        self._record_data = {}
        return []

    def _once_run(
        self,
        once_mode_list: List[OnceQubitMeasureMode],
        dcms: List[IQdiscriminator] = None,
    ):
        dcm_list = dcms or self._dcm_list
        dcm_map = {dcm.name: dcm for dcm in dcm_list}
        self._cache_modes = once_mode_list

        # Precompute labels and filter indexes
        label_map = {}
        all_ok_indexes = []

        for tackle_type in once_mode_list:
            dcm = dcm_map[tackle_type.name]
            labels = self._label_map[dcm.name]
            label_map[dcm.name] = labels
            ok_indexes = self._filter_index(tackle_type, labels)
            all_ok_indexes.append(ok_indexes)

        # Compute intersection of all indexes
        if all_ok_indexes:
            repeat_indexes = all_ok_indexes[0]
            for ok_indexes in all_ok_indexes[1:]:
                repeat_indexes = np.intersect1d(
                    repeat_indexes, ok_indexes, assume_unique=True
                )
        else:
            repeat_indexes = np.array([], dtype=int)

        if self.plot_iq:
            filtered_count = len(repeat_indexes)
            coe = round(1 - filtered_count / self._repeat, 4)
            mode_str = " | ".join(str(mode) for mode in once_mode_list)
            pyqlog.info(
                f"Title: {mode_str} | Filter({filtered_count}) | "
                f"Repeat({self._repeat}) | COE({coe})"
            )

        # Process final labels
        qubit_num = len(once_mode_list)
        if qubit_num == 0:
            return {}, repeat_indexes

        # Collect all labels for selected indexes with optimization
        final_labels = [
            label_map[dcm_map[tackle_type.name].name][
                tackle_type.measure_num, list(repeat_indexes)
            ]
            for tackle_type in once_mode_list
        ]

        # Stack all labels together
        final_label = np.vstack(final_labels) if final_labels else np.empty((0, 0))

        # Count status occurrences using Counter for better performance
        total = final_label.shape[-1] or 1
        label_matrix = final_label.T
        if qubit_num == 1:
            # For single qubit
            int_labels = label_matrix.astype(int).flatten()
            prob_status = Counter(int_labels)
        else:
            # For multiple qubits
            prob_status = Counter(map(tuple, label_matrix.astype(int)))

        # Update record data using Counter's items method for efficiency
        result = {}
        for key, v in prob_status.items():
            key_str = str(key)
            records = self._record_data.setdefault(key_str, [])
            pre_length = len(records)
            if pre_length < self._loop:
                records.extend([0 for _ in range(self._loop - pre_length)])
            records.append(v)

        return result, repeat_indexes


def readout_fidelity_correct(
    origin_result: np.ndarray,
    dcm_list: List[IQdiscriminator],
    fidelity_correct_type: str,
):
    if len(dcm_list) == 1:
        new_fd_matrix = dcm_list[0].fidelity_matrix
    else:
        new_fd_matrix = None
        for dcm in dcm_list:
            if new_fd_matrix is None:
                new_fd_matrix = dcm.fidelity_matrix
            else:
                new_fd_matrix = np.kron(new_fd_matrix, dcm.fidelity_matrix)

    return correct_fidelity(
        bit_num=len(dcm_list),
        index_list=[True] * len(dcm_list),
        std_p=origin_result,
        f_matrix=new_fd_matrix,
        method=fidelity_correct_type,
    )


def generate_binary_strings(n):
    total = 2**n
    binary_strings = []
    for i in range(total):
        binary = bin(i)[2:]
        padded = binary.zfill(n)
        binary_strings.append(f"P{padded}")
    return binary_strings
