# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:2
msgid "pyQCat.preliminary.FindBusCavityFreq"
msgstr ""

#: of pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq:1
msgid ""
"Bases: "
":py:class:`~pyQCat.preliminary.preliminary_models.PreliminaryExperiment`"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
#: of pyQCat.preliminary.preliminary_models.PreliminaryExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.preliminary.FindBusCavityFreq.__init__>`\\ "
"\\(\\[qubit\\, coupler\\]\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.preliminary.FindBusCavityFreq.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.preliminary.FindBusCavityFreq.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.preliminary.FindBusCavityFreq.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ":py:obj:`run <pyQCat.preliminary.FindBusCavityFreq.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ""
":py:obj:`select_dc_source "
"<pyQCat.preliminary.FindBusCavityFreq.select_dc_source>`\\ "
"\\(\\[dc\\_source\\, qaio\\_ip\\, qaio\\_type\\]\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ""
":py:obj:`select_mic_source "
"<pyQCat.preliminary.FindBusCavityFreq.select_mic_source>`\\ "
"\\(mic\\_source\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ""
":py:obj:`select_net_analyzer "
"<pyQCat.preliminary.FindBusCavityFreq.select_net_analyzer>`\\ "
"\\(net\\_analyzer\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.preliminary.FindBusCavityFreq.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.preliminary.FindBusCavityFreq.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.preliminary.FindBusCavityFreq.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.preliminary.FindBusCavityFreq.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid ""
":py:obj:`set_sub_analysis_options "
"<pyQCat.preliminary.FindBusCavityFreq.set_sub_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:33:<autosummary>:1
msgid "Set subclass analysis options."
msgstr ""

#: ../../source/stubs/preliminary/pyQCat.preliminary.FindBusCavityFreq.rst:35
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.preliminary.FindBusCavityFreq.analysis>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.preliminary.FindBusCavityFreq.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`coupler <pyQCat.preliminary.FindBusCavityFreq.coupler>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.preliminary.FindBusCavityFreq.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`qaio <pyQCat.preliminary.FindBusCavityFreq.qaio>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`qubit <pyQCat.preliminary.FindBusCavityFreq.qubit>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`run_options <pyQCat.preliminary.FindBusCavityFreq.run_options>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`sub_analysis_options "
"<pyQCat.preliminary.FindBusCavityFreq.sub_analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:1
msgid "Default kwarg options for experiment."
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_analysis_options
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options
msgid "Return type"
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_analysis_options:4
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_experiment_options:5
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.preliminary.library.find_cavity_freq.FindBusCavityFreq._default_analysis_options:1
msgid "Default Analysis Options"
msgstr ""

