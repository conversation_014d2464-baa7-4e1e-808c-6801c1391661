# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:2
msgid "pyQCat.experiments.composite.ACSpectrum"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum:1
msgid "AC Spectrum experiment to get the relationship of qubit frequency-zamp."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.composite.ACSpectrum.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.composite.ACSpectrum.component_experiment>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.composite.ACSpectrum.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.composite.ACSpectrum.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.composite.ACSpectrum.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.composite.ACSpectrum.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid ""
"The first three points are used for quadratic function fitting to predict"
" the bit frequency value f_guess at the current z_amp."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.composite.ACSpectrum.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.composite.ACSpectrum.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.composite.ACSpectrum.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.composite.ACSpectrum.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ACSpectrum.rst:32
msgid "Attributes"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.composite.ACSpectrum.analysis>`\\"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.composite.ACSpectrum.analysis_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.composite.ACSpectrum.child_experiment>`\\"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.composite.ACSpectrum.experiment_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.composite.ACSpectrum.run_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.ramsey.Ramsey:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_analysis_options:1
msgid "Default analysis options for AC spectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_analysis_options
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_run_options
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._guess_next_freq
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._metadata
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_result
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_analysis_options:4
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:14
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_run_options:4
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:1
msgid "Default experiment options for AC spectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:12
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:4
msgid ""
"init_fringe (float): The initialize value of fringe. delays (Union[List, "
"np.ndarray]): Delay time scanned when performing Ramsey"
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:6
msgid "experiments."
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:7
msgid ""
"z_amp_list (Union[List, np.ndarray]): Z line amplitude (flux pulse) sweep"
" list. freq_bound (Optional[float], optional): Experiment will be stopped"
" when qubit's"
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:9
msgid "frequency delta value lower than this value. Defaults to 800MHz."
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:11
msgid ""
"osc_freq_limit (Optional[float], optional): [description]. Defaults to "
"2.5."
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_run_options:1
msgid "Default options values for the experiment :meth:`run` method."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._metadata:1
msgid "Return experiment metadata for ExperimentData."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._metadata:5
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum.run:1
msgid ""
"The first three points are used for quadratic function fitting to predict"
" the bit frequency value f_guess at the current z_amp. By running this "
"experiment, you can get the relationship between flux pulse's amplitude "
"and qubit's frequency."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe:1
msgid "Update fringe frequency."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_osc_freq
msgid "Parameters"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe:4
msgid "Ramsey index number."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe:7
msgid "IF."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe:10
msgid "Qubit frenquency get from Ramsey exp."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe:13
msgid "Z line amp at now."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_result
msgid "Returns"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe:16
msgid "Fringe frequency for the next Ramsey experiment."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._guess_next_freq:1
msgid "Predict next drive frenquency."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._guess_next_freq:4
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_osc_freq:1
msgid "Validate oscillation frequency."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_osc_freq:4
msgid "The index of the"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_result:1
msgid ""
"Truncation condition of AC/DC Spectrum experiment. :type fd: "
":py:class:`float` :param fd: The value of qubit's drive frequency. :type "
"fd: float :type f10: :py:class:`float` :param f10: The value of qubit's "
"real frequency. :type f10: float"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_result:9
msgid ":py:class:`bool`"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_result:10
msgid ""
"bool variable. True represent validate successfully and False represent "
"validate failed!"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._abnormal_diagnose:1
msgid "Diagnose experiment failed possible reason."
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.composite.ACSpectrum.__init__>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`component_experiment "
#~ "<pyQCat.experiments.composite.ACSpectrum.component_experiment>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.ACSpectrum.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.ACSpectrum.get_qubit_str>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`run <pyQCat.experiments.composite.ACSpectrum.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.ACSpectrum.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.ACSpectrum.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.ACSpectrum.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.composite.ACSpectrum.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":py:obj:`analysis <pyQCat.experiments.composite.ACSpectrum.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.composite.ACSpectrum.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`child_experiment "
#~ "<pyQCat.experiments.composite.ACSpectrum.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.composite.ACSpectrum.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.composite.ACSpectrum.run_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.composite.ACSpectrum.__init__>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`component_experiment "
#~ "<pyQCat.experiments.composite.ACSpectrum.component_experiment>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.ACSpectrum.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.ACSpectrum.get_qubit_str>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.composite.ACSpectrum.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`run <pyQCat.experiments.composite.ACSpectrum.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.ACSpectrum.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.ACSpectrum.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.ACSpectrum.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.composite.ACSpectrum.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.composite.ACSpectrum.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.composite.ACSpectrum.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`child_experiment "
#~ "<pyQCat.experiments.composite.ACSpectrum.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.composite.ACSpectrum.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.composite.ACSpectrum.run_options>`\\"
#~ msgstr ""

