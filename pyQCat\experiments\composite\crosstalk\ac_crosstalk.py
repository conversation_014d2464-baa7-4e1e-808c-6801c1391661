# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/07
# __author:       <PERSON><PERSON><PERSON>
"""
AC crosstalk experiment.
"""

import numpy as np

from ....analysis import CrosstalkAnalysis
from ....analysis.fit.fit_models import freq2amp_formula
from ....analysis.specification import ParameterRepr
from ....log import pyqlog
from .base_crosstalk import Crosstalk


class ACCrosstalk(Crosstalk):
    """AC crosstalk experiment to get the ac crosstalk matrix elements."""

    @classmethod
    def _default_analysis_options(cls):
        opt = super()._default_analysis_options()
        opt.result_parameters = [
            ParameterRepr("k", "coefficient", param_path="Crosstalk.ac_crosstalk")
        ]
        return opt

    async def _sync_composite_run(self):
        """Run AC crosstalk experiment and get the crosstalk coefficient."""
        # super().run()

        target_qubit, _ = self._get_qubits()
        fd, baseband_freq, fringe = self._get_runtime_parameters(target_qubit)
        fq = target_qubit.drive_freq - self.experiment_options.freq_offset

        v_target = round(
            freq2amp_formula(
                fd - self.experiment_options.freq_offset,
                *self.experiment_options.spectrum_params,
            ),
            3,
        )

        # Get bias voltage list
        await self._get_v_bias_list(
            v_target=v_target,
            fringe=fringe,
            fd=fd,
            fq=fq,
            baseband_freq=baseband_freq,
            z_amp=v_target,
        )

        for index, v_bias in enumerate(self.run_options.v_bias_list):
            pyqlog.info(f"Run Ramsey: bias voltage={v_bias}v")
            v_real = await self._run_ramsey(
                index=index + 1,
                v_bias=round(v_bias, 3),
                v_target=v_target,
                fringe=fringe,
                fd=fd,
                baseband_freq=baseband_freq,
            )
            pyqlog.info(f"Ramsey: real target voltage={v_real}v")
            self.run_options.v_real_list.append(v_real)

        self._run_analysis(
            x_data=np.asarray(self.run_options.v_bias_list),
            analysis_class=CrosstalkAnalysis,
        )

        self.file.save_data(
            self.run_options.v_bias_list, self.run_options.v_real_list, name=self._label
        )
