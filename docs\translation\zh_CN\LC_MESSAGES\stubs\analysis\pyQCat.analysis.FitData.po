# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.FitData.rst:2
msgid "pyQCat.analysis.FitData"
msgstr ""

#: of pyQCat.analysis.specification.FitData:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.analysis.specification.FitData:1
msgid "A dataclass to store the outcome of the fitting."
msgstr ""

#: of pyQCat.analysis.specification.FitData:5
msgid "List of optimal parameter values with uncertainties if available."
msgstr ""

#: of pyQCat.analysis.specification.FitData
msgid "type"
msgstr ""

#: of pyQCat.analysis.specification.FitData:7
msgid "List[float]"
msgstr ""

#: of pyQCat.analysis.specification.FitData:11
msgid "List of parameter names being fit."
msgstr ""

#: of pyQCat.analysis.specification.FitData:13
msgid "List[str]"
msgstr ""

#: of pyQCat.analysis.specification.FitData:17
msgid "The value of rmse."
msgstr ""

#: of pyQCat.analysis.specification.FitData:19
msgid "float"
msgstr ""

#: of pyQCat.analysis.specification.FitData:23
msgid "Y-values got form the fitter."
msgstr ""

#: of pyQCat.analysis.specification.FitData:25
msgid "numpy.ndarray"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitData.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitData.rst:23:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.FitData.__init__>`\\ \\(popt\\, "
"popt\\_keys\\, variance\\, y\\_fit\\[\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitData.rst:23:<autosummary>:1
msgid ":py:obj:`fitval <pyQCat.analysis.FitData.fitval>`\\ \\(key\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitData.rst:23:<autosummary>:1
#: of pyQCat.analysis.specification.FitData.fitval:1
msgid "A helper method to get fit value object from parameter key name."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitData.rst:23:<autosummary>:1
msgid ":py:obj:`format <pyQCat.analysis.FitData.format>`\\ \\(\\[num\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.FitData.rst:25
msgid "Attributes"
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval:1:<autosummary>:1
msgid ":py:obj:`goodness_of_fit <pyQCat.analysis.FitData.goodness_of_fit>`\\"
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval:1:<autosummary>:1
msgid ":py:obj:`popt <pyQCat.analysis.FitData.popt>`\\"
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval:1:<autosummary>:1
msgid ":py:obj:`popt_keys <pyQCat.analysis.FitData.popt_keys>`\\"
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval:1:<autosummary>:1
msgid ":py:obj:`variance <pyQCat.analysis.FitData.variance>`\\"
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval:1:<autosummary>:1
msgid ":py:obj:`y_fit <pyQCat.analysis.FitData.y_fit>`\\"
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval:4
msgid "Name of parameters to extract."
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval:6
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval:7
msgid ""
"A float object which functions as a standard Python float object but with"
" automatic error propagation."
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval
msgid "Raises"
msgstr ""

#: of pyQCat.analysis.specification.FitData.fitval:10
msgid "When specified parameter is not defined."
msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.analysis.FitData.__init__>`\\ "
#~ "\\(popt\\, popt\\_keys\\, variance\\, y\\_fit\\[\\,"
#~ " ...\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`fitval <pyQCat.analysis.FitData.fitval>`\\ \\(key\\)"
#~ msgstr ""

#~ msgid ":obj:`format <pyQCat.analysis.FitData.format>`\\ \\(\\[num\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`goodness_of_fit <pyQCat.analysis.FitData.goodness_of_fit>`\\"
#~ msgstr ""

#~ msgid ":obj:`popt <pyQCat.analysis.FitData.popt>`\\"
#~ msgstr ""

#~ msgid ":obj:`popt_keys <pyQCat.analysis.FitData.popt_keys>`\\"
#~ msgstr ""

#~ msgid ":obj:`variance <pyQCat.analysis.FitData.variance>`\\"
#~ msgstr ""

#~ msgid ":obj:`y_fit <pyQCat.analysis.FitData.y_fit>`\\"
#~ msgstr ""

