<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1086</width>
    <height>625</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>WorkSpace Manage</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item row="0" column="0">
     <widget class="QGroupBox" name="groupBox_2">
      <property name="title">
       <string>WorkSpace</string>
      </property>
      <layout class="QGridLayout" name="gridLayout_3" rowstretch="2,10">
       <item row="1" column="0">
        <widget class="QTableViewWorkSpaceWidget" name="tableWorkSpaceView"/>
       </item>
       <item row="0" column="0">
        <widget class="QWidget" name="widget_5" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="5,1">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QWidget" name="widget_12" native="true">
            <layout class="QVBoxLayout" name="verticalLayout_4">
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QWidget" name="widget_6" native="true">
               <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="2,10,5">
                <property name="spacing">
                 <number>6</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="label_4">
                  <property name="text">
                   <string>user</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="SearchComboBox" name="workUserContent"/>
                </item>
                <item>
                 <spacer name="horizontalSpacer_4">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>91</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="widget_7" native="true">
               <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="2,10,5">
                <property name="sizeConstraint">
                 <enum>QLayout::SizeConstraint::SetDefaultConstraint</enum>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="label_5">
                  <property name="text">
                   <string>sample</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="SearchComboBox" name="workSampleContent">
                  <property name="enabled">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_5">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>91</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="widget_13" native="true">
               <property name="enabled">
                <bool>true</bool>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="2,10,5">
                <property name="spacing">
                 <number>5</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="label_6">
                  <property name="text">
                   <string>env_name</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="SearchComboBox" name="workEnvContent"/>
                </item>
                <item>
                 <spacer name="horizontalSpacer_6">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>38</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_14" native="true">
            <layout class="QVBoxLayout" name="verticalLayout_5">
             <item>
              <spacer name="verticalSpacer_3">
               <property name="orientation">
                <enum>Qt::Orientation::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>15</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="queryWorkSpace">
               <property name="text">
                <string>query</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_4">
               <property name="orientation">
                <enum>Qt::Orientation::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>15</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionCreatSpace"/>
   <addaction name="separator"/>
   <addaction name="actionCopySpace"/>
   <addaction name="separator"/>
   <addaction name="actionRefresh"/>
  </widget>
  <action name="actionCreatSpace">
   <property name="text">
    <string>Creat Space</string>
   </property>
  </action>
  <action name="actionRefresh">
   <property name="text">
    <string>refresh</string>
   </property>
  </action>
  <action name="actionCopySpace">
   <property name="text">
    <string>Copy Space</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QTableViewWorkSpaceWidget</class>
   <extends>QTableView</extends>
   <header>.widgets.chip_manage_files.table_view_workspace</header>
  </customwidget>
  <customwidget>
   <class>SearchComboBox</class>
   <extends>QComboBox</extends>
   <header>.widgets.combox_custom.combox_search</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>queryWorkSpace</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>query_workspace()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>962</x>
     <y>105</y>
    </hint>
    <hint type="destinationlabel">
     <x>513</x>
     <y>323</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionCreatSpace</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>creat_work_space()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>541</x>
     <y>335</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionRefresh</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>refresh()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>541</x>
     <y>335</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>workSampleContent</sender>
   <signal>currentTextChanged(QString)</signal>
   <receiver>MainWindow</receiver>
   <slot>sample_change()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>742</x>
     <y>107</y>
    </hint>
    <hint type="destinationlabel">
     <x>541</x>
     <y>335</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionCopySpace</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>copy_space()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>542</x>
     <y>312</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>workUserContent</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>user_change()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>379</x>
     <y>71</y>
    </hint>
    <hint type="destinationlabel">
     <x>542</x>
     <y>312</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>create_chip()</slot>
  <slot>query_chip()</slot>
  <slot>query_workspace()</slot>
  <slot>creat_work_space()</slot>
  <slot>refresh()</slot>
  <slot>sample_change()</slot>
  <slot>user_change()</slot>
  <slot>copy_space()</slot>
 </slots>
</ui>
