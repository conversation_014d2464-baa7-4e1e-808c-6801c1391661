# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/05/31
# __author:       xw
# __corporation:  OriginQuantum

from typing import List

from ...log import pyqlog
from ...structures import Options
from ...types import QualityDescribe
from ..curve_analysis import CurveAnalysis
from ..quality import BaseQuality
from ..specification import ParameterRepr


class AmpCompositeAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Options:
            diff_threshold (float): Twice amp results difference.
        """
        options = super()._default_options()

        options.fine = True
        options.step = None

        options.subplots = (1, 1)
        options.x_label = "Amp [v]"
        options.y_label = "P1"
        options.plot_keys = "P1"

        options.use_deviation = False
        options.deviation_limit = 0.5
        options.diff_threshold = 0.2

        options.result_parameters = [ParameterRepr(name="Xpi", repr="X-amp", unit="v")]

        options.figsize = (12, 8)

        return options

    @staticmethod
    def find_coincident_amp(amp_list: List, N: int) -> List:
        order_amp_list = sorted(amp_list)
        distance_list = []
        iter_count = len(amp_list) - N + 1
        for loop in range(iter_count):
            temp_distance_list = []
            temp_amp = order_amp_list[loop : loop + N]
            for i, p in enumerate(temp_amp):
                if i > 0:
                    temp_distance = temp_amp[i] - temp_amp[i - 1]
                    temp_distance_list.append(temp_distance)
            distance = sum(temp_distance_list)
            distance_list.append(distance)
        min_dis = min(distance_list)
        min_index = distance_list.index(min_dis)
        min_amp_list = order_amp_list[min_index : min_index + N]
        return min_amp_list

    def _extract_result(self, data_key: str):
        """Extract amp value from points, and judge coincident or not.

        Args:
            data_key (str): The basis for selecting data.
        """
        self._quality = BaseQuality()
        analysis_data = self.analysis_datas[data_key]
        y = analysis_data.y.tolist()
        freq = self.analysis_datas.freq.y
        t_list = 1 / freq
        amp_list = [item[0] for item in y]
        y_list = [item[1] for item in y]
        res_list = []
        for amp, t in zip(amp_list, t_list):
            res_list.extend([amp - t, amp, amp + t])
            res_list = [x for x in res_list if 0 < x < 1]

        target_amp_list = self.find_coincident_amp(res_list, len(analysis_data.x))
        max_amp = max(target_amp_list)
        min_amp = min(target_amp_list)
        mean_amp = round((sum(target_amp_list)) / len(target_amp_list), 5)
        mean_y = round((sum(y_list)) / len(y_list), 5)
        amp_diff = abs(max_amp - min_amp)

        pos_list = [(mean_amp, mean_y)]

        for p in self.options.result_parameters:
            self.results.get(p.name).value = mean_amp

        rp_list = [f"Coincident Point\n{pos}" for pos in pos_list]
        self.drawer.set_options(text_pos=pos_list, text_rp=rp_list)

        pyqlog.info(
            f"{self.options.result_name} mean_amp: {mean_amp}, amp_diff: {amp_diff}"
        )

        # Set quality.
        # 2024.08.06, Issue by Kong Zong, recommend validate deviation.
        use_deviation = self.options.use_deviation
        deviation_limit = self.options.deviation_limit
        diff_threshold = self.options.diff_threshold

        if use_deviation is True:
            amp_arr = self.experiment_data.child_data(0).x_data
            min_amp, max_amp = min(amp_arr), max(amp_arr)
            mid_amp = (max_amp + min_amp) / 2
            scope = (mid_amp - min_amp) * deviation_limit
            lb, rb = mid_amp - scope, mid_amp + scope
            if lb < mean_amp < rb and amp_diff <= diff_threshold:
                self._quality = BaseQuality.instantiate(QualityDescribe.perfect)
            else:
                self._quality = BaseQuality.instantiate(QualityDescribe.bad)
        elif amp_diff <= diff_threshold:
            self._quality = BaseQuality.instantiate(QualityDescribe.perfect)
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)

        if self._quality.is_pass():
            for child_exp_data in self.experiment_data._child_data.values():
                quality = child_exp_data.metadata.process_meta.get('quality')
                if not quality or not quality.is_pass():
                    self._quality.set_bad()
                    break

    def _visualization(self):
        self.drawer.set_options(title=self._description())
        self.drawer.set_options(default_colors=["blueviolet", "orangered", "green"])

        if self.has_child is True:
            x_arr = None
            self.drawer.set_options(raw_data_format="plot")

            plot_keys = self.options.plot_keys
            if isinstance(plot_keys, str):
                plot_keys = [plot_keys]
            length = len(plot_keys)

            for i, n in enumerate(self.experiment_data.x_data):
                child_data = self.experiment_data.child_data(index=i)

                if x_arr is None:
                    x_arr = child_data.x_data

                i_key = plot_keys[i] if i < length else plot_keys[-1]
                y_arr = child_data.y_data.get(i_key)

                color = self.drawer.options.default_colors[
                    i % len(self.drawer.options.default_colors)
                ]

                self.drawer.draw_raw_data(
                    x_data=x_arr, y_data=y_arr, ax_index=0, label=f"N={n}", color=color
                )

            self.drawer.draw_text(ax_index=0)
        self.drawer.format_canvas()
