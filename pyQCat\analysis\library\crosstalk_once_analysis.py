# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/11/22
# __author:       <PERSON><PERSON><PERSON>


from typing import Union, List

import numpy as np

from scipy.signal import find_peaks

from pyQCat.types import QualityDescribe
from pyQCat.structures import Options

from ..algorithms.find_peak import judge_peak_dip
from ..curve_analysis import (
    CurveAnalysis,
    CurveAnalysisData,
    FitModel,
    FitOptions
)
from ..fit.fit_models import gauss_lorentzian
from ..quality.base_quality import BaseQuality
from ..specification import ParameterRepr


class CrosstalkOnceAnalysis(CurveAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the QubitSpectrum Analysis options, and set some fields."""
        options = super()._default_options()

        # set fitting iteration parameters
        options.curve_fit_extra.update({"ftol": 1.49012e-8, "xtol": 1.49012e-8})

        # default x label (Drive Frequency (MHz))
        options.x_label = "Target Bit Ac (V)"

        # set fit model (lorentzian)
        options.fit_model = FitModel(fit_func=gauss_lorentzian)

        # default quality bounds
        options.quality_bounds = [0.8, 0.6, 0.5]

        # default result parameters (freq)
        options.result_parameters = [
            ParameterRepr(name="b", repr="best_ac", unit="V")
        ]

        return options

    def _guess_fit_param(
            self,
            user_opt: FitOptions,
            curve_data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        x = curve_data.x
        y = curve_data.y

        if judge_peak_dip(y) == 'dips':
            y = -y

        idx_max = np.argmax(y)
        b0 = x[idx_max]
        offset0 = np.min(y)
        a0 = np.max(y) - np.min(y)
        idx_half = np.max(np.argwhere(y > (np.max(y) + np.min(y)) / 2))
        c0 = x[idx_half] - x[idx_max]
        e0 = -0.1

        new_opt = user_opt.copy()
        new_opt.p0.set_if_empty(offset=offset0)
        new_opt.p0.set_if_empty(a=a0)
        new_opt.p0.set_if_empty(b=b0)
        new_opt.p0.set_if_empty(c=c0)
        new_opt.p0.set_if_empty(e=e0)

        return new_opt

    def _extract_result(self, data_key: str):
        """Extract the Analysis results from fitted data.

        Args:
            data_key (str): The basis for selecting data.

        """
        analysis_data = self.analysis_datas[data_key]
        x = analysis_data.x
        y = analysis_data.y

        if isinstance(self.quality, BaseQuality) and self.quality._quality in [
            QualityDescribe.perfect,
            QualityDescribe.normal,
        ]:
            target_val = analysis_data.fit_data.fitval("b")
        else:
            if judge_peak_dip(y) == "dips":
                y = -y

            x_step = round(abs(x[1] - x[0]), 6)
            y_max = np.max(y)
            y_mean = np.mean(y)
            height = (y_max - y_mean) / 5 + y_mean

            peaks, properties = find_peaks(y, height=height, width=x_step / 2)
            if peaks.size > 0:
                peak_value_arr = properties.get("peak_heights")
                t_idx = peaks[np.argmax(peak_value_arr)]
            else:
                t_idx = np.argmax(y)
            target_val = x[t_idx]

        self.results.b.value = target_val
