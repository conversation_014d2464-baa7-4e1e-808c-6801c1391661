# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/11/10
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy
from typing import List

from ....analysis import ParameterRepr, QubitSpectrumAnalysis
from ....errors import ExperimentFieldError
from ....log import pyqlog
from ....pulse import Constant, Drag, PulseComponent
from ....pulse.pulse_function import pi_pulse
from ....qaio_property import QAIO
from ....qubit import Qubit
from ....structures import MetaData, Options
from ....tools import qarange
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class QubitSpectrumF12(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("f12_list", list)
        options.set_validator("f12_xpi", float)
        options.set_validator("f12_width", float)
        options.set_validator("scope", dict)
        options.set_validator("z_amp", float)
        options.set_validator("delay", float)

        options.f12_xpi = None
        options.f12_width = 500
        options.f12_list = None
        options.scope = {"l": 250, "r": -150, "s": 2}
        options.z_amp = 0
        options.delay = 0

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("window_length", (5, 20, 0))
        options.set_validator("freq_distance", float)

        options.window_length = 11
        options.freq_distance = 80
        options.data_key = None
        options.quality_bounds = [0.8, 0.6, 0.5]
        options.result_parameters = [
            ParameterRepr(
                name="freq",
                repr="f12",
                unit="MHz",
                param_path="Qubit.f12_options.drive_freq",
            ),
            ParameterRepr(name="power", repr="drive power", unit="db"),
            ParameterRepr(
                name="anharmonicity",
                repr="anharmonicity",
                unit="MHz",
                param_path="Qubit.anharmonicity",
            ),
        ]

        return options

    @staticmethod
    def set_xy_pulses(builder):
        xy_pulses = QubitSpectrumF12.get_xy_pulse(
            builder.qubit,
            builder.experiment_options.f12_list,
            builder.experiment_options.delay,
        )
        builder.play_pulse("XY", builder.qubit, xy_pulses)

    @staticmethod
    def set_z_pulses(builder):
        pass

    @staticmethod
    def get_xy_pulse(
        qubit: Qubit, f12_list: List, delay: float
    ) -> List[PulseComponent]:
        f01 = qubit.drive_freq
        options = qubit.f12_options
        baseband_freq = qubit.XYwave.baseband_freq
        delay_pulse = Constant(delay, 0, "XY")()
        f01_pulse = pi_pulse(qubit)()
        f12_pulse = Drag(
            time=options.time,
            offset=options.offset,
            amp=options.Xpi,
            detune=options.detune_pi,
            freq=baseband_freq,
            alpha=options.alpha,
            delta=options.delta,
        )

        xy_pulses = []
        for f12 in f12_list:
            if QAIO.type in [QAIO.qaio_8, QAIO.qaio_30]:
                f02_baseband_freq = round(baseband_freq + f01 - f12, 3)
            elif QAIO.type in [QAIO.qaio_72]:
                f02_baseband_freq = round(f12 - (f01 - baseband_freq), 3)
            else:
                raise ExperimentFieldError(
                    "QubitSpectrumF12", f"Not Support QAIO type {QAIO.type} !"
                )
            pyqlog.log(
                "EXP",
                f"f01={f01} | IF01={baseband_freq} | f12={f12} | IF02={f02_baseband_freq}",
            )

            new_f12_pulse = deepcopy(f12_pulse)
            new_f12_pulse.freq = f02_baseband_freq * 1e-3

            pulse = (
                deepcopy(f01_pulse)
                + deepcopy(delay_pulse)
                + new_f12_pulse()
                + deepcopy(delay_pulse)
                + deepcopy(f01_pulse)
            )
            xy_pulses.append(pulse)

        return xy_pulses

    def _check_options(self):
        super()._check_options()
        f12_xpi = self.experiment_options.f12_xpi
        f12_width = self.experiment_options.f12_width
        f12_list = self.experiment_options.f12_list
        scope = self.experiment_options.scope
        f01 = self.qubit.drive_freq

        if f12_list is None:
            f12_list = qarange(
                round(f01 - scope.get("l"), 3),
                round(f01 + scope.get("r"), 3),
                round(scope.get("s"), 3),
            )
            self.experiment_options.f12_list = f12_list

        if f12_xpi is not None:
            self.qubit.f12_options.Xpi = f12_xpi

        if f12_width is not None:
            self.qubit.f12_options.time = f12_width

        if self.discriminator is not None:
            data_type = "I_Q"
        else:
            data_type = "amp_phase"

        self.set_experiment_options(data_type=data_type)
        self.set_run_options(
            x_data=self.experiment_options.f12_list,
            analysis_class=QubitSpectrumAnalysis,
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()

        metadata.draw_meta = {
            "f01": self.qubit.drive_freq,
            "f12_xpi": self.qubit.f12_options.Xpi,
            "drive_power": self.qubit.drive_power,
        }
        metadata.process_meta = {"f01": self.qubit.drive_freq if self.qubit else None}

        return metadata
