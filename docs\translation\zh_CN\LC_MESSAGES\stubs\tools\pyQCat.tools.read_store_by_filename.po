# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-29 13:55+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.read_store_by_filename.rst:2
msgid "pyQCat.tools.read\\_store\\_by\\_filename"
msgstr ""

#~ msgid "Get config file data api."
#~ msgstr ""

#~ msgid "Parameters"
#~ msgstr ""

#~ msgid "config file name, likes `dc_crosstalk.dat`"
#~ msgstr ""

#~ msgid "Belong to user config file."
#~ msgstr ""

#~ msgid "Returns"
#~ msgstr ""

#~ msgid "The config file origin data."
#~ msgstr ""

#~ msgid "Return type"
#~ msgstr ""

