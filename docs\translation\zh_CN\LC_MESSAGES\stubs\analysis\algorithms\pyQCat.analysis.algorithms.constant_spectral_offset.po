# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.constant_spectral_offset.rst:2
msgid "pyQCat.analysis.algorithms.constant\\_spectral\\_offset"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:1
msgid "Get constant offset of spectral baseline."
msgstr "获取能谱曲线基底"

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:3
msgid ""
"This function searches constant offset by finding a region where 1st and "
"2nd order differentiation are close to zero. A return value is an average"
" y value of that region. To suppress the noise contribution to "
"derivatives, this function also applies a Savitzky-Golay filter to y "
"value."
msgstr ""
"此函数通过查找一阶和二阶微分接近于零的区域来搜索恒定偏移量。 返回值是该区域"
"的平均 y 值。 为了抑制对导数的噪声贡献，此函数还将 Savitzky-Golay 滤波器应用于 y 值。"

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:8
msgid ""
"This method is more robust to offset error than just taking median or "
"average of y values especially when a peak width is wider compared to the"
" scan range."
msgstr ""
"与仅取 y 值的中值或平均值相比，此方法对偏移误差更稳健，尤其是当峰宽与扫描范"
"围相比更宽时。"

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:12
msgid "Array of y values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:14
msgid "Window size of Savitzky-Golay filter. This should be odd number."
msgstr "``Savitzky-Golay`` 滤波器的窗口大小，应该为奇数"

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:16
msgid "Dimension of Savitzky-Golay filter."
msgstr "``Savitzky-Golay`` 滤波器的维度"

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:18
msgid ""
"Threshold value to decide flat region. This value represent a ratio to "
"the maximum derivative value."
msgstr "决定平坦区域的阈值。 该值表示与最大导数值的比率。"

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:21
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.constant_spectral_offset:22
msgid "Offset value."
msgstr "偏移量"
