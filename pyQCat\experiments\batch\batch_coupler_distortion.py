# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/09/15
# __author:       <PERSON>za

import time
import math
from typing import List
import numpy as np

from ...analysis.z_distortion_correct.Unit_step_response_fit_oscilloscope_integration import (
    integration,
)
from ...executor.batch import divide_coupler_calibration_parallel_group
from ...log import pyqlog
from ...tools import get_bound_ac_spectrum
from ..batch_experiment import BatchExperiment


class BatchCouplerDistortionT1New(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.coupler_shift_flows = None
        options.qubit_cali_flows = None
        options.coupler_timing_flows = None
        options.distortion_flows = None
        options.xpi_flows = None
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.qc2qh_map = {}
        options.origin_data = {}
        return options

    def _qc_to_qh(self, qc_units: List[str]):
        return [self.run_options.qc2qh_map.get(qc) for qc in qc_units]

    def _qh_to_qc(self, qh_units: List[str]):
        qc_units = []
        for qc, qh in self.run_options.qc2qh_map.items():
            if qh in qh_units:
                qc_units.append(qc)
        return qc_units

    def _run_batch(self):
        qh_list = []
        for unit in self.experiment_options.physical_units:
            coupler = self.context_manager.chip_data.get_physical_unit(unit)
            qh_list.append(f"q{coupler.probe_bit}")
        groups = divide_coupler_calibration_parallel_group(
            self.experiment_options.physical_units,
            self.context_manager.chip_data,
            **self.backend.system.parallel_divide,
        )

        self._set_qubit_ac_bias()

        # coupler fixed point calibration flows；根据Coupler参数自动选择实验branch
        for unit in self.experiment_options.physical_units:
            coupler = self.context_manager.chip_data.get_physical_unit(unit)
            half_period = abs(coupler.dc_max - coupler.dc_min)
            if coupler.dc_max > 0:
                ac_branch = "left"
                z_amp = -half_period * 0.66
            else:
                ac_branch = "right"
                z_amp = half_period * 0.66
            self.change_parallel_exec_exp_options(
                # exp_name="ZZShiftFixedPointCalibration_0",
                exp_name="FreqShiftByCoupler",
                unit=unit,
                z_amp=z_amp,
                options={"child_exp_options.ac_branch": ac_branch},
            )
        for group in groups:
            pass_units = self._run_flow(
                flows=self.experiment_options.coupler_shift_flows,
                physical_units=group,
            )
            if not pass_units:
                pyqlog.warning(f"No find any unit, final in coupler shift flows!")
                continue

            # set coupler dc_max=dc_max+idle_point, idle_point=0;同时设置Distortion实验中的amp
            for unit in pass_units:
                coupler = self.context_manager.chip_data.get_physical_unit(unit)
                period = 2 * abs(coupler.dc_max - coupler.dc_min)
                distance = period - 2 * abs(coupler.idle_point)
                amp_temp = period * 0.65
                if coupler.dc_max > 0:
                    if (
                        abs(distance - amp_temp) > period / 15
                        and (coupler.dc_max + coupler.idle_point - amp_temp) > -0.45
                    ):
                        z_amp = -amp_temp
                    elif (
                        abs(distance - amp_temp) > period / 15
                        and (coupler.dc_max + coupler.idle_point - amp_temp) < -0.45
                    ):
                        z_amp = -0.45 - coupler.dc_max - coupler.idle_point
                    elif (
                        abs(distance - amp_temp) < period / 15
                        and (coupler.dc_max + coupler.idle_point - 0.3) > -0.45
                    ):
                        z_amp = -period * 0.5
                    else:
                        z_amp = -0.25
                else:
                    if (
                        abs(distance - amp_temp) > period / 15
                        and (coupler.dc_max + coupler.idle_point + amp_temp) < 0.45
                    ):
                        z_amp = -amp_temp
                    elif (
                        abs(distance - amp_temp) > period / 15
                        and (coupler.dc_max + coupler.idle_point + amp_temp) > 0.45
                    ):
                        z_amp = 0.45 - coupler.dc_max - coupler.idle_point
                    elif (
                        abs(distance - amp_temp) < period / 15
                        and (coupler.dc_max + coupler.idle_point + 0.3) < 0.45
                    ):
                        z_amp = period * 0.5
                    else:
                        z_amp = 0.25
                self.change_parallel_exec_exp_options(
                    exp_name="CouplerDistortionZZCompositeNew_0", unit=unit, z_amp=z_amp
                )
            self._set_coupler_ac_bias(pass_units)

            # qubit freq calibration flow
            pass_qh_units = self._qc_to_qh(pass_units)
            self._reset_xy_pulse_params(pass_qh_units, detune=0.0)
            self._reset_xy_pulse_params(
                pass_qh_units, freq=0.0
            )  # 直接修改shift20 MHz之后的频率作为drive_freq
            pass_qh_units = self._run_flow(
                flows=self.experiment_options.qubit_cali_flows,
                physical_units=pass_qh_units,
            )
            if not pass_qh_units:
                pyqlog.warning(f"No find any unit, final in qubit cali flows!")
                continue

            self._reset_xy_pulse_params(pass_qh_units, width=200)
            pass_qh_units = self._run_flow(
                flows=self.experiment_options.xpi_flows,
                physical_units=pass_qh_units,
            )
            if not pass_qh_units:
                pyqlog.warning(f"No find any unit, final in xpi flows!")
                continue

            # distortion composite flow
            # Set amp to avoid areas with close frequencies
            pass_qc_units = self._qh_to_qc(
                pass_qh_units
            )  # 根据qh反向计算得到的qc需要判断是否包含在初始的list之中
            physical_psss_units = []
            for unit in pass_qc_units:
                if unit in pass_units:
                    physical_psss_units.append(unit)
                else:
                    pass
            pass_qc_units = self._run_flow(
                flows=self.experiment_options.distortion_flows,
                physical_units=physical_psss_units,
            )

            fail_units = [
                unit
                for unit in self.experiment_options.physical_units
                if unit not in pass_qc_units
            ]
            pyqlog.info(f"Pass Units: {pass_qc_units} | Fail Units: {fail_units}")

    def _record_experiment(self, exp_name, exp, physical_units, err: Exception = None):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if record.exp_name == "CouplerDistortionZZCompositeNew_0":
            origin_data = distortion_collection(self, exp, record)
            self.run_options.origin_data.update(origin_data)
            self._save_data_to_json(
                self.run_options.origin_data, "origin-iir-data.json"
            )
        return record

    def _set_qubit_ac_bias(self):
        for coupler_name in self.experiment_options.physical_units:
            # get coupler structure
            coupler_struct = self.context_manager.chip_data.get_coupler_struct(
                coupler_name
            )
            qh = coupler_struct.qh
            ql = coupler_struct.ql
            self.run_options.qc2qh_map[coupler_name] = qh.name

            # set the env bit working voltage
            self.refresh_working_point(ql, qh)

    def _set_coupler_ac_bias(self, units: List[str]):
        for coupler_name in units:
            coupler = self.context_manager.chip_data.cache_coupler.get(coupler_name)
            print(f"{coupler}: {coupler.idle_point}")
            coupler.dc_max += coupler.idle_point
            coupler.idle_point = 0

    def _reset_xy_pulse_params(
        self,
        units: List[str],
        detune: float = None,
        width: float = None,
        freq: float = None,
    ):
        for qubit_name in units:
            qubit = self.context_manager.chip_data.cache_qubit.get(qubit_name)
            if detune is not None:
                qubit.XYwave.detune_pi = detune
                qubit.XYwave.detune_pi2 = detune
            if width is not None:
                delta_power = math.log2(width / qubit.XYwave.time) * 6
                qubit.XYwave.time = width
                new_power = qubit.drive_power - delta_power
                if -40 < new_power < -10:
                    qubit.drive_power = round(new_power, 1)
                elif new_power < -40:
                    qubit.drive_power = -40
                else:
                    qubit.drive_power = -10
            if freq is not None:
                qubit.drive_freq -= 20
            # qubit.save_data()

    @staticmethod
    def refresh_working_point(ql, qh):
        ql_max_freq, ql_min_freq = get_bound_ac_spectrum(ql)
        if ql_max_freq != ql_min_freq:
            if abs(ql_max_freq - qh.drive_freq) > abs(ql_min_freq - qh.drive_freq):
                ql.dc_min = ql.dc_max
            else:
                ql.dc_min = ql.dc_max - 1 / (2 * ql.ac_spectrum_v[2])
        return ql_max_freq, ql_min_freq


def distortion_collection(batch_obj, exp, record):
    try:
        origin_sos_data = {}

        def from_exp(_exp):
            qubit_name = _exp.qubits[0].name
            unit_name = qubit_name
            if _exp.couplers:
                coupler_name = _exp.couplers[0].name
                unit_name = coupler_name

            _exp.file.wait_thread_clear()

            if unit_name in record.pass_units:
                time.sleep(0.3)
                exp_dir = _exp.file.dirs
                iter_num = exp_dir.split("\\")[-2].split("_")[-1][4:]
                osc_data_path = (
                    exp_dir + qubit_name + f"_iteration_{iter_num}_delay_response.dat"
                )
                osc_data = np.genfromtxt(osc_data_path)

                integration(
                    osc_data,
                    exp_dir,
                    real_poles_num_list=[3, 4, 2],
                    complex_poles_num_list=[0, 0, 1],
                    best_poles_num_index=1,
                    start_time=20,
                    FIR_length=0,
                    step_response_normalization=True,
                    Ts_AWG=1 / 1.2,
                    response_type="rise",
                    temperature="low_temperature",
                )

                character_dict = batch_obj.context_manager.chip_data.cache_config.get(
                    "character.json"
                )
                rt_arr = np.loadtxt(exp_dir + "_sos_digital_filter_RT.dat")
                single_dict = character_dict.get(unit_name)
                single_dict.update({"distortion_type": "sos"})
                origin_sos_data[unit_name] = [rt_arr.tolist()]

        if len(record.analysis_data.keys()) > 1:
            for index, _exp in enumerate(exp.experiments):
                from_exp(_exp)
        else:
            from_exp(exp)

        batch_obj.backend.save_chip_data_to_db("character.json")
        return origin_sos_data
    except Exception as err:
        pyqlog.error(f"error msg: {err}")
        import traceback

        print(traceback.format_exc())
