# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/17
# __author:       <PERSON><PERSON><PERSON>


import matplotlib.pyplot as plt
import numpy as np

from pyQCat.analysis.curve_analysis import CurveAnalysis
from pyQCat.structures import Options


class DistortionAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:

        options = super()._default_options()

        options.p_all_list = []
        options.x_label = "Delay [ns]"
        options.name = "xy"  # or sweep

        options.pcolormesh_options = {
            "shading": "nearest",
            "cmap": plt.cm.get_cmap('viridis')
        }

        return options

    def _visualization(self):
        super()._visualization()

        if self.options.name == 'sweep':
            base_ax_index = len(self.experiment_data.y_data.keys())
            if self.has_child is True:
                x_arr = self.experiment_data.x_data
                p_all_list = self.options.p_all_list
                y_arr = None
                p_arr = []

                for i, _ in enumerate(x_arr):
                    child_data = self.experiment_data.child_data(index=i)
                    if y_arr is None:
                        y_arr = child_data.x_data

                    for j, tub in enumerate(child_data.y_data.items()):
                        p_label, p_value = tub
                        p_arr.append(p_value)
                        p_all_list[j].append(p_value)
                # p_all_list.append(p_arr)

                for i, new_p_arr in enumerate(p_all_list):
                    ax_index = base_ax_index + i
                    self.drawer.draw_color_map(x_arr, y_arr, np.array(new_p_arr).T,
                                               ax_index=ax_index,
                                               **self.options.pcolormesh_options)

    def _pre_operation(self):
        if self.options.name == 'sweep':
            self.set_options(
                raw_data_format='plot',
                y_label=["MAX Phase", 'Unwrap Phase', 'Phase', 'Phase'],
                subplots=(4, 1),
                sub_title=["Phase", 'Unwrap Phase', 'Phase', "Phase"],
                p_all_list=[[], []]
            )
        else:
            self.set_options(
                raw_data_format='plot',
                y_label=["X", 'Y', 'Phase', "Unwrap Phase"],
                subplots=(4, 1),
                sub_title=["X", 'Y', "Phase", "Unwrap Phase"],
            )

    def run_analysis(self):
        """Run analysis on experiment data."""
        self._pre_operation()
        super().run_analysis()
