# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/09/15
# __author:       <PERSON><PERSON><PERSON>

from __future__ import annotations

import json
import re
from dataclasses import asdict, dataclass, field
from enum import Enum
from typing import Any, Dict, List, Sequence, Union

from ..analysis.algorithms import IQdiscriminator
from ..config import PyqcatConfig
from ..instrument import Instrument
from ..pulse import PulseCorrection
from ..qm_protocol import EnvironmentBitResource
from ..qubit import BaseQubitsType, Coupler, Qubit, QubitPair
from ..structures import QDict
from ..tools.parse_options import parse_options
from ..types import StandardContext


class WorkingType(str, Enum):
    dc = "dc"
    ac = "ac"
    ac_bias = "awg_bias"


class DivideType(str, Enum):
    CHA = "character_idle_point"
    CAL = "calibrate_idle_point"


class CCEReadoutType(str, Enum):
    QC = "Coupler"
    QP = "probeQ"
    QD = "driveQ"
    QH = "QH"
    QL = "QL"


BIT_FORMULA = [
    "abs(dc_max - dc_min) * 2",
    "XYwave.time + XYwave.offset * 2",
    "freq_max - freq_min",
    "drive_freq + anharmonicity",
]


def generate_default_context_data():
    context_data = {}
    for context in StandardContext._value2member_map_:
        context_data[context] = {
            "physical_unit": "",
            "readout_type": "",
            "default": False,
        }
    return context_data


# exp_class_name: str = None
# description: str = None
# ## Meta from Visage: username (str), export_datetime (datetime), visage_version (str), monster_version (str)
# meta_from_visage: dict = None
# ## Chip: sample (str), env_name (str), point_label (str)
# chip: dict = None
@dataclass
class OptionsJson:
    # Meta: username (str), visage_version (str), monster_version (str),
    # chip (dict), exp_class_name (str), export_datetime (str), description (str)
    # chip: sample (str), env_name (str), point_label (str)
    meta: dict = None

    # Context options: name (str), readout_type (str), physical_unit (str)
    context_options: dict = None

    # Options for regular execution: experiment_options (dict), analysis_options (dict)
    options_for_regular_exec: dict = None
    options_for_parallel_exec: dict = None

    __annotations__ = {
        "meta": dict,
        "context_options": dict,
        "options_for_regular_exec": dict,
        "options_for_parallel_exec": dict,
    }

    @classmethod
    def from_json_file(cls, file_path: str) -> "OptionsJson":
        """*.json -> instance of `OptionsJson`"""
        with open(file_path, "r") as f:
            json_data = json.load(f)
        json_data = parse_options(json_data)
        return cls(**json_data)

    def to_json_file(self, file_path: str):
        """Instance of `OptionsJson` -> *.json"""
        with open(file_path, "w") as f:
            json.dump(self.__dict__, f, indent=4)

    @classmethod
    def from_dict(cls, options_json_dict: dict) -> "OptionsJson":
        """Python dict -> instance of `OptionsJson`"""
        return cls(**options_json_dict)

    def to_dict(self) -> dict:
        """instance of `OptionsJson` -> Python dict"""
        return self.__dict__


@dataclass
class ExperimentContext:
    inst: Instrument = None
    qubits: List[Qubit] = field(default_factory=list)
    couplers: List[Coupler] = field(default_factory=list)
    qubit_pair: List[QubitPair] = field(default_factory=list)
    compensates: Dict[BaseQubitsType, PulseCorrection] = None
    discriminators: Union[IQdiscriminator, List[IQdiscriminator]] = None
    working_dc: Dict[str, List] = None
    ac_bias: Dict[str, List] = None
    crosstalk_dict: Dict = field(default_factory=dict)
    config: PyqcatConfig = None
    parallel_component: Dict = field(default_factory=dict)
    online_bits: List[BaseQubitsType] = None
    online_dcms: List[IQdiscriminator] = None
    online_coms: Dict[BaseQubitsType, PulseCorrection] = None
    unit_map: QDict = field(default_factory=QDict)
    env_bit_resource: EnvironmentBitResource = None
    xy_crosstalk_dict: Dict = field(default_factory=dict)
    context_name: str = ""
    read_env_bits: List[BaseQubitsType] = None
    token: str = ""

    __annotations__ = {
        "inst": Instrument,
        "qubits": List[Qubit],
        "couplers": List[Coupler],
        "qubit_pair": List[QubitPair],
        "compensates": Dict[BaseQubitsType, PulseCorrection],
        "discriminators": Union[IQdiscriminator, List[IQdiscriminator]],
        "working_dc": Dict[str, List],
        "ac_bias": Dict[str, List],
        "crosstalk_dict": Dict,
        "config": PyqcatConfig,
        "parallel_component": Dict,
        "online_bits": List[BaseQubitsType],
        "online_dcms": List[IQdiscriminator],
        "online_coms": Dict[BaseQubitsType, PulseCorrection],
        "unit_map": QDict,
        "env_bit_resource": EnvironmentBitResource,
        "xy_crosstalk_dict": Dict,
        "context_name": str,
        "read_env_bits": List[BaseQubitsType],
        "token": str
    }

    @property
    def is_parallel(self):
        if self.parallel_component:
            return True
        else:
            return False

    @property
    def physical_unit_map(self):
        self.collect_base_qubits()
        unit_map = {}
        for qubit in self.qubits:
            unit_map[qubit.name] = qubit

        for coupler in self.couplers:
            unit_map[coupler.name] = coupler

        return unit_map

    def collect_base_qubits(self):
        if self.is_parallel and not self.qubits:
            total_qubits = []
            total_couplers = []
            for ctx in self.parallel_component.values():
                total_qubits.extend(ctx.qubits)
                total_couplers.extend(ctx.couplers)
            self.qubits = list(set(total_qubits))
            self.couplers = list(set(total_couplers))

    def to_ready(self):
        if self.is_parallel:
            for ctx in self.parallel_component.values():
                ctx.working_dc = self.working_dc
                ctx.ac_bias = self.ac_bias
                ctx.context_name = self.context_name

                new_compensates = {}
                for qubit in ctx.qubits:
                    new_compensates[qubit] = self.compensates[qubit]
                for coupler in ctx.couplers:
                    new_compensates[coupler] = self.compensates[coupler]
                ctx.compensates = new_compensates

                # ctx.env_bit_dict = self.env_bit_dict
                if not isinstance(ctx.config, PyqcatConfig):
                    settings_dict = ctx.config.to_dict()
                    ctx.config = PyqcatConfig(settings=settings_dict)

        if not isinstance(self.config, PyqcatConfig):
            settings_dict = self.config.to_dict()
            self.config = PyqcatConfig(settings=settings_dict)


@dataclass
class FakeTaskContext:
    """Monster fake context."""

    qubits: Sequence[str] = None
    couplers: Sequence[str] = None
    pairs: Sequence[str] = None
    discriminators: Sequence[str] = None
    config_field: bool = False
    point_label: int = 1

    __annotations__ = {
        "qubits": Sequence[str],
        "couplers": Sequence[str],
        "pairs": Sequence[str],
        "discriminators": Sequence[str],
        "config_field": bool,
        "point_label": int,
    }

    def to_dict(self):
        return asdict(self)

    def update_records(self, records: dict):
        for key, value in records.items():
            if key in ["IQdiscriminator"]:
                if not self.discriminators:
                    self.discriminators = []
                for discriminator in value.keys():
                    self.discriminators.append(discriminator)
            elif key in ["dc_crosstalk", "Hardware", "Character"]:
                self.config_field = True
            elif re.match(r"q\d+q\d+", key):
                if not self.pairs:
                    self.pairs = []
                self.pairs.append(key)


class FlowState(int, Enum):
    success = 1
    error = 2
    bad = 3


@dataclass()
class ContextOptions:
    name: str = None
    physical_unit: Union[str, List] = None
    readout_type: str = None
    use_parallel: bool = False
    pre_exp: bool = False

    __annotations__ = {
        "name": str,
        "physical_unit": Union[str, List],
        "readout_type": str,
        "use_parallel": bool,
        "pre_exp": bool,
    }

    @classmethod
    def from_dict(cls, data):
        return ContextOptions(
            name=data.get("name"),
            physical_unit=data.get("physical_unit"),
            readout_type=data.get("readout_type"),
            use_parallel=data.get("use_parallel", False),
            pre_exp=data.get("pre_exp", False),
        )

    def to_dict(self):
        self._validate_parallel()
        return asdict(self)

    def _validate_parallel(self):
        if self.name in [StandardContext.CM, StandardContext.URM]:
            self.use_parallel = False
        else:
            if self.pre_exp:
                self.use_parallel = False
            elif isinstance(self.physical_unit, list):
                if len(self.physical_unit) == 1:
                    self.physical_unit = self.physical_unit[0]
                    self.use_parallel = False
                else:
                    self.use_parallel = True
            elif isinstance(self.physical_unit, str):
                if "," in self.physical_unit:
                    self.physical_unit = self.physical_unit.split(",")
                    self.use_parallel = True
                else:
                    self.use_parallel = False
            else:
                pass


@dataclass
class ExpStruct:
    name: str = None
    context_options: ContextOptions = None
    experiment_options: Dict = field(default_factory=list)
    analysis_options: Dict = field(default_factory=list)
    parallel_component: Dict = field(default_factory=dict)

    __annotations__ = {
        "name": str,
        "context_options": ContextOptions,
        "experiment_options": Dict,
        "analysis_options": Dict,
        "parallel_component": Dict,
    }

    @classmethod
    def from_dict(cls, data):
        struct = ExpStruct()
        data = parse_options(data)

        if "context_options" in data:
            struct.context_options = ContextOptions.from_dict(
                data.get("context_options")
            )

        if "experiment_options" in data:
            struct.experiment_options = data.get("experiment_options")

        if "analysis_options" in data:
            struct.analysis_options = data.get("analysis_options")

        return struct


@dataclass
class BatchState:
    state: int = FlowState.success
    execute_map: Dict = field(default_factory=dict)
    message: str = None
    metadata: Dict = field(default_factory=dict)

    x_data: Any = None
    y_data: Dict = field(default_factory=dict)

    __annotations__ = {
        "state": int,
        "execute_map": Dict,
        "message": str,
        "metadata": Dict,
        "x_data": Any,
        "y_data": Dict,
    }

    def to_dict(self):
        return {
            "x": str(self.x_data),
            "y": self.y_data,
            "state": self.state,
            "message": self.message,
            "metadata": self.metadata,
        }

