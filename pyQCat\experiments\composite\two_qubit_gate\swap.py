# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/02/13
# __author:       <PERSON>, <PERSON><PERSON><PERSON>

from copy import deepcopy

import numpy as np

from ....analysis import AnalysisResult, SwapAnalysis
from ....errors import ExperimentFieldError
from ....parameters import options_wrapper, save_scan_map
from ....structures import MetaData, Options
from ....tools import cz_flow_options_adapter, judge_exp_failed
from ....tools.calculator import qubit_pair_detune_prepare
from ...composite_experiment import CompositeExperiment, ExperimentRunMode
from ...single import SwapOnce


@options_wrapper
class Swap(CompositeExperiment):
    _sub_experiment_class = SwapOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("scan_name", str)
        options.set_validator("z_amp_list", list)
        options.set_validator("scope", dict)
        options.set_validator("auto_check", bool)
        options.set_validator("scope_detune", bool)

        options.scan_name = None
        options.z_amp_list = None
        options.auto_check = False
        options.scope = {"l": 30, "r": 30, "p": 31}
        options.scope_detune = False
        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options.

        Analysis Options:
            select_key (str): Select SwapOnce Pxx, mark label process width.
            quality_bounds (Iterable[float]):
                The bounds value of the goodness of fit.
            is_plot (bool): Plot or not analysis fit photo.
            data_key (List[str]): List of mark select data.
        """
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)
        options.set_validator("goal_width", float)
        options.set_validator("var_limit", float)
        options.set_validator("is_check_child_exp", float)

        options.quality_bounds = [0.98, 0.95, 0.85]
        options.goal_width = None
        options.var_limit = 1
        options.data_key = ["freq"]
        options.select_key = ["freq"]
        options.is_check_child_exp = True

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.bad_child_index = []
        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "swap_state": self.experiment_options.child_exp_options.swap_state,
            "label": self.experiment_options.child_exp_options.label,
            "scan_name": self.experiment_options.scan_name,
        }
        metadata.process_meta = {
            "scan_name": self.experiment_options.scan_name,
            "bad_index": self.run_options.bad_child_index,
        }
        return metadata

    def _check_options(self):
        super()._check_options()
        cz_flow_options_adapter(self)

        child_exp = deepcopy(self.child_experiment)
        child_exp._check_options()

        eop = self.experiment_options
        # eop.validate_fields("scan_name")

        pair = self.qubit_pair
        crop = child_exp.run_options
        params = pair.gate_params(eop.child_exp_options.label)

        # auto freq check
        if eop.auto_check:
            if str(eop.child_exp_options.swap_state) == "11":
                fl = params.get(pair.ql).get("freq")
                fh = (
                    fl - crop.qh.anharmonicity
                    if fl
                    else crop.ql.drive_freq - crop.qh.anharmonicity
                )
            else:
                fl = params.get(pair.ql).get("freq")
                fh = fl if fl else crop.ql.drive_freq
            params[pair.qh]["freq"] = fh

        # scope/point check
        if (
            (eop.freq_list is None or len(eop.freq_list) == 0)
            and (eop.z_amp_list is None or len(eop.z_amp_list) == 0)
            and (not self.run_options.scan_map)
        ):
            left = eop.scope.get("l")
            right = eop.scope.get("r")
            point = eop.scope.get("p")

            if left + right < 1:
                v = params[eop.scan_name]["amp"]
                sweep_list = np.linspace(v - left, v + right, point).tolist()
                self.experiment_options.z_amp_list = sweep_list
            else:
                v = params[eop.scan_name]["freq"]
                if v is None:
                    raise ExperimentFieldError(
                        self, f"{eop.scan_name} default freq is None!"
                    )
                sweep_list = np.round(np.linspace(v - left, v + right, point), 3)
                self.experiment_options.freq_list = sweep_list.tolist()
                self._check_options()

        # auto set goal width:
        if (
            eop.scan_name
            and eop.scan_name.startswith("c")
            and self.analysis_options.goal_width is None
        ):
            goal_width = pair.width(eop.child_exp_options.label)
            self.analysis_options.goal_width = goal_width

        # auto bind select key and result name
        self.set_analysis_options(
            select_key=child_exp.analysis_options.data_key[0],
            result_name=self.qubit_pair.name,
        )

        self.set_run_options(
            x_data=self.run_options.x_data, analysis_class=SwapAnalysis
        )

    def _setup_child_experiment(
        self, swap_once_exp: SwapOnce, index: int, width: float
    ):
        swap_once_exp.run_options.index = index
        label = self.experiment_options.child_exp_options.label
        scan_map = self.run_options.scan_map
        max_iter_count = self.run_options.max_iter_count
        gate_params = swap_once_exp.qubit_pair.metadata.std.get(label).params

        tail_name = "scan"
        for unit, collects in scan_map.items():
            if "freq" in collects:
                freq = collects.get("freq")[index]
                gate_params[unit]["freq"] = freq
                tail_name += f" {unit} {freq}MHz"
            else:
                amp = collects.get("amp")[index]
                gate_params[unit]["amp"] = amp
                gate_params[unit]["freq"] = None
                tail_name += f" {unit} {amp}V"
        swap_once_exp.set_parent_file(self, tail_name, index, max_iter_count)

        self._check_simulator_data(swap_once_exp, index)

    def _handle_child_result(self, swap_once_exp: SwapOnce):
        results = swap_once_exp.analysis.results
        swap_once_exp.analysis.provide_for_parent = {
            "freq": results.freq.value,
            "width": results.width.value,
        }
        if self.analysis_options.is_check_child_exp and judge_exp_failed(
            swap_once_exp.analysis.quality
        ):
            self.run_options.bad_child_index.append(swap_once_exp.run_options.index)

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        eop = self.experiment_options
        save_scan_map(self)
        # scope transform
        if self.experiment_options.scope_detune is True:
            result = self.analysis.results.resonance_point
            if result.value:
                freq_map = qubit_pair_detune_prepare(
                    self.qubit_pair,
                    self.qubits,
                    goal_detune=result.value,
                    label=eop.child_exp_options.label,
                    **eop.scope,
                )

                for unit, freq in freq_map.items():
                    ar = AnalysisResult(
                        name=f"{unit}-freq",
                        value=round(freq[0], 3),
                        extra={
                            "path": f"QubitPair.metadata.std.{eop.child_exp_options.label}.params.{unit}.freq",
                            "name": self.qubit_pair.name,
                        },
                    )
                    self.analysis.results[unit] = ar

        elif not judge_exp_failed(self.analysis.quality):
            for key, result in self.analysis.results.items():
                if key == "resonance_point" and eop.scan_name.startswith("q"):
                    pn = "freq" if result.value > 1 else "amp"
                    path = f"QubitPair.metadata.std.{eop.child_exp_options.label}.params.{eop.scan_name}.{pn}"
                    result.extra["path"] = path
                elif (
                    key == "goal_point"
                    and eop.scan_name.startswith("c")
                    and result.value is not None
                ):
                    pn = "freq" if result.value > 1 else "amp"
                    path = f"QubitPair.metadata.std.{eop.child_exp_options.label}.params.{eop.scan_name}.{pn}"
                    result.extra["path"] = path
