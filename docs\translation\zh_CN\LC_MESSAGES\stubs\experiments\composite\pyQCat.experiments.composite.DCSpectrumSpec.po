# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:2
msgid "pyQCat.experiments.composite.DCSpectrumSpec"
msgstr ""

#: of pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
msgstr ""

#: of pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec:1
msgid "DC Spectrum Experiment."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.experiments.composite.DCSpectrumSpec.__init__>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.composite.DCSpectrumSpec.component_experiment>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.composite.DCSpectrumSpec.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.composite.DCSpectrumSpec.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.composite.DCSpectrumSpec.options_table>`\\ "
"\\(\\[mode\\, detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.composite.DCSpectrumSpec.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec.run:1
msgid "Run DC Spectrum Composite Experiment."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.composite.DCSpectrumSpec.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.composite.DCSpectrumSpec.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.composite.DCSpectrumSpec.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.composite.DCSpectrumSpec.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.DCSpectrumSpec.rst:32
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite:1:<autosummary>:1
msgid ""
":py:obj:`analysis "
"<pyQCat.experiments.composite.DCSpectrumSpec.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.composite.DCSpectrumSpec.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.composite.DCSpectrumSpec.child_experiment>`\\"
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.composite.DCSpectrumSpec.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.composite.DCSpectrumSpec.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:26
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:4
msgid "dc_list (List, np.ndarray): Scan Z line dc list."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:6
msgid "rough_freq_list (List): Rough scan qubit frequency."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:8
msgid "rough_threshold (List): Set rough scan qubit frequency"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:9
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:18
msgid "upper and lower threshold."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:10
msgid "rough_step (float): Rough scan step value."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:12
msgid "use_square (True): Qubit Spectrum rough qubit_test use pulse model."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:13
msgid "True use square, False use chirp"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:15
msgid "band_width (float): When use_square is False,"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:15
msgid "set chirp pulse band width."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:17
msgid "fine_threshold (List): Set fine scan qubit frequency"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:19
msgid "fine_step (float): Fine scan step value."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:21
msgid "rabi_widths (List, array): Rabi scan widths."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:23
msgid ""
"f02_bounds (List): Difference of f01, f02 range limit. freq_bounds "
"(List): Frequency range limit."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_analysis_options
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_run_options
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_analysis_options:8
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:28
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_run_options:4
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_analysis_options:1
msgid "Default analysis options for DCSpectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_analysis_options:6
msgid "Options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_analysis_options:6
msgid "freq_list (List, np.ndarray): The frequency calculate"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_analysis_options:5
msgid "by ac spectrum paras and z amp."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_run_options:1
msgid "Default options values for the experiment :meth:`run` method."
msgstr ""

#: of pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._metadata:1
msgid "Set metadata."
msgstr ""

#: of pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.__init__>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`component_experiment "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.component_experiment>`\\"
#~ " \\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.get_qubit_str>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.set_analysis_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`child_experiment "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.run_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.__init__>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`component_experiment "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.component_experiment>`\\"
#~ " \\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.get_qubit_str>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`run <pyQCat.experiments.composite.DCSpectrumSpec.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.set_analysis_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`child_experiment "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec.run_options>`\\"
#~ msgstr ""

