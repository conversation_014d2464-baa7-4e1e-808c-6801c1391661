# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/05/16
# __author:       XW

"""
Optimize FIR response composite experiment.

"""

import traceback
from copy import deepcopy

import numpy as np
from scipy import interpolate, signal
from scipy.fftpack import fft, ifft

from ....analysis import AnalysisResult
from ....analysis.algorithms.smooth import smooth3rd
from ....analysis.fit.fit_models import get_zfunc
from ....analysis.library import Dicarlo2Analysis, DicarloAnalysis
from ....analysis.library.dicarlo_analysis import AvgDicarloAnalysis
from ....log import pyqlog
from ....parameters import get_parameters
from ....qaio_property import QAIO
from ....structures import MetaData, Options
from ....tools.savefile import LocalFile, S3File
from ....tools.utilities import qarange
from ....types import ExperimentRunMode, QualityDescribe
from ...composite_experiment import CompositeExperiment
from ...single import CouplerDistortionAssist, DistortionAssist


def get_response(width_list, func_list, SampRate):
    Width2 = np.max(width_list)
    Width1 = 100
    N1 = int(Width1 / SampRate) + 1
    N2 = int(Width2 / SampRate) + 1
    N = N1 + N2
    NFFT = 2 * N
    Si = np.ones(
        N2,
    )
    S0 = np.zeros(
        N1,
    )
    h_f = np.ones(NFFT, dtype="complex128")
    for Width, func in zip(width_list, func_list):
        t2 = np.linspace(0, Width, int(Width / SampRate) + 1)
        So = func(t2)
        N3 = len(So)
        if N3 < N2:
            So = np.hstack([np.zeros(N1), So, np.ones(N2 - N3) * So[-1]])
        else:
            So = np.hstack([np.zeros(N1), So])
        h = np.hstack([0, np.diff(So)])
        h = h / np.sum(h)
        h_f *= fft(h, NFFT)

    m = len(func_list) - 1
    h = ifft(h_f, NFFT)
    h = np.real(h[m * N1 : N + m * N1])
    h = h / np.sum(h)
    So = np.convolve(h, np.hstack([S0, Si]))
    So = So[N1 : N1 + N]
    So = So[N1:]
    tlist = np.arange(0, Width2 + 0.1 * SampRate, SampRate)
    func = interpolate.interp1d(tlist, So, kind="cubic")

    tlist_h = np.arange(0, tlist[-1] + 0.1 * SampRate, SampRate)
    So = np.hstack([0, func(tlist_h)])
    h = np.diff(So)
    h = h / sum(h)
    h = np.append(h, 0)
    return h


def get_response2(h_list, SampRate):
    width_list = []
    func_list = []
    for h in h_list:
        if len(h) > 0:
            h = h[:-1]
            Si = np.ones_like(h)
            So = signal.lfilter(h, 1, Si)
            width = (len(h) - 1) * SampRate
            tlist = np.arange(0, width + 0.1 * SampRate, SampRate)
            func = interpolate.interp1d(tlist, So, kind="cubic")
            width_list.append(width)
            func_list.append(func)
    return get_response(width_list, func_list, SampRate)


class OptimizeFirDicarlo(CompositeExperiment):
    """Optimize FIR response composite experiment class."""

    _sub_experiment_class = DistortionAssist

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()
        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])

        options.set_validator("fringe", float)
        options.set_validator("iter", int)
        options.set_validator("separation", int)
        options.set_validator("width_list", list)
        options.set_validator("z_amp", (-1, 1, 2))
        options.set_validator("repeat_times", int)
        options.set_validator("separa_num", int)
        options.set_validator("ignore_history", bool)
        options.set_validator("fir_length", float)
        options.set_validator("branch", ["right", "left"])
        options.set_validator("fname", str)
        options.set_validator("sample_step", float)
        options.set_validator("ac_data", list)
        options.set_validator("opti_sigma", bool)

        options.start_time = 2.5
        options.fringe = 50
        options.fir_length = 30
        options.iter = 5
        options.separation = 3000
        options.sample_step = 0.8333
        options.width_list = None
        options.z_amp = 0.13
        options.repeat_times = None
        options.separa_num = 2
        options.repeat_loops = None
        options.fname = None
        options.branch = "right"
        options.ignore_history = True
        options.ac_data = None
        options.opti_sigma = False

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("ac_spectrum_params", list)
        options.set_validator("z_amp", float)
        options.set_validator("drive_freq", float)
        options.set_validator("cali_offset", bool)
        options.set_validator("freq_switch", bool)
        options.set_validator("savgol_win_length", int)
        options.set_validator("smooth_win_length", float)
        options.set_validator("cal_fir_length", bool)
        options.set_validator("k", float)

        options.set_validator("time_point", float)
        options.set_validator("avg_window", int)
        options.set_validator("target_value", float)
        options.set_validator("before_time", float)
        options.set_validator("percentage", float)
        options.set_validator("threshold", float)

        options.ac_spectrum_params = []
        options.z_amp = None
        options.drive_freq = None
        options.cali_offset = True
        options.freq_switch = False
        options.savgol_win_length = 21
        options.smooth_win_length = 11
        options.amp2freq_func = None
        options.freq2amp_func = None
        options.fringe = None
        options.start_index = None
        options.cal_fir_length = True
        options.k = 1.2

        options.time_point = 60
        options.avg_window = 5
        options.target_value = 1
        options.before_time = 10
        options.percentage = 0.01
        options.threshold = 0.01
        # options.save_exp_data = False

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options.
        Options:
        """
        options = super()._default_run_options()
        options.mark_name = None
        options.pre_h_old_list = []
        options.pre_quality = None
        options.child_quality = None
        options.width_list = None
        options.sigma_x = None
        options.sigma_y = None
        options.amp_response_list = None
        options.smooth_z_amp_list = None
        options.z_amp_list = None
        options.meata_name = None
        options.exp_qubit = None
        options.amp_avg_list = None
        options.dat_name = "ACSpectrum.dat"
        return options

    def _check_options(self):
        super()._check_options()
        # self.experiment_options.fringe = abs(self.experiment_options.fringe)
        self.set_analysis_options(
            ac_spectrum_params=self.analysis_options.ac_spectrum_params,
            drive_freq=self.qubits[0].drive_freq,
            fringe=self.experiment_options.fringe,
        )
        if self.experiment_options.fname:
            func1, func2 = get_zfunc(
                fname=self.experiment_options.fname + self.run_options.dat_name,
                branch=self.experiment_options.branch,
            )
        else:
            func1, func2 = get_zfunc(
                ac_data=self.experiment_options.ac_data,
                branch=self.experiment_options.branch,
            )

        self.set_analysis_options(amp2freq_func=func1, freq2amp_func=func2)
        self.set_run_options(exp_qubit=self.qubits[0])

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "z_amp": self.experiment_options.z_amp,
            "width_list": self.run_options.width_list,
            "sigma_x": self.run_options.sigma_x,
            "sigma_y": self.run_options.sigma_y,
            "z_amp_list": self.run_options.z_amp_list,
            "smooth_z_amp_list": self.run_options.smooth_z_amp_list,
            "amp_response_list": self.run_options.amp_response_list,
            "amp_avg_list": self.run_options.amp_avg_list,
        }
        metadata.name = self.run_options.meata_name
        return metadata

    def _update_file_dirs(self, file_path: str, label: str):
        """Update self.file object dirs value."""
        if not label:
            self.file.dirs = file_path
        elif isinstance(self.file, LocalFile):
            self.file.dirs = f"{file_path}{label}\\"
        elif isinstance(self.file, S3File):
            self.file.dirs = f"{file_path}{label}/"

    def _save_curve_analysis_plot(self, save_mark: str = None):
        if save_mark is None:
            save_mark = self.run_options.mark_name
        super()._save_curve_analysis_plot(save_mark=save_mark)

    def _set_dir_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""

        self.analysis.results["z_distortion_sos"] = AnalysisResult(
            name="z_distortion_sos",
            value={"Low_temperature_FIR_tf_filter": self.run_options.pre_h_old_list},
            extra={
                "path": f"Compensate.z_distortion_sos",
                "name": self.run_options.exp_qubit.name,
            },
        )

    async def _run_once(self, h_old_list, count, i):
        z_amp = self.experiment_options.z_amp
        separation = self.experiment_options.separation
        sample_step = self.experiment_options.sample_step
        for i, phi in enumerate([0, np.pi / 2]):
            dis_assist = deepcopy(self.child_experiment)
            for compensate in dis_assist.compensates.values():
                if compensate.name == self.run_options.exp_qubit.name:
                    compensate.z_distortion_sos.update(
                        {"Low_temperature_FIR_tf_filter": h_old_list}
                    )
            file_name = (
                f"Phi={round(phi, 4)}_Yoperation_Zamp={z_amp}_repeat_time_{count}"
            )
            dis_assist.set_parent_file(self, file_name)

            dis_assist.set_experiment_options(
                z_amp=z_amp,
                separation=separation,
                separa_num=self.experiment_options.separa_num,
                sample_step=sample_step,
                phi=phi,
                data_type="I_Q",
                fringe=self.experiment_options.fringe,
            )
            self._check_simulator_data(dis_assist, i)
            await dis_assist.run_experiment()
            # self._experiments.append(dis_assist)
            if self.experiment_options.minimize_mode is True:
                self._minimize_experiments.append(
                    dis_assist.analysis.analysis_program()
                )
            else:
                self._experiments.append(dis_assist)

            if i == 0:
                p1_y = dis_assist.experiment_data.y_data.get("P1")
                sigma_y = 1 - 2 * p1_y
                if self.experiment_options.opti_sigma:
                    sigma_y_mean = np.mean(sigma_y)
                    sigma_y -= sigma_y_mean
                self.set_run_options(sigma_y=sigma_y)
            else:
                p1_x = dis_assist.experiment_data.y_data.get("P1")
                sigma_x = 1 - 2 * p1_x
                if self.experiment_options.opti_sigma:
                    sigma_x_mean = np.mean(sigma_x)
                    sigma_x -= sigma_x_mean
                self.set_run_options(sigma_x=sigma_x)
        self.set_run_options(
            meata_name=f"{self.run_options.exp_qubit.name}-{self.__class__.__name__}-{i}-{count}"
        )
        self._run_analysis([0, np.pi / 2] * (count + 1), DicarloAnalysis)
        child_quality = self.analysis.quality.descriptor
        self.set_run_options(child_quality=child_quality)
        z_amp_list = self.analysis.analysis_datas.get("z_amp_list")
        smooth_z_amp_list = self.analysis.analysis_datas.get("smooth_z_amp_list")
        amp_response_list = self.analysis.analysis_datas.get("amp_response_list")
        self.set_run_options(
            z_amp_list=z_amp_list,
            smooth_z_amp_list=smooth_z_amp_list,
            amp_response_list=amp_response_list,
        )
        self.set_run_options(
            meata_name=f"{self.run_options.exp_qubit.name}-{self.__class__.__name__}-{i}-{count}-nor"
        )
        self._run_analysis([0, np.pi / 2] * (count + 1), Dicarlo2Analysis)

    def _cal_new_amp_response(self, width_list, start_index, amp_avg_list):
        new_width_list = width_list[start_index:] - width_list[start_index]
        new_amp_response_list = amp_avg_list[start_index:]

        index_list = np.logical_and(
            new_amp_response_list < 1.3, new_amp_response_list > 0.7
        )
        abnormal_count = len(new_amp_response_list) - np.sum(index_list)
        new_amp_response_list = new_amp_response_list[index_list]
        new_width_list = new_width_list[index_list]
        new_amp_response_list = smooth3rd(
            new_amp_response_list, self.analysis_options.smooth_win_length
        )

        return new_width_list, new_amp_response_list, abnormal_count

    def _cal_so_res(
        self, new_width_list, new_amp_response_list, abnormal_count, h_old_list, i
    ):
        try:
            func1 = interpolate.interp1d(
                new_width_list, new_amp_response_list, kind="cubic"
            )
        except Exception as err:
            pyqlog.error(f"{self.run_options.exp_qubit.name} interp1d error: {err}")
            raise ValueError(err)
        q_name = self.run_options.exp_qubit.name
        SampRate = round(1 / QAIO.awg_sample_rate, 4)
        tmax = self.experiment_options.fir_length
        if (
            abnormal_count <= 3
            and new_width_list[-1] >= tmax
            and new_width_list[0] == 0
        ):
            tlist_h = np.round(np.arange(0, tmax + 0.1, SampRate), 4)
            So = np.hstack([0, func1(tlist_h)])
            h = np.diff(So)
            h = np.append(h, 0)
            h_old_list.append(h.tolist())
            try:
                h_new = get_response2(h_old_list, SampRate)
            except Exception as err:
                pyqlog.error(f"{q_name} get_response2: {err}")
                raise ValueError(err)

            h_old_list = [h_new.tolist()]
            self.run_options.pre_h_old_list = h_old_list
        else:
            pyqlog.warning(
                f"{q_name}: abnormal response,abnormal points_count:{abnormal_count}, forget this iter{i}"
            )
            h = 1
            if np.any(h_old_list):
                h_new = h_old_list[0]
            else:
                h_new = []

        return h_new, h_old_list, h

    async def _sync_composite_run(self):
        separation = self.experiment_options.separation
        sample_step = self.experiment_options.sample_step
        q_obj = self.run_options.exp_qubit
        q_name = q_obj.name
        origin_file_dirs = self.file.dirs

        try:
            compensate = get_parameters("compensate", q_name)
            h_old_list = compensate.z_distortion_sos["Low_temperature_FIR_tf_filter"]
        except Exception as err:
            pyqlog.error(f"Get {q_name} h_old_list error!\n{traceback.format_exc()}")
            h_old_list = []

        if self.experiment_options.ignore_history:
            h_old_list = []
        for i in range(self.experiment_options.iter):
            mark_info = f"dicarlo-iter{i}"
            self._update_file_dirs(origin_file_dirs, mark_info)
            if np.any(h_old_list):
                self.file.save_data(np.array(h_old_list[0]), name=f"old-h-iter{i}")
            pre_quality = self.run_options.pre_quality
            if pre_quality:
                if pre_quality == QualityDescribe.bad:
                    h_old_list = self.run_options.pre_h_old_list
                else:
                    self.set_run_options(pre_h_old_list=deepcopy(h_old_list))
            width_end = separation / self.experiment_options.separa_num
            if width_end <= separation / 2:
                width_end = width_end
            else:
                width_end = separation / 2
            width_list = np.array(qarange(0, width_end, sample_step))
            self.set_run_options(width_list=width_list)
            start_time = self.experiment_options.start_time
            start_index = np.argmin(np.abs(np.array(width_list) - start_time))
            pyqlog.info(f"{q_name}-start_time:{start_time}, start_index:{start_index}")
            self.set_analysis_options(
                start_index=start_index, z_amp=self.experiment_options.z_amp
            )
            amp_response_list = []
            self._experiments = []
            self._minimize_experiments = []
            for count in range(self.experiment_options.repeat_times):
                self.set_run_options(mark_name=f"dicarlo_first_iter{i}_repeat{count}")
                await self._run_once(h_old_list, count, i)
                amp_list = self.analysis.analysis_datas.amp_response_list
                child_quality = self.run_options.child_quality
                if child_quality is not QualityDescribe.bad:
                    amp_response_list.append(amp_list)
                else:
                    pyqlog.warning(
                        f"{q_name}- dicarlo_iter{i}_repeat{count} quality bad, ignore this data"
                    )
                self.file.save_data(
                    width_list, amp_response_list, name=f"amp_response-repeat{count}"
                )
            if amp_response_list:
                min_length = min(len(sublist) for sublist in amp_response_list)

                # 筛选出所有长度等于最短长度的子列表
                shortest_lists = [
                    sublist
                    for sublist in amp_response_list
                    if len(sublist) == min_length
                ]

                amp_avg_list = np.mean(shortest_lists, axis=0)
            else:
                amp_avg_list = np.hstack([0, np.zeros_like(width_list)[1:]])
            new_width_list, new_amp_response_list, abnormal_count = (
                self._cal_new_amp_response(width_list, start_index, amp_avg_list)
            )
            h_new, h_old_list, h = self._cal_so_res(
                new_width_list,
                new_amp_response_list,
                abnormal_count,
                deepcopy(h_old_list),
                i,
            )

            self.set_run_options(amp_avg_list=amp_avg_list)
            self.set_run_options(
                meata_name=f"{self.run_options.exp_qubit.name}-Avg-{i}"
            )
            self._run_analysis(
                x_data=width_list,
                analysis_class=AvgDicarloAnalysis,
            )
            self.file.save_data(np.array(h), name=f"current-h-iter{i}")
            self.file.save_data(
                new_width_list, new_amp_response_list, name=f"amp_response_ave-iter{i}"
            )
            if np.any(h_new):
                self.file.save_data(np.array(h_new), name=f"new-h-iter{i}")
            quality_value = self.analysis.quality.descriptor
            self.set_run_options(pre_quality=quality_value)

            if quality_value == QualityDescribe.perfect:
                self._set_dir_result_path()
                break


class CouplerOptimizeFirDicarlo(OptimizeFirDicarlo):
    _sub_experiment_class = CouplerDistortionAssist

    def _check_options(self):
        # self.experiment_options.fringe = abs(self.experiment_options.fringe)
        self.set_analysis_options(
            ac_spectrum_params=self.analysis_options.ac_spectrum_params,
            drive_freq=self.qubits[0].drive_freq,
            fringe=self.experiment_options.fringe,
        )
        self.set_run_options(dat_name="ACSpectrumByCoupler.dat")
        super()._check_options()
        self.set_run_options(exp_qubit=self.couplers[0])
