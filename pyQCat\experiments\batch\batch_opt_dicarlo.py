# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/2/14
# __author:       xw

import json
import os
from enum import Enum
from typing import List

import numpy as np
from scipy.optimize import curve_fit

from ...analysis.fit.fit_models import linear
from ...log import pyqlog
from ...structures import QDict
from ...tools.utilities import amp_to_freq, qarange
from ..batch_experiment import BatchExperiment


class SpectrumType(str, Enum):
    AC = "ACSpectrum"
    Dicarlo = "OptimizeFirDicarlo"


def curve_line_fit(x_data, y_data):
    lower_bounds = [-np.inf, -np.inf]
    upper_bounds = [np.inf, np.inf]

    params, covariance = curve_fit(
        linear, x_data, y_data, p0=[0, 0], bounds=(lower_bounds, upper_bounds)
    )

    y_mean = np.mean(y_data)
    y_fit = linear(np.asarray(x_data), *params)
    ssr = np.sum(np.square(y_fit - y_mean))
    sse = np.sum(np.square(y_data - y_fit))
    r_square = np.round(ssr / (ssr + sse), 4)

    return r_square


def objective_function(length: int, x_data, y_data):
    data_length = len(y_data)
    step = int(length // 2)
    fit_data = {}
    for start in range(0, len(x_data) - length + 1, step):
        end = start + length
        if end > data_length:
            return fit_data
        else:
            subset_x = x_data[start:end]
            subset_y = y_data[start:end]
            r_square = curve_line_fit(subset_x, subset_y)
            fit_data.update({r_square: [subset_x, subset_y]})
    return fit_data


def optimize_window_function(
    optimized_length, x_data, y_data, optimized_length_threshold
):
    best_square = 0
    best_ac_data, mean_z_amp = None, None
    while optimized_length > optimized_length_threshold and best_square < 0.95:
        fit_data = objective_function(optimized_length, x_data, y_data)
        if fit_data:
            best_square, max_value = max(fit_data.items(), key=lambda x: x[0])
        if best_square > 0.95:
            best_ac_data = fit_data.get(best_square)
            max_z_amp = max(best_ac_data[0])
            min_z_amp = min(best_ac_data[0])
            mean_z_amp = (max_z_amp + min_z_amp) / 2
        else:
            optimized_length = int(optimized_length // 1.5)
    return best_ac_data, mean_z_amp


def save_text(path: str, text):
    os.makedirs(os.path.dirname(path), exist_ok=True)

    with open(path, "wt", encoding="utf-8") as write_file:
        write_file.write(text)


def parse_ac_json(path: str, length: int, working_units: list):
    with open(path, encoding="utf-8") as f:
        ac_spectrum_data = json.load(f)
        res_dict = dict()
        pass_unit = []
        for key in ac_spectrum_data:
            if "ACSpectrum" in key:
                v = ac_spectrum_data.get(key)
                ana_data = v.get("analysis_data")
                for unit in ana_data:
                    if unit in working_units:
                        actual_amp_list = (
                            ana_data.get(unit).get("result").get("z_amp_list")
                        )
                        f10_list = ana_data.get(unit).get("result").get("f10_list")
                        ac_best_data, mean_z_amp = optimize_window_function(
                            length, actual_amp_list, f10_list
                        )
                        delta_x = actual_amp_list[-1] - actual_amp_list[0]
                        delta_y = f10_list[-1] - f10_list[0]
                        k = delta_y / delta_x
                        if k >= 0:
                            ac_branch = "left"
                        else:
                            ac_branch = "right"
                        pass_unit.append(unit)
                        res_dict.update(
                            {
                                unit: {
                                    "ac_best_data": ac_best_data,
                                    "z_amp": mean_z_amp,
                                    "ac_branch": ac_branch,
                                }
                            }
                        )
    return res_dict, pass_unit


class BatchOptDicarlo(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.parallel_units = None
        options.ac_spec_flows = [SpectrumType.AC]
        options.dcl_spec_flows = [SpectrumType.Dicarlo]
        options.param_path = None
        options.run_ac_flag = False
        options.ac_path = None
        options.single_gate_cali_flow = [
            "SingleShot_0",
            # "XpiDetection",
            # "VoltageDriftGradientCalibration",
            # "QubitFreqCalibration",
            # "AmpOptimize_0",
            # "DetuneCalibration",
            # "AmpOptimize_1",
            # "AmpComposite_1",
            # "AmpComposite_2",
            # "SingleShot_1"
        ]
        options.pre_dicarlo_flow = ["OptimizeFirDicarlo1", "SingleShot_0"]
        options.length = 20
        options.separation = 240
        options.quality_block_exp = ["SingleShot_0", "OptimizeFirDicarlo1"]
        options.optimized_length_threshold = 10

        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.opt_dicarlo_obj = None
        options.z_amp_obj = None
        options.res_obj = QDict()
        options.pass_units = []
        return options

    def _run_batch(self):
        self.pre_operate()

        group_map = self.parallel_allocator_for_qc(self.experiment_options.parallel_units)

        for gn, group in group_map.items():
            pyqlog.info(f"start calibration of {gn}")
            single_gate_cali_flow = self.experiment_options.single_gate_cali_flow
            if single_gate_cali_flow:
                pass_units = self._run_flow(
                    flows=single_gate_cali_flow, physical_units=group, name=gn
                )
                if pass_units:
                    working_unit = self._common_run_func(
                        SpectrumType.AC, pass_units
                    )
                    self._common_run_func(SpectrumType.Dicarlo, working_unit)

    def _auto_divide_amp_for_ac_spectrum(self, working_units):
        ace = self.params_manager.exp_map.get(SpectrumType.AC)
        res_dict = QDict()
        for unit in working_units:
            qubit = self.context_manager.chip_data.get_physical_unit(unit)
            try:
                z_amp_obj = self.run_options.z_amp_obj
                z_amp_list = z_amp_obj.get(unit).get("z_amp")
            except Exception as e:
                raise ValueError(f"invalid params, error msg={e}")
            step = (z_amp_list[2] - z_amp_list[0]) / 30
            actual_amp_list = qarange(z_amp_list[0], z_amp_list[2], step)
            ace.options_for_parallel_exec["experiment_options"]["z_amps"].update(
                {unit: actual_amp_list}
            )

            new_zamp = z_amp_list[0] + qubit.idle_point
            freq = amp_to_freq(qubit, new_zamp)

            z_amp_end = z_amp_list[-1] + qubit.idle_point
            freq_end = amp_to_freq(qubit, z_amp_end)

            delta_x = z_amp_end - new_zamp
            delta_y = freq_end - freq
            k = delta_y / delta_x
            if k >= 0:
                ac_branch = "left"
            else:
                ac_branch = "right"
            res_dict[unit].update({"ac_branch": ac_branch})
            # fringe = -freq + qubit.drive_freq + 200
            fringe = 130  # 不要用采样率的公因数（目前是1.2 GHz）
            ace.options_for_parallel_exec["experiment_options"]["init_fringe"].update(
                {unit: fringe}
            )
        self.set_run_options(res_obj=res_dict)

    def _auto_set_conf_for_dcl(self, working_units, dcl_exp):
        if len(working_units) == 0:
            raise ValueError(f"invalid unit, working_units = {working_units}")
        for unit in working_units:
            res_obj = self.run_options.res_obj
            ac_branch = res_obj.get(unit).get("ac_branch")

            dcl_exp.options_for_parallel_exec["experiment_options"]["branch"].update(
                {unit: ac_branch}
            )

            ac_data = res_obj.get(unit).get("ac_best_data")
            dcl_exp.options_for_parallel_exec["experiment_options"]["ac_data"].update(
                {unit: ac_data}
            )
            z_amp = res_obj.get(unit).get("z_amp")
            dcl_exp.options_for_parallel_exec["experiment_options"]["z_amp"].update(
                {unit: z_amp}
            )

    def _common_run_flow(self, name, flows, working_units):
        pass_units = []
        cur_pass_units = self._run_flow(flows=flows, physical_units=working_units)
        if cur_pass_units:
            pass_units.extend(cur_pass_units)

        fail_units = [unit for unit in working_units if unit not in pass_units]
        pyqlog.log(
            "RESULT",
            f"{name} Result:\npass units: {pass_units}\nfail units: {fail_units}",
        )
        return pass_units

    def _common_run_func(self, name: str, working_units: List[str]):
        ac_spec_flows = self.experiment_options.ac_spec_flows
        dcl_spec_flows = self.experiment_options.dcl_spec_flows
        length = self.experiment_options.length
        pre_dicarlo_flow = self.experiment_options.pre_dicarlo_flow
        if name == SpectrumType.AC and ac_spec_flows:
            self._auto_divide_amp_for_ac_spectrum(working_units)
            flows = self.experiment_options.ac_spec_flows
            if self.experiment_options.run_ac_flag:
                ac_path = self.experiment_options.ac_path
                res_dict, pass_units1 = parse_ac_json(ac_path, length, working_units)
                self.set_run_options(res_obj=res_dict)
                self.set_run_options(pass_units=pass_units1)
                return pass_units1
            elif flows:
                self.set_experiment_options(save_every_exp=False)
                pass_units1 = self._common_run_flow(name, flows, working_units)
                self.set_run_options(pass_units=pass_units1)
                for key, params in self.batch_records.items():
                    if name in key:
                        res_dict = self.run_options.res_obj
                        for unit in pass_units1:
                            if params.analysis_data.get(unit):
                                res = params.analysis_data.get(unit).result
                                actual_amp_list = res.get("z_amp_list")
                                f10_list = res.get("f10_list")
                                ac_best_data, mean_z_amp = optimize_window_function(
                                    length,
                                    actual_amp_list,
                                    f10_list,
                                    self.experiment_options.optimized_length_threshold,
                                )

                                res_dict[unit].update(
                                    {"ac_best_data": ac_best_data, "z_amp": mean_z_amp}
                                )
                        json_str = json.dumps(res_dict, ensure_ascii=False, indent=4)
                        current_directory = os.getcwd()
                        save_text(f"{current_directory}\\res.json", json_str)
                        pyqlog.info(f"ac spectrum best data path={current_directory}")
                        self.set_run_options(res_obj=res_dict)
                return pass_units1
        elif name == SpectrumType.Dicarlo and dcl_spec_flows:
            pass_units = self.run_options.pass_units
            res_dict = self.run_options.res_obj
            # dcl = self.params_manager.exp_map.get(SpectrumType.Dicarlo)
            flows = self.experiment_options.dcl_spec_flows
            pre_pass_units = pass_units

            # calculate fringe Avoid undersampling
            for unit in pre_pass_units:
                qubit = self.context_manager.chip_data.get_physical_unit(unit)
                work_amp = res_dict.get(unit).get("z_amp")
                freq_data = res_dict.get(unit).get("ac_best_data")[1]
                w10 = qubit.drive_freq
                wd = (freq_data[0] + freq_data[-1]) / 2
                # wd = amp_to_freq(qubit, work_amp)
                new_fringe = 130 + wd - w10
                print(f"{unit} new_fringe: {new_fringe},work_amp:{work_amp}")

                self.change_parallel_exec_exp_options(
                    exp_name="OptimizeFirDicarlo",
                    unit=unit,
                    fringe=new_fringe,
                    z_amp=work_amp,
                )
                if pre_dicarlo_flow:
                    self.change_parallel_exec_exp_options(
                        exp_name="OptimizeFirDicarlo1",
                        unit=unit,
                        fringe=new_fringe,
                        z_amp=work_amp,
                    )

            if pre_dicarlo_flow:
                pre_dcl = self.params_manager.exp_map.get(pre_dicarlo_flow[0])
                self._auto_set_conf_for_dcl(pass_units, pre_dcl)
                pre_dcl.options_for_regular_exec["experiment_options"].update(
                    {"separation": self.experiment_options.separation}
                )
                pre_pass_units = self._common_run_flow(
                    name, pre_dicarlo_flow, pass_units
                )
                for key, params in self.batch_records.items():
                    if name in key:
                        for unit in pre_pass_units:
                            if params.analysis_data.get(unit):
                                res = params.analysis_data.get(unit).result
                                fir_length = res.get("fir_length")
                                max_error = res.get("max_error")
                                res_dict[unit].update(
                                    {"fir_length": fir_length, "max_error": max_error}
                                )
                self.set_run_options(res_obj=res_dict)
            if flows:
                res_dict = self.run_options.res_obj
                dcl = self.params_manager.exp_map.get(SpectrumType.Dicarlo)
                self._auto_set_conf_for_dcl(pass_units, dcl)
                pass_unit = []
                self.set_experiment_options(save_every_exp=True)
                for unit in pre_pass_units:
                    fir_length = res_dict.get(unit).get("fir_length")
                    if fir_length:
                        dcl.options_for_parallel_exec["experiment_options"][
                            "fir_length"
                        ].update({unit: fir_length + 10})
                    dcl.options_for_parallel_exec["experiment_options"][
                        "separation"
                    ].update({unit: self.experiment_options.separation})
                self.set_experiment_options(save_every_exp=True)
                pass_units2 = (
                    self._common_run_flow(name, flows, pre_pass_units) + pass_unit
                )

                return pass_units2

    def pre_operate(self):
        qubit_maps = {}
        for unit in self.experiment_options.parallel_units:
            qubit = self.context_manager.chip_data.get_physical_unit(unit)
            idle_point = qubit.idle_point
            dc_max = qubit.dc_max
            dc_min = qubit.dc_min
            mid_point = abs(dc_max - dc_min) / 2
            # dc_max 异侧同分支
            zamp = mid_point + abs(idle_point)
            zamp = zamp if idle_point < 0 else -zamp
            type = 0

            # dc_max 同侧跨分支
            if abs(zamp) < 0.3 and abs(1.1 * zamp + idle_point + dc_max) < 0.45:
                if idle_point < 0:
                    if (dc_max + idle_point) < 0:
                        zamp = mid_point * 3 + abs(idle_point)
                    else:
                        zamp = -(mid_point * 3 + idle_point)
                if idle_point > 0:
                    if (dc_max + idle_point) < 0:
                        zamp = mid_point * 3 - abs(idle_point)
                    else:
                        zamp = -(mid_point * 3 + idle_point)
                type = 2

                if abs(1.1 * zamp + idle_point + dc_max) > 0.45:
                    print(f"{unit}-zamp{zamp} to large")
                    # dc_max 异侧同分支
                    zamp = mid_point + abs(idle_point)
                    zamp = zamp if (idle_point) < 0 else -zamp
                    print(f"{unit}-change to :zamp{zamp}")
                    type = 0

            if abs(1.1 * zamp + idle_point + dc_max) > 0.45:
                print(f"{unit}-zamp{zamp} to large")
                # dc_max 同侧同分支
                zamp = mid_point - abs(idle_point)
                zamp = zamp if idle_point > 0 else -zamp
                print(f"{unit}-change to :zamp{zamp}")
                type = 1

            zamp_left = 0.9 * zamp
            zamp_right = 1.1 * zamp
            qubit_maps.update(
                {unit: {"z_amp": [zamp_left, zamp, zamp_right], "type": type}}
            )
        self.set_run_options(z_amp_obj=qubit_maps)
