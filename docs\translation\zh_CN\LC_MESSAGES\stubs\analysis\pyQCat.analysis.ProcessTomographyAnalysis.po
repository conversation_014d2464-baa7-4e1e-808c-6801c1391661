# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:2
msgid "pyQCat.analysis.ProcessTomographyAnalysis"
msgstr ""

#: of pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.tomography_analysis.TomographyAnalysis`"
msgstr ""

#: of pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis:1
msgid "Analysis for process tomography experiments."
msgstr "用于量子过程层析的分析类"

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:1
msgid "Initialize the analysis object."
msgstr "初始化一个新的分析对象"

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:4
msgid "pyQCat Experiments Data container class."
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:7
msgid "Whether the experiment contains sub-experiments."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.ProcessTomographyAnalysis.__init__>`\\"
" \\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`from_sub_analysis "
"<pyQCat.analysis.ProcessTomographyAnalysis.from_sub_analysis>`\\ "
"\\(x\\_data\\, sub\\_analysis\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:25:<autosummary>:1
msgid "This interface is used for composite experiment."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`run_analysis "
"<pyQCat.analysis.ProcessTomographyAnalysis.run_analysis>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:25:<autosummary>:1
#, fuzzy
msgid "Start analysis."
msgstr "构造分析数据"

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`set_options "
"<pyQCat.analysis.ProcessTomographyAnalysis.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:25:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`show_results "
"<pyQCat.analysis.ProcessTomographyAnalysis.show_results>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:25:<autosummary>:1
msgid "Show analysis results"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.ProcessTomographyAnalysis.rst:27
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_datas "
"<pyQCat.analysis.ProcessTomographyAnalysis.analysis_datas>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis datas."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`drawer <pyQCat.analysis.ProcessTomographyAnalysis.drawer>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid "A short-cut for curve drawer instance."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_data "
"<pyQCat.analysis.ProcessTomographyAnalysis.experiment_data>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid "Return the experiment data for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`has_child "
"<pyQCat.analysis.ProcessTomographyAnalysis.has_child>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis has_child value."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.ProcessTomographyAnalysis.options>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis options for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.ProcessTomographyAnalysis.quality>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis data quality."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`results <pyQCat.analysis.ProcessTomographyAnalysis.results>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1:<autosummary>:1
msgid "Get the result datas."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:1
msgid "Create the Analysis options, and set some fields."
msgstr "创建分析选项，并设置一些属性"

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:3
msgid "**Analysis Options**:"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:5
msgid ""
"**titles (List)** - Subplot title, default is ``[ideal-real, ideal-image,"
" exp-real, exp-image]``."
msgstr ""
"**titles (List)** - 结果子图标题，默认为 ``[ideal-real, ideal-image, exp-real, exp-"
"image]``."

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:8
msgid "**labels (List)** - Axis labels, default is ``[I, X, Y, Z]``."
msgstr "**labels (List)** - 子图轴标签, 默认为 ``[I, X, Y, Z]``."

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:10
msgid ""
"**result_parameters (List)** - Expect to extract the ``exp_chi_matrix``, "
"``ideal_chi_matrix`` , ``fidelity`` and ``process_fidelity``."
msgstr ""
"**result_parameters (List)** - 期望提取 ``exp_chi_matrix``, "
"``ideal_chi_matrix`` , ``fidelity`` 和 ``process_fidelity``."

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options
msgid "Return type"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:13
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options
msgid "Returns"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._default_options:14
#, fuzzy
msgid "Process tomography experiment analysis options."
msgstr "量子过程层析实验分析选项"

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._create_analysis_data:1
msgid "Constructing analysis data."
msgstr "构造分析数据"

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._tomography:1
msgid "Tomography process."
msgstr "层析过程"

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._tomography:3
msgid "We do as follows:"
msgstr "我们这样做的："

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._tomography:5
msgid "calculate ideal chi matrix"
msgstr "首先计算理想门操作的 Chi 矩阵"

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._tomography:6
msgid "calculate exp chi matrix"
msgstr "计算实验测量得到的实际 Chi 矩阵"

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._tomography:7
msgid "calculate fidelity"
msgstr "计算门操作的保真度"

#: of
#: pyQCat.analysis.tomography_analysis.ProcessTomographyAnalysis._cal_fidelity:1
msgid "Calculate CZ fidelity."
msgstr "计算CZ门保真度"

#~ msgid "Bases: :class:`pyQCat.analysis.tomography_analysis.TomographyAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.ProcessTomographyAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_sub_analysis "
#~ "<pyQCat.analysis.ProcessTomographyAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_analysis "
#~ "<pyQCat.analysis.ProcessTomographyAnalysis.run_analysis>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid "Start anlysis."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options "
#~ "<pyQCat.analysis.ProcessTomographyAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_datas "
#~ "<pyQCat.analysis.ProcessTomographyAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ":obj:`drawer <pyQCat.analysis.ProcessTomographyAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_data "
#~ "<pyQCat.analysis.ProcessTomographyAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`has_child "
#~ "<pyQCat.analysis.ProcessTomographyAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.ProcessTomographyAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.ProcessTomographyAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":obj:`results <pyQCat.analysis.ProcessTomographyAnalysis.results>`\\"
#~ msgstr ""

