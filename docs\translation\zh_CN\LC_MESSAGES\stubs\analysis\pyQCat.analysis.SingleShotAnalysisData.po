# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.SingleShotAnalysisData.rst:2
msgid "pyQCat.analysis.SingleShotAnalysisData"
msgstr ""

#: of pyQCat.analysis.specification.SingleShotAnalysisData:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.analysis.specification.SingleShotAnalysisData:1
msgid "A dataclass to store the process of the analysis."
msgstr ""

#: of pyQCat.analysis.specification.SingleShotAnalysisData:5
msgid "some bit IQ data, np.ndarray([I0, Q0, I1, Q1])."
msgstr ""

#: of pyQCat.analysis.specification.SingleShotAnalysisData
msgid "type"
msgstr ""

#: of pyQCat.analysis.specification.SingleShotAnalysisData:7
msgid "numpy.ndarray"
msgstr ""

#: of pyQCat.analysis.specification.SingleShotAnalysisData:11
msgid "IQdiscriminator object, after train."
msgstr ""

#: of pyQCat.analysis.specification.SingleShotAnalysisData:13
msgid "pyQCat.analysis.algorithms.iqprobability.IQdiscriminator"
msgstr ""

#: of pyQCat.analysis.specification.SingleShotAnalysisData:17
msgid "SingleShotQuality object."
msgstr ""

#: of pyQCat.analysis.specification.SingleShotAnalysisData:19
msgid "pyQCat.analysis.quality.single_shot_quality.SingleShotQuality"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.SingleShotAnalysisData.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.SingleShotAnalysisData.rst:21:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.SingleShotAnalysisData.__init__>`\\ "
"\\(\\[source\\_data\\, discriminator\\, quality\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.SingleShotAnalysisData.rst:23
msgid "Attributes"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.SingleShotAnalysisData.rst:28:<autosummary>:1
msgid ""
":py:obj:`discriminator "
"<pyQCat.analysis.SingleShotAnalysisData.discriminator>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.SingleShotAnalysisData.rst:28:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.SingleShotAnalysisData.quality>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.SingleShotAnalysisData.rst:28:<autosummary>:1
msgid ""
":py:obj:`source_data "
"<pyQCat.analysis.SingleShotAnalysisData.source_data>`\\"
msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.SingleShotAnalysisData.__init__>`\\ "
#~ "\\(\\[source\\_data\\, discriminator\\, quality\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`discriminator "
#~ "<pyQCat.analysis.SingleShotAnalysisData.discriminator>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.SingleShotAnalysisData.quality>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`source_data "
#~ "<pyQCat.analysis.SingleShotAnalysisData.source_data>`\\"
#~ msgstr ""

