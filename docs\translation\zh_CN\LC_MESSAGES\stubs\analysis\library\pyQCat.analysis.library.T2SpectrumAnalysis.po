# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:2
msgid "pyQCat.analysis.library.T2SpectrumAnalysis"
msgstr ""

#: of pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.curve_analysis.CurveAnalysis`"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:1
msgid "Initialize the analysis object."
msgstr "初始化新的分析对象"

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:4
msgid "pyQCat Experiments Data container class."
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:7
msgid "Whether the experiment contains sub-experiments."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.analysis.library.T2SpectrumAnalysis.__init__>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`from_sub_analysis "
"<pyQCat.analysis.library.T2SpectrumAnalysis.from_sub_analysis>`\\ "
"\\(x\\_data\\, sub\\_analysis\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:25:<autosummary>:1
msgid "This interface is used for composite experiment."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`run_analysis "
"<pyQCat.analysis.library.T2SpectrumAnalysis.run_analysis>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:25:<autosummary>:1
#, fuzzy
msgid "Run analysis on experiment data."
msgstr "初始化曲线分析数据"

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`set_options "
"<pyQCat.analysis.library.T2SpectrumAnalysis.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:25:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`show_results "
"<pyQCat.analysis.library.T2SpectrumAnalysis.show_results>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:25:<autosummary>:1
msgid "Show analysis results"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T2SpectrumAnalysis.rst:27
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_datas "
"<pyQCat.analysis.library.T2SpectrumAnalysis.analysis_datas>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis datas."
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`data_filter "
"<pyQCat.analysis.library.T2SpectrumAnalysis.data_filter>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "The parameters provided for data filter functions."
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`drawer <pyQCat.analysis.library.T2SpectrumAnalysis.drawer>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "A short-cut for curve drawer instance."
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_data "
"<pyQCat.analysis.library.T2SpectrumAnalysis.experiment_data>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "Return the experiment data for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`has_child "
"<pyQCat.analysis.library.T2SpectrumAnalysis.has_child>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis has_child value."
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.library.T2SpectrumAnalysis.options>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis options for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.library.T2SpectrumAnalysis.quality>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis data quality."
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`results <pyQCat.analysis.library.T2SpectrumAnalysis.results>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "Get the result datas."
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:1
msgid "Create the Analysis options, and set some fields."
msgstr "创建分析选项，并设置一些属性"

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:3
msgid "**Analysis Options**:"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:5
msgid ""
"**freq_list (List, array)** - List of frequencies calculated from the AC "
"spectrum, Used to plot the relationship between qubit frequency and T1."
msgstr "**freq_list (List, array)** - 通过AC谱计算出的频率列表，用来绘制评率和T2之间的关系。"

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:8
msgid ""
"**r_square_threshold (float)** - T1 experiment goodness-of-fit threshold,"
" used to extract abnormal sub-experiments."
msgstr "**r_square_threshold (float)** - T2Ramsey实验的拟合优度，用于提取子实验中异常点"

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:11
msgid ""
"**rate_threshold (float)** - The proportion of the decoherence time `t1` "
"to the maximum scan delay `max_delay` is used to extract abnormal sub-"
"experiments."
msgstr "**rate_threshold (float)** - 退相干时间 `t2` 占最大扫描延时 `max_delay` 的比例，用来提取异常子实验"

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:14
msgid ""
"**subplots (Tuple)** - The layout of the drawing canvas of the "
"experimental results."
msgstr "**subplots (Tuple)** - 实验结果可视化的画布布局"

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:16
msgid ""
"**x_label (List)** - The labels of the X-axis in the resulting graph, "
"corresponding to the number in `subplots`."
msgstr "**x_label (List)** - 结果图的X轴标签，与 ``subplots`` 中的个数对应"

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:19
msgid ""
"**y_label (List)** - The labels of the Y-axis in the resulting graph, "
"corresponding to the number in `subplots`."
msgstr "**y_label (List)** - 结果图的Y轴标签，与 ``subplots`` 中的个数对应"

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:22
msgid "**result_parameters (List)** - Expected Extracted Experimental Results."
msgstr "**result_parameters (List)** - 期望提取的实验结果"

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options
msgid "Return type"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._default_options:25
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._visualization:1
msgid "Visualization of experimental results"
msgstr "实验结果可视化"

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._visualization:3
msgid "We will draw four graphs:"
msgstr "我门将画两张图"

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._visualization:5
msgid "The relationship between `AC` and `T2`;"
msgstr "`AC` 与 `T2` 的关系"

#: of
#: pyQCat.analysis.library.t2_spectrum_analysis.T2SpectrumAnalysis._visualization:6
msgid "The relationship between `Frequncy` and `T2`;"
msgstr "`Frequency` 与 `T2` 的关系"

#~ msgid "Bases: :py:class:`~pyQCat.analysis.curve_analysis.CurveAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_analysis "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.run_analysis>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid "Run analysis."
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_options "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_datas "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`data_filter "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`drawer <pyQCat.analysis.library.T2SpectrumAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_data "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`has_child "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`options "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`quality "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`results "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "Options:"
#~ msgstr ""

#~ msgid "freq_list (List, array): Corresponding to z_amp frequency list."
#~ msgstr ""

#~ msgid "Now no fit model."
#~ msgstr ""

#~ msgid ""
#~ "Extract cavity frequency from twice "
#~ "cavity frequency spectrum experiment data."
#~ msgstr ""

#~ msgid "The basis for selecting data."
#~ msgstr ""

#~ msgid "Quality of fit outcome."
#~ msgstr ""

#~ msgid "Plot z_amp, frequency and T2* relationship."
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.analysis.curve_analysis.CurveAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_analysis "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.run_analysis>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid "Run analysis"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_datas "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`data_filter "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid "The parameters provided for data filter funtions."
#~ msgstr ""

#~ msgid ":obj:`drawer <pyQCat.analysis.library.T2SpectrumAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_data "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`has_child "
#~ "<pyQCat.analysis.library.T2SpectrumAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.library.T2SpectrumAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.library.T2SpectrumAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":obj:`results <pyQCat.analysis.library.T2SpectrumAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "This experiment is currently not being evaluated for quality."
#~ msgstr "此实验上未进行质量评估"

#~ msgid "This experiment does not extract experimental results."
#~ msgstr "此实验不用提取实验结果"

#~ msgid "We do as follows:"
#~ msgstr "我们是这样做的:"

#~ msgid "expeirment results visualization."
#~ msgstr "实验结果可视化"

