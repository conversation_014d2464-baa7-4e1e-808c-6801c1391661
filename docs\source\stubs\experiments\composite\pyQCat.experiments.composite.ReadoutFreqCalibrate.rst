﻿pyQCat.experiments.composite.ReadoutFreqCalibrate
=================================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: ReadoutFreqCalibrate

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~ReadoutFreqCalibrate.__init__
      ~ReadoutFreqCalibrate.component_experiment
      ~ReadoutFreqCalibrate.from_experiment_context
      ~ReadoutFreqCalibrate.get_qubit_str
      ~ReadoutFreqCalibrate.options_table
      ~ReadoutFreqCalibrate.run
      ~ReadoutFreqCalibrate.set_analysis_options
      ~ReadoutFreqCalibrate.set_experiment_options
      ~ReadoutFreqCalibrate.set_parent_file
      ~ReadoutFreqCalibrate.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~ReadoutFreqCalibrate.analysis
      ~ReadoutFreqCalibrate.analysis_options
      ~ReadoutFreqCalibrate.child_experiment
      ~ReadoutFreqCalibrate.experiment_options
      ~ReadoutFreqCalibrate.run_options
   
   