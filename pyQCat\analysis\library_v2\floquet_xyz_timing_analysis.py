# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/12/05
# __author:       <PERSON> Fang

"""
Floquet single qubit XYZ timing analysis.
"""

from ...analysis.standard_curve_analysis import StandardCurveAnalysis
from ...structures import Options


class FloquetXYZTimingOnceAnalysis(StandardCurveAnalysis):
    """FloquetXYZTiming once analysis."""

    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.x_label = "N"

        return options


class FloquetXYZTimingAnalysis(StandardCurveAnalysis):
    """FloquetXYZTiming analysis."""

    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.x_label = "Delay (ns)"
        options.y_label = ""
        options.plot_2d = True

        return options
