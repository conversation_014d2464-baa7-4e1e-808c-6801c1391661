# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/27
# __author:       <PERSON><PERSON><PERSON>

from ....analysis import AnalysisResult, MulInterleavedRBAnalysis
from ....gate import GateCollection
from ....structures import Options
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import RBMultiple


class RBInterleavedMultiple(CompositeExperiment):
    _sub_experiment_class = RBMultiple

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("interleaved_gate", GateCollection.double_gate_infos())
        options.interleaved_gate = "CZ"
        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options."""
        options = super()._default_run_options()

        options.qh = None
        options.ql = None

        return options

    def _check_options(self):
        super()._check_options()
        self.set_analysis_options(result_name=self.qubit_pair.name)
        for qubit in self.qubits:
            if qubit.name == self.qubit_pair.qh:
                self.set_run_options(qh=qubit)
            elif qubit.name == self.qubit_pair.ql:
                self.set_run_options(ql=qubit)
        self.set_run_options(x_data=[0, 1], analysis_class=MulInterleavedRBAnalysis)

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        if self.experiment_options.interleaved_gate == "CZ":
            for key, result in self.analysis.results.items():
                if key == "f":
                    result.extra["path"] = (
                        "QubitPair.metadata.std.fidelity.rb_fidelity"
                    )
                elif key == "f_ave":
                    result.extra["path"] = (
                        "QubitPair.metadata.std.fidelity.rb_reference_fidelity"
                    )
                elif key == "f_int":
                    result.extra["path"] = (
                        "QubitPair.metadata.std.fidelity.rb_cz_fidelity"
                    )
                elif key == "depth":
                    result.extra["path"] = "QubitPair.metadata.std.fidelity.rb_depth"

            qh_rb = self.qh.fidelity
            ql_rb = self.ql.fidelity
            if qh_rb and ql_rb:
                v = self.analysis.results.f.value + 2 - (ql_rb + qh_rb)
                self.analysis.results["relative_fidelity"] = AnalysisResult(
                    name="rb_relative_fidelity",
                    value=v,
                    extra={
                        "path": "QubitPair.metadata.std.fidelity.rb_relative_fidelity",
                        "name": self.qubit_pair.name,
                    },
                )

    def _setup_child_experiment(self, exp: RBMultiple, index: int, vol):
        interleaved_gate = (
            None if index == 0 else self.experiment_options.interleaved_gate
        )
        exp.run_options.index = index
        total = len(self.run_options.x_data)
        describe = f"Interleaved={interleaved_gate}"
        exp.set_parent_file(self, describe, index, total)
        exp.set_experiment_options(interleaved_gate=interleaved_gate)
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp: RBMultiple):
        exp.analysis.provide_for_parent.update({"analysis": exp.analysis})
