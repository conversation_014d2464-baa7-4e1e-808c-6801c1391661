# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/30
# __author:       <PERSON><PERSON><PERSON>

import json
import os
import socket
import time
from typing import Union

from ..protobuf.v1.program_pb2 import ProgramPointer
from ..qm_protocol import ProtocolType


def generate_unique_identity(identity: str):
    identity = (
        str(socket.gethostname()) + str(os.getpid()) + str(time.time()) + identity
    )
    return encode_msg(identity)


def encode_msg(msg: Union[str, dict], mode: str = "utf-8"):
    if isinstance(msg, dict):
        msg = json.dumps(msg)
    return msg.encode(mode)


def decode_msg(msg, mode: str = "utf-8", parse_json: bool = False):
    msg_str = msg.decode(mode)
    if parse_json is True:
        return json.loads(msg_str)
    return msg_str


def heart_msg():
    pointer = ProgramPointer()
    pointer.protocol_type = ProtocolType.HEART
    return pointer.SerializeToString()


def build_ipc_file(name: str):
    ipc_root = os.path.join(
        os.path.dirname(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        ),
        ".pipe",
    )
    os.makedirs(ipc_root, exist_ok=True)
    return f"ipc://{os.path.join(ipc_root, name)}"


def remove_ipc_file(name: str):
    file_path = build_ipc_file(name).split("ipc://")[-1]
    if os.path.exists(file_path):
        os.remove(file_path)
        print(f"remove ipc file {file_path}")
