# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/gate/pyQCat.gate.CPhaseGate.rst:2
msgid "pyQCat.gate.CPhaseGate"
msgstr ""

#: of pyQCat.gate.notable_gate.CPhaseGate:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.CPhaseGate.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.CPhaseGate.rst:24:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.gate.CPhaseGate.__init__>`\\ "
"\\(\\[accumulation\\_phase\\]\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.CPhaseGate.rst:24:<autosummary>:1
msgid ":py:obj:`bind_gate <pyQCat.gate.CPhaseGate.bind_gate>`\\ \\(cz\\_gate\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.CPhaseGate.rst:24:<autosummary>:1
msgid ":py:obj:`get_phase <pyQCat.gate.CPhaseGate.get_phase>`\\ \\(qubit\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.CPhaseGate.rst:24:<autosummary>:1
msgid ":py:obj:`to_pulse <pyQCat.gate.CPhaseGate.to_pulse>`\\ \\(qubit\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.CPhaseGate.rst:26
msgid "Attributes"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.CPhaseGate.rst:31:<autosummary>:1
msgid ":py:obj:`matrix <pyQCat.gate.CPhaseGate.matrix>`\\"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.CPhaseGate.rst:31:<autosummary>:1
msgid ":py:obj:`parking_qubits <pyQCat.gate.CPhaseGate.parking_qubits>`\\"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.CPhaseGate.rst:31:<autosummary>:1
msgid ":py:obj:`width <pyQCat.gate.CPhaseGate.width>`\\"
msgstr ""

