# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/20
# __author:       <PERSON>

from typing import List

import numpy as np

from ..base_experiment import BaseExperiment
from ..batch_experiment import Batch<PERSON>xperiment
from ...executor.batch import (
    divide_qubit_parallel_group,
    divide_coupler_calibration_parallel_group,
    divide_same_lo_baseband_freq,
)
from ...log import pyqlog
from ...qubit import Qubit, Coupler
from ...tools.utilities import get_bound_ac_spectrum, freq_to_amp
from ...types import QualityDescribe


class BatchZZTimingByCouplerXYZ(BatchExperiment):
    """By two times CouplerXYZTimingByZZShift measure Q-C-Q XYZ delay."""

    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()

        options.physical_units = []
        options.qubit_check_flows = ["CavityFreqSpectrum", "RabiScanWidth"]
        options.qubit_dcm_flows = [
            "XpiDetection",
            "SingleShot",
            "QubitFreqCalibration",
            "DetuneCalibration",
            "XpiDetection",
            "ReadoutFreqCalibrate",
            "SingleShot",
        ]
        options.coupler_shift_flows = ["FreqShiftByCoupler"]
        options.coupler_timing_flows = ["CouplerXYZTimingByZZShift"]

        options.step_freq = 20  # QH adjust frequency reduce step 20 MHz
        options.reduce_count = 5  # QH adjust frequency max times
        options.select_zamp_rules = [0.5, 0.6, 0.7]  # Set FreqShiftByCoupler z_amp coefficient list.
        options.shift_freq = 40  # Set FreqShiftByCoupler `delta_freq` value.
        options.z_amp_list = np.linspace(0.15, 0.25, 10)

        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()

        options.qubit_freq_limit_dict = {}  # {"q1": [f_min, f_max], ... }
        options.read_point_dict = {}
        options.idle_point_dict = {}

        options.coupler_zamp_dict = {}
        options.xyz_timing_data = {}
        return options

    def _record_experiment(
        self, exp_name, exp, physical_units, err: Exception = None
    ):  # mark 记录参数
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if exp is None or err:
            return record
        elif isinstance(exp, BaseExperiment):
            exp_list = [exp]
        else:
            exp_list = exp.experiments

        failed_values = [QualityDescribe.abnormal, QualityDescribe.bad]
        if "FreqShiftByCoupler" in exp.label:
            coupler_zamp_dict = self.run_options.coupler_zamp_dict

            for idx, exp_obj in enumerate(exp_list):
                unit = physical_units[idx]
                if (
                    exp_obj.analysis
                    and exp_obj.analysis.quality.descriptor not in failed_values
                ):
                    coupler_zamp_dict[unit] = {
                        "z_amp": exp_obj.experiment_options.z_amp,
                        "delta_freq": exp_obj.experiment_options.delta_freq,
                    }

            self._save_data_to_json(coupler_zamp_dict, "coupler_zamp")

        if "CouplerXYZTimingByZZShift" in exp.label:
            xyz_timing_data = self.run_options.xyz_timing_data
            for idx, exp_obj in enumerate(exp_list):
                unit = physical_units[idx]
                if (
                    exp_obj.analysis
                    and exp_obj.analysis.quality.descriptor not in failed_values
                ):
                    once_dict = xyz_timing_data.get(unit, {})
                    q_name = exp_obj.qubits[0].name
                    once_dict.update(
                        {
                            q_name: {
                                "xy_chanel": exp_obj.analysis.options.link_channel.get("xy"),
                                "z_flux_chanel": exp_obj.analysis.options.link_channel.get("z"),
                                "xy_delay": float(exp_obj.analysis.results.xy_delay.value),
                                "z_delay": float(exp_obj.analysis.results.z_delay.value),
                            }
                        }
                    )
                    xyz_timing_data.update({unit: once_dict})

            self._save_data_to_json(xyz_timing_data, "xyz-timing-data")

        return record

    def _batch_up(self):
        """Batch Pre-Execution Processing."""
        super()._batch_up()

        # 记录 qubit 的 f_min, f_max, 原始 idle, 以及读取工作点 amp
        c_names = self.experiment_options.physical_units
        qubit_freq_limit_dict = self.run_options.qubit_freq_limit_dict
        read_point_dict = self.run_options.read_point_dict
        idle_point_dict = self.run_options.idle_point_dict

        cache_coupler = self.context_manager.chip_data.cache_coupler
        cache_qubit = self.context_manager.chip_data.cache_qubit
        for c_name in c_names:
            coupler_obj: "Coupler" = cache_coupler.get(c_name)
            idle_point_dict[c_name] = coupler_obj.idle_point
            dq_name, pq_name = f"q{coupler_obj.drive_bit}", f"q{coupler_obj.probe_bit}"
            for q_name in [dq_name, pq_name]:
                qubit_obj = cache_qubit.get(q_name)
                freq_max, freq_min = get_bound_ac_spectrum(qubit_obj)
                qubit_freq_limit_dict[q_name] = [round(freq_min, 3), round(freq_max, 3)]
                read_point_dict[q_name] = qubit_obj.readout_point.amp
                idle_point_dict[q_name] = qubit_obj.idle_point

    def _set_qubit_point(self, q_name: str, freq: float) -> "Qubit":
        """Set qubit new point."""
        read_point = self.run_options.read_point_dict.get(q_name)
        idle_point = self.run_options.idle_point_dict.get(q_name)

        qubit_obj: "Qubit" = self.context_manager.chip_data.cache_qubit.get(q_name)
        z_amp = freq_to_amp(qubit_obj, freq)
        qubit_obj.drive_freq = freq
        qubit_obj.idle_point = z_amp
        qubit_obj.readout_point.amp = read_point - (z_amp - idle_point)
        qubit_obj.XYwave.detune_pi = 0
        qubit_obj.XYwave.detune_pi2 = 0

        # Reset bit attribute parameters
        qubit_obj.T1 = 0.0
        qubit_obj.T2 = 0.0
        qubit_obj.TS2 = 0.0
        qubit_obj.fidelity = 0.0
        return qubit_obj

    def _check_high_bit(self, q_names: List[str]) -> List[str]:
        """Check higt bit rabi width and refresh dcm."""
        qubit_check_flows = self.experiment_options.qubit_check_flows
        qubit_dcm_flows = self.experiment_options.qubit_dcm_flows
        step_freq = self.experiment_options.step_freq
        reduce_count = self.experiment_options.reduce_count
        quality_filter = self.experiment_options.quality_filter

        if len(q_names) > 1:
            group_list = divide_qubit_parallel_group(
                q_names,
                self.context_manager.chip_data,
                **self.backend.system.parallel_divide,
            )
        else:
            group_list = [q_names]

        pass_qh_names = []
        for qh_names in group_list:
            count = 0
            physical_units = qh_names
            pass_units = []
            # 检查 RabiScanWidth
            while physical_units and count < reduce_count:
                # if len(physical_units) > 1:
                divide_same_lo_baseband_freq(
                    physical_units, self.context_manager.chip_data, is_force=True
                )
                p_units = self._run_flow(qubit_check_flows, physical_units)
                p_units = p_units or []
                b_units = [q for q in physical_units if q not in p_units]
                physical_units = b_units
                pass_units.extend(p_units)

                count += 1
                for q_name in physical_units:
                    qubit_obj: "Qubit" = self.context_manager.chip_data.cache_qubit.get(
                        q_name
                    )
                    freq = round(qubit_obj.drive_freq - step_freq, 3)
                    self._set_qubit_point(q_name, freq)

            bad_units = [q for q in qh_names if q not in pass_units]
            pyqlog.info(
                f"{qubit_check_flows} physical_units: {qh_names}, "
                f"pass_units: {pass_units}, bad_units: {bad_units}"
            )

            if pass_units:
                # 刷新判据
                if len(pass_units) > 1:
                    divide_same_lo_baseband_freq(
                        pass_units, self.context_manager.chip_data, is_force=True
                    )
                new_pass_units = self._run_flow(qubit_dcm_flows, pass_units)
                new_pass_units = new_pass_units or []
                pyqlog.info(
                    f"{qubit_dcm_flows} physical_units: {qh_names}, "
                    f"pass_units: {new_pass_units}"
                )
                if quality_filter is True:
                    pass_qh_names.extend(new_pass_units)
                else:
                    pass_qh_names.extend(pass_units)
        return pass_qh_names

    def _check_coupler_shift(self, c_names: List[str]) -> List[str]:
        """Check coupler shift by `FreqShiftByCoupler`."""
        pyqlog.log("FLOW", f"Start `FreqShiftByCoupler` c_names: {c_names}")
        coupler_shift_flows = self.experiment_options.coupler_shift_flows
        select_zamp_rules = self.experiment_options.select_zamp_rules

        physical_units = c_names
        pass_c_names = []
        for idx, coefficient in enumerate(select_zamp_rules):
            pyqlog.info(
                f"Select z_amp times: {idx}, coefficient: {coefficient}, "
                f"physical_units: {physical_units}"
            )
            if not physical_units:
                break

            parallel_flag = True if len(physical_units) > 1 else False
            for unit in physical_units:
                coupler = self.context_manager.chip_data.get_physical_unit(unit)
                half_period = abs(coupler.dc_max - coupler.dc_min)

                if coupler.dc_max > 0:
                    ac_branch = "left"
                    z_amp = -half_period * coefficient
                else:
                    ac_branch = "right"
                    z_amp = half_period * coefficient
                pyqlog.info(
                    f"Select z_amp times: {idx} {unit} ac_branch: {ac_branch}, z_amp: {z_amp}"
                )

                if parallel_flag is True:
                    self.change_parallel_exec_exp_options(
                        exp_name="FreqShiftByCoupler",
                        unit=unit,
                        z_amp=z_amp,
                        delta_freq=self.experiment_options.shift_freq,
                        options={"child_exp_options.ac_branch": ac_branch},
                    )
                else:
                    self.change_regular_exec_exp_options(
                        exp_name="FreqShiftByCoupler",
                        z_amp=z_amp,
                        delta_freq=self.experiment_options.shift_freq,
                        options={"child_exp_options.ac_branch": ac_branch},
                    )

            p_units = self._run_flow(coupler_shift_flows, physical_units)
            p_units = p_units or []
            b_units = [q for q in physical_units if q not in p_units]
            physical_units = b_units
            pass_c_names.extend(p_units)

        pyqlog.log("FLOW", f"After `FreqShiftByCoupler` pass_c_names: {pass_c_names}")
        return pass_c_names

    def _measure_coupler_timing(self, c_names: List[str]):
        """Measure coupler Q-C XYZ delay by `CouplerXYZTimingByZZShift`."""
        pyqlog.log("FLOW", f"Start `CouplerXYZTimingByZZShift` c_names: {c_names}")
        coupler_timing_flows = self.experiment_options.coupler_timing_flows
        idle_point_dict = self.run_options.idle_point_dict
        coupler_zamp_dict = self.run_options.coupler_zamp_dict
        if c_names:
            parallel_flag = True if len(c_names) > 1 else False
            for unit in c_names:
                coupler = self.context_manager.chip_data.get_physical_unit(unit)

                coupler.idle_point = idle_point_dict.get(unit, 0.0)
                info_dict = coupler_zamp_dict.get(unit, {})
                z_amp = info_dict.get("z_amp", 0.0)
                z_pulse_params = {
                    "amp": z_amp,
                    "time": 15,
                    "sigma": 0.01,
                    "buffer": 0.05,
                }

                pyqlog.log(
                    "FLOW",
                    f"CouplerXYZTimingByZZShift {unit} idle_point: {coupler.idle_point}, "
                    f"z_amp: {z_amp}, z_pulse_params: {z_pulse_params}",
                )
                if parallel_flag is True:
                    self.change_parallel_exec_exp_options(
                        exp_name="CouplerXYZTimingByZZShift",
                        unit=unit,
                        z_pulse_params=z_pulse_params,
                    )
                else:
                    self.change_regular_exec_exp_options(
                        exp_name="CouplerXYZTimingByZZShift",
                        z_pulse_params=z_pulse_params,
                    )
            #
            p_units = self._run_flow(coupler_timing_flows, c_names)
            pyqlog.log(
                "FLOW", f"After `CouplerXYZTimingByZZShift` pass_c_names: {p_units}"
            )
            return p_units

    def _measure_other_coupler_timing(self, c_names: List[str]):
        """Measure coupler Q-C XYZ delay by `CouplerXYZTimingByZZShift`."""
        pyqlog.log("FLOW", f"Start `CouplerXYZTimingByZZShift` c_names: {c_names}")
        coupler_timing_flows = self.experiment_options.coupler_timing_flows
        z_amp_list = self.experiment_options.z_amp_list
        idle_point_dict = self.run_options.idle_point_dict

        physical_units = c_names
        pass_c_names = []
        for idx, z_amp in enumerate(z_amp_list):
            if physical_units:
                parallel_flag = True if len(physical_units) > 1 else False
                for unit in physical_units:
                    coupler = self.context_manager.chip_data.get_physical_unit(unit)

                    coupler.idle_point = idle_point_dict.get(unit, 0.0)

                    if coupler.dc_max > 0 and z_amp > 0:
                        z_amp = -z_amp
                    elif coupler.dc_max < 0 and z_amp < 0:
                        z_amp = -z_amp

                    z_pulse_params = {
                        "amp": z_amp,
                        "time": 15,
                        "sigma": 0.01,
                        "buffer": 0.05,
                    }

                    pyqlog.log(
                        "FLOW",
                        f"CouplerXYZTimingByZZShift iteration: {idx}, {unit} idle_point: {coupler.idle_point}, "
                        f"z_amp: {z_amp}, z_pulse_params: {z_pulse_params}",
                    )
                    if parallel_flag is True:
                        self.change_parallel_exec_exp_options(
                            exp_name="CouplerXYZTimingByZZShift",
                            unit=unit,
                            z_pulse_params=z_pulse_params,
                        )
                    else:
                        self.change_regular_exec_exp_options(
                            exp_name="CouplerXYZTimingByZZShift",
                            z_pulse_params=z_pulse_params,
                        )
                #
                p_units = self._run_flow(coupler_timing_flows, c_names)
                p_units = p_units or []
                b_units = [c for c in physical_units if c not in p_units]
                pass_c_names.extend(p_units)
                physical_units = b_units
                pyqlog.log(
                    "FLOW",
                    f"After `CouplerXYZTimingByZZShift` iteration: {idx} pass_c_names: {p_units}",
                )

        pyqlog.log(
            "FLOW", f"End `CouplerXYZTimingByZZShift` pass_c_names: {pass_c_names}"
        )

    def _run_once_times(self, c_names: List[str], set_high: str = "driveQ"):
        """Set driveQ or probeQ run flows."""
        pyqlog.log(
            "FLOW", f"Start run_once_times c_names: {c_names} set_high: {set_high}"
        )
        qubit_freq_limit_dict = self.run_options.qubit_freq_limit_dict

        # 1.设置其中一个比特为最高点，另一外一个比特为最低点
        qh_list = []
        qh_names = []
        qh_c_name_map = {}
        for c_name in c_names:
            coupler_obj: "Coupler" = self.context_manager.chip_data.cache_coupler.get(
                c_name
            )
            dq_name, pq_name = f"q{coupler_obj.drive_bit}", f"q{coupler_obj.probe_bit}"
            dq_freq_limit = qubit_freq_limit_dict.get(dq_name)
            pq_freq_limit = qubit_freq_limit_dict.get(pq_name)
            if set_high == "driveQ":
                qh_obj = self._set_qubit_point(dq_name, dq_freq_limit[1])
                ql_obj = self._set_qubit_point(pq_name, pq_freq_limit[0])
            else:
                ql_obj = self._set_qubit_point(dq_name, dq_freq_limit[0])
                qh_obj = self._set_qubit_point(pq_name, pq_freq_limit[1])
            qh_list.append(qh_obj)
            qh_names.append(qh_obj.name)
            qh_c_name_map.update({qh_obj.name: c_name})

        # 2.检查高频比特 RabiScanWidth, 并刷新高频比特的读取判据
        pass_qh_names = self._check_high_bit(qh_names)
        next_c_names = [qh_c_name_map.get(q_name) for q_name in pass_qh_names]
        pyqlog.log(
            "FLOW", f"After `CHECK` pass_qh_names: {pass_qh_names}, c_names {c_names}"
        )

        if len(next_c_names) > 1:
            divide_same_lo_baseband_freq(
                pass_qh_names, self.context_manager.chip_data, is_force=True
            )
            group_list = divide_coupler_calibration_parallel_group(
                next_c_names,
                self.context_manager.chip_data,
                **self.backend.system.parallel_divide,
            )
        else:
            group_list = [next_c_names]

        pyqlog.log(
            "FLOW",
            f"Will execute `NEXT` c_names: {next_c_names}, group_list: {group_list}",
        )
        for group in group_list:
            pass_c_names = self._check_coupler_shift(group)

            # 4. pass coupler 进行 CouplerXYZTimingByZZShift
            pass_c_names2 = self._measure_coupler_timing(pass_c_names)

            bad_c_names = [
                c for c in group if {c not in pass_c_names and c not in pass_c_names2}
            ]

            # 5.bad coupler 进行 CouplerXYZTimingByZZShift
            self._measure_other_coupler_timing(bad_c_names)

    def _run_batch(self):
        """Run batch flows logic."""
        c_names = self.experiment_options.physical_units
        if len(c_names) > 1:
            group_list = divide_coupler_calibration_parallel_group(
                c_names,
                self.context_manager.chip_data,
                **self.backend.system.parallel_divide,
            )
        else:
            group_list = [c_names]
        pyqlog.log(
            "FLOW", f"Initial physical_units: {c_names}, group_list: {group_list}"
        )

        for group in group_list:
            self._run_once_times(group, set_high="driveQ")
            self._run_once_times(group, set_high="probeQ")
