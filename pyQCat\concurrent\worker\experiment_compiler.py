# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/23
# __author:       <PERSON><PERSON><PERSON>

"""
Parallel Merge Utils:

Used to merge parallel sub experiments into large experiments.
"""

import os
import re
import sys
import traceback
import uuid
from collections import defaultdict
from copy import deepcopy
from enum import Enum
from typing import List

from ...proto_utils import program_to_protobuf
from ...pulse.pulse_lib import Constant, PulseComponent
from ...qm_protocol import (
    CommonMessage,
    Component,
    DescribeFile,
    ExperimentFile,
    Program,
    PyLockType,
    SchedulerFile,
    SweepControl,
    ZFluxControl,
)
from ...types import ResourceComponentRole
from .experiment_transformer import ExperimentTransformer


class CompileStatus(str, Enum):
    DONE = "merge jobs have successfully run"
    ERROR = "merge jobs error"


class CompileResult:
    __slots__ = (
        "program_buf",
        "status",
        "message",
        "options",
        "require_id",
        "program_pointer_buf",
        "readout_channels",
        "loop",
        "data_type",
        "shot",
    )

    def __init__(self):
        self.require_id: str = ""
        self.program_buf: bytes = b""
        self.program_pointer_buf: bytes = b""
        self.readout_channels: dict = {}
        self.loop: int = 0
        self.status = None
        self.message = None
        self.options = None
        self.data_type = None
        self.shot = None

    def to_register(self):
        return dict(
            require_id=self.require_id,
            readout_channels=self.readout_channels,
            loop=self.loop,
            data_type=self.data_type,
            shot=self.shot,
        )


class ExperimentCompiler:
    # 2024/07/04: zyc: Lock all resource qubits
    LOCK_ALL_RESOURCE = False

    def __init__(self, child_experiments: List[ExperimentFile], common: CommonMessage):
        # bugfix: zyc 2024/07/05
        # Asynchronous parallel composite experiment, if a sub experiment fails
        # to generate a message body, a placeholder with a seat tag will be sent over,
        # and the message body will be filtered by parallel merging before registration
        valid_experiments = []
        for exp in child_experiments:
            if exp.extra.get("seat", False) is False:
                valid_experiments.append(exp)

        self.child_experiments = valid_experiments
        self.common = deepcopy(common)

        self.result = CompileResult()
        self.program = Program(token_key=common.token, platform=common.platform, task_tag=common.task_tag)

        self._execute_bit_resource = defaultdict(dict)

    @property
    def measure_aio(self):
        return self.experiment.measure_aio

    @property
    def experiment(self) -> ExperimentFile:
        return self.program.experiment_file

    @property
    def describe_file(self) -> DescribeFile:
        return self.program.describe_file

    @property
    def scheduler_file(self) -> SchedulerFile:
        return self.program.scheduler_file

    def _task_label(self):
        if len(self.child_experiments) > 1:
            return f"Parallel{self.child_experiments[0].label}"
        elif len(self.child_experiments) == 1:
            return self.child_experiments[0].label
        else:
            return "UnKnow"

    def _get_bus_from_channel(self, channel: int) -> str:
        unit = self.common.channel_bit_map.get(f"m-{channel}")
        bus = self.common.q_component_resources.get(unit).get("bus")
        return str(bus)

    def _get_lo_from_channel(self, channel: int, mode: str = "xy") -> str:
        unit = self.common.channel_bit_map.get(f"{mode}-{channel}")
        lo = self.common.q_component_resources.get(unit).get(f"{mode}_lo")
        return f"{mode}-{lo}"

    def _validate_and_merge(self):
        """
        1. Verify whether the types of all sub experiments are consistent
        2. Verify whether there are duplicate items in MeasureAIO/SweetControl
        3. Check if the XY/M line matches the local oscillator intermediate frequency
        4. Check if the reading power of the same BUS is consistent
        5. Extract experimental bit information
        6. Extract IF/sampling_idle/channel, etc
        7. Synchronize experimental bit information that does not exist in MeasureAIO
        """

        label_list = []
        status_list = []
        xy_channel_list = []
        z_flux_channel_list = []
        readout_channel_list = []
        sweep_func_list = []
        intermediate_freq_map = {}
        sampling_width_map = {}
        xy_power_dict = {}
        pre_xy_cross_list = []
        same_bus_power_map = defaultdict(list)
        same_bus_unit_map = defaultdict(list)
        same_lo_gap_map = defaultdict(list)
        same_lo_unit_map = defaultdict(list)
        loop_nums = []
        options = {}

        # iterate child experiments, extract important information
        for exp in self.child_experiments:
            options.update(exp.extra.get("options", {}))
            label_list.append(exp.label)
            status_list.append(exp.status)

            # bugfix: update ac bias in child experiment
            if "ac_bias" in exp.extra:
                for unit, value in exp.extra.get("ac_bias").items():
                    self.common.ac_bias[unit][-1] = value

            # bugfix: XYCrossNpiOnce, XYCrossRB adjust to new version.
            if "pre_xy_cross_list" in exp.extra:
                child_pre = exp.extra.get("pre_xy_cross_list", [])
                pre_xy_cross_list.extend(child_pre)

            for ctrl in exp.measure_aio.XY_control:
                unit_name = self.common.channel_bit_map.get(f"xy-{ctrl.channel}")
                lo_num = self._get_lo_from_channel(ctrl.channel)
                xy_channel_list.append(ctrl.channel)
                xy_power_dict[unit_name] = ctrl.pulse_power
                xy_gap = round(ctrl.output_frequency - ctrl.intermediate_frequency, 3)
                same_lo_gap_map[lo_num].append(xy_gap)
                same_lo_unit_map[lo_num].append(unit_name)
                self._execute_bit_resource[unit_name].update(
                    {
                        "name": unit_name,
                        "xy_gap": xy_gap,
                        "drive_freq": ctrl.output_frequency,
                        "xy_baseband_freq": ctrl.intermediate_frequency,
                    }
                )
                self.measure_aio.XY_control.append(ctrl)
            for ctrl in exp.measure_aio.Z_flux_control:
                unit_name = self.common.channel_bit_map.get(f"z-{ctrl.channel}")
                z_flux_channel_list.append(ctrl.channel)
                self.measure_aio.Z_flux_control.append(ctrl)
                self._execute_bit_resource[unit_name].update(
                    {
                        "name": unit_name,
                    }
                )
            for ctrl in exp.measure_aio.Readout_control:
                unit_name = self.common.channel_bit_map.get(f"m-{ctrl.channel}")
                bus_num = self._get_bus_from_channel(ctrl.channel)
                lo_num = self._get_lo_from_channel(ctrl.channel, "m")
                m_gap = round(ctrl.output_frequency - ctrl.intermediate_frequency, 3)
                readout_channel_list.append(ctrl.channel)
                intermediate_freq_map[str(ctrl.channel)] = [ctrl.intermediate_frequency]
                sampling_width_map[str(ctrl.channel)] = ctrl.sampling_width
                same_bus_power_map[bus_num].append(ctrl.pulse_power)
                same_bus_unit_map[bus_num].append(unit_name)
                same_lo_gap_map[lo_num].append(m_gap)
                same_lo_unit_map[lo_num].append(unit_name)
                self._execute_bit_resource[unit_name].update(
                    {
                        "name": unit_name,
                        "m_gap": m_gap,
                        "probe_freq": ctrl.output_frequency,
                        "probe_power": ctrl.pulse_power,
                        "m_baseband_freq": ctrl.intermediate_frequency,
                        "readout_waveform_amp": ctrl.waveform[0].amp_list[0],
                    }
                )
                self.measure_aio.Readout_control.append(ctrl)
            for ctrl in exp.sweep_control:
                loop_nums.append(len(ctrl.points))
                sweep_func_list.append(f"{ctrl.func}-{ctrl.channel}")
                self.experiment.sweep_control.append(ctrl)

        # assert label/status/channel/sweep
        assert len(set(label_list)) == 1, (
            f"Different sub experiment labels participating in parallel, details:\n{label_list}"
        )
        assert len(set(status_list)) == 1, (
            f"Different sub experiment status participating in parallel, details:\n{status_list}"
        )
        assert len(xy_channel_list) == len(set(xy_channel_list)), (
            f"Found duplicate xy channels, details:\n{xy_channel_list}"
        )
        assert len(z_flux_channel_list) == len(set(z_flux_channel_list)), (
            f"Found duplicate z flux channels, details:\n{z_flux_channel_list}"
        )
        assert len(readout_channel_list) == len(set(readout_channel_list)), (
            f"Found duplicate readout channels, details:\n{readout_channel_list}"
        )
        assert len(sweep_func_list) == len(set(sweep_func_list)), (
            f"Found duplicate sweep struct, details:\n{sweep_func_list}"
        )
        assert len(set(loop_nums)) == 1, (
            f"Inconsistent number of loops in sweep control, details:\n{loop_nums}"
        )
        for name, same_bus_powers in same_bus_power_map.items():
            assert len(set(same_bus_powers)) == 1, (
                f"Same bus power validate error, "
                f"details:\nbus: {name}\nvalue: {same_bus_powers}\nunit: {same_bus_unit_map[name]}"
            )
        for name, same_lo_gaps in same_lo_gap_map.items():
            assert len(set(same_lo_gaps)) == 1, (
                f"Same lo validate error, details:\nlo: {name}\nvalue:{same_lo_gaps}\nunit: {same_lo_unit_map[name]}"
            )

        # extend ac working point
        if self.common.working_dc:
            for _, (channel, vol) in self.common.working_dc.items():
                self.measure_aio.add_dc(int(channel), vol)
        elif not self.common.ac_bias:
            PulseComponent.fake = self.experiment.fake_pulse
            z_template_pulse_width_list = None
            readout_measure_delay_map = {}
            
            for read_ctrl in self.measure_aio.Readout_control:
                if read_ctrl.waveform:
                    readout_measure_delay_map[read_ctrl.channel] = deepcopy(read_ctrl.measure_delay)
                    for md_index in read_ctrl.measure_index:
                        readout_measure_delay_map[read_ctrl.channel].append(read_ctrl.waveform[md_index].width)
            
            for sweep_ctrl in self.experiment.sweep_control:
                if "Z_flux_control:waveform" in sweep_ctrl.func:
                    z_template_pulse_width_list = [p.width for p in sweep_ctrl.waveform]
                elif "measure_delay" in sweep_ctrl.func:
                    index = int(sweep_ctrl.func.split(":")[-1])
                    readout_measure_delay_map[sweep_ctrl.channel][index] = max(sweep_ctrl.points)
            
            if not z_template_pulse_width_list:
                max_readout_width = max([sum(measure_delay) for measure_delay in readout_measure_delay_map.values()])
                z_template_pulse_width_list = [max_readout_width]
    
            z_template_length = len(z_template_pulse_width_list)
            
            for bit_obj, _ in self.common.compensate_dict.items():
                if bit_obj.z_flux_channel not in z_flux_channel_list and bit_obj.ac:
                    z_flux_channel_list.append(bit_obj.z_flux_channel)
                    point_pulse_list = []
                    for pw in z_template_pulse_width_list:
                        cur_p = Constant(pw, bit_obj.ac)()
                        cur_p.bit = bit_obj.name
                        point_pulse_list.append(cur_p)
                    z_flux_control = ZFluxControl(
                        channel=bit_obj.z_flux_channel, measure_delay=[0.0],
                    )
                    if z_template_length == 1:
                        z_flux_control.waveform = point_pulse_list
                    else:
                        self.experiment.sweep_control.append(
                            SweepControl(
                                channel=bit_obj.z_flux_channel,
                                func="Z_flux_control:waveform",
                                points=list(range(z_template_length)),
                                waveform=point_pulse_list,
                            )
                        )
                    self.measure_aio.Z_flux_control.append(z_flux_control)

        # set measure_aio pulse period
        self.measure_aio.pulse_period = self.common.pulse_period

        # set experiment task
        self.describe_file.flux_channels = z_flux_channel_list
        self.describe_file.xy_channels = xy_channel_list
        self.describe_file.readout_channels = readout_channel_list
        self.describe_file.IF = intermediate_freq_map
        self.describe_file.sampling_width = sampling_width_map

        # set experiment
        self.experiment.label = self.child_experiments[0].label
        self.experiment.status = self.child_experiments[0].status
        self.experiment.fake_pulse = self.child_experiments[0].fake_pulse
        self.experiment.repeat = self.child_experiments[0].repeat
        self.experiment.xy_power_dict = xy_power_dict
        self.experiment.extra["pre_xy_cross_list"] = pre_xy_cross_list

        # set cache options
        self.result.options = options
        self.result.loop = loop_nums[0]
        self.program.loop = self.result.loop

    def _transformer(self):
        # set some fields
        self.describe_file.exp_name = self._task_label()
        self.describe_file.username = self.common.username
        self.describe_file.file_flag = self.common.file_flag
        self.describe_file.status = self.experiment.status

        # filter z flux channel 0
        self.common.ac_bias = {k: v for k, v in self.common.ac_bias.items() if v[0] != "0"}

        # fake pulse to actual pulse
        transformer = ExperimentTransformer(
            experiment=self.experiment,
            common=self.common,
            channel_bit_map=self.common.channel_bit_map,
        )
        transformer.run()

        # Set `ac_bias` after correcting with ac crosstalk matrix.
        self.describe_file.ac_bias = {key: value for key, value in self.common.ac_bias.values()}

    def _get_expected_time(self):
        """Calculate the expected time to execute the experiment,
        estimated based on the length of the waveform, which will
        be less than the actual execution time.
        """
        pulse_period = self.measure_aio.pulse_period
        repeat = self.experiment.repeat
        loop_count = len(self.experiment.sweep_control[0].points)

        # unit: s
        expected_time = loop_count * repeat * pulse_period * 1e-6
        return round(float(expected_time), 6), pulse_period, repeat, loop_count

    def _build_chimera_data(self):
        # Set scheduling metadata
        expected_time, pulse_period, repeat, loop_count = self._get_expected_time()
        self.scheduler_file.exp_name = self.program.describe_file.exp_name
        self.scheduler_file.username = self.program.describe_file.username
        self.scheduler_file.loop_count = loop_count
        self.scheduler_file.shot = repeat
        self.scheduler_file.pulse_period = pulse_period
        self.scheduler_file.expected_time = expected_time

        # for ac/dc model
        if not self.common.ac_bias:
            self.common.ac_bias = {}
            for bit, resource in self.common.q_component_resources.items():
                self.common.ac_bias[bit] = (resource["z_flux_channel"], 0)

        # set resource components
        resource_components = []
        for bit, resource in self.common.q_component_resources.items():
            if bit in self._execute_bit_resource:
                execute_data = dict(
                    role=ResourceComponentRole.execute.value,
                    awg_bias=self.common.ac_bias[bit][-1],
                )
                execute_data.update(**self._execute_bit_resource[bit])
            else:
                execute_data = dict(
                    awg_bias=self.common.ac_bias[bit][-1],
                    role=ResourceComponentRole.env.value,
                )
            execute_data.update(**resource)
            resource_components.append(Component(**execute_data))

        # set lock type
        if self.LOCK_ALL_RESOURCE:
            self.scheduler_file.lock_type = PyLockType.ALL
        elif "QubitSpectrum" in self.scheduler_file.exp_name:
            self.scheduler_file.lock_type = PyLockType.DA
        elif "CavityFreqSpectrum" in self.scheduler_file.exp_name:
            self.scheduler_file.lock_type = PyLockType.ADDA

        self.scheduler_file.resources = resource_components

    def _build_experiment_task(self):
        self.program.require_id = str(uuid.uuid4())
        self.program.protocol_type = self.common.protocol_type
        program_buf, program_pointer_buf = program_to_protobuf(self.program)
        program_pointer_buf.special_tag = self.common.special_tag
        program_bytes = program_buf.SerializeToString()
        program_pointer_bytes = program_pointer_buf.SerializeToString()
        self.result.program_buf = program_bytes
        self.result.program_pointer_buf = program_pointer_bytes
        self.result.require_id = program_pointer_buf.require_id
        self.result.readout_channels = {
            ctrl.channel: len(ctrl.measure_delay) for ctrl in self.measure_aio.Readout_control
        }
        self.result.shot = self.program.experiment_file.repeat
        self.result.data_type = self.program.describe_file.status

    def _save_program_data(self):
        save_path = None
        for key in sys.argv[1:]:
            if re.match(r"^-sp.*", key):
                if "=" in key:
                    save_path = key.split("=")[-1]
                else:
                    save_path = "Program"
                print(save_path)

        if save_path == "s3":
            from pyQCat.tools import S3Storage

            s3 = S3Storage()
            file_path = f"naga-ParallelCount-{len(self.child_experiments)}/{self.experiment.label}"
            s3.put_object(
                bucket_name="monster-program",
                object_name=f"{file_path}/{self.experiment.label}.program",
                data=self.result.program_buf,
            )
            s3.put_object(
                bucket_name="monster-program",
                object_name=f"{file_path}/{self.experiment.label}.pointer",
                data=self.result.program_pointer_buf,
            )
            print(f"Save program in {file_path}")
        elif save_path:
            data_path = os.path.join(
                save_path,
                "IQMIX",
                "0.23.2",
                "Y3",
                f"{self.experiment.label}-ParallelCount-{len(self.child_experiments)}",
                self.experiment.label,
            )
            os.makedirs(data_path, exist_ok=True)
            with open(os.path.join(data_path, f"{self.experiment.label}.program"), "wb") as file:
                file.write(self.result.program_buf)
            with open(os.path.join(data_path, f"{self.experiment.label}.pointer"), "wb") as file:
                file.write(self.result.program_pointer_buf)
            print(f"Save program in {data_path}")

    def _fix_freq_bit_filter(self):
        """
        When z_flux_channel or z-d c_channel is 0, it is considered a fixed frequency bit
        and is removed during the synthesis of the program
        """
        for exp in self.child_experiments:
            exp.measure_aio.Z_flux_control = [ctrl for ctrl in exp.measure_aio.Z_flux_control if ctrl.channel]
            exp.sweep_control = [ctrl for ctrl in exp.sweep_control if ctrl.channel]

    def run(self):
        try:
            self._fix_freq_bit_filter()
            self._validate_and_merge()
            self._transformer()
            self._build_chimera_data()
            self._build_experiment_task()
            self._save_program_data()
        except Exception:
            self.result.status = CompileStatus.ERROR
            self.result.message = traceback.format_exc()
        else:
            self.result.status = CompileStatus.DONE
        finally:
            del self.child_experiments
            del self.common
            del self.program
            del self._execute_bit_resource


class SQMCCompiler(ExperimentCompiler):
    def _validate_and_merge(self):
        xy_channel_list = []
        z_dc_channel_list = []
        z_flux_channel_list = []
        readout_channel_list = []
        intermediate_freq_map = {}
        sampling_width_map = {}

        loop_nums = []

        assert len(self.child_experiments) == 1, (
            f"SQMC only supports single task compilation, actual find {len(self.child_experiments)}"
        )

        exp = self.child_experiments[0]

        for ctrl in exp.measure_aio.Z_dc_control:
            z_dc_channel_list.append(ctrl.channel)
        for ctrl in exp.measure_aio.XY_control:
            xy_channel_list.append(ctrl.channel)
        for ctrl in exp.measure_aio.Z_flux_control:
            z_flux_channel_list.append(ctrl.channel)
        for ctrl in exp.measure_aio.Readout_control:
            readout_channel_list.append(ctrl.channel)
            intermediate_freq_map[str(ctrl.channel)] = [ctrl.intermediate_frequency]
            sampling_width_map[str(ctrl.channel)] = ctrl.sampling_width
        for ctrl in exp.sweep_control:
            loop_nums.append(len(ctrl.points))

        if loop_nums:
            assert len(set(loop_nums)) == 1, f"Inconsistent number of loops in sweep control, details:\n{loop_nums}"

        # set measure_aio pulse period
        self.measure_aio.pulse_period = self.common.pulse_period

        # set experiment task
        self.describe_file.dc_channels = z_dc_channel_list
        self.describe_file.flux_channels = z_flux_channel_list
        self.describe_file.xy_channels = xy_channel_list
        self.describe_file.readout_channels = readout_channel_list
        self.describe_file.IF = intermediate_freq_map
        self.describe_file.sampling_width = sampling_width_map

        # set experiment
        self.experiment.label = self.child_experiments[0].label
        self.experiment.status = self.child_experiments[0].status
        self.experiment.fake_pulse = self.child_experiments[0].fake_pulse
        self.experiment.repeat = self.child_experiments[0].repeat

        # set cache options
        self.result.loop = loop_nums[0] if loop_nums else 0
        self.program.loop = self.result.loop
        self.program.experiment_file.measure_aio = exp.measure_aio
        self.program.experiment_file.sweep_control = exp.sweep_control

    def _transformer(self):
        self.describe_file.exp_name = self._task_label()
        self.describe_file.username = self.common.username
        self.describe_file.file_flag = self.common.file_flag
        self.describe_file.status = self.experiment.status

    def _build_chimera_data(self):
        pass


def sqmc_program_compiler(experiments: List[ExperimentFile], parallel_cache: CommonMessage):
    """
    concurrent merge experiment, use to merge experiment.
    """
    compiler = SQMCCompiler(experiments, parallel_cache)
    compiler.run()
    return compiler.result
