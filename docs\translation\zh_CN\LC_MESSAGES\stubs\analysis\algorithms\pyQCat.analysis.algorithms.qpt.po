# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.qpt.rst:2
msgid "pyQCat.analysis.algorithms.qpt"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt:1
msgid "Calculate the chi matrix of a quantum process."
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt:3
msgid "array of input density matrices"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt:4
msgid "array of output density matrices"
msgstr ""

#: of pyQCat.analysis.algorithms.tomography.qpt:5
msgid ""
"transformation matrix from init_qpt for the desired operator basis, or "
"key passed to init_qpt under which this basis was saved"
msgstr ""

#~ msgid ""
#~ "rhos - array of input density "
#~ "matrices e_rhos - array of output "
#~ "density matrices"
#~ msgstr ""

#~ msgid "T - transformation matrix from init_qpt for the desired operator"
#~ msgstr ""

#~ msgid "basis, or key passed to init_qpt under which this basis was saved"
#~ msgstr ""

