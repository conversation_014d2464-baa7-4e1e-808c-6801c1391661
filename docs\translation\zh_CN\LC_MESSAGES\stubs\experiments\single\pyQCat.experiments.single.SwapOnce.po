# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:2
msgid "pyQCat.experiments.single.SwapOnce"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce:1
msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce:1
msgid "SwapOnce scan z line pulse width list."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.__init__:1
msgid "Create a new experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.single.SwapOnce.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`acquire_pulse "
"<pyQCat.experiments.single.SwapOnce.acquire_pulse>`\\ "
"\\(acq\\_pulse\\[\\, qubit\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Set measure pulse."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`cal_fidelity "
"<pyQCat.experiments.single.SwapOnce.cal_fidelity>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Calculate the fidelity matrix."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`experiment_info "
"<pyQCat.experiments.single.SwapOnce.experiment_info>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Show experiment informations."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.single.SwapOnce.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.single.SwapOnce.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`jupyter_schedule "
"<pyQCat.experiments.single.SwapOnce.jupyter_schedule>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Show experiment pulses's schedules in Jupyter Notebook."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.single.SwapOnce.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`play_pulse <pyQCat.experiments.single.SwapOnce.play_pulse>`\\ "
"\\(name\\, base\\_qubit\\, pulse\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Play pulse for XY control and Z flux control."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`plot_schedule "
"<pyQCat.experiments.single.SwapOnce.plot_schedule>`\\ "
"\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
"Plot the pulse schedule which is very important to confirm experiment "
"logical correction."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.single.SwapOnce.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
#: of pyQCat.experiments.single.swap_once.SwapOnce.run:1
msgid "Run SwapOnce experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.single.SwapOnce.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.single.SwapOnce.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_multiple_IF "
"<pyQCat.experiments.single.SwapOnce.set_multiple_IF>`\\ \\(\\*IF\\[\\, "
"channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Used for multiple qubits experiment, set mutiple IF."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_multiple_index "
"<pyQCat.experiments.single.SwapOnce.set_multiple_index>`\\ "
"\\(\\*args\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
"Used for multiple qubits experiment, set mutiple index. :type args: "
":py:class:`list` :param args: Setting the start and end values :type "
"channel: :py:class:`int` :param channel: channel settings Usage::     "
">>>experiment = TopExperiment()     >>>experiment.set_multiple_index([0, "
"1500], [100, 900], channel=1)."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.single.SwapOnce.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.single.SwapOnce.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_sweep_order "
"<pyQCat.experiments.single.SwapOnce.set_sweep_order>`\\ "
"\\(order\\_list\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid "Old interface."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:39:<autosummary>:1
msgid ""
":py:obj:`sweep_readout_trigger_delay "
"<pyQCat.experiments.single.SwapOnce.sweep_readout_trigger_delay>`\\ "
"\\(channel\\, sweep\\_delay\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.SwapOnce.rst:41
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.single.SwapOnce.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.single.SwapOnce.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.single.SwapOnce.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`flattop_gaussian_paras "
"<pyQCat.experiments.single.SwapOnce.flattop_gaussian_paras>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`run_options <pyQCat.experiments.single.SwapOnce.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:38
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:4
msgid ""
"swap_state (str): Swap experiment initial state of QH, QL. readout_type "
"(str): Readout type. is_amend (bool): True means use fidelity_matrix "
"amend result."
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:8
msgid ""
"ql_name (str): The low frequency bit name. qh_name (str): The high "
"frequency bit name."
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:11
msgid ""
"scan_buffer (bool): Is or not scan Z Flattop Gaussian Pulse buffer. "
"scan_high_bit (bool): Is or not scan high frequency bit."
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:14
msgid ""
"const_z_amp (float): The set z_amp of no-scan-bit. z_amp (float): The "
"z_amp of scan-bit."
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:17
msgid "fixed_width (float): When scan_buffer is True,"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:18
msgid "the value is Flattop Gaussian Pulse width."
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:19
msgid ""
"tc_list (List, array): List of tc, Z Flattop Gaussian Pulse tc. sigma "
"(float): The value is Flattop Gaussian Pulse sigma. buffer (float): When "
"scan_buffer is False,"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:22
msgid "the value is Flattop Gaussian Pulse buffer."
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:24
msgid "parking_bits (List(str)): List of parking name,"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:25
msgid "normal like: [\"q2\", \"c0\", ...]"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:33
msgid "parking_param_dict (dict):"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:27
msgid "The parking bits parameter of Flattop Gaussian Pulse. normal like: {"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:30
msgid "\"q2\": {\"amp\": 0.4}, \"c0\": {\"amp\": 0.2}, ..."
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:33
msgid "}"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:38
msgid "drag_assign_amp_map (dict): When QH, QL spplied excitation waveform,"
msgstr ""

#: of
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:36
msgid "the map of bit and amp Z Line Flattop Gaussian Pulse."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._default_analysis_options
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options
#: pyQCat.experiments.single.swap_once.SwapOnce._default_run_options
#: pyQCat.experiments.single.swap_once.SwapOnce._metadata
msgid "Return type"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._default_analysis_options:14
#: pyQCat.experiments.single.swap_once.SwapOnce._default_experiment_options:40
#: pyQCat.experiments.single.swap_once.SwapOnce._default_run_options:11
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._default_run_options:1
msgid "Set run experiment operate options."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._default_run_options:9
msgid "Options:"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._default_run_options:4
msgid ""
"QL: Qubit object, which bit name is ql_name. QH: Qubit object, which bit "
"name is qh_name. parking_qubits (List): List of BaseQubit,"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._default_run_options:7
msgid "when name in parking_bits."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._default_analysis_options:1
msgid "Default analysis options."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._default_analysis_options:12
msgid "Analysis Options:"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._default_analysis_options:4
msgid ""
"swap_state (str): Swap experiment initial state of QH, QL. readout_type "
"(str): Readout type. interaction_location (int): Select interaction point"
" number."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._default_analysis_options:8
msgid "quality_bounds (Iterable[float]):"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._default_analysis_options:9
msgid "The bounds value of the goodness of fit."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._default_analysis_options:10
msgid ""
"is_plot (bool): Plot or not analysis fit photo. data_key (List[str]): "
"List of mark select data."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._metadata:1
msgid "Set metadata."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._check_options:1
msgid "Check Options."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._set_xy_pulses:1
#: pyQCat.experiments.single.swap_once.SwapOnce._set_z_pulses:1
msgid "Set SwapOnce experiment XY pulses."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._set_measure_pulses:1
msgid "Set readout pulse."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._update_instrument:1
msgid "Update instrument parameters before running."
msgstr ""

#: of pyQCat.experiments.single.swap_once.SwapOnce._save_swap_once_json:1
msgid "Save swap once json."
msgstr ""

