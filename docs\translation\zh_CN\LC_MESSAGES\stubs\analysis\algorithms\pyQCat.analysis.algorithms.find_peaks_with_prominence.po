# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 19:15+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.find_peaks_with_prominence.rst:2
msgid "pyQCat.analysis.algorithms.find\\_peaks\\_with\\_prominence"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:1
msgid "Adaptive peak-seeking algorithm based on protrusion value."
msgstr "根据突起值自适应寻峰算法"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:4
msgid ""
"This method adds some preprocessing of peak-finding, hoping to make the "
"peak-finding operation easier. The details of the algorithm are as "
"follows:"
msgstr ""
"此方法中加入了一些寻峰的预处理，期望将寻峰操作变得更加简单，算法细节如下："

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:7
msgid ""
"First, it will be determined by the data distribution characteristics "
"that the data is more suitable for peak or valley search;"
msgstr "首先会通过数据分布特征确定数据更适合寻峰还是寻谷；"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:9
msgid ""
"We use the `scipy.signal.find_peaks` method with all default parameters "
"to find all possible peaks, and then calculate the prominence of each "
"peak, denoted as `prominence`;"
msgstr ""
"我们通过全默认参数下的 `scipy.signal.find_peaks` 方法找到所有可能性的峰，"
"然后计算每个峰的突起程度，记为 `prominence`,"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:11
msgid ""
"Select the maximum degree of protrusion as the peak-seeking condition for"
" the next peak-seeking;"
msgstr "选取最大的突起程度，作为下一次寻峰的寻峰条件；"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:12
msgid "Finally, the index and value corresponding to the peak are returned"
msgstr "最终返回峰对应的索引和数值"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:14
msgid "Most of the small peaks can be filtered in this way."
msgstr "以这样的方式可以过滤大多数细小的峰"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:17
msgid "Input signals."
msgstr "输入信号"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:20
msgid ""
"The number of peaks expected to be found, the default is 1, and the peak "
"with the most prominence."
msgstr ""
"期望寻找峰的序号，默认为1，即突起程度最大的峰。"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:24
msgid ""
"Required minimal horizontal distance (>= 1) in samples between "
"neighbouring peaks. Smaller peaks are removed first until the condition "
"is fulfilled for all remaining peaks."
msgstr ""
"相邻峰之间的最小距离（>=1），先移除较小的峰，直到所有剩余峰的条件都满足为止。"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence:29
msgid "The index and peak value corresponding to the peak."
msgstr "峰对应的索引和峰值"

#: of pyQCat.analysis.algorithms.find_peak.find_peaks_with_prominence
msgid "Return type"
msgstr ""

