# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/10/15
# __author:       <PERSON> Fang

"""
Integrate YXY version Z cross case.
ZCrossV1Base support async mode.

"""

import json
import os
import re
from copy import deepcopy

import numpy as np
import pandas as pd

from pyQCat.analysis.library import (
    CrosstalkLinearAnalysis,
    XYCrossRwAnalysis,
    ZCrossDelayAnalysis,
    ZCrossSpinechoAnalysis,
    # ZCrossSEPTMAnalysis
)
from pyQCat.concurrent.worker.analysis_interface import run_analysis_process
from pyQCat.errors import ExperimentOptionsError
from pyQCat.experiments.composite_experiment import CompositeExperiment
from pyQCat.experiments.single.crosstalk.z_cross_v1_once import (
    ZCrossZampOnce,
    ZCrossDelayOnce,
    ZCrossDelayLinearOnce,
    SpinEchoZCrosstalk0nce,
)
from pyQCat.log import pyqlog
from pyQCat.qubit import NAME_PATTERN
from pyQCat.structures import ExperimentData, MetaData, Options, QDict
from pyQCat.tools.savefile import LocalFile, BaseFile
from pyQCat.tools.utilities import format_results
from pyQCat.types import ExperimentRunMode


class ZCrossV1Base(CompositeExperiment):
    """New case ZCross composite experiment."""

    def get_qubit_str(self):
        """Get qubit string."""
        b_name_list = self.run_options.b_name_list
        t_name_list = self.run_options.t_name_list
        b_length = len(b_name_list)
        t_length = len(t_name_list)

        if b_length > 1:
            bias_info = f"{b_name_list[0]}~{b_name_list[-1]}"
        elif b_length == 1:
            bias_info = f"{b_name_list[0]}"
        else:
            bias_info = ""

        if t_length > 1:
            target_info = f"{t_name_list[0]}~{t_name_list[-1]}"
        elif t_length == 1:
            target_info = f"{t_name_list[0]}"
        else:
            target_info = ""

        string = f"B{bias_info}-T{target_info}"
        return string

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options.

        Experiment options:
            bias_name (str): Bias Qubit/Coupler name.
            target_name_list (list): Target qubit name list.
            bz_amp_list_map (dict): Set Bias qubit z_amp list.

        """
        options = super()._default_experiment_options()

        options.set_validator("bias_name_list", list)
        options.set_validator("target_name_list", list)
        options.set_validator("bz_amp_list_map", dict)

        options.bias_name_list = []
        options.target_name_list = []

        # NOTE：每个 bias 对应的 bz_amp_list 同长
        options.bz_amp_list_map = {}

        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.b_name_list = []
        options.t_name_list = []
        options.qc_map = {}  # All Qubit or Coupler object map

        options.cross_z_amp_map = {}  # Note once cross z_amp
        options.extra_data_map = {}  # Note some extra data
        options.analysis_class = XYCrossRwAnalysis
        options.once_analysis_class = None  # Once target analysis class
        options.once_analysis_ylabel = ""
        options.fy_coefficient_map = {}  # fit crosstalk coefficient

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        options.set_validator("n_multiple", float)
        options.set_validator("once_ana_options", dict)

        options.n_multiple = 100
        options.cross_coe_map = {}
        options.cross_trust_map = {}
        options.once_ana_options = {}

        return options

    def _metadata(self) -> MetaData:
        """Set experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {}
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        bias_name_list = self.experiment_options.bias_name_list
        target_name_list = self.experiment_options.target_name_list
        bz_amp_list_map = self.experiment_options.bz_amp_list_map
        figsize = self.analysis_options.figsize
        once_ana_options = self.analysis_options.once_ana_options

        qc_map = {}
        qubit_map = {qubit.name: qubit for qubit in self.qubits}
        coupler_map = {qubit.name: qubit for qubit in self.couplers}
        qc_map.update(qubit_map)
        qc_map.update(coupler_map)

        q_names = list(qubit_map.keys())
        c_names = list(coupler_map.keys())
        all_names = list(qc_map.keys())
        q_pattern = re.compile(NAME_PATTERN.qubit)
        c_pattern = re.compile(NAME_PATTERN.coupler)

        b_name_list = []
        if bias_name_list:
            for b_name in bias_name_list:
                if isinstance(b_name, int):
                    b_name = f"q{b_name}"
                if b_name in all_names:
                    b_name_list.append(b_name)
                else:
                    pyqlog.warning(
                        f"Please check, not found bias {b_name} from qubits and couplers!"
                    )
        else:
            raise ExperimentOptionsError(
                self.label,
                msg=f"Please check, `bias_name_list` is empty!",
                key="bias_name_list",
                value=bias_name_list,
            )

        if target_name_list:
            t_name_list = []
            for t_name in target_name_list:
                if isinstance(t_name, int):
                    t_name = f"q{t_name}"
                if t_name in b_name_list:
                    pyqlog.warning(
                        f"Set target {t_name} in bias_name_list: {b_name_list}"
                    )
                elif t_name in all_names:
                    t_name_list.append(t_name)
                else:
                    pyqlog.warning(
                        f"Please check, not found target {t_name} from qubits and couplers!"
                    )
        else:
            # NOTE: default set just only support Qubit objects.
            t_name_list = [name for name in q_names if name not in b_name_list]

        b_name_list.sort(key=lambda x: int("".join(x.split("-"))[1:]))
        t_name_list.sort(key=lambda x: int("".join(x.split("-"))[1:]))

        check_flag = True
        for t_name in t_name_list:
            if t_name not in all_names:
                pyqlog.error(f"{t_name} not in qubits or couplers: {all_names}")
                if check_flag is True:
                    check_flag = False
        if check_flag is False:
            raise ExperimentOptionsError(
                self.label,
                msg=f"Check target names: {t_name_list} were not in exist environment!",
            )

        # change analysis figsize
        base_len = 10
        row_length = len(list(bz_amp_list_map.values())[0])
        col_length = len(t_name_list)
        if figsize in [[12, 8], (12, 8)]:
            r_ratio = 1
            c_ratio = 1
            if row_length > base_len:
                r_ratio = row_length / base_len
            if col_length > base_len:
                c_ratio = col_length / base_len
            figsize = (int(12 * c_ratio), int(8 * r_ratio))
        pyqlog.info(f"{self.label} analysis figsize: {figsize}")

        once_ana_options.update({"x_label": f"Bias Zamp (V)"})
        self.set_analysis_options(figsize=figsize)
        self.set_run_options(
            x_data=np.array(list(bz_amp_list_map.values())).T,
            b_name_list=b_name_list,
            t_name_list=t_name_list,
            qc_map=qc_map,
        )

    def _alone_save_result(self):
        """Alone save some special result."""
        t_name_list = self.run_options.t_name_list
        cross_coe_map = self.analysis_options.cross_coe_map
        cross_z_amp_map = self.run_options.cross_z_amp_map

        mark_info = f"{self}_coefficient"
        json_str = json.dumps(cross_coe_map, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=mark_info, prefix=".json")

        extra_info = f"{self}_zamp"
        extra_json_str = json.dumps(cross_z_amp_map, ensure_ascii=False, indent=4)
        self.file.save_text(extra_json_str, name=extra_info, prefix=".json")

        index_list = list(list(cross_coe_map.values())[0].keys())
        cross_coe_df = pd.DataFrame(data=cross_coe_map, index=index_list).T
        pyqlog.log("RESULT", f"{t_name_list}{mark_info}:\n{cross_coe_df}")
        if isinstance(self.file, LocalFile):
            csv_name = "".join([self.file.dirs, mark_info, ".csv"])
            dir_path = os.path.dirname(csv_name)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
            cross_coe_df.to_csv(csv_name)

    def _split_once_analysis(self):
        """Split run once analysis."""
        bz_amp_list_map = self.experiment_options.bz_amp_list_map
        b_name_list = self.run_options.b_name_list
        t_name_list = self.run_options.t_name_list
        qc_map = self.run_options.qc_map
        cross_z_amp_map = self.run_options.cross_z_amp_map
        extra_data_map = self.run_options.extra_data_map
        once_analysis_class = self.run_options.once_analysis_class
        y_label = self.run_options.once_analysis_ylabel
        once_ana_options = self.run_options.once_ana_options

        extra_data_map = {
            key: extra_data_map.get(key)
            for key in sorted(extra_data_map.keys(), key=lambda x: int(x))
        }

        bias_name = f"{b_name_list[0]}~{b_name_list[-1]}"
        bz_amp_list = list(bz_amp_list_map.values())[0]
        metadata = self._metadata()
        y_data_key = "bias_v"
        for t_name in t_name_list:
            tq_obj = qc_map.get(t_name)

            sub_title = []
            bq_ac_list = []
            tq_ac_fit_list = []
            tq_ac_array = []
            z0_array = []
            z1_array = []
            for bz_amp_str, t_ed_dict in extra_data_map.items():
                st_exp_data = t_ed_dict.get(t_name)
                if isinstance(st_exp_data, ExperimentData):
                    bz_amp_index = float(bz_amp_str)
                    fit_tz_amp_map = cross_z_amp_map.get(bz_amp_str, {})
                    fit_tz_amp = fit_tz_amp_map.get(t_name, 0.0)
                    if not sub_title:
                        sub_title = list(st_exp_data.y_data.keys())[:2]
                    x_arr = st_exp_data.x_data
                    z0_arr, z1_arr, *_ = st_exp_data.y_data.values()

                    bq_ac_list.append(bz_amp_index)
                    tq_ac_fit_list.append(fit_tz_amp)
                    tq_ac_array.append(x_arr)
                    z0_array.append(z0_arr)
                    z1_array.append(z1_arr)
                else:
                    pyqlog.warning(
                        f"B{bias_name} Z{bz_amp_str} {t_name} experiment_data: {st_exp_data}"
                    )
            sy_data = {y_data_key: np.array(tq_ac_fit_list)}

            analysis_params = QDict()
            analysis_params.bq_name = b_name_list
            analysis_params.tq_name = t_name
            analysis_params.tq_ac_fit_list = tq_ac_fit_list
            analysis_params.tq_ac_array = tq_ac_array
            analysis_params.z0_array = z0_array
            analysis_params.z1_array = z1_array
            analysis_params.drive_freq = tq_obj.drive_freq

            if len(b_name_list) == 1:
                analysis_params.bq_ac_list = bz_amp_list
            else:
                analysis_params.bq_ac_list = bq_ac_list

            meta_dict = MetaData(
                name=f"{self.label} {t_name}",
                qubits=metadata.qubits,
                couplers=metadata.couplers,
                save_location=metadata.save_location,
                draw_meta={"target name": t_name},
                process_meta={"analysis_params": analysis_params},
            )
            if metadata.draw_meta:
                meta_dict.draw_meta.update(metadata.draw_meta)
            if metadata.process_meta:
                meta_dict.process_meta.update(metadata.process_meta)

            t_exp_data = ExperimentData(
                x_data=np.array(analysis_params.bq_ac_list),
                y_data=sy_data,
                experiment_id=self.id,
                metadata=meta_dict,
            )

            t_file: "BaseFile" = deepcopy(self.file)
            t_file.file_extension(
                f"split{os.sep}{t_name}",
                "",
                "",
                self.experiment_options.is_sub_merge,
            )

            new_y_label = f"Target {t_name} {y_label}"
            t_ana_options = Options(**once_ana_options)
            t_ana_options.y_label = new_y_label
            t_ana_options.sub_title = sub_title
            t_ana_options.result_name = f"B{bias_name}T{t_name}"
            t_ana_options.pure_exp_mode = False

            analysis_obj = run_analysis_process(
                once_analysis_class,
                t_exp_data,
                t_ana_options,
                t_file,
            )
            pyqlog.log("RESULT", format_results(analysis_obj))

            fy_coef = analysis_obj.results.get("coefficient") or analysis_obj.results.get("k")
            fy_coef_val = float(fy_coef.value) if hasattr(fy_coef, "value") else 0.0
            self.run_options.fy_coefficient_map.update({t_name: fy_coef_val})

        pyqlog.info(f"fy_coefficient_map: {self.run_options.fy_coefficient_map}")
        mark_info = f"B{bias_name}_finally_coefficient"
        json_str = json.dumps(self.run_options.fy_coefficient_map, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=mark_info, prefix=".json")

    def _setup_child_experiment(self, exp: "ZCrossZampOnce", index: int, bz_amp_list: list):
        bz_amp_list_map = self.experiment_options.bz_amp_list_map
        b_name_list = self.run_options.b_name_list
        t_name_list = self.run_options.t_name_list

        bz_amp_map = {
            b_name: bz_amp_list_map.get(b_name)[index]
            for b_name in b_name_list
        }

        exp.run_options.index = index
        exp.set_experiment_options(
            bias_name_list=b_name_list,
            target_name_list=t_name_list,
            bz_amp_map=bz_amp_map,
        )

        length = len(list(bz_amp_list_map.values())[0])
        description = f"Bias idx{index}"
        exp.set_parent_file(self, description, index, length)
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp: "ZCrossZampOnce"):
        s_cross_coe_map = exp.analysis_options.cross_coe_map
        s_cross_trust_map = exp.analysis_options.cross_trust_map
        s_cross_z_amp_map = exp.run_options.cross_z_amp_map
        s_extra_data_map = exp.run_options.extra_data_map

        self.analysis_options.cross_coe_map.update(s_cross_coe_map)
        self.analysis_options.cross_trust_map.update(s_cross_trust_map)
        self.run_options.cross_z_amp_map.update(s_cross_z_amp_map)
        self.run_options.extra_data_map.update(s_extra_data_map)
        # freq = exp.analysis.results.freq.value
        # exp.analysis.provide_for_parent.update({"fosc": freq})

    def _create_composite_experiment_data(self, x_data):
        # filer error child experiment
        x_data = list(x_data)

        # # Choose whether to extract the minimum experimental information
        # if self.experiment_options.minimize_mode is True:
        #     sub_analysis_list = self._extrct_mini_child_analysis_data(x_data)
        # else:
        #     sub_analysis_list = self._extrct_child_analysis_data(x_data)

        # create experiment data
        experiment_data = ExperimentData(np.asarray(x_data), {}, self.id, self._metadata())

        # record analysis options in meta process meta
        experiment_data.metadata.process_meta["analysis_options"] = (
            self.analysis_options
        )

        return experiment_data

    async def _async_run_analysis(self):
        await super()._async_run_analysis()
        self._split_once_analysis()


class ZCrossZamp(ZCrossV1Base):
    """Sub experiment scan target z_amp."""

    _sub_experiment_class = ZCrossZampOnce

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.once_analysis_class = CrosstalkLinearAnalysis
        options.once_analysis_ylabel = "Zamp (V)"

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        options.once_ana_options = {
            "fit_type": "linear",  # "linear" or "anal"
            "popt": [0.01, 0.001],
        }

        return options


class ZCrossDelay(ZCrossV1Base):
    """Sub experiment scan target z_amp."""

    _sub_experiment_class = ZCrossDelayOnce

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.once_analysis_class = ZCrossDelayAnalysis
        options.once_analysis_ylabel = "Delay (ns)"

        return options


class ZCrossDelayLiner(ZCrossDelay):
    """Sub experiment scan target z_amp."""

    _sub_experiment_class = ZCrossDelayLinearOnce


class ZCrossSpinEcho(ZCrossDelay):
    _sub_experiment_class = SpinEchoZCrosstalk0nce

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.once_analysis_class = ZCrossSpinechoAnalysis
        options.once_analysis_ylabel = "Delay (ns)"

        return options

# class ZCrossSEPTM(ZCrossV1Base):
#     _sub_experiment_class = SEPTMZCrosstalkOnce
#     @classmethod
#     def _default_run_options(cls) -> Options:
#         """Set default run_options."""
#         options = super()._default_run_options()
#
#         options.once_analysis_class = ZCrossSEPTMAnalysis
#         options.once_analysis_ylabel = "Phase"
#
#         return options
#
#     def _split_once_analysis(self):
#         """Split run once analysis."""
#         bias_name = self.experiment_options.bias_name
#         t_name_list = self.run_options.t_name_list
#         qc_map = self.run_options.qc_map
#         cross_z_amp_map = self.run_options.cross_z_amp_map
#         extra_data_map = self.run_options.extra_data_map
#         once_analysis_class = self.run_options.once_analysis_class
#         y_label = self.run_options.once_analysis_ylabel
#         once_ana_options = self.run_options.once_ana_options
#         fy_coefficient_map = self.run_options.fy_coefficient_map
#
#         y_data_key = "bias_v"
#         sub_title = []
#         for t_name in t_name_list:
#             mark_info = f"B{bias_name}-split{os.sep}{t_name}"
#             tq_obj = qc_map.get(t_name)
#
#             bq_ac_list = []
#             tq_ac_fit_list = []
#             for bz_amp_str, t_ed_dict in extra_data_map.items():
#                 st_exp_data = t_ed_dict.get(t_name)
#                 if isinstance(st_exp_data, ExperimentData):
#                     bz_amp = float(bz_amp_str)
#                     fit_tz_amp_map = cross_z_amp_map.get(bz_amp_str, {})
#                     fit_tz_amp = fit_tz_amp_map.get(t_name, 0.0)
#
#                     bq_ac_list.append(bz_amp)
#                     tq_ac_fit_list.append(fit_tz_amp)
#                 else:
#                     pyqlog.warning(
#                         f"B{bias_name} Z{bz_amp_str} {t_name} experiment_data: {st_exp_data}"
#                     )
#             sy_data = {y_data_key: np.array(tq_ac_fit_list)}
#
#             analysis_params = QDict()
#             analysis_params.bq_name = bias_name
#             analysis_params.tq_name = t_name
#             analysis_params.bq_ac_list = bq_ac_list
#             analysis_params.tq_ac_fit_list = tq_ac_fit_list
#             analysis_params.drive_freq = tq_obj.drive_freq
#
#             meta_dict = self._metadata()
#             meta_dict.draw_meta.update({"target name": t_name})
#             meta_dict.process_meta = {"analysis_params": analysis_params}
#
#             t_exp_data = ExperimentData(
#                 x_data=np.array(bq_ac_list),
#                 y_data=sy_data,
#                 experiment_id=self.id,
#                 metadata=meta_dict,
#             )
#
#             new_y_label = f"Target {t_name} {y_label}"
#             once_ana_options.update(
#                 {
#                     "y_label": new_y_label,
#                     "sub_title": sub_title,
#                 }
#             )
#
#             analysis_obj = SpiltMixin.run_once_analysis(
#                 t_exp_data,
#                 once_analysis_class,
#                 once_ana_options,
#                 has_child=True,
#                 show_result=True,
#             )
#             analysis_obj.options.result_name = f"B{bias_name}T{t_name}"
#             SpiltMixin.save_once_analysis_figure(
#                 analysis_obj, self.file, mark=mark_info, record_text=True
#             )
#             fy_coef = analysis_obj.results.get("coefficient") or analysis_obj.results.get("k")
#             fy_coefficient_map.update({t_name: float(fy_coef.value)})
#         pyqlog.info(f"fy_coefficient_map: {fy_coefficient_map}")
#         mark_info = f"B{bias_name}_finally_coefficient"
#         json_str = json.dumps(fy_coefficient_map, ensure_ascii=False, indent=4)
#         self.file.save_text(json_str, name=mark_info, prefix=".json")
