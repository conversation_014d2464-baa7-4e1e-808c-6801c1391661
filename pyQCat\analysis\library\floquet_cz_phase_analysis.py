# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/07/31
# __author:       SS Fang, ZS

"""Floquet calibrate CZ phase experiment analysis."""

from typing import Any, <PERSON>ple

import numpy as np

from scipy.fft import fft, fftfreq
from scipy.signal import find_peaks

from pyQCat.log import pyqlog
from pyQCat.types import QualityDescribe
from pyQCat.structures import Options
from pyQCat.analysis.curve_analysis import CurveAnalysis
from pyQCat.analysis.specification import ParameterRepr
from pyQCat.analysis.quality.base_quality import BaseQuality


class FloquetCaliCZphaseAnalysis(CurveAnalysis):
    """Floquet calibrate CZ phase experiment analysis.
    Set QubitPair.accumulation_phase value.
    """

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.subplots = (3, 1)
        options.x_label = ["N", "Frequency (Hz)", "Frequency (Hz)"]
        options.y_label = ["P00", "Amplitude", "Amplitude"]

        options.padding_factor = 200  # 增加分辨率
        options.reduce_value = 0.25  # 减去直流分量值
        options.dz = 0.8 * np.pi  # 猜测初值
        options.dp = 0.0  # 移动初值

        options.changing_phase = 0.2 * np.pi
        options.freq_tolerance = 0.1  # 容差

        # No set.
        options.p_label = ""
        options.origin_fft_result = {}
        options.reduce_fft_result = {}

        options.result_parameters = [
            ParameterRepr(
                name="phase",
                repr="accumulation_phase",
                unit="",
                param_path="QubitPair.accumulation_phase"
            ),
        ]

        return options

    def _evaluate_quality(self) -> Tuple[str, Any]:
        """Evaluates the quality of the fit based on the fit result.

        Returns:
            The goodness of fit.
        """
        data_key = ""
        quality = BaseQuality.instantiate(QualityDescribe.normal)
        self._quality = quality
        return data_key, quality

    def _guess_freq(self):
        """Guess initial frequency."""
        dz = self.options.dz
        dp = self.options.dp

        freq = (dz - dp) / (2 * np.pi)
        return freq

    @staticmethod
    def _closest_find(a, b, c):
        distance_a = abs(a - c)
        distance_b = abs(b - c)

        if distance_a < distance_b:
            return a
        elif distance_b < distance_a:
            return b
        else:
            return a

    def _fft_process(self, arr, d: float = 1.0, mode: str = ""):
        """FFT process function."""
        changing_phase = self.options.changing_phase
        padding_factor = self.options.padding_factor

        N = len(arr)
        # print(arr)
        arr_padded = np.pad(arr, (0, N * (padding_factor - 1)), "constant")
        length = len(arr_padded)
        select_idx = length // 2

        yf_padded = np.abs(fft(arr_padded)[:select_idx])
        xf_padded = fftfreq(length, d)[:select_idx]

        initial_freq_guess = self._guess_freq()  # 粗略初值
        freq_tolerance = self.options.freq_tolerance  # 容差

        # idx = np.where(
        #     (xf_padded >= initial_freq_guess - freq_tolerance)
        #     & (xf_padded <= initial_freq_guess + freq_tolerance)
        # )
        # peak_freq = float(xf_padded[idx][np.argmax(np.abs(yf_padded[idx]))])

        peaks, _ = find_peaks(np.abs(yf_padded[:select_idx]))
        peak_values = yf_padded[:select_idx][peaks]

        # 创建一个元组列表，每个元素包含峰值的高度和其在原始数据中的索引
        peaks_with_heights = [(np.abs(height), idx) for idx, height in zip(peaks, peak_values)]

        # 根据高度对这些元组进行排序，高度相同的峰值按照他们在原始数据中的顺序排序
        sorted_peaks = sorted(peaks_with_heights, key=lambda x: (-x[0], x[1]))

        # 提取最高的两个峰值的原始数据索引
        top_two_peaks_original_indices = [idx for _, idx in sorted_peaks[:2]]

        peak_freq1 = xf_padded[top_two_peaks_original_indices[0]]
        peak_freq2 = xf_padded[top_two_peaks_original_indices[1]]
        peak_freq =self._closest_find(peak_freq1, peak_freq2, initial_freq_guess)
        phase = 2 * np.pi * peak_freq + changing_phase

        pyqlog.info(
            f"{mode} data FFT, changing_phase: {changing_phase}, "
            f"peak_freq: {peak_freq}, phase: {phase}"
        )

        res_dict = {
            "xf_padded": xf_padded,
            "yf_padded": yf_padded,
            "peak_freq": peak_freq,
            "phase": phase,
        }
        return res_dict

    def _extract_result(self, data_key: str):
        """Calculate result.

        Args:
            data_key (str): The basis for selecting data.
        """
        reduce_value = self.options.reduce_value

        x_arr = self.experiment_data.x_data
        y_data = self.experiment_data.y_data

        p_label = "P00" if "P00" in y_data else "P0"
        p_arr = y_data.get(p_label)
        T = x_arr[1] - x_arr[0]

        length = len(p_arr)
        if length > 10:
            mean_value = np.mean(p_arr[::-1][:10])
            reduce_arr = p_arr - mean_value
        else:
            reduce_arr = p_arr - reduce_value

        origin_fft_result = self._fft_process(p_arr, d=T, mode="origin")
        reduce_fft_result = self._fft_process(reduce_arr, d=T, mode="reduce")

        # Use reduce fft result extract phase value.
        phase = reduce_fft_result.get("phase", 0.0)

        self.options.p_label = p_label
        self.options.origin_fft_result = origin_fft_result
        self.options.reduce_fft_result = reduce_fft_result
        self.results.phase.value = phase

    def _visualization(self):
        """Plot."""
        # Set plot title.
        self.drawer.set_options(title=self._description())

        p_label = self.options.p_label
        x_arr = self.experiment_data.x_data
        y_data = self.experiment_data.y_data
        p_arr = y_data.get(p_label)
        origin_fft_result = self.options.origin_fft_result
        reduce_fft_result = self.options.reduce_fft_result

        self.drawer.draw_raw_data(x_arr, p_arr, ax_index=0)

        for idx, result_dict in enumerate([origin_fft_result, reduce_fft_result]):
            ax_index = idx + 1
            self.drawer.draw_raw_data(
                x_data=result_dict.get("xf_padded"),
                y_data=result_dict.get("yf_padded"),
                ax_index=ax_index,
            )
            self.drawer.draw_axv_line(
                x=result_dict.get("peak_freq"), ax_index=ax_index, c="g", linestyle="--"
            )

        # Finalize plot.
        self.drawer.format_canvas()