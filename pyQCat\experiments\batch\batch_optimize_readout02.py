# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/31
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

import json
from pathlib import Path

from ...log import pyqlog
from ...structures import QDict
from ..batch_experiment import BatchExperiment


class BatchOptimizeReadout02(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.physical_units = None

        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.f12_record = {}

        return options

    def _batch_up(self):
        super()._batch_up()
        working_qubits = self.experiment_options.physical_units
        point_map = {
            "is_pass": False,
            "error_reason": None,
            "error_exp": None,
            "qubit_params": None,
            "Fidelity02": {"fidelity": None, "outlier": None},
        }
        for q_name in working_qubits:
            self.run_options.f12_record[q_name] = QDict(**point_map)

    def _run_batch(self):
        working_qubits = self._check_baseband_freq()
        if working_qubits:
            self._run_flow(
                physical_units=working_qubits, flows=self.experiment_options.flows
            )
            self._record_point_state()

    def _batch_down(self):
        super()._batch_down()

        if not self.experiment_options.use_simulator:
            for unit, params in self.run_options.f12_record.items():
                if params.is_pass is True:
                    qubit = self.context_manager.chip_data.get_physical_unit(unit)
                    qubit.save_data()

    def _record_experiment(self, exp_name, exp, physical_units, err: Exception = None):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if exp_name in ["SingleShot_02"]:
            for unit in record.analysis_data.keys():
                point_state = self.run_options.f12_record.get(unit)
                point_state.qubit_params = (
                    self.context_manager.chip_data.cache_qubit.get(unit).to_dict()
                )
                for field in ["fidelity", "outlier"]:
                    point_state.Fidelity02[field] = (
                        record.analysis_data.get(unit)
                        .get("result")
                        .get("dcm")
                        .get(field)
                    )
                point_state.is_pass = True
        else:
            if record.bad_units:
                for unit in record.bad_units:
                    point_state = self.run_options.f12_record.get(unit)
                    point_state.error_reason = record.fail_reason
                    point_state.error_exp = exp_name
                    point_state.qubit_params = (
                        self.context_manager.chip_data.cache_qubit.get(unit).to_dict()
                    )

        return record

    def _check_baseband_freq(self):
        working_qubits = []
        for qubit_name in self.experiment_options.physical_units:
            qubit = self.backend.context_manager.chip_data.cache_qubit.get(qubit_name)
            if qubit.XYwave.baseband_freq - 270 > 800:
                working_qubits.append(qubit.name)
            else:
                pyqlog.warning(
                    f"{qubit_name} baseband freq too larger ({qubit.XYwave.baseband_freq})"
                )
                self.run_options.f12_record[
                    qubit_name
                ].error_reason = "baseband frequency limit"
        return working_qubits

    def _record_point_state(self):
        record_path = str(
            Path(Path(self.run_options.record_path).parent, "f12_record.json")
        )
        with open(record_path, mode="w", encoding="utf-8") as fp:
            json.dump(self.run_options.f12_record, fp, indent=4, ensure_ascii=False)
