# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/05/27
# __author:       <PERSON><PERSON><PERSON> Shi

import numpy as np

from .base_quality import BaseQuality, QualityDescribe


class GoodnessofFit(BaseQuality):
    def __init__(self, perfect, normal, abnormal):
        super().__init__()

        self._perfect = perfect
        self._normal = normal
        self._abnormal = abnormal

        self._r_square = None

    def __repr__(self):
        return f"R\u00b2={self._r_square}({self.descriptor})"

    @property
    def value(self):
        return self._r_square

    def evaluate(self, y, y_fit):
        y_mean = np.mean(y)
        ssr = np.sum(np.square(y_fit - y_mean))
        sse = np.sum(np.square(y - y_fit))
        self._r_square = np.round(ssr / ((ssr + sse) or 1), 4)

        # judge quality.
        if self._r_square >= self._perfect:
            self._quality = QualityDescribe.perfect
        elif self._r_square >= self._normal:
            self._quality = QualityDescribe.normal
        elif self._r_square >= self._abnormal:
            self._quality = QualityDescribe.abnormal
        else:
            self._quality = QualityDescribe.bad
            
    def to_dict(self):
        return dict(descriptor=self.descriptor, r2=self.value)
