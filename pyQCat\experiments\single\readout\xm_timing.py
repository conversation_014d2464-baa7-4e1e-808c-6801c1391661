# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/06/28
# __author:       <PERSON><PERSON><PERSON>

import copy

from ....acquisition.acq_tackle import QubitMeasureMode
from ....analysis import XMTimingAnalysis
from ....errors import ExperimentFieldError
from ....pulse.pulse_function import Constant, pi_pulse
from ....structures import Options
from ....tools import qarange
from ...top_experiment_v1 import TopExperimentV1


class XMTiming(TopExperimentV1):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("delays", list, limit_null=True)
        options.set_validator("one_m_offset", float)
        options.set_validator("scope", dict)
        options.delays = None
        options.one_m_offset = 300
        options.scope = {"l": 100, "r": 100, "s": 2}
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("extract_mode", ["fit_params", "min_point"])
        options.extract_mode = "fit_params"
        options.const_delay = 3100
        options.data_key = [1]
        options.raw_data_format = "plot"
        return options

    def _check_options(self):
        super()._check_options()
        self.run_options.measure_modes = [
            QubitMeasureMode(
                name=self.qubit.name,
                measure_total=2,
                measure_num_list=[-1],
                base_label_list=["x"],
            )
        ]

        self.set_experiment_options(data_type="I_Q")
        self.run_options.auto_set_readout_delay = False

        if self.qubit.Mwave.name != "AcquireClear":
            raise ExperimentFieldError(self, f"Mwave must be AcquireClear!")

        goal_time = self.qubit.Mwave.width + self.qubit.Mwave.tkick
        self.set_analysis_options(const_delay=goal_time)

        if not self.experiment_options.delays:
            left = self.experiment_options.scope.get("l")
            right = self.experiment_options.scope.get("r")
            step = self.experiment_options.scope.get("s")
            delays = qarange(goal_time - left, goal_time + right, step)
            self.experiment_options.delays = delays

        self.set_run_options(
            x_data=self.experiment_options.delays,
            analysis_class=XMTimingAnalysis,
        )

    @staticmethod
    def set_xy_pulses(builder):
        qubit = builder.qubit
        delays = builder.experiment_options.delays
        one_m_offset = builder.experiment_options.one_m_offset
        mwave_width = qubit.Mwave.MWidth
        total_width = mwave_width + one_m_offset
        pi_p = pi_pulse(qubit)()

        pulse_list = []
        for delay in delays:
            tail_width = total_width - delay - pi_p.width
            pulse = (
                Constant(delay, 0, "XY")()
                + copy.deepcopy(pi_p)
                + Constant(tail_width, 0, "XY")()
            )
            pulse_list.append(pulse)

        builder.play_pulse("XY", qubit, pulse_list)

    @staticmethod
    def update_instrument(builder):
        qubit = builder.qubit
        mwave_width = qubit.Mwave.MWidth
        one_m_offset = builder.experiment_options.one_m_offset
        readout_raw_delay = builder.inst.get_trigger_delay(
            "Readout_control", qubit.readout_channel
        )[0]

        trigger_delay1 = readout_raw_delay
        trigger_delay2 = one_m_offset
        sample_delay1 = readout_raw_delay + qubit.sample_delay
        sample_delay2 = mwave_width - qubit.sample_width + one_m_offset

        builder.inst.set_trigger_delay(
            "Readout_control", qubit.readout_channel, trigger_delay1, trigger_delay2
        )
        builder.inst.set_sample_delay(
            qubit.readout_channel, sample_delay1, sample_delay2
        )
        builder.inst.set_sample_width(
            qubit.readout_channel, qubit.sample_width, qubit.sample_width
        )
        builder.inst.set_measure_width(qubit.readout_channel, mwave_width, mwave_width)
        builder.inst.set_measure_index(qubit.readout_channel, 0, 0)
