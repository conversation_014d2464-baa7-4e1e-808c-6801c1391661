# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/19
# __author:       <PERSON><PERSON><PERSON>


import copy
import json
from pathlib import Path
from typing import Dict, List, Union

import matplotlib.pyplot as plt

from .batch_rb_spectrum import (
    BatchExperiment,
    QDict,
    Qubit,
    TraversalPattern,
    divide_same_lo_baseband_freq,
    freq_to_amp,
    get_bound_ac_spectrum,
    pyqlog,
    qarange,
)


class BatchDriveCoefficientSpectrum(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.parallel_gap_limit = 100
        options.allocate_baseband_freq = 1050
        options.step = 20
        options.traversal_pattern = TraversalPattern.Expand
        options.prepare_flows = [
            "SingleShot",
            "QubitFreqCalibration_preliminary",
            "XpiDetection",
            "QubitFreqCalibration_detail",
            "SingleShot",
        ]
        options.flows = ["RabiScanWidth"]
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.fq_dict = {}
        options.coe_records = {}
        options.idle_point_dict = {}
        options.read_point_dict = {}
        options.rf_dict = {}
        options.xy_gap_dict = {}
        options.group_map = {}
        return options

    def _batch_up(self):
        super()._batch_up()

        traversal_pattern = self.experiment_options.traversal_pattern

        # parallel divide group
        for qubit_name in self.experiment_options.physical_units:
            # record init readout point information
            qubit_obj: Qubit = self.context_manager.chip_data.cache_qubit.get(
                qubit_name
            )
            idle_freq = qubit_obj.drive_freq

            # calculate freq list
            freq_max, freq_min = get_bound_ac_spectrum(qubit_obj)

            if freq_max == freq_min:
                freq_max = [idle_freq]
            elif traversal_pattern == TraversalPattern.Decrement:
                freq_list = qarange(
                    freq_max - 0.001,
                    freq_min + 0.001,
                    -abs(self.experiment_options.step),
                )
            elif traversal_pattern == TraversalPattern.Increment:
                freq_list = qarange(
                    freq_min + 0.001,
                    freq_max - 0.001,
                    abs(self.experiment_options.step),
                )
            else:
                freq_list = [idle_freq]
                step = abs(self.experiment_options.step)
                cur_step = step
                print(freq_max, freq_min)
                while True:
                    is_choice = False
                    if idle_freq + cur_step <= freq_max:
                        freq_list.append(idle_freq + cur_step)
                        is_choice = True
                    else:
                        freq_list.append(0)

                    if idle_freq - cur_step >= freq_min:
                        freq_list.append(idle_freq - cur_step)
                        is_choice = True
                    else:
                        freq_list.append(0)

                    if is_choice is False:
                        break
                    cur_step += step
            freq_list = [round(freq, 3) for freq in freq_list]

            self.run_options.fq_dict[qubit_name] = freq_list

            # init freq record map
            point_map = {}
            for freq in freq_list:
                if freq:
                    point_map.update(
                        {
                            str(round(freq, 3)): {
                                "is_pass": False,
                                "coefficient": None,
                                "calibration_freq": None,
                                "error_reason": None,
                                "error_exp": None,
                            }
                        }
                    )
            self.run_options.coe_records[qubit_name] = QDict(**point_map)

            # set max point to parallel divide
            qubit_obj.drive_freq = freq_list[0]

        # auto parallel divide
        parallel_divide: QDict = self.backend.system.parallel_divide
        parallel_divide.check_xy_lo = True
        parallel_divide.intermediate_freq_allocate = True
        parallel_divide.max_limit = self.experiment_options.parallel_gap_limit
        parallel_divide.intermediate_freq_allocate_options = QDict(is_force=True)

        group_map = self.parallel_allocator_for_qc(
            self.experiment_options.physical_units
        )
        self.run_options.group_map = group_map
        self.run_options.dir_describe = {}

        # filter low rf which cause IF lower 800
        for _, group in group_map.items():
            for unit in group:
                qubit = self.context_manager.chip_data.get_physical_unit(unit)

                # record init qubit information
                self.run_options.idle_point_dict[unit] = qubit.idle_point
                self.run_options.read_point_dict[unit] = qubit.readout_point.amp
                self.run_options.rf_dict[unit] = qubit.drive_freq
                self.run_options.xy_gap_dict[unit] = qubit.inst.xy_gap

                # Minimum frequency limit filtering
                # freq_limit = qubit.XYwave.baseband_freq - 800 + 3750
                freq_list = self.run_options.fq_dict[unit]
                filter_freq = [freq for freq in freq_list if freq > 3800]
                self.run_options.fq_dict[unit] = filter_freq
                pyqlog.info(
                    f"{unit} scope info: max({freq_list[0]}) min({freq_list[-1]}) points({len(freq_list)})"
                )

    def _count_max_sweep_count(self, parallel_units: List[str]):
        return max([len(self.run_options.fq_dict.get(unit)) for unit in parallel_units])

    def _change_work_point(self, i: int, parallel_units: List[str]):
        working_units = []
        qubit_list = []
        for bit in self.run_options.fq_dict.keys():
            if len(self.run_options.fq_dict.get(bit)) > i and bit in parallel_units:
                freq = self.run_options.fq_dict[bit][i]
                if freq:
                    working_units.append(bit)
                    qubit_obj: Qubit = self.context_manager.chip_data.cache_qubit[bit]
                    z_amp = freq_to_amp(qubit_obj, freq)
                    qubit_obj.drive_freq = freq
                    qubit_obj.idle_point = z_amp
                    qubit_obj.readout_point.amp = self.run_options.read_point_dict[
                        bit
                    ] - (z_amp - self.run_options.idle_point_dict[bit])
                    qubit_obj.XYwave.detune_pi = 0
                    qubit_obj.XYwave.detune_pi2 = 0
                    qubit_list.append(qubit_obj)

                    self.run_options.current_point_map[bit] = str(round(freq, 3))
                    self.run_options.dir_describe[bit] = f"sweep-{round(freq, 3)}"
                    pyqlog.log(
                        "EXP",
                        f"{bit} | drive freq ({qubit_obj.drive_freq}) | "
                        f"idle point ({qubit_obj.idle_point}) | "
                        f"readout point ({qubit_obj.readout_point.amp})",
                    )
        divide_same_lo_baseband_freq(
            qubit_list, self.context_manager.chip_data, is_force=True
        )
        return working_units

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "RabiScanWidth" in exp.label:
            for unit in record.analysis_data.keys():
                cur_point = self.run_options.current_point_map[unit]
                point_sate = self.run_options.coe_records.get(unit).get(cur_point)
                if unit in record.pass_units:
                    qubit = self.backend.context_manager.chip_data.get_physical_unit(
                        unit
                    )
                    point_sate.is_pass = True
                    point_sate.coefficient = (
                        record.analysis_data.get(unit).get("result").get("freq")
                    )
                    # import random
                    # point_sate.coefficient = random.random() * 40 + 10
                    point_sate.calibration_freq = qubit.drive_freq
                else:
                    point_sate.error_reason = record.fail_reason
                    point_sate.error_exp = exp.label
        else:
            for unit in record.bad_units:
                cur_point = self.run_options.current_point_map[unit]
                point_sate = self.run_options.coe_records.get(unit).get(cur_point)
                point_sate.error_reason = record.fail_reason
                point_sate.error_exp = exp.label
        return record

    def _run_coe_acq(self, physical_units: List[str], process_bar: str):
        cache_qubit = {}
        for unit in physical_units:
            qubit: Qubit = self.backend.context_manager.chip_data.get_physical_unit(
                unit
            )
            cache_qubit[unit] = copy.deepcopy(qubit)
            qubit.drive_power = -25
            qubit.XYwave.Xpi = 0.5
            qubit.XYwave.alpha = 0
            qubit.XYwave.offset = 5
        pass_units = self._run_flow(
            physical_units=physical_units,
            flows=self.experiment_options.flows,
            name=f"Acquisition Coe {process_bar}",
        )
        for unit in physical_units:
            self.backend.context_manager.chip_data.cache_qubit[unit] = cache_qubit[unit]
        return pass_units

    def _run_analysis(self):
        super()._run_analysis()
        plot_coe_relationship(
            self.run_options.coe_records,
            save_path=str(Path(self.run_options.record_path).parent),
        )

    def _run_batch(self):
        for group_name, group in self.run_options.group_map.items():
            sweep_count = self._count_max_sweep_count(group)

            for i in range(sweep_count):
                # change working point
                working_units = self._change_work_point(i, group)

                # run exp flow
                process_bar = f"{group_name} ({i + 1}/{sweep_count})"
                pass_units = self._run_flow(
                    physical_units=working_units,
                    flows=self.experiment_options.prepare_flows,
                    name=f"Prepare point {process_bar}",
                )

                # acquisition coe data
                if pass_units:
                    pass_units = self._run_coe_acq(pass_units, process_bar)

                # record working point state
                self._save_data_to_json(
                    self.run_options.coe_records, "coe_records.json"
                )

        self._run_analysis()


def plot_coe_relationship(origin: Union[str, Dict], save_path: str = ""):
    if isinstance(origin, str):
        if not save_path:
            save_path = str(Path(origin).parent)
        with open(origin, mode="r") as f:
            record_data: Dict = json.load(f)
    else:
        record_data: Dict = origin

    for unit, record in record_data.items():
        cur_data = {"freq": [], "coe": []}
        for freq_str, data in record.items():
            if data.get("is_pass") is True:
                cur_data["freq"].append(data.get("calibration_freq") or float(freq_str))
                cur_data["coe"].append(data.get("coefficient"))
        if cur_data["freq"]:
            plt.scatter(cur_data["freq"], cur_data["coe"])
            plt.xlabel("Calibration Work Point (MHz)")
            plt.ylabel("Drive Coefficient")
            plt.title(f"{unit} Drive Coefficient")
            plt.savefig(str(Path(save_path, f"{unit}-freq-coe_result.png")))
            plt.close()
