# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/30
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy
from functools import wraps
from typing import List

from prettytable import PrettyTable

from ...log import pyqlog
from ...pulse import (
    Constant,
    PulseComponent,
)
from ...pulse.pulse_function import f12_pi_pulse, zero_x12_pulse
from ...qaio_property import QAIO
from ...qubit import Qubit


def extend_f12_pulse(func):
    # def check_dcm(dcm: IQdiscriminator):
    #     dcm._level_str = "01"

    @wraps(func)
    def wrapper(*args, **kwargs):
        exp = args[0]
        qubits = func(*args, **kwargs)
        if isinstance(qubits, Qubit):
            qubits = [qubits]

        flag = False
        for qubit in qubits:
            if qubit.f12_options.switch is True:
                flag = True

        if flag:
            table = PrettyTable()
            table.field_names = [
                "Qubit",
                "F12 Switch",
                "Tail Pulse Type",
                "Reason",
                "F01",
                "F01 BF",
                "F12",
                "F02 BF",
            ]

            # new experiment builder not iq discriminator
            # if isinstance(exp.discriminator, IQdiscriminator):
            #     check_dcm(exp.discriminator)
            # elif isinstance(exp.discriminator, List):
            #     for dcm in exp.discriminator:
            #         check_dcm(dcm)

            IFL, IFR, _ = QAIO.param_range.get("intermediate_frequency")

            zero_xy_p = zero_x12_pulse(qubits[0])()
            zero_z_p = zero_x12_pulse(qubits[0], name="Z")()

            pulse_time_list = []
            for bit in list(exp.xy_pulses.keys()):
                pulses = exp.xy_pulses.get(bit)
                if pulses:
                    if bit in qubits:
                        if bit.f12_options.switch:
                            f01 = bit.drive_freq
                            f12 = bit.f12_options.drive_freq
                            f01_bf = bit.XYwave.baseband_freq
                            try:
                                f12_bf = bit.XYwave.baseband_freq + f12 - f01
                                assert IFR >= f12_bf >= IFL, (
                                    f"F12 pi pulse IF out of scope {f12_bf}"
                                )
                                tail_p = f12_pi_pulse(bit)()
                                phase = tail_p.phase
                                table.add_row(
                                    [
                                        bit.name,
                                        bit.f12_options.switch,
                                        "12 pi pulse",
                                        "-",
                                        f01,
                                        f01_bf,
                                        f12,
                                        f12_bf,
                                    ]
                                )
                            except Exception as e:
                                pyqlog.error(f"f12 pi pulse add error: {e}")
                                tail_p = zero_xy_p
                                phase = 0
                                table.add_row(
                                    [
                                        bit.name,
                                        bit.f12_options.switch,
                                        "zero pulse",
                                        "validate error",
                                        f01,
                                        f01_bf,
                                        f12,
                                        "-",
                                    ]
                                )
                        else:
                            tail_p = zero_xy_p
                            phase = 0
                            table.add_row(
                                [
                                    bit.name,
                                    bit.f12_options.switch,
                                    "zero pulse",
                                    "f12 switch is false",
                                    "-",
                                    "-",
                                    "-",
                                    "-",
                                ]
                            )
                    else:
                        tail_p = zero_xy_p
                        phase = 0
                        table.add_row(
                            [
                                bit.name,
                                bit.f12_options.switch,
                                "Zero Pulse",
                                "not measure qubit",
                                "-",
                                "-",
                                "-",
                                "-",
                            ]
                        )

                    if isinstance(pulses, List):
                        n_p = []
                        for p in pulses:
                            pulse_obj = p + deepcopy(tail_p)
                            if p.virtual_z_phase:
                                p.virtual_z_phase.append(phase)
                            n_p.append(pulse_obj)
                            pulse_time_list.append(pulse_obj.width)
                    else:
                        n_p = pulses + deepcopy(tail_p)
                        if n_p.virtual_z_phase:
                            n_p.virtual_z_phase.append(phase)
                        pulse_time_list.append(n_p.width)
                    exp.xy_pulses[bit] = n_p
                else:
                    table.add_row(
                        [
                            bit.name,
                            bit.f12_options.switch,
                            "-",
                            "No xy pulse",
                            "-",
                            "-",
                            "-",
                            "-",
                        ]
                    )

            for bit in list(exp.z_pulses.keys()):
                pulses = exp.z_pulses.get(bit)
                if pulses:
                    if isinstance(pulses, List):
                        n_p = []
                        for p in pulses:
                            pulse_obj = p + deepcopy(zero_z_p)
                            n_p.append(pulse_obj)
                            pulse_time_list.append(pulse_obj.width)
                    else:
                        n_p = pulses + deepcopy(zero_z_p)
                        pulse_time_list.append(n_p.width)
                    exp.z_pulses[bit] = n_p

            pyqlog.log(
                "EXP",
                f"Add f12 pi pulse before measure pulse to improve 01 state read fidelity: \n{table}",
            )

            # BugFixed, when add 12 pulse, exp._pulse_time_list is not right.
            exp._pulse_time_list.clear()
            exp._pulse_time_list.extend(pulse_time_list)

        return qubits

    return wrapper


def extend_f12_pulse_v2(func):

    @wraps(func)
    def wrapper(*args, **kwargs):
        exp = args[0]
        qubits = func(*args, **kwargs)
        if isinstance(qubits, Qubit):
            qubits = [qubits]

        flag = False
        for qubit in qubits:
            if qubit.f12_options.switch is True:
                flag = True

        table = PrettyTable()
        table.field_names = [
            "Qubit",
            "F12 Switch",
            "Tail Pulse Type",
            "Reason",
            "F01",
            "F01 BF",
            "F12",
            "F02 BF",
        ]

        IFL, IFR, _ = QAIO.param_range.get("intermediate_frequency")

        if flag is True:
            zero_xy_p = zero_x12_pulse(qubits[0])()
            zero_z_p = zero_x12_pulse(qubits[0], name="Z")()
        else:
            zero_xy_p = Constant(0, 0, "XY")()   
            zero_z_p = Constant(0, 0)()

        pulse_time_list = []
        for bit in list(exp.xy_pulses.keys()):
            pulses = exp.xy_pulses.get(bit)
            if pulses:
                if bit in qubits:
                    if bit.f12_options.switch:
                        f01 = bit.drive_freq
                        f12 = bit.f12_options.drive_freq
                        f01_bf = bit.XYwave.baseband_freq
                        try:
                            f12_bf = bit.XYwave.baseband_freq + f12 - f01
                            assert IFR >= f12_bf >= IFL, (
                                f"F12 pi pulse IF out of scope {f12_bf}"
                            )
                            tail_p = f12_pi_pulse(bit)()
                            phase = tail_p.phase
                            table.add_row(
                                [
                                    bit.name,
                                    bit.f12_options.switch,
                                    "12 pi pulse",
                                    "-",
                                    f01,
                                    f01_bf,
                                    f12,
                                    f12_bf,
                                ]
                            )
                        except Exception as e:
                            pyqlog.error(f"f12 pi pulse add error: {e}")
                            tail_p = zero_xy_p
                            phase = 0
                            table.add_row(
                                [
                                    bit.name,
                                    bit.f12_options.switch,
                                    "zero pulse",
                                    "validate error",
                                    f01,
                                    f01_bf,
                                    f12,
                                    "-",
                                ]
                            )
                    else:
                        tail_p = zero_xy_p
                        phase = 0
                        table.add_row(
                            [
                                bit.name,
                                bit.f12_options.switch,
                                "zero pulse",
                                "f12 switch is false",
                                "-",
                                "-",
                                "-",
                                "-",
                            ]
                        )
                else:
                    tail_p = zero_xy_p
                    phase = 0
                    table.add_row(
                        [
                            bit.name,
                            bit.f12_options.switch,
                            "Zero Pulse",
                            "not measure qubit",
                            "-",
                            "-",
                            "-",
                            "-",
                        ]
                    )

                if isinstance(pulses, List):
                    n_p = []
                    for p in pulses:
                        p += tail_p
                        if p.virtual_z_phase:
                            p.virtual_z_phase.append(phase)
                        n_p.append(p)
                        pulse_time_list.append(p.width)
                else:
                    n_p = pulses
                    n_p += tail_p
                    if n_p.virtual_z_phase:
                        n_p.virtual_z_phase.append(phase)
                    pulse_time_list.append(n_p.width)
                exp.xy_pulses[bit] = n_p
            else:
                table.add_row(
                    [
                        bit.name,
                        bit.f12_options.switch,
                        "-",
                        "No xy pulse",
                        "-",
                        "-",
                        "-",
                        "-",
                    ]
                )

        for bit in list(exp.z_pulses.keys()):
            pulses = exp.z_pulses.get(bit)
            if pulses:
                if isinstance(pulses, List):
                    n_p = []
                    for p in pulses:
                        pulse_obj = p + zero_z_p
                        n_p.append(pulse_obj)
                        pulse_time_list.append(pulse_obj.width)
                else:
                    n_p = pulses + zero_z_p
                    pulse_time_list.append(n_p.width)
                exp.z_pulses[bit] = n_p

        if flag is True:
            pyqlog.log(
                "EXP",
                f"Add f12 pi pulse before measure pulse to improve 01 state read fidelity: \n{table}",
            )

            # BugFixed, when add 12 pulse, exp._pulse_time_list is not right.
            exp._pulse_time_list.clear()
            exp._pulse_time_list.extend(pulse_time_list)

        return qubits

    return wrapper



def online_extend(func):
    """Expanding the experiment to a composite environment with online bits.

    Args:
        func: `TopExperiment._register` function

    Returns:

    """

    def trans_zero_pulse(pulse_obj: "PulseComponent", bit: str, name: str = "Z"):
        """Trans zero pulse."""
        new_pulse_obj = Constant(pulse_obj.width, 0, name)()
        new_pulse_obj.bit = bit
        return new_pulse_obj

    def wrapper(*args, **kwargs):
        exp = args[0]

        online_bits = exp.run_options.online_bits
        online_compensates = exp.run_options.online_coms
        readout_trigger_delay = exp.run_options.readout_trigger_delay

        if online_bits:
            # actual participating bits in the experiment
            exp_bit_name = [bit.name for bit in exp.qubits]
            exp_bit_name.extend([bit.name for bit in exp.couplers])

            # update online pulse compensate
            exp.compensates = online_compensates

            # records actual measure qubit name
            exp.run_options.measure_qubits = [bit.name for bit in exp.readout_qubits]

            # rebuild readout qubits and pulses
            exp.readout_qubits = []
            exp.readout_pulses = []

            # extract online qubits, use union read
            online_qubits = [bit for bit in online_bits if bit.name.startswith("q")]

            # extend xy and z pulses
            exp_xy_pulse = exp.xy_pulses.get(exp.qubits[0])
            for bit in online_bits:
                if bit.name not in exp_bit_name:
                    if bit.name.startswith("q"):
                        exp._bind_qubit_drive(bit)
                        exp.xy_pulses[bit] = [
                            trans_zero_pulse(p, bit.name, "XY") for p in exp_xy_pulse
                        ]
                        exp.z_pulses[bit] = [
                            trans_zero_pulse(p, bit.name, "Z") for p in exp_xy_pulse
                        ]
                    else:
                        exp.z_pulses[bit] = [
                            trans_zero_pulse(p, bit.name, "Z") for p in exp_xy_pulse
                        ]

            # reset union readout pulse by online qubits

            # bugfix: online extend no need wrapped
            exp._set_union_readout_pulse.__wrapped__(exp, online_qubits)

            # if one qubit has readout trigger delay, extend all qubits
            if readout_trigger_delay:
                for qubit in online_qubits:
                    exp.sweep_readout_trigger_delay(
                        qubit.readout_channel,
                        deepcopy(readout_trigger_delay),
                        online=True,
                    )

            # union readout no support dynamic plot
            exp.experiment_options.is_dynamic = 0

        # actual register method
        func(*args, **kwargs)

    return wrapper
