# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/01
# __author:       <PERSON><PERSON><PERSON>

import j<PERSON>
from pathlib import Path

from ...instrument.INSTRUMENT import INSTRUMENT
from ..batch_experiment import BatchExperiment, BatchPhysicalUnitType


class BatchImpaCalibrate(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.micro_switch = "MicroSwitch"
        options.flux_scan_flows = ["ImpaCavityFluxScan"]
        options.flows = ["ImpaOptiParams", "ImpaGain"]
        options.physical_unit_type = BatchPhysicalUnitType.BUS
        options.affect_next_node = False
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.micro_switch = None
        options.ppt_template.update(
            dict(
                shape=(2, 3),
                split_by_unit=True,
            )
        )
        options.flux_points_map = {}
        options.fc_scope = {}
        options.impa_gain = {}
        return options

    def _check_options(self):
        self.run_options.micro_switch = INSTRUMENT(
            self.context_manager.config.system.config_path
        )[self.experiment_options.micro_switch]

        for bus in self.experiment_options.physical_units:
            fcs = [
                qubit.probe_freq
                for qubit in self.backend.chip_data.get_qubits_from_bus(bus).values()
            ]
            fc_min, fc_max = min(fcs) - 50, max(fcs) + 50
            self.run_options.fc_scope[bus] = [
                fc_min * 2 * 1e-3,
                (fc_min + fc_max) * 1e-3,
                fc_max * 2 * 1e-3,
            ]

        super()._check_options()
        self.run_options.ppt_template.exps = (
            self.experiment_options.flux_scan_flows + self.experiment_options.flows
        )

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "ImpaCavityFluxScan" == exp_name:
            for unit in record.pass_units:
                flux_points = record.analysis_data.get(unit).get("result").get("points")
                self.run_options.flux_points_map[unit] = flux_points

        elif "ImpaOptiParams" == exp_name:
            for unit in record.pass_units:
                best_gain = (
                    record.analysis_data.get(unit).get("result").get("best_gain")
                )
                self.run_options.impa_gain[unit] = best_gain
                self.change_regular_exec_exp_options(
                    exp_name="ImpaGain", ffp_list=best_gain
                )

        return record

    def _run_batch(self):
        pass_units = []
        for bus in self.experiment_options.physical_units:
            bus_num = int(bus.split("-")[-1])
            self.run_options.micro_switch.open_bus(bus_num)
            for exp_name in self.experiment_options.flux_scan_flows:
                self.change_regular_exec_exp_options(exp_name=exp_name, bus=bus_num)
            for exp_name in self.experiment_options.flows:
                self.change_regular_exec_exp_options(exp_name=exp_name, bus=bus_num)

            self._run_flow(
                flows=self.experiment_options.flux_scan_flows,
                physical_units=[bus],
                name=bus,
            )

            flux_points = self.run_options.flux_points_map.get(bus)
            fc_list = self.run_options.fc_scope.get(bus)

            if flux_points:
                groups = [(flux, fc) for flux in flux_points for fc in fc_list]
            else:
                groups = [(None, fc) for fc in fc_list]

            for flux, fc in groups:
                if flux is None:
                    self.change_regular_exec_exp_options(
                        exp_name="ImpaOptiParams",
                        input_data=dict(
                            flux=dict(is_opt=True, init_v=-0.01, bound=[-9.9, 9.9]),
                            fp=dict(is_opt=True, init_v=fc, bound=[-0.3, 0.3]),
                            pp=dict(is_opt=True, init_v=-0.1, bound=[-15, 5]),
                        ),
                        de_opts={"NIND": 8, "MAXGEN": 60, "mutF": 0.6, "XOVR": 0.7},
                    )
                else:
                    self.change_regular_exec_exp_options(
                        exp_name="ImpaOptiParams",
                        input_data=dict(
                            flux=dict(is_opt=True, init_v=flux, bound=[-2, 2]),
                            fp=dict(is_opt=True, init_v=fc, bound=[-0.3, 0.3]),
                            pp=dict(is_opt=True, init_v=-0.1, bound=[-15, 5]),
                        ),
                        de_opts={"NIND": 8, "MAXGEN": 30, "mutF": 0.6, "XOVR": 0.7},
                    )

                pass_bus = self._run_flow(
                    flows=self.experiment_options.flows,
                    physical_units=[bus],
                    name=bus,
                )

                if pass_bus:
                    pass_units.extend(pass_bus)
                    break

        self._save_data_to_json(self.run_options.impa_gain, "best_gain.json")
        self.bind_pass_units(pass_units)
        config_path = (
            Path(self.context_manager.config.system.config_path) / "impa_params.json"
        )
        with open(config_path, mode="w", encoding="utf-8") as fp:
            json.dump(
                self.run_options.impa_gain,
                fp,
                indent=4,
                ensure_ascii=False,
            )
