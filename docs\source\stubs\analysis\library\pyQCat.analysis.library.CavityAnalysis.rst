﻿pyQCat.analysis.library.CavityAnalysis
======================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: CavityAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CavityAnalysis.__init__
      ~CavityAnalysis.from_sub_analysis
      ~CavityAnalysis.run_analysis
      ~CavityAnalysis.set_options
      ~CavityAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CavityAnalysis.analysis_datas
      ~CavityAnalysis.data_filter
      ~CavityAnalysis.drawer
      ~CavityAnalysis.experiment_data
      ~CavityAnalysis.has_child
      ~CavityAnalysis.options
      ~CavityAnalysis.quality
      ~CavityAnalysis.results
   
   