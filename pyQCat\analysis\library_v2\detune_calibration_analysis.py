# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/09/16
# __author:       <PERSON><PERSON><PERSON>

"""
APE Composite Analysis.

"""

from ..curve_fit_analysis import CurveFitAnalysis
from ..quality import BaseQuality
from ..specification import ParameterRepr
from ...structures import Options
from ...types import QualityDescribe


class DetuneCalibrationAnalysisV2(CurveFitAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.subplots = (2, 1)
        options.x_label = "Detune [MHz]"
        options.y_label = "P0"
        options.sub_title = ["rough scan", "fine scan"]
        options.data_key = ["analysis"]
        options.plot_key = "P0"

        options.result_parameters = [
            ParameterRepr(name="detune", repr="detune", unit="MHz")
        ]

        options.figsize = (12, 12)

        return options

    def _extract_result(self, data_key: str):
        analysis_data_list = self.analysis_datas.get(data_key).y

        for result in self.results.values():
            result.value = analysis_data_list[1].results.detune.value

        self._quality = BaseQuality()
        self._quality.descriptor = QualityDescribe.perfect
        for analysis in analysis_data_list:
            if analysis.quality.descriptor in [QualityDescribe.abnormal, QualityDescribe.bad]:
                self._quality.descriptor = QualityDescribe.bad
                break

        self.drawer.set_options(
            text_pos=self.options.text_pos, text_rp=self.options.text_rp
        )

    def _visualization(self):
        self.drawer.set_options(title=self._description())

        count = 0
        colors = ["red", "blue", "green"]
        if self.has_child is True:
            self.drawer.set_options(raw_data_format="plot")
            for index1, scan_type in enumerate(self.experiment_data.x_data):
                ape_composite_data = self.experiment_data.child_data(index=index1)

                for index2, n in enumerate(ape_composite_data.x_data):
                    ape_data = ape_composite_data.child_data(index=index2)

                    x_arr = ape_data.x_data
                    y_arr = ape_data.y_data.get(self.options.plot_key)
                    if y_arr is None:
                        y_arr = ape_data.y_data.get("Amp")

                    self.drawer.draw_raw_data(
                        x_data=x_arr,
                        y_data=y_arr,
                        ax_index=index1,
                        label=f"N={n}",
                        color=colors[count % len(colors)],
                    )
                    count += 1

        self.drawer.draw_text(ax_index=0)
        self.drawer.draw_text(ax_index=1)

        self.drawer.format_canvas()
