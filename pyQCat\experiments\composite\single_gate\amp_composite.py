# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/05/31
# __author:       xw
# __corporation:  OriginQuantum
"""AmpComposite experiment."""

from ....analysis import AmpCompositeAnalysis
from ....analysis.specification import ParameterRepr
from ....structures import MetaData, Options
from ...composite_experiment import CompositeExperiment
from ...single import AmpOptimize, CouplerAmpOptimize


class AmpComposite(CompositeExperiment):
    _sub_experiment_class = AmpOptimize

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("n_list", list)
        options.set_validator("theta_type", ["Xpi", "Xpi/2"])
        options.set_validator("f12_opt", bool)

        options.n_list = [7, 9, 13]
        options.theta_type = "Xpi"
        options.f12_opt = False

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("use_deviation", bool)
        options.set_validator("deviation_limit", float)
        options.set_validator("diff_threshold", (0, 1, 2))

        options.use_deviation = False
        options.deviation_limit = 0.5
        options.diff_threshold = 0.2
        options.plot_keys = None
        options.y_label = None
        options.result_parameters = None
        return options

    @staticmethod
    def check_key(cc_exp, theta_type, n_list, f12_opt):
        data_keys = []
        if f12_opt:
            path_pre = "Qubit.f12_options"
        elif cc_exp.is_coupler_exp:
            path_pre = "Coupler.drive_XYwave"
        elif cc_exp.coupler and cc_exp.is_coupler_exp is False:
            path_pre = "Coupler.probe_XYwave"
        else:
            path_pre = "Qubit.XYwave"

        # validate data key and result parameters

        if theta_type == "Xpi":
            result_parameters = [
                ParameterRepr(
                    name="Xpi", repr="X-amp", unit="V", param_path=f"{path_pre}.Xpi"
                )
            ]
        else:
            result_parameters = [
                ParameterRepr(
                    name="Xpi2", repr="X2-amp", unit="V", param_path=f"{path_pre}.Xpi2"
                )
            ]
        for N in n_list:
            if theta_type == "Xpi":
                if hasattr(cc_exp, "qubit"):
                    data_key = ["P0"] if N % 2 == 0 else ["P1"]
                else:
                    data_key = ["P1"] if N % 2 == 0 else ["P0"]
            else:
                if hasattr(cc_exp, "qubit"):
                    data_key = ["P0"] if N % 4 == 0 else ["P1"]
                else:
                    data_key = ["P1"] if N % 4 == 0 else ["P0"]
            data_keys.extend(data_key)

        return data_keys, result_parameters

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        child_exp = self.child_experiment

        n_list = self.experiment_options.n_list
        theta_type = self.experiment_options.theta_type
        f12_opt = self.experiment_options.f12_opt

        data_keys, result_parameters = self.check_key(
            child_exp, theta_type, n_list, f12_opt
        )

        result_name = self.analysis_options.result_name
        cd_exp = self.child_experiment
        if cd_exp.coupler and cd_exp.is_coupler_exp is False:
            result_name = cd_exp.coupler.name
        elif cd_exp.is_coupler_exp is True and cd_exp.coupler:
            result_name = cd_exp.coupler.name
        elif cd_exp.qubit:
            result_name = cd_exp.qubit.name

        self.set_analysis_options(
            result_parameters=result_parameters,
            plot_keys=data_keys,
            result_name=result_name,
        )

        self.run_options.x_data = self.experiment_options.n_list
        self.run_options.analysis_class = AmpCompositeAnalysis

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.draw_meta = {
            "theta_type": self.experiment_options.theta_type,
            "n_list": self.experiment_options.n_list,
        }
        return metadata

    def _setup_child_experiment(self, amp_opt: AmpOptimize, idx: int, n: float):
        n_list = self.experiment_options.n_list
        theta_type = self.experiment_options.theta_type
        f12_opt = self.experiment_options.f12_opt

        amp_opt.set_parent_file(self, f"N={n}", idx, len(n_list))

        amp_opt.set_experiment_options(theta_type=theta_type, N=int(n), f12_opt=f12_opt)
        self._check_simulator_data(amp_opt, idx)

    def _handle_child_result(self, amp_opt: AmpOptimize):
        points = amp_opt.analysis.results.points.value
        freq = amp_opt.analysis.results.freq.value

        amp_opt.analysis.provide_for_parent.update({"points": points, "freq": freq})
        amp_opt.experiment_data.metadata.process_meta["quality"] = amp_opt.analysis.quality


class CouplerAmpComposite(AmpComposite):
    _sub_experiment_class = CouplerAmpOptimize
