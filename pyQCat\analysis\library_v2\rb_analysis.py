# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/27
# __author:       <PERSON><PERSON><PERSON>

from typing import List, Union

import numpy as np

from ...structures import Options, QDict
from ...types import QualityDescribe
from ..curve_fit_analysis import (CurveAnalysisData, CurveFitAnalysis,
                                  FitModel, FitOptions)
from ..fit.fit_models import rb_exponential_decay_func
from ..quality.base_quality import BaseQuality


class RBAnalysisV2(CurveFitAnalysis):
    """A class to analyze general randomized benchmarking experiment."""

    @classmethod
    def _default_options(cls) -> Options:
        r"""Create the Analysis options, and set some fields.

        **Analysis Options**:

            - **fit_model (FitModel)** - Curve fitting model, the formula is as follows:

            .. math::
                y = {\rm amp} \cdot {\rm base}^{\left(x + {\rm x0} \right)} + {\rm baseline}

            - **x_label (str)** - The labels of the X-axis in the resulting graph, default is
              `Number of clifford`

            - **quality_bounds (List)** - Fit quality evaluation metrics, default is `[0.9, 0.85, 0.77]`

            - **depths (List)** - RB scan depth, determined by experiment

            - **k (int)** - Random number of times at each depth, determined by experiment.

            - **result_parameters (List)** - Expected Extracted Experimental Results,
                default is `['rc', 'rg', "fidelity"]`.

        Returns:
            RB experiment analysis options.
        """
        options = super()._default_options()
        options.fit_model = FitModel(
            fit_func=rb_exponential_decay_func,
            model_description=r"amp \exp(x) + baseline",
        )
        options.x_label = "Number of Clifford"
        options.quality_bounds = [0.9, 0.85, 0.77]
        options.result_parameters = ["rc", "rg", "fidelity", "depth", "std"]
        options.depths = None
        options.k = None
        options.rate = 1.875
        options.expect_depth = 100
        options.std_bound = 0.05
        options.fidelity_threshold = 0.995
        options.fidelity = 0
        options.std = None
        return options

    def _guess_fit_param(
        self,
        user_opt: FitOptions,
        curve_data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Create algorithmic guess with analysis options and curve data.

        We do as follows:

            - Use the mean of the data excluding the first 5 points as the initial `baseline`
            - The difference between the first y value and the baseline is used as the initial `amp`
            - `base` defaults to 0.9

        Args:
            user_opt: Fit options filled with user provided guess and bounds.
            curve_data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        baseline = np.mean(curve_data.y[-5:])
        amp = curve_data.y[0] - baseline

        user_opt.p0.set_if_empty(base=0.9)
        user_opt.p0.set_if_empty(baseline=baseline)
        user_opt.p0.set_if_empty(amp=amp)

        return user_opt

    def _create_analysis_data(self) -> QDict:
        """Create Analysis data provided for detailed analyze.

        Returns:
            A QDict object, key represents data type and value is
            CurveAnalysisData object.
        """
        analysis_data_dict = QDict()

        data_key = list(
            self.options.get("data_key", None) or self._experiment_data.y_data.keys()
        )

        k = self.options.k
        depths = self.options.depths

        if data_key[0] == "qh-0(P00+P01)":
            qh_0 = np.copy(self.experiment_data.y_data.get("P00")) + np.copy(
                self.experiment_data.y_data.get("P01")
            )
            qh_1 = np.copy(self.experiment_data.y_data.get("P10")) + np.copy(
                self.experiment_data.y_data.get("P11")
            )
            ql_0 = np.copy(self.experiment_data.y_data.get("P00")) + np.copy(
                self.experiment_data.y_data.get("P10")
            )
            ql_1 = np.copy(self.experiment_data.y_data.get("P01")) + np.copy(
                self.experiment_data.y_data.get("P11")
            )

            self.experiment_data._y_data = {
                "qh-0(P00+P01)": qh_0,
                "qh-1(P10+P11)": qh_1,
                "ql-0(P00+P10)": ql_0,
                "ql-1(P01+P11)": ql_1,
            }

            for key in data_key:
                if key == "qh-0(P00+P01)":
                    analysis_data = CurveAnalysisData(
                        x=np.copy(self._experiment_data.x_data), y=qh_0
                    )
                elif key == "qh-1(P10+P11)":
                    analysis_data = CurveAnalysisData(
                        x=np.copy(self._experiment_data.x_data), y=qh_1
                    )
                elif key == "ql-0(P00+P10)":
                    analysis_data = CurveAnalysisData(
                        x=np.copy(self._experiment_data.x_data), y=ql_0
                    )
                elif key == "ql-1(P01+P11)":
                    analysis_data = CurveAnalysisData(
                        x=np.copy(self._experiment_data.x_data), y=ql_1
                    )
                else:
                    raise Exception(f"Not support {key}")
                analysis_data_dict[key] = analysis_data

        for key in data_key:
            if key in self.experiment_data.y_data:
                matrix_p = self.experiment_data.y_data[key].reshape((len(depths), k))

                if key in ["P0", "P00"]:
                    std_list = []
                    for i, p in enumerate(matrix_p):
                        if self.experiment_data.x_data[i] <= self.options.expect_depth:
                            std = np.std(p)
                            std_list.append(std)
                        else:
                            break
                    self.options.std = np.mean(std_list)

                average_p = np.mean(matrix_p, axis=1)
                analysis_data = CurveAnalysisData(x=np.copy(depths), y=average_p)
                analysis_data_dict[key] = analysis_data
        return analysis_data_dict

    def _extract_result(self):
        r"""Extract analysis results from important fit parameters.

        The error rate of a clifford individual is:

        .. math::

            rc = \frac{1 - base}{2}

        An average individual in a single-bit Clifford cluster contains 1.875 single gates,
        so the single-bit gate error rate is calculated as follows:

        .. math::

            rg = \frac{rc}{1.875}

        So the basic book fidelity is calculated as follows:

        .. math::

            fidelity = 1 - rg
        """

        data_key = self.experiment_data.metadata.process_meta.get("best_data_key")

        def cal_fidelity(ana_data):
            """Calculating single qubit gate fidelity

            Args:
                ana_data: AnalysisData

            Returns:
                rc: Average clifford individual error rate
                rg: Average single qubit gate error rate
                fidelity: Average single qubit fidelity
            """
            _base = ana_data.fit_data.fitval("base")
            _rc = (1 - _base) * (1 - 1 / 2)
            _rg = _rc / self.options.rate
            _fidelity = 1 - _rg
            return _rc, _rg, _fidelity

        def compare_fidelity(ana0, ana1):
            """Choose the best fidelity, use for JointRBSingle

            Args:
                ana0: P0 AnalysisData
                ana1: P1 AnalysisData

            Returns:
                rc: Average clifford individual error rate
                rg: Average single qubit gate error rate
                fidelity: Average single qubit fidelity
            """
            rc0, rg0, fidelity0 = cal_fidelity(ana0)
            rc1, rg1, fidelity1 = cal_fidelity(ana1)
            if fidelity1 > fidelity0:
                rc0, rg0, fidelity0 = rc1, rg1, fidelity1
            return rc0, rg0, fidelity0

        if data_key in [
            "qh-0(P00+P01)",
            "qh-1(P10+P11)",
            "ql-0(P00+P10)",
            "ql-1(P01+P11)",
        ]:
            # use for joint rb single
            qh_0_ana = self.analysis_datas["qh-0(P00+P01)"]
            qh_1_ana = self.analysis_datas["qh-1(P10+P11)"]
            qh_rc, qh_rg, qh_fidelity = compare_fidelity(qh_0_ana, qh_1_ana)
            self.results.qh_rc.value = round(qh_rc, 4)
            self.results.qh_rg.value = round(qh_rg, 4)
            self.results.qh_fidelity.value = round(qh_fidelity, 4)

            ql_0_ana = self.analysis_datas["ql-0(P00+P10)"]
            ql_1_ana = self.analysis_datas["ql-1(P01+P11)"]
            ql_rc, ql_rg, ql_fidelity = compare_fidelity(ql_0_ana, ql_1_ana)
            self.results.ql_rc.value = round(ql_rc, 4)
            self.results.ql_rg.value = round(ql_rg, 4)
            self.results.ql_fidelity.value = round(ql_fidelity, 4)
        elif data_key == "P00":
            # use for cz rb
            analysis_data = self.analysis_datas[data_key]
            base = analysis_data.fit_data.fitval("base")
            r = (1 - base) * 3 / 4
            f = 1 - r
            self.results.p.value = base
            self.results.r.value = r
            self.results.f.value = f
        else:
            # use for single qubit rb
            analysis_data = self.analysis_datas[data_key]
            rc, rg, fidelity = cal_fidelity(analysis_data)
            self.results.rc.value = round(rc, 4)
            self.results.rg.value = round(rg, 4)
            self.results.fidelity.value = round(fidelity, 4)
            self.results.std.value = round(self.options.std, 4)
            self.options.fidelity = fidelity

        self.results.depth.value = self.options.depths[-1]

    def _evaluate_quality(self):
        super()._evaluate_quality()

        if self.options.std is not None and self.options.std > self.options.std_bound:
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)

        if self.options.fidelity < self.options.fidelity_threshold:
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)
