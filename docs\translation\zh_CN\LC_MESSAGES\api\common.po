# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/common.rst:2
msgid "公共方法"
msgstr ""

#: ../../source/api/common.rst:5
msgid "pyQCat.config module"
msgstr ""

#: of pyQCat.config:1 pyQCat.context:1
msgid "Parse config.conf file. Model of config yaml/conf file."
msgstr ""

#: of pyQCat.config.PyqcatConfig:1 pyQCat.context.ExperimentContext:1
#: pyQCat.structures.Descriptor:1 pyQCat.structures.ExperimentData:1
#: pyQCat.structures.MetaData:1 pyQCat.structures.Point:1
#: pyQCat.structures.SaveFileStruct:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.config.PyqcatConfig:1
msgid "Class representing a user config file"
msgstr ""

#: of pyQCat.config.PyqcatConfig._read_config:1
msgid "Read config file and parse the contents into the settings attr."
msgstr ""

#: of pyQCat.config.PyqcatConfig.save_type:1
msgid "Get result save type, 'local' or 's3'"
msgstr ""

#: of pyQCat.config.PyqcatConfig.local_root:1
msgid "Get local root."
msgstr ""

#: of pyQCat.config.PyqcatConfig.s3_root:1
msgid "Get s3 root."
msgstr ""

#: of pyQCat.config.PyqcatConfig.inst_address:1
msgid "Get mongo instrument address."
msgstr ""

#: ../../source/api/common.rst:13
msgid "pyQCat.context module"
msgstr ""

#: of pyQCat.context.ExperimentContext:1
msgid ""
"Experiment basic environment class, which supports customized and "
"flexible functions, such as customized configuration files, customized "
"object storage, customized experimental parameter binding and many other "
"functions, you can use it to obtain the dependent parameters for "
"performing quantum experiments. Of course, if you want more functions, "
"you can also customize the method to achieve."
msgstr ""

#: of pyQCat.context.ExperimentContext.config:1
msgid "Configuration file manipulation tool."
msgstr ""

#: of pyQCat.context.ExperimentContext.compensates
#: pyQCat.context.ExperimentContext.config
#: pyQCat.context.ExperimentContext.configure_coupler_cali_infos
#: pyQCat.context.ExperimentContext.configure_coupler_probe_qubit_cali_infos
#: pyQCat.context.ExperimentContext.configure_couplers
#: pyQCat.context.ExperimentContext.configure_dcm
#: pyQCat.context.ExperimentContext.configure_environment
#: pyQCat.context.ExperimentContext.configure_inst
#: pyQCat.context.ExperimentContext.configure_qubits
#: pyQCat.context.ExperimentContext.coupler
#: pyQCat.context.ExperimentContext.couplers
#: pyQCat.context.ExperimentContext.discriminators
#: pyQCat.context.ExperimentContext.get_ac_spectrum_paras
#: pyQCat.context.ExperimentContext.get_base_qubit
#: pyQCat.context.ExperimentContext.get_coupler
#: pyQCat.context.ExperimentContext.get_dc_spectrum_paras
#: pyQCat.context.ExperimentContext.get_qubit
#: pyQCat.context.ExperimentContext.inst
#: pyQCat.context.ExperimentContext.linked
#: pyQCat.context.ExperimentContext.qubit
#: pyQCat.context.ExperimentContext.qubits
#: pyQCat.context.ExperimentContext.transform_coupler_to_qubits
#: pyQCat.parameters.corrected_dc pyQCat.parameters.decorate_dc
#: pyQCat.parameters.get_parameters pyQCat.structures.ExperimentData.child_data
#: pyQCat.structures.ExperimentData.from_data
msgid "Return type"
msgstr ""

#: of pyQCat.context.ExperimentContext.config:3
msgid ":py:class:`~pyQCat.config.PyqcatConfig`"
msgstr ""

#: of pyQCat.context.ExperimentContext.linked:1
msgid "Link state"
msgstr ""

#: of pyQCat.context.ExperimentContext.linked:3
msgid ":py:class:`bool`"
msgstr ""

#: of pyQCat.context.ExperimentContext.inst:1
msgid "Qaio instrument."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_inst:11
#: pyQCat.context.ExperimentContext.inst:3
msgid ":py:class:`~pyQCat.instrument.instrument_aio.Instrument`"
msgstr ""

#: of pyQCat.context.ExperimentContext.qubit:1
msgid "First qubit"
msgstr ""

#: of pyQCat.context.ExperimentContext.get_qubit:8
#: pyQCat.context.ExperimentContext.qubit:3
msgid ":py:class:`~pyQCat.qubit.qubit.Qubit`"
msgstr ""

#: of pyQCat.context.ExperimentContext.qubits:1
msgid "All qubits."
msgstr ""

#: of pyQCat.context.ExperimentContext.qubits:3
msgid ":py:class:`~typing.List`\\[:py:class:`~pyQCat.qubit.qubit.Qubit`]"
msgstr ""

#: of pyQCat.context.ExperimentContext.coupler:1
msgid "First Coupler."
msgstr ""

#: of pyQCat.context.ExperimentContext.coupler:3
#: pyQCat.context.ExperimentContext.get_coupler:8
msgid ":py:class:`~pyQCat.qubit.qubit.Coupler`"
msgstr ""

#: of pyQCat.context.ExperimentContext.couplers:1
msgid "All couplers."
msgstr ""

#: of pyQCat.context.ExperimentContext.couplers:3
msgid ":py:class:`~typing.List`\\[:py:class:`~pyQCat.qubit.qubit.Coupler`]"
msgstr ""

#: of pyQCat.context.ExperimentContext.compensates:1
msgid "Pulse compensates."
msgstr ""

#: of pyQCat.context.ExperimentContext.compensates:3
msgid ""
":py:class:`~typing.Dict`\\[:py:class:`~pyQCat.qubit.qubit.BaseQubit`, "
":py:class:`~pyQCat.pulse.base_pulse.PulseCorrection`]"
msgstr ""

#: of pyQCat.context.ExperimentContext.discriminators:1
msgid "IQ discriminators."
msgstr ""

#: of pyQCat.context.ExperimentContext.discriminators:3
msgid ""
":py:data:`~typing.Union`\\[:py:class:`~pyQCat.analysis.algorithms.iqprobability.IQdiscriminator`,"
" "
":py:class:`~typing.List`\\[:py:class:`~pyQCat.analysis.algorithms.iqprobability.IQdiscriminator`]]"
msgstr ""

#: of pyQCat.context.ExperimentContext.working_dc:1
msgid "Working dc."
msgstr ""

#: of pyQCat.context.ExperimentContext.crosstalk_dict:1
msgid "crosstalk.json data."
msgstr ""

#: of pyQCat.context.ExperimentContext._init_context:1
msgid ""
"Initialize the experimental environment, it establishes the MongoDB "
"service connection of the data collector, establishes the Minio service "
"connection of the object storage, and defines the database collection "
"name set by the user."
msgstr ""

#: of pyQCat.context.ExperimentContext._set_env:1
msgid "Set Invoker environment."
msgstr ""

#: of pyQCat.context.ExperimentContext._open_mongo_context:1
msgid ""
"Initialize the mongo environment, it establishes the MongoDB service "
"connection of the data collector and defines the database collection name"
" set by the user."
msgstr ""

#: of pyQCat.context.ExperimentContext.minimize_compensate:1
msgid "Only open qubits and couplers which plays roles in experiment."
msgstr ""

#: of pyQCat.context.ExperimentContext.maximize_compensate:1
msgid "Open all qubits and couplers include environmnet's variables."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_inst:1
msgid "Configure qaio instrument."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_inst:3
msgid ""
"The qaio type is obtained from the configuration file by default, "
"currently only 8-bit and 30-bit are supported."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_coupler_cali_infos
#: pyQCat.context.ExperimentContext.configure_coupler_probe_qubit_cali_infos
#: pyQCat.context.ExperimentContext.configure_couplers
#: pyQCat.context.ExperimentContext.configure_dcm
#: pyQCat.context.ExperimentContext.configure_environment
#: pyQCat.context.ExperimentContext.configure_inst
#: pyQCat.context.ExperimentContext.configure_qubits
#: pyQCat.context.ExperimentContext.get_ac_spectrum_paras
#: pyQCat.context.ExperimentContext.get_base_qubit
#: pyQCat.context.ExperimentContext.get_coupler
#: pyQCat.context.ExperimentContext.get_dc_spectrum_paras
#: pyQCat.context.ExperimentContext.get_qubit
#: pyQCat.context.ExperimentContext.transform_coupler_to_qubits
#: pyQCat.parameters.corrected_dc pyQCat.parameters.decorate_dc
#: pyQCat.parameters.get_parameters pyQCat.structures.ExperimentData.child_data
#: pyQCat.structures.ExperimentData.from_data
#: pyQCat.structures.Options.set_validator
msgid "Parameters"
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_inst:7
msgid "Qaio type, 8 or 30. Defaults to None."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_qubits:1
msgid "Configure qubits."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_qubits:3
msgid ""
"Configure qubits, you can output int, str, List, and Qubit types, after "
"obtaining qubits, it will be added to the _qubits property."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_qubits:7
msgid "Qubit number, name, list or object."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_coupler_cali_infos
#: pyQCat.context.ExperimentContext.configure_coupler_probe_qubit_cali_infos
#: pyQCat.context.ExperimentContext.configure_couplers
#: pyQCat.context.ExperimentContext.configure_dcm
#: pyQCat.context.ExperimentContext.configure_environment
#: pyQCat.context.ExperimentContext.configure_qubits
#: pyQCat.context.ExperimentContext.transform_coupler_to_qubits
#: pyQCat.parameters.corrected_dc pyQCat.parameters.decorate_dc
#: pyQCat.structures.ExperimentData.child_data
#: pyQCat.structures.ExperimentData.from_data
msgid "Returns"
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_qubits:10
msgid "The qubit information needed for the experiment."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_couplers:1
msgid "Configure couplers."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_couplers:3
msgid ""
"Configure couplers, you can output int, str, List, and Coupler types, "
"after obtaining couplers, it will be added to the _couplers property."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_couplers:7
msgid "Coupler number, name, list or object."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_couplers:10
msgid "The coupler information needed for the experiment."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_coupler_cali_infos:1
msgid "Configure coupler base experiment physical qubit information."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_coupler_cali_infos:3
msgid ""
"It first generates a Coupler object, and then maps the Coupler object "
"into drive qubits and read qubits. Experimenting with the Coupler base in"
" this way ensures the general configuration of the experiment."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_coupler_cali_infos:8
#: pyQCat.context.ExperimentContext.configure_coupler_probe_qubit_cali_infos:7
#: pyQCat.context.ExperimentContext.get_coupler:4
msgid "Coupler number or name."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_coupler_cali_infos:11
#: pyQCat.context.ExperimentContext.transform_coupler_to_qubits:7
msgid "Drive qubit and probe qubit."
msgstr ""

#: of
#: pyQCat.context.ExperimentContext.configure_coupler_probe_qubit_cali_infos:1
msgid "Configure coupler probe qubit calibration process experiment context."
msgstr ""

#: of
#: pyQCat.context.ExperimentContext.configure_coupler_probe_qubit_cali_infos:3
msgid ""
"The Coupler needs to characterize the X gate attribute of the read bit in"
" advance. This method is only used to extract the probe qubit."
msgstr ""

#: of
#: pyQCat.context.ExperimentContext.configure_coupler_probe_qubit_cali_infos:10
msgid "Experiment coupler."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_dcm:1
msgid "Configure IQ classifier."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_dcm:3
msgid "Get IQ classifier, supports int, str and List types."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_dcm:6
msgid "Base qubit number or number."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_dcm:9
msgid "Union read qubit discriminator."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_dcm:12
msgid "IQ classifier."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_environment:1
msgid "Configure system environment."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_environment:3
msgid ""
"Configure the line compensator or voltage, note that duplicate numbers "
"need to be eliminated here."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_environment:7
#: pyQCat.context.ExperimentContext.get_base_qubit:4
msgid "Base qubit number or name."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_environment:10
msgid "System activates qubits and couplers."
msgstr ""

#: of pyQCat.context.ExperimentContext.configure_crosstalk_dict:1
msgid "Get crosstalk dict."
msgstr ""

#: of pyQCat.context.ExperimentContext.get_coupler:1
#: pyQCat.context.ExperimentContext.get_qubit:1
msgid "Get coupler from database."
msgstr ""

#: of pyQCat.context.ExperimentContext.get_qubit:4
msgid "Qubit number or name."
msgstr ""

#: of pyQCat.context.ExperimentContext.get_dc_spectrum_paras:1
msgid "Get DC Spectrum parameters."
msgstr ""

#: of pyQCat.context.ExperimentContext.get_ac_spectrum_paras:4
#: pyQCat.context.ExperimentContext.get_dc_spectrum_paras:4
msgid "Base qubit name."
msgstr ""

#: of pyQCat.context.ExperimentContext.get_ac_spectrum_paras:8
#: pyQCat.context.ExperimentContext.get_dc_spectrum_paras:8
msgid ":py:class:`~typing.List`"
msgstr ""

#: of pyQCat.context.ExperimentContext.get_ac_spectrum_paras:1
msgid "Get AC Spectrum parameters."
msgstr ""

#: of pyQCat.context.ExperimentContext.get_base_qubit:1
msgid "Get base qubit from database."
msgstr ""

#: of pyQCat.context.ExperimentContext.get_base_qubit:8
msgid ":py:class:`~pyQCat.qubit.qubit.BaseQubit`"
msgstr ""

#: of pyQCat.context.ExperimentContext.transform_coupler_to_qubits:1
#: pyQCat.parameters.transform_coupler_to_qubits:1
msgid "Transform coupler to drive qubit and probe qubit."
msgstr ""

#: of pyQCat.context.ExperimentContext.transform_coupler_to_qubits:4
msgid "Coupler object."
msgstr ""

#: of pyQCat.context.ExperimentContext.close_context:1
msgid "Close the link with MongoDB server."
msgstr ""

#: of pyQCat.context.ExperimentContext.clear_experiment_params:1
msgid ""
"Clear experiment parameters. When you want to clear the experimental "
"environment and reconfigure, you can call this interface."
msgstr ""

#: of pyQCat.context.ExperimentContext.validate:1
msgid "Validate parameters."
msgstr ""

#: ../../source/api/common.rst:21
msgid "pyQCat.errors module"
msgstr ""

#: of pyQCat.errors.AnalysisDataError:1 pyQCat.errors.ArgumentsError:1
#: pyQCat.errors.ChannelError:1 pyQCat.errors.CompensateError:1
#: pyQCat.errors.DatabaseQueryError:1 pyQCat.errors.DatabaseUpdateError:1
#: pyQCat.errors.ExperimentDataError:1 pyQCat.errors.ExperimentError:1
#: pyQCat.errors.IQdiscriminatorError:1 pyQCat.errors.InstrumentError:1
#: pyQCat.errors.InvalidValueError:1 pyQCat.errors.LoadConfigError:1
#: pyQCat.errors.ModuleError:1 pyQCat.errors.PulseError:1
#: pyQCat.errors.QfitError:1 pyQCat.errors.RawDataError:1
#: pyQCat.errors.SaveFileError:1 pyQCat.errors.ScheduleError:1
#: pyQCat.errors.SingleShotError:1 pyQCat.errors.SwapError:1
#: pyQCat.errors.SweepBindError:1 pyQCat.errors.VisualizationError:1
#: pyQCat.errors.WaveFormError:1
msgid "Bases: :py:class:`Exception`"
msgstr ""

#: of pyQCat.errors.ModuleChannelError:1 pyQCat.errors.ModuleNameError:1
#: pyQCat.errors.ModuleSetError:1
msgid "Bases: :py:class:`~pyQCat.errors.ModuleError`"
msgstr ""

#: of pyQCat.errors.ValidateError:1
msgid "Bases: :py:class:`~pyQCat.errors.LoadConfigError`"
msgstr ""

#: of pyQCat.errors.AnalysisError:1
msgid "Bases: :py:class:`~pyQCat.errors.PulseError`"
msgstr ""

#: ../../source/api/common.rst:29
msgid "pyQCat.log module"
msgstr ""

#: ../../source/api/common.rst:37
msgid "pyQCat.parameters module"
msgstr ""

#: of pyQCat.parameters:1
msgid "Get experimental parameters."
msgstr ""

#: of pyQCat.parameters.get_parameters:1
msgid "Get experimental parameters by name."
msgstr ""

#: of pyQCat.parameters.get_parameters:4
msgid "Map api field name."
msgstr ""

#: of pyQCat.parameters.get_parameters:7
msgid ""
"When obtaining the QAIO, it indicates the type(8 or 30), and when "
"obtaining the qubit parameters, it indicates the qubit number."
msgstr ""

#: of pyQCat.parameters.get_parameters:11
msgid "Fixed parameters, like bit_type."
msgstr ""

#: of pyQCat.parameters.get_parameters:14
msgid "Some api need additional parameters."
msgstr ""

#: of pyQCat.parameters.get_parameters:18
msgid ":py:data:`~typing.Any`"
msgstr ""

#: of pyQCat.parameters.corrected_dc:1
msgid "Calibrate the DC applied on the individual qubits."
msgstr ""

#: of pyQCat.parameters.corrected_dc:3
msgid ""
"If the ``sweet_point`` parameter is passed in, the bit corresponding to "
"``sweet_point`` takes the ``dc_max`` attribute, and the other qubits take"
" the ``dc_min`` attribute, and no distortion correction is required;"
msgstr ""

#: of pyQCat.parameters.corrected_dc:7
msgid ""
"If ``sweet_point`` is not passed in, then all bit voltage values take the"
" ``dc`` attribute of the bit and need to be corrected for distortion;"
msgstr ""

#: of pyQCat.parameters.corrected_dc:10
msgid ""
"If the ``static_qubits`` parameter is passed in, the voltage value is "
"obtained from the ``qubit`` instance in ``static_qubits``, The rest of "
"the bits are taken from the database."
msgstr ""

#: of pyQCat.parameters.corrected_dc:15
msgid "Number of bits for DC calibration."
msgstr ""

#: of pyQCat.parameters.corrected_dc:18
msgid "Number of couplers."
msgstr ""

#: of pyQCat.parameters.corrected_dc:20
msgid "Configuration file path to load qubit information."
msgstr ""

#: of pyQCat.parameters.corrected_dc:23
msgid "Working qubit number."
msgstr ""

#: of pyQCat.parameters.corrected_dc:26
msgid "Static custom qubit."
msgstr ""

#: of pyQCat.parameters.corrected_dc:29
msgid "True means use dc crosstalk calibrate dc value, default None."
msgstr ""

#: of pyQCat.parameters.corrected_dc:32
msgid "True means debug model, default True."
msgstr ""

#: of pyQCat.parameters.corrected_dc
#: pyQCat.structures.ExperimentData.child_data
#: pyQCat.structures.Options.set_validator
msgid "Raises"
msgstr ""

#: of pyQCat.parameters.corrected_dc:35
msgid "Dimension only sopport list and int type."
msgstr ""

#: of pyQCat.parameters.corrected_dc:37
msgid ""
"The voltage value corresponding to each channel,     the voltage finally "
"applied to the qubit."
msgstr ""

#: of pyQCat.parameters.corrected_dc:39
msgid "The voltage value corresponding to each channel,"
msgstr ""

#: of pyQCat.parameters.corrected_dc:40
msgid "the voltage finally applied to the qubit."
msgstr ""

#: of pyQCat.parameters.decorate_dc:1
msgid "DC Correction Using DC Crosstalk Matrix."
msgstr ""

#: of pyQCat.parameters.decorate_dc:4
msgid "The qubit input dc."
msgstr ""

#: of pyQCat.parameters.decorate_dc:7
msgid "Crosstalk qubit name list."
msgstr ""

#: of pyQCat.parameters.decorate_dc:10
msgid "Belong to the user data."
msgstr ""

#: of pyQCat.parameters.decorate_dc:13
msgid "Mark the simulator environment name."
msgstr ""

#: of pyQCat.parameters.decorate_dc:16
msgid "The vol of every DC channel."
msgstr ""

#: ../../source/api/common.rst:45
msgid "pyQCat.structures module"
msgstr ""

#: of pyQCat.structures:1
msgid "Data structure for pyQCat."
msgstr ""

#: of pyQCat.structures.Descriptor:1
msgid "Use a descriptor to set a value"
msgstr ""

#: of pyQCat.structures.Float:1 pyQCat.structures.Integer:1
#: pyQCat.structures.String:1
msgid "Bases: :py:class:`~pyQCat.structures.Descriptor`"
msgstr ""

#: of pyQCat.structures.UnsignedInteger:1
msgid "Bases: :py:class:`~pyQCat.structures.Integer`"
msgstr ""

#: of pyQCat.structures.UnsignedFloat:1
msgid "Bases: :py:class:`~pyQCat.structures.Float`"
msgstr ""

#: of pyQCat.structures.SizedString:1
msgid "Bases: :py:class:`~pyQCat.structures.String`"
msgstr ""

#: of pyQCat.structures.Cached:1 pyQCat.structures.Singleton:1
msgid "Bases: :py:class:`type`"
msgstr ""

#: of pyQCat.structures.Cached:1
msgid "Cached father class"
msgstr ""

#: of pyQCat.structures.Singleton:1
msgid "Singleton data structure"
msgstr ""

#: of pyQCat.structures.QDict:1
msgid "Bases: :py:class:`dict`"
msgstr ""

#: of pyQCat.structures.QDict.update:1
msgid ""
"If E is present and has a .keys() method, then does:  for k in E: D[k] = "
"E[k] If E is present and lacks a .keys() method, then does:  for k, v in "
"E: D[k] = v In either case, this is followed by: for k in F:  D[k] = F[k]"
msgstr ""

#: of pyQCat.structures.QDict.setdefault:1
msgid "Insert key with a value of default if key is not in the dictionary."
msgstr ""

#: of pyQCat.structures.QDict.setdefault:3
msgid "Return the value for key if key is in the dictionary, else default."
msgstr ""

#: of pyQCat.structures.Options:1
msgid "Bases: :py:class:`~pyQCat.structures.QDict`"
msgstr ""

#: of pyQCat.structures.Options:1
msgid "Base options object which pass parameters."
msgstr ""

#: of pyQCat.structures.Options.set_validator:1
msgid "Set an optional validator for a field in the options."
msgstr ""

#: of pyQCat.structures.Options.set_validator:5
msgid "The field name to set the validator on"
msgstr ""

#: of pyQCat.structures.Options.set_validator:7
msgid ""
"The value to use for the validator depending on the type indicates on how"
" the value for a field is enforced. If a tuple is passed in it must have "
"a length of two and will enforce the min and max value (inclusive) for an"
" integer or float value option. If it's a list it will list the valid "
"values for a field. If it's a ``type`` the validator will just enforce "
"the value is of a certain type."
msgstr ""

#: of pyQCat.structures.Options.set_validator:17
msgid "If field is not present in the options object"
msgstr ""

#: of pyQCat.structures.Options.set_validator:18
msgid "If the ``validator_value`` has an invalid value for a     given type"
msgstr ""

#: of pyQCat.structures.Options.set_validator:19
msgid "If ``validator_value`` is not a valid type"
msgstr ""

#: of pyQCat.structures.MetaData:1
msgid "Experiment metadata."
msgstr ""

#: of pyQCat.structures.ExperimentData:1
msgid "pyQCat Experiments Data container class."
msgstr ""

#: of pyQCat.structures.ExperimentData._set_child_data:1
msgid "Set child experiment data for the current experiment."
msgstr ""

#: of pyQCat.structures.ExperimentData.x_data:1
msgid "get x data."
msgstr ""

#: of pyQCat.structures.ExperimentData.y_data:1
msgid "get y data"
msgstr ""

#: of pyQCat.structures.ExperimentData.from_data:1
msgid "Create a experiment data object from a dataset."
msgstr ""

#: of pyQCat.structures.ExperimentData.from_data:4
msgid "Raw data which saved as numpy array type."
msgstr ""

#: of pyQCat.structures.ExperimentData.from_data:7
msgid "The y data labels."
msgstr ""

#: of pyQCat.structures.ExperimentData.from_data:10
msgid "Additional experiment metadata."
msgstr ""

#: of pyQCat.structures.ExperimentData.from_data:13
msgid "the experiment data object."
msgstr ""

#: of pyQCat.structures.ExperimentData.add_child_data:1
msgid "Add child experiment data to the current experiment data"
msgstr ""

#: of pyQCat.structures.ExperimentData.child_data:1
msgid "Return child experiment data."
msgstr ""

#: of pyQCat.structures.ExperimentData.child_data:4
msgid ""
"Index of the child experiment data to be returned. Several types are "
"accepted for convenience:      * None: Return all child data.     * int: "
"Specific index of the child data.     * slice: A list slice of indexes."
"     * str: experiment ID of the child data."
msgstr ""

#: of pyQCat.structures.ExperimentData.child_data:4
msgid ""
"Index of the child experiment data to be returned. Several types are "
"accepted for convenience:"
msgstr ""

#: of pyQCat.structures.ExperimentData.child_data:7
msgid "None: Return all child data."
msgstr ""

#: of pyQCat.structures.ExperimentData.child_data:8
msgid "int: Specific index of the child data."
msgstr ""

#: of pyQCat.structures.ExperimentData.child_data:9
msgid "slice: A list slice of indexes."
msgstr ""

#: of pyQCat.structures.ExperimentData.child_data:10
msgid "str: experiment ID of the child data."
msgstr ""

#: of pyQCat.structures.ExperimentData.child_data:12
msgid ""
":py:data:`~typing.Union`\\[:py:class:`~pyQCat.structures.ExperimentData`,"
" "
":py:class:`~typing.List`\\[:py:class:`~pyQCat.structures.ExperimentData`]]"
msgstr ""

#: of pyQCat.structures.ExperimentData.child_data:13
msgid "The requested single or list of child experiment data."
msgstr ""

#: of pyQCat.structures.ExperimentData.child_data:15
msgid "if the index or ID of the child experiment data     cannot be found."
msgstr ""

#: ../../source/api/common.rst:53
msgid "pyQCat.types module"
msgstr ""

#: of pyQCat.types.AnalysisDataStatus:1 pyQCat.types.CompensatePolicy:1
#: pyQCat.types.LogFormat:1 pyQCat.types.Quality:1
#: pyQCat.types.QubitFreqRoughStep:1 pyQCat.types.SaveType:1
msgid "Bases: :py:class:`str`, :py:class:`~enum.Enum`"
msgstr ""

#: of pyQCat.types.AnalysisDataStatus:1 pyQCat.types.CompensatePolicy:1
#: pyQCat.types.LogFormat:1 pyQCat.types.Quality:1
#: pyQCat.types.QubitFreqRoughStep:1 pyQCat.types.SaveType:1
msgid "An enumeration."
msgstr ""

#: of pyQCat.types.RespCode:1
msgid "Bases: :py:class:`~enum.Enum`"
msgstr ""

#: of pyQCat.types.RespCode:1
msgid "Response code."
msgstr ""

#: ../../source/api/common.rst:61
msgid "pyQCat.version module"
msgstr ""

#: ../../source/api/common.rst:69
msgid "Module contents"
msgstr ""

#: of pyQCat:3
msgid "pyQCat (:mod:`pyQCat`)"
msgstr ""

#: of pyQCat:8
msgid "Modules"
msgstr ""

#: of pyQCat:12
msgid ":mod:`~pyQCat.acquisition`"
msgstr ""

#: of pyQCat:13
msgid "数据采集器"
msgstr ""

#: of pyQCat:14
msgid ":mod:`~pyQCat.analysis`"
msgstr ""

#: of pyQCat:15
msgid "实验结果分析模块"
msgstr ""

#: of pyQCat:16
msgid ":mod:`~pyQCat.experiments`"
msgstr ""

#: of pyQCat:17
msgid "基础实验包."
msgstr ""

#: of pyQCat:18
msgid ":mod:`~pyQCat.gate`"
msgstr ""

#: of pyQCat:19
msgid "基础门"
msgstr ""

#: of pyQCat:20
msgid ":mod:`~pyQCat.instrument`"
msgstr ""

#: of pyQCat:21
msgid "设备"
msgstr ""

#: of pyQCat:22
msgid ":mod:`~pyQCat.invoker`"
msgstr ""

#: of pyQCat:23
msgid "数据交互中心"
msgstr ""

#: of pyQCat:24
msgid ":mod:`~pyQCat.preliminary`"
msgstr ""

#: of pyQCat:25
msgid "初扫实验"
msgstr ""

#: of pyQCat:26
msgid ":mod:`~pyQCat.pulse`"
msgstr ""

#: of pyQCat:27
msgid "基础波形"
msgstr ""

#: of pyQCat:28
msgid ":mod:`~pyQCat.tools`"
msgstr ""

#: of pyQCat:29
msgid "工具类"
msgstr ""

#: of pyQCat.get_version:1
msgid "Return the VERSION as a string."
msgstr ""

#: of pyQCat.get_version:3
msgid "For example, if `VERSION == (1, 11, 1)`, return '1.11.1'."
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`Exception`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.errors.ModuleError`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.errors.LoadConfigError`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.errors.PulseError`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.structures.Descriptor`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.structures.Integer`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.structures.Float`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.structures.String`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`type`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`dict`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.structures.QDict`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`str`, :py:class:`~enum.Enum`"
#~ msgstr ""

#~ msgid "Get mongo address."
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid "Get mongo user database address."
#~ msgstr ""

#~ msgid "Get qubit collection name."
#~ msgstr ""

#~ msgid "Get coupler collection name."
#~ msgstr ""

#~ msgid "Get config collection name."
#~ msgstr ""

#~ msgid ":py:class:`~typing.List`\\[:py:class:`~pyQCat.pulse.base_pulse.PulseCorrection`]"
#~ msgstr ""

#~ msgid "Get Operating point"
#~ msgstr ""

#~ msgid "Login user name."
#~ msgstr ""

#~ msgid "Configure line compensates."
#~ msgstr ""

#~ msgid ""
#~ "Configure the line compensator, note "
#~ "that duplicate numbers need to be "
#~ "eliminated here."
#~ msgstr ""

#~ msgid "Clear experiment parameters."
#~ msgstr ""

#~ msgid ""
#~ "When you want to clear the "
#~ "experimental environment and reconfigure, you"
#~ " can call this interface."
#~ msgstr ""

#~ msgid "Create new qubits."
#~ msgstr ""

#~ msgid "Create new couplers."
#~ msgstr ""

#~ msgid "Create new config parameters."
#~ msgstr ""

#~ msgid "Create chip context tool."
#~ msgstr ""

#~ msgid ""
#~ "It will create the specified bits "
#~ "and environment parameters in the "
#~ "database according to the configuration "
#~ "file."
#~ msgstr ""

#~ msgid "Bind user."
#~ msgstr ""

#~ msgid "Bases: :class:`Exception`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.errors.ModuleError`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.errors.LoadConfigError`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.errors.PulseError`"
#~ msgstr ""

#~ msgid ""
#~ "Fixed parameters, like bit or coupler"
#~ " sample, and get config file "
#~ "env_name."
#~ msgstr ""

#~ msgid "Mark the chip number."
#~ msgstr ""

#~ msgid "Mark qubit or coupler relate parameters."
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.structures.Descriptor`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.structures.Integer`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.structures.Float`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.structures.String`"
#~ msgstr ""

#~ msgid "Bases: :class:`type`"
#~ msgstr ""

#~ msgid "Bases: :class:`dict`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.structures.QDict`"
#~ msgstr ""

#~ msgid "Bases: :class:`str`, :class:`enum.Enum`"
#~ msgstr ""

#~ msgid ":mod:`~pyQCat.database`"
#~ msgstr ""

#~ msgid "基础数据库映射数据结构."
#~ msgstr ""

#~ msgid ":mod:`~pyQCat.schema`"
#~ msgstr ""

#~ msgid "待开发"
#~ msgstr ""

