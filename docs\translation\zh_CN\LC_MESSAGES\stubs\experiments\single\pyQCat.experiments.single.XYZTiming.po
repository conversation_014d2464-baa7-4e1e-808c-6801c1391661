# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:2
msgid "pyQCat.experiments.single.XYZTiming"
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.XYZTiming:1
msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.XYZTiming:1
msgid "XYZTiming Experiment, get the qubit xy or z line delay value."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.__init__:1
msgid "Create a new experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.single.XYZTiming.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`acquire_pulse "
"<pyQCat.experiments.single.XYZTiming.acquire_pulse>`\\ "
"\\(acq\\_pulse\\[\\, qubit\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Set measure pulse."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`cal_fidelity "
"<pyQCat.experiments.single.XYZTiming.cal_fidelity>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Calculate the fidelity matrix."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`experiment_info "
"<pyQCat.experiments.single.XYZTiming.experiment_info>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Show experiment informations."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.single.XYZTiming.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.single.XYZTiming.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`get_xy_pulse "
"<pyQCat.experiments.single.XYZTiming.get_xy_pulse>`\\ \\(qubit\\, "
"const\\_delay\\, max\\_delay\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`get_z_pulse <pyQCat.experiments.single.XYZTiming.get_z_pulse>`\\"
" \\(qubit\\, xy\\_drag\\_time\\, ...\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`jupyter_schedule "
"<pyQCat.experiments.single.XYZTiming.jupyter_schedule>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Show experiment pulses's schedules in Jupyter Notebook."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.single.XYZTiming.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`play_pulse <pyQCat.experiments.single.XYZTiming.play_pulse>`\\ "
"\\(name\\, base\\_qubit\\, pulse\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Play pulse for XY control and Z flux control."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`plot_schedule "
"<pyQCat.experiments.single.XYZTiming.plot_schedule>`\\ "
"\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
"Plot the pulse schedule which is very important to confirm experiment "
"logical correction."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.single.XYZTiming.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
#: of pyQCat.experiments.single.xyz_timing.XYZTiming.run:1
msgid "Run XYZTiming experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.single.XYZTiming.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.single.XYZTiming.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_multiple_IF "
"<pyQCat.experiments.single.XYZTiming.set_multiple_IF>`\\ \\(\\*IF\\[\\, "
"channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Used for multiple qubits experiment, set mutiple IF."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_multiple_index "
"<pyQCat.experiments.single.XYZTiming.set_multiple_index>`\\ "
"\\(\\*args\\[\\, channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
"Used for multiple qubits experiment, set mutiple index. :type args: "
":py:class:`list` :param args: Setting the start and end values :type "
"channel: :py:class:`int` :param channel: channel settings Usage::     "
">>>experiment = TopExperiment()     >>>experiment.set_multiple_index([0, "
"1500], [100, 900], channel=1)."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.single.XYZTiming.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.single.XYZTiming.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`set_sweep_order "
"<pyQCat.experiments.single.XYZTiming.set_sweep_order>`\\ "
"\\(order\\_list\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid "Old interface."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:41:<autosummary>:1
msgid ""
":py:obj:`sweep_readout_trigger_delay "
"<pyQCat.experiments.single.XYZTiming.sweep_readout_trigger_delay>`\\ "
"\\(channel\\, sweep\\_delay\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.XYZTiming.rst:43
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.single.XYZTiming.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.single.XYZTiming.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.single.XYZTiming.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`run_options <pyQCat.experiments.single.XYZTiming.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:9
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:5
msgid "delay_list (List, np.ndarray): The list of delays that will"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:5
msgid "be scanned in the experiment, in nanoseconds."
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:7
msgid ""
"const_delay (float): Set XY line const delay. z_amp (float): A ac pulse "
"amplitude."
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_run_options
#: pyQCat.experiments.single.xyz_timing.XYZTiming._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options:11
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_experiment_options:11
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_run_options:4
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options:1
msgid "Default analysis options."
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options:9
msgid "Analysis Options:"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options:4
msgid "quality_bounds (Iterable[float]): The bounds value of the"
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options:5
msgid "goodness of fit."
msgstr ""

#: of
#: pyQCat.experiments.single.xyz_timing.XYZTiming._default_analysis_options:6
msgid ""
"is_plot (bool): Plot or not analysis fit photo. sample_rate (float): "
"Sample rate, unit GHz. data_key (List[str]): List of mark select data."
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.XYZTiming._default_run_options:1
msgid "Default options values for the experiment :meth:`run` method."
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.XYZTiming._set_xy_pulses:1
msgid "Set XY pulses."
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.XYZTiming._set_z_pulses:1
msgid "Set Z pulses."
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.XYZTiming._metadata:1
msgid "Set metadata."
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.XYZTiming._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#: of pyQCat.experiments.single.xyz_timing.XYZTiming._check_options:1
msgid "Check options."
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.single.XYZTiming.__init__>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`acquire_pulse "
#~ "<pyQCat.experiments.single.XYZTiming.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.single.XYZTiming.from_experiment_context>`\\ "
#~ "\\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.single.XYZTiming.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_xy_pulse "
#~ "<pyQCat.experiments.single.XYZTiming.get_xy_pulse>`\\ "
#~ "\\(qubit\\, const\\_delay\\, max\\_delay\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_z_pulse "
#~ "<pyQCat.experiments.single.XYZTiming.get_z_pulse>`\\ \\(qubit\\,"
#~ " xy\\_drag\\_time\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`play_pulse "
#~ "<pyQCat.experiments.single.XYZTiming.play_pulse>`\\ \\(name\\,"
#~ " base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot_schedule "
#~ "<pyQCat.experiments.single.XYZTiming.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`run <pyQCat.experiments.single.XYZTiming.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.single.XYZTiming.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.single.XYZTiming.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_IF "
#~ "<pyQCat.experiments.single.XYZTiming.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_index "
#~ "<pyQCat.experiments.single.XYZTiming.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.single.XYZTiming.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.single.XYZTiming.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_sweep_order "
#~ "<pyQCat.experiments.single.XYZTiming.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":py:obj:`analysis <pyQCat.experiments.single.XYZTiming.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.single.XYZTiming.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.single.XYZTiming.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.single.XYZTiming.run_options>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.experiments.single.XYZTiming.__init__>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`acquire_pulse "
#~ "<pyQCat.experiments.single.XYZTiming.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_info "
#~ "<pyQCat.experiments.single.XYZTiming.experiment_info>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.single.XYZTiming.from_experiment_context>`\\ "
#~ "\\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.single.XYZTiming.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_xy_pulse "
#~ "<pyQCat.experiments.single.XYZTiming.get_xy_pulse>`\\ "
#~ "\\(qubit\\, const\\_delay\\, max\\_delay\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_z_pulse "
#~ "<pyQCat.experiments.single.XYZTiming.get_z_pulse>`\\ \\(qubit\\,"
#~ " xy\\_drag\\_time\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`jupyter_schedule "
#~ "<pyQCat.experiments.single.XYZTiming.jupyter_schedule>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.single.XYZTiming.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`play_pulse "
#~ "<pyQCat.experiments.single.XYZTiming.play_pulse>`\\ \\(name\\,"
#~ " base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot_schedule "
#~ "<pyQCat.experiments.single.XYZTiming.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`run <pyQCat.experiments.single.XYZTiming.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.single.XYZTiming.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.single.XYZTiming.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_IF "
#~ "<pyQCat.experiments.single.XYZTiming.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_index "
#~ "<pyQCat.experiments.single.XYZTiming.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.single.XYZTiming.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.single.XYZTiming.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_sweep_order "
#~ "<pyQCat.experiments.single.XYZTiming.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.single.XYZTiming.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.single.XYZTiming.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.single.XYZTiming.experiment_options>`\\"
#~ msgstr ""

#~ msgid ":obj:`run_options <pyQCat.experiments.single.XYZTiming.run_options>`\\"
#~ msgstr ""

