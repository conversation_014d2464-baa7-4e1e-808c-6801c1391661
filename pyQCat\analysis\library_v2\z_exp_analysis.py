# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2022-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/01/08
# __author:       <PERSON> Fang


from ..standard_curve_analysis import StandardCurveAnalysis
from ...structures import Options


class ZExpAnalysis(StandardCurveAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.x_label = "N"

        return options
