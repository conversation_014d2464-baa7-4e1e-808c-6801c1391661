﻿pyQCat.analysis.library.AmpOptAnalysis
======================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: AmpOptAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~AmpOptAnalysis.__init__
      ~AmpOptAnalysis.from_sub_analysis
      ~AmpOptAnalysis.run_analysis
      ~AmpOptAnalysis.set_options
      ~AmpOptAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~AmpOptAnalysis.analysis_datas
      ~AmpOptAnalysis.data_filter
      ~AmpOptAnalysis.drawer
      ~AmpOptAnalysis.experiment_data
      ~AmpOptAnalysis.has_child
      ~AmpOptAnalysis.options
      ~AmpOptAnalysis.quality
      ~AmpOptAnalysis.results
   
   