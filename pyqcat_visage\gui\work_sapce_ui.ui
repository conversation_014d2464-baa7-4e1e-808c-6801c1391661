<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>647</width>
    <height>455</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>WorkSpace</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_2">
    <item>
     <widget class="QGroupBox" name="groupBox">
      <property name="title">
       <string>User WorkSpace Configuration</string>
      </property>
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="0">
        <widget class="QWidget" name="widget_5" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="1,7">
          <item>
           <widget class="QLabel" name="label_4">
            <property name="text">
             <string>qubit</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QMultiComboBox" name="QubitBox"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QWidget" name="widget_3" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,7">
          <item>
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>config</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QMultiComboBox" name="ConfigBox"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QWidget" name="widget_4" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="1,7">
          <item>
           <widget class="QLabel" name="label_3">
            <property name="text">
             <string>point_label</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QMultiComboBox" name="PointBox"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QWidget" name="widget_6" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="1,7">
          <item>
           <widget class="QLabel" name="label_5">
            <property name="text">
             <string>bit_attr</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QMultiComboBox" name="AttrBox"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="4" column="0">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>0</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="5" column="0">
        <widget class="QWidget" name="widget_2" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_6">
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>249</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="SaveConfButton">
            <property name="text">
             <string>Save</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>249</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item row="6" column="0">
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>0</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QGroupBox" name="groupBox_3">
      <property name="title">
       <string>Setting</string>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <spacer name="horizontalSpacer_9">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>74</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QCheckBox" name="autoPullCheck">
         <property name="text">
          <string>Auto PULL</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_10">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>74</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QCheckBox" name="autoPushCheck">
         <property name="text">
          <string>Auto PUSH</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_11">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>74</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="autoPullPushSave">
         <property name="text">
          <string>save</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QGroupBox" name="groupBox_4">
      <property name="title">
       <string>Manual-Sync</string>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <item>
        <spacer name="horizontalSpacer_12">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>80</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pullButton">
         <property name="font">
          <font>
           <bold>true</bold>
          </font>
         </property>
         <property name="text">
          <string>PULL</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_13">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>80</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton">
         <property name="font">
          <font>
           <bold>true</bold>
          </font>
         </property>
         <property name="text">
          <string>PUSH</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_14">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>80</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="ActionRefresh"/>
  </widget>
  <action name="ActionRefresh">
   <property name="text">
    <string>refresh</string>
   </property>
   <property name="toolTip">
    <string>Refresh the options in the drop-down menu below</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QMultiComboBox</class>
   <extends>QComboBox</extends>
   <header>.widgets.combox_custom.combox_multi</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>autoPullPushSave</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>change_auto_option()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>454</x>
     <y>421</y>
    </hint>
    <hint type="destinationlabel">
     <x>280</x>
     <y>290</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pullButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>pull_data()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>195</x>
     <y>515</y>
    </hint>
    <hint type="destinationlabel">
     <x>280</x>
     <y>290</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>push_data()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>365</x>
     <y>515</y>
    </hint>
    <hint type="destinationlabel">
     <x>280</x>
     <y>290</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>ActionRefresh</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>refresh_query_data()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>280</x>
     <y>203</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>SaveConfButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>save_space_conf()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>280</x>
     <y>227</y>
    </hint>
    <hint type="destinationlabel">
     <x>280</x>
     <y>203</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>change_auto_option()</slot>
  <slot>pull_data()</slot>
  <slot>push_data()</slot>
  <slot>save_space_conf()</slot>
  <slot>refresh_query_data()</slot>
 </slots>
</ui>
