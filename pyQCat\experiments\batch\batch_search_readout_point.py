# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/01/03
# __author:       <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

from __future__ import annotations

import json
import os
from collections import defaultdict
from pathlib import Path

import numpy as np

from ...log import pyqlog
from ...qubit import Qubit
from ...structures import CommonDict, Options
from ...tools import get_bound_ac_spectrum, qarange
from ..batch_experiment import BatchExperiment


class BatchSearchReadoutPoint(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.flows = [
            "CavityFreqSpectrum",
            "ReadoutFreqCalibrate",
            "ReadoutAmpCalibration",
            "SampleWidthOptimize",
            "SingleShot",
        ]
        options.points = 30
        options.amp_map = {}
        options.auto_set_readout_point = True
        options.fix_point = False
        options.all_point = True
        options.scan_mode = "amp"
        options.set_validator("filter_mode", ["fm", "f0", "f1"])
        options.filter_mode = "fm"
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.point_record = defaultdict(dict)
        options.point_path = None
        options.best_fidelity_map = {}

        options.pass_units = None
        options.fail_units = None

        return options

    def _batch_up(self):
        super()._batch_up()
        point_path_dir = os.path.dirname(self.run_options.record_path)
        point_path = os.path.join(point_path_dir, "point_record.json")
        best_amp_path = os.path.join(point_path_dir, "best_amp.json")
        self.run_options.point_path = point_path
        self.run_options.best_amp_path = best_amp_path
        self.run_options.dirs = point_path_dir

    def _count_max_sweep_count(self, units):
        if self.experiment_options.auto_set_readout_point:
            point_map = {}
            for unit in units:
                amp_list = self._get_readout_point_list(unit)
                point_map.update({unit: amp_list})
            self.experiment_options.amp_map = point_map
        return max([len(v) for v in self.experiment_options.amp_map.values()])

    def _get_readout_point_list(self, unit):
        qubit: Qubit | None = self.context_manager.chip_data.cache_qubit.get(unit)

        if not qubit:
            return []

        # default set readout point freq to none
        readout_point = qubit.readout_point.get("amp")
        qubit.readout_point.freq = None

        # fix readout point
        if self.experiment_options.fix_point:
            return [readout_point]

        # auto scope max and min
        if qubit.tunable:
            if self.experiment_options.scan_mode == "amp":
                # scan amp mode
                max_point = qubit.dc_max
                min_point = qubit.dc_min
                idle_point = qubit.idle_point
                mid_amp_list = np.linspace(
                    max_point, min_point, self.experiment_options.points
                ).tolist()
            else:
                # scan freq mode
                freq_max, freq_min = get_bound_ac_spectrum(qubit)
                if freq_max == freq_min:
                    return [freq_max]
                else:
                    return qarange(
                        np.ceil(freq_min),
                        np.floor(freq_max),
                        self.experiment_options.points,
                    )

            expect_amp_list = [amp for amp in mid_amp_list if abs(amp) < 0.48]
            amp_list = [
                round(amp - idle_point - max_point, 5) for amp in expect_amp_list
            ]
            distance = abs(np.array(amp_list) - readout_point)
            sort_index = distance.argsort()
            amp_list = np.array(amp_list)[sort_index]
        else:
            # tunable false fix point 0
            amp_list = [0]
        return amp_list

    def _change_read_point(self, i, units):
        working_units = []
        amp_map = self.experiment_options.amp_map
        for unit in units:
            if i < len(amp_map.get(unit)):
                read_point = amp_map.get(unit)[i]
                qubit: Qubit = self.context_manager.chip_data.cache_qubit.get(unit)  # type: ignore
                qubit.Mwave.amp = 0.08
                if self.experiment_options.scan_mode == "amp":
                    qubit.readout_point.amp = read_point
                else:
                    qubit.readout_point.freq = read_point
                working_units.append(unit)
                pyqlog.info(f"Change {unit} readout point {read_point}")
                self.run_options.dir_describe[unit] = f"ReadoutPoint-{read_point}"
        return working_units

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        def record_fm(
            _q: Qubit,
            point: float,
            _res: CommonDict,
            is_best: bool = True,
        ):
            if is_best is True:
                _res.update({self.experiment_options.scan_mode: point})
                self.run_options.best_fidelity_map[unit] = _res

            with open(
                os.path.join(self.run_options.dirs, f"{_q.name}_{point}.json"),
                mode="w",
                encoding="utf-8",
            ) as f:
                data = _q.to_dict()
                json.dump(data, f, indent=4, ensure_ascii=False)

        if "SingleShot" in exp.label:
            for unit in physical_units:
                qubit = self.context_manager.chip_data.get_physical_unit(unit)
                dcm = record.analysis_data.get(unit).result.get("dcm")
                is_pass = True if unit in record.pass_units else False

                f0 = dcm.get("fidelity")[0]
                f1 = dcm.get("fidelity")[1]
                outlier = dcm.get("outlier")
                cur_fm = np.mean(dcm.get("fidelity"))
                res = {
                    "is_pass": is_pass,
                    "f0": f0,
                    "f1": f1,
                    "fm": cur_fm,
                    "outlier": outlier,
                }

                if is_pass is False:
                    res.update(
                        {
                            "fail_node": exp_name,
                            "fail_reason": record.fail_reason,
                        }
                    )
                read_point = getattr(
                    qubit.readout_point, self.experiment_options.scan_mode
                )
                self.run_options.point_record[unit].update({str(read_point): res})
                if unit not in self.run_options.best_fidelity_map:
                    record_fm(qubit, read_point, res, is_best=True)
                else:
                    filter_mode = self.experiment_options.filter_mode
                    pre_f = self.run_options.best_fidelity_map[unit][filter_mode]
                    cur_f = res[filter_mode]
                    # bugfix: Error Case: pass fm < pre bad fm
                    pre_pass = self.run_options.best_fidelity_map[unit]["is_pass"]
                    if pre_pass is False:
                        is_best = True if is_pass is True else pre_f < cur_f
                    else:
                        is_best = pre_f < cur_f if is_pass is True else False
                    record_fm(qubit, read_point, res, is_best=is_best)
        else:
            for unit in record.bad_units:
                qubit = self.context_manager.chip_data.get_physical_unit(unit)
                self.run_options.point_record[unit].update(
                    {
                        str(qubit.readout_point.amp): {
                            "is_pass": False,
                            "fail_node": exp_name,
                            "fail_reason": record.fail_reason,
                        }
                    }
                )

        with open(self.run_options.point_path, mode="w", encoding="utf-8") as fp:
            json.dump(self.run_options.point_record, fp, indent=4, ensure_ascii=False)

        with open(self.run_options.best_amp_path, mode="w", encoding="utf-8") as fp:
            json.dump(
                self.run_options.best_fidelity_map, fp, indent=4, ensure_ascii=False
            )

        return record

    def _run_batch(self):
        all_units = self.experiment_options.physical_units
        all_good_units = []
        bad_units = []
        sweep_count = self._count_max_sweep_count(all_units)

        group_map = self.parallel_allocator_for_qc(all_units)

        for group_name, group in group_map.items():
            pyqlog.info(f"start calibration of {group_name}")
            for i in range(sweep_count):
                working_units = self._change_read_point(i, group)
                if working_units:
                    pass_units = self._run_flow(
                        flows=self.experiment_options.flows,
                        physical_units=working_units,
                        name=f"{group_name} search read point ({i + 1}/{sweep_count})",
                    )
                    if pass_units and not self.experiment_options.all_point:
                        for unit in pass_units:
                            all_good_units.append(unit)
                            group.remove(unit)
            bad_units += group
        pyqlog.info(f"pass_units:{all_good_units},bad_units:{bad_units}")

    def _batch_down(self):
        super()._batch_down()

        self.run_options.pass_units = list(self.run_options.best_fidelity_map.keys())
        self.run_options.fail_units = [
            unit
            for unit in self.experiment_options.physical_units
            if unit not in self.run_options.pass_units
        ]

        for unit, params in self.run_options.best_fidelity_map.items():
            json_name = f"{unit}_{params.get(self.experiment_options.scan_mode)}.json"
            with open(str(Path(self.run_options.dirs, json_name)), mode="r") as f:
                bit_data = json.load(f)
            qubit = self.context_manager.chip_data.get_physical_unit(unit)
            new_qubit = qubit.from_dict(bit_data)
            new_qubit.save_data()
            self.context_manager.chip_data.cache_qubit.update({unit: new_qubit})
