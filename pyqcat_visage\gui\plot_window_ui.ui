<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindowPlot</class>
 <widget class="QMainWindow" name="MainWindowPlot">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="iconSize">
   <size>
    <width>24</width>
    <height>24</height>
   </size>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>33</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar">
   <property name="enabled">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QToolBarExpanding" name="toolBar">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <property name="iconSize">
    <size>
     <width>20</width>
     <height>20</height>
    </size>
   </property>
   <property name="toolButtonStyle">
    <enum>Qt::ToolButtonStyle::ToolButtonTextUnderIcon</enum>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionPan"/>
   <addaction name="actionAuto"/>
   <addaction name="separator"/>
   <addaction name="actionCoords"/>
   <addaction name="actionConnectors"/>
   <addaction name="separator"/>
   <addaction name="actionRuler"/>
  </widget>
  <action name="actionPan">
   <property name="icon">
    <iconset>
     <normalon>:/plot/pan</normalon>
    </iconset>
   </property>
   <property name="text">
    <string>Help</string>
   </property>
   <property name="shortcut">
    <string>P</string>
   </property>
  </action>
  <action name="actionZoom">
   <property name="icon">
    <iconset>
     <normaloff>:/plot/zoom</normaloff>:/plot/zoom</iconset>
   </property>
   <property name="text">
    <string>Zoom</string>
   </property>
   <property name="toolTip">
    <string>Zoom control</string>
   </property>
   <property name="shortcut">
    <string>Z</string>
   </property>
  </action>
  <action name="actionConnectors">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset>
     <normaloff>:/connectors</normaloff>:/connectors</iconset>
   </property>
   <property name="text">
    <string>Pins</string>
   </property>
   <property name="toolTip">
    <string>Show connector pins for selected qcomponents</string>
   </property>
   <property name="shortcut">
    <string>C</string>
   </property>
  </action>
  <action name="actionCoords">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset>
     <normaloff>:/plot/point</normaloff>:/plot/point</iconset>
   </property>
   <property name="text">
    <string>Get point</string>
   </property>
   <property name="toolTip">
    <string>Click for position --- Enable this to click on the plot and log the (x,y) position</string>
   </property>
   <property name="shortcut">
    <string>P</string>
   </property>
  </action>
  <action name="actionAuto">
   <property name="icon">
    <iconset>
     <normaloff>:/plot/autozoom</normaloff>:/plot/autozoom</iconset>
   </property>
   <property name="text">
    <string>Autoscale</string>
   </property>
   <property name="toolTip">
    <string>Auto Zoom</string>
   </property>
   <property name="shortcut">
    <string>A</string>
   </property>
  </action>
  <action name="actionReplot">
   <property name="icon">
    <iconset>
     <normaloff>:/plot/refresh_plot</normaloff>:/plot/refresh_plot</iconset>
   </property>
   <property name="text">
    <string>Replot</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+R</string>
   </property>
  </action>
  <action name="actionRuler">
   <property name="icon">
    <iconset>
     <normaloff>:/plot/ruler</normaloff>:/plot/ruler</iconset>
   </property>
   <property name="text">
    <string>Ruler</string>
   </property>
   <property name="toolTip">
    <string>Activate the ruler</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QToolBarExpanding</class>
   <extends>QToolBar</extends>
   <header>.widgets.bases.expanding_toolbar</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="_imgs/_imgs.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>actionAuto</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowPlot</receiver>
   <slot>auto_scale()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>399</x>
     <y>299</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionConnectors</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowPlot</receiver>
   <slot>set_show_pins(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>399</x>
     <y>299</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionCoords</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowPlot</receiver>
   <slot>set_position_track(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>399</x>
     <y>299</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionPan</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowPlot</receiver>
   <slot>pan()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>399</x>
     <y>299</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionZoom</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowPlot</receiver>
   <slot>zoom()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>399</x>
     <y>299</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionReplot</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowPlot</receiver>
   <slot>replot()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>399</x>
     <y>299</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>auto_scale()</slot>
  <slot>replot()</slot>
  <slot>pan()</slot>
  <slot>zoom()</slot>
  <slot>set_position_track(bool)</slot>
  <slot>set_show_pins(bool)</slot>
 </slots>
</ui>
