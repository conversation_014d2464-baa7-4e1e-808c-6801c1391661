# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/07/11
# __author:       SS Fang

import json
from copy import deepcopy
from pathlib import Path
from typing import Dict, Tuple

import numpy as np
import pandas as pd

from ....analysis.library.xy_cross_rw_analysis import XYCrossRwAnalysis
from ....errors import ExperimentOptionsError
from ....log import pyqlog
from ....qubit.qubit import Qubit, XYwave
from ....structures import MetaData, Options
from ....tools.savefile import LocalFile
from ....tools.utilities import qarange
from ....types import ExperimentRunMode, QualityDescribe, StandardContext
from ...composite_experiment import CompositeExperiment
from ...single import XYCrossRabiWidthOnce
from ..single_gate.qubit_freq_calibration import QubitFreqCalibration


class XYCrossRabiWidth(CompositeExperiment):
    """XY Cross by RabiScanWidth."""

    _sub_experiment_class = XYCrossRabiWidthOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()

        options.set_validator("case_mode", ["simple", "real"])
        options.set_validator("target_name", str)
        options.set_validator("bias_name_list", list)
        options.set_validator("cali_freq_flag", bool)
        options.set_validator("max_count", int)
        options.set_validator("max_distance", int)

        # Adjust Kong Zong `strength` issue, 2024-04-02
        options.set_validator("run_once_mode", ["normal", "strength"])
        options.set_validator("update_target_strength", bool)
        options.set_validator("target_widths", list)
        options.set_validator("bias_widths_1", list)
        options.set_validator("bias_widths_2", list)
        options.set_validator("target_expect", list)
        options.set_validator("bias_expect_1", list)
        options.set_validator("bias_expect_2", list)

        options.case_mode = "simple"
        options.target_name = ""
        options.bias_name_list = []
        options.cali_freq_flag = False
        options.max_count = 3
        options.max_distance = 3

        options.run_once_mode = "strength"
        options.update_target_strength = True
        options.target_widths = qarange(5, 200, 2.5)
        options.bias_widths_1 = qarange(5, 10000, 250)
        options.bias_widths_2 = qarange(5, 2000, 25)
        options.target_expect = [5, 100]
        options.bias_expect_1 = [0.1, 1.0]
        options.bias_expect_2 = [0.5, 10]

        options.set_validator("run_mode", [ExperimentRunMode.sync_mode])
        options.run_mode = ExperimentRunMode.sync_mode

        # Issue by Kong Zong, save coefficient when large than select_coe_threshold.
        options.set_validator("select_coe_threshold", float)
        options.select_coe_threshold = 0.01

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for DistortionT1Composite experiment."""
        options = super()._default_analysis_options()
        options.set_validator("n_multiple", float)

        options.cross_coe_map = {}
        options.cross_trust_map = {}
        options.n_multiple = 100

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method.

        Options:
            qubit_map (dict): Qubit name and Qubit object map.

        """
        options = super()._default_run_options()

        options.qubit_map = {}
        options.target_qubit = None  # Qubit object
        options.exp_total = 1

        options.drive_power = -10.0
        options.amp = 0.7
        options.drive_power_cc = 1.0
        options.amp_cc = 1.0

        options.drive_freq = None  # target_qubit.drive_freq
        options.xy_wave = None  # XYwave object
        options.discriminator = None  # IQdiscriminator object

        options.working_dc = {}
        options.ac_bias = {}
        options.bias_name_list = []
        options.index = 0

        # Issue, note bias drive_power and amp.
        options.b_drive_power = -10.0
        options.b_amp = 0.7
        options.cross_extra_map = {}

        options.support_context = [StandardContext.URM]

        return options

    def get_qubit_str(self):
        """Get qubit str, an args qubit for SaveFile class.

        Returns:
            qubit_str: string, use to create save data path.
        """
        target_name = self.experiment_options.target_name
        return target_name

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "case_mode": self.experiment_options.case_mode,
            "max_distance": self.experiment_options.max_distance,
        }
        return metadata

    def _check_options(self):
        """Check options."""
        super()._check_options()

        target_name = self.experiment_options.target_name
        bias_name_list = self.experiment_options.bias_name_list
        figsize = self.analysis_options.figsize

        qubit_map = {qubit.name: qubit for qubit in self.qubits}
        all_name_list = list(qubit_map.keys())

        if bias_name_list:
            b_name_list = [
                name if str(name).startswith("q") else f"q{name}"
                for name in bias_name_list
            ]
        else:
            b_name_list = list(qubit_map.keys())
            b_name_list.remove(target_name)
        b_name_list.sort(key=lambda x: int(x[1:]))

        # Check self.qubits contained target and bias bits.
        check_names = deepcopy(b_name_list)
        check_names.append(target_name)
        check_names_set = set(check_names)

        check_flag = True
        for name in check_names_set:
            if name not in all_name_list:
                pyqlog.error(f"{name} not in qubits {self.qubits}")
                if check_flag is True:
                    check_flag = False
        if check_flag is False:
            raise ExperimentOptionsError(
                self.label,
                msg=f"Please check qubits {self.qubits} is not contained "
                f"target {target_name}, or bias_name_list {b_name_list}",
            )

        target_qubit: Qubit = qubit_map.get(target_name)
        exp_total = len(bias_name_list) + 1

        drive_freq = target_qubit.drive_freq
        xy_wave: XYwave = deepcopy(target_qubit.XYwave)
        baseband = target_qubit.XYwave.baseband_freq
        xy_gap = target_qubit.inst.xy_gap
        # xy_wave.alpha = 0.0
        # xy_wave.detune_pi = 0.0
        # xy_wave.detune_pi2 = 0.0
        for bias in b_name_list:
            bias_qubit: Qubit = qubit_map.get(bias)
            bias_qubit.drive_freq = drive_freq
            bias_qubit.XYwave.baseband_freq = baseband
            bias_qubit.inst.xy_gap = xy_gap
            qubit_map[bias] = bias_qubit

        # Get target bit discriminator.
        if self.discriminator:
            if isinstance(self.discriminator, list):
                dcm_list = self.discriminator
            else:
                dcm_list = [self.discriminator]
            new_dcm = None
            for dcm in dcm_list:
                if target_name == dcm.name:
                    new_dcm = dcm
                    break
            pyqlog.info(f"Add {new_dcm.name} discriminator: {new_dcm}")
        else:
            new_dcm = None

        def change_dict(mode: str, data: Dict) -> Dict:
            """Change ac_bias or working_dc value."""
            for name in all_name_list:
                if name != target_name:
                    q_obj = qubit_map.get(name)
                    data[name][1] = q_obj.dc_min

            df = pd.DataFrame(data=data).T
            df.columns = ["channel", "value"]
            pyqlog.info(f"Target {target_name}, so change {mode} values:\n{df}")
            return data

        # Set bias bits in min point.
        if self.working_dc:
            new_working_dc = change_dict("working_dc", self.working_dc)
        else:
            new_working_dc = {}
        if self.ac_bias:
            new_ac_bias = change_dict("ac_bias", self.ac_bias)
        else:
            new_ac_bias = {}

        base_len = 10
        q_length = len(check_names_set)
        if figsize in [[12, 8], (12, 8)] and q_length > base_len:
            ratio = q_length / base_len
            figsize = (int(12 * ratio), int(8 * ratio))
        pyqlog.info(f"{self.label} analysis figsize: {figsize}")

        self.set_analysis_options(figsize=figsize)

        self.set_run_options(
            qubit_map=qubit_map,
            target_qubit=target_qubit,
            exp_total=exp_total,
            drive_freq=drive_freq,
            drive_power=target_qubit.drive_power,
            amp=xy_wave.Xpi,
            xy_wave=xy_wave,
            discriminator=new_dcm,
            working_dc=new_working_dc,
            ac_bias=new_ac_bias,
            bias_name_list=b_name_list,
        )

    async def _calibrate_target_frequency(self):
        """Calibrate target qubit frequency."""

        def recursive_set_options(_exp, **kwargs):
            _exp.set_run_options(**kwargs)
            if hasattr(_exp, "child_experiment"):
                child_exp = getattr(_exp, "child_experiment")
                child_exp.env_bit_resource = self.env_bit_resource
                recursive_set_options(child_exp, **kwargs)

        target_name = self.experiment_options.target_name
        target_qubit = self.run_options.target_qubit
        discriminator = self.run_options.discriminator
        working_dc = self.run_options.working_dc
        ac_bias = self.run_options.ac_bias

        fringes = [50, -50]
        delays = qarange(20, 120, 2.5)
        freq_gap_threshold = 0.5

        cali_freq_exp = QubitFreqCalibration(
            inst=self.inst,
            qubits=target_qubit,
            couplers=self.couplers,
            compensates=self.compensates,
            discriminators=discriminator,
            working_dc=working_dc,
            ac_bias=ac_bias,
            is_paternal=False,
            config=self.config,
        )
        cali_freq_exp.env_bit_resource = self.env_bit_resource
        cali_freq_exp.run_options.unit_map = Options(qubit=target_qubit)

        recursive_set_options(
            cali_freq_exp,
            config=self.run_options.config,
            crosstalk_dict=self.run_options.crosstalk_dict,
            online_bits=self.run_options.online_bits,
            online_dcms=self.run_options.online_dcms,
            online_coms=self.run_options.online_coms,
            xy_crosstalk_dict=self.run_options.xy_crosstalk_dict,
            # env_bit_dict=self.run_options.env_bit_dict,
            # username=self.run_options.username,
            read_env_bits=self.run_options.read_env_bits,
            unit_map=cali_freq_exp.run_options.unit_map,
            token=self.run_options.token,
        )

        cali_freq_exp.set_parent_file(self, f"cali_freq_{target_name}")
        cali_freq_exp.set_experiment_options(
            fringes=fringes,
            delays=delays,
        )
        # cali_freq_exp.set_child_exp_options(is_dynamic=0)
        cali_freq_exp.set_analysis_options(freq_gap_threshold=freq_gap_threshold)
        # cali_freq_exp.run()
        await cali_freq_exp.run_experiment()
        cali_freq_exp.check_exp_is_done(True)

        c_analysis = cali_freq_exp.analysis
        if c_analysis and c_analysis.quality.descriptor in [QualityDescribe.perfect, QualityDescribe.normal]:
            old_df = target_qubit.drive_freq
            old_bf = target_qubit.XYwave.baseband_freq
            pyqlog.info(
                f"Before calibrate, drive_freq: {old_df}, baseband_freq: {old_bf}"
            )

            xy_gap = round(old_df - old_bf, 3)
            new_df = c_analysis.results.f01.value
            new_bf = round(new_df - xy_gap, 3)
            target_qubit.drive_freq = new_df
            target_qubit.XYwave.baseband_freq = new_bf
            self.run_options.drive_freq = new_df
            self.run_options.xy_wave.baseband_freq = new_bf
            pyqlog.info(
                f"After calibrate, drive_freq: {new_df}, baseband_freq: {new_bf}"
            )
        else:
            pyqlog.warning(f"Calibrate {target_name} frequency failed!")

    async def _run_rabi_width(self, tb_qubit, count: int, name: str, **kwargs):
        """Run RabiScanWidth once."""
        target_name = self.experiment_options.target_name
        discriminator = self.run_options.discriminator
        working_dc = self.run_options.working_dc
        ac_bias = self.run_options.ac_bias
        target_qubit = deepcopy(self.run_options.target_qubit)

        kwargs.update({"xy_name": name, "rd_name": target_name})
        if name == target_name:
            qubit_list = [tb_qubit]
        else:
            # NOTE: chimera check common bus readout parameters
            if tb_qubit.inst.bus == target_qubit.inst.bus:
                tb_qubit.probe_power = target_qubit.probe_power
                tb_qubit.Mwave.amp = 0
            qubit_list = [tb_qubit, target_qubit]

        rabi_wd_exp = deepcopy(self.child_experiment)
        rabi_wd_exp.qubits = qubit_list
        rabi_wd_exp.discriminator = discriminator
        rabi_wd_exp.working_dc = working_dc
        rabi_wd_exp.ac_bias = ac_bias

        # rabi_wd_exp.xy_pulses.clear()
        # rabi_wd_exp.z_pulses.clear()
        # rabi_wd_exp.xy_pulses.update({qubit: [] for qubit in rabi_wd_exp.qubits})
        # rabi_wd_exp.z_pulses.update({qubit: [] for qubit in rabi_wd_exp.qubits})
        # rabi_wd_exp.z_pulses.update({coupler: [] for coupler in rabi_wd_exp.couplers})

        description = f"T{target_name}-S{name}-C{count}"
        rabi_wd_exp.set_parent_file(self, description, count)
        rabi_wd_exp.set_experiment_options(**kwargs)
        self._check_simulator_data(rabi_wd_exp, self.run_options.index)
        self.run_options.index += 1

        # rabi_wd_exp.run()
        # rabi_wd_exp.clear_params()
        # self._experiments.append(rabi_wd_exp)
        await rabi_wd_exp.run_experiment()
        rabi_wd_exp.check_exp_is_done(True)
        analysis_obj = rabi_wd_exp.analysis
        oscillating = analysis_obj.results.oscillating.value
        osci_freq = analysis_obj.results.freq.value
        return oscillating, abs(osci_freq)

    async def _run_once(self, name: str, idx: int) -> Tuple:
        """One bit run once logic."""
        case_mode = self.experiment_options.case_mode
        target_name = self.experiment_options.target_name
        max_count = self.experiment_options.max_count
        max_distance = self.experiment_options.max_distance

        qubit_map = self.run_options.qubit_map
        target_qubit = deepcopy(self.run_options.target_qubit)
        drive_freq = self.run_options.drive_freq
        xy_wave = deepcopy(self.run_options.xy_wave)

        oscillating = None
        osci_freq = None
        tb_qubit = deepcopy(qubit_map.get(name))
        # map_distance = abs(target_qubit.row - tb_qubit.row) + abs(
        #     target_qubit.col - tb_qubit.col
        # )
        map_distance = self.env_bit_resource.topology.bit_distance(
            target_qubit.name, tb_qubit.name
        )
        if name == target_name:
            his_amp = target_qubit.XYwave.Xpi
            his_drive_power = target_qubit.drive_power

            amp = his_amp
            drive_power = his_drive_power
            # widths = qarange(5, 50, 1.25)
            widths = np.hstack(
                [
                    np.arange(5, 200, 2.5),
                    np.arange(220, 300, 5),
                    np.arange(350, 550, 10),
                ]
            )

            count = 0
            run_flag = True
            tb_qubit.drive_freq = drive_freq
            tb_qubit.XYwave = xy_wave
            while count < max_count and run_flag is True:
                tb_qubit.XYwave.Xpi = amp
                pyqlog.info(
                    f"target: {target_name}, test: {name}, count: {count}, "
                    f"RabiScanWidth drive_power: {drive_power}, XYwave.Xpi: {amp}"
                )
                oscillating, osci_freq = await self._run_rabi_width(
                    tb_qubit,
                    count,
                    name,
                    drive_power=drive_power,
                    widths=widths,
                )

                # Update drive_power, amp logic.
                expect_value = 100
                if expect_value - 20 <= osci_freq <= expect_value + 20:
                    run_flag = False
                else:
                    power_update_step = 20 * np.log10(expect_value / osci_freq)
                    pyqlog.info(f"power_update_step: {power_update_step}")
                    new_drive_power = round(drive_power + power_update_step, 1)
                    if -40 < new_drive_power < -10:
                        drive_power = new_drive_power
                        new_amp = amp
                    elif new_drive_power > -10:
                        drive_power = -10
                        owe_power = new_drive_power - drive_power
                        new_amp = amp * (10 ** (owe_power / 20))
                    else:
                        drive_power = -40
                        owe_power = new_drive_power - drive_power
                        new_amp = amp * (10 ** (owe_power / 20))
                    if 0 < new_amp <= 0.95:
                        amp = new_amp
                    elif new_amp > 0.95:
                        amp = 0.95
                    else:  ## impossible
                        amp = 0.01
                count += 1

            drive_power_cc = round(his_drive_power - drive_power, 1)
            amp_cc = round(his_amp / amp, 3)
            self.set_run_options(
                drive_power=drive_power,
                amp=amp,
                drive_power_cc=drive_power_cc,
                amp_cc=amp_cc,
            )
        else:
            if map_distance <= max_distance:
                if case_mode == "simple":
                    amp = self.run_options.amp
                    drive_power = self.run_options.drive_power
                else:
                    amp_cc = self.run_options.amp_cc
                    drive_power_cc = self.run_options.drive_power_cc
                    drive_power_cc = drive_power_cc + 20 * np.log10(amp_cc)
                    o_amp = tb_qubit.XYwave.Xpi
                    # n_amp = round(o_amp / amp_cc, 3)
                    # amp = n_amp if 0.0 < n_amp <= 0.95 else o_amp

                    od_power = tb_qubit.drive_power
                    nd_power = round(od_power - drive_power_cc, 1)
                    if -40 <= nd_power <= -10:
                        drive_power = nd_power
                        n_amp = o_amp
                    elif nd_power > -10:
                        drive_power = -10
                        own_power = nd_power - drive_power
                        n_amp = o_amp * (10 ** (own_power / 20))
                    else:
                        drive_power = -40
                        own_power = nd_power - drive_power
                        n_amp = o_amp * (10 ** (own_power / 20))
                    if 0 < n_amp <= 0.95:
                        amp = n_amp
                    elif n_amp > 0.95:
                        amp = 0.95
                    else:  ## impossible
                        amp = 0.01
                    # drive_power = nd_power if -40 <= nd_power <= -10 else od_power

                tb_qubit.drive_freq = drive_freq
                tb_qubit.XYwave = xy_wave
                tb_qubit.XYwave.Xpi = amp
                # widths = qarange(5, 100, 1.25)
                if map_distance <= 1:
                    widths = np.hstack(
                        [
                            np.arange(5, 100, 2.5),
                            np.arange(120, 1000, 25),
                            np.arange(1500, 5000, 250),
                        ]
                    )
                elif map_distance == 2:
                    widths = np.hstack(
                        [
                            np.arange(5, 70, 2.5),
                            np.arange(80, 1500, 25),
                            np.arange(2000, 7000, 250),
                        ]
                    )
                else:
                    widths = np.hstack(
                        [
                            np.arange(5, 55, 2.5),
                            np.arange(60, 560, 25),
                            np.arange(600, 5600, 250),
                            np.arange(6000, 20000, 2500),
                        ]
                    )

                count = 0
                run_flag = True
                while count < max_count and run_flag is True:
                    pyqlog.info(
                        f"target: {target_name}, test: {name}, count: {count}, "
                        f"RabiScanWidth drive_power: {drive_power}, XYwave.Xpi: {amp}"
                    )
                    oscillating, osci_freq = await self._run_rabi_width(
                        tb_qubit,
                        count,
                        name,
                        drive_power=drive_power,
                        widths=widths,
                    )

                    # Update widths or not.
                    if oscillating is True:
                        # if abs(osci_freq) <= 2.5:
                        #     widths = qarange(5, 5000, 100)
                        # elif 2.5 < abs(osci_freq) < 5:
                        #     widths = qarange(5, 1000, 50)
                        # elif 5 <= abs(osci_freq) <= 200:
                        #     run_flag = False
                        # else:
                        #     widths = qarange(5, 50, 0.625)
                        run_flag = False
                    else:
                        # widths = qarange(5, 10000, 200)
                        run_flag = False
                    count += 1

                self.run_options.b_drive_power = drive_power
                self.run_options.b_amp = amp
            else:
                oscillating = False
                osci_freq = 0
                self.run_options.b_drive_power = 0.0
                self.run_options.b_amp = 0.0
        if oscillating is True:
            trust = True
        else:
            trust = False
            pyqlog.warning(
                f"RabiScanWidth {name} oscillating is {oscillating}, "
                f"so oscillating frequency {osci_freq} is not trusted!"
            )
        return trust, osci_freq

    async def _strength_run_once(self, name: str, idx: int) -> Tuple:
        """One bit run once logic."""
        case_mode = self.experiment_options.case_mode
        target_name = self.experiment_options.target_name
        max_count = self.experiment_options.max_count
        max_distance = self.experiment_options.max_distance
        target_widths = self.experiment_options.target_widths
        bias_widths_1 = self.experiment_options.bias_widths_1
        bias_widths_2 = self.experiment_options.bias_widths_2
        target_expect = self.experiment_options.target_expect
        bias_expect_1 = self.experiment_options.bias_expect_1
        bias_expect_2 = self.experiment_options.bias_expect_2

        t_min, t_max = min(target_expect), max(target_expect)
        b_min_1, b_max_1 = min(bias_expect_1), max(bias_expect_1)
        b_min_2, b_max_2 = min(bias_expect_2), max(bias_expect_2)

        qubit_map = self.run_options.qubit_map
        target_qubit = deepcopy(self.run_options.target_qubit)
        drive_freq = self.run_options.drive_freq
        xy_wave = deepcopy(self.run_options.xy_wave)

        oscillating = None
        osci_freq = None
        tb_qubit = deepcopy(qubit_map.get(name))
        if name == target_name:
            his_amp = target_qubit.XYwave.Xpi
            his_drive_power = target_qubit.drive_power

            amp = his_amp
            drive_power = his_drive_power
            widths = target_widths

            tb_qubit.drive_freq = drive_freq
            tb_qubit.XYwave = xy_wave

            count = 0
            run_flag = True
            while count < max_count and run_flag is True:
                tb_qubit.XYwave.Xpi = amp
                pyqlog.info(
                    f"target: {target_name}, test: {name}, count: {count}, "
                    f"RabiScanWidth drive_power: {drive_power}, XYwave.Xpi: {amp}"
                )
                oscillating, osci_freq = await self._run_rabi_width(
                    tb_qubit,
                    count,
                    name,
                    drive_power=drive_power,
                    widths=widths,
                )

                # Update drive_power, amp logic.
                if t_min <= osci_freq <= t_max:
                    run_flag = False
                else:
                    # power_update_step = 20 * np.log10(expect_value / osci_freq)
                    if osci_freq > t_max:
                        power_update_step = -5
                    else:
                        power_update_step = 5

                    pyqlog.info(f"power_update_step: {power_update_step}")
                    new_drive_power = round(drive_power + power_update_step, 1)
                    if -40 < new_drive_power < -10:
                        drive_power = new_drive_power
                        new_amp = amp
                    elif new_drive_power > -10:
                        drive_power = -10
                        owe_power = new_drive_power - drive_power
                        new_amp = amp * (10 ** (owe_power / 20))
                    else:
                        drive_power = -40
                        owe_power = new_drive_power - drive_power
                        new_amp = amp * (10 ** (owe_power / 20))
                    if 0 < new_amp <= 0.95:
                        amp = new_amp
                    elif new_amp > 0.95:
                        amp = 0.95
                    else:
                        amp = 0.01
                count += 1

            drive_power_cc = round(his_drive_power - drive_power, 1)
            amp_cc = round(his_amp / amp, 3)
            self.set_run_options(
                drive_power=drive_power,
                amp=amp,
                drive_power_cc=drive_power_cc,
                amp_cc=amp_cc,
            )
        else:
            # map_distance = abs(target_qubit.row - tb_qubit.row) + abs(
            #     target_qubit.col - tb_qubit.col
            # )
            map_distance = self.env_bit_resource.topology.bit_distance(
                target_qubit.name, tb_qubit.name
            )
            if map_distance <= max_distance:
                if case_mode == "simple":
                    amp = self.run_options.amp
                    drive_power = self.run_options.drive_power
                else:
                    amp_cc = self.run_options.amp_cc
                    drive_power_cc = self.run_options.drive_power_cc
                    drive_power_cc = drive_power_cc + 20 * np.log10(amp_cc)
                    o_amp = tb_qubit.XYwave.Xpi
                    # n_amp = round(o_amp / amp_cc, 3)
                    # amp = n_amp if 0.0 < n_amp <= 0.95 else o_amp

                    od_power = tb_qubit.drive_power
                    nd_power = round(od_power - drive_power_cc, 1)
                    if -40 <= nd_power <= -10:
                        drive_power = nd_power
                        n_amp = o_amp
                    elif nd_power > -10:
                        drive_power = -10
                        own_power = nd_power - drive_power
                        n_amp = o_amp * (10 ** (own_power / 20))
                    else:
                        drive_power = -40
                        own_power = nd_power - drive_power
                        n_amp = o_amp * (10 ** (own_power / 20))
                    if 0 < n_amp <= 0.95:
                        amp = n_amp
                    elif n_amp > 0.95:
                        amp = 0.95
                    else:
                        amp = 0.01

                tb_qubit.drive_freq = drive_freq
                tb_qubit.XYwave = xy_wave
                tb_qubit.XYwave.Xpi = amp
                widths = bias_widths_1

                count = 0
                run_flag = True
                while run_flag is True:
                    pyqlog.info(
                        f"target: {target_name}, test: {name}, count: {count}, "
                        f"RabiScanWidth drive_power: {drive_power}, XYwave.Xpi: {amp}"
                    )
                    oscillating, osci_freq = await self._run_rabi_width(
                        tb_qubit,
                        count,
                        name,
                        drive_power=drive_power,
                        widths=widths,
                    )

                    # Update widths or not.
                    if count == 0:
                        if osci_freq < b_min_1:
                            run_flag = False
                            osci_freq = b_min_1
                        elif b_min_1 <= osci_freq <= b_max_1:
                            run_flag = False
                        else:
                            widths = bias_widths_2
                    else:
                        run_flag = False
                        if osci_freq > b_max_2:
                            osci_freq = b_max_2
                    count += 1

                self.run_options.b_drive_power = drive_power
                self.run_options.b_amp = amp
            else:
                oscillating = False
                osci_freq = 0
                self.run_options.b_drive_power = 0.0
                self.run_options.b_amp = 0.0

        if oscillating is True:
            trust = True
        else:
            trust = False
            pyqlog.warning(
                f"RabiScanWidth {name} oscillating is {oscillating}, "
                f"so oscillating frequency {osci_freq} is not trusted!"
            )
        return trust, osci_freq

    def _alone_save_result(self):
        """Alone save some special result."""
        target_name = self.experiment_options.target_name
        bias_name_list = self.run_options.bias_name_list
        cross_coe_map = self.analysis_options.cross_coe_map
        cross_trust_map = self.analysis_options.cross_trust_map
        cross_extra_map = self.run_options.cross_extra_map

        mark_info = f"{self}({target_name}_cross_coefficient)"
        json_str = json.dumps(cross_coe_map, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=mark_info, prefix=".json")

        extra_info = f"{self}({target_name}_cross_extra)"
        extra_json_str = json.dumps(cross_extra_map, ensure_ascii=False, indent=4)
        self.file.save_text(extra_json_str, name=extra_info, prefix=".json")

        index_list = list(list(cross_coe_map.values())[0].keys())
        cross_coe_df = pd.DataFrame(data=cross_coe_map, index=index_list).T
        pyqlog.log(
            "RESULT", f"{target_name}{bias_name_list}{mark_info}:\n{cross_coe_df}"
        )
        if isinstance(self.file, LocalFile):
            if self.file._extension:
                csv_name = str(Path(self.file.dirs, self.file._extension, f"{mark_info}.csv"))
            else:
                csv_name = str(Path(self.file.dirs, f"{mark_info}.csv"))
            cross_coe_df.to_csv(csv_name)

        # Issue by Kong Zong, 2024.08.19
        # Save cross coefficient when large than threshold and the oscillation is true.
        select_coe_threshold = self.experiment_options.select_coe_threshold
        select_threshold_coe_dict = {}
        select_true_coe_dict = {}
        for t_name, coe_dict in cross_coe_map.items():
            threshold_coe_dict = {}
            true_coe_dict = deepcopy(coe_dict)
            trust_dict = cross_trust_map.get(t_name, {})
            for b_name, coe_val in coe_dict.items():
                trust_flag = trust_dict.get(b_name, False)
                if trust_flag is True and coe_val > select_coe_threshold:
                    threshold_coe_dict[b_name] = coe_val
                if trust_flag is False:
                    true_coe_dict[b_name] = 0.0
            if threshold_coe_dict:
                sort_coe_dict = dict(
                    sorted(threshold_coe_dict.items(), key=lambda x: x[1], reverse=True)
                )
                select_threshold_coe_dict[t_name] = sort_coe_dict
            select_true_coe_dict[t_name] = true_coe_dict

        threshold_info = f"{self}({target_name}_select_threshold_coefficient)"
        json_str = json.dumps(select_threshold_coe_dict, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=threshold_info, prefix=".json")

        true_info = f"{self}({target_name}_select_true_coefficient)"
        json_str = json.dumps(select_true_coe_dict, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=true_info, prefix=".json")
        index_list = list(list(select_true_coe_dict.values())[0].keys())
        true_coe_df = pd.DataFrame(data=select_true_coe_dict, index=index_list).T
        if isinstance(self.file, LocalFile):
            if self.file._extension:
                csv_name = str(Path(self.file.dirs, self.file._extension, f"{true_info}.csv"))
            else:
                csv_name = str(Path(self.file.dirs, f"{true_info}.csv"))
            true_coe_df.to_csv(csv_name)

    async def _sync_composite_run(self):
        """XY Cross run logic."""
        # super().run()

        target_name = self.experiment_options.target_name
        run_once_mode = self.experiment_options.run_once_mode
        cali_freq_flag = self.experiment_options.cali_freq_flag
        bias_name_list = self.run_options.bias_name_list

        if run_once_mode == "normal":
            if cali_freq_flag is True:
                await self._calibrate_target_frequency()
            t_trust, t_osci_freq = await self._run_once(target_name, 0)
        elif run_once_mode == "strength":
            if cali_freq_flag is True:
                await self._calibrate_target_frequency()

            update_target_strength = self.experiment_options.update_target_strength
            xy_wave = self.run_options.xy_wave
            if update_target_strength is True:
                # await self._calibrate_target_frequency()
                t_trust, t_osci_freq = await self._strength_run_once(target_name, 0)
            else:
                t_trust = True
                t_osci_freq = round(1000 / (xy_wave.time * 2), 3)
        else:
            pyqlog.warning(f"Set run_once_mode: {run_once_mode} not support!")
            t_trust, t_osci_freq = False, 0.0

        coefficient_map = {}
        trust_map = {}
        extra_map = {}
        for exp_idx, bias_name in enumerate(bias_name_list):
            if bias_name == target_name:
                b_trust, b_osci_freq = t_trust, 0.0  # set 0.0, easy to view
                drive_power = self.run_options.drive_power
                amp = self.run_options.amp
            else:
                if run_once_mode == "normal":
                    b_trust, b_osci_freq = await self._run_once(bias_name, exp_idx + 1)
                elif run_once_mode == "strength":
                    b_trust, b_osci_freq = await self._strength_run_once(
                        bias_name, exp_idx + 1
                    )
                else:
                    b_trust, b_osci_freq = False, 0.0
                drive_power = self.run_options.b_drive_power
                amp = self.run_options.b_amp
            coefficient = round(b_osci_freq / t_osci_freq, 4)
            coefficient_map.update({bias_name: coefficient})
            trust_map.update({bias_name: b_trust})
            extra_map.update({bias_name: [drive_power, amp]})

        if target_name not in list(extra_map.keys()):
            extra_map.update(
                {
                    target_name: [
                        self.run_options.drive_power,
                        self.run_options.amp,
                    ]
                }
            )

        cross_coe_map = {target_name: coefficient_map}
        cross_trust_map = {target_name: trust_map}
        cross_extra_map = {target_name: extra_map}

        self.analysis_options.cross_coe_map = cross_coe_map
        self.analysis_options.cross_trust_map = cross_trust_map
        self.run_options.cross_extra_map = cross_extra_map

        # self._alone_save_result()
        # To avoid analysis errors and failure to save data.
        self._run_analysis([], XYCrossRwAnalysis)


class XYCrossPlusRabiWidth(CompositeExperiment):
    """XY Cross Plus."""

    _sub_experiment_class = XYCrossRabiWidth

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()
        options.set_validator("target_name_list", list)

        options.target_name_list = []

        # Issue by Kong Zong, save coefficient when large than select_coe_threshold.
        options.set_validator("select_coe_threshold", float)
        options.select_coe_threshold = 0.01

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for DistortionT1Composite experiment."""
        options = super()._default_analysis_options()
        options.set_validator("n_multiple", float)

        options.cross_coe_map = {}
        options.cross_trust_map = {}
        options.n_multiple = 100

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method.

        Options:
            name_list (list): All bit name list.

        """
        options = super()._default_run_options()

        options.t_name_list = []
        options.name_list = []
        options.cross_extra_map = {}
        options.history_ac_bias = {}

        options.support_context = [StandardContext.URM]

        return options

    def get_qubit_str(self):
        """Get qubit str, an args qubit for SaveFile class.

        Returns:
            qubit_str: string, use to create save data path.
        """
        t_name_list = self.run_options.t_name_list
        string = f"{t_name_list[0]}~{t_name_list[-1]}"
        return string

    def _metadata(self) -> MetaData:
        """Set RabiScanAmp experiment metadata."""
        metadata = super()._metadata()
        n_multiple = self.analysis_options.n_multiple
        metadata.draw_meta = {"n_multiple": n_multiple}
        return metadata

    def _check_options(self):
        """Check options."""
        super()._check_options()

        target_name_list = self.experiment_options.target_name_list
        figsize = self.analysis_options.figsize

        name_list = [qubit.name for qubit in self.qubits]
        name_list.sort(key=lambda x: int(x[1:]))

        if target_name_list:
            t_name_list = [
                name if str(name).startswith("q") else f"q{name}"
                for name in target_name_list
            ]
            t_name_list.sort(key=lambda x: int(x[1:]))
        else:
            t_name_list = deepcopy(name_list)

        base_len = 10
        q_length = len(name_list)
        if figsize in [[12, 8], (12, 8)] and q_length > base_len:
            ratio = q_length / base_len
            figsize = (int(12 * ratio), int(8 * ratio))
        pyqlog.info(f"{self.label} analysis figsize: {figsize}")

        self.set_analysis_options(figsize=figsize)
        self.set_run_options(
            t_name_list=t_name_list,
            name_list=name_list,
            x_data=t_name_list,
            analysis_class=XYCrossRwAnalysis,
            history_ac_bias=self.ac_bias,
        )

    def _alone_save_result(self):
        """Alone save some special result."""
        t_name_list = self.run_options.t_name_list
        cross_coe_map = self.analysis_options.cross_coe_map
        cross_trust_map = self.analysis_options.cross_trust_map
        cross_extra_map = self.run_options.cross_extra_map

        mark_info = f"cross_coefficient"
        json_str = json.dumps(cross_coe_map, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=mark_info, prefix=".json")

        extra_info = f"cross_extra"
        extra_json_str = json.dumps(cross_extra_map, ensure_ascii=False, indent=4)
        self.file.save_text(extra_json_str, name=extra_info, prefix=".json")

        index_list = list(list(cross_coe_map.values())[0].keys())
        cross_coe_df = pd.DataFrame(data=cross_coe_map, index=index_list).T
        pyqlog.log("RESULT", f"{t_name_list}{mark_info}:\n{cross_coe_df}")
        if isinstance(self.file, LocalFile):
            csv_name = "".join([self.file.dirs, mark_info, ".csv"])
            cross_coe_df.to_csv(csv_name)

        # Issue by Kong Zong, 2024.08.19
        # Save cross coefficient when large than threshold and the oscillation is true.
        select_coe_threshold = self.experiment_options.select_coe_threshold
        select_threshold_coe_dict = {}
        select_true_coe_dict = {}
        for t_name, coe_dict in cross_coe_map.items():
            threshold_coe_dict = {}
            true_coe_dict = deepcopy(coe_dict)
            trust_dict = cross_trust_map.get(t_name, {})
            for b_name, coe_val in coe_dict.items():
                trust_flag = trust_dict.get(b_name, False)
                if trust_flag is True and coe_val > select_coe_threshold:
                    threshold_coe_dict[b_name] = coe_val
                if trust_flag is False:
                    true_coe_dict[b_name] = 0.0
            if threshold_coe_dict:
                sort_coe_dict = dict(
                    sorted(threshold_coe_dict.items(), key=lambda x: x[1], reverse=True)
                )
                select_threshold_coe_dict[t_name] = sort_coe_dict
            select_true_coe_dict[t_name] = true_coe_dict

        threshold_info = f"select_threshold_coefficient)"
        json_str = json.dumps(select_threshold_coe_dict, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=threshold_info, prefix=".json")

        true_info = f"select_true_coefficient)"
        json_str = json.dumps(select_true_coe_dict, ensure_ascii=False, indent=4)
        self.file.save_text(json_str, name=true_info, prefix=".json")
        index_list = list(list(cross_coe_map.values())[0].keys())
        true_coe_df = pd.DataFrame(data=select_true_coe_dict, index=index_list).T
        if isinstance(self.file, LocalFile):
            csv_name = "".join([self.file.dirs, true_info, ".csv"])
            true_coe_df.to_csv(csv_name)

    async def _sync_composite_run(self):
        """XY Cross run logic."""
        # super().run()

        cross_coe_map = self.analysis_options.cross_coe_map
        cross_trust_map = self.analysis_options.cross_trust_map
        cross_extra_map = self.run_options.cross_extra_map
        t_name_list = self.run_options.t_name_list
        name_list = self.run_options.name_list

        length = len(t_name_list)
        for idx, t_name in enumerate(t_name_list):
            xy_cross_rabi_wd_exp = deepcopy(self.child_experiment)
            xy_cross_rabi_wd_exp.ac_bias = deepcopy(self.run_options.history_ac_bias)
            xy_cross_rabi_wd_exp.set_experiment_options(
                target_name=t_name,
                bias_name_list=name_list,
            )
            xy_cross_rabi_wd_exp.set_parent_file(self, f"T{t_name}", idx, length)
            self._check_simulator_data(xy_cross_rabi_wd_exp, idx)

            # xy_cross_rabi_wd_exp.run()
            # self._experiments.append(xy_cross_rabi_wd_exp)
            await xy_cross_rabi_wd_exp.run_experiment()
            xy_cross_rabi_wd_exp.check_exp_is_done(True)
            s_cross_coe_map = xy_cross_rabi_wd_exp.analysis_options.cross_coe_map
            s_cross_trust_map = xy_cross_rabi_wd_exp.analysis_options.cross_trust_map
            s_cross_extra_map = xy_cross_rabi_wd_exp.run_options.cross_extra_map
            cross_coe_map.update(s_cross_coe_map)
            cross_trust_map.update(s_cross_trust_map)
            cross_extra_map.update(s_cross_extra_map)

        # self._alone_save_result()
        # To avoid analysis errors and failure to save data.
        self._run_analysis([], XYCrossRwAnalysis)

    def _setup_child_experiment(
        self, exp: "XYCrossRabiWidth", index: int, value: float
    ):
        """Set child_experiment some options."""
        xy_cross_rabi_wd_exp = exp
        # 2024.12.23 BugFixed, `XYCrossRabiWidth` will modify bias ac_bias value.
        xy_cross_rabi_wd_exp.ac_bias = deepcopy(self.run_options.history_ac_bias)
        name_list = self.run_options.name_list
        length = len(self.run_options.x_data)

        xy_cross_rabi_wd_exp.set_parent_file(self, f"T{value}", index, length)
        xy_cross_rabi_wd_exp.set_experiment_options(
            target_name=value,
            bias_name_list=name_list,
        )
        self._check_simulator_data(xy_cross_rabi_wd_exp, index)

    def _handle_child_result(self, exp: "XYCrossRabiWidth"):
        # collect child experiment result and provide it for parent.
        cross_coe_map = self.analysis_options.cross_coe_map
        cross_trust_map = self.analysis_options.cross_trust_map
        cross_extra_map = self.run_options.cross_extra_map

        xy_cross_rabi_wd_exp = exp
        s_cross_coe_map = xy_cross_rabi_wd_exp.analysis_options.cross_coe_map
        s_cross_trust_map = xy_cross_rabi_wd_exp.analysis_options.cross_trust_map
        s_cross_extra_map = xy_cross_rabi_wd_exp.run_options.cross_extra_map
        cross_coe_map.update(s_cross_coe_map)
        cross_trust_map.update(s_cross_trust_map)
        cross_extra_map.update(s_cross_extra_map)
