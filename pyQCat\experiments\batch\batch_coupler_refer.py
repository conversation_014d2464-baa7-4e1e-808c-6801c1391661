# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON><PERSON><PERSON><PERSON>

from ...log import pyqlog
from ..batch_experiment import BatchExperiment


class BatchCouplerRefer(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.qubit_groups = []
        options.coupler_groups = []
        options.coupler_q_flows = ["SingleShot_1", "VoltageDriftGradientCalibration"]
        options.coupler_c_flows = [
            "RefreshCouplerCaliRefer",
        ]

        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        return options

    def _run_batch(self):
        """Run batch flows logic."""
        qubit_groups = self.experiment_options.qubit_groups
        coupler_groups = self.experiment_options.coupler_groups
        coupler_q_flows = self.experiment_options.coupler_q_flows
        coupler_c_flows = self.experiment_options.coupler_c_flows

        if coupler_q_flows:
            qubit_bad_group = {}
            for idx, collections in enumerate(qubit_groups):
                pyqlog.info(f"start calibration qubit of group {idx}")
                pass_units = self._run_flow(coupler_q_flows, collections)
                if pass_units:
                    bad_units = [name for name in collections if name not in pass_units]
                    qubit_bad_group.update({f"{idx}": bad_units})
                    pyqlog.info(
                        f"qubit failed units in group {idx}:{qubit_bad_group[f'{idx}']}"
                    )
            pyqlog.info(f"qubit failed units:{qubit_bad_group}")

        if coupler_c_flows:
            coupler_bad_group = {}
            for idx, collections in enumerate(coupler_groups):
                pyqlog.info(f"start calibration coupler of group {idx}")
                pass_units = self._run_flow(coupler_c_flows, collections)
                if pass_units:
                    bad_units = [name for name in collections if name not in pass_units]
                    coupler_bad_group.update({f"{idx}": bad_units})
                    pyqlog.info(
                        f"coupler failed units in group {idx}:{coupler_bad_group[f'{idx}']}"
                    )
            pyqlog.info(f"coupler failed units:{coupler_bad_group}")
