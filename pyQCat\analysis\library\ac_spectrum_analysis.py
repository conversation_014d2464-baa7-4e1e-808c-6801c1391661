# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/07
# __author:       <PERSON><PERSON><PERSON> Shi
"""
AC Spectrum Analysis.
"""
import warnings
from typing import Union, List

import numpy as np

from ..curve_analysis import CurveAnalysis
from ..fit import curve_fitting
from ..fit.fit_models import (
    amp2freq_formula,
    flat_top_amp2freq_formula,
    flat_bottom_amp2freq_formula,
)
from ..quality import GoodnessofFit
from ..specification import FitModel, FitOptions, CurveAnalysisData, ParameterRepr
from ...log import pyqlog
from ...structures import Options, QDict
from ...errors import AnalysisOptionsError, CurveFittingError


class ACSpectrumAnalysis(CurveAnalysis):
    """AC/DC spectrum result analysis class."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        # set fit func
        options.fit_func = None
        # no bounds fitting.
        options.curve_fit_extra["xtol"] = 1.49012e-8
        options.curve_fit_extra["ftol"] = 1.49012e-8

        # todo michael
        options.result_parameters = [
            ParameterRepr(name="fq_max"),
            ParameterRepr(name="detune"),
            ParameterRepr(name="M"),
            ParameterRepr(name="offset"),
            ParameterRepr(name="d"),
            ParameterRepr(name="w"),
            ParameterRepr(name="g"),
            ParameterRepr(name="params", param_path="Qubit"),
            ParameterRepr(name="bottom_left", param_path="Qubit"),
            ParameterRepr(name="bottom_right", param_path="Qubit"),
            ParameterRepr(name="top", param_path="Qubit"),
            ParameterRepr(name="f10_list", param_path="Qubit"),
            ParameterRepr(name="z_amp_list", param_path="Qubit"),
        ]
        options.spectrum_type = None
        options.segm_x_data = None
        options.segm_y_data = None
        options.w = None
        options.fit_model_list = None
        options.buttom_type = "bottom_left"

        return options

    def _create_segm_data(self, x_data, y_data, data_key_list):
        res = QDict()
        diff_y = np.abs(np.diff(y_data))
        split_point_idx = np.argmax(diff_y)

        split_ringt_x = x_data[split_point_idx + 1:]
        split_ringt_y = y_data[split_point_idx + 1:]

        split_left_x = x_data[: split_point_idx - 1]
        split_left_y = y_data[: split_point_idx - 1]

        delta_x = x_data[-1] - x_data[0]
        delta_y = y_data[-1] - y_data[0]
        k = delta_y / delta_x
        if k > 0:
            fit_model_list = [
                FitModel(fit_func=flat_bottom_amp2freq_formula),
                FitModel(fit_func=flat_top_amp2freq_formula)
            ]

            buttom_type = "bottom_left"
        else:
            fit_model_list = [
                FitModel(fit_func=flat_top_amp2freq_formula),
                FitModel(fit_func=flat_bottom_amp2freq_formula)
            ]
            buttom_type = "bottom_right"
        res.update({data_key_list[0]: {"x_data": split_left_x, "y_data": split_left_y},
                    data_key_list[1]: {"x_data": split_ringt_x, "y_data": split_ringt_y}})

        self.set_options(fit_model_list=fit_model_list,
                         buttom_type=buttom_type)
        return res

    def _pre_operation(self):
        """Do some special operation."""
        fit_model_name = self.options.fit_model_name

        # According to fit_model_name adjust fit_model.
        if fit_model_name == "amp2freq_formula":
            fit_model = FitModel(fit_func=amp2freq_formula)
            fit_model_list = [FitModel(fit_func=amp2freq_formula)]
        elif fit_model_name == "flat_bottom_amp2freq_formula":
            fit_model = FitModel(fit_func=flat_bottom_amp2freq_formula)
            fit_model_list = [FitModel(fit_func=flat_bottom_amp2freq_formula)]
        elif fit_model_name == "flat_top_amp2freq_formula":
            fit_model = FitModel(fit_func=flat_top_amp2freq_formula)
            fit_model_list = [FitModel(fit_func=flat_top_amp2freq_formula)]
        elif fit_model_name == "segm_formula":
            fit_model_list = [
                FitModel(fit_func=flat_bottom_amp2freq_formula),
                FitModel(fit_func=flat_top_amp2freq_formula),
            ]
            fit_model = FitModel(fit_func=flat_bottom_amp2freq_formula)
            y_labels = ["raw_qubit_freq", "qubit_freq", "qubit_freq"]
            self.set_options(subplots=(3, 1), y_label=y_labels)
        else:
            raise ValueError(f"Set fit_model_name {fit_model_name} is not supported.")
        self.set_options(fit_model=fit_model, fit_model_list=fit_model_list)

    def _guess_fit_param(
            self,
            fit_opt: FitOptions,
            data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Guess initial fit parameters.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        fit_model_name = self.options.fit_model.fit_func.__name__
        x = data.x
        y = data.y

        f0 = np.max(y)
        x_max = x[y.argmax()]
        x_min = x[y.argmin()]
        max_spectrum_width = np.abs(1 / 2 / (x_max - x_min))
        alpha = -240

        fit_opt.p0.set_if_empty(
            fq_max=f0, detune=alpha, M=max_spectrum_width, offset=x_max, d=0
        )
        if fit_model_name != "amp2freq_formula":
            w = None
            if fit_model_name == "flat_bottom_amp2freq_formula":
                w = np.min(y)
            elif fit_model_name == "flat_top_amp2freq_formula":
                w = np.max(y)
            if w is None:
                raise AnalysisOptionsError(
                    "ACSpectrum",
                    key="w",
                    value=w,
                    msg=f"fit params w {w} is not supported.",
                )
            fit_opt.p0.set_if_empty(w=w, g=120)
        fit_opt.bounds.set_if_empty(
            fq_max=(f0 - 300, f0 + 300),
            M=(0, np.inf),
            d=(0, 1),
        )
        return fit_opt

    def _create_analysis_data(self) -> QDict:
        fit_model_name = self.options.fit_model_name
        analysis_data_dict = QDict()
        x_data = self.experiment_data.x_data
        y_data = self.experiment_data.y_data.get("qubit_freq")
        if fit_model_name == "segm_formula":
            data_key = [f"qubit_freq{i}" for i in range(2)]
            res = self._create_segm_data(x_data, y_data, data_key)
            for key in data_key:
                segm_x_data = res.get(key).get("x_data")
                segm_y_data = res.get(key).get("y_data")
                analysis_data = CurveAnalysisData(
                    x=segm_x_data,
                    y=segm_y_data,
                )
                analysis_data_dict[key] = analysis_data
            return analysis_data_dict
        else:
            delta_x = x_data[-1] - x_data[0]
            delta_y = y_data[-1] - y_data[0]
            k = delta_y / delta_x
            if k > 0:
                buttom_type = "bottom_right"
            else:
                buttom_type = "bottom_left"
            self.set_options(buttom_type=buttom_type)
            analysis_data_dict = super()._create_analysis_data()
            return analysis_data_dict

    def _run_fitting(self) -> bool:
        """Perform curve fitting on given data collection and fit models.

        Returns:
            fitting success or failed.
        """
        default_fit_opt = FitOptions(
            parameters=self.options.fit_model.signature,
            default_p0=self.options.p0,
            default_bounds=self.options.bounds,
            **self.options.curve_fit_extra
        )
        fit_results = []
        for i, analysis_data in enumerate(self.analysis_datas.values()):
            # Guess initial parameters.
            new_fit_opt = default_fit_opt.copy()
            guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)
            if isinstance(guess_fit_options, FitOptions):
                guess_fit_options = [guess_fit_options]

            # Run fit for each guessed parameters.
            sub_fit_results = []
            for guess_fit_option in set(guess_fit_options):
                try:
                    fit_data = curve_fitting(
                        analysis_data, guess_fit_option, self.options.fit_model_list[i]
                    )
                    sub_fit_results.append(fit_data)
                except CurveFittingError as analysis_error:
                    # Some guesses might be too far from the true parameters and may thus fail.
                    # We ignore initial guesses that fail and continue with the next fit candidate.
                    # todo log remind fitting failed
                    pyqlog.warning(f"<Curve Fitting Error> | {analysis_error}")
                    pass

            # fixed bug:
            fit_results += sub_fit_results

            if sub_fit_results:
                # Find best value with r-square value.
                for fit_data in sub_fit_results:
                    fit_data.goodness_of_fit = GoodnessofFit(
                        *self.options.quality_bounds
                    )
                    fit_data.goodness_of_fit.evaluate(analysis_data.y, fit_data.y_fit)
                analysis_data.fit_data = sorted(
                    sub_fit_results, key=lambda r: r.goodness_of_fit.value
                )[-1]

        # if all guess parameters fitting failed, return false.
        if len(fit_results) == 0:
            warnings.warn(
                "All initial guesses and parameter boundaries failed to fit the data. "
                "Please provide better initial guesses or fit parameter boundaries.",
                UserWarning,
            )
            # at least return raw data points rather than terminating
            return False
        else:
            return True

    def _extract_result(self, data_key: str):
        fit_model_name = self.options.fit_model_name
        buttom_type = self.options.buttom_type
        fit_model_list = self.options.fit_model_list
        for i, analysis_data in enumerate(self.analysis_datas.values()):
            if analysis_data.fit_data is not None:
                x_data = analysis_data.x
                y_data = analysis_data.y
                x_min = np.min(x_data)
                x_max = np.max(x_data)
                y_min = np.min(y_data)
                y_max = np.max(y_data)
                super()._extract_result(data_key)
                result = [
                    self.results.fq_max.value,
                    self.results.detune.value,
                    self.results.M.value,
                    self.results.offset.value,
                    self.results.d.value,
                ]

                if fit_model_name != "amp2freq_formula":
                    result.append(self.results.w.value)
                    result.append(self.results.g.value)
                    result.append(x_min - self.results.offset.value)
                    result.append(x_max - self.results.offset.value)
                    result.append(y_min)
                    result.append(y_max)
                    self.results.params.value = [0.0] * 5
                if fit_model_list[i].fit_func.__name__ == "flat_bottom_amp2freq_formula":
                    if buttom_type == "bottom_left":
                        self.results.bottom_left.value = result
                    elif buttom_type == "bottom_right":
                        self.results.bottom_right.value = result
                if fit_model_list[i].fit_func.__name__ == "flat_top_amp2freq_formula":
                    self.results.top.value = result
                else:
                    self.results.params.value = result
                self.results.f10_list.value = y_data
                self.results.z_amp_list.value = x_data
                self.results.params.extra["out_flag"] = False
                self.results.top.extra["out_flag"] = False
                self.results.bottom_left.extra["out_flag"] = False
                self.results.bottom_right.extra["out_flag"] = False
                self.results.f10_list.extra['out_flag'] = False
                self.results.z_amp_list.extra['out_flag'] = False

    def _visualization(self):
        """Draw all data, including raw data and fit data and so on.
        Some composite experiments may need to override this function.
        """
        fit_model_name = self.options.fit_model_name
        if fit_model_name != "segm_formula":
            super()._visualization()
        else:
            # Set plot title.
            self.drawer.set_options(title=self._description())

            x_data = self.experiment_data.x_data
            y_data = self.experiment_data.y_data.get("qubit_freq")
            self.drawer.draw_raw_data(x_data=x_data,
                                      y_data=y_data,
                                      ax_index=0)

            for i, analysis_data in enumerate(self.analysis_datas.values()):

                # plot raw data.
                if self.options.plot_raw_data:
                    raw_data = analysis_data.y
                    x_data = analysis_data.x
                    self.drawer.draw_raw_data(x_data=x_data,
                                              y_data=raw_data,
                                              ax_index=i + 1)

                # plot fit data.
                if analysis_data.fit_data is not None:
                    fit_data = analysis_data.fit_data.y_fit
                    x_data = analysis_data.x
                    self.drawer.draw_fit_line(x_data=x_data,
                                              y_data=fit_data,
                                              ax_index=i + 1)
        # Finalize plot.
            self.drawer.format_canvas()

    def run_analysis(self):
        self._pre_operation()
        super().run_analysis()
