# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:2
msgid "pyQCat.analysis.StateTomographyAnalysis"
msgstr ""

#: of pyQCat.analysis.tomography_analysis.StateTomographyAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.tomography_analysis.TomographyAnalysis`"
msgstr ""

#: of pyQCat.analysis.tomography_analysis.StateTomographyAnalysis:1
msgid "Analysis for state tomography experiments."
msgstr "量子状态层析分析类"

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:1
msgid "Initialize the analysis object."
msgstr "初始化一个新的分析对象"

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:4
msgid "pyQCat Experiments Data container class."
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:7
msgid "Whether the experiment contains sub-experiments."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.StateTomographyAnalysis.__init__>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`from_sub_analysis "
"<pyQCat.analysis.StateTomographyAnalysis.from_sub_analysis>`\\ "
"\\(x\\_data\\, sub\\_analysis\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:25:<autosummary>:1
msgid "This interface is used for composite experiment."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`run_analysis "
"<pyQCat.analysis.StateTomographyAnalysis.run_analysis>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:25:<autosummary>:1
#, fuzzy
msgid "Start analysis."
msgstr "构造分析数据"

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`set_options "
"<pyQCat.analysis.StateTomographyAnalysis.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:25:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`show_results "
"<pyQCat.analysis.StateTomographyAnalysis.show_results>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:25:<autosummary>:1
msgid "Show analysis results"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.StateTomographyAnalysis.rst:27
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_datas "
"<pyQCat.analysis.StateTomographyAnalysis.analysis_datas>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis datas."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`drawer <pyQCat.analysis.StateTomographyAnalysis.drawer>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid "A short-cut for curve drawer instance."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_data "
"<pyQCat.analysis.StateTomographyAnalysis.experiment_data>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid "Return the experiment data for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`has_child <pyQCat.analysis.StateTomographyAnalysis.has_child>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis has_child value."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.StateTomographyAnalysis.options>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis options for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.StateTomographyAnalysis.quality>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis data quality."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`results <pyQCat.analysis.StateTomographyAnalysis.results>`\\"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1:<autosummary>:1
msgid "Get the result datas."
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:1
msgid "Create the Analysis options, and set some fields."
msgstr "创建分析选项，并设置一些属性"

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:3
msgid "**Analysis Options**:"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:5
msgid "**titles (List)** - Subplot title, default is ``[real, image]``."
msgstr "**titles (List)** - 字体标题，默认为 ``[real, image]``."

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:7
msgid "**result_parameters (List)** - Expect to extract the ``density_matrix``"
msgstr "**result_parameters (List)** - 期望提取的结果为密度矩阵 ``density_matrix``"

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:9
msgid "**base_gate (List)** - Projection gate in three directions (X, Y, Z)."
msgstr "**base_gate (List)** - 三个反向的投影门 (X, Y, Z)."

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:11
msgid "**base_ops (List)** - Projection operator in three directions (X, Y, Z)."
msgstr "**base_ops (List)** - 三个方向的投影操作矩阵 (X, Y, Z)."

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options
msgid "Return type"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:13
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options
msgid "Returns"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._default_options:14
#, fuzzy
msgid "State tomography experiment analysis options."
msgstr "量子状态层析实验分析选项"

#: of pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._tomography:1
msgid "Calculate the density matrix."
msgstr "计算密度矩阵"

#: of
#: pyQCat.analysis.tomography_analysis.StateTomographyAnalysis._create_analysis_data:1
msgid "Constructing analysis data."
msgstr "构造分析数据"

#~ msgid "Bases: :class:`pyQCat.analysis.tomography_analysis.TomographyAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.StateTomographyAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_sub_analysis "
#~ "<pyQCat.analysis.StateTomographyAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_analysis "
#~ "<pyQCat.analysis.StateTomographyAnalysis.run_analysis>`\\ \\(\\)"
#~ msgstr ""

#~ msgid "Start anlysis."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options "
#~ "<pyQCat.analysis.StateTomographyAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_datas "
#~ "<pyQCat.analysis.StateTomographyAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ":obj:`drawer <pyQCat.analysis.StateTomographyAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_data "
#~ "<pyQCat.analysis.StateTomographyAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ":obj:`has_child <pyQCat.analysis.StateTomographyAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.StateTomographyAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.StateTomographyAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":obj:`results <pyQCat.analysis.StateTomographyAnalysis.results>`\\"
#~ msgstr ""

