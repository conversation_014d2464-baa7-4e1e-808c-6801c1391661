# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.qarange.rst:2
msgid "pyQCat.tools.qarange"
msgstr ""

#: of pyQCat.tools.utilities.qarange:1
msgid "Convert the cyclic measurement and control data into the required array."
msgstr ""

#: of pyQCat.tools.utilities.qarange
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.utilities.qarange:3
msgid "start value"
msgstr ""

#: of pyQCat.tools.utilities.qarange:4
msgid "end value"
msgstr ""

#: of pyQCat.tools.utilities.qarange:5
msgid "step value"
msgstr ""

#: of pyQCat.tools.utilities.qarange
msgid "Returns"
msgstr ""

#: of pyQCat.tools.utilities.qarange:7
msgid "suitable list"
msgstr ""

