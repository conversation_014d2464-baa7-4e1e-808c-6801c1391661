# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:2
msgid "pyQCat.analysis.TomographyAnalysis"
msgstr ""

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.top_analysis.TopAnalysis`"
msgstr ""

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis:1
msgid "Base analysis for state and process tomography experiments."
msgstr "量子层析基础分析基类"

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:1
msgid "Initialize the analysis object."
msgstr "创建一个新的分析对象"

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:4
msgid "pyQCat Experiments Data container class."
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:7
msgid "Whether the experiment contains sub-experiments."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.TomographyAnalysis.__init__>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`from_sub_analysis "
"<pyQCat.analysis.TomographyAnalysis.from_sub_analysis>`\\ \\(x\\_data\\, "
"sub\\_analysis\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:25:<autosummary>:1
msgid "This interface is used for composite experiment."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`run_analysis "
"<pyQCat.analysis.TomographyAnalysis.run_analysis>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis.run_analysis:1
msgid "Start analysis."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`set_options <pyQCat.analysis.TomographyAnalysis.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:25:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`show_results "
"<pyQCat.analysis.TomographyAnalysis.show_results>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:25:<autosummary>:1
msgid "Show analysis results"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TomographyAnalysis.rst:27
msgid "Attributes"
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid ""
":py:obj:`analysis_datas "
"<pyQCat.analysis.TomographyAnalysis.analysis_datas>`\\"
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid "Get the analysis datas."
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`drawer <pyQCat.analysis.TomographyAnalysis.drawer>`\\"
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1
#: pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid "A short-cut for curve drawer instance."
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid ""
":py:obj:`experiment_data "
"<pyQCat.analysis.TomographyAnalysis.experiment_data>`\\"
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid "Return the experiment data for :meth:`run` method."
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`has_child <pyQCat.analysis.TomographyAnalysis.has_child>`\\"
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid "Return the analysis has_child value."
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.TomographyAnalysis.options>`\\"
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid "Return the analysis options for :meth:`run` method."
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.TomographyAnalysis.quality>`\\"
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid "Get the analysis data quality."
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid ":py:obj:`results <pyQCat.analysis.TomographyAnalysis.results>`\\"
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:1:<autosummary>:1
msgid "Get the result datas."
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer
#: pyQCat.analysis.tomography_analysis.TomographyAnalysis._default_options
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.TomographyAnalysis.drawer:3
msgid ":py:class:`~pyQCat.analysis.visualization.tomography_drawer.TomographyDrawer`"
msgstr ""

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis._default_options:1
msgid "Create the Analysis options, and set some fields."
msgstr "创建一个分析选项，并设置一些属性"

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis._default_options:3
msgid "**Analysis Options**:"
msgstr ""

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis._default_options:5
msgid "**labels (List)** - Matrix distribution chart axis label."
msgstr "**labels (List)** - 坐标轴标签"

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis._default_options:7
msgid "**use_mle (bool)** - Whether to use maximum likelihood estimation"
msgstr "**use_mle (bool)** - 是否使用最大似然估计进行计算"

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis._default_options:9
msgid ""
"**drawer (TomographyDrawer)** - Matrix drawing, plotting the real and "
"imaginary parts of a matrix."
msgstr "**drawer (TomographyDrawer)** - 矩阵画布 "

#: of
#: pyQCat.analysis.tomography_analysis.TomographyAnalysis._default_options:12
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis._default_options
msgid "Returns"
msgstr ""

#: of
#: pyQCat.analysis.tomography_analysis.TomographyAnalysis._default_options:13
#, fuzzy
msgid "Tomography base experiment analysis options."
msgstr "层析基础实验分析选项"

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis.run_analysis:3
msgid "Define standard chromatography procedures as follows:"
msgstr "定义标准层析过程如下："

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis.run_analysis:5
msgid "Initialize tomography analysis with experiment data"
msgstr "初始化层析分析数据"

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis.run_analysis:6
msgid "Tomography operations"
msgstr "层析操作"

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis.run_analysis:7
msgid "Extract tomography result"
msgstr "提取层析结果"

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis.run_analysis:8
msgid "Visualization of experimental results"
msgstr "实验结果可视化"

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis._initialize:1
msgid "Initialize tomography analysis with experiment data."
msgstr "初始化层析分析数据"

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis._extract_result:1
msgid "Extract experimental results according to experimental result parameters."
msgstr "根据实验选型提取实验结果"

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis._description:1
msgid "A description of the important information in the figure."
msgstr "实验结果图中重要信息描述"

#: of
#: pyQCat.analysis.tomography_analysis.TomographyAnalysis._create_analysis_data:1
msgid "Create Analysis data provided for detailed analyze."
msgstr "创建分析数据"

#: of pyQCat.analysis.tomography_analysis.TomographyAnalysis._visualization:1
msgid "Plot the real and imaginary parts of a matrix."
msgstr "绘制矩阵的实部和虚部"

#: of
#: pyQCat.analysis.tomography_analysis.TomographyAnalysis._initialize_canvas:1
msgid "Initialize matplotlib canvas."
msgstr "初始化 matplolib 的画布"

#~ msgid "Bases: :py:class:`~pyQCat.analysis.top_analysis.TopAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.analysis.TomographyAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_sub_analysis "
#~ "<pyQCat.analysis.TomographyAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_analysis "
#~ "<pyQCat.analysis.TomographyAnalysis.run_analysis>`\\ \\(\\)"
#~ msgstr ""

#~ msgid "Run analysis on experiment data."
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_options "
#~ "<pyQCat.analysis.TomographyAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_datas "
#~ "<pyQCat.analysis.TomographyAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`drawer <pyQCat.analysis.TomographyAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_data "
#~ "<pyQCat.analysis.TomographyAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`has_child <pyQCat.analysis.TomographyAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`options <pyQCat.analysis.TomographyAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`quality <pyQCat.analysis.TomographyAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`results <pyQCat.analysis.TomographyAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "Default analysis options common to all analyzes."
#~ msgstr ""

#~ msgid ""
#~ "Create Analysis result data structure to"
#~ " save analysis results. The AnalysisResult"
#~ " object will get more attributes "
#~ "after analysis."
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.analysis.top_analysis.TopAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.analysis.TomographyAnalysis.__init__>`\\"
#~ " \\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_sub_analysis "
#~ "<pyQCat.analysis.TomographyAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_analysis "
#~ "<pyQCat.analysis.TomographyAnalysis.run_analysis>`\\ \\(\\)"
#~ msgstr ""

#~ msgid "Start anlysis."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options "
#~ "<pyQCat.analysis.TomographyAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_datas "
#~ "<pyQCat.analysis.TomographyAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ":obj:`drawer <pyQCat.analysis.TomographyAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_data "
#~ "<pyQCat.analysis.TomographyAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ":obj:`has_child <pyQCat.analysis.TomographyAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.TomographyAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.TomographyAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":obj:`results <pyQCat.analysis.TomographyAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "Create ``AnalysisResult`` according to experimental result parameters."
#~ msgstr "创建实验结果对象"

