# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/04
# __author:       KangKang Geng
# __corporation:  OriginQuantum


from typing import List, Optional, Union

import numpy as np

from ....errors import ExperimentOptionsError
from ....log import pyqlog
from ....pulse.pulse_function import pi_pulse
from ....pulse.pulse_lib import Constant
from ....structures import MetaData, Options
from ...coupler_experiment_v1 import StandardContext
from .t1 import T1


class T1WithBiasCoupler(T1):
    """T1 experiment with bias coupler z_amp to study coupler influence on qubit."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""

        options = super()._default_experiment_options()
        options.set_validator("bias_coupler_name_list", list)
        options.set_validator("bias_coupler_z_amp_list", list)

        options.bias_coupler_name_list = None
        options.bias_coupler_z_amp_list = None

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.drag_width = 0.0
        options.bias_coupler_list = []
        options.bias_qubit_z_amp_map = {}
        options.target_qubit = None
        options.support_context = [StandardContext.URM]

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        bias_coupler_name_list = self.experiment_options.bias_coupler_name_list or []
        bias_coupler_z_amp_list = self.experiment_options.bias_coupler_z_amp_list or []

        # verify that the name and amp lists are of equal length
        if bias_coupler_name_list:
            amp_list_length = len(bias_coupler_z_amp_list)
            name_list_length = len(bias_coupler_name_list)
            if amp_list_length < name_list_length:
                bias_coupler_z_amp_list.extend(
                    [0.0 for _ in range(name_list_length - amp_list_length)]
                )

        # collect base qubit/coupler
        physical_units_map = self.physical_units_map()
        target_qubit_obj = self.qubits[0]

        # target_qubit_obj assert not None
        if target_qubit_obj is None:
            raise ExperimentOptionsError(
                self,
                f"Set target {target_qubit_obj.name} not in all bit names!",
                "target_qubit",
                target_qubit_obj.name,
            )

        # discriminator filter only for target_qubit object
        if self.discriminator:
            if not isinstance(self.discriminator, list):
                dcm_list = [self.discriminator]
            else:
                dcm_list = self.discriminator

            target_qubit_dcm = None
            for dcm_obj in dcm_list:
                if dcm_obj.name == target_qubit_obj.name:
                    target_qubit_dcm = dcm_obj
                    break
            self.discriminator = target_qubit_dcm

        # build bias coupler z amp map
        bias_coupler_list = []
        rp_bias_qubit_z_amp_map = {}
        for index, bq_name in enumerate(bias_coupler_name_list):

            bq_obj = physical_units_map.get(bq_name)
            bq_z_amp = bias_coupler_z_amp_list[index]

            # For T1WithBiasCoupler, directly use the provided z_amp without frequency conversion
            if bq_z_amp is not None and not np.isnan(bq_z_amp):
                bias_coupler_list.append(bq_obj)
                rp_bias_qubit_z_amp_map.update({bq_name: bq_z_amp})
            else:
                pyqlog.warning(f"{bq_name} z_amp is None or NaN, will be ignored")

        # check run options
        multi_readout_channels = [target_qubit_obj.readout_channel]
        drag_width = target_qubit_obj.XYwave.time + 2 * target_qubit_obj.XYwave.offset
        self.set_experiment_options(
            multi_readout_channels=multi_readout_channels,
        )
        self.set_run_options(
            measure_qubits=[target_qubit_obj],
            target_qubit=target_qubit_obj,
            drag_width=drag_width,
            bias_coupler_list=bias_coupler_list,
            bias_qubit_z_amp_map=rp_bias_qubit_z_amp_map,
        )

    @staticmethod
    def get_z_pulse(
        qubit,
        delays: Union[List, np.ndarray],
        z_amp: Union[int, float],
        drag_time: Optional[Union[float, int]] = None,
    ):
        """Get Z pulse list"""
        if drag_time is None:
            drag = pi_pulse(qubit)
            drag_time = drag().width

        z_pulse_list = []
        for delay in delays:
            front_constant = Constant(drag_time, 0)
            center_delay = Constant(delay, z_amp)

            t2_z_pulse = front_constant() + center_delay()
            t2_z_pulse.bit = qubit.bit
            t2_z_pulse.sweep = "sweep delay"

            z_pulse_list.append(t2_z_pulse)
        return z_pulse_list

    @staticmethod
    def set_xy_pulses(builder):
        """Set XY pulses."""
        delays = builder.experiment_options.delays
        xy_pulse_list = T1.get_xy_pulse(builder.run_options.target_qubit, delays)

        for qubit in builder.qubits:
            if qubit == builder.run_options.target_qubit:
                builder.play_pulse("XY", qubit, xy_pulse_list)
            else:
                new_xy_pulse_list = [
                    Constant(pulse_obj.width, 0, "XY")() for pulse_obj in xy_pulse_list
                ]
                builder.play_pulse("XY", qubit, new_xy_pulse_list)

    @staticmethod
    def set_z_pulses(builder):
        """Set Z pulses."""
        delays = builder.experiment_options.delays
        z_amp = builder.experiment_options.z_amp
        drag_width = builder.run_options.drag_width
        bias_qubit_z_amp_map = builder.run_options.bias_qubit_z_amp_map

        z_amp = z_amp or 0.0
        z_pulse_list = T1WithBiasCoupler.get_z_pulse(
            builder.run_options.target_qubit, delays, z_amp, drag_width
        )
        builder.play_pulse("Z", builder.run_options.target_qubit, z_pulse_list)

        for qc_obj in builder.qubits + builder.couplers:
            if qc_obj.name != builder.run_options.target_qubit.name:
                bq_z_amp = bias_qubit_z_amp_map.get(qc_obj.name, 0.0)
                z_pulse_list = T1WithBiasCoupler.get_z_pulse(
                    qc_obj, delays, bq_z_amp, drag_width
                )
                builder.play_pulse("Z", qc_obj, z_pulse_list)

    @staticmethod
    def update_instrument(builder):
        qubit = builder.run_options.target_qubit
        pulse = pi_pulse(qubit)
        real_delay_list = [
            delay + pulse.width for delay in builder.experiment_options.delays
        ]
        builder.sweep_readout_trigger_delay(qubit.readout_channel, real_delay_list)
