# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:41+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.invoker.rst:2
msgid "pyQCat.invoker package"
msgstr ""

#: ../../source/api/pyQCat.invoker.rst:5
msgid "Module contents"
msgstr ""

#: of pyQCat.invoker:1
msgid "The Monster Invoker Base."
msgstr ""

#: of pyQCat.invoker.InvokerWrapper:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.invoker.InvokerWrapper:1
msgid "Invoker Wrapper."
msgstr ""

#: of pyQCat.invoker.InvokerWrapper:3
msgid ""
"Invoker Wrapper is a shell used for Database initialization work, and can"
" be used to set relevant parameters of database. If you want to connect "
"to pyqcat invoker, you need to install     the pyqcat-invoker extension "
"package first. After installing pyqcat-invoker, you can use INVOKER     "
"to set the underlying environment variables and account information. "
"After setting it up and making some magical modifications to Database,"
msgstr ""

#: of pyQCat.invoker.InvokerWrapper:8
msgid "you can use Database to connect to the database on pyqcat service."
msgstr ""

#: of pyQCat.invoker.InvokerWrapper.datacenter:1
msgid "get database Returns: DataBase obj."
msgstr ""

#: of pyQCat.invoker.InvokerWrapper.datacenter
msgid "Return type"
msgstr ""

#: of pyQCat.invoker.InvokerWrapper.datacenter:4
msgid ":py:class:`~pyQCat.invoker.datacenter.DataCenter`"
msgstr ""

