# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# __date:         2023/05/20
# __author:       <PERSON><PERSON><PERSON>

import json
from copy import deepcopy

import numpy as np

from ....analysis import FitModel, XEBPhaseAnalysis
from ....analysis.fit.fit_models import rb_exponential_decay_func
from ....log import pyqlog
from ....structures import MetaData, Options
from .xeb import XEBMultiple


class XEBPhase(XEBMultiple):
    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("input_data", dict)
        options.set_validator("nm_params", dict)
        options.set_validator("de_opts", dict)
        options.set_validator("opt_phase", bool)
        options.set_validator("opt_method", ["DE", "NM"])
        options.nm_params = Options(
            ftarget=-1e8,
            maxiter=50,
            maxfev=50,
            disp=True,
            return_all=True,
            initial_simplex=None,
            xatol=1e-7,
            fatol=1e-7,
            adaptive=False,
        )
        options.de_opts = {"NIND": 8, "MAXGEN": 15, "mutF": 0.7, "XOVR": 0.7}
        options.input_data = {
            "phase_qh": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.0001,
                "bound": [-6.3, 6.3],
                "nonzdelt": 0.05,
            },
            "phase_ql": {
                "is_opt": None,
                "init_v": None,
                "iter_step": 0.0001,
                "bound": [-6.3, 6.3],
                "nonzdelt": 0.05,
            },
        }
        options.init_v = None
        options.nonzdelt = None
        options.iter_step = None
        options.bound = None
        options.opt_keys = None
        options.opt_phase = True
        options.std_struct = None
        options.std_aft_matrix = None
        options.goal_matrix = None
        options.gate_bucket = None
        options.file_path = None
        options.opt_method = "NM"
        options.fit_model = FitModel(
            fit_func=rb_exponential_decay_func,
            model_description=r"amp \exp(x) + baseline",
        )
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []
        options.phase_qh = 0.0
        options.phase_ql = 0.0

        return options

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.process_meta = {"std_struct": self.run_options.std_struct}
        metadata.save_location = self.file.dirs

        return metadata

    def _check_options(self):
        qubit_pair = self.qubit_pair
        ql_name = qubit_pair.metadata.std.ql
        qh_name = qubit_pair.metadata.std.qh

        init_v = []
        opt_keys = []
        bound = []
        iter_step = []
        nonzdelt = []
        if self.analysis_options.opt_phase:
            for k, v in self.analysis_options.input_data.items():
                if v.get("is_opt") is True:
                    iv = v.get("init_v")
                    if iv is not None:
                        if k == "phase_qh":
                            phase_qh = iv
                            self.set_run_options(phase_qh=phase_qh)
                        if k == "phase_ql":
                            phase_ql = iv
                            self.set_run_options(phase_ql=phase_ql)
                        init_v.append(iv)
                    else:
                        if k == "phase_qh":
                            phase_qh = -self.qubit_pair.cz_value(qh_name, "phase")
                            self.set_run_options(phase_qh=phase_qh)
                            init_v.append(phase_qh)
                        elif k == "phase_ql":
                            phase_ql = -self.qubit_pair.cz_value(ql_name, "phase")
                            self.set_run_options(phase_ql=phase_ql)
                            init_v.append(phase_ql)
                    opt_keys.append(k)
                    iter_step.append(v.get("iter_step"))
                    nonzdelt.append(v.get("nonzdelt"))
                    pyqlog.info(f"{k} init value: {init_v[-1]}")

                    b = v.get("bound")
                    if b:
                        bound.append(b)
                    else:
                        bound.append([float("-inf"), float("inf")])
            gate_params = qubit_pair.metadata.std.cz.params
            gate_params.get(ql_name).phase = 0.0
            gate_params.get(qh_name).phase = 0.0

        super()._check_options()

        self.set_analysis_options(
            init_v=init_v,
            opt_keys=opt_keys,
            iter_step=iter_step,
            nonzdelt=nonzdelt,
            bound=bound,
        )

        if len(self.run_options.depths) < 3:
            self.set_analysis_options(fit_model=None)
        self.set_run_options(
            x_data=self.run_options.x_data, analysis_class=XEBPhaseAnalysis
        )

    def _random_su2(self):
        goal_gate = self.experiment_options.goal_gate
        gate_bucket = self.run_options.gate_bucket
        ql = self.run_options.ql
        qh = self.run_options.qh
        qh_phase = gate_bucket.cz_gate.phase_map.get(qh.name)
        ql_phase = gate_bucket.cz_gate.phase_map.get(ql.name)
        depths = self.run_options.depths
        x_data = np.asarray(depths).repeat(self.experiment_options.times)
        goal_matrix = self.experiment_options.goal_matrix

        std_aft_matrix = []
        for idx, struct in enumerate(self.run_options.temp_struct):
            std_cm_list = []
            qh_struct_gates = struct.qh_struct.gates
            ql_struct_gates = struct.ql_struct.gates
            for i in range(len(qh_struct_gates)):
                qh_gate = qh_struct_gates[i]
                ql_gate = ql_struct_gates[i]
                if qh_gate == "CZ":
                    std_cm_list.append(gate_bucket.get_matrix(qh_gate))
                else:
                    cm = np.kron(
                        gate_bucket.get_matrix(qh_gate), gate_bucket.get_matrix(ql_gate)
                    )
                    std_cm_list.append(cm)
            struct.std_cm_list = std_cm_list

            cz_num = 0
            if goal_gate:
                cz_num = x_data[idx]
            matrix1 = self.one_random_su2(struct.qh_struct, qh_phase * cz_num)
            matrix2 = self.one_random_su2(struct.ql_struct, ql_phase * cz_num)
            aft_matrix = np.kron(matrix1, matrix2)
            std_aft_matrix.append(aft_matrix)
        self.set_analysis_options(
            std_aft_matrix=std_aft_matrix,
            goal_matrix=goal_matrix,
            gate_bucket=gate_bucket,
        )
        self.run_options.std_struct = deepcopy(self.run_options.temp_struct)

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        ql_name = self.qubit_pair.metadata.std.ql
        qh_name = self.qubit_pair.metadata.std.qh
        try:
            gate_params = self.qubit_pair.metadata.std.cz["params"]
            json_str = json.dumps(gate_params, ensure_ascii=False, indent=4)
            self.file.save_text(json_str, name="gate_params", prefix=".json")
        except Exception as e:
            pyqlog.warning(f"{e} error occrued when saving gate_params")

        for key, result in self.analysis.results.items():
            if key == "f_xeb" and self.experiment_options.goal_gate == "CZ":
                result.extra["path"] = f"QubitPair.metadata.std.fidelity.xeb_fidelity"
            elif key == "depth" and self.experiment_options.goal_gate == "CZ":
                result.extra["path"] = f"QubitPair.metadata.std.fidelity.xeb_depth"
            elif key == "phase_ql" and self.analysis_options.opt_phase:
                result.extra["path"] = (
                    f"QubitPair.metadata.std.cz.params.{ql_name}.phase"
                )
            elif key == "phase_qh" and self.analysis_options.opt_phase:
                result.extra["path"] = (
                    f"QubitPair.metadata.std.cz.params.{qh_name}.phase"
                )
