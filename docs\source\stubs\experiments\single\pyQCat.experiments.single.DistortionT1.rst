﻿pyQCat.experiments.single.DistortionT1
======================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: DistortionT1

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~DistortionT1.__init__
      ~DistortionT1.acquire_pulse
      ~DistortionT1.cal_fidelity
      ~DistortionT1.experiment_info
      ~DistortionT1.from_experiment_context
      ~DistortionT1.get_qubit_str
      ~DistortionT1.get_xy_pulse
      ~DistortionT1.get_z_pulse
      ~DistortionT1.jupyter_schedule
      ~DistortionT1.options_table
      ~DistortionT1.play_pulse
      ~DistortionT1.plot_schedule
      ~DistortionT1.run
      ~DistortionT1.set_analysis_options
      ~DistortionT1.set_experiment_options
      ~DistortionT1.set_multiple_IF
      ~DistortionT1.set_multiple_index
      ~DistortionT1.set_parent_file
      ~DistortionT1.set_run_options
      ~DistortionT1.set_sweep_order
      ~DistortionT1.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~DistortionT1.analysis
      ~DistortionT1.analysis_options
      ~DistortionT1.experiment_options
      ~DistortionT1.run_options
   
   