# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/07
# __author:       <PERSON><PERSON><PERSON>

from enum import Enum


class ExecutorState(Enum):
    SUC = "suc"
    FAL = "fail"


class OptionMode(Enum):
    regular = "options_for_regular_exec"
    parallel = "options_for_parallel_exec"


class OptionType(Enum):
    exp = "experiment_options"
    ana = "analysis_options"
