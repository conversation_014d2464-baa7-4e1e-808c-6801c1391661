# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/11/08
# __author:       xw

"""
Purity RB Experiment class.
"""

from copy import deepcopy

import numpy as np

from ....analysis import PurityRBAnalysis
from ....analysis.algorithms.tomography import tensor_combinations
from ....structures import MetaData, Options
from .single_rb import RBSingle


class PurityRBSingle(RBSingle):
    """Purity single qubit randomized benchmarking experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options"""
        options = super()._default_experiment_options()
        options.set_validator("base_gate", list)
        options.base_gate = ["I", "X/2", "Y/2"]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.base_ops = None
        options.use_mle = True
        options.N = 1
        options.data_key = ["trace_rho"]

        return options

    def _check_options(self):
        super()._check_options()
        gate_bucket = self.run_options.gate_bucket
        # check base ops and base gate
        base_ops = self.analysis_options.base_ops
        base_gate = self.experiment_options.base_gate
        if base_ops is None:
            base_ops = [gate_bucket.get_matrix(gate) for gate in base_gate]
            if self.qubit_pair is not None:
                self.set_analysis_options(N=2)
                base_ops = tensor_combinations(base_ops, repeat=2)
            self.set_analysis_options(base_ops=base_ops)
        depths = self.run_options.depths
        k = self.experiment_options.times
        counts = 3 ** (2 if self.qubit_pairs else 1)
        x_data = np.asarray(depths).repeat(k * counts)
        self.run_options.x_data = x_data
        self.run_options.analysis_class = PurityRBAnalysis

    def _metadata(self) -> MetaData:
        """Set RB experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"K": self.experiment_options.times}
        return metadata

    @staticmethod
    def set_xy_pulses(self):
        if self.experiment_options.gate_split:
            self._split_gate()

        if self.experiment_options.mode == "dynamic":
            clifford_matrix_set = self._get_clifford_matrix_set()
            quantum_circuits = self._get_quantum_circuits(clifford_matrix_set)
        else:
            quantum_circuits = self._get_quantum_circuits2()

        gates = []
        pulse_list = []

        for pre_gate in quantum_circuits:
            for gate in self.experiment_options.base_gate:
                pre_gates = deepcopy(pre_gate)
                pre_gates.append(gate)
                gates.append(pre_gates)
        for gate in gates:
            pulse_list.append(
                self.run_options.gate_bucket.get_xy_pulse(self.qubit, gate)
            )
        self.play_pulse("XY", self.qubit, pulse_list)

    def _set_result_path(self):
        """Set path to save parameter of Qubit."""
        for key, result in self.analysis.results.items():
            if key == "fidelity":
                result.extra["path"] = "Qubit.rb_purity_fidelity"
