# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/27
# __author:       <PERSON>, <PERSON><PERSON><PERSON>

"""
Standard ConditionalPhase Analysis.
"""
from copy import deepcopy

import matplotlib.pyplot as plt
import numpy as np
from scipy.interpolate import interp1d

from ...log import pyqlog
from ...parameters import analysis_options_wrapper
from ...structures import Options
from ..algorithms import change_phase, phase_tomograph
from ..curve_analysis import CurveAnalysis
from ..fit.fit_models import linear, swap_formula
from ..quality.base_quality import BaseQuality, QualityDescribe
from ..specification import FitModel, ParameterRepr


def get_lens(x_data: np.ndarray, round_bit: int = 2):
    x_diff = np.diff(x_data)
    x_diff = [np.round(diff, round_bit) for diff in x_diff]
    x_len_list = []
    x_len = 1
    diff0 = x_diff[0]
    for index, diff in enumerate(x_diff):
        if diff == diff0:
            x_len += 1
            if index == len(x_diff) - 1:
                x_len_list.append(x_len)
        else:
            x_len_list.append(x_len)
            x_len = 1
            if index == len(x_diff) - 1:
                x_len_list.append(x_len)
            else:
                index += 1
                diff0 = x_diff[index]
    return x_len_list


def detect_and_remove_jumps(positions: np.ndarray, values: np.ndarray, custom_threshold=0.01):
    # Define custom threshold for detecting jumps
    # custom_threshold = 0.01

    # Compute the absolute difference between consecutive values
    differences_np = np.abs(np.diff(values))

    # Identify indices where the difference exceeds the custom threshold (jump points)
    jump_indices_np = np.where(differences_np > custom_threshold)[0] + 1  # +1 to offset to the correct index

    # Remove jump points from values and positions
    values_cleaned = np.delete(values, jump_indices_np)
    positions_cleaned = np.delete(positions, jump_indices_np)
    return positions_cleaned, values_cleaned


@analysis_options_wrapper(x_label="Freq [MHz]")
class ConditionalPhaseAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.fit_model = FitModel(fit_func=linear)
        options.p0 = {"k": 0, "baseline": 0}

        options.accumulation_phase = np.pi
        options.swap_fit_args = []

        options.x_label = "Z Amp (V)"

        options.quality_bounds = [0.99, 0.9, 0.8]
        options.curve_fit_extra.update({"ftol": 1.49012e-8, "xtol": 1.49012e-8})

        options.result_parameters = [
            ParameterRepr(name="ac_scan", repr="Amp", unit="V"),
            ParameterRepr(name="ac_adapter", repr="Amp", unit="V"),
            ParameterRepr(name="tc", repr="Tc", unit="ns"),
        ]

        options.adapter_amp_list = None
        options.diff_threshold = 1
        options.delete_index = None

        return options

    def _pre_operation(self):
        """Prepare one more canvas axis."""

        phase_i_arr = self.experiment_data.y_data.get("phase_I")
        phase_x_arr = self.experiment_data.y_data.get("phase_X")
        x_arr = self.experiment_data.x_data

        new_phase_x_list = []
        for phi_0, phi_1 in zip(phase_i_arr, phase_x_arr):
            if phi_0 > phi_1:
                phi_1 += 2 * np.pi
            new_phase_x_list.append(phi_1)

        new_phase_x_arr = np.array(new_phase_x_list)

        delta_phase_arr = new_phase_x_arr - phase_i_arr
        delta_phase_arr = np.mod(delta_phase_arr, np.pi * 2)
        # The default measured control phase monotonically increases or vice versa
        # (assuming the independent variable is continuous), so the unwrap below is
        # actually not very useful.
        delta_phase_arr = np.unwrap(delta_phase_arr, np.pi)

        diff_phase = np.diff(delta_phase_arr)
        abnormal_index = np.argwhere(diff_phase > self.options.diff_threshold)
        if np.any(abnormal_index):
            abnormal_index = abnormal_index.flatten().tolist()
            x_arr = np.delete(x_arr, abnormal_index)
            phase_i_arr = np.delete(np.array(phase_i_arr), abnormal_index)
            new_phase_x_arr = np.delete(new_phase_x_arr, abnormal_index)
            if self.options.adapter_amp_list is not None:
                self.options.adapter_amp_list = np.delete(self.options.adapter_amp_list, abnormal_index)
            self.options.delete_index = abnormal_index

        self.experiment_data._x_data = x_arr
        self.experiment_data._y_data.update(
            {"delta_phase": delta_phase_arr}
        )
        y_label = list(self._experiment_data.y_data.keys())
        y_label.append("Phase")
        length = len(y_label)
        subplots = (length, 1)

        self.options.curve_drawer.set_options(figsize=(20, length * 8))
        self.set_options(
            y_label=y_label,
            subplots=subplots,
            phase_I_arr=phase_i_arr,
            phase_X_arr=new_phase_x_arr,
        )

        x_label = self.options.x_label
        scan_name = self.experiment_data.metadata.process_meta.get("scan_name")
        self.options.x_label = f"{scan_name} {x_label}"

    def _extract_result(self, data_key: str, quality=None):
        """Extract Xpi from fitted data.

        Args:
            data_key (str): The basis for selecting data.
            quality : Quality of fit outcome.
        """

        analysis_data = self.analysis_datas[data_key]
        k, b = analysis_data.fit_data.popt

        z_amp_ret = self.results.ac_scan
        tc_ret = self.results.tc

        z_amp = (self.options.accumulation_phase - b) / k
        z_amp_ret.value = z_amp

        if self.options.adapter_amp_list or self.options.adapter_freq_list:
            x_data = self.experiment_data.x_data
            if self.options.adapter_freq_list:
                f = interp1d(np.array(x_data), np.array(self.options.adapter_freq_list), kind="cubic")
            else:
                f = interp1d(np.array(x_data), np.array(self.options.adapter_amp_list), kind="cubic")
            ac_adapter = None
            try:
                ac_adapter = f(z_amp)
            except Exception as e:
                pyqlog.error(f"interpolate error, because {e}")
            self.results.ac_adapter.value = ac_adapter

        swap_fit_args = self.options.swap_fit_args
        if swap_fit_args:
            tc = 1 / swap_formula(z_amp, *swap_fit_args)
            tc_ret.value = tc
        else:
            pyqlog.warning(
                f"The swap_fit_args is {swap_fit_args}, " f"can't calculate tc value!"
            )

        if self.options.is_plot is True:
            pos = (round(z_amp, 6), round(self.options.accumulation_phase, 6))
            self.drawer.set_options(
                text_pos=[pos], text_rp=[f"Target\n{pos}"], text_key=[data_key]
            )

    def _visualization(self):
        """Plot visualization."""
        exp_data = deepcopy(self.experiment_data)
        for key in list(self.experiment_data.y_data.keys()):
            va = self.experiment_data.y_data.get(key)
            if self.options.delete_index:
                self.experiment_data.y_data[key] = np.delete(va, self.options.delete_index)

        super()._visualization()
        self.drawer.set_options(raw_data_format="plot")

        x_arr = self.experiment_data.x_data
        phase_i_arr = self.options.phase_I_arr
        phase_x_arr = self.options.phase_X_arr

        default_colors = self.drawer.options.default_colors
        color_len = len(default_colors)
        draw_ops = {"linewidth": 2.5, "color": None}

        for i, y_arr in enumerate([phase_i_arr, phase_x_arr]):
            if y_arr is not None:
                draw_ops.update({"label": f"phase_{i}"})
                if i < color_len:
                    draw_ops.update({"color": default_colors[i]})
                self.drawer.draw_raw_data(
                    x_data=x_arr, y_data=y_arr, ax_index=-1, **draw_ops
                )

        # Finalize plot.
        self.drawer.format_canvas()

        self.experiment_data.y_data.update({
            "phase_I": phase_i_arr,
            "phase_X": phase_x_arr
        })

        self._experiment_data = exp_data

    def run_analysis(self):
        """Run analysis on experiment data."""
        self._pre_operation()

        super().run_analysis()


@analysis_options_wrapper(x_label="Freq [MHz]", y_label="Freq [MHz]")
class ConditionalPhaseAdjustAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.x_label = "Z Amp (v)"
        options.y_label = "Z Amp (v)"

        options.pcolormesh_options = {
            "shading": "nearest",
            "cmap": plt.cm.get_cmap("viridis"),
        }

        return options

    def _initialize_canvas(self):
        x_label = self.options.x_label
        y_label = self.options.y_label
        x_scan = self.experiment_data.metadata.process_meta.get("x_scan")
        y_scan = self.experiment_data.metadata.process_meta.get("y_scan")

        if y_scan == "":
            y_label = "CZ Number"

        self.drawer.set_options(
            subplots=(1, 1),
            xlabel=f'{x_scan} {x_label}',
            ylabel=[f'{y_scan} {y_label}'],
            figsize=self.options.figsize,
        )
        self.drawer.initialize_canvas()

    def _visualization(self):
        super()._visualization()

        child_exp_datas = self.experiment_data.child_data()

        y_data = self.experiment_data.metadata.process_meta.get("y_data")
        if y_data:
            self.experiment_data.metadata.process_meta.pop("y_data")
        x_data = self.experiment_data.x_data
        if y_data is None:
            y_data = child_exp_datas[0].x_data
        z_data = []

        for ced in child_exp_datas:
            z_data.append(np.mod(np.array(ced.y_data.get("delta_phase")), np.pi * 2))

        self.drawer.draw_color_map(
            y_data,
            x_data,
            # np.array(z_data).T,
            np.array(z_data),
            **self.options.pcolormesh_options,
        )


# @analysis_options_wrapper(x_label="Freq [MHz]")
class CZPhaseAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.fit_model = FitModel(fit_func=linear)
        options.p0 = {"k": 0, "baseline": 0}
        options.accumulation_phase = np.pi
        options.x_label = "Z Amp (V)"
        options.quality_bounds = [0.99, 0.9, 0.8]
        options.curve_fit_extra.update({"ftol": 1.49012e-8, "xtol": 1.49012e-8})
        options.adapter_amp_list = None
        options.result_parameters = [
            ParameterRepr(name="ac_scan", repr="Target", unit="MHz/V"),
            ParameterRepr(name="ac_adapter", repr="Adapter", unit="MHz/V"),
            ParameterRepr(name="phase", repr="SinglePhase", unit="°"),
        ]
        options.round_bit = 2

        return options

    def _extract_result(self, data_key: str, quality=None):
        """Extract Xpi from fitted data.

        Args:
            data_key (str): The basis for selecting data.
            quality : Quality of fit outcome.
        """
        if self.options.fit_model:
            analysis_data = self.analysis_datas[data_key]

            self.results.pop("phase")
            k, b = analysis_data.fit_data.popt
            z_amp_ret = self.results.ac_scan
            z_amp = (self.options.accumulation_phase - b) / k
            z_amp_ret.value = z_amp

            if self.options.adapter_amp_list:
                # x_data = self.experiment_data.x_data
                x_data = self.experiment_data.metadata.process_meta.get("actual_x_data")
                if self.options.adapter_freq_list:
                    f = interp1d(np.array(x_data), np.array(self.options.adapter_freq_list), kind="cubic")
                else:
                    f = interp1d(np.array(x_data), np.array(self.options.adapter_amp_list), kind="cubic")
                ac_adapter = None
                try:
                    ac_adapter = float(f(z_amp))
                except Exception as e:
                    pyqlog.error(f"interpolate error, because {e}")
                    self.quality._quality = QualityDescribe.bad
                self.results.ac_adapter.value = ac_adapter

            if self.options.is_plot is True:
                pos = (round(z_amp, 6), round(self.options.accumulation_phase, 6))
                self.drawer.set_options(text_pos=[pos], text_rp=[f"Target\n{pos}"], text_key=[data_key])

    def _visualization(self):
        super()._visualization()

        all_phase_data = self.experiment_data.metadata.process_meta.get("all_phase_data")
        if all_phase_data is not None:
            col = len(all_phase_data[0])
            # x_data = self.experiment_data.x_data
            x_data = self.experiment_data.metadata.process_meta.get("actual_x_data")
            new_x_data = np.array([x_data] * col)
            new_x_data = new_x_data.T.ravel()
            # new_x_data = []
            # for x in x_data:
            #     new_x_data.extend([x for _ in range(col)])
            phase_data = all_phase_data.ravel()
            self.drawer.draw_scatter_point(x_data=new_x_data, y_data=phase_data, ax_index=0)

    def _experiment_data_adapter(self):
        metadata = self.experiment_data.metadata
        adapter_options = metadata.process_meta.get("adapter_options")

        k = adapter_options.k
        mode = adapter_options.mode
        data_acq = adapter_options.data_acq

        if data_acq:
            p0 = self.experiment_data.y_data.get(data_acq[0][0])
            p1 = self.experiment_data.y_data.get(data_acq[0][1])
            p2 = self.experiment_data.y_data.get(data_acq[1][0])
            p3 = self.experiment_data.y_data.get(data_acq[1][1])
            p0 = (np.array(p0) + np.array(p1)).tolist()
            p1 = (np.array(p2) + np.array(p3)).tolist()
        else:
            p0 = self.experiment_data.y_data.get("P0")
            p1 = self.experiment_data.y_data.get("P1")

        x_data = self.experiment_data.metadata.process_meta.get("actual_x_data")
        all_phase_data = []
        phase_list = []
        if mode == "TM":
            for j in range(len(p0)):
                if j % 4 == 0:
                    *_, phase_i = phase_tomograph(p0[j], p1[j], p0[j + 1], p1[j + 1])
                    *_, phase_x = phase_tomograph(p0[j + 2], p1[j + 2], p0[j + 3], p1[j + 3])
                    phase_list.append(change_phase(phase_x - phase_i))
        else:
            for j in range(len(p0)):
                if j % 2 == 0:
                    *_, phase = phase_tomograph(p0[j], p1[j], p0[j + 1], p1[j + 1])
                    phase_list.append(change_phase(phase))

        sl = len(x_data)
        mean_phase = []
        for i, amp in enumerate(x_data):
            tp = []
            for j in range(k):
                tp.append(phase_list[i + j * sl])
            tp = phase_format(np.array(tp))
            # all_phase_data.append(tp.tolist())
            all_phase_data.append(tp)
            mean_phase.append(np.mean(tp))
        # phase_list = phase_format(mean_phase)
        phase_list = np.array(mean_phase)
        x_data = np.array(x_data)
        if len(x_data) > 1:  # for cphase experiment
            max_phase_for_fit = abs(self.options.accumulation_phase) * 4 / 3
            min_phase_for_fit = abs(self.options.accumulation_phase) * 2 / 3

            if max(phase_list) > max_phase_for_fit and min(phase_list) < self.options.accumulation_phase:
                index = np.array(phase_list) <= max_phase_for_fit
                phase_list = phase_list[index]
                x_data = x_data[index]

                x_len_list = get_lens(x_data, self.options.round_bit)
                max_index = np.argmax(x_len_list)
                start_index = sum(x_len_list[:max_index])
                end_index = start_index + x_len_list[max_index]
                x_data = x_data[start_index:end_index]
                phase_list = phase_list[start_index:end_index]

            if min(phase_list) < min_phase_for_fit and max(phase_list) > self.options.accumulation_phase:
                index = np.array(phase_list) >= min_phase_for_fit
                phase_list = phase_list[index]
                x_data = x_data[index]

                x_len_list = get_lens(x_data, self.options.round_bit)
                max_index = np.argmax(x_len_list)
                start_index = sum(x_len_list[:max_index])
                end_index = start_index + x_len_list[max_index]
                x_data = x_data[start_index:end_index]
                phase_list = phase_list[start_index:end_index]
            x_data, phase_list = detect_and_remove_jumps(x_data, phase_list, custom_threshold=1)

        self.experiment_data._x_data = x_data.tolist()
        self.experiment_data._y_data = {"delta_phase": phase_list.tolist()}
        self.experiment_data.metadata.process_meta["all_phase_data"] = np.array(all_phase_data)

    def run_analysis(self):
        """Run analysis on experiment data."""
        self._experiment_data_adapter()

        x_label = self.options.x_label
        scan_name = self.experiment_data.metadata.process_meta.get("scan_name")
        if scan_name:
            self.options.x_label = f"{scan_name} {x_label}"
        else:
            self.options.x_label = "Detune (MHz)"
        x_data = self.experiment_data.x_data

        if len(x_data) == 1:
            self.options.fit_model = None

        super().run_analysis()

        if len(x_data) == 1:
            self.results.pop("ac_scan")
            self.results.pop("ac_adapter")
            self.results.phase.value = -self.experiment_data.y_data.get("delta_phase")[0]


class CZPhaseAnalysisV1(CurveAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.accumulation_phase = np.pi
        options.x_label = "Z Amp (V)"
        options.quality_bounds = [0.99, 0.9, 0.8]
        options.result_parameters = [
            ParameterRepr(name="ac_scan", repr="Detune", unit="MHZ/V"),
            ParameterRepr(name="best_phase", repr="best_phase", unit=""),
        ]
        options.threshold = 0.2

        return options

    def _extract_result(self, data_key: str, quality=None):
        """Extract Xpi from fitted data.

        Args:
            data_key (str): The basis for selecting data.
            quality : Quality of fit outcome.
        """
        analysis_data = self.analysis_datas[data_key]

        diff = np.abs(analysis_data.y - np.pi)
        closest_index = int(np.argmin(diff))
        self.results.best_phase.value = analysis_data.y[closest_index]
        self.results.ac_scan.value = analysis_data.x[closest_index]

        if abs(self.results.best_phase.value - np.pi) > self.options.threshold:
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.perfect)

    def _visualization(self):
        super()._visualization()

        all_phase_data = self.experiment_data.metadata.process_meta.get("all_phase_data")
        if all_phase_data is not None:
            col = len(all_phase_data[0])
            x_data = self.experiment_data.x_data
            new_x_data = []
            for x in x_data:
                new_x_data.extend([x for _ in range(col)])
            phase_data = all_phase_data.ravel()
            self.drawer.draw_scatter_point(
                x_data=new_x_data, y_data=phase_data, ax_index=0
            )

    def run_analysis(self):
        """Run analysis on experiment data."""
        self._experiment_data_adapter()
        x_label = self.options.x_label
        scan_name = self.experiment_data.metadata.process_meta.get("scan_name")
        self.options.x_label = f"{scan_name} {x_label}"
        x_data = self.experiment_data.x_data

        if len(x_data) == 1:
            self.options.fit_model = None

        super().run_analysis()

        if len(x_data) == 1:
            self.results.pop("ac_scan")
            self.results.pop("ac_adapter")
            self.results.phase.value = -self.experiment_data.y_data.get("delta_phase")[0]

    def _experiment_data_adapter(self):
        metadata = self.experiment_data.metadata
        adapter_options = metadata.process_meta.get("adapter_options")

        k = adapter_options.k
        mode = adapter_options.mode
        data_acq = adapter_options.data_acq

        if data_acq:
            p0 = self.experiment_data.y_data.get(data_acq[0][0])
            p1 = self.experiment_data.y_data.get(data_acq[0][1])
            p2 = self.experiment_data.y_data.get(data_acq[1][0])
            p3 = self.experiment_data.y_data.get(data_acq[1][1])
            p0 = (np.array(p0) + np.array(p1)).tolist()
            p1 = (np.array(p2) + np.array(p3)).tolist()
        else:
            p0 = self.experiment_data.y_data.get("P0")
            p1 = self.experiment_data.y_data.get("P1")

        x_data = self.experiment_data.metadata.process_meta.get("actual_x_data")
        all_phase_data = []
        phase_list = []
        if mode == "TM":
            for j in range(len(p0)):
                if j % 4 == 0:
                    *_, phase_i = phase_tomograph(p0[j], p1[j], p0[j + 1], p1[j + 1])
                    *_, phase_x = phase_tomograph(
                        p0[j + 2], p1[j + 2], p0[j + 3], p1[j + 3]
                    )
                    phase_list.append(change_phase(phase_x - phase_i))
        else:
            for j in range(len(p0)):
                if j % 2 == 0:
                    *_, phase = phase_tomograph(p0[j], p1[j], p0[j + 1], p1[j + 1])
                    phase_list.append(change_phase(phase))

        sl = len(x_data)
        mean_phase = []
        for i, amp in enumerate(x_data):
            tp = []
            for j in range(k):
                tp.append(phase_list[i + j * sl])
            tp = phase_format(np.array(tp))
            all_phase_data.append(tp.tolist())
            mean_phase.append(np.mean(tp))
        phase_list = phase_format(mean_phase)

        self.experiment_data._x_data = x_data
        self.experiment_data._y_data = {"delta_phase": phase_list}
        self.experiment_data.metadata.process_meta["all_phase_data"] = np.array(all_phase_data)


class SQPhaseTMSEAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        # no bounds fitting.
        # options.curve_fit_extra.update({"ftol": 1.49012e-8, "xtol": 1.49012e-8})
        # options.fit_model = FitModel(fit_func=linear_func)
        # options.p0 = {"k": 0, "b": 0}

        options.std_limit = 0.5
        options.raw_data_format = "plot"
        options.gap_std = {}

        return options

    def _extract_result(self, data_key: str):

        for key, analysis_data in self.analysis_datas.items():
            # popt = analysis_data.fit_data.popt
            # self.results.get(key).value = popt[0]
            self.results.get(key).value = np.mean(analysis_data.y)

    def _evaluate_quality(self):

        quality = BaseQuality.instantiate(QualityDescribe.perfect)

        for v in self.options.gap_std.values():
            if v[0] > self.options.std_limit:
                quality._quality = QualityDescribe.bad

        self._quality = quality

        return None, quality

    def _visualization(self):
        """Draw all data, including raw data and fit data and so on.
        Some composite experiments may need to override this function.
        """
        super()._visualization()

        for index, (key, value) in enumerate(list(self.analysis_datas.items())):
            ax = self.drawer._get_axis(index)
            std, level = self.options.gap_std.get(key)
            ax.set_ylim(level - np.pi * 1.2, level + np.pi * 1.2)
            ax.text(len(value.x) / 2, level, f"STD({std})")
            self.drawer.draw_axh_line(
                y=self.results.get(key).value, ax_index=index, label="Mean-Phase"
            )

        # Finalize plot.
        self.drawer.format_canvas()

    def run_analysis(self):
        y_data = self.experiment_data.y_data
        for key in list(y_data.keys()):
            phases = y_data.get(key)
            # phases = np.unwrap(phases)
            # phase = self.define_unwrap(phases)
            phases, std, level = self.gap_tackle(np.hstack((np.array([0]), phases)))
            y_data[key] = phases
            self.options.gap_std[key] = (std, level)

        super().run_analysis()

    @staticmethod
    def define_unwrap(phases):
        new_phases = [phases[0]]
        for p in phases[1:]:
            pre_p = new_phases[-1]

            while not 0 <= p - pre_p <= 2 * np.pi:
                if p < pre_p:
                    p += 2 * np.pi
                elif p > pre_p:
                    p -= 2 * np.pi

            new_phases.append(p)

        return np.array(new_phases)

    @staticmethod
    def gap_tackle(phases):
        level = np.pi
        gaps = np.diff(np.array(phases))
        gaps = change_phase(gaps)
        if np.max(gaps) - np.min(gaps) > np.pi:
            level = 0
            gaps = change_phase(gaps, level=level)
        std = np.std(gaps)
        return gaps, std, level


def phase_format(phase_list):
    index = 0
    while True:
        diff_phase = np.diff(phase_list)
        abnormal_index = np.argwhere(np.abs(diff_phase) > np.pi)

        if len(abnormal_index) == 0:
            break

        for ai in abnormal_index:
            idx = ai[0]
            if diff_phase[idx] > 0:
                phase_list[idx + 1] -= 2 * np.pi
            else:
                phase_list[idx + 1] += 2 * np.pi
        index += 1
        if index > 10000:
            break
    return phase_list


if __name__ == "__main__":
    print(phase_format(np.array([6.23092524, 0.12511])))
