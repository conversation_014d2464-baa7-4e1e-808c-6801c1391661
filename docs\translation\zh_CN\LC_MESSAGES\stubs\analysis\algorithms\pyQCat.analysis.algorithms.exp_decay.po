# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.exp_decay.rst:2
msgid "pyQCat.analysis.algorithms.exp\\_decay"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:1
msgid ""
"Get exponential decay parameter from monotonically increasing "
"(decreasing) curve."
msgstr "从单调递增（递减）曲线获取指数衰减参数。"

#: of pyQCat.analysis.algorithms.guess.exp_decay:3
msgid "This assumes following function form."
msgstr "假定以下函数形式:"

#: of pyQCat.analysis.algorithms.guess.exp_decay:5
msgid "y(x) = e^{\\alpha x}"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:9
msgid "We can calculate :math:`\\alpha` as"
msgstr "其中 :math:`\\alpha` 为"

#: of pyQCat.analysis.algorithms.guess.exp_decay:11
msgid "\\alpha = \\log(y(x)) / x"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:15
msgid "To find this number, the numpy polynomial fit with ``deg=1`` is used."
msgstr "为了找到这个数字，使用 ``deg=1`` 的 numpy 多项式拟合。"

#: of pyQCat.analysis.algorithms.guess.exp_decay
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:18
msgid "Array of x values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:20
msgid "Array of y values."
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:22
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.guess.exp_decay:23
msgid "Decay rate of signal."
msgstr "信号的衰减率"

