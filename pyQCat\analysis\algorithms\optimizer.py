# -*- coding: utf-8 -*-
# @Time     : 2024/2/21 22:20
# <AUTHOR> WTL
# @Software : PyCharm
from __future__ import annotations

import decimal
import warnings
from copy import copy, deepcopy
from enum import Enum
from typing import Callable, Iterable, Union

import numpy as np
import qutip as qp
from scipy.optimize import OptimizeResult, curve_fit, least_squares, minimize

from ...structures import QDict
from ...tools.de_optimize.import_ea import ea

MINIMIZE_METHODS = [
    "nelder-mead",
    "powell",
    "cg",
    "bfgs",
    "newton-cg",
    "l-bfgs-b",
    "tnc",
    "cobyla",
    "slsqp",
    "trust-constr",
    "dogleg",
    "trust-ncg",
    "trust-exact",
    "trust-krylov",
]


class OptiMethod(Enum):
    opti = [*MINIMIZE_METHODS, "least_squares", "nm", "de"]
    fit = ["curve_fit"]


class OptiOpts(Enum):
    minimize = {
        "maxiter": 1000,
        "maxfev": 1000,
        "xatol": 3e-5,
        "return_all": True,
        "disp": True,
    }

    least_squares = {}

    nm = {
        "ftarget": -100,
        "nonzdelt": 0.01,
        "maxiter": 1000,
        "maxfev": 1000,
        "xatol": 3e-4,
        "fatol": 3e-4,
        "disp": True,
        "return_all": True,
        "initial_simplex": None,
        "adaptive": False,
    }

    de = {"NIND": 10, "MAXGEN": 100, "mutF": 0.7, "XOVR": 0.7, "prophet_flag": False}

    curve_fit = {
        "maxfev": 100000,
        "ftol": 1.49012e-4,
        "xtol": 1.49012e-4,
    }


class Optimizer:
    def __init__(
        self,
        args0,
        lb,
        ub,
        opti_method,
        opts_least_squares: dict = None,
        opts_minimize: dict = None,
        opts_nm: dict = None,
        opts_de: dict = None,
        opts_curve_fit: dict = None,
        flag_bounds_relative: Union[bool, list] = True,
        flag_parallel: bool = False,
    ):
        """

        :param args0: 初值参数，支持多套初值优化
        :param lb: 优化下界
        :param ub: 优化上界
        :param opti_method: 优化方法，支持curve_fit, nm, de, least_squares, scipy.optimize.minimize包中的优化算法等不同方法
        :param opts_least_squares:
        :param opts_minimize:
        :param opts_nm:
        :param opts_de:
        :param opts_curve_fit:
        :param flag_bounds_relative: 上下界是否是相对于初值而言的相对值，支持传入列表，指定每个bound是否为relative。只有单套初值下才能生效
        :param flag_parallel:
        """
        self.args0 = np.array(args0)
        self.lb = self._check_bounds("lb", lb, args0, flag_bounds_relative)
        self.ub = self._check_bounds("ub", ub, args0, flag_bounds_relative)
        self.opti_method = opti_method
        self.opts_minimize = opts_minimize if opts_minimize else {}
        self.opts_least_squares = opts_least_squares if opts_least_squares else {}
        self.opts_nm = opts_nm if opts_nm else {}
        self.opts_de = opts_de if opts_de else {}
        self.opts_curve_fit = opts_curve_fit if opts_curve_fit else {}
        self._check_opti_opts()
        self.iter = 0
        self.x_data = None
        self.y_data = None
        self.flag_parallel = flag_parallel
        self.cost_func: Callable | None = None
        print(f"lower bounds: {self.lb}\nupper bounds: {self.ub}")

    def _check_bounds(
        self, bound_type: str, bound, args0, flag_relative: Union[bool, list]
    ):
        if bound is not None:
            # 单套初值
            if not isinstance(args0[0], Iterable):
                if not isinstance(flag_relative, list):
                    flag_relative = np.array([flag_relative] * len(args0))
                return (np.array(bound) + np.array(args0) * flag_relative).tolist()
            # 多套初值
            else:
                warnings.warn(
                    f"args0 {args0} have parallel structure and bound will not add args0."
                )
                return bound
        else:
            warnings.warn(f"bound is None and is set to be -np.inf(lb) or np.inf(ub).")
            if not isinstance(args0[0], Iterable):
                return [-np.inf if bound_type == "lb" else np.inf] * len(self.args0)
            else:
                return [-np.inf if bound_type == "lb" else np.inf] * len(self.args0[0])

    def _check_opti_opts(self):
        opts_minimize = OptiOpts.minimize.value
        opts_minimize.update(self.opts_minimize)
        self.opts_minimize = opts_minimize

        opts_least_squares = OptiOpts.least_squares.value
        opts_least_squares.update(self.opts_least_squares)
        self.opts_least_squares = opts_least_squares

        opts_nm = OptiOpts.nm.value
        opts_nm.update(self.opts_nm)
        self.opts_nm = opts_nm

        opts_de = OptiOpts.de.value
        opts_de.update(self.opts_de)
        self.opts_de = opts_de

        opts_curve_fit = OptiOpts.curve_fit.value
        opts_curve_fit.update(self.opts_curve_fit)
        self.opts_curve_fit = opts_curve_fit

    def opti_minimize(self):
        print(f"opti options of minimize:\n{self.opts_minimize}")
        res = minimize(
            self.cost_func,
            method=self.opti_method,
            x0=self.args0,
            bounds=np.array([(lb, ub) for lb, ub in zip(self.lb, self.ub)]),
            options=self.opts_minimize,
        )
        return res

    def opti_least_squares(self):
        print(f"opti options of least_squares:\n{self.opts_least_squares}")
        res = least_squares(
            self.cost_func,
            x0=self.args0,
            bounds=(self.lb, self.ub),
            **self.opts_least_squares,
        )
        print(res)
        return res

    def opti_nm_custom(self):
        print(f"opti options of nm_minimize:\n{self.opts_nm}")
        res, _ = nm_minimize(
            func=self.cost_func,
            x0=self.args0,
            bound=np.array([(lb, ub) for lb, ub in zip(self.lb, self.ub)]),
            **self.opts_nm,
        )
        return res

    def opti_de(self):
        print(f"opti options of de:\n{self.opts_de}")
        mutF, XOVR, prophet_flag, MAXGEN, NIND, dirName = [
            self.opts_de.get(key)
            for key in ["mutF", "XOVR", "prophet_flag", "MAXGEN", "NIND", "dirName"]
        ]
        print(f"DE algorithm mutF:{mutF}, XOVR:{XOVR}, prophet:{prophet_flag}")

        problem = ea.Problem(
            name=f"state transfer optimize using DE",  # 初始化name（函数名称，可以随意设置）
            M=1,  # 初始化M（目标维数）
            maxormins=[
                1
            ],  # 初始化maxormins（目标最小最大化标记列表，1：最小化该目标；-1：最大化该目标）
            Dim=len(self.args0),  # 初始化Dim（决策变量维数）
            varTypes=[0] * len(self.args0),
            lb=self.lb,  # 决策变量下界
            ub=self.ub,  # 决策变量上界
            evalVars=self.soea_eval,
        )

        # path_track = []
        algorithm = ea.soea_DE_best_1_bin_templet(
            problem,
            ea.Population(Encoding="RI", NIND=NIND),
            MAXGEN=MAXGEN,
            logTras=1,
            outFunc=None,
        )
        algorithm.mutOper.F = mutF
        algorithm.recOper.XOVR = XOVR

        prophetVars = self.args0 if prophet_flag else None
        res = ea.optimize(
            algorithm,
            prophet=prophetVars,
            verbose=True,
            drawing=1,
            outputMsg=True,
            drawLog=False,
            saveFlag=True,
            dirName=f"{dirName}soea_DE result",
        )
        return res

    def soea_eval(self, Vars):
        cost = []
        for i in range(Vars.shape[0]):
            costi = self.cost_func(Vars[i, :])
            cost.append(np.atleast_1d(costi))

        return np.vstack(cost)

    def cost_func(self, args):
        """
        cost function passed into optimize algorithm
        :return:
        """

    def model_func(self, args):
        """
        model function passed into curve_fit()
        :param args:
        :return:
        """

    def run_opti(self):
        if not isinstance(self.args0[0], Iterable):
            return self._run_opti(self.args0)

        # 若有多组初值，如果flag_parallel为False，则依次遍历不同初值并返回cost最小的一组解；如果为True则多组初值并行优化
        args0_set = copy(self.args0)
        if not self.flag_parallel:
            cost_list = []
            res_list = []
            for args0_i in args0_set:
                res = self._run_opti(args0_i)
                cost_list.append(res.get("cost"))
                res_list.append(res)
        else:
            res_list = qp.parallel_map(
                self._run_opti,
                args0_set,
            )
            cost_list = [res.get("cost") for res in res_list]

        res_best = res_list[np.argmin(cost_list)]
        return res_best

    def _run_opti(self, args0):
        self.args0 = args0
        res_info = QDict.fromkeys(["result", "x", "cost"])

        if self.opti_method in MINIMIZE_METHODS:
            res = self.opti_minimize()
            res_info.update({"result": res, "x": res.x, "cost": res.fun})
        elif self.opti_method == "least_squares":
            res = self.opti_least_squares()
            res_info.update({"result": res, "x": res.x, "cost": res.fun[0]})
        elif self.opti_method == "nm":
            res = self.opti_nm_custom()
            res_info.update({"result": res, "x": res.x, "cost": res.fun})
        elif self.opti_method == "de":
            res = self.opti_de()
            res_info.update(
                {"result": res, "x": res["Vars"][0], "cost": res["ObjV"].item()}
            )
        else:
            raise ValueError(f"opti_method {self.opti_method} is not supported now.")

        return res_info

    def run_fit(self):
        popt, others = curve_fit(
            self.model_func,
            xdata=self.x_data,
            ydata=self.y_data,
            p0=self.args0,
            bounds=(self.lb, self.ub),
            **self.opts_curve_fit,
        )
        return others, popt


def nm_minimize(
    func,
    x0,
    args=(),
    ftarget: float = float("-inf"),
    nonzdelt: float = 0.05,
    callback: Callable = None,
    maxiter: int = None,
    maxfev: int = None,
    disp: bool = False,
    return_all: bool = False,
    initial_simplex: np.ndarray = None,
    xatol: float = 1e-4,
    fatol: float = 1e-4,
    adaptive: bool = False,
    step: np.ndarray = None,
    bound: np.ndarray = None,
):
    """NM algorithm process.

    Args:
        func: goal function
        x0: initial simple x0
        args: the args of func
        ftarget: optimize the target value, when the target function value is
            lower than ftarget, the algorithm will be terminated.
        nonzdelt: When initial simplex is None, use nonzdelt to calculate the
            initial simplex.
        callback: The callback function after each iteration. If the target
            function is RB, the callback function can be set to SingleShot,
            indicating that after each RB experiment, SingleShot is executed
            to refresh the IQ criteria.
        maxiter: Maximum allowed number of iterations and function evaluations.
            Will default to ``N*200``, where ``N`` is the number of
            variables, if neither `maxiter` nor `maxfev` is set. If both
            `maxiter` and `maxfev` are set, minimization will stop at the
            first reached.
        maxfev: Maximum number of function executions
        disp: Set to True to print convergence messages.
        return_all: Whether to record the optimal simplex for each iteration.
        initial_simplex: Initial simplex, must shape (N+1, N)
        xatol: Absolute error in xopt between iterations that is acceptable for
            convergence. If the interval between the maximum and minimum values
            at the same positions in the simplex parameter set is less than
            xatol, it is considered to be trapped in a local solution and the
            iteration is terminated;
        fatol: Absolute error in func(xopt) between iterations that is acceptable for
            convergence. If the interval between the maximum and minimum values at the
            same location in the result set is less than fatol, it is
            considered to be trapped in a local solution and the
            iteration is terminated;
        adaptive: Adjust iteration coefficient based on dimension. Adapt algorithm
            parameters to dimensionality of problem. Useful for high-dimensional
            minimization [1]_.
        step: Parameter iteration minimum step size
        bound: Parameter upper and lower limits

    Returns:
        OptimizeResult: NM Optimize Result.

    References:
        .. [1] Gao, F. and Han, L.
           Implementing the Nelder-Mead simplex algorithm with adaptive
           parameters. 2012. Computational Optimization and Applications.
           51:1, pp. 259-277
    """

    # standard status messages of optimizers
    _status_message = {
        "success": "Optimization terminated successfully.",
        "maxfev": "Maximum number of function evaluations has " "been exceeded.",
        "maxiter": "Maximum number of iterations has been " "exceeded.",
        "pr_loss": "Desired error not necessarily achieved due " "to precision loss.",
    }

    step = None if step is None else np.asarray(step)
    bound = None if bound is None else np.asarray(bound)
    sim_records = []
    fsim_records = []

    def x_check(_x):
        """check step and bound, eg:

        probe power: max -10, min -40, step is 0.1, we must limit the x into exp.

        Args:
            _x: input x

        Returns:

        """
        if step is None and bound is None:
            return _x

        if step is not None:
            _x = np.asarray(_x)
            if _x.size != step.size:
                raise ValueError(
                    "'initial_simplex' and 'step' shape(1) should have the same size!"
                )
            remainder = [
                (decimal.Decimal(str(_x[i])) % decimal.Decimal(str(step[i])))
                for i in range(len(step))
            ]
            _x = [
                decimal.Decimal(str(_x[i])) - decimal.Decimal(str(remainder[i]))
                for i in range(len(_x))
            ]
            _x = [float(i) for i in _x]

        if bound is not None:
            n_x = []
            for i, v in enumerate(list(_x)):
                n_x.append(np.clip(np.array([v]), bound[i][0], bound[i][1])[0])
            _x = n_x

        return np.array(_x)

    def wrap_function(function, common_args):
        """Record the number of calls to the target function.

        Args:
            function: Target function.
            common_args: The common args of target function.

        Returns:
            call_counts: the number of call function.
            function_wrapper: target function.

        """
        call_counts = [0]

        if function is None:
            return call_counts, None

        def function_wrapper(wrapper_args):
            call_counts[0] += 1
            res = function(wrapper_args, *common_args)
            sim_records.append(deepcopy(wrapper_args))
            fsim_records.append(res)
            return res

        return call_counts, function_wrapper

    # srap function
    f_calls, func = wrap_function(func, args)

    # cal iteration parameters
    if adaptive:
        dim = float(len(x0))
        alpha = 1
        gamma = 1 + 2 / dim
        rho = 0.75 - 1 / (2 * dim)
        sigma = 1 - 1 / dim
    else:
        alpha = 1
        gamma = 2
        rho = 0.5
        sigma = 0.5

    #
    x0 = np.asfarray(x0).flatten()
    n = len(x0)
    nonzdelt_ = np.asfarray(nonzdelt).flatten()
    if np.size(nonzdelt_) == 1:
        nonzdelt_ = nonzdelt_ * np.ones(n)
    zdelt = 0.00025
    if initial_simplex is None:
        sim = np.zeros((n + 1, n), dtype=x0.dtype)
        # sim[0] = x0
        sim[0] = x_check(x0)
        for k in range(n):
            y = np.array(x0, copy=True)
            # 将nonzdelt定义为绝对量而非相对量
            # y[k] += nonzdelt_[k]
            if y[k] != 0:
                y[k] = (1 + nonzdelt_[k]) * y[k]
            else:
                y[k] = zdelt
            sim[k + 1] = x_check(y)
    else:
        sim = np.asfarray(initial_simplex).copy()
        if sim.ndim != 2 or sim.shape[0] != sim.shape[1] + 1:
            raise ValueError("`initial_simplex` should be an array of shape (N+1,N)")
        if len(x0) != sim.shape[1]:
            raise ValueError("Size of `initial_simplex` is not consistent with `x0`")
        n = sim.shape[1]

        for k in range(n + 1):
            sim[k] = x_check(sim[k])

    allvecs = None
    if return_all:
        allvecs = [sim[0]]
    # If neither are set, then set both to default
    if maxiter is None and maxfev is None:
        maxiter = n * 200
        maxfev = n * 200
    elif maxiter is None:
        # Convert remaining Nones, to np.inf, unless the other is np.inf, in
        # which case use the default to avoid unbounded iteration
        if maxfev == np.inf:
            maxiter = n * 200
        else:
            maxiter = np.inf
    elif maxfev is None:
        if maxiter == np.inf:
            maxfev = n * 200
        else:
            maxfev = np.inf
    one2np1 = list(range(1, n + 1))
    fsim = np.zeros((n + 1,), float)
    for k in range(n + 1):
        fsim[k] = func(sim[k])
    ind = np.argsort(fsim)
    fsim = np.take(fsim, ind, 0)
    # sort so sim[0,:] has the lowest function value
    sim = np.take(sim, ind, 0)
    iterations = 1
    while f_calls[0] < maxfev and iterations < maxiter and fsim[0] > ftarget:
        if (
            np.max(np.ravel(np.abs(sim[1:] - sim[0]))) <= xatol
            and np.max(np.abs(fsim[0] - fsim[1:])) <= fatol
        ):
            # When the maximum vertex distance or the maximum target value
            # is less than a certain value, the iteration ends
            break

        # cal centroid x
        xbar = np.add.reduce(sim[:-1], 0) / n
        xbar = x_check(xbar)

        # cal reflection x
        xr = (1 + alpha) * xbar - alpha * sim[-1]
        xr = x_check(xr)

        # cal f(xr)
        fxr = func(xr)

        # control shrink
        doshrink = 0

        if fxr < fsim[0]:
            # f(xr) < f(x1)[best pint], cal expansion point
            xe = (1 + alpha * gamma) * xbar - alpha * gamma * sim[-1]
            xe = x_check(xe)

            # cal f(xe)
            fxe = func(xe)

            if fxe < fxr:
                # if f(xe) < f(xr), replace f(n+1) with f(xe)
                sim[-1] = xe
                fsim[-1] = fxe
            else:
                # else replace f(n+1) with f(xr)
                sim[-1] = xr
                fsim[-1] = fxr

        else:
            if fxr < fsim[-2]:
                # if f(x1) < f(xr) < f(xn), replace f(n+1) with f(xr)
                sim[-1] = xr
                fsim[-1] = fxr
            else:
                if fxr < fsim[-1]:
                    # if f(xr) < f(n+1), cal contraction point
                    xc = (1 + rho * alpha) * xbar - rho * alpha * sim[-1]
                    xc = x_check(xc)

                    # cal f(xc)
                    fxc = func(xc)

                    if fxc <= fxr:
                        # replace f(n+1) with f(xc)
                        sim[-1] = xc
                        fsim[-1] = fxc
                    else:
                        # enter shrink process
                        doshrink = 1
                else:
                    # Perform an inside contraction
                    xcc = (1 - rho) * xbar + rho * sim[-1]
                    xcc = x_check(xcc)
                    fxcc = func(xcc)
                    if fxcc < fsim[-1]:
                        # replace f(n+1) with f(xc)
                        sim[-1] = xcc
                        fsim[-1] = fxcc
                    else:
                        # enter shrink process
                        doshrink = 1
                if doshrink:
                    for j in one2np1:
                        # replace x1 - xn+1
                        sim[j] = sim[0] + sigma * (sim[j] - sim[0])
                        fsim[j] = func(x_check(sim[j]))

        # re cal and sort x and f(x)
        ind = np.argsort(fsim)
        sim = np.take(sim, ind, 0)
        fsim = np.take(fsim, ind, 0)

        # callback function
        if callback is not None:
            callback(fsim[0])

        # add iterations
        iterations += 1

        # record best simplex each iteration
        if return_all:
            allvecs.append(sim[0])

    # best x
    x = sim[0]

    # best function result
    fval = np.min(fsim)

    warnflag = 0
    if f_calls[0] >= maxfev:
        # more than max function executes
        warnflag = 1
        msg = _status_message["maxfev"]
        if disp:
            print(f"warning: {msg}")
    elif iterations >= maxiter:
        # more than iterations
        warnflag = 2
        msg = _status_message["maxiter"]
        if disp:
            print(f"warning: {msg}")
    else:
        # successful
        msg = _status_message["success"]
        if disp:
            msg += (
                f"\n         Current function value: {fval}"
                f"\n         Iterations: {iterations}"
                f"\n         Function evaluations: {f_calls[0]}"
            )
            print(f"EXP log: {msg}")

    # build optimize result
    result = OptimizeResult(
        fun=fval,
        nit=iterations,
        nfev=f_calls[0],
        status=warnflag,
        success=(warnflag == 0),
        message=msg,
        x=x,
        final_simplex=(sim, fsim),
    )

    if return_all:
        result["allvecs"] = allvecs

    result["sim_records"] = sim_records
    result["fsim_records"] = fsim_records

    return result, sim
