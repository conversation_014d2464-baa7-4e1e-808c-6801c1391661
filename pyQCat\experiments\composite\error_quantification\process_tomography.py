# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/02/24
# __author:       <PERSON><PERSON><PERSON>

import itertools

from ....analysis import AnalysisResult, ProcessTomographyAnalysis
from ....errors import ExperimentOptionsError
from ....gate import GateBucket, GateCollection
from ....structures import MetaData, Options
from ....types import <PERSON>RunMode, StandardContext
from ...composite_experiment import CompositeExperiment
from ...single import StateTomography


class ProcessTomography(CompositeExperiment):
    _sub_experiment_class = StateTomography

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options for AC spectrum experiment.

        Experiment options:
            init_fringe (float): The init value of fringe.
            delays (Union[List, np.ndarray]): Delay time scanned when performing Ramsey
                                              experiments.
            z_amp_list (Union[List, np.ndarray]): Z line amplitude (flux pulse) sweep list.
            freq_bound (Optional[float], optional): Experiment will be stopped when qubit
                                                    frequency delta value lower than this value.
                                                    Defaults to 800MHz.
            osc_freq_limit (Optional[float], optional): [description]. Defaults to 2.5.
        """
        options = super()._default_experiment_options()

        options.set_validator("base_gates", list)
        options.set_validator("qst_base_gates", list)
        options.set_validator("goal_gate", GateCollection.gate_infos())

        options.base_gates = ["I", "Y", "Y/2", "-X/2"]
        options.qst_base_gates = ["I", "X/2", "Y/2"]
        options.goal_gate = None
        options.goal_matrix = None
        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("use_mle", bool)
        options.set_validator("fidelity_accuracy", (1, 10, 0))

        options.use_mle = True
        options.sigma_basis = None
        options.goal_gate_matrix = None
        options.base_ops = None
        options.qst_base_ops = None
        options.labels = None
        options.qubit_nums = None

        # accuracy of result calculation fidelity
        options.fidelity_accuracy = 3

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options.

        Options:
            parking_qubits (List): List of BaseQubit,
                when name in parking_bits.

        """
        options = super()._default_run_options()

        options.qh = None
        options.ql = None
        options.pre_gate_list = None
        options.gate_bucket = GateBucket()
        options.support_context = [StandardContext.QC, StandardContext.CGC]

        return options

    def _check_options(self):
        super()._check_options()
        goal_gate = self.experiment_options.goal_gate
        base_gates = self.experiment_options.base_gates

        qubit_nums = 2 if self.qubit_pair else 1
        gate_bucket = self.run_options.gate_bucket
        self._label += f"{qubit_nums}Q"

        goal_matrix = gate_bucket.get_matrix(goal_gate)
        if self.experiment_options.goal_matrix is not None:
            goal_matrix = self.experiment_options.goal_matrix
        self.set_analysis_options(
            goal_gate_matrix=goal_matrix,
            base_ops=[gate_bucket.get_matrix(gate) for gate in base_gates],
            qst_base_ops=[
                gate_bucket.get_matrix(gate)
                for gate in self.child_experiment.experiment_options.base_gate
            ],
            labels=list(gate_bucket.pauli_matrix.keys()),
            sigma_basis=list(gate_bucket.pauli_matrix.values()),
            qubit_nums=qubit_nums,
        )

        if qubit_nums == 2:
            self.set_analysis_options(result_name=self.qubit_pair.name)
            for qubit in self.qubits:
                if qubit.name == self.qubit_pair.qh:
                    self.set_run_options(qh=qubit)
                elif qubit.name == self.qubit_pair.ql:
                    self.set_run_options(ql=qubit)
        else:
            self.set_analysis_options(result_name=self.qubits[0].name)

        pre_gate_list = self._generate_pre_gates()
        self.run_options.pre_gate_list = pre_gate_list
        self.set_run_options(
            x_data=list(range(len(pre_gate_list))),
            analysis_class=ProcessTomographyAnalysis,
        )

    def _metadata(self) -> MetaData:
        """Return experiment metadata for ExperimentData."""
        metadata = super()._metadata()

        metadata.draw_meta = {"Gate": self.experiment_options.goal_gate}

        return metadata

    def _generate_pre_gates(self):
        qubit_nums = 2 if self.qubit_pair else 1
        base_gates = self.experiment_options.base_gates
        goal_gate = self.experiment_options.goal_gate

        if qubit_nums == 1:
            pre_gate_list = []
            for index in range(2):
                if index == 0:
                    pre_gate_list.extend(
                        [[gate] for gate in self.experiment_options.base_gates]
                    )
                else:
                    pre_gate_list.extend(
                        [
                            [gate, self.experiment_options.goal_gate]
                            for gate in self.experiment_options.base_gates
                        ]
                    )
        else:
            if goal_gate == "CZ":
                goal_gate = [["CZ"], ["CZ"]]
            else:
                raise ExperimentOptionsError(
                    self,
                    key="goal_gate",
                    value=goal_gate,
                    msg="Current only support cz gate!",
                )
            gates_left = []
            gates_right = []
            for item in itertools.product(base_gates, repeat=2):
                gates_left.append([[item[0]], [item[1]]])
                r_l = [item[0]]
                r_l.extend(goal_gate[0])
                r_r = [item[1]]
                r_r.extend(goal_gate[1])
                gates_right.append([r_l, r_r])
            gates_left.extend(gates_right)
            pre_gate_list = gates_left

        return pre_gate_list

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        self.file.save_text(
            self.analysis.results.ideal_chi_matrix.__str__(), name="ideal_chi_matrix"
        )
        self.file.save_text(
            self.analysis.results.exp_chi_matrix.__str__(), name="exp_chi_matrix"
        )
        self.file.save_data(
            self.analysis.results.ideal_chi_matrix.value,
            name="ideal_chi_matrix",
        )
        self.file.save_data(
            self.analysis.results.exp_chi_matrix.value,
            name="exp_chi_matrix",
        )

        # if self.experiment_options.goal_gate == "CZ":
        #     for key, result in self.analysis.results.items():
        #         if key == "fidelity":
        #             result.extra["path"] = (
        #                 "QubitPair.metadata.std.fidelity.qpt_fidelity"
        #             )
        #         elif key == "process_fidelity":
        #             result.extra["path"] = (
        #                 "QubitPair.metadata.std.fidelity.qpt_process_fidelity"
        #             )
        #     qh_qpt = self.run_options.qh.qpt_fidelity
        #     ql_qpt = self.run_options.ql.qpt_fidelity
        #     v = self.analysis.results.fidelity.value + 2 - (ql_qpt + qh_qpt)

        #     self.analysis.results["ref_fidelity"] = AnalysisResult(
        #         name="ref_fidelity",
        #         value=self.analysis.results.fidelity.value,
        #         extra={
        #             "path": "QubitPair.metadata.std.pair_fidelity.ref_fidelity.qpt",
        #             "name": self.qubit_pair.name,
        #         },
        #     )

        #     self.analysis.results["aft_fidelity"] = AnalysisResult(
        #         name="aft_fidelity",
        #         value=v,
        #         extra={
        #             "path": "QubitPair.metadata.std.pair_fidelity.aft_fidelity.qpt",
        #             "name": self.qubit_pair.name,
        #         },
        #     )

        # elif self.experiment_options.goal_gate == "X" and not self.qubit_pair:
        #     for key, result in self.analysis.results.items():
        #         if key == "fidelity":
        #             result.extra["path"] = "Qubit.qpt_fidelity"

    def _setup_child_experiment(self, exp: StateTomography, index: int, *args):
        exp.run_options.index = index
        pre_gate = self.run_options.pre_gate_list[index]
        total = len(self.run_options.x_data)
        describe = str(pre_gate).replace("/", "-")
        exp.set_parent_file(self, describe, index, total)
        exp.set_experiment_options(pre_gate=pre_gate)
        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp: StateTomography):
        result = exp.analysis.results
        density_matrix = result.density_matrix.value
        exp.analysis.provide_for_parent["density_matrix"] = density_matrix
