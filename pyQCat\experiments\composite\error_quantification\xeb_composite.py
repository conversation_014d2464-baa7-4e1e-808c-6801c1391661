# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/23
# __author:       <PERSON><PERSON><PERSON>

from ....analysis import XEBCompositeAnalysis
from ....log import pyqlog
from ....structures import Options
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import XEBMultiple


class XEBComposite(CompositeExperiment):
    _sub_experiment_class = XEBMultiple

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("scan_list", list)
        options.set_validator("scan_name", ["qc", "ql", "qh"])
        options.scan_list = None
        options.scan_name = "qc"
        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.x_label = None
        options.raw_data_format = "plot"
        return options

    def _check_options(self):
        super()._check_options()
        filed_name = (
            "freq" if abs(self.experiment_options.scan_list[0]) > 1000 else "amp"
        )
        unit_name = getattr(self.qubit_pair, self.experiment_options.scan_name)
        self.analysis_options.x_label = f"{unit_name}-{filed_name}"
        self.set_run_options(
            x_data=self.experiment_options.scan_list,
            analysis_class=XEBCompositeAnalysis,
        )

    @staticmethod
    def set_child_exp_v(child_exp: XEBMultiple, scan_name: str, v: float):
        filed_name = "freq" if abs(v) > 1000 else "amp"
        unit_name = getattr(child_exp.qubit_pair, scan_name)
        child_exp.qubit_pair.set_cz_value(unit_name, filed_name, v)
        pyqlog.log("EXP", f"Set {unit_name} {filed_name} to {v}")

    def _setup_child_experiment(self, exp: XEBMultiple, idx: int, vol: float):
        exp.run_options.index = idx
        scan_name = self.experiment_options.scan_name
        describe = f"{scan_name}={vol}"
        total = len(self.run_options.x_data)
        exp.set_parent_file(self, describe, idx, total)
        self.set_child_exp_v(exp, scan_name, vol)
        self._check_simulator_data(exp, idx)

    def _handle_child_result(self, once_exp: XEBMultiple):
        once_exp.analysis.provide_for_parent["EA"] = (
            once_exp.analysis.results.ra.value * 0.01
        )
        once_exp.experiment_data.metadata.process_meta["depths"] = (
            once_exp.run_options.depths
        )
