# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.normalization.rst:2
msgid "pyQCat.tools.normalization"
msgstr ""

#: of pyQCat.tools.utilities.normalization:1
msgid "Normalize the data to the interval [0, 1]."
msgstr ""

#: of pyQCat.tools.utilities.normalization
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.utilities.normalization:4
msgid ""
"A data array to process. This is a single numpy array containing all "
"circuit results input to the data processor."
msgstr ""

#: of pyQCat.tools.utilities.normalization
msgid "Return type"
msgstr ""

#: of pyQCat.tools.utilities.normalization:7
msgid ":py:class:`~numpy.ndarray`"
msgstr ""

#: of pyQCat.tools.utilities.normalization
msgid "Returns"
msgstr ""

#: of pyQCat.tools.utilities.normalization:8
msgid "The normalized data."
msgstr ""

