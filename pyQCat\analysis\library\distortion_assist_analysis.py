# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/12/21
# __author:       xw

"""
DistortionAssist Analysis.
"""

from ..oscillation_analysis import DumpedOscillationAnalysis
from ..specification import ParameterRepr
from ...structures import Options


class DistortionAssistAnalysis(DumpedOscillationAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Return the default analysis options."""
        options = super()._default_options()
        options.x_label = "Delay (ns)"
        options.result_parameters = [ParameterRepr("freq", "fosc", "MHz")]
        options.quality_bounds = [0.98, 0.93, 0.81]
        return options

    def _extract_result(self, data_key: str):
        """Convert frequency from GHz to MHz.
        Args:
            data_key (str): The basis for selecting data.
        """
        super()._extract_result(data_key)
        result = self.results.freq
        result.value = round(result.value * 1e3, 5)
