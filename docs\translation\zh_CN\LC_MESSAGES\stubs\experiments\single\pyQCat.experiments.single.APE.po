# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:2
msgid "pyQCat.experiments.single.APE"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE:1
msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE:1
msgid "APE experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
#: of pyQCat.experiments.top_experiment.TopExperiment.__init__:1
msgid "Create a new experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.single.APE.__init__>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`acquire_pulse <pyQCat.experiments.single.APE.acquire_pulse>`\\ "
"\\(acq\\_pulse\\[\\, qubit\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Set measure pulse."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`cal_fidelity <pyQCat.experiments.single.APE.cal_fidelity>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Calculate the fidelity matrix."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`experiment_info "
"<pyQCat.experiments.single.APE.experiment_info>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Show experiment informations."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.single.APE.from_experiment_context>`\\ \\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str <pyQCat.experiments.single.APE.get_qubit_str>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`jupyter_schedule "
"<pyQCat.experiments.single.APE.jupyter_schedule>`\\ \\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Show experiment pulses's schedules in Jupyter Notebook."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`options_table <pyQCat.experiments.single.APE.options_table>`\\ "
"\\(\\[mode\\, detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`play_pulse <pyQCat.experiments.single.APE.play_pulse>`\\ "
"\\(name\\, base\\_qubit\\, pulse\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Play pulse for XY control and Z flux control."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`plot_schedule <pyQCat.experiments.single.APE.plot_schedule>`\\ "
"\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
"Plot the pulse schedule which is very important to confirm experiment "
"logical correction."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.single.APE.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
#: of pyQCat.experiments.single.ape_once.APE.run:1
msgid "Run the APE once experiment."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.single.APE.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.single.APE.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_multiple_IF "
"<pyQCat.experiments.single.APE.set_multiple_IF>`\\ \\(\\*IF\\[\\, "
"channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Used for multiple qubits experiment, set mutiple IF."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_multiple_index "
"<pyQCat.experiments.single.APE.set_multiple_index>`\\ \\(\\*args\\[\\, "
"channel\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
"Used for multiple qubits experiment, set mutiple index. :type args: "
":py:class:`list` :param args: Setting the start and end values :type "
"channel: :py:class:`int` :param channel: channel settings Usage::     "
">>>experiment = TopExperiment()     >>>experiment.set_multiple_index([0, "
"1500], [100, 900], channel=1)."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.single.APE.set_parent_file>`\\ \\(save\\_file\\[\\, "
"description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.single.APE.set_run_options>`\\ \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`set_sweep_order "
"<pyQCat.experiments.single.APE.set_sweep_order>`\\ \\(order\\_list\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid "Old interface."
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:39:<autosummary>:1
msgid ""
":py:obj:`sweep_readout_trigger_delay "
"<pyQCat.experiments.single.APE.sweep_readout_trigger_delay>`\\ "
"\\(channel\\, sweep\\_delay\\)"
msgstr ""

#: ../../source/stubs/experiments/single/pyQCat.experiments.single.APE.rst:41
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.single.APE.analysis>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.single.APE.analysis_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.single.APE.experiment_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`multi_sweep <pyQCat.experiments.single.APE.multi_sweep>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`one_sweep <pyQCat.experiments.single.APE.one_sweep>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:1:<autosummary>:1
msgid ":py:obj:`run_options <pyQCat.experiments.single.APE.run_options>`\\"
msgstr ""

#: of
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._default_experiment_options:10
msgid "Experiment options:"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._default_experiment_options:4
msgid ""
"sweep_name (str): Experiment purpose, \"detune\" or \"phase\". sweep_list"
" (List, np.ndarray): Scan detune or phase list. phi_num (int): Multiples "
"phase of π or π/2 pulse. theta_type (str): Support `θ` type, normal "
"\"Xpi\" or \"Xpi/2\"."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._default_experiment_options:8
msgid "Calibrate π gate or π/2 gate."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._default_experiment_options:9
msgid "N (int): Repeat times of pairing drag pulse."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._default_analysis_options
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options
#: pyQCat.experiments.single.ape_once.APE._metadata
msgid "Return type"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._default_analysis_options:4
#: pyQCat.experiments.single.ape_once.APE._default_experiment_options:12
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._default_analysis_options:1
msgid "Default kwarg options for analysis."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._set_xy_pulses:1
msgid "Set XY pulses."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._metadata:1
msgid "Set metadata."
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._check_options:1
msgid "sweep_name仅支持detune和phase两种类型;"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._check_options:2
msgid "theta_type仅支持Xpi和 Xpi/2两种类型;"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._check_options:3
msgid "如果用判据, 分析P0数据, 否则幅值相位数据均处理;"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._check_options:4
msgid "输出结果名称由sweep_name决定"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._check_options:5
msgid "x_lable由sweep_name决定"
msgstr ""

#: of pyQCat.experiments.single.ape_once.APE._check_options:7
msgid ""
"`sweep_name` only supports two types: detune and phase; `theta_type` only"
" supports two types: xpi and xpi/2; If the criterion is used, process P0 "
"data, otherwise the amplitude and phase data are processed; The output "
"result name is determined by sweep name."
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.experiments.single.APE.__init__>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`acquire_pulse "
#~ "<pyQCat.experiments.single.APE.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.single.APE.from_experiment_context>`\\ "
#~ "\\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.single.APE.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`play_pulse "
#~ "<pyQCat.experiments.single.APE.play_pulse>`\\ \\(name\\, "
#~ "base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`plot_schedule "
#~ "<pyQCat.experiments.single.APE.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`run <pyQCat.experiments.single.APE.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.single.APE.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.single.APE.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_IF "
#~ "<pyQCat.experiments.single.APE.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_multiple_index "
#~ "<pyQCat.experiments.single.APE.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.single.APE.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.single.APE.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_sweep_order "
#~ "<pyQCat.experiments.single.APE.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":py:obj:`analysis <pyQCat.experiments.single.APE.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.single.APE.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.single.APE.experiment_options>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`multi_sweep <pyQCat.experiments.single.APE.multi_sweep>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`one_sweep <pyQCat.experiments.single.APE.one_sweep>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`run_options <pyQCat.experiments.single.APE.run_options>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.top_experiment.TopExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.experiments.single.APE.__init__>`\\ "
#~ "\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`acquire_pulse "
#~ "<pyQCat.experiments.single.APE.acquire_pulse>`\\ "
#~ "\\(acq\\_pulse\\[\\, qubit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_info "
#~ "<pyQCat.experiments.single.APE.experiment_info>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.single.APE.from_experiment_context>`\\ "
#~ "\\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.single.APE.get_qubit_str>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`jupyter_schedule "
#~ "<pyQCat.experiments.single.APE.jupyter_schedule>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.single.APE.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`play_pulse <pyQCat.experiments.single.APE.play_pulse>`\\"
#~ " \\(name\\, base\\_qubit\\, pulse\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`plot_schedule "
#~ "<pyQCat.experiments.single.APE.plot_schedule>`\\ "
#~ "\\(\\[schedule\\_index\\, is\\_save\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`run <pyQCat.experiments.single.APE.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.single.APE.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.single.APE.set_experiment_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_IF "
#~ "<pyQCat.experiments.single.APE.set_multiple_IF>`\\ "
#~ "\\(\\*IF\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_multiple_index "
#~ "<pyQCat.experiments.single.APE.set_multiple_index>`\\ "
#~ "\\(\\*args\\[\\, channel\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.single.APE.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.single.APE.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_sweep_order "
#~ "<pyQCat.experiments.single.APE.set_sweep_order>`\\ "
#~ "\\(order\\_list\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.single.APE.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.single.APE.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.single.APE.experiment_options>`\\"
#~ msgstr ""

#~ msgid ":obj:`multi_sweep <pyQCat.experiments.single.APE.multi_sweep>`\\"
#~ msgstr ""

#~ msgid ":obj:`one_sweep <pyQCat.experiments.single.APE.one_sweep>`\\"
#~ msgstr ""

#~ msgid ":obj:`run_options <pyQCat.experiments.single.APE.run_options>`\\"
#~ msgstr ""

