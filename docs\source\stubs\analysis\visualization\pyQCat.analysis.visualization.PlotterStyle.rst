﻿pyQCat.analysis.visualization.PlotterStyle
==========================================

.. currentmodule:: pyQCat.analysis.visualization

.. autoclass:: PlotterStyle

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~PlotterStyle.__init__
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~PlotterStyle.axis_label_size
      ~PlotterStyle.figsize
      ~PlotterStyle.fit_report_rpos
      ~PlotterStyle.fit_report_text_size
      ~PlotterStyle.legend_loc
      ~PlotterStyle.tick_label_size
      ~PlotterStyle.plot_sigma
   
   