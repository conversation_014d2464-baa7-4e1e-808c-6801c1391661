# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/12/27
# __author:       <PERSON><PERSON><PERSON>

from typing import TYPE_CHECKING

from PySide6.QtCore import QModelIndex

from ..base.table_structure import QTableModelBase
from ..base.table_structure import QTableViewBase

if TYPE_CHECKING:
    from ...main_window import VisageGUI
    from ..user_operation import UserManagerWindow
    from .table_view_users import QTableViewUserWidget


class QTableModelUser(QTableModelBase):
    def __init__(
        self,
        gui: "VisageGUI",
        parent: "UserManagerWindow" = None,
        table_view: "QTableViewUserWidget" = None,
    ):
        super().__init__(gui=gui, parent=parent, table_view=table_view)
        self.columns = ["username", "groups", "is_super", "is_admin", "white", "black"]
        self.group_name = None

    def set_columns(self, columns):
        self.columns = columns

    def rowCount(self, parent: QModelIndex = None) -> int:
        if self.backend and self.filtered_model_data:  # type(self.backend) == <class 'pyqcat_visage.backend.backend.Backend'>
            num = len(self.filtered_model_data)
            if num == 0:
                if isinstance(self._tableView, QTableViewBase):
                    self._tableView.show_placeholder_text()
            else:
                if isinstance(self._tableView, QTableViewBase):
                    self._tableView.hide_placeholder_text()
            return num
        else:
            if isinstance(self._tableView, QTableViewBase):
                self._tableView.show_placeholder_text()
            return 0

    def filter_users(self, text: str):
        """Filters users by their username."""
        for user in self.model_data:  # type(self.model_data[0]) == <class 'pyQCat.structures.QDict'>
            if text.lower() in user.username.lower():
                user.should_be_shown = True
            else:
                user.should_be_shown = False
        self._tableView.viewport().update()

    @property
    def model_data(self):
        return self.widget.users.get(self.group_name)  # type(self.widget.users.get(self.group_name)) == <class 'list'>

    @property
    def filtered_model_data(self):
        users = self.model_data
        if users is None:
            return None
        return [user for user in users if user.should_be_shown is True or user.should_be_shown == {}]

    def user_from_index(self, index: QModelIndex):
        return self.filtered_model_data[index.row()]

    def _display_data(self, index: QModelIndex):
        user = self.user_from_index(index)
        if user.should_be_shown is False:
            return None
        return user.get(self.columns[index.column()])
