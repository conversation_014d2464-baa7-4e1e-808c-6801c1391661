# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:2
msgid "pyQCat.experiments.composite.ConditionalPhase"
msgstr ""

#: of pyQCat.experiments.composite.conditional_phase.ConditionalPhase:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
msgstr ""

#: of pyQCat.experiments.composite.conditional_phase.ConditionalPhase:1
msgid "Standard ConditionalPhase experiment."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.experiments.composite.ConditionalPhase.__init__>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.composite.ConditionalPhase.component_experiment>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.composite.ConditionalPhase.from_experiment_context>`\\"
" \\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.composite.ConditionalPhase.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.composite.ConditionalPhase.options_table>`\\ "
"\\(\\[mode\\, detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.composite.ConditionalPhase.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite.conditional_phase.ConditionalPhase.run:1
msgid "Standard ConditionalPhase Run Logic."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.composite.ConditionalPhase.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.composite.ConditionalPhase.set_experiment_options>`\\"
" \\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.composite.ConditionalPhase.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.composite.ConditionalPhase.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.ConditionalPhase.rst:32
msgid "Attributes"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid ""
":py:obj:`analysis "
"<pyQCat.experiments.composite.ConditionalPhase.analysis>`\\"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.composite.ConditionalPhase.analysis_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.composite.ConditionalPhase.child_experiment>`\\"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.composite.ConditionalPhase.experiment_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.composite.ConditionalPhase.run_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.cz_assist.CZAssist:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:37
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:4
msgid ""
"z_amp_list (List): Scan qc z_amp list. swap_fit_args (List): Swap fit "
"parameters, [g, A, z0]. tc_list (List): Corresponding z_amp_list, relate "
"tc list,"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:7
msgid "if give swap_fit_args, no setting this field."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:9
msgid ""
"readout_type (str): Readout type. is_amend (bool): True means use "
"fidelity_matrix amend result. cz_num (int): Two X/2 center add number CZ "
"pulse, default 1."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:13
msgid "qt_name (str): The target bit name. qc_name (str): The control bit name."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:17
msgid "scan_high_bit (bool): Is or not scan high frequency bit."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:17
msgid "Normal scan control bit, will set z_amp to the bit Z line."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:19
msgid ""
"const_z_amp (float): The set z_amp of no-scan-bit. cz_width (float): The "
"value is Flattop Gaussian Pulse width."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:21
msgid "CZ pulse width, that normal determined by Swap experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:22
msgid "sigma (float): The value is Flattop Gaussian Pulse sigma."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:24
msgid "phase_list (List, array): When simulator bit, last X/2 scan phase list."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:26
msgid "parking_bits (List(str)): List of parking name,"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:27
msgid "normal like: [\"q2\", \"c0\", ...]"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:37
msgid "parking_param_dict (dict):"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:29
msgid "The parking bits parameter of Flattop Gaussian Pulse. normal like: {"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:32
msgid "\"q2\": {\"amp\": 0.4}, \"c0\": {\"amp\": 0.2}, ..."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:35
msgid "}"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_analysis_options
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_run_options
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_analysis_options:10
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_experiment_options:39
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_run_options:9
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_run_options:1
msgid "Set run experiment operate options."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_run_options:7
msgid "Options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_run_options:4
msgid ""
"phase_I_list (List): List of phase, control_gate is I. phase_X_list "
"(List): List of phase, control_gate is X."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_analysis_options:1
msgid "Default analysis options."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_analysis_options:8
msgid "Analysis Options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_analysis_options:4
msgid "quality_bounds (Iterable[float]):"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_analysis_options:5
msgid "The bounds value of the goodness of fit."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._default_analysis_options:6
msgid ""
"is_plot (bool): Plot or not analysis fit photo. data_key (List[str]): "
"List of mark select data."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._metadata:1
msgid "Set metadata."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._check_options:1
msgid "Check Options."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._run_analysis:1
msgid "Run composite analysis."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._run_analysis
msgid "Parameters"
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._run_analysis:4
msgid "The value of the x data."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._run_analysis:7
msgid "analysis class."
msgstr ""

#: of
#: pyQCat.experiments.composite.conditional_phase.ConditionalPhase._save_cz_pulse_json:1
msgid "Save CZ pulse json."
msgstr ""

