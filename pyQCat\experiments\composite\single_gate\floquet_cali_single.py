# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/10/30
# __author:       <PERSON> Fang

"""
Floquet calibration Single gate once experiment.
"""

import numpy as np

from ....log import pyqlog
from ....structures import MetaData, Options
from ....tools.utilities import qarange
from ....types import ExperimentRunMode
from ....analysis.library_v2 import FloquetCaliSingleAnalysis, FloquetAmpOptimizeAnalysis
from ...composite_experiment import CompositeExperiment
from ...single.single_gate.floquet_cali_single_once import FloquetCalibrationSingleOnce


class FloquetCalibrationSingle(CompositeExperiment):
    """Experiment FloquetCalibrationSingle class."""

    _sub_experiment_class = FloquetCalibrationSingleOnce

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options."""
        options = super()._default_experiment_options()

        options.set_validator("phase_list", list)
        options.set_validator("limit_phase", float)
        options.set_validator("zeta0", float)
        options.set_validator("amp", float)

        options.phase_list = np.linspace(-np.pi, np.pi, 61)
        options.limit_phase = 0.1
        options.zeta0 = None
        options.amp = None

        options.run_mode = ExperimentRunMode.async_mode
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options."""
        options = super()._default_analysis_options()
        options.set_validator("theta", float)
        options.set_validator("zeta0", float)

        options.theta = 0.5  # 拟合参数 theta 初始值
        options.zeta0 = 0.0  # 拟合参数 zeta0 初始值
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default run options."""
        options = super()._default_run_options()

        options.x_data = []
        options.analysis_class = FloquetCaliSingleAnalysis
        return options

    def _metadata(self) -> MetaData:
        """Set RabiScanWidth experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "exp zeta0": self.experiment_options.zeta0,
            "amp": self.experiment_options.amp,
        }
        return metadata

    def _check_options(self):
        super()._check_options()
        phase_list = self.experiment_options.phase_list
        limit_phase = self.experiment_options.limit_phase
        set_zeta0 = self.experiment_options.zeta0

        t_qubit = self.qubits[0]
        zeta0 = t_qubit.zeta if set_zeta0 is None else set_zeta0
        pyqlog.info(f"You set zeta0: {set_zeta0}, so final zeta0: {zeta0}")

        x_data = []
        for phase in phase_list:
            if (
                zeta0 - np.pi - limit_phase
                <= phase
                <= zeta0 - np.pi + limit_phase
            ) or (
                zeta0 + np.pi - limit_phase
                <= phase
                <= zeta0 + np.pi + limit_phase
            ):
                pyqlog.warning(
                    f"zeta0 {zeta0}, limit_phase {limit_phase} so remove phase: {phase}"
                )
                continue
            x_data.append(phase)

        self.set_experiment_options(zeta0=zeta0)
        self.set_run_options(x_data=x_data)
        self.set_analysis_options(result_name=t_qubit.name)

    def _setup_child_experiment(
        self, exp: "FloquetCalibrationSingleOnce", index: int, value: float
    ):
        """Set child_experiment some options."""
        amp = self.experiment_options.amp

        flo_once_exp = exp
        flo_once_exp.run_options.index = index
        total = len(self.run_options.x_data)

        describe = f"changing_phase={value} amp={amp}"
        flo_once_exp.set_parent_file(self, describe, index, total)
        flo_once_exp.set_experiment_options(changing_phase=value, amp=amp)
        self._check_simulator_data(flo_once_exp, index)

    def _handle_child_result(self, exp: "FloquetCalibrationSingleOnce"):
        ou = exp.analysis.results.ou.value
        exp.analysis.provide_for_parent.update({"ou": ou})


class FloquetAmpOptimize(CompositeExperiment):
    """Experiment Floquet optimize X/2 amp class."""

    _sub_experiment_class = FloquetCalibrationSingle

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options."""
        options = super()._default_experiment_options()
        options.set_validator("amp_list", list)

        options.amp_list = qarange(0.1, 0.5, 0.05)
        options.run_mode = ExperimentRunMode.async_mode
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options."""
        options = super()._default_analysis_options()

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default run options."""
        options = super()._default_run_options()

        options.x_data = []
        options.analysis_class = FloquetAmpOptimizeAnalysis
        return options

    def _metadata(self) -> MetaData:
        """Set RabiScanWidth experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {}
        return metadata

    def _check_options(self):
        super()._check_options()
        t_qubit = self.qubits[0]
        self.set_run_options(x_data=self.experiment_options.amp_list)
        self.set_analysis_options(result_name=t_qubit.name)

    def _setup_child_experiment(
        self, exp: "FloquetCalibrationSingle", index: int, value: float
    ):
        """Set child_experiment some options."""
        flo_once_exp = exp
        flo_once_exp.run_options.index = index
        total = len(self.run_options.x_data)

        describe = f"amp={value}"
        flo_once_exp.set_parent_file(self, describe, index, total)
        flo_once_exp.set_experiment_options(amp=value)
        self._check_simulator_data(flo_once_exp, index)

    def _handle_child_result(self, exp: "FloquetCalibrationSingle"):
        theta = exp.analysis.results.theta.value
        zeta0 = exp.analysis.results.zeta0.value
        exp.analysis.provide_for_parent.update({"theta": theta, "zeta": zeta0})
