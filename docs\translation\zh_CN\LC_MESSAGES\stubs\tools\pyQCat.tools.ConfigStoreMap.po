# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-29 13:55+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.ConfigStoreMap.rst:2
msgid "pyQCat.tools.ConfigStoreMap"
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.tools.ConfigStoreMap.__init__>`\\ "
#~ "\\(file\\_name\\[\\, username\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`read_config "
#~ "<pyQCat.tools.ConfigStoreMap.read_config>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`save_config "
#~ "<pyQCat.tools.ConfigStoreMap.save_config>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`write_config "
#~ "<pyQCat.tools.ConfigStoreMap.write_config>`\\ \\(\\[value\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`filename <pyQCat.tools.ConfigStoreMap.filename>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`postfix_list <pyQCat.tools.ConfigStoreMap.postfix_list>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`username <pyQCat.tools.ConfigStoreMap.username>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid "Class map ConfigStore."
#~ msgstr ""

#~ msgid ""
#~ "self.filename is a config file name, "
#~ "self._file is a config file name "
#~ "without a postfix, self._postfix is a"
#~ " postfix of config file name, "
#~ "self._config_doc is an object of "
#~ "ConfigDoc."
#~ msgstr ""

#~ msgid "Parameters"
#~ msgstr ""

#~ msgid "config file name, likes `dc_crosstalk.dat`"
#~ msgstr ""

#~ msgid "Belong to user config file."
#~ msgstr ""

#~ msgid "Methods"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.tools.ConfigStoreMap.__init__>`\\ "
#~ "\\(file\\_name\\[\\, username\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`read_config <pyQCat.tools.ConfigStoreMap.read_config>`\\ \\(\\)"
#~ msgstr ""

#~ msgid "Read from MongoDB ConfigStore."
#~ msgstr ""

#~ msgid ":obj:`save_config <pyQCat.tools.ConfigStoreMap.save_config>`\\ \\(\\)"
#~ msgstr ""

#~ msgid "Save self._config_doc to MongDB ConfigStore."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`write_config <pyQCat.tools.ConfigStoreMap.write_config>`\\"
#~ " \\(\\[value\\]\\)"
#~ msgstr ""

#~ msgid "Write a value to MongoDB ConfigStore."
#~ msgstr ""

#~ msgid "Attributes"
#~ msgstr ""

#~ msgid ":obj:`filename <pyQCat.tools.ConfigStoreMap.filename>`\\"
#~ msgstr ""

#~ msgid ":obj:`postfix_list <pyQCat.tools.ConfigStoreMap.postfix_list>`\\"
#~ msgstr ""

#~ msgid ":obj:`username <pyQCat.tools.ConfigStoreMap.username>`\\"
#~ msgstr ""

#~ msgid "Specific config info by self._config_doc."
#~ msgstr ""

#~ msgid "Returns"
#~ msgstr ""

#~ msgid ""
#~ "The config file data,     may be a"
#~ " np.array, dict or IQdiscriminator object."
#~ msgstr ""

#~ msgid "The config file data,"
#~ msgstr ""

#~ msgid "may be a np.array, dict or IQdiscriminator object."
#~ msgstr ""

#~ msgid "Return type"
#~ msgstr ""

#~ msgid ""
#~ "Write a value to MongoDB ConfigStore."
#~ " The corresponding self._config_doc's field "
#~ "is determined by self._file and "
#~ "self._postfix."
#~ msgstr ""

#~ msgid "the value specifically written to the database."
#~ msgstr ""

