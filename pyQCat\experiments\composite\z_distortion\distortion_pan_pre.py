# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/17
# __author:       <PERSON><PERSON><PERSON>

from collections import defaultdict

import numpy as np

from ....analysis.library.distortion_pan_analysis import DistortionAnalysis
from ....structures import Options
from ....tools import qarange
from ...composite_experiment import CompositeExperiment
from ...single import DistortionPhaseDetuneScan
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class DistortionPhaseDetune2D(CompositeExperiment):
    _sub_experiment_class = DistortionPhaseDetuneScan

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("detune_list", list)
        options.set_validator("unit", ["MHz", "V"])

        options.detune_list = qarange(-1, 1, 0.1)
        options.unit = "MHz"
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.name = "sweep"
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.F_dict = defaultdict(list)
        return options

    def _check_options(self):
        super()._check_options()

        if len(self.child_experiment.experiment_options.phases) == 2:
            self._label = "DistortionDetuneX2Y2"
            self.set_analysis_options(name="y2")
        else:
            self._label = "DistortionDetuneSweepPhase"
            self.set_analysis_options(name="sweep")
        self.set_run_options(
            x_data=self.experiment_options.detune_list,
            analysis_class=DistortionAnalysis,
        )

    def _setup_child_experiment(self, exp: "TopExperiment", index: int, value: float):
        detune_list = self.experiment_options.detune_list
        if self.experiment_options.unit == "MHz":
            describe = f"detune = {value} MHz"
            exp.set_experiment_options(detune=value, z_amp=0)
        elif self.experiment_options.unit == "V":
            describe = f"detune = {value} V"
            exp.set_experiment_options(detune=None, z_amp=value)
        else:
            raise ValueError(f"unit {self.experiment_options.unit} is not supproted.")

        exp.set_parent_file(self, describe, index=index, total=len(detune_list))

        self._check_simulator_data(exp, index)

    def _handle_child_result(self, exp: "TopExperiment"):
        results = exp.analysis.results
        for key in results.keys():
            v = results.get(key).value
            exp.analysis.provide_for_parent[key] = v
            self.run_options.F_dict[key].append(v)

    def _alone_save_result(self):
        detune_list = self.experiment_options.detune_list
        result_datas = self.run_options.F_dict

        save_file = f"{self}(distortion_phase_phase unwrap)"
        save_data_list = [np.array(detune_list)]

        if self.analysis_options.name == "sweep":
            new_y = result_datas.get("point")
        else:
            new_y = result_datas.get("phase")

        new_y = np.unwrap(new_y)

        for i, v in enumerate(new_y):
            exp = self._experiments[i]
            exp.analysis.provide_for_parent["unwrap_phase"] = v
            result_datas["unwrap_phase"].append(v)

        save_data_list.extend(np.array(data) for data in list(result_datas.values()))
        self.file.save_data(*save_data_list, name=save_file)
