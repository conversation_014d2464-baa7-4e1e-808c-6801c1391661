# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/gate/pyQCat.gate.GateCollection.rst:2
msgid "pyQCat.gate.GateCollection"
msgstr ""

#: of pyQCat.gate.notable_gate.GateCollection:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateCollection.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateCollection.rst:21:<autosummary>:1
msgid ":py:obj:`__init__ <pyQCat.gate.GateCollection.__init__>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateCollection.rst:23
msgid "Attributes"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateCollection.rst:27:<autosummary>:1
msgid ":py:obj:`double_gate_map <pyQCat.gate.GateCollection.double_gate_map>`\\"
msgstr ""

#: ../../source/stubs/gate/pyQCat.gate.GateCollection.rst:27:<autosummary>:1
msgid ":py:obj:`single_gate_map <pyQCat.gate.GateCollection.single_gate_map>`\\"
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ":py:obj:`__init__ <pyQCat.gate.GateCollection.__init__>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`gate_map <pyQCat.gate.GateCollection.gate_map>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`pauli_matrix <pyQCat.gate.GateCollection.pauli_matrix>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ":obj:`__init__ <pyQCat.gate.GateCollection.__init__>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`gate_map <pyQCat.gate.GateCollection.gate_map>`\\"
#~ msgstr ""

#~ msgid ":obj:`pauli_matrix <pyQCat.gate.GateCollection.pauli_matrix>`\\"
#~ msgstr ""

