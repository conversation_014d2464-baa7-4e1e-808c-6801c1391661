# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.set_attr.rst:2
msgid "pyQCat.tools.set\\_attr"
msgstr ""

#: of pyQCat.tools.utilities.set_attr:1
msgid "Set obj attr value,"
msgstr ""

#: of pyQCat.tools.utilities.set_attr:2
msgid "when inconsistent with other obj attribute value"
msgstr ""

#: of pyQCat.tools.utilities.set_attr
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.utilities.set_attr:4
msgid "Any object"
msgstr ""

#: of pyQCat.tools.utilities.set_attr:7
msgid "attribute name"
msgstr ""

#: of pyQCat.tools.utilities.set_attr:10
msgid "Other Any object"
msgstr ""

#: of pyQCat.tools.utilities.set_attr
msgid "Returns"
msgstr ""

#: of pyQCat.tools.utilities.set_attr:13
msgid "True or None"
msgstr ""

