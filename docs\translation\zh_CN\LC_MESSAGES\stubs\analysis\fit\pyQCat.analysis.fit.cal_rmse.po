# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/fit/pyQCat.analysis.fit.cal_rmse.rst:2
msgid "pyQCat.analysis.fit.cal\\_rmse"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:1
msgid "Calculate curve goodness of fit."
msgstr "计算拟合优度"

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:3
msgid ""
"We usually use goodness of fit to assess the quality of experimental "
"results, which is calculated as:"
msgstr ""
"我们通常使用拟合优度来评估实验结果的质量，计算方法如下："

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:6
msgid "rmse = \\sqrt{\\frac{\\sum_{i=0}^{n} {(y_2 - y_1)}^2}{n} }"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:11
msgid "x-axis data."
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:13
msgid "y1-axis data, usually input raw signal."
msgstr "通常为原始数据"

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:15
msgid "y2-axis data, usually input fit signal."
msgstr "通常为拟合数据"

#: of pyQCat.analysis.fit.curve_fit.cal_rmse
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:17
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.fit.curve_fit.cal_rmse:18
msgid "Goodness of fit"
msgstr "拟合优度"

