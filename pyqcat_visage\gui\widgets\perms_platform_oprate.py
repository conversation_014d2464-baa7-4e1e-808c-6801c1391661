# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/03/14
# __author:       <PERSON><PERSON><PERSON>

from typing import TYPE_CHECKING, Dict, List, Union

from PySide6.QtWidgets import QCheckBox, QVBoxLayout, QMessageBox, QSpacerItem, QSizePolicy

from pyqcat_visage.gui.widgets.title_window import TitleWindow
from pyqcat_visage.gui.permission_platform_operate_ui import Ui_MainWindow

if TYPE_CHECKING:
    from pyqcat_visage.gui.main_window import VisageGUI


class PlatformManageWindow(TitleWindow):
    def __init__(self, gui: "VisageGUI", parent=None):
        super().__init__(parent)
        self._ui = Ui_MainWindow()
        self._ui.setupUi(self)
        self.gui = gui
        self.widgets = [self._ui.ListBox1, self._ui.ListBox2]
        self.layouts: Dict[int, QVBoxLayout] = {}
        self.checkboxes: List[QCheckBox] = []
        self.origin_target = []
        self.init_widget()

    @property
    def ui(self):
        return self._ui

    def reset_window_layout(self):
        pass

    def load_default_data(self):
        pass

    def clear_children(self):
        self.checkboxes.clear()
        self.origin_target.clear()
        for widget in self.widgets:
            layout = widget.layout()
            if layout:
                while layout.count():
                    child = layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()
        self.ui.groupBox_2.update()

    def init_widget(self):
        self.ui.TypeBox.clear()
        self.ui.PlatformBox.clear()
        self.ui.TypeBox.addItems(["user", "group"])
        ret_data = self.gui.backend.db.query_platform(perms_type=3, page_size=100)
        self.clear_children()
        if ret_data.get("code") == 200:
            if ret_data.get("data"):
                for item in ret_data["data"]:
                    self.ui.PlatformBox.addItem(item["name"], item["id"])

    def query_info(self):

        perms_id = self.ui.PlatformBox.currentData()
        perms_type = self.ui.TypeBox.currentText()
        ret_data = self.gui.backend.db.query_platform_perms(perms_id=perms_id, perms_type=perms_type)
        self.clear_children()
        data = ret_data.get("data")
        if data:
            self.origin_target = data
            for num, name in enumerate(data):
                index = num % len(self.widgets)
                layout = self.layouts.get(index)
                if not layout:
                    self.layouts[index] = QVBoxLayout(self.widgets[index])

                checkbox = QCheckBox(name)
                checkbox.setChecked(True)
                self.checkboxes.append(checkbox)
                self.layouts[index].addWidget(checkbox)
                self.layouts[index].setStretch(0, 1)
            vertical_spacer = QSpacerItem(10, 10, QSizePolicy.Minimum, QSizePolicy.Expanding)
            self.layouts[0].addItem(vertical_spacer)
            if len(self.layouts) > 1:
                self.layouts[1].addItem(vertical_spacer)
            self.ui.groupBox_2.update()

    @staticmethod
    def compare_diff_list(old_list: List, new_list: List):
        """
        Compare the two lists and calculate the difference set
        Args:
            old_list: origin data
            new_list: new data

        Returns(tuple):
                (add_list, del_list)
        """
        if not old_list and not new_list:
            return [], []
        elif not old_list:
            return new_list, []
        elif not new_list:
            return [], old_list
        add_list = list(set(new_list) - set(old_list))
        del_list = list(set(old_list) - set(new_list))
        return add_list, del_list

    def save_info(self):
        target = []
        for checkbox in self.checkboxes:
            if checkbox.isChecked():
                target.append(checkbox.text())
        perms_id = self.ui.PlatformBox.currentData()
        perms_type = self.ui.TypeBox.currentText()
        add_name, del_name = self.compare_diff_list(self.origin_target, target)
        if not add_name and not del_name:
            QMessageBox().warning(self.ui.groupBox_2, "warning", "no change permissions!")
            return
        ret_data = self.gui.backend.db.operate_platform_perms(perms_id=perms_id, perms_type=perms_type, target=target)
        self.handler_ret_data(ret_data, show_suc=True)

        self.origin_target = target
        return

    def reset(self):
        self.init_widget()
        self.clear_children()


