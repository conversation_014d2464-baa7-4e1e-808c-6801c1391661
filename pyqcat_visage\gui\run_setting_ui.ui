<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>598</width>
    <height>372</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Run Setting</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0" rowspan="2">
     <widget class="QGroupBox" name="groupBox_2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="title">
       <string>EXP</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,2,2,2">
         <property name="spacing">
          <number>6</number>
         </property>
         <item>
          <widget class="QLabel" name="label_2">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>Result</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="exp_save_mode">
           <item>
            <property name="text">
             <string>Unsave</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Save</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="use_simulator">
           <property name="text">
            <string>simulator</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_4">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Policy::Preferred</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,5,1">
         <property name="spacing">
          <number>6</number>
         </property>
         <item>
          <widget class="QLabel" name="label_3">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>Path</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="simulator_data_path">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="placeholderText">
            <string>Please input simulator dat path...</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="import_button">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>Import</string>
           </property>
           <property name="icon">
            <iconset>
             <normaloff>_imgs/import.png</normaloff>_imgs/import.png</iconset>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_8">
         <item>
          <widget class="QLabel" name="label_9">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>simulator delay</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="simulator_delay">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="placeholderText">
            <string>delay...</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_7">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
    <item row="4" column="0">
     <widget class="QGroupBox" name="groupBox_3">
      <property name="title">
       <string>QAIO</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2" stretch="1,2,1,2,1">
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>37</width>
             <height>17</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>qaio ip</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>37</width>
             <height>17</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLineEdit" name="inst_host">
           <property name="text">
            <string>127.0.0.1</string>
           </property>
           <property name="maxLength">
            <number>15</number>
           </property>
           <property name="placeholderText">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>37</width>
             <height>17</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <item>
          <widget class="QLabel" name="label_5">
           <property name="text">
            <string>mongo port</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSpinBox" name="inst_mongo">
           <property name="maximum">
            <number>50000</number>
           </property>
           <property name="value">
            <number>27017</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>log port</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSpinBox" name="inst_log">
           <property name="maximum">
            <number>50000</number>
           </property>
           <property name="value">
            <number>27021</number>
           </property>
           <property name="displayIntegerBase">
            <number>10</number>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_3">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
    <item row="2" column="0">
     <widget class="QGroupBox" name="groupBox">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="title">
       <string>DAG</string>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,2,2,2">
       <item>
        <widget class="QLabel" name="label">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="text">
          <string>Result</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="dag_save_mode">
         <item>
          <property name="text">
           <string>Unsave</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>Save In Process</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>Save In Final</string>
          </property>
         </item>
        </widget>
       </item>
       <item>
        <widget class="QCheckBox" name="use_backtrace">
         <property name="text">
          <string>backtrace</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QCheckBox" name="register_dag">
         <property name="text">
          <string>register</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item row="4" column="1">
     <widget class="QPushButton" name="default_button">
      <property name="text">
       <string>Default</string>
      </property>
      <property name="icon">
       <iconset>
        <normaloff>_imgs/reset.png</normaloff>_imgs/reset.png</iconset>
      </property>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QPushButton" name="ok_button">
      <property name="text">
       <string>Ok</string>
      </property>
      <property name="icon">
       <iconset>
        <normaloff>_imgs/ok.png</normaloff>_imgs/ok.png</iconset>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>ok_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>ok()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>554</x>
     <y>92</y>
    </hint>
    <hint type="destinationlabel">
     <x>477</x>
     <y>-13</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>default_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>default()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>537</x>
     <y>228</y>
    </hint>
    <hint type="destinationlabel">
     <x>416</x>
     <y>-4</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>import_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>import_sp()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>453</x>
     <y>137</y>
    </hint>
    <hint type="destinationlabel">
     <x>337</x>
     <y>-19</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>ok()</slot>
  <slot>default()</slot>
  <slot>import_sp()</slot>
 </slots>
</ui>
