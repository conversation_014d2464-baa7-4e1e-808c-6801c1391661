# -*- coding: utf-8 -*-
# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/19
# __author:       <PERSON>

from ....pulse import Constant
from ....structures import Options
from .f12_spectrum import QubitSpectrumF12


class QubitSpectrumF12_2D(QubitSpectrumF12):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.z_amp = 0
        options.delay = 20
        return options

    @staticmethod
    def set_z_pulses(builder):
        """Set z pulse."""
        f01_pulse_width = builder.qubit.XYwave.time + builder.qubit.XYwave.offset * 2
        f12_pulse_width = (
            builder.qubit.f12_options.time + builder.qubit.f12_options.offset * 2
        )
        z_amp = builder.experiment_options.z_amp
        delay = builder.experiment_options.delay

        pulse_1_width = f01_pulse_width
        pulse_2 = f12_pulse_width + delay

        pulse_1 = Constant(pulse_1_width, 0)
        pulse_2 = Constant(pulse_2, z_amp)
        pulse_3 = Constant(pulse_1_width, 0)

        z_pulse = pulse_1() + pulse_2() + pulse_3()
        if z_pulse is not None:
            if len(builder.couplers) != 0:
                builder.play_pulse("Z", builder.couplers[0], z_pulse)
            else:
                builder.play_pulse("Z", builder.qubit, z_pulse)
