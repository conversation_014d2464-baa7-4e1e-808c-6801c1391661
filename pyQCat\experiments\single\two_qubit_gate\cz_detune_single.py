# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/06/11
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy

from ....analysis.library.cz_detune_analysis import (
    SweepDetuneAnalysis,
    SweepDetunePurityAnalysis,
)
from ....parameters import options_wrapper
from ....pulse.pulse_function import stimulate_state_pulse, zero_pulse
from ....pulse.pulse_lib import Constant
from ....pulse_adjust import params_to_pulse
from ....structures import MetaData, Options
from ....tools import cz_flow_options_adapter
from ....types import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment
from .swap_once import (
    validate_data_key,
    validate_qubit_pair_cz_std,
    validate_two_qubit_exp_read_options,
)


@options_wrapper
class SweepDetune(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("detune_list", list)
        options.set_validator("adapter_detune_list", list)
        options.set_validator("label", ["detune1", "detune2"])
        options.set_validator("scan_name", str)
        options.set_validator("gate_num", int)
        options.set_validator("auto_fetch", bool)
        options.detune_list = None
        options.adapter_detune_list = None
        options.scan_name = "qh"
        options.label = "detune1"
        options.readout_type = None
        options.gate_num = 1
        options.auto_fetch = False
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.x_label = None
        options.quality_bounds = [0.98, 0.93, 0.81]
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []
        options.offset_width = None
        options.support_context = [StandardContext.CGC]
        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        cz_flow_options_adapter(self)
        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)
        validate_data_key(self)

        if self.experiment_options.auto_fetch is True:
            max_phase_point = self.qubit_pair.metadata.std.process.max_phase_point
            detune1 = max_phase_point.get("detune1")
            detune2 = max_phase_point.get("detune2")

            if abs(detune1[0]) > 1:
                self.experiment_options.detune_freq_list = detune1
            else:
                self.experiment_options.detune_list = detune1

            if abs(detune2[0]) > 1:
                self.experiment_options.adapter_detune_freq_list = detune2
            else:
                self.experiment_options.adapter_detune_list = detune2

            if self.experiment_options.adapter_detune_list is not None:
                assert len(self.experiment_options.detune_list) == len(
                    self.experiment_options.adapter_detune_list
                )

            self.run_options.analysis_class = SweepDetuneAnalysis
        else:
            self.run_options.analysis_class = SweepDetunePurityAnalysis

        self.analysis_options.x_label = self.experiment_options.label
        self.set_run_options(x_data=self.experiment_options.detune_list)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "scan_name": self.experiment_options.scan_name,
        }
        metadata.process_meta = {
            "x_data": self.experiment_options.detune_list,
            "detune_freq": self.experiment_options.detune_freq_list,
            "adapter_detune_freq": self.experiment_options.adapter_detune_freq_list,
        }
        return metadata

    @staticmethod
    def set_xy_pulses(self):
        """Set SwapOnce experiment XY pulses."""
        eop = self.experiment_options
        rop = self.run_options
        cz_width = rop.width

        for qubit in rop.env_bits:
            if qubit.name in [self.qubit_pair.qh, self.qubit_pair.ql]:
                state = "1"
            elif qubit.name.startswith("q"):
                state = "0"
            else:
                continue

            state_pulse = stimulate_state_pulse(state, qubit)
            offset_pulse = Constant(cz_width, 0, name="XY")
            xy_pulse = state_pulse() + offset_pulse()

            self.play_pulse(
                "XY", qubit, [deepcopy(xy_pulse) for _ in range(len(eop.detune_list))]
            )

    @staticmethod
    def set_z_pulses(self):
        """Set SwapOnce experiment XY pulses."""
        eop = self.experiment_options
        rop = self.run_options
        zero_x = zero_pulse(rop.qh, "Z")
        cz_width = self.qubit_pair.width()

        for qubit in rop.env_bits:
            q_assign_pulse = deepcopy(zero_x)
            s_gate_params = deepcopy(rop.gate_params.get(qubit.name))
            s_gate_params.update({"time": cz_width})
            sd = False

            if qubit.name == eop.scan_name:
                sd = True

            z_pulse_list = []
            for idx, detune in enumerate(eop.detune_list):
                new_q_assign_pulse = deepcopy(q_assign_pulse)
                if sd:
                    s_gate_params.update({eop.label: detune})
                    if eop.adapter_detune_list:
                        nl = "detune2" if eop.label == "detune1" else "detune1"
                        s_gate_params.update({nl: eop.adapter_detune_list[idx]})
                target_pulse = params_to_pulse(**s_gate_params)
                z_pulse = new_q_assign_pulse()
                for i in range(self.experiment_options.gate_num):
                    z_pulse += deepcopy(target_pulse)()
                z_pulse_list.append(z_pulse)

            self.play_pulse("Z", qubit, z_pulse_list)

    def _set_result_path(self):
        for k, result in self.analysis.results.items():
            result.extra.update(
                {
                    "path": f"QubitPair.metadata.std.cz.params.{self.experiment_options.scan_name}.{k}",
                    "name": self.qubit_pair.name,
                }
            )
