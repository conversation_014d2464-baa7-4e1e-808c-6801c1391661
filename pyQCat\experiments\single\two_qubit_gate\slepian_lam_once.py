# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/18
# __author:       <PERSON> <PERSON>

"""
SlepianLamOnce Experiment.
"""

from copy import deepcopy

import numpy as np

from ....analysis.library.slepian_lam_once_anlysis import SlepianLamOnceAnalysis
from ....errors import ExperimentError, ExperimentOptionsError
from ....log import logger
from ....pulse.pulse_function import stimulate_state_pulse, pi_pulse
from ....pulse.pulse_lib import Constant
from ....pulse_adjust import params_to_pulse
from ....qubit import Coupler
from ....structures import MetaData, Options
from ....tools.utilities import cz_flow_options_adapter
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment
from .swap_once import (
    validate_data_key,
    validate_qubit_pair_cz_std,
    validate_two_qubit_exp_read_options,
)


class SlepianLamOnce(TopExperiment):
    """Test Slepian pulse lam experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Set default experiment_options."""
        options = super()._default_experiment_options()

        options.set_validator("freq_list", list)
        options.set_validator("lam1_list", list)
        options.set_validator("lam2", float)
        options.set_validator("swap_state", ["10", "11", "01"])
        options.set_validator("cz_num", int)

        options.freq_list = []
        options.lam1_list = []
        options.lam2 = 0.1
        options.swap_state = "11"
        options.cz_num = 1

        options.scan_name = "qc"
        options.label = "cz"
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Set default analysis_options."""
        options = super()._default_analysis_options()

        options.x_label = "lam1"
        options.data_key = None
        options.raw_data_format = "plot"

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set default run_options."""
        options = super()._default_run_options()

        options.freq_list = []
        options.lam1_list = []
        options.max_iter_count = 0
        options.width = 0.0
        options.offset_width = 0.0
        options.env_bits = []
        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = None
        options.support_context = [StandardContext.CGC]

        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        cz_flow_options_adapter(self)
        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)
        validate_data_key(self)

        # check offset width
        cz_num = self.experiment_options.cz_num
        offset_width = self.run_options.width * cz_num
        self.set_run_options(offset_width=offset_width)

        # ================ alone set ================
        lam1_list = self.experiment_options.lam1_list
        freq_list = self.experiment_options.freq_list
        gate_params = self.run_options.gate_params
        coupler_obj = self.couplers[0]

        c_gate_params = gate_params.get(coupler_obj.name)
        if not isinstance(c_gate_params.get("coupler_ac_spectrum"), dict):
            c_gate_params.update(
                {"coupler_ac_spectrum": coupler_obj.ac_spectrum.to_dict()}
            )

        freq_high_work = c_gate_params.get("freq_high_work")
        freq_coupler_idle = c_gate_params.get("freq_coupler_idle")
        glc_idle = c_gate_params.get("glc_idle")

        # optimize get lam1_list and freq_list
        if not lam1_list and not freq_list:
            raise ExperimentOptionsError(
                exp=self,
                msg="Options lam1_list, freq_list, at least set one!",
            )

        if any(v is None for v in [freq_high_work, freq_coupler_idle, glc_idle]):
            raise ExperimentOptionsError(
                self, msg=f"Please check gate params | {c_gate_params}"
            )

        theta_i = np.arctan(2 * glc_idle / (freq_coupler_idle - freq_high_work))
        
        new_freq_list = []
        if lam1_list:
            for lam1 in lam1_list:
                theta_f = 2 * lam1 - theta_i
                freq = freq_high_work + 2 * glc_idle / np.tan(theta_f)
                new_freq_list.append(freq)
        else:
            lam1_list = []
            for freq in freq_list:
                if not np.isnan(freq):
                    theta_f = np.arctan(2 * glc_idle / (freq - freq_high_work))
                    lam1 = (theta_f - theta_i) / 2
                    lam1_list.append(lam1)
                    new_freq_list.append(freq)

        logger.info(f"First lam1 will scan, lam1_list: {lam1_list}")
        logger.info(f"First lam1 will scan, freq_list: {new_freq_list}")
        max_iter_count = len(lam1_list)

        self.set_run_options(
            freq_list=new_freq_list,
            x_data=lam1_list,
            lam1_list=lam1_list,
            max_iter_count=max_iter_count,
            gate_params=gate_params,
            analysis_class=SlepianLamOnceAnalysis,
        )

        readout_type = self.experiment_options.readout_type

        if readout_type in ["ql-01", "ql-02", "ql-012"]:
            self.run_options.measure_qubits = [self.run_options.ql]
        elif readout_type in ["qh-01", "qh-02", "qh-012"]:
            self.run_options.measure_qubits = [self.run_options.qh]
        else:
            self.run_options.measure_qubits = [self.run_options.qh, self.run_options.ql]

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {"lam2": self.experiment_options.lam2}
        return metadata

    @staticmethod
    def set_xy_pulses(self):
        """Set LeakageOnce experiment XY pulses."""
        swap_state = self.experiment_options.swap_state
        max_iter_count = self.run_options.max_iter_count
        offset_width = self.run_options.offset_width
        env_bits = self.run_options.env_bits
        qh_name = self.run_options.qh.name
        ql_name = self.run_options.ql.name

        for qubit in env_bits:
            if qubit.name == qh_name:
                state = swap_state[0]
            elif qubit.name == ql_name:
                state = swap_state[1]
            elif qubit.name.startswith("q"):
                state = "0"
            else:
                continue

            state_pulse = stimulate_state_pulse(state, qubit)
            offset_pulse = Constant(offset_width, 0, name="XY")

            xy_pulse = state_pulse() + offset_pulse()
            self.play_pulse(
                "XY", qubit, [deepcopy(xy_pulse) for _ in range(max_iter_count)]
            )

    @staticmethod
    def set_z_pulses(self):
        """Set LeakageOnce experiment Z pulses."""
        cz_num = self.experiment_options.cz_num
        lam2 = self.experiment_options.lam2
        lam1_list = self.run_options.lam1_list
        max_iter_count = self.run_options.max_iter_count
        gate_params = self.run_options.gate_params
        env_bits = self.run_options.env_bits
        width = self.run_options.width
        drag_time = pi_pulse(self.run_options.qh).width

        for qc_obj in env_bits:
            s_gate_params = gate_params.get(qc_obj.name)
            s_gate_params.update({"time": width})
            if isinstance(qc_obj, Coupler):
                z_pulse_list = []
                for lam1 in lam1_list:
                    s_gate_params.update({"lam_list": [lam1, lam2]})
                    q_assign_pulse = Constant(drag_time, 0)
                    target_pulse = params_to_pulse(**s_gate_params)
                    all_cz_pulse = target_pulse() * cz_num
                    once_pulse = q_assign_pulse() + all_cz_pulse
                    z_pulse_list.append(once_pulse)
            else:
                z_pulse_list = []
                for i in range(max_iter_count):
                    q_assign_pulse = Constant(drag_time, 0)
                    target_pulse = params_to_pulse(**s_gate_params)
                    all_cz_pulse = target_pulse() * cz_num
                    once_pulse = q_assign_pulse() + all_cz_pulse
                    z_pulse_list.append(once_pulse)
            self.play_pulse("Z", qc_obj, z_pulse_list)

    def _set_result_path(self):
        """Set result path."""
        lam2 = self.experiment_options.lam2
        file_name = f"{self.qubit_pair.name}_lam2={lam2}_lam1_freq"
        self.file.save_data(
            np.asarray(self.run_options.lam1_list),
            np.asarray(self.run_options.freq_list),
            name=file_name,
        )


class SlepianLamNumOnce(SlepianLamOnce):
    def _check_options(self):
        """Check Options."""
        super(SlepianLamOnce, self)._check_options()

        cz_flow_options_adapter(self)
        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)
        validate_data_key(self)

        # check offset width
        cz_num = self.experiment_options.cz_num
        offset_width = self.run_options.width * cz_num
        self.set_run_options(offset_width=offset_width)

        # ================ alone set ================
        gate_params = self.run_options.gate_params
        coupler_obj = self.couplers[0]

        slepian_params = self.qubit_pair.metadata.std.process.slepian
        lam1_list = slepian_params.lam1_list

        c_gate_params = gate_params.get(coupler_obj.name)
        if not isinstance(c_gate_params.get("coupler_ac_spectrum"), dict):
            c_gate_params.update(
                {"coupler_ac_spectrum": coupler_obj.ac_spectrum.to_dict()}
            )

        logger.info(f"First lam1 will scan, lam1_list: {lam1_list}")
        max_iter_count = len(lam1_list)

        self.set_run_options(
            max_iter_count=max_iter_count,
            gate_params=gate_params,
            analysis_class=SlepianLamOnceAnalysis,
        )

        x_data = self.qubit_pair.metadata.std.process.slepian.lam1_list
        self.run_options.x_data = x_data

    @staticmethod
    def set_z_pulses(self):
        """Set LeakageOnce experiment Z pulses."""
        cz_num = self.experiment_options.cz_num
        print(self.qubit_pairs)
        slepian_params = self.qubit_pairs[0].metadata.std.process.slepian
        lam2_list = slepian_params.lam2_list
        lam1_list = slepian_params.lam1_list
        if not lam2_list or not lam1_list:
            raise ExperimentError(
                exp=self,
                msg=f"Options lam1_list and lam2_list must not be empty!",
            )

        if len(lam1_list) != len(lam2_list):
            raise ExperimentError(
                exp=self,
                msg=f"Options lam1_list and lam2_list must be same length!",
            )

        max_iter_count = self.run_options.max_iter_count
        gate_params = self.run_options.gate_params
        env_bits = self.run_options.env_bits
        width = self.run_options.width
        drag_time = pi_pulse(self.run_options.qh).width

        for qc_obj in env_bits:
            s_gate_params = gate_params.get(qc_obj.name)
            s_gate_params.update({"time": width})
            if isinstance(qc_obj, Coupler):
                z_pulse_list = []
                for i, lam1 in enumerate(lam1_list):
                    lam2 = lam2_list[i]
                    s_gate_params.update({"lam_list": [lam1, lam2]})
                    q_assign_pulse = Constant(drag_time, 0)
                    target_pulse = params_to_pulse(**s_gate_params)
                    all_cz_pulse = target_pulse() * cz_num
                    once_pulse = q_assign_pulse() + all_cz_pulse
                    z_pulse_list.append(once_pulse)
            else:
                z_pulse_list = []
                for i in range(max_iter_count):
                    q_assign_pulse = Constant(drag_time, 0)
                    target_pulse = params_to_pulse(**s_gate_params)
                    all_cz_pulse = target_pulse() * cz_num
                    once_pulse = q_assign_pulse() + all_cz_pulse
                    z_pulse_list.append(once_pulse)
            self.play_pulse("Z", qc_obj, z_pulse_list)
