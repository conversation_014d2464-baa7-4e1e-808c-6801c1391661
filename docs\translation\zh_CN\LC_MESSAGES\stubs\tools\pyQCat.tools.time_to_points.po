# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.time_to_points.rst:2
msgid "pyQCat.tools.time\\_to\\_points"
msgstr ""

#: of pyQCat.tools.utilities.time_to_points:1
msgid "Calculate sampling points by waveform width ."
msgstr ""

#: of pyQCat.tools.utilities.time_to_points
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.utilities.time_to_points:4
msgid "waveform width"
msgstr ""

#: of pyQCat.tools.utilities.time_to_points:7
msgid "sample rate of AIO"
msgstr ""

#: of pyQCat.tools.utilities.time_to_points
msgid "Returns"
msgstr ""

#: of pyQCat.tools.utilities.time_to_points:10
msgid "sample points"
msgstr ""

#: of pyQCat.tools.utilities.time_to_points
msgid "Return type"
msgstr ""

