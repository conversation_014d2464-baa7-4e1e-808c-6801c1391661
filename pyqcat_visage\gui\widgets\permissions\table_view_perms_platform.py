# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/02/19
# __author:       <PERSON><PERSON><PERSON>
from functools import partial

from PySide6.QtWidgets import QTableView, QAbstractItemView, QWidget, QHeaderView, QMenu
from PySide6.QtCore import Signal, QModelIndex
from ..base.right_click_menu import bind_action
from ..base.table_structure import QTableViewBase


class QTableViewPermsPlatformWidget(QTableViewBase):
    choose_item_signal = Signal(dict)

    def __init__(self, parent: QWidget):
        """
        Args:
            parent (QtWidgets.QWidget): The parent widget
        """
        self.ui = parent.parent().parent()
        QTableViewBase.__init__(self, parent, "Select permission/username to query Notes!")

    def _define_style(self):
        # Handling selection dynamically
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.setSelectionBehavior(QTableView.SelectRows)
        # self.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

    @property
    def backend(self):
        """Returns the design."""
        return self.model().backend

    @property
    def gui(self):
        """Returns the GUI."""
        return self.model().gui

    def update_column_widths(self):
        total_width = self.width()
        if self.model().columns_ratio:
            total_weight = sum(self.model().columns_ratio)
            for i, width in enumerate(self.model().columns_ratio):
                new_width = int((width / total_weight) * total_width)
                self.setColumnWidth(i, new_width)
        else:
            self.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.update_column_widths()

    # def view_clicked(self, index: QModelIndex):
    #     """Select a component and set it in the component widget when you left mouse click.
    #
    #     In the init, we had to connect with self.clicked.connect(self.viewClicked)
    #
    #     Args:
    #         index (QModelIndex): The index
    #     """
    #
    #     self.his_index = index
    #
    #     if self.gui is None or not index.isValid():
    #         return
    #
    #     model = self.model()
    #     item = model.item_from_index(index)
    #     if item:
    #         self.choose_item_signal.emit(item)
    #
    # def refresh_view(self):
    #     if self.his_index:
    #         self.view_clicked(self.his_index)


