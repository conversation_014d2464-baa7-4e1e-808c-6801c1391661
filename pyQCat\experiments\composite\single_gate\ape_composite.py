# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/24
# __author:       ssfang

"""
APE Composite.
"""

from ....analysis.library import APECompositeAnalysis
from ....structures import MetaData, Options
from ....tools import qarange
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single import APE, CouplerAPE


class APEComposite(CompositeExperiment):
    _sub_experiment_class = APE

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("detune_list", list)
        options.set_validator("n_list", list)
        options.set_validator("theta_type", ["Xpi", "Xpi/2"])
        options.set_validator("scan_type", ["rough", "fine"])

        options.detune_list = qarange(-25, 10, 1)
        options.n_list = [7, 9, 13]
        options.theta_type = "Xpi"
        options.scan_type = "rough"
        options.f12_opt = False

        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("diff_threshold", (0, 1, 2))

        options.diff_threshold = 0.2
        options.data_key = ["Points"]
        options.fine = None
        options.step = None
        options.plot_key = None
        options.y_label = None

        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        scan_type = self.experiment_options.scan_type
        detune_list = self.experiment_options.detune_list

        step = detune_list[1] - detune_list[0]
        fine = False if scan_type == "rough" else True

        cc_exp = self.child_experiment
        result_name, plot_key, y_label = self.check_key(cc_exp)

        self.set_analysis_options(
            step=step,
            fine=fine,
            result_name=result_name,
            plot_key=plot_key,
            y_label=y_label,
        )

        # Shq 2024/04/29
        # for async mode.
        self.run_options.x_data = self.experiment_options.n_list
        self.run_options.analysis_class = APECompositeAnalysis

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.draw_meta = {
            "theta_type": self.experiment_options.theta_type,
            "scan_type": self.experiment_options.scan_type,
        }
        return metadata

    @staticmethod
    def check_key(cc_exp):
        if cc_exp.is_coupler_exp:
            result_name = cc_exp.coupler.name
            plot_key = "P1"
            y_label = "P1"
        elif cc_exp.coupler:
            result_name = cc_exp.coupler.name
            plot_key = "P0"
            y_label = "P0"
        else:
            result_name = cc_exp.qubit.name
            plot_key = "P0"
            y_label = "P0"

        return result_name, plot_key, y_label

    def _setup_child_experiment(self, child_exp: APE, index: int, n: float):
        n_list = self.experiment_options.n_list
        detune_list = self.experiment_options.detune_list
        theta_type = self.experiment_options.theta_type
        f12_opt = self.experiment_options.f12_opt
        fine = self.analysis_options.fine
        child_exp.set_parent_file(self, f"N={n}", index, len(n_list))
        child_exp.set_experiment_options(
            sweep_name="detune",
            sweep_list=detune_list,
            theta_type=theta_type,
            N=int(n),
            f12_opt=f12_opt,
        )
        self._check_simulator_data(child_exp, index)
        child_exp.set_analysis_options(fine=fine)

    def _handle_child_result(self, child_exp: APE):
        provide_field = self.analysis_options.data_key[0]
        fine = self.analysis_options.fine
        if fine is True:
            points = child_exp.analysis.results.fit_points_0.value
        else:
            points = child_exp.analysis.results.points_0.value

        child_exp.analysis.provide_for_parent.update({provide_field: points})
        child_exp.experiment_data.metadata.process_meta["quality"] = child_exp.analysis.quality


class CouplerAPEComposite(APEComposite):
    """Coupler Frequency Cali."""

    _sub_experiment_class = CouplerAPE
