# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/09/15
# __author:       Zza

import math

from prettytable import PrettyTable

from ...log import pyqlog
from ..batch_experiment import EXP_TYPE, BatchExperiment
from .batch_coupler_distortion import distortion_collection


class BatchQubitDistortionT1New(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.change_xpi_width = False
        options.pi_pulse_params = {"width": 200, "offset": 5}
        options.iir_distortion_flows = [
            "XpiDetection",
            "SingleShot",
            "VoltageDriftGradientCalibration",
            "DistortionT1CompositeNew",
        ]
        options.rb_opt_flows = [
            "SingleShot",
            "VoltageDriftGradientCalibration",
            "DistortionPolesOpt",
        ]
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.origin_data = {}
        return options

    def _batch_up(self):
        """Batch Pre-Execution Processing."""
        super()._batch_up()

        if self.experiment_options.iir_distortion_flows:
            table = PrettyTable()
            table.field_names = [
                "unit",
                "drive_power",
                "width",
                "offset",
                "detune_pi",
                "detune_pi2",
            ]
            if self.experiment_options.change_xpi_width is True:
                pyqlog.info(f"Change XPI width ...")
                pi_pulse_params = self.experiment_options.pi_pulse_params
                for unit in self.experiment_options.physical_units:
                    qubit = self.context_manager.chip_data.cache_qubit.get(unit)
                    _width = pi_pulse_params.get("width", 200)
                    _offset = pi_pulse_params.get("offset", 5)
                    db = math.log2(_width / qubit.XYwave.time)
                    new_drive_power = qubit.drive_power - db * 6
                    qubit.XYwave.time = _width
                    qubit.XYwave.offset = _offset
                    if new_drive_power > -11:
                        new_drive_power = -11
                    if new_drive_power < -40:
                        new_drive_power = -40
                    qubit.drive_power = round(new_drive_power, 1)
                    qubit.XYwave.detune_pi = 0
                    qubit.XYwave.detune_pi2 = 0
                    table.add_row(
                        [
                            unit,
                            qubit.drive_power,
                            qubit.XYwave.time,
                            qubit.XYwave.offset,
                            qubit.XYwave.detune_pi,
                            qubit.XYwave.detune_pi2,
                        ]
                    )
                pyqlog.info(
                    f"{self.__class__.__name__} change pi pulse overview:\n {table}"
                )

    def _run_batch(self):
        group_map = self.parallel_allocator_for_qc(self.experiment_options.physical_units)

        save_every_exp = self.experiment_options.save_every_exp
        if self.experiment_options.iir_distortion_flows:
            flows = self.experiment_options.iir_distortion_flows
            self.experiment_options.save_every_exp = False
        else:
            flows = self.experiment_options.rb_opt_flows
            self.experiment_options.save_every_exp = save_every_exp

        pass_units = []
        for gn, group in group_map.items():
            pus = self._run_flow(flows, group, name=f"{gn} flow")
            if pus:
                pass_units.extend(pus)
        fail_units = [
            unit
            for unit in self.experiment_options.physical_units
            if unit not in pass_units
        ]

        self._show_batch_execute_result(
            all_units=self.experiment_options.physical_units, bad_units=fail_units
        )

        return fail_units

    def _record_experiment(
        self, exp_name: str, exp: EXP_TYPE, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "DistortionT1CompositeNew" in exp_name:
            origin_data = distortion_collection(self, exp, record)
            self.run_options.origin_data.update(origin_data)
            self._save_data_to_json(
                self.run_options.origin_data, "origin-iir-data.json"
            )
        return record
