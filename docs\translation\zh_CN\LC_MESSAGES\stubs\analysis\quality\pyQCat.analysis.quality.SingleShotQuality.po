# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-07 11:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.SingleShotQuality.rst:2
msgid "pyQCat.analysis.quality.SingleShotQuality"
msgstr ""

#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality:1
msgid "SingleShot analysis quality evaluation standard."
msgstr "SingleShot 实验得到分类器的质量判断。"

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.SingleShotQuality.rst:22:<autosummary>:1
#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality.__init__:1
msgid "Initial SingleShot Quality object."
msgstr "初始化方法。"

#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality.__init__:4
msgid "Model ideal clusters."
msgstr "SingleShot 分类器推荐聚类数量 k_recommend."

#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality.__init__:7
msgid "The `0` state fidelity."
msgstr "分类器 0 态保真度。"

#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality.__init__:10
msgid "The `1` state fidelity."
msgstr "分类器 1 态保真度。"

#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality.__init__:13
msgid "The dcm outlier value."
msgstr "分类器 outlier 值。"

#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality.__init__:16
msgid "The bounds of quality judge standard. normal like: [2, 0.85, 0.7, 0.011]"
msgstr "分类器判断条件，比如设置： [2, 0.85, 0.7, 0.011]，"
"分类器的参数要满足： k_recommend = 2, F0 > 0.85, F1 > 0.7, outlier < 0.011 这些条件，才合格。"

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.SingleShotQuality.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.SingleShotQuality.rst:22:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.quality.SingleShotQuality.__init__>`\\"
" \\(k\\_recommend\\, F0\\, F1\\, outlier\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.SingleShotQuality.rst:22:<autosummary>:1
msgid ""
":py:obj:`evaluate <pyQCat.analysis.quality.SingleShotQuality.evaluate>`\\"
" \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.SingleShotQuality.rst:22:<autosummary>:1
#: of pyQCat.analysis.quality.single_shot_quality.SingleShotQuality.evaluate:1
msgid "Calculate quality value."
msgstr "根据传入的 quality_bounds 进行质量判断，提取质量结果。"

#: ../../source/stubs/analysis/quality/pyQCat.analysis.quality.SingleShotQuality.rst:24
msgid "Attributes"
msgstr ""

#: of pyQCat.analysis.quality.SingleShotQuality.quality:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.quality.SingleShotQuality.quality>`\\"
msgstr ""

#: of pyQCat.analysis.quality.SingleShotQuality.quality:1
#: pyQCat.analysis.quality.SingleShotQuality.quality:1:<autosummary>:1
msgid "Quality value."
msgstr "质量结果"

