# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/6/27
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

import matplotlib.pyplot as plt
import numpy as np

from ...structures import Options, QDict
from ..curve_analysis import CurveAnalysis, CurveAnalysisData
from ..fit.fit_models import erf_fit_func
from ..specification import FitModel, ParameterRepr


class CavityPowerScanAnalysis(CurveAnalysis):
    """
    Cavity Power Scan Analysis class, inherits from CurveAnalysis.
    Used for analyzing cavity power scan experiment data, extracting characteristic parameters, and visualization.
    """

    @classmethod
    def _default_options(cls) -> Options:
        """
        Get the default parameter options for this analysis.
        Returns:
            Options: Contains all default parameter settings required for this analysis.
        """
        options = super()._default_options()
        options.subplots = (2, 2)
        options.data_key = ["power"]
        options.x_label = "probe power[dBm]"
        options.y_label = [
            "Frequency [MHz]",
            "Amp - Fc [MHz]",
            "Phase - Fc [MHz]",
            "Amp-FC-Normalization",
        ]
        options.pcolormesh_options = {
            "shading": "auto",
            "cmap": plt.cm.get_cmap("rainbow"),
        }
        options.result_parameters = [
            ParameterRepr(
                name="power",
                repr="probe-power",
                unit="db",
                param_path="Qubit.probe_power",
            ),
            ParameterRepr(
                name="hf_power", repr="high-freq-probe-power", unit="db", param_path=""
            ),
            ParameterRepr(
                name="fr", repr="fc", unit="MHz", param_path="Qubit.probe_freq"
            ),
            ParameterRepr(name="l_fr", repr="low_freq", unit="MHz", param_path=""),
            ParameterRepr(name="h_fr", repr="high_freq", unit="MHz", param_path=""),
            ParameterRepr(
                name="goodness", repr="", unit="", param_path="Qubit.goodness"
            ),
        ]
        options.figsize = (12, 8)
        options.f_threshold = 0.1
        options.coeff = 2

        options.fit_model = FitModel(
            fit_func=erf_fit_func, model_description=r"-A * erf((x-a)/b) + B"
        )
        options.quality_bounds = [0.98, 0.95, 0.85]  # perfect, normal, abnormal, bad
        return options

    def _create_analysis_data(self):
        """Create Analysis data provided for detailed analyze.

        Returns:
            A QDict object, key represents data type and value is
            CurveAnalysisData object.
        """
        fit_x = []
        fit_y = []
        for idx, x in enumerate(self.experiment_data.x_data):
            child_exp = self.experiment_data.child_data(idx)
            quality = child_exp.metadata.process_meta["quality"]
            if quality.is_pass():
                fit_x.append(x)
                fit_y.append(self.experiment_data.y_data["fc"][idx])

        return QDict(
            fc=CurveAnalysisData(
                x=np.array(fit_x),
                y=np.array(fit_y),
            )
        )

    def _guess_fit_param(self, fit_opt, data):
        """
        Data preprocessing and initial parameter guessing for the fitter.
        """
        x = data.x
        y = data.y
        amp = (max(y) - min(y)) / 2
        a = np.median(x)
        b = 4
        B = np.mean(y)
        fit_opt.p0.set_if_empty(A=amp, a=a, b=b, B=B)
        fit_opt.bounds.set_if_empty(b=(1e-3, np.inf))
        return fit_opt

    def _extract_result(self, data_key: str):
        """
        Extract analysis results, calculate critical points and quality based on fitting parameters.
        """
        freq_list = self.experiment_data.child_data(0).x_data
        freq_left, freq_right = freq_list[0], freq_list[-1]
        if self._quality.is_pass():
            analysis_data = self.analysis_datas[data_key]
            A, a, b, B = analysis_data.fit_data.popt

            coefficient = self.options.coeff
            low_drive_power = a - (coefficient * b)
            high_drive_power = a + (coefficient * b)

            freq_at_low_power = erf_fit_func(low_drive_power, A, a, b, B)
            freq_at_high_power = erf_fit_func(high_drive_power, A, a, b, B)
            hf_power_found = True

            # Quality evaluation
            if (
                hf_power_found
                and abs(freq_at_high_power - freq_at_low_power)
                > self.options.f_threshold
                and (freq_left - 2 < freq_at_low_power < freq_right + 2)
                and (freq_left - 2 < freq_at_high_power < freq_right + 2)
            ):
                self._quality.set_perfect()
                self.results.goodness.value = True
            else:
                self._quality.set_bad()
                self.results.goodness.value = False

            # Assign results
            # Round the power results
            low_drive_power_rounded = np.round(low_drive_power)
            high_drive_power_rounded = np.round(high_drive_power)

            freq_at_low_power_rounded = np.round(freq_at_low_power, 3)
            freq_at_high_power_rounded = np.round(freq_at_high_power, 3)

            self.results.fr.value = freq_at_low_power_rounded
            self.results.power.value = low_drive_power_rounded
            self.results.hf_power.value = high_drive_power_rounded
            self.results.l_fr.value = freq_at_low_power_rounded
            self.results.h_fr.value = freq_at_high_power_rounded

    def _visualization(self):
        """
        Visualize analysis results.
        If there is child experiment data, draw pseudo-color maps of amplitude, phase, and normalized amplitude.
        """
        super()._visualization()
        if self.has_child is True:
            x_arr = self.experiment_data.x_data  # Outer power scan points
            y_arr = None  # Inner frequency scan points
            amp_arr = []  # Amplitude data
            phase_arr = []  # Phase data
            for i, _ in enumerate(x_arr):
                child_data = self.experiment_data.child_data(index=i)
                if y_arr is None:
                    y_arr = child_data.x_data
                amp_arr.append(child_data.y_data.get("Amp"))
                phase_arr.append(child_data.y_data.get("Phase"))

            # Calculate max and min for each group of data
            max_vals = [np.max(arr) for arr in amp_arr]
            min_vals = [np.min(arr) for arr in amp_arr]

            # Min-Max normalization for each group of amplitude data
            new_amp_arr = [
                (arr - min_val) / (max_val - min_val)
                for arr, min_val, max_val in zip(amp_arr, min_vals, max_vals)
            ]

            # Draw amplitude pseudo-color map
            self.drawer.draw_color_map(
                x_arr,
                y_arr,
                np.array(amp_arr).T,
                ax_index=1,
                **self.options.pcolormesh_options,
            )

            # Draw phase pseudo-color map
            self.drawer.draw_color_map(
                x_arr,
                y_arr,
                np.array(phase_arr).T,
                ax_index=2,
                **self.options.pcolormesh_options,
            )

            # Draw normalized amplitude pseudo-color map
            self.drawer.draw_color_map(
                x_arr,
                y_arr,
                np.array(new_amp_arr).T,
                ax_index=3,
                **self.options.pcolormesh_options,
            )

            # Mark low power and high power point
            if self.quality.is_pass():
                self.drawer.draw_scatter_point(
                    x_data=[self.results.power.value],
                    y_data=[self.results.fr.value],
                    ax_index=0,
                    marker="*",
                    c="black",
                    s=300,
                )
                self.drawer.draw_scatter_point(
                    x_data=[self.results.hf_power.value],
                    y_data=[self.results.h_fr.value],
                    ax_index=0,
                    marker="*",
                    c="red",
                    s=300,
                )
