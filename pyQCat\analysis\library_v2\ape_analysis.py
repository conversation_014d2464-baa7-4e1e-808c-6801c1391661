# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/14
# __author:       ssfang
"""
APE Analysis.

"""

from typing import List

import numpy as np

from ...structures import Options
from ..algorithms.find_peak import get_peak_point
from ..specification import ParameterRepr
from .oscillation_analysis import OscillationAnalysis


class APEAnalysisV2(OscillationAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.sweep_name = "detune"

        # APE_detune
        options.filter = {"window_length": 5, "polyorder": 3}
        options.fine = False

        options.result_parameters = [ParameterRepr(name="points_0", repr="points_0")]

        options.data_key = ["P0"]
        options.quality_bounds = [0.95, 0.9, 0.8]
        options.prominence_divisor = 4.0
        return options

    def _extract_result(self):
        """Extract the Analysis results from fitted data.

        Args:
            data_key (str): The basis for selecting data.
            quality : Quality of fit outcome.
        """
        data_key = self.experiment_data.metadata.process_meta.get("best_data_key")
        for result in self.results.values():
            result.extra.update({"out_flag": False})

        x = self.experiment_data.x_data
        y = self.experiment_data.y_data[data_key]

        analysis_data = self.analysis_datas[data_key]
        smooth_y = analysis_data.y
        fit_y = analysis_data.fit_data.y_fit

        if self.options.sweep_name == "detune":
            distance = None
            height = np.max(y) / 2
            prominence = (np.max(y) - np.min(y)) / self.options.prominence_divisor
            points_0 = [
                p for p in get_peak_point(x, y, distance, height, prominence=prominence)
            ]
            fit_points_0 = [
                p
                for p in get_peak_point(
                    x, fit_y, distance, height / 2, prominence=prominence
                )
            ]

            if self.results.points_0:
                self.results.points_0.value = APEPoints(points_0)

            if self.results.fit_points_0:
                self.results.fit_points_0.value = APEPoints(fit_points_0)

            if self.options.is_plot is True:
                if self.options.fine:
                    points = fit_points_0
                else:
                    points = points_0

                if len(points) > 1:
                    pos_list = [
                        (round(point.x, 4), round(point.y, 3)) for point in points
                    ]
                else:
                    pos_list = [
                        (round(point.x, 4), round(point.y, 3)) for point in points
                    ]

                rp_list = [f"X\n{pos}" for pos in pos_list]
                self.drawer.set_options(
                    text_pos=pos_list, text_rp=rp_list, text_key=[data_key]
                )
        elif self.options.sweep_name == "phase":
            target_index = np.argmin(fit_y)
            phase_point = x[target_index]
            self.results.phase_point.value = phase_point

            if self.options.is_plot is True:
                pos = (phase_point, round(fit_y[target_index], 4))
                self.drawer.set_options(
                    text_pos=[pos], text_rp=[f"X\n{pos}"], text_key=[data_key]
                )


class APEPoints:
    def __init__(self, points: List):
        self._points = points

    def __str__(self):
        return f"detune peaks: {str([point.x for point in self._points])}"

    @property
    def points(self):
        return self._points
