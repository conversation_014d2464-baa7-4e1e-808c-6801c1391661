# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.experiments.composite.rst:2
msgid "pyQCat.experiments.composite package"
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:5
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:8
msgid "pyQCat.experiments.composite.ac\\_crosstalk module"
msgstr ""

#: of pyQCat.experiments.composite.ac_crosstalk:1
msgid "AC crosstalk experiment."
msgstr ""

#: of pyQCat.experiments.composite.ac_crosstalk.ACCrosstalk:1
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk:1
msgid "Bases: :py:class:`~pyQCat.experiments.composite.crosstalk.Crosstalk`"
msgstr ""

#: of pyQCat.experiments.composite.ac_crosstalk.ACCrosstalk:1
#: pyQCat.experiments.composite:28:<autosummary>:1
msgid "AC crosstalk experiment to get the ac crosstalk matrix elements."
msgstr ""

#: of pyQCat.experiments.composite.ac_crosstalk.ACCrosstalk.run:1
msgid "Run AC crosstalk experiment and get the crosstalk coefficient."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:16
msgid "pyQCat.experiments.composite.ac\\_spectrum module"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum:1
msgid "AC Spectrum experiment."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum:1
#: pyQCat.experiments.composite.ape_composite.APEComposite:1
#: pyQCat.experiments.composite.crosstalk.Crosstalk:1
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec:1
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite:1
#: pyQCat.experiments.composite.process_tomography.ProcessTomography:1
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite:1
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate:1
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite:1
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum:1
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum:1
#: pyQCat.experiments.composite:28:<autosummary>:1
msgid "AC Spectrum experiment to get the relationship of qubit frequency-zamp."
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_analysis_options:1
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_analysis_options:1
msgid "Default analysis options for AC spectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_analysis_options
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_run_options
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._guess_next_freq
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._metadata
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_result
#: pyQCat.experiments.composite.ape_composite.APEComposite._default_analysis_options
#: pyQCat.experiments.composite.ape_composite.APEComposite._default_experiment_options
#: pyQCat.experiments.composite.ape_composite.APEComposite._metadata
#: pyQCat.experiments.composite.crosstalk.Crosstalk._create_child_experiment
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_analysis_options
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_experiment_options
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_run_options
#: pyQCat.experiments.composite.crosstalk.Crosstalk._run_ramsey
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._create_child_experiment
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._default_experiment_options
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._default_run_options
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._run_ramsey
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_analysis_options
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_run_options
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._metadata
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._create_child_experiment
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_analysis_options
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_run_options
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._metadata
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_analysis_options
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_run_options
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._metadata
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_analysis_options
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_run_options
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._metadata
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_analysis_options
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_experiment_options
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_run_options
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._metadata
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_analysis_options
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_run_options
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._metadata
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._default_analysis_options
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._default_experiment_options
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._default_run_options
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._metadata
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_analysis_options
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_experiment_options
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_run_options
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._metadata
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_experiment_options
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._metadata
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_analysis_options
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_experiment_options
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_run_options
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_analysis_options:4
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:14
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_run_options:4
#: pyQCat.experiments.composite.ape_composite.APEComposite._default_analysis_options:4
#: pyQCat.experiments.composite.ape_composite.APEComposite._default_experiment_options:5
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_analysis_options:4
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_experiment_options:16
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_run_options:4
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._default_experiment_options:4
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._default_run_options:4
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_analysis_options:8
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:28
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_run_options:4
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_analysis_options:10
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:27
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_run_options:13
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_analysis_options:4
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:14
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_run_options:11
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_analysis_options:8
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:25
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_run_options:4
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_analysis_options:8
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_experiment_options:9
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_run_options:4
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_analysis_options:4
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:9
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_run_options:4
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._default_analysis_options:4
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._default_experiment_options:9
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._default_run_options:4
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_analysis_options:11
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_experiment_options:9
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_run_options:4
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:13
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_experiment_options:11
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:12
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_analysis_options:9
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_experiment_options:24
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_run_options:15
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:1
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:1
msgid "Default experiment options for AC spectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:12
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_experiment_options:14
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:26
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:25
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:12
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:23
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_experiment_options:7
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:7
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._default_experiment_options:7
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_experiment_options:7
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_experiment_options:9
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_experiment_options:22
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:4
msgid ""
"init_fringe (float): The initialize value of fringe. delays (Union[List, "
"np.ndarray]): Delay time scanned when performing Ramsey"
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:6
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_experiment_options:5
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:6
msgid "experiments."
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:7
msgid ""
"z_amp_list (Union[List, np.ndarray]): Z line amplitude (flux pulse) sweep"
" list. freq_bound (Optional[float], optional): Experiment will be stopped"
" when qubit's"
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:9
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:9
msgid "frequency delta value lower than this value. Defaults to 800MHz."
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_experiment_options:11
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:11
msgid ""
"osc_freq_limit (Optional[float], optional): [description]. Defaults to "
"2.5."
msgstr ""

#: of
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._default_run_options:1
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_run_options:1
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._default_run_options:1
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_run_options:1
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_run_options:1
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_run_options:1
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_run_options:1
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._default_run_options:1
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_run_options:1
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:1
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_run_options:1
msgid "Default options values for the experiment :meth:`run` method."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._metadata:1
#: pyQCat.experiments.composite.ape_composite.APEComposite._metadata:1
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._metadata:1
msgid "Return experiment metadata for ExperimentData."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._metadata:5
#: pyQCat.experiments.composite.ape_composite.APEComposite._metadata:7
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._metadata:4
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._metadata:4
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._metadata:4
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._metadata:4
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._metadata:4
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._metadata:4
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._metadata:4
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._metadata:4
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._metadata:4
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum.run:1
msgid ""
"The first three points are used for quadratic function fitting to predict"
" the bit frequency value f_guess at the current z_amp. By running this "
"experiment, you can get the relationship between flux pulse's amplitude "
"and qubit's frequency."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe:1
msgid "Update fringe frequency."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_osc_freq
#: pyQCat.experiments.composite.crosstalk.Crosstalk._get_v_bias_list
#: pyQCat.experiments.composite.crosstalk.Crosstalk._run_ramsey
#: pyQCat.experiments.composite.crosstalk.Crosstalk._validate_voltage_bounds
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._validate_freq
msgid "Parameters"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe:4
msgid "Ramsey index number."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe:7
msgid "IF."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe:10
msgid "Qubit frenquency get from Ramsey exp."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe:13
msgid "Z line amp at now."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe
#: pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_result
#: pyQCat.experiments.composite.crosstalk.Crosstalk._create_child_experiment
#: pyQCat.experiments.composite.crosstalk.Crosstalk._run_ramsey
#: pyQCat.experiments.composite.crosstalk.Crosstalk._validate_voltage_bounds
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._create_child_experiment
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._validate_freq
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._create_child_experiment
msgid "Returns"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._update_fringe:16
msgid "Fringe frequency for the next Ramsey experiment."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._guess_next_freq:1
msgid "Predict next drive frenquency."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._guess_next_freq:4
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._run_ramsey:4
msgid ":py:class:`float`"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_osc_freq:1
msgid "Validate oscillation frequency."
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_osc_freq:4
msgid "The index of the"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_result:1
msgid ""
"Truncation condition of AC/DC Spectrum experiment. :type fd: "
":py:class:`float` :param fd: The value of qubit's drive frequency. :type "
"fd: float :type f10: :py:class:`float` :param f10: The value of qubit's "
"real frequency. :type f10: float"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_result:9
msgid ":py:class:`bool`"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._validate_result:10
msgid ""
"bool variable. True represent validate successfully and False represent "
"validate failed!"
msgstr ""

#: of pyQCat.experiments.composite.ac_spectrum.ACSpectrum._abnormal_diagnose:1
msgid "Diagnose experiment failed possible reason."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:24
msgid "pyQCat.experiments.composite.ape\\_composite module"
msgstr ""

#: of pyQCat.experiments.composite.ape_composite:1
msgid "APE Composite."
msgstr ""

#: of
#: pyQCat.experiments.composite.ape_composite.APEComposite._default_experiment_options:1
msgid "Default kwarg options for experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.ape_composite.APEComposite._default_analysis_options:1
msgid "Default kwarg options for analysis."
msgstr ""

#: of pyQCat.experiments.composite.ape_composite.APEComposite._check_options:1
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._check_options:1
msgid "Check Options."
msgstr ""

#: of pyQCat.experiments.composite.ape_composite.APEComposite._metadata:3
msgid ""
"Subclasses can override this method to add custom experiment metadata to "
"the returned experiment result data."
msgstr ""

#: of pyQCat.experiments.composite.ape_composite.APEComposite.run:1
#: pyQCat.experiments.composite.process_tomography.ProcessTomography.run:1
msgid "Run composite experiment."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:32
msgid "pyQCat.experiments.composite.crosstalk module"
msgstr ""

#: of pyQCat.experiments.composite.crosstalk:1
msgid "Crosstalk experiment."
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk:1
msgid "Crosstalk experiment abstract class."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._create_child_experiment:1
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._create_child_experiment:1
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._create_child_experiment:1
msgid "Create a child experiment object."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._create_child_experiment:3
msgid ":py:class:`~pyQCat.experiments.single.ramsey.RamseyCrosstalk`"
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._create_child_experiment:4
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._create_child_experiment:4
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._create_child_experiment:4
msgid "BaseExperiment object."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_analysis_options:1
msgid "Default analysis options for crosstalk experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_experiment_options:1
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._default_experiment_options:1
msgid "Default experiment options for crosstalk experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_experiment_options:4
msgid ""
"delays (Union[List, np.ndarray]): Delay time scanned when performing "
"Ramsey"
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_experiment_options:6
msgid ""
"init_fringe (float): The initialize value of fringe. spectrum_params "
"(list, np.ndarray): The fitted parameters of AC/DC spectrum. init_v_bias "
"(float): The initialize value of bias voltage. v_bias_bound (float): The "
"bias voltage value range of bias qubit. freq_offset (float): The offset "
"value of qubit at sweet spot deviates"
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_experiment_options:11
msgid "from target qubit."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._default_experiment_options:12
msgid ""
"freq_threshold (tuple): The frequency range of target qubit. scan_points "
"(int): The bias voltage points to be scanned."
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk._get_qubits:1
msgid "Gets the target qubit and the bias qubit."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._get_runtime_parameters:1
msgid "Get the crosstalk experiment run time parameters"
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk._get_v_bias_list:1
msgid "Get the bias voltage list."
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk._get_v_bias_list:4
#: pyQCat.experiments.composite.crosstalk.Crosstalk._run_ramsey:7
msgid "The voltage which was applied to target qubit."
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk._get_v_bias_list:7
#: pyQCat.experiments.composite.crosstalk.Crosstalk._run_ramsey:10
msgid ""
"Ramsey oscillation frequency which is used to amplify the difference "
"between the drive frequency and the qubit real frequency."
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk._get_v_bias_list:11
#: pyQCat.experiments.composite.crosstalk.Crosstalk._run_ramsey:14
msgid "The drive frequency of qubit."
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk._get_v_bias_list:14
msgid "The real frequency of qubit."
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk._get_v_bias_list:17
msgid "The intermediate frequency."
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk._get_v_bias_list:20
msgid "The Z flux pulse amplitude which was applied to target qubit."
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk._run_ramsey:1
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._run_ramsey:1
msgid ""
"Execute ramsey experiment to get the actual voltage applied to target "
"qubit."
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk._run_ramsey:4
msgid "The voltage which was applied to bias qubit."
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk._run_ramsey:17
msgid "The intermediate frequency of pulse."
msgstr ""

#: of pyQCat.experiments.composite.crosstalk.Crosstalk._run_ramsey:20
msgid "The real voltage on target qubit."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._execute_child_experiment:1
msgid "Run child ramsey experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._get_child_experiment_result:1
msgid "Get child Ramsey experiment results."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._validate_voltage_bounds:1
msgid "Validate the bias voltage boundary."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._validate_voltage_bounds:3
msgid ""
"Use ac/dc spectrum fitted parameters to calculate the theoretical voltage"
" boundary value. With the Target qubit frequency varying [+30, -20]MHz, "
"calculate the required bias qubit voltage range (v_min~v_max), if bias "
"qubit voltage is over 0.7V or less than -0.7V, take +-0.7."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._validate_voltage_bounds:9
msgid "The voltage of the target qubit by calculated."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._validate_voltage_bounds:12
msgid "The qubit frequency."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._validate_voltage_bounds:15
msgid "The voltage of the target qubit by experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.crosstalk.Crosstalk._validate_voltage_bounds:18
msgid "The boundary value of the bias voltage."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:40
msgid "pyQCat.experiments.composite.dc\\_crosstalk module"
msgstr ""

#: of pyQCat.experiments.composite.dc_crosstalk:1
msgid "DC crosstalk experiment."
msgstr ""

#: of pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk:1
#: pyQCat.experiments.composite:28:<autosummary>:1
msgid "DC crosstalk experiment to get the dc crosstalk matrix elements."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._create_child_experiment:3
msgid ":py:class:`~pyQCat.experiments.single.ramsey.Ramsey`"
msgstr ""

#: of pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk.run:1
msgid "Run DC crosstalk experiment and get the crosstalk coefficient."
msgstr ""

#: of pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk.run:3
msgid ""
"Unlike the logic of AC crosstalk dynamically adjusting fringe, the Ramsey"
" experiment for DC crosstalk dynamically adjusts the frequency of qubit "
"while maintaining the oscillation frequency at a preset value."
msgstr ""

#: of pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._validate_freq:1
msgid "Validate the qubit drive frequency."
msgstr ""

#: of pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._validate_freq:3
msgid ""
"Adjusting the driving frequency will lead to the deviation of the static "
"working point of the qubit, resulting in the failure of the pulse "
"resonance to drive the qubit, and also cause the change of the reading "
"criterion. However, within a certain range, no influence will be caused, "
"so the experiment can continue."
msgstr ""

#: of pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._validate_freq:10
msgid "The qubit drive frequency."
msgstr ""

#: of pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._validate_freq:13
msgid "The qubit initialized drive frequency."
msgstr ""

#: of pyQCat.experiments.composite.dc_crosstalk.DCCrosstalk._validate_freq:16
msgid "True of False."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:48
msgid "pyQCat.experiments.composite.dc\\_spectrum\\_spec module"
msgstr ""

#: of pyQCat.experiments.composite.dc_spectrum_spec:1
msgid "DC Spectrum composite experiment, base on QubitSpectrum."
msgstr ""

#: of pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec:1
#: pyQCat.experiments.composite:28:<autosummary>:1
msgid "DC Spectrum Experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:1
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:1
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:1
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_experiment_options:1
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:1
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._default_experiment_options:1
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_experiment_options:1
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_experiment_options:1
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:4
msgid "dc_list (List, np.ndarray): Scan Z line dc list."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:6
msgid "rough_freq_list (List): Rough scan qubit frequency."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:8
msgid "rough_threshold (List): Set rough scan qubit frequency"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:9
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:18
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:13
msgid "upper and lower threshold."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:10
msgid "rough_step (float): Rough scan step value."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:12
msgid "use_square (True): Qubit Spectrum rough qubit_test use pulse model."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:13
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:8
msgid "True use square, False use chirp"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:15
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:10
msgid "band_width (float): When use_square is False,"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:15
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:10
msgid "set chirp pulse band width."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:17
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:12
msgid "fine_threshold (List): Set fine scan qubit frequency"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:19
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:14
msgid "fine_step (float): Fine scan step value."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:21
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:16
msgid "rabi_widths (List, array): Rabi scan widths."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_experiment_options:23
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:18
msgid ""
"f02_bounds (List): Difference of f01, f02 range limit. freq_bounds "
"(List): Frequency range limit."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_analysis_options:1
msgid "Default analysis options for DCSpectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_analysis_options:6
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_analysis_options:8
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_run_options:4
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_run_options:9
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_analysis_options:6
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_analysis_options:6
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_analysis_options:9
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:11
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:10
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_analysis_options:7
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_run_options:13
msgid "Options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_analysis_options:6
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_analysis_options:6
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:5
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_analysis_options:7
msgid "freq_list (List, np.ndarray): The frequency calculate"
msgstr ""

#: of
#: pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._default_analysis_options:5
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_analysis_options:5
msgid "by ac spectrum paras and z amp."
msgstr ""

#: of pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec._metadata:1
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._metadata:1
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._metadata:1
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._metadata:1
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._metadata:1
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._metadata:1
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._metadata:1
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._metadata:1
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._metadata:1
msgid "Set metadata."
msgstr ""

#: of pyQCat.experiments.composite.dc_spectrum_spec.DCSpectrumSpec.run:1
msgid "Run DC Spectrum Composite Experiment."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:56
msgid "pyQCat.experiments.composite.distortion\\_t1\\_composite module"
msgstr ""

#: of pyQCat.experiments.composite.distortion_t1_composite:1
msgid "Distortion T1 composite experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite:1
#: pyQCat.experiments.composite:28:<autosummary>:1
msgid "Distortion node."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:4
msgid ""
"iteration_times (int): Set experiment iteration times. xy_delay_max "
"(float): XY line max align delay value,"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:6
msgid "recommend less than tb."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:7
msgid ""
"sample_rate (float): Sample rate. init_step (float): Initial update xy "
"delay step,"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:9
msgid "must be multiple of sample period."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:10
msgid "delay_watershed (float): When xy delay more than watershed,"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:11
msgid "will scan points which set."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:12
msgid "scan_points (int): Scan point number."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:14
msgid ""
"z_amp (float): The const z_amp of Z line. gauss_sigma (float): The sigma "
"of GaussUP, GaussDown wave. gauss_width (float): The width of GaussUP, "
"GaussDown wave. const_width (float): The width of Constant wave. ta "
"(float): Set a width of Constant wave. tb (float): Set a width of "
"Constant wave. z_offset_list (List, np.ndarray): Scan Z offset range."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_experiment_options:22
msgid ""
"bit_type(str): bit type, normal `Qubit` or `Coupler`. base_history "
"(bool): Base on history distortion data calibrate."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_analysis_options:1
msgid "Default analysis options for DistortionT1Composite experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_analysis_options:4
msgid ""
"iteration_time (int): Iteration time number. dt_list (List[np.ndarray]): "
"so_list (List[np.ndarray]):"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_run_options:1
msgid ""
"Default options values for the experiment :meth:`run` method. Statistics "
"and saving parameters of running process."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._default_run_options:6
msgid ""
"distortion_width (float): Calibrate z line distortion width. "
"distortion_ab (List): IIR filter parameters. [ a, b ] delay_arr "
"(np.ndarray): Distortion data delay array. response_arr (np.ndarray): "
"Distortion data response array."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._create_child_experiment:3
msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._save_curve_analysis_plot:1
msgid "Save CurveAnalysis plot figure."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._adjust_xy_delay:1
msgid "According to the current xy delay, update changing step of xy delay."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite._adjust_z_offset_list:1
msgid "Adjust z_offset_list."
msgstr ""

#: of
#: pyQCat.experiments.composite.distortion_t1_composite.DistortionT1Composite.run:1
msgid "Distortion T1 Composite Run Logic."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:64
msgid "pyQCat.experiments.composite.process\\_tomography module"
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:4
msgid ""
"init_fringe (float): The init value of fringe. delays (Union[List, "
"np.ndarray]): Delay time scanned when performing Ramsey"
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_experiment_options:7
msgid ""
"z_amp_list (Union[List, np.ndarray]): Z line amplitude (flux pulse) sweep"
" list. freq_bound (Optional[float], optional): Experiment will be stopped"
" when qubit"
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_run_options:1
msgid "Set run experiment operate options."
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_run_options:4
msgid ""
"QL: Qubit object, which bit name is ql_name. QH: Qubit object, which bit "
"name is qh_name. parking_qubits (List): List of BaseQubit,"
msgstr ""

#: of
#: pyQCat.experiments.composite.process_tomography.ProcessTomography._default_run_options:7
msgid "when name in parking_bits."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:72
msgid "pyQCat.experiments.composite.qubit\\_spectrum\\_com module"
msgstr ""

#: of pyQCat.experiments.composite.qubit_spectrum_com:1
msgid "Qubit Spectrum Composite experiment, base on QubitSpectrum."
msgstr ""

#: of pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite:1
#: pyQCat.experiments.composite:28:<autosummary>:1
msgid "Qubit Spectrum Experiment, find frequency and judge oscillating."
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:4
msgid ""
"rough_freq_list (List): Rough scan qubit frequency. drive_power (float): "
"Set driver power value. z_amp (float): Set Z line pulse amp. use_square "
"(True): Qubit Spectrum rough qubit_test use pulse model."
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_experiment_options:21
msgid "spec_mark (str): Special mark, like dc, drive_power, z_amp."
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite._default_analysis_options:1
msgid "Default analysis options for QubitSpectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.qubit_spectrum_com.QubitSpectrumComposite.run:1
msgid "Run Qubit Spectrum Composite Experiment."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:80
msgid "pyQCat.experiments.composite.readout\\_freq\\_calibrate module"
msgstr ""

#: of pyQCat.experiments.composite.readout_freq_calibrate:1
msgid "Readout frequency calibrate node composite experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate:1
#: pyQCat.experiments.composite:28:<autosummary>:1
msgid "Optimize Readout Frequency."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_experiment_options:4
msgid ""
"fc_list (List, np.ndarray): Scan cavity frequency list. readout_power "
"(float): Set readout channel power."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_analysis_options:1
msgid "Default analysis options for Readout frequency calibrate experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate._default_analysis_options:4
msgid ""
"distance_flag (bool): True means use distance opt probe freq. "
"diff_threshold (float): Twice cavity frequency difference."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_freq_calibrate.ReadoutFreqCalibrate.run:1
msgid ""
"The first XY line pulse amp is 0, the second XY line pulse amp is "
"`qubit.XYwave.Xpi`."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:88
msgid "pyQCat.experiments.composite.readout\\_power\\_calibrate module"
msgstr ""

#: of pyQCat.experiments.composite.readout_power_calibrate:1
msgid "Readout power calibrate node composite experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate:1
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite.single_shot_composite.SingleShotComposite`"
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate:1
#: pyQCat.experiments.composite:28:<autosummary>:1
msgid "Optimize Readout Power of Qubit."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_experiment_options:4
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._default_experiment_options:4
msgid ""
"optimize_field (str): Give target optimize field. sweep_list (List, "
"np.ndarray): Scan optimize field list."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate._default_analysis_options:1
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize._default_analysis_options:1
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_analysis_options:1
msgid "Default analysis options for optimize experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.readout_power_calibrate.ReadoutPowerCalibrate.run:1
#: pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize.run:1
msgid "Run SingleShot Composite Experiment Optimize sample width."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:96
msgid "pyQCat.experiments.composite.sample\\_width\\_optimize module"
msgstr ""

#: of pyQCat.experiments.composite.sample_width_optimize:1
msgid "Sample width optimize composite experiment."
msgstr ""

#: of pyQCat.experiments.composite.sample_width_optimize.SampleWidthOptimize:1
#: pyQCat.experiments.composite:28:<autosummary>:1
msgid "Optimize Sample Width."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:104
msgid "pyQCat.experiments.composite.single\\_shot\\_composite module"
msgstr ""

#: of pyQCat.experiments.composite.single_shot_composite:1
msgid "Create Base SingleShot Composite Optimize One Field of Qubit."
msgstr ""

#: of pyQCat.experiments.composite.single_shot_composite.SingleShotComposite:1
msgid "Optimize One Field of Qubit."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_experiment_options:4
msgid ""
"optimize_field (str): Give the target optimize field. sweep_list (List, "
"np.ndarray): Scan optimize field list."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_analysis_options:4
msgid ""
"fidelity_threshold (List): Threshold of F0, F1. outlier (float): Set "
"outlier threshold value. distance_flag (bool): True means use center-"
"distance"
msgstr ""

#: of
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite._default_analysis_options:7
msgid "to optimize target field."
msgstr ""

#: of
#: pyQCat.experiments.composite.single_shot_composite.SingleShotComposite.run:1
msgid "Run SingleShot Composite Experiment Optimize One Field."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:112
msgid "pyQCat.experiments.composite.t1\\_spectrum module"
msgstr ""

#: of pyQCat.experiments.composite.t1_spectrum:1
msgid "T1 Spectrum composite experiment."
msgstr ""

#: of pyQCat.experiments.composite.t1_spectrum.T1Spectrum:1
#: pyQCat.experiments.composite:28:<autosummary>:1
msgid "T1 Spectrum Experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_experiment_options:4
msgid ""
"z_amp_list (List, np.ndarray): Scan Z line amp list. delay_list (List, "
"np.ndarray): T1 experiment scan delay. ac_spectrum_paras (List): The "
"target qubit ac spectrum"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_experiment_options:7
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_experiment_options:17
msgid "fit parameters, default None."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:1
msgid "Default analysis options for T1Spectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:5
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_analysis_options:5
msgid "by ac spectrum paras and z amp. If no ac spectrum paras, this is None."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:7
msgid "r_square_threshold (float): To extract abnormal points,"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:8
msgid "set threshold of the analysis quality's r_square."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:11
msgid "rate_threshold (float): To extract abnormal points,"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:10
msgid "set threshold of the analysis results' rate."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:4
msgid "tau_list (List): List of t1 value, that the T1 experiment"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:5
msgid "run analysis result."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:10
msgid "freq_list (List, array): The frequency calculate by"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:7
msgid "ac spectrum paras and z amp. If no ac spectrum paras, this is None."
msgstr ""

#: of pyQCat.experiments.composite.t1_spectrum.T1Spectrum.run:1
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum.run:1
msgid "Run T1 Spectrum Composite Experiment."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:120
msgid "pyQCat.experiments.composite.t2\\_spectrum module"
msgstr ""

#: of pyQCat.experiments.composite.t2_spectrum:1
msgid "T2 Spectrum composite experiment."
msgstr ""

#: of pyQCat.experiments.composite.t2_spectrum.T2Spectrum:1
#: pyQCat.experiments.composite:28:<autosummary>:1
msgid "T2 Spectrum Experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_experiment_options:4
msgid ""
"delays (List, np.ndarray): T1 experiment scan delay. fringe (float): A "
"frequency shift in Hz that will be applied"
msgstr ""

#: of
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_experiment_options:6
msgid ""
"by means of a virtual Z rotation to increase the frequency of the "
"measured oscillation."
msgstr ""

#: of
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_experiment_options:8
msgid ""
"rate_threshold (Tuple): Set threshold of rate, optimize qubit_test T2. "
"max_loops (int): T2Ramsey optimize qubit_test T2 times."
msgstr ""

#: of
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_experiment_options:11
msgid ""
"freq_list (List, np.ndarray): Scan bit frequency list. reduce_freq_range "
"(float): Lower bit frequency range. reduce_step (float): Calculate "
"frequency list step."
msgstr ""

#: of
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_experiment_options:15
msgid ""
"z_amp_list (List, np.ndarray): Scan Z line amp list. ac_spectrum_paras "
"(List): The target qubit ac spectrum"
msgstr ""

#: of
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_experiment_options:22
msgid "pattern_flag (bool): Select run t2 spectrum model,"
msgstr ""

#: of
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_experiment_options:19
msgid "False means like T1 Spectrum, True means precise qubit_test T2."
msgstr ""

#: of
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_analysis_options:1
msgid "Default analysis options for T2Spectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_run_options:4
msgid ""
"z_amp_list (List): List of z_amp, run T2Ramsey get target value. tau_list"
" (List): List of t2 value, scan z_amp_list run T2Ramsey. freq_list "
"(array): The frequency calculate"
msgstr ""

#: of
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._default_run_options:7
msgid ""
"by ac spectrum paras and z amp. If no ac spectrum paras, this is None. "
"The run_options.freq_list not necessarily with "
"experiment_options.freq_list, it is possible to calculate z_amp through "
"frequency and ac spectrum paras, if the z_amp is nan, that will remove "
"the frequency."
msgstr ""

#: of
#: pyQCat.experiments.composite.t2_spectrum.T2Spectrum._calculate_z_amp_list:1
msgid "Calculate z_amp_list by freq_list or reduce_freq_range, reduce_step."
msgstr ""

#: ../../source/api/pyQCat.experiments.composite.rst:128
msgid "Module contents"
msgstr ""

#: of pyQCat.experiments.composite:3
msgid "Composite Experiment Library (:mod:`pyQCat.experiments.composite`)"
msgstr ""

#: of pyQCat.experiments.composite:6
msgid "Composite Experiment Classes"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`ACCrosstalk <pyQCat.experiments.composite.ACCrosstalk>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`ACSpectrum <pyQCat.experiments.composite.ACSpectrum>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`APEComposite <pyQCat.experiments.composite.APEComposite>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`ConditionalPhase "
"<pyQCat.experiments.composite.ConditionalPhase>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid "Standard ConditionalPhase experiment."
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`DCCrosstalk <pyQCat.experiments.composite.DCCrosstalk>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`DCSpectrumSpec <pyQCat.experiments.composite.DCSpectrumSpec>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`DistortionT1Composite "
"<pyQCat.experiments.composite.DistortionT1Composite>`\\ \\(inst\\, "
"qubits\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`QubitSpectrumComposite "
"<pyQCat.experiments.composite.QubitSpectrumComposite>`\\ \\(inst\\, "
"qubits\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`ReadoutFreqCalibrate "
"<pyQCat.experiments.composite.ReadoutFreqCalibrate>`\\ \\(inst\\, "
"qubits\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`ReadoutPowerCalibrate "
"<pyQCat.experiments.composite.ReadoutPowerCalibrate>`\\ \\(inst\\, "
"qubits\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`SampleWidthOptimize "
"<pyQCat.experiments.composite.SampleWidthOptimize>`\\ \\(inst\\, "
"qubits\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`SingleQubitPhase "
"<pyQCat.experiments.composite.SingleQubitPhase>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid "SingleQubitPhase experiment."
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`Swap <pyQCat.experiments.composite.Swap>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, compensates\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid "Swap experiment."
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`T1Spectrum <pyQCat.experiments.composite.T1Spectrum>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`T2Spectrum <pyQCat.experiments.composite.T2Spectrum>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`QubitFreqCalibration "
"<pyQCat.experiments.composite.QubitFreqCalibration>`\\ \\(inst\\, "
"qubits\\[\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`DetuneCalibration "
"<pyQCat.experiments.composite.DetuneCalibration>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: of pyQCat.experiments.composite:28:<autosummary>:1
msgid ""
":py:obj:`ProcessTomography "
"<pyQCat.experiments.composite.ProcessTomography>`\\ \\(inst\\, "
"qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.experiments.composite.crosstalk.Crosstalk`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.composite.single_shot_composite.SingleShotComposite`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`ACCrosstalk "
#~ "<pyQCat.experiments.composite.ACCrosstalk>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`ACSpectrum <pyQCat.experiments.composite.ACSpectrum>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`APEComposite "
#~ "<pyQCat.experiments.composite.APEComposite>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`DCCrosstalk "
#~ "<pyQCat.experiments.composite.DCCrosstalk>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`DCSpectrumSpec "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`DistortionT1Composite "
#~ "<pyQCat.experiments.composite.DistortionT1Composite>`\\ "
#~ "\\(inst\\, qubits\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`ProcessTomography "
#~ "<pyQCat.experiments.composite.ProcessTomography>`\\ \\(cls\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`QubitSpectrumComposite "
#~ "<pyQCat.experiments.composite.QubitSpectrumComposite>`\\ "
#~ "\\(inst\\, qubits\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`ReadoutFreqCalibrate "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate>`\\ "
#~ "\\(inst\\, qubits\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`ReadoutPowerCalibrate "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate>`\\ "
#~ "\\(inst\\, qubits\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`SampleWidthOptimize "
#~ "<pyQCat.experiments.composite.SampleWidthOptimize>`\\ \\(inst\\,"
#~ " qubits\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`T1Spectrum <pyQCat.experiments.composite.T1Spectrum>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`T2Spectrum <pyQCat.experiments.composite.T2Spectrum>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.experiments.composite.crosstalk.Crosstalk`"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid "APE node, rough scan detune and fine scan detune."
#~ msgstr ""

#~ msgid ""
#~ "detune_list (Union[List, np.ndarray]):  rough "
#~ "scan detune range. rough_n_list (List[int]):"
#~ " when rough scan, N list, normal "
#~ "`[ 7, 9, 13 ]`. fine_n_list "
#~ "List[int]: when fine scan, N list, "
#~ "normal `[ 7, 11 ]` theta_type "
#~ "(str): run ape theta type, \"Xpi\" "
#~ "or \"Xpi/2\". fine_precision (float): "
#~ "precision of fine scan detune. scan_type"
#~ " (str): Normal 'rough' or 'fine'."
#~ msgstr ""

#~ msgid "diff_threshold (float): Twice fine scan results difference."
#~ msgstr ""

#~ msgid "Execute multiple time child experiment."
#~ msgstr ""

#~ msgid ""
#~ "The rough scan detune get a rough"
#~ " detune value, the fine scan detune"
#~ " improve accuracy."
#~ msgstr ""

#~ msgid "Run crosstalk experiment and get the crosstalk coefficient."
#~ msgstr ""

#~ msgid ""
#~ "Excute ramsey experiment to get the "
#~ "actual voltage applied to target qubit."
#~ msgstr ""

#~ msgid "True of Flase."
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.composite.single_shot_composite.SingleShotComposite`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`ACCrosstalk <pyQCat.experiments.composite.ACCrosstalk>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`ACSpectrum <pyQCat.experiments.composite.ACSpectrum>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`APEComposite "
#~ "<pyQCat.experiments.composite.APEComposite>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`DCCrosstalk <pyQCat.experiments.composite.DCCrosstalk>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`DCSpectrumSpec "
#~ "<pyQCat.experiments.composite.DCSpectrumSpec>`\\ \\(inst\\, "
#~ "qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`DistortionT1Composite "
#~ "<pyQCat.experiments.composite.DistortionT1Composite>`\\ "
#~ "\\(inst\\, qubits\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`ProcessTomography "
#~ "<pyQCat.experiments.composite.ProcessTomography>`\\ \\(cls\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~typing.Type`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`QubitSpectrumComposite "
#~ "<pyQCat.experiments.composite.QubitSpectrumComposite>`\\ "
#~ "\\(inst\\, qubits\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`ReadoutFreqCalibrate "
#~ "<pyQCat.experiments.composite.ReadoutFreqCalibrate>`\\ "
#~ "\\(inst\\, qubits\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`ReadoutPowerCalibrate "
#~ "<pyQCat.experiments.composite.ReadoutPowerCalibrate>`\\ "
#~ "\\(inst\\, qubits\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`SampleWidthOptimize "
#~ "<pyQCat.experiments.composite.SampleWidthOptimize>`\\ \\(inst\\,"
#~ " qubits\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`T1Spectrum <pyQCat.experiments.composite.T1Spectrum>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`T2Spectrum <pyQCat.experiments.composite.T2Spectrum>`\\"
#~ " \\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

