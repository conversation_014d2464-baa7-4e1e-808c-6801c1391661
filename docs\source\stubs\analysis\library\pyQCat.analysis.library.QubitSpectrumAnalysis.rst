﻿pyQCat.analysis.library.QubitSpectrumAnalysis
=============================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: QubitSpectrumAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~QubitSpectrumAnalysis.__init__
      ~QubitSpectrumAnalysis.from_sub_analysis
      ~QubitSpectrumAnalysis.run_analysis
      ~QubitSpectrumAnalysis.set_options
      ~QubitSpectrumAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~QubitSpectrumAnalysis.analysis_datas
      ~QubitSpectrumAnalysis.data_filter
      ~QubitSpectrumAnalysis.drawer
      ~QubitSpectrumAnalysis.experiment_data
      ~QubitSpectrumAnalysis.has_child
      ~QubitSpectrumAnalysis.options
      ~QubitSpectrumAnalysis.quality
      ~QubitSpectrumAnalysis.results
   
   