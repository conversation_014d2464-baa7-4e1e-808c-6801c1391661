# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/pulse/pyQCat.pulse.PulseCorrection.rst:2
msgid "pyQCat.pulse.PulseCorrection"
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseCorrection:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseCorrection:1
msgid ""
"The PulseCorrection class is used for pulse compensation calibration. Due"
" to the different lengths of the lines, the time for the pulse to reach "
"the device has a certain degree of delay. In addition, there is also the "
"influence of distortion, so pulse compensation calibration must be "
"performed."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseCorrection.__init__:1
#: pyQCat.pulse.base_pulse.PulseCorrection._get_distortion_data:1:<autosummary>:1
msgid "Create a new PulseCorrection."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseCorrection.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseCorrection.__init__:4
msgid "BaseQubit name."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseCorrection.__init__:7
msgid "The distortion data of name."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseCorrection.__init__:10
msgid "The hardware data of name."
msgstr ""

#: ../../source/stubs/pulse/pyQCat.pulse.PulseCorrection.rst:13
msgid "Methods"
msgstr ""

#: of
#: pyQCat.pulse.base_pulse.PulseCorrection._get_distortion_data:1:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.pulse.PulseCorrection.__init__>`\\ "
"\\(name\\[\\, distortion\\_data\\, hardware\\_data\\]\\)"
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseCorrection._get_distortion_data:1
msgid "Get distortion compensate data."
msgstr ""

#: of pyQCat.pulse.base_pulse.PulseCorrection._get_hardware_offset:1
msgid "Get compensate data."
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.pulse.PulseCorrection.__init__>`\\ "
#~ "\\(name\\, hardware\\_dir\\[\\, username\\]\\)"
#~ msgstr ""

#~ msgid "Configuration file path."
#~ msgstr ""

#~ msgid "Belong to some user name."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.pulse.PulseCorrection.__init__>`\\ "
#~ "\\(name\\, hardware\\_dir\\[\\, username\\]\\)"
#~ msgstr ""

#~ msgid "Get compensate data form configuration file."
#~ msgstr ""

#~ msgid "Return type"
#~ msgstr ""

#~ msgid ":py:class:`~typing.Dict`"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.pulse.PulseCorrection.__init__>`\\ "
#~ "\\(name\\[\\, distortion\\_data\\, hardware\\_data\\]\\)"
#~ msgstr ""

