# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON>

import copy

import numpy as np

from ...log import pyqlog
from ...qaio_property import QAIO
from ...qubit import <PERSON><PERSON><PERSON>, <PERSON>ubi<PERSON>, QubitPair
from ...tools.utilities import amp_to_freq, get_bound_ac_spectrum, qarange, validate_ac_spectrum
from ..batch_experiment import BatchExperiment


class BatchSWAP(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.pair_names = None
        options.cz_point_map = None
        options.iter_swap_freq = None
        options.extra_widths = None
        options.swap_flows = [
            "FixedPointCalibration_01_qh",
            "FixedPointCalibration_01_ql",
            "FixedSwapFreqCaliCoupler_01",
            "SwapOnce_01",
        ]

        options.swap_state = "01"
        options.swap_state_map = {}
        options.swap_sweep_count = 4
        options.sweep_step = 30
        options.auto_set_interaction_freq = True
        options.fix_interaction_freq = False
        options.swap_threshold = 500
        options.save_at_pass = True
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.swap_fl_map = {}
        options.swap_fh_map = {}
        return options

    def _count_max_sweep_count(self, pair_collections):
        if self.experiment_options.fix_interaction_freq:
            return 1
        elif self.experiment_options.auto_set_interaction_freq:
            point_map = {}
            sweep_count = 0
            for pair in pair_collections:
                swap_inter_freq = self._get_interection_freq_for_01swap(
                    pair, step=self.experiment_options.sweep_step, threshold=self.experiment_options.swap_threshold
                )
                point_map.update({pair: swap_inter_freq})
                self.experiment_options.swap_point_map = point_map
                sweep_count = self.experiment_options.swap_sweep_count
            return min([sweep_count, max([len(v) for v in point_map.values()])])
        else:
            return max([len(v) for v in self.experiment_options.swap_point_map.values()])

    def _init_coupler_zamp(self, pair_obj):
        qc: Coupler = self.context_manager.chip_data.cache_coupler.get(pair_obj.qc)
        ## 将coupler电压偏置到与它的idle_point同侧同分支的腰部
        idle_point = qc.idle_point
        dc_max = qc.dc_max
        dc_min = qc.dc_min
        mid_point = abs(dc_max - dc_min) / 3 * 2
        zamp = mid_point - abs(idle_point)
        if idle_point != 0:
            zamp = zamp if idle_point > 0 else -zamp
        else:
            zamp = zamp if dc_max < 0 else -zamp
        return zamp

    def _change_work_point(self, i: int, pair_collections: list):
        working_units = []
        point_map = self.experiment_options.swap_point_map
        if not self.experiment_options.fix_interaction_freq:
            for pair in pair_collections:
                if pair in self.experiment_options.swap_state_map.keys():
                    swap_state = self.experiment_options.swap_state_map[pair]
                else:
                    swap_state = self.experiment_options.swap_state

                if len(point_map.get(pair)) > i:
                    working_units.append(pair)
                    freq = float(point_map[pair][i])
                    pair_obj: QubitPair = self.context_manager.chip_data.cache_qubit_pair.get(pair)
                    qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)

                    gate_params = pair_obj.gate_params("swap")
                    zamp = gate_params.get(pair_obj.qc).amp
                    if not zamp:
                        zamp = self._init_coupler_zamp(pair_obj)
                    gate_params.get(pair_obj.qc).amp = zamp

                    if swap_state in ["01", "10"]:
                        gate_params.get(pair_obj.qh).freq = freq
                        gate_params.get(pair_obj.ql).freq = freq
                    elif swap_state == "11":
                        gate_params.get(pair_obj.qh).freq = freq - qh.anharmonicity
                        gate_params.get(pair_obj.ql).freq = freq
                    elif swap_state == "11-02":
                        ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
                        gate_params.get(pair_obj.qh).freq = freq
                        gate_params.get(pair_obj.ql).freq = freq - ql.anharmonicity
                    else:
                        raise NameError(f"swap_sate({swap_state}) error!")

                    pyqlog.log("EXP", f"{pair_obj}: qc zamp= ({zamp})V,{pair_obj} swap {swap_state}, ({freq})MHz")
            return working_units
        else:
            return pair_collections

    def _run_batch(self):
        pass_group = {}
        qubit_bad_group = {}
        for index, pair_collections in enumerate(self.experiment_options.pair_names):
            qubit_bad_group.update({f"{index}": copy.copy(pair_collections)})
            pass_units = self._run_swap_flow(pair_collections)
            pass_group.update({f"{index}": pass_units})
            pyqlog.info(f"passed units in group {index}:{pass_units}")
            if self.experiment_options.save_at_pass:
                self.backend.save_chip_data_to_db(pass_units)
            if pass_units:
                for qubit in pass_units:
                    qubit_bad_group[f"{index}"].remove(qubit)
                pyqlog.info(f"qubit failed units in group {index}:{qubit_bad_group[f'{index}']}")
        pyqlog.info(f"qubit failed units:{qubit_bad_group}")

    def _run_swap_flow(self, pair_collections):
        if self.experiment_options.swap_flows:
            good_units = []
            sweep_count = self._count_max_sweep_count(pair_collections)
            for i in range(sweep_count):
                working_units = self._change_work_point(i, pair_collections)
                if working_units:
                    pass_units = self._run_flow(flows=self.experiment_options.swap_flows, physical_units=working_units)
                    if pass_units:
                        for unit in pass_units:
                            good_units.append(unit)
                            pair_collections.remove(unit)
            return good_units
        else:
            return pair_collections

    def _get_interection_freq_for_cz(self, pair: str, step: float = 10, threshold: float = 200):
        pair_obj: QubitPair = self.context_manager.chip_data.cache_qubit_pair.get(pair)
        qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)
        ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
        fl = ql.drive_freq
        fh = qh.drive_freq
        alpha = qh.anharmonicity

        if ql.tunable:
            ql_params, fl_max = validate_ac_spectrum(ql.ac_spectrum, fl)
        else:
            fl_max = 0
        if qh.tunable:
            qh_params, fh_max = validate_ac_spectrum(qh.ac_spectrum, fh)
        else:
            fh_max = 0

        if fl_max == 0 and fh_max > 0:
            pyqlog.warning(f"{pair_obj} interaction set to fl:{fl}MHz")
            return [fl]
        if fh_max == 0 and fl_max > 0:
            pyqlog.warning(f"{pair_obj} interaction set to fh+alpha:{fh + alpha}MHz")
            return [fh + alpha]

        fl_max = fl_max - 2
        fh_max = fh_max - 2
        fl_min = amp_to_freq(ql, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
        fh_min = amp_to_freq(qh, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
        f_int_min = fl_min
        f_int_max = fl_max

        if fh_min > fl_min - alpha:
            f_int_min = fh_min + alpha
        if fh_max < fl_max - alpha:
            f_int_max = fh_max + alpha

        if abs(f_int_max - fl) > abs(f_int_min - fl):
            inter_freq_list = np.array(qarange(f_int_min, f_int_max, abs(step)))
        else:
            inter_freq_list = np.array(qarange(f_int_max, f_int_min, -abs(step)))
        ql_distance = np.abs(inter_freq_list - fl)
        qh_distance = np.abs(inter_freq_list - alpha - fh)
        distance = ql_distance + qh_distance
        ql_filter = np.argwhere(ql_distance < threshold)
        ql_filter = ql_filter.flatten()
        qh_filter = np.argwhere(qh_distance < threshold)
        qh_filter = qh_filter.flatten()

        legal_index = list(set(ql_filter) & set(qh_filter))
        if legal_index:
            legal_inter_freq = inter_freq_list[legal_index]
            distance_filter = distance[legal_index]
            sort_index = distance_filter.argsort()
            sorted_legal_inter_freq = legal_inter_freq[sort_index]
            pyqlog.info(f"{pair_obj} {len(sorted_legal_inter_freq)} points find")
            return sorted_legal_inter_freq

        elif fl_max > fh_max:
            # 交换qh, ql
            pyqlog.warning(f"{pair}: change ql and qh")
            pair_obj.metadata.std.qh = ql.name
            pair_obj.metadata.std.ql = qh.name
            # self.experiment_options.swap_state_map.update({pair: '11-02'})
            qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)
            ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
            # fl = pair_obj.gate_params('cz').get(pair_obj.ql).freq or ql.drive_freq
            # fh = pair_obj.gate_params('cz').get(pair_obj.qh).freq or qh.drive_freq
            fl0 = pair_obj.gate_params("cz").get(pair_obj.ql).freq
            fh0 = pair_obj.gate_params("cz").get(pair_obj.qh).freq
            fl = ql.drive_freq
            fh = qh.drive_freq
            alpha = qh.anharmonicity

            fl_max = fl_max - 2
            fh_max = fh_max - 2

            fl_min = amp_to_freq(ql, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
            fh_min = amp_to_freq(qh, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
            f_int_min = fl_min
            f_int_max = fl_max

            if fh_min > fl_min - alpha:
                f_int_min = fh_min + alpha
            if fh_max < fl_max - alpha:
                f_int_max = fh_max + alpha

            if abs(f_int_max - fl) > abs(f_int_min - fl):
                inter_freq_list = np.array(qarange(f_int_min, f_int_max, abs(step)))
            else:
                inter_freq_list = np.array(qarange(f_int_max, f_int_min, -abs(step)))
            # inter_freq_list = np.array(qarange(f_int_min, f_int_max, step))
            ql_distance = np.abs(inter_freq_list - fl)
            qh_distance = np.abs(inter_freq_list - alpha - fh)
            distance = ql_distance + qh_distance
            ql_filter = np.argwhere(ql_distance < threshold)
            ql_filter = ql_filter.flatten()
            qh_filter = np.argwhere(qh_distance < threshold)
            qh_filter = qh_filter.flatten()

            if np.any(ql_filter) and np.any(qh_filter):
                legal_index = list(set(ql_filter) & set(qh_filter))
                legal_inter_freq = inter_freq_list[legal_index]
                distance_filter = distance[legal_index]
                sort_index = distance_filter.argsort()
                sorted_legal_inter_freq = legal_inter_freq[sort_index]
                if fl0 and fh0:
                    sorted_legal_inter_freq.tolist().insert(0, fl0)
                pyqlog.info(f"{pair_obj} {len(sorted_legal_inter_freq)} points find")
                return sorted_legal_inter_freq
            else:
                min_threshold = threshold
                while min_threshold < 500:
                    min_threshold += step
                    ql_filter = np.argwhere(ql_distance < min_threshold)
                    ql_filter = ql_filter.flatten()
                    qh_filter = np.argwhere(qh_distance < min_threshold)
                    qh_filter = qh_filter.flatten()
                    if np.any(ql_filter) and np.any(qh_filter):
                        legal_index = list(set(ql_filter) & set(qh_filter))
                        legal_inter_freq = inter_freq_list[legal_index]
                        distance_filter = distance[legal_index]
                        sort_index = distance_filter.argsort()
                        sorted_legal_inter_freq = legal_inter_freq[sort_index]
                        pyqlog.warning(
                            f"Can't not find any interection frequnecy at the given threshold:{threshold}. "
                            f"Find {len(sorted_legal_inter_freq)} points of interection frequency, when change to the larger threshold:{min_threshold}"
                        )
                        return sorted_legal_inter_freq

        else:
            min_threshold = threshold
            while min_threshold < 500:
                min_threshold += step
                ql_filter = np.argwhere(ql_distance < min_threshold)
                ql_filter = ql_filter.flatten()
                qh_filter = np.argwhere(qh_distance < min_threshold)
                qh_filter = qh_filter.flatten()
                if np.any(ql_filter) and np.any(qh_filter):
                    legal_index = list(set(ql_filter) | set(qh_filter))
                    legal_inter_freq = inter_freq_list[legal_index]
                    distance_filter = distance[legal_index]
                    sort_index = distance_filter.argsort()
                    sorted_legal_inter_freq = legal_inter_freq[sort_index]
                    pyqlog.warning(
                        f"{pair_obj} Can't not find any interection frequnecy at the given threshold:{threshold}. "
                        f"Find {len(sorted_legal_inter_freq)} points of interection frequency, when change to the maximum possible threshold:{min_threshold}"
                    )
                    return sorted_legal_inter_freq

            pyqlog.error(f" {pair_obj} can't not find any interection frequnecy")
            return []

    def _get_interection_freq_for_01swap(self, pair: str, step: float = 10, threshold: float = 100):
        pair_obj: QubitPair = self.context_manager.chip_data.cache_qubit_pair.get(pair)
        qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)
        ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
        self.run_options.swap_fl_map.update({pair: pair_obj.gate_params("swap").get(pair_obj.ql).freq})
        self.run_options.swap_fh_map.update({pair: pair_obj.gate_params("swap").get(pair_obj.qh).freq})
        fl = ql.drive_freq
        fh = qh.drive_freq
        if ql.tunable:
            ql_params, fl_max = validate_ac_spectrum(ql.ac_spectrum, fl)
        else:
            fl_max = 0
        if qh.tunable:
            qh_params, fh_max = validate_ac_spectrum(qh.ac_spectrum, fh)
        else:
            fh_max = 0
        if fl_max == 0 and fh_max > 0:
            fh_min = amp_to_freq(qh, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
            if fh_min > fl or fh_max < fl:
                pyqlog.warning(f"{pair}: swap state change to 11")
                self.experiment_options.swap_state_map.update({pair: "11"})
                return self._get_interection_freq_for_cz(pair, step, threshold)
            else:
                pyqlog.warning(f"{pair_obj} interaction set to fl:{fl}MHz")
                return [fl]
        if fh_max == 0 and fl_max > 0:
            fl_min = amp_to_freq(qh, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
            if fl_min > fh or fl_max < fh:
                pyqlog.warning(f"{pair}: swap state change to 11")
                self.experiment_options.swap_state_map.update({pair: "11"})
                return self._get_interection_freq_for_cz(pair, step, threshold)
            else:
                pyqlog.warning(f"{pair_obj} interaction set to ff:{fh}MHz")
                return [fh]

        fl_max = fl_max - 2
        fh_max = fh_max - 2
        fl_min = amp_to_freq(ql, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
        fh_min = amp_to_freq(qh, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
        f_int_min = max([fl_min, fh_min])
        f_int_max = min([fl_max, fh_max])

        if f_int_max < f_int_min:
            pyqlog.warning(f"{pair}: swap state change to 11")
            self.experiment_options.swap_state_map.update({pair: "11"})
            return self._get_interection_freq_for_cz(pair, step, threshold)

        inter_freq_list = np.array(qarange(f_int_min, f_int_max, step))
        ql_distance = np.abs(inter_freq_list - fl)
        qh_distance = np.abs(inter_freq_list - fh)
        distance = ql_distance + qh_distance
        ql_filter = np.argwhere(ql_distance < threshold)
        ql_filter = ql_filter.flatten()
        qh_filter = np.argwhere(qh_distance < threshold)
        qh_filter = qh_filter.flatten()

        legal_index = list(set(ql_filter) & set(qh_filter))
        if legal_index:
            legal_inter_freq = inter_freq_list[legal_index]
            distance_filter = distance[legal_index]
            sort_index = distance_filter.argsort()
            sorted_legal_inter_freq = legal_inter_freq[sort_index]
            pyqlog.info(f"{pair_obj} {len(sorted_legal_inter_freq)} points find")
            return sorted_legal_inter_freq
        else:
            max_threshold = threshold
            while max_threshold < 700:
                max_threshold += 10 * step
                ql_filter = np.argwhere(ql_distance < max_threshold)
                ql_filter = ql_filter.flatten()
                qh_filter = np.argwhere(qh_distance < max_threshold)
                qh_filter = qh_filter.flatten()
                legal_index = list(set(ql_filter) & set(qh_filter))
                if legal_index:
                    legal_inter_freq = inter_freq_list[legal_index]
                    distance_filter = distance[legal_index]
                    sort_index = distance_filter.argsort()
                    sorted_legal_inter_freq = legal_inter_freq[sort_index]
                    pyqlog.warning(
                        f"{pair_obj} Can't not find any interection frequnecy at the given threshold:{threshold}. "
                        f"Find {len(sorted_legal_inter_freq)} points of interection frequency, when change to the larger threshold:{max_threshold}"
                    )
                    return sorted_legal_inter_freq
            pyqlog.error(f" {pair_obj} can't not find any interection frequnecy")
            return []


# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       Peng Duan

import copy

import numpy as np

from ...log import pyqlog
from ...qaio_property import QAIO
from ...qubit import Coupler, Qubit, QubitPair
from ...tools.utilities import amp_to_freq, get_bound_ac_spectrum, qarange, validate_ac_spectrum
from ..batch_experiment import BatchExperiment


class BatchSWAP(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.pair_names = None
        options.cz_point_map = None
        options.iter_swap_freq = None
        options.extra_widths = None
        options.swap_flows = [
            "FixedPointCalibration_01_qh",
            "FixedPointCalibration_01_ql",
            "FixedSwapFreqCaliCoupler_01",
            "SwapOnce_01",
        ]

        options.swap_state = "01"
        options.swap_state_map = {}
        options.swap_sweep_count = 4
        options.sweep_step = 30
        options.auto_set_interaction_freq = True
        options.fix_interaction_freq = False
        options.swap_threshold = 500
        options.save_at_pass = True
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.swap_fl_map = {}
        options.swap_fh_map = {}
        return options

    def _count_max_sweep_count(self, pair_collections):
        if self.experiment_options.fix_interaction_freq:
            return 1
        elif self.experiment_options.auto_set_interaction_freq:
            point_map = {}
            sweep_count = 0
            for pair in pair_collections:
                swap_inter_freq = self._get_interection_freq_for_01swap(
                    pair, step=self.experiment_options.sweep_step, threshold=self.experiment_options.swap_threshold
                )
                point_map.update({pair: swap_inter_freq})
                self.experiment_options.swap_point_map = point_map
                sweep_count = self.experiment_options.swap_sweep_count
            return min([sweep_count, max([len(v) for v in point_map.values()])])
        else:
            return max([len(v) for v in self.experiment_options.swap_point_map.values()])

    def _init_coupler_zamp(self, pair_obj):
        qc: Coupler = self.context_manager.chip_data.cache_coupler.get(pair_obj.qc)
        ## 将coupler电压偏置到与它的idle_point同侧同分支的腰部
        idle_point = qc.idle_point
        dc_max = qc.dc_max
        dc_min = qc.dc_min
        mid_point = abs(dc_max - dc_min) / 3 * 2
        zamp = mid_point - abs(idle_point)
        if idle_point != 0:
            zamp = zamp if idle_point > 0 else -zamp
        else:
            zamp = zamp if dc_max < 0 else -zamp
        return zamp

    def _change_work_point(self, i: int, pair_collections: list):
        working_units = []
        point_map = self.experiment_options.swap_point_map
        if not self.experiment_options.fix_interaction_freq:
            for pair in pair_collections:
                if pair in self.experiment_options.swap_state_map.keys():
                    swap_state = self.experiment_options.swap_state_map[pair]
                else:
                    swap_state = self.experiment_options.swap_state

                if len(point_map.get(pair)) > i:
                    working_units.append(pair)
                    freq = float(point_map[pair][i])
                    pair_obj: QubitPair = self.context_manager.chip_data.cache_qubit_pair.get(pair)
                    qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)

                    gate_params = pair_obj.gate_params("swap")
                    zamp = gate_params.get(pair_obj.qc).amp
                    if not zamp:
                        zamp = self._init_coupler_zamp(pair_obj)
                    gate_params.get(pair_obj.qc).amp = zamp

                    if swap_state in ["01", "10"]:
                        gate_params.get(pair_obj.qh).freq = freq
                        gate_params.get(pair_obj.ql).freq = freq
                    elif swap_state == "11":
                        gate_params.get(pair_obj.qh).freq = freq - qh.anharmonicity
                        gate_params.get(pair_obj.ql).freq = freq
                    elif swap_state == "11-02":
                        ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
                        gate_params.get(pair_obj.qh).freq = freq
                        gate_params.get(pair_obj.ql).freq = freq - ql.anharmonicity
                    else:
                        raise NameError(f"swap_sate({swap_state}) error!")

                    pyqlog.log("EXP", f"{pair_obj}: qc zamp= ({zamp})V,{pair_obj} swap {swap_state}, ({freq})MHz")
            return working_units
        else:
            return pair_collections

    def _run_batch(self):
        pass_group = {}
        qubit_bad_group = {}
        for index, pair_collections in enumerate(self.experiment_options.pair_names):
            qubit_bad_group.update({f"{index}": copy.copy(pair_collections)})
            pass_units = self._run_swap_flow(pair_collections)
            pass_group.update({f"{index}": pass_units})
            pyqlog.info(f"passed units in group {index}:{pass_units}")
            if self.experiment_options.save_at_pass:
                self.backend.save_chip_data_to_db(pass_units)
            if pass_units:
                for qubit in pass_units:
                    qubit_bad_group[f"{index}"].remove(qubit)
                pyqlog.info(f"qubit failed units in group {index}:{qubit_bad_group[f'{index}']}")
        pyqlog.info(f"qubit failed units:{qubit_bad_group}")

    def _run_swap_flow(self, pair_collections):
        if self.experiment_options.swap_flows:
            good_units = []
            sweep_count = self._count_max_sweep_count(pair_collections)
            for i in range(sweep_count):
                working_units = self._change_work_point(i, pair_collections)
                if working_units:
                    pass_units = self._run_flow(flows=self.experiment_options.swap_flows, physical_units=working_units)
                    if pass_units:
                        for unit in pass_units:
                            good_units.append(unit)
                            pair_collections.remove(unit)
            return good_units
        else:
            return pair_collections

    def _get_interection_freq_for_cz(self, pair: str, step: float = 10, threshold: float = 200):
        pair_obj: QubitPair = self.context_manager.chip_data.cache_qubit_pair.get(pair)
        qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)
        ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
        fl = ql.drive_freq
        fh = qh.drive_freq
        alpha = qh.anharmonicity

        if ql.tunable:
            ql_params, fl_max = validate_ac_spectrum(ql.ac_spectrum, fl)
        else:
            fl_max = 0
        if qh.tunable:
            qh_params, fh_max = validate_ac_spectrum(qh.ac_spectrum, fh)
        else:
            fh_max = 0

        if fl_max == 0 and fh_max > 0:
            pyqlog.warning(f"{pair_obj} interaction set to fl:{fl}MHz")
            return [fl]
        if fh_max == 0 and fl_max > 0:
            pyqlog.warning(f"{pair_obj} interaction set to fh+alpha:{fh + alpha}MHz")
            return [fh + alpha]

        fl_max = fl_max - 2
        fh_max = fh_max - 2
        fl_min = amp_to_freq(ql, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
        fh_min = amp_to_freq(qh, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
        f_int_min = fl_min
        f_int_max = fl_max

        if fh_min > fl_min - alpha:
            f_int_min = fh_min + alpha
        if fh_max < fl_max - alpha:
            f_int_max = fh_max + alpha

        if abs(f_int_max - fl) > abs(f_int_min - fl):
            inter_freq_list = np.array(qarange(f_int_min, f_int_max, abs(step)))
        else:
            inter_freq_list = np.array(qarange(f_int_max, f_int_min, -abs(step)))
        ql_distance = np.abs(inter_freq_list - fl)
        qh_distance = np.abs(inter_freq_list - alpha - fh)
        distance = ql_distance + qh_distance
        ql_filter = np.argwhere(ql_distance < threshold)
        ql_filter = ql_filter.flatten()
        qh_filter = np.argwhere(qh_distance < threshold)
        qh_filter = qh_filter.flatten()

        legal_index = list(set(ql_filter) & set(qh_filter))
        if legal_index:
            legal_inter_freq = inter_freq_list[legal_index]
            distance_filter = distance[legal_index]
            sort_index = distance_filter.argsort()
            sorted_legal_inter_freq = legal_inter_freq[sort_index]
            pyqlog.info(f"{pair_obj} {len(sorted_legal_inter_freq)} points find")
            return sorted_legal_inter_freq

        elif fl_max > fh_max:
            # 交换qh, ql
            pyqlog.warning(f"{pair}: change ql and qh")
            pair_obj.metadata.std.qh = ql.name
            pair_obj.metadata.std.ql = qh.name
            # self.experiment_options.swap_state_map.update({pair: '11-02'})
            qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)
            ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
            # fl = pair_obj.gate_params('cz').get(pair_obj.ql).freq or ql.drive_freq
            # fh = pair_obj.gate_params('cz').get(pair_obj.qh).freq or qh.drive_freq
            fl0 = pair_obj.gate_params("cz").get(pair_obj.ql).freq
            fh0 = pair_obj.gate_params("cz").get(pair_obj.qh).freq
            fl = ql.drive_freq
            fh = qh.drive_freq
            alpha = qh.anharmonicity

            fl_max = fl_max - 2
            fh_max = fh_max - 2

            fl_min = amp_to_freq(ql, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
            fh_min = amp_to_freq(qh, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
            f_int_min = fl_min
            f_int_max = fl_max

            if fh_min > fl_min - alpha:
                f_int_min = fh_min + alpha
            if fh_max < fl_max - alpha:
                f_int_max = fh_max + alpha

            if abs(f_int_max - fl) > abs(f_int_min - fl):
                inter_freq_list = np.array(qarange(f_int_min, f_int_max, abs(step)))
            else:
                inter_freq_list = np.array(qarange(f_int_max, f_int_min, -abs(step)))
            # inter_freq_list = np.array(qarange(f_int_min, f_int_max, step))
            ql_distance = np.abs(inter_freq_list - fl)
            qh_distance = np.abs(inter_freq_list - alpha - fh)
            distance = ql_distance + qh_distance
            ql_filter = np.argwhere(ql_distance < threshold)
            ql_filter = ql_filter.flatten()
            qh_filter = np.argwhere(qh_distance < threshold)
            qh_filter = qh_filter.flatten()

            if np.any(ql_filter) and np.any(qh_filter):
                legal_index = list(set(ql_filter) & set(qh_filter))
                legal_inter_freq = inter_freq_list[legal_index]
                distance_filter = distance[legal_index]
                sort_index = distance_filter.argsort()
                sorted_legal_inter_freq = legal_inter_freq[sort_index]
                if fl0 and fh0:
                    sorted_legal_inter_freq.tolist().insert(0, fl0)
                pyqlog.info(f"{pair_obj} {len(sorted_legal_inter_freq)} points find")
                return sorted_legal_inter_freq
            else:
                min_threshold = threshold
                while min_threshold < 500:
                    min_threshold += step
                    ql_filter = np.argwhere(ql_distance < min_threshold)
                    ql_filter = ql_filter.flatten()
                    qh_filter = np.argwhere(qh_distance < min_threshold)
                    qh_filter = qh_filter.flatten()
                    if np.any(ql_filter) and np.any(qh_filter):
                        legal_index = list(set(ql_filter) & set(qh_filter))
                        legal_inter_freq = inter_freq_list[legal_index]
                        distance_filter = distance[legal_index]
                        sort_index = distance_filter.argsort()
                        sorted_legal_inter_freq = legal_inter_freq[sort_index]
                        pyqlog.warning(
                            f"Can't not find any interection frequnecy at the given threshold:{threshold}. "
                            f"Find {len(sorted_legal_inter_freq)} points of interection frequency, when change to the larger threshold:{min_threshold}"
                        )
                        return sorted_legal_inter_freq

        else:
            min_threshold = threshold
            while min_threshold < 500:
                min_threshold += step
                ql_filter = np.argwhere(ql_distance < min_threshold)
                ql_filter = ql_filter.flatten()
                qh_filter = np.argwhere(qh_distance < min_threshold)
                qh_filter = qh_filter.flatten()
                if np.any(ql_filter) and np.any(qh_filter):
                    legal_index = list(set(ql_filter) | set(qh_filter))
                    legal_inter_freq = inter_freq_list[legal_index]
                    distance_filter = distance[legal_index]
                    sort_index = distance_filter.argsort()
                    sorted_legal_inter_freq = legal_inter_freq[sort_index]
                    pyqlog.warning(
                        f"{pair_obj} Can't not find any interection frequnecy at the given threshold:{threshold}. "
                        f"Find {len(sorted_legal_inter_freq)} points of interection frequency, when change to the maximum possible threshold:{min_threshold}"
                    )
                    return sorted_legal_inter_freq

            pyqlog.error(f" {pair_obj} can't not find any interection frequnecy")
            return []

    def _get_interection_freq_for_01swap(self, pair: str, step: float = 10, threshold: float = 100):
        pair_obj: QubitPair = self.context_manager.chip_data.cache_qubit_pair.get(pair)
        qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)
        ql: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.ql)
        self.run_options.swap_fl_map.update({pair: pair_obj.gate_params("swap").get(pair_obj.ql).freq})
        self.run_options.swap_fh_map.update({pair: pair_obj.gate_params("swap").get(pair_obj.qh).freq})
        fl = ql.drive_freq
        fh = qh.drive_freq
        if ql.tunable:
            ql_params, fl_max = validate_ac_spectrum(ql.ac_spectrum, fl)
        else:
            fl_max = 0
        if qh.tunable:
            qh_params, fh_max = validate_ac_spectrum(qh.ac_spectrum, fh)
        else:
            fh_max = 0
        if fl_max == 0 and fh_max > 0:
            fh_min = amp_to_freq(qh, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
            if fh_min > fl or fh_max < fl:
                pyqlog.warning(f"{pair}: swap state change to 11")
                self.experiment_options.swap_state_map.update({pair: "11"})
                return self._get_interection_freq_for_cz(pair, step, threshold)
            else:
                pyqlog.warning(f"{pair_obj} interaction set to fl:{fl}MHz")
                return [fl]
        if fh_max == 0 and fl_max > 0:
            fl_min = amp_to_freq(qh, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
            if fl_min > fh or fl_max < fh:
                pyqlog.warning(f"{pair}: swap state change to 11")
                self.experiment_options.swap_state_map.update({pair: "11"})
                return self._get_interection_freq_for_cz(pair, step, threshold)
            else:
                pyqlog.warning(f"{pair_obj} interaction set to ff:{fh}MHz")
                return [fh]

        fl_max = fl_max - 2
        fh_max = fh_max - 2
        fl_min = amp_to_freq(ql, abs(1 / 2 / ql_params[2]), new_offset=0) + 2
        fh_min = amp_to_freq(qh, abs(1 / 2 / qh_params[2]), new_offset=0) + 2
        f_int_min = max([fl_min, fh_min])
        f_int_max = min([fl_max, fh_max])

        if f_int_max < f_int_min:
            pyqlog.warning(f"{pair}: swap state change to 11")
            self.experiment_options.swap_state_map.update({pair: "11"})
            return self._get_interection_freq_for_cz(pair, step, threshold)

        inter_freq_list = np.array(qarange(f_int_min, f_int_max, step))
        ql_distance = np.abs(inter_freq_list - fl)
        qh_distance = np.abs(inter_freq_list - fh)
        distance = ql_distance + qh_distance
        ql_filter = np.argwhere(ql_distance < threshold)
        ql_filter = ql_filter.flatten()
        qh_filter = np.argwhere(qh_distance < threshold)
        qh_filter = qh_filter.flatten()

        legal_index = list(set(ql_filter) & set(qh_filter))
        if legal_index:
            legal_inter_freq = inter_freq_list[legal_index]
            distance_filter = distance[legal_index]
            sort_index = distance_filter.argsort()
            sorted_legal_inter_freq = legal_inter_freq[sort_index]
            pyqlog.info(f"{pair_obj} {len(sorted_legal_inter_freq)} points find")
            return sorted_legal_inter_freq
        else:
            max_threshold = threshold
            while max_threshold < 700:
                max_threshold += 10 * step
                ql_filter = np.argwhere(ql_distance < max_threshold)
                ql_filter = ql_filter.flatten()
                qh_filter = np.argwhere(qh_distance < max_threshold)
                qh_filter = qh_filter.flatten()
                legal_index = list(set(ql_filter) & set(qh_filter))
                if legal_index:
                    legal_inter_freq = inter_freq_list[legal_index]
                    distance_filter = distance[legal_index]
                    sort_index = distance_filter.argsort()
                    sorted_legal_inter_freq = legal_inter_freq[sort_index]
                    pyqlog.warning(
                        f"{pair_obj} Can't not find any interection frequnecy at the given threshold:{threshold}. "
                        f"Find {len(sorted_legal_inter_freq)} points of interection frequency, when change to the larger threshold:{max_threshold}"
                    )
                    return sorted_legal_inter_freq
            pyqlog.error(f" {pair_obj} can't not find any interection frequnecy")
            return []
