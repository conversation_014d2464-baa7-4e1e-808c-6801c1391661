# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/fit/pyQCat.analysis.fit.cos_decay.rst:2
msgid "pyQCat.analysis.fit.cos\\_decay"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.cos_decay:1
msgid "Cosine function with exponential decay."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.cos_decay:3
msgid ""
"y = {\\rm amp} \\cdot e^{-x/\\tau} \\cos\\left(2 \\pi \\cdot {\\rm freq} "
"\\cdot x\n"
"+ {\\rm phase}\\right) + {\\rm baseline}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.cos_decay
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.cos_decay:8
msgid ":py:class:`~numpy.ndarray`"
msgstr ""

