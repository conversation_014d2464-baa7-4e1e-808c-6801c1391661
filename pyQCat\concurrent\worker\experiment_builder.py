# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/19
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy
from dataclasses import dataclass, field
from typing import Callable, Dict, List, Optional, Union

import numpy as np

from pyQCat.structures import INST_SELECT, CommonDict

from ...errors import ExperimentOptionsError, ExperimentPulseModuleError
from ...experiments.base_experiment import BaseQubitsType, UnitMapMixin
from ...instrument import Instrument
from ...log import pyqlog
from ...pulse import (
    CUSTOM_PULSE_MODELS,
    AcquireClear,
    AcquireSine,
    Constant,
    FlatTopGaussianAsymmetric,
    FlatTopGaussianSide,
    PulseComponent,
    pulse_lib,
)
from ...pulse.pulse_function import pi_pulse, zero_pulse
from ...qaio_property import QA<PERSON>
from ...qm_protocol import ExperimentFile
from ...qubit import <PERSON>Q<PERSON>t, <PERSON><PERSON><PERSON>, Qubit, QubitPair
from ...structures import QDict
from ...tools import check_readout_point
from ...types import ExperimentDocStatus, SamplingMode
from .prepare_measure_wrapper import PrepareMeasureWrapperV2 as PrepareMeasureWrapper
from .wrapper import extend_f12_pulse_v2 as extend_f12_pulse
from .wrapper import online_extend


@dataclass
class ExperimentEnvironment:
    label: str
    inst: Dict
    experiment_options: QDict
    run_options: QDict
    set_xy_pulses: Optional[Callable] = None
    set_z_pulses: Optional[Callable] = None
    update_instrument: Optional[Callable] = None
    qubits: List[Qubit] = field(default_factory=list)
    couplers: List[Coupler] = field(default_factory=list)
    qubit_pairs: List[QubitPair] = field(default_factory=list)
    compensate_units: List[BaseQubit] = field(default_factory=list)
    working_dc: Dict = field(default_factory=dict)
    is_coupler_exp: bool = False

    __annotations__ = {
        "label": str,
        "inst": Dict,
        "experiment_options": QDict,
        "run_options": QDict,
        "set_xy_pulses": callable,
        "set_z_pulses": callable,
        "update_instrument": callable,
        "qubits": List[Qubit],
        "couplers": List[Coupler],
        "qubit_pairs": List[QubitPair],
        "compensate_units": List[BaseQubit],
        "working_dc": Dict,
        "is_coupler_exp": bool,
    }


class ExperimentProtocolBuilder(UnitMapMixin):
    def __init__(
        self,
        label: str,
        inst_data: Dict,
        qubits: List[Qubit],
        couplers: Optional[List[Coupler]] = None,
        qubit_pairs: Optional[List[QubitPair]] = None,
        compensate_units: Optional[List[Union[Qubit, Coupler]]] = None,
        experiment_options: Optional[QDict] = None,
        run_options: Optional[QDict] = None,
        is_coupler_exp: Optional[bool] = False,
    ):
        self.label = label
        self.inst = Instrument(72)
        self.inst.load(inst_data)
        self.qubits = qubits
        self.couplers = couplers
        self.qubit_pairs = qubit_pairs
        self.compensate_units = compensate_units
        self.experiment_options = experiment_options
        self.run_options = run_options
        self.is_coupler_exp = is_coupler_exp

        # init experiment program
        self._experiment_file = ExperimentFile()

        # xy/z pulse
        self.z_pulses: Dict[Union[Qubit, Coupler], List] = {qubit: [] for qubit in self.qubits}
        if self.couplers:
            self.z_pulses.update({coupler: [] for coupler in self.couplers})
        if self.compensate_units:
            self.z_pulses.update({unit: [] for unit in self.compensate_units})
        self.xy_pulses: Dict[Qubit, List] = {qubit: [] for qubit in self.qubits}
        self.readout_qubits = []
        self.readout_pulses = []

        # used for auto setting readout delay.
        self._pulse_time_list = []

        # dynamic inject property
        self.inject_property()
        
        # adapter coupler experiment
        if is_coupler_exp is True:
            self.run_options.co = QDict(
                probeQ=self.probeQ,
                driveQ=self.driveQ,
                coupler=self.coupler,
                head_pulse=None,
                tail_pulse=None,
                right_zero_pulse=None,
                add_zero_pulse=None,
            )
            self._init_zero_pulse()
            
        self._execute_unit_names = []
        for qubit in self.qubits:
            self._execute_unit_names.append(qubit.name)
        for coupler in self.couplers:
            self._execute_unit_names.append(coupler.name)

    @property
    def driveQ(self):
        if self.is_coupler_exp is True:
            return self.run_options.co.driveQ

    @property
    def probeQ(self):
        if self.is_coupler_exp is True:
            return self.run_options.co.probeQ

    @property
    def experiment_protocol(self):
        return self._experiment_file

    @property
    def pulse_time_list(self):
        return self._pulse_time_list

    @classmethod
    def from_experiment_environment(cls, env: ExperimentEnvironment):
        return cls(
            label=env.label,
            inst_data=env.inst,
            qubits=env.qubits,
            couplers=env.couplers,
            qubit_pairs=env.qubit_pairs,
            compensate_units=env.compensate_units,
            experiment_options=env.experiment_options,
            run_options=env.run_options,
            is_coupler_exp=env.is_coupler_exp,
        )

    @staticmethod
    def _search_pulse_model(model_name: str) -> Union[PulseComponent, None]:
        """According to pulse_lib and user register pulses,
        search pulse class.
        """
        model_cls = None
        if hasattr(pulse_lib, model_name):
            model_cls = getattr(pulse_lib, model_name)
        else:
            for custom_model in CUSTOM_PULSE_MODELS:
                if model_name == custom_model.__name__:
                    model_cls = custom_model
                    break
        return model_cls

    def set_measure_pulses(self):
        """Set measure pulse."""
        if self.run_options.multiple_measure_options:
            self._tackle_multiple_measure_options()
        # elif self.run_options.acq_pulse:
        #     acq_pulse = self.run_options.acq_pulse
        #     channel = self.qubit.readout_channel
        #     self.readout_pulses.append(acq_pulse)
        #     self.readout_qubits.append(self.qubit)
        #     self.inst.set_custom_waveform("Readout_control", channel, [acq_pulse.pulse])
        #     if self.run_options.measure_modes:
        #         self.inst.set_measure_index(channel, 0, 0)
        #         self.inst.set_measure_width(channel, acq_pulse.width, acq_pulse.width)
        #     else:
        #         self.inst.set_measure_index(channel, 0)
        #         self.inst.set_measure_width(channel, acq_pulse.width)
        #     self.run_options.measure_qubits = [self.qubit]
        elif self.run_options.measure_qubits:
            self._set_union_readout_pulse(self.run_options.measure_qubits)
        elif self.is_coupler_exp is True:
            pyqlog.debug("single qubit set readout parameters")
            self._create_readout_pulse(self.run_options.co.probeQ)
            self._bind_probe_inst(self.run_options.co.probeQ)
            self.run_options.measure_qubits = [self.run_options.co.probeQ]
        else:
            self._set_single_readout_pulse()
            self.run_options.measure_qubits = [self.qubit]

    def compose_xy_pulses(self, exp_pulse_list: Union[List, PulseComponent]):
        """Compose xy line pulses."""
        if isinstance(exp_pulse_list, PulseComponent):
            exp_pulse_list = [exp_pulse_list]

        xy_head_pulse = deepcopy(self.run_options.co.head_pulse)
        xy_head_pulse.type = "XY"

        xy_right_pulse = deepcopy(self.run_options.co.right_zero_pulse)
        xy_right_pulse.type = "XY"

        xy_tail_pulse = deepcopy(self.run_options.co.tail_pulse)
        xy_tail_pulse.type = "XY"

        probe_pi_pulse = pi_pulse(self.run_options.co.probeQ)
        probe_zero_pulse = zero_pulse(self.run_options.co.probeQ)

        self.run_options.exp_pulse_list = exp_pulse_list
        self.run_options.probe_pi_width = probe_pi_pulse.width

        tail_probe_pulse = deepcopy(xy_right_pulse)() + deepcopy(probe_pi_pulse)() + deepcopy(xy_tail_pulse)()
        tail_zero_pulse = deepcopy(xy_right_pulse)() + deepcopy(probe_zero_pulse)() + deepcopy(xy_tail_pulse)()

        drive_xy_pulse_list = []
        probe_xy_pulse_list = []

        for exp_pulse in exp_pulse_list:
            drive_xy_pulse = deepcopy(xy_head_pulse)()
            probe_xy_pulse = deepcopy(xy_head_pulse)()

            exp_zero_pulse = Constant(exp_pulse.width, 0, name="XY")
            exp_zero_pulse()

            drive_xy_pulse = drive_xy_pulse + exp_pulse + deepcopy(tail_zero_pulse)
            probe_xy_pulse = probe_xy_pulse + exp_zero_pulse + deepcopy(tail_probe_pulse)

            drive_xy_pulse_list.append(drive_xy_pulse)
            probe_xy_pulse_list.append(probe_xy_pulse)

        self.play_pulse("XY", self.run_options.co.probeQ, probe_xy_pulse_list)
        self.play_pulse("XY", self.run_options.co.driveQ, drive_xy_pulse_list)

    def compose_z_pulses(self, exp_pulse_list: Union[List, PulseComponent], base_qubit: BaseQubit = None):
        """Compose z line pulses."""
        if exp_pulse_list is None:
            return

        if not isinstance(exp_pulse_list, list):
            exp_pulse_list = [exp_pulse_list]
        if base_qubit is None:
            base_qubit = self.run_options.co.coupler

        z_pulse_list = []
        for exp_pulse in exp_pulse_list:
            if base_qubit == self.run_options.co.coupler:
                z_pulse_list.append(self._compose_coupler_z_pulse(exp_pulse))
            else:
                z_pulse_list.append(self._compose_qubit_z_pulse(exp_pulse))

        self.play_pulse("Z", base_qubit, z_pulse_list)

    def sweep_readout_trigger_delay(self, channel: int, sweep_delay: Union[List, np.ndarray], online: bool = False):
        """Sweep readout trigger delay.

        Args:
            channel (int): Readout channel.
            sweep_delay (List): time list.
            online (bool):
        """
        # feature: record one qubit readout trigger delay
        if self.run_options.readout_trigger_delay is None:
            self.run_options.readout_trigger_delay = deepcopy(sweep_delay)

        # bugfix: sweep trigger can not repeat set
        if self.run_options.online_bits:
            if online is False:
                return

        sweep_delay = np.asarray(sweep_delay)
        raw_delay = self.inst.get_trigger_delay("Readout_control", channel)
        sweep_delay += raw_delay[0]
        sweep_delay += self.experiment_options.ac_prepare_time
        delay_step = QAIO.get_range("trigger_delay")[-1]
        new_sweep_delay = []
        for delay in sweep_delay:
            af_delay = QAIO.delay_ceil(delay, delay_step)
            new_sweep_delay.append(af_delay)

        self.inst.sweep_delay(
            "Readout_control",
            channel,
            points=new_sweep_delay,
            repeat=self.experiment_options.repeat,
        )

    def play_pulse(self, name: str, base_qubit, pulse):
        if name not in ("XY", "Z"):
            raise ExperimentPulseModuleError(self.label, name, ("XY", "Z"))

        if name == "XY" and isinstance(base_qubit, Coupler):
            raise ExperimentPulseModuleError(self.label, name, "Z")

        if isinstance(pulse, List):
            pulses = pulse  # override
            for p in pulses:
                p.type = name
                p.bit = base_qubit.name
                p.envelope2sequence()
                self._pulse_time_list.append(p.width)
        elif isinstance(pulse, PulseComponent):
            pulse.type = name
            pulse.bit = base_qubit.name
            pulse.envelope2sequence()
            pulses = [pulse]
            self._pulse_time_list.append(pulse.width)
        else:
            raise ExperimentPulseModuleError(self.label, type(pulse), [List, PulseComponent])

        if name == "XY":
            self.xy_pulses.update({base_qubit: pulses})
        else:
            self.z_pulses.update({base_qubit: pulses})

    def initial(self):
        self._initialize_experiment()

    def build(self):
        self._register()
        self._prepare_measure_wrapper()

    def _compose_coupler_z_pulse(self, pulse: PulseComponent):
        head_p = deepcopy(self.run_options.co.head_pulse)
        tail_p = deepcopy(self.run_options.coupler_z_pi_pulse)
        return head_p() + pulse + tail_p()

    def _compose_qubit_z_pulse(self, pulse: PulseComponent):
        head_p = deepcopy(self.run_options.co.head_pulse)
        pi_pulse_width = self.run_options.coupler_z_pi_pulse.width
        return head_p() + pulse + Constant(pi_pulse_width, 0)()

    def _reset_drive_power(self):
        for qubit, pulse_list in self.xy_pulses.items():
            need_reset = True
            for com_pulse in pulse_list:
                if need_reset is False:
                    break
                for base_pulse in com_pulse.pulse:
                    if not (base_pulse.amp == 0 or base_pulse.amp == 0.0):
                        need_reset = False
                        break
            if need_reset is True:
                lower_drive_power = QAIO.get_range("pulse_power")[0]
                self.inst.set_power(
                    module="XY_control",
                    channel=qubit.xy_channel,
                    value=lower_drive_power,
                )
                pyqlog.warning(f"Zero XY | Set {qubit.name} drive power into {lower_drive_power}dB")

    def _get_readout_channels(self):
        """Get readout or sample channels."""
        if self.run_options.online_bits:
            # feature online ctx use all qubit readout channel
            readout_channels = [bit.readout_channel for bit in self.run_options.online_bits if isinstance(bit, Qubit)]
        elif self.experiment_options.multi_readout_channels:
            readout_channels = self.experiment_options.multi_readout_channels
        elif self.is_coupler_exp is True:
            # adjust coupler experiment, open probeQ readout_channel
            # probe_qubit = getattr(self, "probeQ")
            probe_qubit = self.run_options.co.probeQ
            readout_channels = [probe_qubit.readout_channel]
        else:
            readout_channels = [self.qubits[0].readout_channel]
        return readout_channels

    def _init_instrument(self):
        """Initialize instrument settings."""
        z_dc_channels = []
        z_flux_channels = []
        xy_channels = []

        # init qubit xy channel and z flux channel
        if self.qubits:
            z_flux_channels += [qubit.z_flux_channel for qubit in self.qubits]
            if self.experiment_options.bind_drive:
                xy_channels += [qubit.xy_channel for qubit in self.qubits]

        # init coupler z flux channel
        if self.couplers:
            z_flux_channels += [coupler.z_flux_channel for coupler in self.couplers]

        # init compensate unit z flux channel
        if self.compensate_units:
            z_flux_channels += [base_qubit.z_flux_channel for base_qubit in self.compensate_units]

        # get readout channel from run options
        readout_channels = self._get_readout_channels()

        # online ctx adapter: extend xy and z channel from run options online bits
        online_bits = self.run_options.online_bits
        if online_bits:
            z_flux_channels = [bit.z_flux_channel for bit in online_bits]
            xy_channels = [bit.xy_channel for bit in online_bits if isinstance(bit, Qubit)]

        # Summarize all channels
        module_control = {
            "Z_dc_control": z_dc_channels,
            "Z_flux_control": list(set(z_flux_channels)),
            "XY_control": list(set(xy_channels)),
            "Readout_control": readout_channels,
        }

        # Initialize MeasureAIO
        if INST_SELECT is True:
            self.inst.select(**module_control)
        else:
            self.inst.select2(**module_control)

    def _bind_qubits_inst(self):
        """Bind qubit parameters to instruments."""
        for qubit in self.qubits:
            self._bind_qubit_drive(qubit)
            # self._bind_qubit_dc(qubit)
        self._bind_qubit_probe()

    def _bind_qubit_probe(self):
        """Only single qubit experiment need bind qubit probe & instrument"""
        if self.experiment_options.bind_probe and self.qubit:
            self._bind_probe_inst(self.qubit)

    def _bind_probe_inst(self, qubit: Qubit):
        """Bind qubit information to instrument."""
        if qubit.probe_freq is not None:
            self.inst.set_output_freq(
                module="Readout_control",
                channel=qubit.readout_channel,
                value=qubit.probe_freq,
            )

        if qubit.probe_power is not None:
            self.inst.set_power(
                module="Readout_control",
                channel=qubit.readout_channel,
                value=qubit.probe_power,
            )

        if qubit.sample_delay is not None:
            self.inst.set_sample_delay(qubit.readout_channel, qubit.sample_delay)

        if qubit.sample_width is not None:
            self.inst.set_sample_width(qubit.readout_channel, qubit.sample_width)

        # set readout sampling_mode
        self.inst.set_sampling_mode(qubit.readout_channel, self._match_sampling_mode())

        # add experiment options to decide bind or not.
        if qubit.Mwave.baseband_freq is not None and self.experiment_options.bind_baseband_freq:
            self.inst.set_intermediate_freq(
                module="Readout_control",
                channel=qubit.readout_channel,
                value=qubit.Mwave.baseband_freq,
            )

            # set readout sampling_IF
            self.inst.set_sampling_freq(
                "Readout_control",
                qubit.readout_channel,
                qubit.Mwave.baseband_freq,
            )

    def _bind_qubit_drive(self, qubit: Qubit):
        """Bind qubit drive parameters to instruments."""
        if self.experiment_options.bind_drive:
            if qubit.drive_freq is not None:
                self.inst.set_output_freq(
                    module="XY_control",
                    channel=qubit.xy_channel,
                    value=qubit.drive_freq,
                )

            if qubit.drive_power is not None:
                self.inst.set_power(
                    module="XY_control",
                    channel=qubit.xy_channel,
                    value=qubit.drive_power,
                )

            # add experiment options to decide bind or not.
            if qubit.XYwave.baseband_freq is not None and self.experiment_options.bind_baseband_freq:
                self.inst.set_intermediate_freq(
                    module="XY_control",
                    channel=qubit.xy_channel,
                    value=qubit.XYwave.baseband_freq,
                )

    def _initialize_experiment(self):
        """initialize experiment settings."""
        self._init_instrument()

        # Bind qubit and instrument.
        self._bind_qubits_inst()

    def _create_readout_pulse(self, qubit: Qubit):
        
        channel = qubit.readout_channel
        if isinstance(self.run_options.acq_pulse, PulseComponent):
            pulse = self.run_options.acq_pulse
        else:
            m_time = qubit.Mwave.width
            amp = qubit.Mwave.amp
            baseband_freq = qubit.Mwave.baseband_freq
            if qubit.Mwave.name == "AcquireSine":
                pulse = AcquireSine(time=m_time, amp_list=[amp], baseband_freq_list=[baseband_freq])()
            elif qubit.Mwave.name == "AcquireClear":
                pulse = AcquireClear(
                    rd_time=m_time,
                    amp_list=[amp],
                    baseband_freq=[baseband_freq],
                    tkick=qubit.Mwave.tkick,
                    amp1=qubit.Mwave.amp1,
                    amp2=qubit.Mwave.amp2,
                )()
            else:
                raise ValueError(f"Qubit.Mwave.name={qubit.Mwave.name} error!")

        self.readout_pulses.append(pulse)
        self.readout_qubits.append(qubit)
        self.inst.set_measure_index(channel, 0)
        self.inst.set_measure_width(channel, pulse.width)
        self.inst.set_custom_waveform("Readout_control", channel, [pulse.pulse])

        return pulse

    @extend_f12_pulse
    def _set_single_readout_pulse(self, qubit=None):
        """Set single bit readout pulse."""
        if qubit is None:
            qubit = self.qubit
        self._create_readout_pulse(qubit)
        return qubit

    @extend_f12_pulse
    def _set_union_readout_pulse(self, qubits=None):
        """Set union readout pulse."""
        readout_params = {}

        if qubits is None:
            qubits = self.qubits

        for qubit in qubits:
            self._bind_probe_inst(qubit)
            if qubit.readout_channel not in readout_params:
                readout_params[qubit.readout_channel] = {
                    "amp_list": [],
                    "baseband_freq_list": [],
                    "index_list": [],
                    "width": set(),
                    "name": "",
                    "tkick": 0,
                    "amp1": 0,
                    "amp2": 0,
                }

        for qubit in qubits:
            readout_params[qubit.readout_channel]["name"] = qubit.Mwave.name
            readout_params[qubit.readout_channel]["tkick"] = qubit.Mwave.tkick
            readout_params[qubit.readout_channel]["amp1"] = qubit.Mwave.amp1
            readout_params[qubit.readout_channel]["amp2"] = qubit.Mwave.amp2
            readout_params[qubit.readout_channel]["amp_list"].append(qubit.Mwave.amp)
            readout_params[qubit.readout_channel]["baseband_freq_list"].append(qubit.Mwave.baseband_freq)
            readout_params[qubit.readout_channel]["index_list"].append(qubit.union_readout.index)
            readout_params[qubit.readout_channel]["width"].add(qubit.Mwave.width)
            self.readout_qubits.append(qubit)

        for readout_channel, params in readout_params.items():
            self._get_union_readout_pulse_utility(readout_channel, params)

        return qubits

    def _get_union_readout_pulse_utility(self, readout_channel: int, params: CommonDict):
        """Set readout pulse with parameters."""
        width = int(max(params.get("width")))
        amp_list = params.get("amp_list")
        baseband_freq_list = params.get("baseband_freq_list")
        name = params.get("name")

        if name == "AcquireSine":
            union_readout_pulse = AcquireSine(width, amp_list, baseband_freq_list)
            union_readout_pulse()
        elif name == "AcquireClear":
            union_readout_pulse = AcquireClear(
                rd_time=width,
                amp_list=amp_list,
                baseband_freq=baseband_freq_list,
                tkick=params.get("tkick"),
                amp1=params.get("amp1"),
                amp2=params.get("amp2"),
            )
            union_readout_pulse()
        else:
            raise ValueError(f"Qubit.Mwave.name={name} error!")

        self.readout_pulses.append(union_readout_pulse)
        self.inst.set_custom_waveform("Readout_control", readout_channel, [union_readout_pulse.pulse])
        self.inst.set_measure_index(readout_channel, 0)
        self.inst.set_measure_width(readout_channel, union_readout_pulse.width)

    @online_extend
    def _register(self):
        """Register all data to database which the data include instrument
        settings and pulse settings.

        Raises:
            ExperimentRegisterError: If experiment data saved to database failed.
            ExperimentOptionsError: If user set `data_type` out of namespace.
        """
        enable_one_sweep = self.experiment_options.enable_one_sweep

        self._max_readout_pulse_width = max([readout_pulse.width for readout_pulse in self.readout_pulses])

        # Add ac crosstalk to correct awg bias value
        # before save to database.
        # fixed bug: We must correct awg bias before generate z pulse.
        # since we call `_compensate_ac_bias` method to add bias for pulse object.
        self._open_zcompensate()

        # compatibility for calculate pulse on qstream.
        self._register_fake_pulse("XY", enable_one_sweep)
        self._register_fake_pulse("Z", enable_one_sweep)

        # if use ac instead dc, XY line need set trigger delay.
        if self.experiment_options.ac_prepare_time:
            for qubit in self.xy_pulses.keys():
                # user may set trigger delay already.
                raw_delay = self.inst.get_trigger_delay("XY_control", qubit.xy_channel)[0]
                self.inst.set_trigger_delay(
                    "XY_control",
                    qubit.xy_channel,
                    raw_delay + self.experiment_options.ac_prepare_time,
                )

        measure_aio, sweep_control = self.inst.get()
        self.experiment_protocol.label = self.label
        self.experiment_protocol.fake_pulse = True
        self.experiment_protocol.repeat = self.experiment_options.repeat
        self.experiment_protocol.measure_aio = measure_aio
        self.experiment_protocol.sweep_control = sweep_control

        # once measure auto set readout delay
        if self.run_options.auto_set_readout_delay is False:
            pass
        else:
            self._auto_set_readout_delay()

        # set program status
        self.experiment_protocol.status = self._match_doc_status()

        # bugfix: parallel child experiment update ac bias
        if self.run_options.ac_bias:
            self.experiment_protocol.extra.update({"ac_bias": self.run_options.ac_bias})

        # bugfix: XYCrossNpiOnce, XYCrossRB adjust to new version, 2024.05.20
        if self.run_options.pre_xy_cross:
            self.experiment_protocol.extra.update(
                {"pre_xy_cross_list": [self.run_options.pre_xy_cross]}
            )

    def _match_doc_status(self) -> int:
        """According to `data_type` or other condition select status value."""
        data_type = self.experiment_options.data_type
        use_simulator = self.experiment_options.use_simulator
        simulator_data_path = self.experiment_options.simulator_data_path

        if use_simulator is True and simulator_data_path:
            status = ExperimentDocStatus.simulator
        elif data_type == "amp_phase":
            status = ExperimentDocStatus.amp_phase
        elif data_type == "I_Q":
            status = ExperimentDocStatus.iq
        elif data_type == "track":
            status = ExperimentDocStatus.track
        else:
            raise ExperimentOptionsError(
                self.label,
                key="data_type",
                value=data_type,
                msg="data type is invalid! Only support ``amp_phase``, ``I_Q`` and ``track``",
            )
        return status

    def _match_sampling_mode(self) -> int:
        """According to `file_flag` and `data_type` select sampling_mode value."""
        data_type = self.experiment_options.data_type
        file_flag = self.experiment_options.file_flag

        mode_val = 10
        if data_type == "probability":
            # 2024-04-18, in the future, maybe support `probability` data_type.
            mode_val = SamplingMode.p.value
        else:
            if file_flag == 0:
                mode_val = SamplingMode.iq.value
            elif file_flag == 1:
                mode_val = SamplingMode.raw.value

        return mode_val

    def _auto_set_readout_delay(self):
        readout_control_list = self.experiment_protocol.measure_aio.Readout_control
        for readout_control in readout_control_list:
            raw_delay = self.inst.get_trigger_delay("Readout_control", readout_control.channel)[0]
            sweep_control = self.inst.get_target_sweep("Readout_control", readout_control.channel, "trigger_delay")
            if not sweep_control:
                readout_delay = max(self._pulse_time_list) if self._pulse_time_list else 0
                delay = raw_delay + readout_delay
                delay += self.experiment_options.ac_prepare_time
                delay = QAIO.delay_ceil(delay) if QAIO.type == QAIO.qaio_72 else delay
                self.inst.set_trigger_delay("Readout_control", readout_control.channel, delay)
            elif QAIO.type == QAIO.qaio_72:
                readout_sweep_delay = np.asarray(sweep_control.points)
                delay_after = QAIO.delay_ceil(readout_sweep_delay)
                sweep_control.points = delay_after.tolist()

    def _register_fake_pulse(self, type_: str, enable_one_sweep: bool = False):
        """Register fake pulse to database.

        Notes::
            Fake pulse not need to do pulse correction
        """
        if type_ == "XY":
            pulse_set = self.xy_pulses.items()
            module = "XY_control"
        elif type_ == "Z":
            pulse_set = self.z_pulses.items()
            module = "Z_flux_control"
        else:
            raise ExperimentPulseModuleError(self.label, type_, ["XY", "Z"])

        # todo add other pulse correction
        for qubit, pulse_list in pulse_set:
            if type_ == "XY":
                channel = qubit.xy_channel
                # for xy_pulse in pulse_list:
                #     if self.compensates:
                #         xy_pulse.correction = self.compensates[qubit]
            else:
                channel = qubit.z_flux_channel
            if pulse_list:
                only_one_pulse = len(pulse_list) == 1
                if only_one_pulse and not enable_one_sweep:
                    self.inst.set_custom_waveform(channel=channel, module=module, wavedata=pulse_list)
                else:
                    self.inst.sweep_wave(  # type(self.inst) == <class 'pyQCat.instrument.instrument_aio.Instrument'>
                        channel=channel,
                        module=module,
                        wavedata=pulse_list,
                        repeat=self.experiment_options.repeat,
                    )

    def _open_zcompensate(self):
        """Open AWG channel for experiment to eliminate AWG channel compensation.
        Set Z_flux module's pulse according to XY module's pulse.
        Must be sure call this method before call set_pulse("Z") method.
        since z pulse time is equal to xy pulse time, not need to append to
        self._pulse_time_list.

        Multiple qubits experiment needs to add AC crosstalk."""
        loop_count = self._build_z_compensate_pulse()

        if self.run_options.reset_empty_xy_power is True:
            self._reset_drive_power()

        if self.run_options.multiple_measure_options:
            return 

        # generate Z pulse.
        for loop in range(loop_count):
            for i, unit in enumerate(list(self.z_pulses.keys())):
                self._connect_z_pulse(
                    unit,
                    loop,
                    self._max_readout_pulse_width,
                )

    def _connect_z_pulse(
        self,
        unit: Union[Qubit, Coupler],
        index: int,
        readout_pulse_width: float,
    ):
        """connect 3 pulse.
        1st is AC prepare z pulse.
        2nd is experimental z pulse.
        3rd is readout work point z pulse.
        """
        _2nd_pulse = self.z_pulses[unit][index]
        _1st_pulse = self._fill_z_prepare_pulse(unit)
        _3rd_pulse = self._fill_readout_work_point_pulse(unit, readout_pulse_width)

        if QAIO.type == QAIO.qaio_72:
            exp_width = _2nd_pulse.width + self.experiment_options.ac_prepare_time
            compensate_delay = round(QAIO.delay_ceil(exp_width) - exp_width, 4)
        else:
            compensate_delay = 0.0

        if compensate_delay:
            if (
                "run_mode" in self.experiment_options
                and self.experiment_options.run_mode in ["new_case"]
                and unit.name == self.run_options.exp_qubit.name
            ):
                compensate_pulse = Constant(compensate_delay, unit.readout_point.amp, name="Z")()
            else:
                compensate_pulse = Constant(compensate_delay, 0.0, name="Z")()
        else:
            compensate_pulse = Constant(compensate_delay, 0.0, name="Z")()
        filled_pulse = _1st_pulse + _2nd_pulse + compensate_pulse + _3rd_pulse
        filled_pulse.only_z_correct = _2nd_pulse.only_z_correct
        filled_pulse.base += unit.ac
        self.z_pulses[unit][index] = filled_pulse
        return filled_pulse

    def _fill_readout_work_point_pulse(self, qubit: BaseQubitsType, readout_pulse_width: float):
        """Fill readout point Z line pulse."""

        # v 0.0.2 update: The readout working point acts on any qubit, not just the readout
        if self.experiment_options.fill_readout_point is False:
            return Constant(0, 0)()

        if self.is_coupler_exp and self.run_options.readout_point_mode != 0 and isinstance(qubit, Coupler):
            if qubit.name not in self._execute_unit_names:
                pulse = Constant(time=readout_pulse_width, amp=0)
            elif self.run_options.readout_point_mode == 1:
                pulse = FlatTopGaussianSide(
                    time=readout_pulse_width,
                    side="right",
                    **check_readout_point(qubit),
                )
            elif self.run_options.readout_point_mode == 2:
                pulse = FlatTopGaussianAsymmetric(
                    time=readout_pulse_width,
                    amp2=self.run_options.co.coupler.readout_point.amp - self.run_options.co.coupler.pi_pulse_point.amp,
                    **check_readout_point(qubit),
                )
            else:
                raise ExperimentPulseModuleError("Coupler Readout Point Error!")

            pulse.type = "Z"
            pulse.bit = qubit.name
            pulse()
            return pulse

        model_str = qubit.readout_point_model

        readout_point_pulse = None
        try:
            if qubit.name not in self._execute_unit_names:
                readout_point_pulse = Constant(time=readout_pulse_width, amp=0)
            else:
                model = self._search_pulse_model(model_str)
                if model:
                    # pyqlog.debug(f"Use pulse model {model_str} for the readout point")
                    pulse_params = check_readout_point(qubit)
                    if model_str == "Constant":
                        readout_point_pulse = model(readout_pulse_width, amp=pulse_params.get('amp'))
                    else:
                        readout_point_pulse = model(readout_pulse_width, **pulse_params)
                else:
                    pyqlog.warning(f"No find the pulse {model_str} model!")
        except TypeError as e:
            pyqlog.error(f"The model parameters do not match. Because {e}")

        if readout_point_pulse is None:
            # pyqlog.debug(f"Use pulse model Constant for the readout point")
            readout_point_pulse = Constant(readout_pulse_width, 0)

        readout_point_pulse.type = "Z"
        readout_point_pulse.bit = qubit.name
        filled_pulse = readout_point_pulse()

        return filled_pulse

    def _fill_z_prepare_pulse(self, qubit: BaseQubitsType):
        """If u use AC instead DC, Z line needs play z pulse before
        experiment starts to reduce distortion."""
        width = self.experiment_options.ac_prepare_time
        amp = 0

        if width > 0:
            # optimize, add ac prepare rise
            rise_model_name = qubit.ac_prepare_rise_model
            rise_options = qubit.ac_prepare_rise_options
            rise_model_cls = self._search_pulse_model(rise_model_name)
            try:
                new_rise_options = deepcopy(rise_options)
                new_rise_options.amp = amp
                new_rise_options.name = "Z"
                if rise_model_name == "GaussianUp":
                    rise_width = new_rise_options.sigma * new_rise_options.num
                    new_rise_options.time = rise_width

                rise_width = new_rise_options.time
                rise_pulse = rise_model_cls(**new_rise_options)
                rise_pulse.bit = qubit.name
            except Exception as err:
                pyqlog.warning(
                    f"According to ac_prepare_rise_model: {rise_model_name}, "
                    f"search pulse class: {rise_model_cls}, "
                    f"ac_prepare_rise_options: {rise_options}, "
                    f"create rise pulse error: {err}"
                )
                rise_width = 0
                rise_pulse = Constant(rise_width, amp)
                rise_pulse.bit = qubit.name

            remain_pulse = Constant(width - rise_width, amp)
            remain_pulse.bit = qubit.name
            prepare_pulse = rise_pulse() + remain_pulse()
        else:
            prepare_pulse = Constant(0, amp)
            prepare_pulse.bit = qubit.name
            prepare_pulse()

        return prepare_pulse

    def _build_z_compensate_pulse(self):
        """build z line pulse."""
        qubit = None

        # Added default compensation zero waveform for Z line of qubit
        for qubit, xy_pulses in self.xy_pulses.items():
            # If the qubit corresponding to A has already set the Z pulse,
            # don't need to add zcompensate again.
            if len(self.z_pulses[qubit]) == 0:
                for pulse in xy_pulses:
                    constant = Constant(pulse.width, 0)
                    constant.type = "Z"
                    constant.bit = qubit.name
                    self.z_pulses[qubit].append(constant())

        # Added default compensation zero waveform for Z line of unit
        for unit in list(self.z_pulses.keys()):
            if len(self.z_pulses[unit]) == 0:
                other_qubit_z_pulses = self.z_pulses.get(qubit, [])
                for pulse in other_qubit_z_pulses:
                    constant = Constant(pulse.width, 0)
                    constant.type = "Z"
                    constant.bit = unit.name
                    self.z_pulses[unit].append(constant())
            elif isinstance(unit, Qubit) and len(self.xy_pulses[unit]) == 0:
                z_pulses = self.z_pulses.get(unit, [])
                x_pulses = []
                for p in z_pulses:
                    xp = Constant(p.width, 0, "XY")()
                    xp.bit = unit.name
                    x_pulses.append(xp)
                self.xy_pulses[unit] = x_pulses

        # Unify the number of Z-line scan waveforms for each physical bit
        z_max_len = max(len(pulses) for pulses in self.z_pulses.values())
        for base_qubit, z_pulses in self.z_pulses.items():
            if len(z_pulses) == 1:
                z_pulses = [deepcopy(z_pulses[0]) for _ in range(z_max_len)]
                self.z_pulses[base_qubit] = z_pulses

        # check z pulse length
        z_pulses_loop_nums = []
        for _, z_pulses in self.z_pulses.items():
            z_pulses_loop_nums.append(len(z_pulses))
        loop_set = set(z_pulses_loop_nums)
        if len(loop_set) != 1:
            pyqlog.warning(f"All Sweep points length is not same or null, loop_list: {z_pulses_loop_nums}")

        return loop_set.pop()

    def _init_zero_pulse(self):
        """Initial some zero Constant pulse."""
        head_time = self.experiment_options.head_time
        tail_time = self.experiment_options.tail_time
        right_delay = self.experiment_options.right_delay
        add_z_delay = self.experiment_options.add_z_delay

        self.run_options.co.head_pulse = Constant(head_time, 0)
        self.run_options.co.tail_pulse = Constant(tail_time, 0)
        self.run_options.co.right_zero_pulse = Constant(right_delay, 0)
        self.run_options.co.add_zero_pulse = Constant(add_z_delay, 0)

    def _compose_coupler_z_pulse(self, pulse: PulseComponent):
        head_p = deepcopy(self.run_options.co.head_pulse)
        tail_p = deepcopy(self.run_options.coupler_z_pi_pulse)
        return head_p() + pulse + tail_p()

    def _compose_qubit_z_pulse(self, pulse: PulseComponent):
        head_p = deepcopy(self.run_options.co.head_pulse)
        pi_pulse_width = self.run_options.coupler_z_pi_pulse.width
        return head_p() + pulse + Constant(pi_pulse_width, 0)()

    def _prepare_measure_wrapper(self):
        prepare_measure_bits = self.run_options.prepare_measure_bits
        measure_qubits = []

        for qubit in self.run_options.measure_qubits:
            if isinstance(qubit, str):
                qubit_name = qubit
            else:
                qubit_name = qubit.name
            measure_qubits.append(qubit_name)

        self.experiment_protocol.extra["measure_qubits"] = measure_qubits
        if prepare_measure_bits:
            self.experiment_protocol.extra["prepare_measure_bits"] = prepare_measure_bits

            readout_raw_delay_map = {}
            if INST_SELECT is True:
                for control in self.inst.json_dict.get("Read_out_control"):
                    readout_raw_delay_map[control["channel"]] = control["trigger_delay"][0]
            else:
                for control in self.inst._measure_aio.Readout_control:
                    readout_raw_delay_map[control.channel] = 50
            worker = PrepareMeasureWrapper(
                self.experiment_protocol,
                offset=self.experiment_options.prepare_measure_offset,
                readout_raw_delay_amp=readout_raw_delay_map,
            )
            worker.run()

    def _reset_drive_power(self):
        for qubit, pulse_list in self.xy_pulses.items():
            need_reset = True
            for com_pulse in pulse_list:
                if need_reset is False:
                    break
                for base_pulse in com_pulse.pulse:
                    if not (base_pulse.amp == 0 or base_pulse.amp == 0.0):
                        need_reset = False
                        break
            if need_reset is True:
                lower_drive_power = QAIO.get_range("pulse_power")[0]
                self.inst.set_power(
                    module="XY_control",
                    channel=qubit.xy_channel,
                    value=lower_drive_power,
                )
                pyqlog.warning(f"Zero XY | Set {qubit.name} drive power into {lower_drive_power}dB")

    def _tackle_multiple_measure_options(self):
        multiple_measure_options = self.run_options.multiple_measure_options
        self.run_options.auto_set_readout_delay = False
        self.run_options.measure_qubits = []
        for qubit, measure_options in multiple_measure_options.items():
            self._bind_probe_inst(qubit)
            self._validate_measure_options(qubit, measure_options)
            acq_data = measure_options.get("acq_data", True)
            if acq_data is True:
                self.run_options.measure_qubits.append(qubit)

    def _validate_measure_options(self, qubit, measure_options):
        if not measure_options.waveform:
            raise ValueError("must provided readout pulse")

        if not measure_options.measure_delay:
            raise ValueError("must set measure delay")
        
        readout_channel = qubit.readout_channel
        sampling_count = len(measure_options.measure_delay)
        measure_options.sampling_count = sampling_count
        
        for pulse in measure_options.waveform:
            pulse.bit = qubit.name
        
        if not measure_options.measure_index:
            measure_options.measure_index = [0 for _ in range(sampling_count)]
        
        if not measure_options.measure_width:
             measure_options.measure_width = [measure_options.waveform[idx].width for idx in measure_options.measure_index]
             
        if not measure_options.sampling_width:
            measure_options.sampling_width = [qubit.sample_width for _ in range(sampling_count)]
            
        if not measure_options.sampling_delay:
            measure_options.sampling_delay = [qubit.sample_delay for _ in range(sampling_count)]
            
        if not measure_options.sampling_IF:
            measure_options.sampling_IF = [qubit.Mwave.baseband_freq]

        # adapter measure delay
        # format measure delay like
        # [[100], [100], [10]]
        # [[100], [100, 200, 300], [100]]
        raw_delay = self.inst.get_trigger_delay("Readout_control", readout_channel)[0]
        measure_delay = measure_options.measure_delay
        measure_options.measure_delay = [
            [
                QAIO.delay_ceil(value + (raw_delay if idx == 0 else 0))
                for value in (md if isinstance(md, list) else [md])
            ]
            for idx, md in enumerate(measure_delay)
        ]
        loop_count = [len(md) for md in measure_options.measure_delay]
        max_loop = max(loop_count)
        loop_set = set(loop_count)
        if len(loop_set) > 2 or (loop_set == 2 and 1 not in loop_set):
            raise ValueError(f"measure delay format error | {measure_options.measure_delay}")

        # adapter z readout point pulse
        z_pulses = self.z_pulses.get(qubit)
        if not z_pulses:
            model = self._search_pulse_model(qubit.readout_point_model)
            readout_point_param = check_readout_point(qubit)
            for idx in range(max_loop):
                z_compensate_pulse = Constant(0, 0)()
                for jdx, mds in enumerate(measure_options.measure_delay):
                    md = mds[0] if idx >= len(mds) else mds[idx]
                    z_compensate_pulse += (
                        Constant(md - raw_delay, 0)() if jdx == 0 else Constant(md, 0)()
                    )
                    readout_pulse_width = measure_options.measure_width[jdx]
                    try:
                        readout_point_pulse = model(readout_pulse_width, **readout_point_param)
                    except Exception:
                        readout_point_pulse = Constant(readout_pulse_width, readout_point_param.get("amp", 0))
                    z_compensate_pulse += readout_point_pulse()
                z_compensate_pulse.bit = qubit.name
                z_compensate_pulse.base += qubit.ac
                z_pulses.append(z_compensate_pulse)

        # adapter sample delay
        adapter_sampling_delay = []
        for idx, sd in enumerate(measure_options.sampling_delay):
            if idx == 0:
                if sampling_count == 1:
                    cur_sample_delay = [sd]
                    cur_sample_delay = [sd for _ in measure_options.measure_delay[idx]]
                else:
                    cur_sample_delay = [
                        sd + md for md in measure_options.measure_delay[idx]
                    ]
            else:
                cur_sample_delay = [
                    md
                    + sd
                    + measure_options.measure_width[idx - 1]
                    - measure_options.sampling_width[idx - 1]
                    - measure_options.sampling_delay[idx - 1]
                    for md in measure_options.measure_delay[idx]
                ]
            adapter_sampling_delay.append(cur_sample_delay)
        measure_options.sampling_delay = adapter_sampling_delay
        
        # set instrument
        self._set_measure_options(readout_channel, measure_options)

    def _set_measure_options(self, readout_channel: int, measure_options: QDict):
        self.inst.set_trigger_delay("Readout_control", readout_channel, *[md[0] for md in measure_options.measure_delay])
        self.inst.set_sample_delay(readout_channel, *[sd[0] for sd in measure_options.sampling_delay])
        # for sweep experiment
        for idx, md in enumerate(measure_options.measure_delay):
            if len(md) > 1:
                self.inst.sweep_delay("Readout_control", readout_channel, points=md, index=idx)
                self.inst.sweep_sample_delay(readout_channel, points=measure_options.sampling_delay[idx], index=idx)
        
        self.inst.set_measure_index(readout_channel, *measure_options.measure_index)
        self.inst.set_measure_width(readout_channel, *measure_options.measure_width)
        self.inst.set_sample_width(readout_channel, *measure_options.sampling_width)
        self.inst.set_sampling_freq("Readout_control", readout_channel, *measure_options.sampling_IF)
        self.inst.set_custom_waveform("Readout_control", readout_channel, measure_options.waveform)
        self.readout_pulses.extend(measure_options.waveform)
