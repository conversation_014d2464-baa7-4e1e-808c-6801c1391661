# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/08
# __author:       <PERSON>Kang Geng


from ...log import pyqlog
from ..batch_experiment import BatchExperiment

import numpy as np
import itertools


class BatchT1SpectrumWithBiasCoupler(BatchExperiment):
    """
    BatchT1SpectrumWithBiasCoupler is used to run T1SpectrumWithBiasCoupler experiments
    in batch mode with configurable coupler bias parameters.
    """

    @classmethod
    def _default_experiment_options(cls):
        """Default experiment options for BatchT1SpectrumWithBiasCoupler"""
        options = super()._default_experiment_options()

        # Standard batch experiment flows
        options.flows = ["T1SpectrumWithBiasCoupler"]
        options.working_points = []

        return options

    @classmethod
    def _default_analysis_options(cls):
        """Default analysis options for BatchT1SpectrumWithBiasCoupler"""
        options = super()._default_analysis_options()

        options.child_ana_options = {"quality_bounds": [0.9, 0.85, 0.77]}
        options.pure_exp_mode = False
        options.save_s3 = False
        options.save_exp_data = True

        return options

    def _batch_up(self):
        """Initialize batch experiment following Monster framework design pattern"""
        super()._batch_up()

        # Validate working_points configuration
        if not self.experiment_options.working_points:
            pyqlog.warning(
                "No working_points defined, will use physical_units with default configuration"
            )
            self._generate_default_working_points()

    def _generate_default_working_points(self):
        """Generate default working points from physical_units when no working_points are defined"""
        self.experiment_options.working_points = []

        for unit in self.experiment_options.physical_units:
            # Extract target qubit from unit name
            if unit.startswith("q") and ("," in unit or unit[1:].isdigit()):
                target_qubit = unit.split(",")[0] if "," in unit else unit
            else:
                target_qubit = unit

            # Create default working point with empty ratio_map
            working_point = {
                "target_qubit": target_qubit,
                "ratio_map": {},  # Empty ratio_map means no bias couplers
            }

            self.experiment_options.working_points.append(working_point)
            pyqlog.info(
                f"Generated default working point for {unit}: target={target_qubit}"
            )

    def _run_batch(self):
        """Execute batch T1SpectrumWithBiasCoupler experiments following Monster framework pattern"""
        pyqlog.info("Starting BatchT1SpectrumWithBiasCoupler execution")

        all_pass_units = []
        experiment_count = 0

        # Iterate through working_points
        for i, wp_group in enumerate(self.experiment_options.working_points):
            target_qubit_name = wp_group.get("target_qubit")
            ratio_map = wp_group.get("ratio_map", {})

            # Get the actual target_qubit object from backend
            target_qubit = self.backend.chip_data.cache_qubit.get(target_qubit_name)

            if not target_qubit:
                pyqlog.error(
                    f"Target qubit '{target_qubit_name}' not found. Skipping this group."
                )
                continue

            if not target_qubit.tunable:
                pyqlog.info(
                    f"Target qubit {target_qubit_name} is not tunable. Skipping this group."
                )
                continue

            # If ratio_map is empty, create a single experiment with no bias couplers
            if not ratio_map:
                experiment_count += 1
                physical_unit = target_qubit_name

                pyqlog.info(
                    f"Executing working point group {i + 1}/{len(self.experiment_options.working_points)} - single experiment (no bias couplers)"
                )
                pyqlog.info(f"  Target qubit: {target_qubit_name}")

            # Extract bias coupler names and prepare z_amp value lists for each coupler
            bias_coupler_names = list(ratio_map.keys())
            coupler_z_amp_ranges = []

            for coupler_name in bias_coupler_names:
                min_ratio, max_ratio, num_points = ratio_map[coupler_name]
                # Generate equally spaced ratio values
                ratios = np.linspace(min_ratio, max_ratio, num_points)

                # Calculate z_amp for each ratio using the formula
                calculated_z_amps = [
                    (
                        (target_qubit.dc_max - target_qubit.dc_min) * ratio
                        + target_qubit.dc_min
                    )
                    for ratio in ratios
                ]
                coupler_z_amp_ranges.append(calculated_z_amps)

            # Generate all combinations of bias_coupler_z_amps
            # If ratio_map has N couplers with M points each, this creates M^N combinations
            all_bias_coupler_z_amp_combinations = list(
                itertools.product(*coupler_z_amp_ranges)
            )

            pyqlog.info(
                f"Processing working point group for {target_qubit_name} with {len(all_bias_coupler_z_amp_combinations)} experiment combinations."
            )

            # Iterate through each combination to run experiments
            for j, bias_coupler_z_amps in enumerate(
                all_bias_coupler_z_amp_combinations
            ):
                experiment_count += 1
                physical_unit = (
                    target_qubit_name  # Assume physical_unit is target_qubit for now
                )

                pyqlog.info(
                    f"Executing working point group {i + 1}/{len(self.experiment_options.working_points)} - experiment combination {j + 1}/{len(all_bias_coupler_z_amp_combinations)}"
                )
                pyqlog.info(f"  Target qubit: {target_qubit_name}")
                pyqlog.info(f"  Bias coupler names: {bias_coupler_names}")
                pyqlog.info(
                    f"  Bias coupler z_amps: {[f'{x:.4f}' for x in bias_coupler_z_amps]}"
                )  # Format for readable logging

                # Set experiment-specific parameters using change_regular_exec_exp_options
                self.change_regular_exec_exp_options(
                    exp_name="T1SpectrumWithBiasCoupler",
                    child_exp_options={
                        "bias_coupler_name_list": bias_coupler_names,
                        "bias_coupler_z_amp_list": list(bias_coupler_z_amps),
                    },
                )

                # Execute the flow for this specific experiment combination
                pass_units = self._run_flow(
                    flows=self.experiment_options.flows,
                    physical_units=bias_coupler_names + [physical_unit],
                    name=f"T1SpectrumWithBiasCoupler_WPGroup_{i + 1}_Comb_{j + 1}",
                )

                if pass_units:
                    all_pass_units.extend(pass_units)
                    pyqlog.info(
                        f"Experiment combination {j + 1} completed successfully. Pass units: {pass_units}"
                    )
                else:
                    pyqlog.warning(
                        f"Experiment combination {j + 1} failed - no units passed"
                    )

        # Remove duplicates while preserving order
        final_pass_units = list(dict.fromkeys(all_pass_units))

        pyqlog.info(
            f"BatchT1SpectrumWithBiasCoupler completed. Total experiments run: {experiment_count}. "
            f"Total pass units: {final_pass_units}"
        )
        return final_pass_units
