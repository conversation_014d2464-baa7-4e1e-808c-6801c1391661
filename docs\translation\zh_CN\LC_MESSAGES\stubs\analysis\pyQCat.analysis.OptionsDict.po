# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.OptionsDict.rst:2
msgid "pyQCat.analysis.OptionsDict"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict:1
msgid "Bases: :py:class:`dict`"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict:1
msgid "General extended dictionary for fit options."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict:3
msgid "This dictionary provides several extra features."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict:5
msgid "A value setting method which validates the dict key and value."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict:6
msgid ""
"Dictionary keys are limited to those specified in the constructor as "
"``parameters``."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.__init__:1
#: pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid "Create new dictionary."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.__init__
#: pyQCat.analysis.specification.OptionsDict.format
#: pyQCat.analysis.specification.OptionsDict.set_if_empty
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.__init__:4
msgid "List of parameter names used in the fit model."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.__init__:6
msgid "Default values."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.__init__
msgid "Raises"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.__init__:8
msgid ""
"When defaults is provided as array-like but the number of     element "
"doesn't match with the number of fit parameters."
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.OptionsDict.rst:13
msgid "Methods"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.OptionsDict.__init__>`\\ "
"\\(parameters\\[\\, defaults\\]\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ":py:obj:`clear <pyQCat.analysis.OptionsDict.clear>`\\ \\(\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ":py:obj:`copy <pyQCat.analysis.OptionsDict.copy>`\\ \\(\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ":py:obj:`format <pyQCat.analysis.OptionsDict.format>`\\ \\(value\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.format:1
#: pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid "Format dictionary value."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ""
":py:obj:`fromkeys <pyQCat.analysis.OptionsDict.fromkeys>`\\ "
"\\(\\[value\\]\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid "Create a new dictionary with keys from iterable and values set to value."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ""
":py:obj:`get <pyQCat.analysis.OptionsDict.get>`\\ \\(key\\[\\, "
"default\\]\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid "Return the value for key if key is in the dictionary, else default."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ":py:obj:`items <pyQCat.analysis.OptionsDict.items>`\\ \\(\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ":py:obj:`keys <pyQCat.analysis.OptionsDict.keys>`\\ \\(\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ":py:obj:`pop <pyQCat.analysis.OptionsDict.pop>`\\ \\(k\\[\\,d\\]\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid "If key is not found, d is returned if given, otherwise KeyError is raised"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ":py:obj:`popitem <pyQCat.analysis.OptionsDict.popitem>`\\ \\(\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid "2-tuple; but raise KeyError if D is empty."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ""
":py:obj:`set_if_empty <pyQCat.analysis.OptionsDict.set_if_empty>`\\ "
"\\(\\*\\*kwargs\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1
#: pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid "Set value to the dictionary if not assigned."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ""
":py:obj:`setdefault <pyQCat.analysis.OptionsDict.setdefault>`\\ "
"\\(key\\[\\, default\\]\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid "Insert key with a value of default if key is not in the dictionary."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ""
":py:obj:`update <pyQCat.analysis.OptionsDict.update>`\\ \\(\\[E\\, "
"\\]\\*\\*F\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ""
"If E is present and has a .keys() method, then does:  for k in E: D[k] = "
"E[k] If E is present and lacks a .keys() method, then does:  for k, v in "
"E: D[k] = v In either case, this is followed by: for k in F:  D[k] = F[k]"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:1:<autosummary>:1
msgid ":py:obj:`values <pyQCat.analysis.OptionsDict.values>`\\ \\(\\)"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.set_if_empty:3
msgid "Key and new value to assign."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.format:3
msgid "Subcasses may override this method to provide their own validation."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.format:6
msgid "New value to assign."
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.format
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.format:8
msgid ":py:data:`~typing.Any`"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.format
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.specification.OptionsDict.format:9
msgid "Formatted value."
msgstr ""

#~ msgid "Bases: :class:`dict`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.analysis.OptionsDict.__init__>`\\ "
#~ "\\(parameters\\[\\, defaults\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`clear <pyQCat.analysis.OptionsDict.clear>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`copy <pyQCat.analysis.OptionsDict.copy>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`format <pyQCat.analysis.OptionsDict.format>`\\ \\(value\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`fromkeys <pyQCat.analysis.OptionsDict.fromkeys>`\\ "
#~ "\\(\\[value\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get <pyQCat.analysis.OptionsDict.get>`\\ "
#~ "\\(key\\[\\, default\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`items <pyQCat.analysis.OptionsDict.items>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`keys <pyQCat.analysis.OptionsDict.keys>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`pop <pyQCat.analysis.OptionsDict.pop>`\\ \\(k\\[\\,d\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`popitem <pyQCat.analysis.OptionsDict.popitem>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_if_empty <pyQCat.analysis.OptionsDict.set_if_empty>`\\"
#~ " \\(\\*\\*kwargs\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`setdefault <pyQCat.analysis.OptionsDict.setdefault>`\\ "
#~ "\\(key\\[\\, default\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`update <pyQCat.analysis.OptionsDict.update>`\\ "
#~ "\\(\\[E\\, \\]\\*\\*F\\)"
#~ msgstr ""

#~ msgid ":obj:`values <pyQCat.analysis.OptionsDict.values>`\\ \\(\\)"
#~ msgstr ""

