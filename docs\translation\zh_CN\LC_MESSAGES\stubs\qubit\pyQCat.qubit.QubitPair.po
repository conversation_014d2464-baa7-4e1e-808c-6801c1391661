# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 11:32+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:2
msgid "pyQCat.qubit.QubitPair"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair:1
msgid "Qubit Pair."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:26:<autosummary>:1 of
#: pyQCat.qubit.qubit_pair.QubitPair.__init__:1
msgid "Create a new QubitPair object to save two qubit gates metadata."
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.__init__
#: pyQCat.qubit.qubit_pair.QubitPair.from_dict
#: pyQCat.qubit.qubit_pair.QubitPair.from_file
#: pyQCat.qubit.qubit_pair.QubitPair.to_file
msgid "Parameters"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.__init__:4
msgid "It is recommended to use \"q0, q1\" for two qubit gate."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:26:<autosummary>:1
msgid ":py:obj:`__init__ <pyQCat.qubit.QubitPair.__init__>`\\ \\(name\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:26:<autosummary>:1
msgid ":py:obj:`from_dict <pyQCat.qubit.QubitPair.from_dict>`\\ \\(data\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:26:<autosummary>:1 of
#: pyQCat.qubit.qubit_pair.QubitPair.from_dict:1
msgid "Create QubitPair object using Dict."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:26:<autosummary>:1
msgid ""
":py:obj:`from_file <pyQCat.qubit.QubitPair.from_file>`\\ \\(file\\[\\, "
"fmt\\]\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:26:<autosummary>:1 of
#: pyQCat.qubit.qubit_pair.QubitPair.from_file:1
msgid "Load qubit information from file."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:26:<autosummary>:1
msgid ":py:obj:`save_database <pyQCat.qubit.QubitPair.save_database>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:26:<autosummary>:1
msgid "Save database hook."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:26:<autosummary>:1
msgid ":py:obj:`to_dict <pyQCat.qubit.QubitPair.to_dict>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:26:<autosummary>:1 of
#: pyQCat.qubit.qubit_pair.QubitPair.to_dict:1
msgid "Convert object to dict structure."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:26:<autosummary>:1
msgid ""
":py:obj:`to_file <pyQCat.qubit.QubitPair.to_file>`\\ "
"\\(export\\_path\\[\\, fmt\\]\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:26:<autosummary>:1 of
#: pyQCat.qubit.qubit_pair.QubitPair.to_file:1
msgid "Export the object to a `yaml` or `json` file."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.QubitPair.rst:28
msgid "Attributes"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict:1:<autosummary>:1
msgid ":py:obj:`name <pyQCat.qubit.QubitPair.name>`\\"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict:1:<autosummary>:1
msgid ":py:obj:`interaction_point <pyQCat.qubit.QubitPair.interaction_point>`\\"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict:1:<autosummary>:1
msgid ":py:obj:`accumulation_phase <pyQCat.qubit.QubitPair.accumulation_phase>`\\"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict:1:<autosummary>:1
msgid ":py:obj:`metadata <pyQCat.qubit.QubitPair.metadata>`\\"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict:4
msgid "Dict of two qubits information."
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict
#: pyQCat.qubit.qubit_pair.QubitPair.to_dict
msgid "Return type"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict:7
msgid ":py:class:`~pyQCat.qubit.qubit_pair.json.QubitPair`"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict
#: pyQCat.qubit.qubit_pair.QubitPair.to_dict
msgid "Returns"
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_dict:8
msgid "`QubitPair` object."
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_file:4
msgid "From file path name."
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.from_file:7
msgid "From file type, support `yaml` and `json`."
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.to_dict:3
msgid "Target data."
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.to_file:4
msgid "Export file path, default `conf/bit_data'."
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.to_file:7
msgid "Export file type, support `yaml` and `json`."
msgstr ""

#: of pyQCat.qubit.qubit_pair.QubitPair.save_database:1
msgid "Save database hook. Push the information to database."
msgstr ""

