﻿pyQCat.analysis.library.T1SpectrumAnalysis
==========================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: T1SpectrumAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~T1SpectrumAnalysis.__init__
      ~T1SpectrumAnalysis.from_sub_analysis
      ~T1SpectrumAnalysis.run_analysis
      ~T1SpectrumAnalysis.set_options
      ~T1SpectrumAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~T1SpectrumAnalysis.analysis_datas
      ~T1SpectrumAnalysis.data_filter
      ~T1SpectrumAnalysis.drawer
      ~T1SpectrumAnalysis.experiment_data
      ~T1SpectrumAnalysis.has_child
      ~T1SpectrumAnalysis.options
      ~T1SpectrumAnalysis.quality
      ~T1SpectrumAnalysis.results
   
   