<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>572</width>
    <height>625</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Revert Bits</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item row="0" column="0">
     <widget class="QGroupBox" name="groupBox_2">
      <property name="title">
       <string>Revet Qubit/Coupler</string>
      </property>
      <layout class="QGridLayout" name="gridLayout_2" rowstretch="1,5,1" rowminimumheight="1,5,1">
       <item row="0" column="0">
        <widget class="QWidget" name="widget_5" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="5,0">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QWidget" name="widget_12" native="true">
            <layout class="QVBoxLayout" name="verticalLayout_4">
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QWidget" name="widget_6" native="true">
               <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1,3,3">
                <item>
                 <widget class="QLabel" name="label_4">
                  <property name="text">
                   <string>time node</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_6">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>58</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QDateTimeEdit" name="TimeNodeText">
                  <property name="minimumDateTime">
                   <datetime>
                    <hour>0</hour>
                    <minute>0</minute>
                    <second>0</second>
                    <year>2020</year>
                    <month>1</month>
                    <day>1</day>
                   </datetime>
                  </property>
                  <property name="currentSection">
                   <enum>QDateTimeEdit::Section::YearSection</enum>
                  </property>
                  <property name="displayFormat">
                   <string>yyyy-M-d HH:mm:ss</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_4">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>91</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_14" native="true">
            <layout class="QVBoxLayout" name="verticalLayout_5">
             <item>
              <spacer name="verticalSpacer_3">
               <property name="orientation">
                <enum>Qt::Orientation::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>15</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="queryRevertButton">
               <property name="text">
                <string>query</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_4">
               <property name="orientation">
                <enum>Qt::Orientation::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>15</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QTableViewRevertWidget" name="tableRevertView"/>
       </item>
       <item row="2" column="0">
        <widget class="QWidget" name="widget" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>451</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="RevetButton">
            <property name="text">
             <string>revert to this time</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>450</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="actionCreatSpace">
   <property name="text">
    <string>Creat Space</string>
   </property>
  </action>
  <action name="actionRefresh">
   <property name="text">
    <string>refresh</string>
   </property>
  </action>
  <action name="actionCopySpace">
   <property name="text">
    <string>Copy Space</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QTableViewRevertWidget</class>
   <extends>QTableView</extends>
   <header>.widgets.component.table_view_revert</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>queryRevertButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>query_revert_bits()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>962</x>
     <y>105</y>
    </hint>
    <hint type="destinationlabel">
     <x>513</x>
     <y>323</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>RevetButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>revert_bits()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>286</x>
     <y>555</y>
    </hint>
    <hint type="destinationlabel">
     <x>285</x>
     <y>312</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>query_revert_bits()</slot>
  <slot>revert_bits()</slot>
 </slots>
</ui>
