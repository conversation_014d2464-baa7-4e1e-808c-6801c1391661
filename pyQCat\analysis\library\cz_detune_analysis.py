# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/06/11
# __author:       <PERSON><PERSON><PERSON>

import math

import matplotlib.pyplot as plt
import numpy as np
from scipy.interpolate import interp1d
from scipy.signal import find_peaks

from pyQCat.analysis.curve_analysis import CurveAnalysis
from pyQCat.analysis.fit.fit_models import lorentzian
from pyQCat.analysis.quality.base_quality import BaseQuality, QualityDescribe
from pyQCat.analysis.specification import CurveAnalysisData, FitModel, FitOptions
from pyQCat.parameters import analysis_options_wrapper
from pyQCat.structures import Options, QDict


@analysis_options_wrapper()
class SweepDetuneAnalysis(CurveAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Options:
            x_label (str): x_label mark.

        """
        options = super()._default_options()

        options.merge_y_data = True
        options.fit_model = FitModel(fit_func=lorentzian)
        options.result_parameters = ["detune1", "detune2"]
        options.data_key = ["P11"]

        return options

    def _create_analysis_data(self) -> QDict:
        """Create Analysis data provided for detailed analyze.

        Returns:
            A QDict object, key represents data type and value is
            CurveAnalysisData object.
        """
        analysis_data_dict = QDict()
        data_key = list(
            self.options.get("data_key", None) or self._experiment_data.y_data.keys()
        )
        for key in data_key:
            if key in self.experiment_data.y_data:
                x_data = np.copy(self._experiment_data.x_data)
                if abs(x_data[0]) > 1:
                    x_data = [x / 1000 for x in x_data]
                y_data = np.copy(self.experiment_data.y_data.get(key))
                detune_freq = self.experiment_data.metadata.process_meta.get("detune_freq")
                adapter_detune_freq = self.experiment_data.metadata.process_meta.get("adapter_detune_freq")

                new_x, new_y, new_d, new_ad = [], [], [], []
                for _x, _y, _d, _ad in zip(x_data, y_data, detune_freq, adapter_detune_freq):
                    if _x not in new_x:
                        new_x.append(_x)
                        new_y.append(_y)
                        new_d.append(_d)
                        new_ad.append(_ad)
                self.experiment_data.metadata.process_meta["detune_freq"] = new_d
                self.experiment_data.metadata.process_meta["adapter_detune_freq"] = new_ad
                x_data = new_x
                y_data = new_y

                max_index = np.argmax(y_data)
                right_index = len(x_data) - max_index
                if max_index < right_index:
                    x_data = x_data[:max_index * 2]
                    y_data = y_data[:max_index * 2]
                else:
                    x_data = x_data[-(right_index * 2):]
                    y_data = y_data[-(right_index * 2):]

                analysis_data = CurveAnalysisData(
                    x=np.array(x_data),
                    y=np.array(y_data),
                )
                analysis_data_dict[key] = analysis_data
        return analysis_data_dict

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ):
        """Guess initial fit parameters.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        x = data.x
        y = data.y

        x_step = round(abs(x[1] - x[0]), 6)

        y_mean = np.mean(y)
        y_max = np.max(y)
        height = (y_max - y_mean) / 5 + y_mean

        peaks, properties = find_peaks(y, height=height, width=x_step / 2)

        if peaks.size != 0:
            peak_value_list = properties.get("peak_heights")
            max_index = np.argmax(peak_value_list)
        else:
            peaks = [np.argmax(y)]
            max_index = 0

        b = x[peaks[max_index]]

        # Set the initial values for each fitted model specifically.
        fit_opt.p0.set_if_empty(A=0.01, offset=y_mean, f0=b, kappa=0.01)
        fit_opt_list = [fit_opt]

        return fit_opt_list

    def _extract_result(self, data_key: str):
        analysis_data = self.analysis_datas.get(data_key)
        if abs(analysis_data.x[0]) > 1:
            analysis_data.x *= 1000

        popt = analysis_data.fit_data.popt
        popt_keys = analysis_data.fit_data.popt_keys
        f0 = popt[popt_keys.index("f0")]

        if abs(f0) > 1:
            self.results.detune1.value = f0 * 1000
            detune_freq = self.experiment_data.metadata.process_meta.get("detune_freq")
            adapter_detune_freq = self.experiment_data.metadata.process_meta.get("adapter_detune_freq")
            f = interp1d(detune_freq, adapter_detune_freq, kind="cubic")
            self.results.detune2.value = float(f(self.results.detune1.value))


@analysis_options_wrapper()
class SweepDetunePurityAnalysis(CurveAnalysis):
    """"""


@analysis_options_wrapper()
class SweepQCAndDetuneAnalysis(CurveAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Options:
            index_policy (str): Select fit target index policy.
            interaction_location (int): Select interaction point number.
        """
        options = super()._default_options()
        options.subplots = (1, 1)
        options.y_label = "Detune Z Amp (V)"
        options.x_label = "QC Z Amp (v)"
        options.p_all_list = []
        options.pcolormesh_options = {
            "shading": "nearest",
            "cmap": plt.cm.get_cmap("viridis"),
        }
        options.threshold = 10
        options.result_parameters = ["detune1", "detune2"]

        return options

    def _check_y_label(self):
        return self.options.y_label

    def _extract_result(self, data_key: str):

        px, py = [], []
        x_arr = self.experiment_data.x_data
        phase_list = self.experiment_data.y_data.get("ac_scan")
        is_pass_list = self.experiment_data.y_data.get("is_pass")
        for idx, phase in enumerate(phase_list):
            if is_pass_list[idx]:
                px.append(x_arr[idx])
                py.append(phase)

        if len(px) < self.options.threshold:
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.perfect)
            if len(px) < 31:
                px.extend([px[-1] for _ in range(31 - len(px))])
                py.extend([py[-1] for _ in range(31 - len(py))])
            elif len(px) > 31:
                px = px[:31]
                py = py[:31]

        self.results.detune1.value = px
        self.results.detune2.value = py
        self.results.detune1.extra.update({'out_flag': False})
        self.results.detune2.extra.update({'out_flag': False})

    def _visualization(self):
        # super()._visualization()
        self.drawer.set_options(title=self._description())

        x_arr = self.experiment_data.metadata.process_meta.get("x_data")
        if x_arr:
            self.experiment_data.metadata.process_meta.pop("x_data")
        else:
            x_arr = self.experiment_data.x_data

        if self.has_child is True:

            detune2_list = self.experiment_data.metadata.process_meta.get("y_data")

            if detune2_list:
                self.experiment_data.metadata.process_meta.pop("y_data")
            else:
                detune2_list = self.experiment_data.metadata.process_meta.get("child_scan_freq")
                # detune2_list = self.experiment_data.child_data(index=0).x_data

            phase_arr = []
            for i, _ in enumerate(x_arr):
                child_data = self.experiment_data.child_data(index=i)
                phase_arr.append(child_data.y_data["phase"][0])

            z_arr = np.array(phase_arr).T
            self.drawer.draw_color_map(
                x_arr, detune2_list, z_arr,
                ax_index=0,
                **self.options.pcolormesh_options
            )

            if abs(self.results.detune1.value[0]) > 1 or (
                    abs(self.results.detune1.value[0]) < 1
                    and abs(self.results.detune2.value[0]) < 1
            ):
                axis = self.drawer._get_axis(0)
                # axis.contour(px, py, z_arr, [3.14], alpha=0.8, colors='black')
                axis.scatter(self.results.detune1.value, self.results.detune2.value, c='red', marker="*")
        self.drawer.format_canvas()


@analysis_options_wrapper()
class SweepDetuneComAnalysis(CurveAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        options:
            select_key (str): Select SwapOnce Pxx, mark label process width.
            p_all_list (list): SwapOnce all P array, to plot depth.

        """
        options = super()._default_options()

        options.x_label = "Detune1 (V)"
        options.y_label = "Detune2 (V)"
        options.p_all_list = []
        options.goal_width = 50
        options.var_limit = 1

        options.pcolormesh_options = {
            "shading": "nearest",
            "cmap": plt.cm.get_cmap("viridis"),
        }

        return options

    def _prepare_subplot(self):
        """Prepare one more canvas axis."""
        sub_title = []
        y_labels = []
        p_all_list = []

        if self.has_child is True:
            child_data = self.experiment_data.child_data(index=0)
            for p_label in child_data.y_data.keys():
                p_all_list.append([])
                if not isinstance(self.options.y_label, list):
                    y_labels.append(self.options.y_label)
                sub_title.append(f"Swap Depth {p_label}")

        if not y_labels:
            y_labels = self.options.y_label

        length = len(y_labels)
        row = math.ceil(length / 2)
        subplots = (row, 2)

        diff = subplots[0] * subplots[1] - length
        if diff > 0:
            y_labels.extend([""] * diff)
            sub_title.extend([""] * diff)

        self.set_options(
            y_label=y_labels,
            subplots=subplots,
            sub_title=sub_title,
            p_all_list=p_all_list,
            figsize=(12, 4 * (len(y_labels) // 2))
        )

    def _visualization(self):
        """Swap plot depth."""
        super()._visualization()

        base_ax_index = len(self.experiment_data.y_data.keys())

        if self.has_child is True:
            p_all_list = self.options.p_all_list

            x_arr = self.experiment_data.metadata.process_meta.get("x_data")

            if x_arr:
                self.experiment_data.metadata.process_meta.pop("x_data")
            else:
                x_arr = self.experiment_data.x_data

            y_arr = self.experiment_data.metadata.process_meta.get("y_data")
            if y_arr:
                self.experiment_data.metadata.process_meta.pop("y_data")

            for i, _ in enumerate(x_arr):
                child_data = self.experiment_data.child_data(index=i)
                if y_arr is None:
                    y_arr = child_data.x_data

                for j, tub in enumerate(child_data.y_data.items()):
                    p_label, p_value = tub
                    p_all_list[j].append(p_value)

            for i, new_p_arr in enumerate(p_all_list):
                ax_index = base_ax_index + i
                self.drawer.draw_color_map(
                    x_arr,
                    y_arr,
                    np.array(new_p_arr).T,
                    ax_index=ax_index,
                    **self.options.pcolormesh_options,
                )

    def run_analysis(self):
        """Run analysis on experiment data."""
        super().run_analysis()
