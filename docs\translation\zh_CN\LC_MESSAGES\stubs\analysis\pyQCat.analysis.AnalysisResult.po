# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.AnalysisResult.rst:2
msgid "pyQCat.analysis.AnalysisResult"
msgstr ""

#: of pyQCat.analysis.specification.AnalysisResult:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.analysis.specification.AnalysisResult:1
msgid "Dataclass for experiment analysis results"
msgstr ""

#: of pyQCat.analysis.specification.AnalysisResult:5
msgid ""
"Human-readable parameter name shown in the analysis result and in the "
"figure."
msgstr ""

#: of pyQCat.analysis.specification.AnalysisResult
msgid "type"
msgstr ""

#: of pyQCat.analysis.specification.AnalysisResult:7
msgid "str"
msgstr ""

#: of pyQCat.analysis.specification.AnalysisResult:11
msgid "Optional. Physical unit of this parameter if applicable."
msgstr ""

#: of pyQCat.analysis.specification.AnalysisResult:13
msgid "Optional[str]"
msgstr ""

#: of pyQCat.analysis.specification.AnalysisResult:17
msgid "Optional.The value of result data."
msgstr ""

#: of pyQCat.analysis.specification.AnalysisResult:19
msgid "Any"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.AnalysisResult.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.AnalysisResult.rst:21:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.AnalysisResult.__init__>`\\ "
"\\(\\[name\\, unit\\, value\\, extra\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.AnalysisResult.rst:23
msgid "Attributes"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.AnalysisResult.rst:29:<autosummary>:1
msgid ":py:obj:`name <pyQCat.analysis.AnalysisResult.name>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.AnalysisResult.rst:29:<autosummary>:1
msgid ":py:obj:`unit <pyQCat.analysis.AnalysisResult.unit>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.AnalysisResult.rst:29:<autosummary>:1
msgid ":py:obj:`value <pyQCat.analysis.AnalysisResult.value>`\\"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.AnalysisResult.rst:29:<autosummary>:1
msgid ":py:obj:`extra <pyQCat.analysis.AnalysisResult.extra>`\\"
msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.analysis.AnalysisResult.__init__>`\\ "
#~ "\\(\\[name\\, unit\\, value\\, extra\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`name <pyQCat.analysis.AnalysisResult.name>`\\"
#~ msgstr ""

#~ msgid ":obj:`unit <pyQCat.analysis.AnalysisResult.unit>`\\"
#~ msgstr ""

#~ msgid ":obj:`value <pyQCat.analysis.AnalysisResult.value>`\\"
#~ msgstr ""

#~ msgid ":obj:`extra <pyQCat.analysis.AnalysisResult.extra>`\\"
#~ msgstr ""

