# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/08/09
# __author:       <PERSON> Fang

"""
Rough test XY Crosstalk base experiment.
"""

from copy import deepcopy

import numpy as np

from ....analysis import XYCrosstalkOnceAnalysis
from ....errors import Experiment<PERSON>ieldError, ExperimentOptionsError
from ....log import pyqlog
from ....pulse.pulse_function import half_pi_pulse, pi_pulse, zero_pulse
from ....pulse.pulse_lib import Constant
from ....structures import MetaData, Options
from ....types import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class XYCrosstalkOnce(TopExperiment):
    """XY Crosstalk Once experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options.

        Experiment Options:
            target_name (str): Target bit name.

        """
        options = super()._default_experiment_options()

        options.set_validator("target_name", str)
        options.set_validator("theta_type", ["Xpi", "Xpi/2"])
        options.set_validator("amp_coe", float)
        options.set_validator("b_amp", float)
        options.set_validator("b_drive_power", float)
        options.set_validator("b_freq", float)
        options.set_validator("x_num", int)
        options.set_validator("x_width", float)
        options.set_validator("delay", float)
        options.set_validator("bounds", dict)
        options.set_validator("sweep_name", ["phase", "amp_coe"])
        options.set_validator("sweep_list", list)
        options.set_validator("phase", float)

        options.target_name = None
        options.theta_type = "Xpi"

        # which maybe sweep parameters.
        options.amp_coe = 0.1
        options.phase = np.pi
        options.b_amp = 0.8
        options.b_drive_power = None
        options.b_freq = None
        options.x_num = 1
        options.x_width = 30.0
        options.delay = 10.0
        options.sweep_name = "phase"
        options.sweep_list = np.linspace(0, 2 * np.pi, 81).tolist()
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default run options.

        Run Options:
            target_qubit (Qubit): Target Qubit object.
            bias_qubit (Qubit): Crosstalk Bias Qubit object.

        """
        options = super()._default_run_options()
        options.target_qubit = None
        options.bias_qubit = None

        options.support_context = [StandardContext.CM, StandardContext.URM]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options.

        Analysis Options:
            is_plot (bool): Plot or not analysis fit photo.
            data_key (List[str]): List of mark select data.

        """
        options = super()._default_analysis_options()
        options.set_validator("phase_bounds", dict)
        options.set_validator("amp_coe_bounds", dict)
        options.set_validator("phase_bias", float)  #

        options.x_label = "phase"
        options.is_plot = True
        options.data_key = ["P1"]
        options.theta_type = None
        options.is_equal = False
        options.tao = 30
        options.delta = -200
        options.sweep_name = "phase"

        options.phase = None
        options.phase_bias = np.pi
        options.power_coe = None

        options.phase_bounds = {
            "coe": [0.01, 1],
            "offset": [0, 1],
            "omega1": [0, 40],
            "omega2": [2, 4],
            "x0": [0, 2 * np.pi],
        }

        options.amp_coe_bounds = {
            "coe": [0.01, 1],
            "offset": [0, 1],
            "rabi": [0, 40],
            "omega2": [2, 4],
        }

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "target": self.run_options.target_qubit.name,
            "bias": self.run_options.bias_qubit.name,
            "amp_coe": self.experiment_options.amp_coe,
            "b_amp": self.experiment_options.b_amp,
            "b_drive_power": self.experiment_options.b_drive_power,
            "b_freq": self.experiment_options.b_freq,
            "x_num": self.experiment_options.x_num,
            "x_width": self.experiment_options.x_width,
            "delay": self.experiment_options.delay,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        target_name = self.experiment_options.target_name
        b_drive_power = self.experiment_options.b_drive_power
        b_freq = self.experiment_options.b_freq
        theta_type = self.experiment_options.theta_type
        x_width = self.experiment_options.x_width
        x_num = self.experiment_options.x_num

        qubit_map = {qubit.name: qubit for qubit in self.qubits}
        q_names = list(qubit_map.keys())

        if len(self.qubits) < 2:
            raise ExperimentFieldError(
                self,
                f"XY Crosstalk at least two Qubit ! "
                f"But the qubits length {len(self.qubits)} !",
            )
        if target_name not in q_names:
            raise ExperimentOptionsError(
                self,
                f"Set target {target_name} not in all bit names!",
                "target_name",
                target_name,
            )

        target_qubit = qubit_map.get(target_name)
        qubit_map.pop(target_name)
        bias_qubit = list(qubit_map.values())[0]
        t_freq = target_qubit.drive_freq
        t_offset = target_qubit.XYwave.offset
        t12_freq = target_qubit.f12_options.drive_freq
        if t12_freq is None:
            t12_freq = round(t_freq + target_qubit.anharmonicity, 3)
            pyqlog.warning(
                f"{target_name} f12 is None, "
                f"according to anharmonicity calculate f12: {t12_freq}"
            )
        if b_freq is None:
            b_freq = bias_qubit.drive_freq
        if b_drive_power is None:
            b_drive_power = bias_qubit.drive_power
        if b_drive_power < -40.0 or b_drive_power > -10.0:
            raise ExperimentOptionsError(
                self,
                f"Set bias drive_power {b_drive_power}, out of [-40.0, -10.0]",
                "b_drive_power",
                b_drive_power,
            )

        multi_readout_channels = [target_qubit.readout_channel]

        # Get target bit discriminator.
        if self.discriminator:
            data_type = "I_Q"
            if isinstance(self.discriminator, list):
                dcm_list = self.discriminator
            else:
                dcm_list = [self.discriminator]
            new_dcm = None
            for dcm in dcm_list:
                if dcm.name == target_name:
                    new_dcm = dcm
                    break
            pyqlog.info(f"Add {new_dcm.name} discriminator: {new_dcm}")
            self.discriminator = new_dcm
        else:
            data_type = "amp_phase"

        self.set_experiment_options(
            multi_readout_channels=multi_readout_channels,
            data_type=data_type,
            b_drive_power=b_drive_power,
            b_freq=b_freq,
        )
        if t_freq == b_freq:
            is_equal = True
        else:
            is_equal = False

        if theta_type == "Xpi" and is_equal:
            delta = None
        elif theta_type == "Xpi" and not is_equal:
            delta = abs(b_freq - t12_freq)
        else:
            delta = abs(b_freq - t_freq)

        if self.experiment_options.sweep_name == "phase":
            x_label = "phase"
            sweep_name = "phase"
        else:
            x_label = "amp coe"
            sweep_name = "amp_coe"

        self.set_analysis_options(
            theta_type=theta_type,
            is_equal=is_equal,
            delta=delta,
            tao=(x_width + 2 * t_offset) * x_num,
            x_label=x_label,
            sweep_name=sweep_name,
            phase=self.experiment_options.phase,
            power_coe=10 ** ((b_drive_power - target_qubit.drive_power) / 20),
        )
        self.set_run_options(
            target_qubit=target_qubit,
            bias_qubit=bias_qubit,
            measure_qubits=[target_qubit],
            x_data=self.experiment_options.sweep_list,
            analysis_class=XYCrosstalkOnceAnalysis,
        )

    @staticmethod
    def set_xy_pulses(self):
        """Set experiment XY pulses."""
        theta_type = self.experiment_options.theta_type
        amp_coe = self.experiment_options.amp_coe
        b_amp = self.experiment_options.b_amp
        b_freq = self.experiment_options.b_freq
        x_num = self.experiment_options.x_num
        x_width = self.experiment_options.x_width
        delay = self.experiment_options.delay
        b_drive_power = self.experiment_options.b_drive_power
        target_qubit = self.run_options.target_qubit
        bias_qubit = self.run_options.bias_qubit

        power_coe = 10 ** ((b_drive_power - target_qubit.drive_power) / 20)
        t_amp = amp_coe * b_amp * power_coe

        t_xy_lo = round(target_qubit.drive_freq - target_qubit.XYwave.baseband_freq, 3)
        b_xy_lo = round(bias_qubit.drive_freq - bias_qubit.XYwave.baseband_freq, 3)
        t_bs_freq = round(b_freq - t_xy_lo, 3)
        b_bs_freq = round(b_freq - b_xy_lo, 3)

        pyqlog.info(
            f"{target_qubit.name}{bias_qubit.name} bias frequency: {b_freq}, power_coe: {power_coe}"
        )
        pyqlog.info(
            f"bias {bias_qubit.name} xy_lo gap: {b_xy_lo}, baseband_freq: {b_bs_freq}"
        )
        pyqlog.info(
            f"target {target_qubit.name} xy_lo gap: {t_xy_lo}, baseband_freq: {t_bs_freq}"
        )

        if theta_type == "Xpi":
            t_pi_x_pulse = pi_pulse(target_qubit)
        else:
            t_pi_x_pulse = half_pi_pulse(target_qubit)
        b_offset_pulse = zero_pulse(target_qubit, name="XY")
        b_x_pulse = pi_pulse(bias_qubit)
        delay_pulse = Constant(delay, 0, name="XY")

        b_pulse_once = (
            deepcopy(b_offset_pulse)()
            + deepcopy(b_x_pulse)(amp=b_amp, time=x_width, freq=b_bs_freq * 1e-3)
            * x_num
            + deepcopy(delay_pulse)()
            + deepcopy(b_offset_pulse)()
        )

        t_pulse_list = []
        b_pulse_list = []

        sweep_list = self.experiment_options.sweep_list
        if self.experiment_options.sweep_name == "phase":
            for phase in sweep_list:
                t_pulse_s = deepcopy(t_pi_x_pulse)
                t_pulse_h = (
                    deepcopy(b_x_pulse)(
                        amp=t_amp, time=x_width, phase=phase, freq=t_bs_freq * 1e-3
                    )
                    * x_num
                )
                t_pulse_d = deepcopy(delay_pulse)
                t_pulse_e = deepcopy(t_pi_x_pulse)
                t_pulse_once = t_pulse_s() + t_pulse_h + t_pulse_d() + t_pulse_e()
                t_pulse_list.append(t_pulse_once)
                b_pulse_list.append(deepcopy(b_pulse_once))
        else:
            phase = self.experiment_options.phase
            for amp_coe in sweep_list:
                t_amp = amp_coe * b_amp * power_coe
                t_pulse_s = deepcopy(t_pi_x_pulse)
                t_pulse_h = (
                    deepcopy(b_x_pulse)(
                        amp=t_amp, time=x_width, phase=phase, freq=t_bs_freq * 1e-3
                    )
                    * x_num
                )
                t_pulse_d = deepcopy(delay_pulse)
                t_pulse_e = deepcopy(t_pi_x_pulse)
                t_pulse_once = t_pulse_s() + t_pulse_h + t_pulse_d() + t_pulse_e()
                t_pulse_list.append(t_pulse_once)
                b_pulse_list.append(deepcopy(b_pulse_once))

        self.play_pulse("XY", target_qubit, t_pulse_list)
        self.play_pulse("XY", bias_qubit, b_pulse_list)

    @staticmethod
    def update_instrument(self):
        """Update instrument."""
        b_drive_power = self.experiment_options.b_drive_power
        target_qubit = self.run_options.target_qubit
        bias_qubit = self.run_options.bias_qubit

        self.inst.set_power("XY_control", bias_qubit.xy_channel, b_drive_power)
        self._bind_probe_inst(target_qubit)
