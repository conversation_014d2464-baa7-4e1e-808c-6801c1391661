# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-07 08:58+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:2
msgid "pyQCat.analysis.algorithms.IQdiscriminator"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator:1
msgid ""
"IQdiscriminator is used to diagnose the probability of P0 and P1 for the "
"I and Q data collected by the experiment. Usually, the object is saved in"
" the database or local bin file in binary form. Therefore, when "
"obtaining, first query from the database, if not found, read from the "
"local configuration file directory."
msgstr ""
"IQdiscriminator IQ 数据分类器，一般称之为判据。"
"通常，通过 SingleShot 实验得到的两组 IQ 数据，训练出模型，"
"后续相关实验得到的 IQ 经过此分类器，输出处于 0/1 态的概率。"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.__init__:1
msgid "Create a new IQdiscriminator."
msgstr "IQdiscriminator 初始化方法。"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.__init__
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_radius
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._inside_k_recommend
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_probability
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.train
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.__init__:4
msgid "Demodulated I data."
msgstr "I 数据列表,一般为 `[I0, I1]`."

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.__init__:7
msgid "Demodulated Q data."
msgstr "Q 数据列表，一般为 `[Q0, Q1]`。"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.__init__:10
msgid "Number of taxonomic clusters."
msgstr "聚类的数量。"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.__init__:13
msgid "Clustering algorithm, KMeans or GMM."
msgstr "聚类算法关键字，可支持 `KMeans` 和 `GMM`。"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.analysis.algorithms.IQdiscriminator.__init__>`\\ \\(I\\_list\\, "
"Q\\_list\\[\\, n\\_clusters\\, method\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
msgid ""
":py:obj:`calculate_radius "
"<pyQCat.analysis.algorithms.IQdiscriminator.calculate_radius>`\\ \\(x\\, "
"y\\, center\\_x\\, center\\_y\\[\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
msgid "Calculate radius."
msgstr "计算 IQ 圆斑半径。"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
msgid ""
":py:obj:`get_k_recommend "
"<pyQCat.analysis.algorithms.IQdiscriminator.get_k_recommend>`\\ "
"\\(name\\, power\\[\\, dirs\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend:1
msgid "Get k_recommend, by `self.I_list` and `self.I_list`."
msgstr "获取输入 IQ 数据的聚类数量的推荐值 k_recommend。"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
msgid ""
":py:obj:`get_probability "
"<pyQCat.analysis.algorithms.IQdiscriminator.get_probability>`\\ \\(i\\, "
"q\\[\\, screen\\_flag\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_probability:1
msgid "Calculate probability."
msgstr "计算 0/1 概率值。"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
msgid ""
":py:obj:`plot <pyQCat.analysis.algorithms.IQdiscriminator.plot>`\\ "
"\\(\\[dirs\\, name\\, is\\_save\\, bit\\, power\\, freq\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.plot:1
msgid "Plot IQ round spot photos."
msgstr "IQ 圆斑图绘制接口。"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
msgid ""
":py:obj:`predict <pyQCat.analysis.algorithms.IQdiscriminator.predict>`\\ "
"\\(i\\, q\\[\\, screen\\_flag\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict:1
msgid "Predict state label."
msgstr "预测 IQ 数据 0/1 标签。"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
msgid ""
":py:obj:`screen_iq "
"<pyQCat.analysis.algorithms.IQdiscriminator.screen_iq>`\\ \\(i\\, q\\)"
msgstr ""

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq:1
msgid "Screen i, q data, within model radius."
msgstr "输入一组 IQ 数据，通过 IQdiscriminator 的模型，筛选出其中在圆斑半径中的 IQ 数据。"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
msgid ""
":py:obj:`train <pyQCat.analysis.algorithms.IQdiscriminator.train>`\\ "
"\\(\\[n\\_multiple\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:28:<autosummary>:1
#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.train:1
msgid "Train model."
msgstr "模型训练接口。"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.IQdiscriminator.rst:30
msgid "Attributes"
msgstr ""

#: of pyQCat.analysis.algorithms.IQdiscriminator.centers:1:<autosummary>:1
msgid ":py:obj:`centers <pyQCat.analysis.algorithms.IQdiscriminator.centers>`\\"
msgstr ""

#: of pyQCat.analysis.algorithms.IQdiscriminator.centers:1
#: pyQCat.analysis.algorithms.IQdiscriminator.centers:1:<autosummary>:1
msgid "Model centers."
msgstr "模型中心点坐标。"

#: of pyQCat.analysis.algorithms.IQdiscriminator.centers:1:<autosummary>:1
msgid ":py:obj:`fidelity <pyQCat.analysis.algorithms.IQdiscriminator.fidelity>`\\"
msgstr ""

#: of pyQCat.analysis.algorithms.IQdiscriminator.centers:1:<autosummary>:1
#: pyQCat.analysis.algorithms.IQdiscriminator.fidelity:1
msgid "Model fidelity."
msgstr "0/1 分辨准确率。"

#: of pyQCat.analysis.algorithms.IQdiscriminator.centers:1:<autosummary>:1
msgid ""
":py:obj:`k_recommend "
"<pyQCat.analysis.algorithms.IQdiscriminator.k_recommend>`\\"
msgstr ""

#: of pyQCat.analysis.algorithms.IQdiscriminator.centers:1:<autosummary>:1
#: pyQCat.analysis.algorithms.IQdiscriminator.k_recommend:1
msgid "Model ideal clusters."
msgstr "模型聚类中心理想数量。"

#: of pyQCat.analysis.algorithms.IQdiscriminator.centers:1:<autosummary>:1
msgid ":py:obj:`outlier <pyQCat.analysis.algorithms.IQdiscriminator.outlier>`\\"
msgstr ""

#: of pyQCat.analysis.algorithms.IQdiscriminator.centers:1:<autosummary>:1
#: pyQCat.analysis.algorithms.IQdiscriminator.outlier:1
msgid "Outlier value."
msgstr "outlier 值"

#: of pyQCat.analysis.algorithms.IQdiscriminator.centers:1:<autosummary>:1
msgid ""
":py:obj:`probability "
"<pyQCat.analysis.algorithms.IQdiscriminator.probability>`\\"
msgstr ""

#: of pyQCat.analysis.algorithms.IQdiscriminator.centers:1:<autosummary>:1
#: pyQCat.analysis.algorithms.IQdiscriminator.probability:1
msgid "Predict probability."
msgstr "预测 0/1 概率值。"

#: of pyQCat.analysis.algorithms.IQdiscriminator.centers:1:<autosummary>:1
msgid ":py:obj:`radius <pyQCat.analysis.algorithms.IQdiscriminator.radius>`\\"
msgstr ""

#: of pyQCat.analysis.algorithms.IQdiscriminator.centers:1:<autosummary>:1
#: pyQCat.analysis.algorithms.IQdiscriminator.radius:1
msgid "Model radius value."
msgstr "分离器模型半径。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:1
msgid ""
"Calculate radius. The radius calculated from the plane coordinate system "
"to the center."
msgstr ""
"计算半径方法。平面坐标系中，计算每个点到中心的距离，"
"再求这些距离的平均值、标准差，半径为标准差 3 倍，再加上距离平均值，"
"默认倍数 3.0， 可设置。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:5
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:7
msgid "one dimension array"
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:9
msgid "value of center's x direction"
msgstr "中心点 x 轴坐标值。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:12
msgid "value of center's y direction"
msgstr "中心点 y 轴坐标值。"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_radius:6
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:15
msgid "multiple of standard deviation"
msgstr "标准差倍数，默认3.0"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq
msgid "Returns"
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius:18
msgid "radius value"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.calculate_radius
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_radius:1
msgid "Get model radius, and assign to self._radius."
msgstr "获取模型半径，并且赋值给 `self._radius` "

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_radius:4
msgid "label array, one dimension array"
msgstr "0/1 标签的一维数组"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr:1
msgid "Get target data bool index."
msgstr "获取目标数据的布尔索引。"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr:4
msgid "i data, need of filter, one dimension array."
msgstr "需要筛选的 I 数据。"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr:6
msgid "q data, need of filter, one dimension array."
msgstr "需要筛选的 Q 数据。"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr:8
msgid ""
"mark target data, iq: default value, means get iq data within model "
"radius; outlier: means get iq data without model radius."
msgstr "数据筛选模式，支持两种模式：`iq`, `outlier``iq` 代表筛选在模型半径内的数据。`outlier` 代表筛选在模型半径之外的数据。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._get_index_arr:13
msgid "target data bool index, one dimension array."
msgstr "目标数据的布尔索引数组，一维数组。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._screen_outlier:1
msgid ""
"Screen i_outlier, q_outlier data, without model radius, and assign to "
"self._iq_outlier. Calculate rate of outlier, update self._outlier value."
msgstr "筛选出不在模型半径中 IQ 数据，并计算出占比，结果为 `outlier` 。"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq:4
msgid "original i data, one dimension array"
msgstr "原始的 I 数据，一维数组。"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq:6
msgid "original q data, one dimension array"
msgstr "原始的 Q 数据，一维数组。"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.screen_iq:8
msgid "tuple of i, q"
msgstr ""

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._inside_k_recommend:1
msgid "Simple and easy get X data k recommend value."
msgstr "通过传入的原始数据，预测聚类数量 `k_recommend`。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator._inside_k_recommend:4
msgid "np.array(I, Q)"
msgstr "二维数据，I，Q 数据列拼接。"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.train:4
msgid "multiple of standard deviation, calculate model radius."
msgstr "标准差的倍数，用于计算模型半径。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_probability:4
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict:4
msgid "Input I data."
msgstr "输入 I 数据。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_probability:7
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict:7
msgid "Input Q data."
msgstr "输入 Q 数据。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_probability:10
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict:10
msgid "Screen iq or not."
msgstr "是否筛选出在模型半径中的 IQ, 控制键参数。"

#: of pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.predict:13
msgid "Label array."
msgstr "0/1 标签数组。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend:4
msgid "Qubit or Coupler name."
msgstr "Qubit 或 Coupler 名称，比如 `q0`, `c0` 。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend:7
msgid "Get IQ data, the readout power."
msgstr "采集 IQ 数据时读取频率。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend:10
msgid "Save plot figure path."
msgstr "保存绘图的目录，若为 None不保存，默认为 None。"

#: of
#: pyQCat.analysis.algorithms.iqprobability.IQdiscriminator.get_k_recommend:13
msgid "k_recommend"
msgstr ""

