# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/tools/pyQCat.tools.update_union_readout_data.rst:2
msgid "pyQCat.tools.update\\_union\\_readout\\_data"
msgstr ""

#: of pyQCat.tools.utilities.update_union_readout_data:1
msgid "Update union readout data by origin union readout data."
msgstr ""

#: of pyQCat.tools.utilities.update_union_readout_data
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.utilities.update_union_readout_data:4
msgid "List of Qubit objects."
msgstr ""

#: of pyQCat.tools.utilities.update_union_readout_data:10
msgid "Sample rate."
msgstr ""

#: of pyQCat.tools.utilities.update_union_readout_data:13
msgid "Calculate bit's amp or not."
msgstr ""

#: of pyQCat.tools.utilities.update_union_readout_data
msgid "Return type"
msgstr ""

#~ msgid "Returns"
#~ msgstr ""

