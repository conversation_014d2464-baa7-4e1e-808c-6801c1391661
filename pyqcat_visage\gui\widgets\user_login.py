# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/11/14
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

import os
import pickle
from collections import OrderedDict
from json import loads, dumps
from threading import Thread
from typing import TYPE_CHECKING

from PySide6.QtCore import Slot
from PySide6.QtWidgets import QMessageBox
from loguru import logger
from pyQCat.invoker import DataCenter, Invoker
from pyQCat import get_version, DEVICE

from pyqcat_visage.config import GUI_CONFIG
from pyqcat_visage.gui.tools.utilies import slot_catch_exception
from pyqcat_visage.gui.user_login_ui import Ui_MainWindow
from pyqcat_visage.gui.widgets.dialog.tips_dialog import TipsDialog
from pyqcat_visage.gui.widgets.title_window import TitleWindow
from pyqcat_visage.gui.widgets.context.table_view_cache_flag import QTableViewCacheFlag
from pyqcat_visage.gui.widgets.context.table_model_cache_flag import QTableModelCacheFlag
from pyqcat_visage.protocol import ExecuteOp
from pyqcat_visage.tool.utilies import wait_send_user_data

if TYPE_CHECKING:
    from pyqcat_visage.gui.main_window import VisageGUI


class UserLoginWindow(TitleWindow):

    def __init__(self, gui: 'VisageGUI', parent=None):
        super().__init__(parent)
        self.gui = gui
        self._ui = Ui_MainWindow()
        self._ui.setupUi(self)
        self._ui.stackedWidget.setCurrentIndex(0)
        self.group_list = ["normal"]

        # load his env
        # his_env = self.gui.backend.get_his_env()
        # if his_env.invoker_addr:
        #     addr = his_env.invoker_addr.split(':')
        #     ip = addr[1][2:]
        #     port = addr[-1]
        #     self._ui.ip_edit.setText(ip)
        #     self._ui.port_edit.setText(port)
        if os.path.exists(GUI_CONFIG.cache_addr):
            with open(GUI_CONFIG.cache_addr, "r") as f1:
                addr = f1.read()
            addr_dict = loads(addr) if addr else {}
            self._ui.ip_edit.setText(addr_dict.get("ip", ""))
            self._ui.port_edit.setText(addr_dict.get("port", ""))

        # load cache user
        self.user_map = {}
        if os.path.exists(GUI_CONFIG.cache_user_bin):
            with open(GUI_CONFIG.cache_user_bin, 'rb') as f:
                user_map_bin = f.read()

            user_map = pickle.loads(user_map_bin)
            users = list(user_map.keys())

            self._ui.user_name_box.addItems(users)
            self._ui.user_name_box.setCurrentIndex(len(users) - 1)
            self._ui.pwd_linedit_login.setText(user_map.get(users[0])[0])
            self._ui.cache_flag.setCurrentIndex(user_map.get(users[0])[-1])
            self.change_cache_flag(user_map.get(users[0])[-1])
            self.user_map = user_map

        self.cache_envs = []
        self.cache_flag_table_model = QTableModelCacheFlag(
            self.gui, self, self._ui.cacheFlagTableView
        )
        self._ui.cacheFlagTableView.setModel(self.cache_flag_table_model)

    def load_group_names(self):
        db = DataCenter()
        ret_data = db.query_group_names()
        print(ret_data)
        if ret_data.get("code") == 200:
            group_list = ret_data["data"]
            if "super" in group_list:
                group_list.remove("super")
            if group_list:
                self.group_list = group_list
        self._ui.GroupComboBox.clear()
        self._ui.GroupComboBox.addItems(self.group_list)

    def auto_login(self):
        if not self.test_connect():
            self._ui.stackedWidget.setCurrentIndex(0)
            self.show()
            return

        self.take_connect()

        self._load_cache_user()

        # if not self.login_to_system():
        self._ui.stackedWidget.setCurrentIndex(1)
        self.show()
        logger.log('FLOW', 'The system is starting, please wait...')
        return

        # logger.log('FLOW', 'The system is starting, please wait...')
        # self.gui.main_window.set_toolbar_state(False)

    @Slot()
    def create_account(self):
        username = self._ui.user_name_lineEdit_register.text()
        group = self._ui.GroupComboBox.currentText()
        password = self._ui.pwd_lineEdit_register.text()
        rep_pwd = self._ui.rpwd_lineEdit.text()
        email = self._ui.mail_lineEdit_register.text()
        if not group:
            QMessageBox.warning(self, "Fail", "pls select group first!")
            return
        response = self.gui.backend.register(username, password, rep_pwd, email, group)

        if response.get('code') == 200:
            QMessageBox().information(self, 'Success', f'Register {username} success!')
            self._ui.user_name_box.addItems([username])
            count = self._ui.user_name_box.count()
            self._ui.user_name_box.setCurrentIndex(count - 1)
            self._ui.pwd_linedit_login.setText(password)
            self.user_map.update({username: password})
            self._ui.stackedWidget.setCurrentIndex(1)
        else:
            QMessageBox().critical(self, 'Error', response.get('message'))

    @Slot()
    def test_connect(self):
        ip = self._ui.ip_edit.text()
        port = self._ui.port_edit.text()
        if ip and port:
            invoker_addr = f"http://{ip}:{port}"
            self._ui.state_label.setText('Wait..')
            self._ui.state_label.setStyleSheet("QLabel[objectName='state_label']{color: #6ddf6d}")
            self._ui.state_label.repaint()
            if self.gui.backend.test_connect(ip, port):
                self.gui.backend.invoker_addr = invoker_addr
                self._ui.state_label.setText('SUC')
                self._ui.state_label.setStyleSheet("QLabel[objectName='state_label']{color: green}")
                return True
            else:
                self._ui.state_label.setText('FAIL')
                self._ui.state_label.setStyleSheet("QLabel[objectName='state_label']{color: red}")

    @Slot()
    def take_connect(self):
        if self.test_connect():
            ip = self._ui.ip_edit.text()
            port = self._ui.port_edit.text()
            self.gui.backend.set_env(ip, port)
            self._ui.stackedWidget.setCurrentIndex(1)
            with open(GUI_CONFIG.cache_addr, "w+") as f:
                f.write(dumps({"ip": ip, "port": port}))
        else:
            QMessageBox().critical(self, 'Error', f'Connect Failed!')


    def login_to_child_single(self, username, cache_flag):
        # 重新登录需向子进程发送信号，以让子进程重新load_account和context
        self.gui.backend.init_execute_login(username, cache_flag)
        if not self.gui.backend.login_first_flag:
            self.gui.backend.init_execute_context()


    @slot_catch_exception()
    def login_to_system(self):
        is_remember = self._ui.is_remember_box.isChecked()
        username = self._ui.user_name_box.currentText()
        password = self._ui.pwd_linedit_login.text()
        cache_flag = int(self._ui.cache_flag.currentText() or 0)
        if not username:
            QMessageBox.warning(self, "Login Warning", "pls input username!")
            return False
        if not password:
            QMessageBox.warning(self, "Login Warning", "pls input password!")
            return False

        check_res = self.gui.backend.db.check_user_version(username, get_version(), DEVICE)
        if check_res.get("code") > 200:
            tip = TipsDialog()
            tip.set_tips(check_res.get("message"))
            ret = tip.exec()
            if int(ret) != 1:
                return False
        if check_res.get("code") >= 400:
            return False

        response = self.gui.backend.login(username, password, cache_flag)
        if response.get('code') == 200:
            self.gui.backend.user_state = True
            if is_remember:
                self._remember(username, password, cache_flag)
            self.gui.backend.context_builder.username = username
            self.gui.backend.cache_flag = cache_flag
            self.gui.backend.username = username
            self.gui.backend.init_config()
            # 登录后第一时间向子进程发送登录信号
            temp = Thread(target=self.login_to_child_single, kwargs=dict(username=username, cache_flag=cache_flag))
            temp.start()
            self.gui.init_visage()
            self.close_()
            self.gui.main_window.set_multi_thread_title()
            self.gui.main_window.show()

            return True
        else:
            # print("login res", response)
            self.handler_ret_data(response)

    @Slot()
    def show_create_account(self):
        self.load_group_names()
        self._ui.stackedWidget.setCurrentIndex(4)

    @Slot()
    def back_up_page(self):
        index = self._ui.stackedWidget.currentIndex()
        if index == 4:
            self._ui.stackedWidget.setCurrentIndex(1)
        else:
            self._ui.stackedWidget.setCurrentIndex(index - 1)

    @Slot(int)
    def choose_user(self, index):
        if index != -1 and self.user_map:
            username = self._ui.user_name_box.itemText(index)
            pwd_flag = self.user_map.get(username)
            self._ui.pwd_linedit_login.setText(pwd_flag[0])
            self._ui.cache_flag.setCurrentIndex(pwd_flag[-1])
            env_str = Invoker.show_accounts(username, pwd_flag[-1], True)
            self._ui.cache_flag.setToolTip(f"<pre>{env_str.replace(' cache_flag ', 'multi-window')}</pre>")


    @Slot(int)
    def change_cache_flag(self, index):
        if index != -1:
            username = self._ui.user_name_box.currentText()
            env_str = Invoker.show_accounts(username, index, True)
            self._ui.cache_flag.setToolTip(f"<pre>{env_str.replace(' cache_flag ', 'multi-window')}</pre>")


    def _remember(self, username: str, password: str, cache_flag: int = None):
        if not os.path.exists(GUI_CONFIG.cache_user_bin):
            user_map = OrderedDict()
            user_map[username] = [password, cache_flag]
        else:
            with open(GUI_CONFIG.cache_user_bin, 'rb') as f:
                user_map_bin = f.read()
            user_map = pickle.loads(user_map_bin)
            if username in user_map:
                user_map.pop(username)
            user_map[username] = [password, cache_flag]

        user_map_bin = pickle.dumps(user_map)
        with open(GUI_CONFIG.cache_user_bin, 'wb') as f:
            f.write(user_map_bin)
        self.user_map = user_map

    def _load_cache_user(self):
        if os.path.exists(GUI_CONFIG.cache_user_bin):
            with open(GUI_CONFIG.cache_user_bin, 'rb') as f:
                user_map_bin = f.read()
            user_map = pickle.loads(user_map_bin)
            if len(user_map) > 0:
                name, [pwd, cache_flag] = list(user_map.items())[-1]
                self._ui.user_name_box.setCurrentText(name)
                self._ui.pwd_linedit_login.setText(pwd)
                self._ui.cache_flag.setCurrentText(str(cache_flag))
                envs_str = Invoker.show_accounts(name, None, True)
                self._ui.user_name_box.setToolTip(f"<pre>{envs_str.replace(' cache_flag ', 'multi-window')}</pre>")


    @Slot()
    def forget_password_link(self):
        self._ui.user_name_lineEdit_find.setText(self._ui.user_name_box.currentText())
        self._ui.stackedWidget.setCurrentIndex(3)

    @Slot()
    def find_account(self):
        username = self._ui.user_name_lineEdit_find.text()
        if not username:
            QMessageBox().critical(self, 'Error', f'username is empty!')
            return False

        pre_pwd = self._ui.pre_pwd_edit.text()
        new_pwd = self._ui.new_pwd_edit.text()
        # email = self._ui.mail_lineEdit_find.text()

        res = self.gui.backend.find_account(username, pre_pwd=pre_pwd, new_pwd=new_pwd)

        if res.get('code') == 200:
            QMessageBox().about(self, 'Success', f'Find Success!')
            self._ui.user_name_box.setCurrentText(username)
            self._ui.pwd_linedit_login.setText(new_pwd)
            self._ui.stackedWidget.setCurrentIndex(1)
        else:
            QMessageBox().critical(self, 'Error', res.get('message'))

    def login_out(self, courier_exit: bool = False):
        self.gui.backend.login_out(courier_exit)
        self._ui.stackedWidget.setCurrentIndex(0 if courier_exit else 1)
        self.show()

    def load_cache_flag_map(self):
        res = Invoker.read_accounts(username=self._ui.user_name_box.currentText())
        use_cache = set()
        for env in res:
            use_cache.add(env["cache_flag"])
        default_cache_flag = 0
        for i in range(1000):
            if i not in use_cache:
                default_cache_flag = i
                break
        res.append(dict(cache_flag=default_cache_flag, sample="default", env_name="default", point_label="default"))
        self.cache_envs = res
        self.cache_flag_table_model.refresh_auto()

    def selector_cache_flag(self):
        self.load_cache_flag_map()
        self._ui.stackedWidget.setCurrentIndex(3)
