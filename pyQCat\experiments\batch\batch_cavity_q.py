# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/17
# __author:       <PERSON><PERSON><PERSON>


"""
流程作用
将低功率腔频更新至各个比特
将高低功率的下 q_int、q_ext 保存到各自的 point_label 中

流程描述
创建低功率 point_label (lp), 并切换；
使用拨码开关控制网分作用的 BUS
使用 FindBusCavityFreq 进行长范围粗扫，采集到 6 个腔后，按照 chip_line_connect.json 中的 bus_cavity_freq_sort_temp 配置的映射关系更新每个比特的腔频；
使用 FindBusCavityFreq 的 segm_scan 开启分段扫描，并将 analysis 中 fit_q 设置为True, 计算出每个比特的 q_int、q_ext, 更新数据库
每根 BUS 重复上述流程
创建高功率 point_label (hb), 并切换，重复上述 2 ~ 5 步
"""

from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np

from ...instrument.INSTRUMENT import INSTRUMENT
from ...invoker import Invoker
from ..batch_experiment import (
    BatchExperiment,
    BatchPhysicalUnitType,
    List,
)


class BatchCavityQ(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.micro_switch = "MicroSwitch"
        options.flows = ["FindBusCavityFreq_rough", "FindBusCavityFreq_segma"]
        options.physical_unit_type = BatchPhysicalUnitType.BUS
        options.low_point_label = "lp"
        options.high_point_label = "hp"
        options.high_net_power = -15
        options.low_net_power = -45
        options.affect_next_node = False
        options.cavity_check = False
        options.quality_block_exp = ["FindBusCavityFreq_segma"]
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.micro_switch = None
        options.init_point_label = None
        options.cur_point_label = None
        options.cur_net_power = {}
        options.records = {}
        options.x_data = None
        options.s21_data = {}
        options.ppt_template.update(
            dict(
                batch_pic_count=1,
                batch_shape=(2, 3),
                shape=(2, 4),
                split_by_unit=True,
            )
        )
        options.bus_qubits_map = {}
        options.saturation_power = {}
        return options

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "FindBusCavityFreq_rough" == exp_name:
            for unit in record.analysis_data.keys():
                fc_list = (
                    record.analysis_data.get(unit).get("result").get("new_fc_list")
                )
                cavity_count = (
                    record.analysis_data.get(unit).get("result").get("cavity_count")
                )
                if self.experiment_options.cavity_check is True:
                    self.change_regular_exec_exp_options(
                        exp_name="CavityCheck",
                        init_cavity=fc_list if cavity_count else [],
                    )
                self.run_options.s21_data[unit][
                    self.run_options.cur_net_power[unit]
                ] = exp.experiment_data.y_data["Amp"]
                self.run_options.x_data = exp.experiment_data.x_data
        elif "CavityCheck" == exp_name:
            for unit in record.analysis_data.keys():
                qubit_cavity_map = (
                    record.analysis_data.get(unit).get("result").get("qubit_cavity_map")
                )
                if qubit_cavity_map:
                    for qubit, cavity in qubit_cavity_map.items():
                        obj = self.backend.chip_data.cache_qubit.get(qubit)
                        obj.probe_freq = cavity
        elif "FindBusCavityFreq_segma" == exp_name:
            for unit in record.analysis_data.keys():
                pass_bits = (
                    record.analysis_data.get(unit).get("result").get("pass_bits")
                )
                fail_bits = (
                    record.analysis_data.get(unit).get("result").get("fail_bits")
                )
                if pass_bits == "None":
                    pass_bits = None
                if fail_bits == "None":
                    fail_bits = None
                if pass_bits:
                    self.run_options.records[self.run_options.cur_point_label][
                        "pass_bits"
                    ].extend(pass_bits)
                    self.run_options.records[self.run_options.cur_point_label][
                        "fail_bits"
                    ].extend(fail_bits)

        return record

    def _check_options(self):
        env = Invoker.get_env()
        self.run_options.init_point_label = env.point_label
        self.run_options.micro_switch = INSTRUMENT(
            self.context_manager.config.system.config_path
        )[self.experiment_options.micro_switch]
        self.run_options.records = {
            self.experiment_options.low_point_label: dict(pass_bits=[], fail_bits=[]),
            self.experiment_options.high_point_label: dict(pass_bits=[], fail_bits=[]),
        }
        for unit in self.experiment_options.physical_units:
            self.run_options.bus_qubits_map[unit] = (
                self.backend.chip_data.get_qubits_from_bus(unit)
            )
            self.run_options.s21_data[unit] = {}
        self.run_options.ppt_template.batch_pic_count = len(
            self.experiment_options.physical_units
        )
        super()._check_options()

    def _config_file_path(self, unit: str, exp):
        """Configure the parent directory for each experiment executed in the BatchExperiment.

        Args:
            unit (str): Physical working unit.
            exp (BaseExperiment) :
        """
        super()._config_file_path(f"{unit}-{self.run_options.cur_point_label}", exp)

    def _run_batch(self):
        if self.experiment_options.cavity_check is True:
            rough, segma = self.experiment_options.flows
            self.run_options.ppt_template.exps = [rough, "CavityCheck", segma]
        self.run_options.saturation_power = self._read_data_from_json(
            "bus_saturation_power", "config"
        )
        saturation_power = self.run_options.saturation_power
        for idx, point_label in enumerate(
            [
                self.experiment_options.low_point_label,
                self.experiment_options.high_point_label,
            ]
        ):
            self.backend.change_point_label(point_label)
            self.run_options.cur_point_label = point_label
            cur_pass_units = []
            for bus in self.experiment_options.physical_units:
                bus_num = int(bus.split("-")[-1])
                bus_s_power = saturation_power.get(bus)
                if idx:
                    net_power = (
                        self.experiment_options.high_net_power
                        if bus_s_power is None
                        else bus_s_power
                    )
                else:
                    net_power = self.experiment_options.low_net_power
                self.run_options.cur_net_power[bus] = net_power
                for exp in self.experiment_options.flows:
                    self.change_regular_exec_exp_options(
                        exp, net_power=net_power, bus=bus_num
                    )

                self.run_options.micro_switch.open_bus(bus_num)
                if idx == 0 and self.experiment_options.cavity_check is True:
                    rough, segma = self.experiment_options.flows
                    flow_pass_units = self._run_flow(
                        flows=[rough],
                        physical_units=[bus],
                        name=f"PointLabel({point_label}) {bus} Rough",
                    )
                    if not flow_pass_units:
                        flow_pass_units = self._run_flow(
                            flows=["CavityCheck"],
                            physical_units=[bus],
                            name=f"PointLabel({point_label}) {bus} Check",
                        )
                    if flow_pass_units:
                        flow_pass_units = self._run_flow(
                            flows=[segma],
                            physical_units=[bus],
                            name=f"PointLabel({point_label}) {bus} Segma",
                        )
                else:
                    flow_pass_units = self._run_flow(
                        self.experiment_options.flows,
                        physical_units=[bus],
                        name=f"PointLabel({point_label}) {bus}",
                    )
                cur_pass_units.extend(flow_pass_units)
            if idx == 0:
                self.bind_pass_units(cur_pass_units)
            all_qubits = self.set_fail_qubit_probe_freq(cur_pass_units)
            self.readout_baseband_freq_allocator(all_qubits)
            if self.experiment_options.save_db is True:
                self.backend.save_chip_data_to_db(all_qubits)

        self._save_data_to_json(self.run_options.records, "state_details")
        self.record_meta.execute_meta.result.hot_data = self.run_options.records

    def _batch_down(self):
        self.backend._set_env()
        self.backend.refresh()
        png_results = self._plot_s21()
        self.record_meta.execute_meta.result.origin_png_results = png_results
        super()._batch_down()

    def collect_record_data(self):
        self.run_options.micro_switch.close_sw()
        super().collect_record_data()

    def bind_process_data(self, process_data):
        process_data["BatchCavityQCheck"] = dict(
            run_options=dict(fail_units=self.record_meta.execute_meta.result.fail_units)
        )

    def set_fail_qubit_probe_freq(self, pass_units: List[str]):
        all_qubits = []
        probe_freq_list = []
        for unit in pass_units:
            qubit_map = self.run_options.bus_qubits_map.get(unit)
            for bit, qubit in qubit_map.items():
                all_qubits.append(bit)
                probe_freq_list.append(qubit.probe_freq)

        if probe_freq_list:
            mean_probe_freq = round(np.mean(probe_freq_list), 3)
            fail_units = self.record_meta.execute_meta.result.fail_units

            for unit in fail_units:
                qubit_map = self.run_options.bus_qubits_map.get(unit)
                for bit, qubit in qubit_map.items():
                    all_qubits.append(bit)
                    qubit.probe_freq = mean_probe_freq
        return all_qubits

    def _plot_s21(self):
        x_data = self.run_options.x_data
        colors = ["maroon", "forestgreen"]
        root_path = Path(self.run_options.record_path).parent
        png_results = []
        for bus, s21_data in self.run_options.s21_data.items():
            fig, ax = plt.subplots(figsize=(12, 8))

            for idx, (net_power, amp) in enumerate(s21_data.items()):
                ax.plot(
                    x_data,
                    amp,
                    label=f"Net Power: {net_power}",
                    marker="",
                    linewidth=2,
                    color=colors[idx % len(colors)],
                )

            ax.set_xlabel("Frequency (MHz)")
            ax.set_ylabel("Amplitude (dB)")
            ax.set_title(f"{bus} - High/Low Net Power S21")
            ax.legend()
            ax.grid(True, linestyle="--", alpha=0.7)

            cur_save_path = root_path / f"{bus}_high_low_s21.png"
            fig.savefig(cur_save_path, bbox_inches="tight", dpi=150)
            png_results.append(str(cur_save_path))
            plt.close(fig)
        self.run_options.s21_data.clear()
        return png_results
