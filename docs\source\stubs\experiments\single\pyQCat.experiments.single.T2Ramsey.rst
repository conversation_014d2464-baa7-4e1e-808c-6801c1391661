﻿pyQCat.experiments.single.T2Ramsey
==================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: T2Ramsey

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~T2Ramsey.__init__
      ~T2Ramsey.acquire_pulse
      ~T2Ramsey.cal_fidelity
      ~T2Ramsey.experiment_info
      ~T2Ramsey.from_experiment_context
      ~T2Ramsey.get_qubit_str
      ~T2Ramsey.get_xy_pulse
      ~T2Ramsey.get_xy_pulse_pre
      ~T2Ramsey.get_z_pulse
      ~T2Ramsey.get_z_pulse_pre
      ~T2Ramsey.jupyter_schedule
      ~T2Ramsey.options_table
      ~T2Ramsey.play_pulse
      ~T2Ramsey.plot_schedule
      ~T2Ramsey.run
      ~T2Ramsey.run_once
      ~T2Ramsey.set_analysis_options
      ~T2Ramsey.set_experiment_options
      ~T2R<PERSON>ey.set_multiple_IF
      ~T2Ramsey.set_multiple_index
      ~T2R<PERSON>ey.set_parent_file
      ~T2Ramsey.set_run_options
      ~T2Ramsey.set_sweep_order
      ~T2Ramsey.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~T2Ramsey.analysis
      ~T2Ramsey.analysis_options
      ~T2Ramsey.experiment_options
      ~T2Ramsey.run_options
   
   