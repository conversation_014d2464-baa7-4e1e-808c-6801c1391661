# -*- coding: utf-8 -*-

# This code is part of QStream.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/07/17
# __author:       xw


def gradient_descent(data, learning_rate=0.01, num_iterations=1000):
    """The critical value is calculated by gradient descent.

    Args:
        data: Array of data.
        learning_rate: The learning rate for gradient descent.
        num_iterations: The number of iterations for gradient descent.

    Returns:
        critical_value: The calculated critical value.
    """

    critical_value = data[0]

    for _ in range(num_iterations):
        gradient = (data - critical_value).mean()

        critical_value += learning_rate * gradient

    return critical_value
