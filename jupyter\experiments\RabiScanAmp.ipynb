{"cells": [{"cell_type": "markdown", "id": "d03ef956", "metadata": {}, "source": ["# RabiScanAmp\n", "\n", "初步校准 X 门幅值\n", "\n", "\n", "## 初始化实验环境"]}, {"cell_type": "code", "execution_count": 1, "id": "6d2584ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\n"]}], "source": ["# 若用户未安装 pyqcat 包（monster），通过本地源码测试，需要执行下面操作\n", "# 若安装了 pyqcat 包（monster），跳过此步骤\n", "\n", "import os\n", "import sys\n", "\n", "\n", "package_root = \"../../../pyqcat-monster\"\n", "\n", "print(os.path.abspath(package_root))\n", "\n", "sys.path.insert(0, package_root)"]}, {"cell_type": "code", "execution_count": 2, "id": "c49a58a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:21:35\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1m\n", "|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |        tcp://************:8088         |\n", "|    point_label     |              person_point              |\n", "|       sample       |               test_chip                |\n", "|      env_name      |                D00_1011                |\n", "|      version       |                 0.1.6                  |\n", "|=============================================================|\n", "\u001b[0m\n"]}, {"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': '6dcd9cb9fd6515decde8c1665a24309f'},\n", " 'msg': 'success'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "\n", "from pyQCat.tools import qarange\n", "from pyQCat.context import ExperimentContext\n", "from pyQCat.invoker import Invoker\n", "from pyQCat.parameters import get_parameters\n", "\n", "\n", "%matplotlib inline\n", "pd.set_option('max_colwidth', None)\n", "\n", "conf_file = r'E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf'\n", "\n", "username = \"test_04\"\n", "password = \"123456\"\n", "\n", "context = ExperimentContext(conf_file)\n", "Invoker.verify_account(username=username, password=password)"]}, {"cell_type": "markdown", "id": "d635b914", "metadata": {}, "source": ["## 配置实验参数"]}, {"cell_type": "code", "execution_count": 3, "id": "25b61579", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:21:35\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1mQubit_(bit=0) is already exist in current environment.\u001b[0m\n"]}], "source": ["# context 加载实验参数\n", "\n", "q_name_list = [\"q0\"]\n", "environment_elements = [\"q0\", \"q1\", \"q2\", \"q3\", \"c0\", \"c1\", \"c2\"]\n", "\n", "context.configure_inst()\n", "context.configure_qubits(q_name_list)\n", "context.minimize_compensate()\n", "# context.maximize_compensate()\n", "context.configure_environment(environment_elements)\n", "context.configure_crosstalk_dict()\n", "\n", "qubit = context.qubit"]}, {"cell_type": "code", "execution_count": 4, "id": "3a1351ca", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'1': 0.744732,\n", " '2': 0.236088,\n", " '3': 0.203233,\n", " '4': 0.525287,\n", " '5': 0.0,\n", " '6': 0.0,\n", " '7': 0.0}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看DC\n", "\n", "context.working_dc"]}, {"cell_type": "code", "execution_count": 5, "id": "2829caaa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>parameter</th>\n", "      <th>object</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>config</td>\n", "      <td>E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>qubits</td>\n", "      <td>[Qubit_(bit=0)]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>couplers</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>discriminators</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>compensates</td>\n", "      <td>{Qubit_(bit=0): PulseCorrectionQ0}</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>working_dc</td>\n", "      <td>True</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>crosstalk_dict</td>\n", "      <td>True</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        parameter  \\\n", "0          config   \n", "1          qubits   \n", "2        couplers   \n", "3  discriminators   \n", "4     compensates   \n", "5      working_dc   \n", "6  crosstalk_dict   \n", "\n", "                                                                  object count  \n", "0  E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf        \n", "1                                                        [Qubit_(bit=0)]     1  \n", "2                                                                     []     0  \n", "3                                                                   None     0  \n", "4                                     {Qubit_(bit=0): PulseCorrectionQ0}     1  \n", "5                                                                   True        \n", "6                                                                   True        "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看配置信息\n", "pd.DataFrame(context.context_table())"]}, {"cell_type": "markdown", "id": "b242e68d", "metadata": {}, "source": ["## 创建实验\n", "\n", "### X 门校准"]}, {"cell_type": "code", "execution_count": 6, "id": "575f094a", "metadata": {}, "outputs": [], "source": ["from pyQCat.experiments import RabiScanAmp\n", "\n", "\n", "rsa = RabiScanAmp.from_experiment_context(context)"]}, {"cell_type": "code", "execution_count": 7, "id": "106dab8d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>experiment option</td>\n", "      <td>show_result</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_shape</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_data_path</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>experiment option</td>\n", "      <td>repeat</td>\n", "      <td>1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>experiment option</td>\n", "      <td>register_pulse_save</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_flag</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_save</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_measure</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_type</td>\n", "      <td>envelop</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_measure</td>\n", "      <td>150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_show_real</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>experiment option</td>\n", "      <td>schedule_index</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>experiment option</td>\n", "      <td>save_label</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>experiment option</td>\n", "      <td>is_dynamic</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>experiment option</td>\n", "      <td>fidelity_matrix</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>experiment option</td>\n", "      <td>ac_prepare_time</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>experiment option</td>\n", "      <td>readout_point_model</td>\n", "      <td>&lt;class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>experiment option</td>\n", "      <td>idle_qubits</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>experiment option</td>\n", "      <td>name</td>\n", "      <td>Xpi</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>experiment option</td>\n", "      <td>amps</td>\n", "      <td>[0.0, 0.02, 0.04, 0.06, 0.08, 0.1, 0.12, 0.14, 0.16, 0.18, 0.2, 0.22, 0.24, 0.26, 0.28, 0.3, 0.32, 0.34, 0.36, 0.38, 0.4, 0.42, 0.44, 0.46, 0.48, 0.5, 0.52, 0.54, 0.56, 0.58, 0.6, 0.62, 0.64, 0.66, 0.68, 0.7, 0.72, 0.74, 0.76, 0.78, 0.8, 0.82, 0.84, 0.86, 0.88, 0.9, 0.92, 0.94, 0.96, 0.98, 1.0]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>experiment option</td>\n", "      <td>drive_power</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>analysis option</td>\n", "      <td>is_plot</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>analysis option</td>\n", "      <td>figsize</td>\n", "      <td>(12, 8)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>analysis option</td>\n", "      <td>result_parameters</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>analysis option</td>\n", "      <td>quality_bounds</td>\n", "      <td>[0.98, 0.95, 0.91]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 type                   name  \\\n", "0   experiment option            show_result   \n", "1   experiment option        simulator_shape   \n", "2   experiment option    simulator_data_path   \n", "3   experiment option                 repeat   \n", "4   experiment option    register_pulse_save   \n", "5   experiment option          schedule_flag   \n", "6   experiment option          schedule_save   \n", "7   experiment option       schedule_measure   \n", "8   experiment option          schedule_type   \n", "9   experiment option  schedule_show_measure   \n", "10  experiment option     schedule_show_real   \n", "11  experiment option         schedule_index   \n", "12  experiment option             save_label   \n", "13  experiment option             is_dynamic   \n", "14  experiment option        fidelity_matrix   \n", "15  experiment option        ac_prepare_time   \n", "16  experiment option    readout_point_model   \n", "17  experiment option            idle_qubits   \n", "18  experiment option                   name   \n", "19  experiment option                   amps   \n", "20  experiment option            drive_power   \n", "21    analysis option                is_plot   \n", "22    analysis option                figsize   \n", "23    analysis option      result_parameters   \n", "24    analysis option         quality_bounds   \n", "\n", "                                                                                                                                                                                                                                                                                                      value  \n", "0                                                                                                                                                                                                                                                                                                      True  \n", "1                                                                                                                                                                                                                                                                                                      None  \n", "2                                                                                                                                                                                                                                                                                                      None  \n", "3                                                                                                                                                                                                                                                                                                      1000  \n", "4                                                                                                                                                                                                                                                                                                     False  \n", "5                                                                                                                                                                                                                                                                                                      True  \n", "6                                                                                                                                                                                                                                                                                                      True  \n", "7                                                                                                                                                                                                                                                                                                      True  \n", "8                                                                                                                                                                                                                                                                                                   envelop  \n", "9                                                                                                                                                                                                                                                                                                       150  \n", "10                                                                                                                                                                                                                                                                                                     True  \n", "11                                                                                                                                                                                                                                                                                                       -1  \n", "12                                                                                                                                                                                                                                                                                                     None  \n", "13                                                                                                                                                                                                                                                                                                        1  \n", "14                                                                                                                                                                                                                                                                                                     None  \n", "15                                                                                                                                                                                                                                                                                                        0  \n", "16                                                                                                                                                                                                                                                         <class 'pyQCat.pulse.pulse_lib.FlatTopGaussian'>  \n", "17                                                                                                                                                                                                                                                                                                       []  \n", "18                                                                                                                                                                                                                                                                                                      Xpi  \n", "19  [0.0, 0.02, 0.04, 0.06, 0.08, 0.1, 0.12, 0.14, 0.16, 0.18, 0.2, 0.22, 0.24, 0.26, 0.28, 0.3, 0.32, 0.34, 0.36, 0.38, 0.4, 0.42, 0.44, 0.46, 0.48, 0.5, 0.52, 0.54, 0.56, 0.58, 0.6, 0.62, 0.64, 0.66, 0.68, 0.7, 0.72, 0.74, 0.76, 0.78, 0.8, 0.82, 0.84, 0.86, 0.88, 0.9, 0.92, 0.94, 0.96, 0.98, 1.0]  \n", "20                                                                                                                                                                                                                                                                                                     None  \n", "21                                                                                                                                                                                                                                                                                                     True  \n", "22                                                                                                                                                                                                                                                                                                  (12, 8)  \n", "23                                                                                                                                                                                                                                                                                                     None  \n", "24                                                                                                                                                                                                                                                                                       [0.98, 0.95, 0.91]  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(rsa.options_table())"]}, {"cell_type": "markdown", "id": "8caad110", "metadata": {}, "source": ["**experiment options**\n", "\n", "*辅助功能选型*\n", "- simulator_data_path (str): 模拟数据地址\n", "- simulator (bool): 是否开启模拟器\n", "- repeat (int): 小循环次数\n", "- fidelity_matrix (np.ndarray): 保真度矩阵，支持外部传入\n", "- show_result (bool): 是否在日志中展示分析结果\n", "- save_lable (str): 采集数据存储标签，用于生成文件名，默认为空\n", "- is_dynamic (int): 是否进行动态绘图，默认为1，单比特读取动态绘图，0关闭动态绘图\n", "\n", "*脉冲时序选项*\n", "- schedule_flag (bool): 是否绘制脉冲时序图\n", "- schedule_save (bool): 是否存储脉冲时序图\n", "- schedule_measure (bool): 是否绘制测量波形\n", "- schedule_type (str): 脉冲时序图的类型，支持 squence 和 envelop 两种\n", "- schedule_index (int or list(int)): 绘制脉冲时序图的索引\n", "- register_pulse_save (bool): 波形存储数据\n", "\n", "*实验参数选项*\n", "\n", "- amps (list): 扫描 Drag 波形幅值\n", "- drive_power (float): 驱动功率\n", "- name (str): 校准目标，`Xpi` 或 `Xpi/2`\n", "\n", "\n", "**analysis options**\n", "- result_parameters (list): 实验结果参数\n", "- quality_bounds (list): 拟合质量评估阈值\n", "\n", "\n", "RabiScanAmp实验中，我们实现了：\n", "\n", "- 根据选项 `theta_type` 更新分析选项 `result_parameters`；\n", "- 如果选项 `drive_power` 为空，则从比特对象中加载；"]}, {"cell_type": "code", "execution_count": 8, "id": "5f3ab062", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:21:36\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mRabiScanAmp register success, id 63492a20bd0ca2fb3a369d2e\u001b[0m\n", "\u001b[33m2022-10-14 17:21:36\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\test_chip\\RabiScanAmp\\q0\\2022-10-14\\17.21.35\\\u001b[0m\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "183cdc7e58304b669f4189f067607eed", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/51 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:21:38\u001b[0m | \u001b[32m RESULT \u001b[0m | \u001b[32mExperiment results as follow:\n", "=====================================================\n", "| name | describe | value | unit |     quality      | \n", "-----------------------------------------------------\n", "| Xpi  |  X-amp   | 0.72  |  V   | R²=0.956(normal) | \n", "=====================================================\u001b[0m\n"]}], "source": ["rsa.set_experiment_options(\n", "    amps=qarange(0, 1, 0.02),\n", "    drive_power=-20,\n", "    name=\"<PERSON><PERSON>\",\n", "    simulator_data_path='../../scripts/simulator/data/RabiScanAmp/'\n", ")\n", "\n", "rsa.run()"]}, {"cell_type": "code", "execution_count": 9, "id": "814b3ffa", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA4QAAAJYCAYAAAA6xSjbAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/av/WaAAAACXBIWXMAAA9hAAAPYQGoP6dpAACoTklEQVR4nOzdd5xcdb3/8ffM7O7sJlvSG2xCSITQEloSQCEgJVQR6aKCIqICV+GqEBuCV2NX1Cvq9SdKEQFpBum9J6ETAiG992R7nZnz+2NzTs6cOefMmd3Zndk9r6ePPCSzs9/5TDs5n/P5fL/fiGEYhgAAAAAAoRMtdAAAAAAAgMIgIQQAAACAkCIhBAAAAICQIiEEAAAAgJAiIQQAAACAkCIhBAAAAICQIiEEAAAAgJAiIQQAAACAkCIhBAAAAICQIiEEAAAAgJAiIQQAAACAkCIhBIAA9tprL51++um9/jirVq1SJBLR3/72t279fiQS0Q9+8IO8xpRvl1xyiSorKwPdtz88n/4ulUrpwAMP1I9+9KNCh5KTH/zgB4pEIr36GI8++qgqKyu1devWXn0cACgkEkIAA9a7776rc845RxMmTFB5ebn22GMPnXjiifrd735X6ND6jb322kuRSMT6M3jwYM2YMUO33nprwWLqL+/r+++/r0gkovLyctXV1RU6HE933nmn1q5dqyuvvLLQoRSdk08+WZMnT9bcuXMLHQoA9BoSQgAD0ssvv6zDDz9cb7/9ti677DL9/ve/1xe/+EVFo1HddNNNhQ6vXzn44IN122236bbbbtMPfvAD1dfX6+KLL9b//d//9fpjt7a26rvf/a719/70vt5+++0aM2aMJOlf//pXgaPx9vOf/1wXXHCBampqCh1KUbr88sv1pz/9SY2NjYUOBQB6RUmhAwCA3vCjH/1INTU1WrhwoYYMGZL2sy1bthQmqH5qjz320Gc+8xnr75dccon23ntv/frXv9Zll13Wq49dXl6e9vf+8r4ahqF//OMf+vSnP62VK1fqjjvu0Be/+MVCh5XhzTff1Ntvv61f/vKXhQ6laJ199tm66qqrdM899+gLX/hCocMBgLyjQghgQFq+fLkOOOCAjKRBkkaNGpVx2+23364ZM2Zo0KBBGjp0qI455hg9/vjjGfd78cUXNWPGDJWXl2vvvfd2bZ2sq6vT17/+ddXW1ioej2vy5Mn66U9/qlQqlXG/Sy65RDU1NRoyZIguvvhi19bCY489Vscee2zG7Zdccon22msvz9fAtH79en3hC1/Q6NGjFY/HdcABB+ivf/1r1t/zMnLkSE2ZMkXLly9Pu/2FF17Queeeq/Hjxysej6u2tlZXX321WltbXcdZsWKFZs+ercGDB2vcuHG68cYbZRhG2n2ccwjz/b4++OCDOu200zRu3DjF43FNmjRJP/zhD5VMJtPGOfbYY3XggQdq8eLFOu644zRo0CDtscce+tnPfub63F566SWtWrVKF1xwgS644AI9//zzWrduXcb9zLmpzz77rA4//HBVVFTooIMO0rPPPitJuu+++3TQQQepvLxchx12mN5888203zfnYwZ5Ld088MADKisr0zHHHJPxsyCfm2effVaRSER33323fvSjH2nPPfdUeXm5jj/+eC1btsy635VXXqnKykq1tLRkPM6FF16oMWPGpL3mjzzyiI4++mgNHjxYVVVVOu200/Tee+9lfT6JREI//OEPNWnSJMXjce2111769re/rfb29rT7ma/7448/roMPPljl5eXaf//9dd9992WMOWrUKE2dOlUPPvhg1scHgP6IhBDAgDRhwgS9/vrrWrRoUdb73nDDDfrsZz+r0tJS3XjjjbrhhhtUW1urp59+Ou1+y5Yt0znnnKMTTzxRv/zlLzV06FBdcsklaSeqLS0tmjVrlm6//XZ97nOf029/+1t99KMf1Zw5c3TNNddY9zMMQ2eeeaZuu+02feYzn9H//M//aN26dbr44ovz9yJI2rx5s4444gg9+eSTuvLKK3XTTTdp8uTJuvTSS/Wb3/ymW2MmEgmtW7dOQ4cOTbv9nnvuUUtLi77yla/od7/7nWbPnq3f/e53+tznPpcxRjKZ1Mknn6zRo0frZz/7mQ477DBdf/31uv76630fO9/v69/+9jdVVlbqmmuu0U033aTDDjtM3//+93XddddljLdz506dfPLJmjZtmn75y19qypQpuvbaa/XII49k3PeOO+7QpEmTNH36dJ1xxhkaNGiQ7rzzTtc4ly1bpk9/+tM644wzNHfuXO3cuVNnnHGG7rjjDl199dX6zGc+oxtuuEHLly/Xeeedl3FhobuvpdTVgnvggQeqtLQ07fZcPzc/+clPdP/99+sb3/iG5syZo1dffVUXXXSR9fPzzz9fzc3N+s9//pP2ey0tLZo3b57OOeccxWIxSdJtt92m0047TZWVlfrpT3+q733ve1q8eLE+9rGPadWqVb7P54tf/KK+//3v69BDD9Wvf/1rzZo1S3PnztUFF1yQcd+lS5fq/PPP1ymnnKK5c+eqpKRE5557rp544omM+x522GF6+eWXfR8bAPotAwAGoMcff9yIxWJGLBYzjjzySONb3/qW8dhjjxkdHR1p91u6dKkRjUaNs846y0gmk2k/S6VS1n9PmDDBkGQ8//zz1m1btmwx4vG48d///d/WbT/84Q+NwYMHGx9++GHaWNddd50Ri8WMNWvWGIZhGA888IAhyfjZz35m3SeRSBhHH320Icm45ZZbrNtnzZplzJo1K+M5XnzxxcaECRPSbpNkXH/99dbfL730UmPs2LHGtm3b0u53wQUXGDU1NUZLS0vGuHYTJkwwTjrpJGPr1q3G1q1bjXfffdf47Gc/a0gyrrjiirT7uo01d+5cIxKJGKtXr06LW5Jx1VVXWbelUinjtNNOM8rKyoytW7d6Pp98v69uMV9++eXGoEGDjLa2Nuu2WbNmGZKMW2+91bqtvb3dGDNmjHH22Wen/X5HR4cxfPhw4zvf+Y5126c//Wlj2rRpGY9lfq5efvll67bHHnvMkGRUVFSkvW5/+tOfDEnGM888Y92Wy2vpZs8998yI3zCCf26eeeYZQ5Kx3377Ge3t7db9brrpJkOS8e6771ox7bHHHhmPdffdd6d9rxobG40hQ4YYl112Wdr9Nm3aZNTU1KTdfv311xv205i33nrLkGR88YtfTPvdb3zjG4Yk4+mnn7ZuM1/3e++917qtvr7eGDt2rHHIIYdkvB4//vGPDUnG5s2bM34GAP0dFUIAA9KJJ56oV155RZ/4xCf09ttv62c/+5lmz56tPfbYQ//+97+t+z3wwANKpVL6/ve/r2g0/ZDoXNJ+//3319FHH239feTIkdp33321YsUK67Z77rlHRx99tIYOHapt27ZZf0444QQlk0k9//zzkqSHH35YJSUl+spXvmL9biwW01VXXZW318AwDN17770644wzZBhGWjyzZ89WfX293njjjazjPP744xo5cqRGjhypgw46SLfddps+//nP6+c//3na/SoqKqz/bm5u1rZt23TUUUfJMIyMVkdJaataRiIRXXnllero6NCTTz7pGUu+31d7zI2Njdq2bZuOPvpotbS06IMPPkj7vcrKyrS5lGVlZZoxY0ba+y91tTtu375dF154oXXbhRdeqLffftu17XH//ffXkUceaf195syZkqSPf/zjGj9+fMbtzseTuvdaStL27dszKr3d+dx8/vOfV1lZmfV383tixhqJRHTuuefq4YcfVlNTk3W/u+66S3vssYc+9rGPSZKeeOIJ1dXV6cILL0x73FgsppkzZ+qZZ57xfC4PP/ywJKVV4iXpv//7vyUpozo5btw4nXXWWdbfq6ur9bnPfU5vvvmmNm3alHZf8zXatm2b5+MDQH9FQghgwJo+fbruu+8+7dy5UwsWLNCcOXPU2Nioc845R4sXL5bUNSctGo1q//33zzqe/eTcNHToUO3cudP6+9KlS/Xoo49aCZT554QTTpC0e+GT1atXa+zYsRn78e27777dfr5OW7duVV1dnf785z9nxPP5z38+LR4/M2fO1BNPPKFHH31Uv/jFLzRkyBDt3LkzLQGQpDVr1uiSSy7RsGHDVFlZqZEjR2rWrFmSpPr6+rT7RqNR7b333mm37bPPPpKUtS0wn+/re++9p7POOks1NTWqrq7WyJEjraTPGfOee+6ZcZHA+f5LXfMWJ06cqHg8rmXLlmnZsmWaNGmSBg0apDvuuCMjBufnylzts7a21vV25+P15LWUlDHXsDufG+dzMBMoe6znn3++WltbrcS9qalJDz/8sM4991zrdV26dKmkrmTY+diPP/647+d19erVikajmjx5ctrtY8aM0ZAhQ7R69eq02ydPnpzxfnq9buZr1Nv7HgJAIbDKKIABr6ysTNOnT9f06dO1zz776POf/7zuueeeQHOs7Mw5Tk72E+pUKqUTTzxR3/rWt1zva55w5iISibguEOJc+MTJnGv2mc98xnNu4tSpU7M+/ogRI6yEdvbs2ZoyZYpOP/103XTTTVY1JplM6sQTT9SOHTt07bXXasqUKRo8eLDWr1+vSy65JGPeWz709H2tq6vTrFmzVF1drRtvvFGTJk1SeXm53njjDV177bUZMQd5/xsaGjRv3jy1tbXpIx/5SMZ9//GPf+hHP/pRWmLhNW6Qx+up4cOHZySY3fncBIn1iCOO0F577aW7775bn/70pzVv3jy1trbq/PPPz3js2267zdqyw66kJPtpS28kbeZrNGLEiLyPDQCFRkIIIFQOP/xwSdLGjRslSZMmTVIqldLixYt18MEH93j8SZMmqampyUqgvEyYMEFPPfWUmpqa0qqES5Ysybjv0KFDXdsEnRUPp5EjR6qqqkrJZDJrPLk47bTTNGvWLP34xz/W5ZdfrsGDB+vdd9/Vhx9+qL///e9pi8i4LdAhdZ34r1ixIi1B/vDDDyUp0MqpTt15X5999llt375d9913X9oqmytXrsz58U333Xef2tradPPNN2ckD0uWLNF3v/tdvfTSS1aLZD705LWcMmVKxvPtrc+NJJ133nm66aab1NDQoLvuukt77bWXjjjiCOvnkyZNktS1smeujz1hwgSlUiktXbpU++23n3X75s2bVVdXpwkTJqTdf9myZTIMIy2B9HrdVq5cqREjRmjkyJE5xQQA/QEtowAGpGeeeca1kmLOMzJbMz/5yU8qGo3qxhtvzKgIdacSc9555+mVV17RY489lvGzuro6JRIJSdKpp56qRCKhm2++2fp5MpnU7373u4zfmzRpkj744ANt3brVuu3tt9/WSy+95BtLLBbT2WefrXvvvdd1VU77eLm69tprtX37dmtzerNCZH/NDMPw3Sz+97//fdp9f//736u0tFTHH3+85+/k8311i7mjo0N/+MMfPB8/m9tvv1177723vvzlL+ucc85J+/ONb3xDlZWVrm2jPdWd11KSjjzySC1atChtW4be/Nycf/75am9v19///nc9+uijOu+889J+Pnv2bFVXV+vHP/6xOjs7c3rsU089VZIyVkH91a9+JanrQobdhg0bdP/991t/b2ho0K233qqDDz44ozr5+uuvp83zBICBhAohgAHpqquuUktLi8466yxNmTJFHR0devnll62qhDkXavLkyfrOd76jH/7whzr66KP1qU99SvF4XAsXLtS4ceM0d+7cnB73m9/8pv7973/r9NNP1yWXXKLDDjtMzc3Nevfdd/Wvf/1Lq1at0ogRI3TGGWfoox/9qK677jqtWrXK2gPNOW9Nkr7whS/oV7/6lWbPnq1LL71UW7Zs0R//+EcdcMABamho8I3nJz/5iZ555hnNnDlTl112mfbff3/t2LFDb7zxhp588knt2LEjp+dnOuWUU3TggQfqV7/6la644gpNmTJFkyZN0je+8Q2tX79e1dXVuvfeezPaEU3l5eV69NFHdfHFF2vmzJl65JFH9J///Eff/va3fasw+XxfjzrqKA0dOlQXX3yx/uu//kuRSES33XZbt1syN2zYoGeeeUb/9V//5frzeDyu2bNn65577tFvf/vbjK0euqu7r6UknXnmmfrhD3+o5557TieddJJ1e299bg499FDrvWlvb09rF5W6Fna5+eab9dnPflaHHnqoLrjgAo0cOVJr1qzRf/7zH330ox9NS37tpk2bposvvlh//vOfrXbgBQsW6O9//7s++clP6rjjjku7/z777KNLL71UCxcu1OjRo/XXv/5Vmzdv1i233JJ2vy1btuidd97RFVdc0a3nDABFrw9XNAWAPvPII48YX/jCF4wpU6YYlZWVRllZmTF58mTjqquucl06/q9//atxyCGHGPF43Bg6dKgxa9Ys44knnrB+PmHCBOO0007L+D23LSEaGxuNOXPmGJMnTzbKysqMESNGGEcddZTxi1/8Im17hO3btxuf/exnjerqaqOmpsb47Gc/a7z55psZ204YhmHcfvvtxt57722UlZUZBx98sPHYY48F2nbCMAxj8+bNxhVXXGHU1tYapaWlxpgxY4zjjz/e+POf/5z1dfR63oZhGH/729/SYl28eLFxwgknGJWVlcaIESOMyy67zHj77bczns/FF19sDB482Fi+fLlx0kknGYMGDTJGjx5tXH/99RlbRDifT77f15deesk44ogjjIqKCmPcuHHWNhZybO8wa9Ys44ADDsgY3/4e/PKXvzQkGU899ZTn62m+Zg8++KDv6yuXbT1WrlxpSDJ+/vOfd+u19DJ16lTj0ksvzbg9yOfG3HbinnvucY3V+Tk2DMP4zne+Y0gyJk+e7BnTM888Y8yePduoqakxysvLjUmTJhmXXHKJ8dprr1n3cW47YRiG0dnZadxwww3GxIkTjdLSUqO2ttaYM2dO2hYihrH7dX/ssceMqVOnGvF43JgyZUrG8zAMw7j55puNQYMGGQ0NDZ7xAkB/FjGMPM5OBwAAfeaSSy7Rv/71r7StHHJ122236YorrtCaNWs0ZMiQ/AVXxPbaay8deOCBeuihh7Le95BDDtGxxx6rX//6130QGQD0PeYQAgAQYhdddJHGjx+v//3f/y10KEXn0Ucf1dKlSzVnzpxChwIAvYY5hAAAhFg0GnVdPAbSySef3KPqKwD0B1QIAQAAACCkmEMIAAAAACFFhRAAAAAAQoqEEAAAAABCioQQAAAAAEKKhBAAAAAAQoqEEAAAAABCioQQAAAAAEKKhBAAAAAAQoqEEAAAAABCioQQAAAAAEKKhBAAAAAAQoqEEAAAAABCioQQAAAAAEKKhBAAAAAAQoqEEAAAAABCioQQAAAAAEKKhBAAAAAAQqqk0AGESXt7u9rb262/G4ahjo4OjRgxQpFIpICRAQAAAAgjEsI+NHfuXN1www0Zt9fX16u6uroAEYWPYRj66aNL9H8vrFAyZVi3RyLSxUfupevP2J/kHAAAAKERMQzDyH435IOzQtjQ0KDa2loSwj704Fvr9bV/viVJmjB8kA4YV60PNjZqxbZmSdKvz5+msw7Zs4ARAgAAAH2HCmEfisfjisfjhQ4jtLY0tun6B9+TJM2cOExH7D1ckvSRUVVasGqHXlm+XT/492IdNWmERleXFzJUAAAAoE+wqAxCwTAMfff+Rapr7dTIyrim7zUs7eeHjx+qUVVx1bd26tv3vSsK5wAAAAgDEkKEwuOLN+vxxZsVjUgn7j9asWj6PMFoNKKT9h+tWCSipz7Yosfe21ygSAEAAIC+Q0KIUPjriyslSYeMH6qRVe5tu8Mr4zpk/BBJ0t9fXtVHkQEAAACFQ0KIAW/ZlkbNX7lDEUnT9qzxve/UPWsUkfTKiu1atqWpT+IDAAAACoWEEAPe7a+ukSRNHDFYVeWlvvetKi/VxBGDJUl3zF/d67EBAAAAhURCiAGttSOpe99YJ0k6KEt10GTe797X16m1I9lrsQEAAACFRkKIAW3e2xvU2JZQdXmJJgwbFOh3JgwbpOryEjW0JTTvnQ29HCEAAABQOCSEGND+saCrXfSgPWoUiUSy3LtLJBLRQXt0VQnv3PX7AAAAwEBEQogBa1N9m95aWydJ2m9sdU6/a97/zTV12tLYlu/QAAAAgKJAQogB66kPuvYSHFNdrsHxkpx+d3C8RKOru7aneOaDLXmPDQAAACgGJIQYsJ56vyuR23vk4G79/sThg9PGAQAAAAYaEkIMSC0dCb24bJskWdtI5GrirkTyhaXb1NbJaqMAAAAYeEgIMSC9uHSbOhIpVZeXaPjgsm6NMbIyrsp4iVo7k3plxfY8RwgAAAAUHgkhBiSrXXREZeDVRZ0ikYj2GtG1VcXTtI0CAABgACIhxICTShl6atdCMBO7OX/QtPeISknSU+9vlmEYPY4NAAAAKCYkhBhw3l5Xp21N7SqLRbXHkIoejVU7tEIl0Yg21Lfp/Y2NeYoQAAAAKA4khBhwXl7eNd+vdliFYtHutYuaSmJR7TG0Yte423ocGwAAAFBMSAgx4Ly6awGY2qGD8jLenruqjAtW7sjLeAAAAECxICHEgNKZTOn11Tslyars9ZQ5zoJVO5RKMY8QAAAAAwcJIQaUd9fXq6UjqfLSaLe3m3AaVVWukmhEdS2d+nAL8wgBAAAwcJAQYkAx20X3GFLR7e0mnGLRiMYOKZckzV9B2ygAAAAGDhJCDChmwtbT1UWd9hzSNR+ReYQAAAAYSEgIMWB0JlN6bVVXwrZnnhaUMZkJ5vyV29mPEAAAAAMGCSEGjEXr69XckVS8JKoRlfmZP2gaXRNXLBrRtqYOLd/anNexAQAAgEIhIcSAMX/l7nbRfM0fNJVEoxpT3TWPkLZRAAAADBQkhBgw5psLyuRpuwknc9z5K7f3yvgAAABAXyMhxIBgGIbeWFMnSRqX5wVlTOY8wjfW7OyV8QEAAIC+RkKIAWHltmbVt3YqFo1oZGW8Vx5jdHXXuGt3tGpbU3uvPAYAAADQl0gIMSC8tbZOkjSqqmvxl94QL4lp2K7N7t/aVY0EAAAA+jMSwj7U3t6uhoaGtD/Ijzd3JWhjasp79XHMhWXeXEvbKAAAAPo/EsI+NHfuXNXU1Fh/amtrCx3SgGEmaGbC1lvMhNOsSAIAAAD9GQlhH5ozZ47q6+utP2vXri10SANCa0dSH2xslNR3FcK319YrmWKDegAAAPRvJYUOIEzi8bji8d5Z8CTMFm2oVyJlaHBZTFXx3v1ID68sU2ksoqb2hJZtadK+Y6p69fEAAACA3kSFEP3em7u2gRhTU573DemdopGIRleZbaPMIwQAAED/RkKIfs+cz9fb8wdNo3e1pb7JSqMAAADo50gI0e/11QqjprEsLAMAAIABgoQQ/dqm+jZtrG9TRNLoPqoQmpXIJZsb1dSe6JPHBAAAAHoDCSH6tbfX1UkyF3vpm4/z4HiJqspLZBjSu+vq++QxAQAAgN5AQoh+bdH6roRsVFXfVAdNo6riaY8PAAAA9EckhOjX3jUTwuq+3c5j1K620XdJCAEAANCPkRCi3zIMw2rZHN3HFcLRuyqEJIQAAADoz0gI0W9trG/T9uYORSPSiMqyPn1ss0V15bZmNbR19uljAwAAAPlCQoh+651d1cFhg8tU0kcLypgqymKqKi+RxDxCAAAA9F8khOi3zESsr7abcDLbVEkIAQAA0F+REKLfshaUqerbBWVM5kI277D1BAAAAPopEkL0S4Zh2BLCwlQIR7GwDAAAAPo5EkL0Sxvq27SjQAvKmMytJ1Zvb1F9CwvLAAAAoP8hIUS/9O66OknS8MHxPl9QxlRRGlO1ubDMBqqEAAAA6H9ICNEvFWpDeic2qAcAAEB/RkKIfmnR+gZJhVtQxmTNI2RhGQAAAPRDJYUOoFDWr1+ve++9V/c/8KDWrlunpqbmbo0Ti8U0ZEiNDj/0EJ177rk68cQTFY8XNkkZ6AzD0HsbCrugjMlMCN+jZRQAAAD9UOgSwmQyqS9/+cv6y1/+omisROV7HaLY0P0UHTlIiuQ+npFKqqG1USseeVa33nqrqmtqdO+//qUTTjgh/8FDkrSlsV3bmjoUKeCCMiYzIV21vUUNbZ2qLi8taDwAAABALkKVECaTSX3hC5fqtttu09CPX6bKg45XtLwyL2MbhqHObatV/+wtOu30M/Sfh+aRFPYScyP4YYPKCragjKmiLKbKeIma2hNavKFBR+w9vKDxAAAAALkI1RzCW265RbfedquGn/7fqp5+Zt6SQUmKRCIqG7mXRpz1HZXscYDO/ORZamlpydv42O29DV3zB0cWeP6gyWwbXcTCMgAAAOhnQpUQ/uMfd2rQhGkavP+sXnuMSEmZhhz/JbU0N+mRRx7ptccJMzPxKraE0ExUAQAAgP4iNAnh1q1b9dxzz6p834/2+mOVDttDFWP21t1339PrjxVGZuJV6BVGTSOrqRACAACgfwpNQvjOO+8olUqpfPzUPnm8ktqpmr/wtT55rDCpa+nQ+rpWScVUIexaWGb51ia1diQLHA0AAAAQXGgSwrq6OklStKK6Tx4vVlGturqdffJYYWJWB2sqShUviRU4mi6Dy2IaVBZTypDe30TbKAAAAPqP0CSEiURCkhSJ9dHCqtESJXc9JvLH3O+vWKqDUteCQmY879E2CgAAgH4kNAkhBoZF64tr/qBp90qjVAgBAADQf5AQ2lx3yhSVxrqxOz36TDFWCKXd8wgXbaBCCAAAgP6DhNDmlAPHaN5VH9P+Y/tmniFy09KR0IptzZKkkZXFlhB2xfPh5ka1J1hYBgAAAP0DCaHN7N88rwUrd+i+rx6lKz8+WZE8Fwvb29vV0NCQ9gfBvb+xQYYhDY7HNDjeR3NBA6oqL1G8JKrOpKGlm5sKHQ4AAAAQSHGdVRdYW2dK33/wPT3y7ib99OypOmHKKN383HIlU0ba/Z58f0u3xp87d65uuOGGfIQaSuYKo8VWHZS6FpYZVRXX2p2tWrS+XgfuUVPokAAAAICsSAhdvLJiu2586D398TOH6eaLDkv7mSFp0rcf7ta4c+bM0TXXXGP9vaGhQbW1tT0JNVTMjd/N+XrFZlRVeVdCyDxCAAAA9BMkhA7xkqiuO2WKPj1jvH739DL97umlchQIuz92PK54vPiqW/2FVSEssgVlTCNZaRQAAAD9DAmhzaHjh+qX501TRyKls//4Mif2RaQjkdKHmxslFd+WE6ZR1V1xvb+xQYlkSiUxpugCAACguHHGavPPLx2hJ9/frDN+9yLJYJFZuqVRnUlD8ZKoqsqL8zrGkIpSlcYiak+ktHxrc6HDAQAAALIiIbR5bfUO/eSRD9SRTLn+fFxNuW67dEYfRwUpvV00ku/lX/MkEonY2kaZRwgAAIDiR0JoM2H4YD14xUe1z+jKjJ99esZ4PXb1MRkrjqJvvLe+ODekd2KDegAAAPQnJIQ2s3/9vJZsbtS8Kz+mrx47SZFIV1Xw9ktn6rpTp+jHD7+vS25ZWOgwQ8msEBbr/EGTGZ8ZLwAAAFDMinMyVoE0tSf033e/rUcXbdKPzzpQp08dqz2HDdLba+t0ym9e0Pq61kKHGEqplKH3NxbvHoR2ZgVz8YYGpVKGotHibG8FAAAAJCqErt5cs1MfbGrUlDHVikYi+v3Ty0gGC2jV9mY1dyRVEo1o6OCyQofja9igMsWiETW1J7R6R0uhwwEAAAB8kRA6fGLaOD1x9SxFIxGd8KvndPurq3XrpTP0vdP3U7yEl6sQFu1qvxxRGVe0SBeUMUWjEauKycIyAAAAKHZkODY3f+ZQzf3UQfrNU0t10V/ma8W2Zv3kkQ904Z/n69h9R+nhrx2tQ8cPKXSYofPehv6xoIyJlUYBAADQX4QvITS8VwkdWRnXab99QX9/eVXa7W+s2alTb3pBz3+4Vf/80pEBHyhVtNsj9DdmYlXsC8qYzDjfJSEEAABAkQvNojJVVVWSpFRHi6LxQa73OfdPr3jmi+2JlG6Yt1iPLNoU6PFS7a2qrKruVqzYzTAMLVq/a4XR6n6SEFbvrhAahsGFAQAAABSt0FQIx48fL0nq3Lra8z4+xUPLgpU7Aj1ecvtqTRhfG+i+8LZ2R6vqWzsVi0Q0fHD/SAiHD44rFomooS2hNSwsAwAAgCIWmoTwgAMO0ISJe6tlyUu9/lip9ha1rnhD55z9qV5/rIHObLscXtm1emd/EItGNLyyazVU2kYBAABQzEKTEEYiEV14/nlqX/aKEo3bevWxmt55XKlEh84555xefZwwWLShf80fNJltoySEAAAAKGahSQgl6atf/apGDq3R9ru+02tJYdO7T2nnM/9Pl19+uSZMmNArjxEm1oIy1eUFjiQ3o6u64mWlUQAAABSz0CwqI0m1tbV64fnndPQxs7TpL19W+d6Hq2Kfj6p0+B6KlA3q1uIfRjKhVFuT2ta8q/YPX1TrxmX64he/qD/84Q+98AzCxTAMq8LW7yqE1tYTDSwsAwAAgKIVqoRQkvbee28tXDBff//733XnXXfr3X//NC/jxssrdPppp+q8836kc845R9FoqIqvvWLdzlbVtXQqGpE1J6+/GF7ZtbBMfWun1u5o1fjh7ivbAgAAAIUUuoRQksaNG6c5c+Zozpw5Wrt2rTZs2KDGxsZujVVSUqIhQ4boIx/5iAYPHpznSMPNbLccURlXST9LsM2FZbY0tuvd9fUkhAAAAChKoUwI7Wpra1Vby/YQxchsFx3Zz9pFTaOq4lZCeNrUsYUOBwAAAMjQv8ouCJX+On/QZC6Ew8IyAAAAKFYkhChKhmHovQ0NkvrfCqOm0VW7t54wDKPA0QAAAACZSAhRlNbuaNWO5g7FIhGN6GcLypiGV8YVi3YtLLNqe0uhwwEAAAAykBCiKL25dqckaURVWb9bUMYUi0asdte319YVNhgAAADARf8808aA99auBGpMP20XNY3eFf9bJIQAAAAoQiSEKEpWQljTvxNCM6F9k4QQAAAARYiEEEWnI5GyFpQZ3c8rhGZC+/6GBrUnkgWOBgAAAEhHQoii8/7GBnUkUioviWpIRWmhw+mR6vISVZTG1JFM6f2NjYUOBwAAAEhDQoiiY7aLjq4pVyQSKWwwPRSJRDS6umthmbfW7CxwNAAAAEA6EkIUnYGyoIzJbHt9ex0b1AMAAKC4kBCi6Ay0hNCcR8hKowAAACg2JYUOIEza29vV3t5u/b2hoaGA0RSnupYOrdzWLKmrZXQgMBPblduaVdfSoSGDygocEQAAANCFCmEfmjt3rmpqaqw/tbW1hQ6p6JhVtJqKUlWUxgobTJ6Ul8ZUs2txHNpGAQAAUExICPvQnDlzVF9fb/1Zu3ZtoUMqOq+v7lp4ZewAqQ6azOfz+qodBY4EAAAA2I2W0T4Uj8cVj8cLHUZRm7+yK2HaY0hFgSPJrz2GVOiDTY3W8wMAAACKARVCFI22zqTVMrrH0IGVEI7bleC+tbaODeoBAABQNEgIUTTeWlunjkRKg8pi/X5Deqehg7rmRLYnUlq0nnmEAAAAKA4khCga81d0tVPuOaSi329I7xSJRDRuSNc8QtpGAQAAUCxICFE05q/cLmngtYuazHmRC0kIAQAAUCRICFEUOhIpvbGma4XRgbagjMl8Xq+t2qlkyihwNAAAAAAJIYrEu+vr1NaZUkVpTMMGD8yN20dUxVUWi6qxPaEPNjUUOhwAAACAhBDF4dUVu7ebGGjzB03RSERjd80jXEDbKAAAAIoACSGKgrX/4ACdP2gy20ZJCAEAAFAMSAhRcK0dSS3YtaDMniFJCF9ZsZ15hAAAACg4EkIU3EvLtqmtM6Wq8hINH6DzB01jqssVL4mqrqVTb62tK3Q4AAAACDkSQhTcUx9sliTtPWLwgJ0/aIpGI5owbJAk6dklWwocDQAAAMKOhBAFlUoZevL9rsRo4ojBBY6mb+y163k+Q0IIAACAAiMhREG9u75eWxvbVRqLDPgFZUzjd1UIF61v0JbGtgJHAwAAgDAjIURBPfV+V7vohOGDVRINx8dxcLxEo6rikqTnlmwtcDQAAAAIs3CcgaNoPbGrXXTvkLSLmsy20WdJCAEAAFBAJIQomPV1rXp/Y4MikvYaHrKEcHhX2+jzS7eqM5kqcDQAAAAIKxJCFMz9b6yTJI0dUq6KsliBo+lbo6vLVV4aVWNbQvNXsEk9AAAACoOEEAWRTBm6c8FaSdKB42oKHE3fi0Yi+sioKknSnQvXFDgaAAAAhBUJIQri2SVbtL6uVeUlUX1kVGWhwymIg/boSoQff2+TtjW1FzgaAAAAhBEJIQri9ldXS5L2H1etklg4P4Yjq+IaXR1XZ9LQPa+tK3Q4AAAACKFwnomjoNbuaNGzH3atrnngHuFrF7Uzq4R3LlijVMoocDQAAAAIGxJC9Lk75q+RYUi1wyo0dFBZocMpqH1GV6msJKo1O1r00vJthQ4HAAAAIUNCiD61enuzbnlppSRp2p5DChtMESiNRbXfmK7FZf78/AoZBlVCAAAA9B0SQvQZwzD03QcWqT2RUu3QitBtRu/l4NohikUiemHpNj2yaFOhwwEAAECIkBCizzz41ga9sHSbYtGIPj5llCKRSKFDKgpDBpXpsL2GSpJunLdYTe2JAkcEAACAsCAhRJ9YtqVJP3xosSRpxl7DNCTkcwedpk8YqpqKUm1qaNNvnviw0OEAAAAgJEgI0evmr9ius29+WdubOzSiskyHTRha6JCKTkksqmP3HSlJ+utLK/XIuxsLHBEAAADCoKTQAWBg6kymtHDVDj3+3mbdMX+1OpOGxlSX64xpYxWL0irqZq/hg3XguGot2tCgq+58UzfHojpx/9GFDgsAAAADWMRgWcOCaWhoUE1Njerr61VdXV3QWB5dtElvrt2pkmhEsWhUMgwlDUMpQ4pIikYiShqGEsmUkikpZRhKpgx1JFJqSyTV0pFUc3tCTe0JbW5o09bGdtm31Zs0crBOPmBMaDehDyplGHr8vc1asrlRpbGIDhhXo0hEGlxWouGVZaqpKFVLR1JNbQnFYhFVxUtUXhpTZzKlzmRKJbGoKkpjKolFlEx2vYcl0YhKY1GVlUT1+Y9OVE1FaaGfJgAAAIoEFcI+1N7ervb2duvvDQ0NBYwm3YvLtur2V9fkdcxBZTEdMK5aB+0xRFPGVinKIjKBfOFjE3XH/NV6Z1293lpbl9exz59eS0IIAAAACwlhH5o7d65uuOGGQofh6mOTR6iiNKZEylAiaSgS6aoKRiMRGTJkGF1/L41FFI1GFItEFI1IZSVRlZfGVFEWU2W8RIPLSjSqOq7R1eUaWRlXlPbQbjn3sD312uqdamjtVNIw1Nye0PamDtW3dmpQPKaqeImSKUNN7Qm1diZVGouqNBZVZzKltk6zWtj1PiVThtoTXbdVxvnKAwAAYDdaRvuQW4Wwtra2KFpGAQAAAIQP5YI+FI/HFY/HCx0GAAAAAEhi2wkAAAAACC0SQgAAAAAIKVpG+5BzDmF9fb0kiWmcAAAAAAqBCmEfmjt3rmpqaqw/48ePlyRF2I4BAAAAQAGwymgfcqsQjh8/nlVGAQAAABQELaN9iFVGAQAAABQTWkYBAAAAIKRICAEAAAAgpEgIAQAAACCkSAgBAAAAIKRICAEAAAAgpEgIAQAAACCkSAgBAAAAIKRICAEAAAAgpEgIAQAAACCkSAgBAAAAIKRICAEAAAAgpEgIAQAAACCkSAgBAAAAIKRICAEAAAAgpEgIAQAAACCkSAgBAAAAIKRICAEAAAAgpEgIAQAAACCkSAgBAAAAIKRICAEAAAAgpEgIAQAAACCkSAgBAAAAIKRICAEAAAAgpEgIAQAAACCkSAgBAAAAIKRICAEAAAAgpEgIAQAAACCkSAgBAAAAIKRICAEAAAAgpEgIAQAAACCkSgodQJi0t7ervb3d+ntDQ0MBowEAAAAQdlQI+9DcuXNVU1Nj/amtrS10SAAAAABCLGIYhlHoIMLCrUJYW1ur+vp6VVdXFzAyAAAAAGFEy2gfisfjisfjhQ4DAAAAACTRMgoAAAAAoUVCCAAAAAAhRUIIAAAAACFFQggAAAAAIUVCCAAAAAAhRUIIAAAAACFFQggAAAAAIUVCCAAAAAAhRUIIAAAAACFFQggAAAAAIUVCCAAAAAAhRUIIAAAAACFFQggAAAAAIUVCCAAAAAAhRUIIAAAAACFFQggAAAAAIUVCCAAAAAAhRUIIAAAAACFFQggAAAAAIUVCCAAAAAAhVVLoAArJMAwtXrxY69evV1NTU7fGiMViGjJkiA444ACNGDEizxECAAAAQO8JZUK4YsUK/fnPf9Y9d92pFavW5GXMWCyqjx97rM674EJdcsklKikJ5UsLAAAAoB+JGIZhFDqIvrR48WJ9/Nhj1NlSr7P3jeic/Uu17/CoquIRRboxXmfK0I5WQ8+uSuqe91N6ZmWHLjj/At16221Zk8KGhgbV1NSovr5e1dXV3XtCAAAAANBNoSpjrVixQh8/9hiNijXqqa+Ua+TgfEyhjGjUYGnKiJi+fLh07+KYzr/7LpWUxHTrbbfnYXwAAAAA6B3dSggNw9Cy17do2WtbtGV1g1obO1VSFlXlsHKN32+YDjpuT1UNK/f8/ZaGDr32n5VatWi7muvbFa8o0biPDNFhJ++lkeOruv1ksrn55puVbK3XU1/OVzKY6ez9S/W/LYa+fPsduv4HN2jSpEm98jgAAAAA0FM5t4w217Xr4T++qy2rGqSINGp8lapHVijRkdKmFfVqa+pUrCSqj54zWQcdu2fG79dtbtF9v3hdrY2dqh5RrlETqtWwrVVbVjcqGo1o9mUHau9DRubtCZoMw9DECbU6ZdQW3Xx6Rd7Ht2vuMDTyl836/g0/0nXXXed5P1pGAQAAABRSThXCtuZO3feL19WwrU0jait1wuf31/BxldbPU8mU3n56nV65f7me/+eHSqUMTft4rfVzwzD02F8WqbWxU/vOHKOPX7yfotGumXvvvbBez96xRE/+bbEu2vsIDa6J5+kpdnnnnXe0eu16nXPsoLyO62ZwWUSnTY7pwfvv9U0IAQAAAKCQcuqbfP6fH6phW5uqR5Trk1cfkpYMSlI0FtUhJ47X0ed9RJL08r3LtHNTs/Xz1Yu2a9vaJsUHleiYC/exkkFJOuDoPbTnlKHqbE/qnafX9eQ5uVq3rmvM/Ub2zdaLU4ZHtH5d/p8HAAAAAORL4Oyofmurlr22WZJ01NmTFR9U6nnfA2ftoeF7ViqVNPTm47u3dVj51lZJ0l5TR6isPLM4+ZHpoyVJK3bdL5/MfQaryrqzlmjuquIRNTR2b29DAAAAAOgLgRPCVe9sk2FI8UElmjjVfwP2SCSifWeOkSStfGebzGmKW9d2JUijJrgvHDNqQtc8urotLepsTwYNLSeRvskHFXV5nPb2djU0NKT9AQAAAIBCCZwQbl3TKEkaUVupaCz7r43eqyvpa2vqVOP2NklSw/ZWSVLlUPcVSCuH7po3aOy+b5/63L+l/c7w/vmgYdLX3u728HPnzlVNTY31p7a2NvsvAQAAAEAvCZwQtjZ1SJIGVZUFun+F7X6tTZ2SpM62rqpfaTzm+jul5btvN+/bpyYeLZ37N+nYOe4/j8Skmu4ncXPmzFF9fb31Z+3atd0eCwAAAAB6qk82pjdSOe1sUVgPXSOd9ENp9AHSfV+SOlvyNnQ8Hlc8nt/VUwEAAACguwJXCMsruxaRaWnsCHT/Vtv9Kqq6ftesAHrND7RXBe3Vwj615D/SX06QRu0nffFJaehehYkDAAAAAHpZ4IRw1PiuBV+2rmlSKpnKev/NK7vmHJZVlKh6eNdG8Ob/N+1sc/2dpp3tXf8RkaqGuc8z7BPbPpT+fJzUsF667Blp72MLFwsAAAAA9JLACeFeU4crEpE6WhNa+fY23/sahqEl8zdJkiZOG6HIriU3R9Z27Vu4ZXWj6+9tWd216uaQUYNct6XoU+0N0h3nSm/8XbroHumIrxY2HgAAAADIs8AJYc3IQZp82ChJ0sv3LVN7S6fnfRc9t17b1zcpWhLRISeNt26fePBISV1bWLi1jS5d2LXP4d677tfnDJe5jk/+QLr/y9LHvyN94nd9HhIAAAAA9JbACaEkHXPhvqoaXq6GbW164NdvavuG9I3XU8mU3npyjV64e6kk6biLpmj4uErr5xMOHK4RtZVqb0nouTuXKGVbbOa9F9Zr3Qc7VRqPaerH9+zJc/LllvNZvDYpXHSv9NeTpdH75+dxAAAAAKAI5NSXWT64VJ/6xqF6+OZ3tXVNo/75wwUaNb5KNSMr1NmR0uaV9Wpt7FRZeUxHnT1ZU44cm/b7kUhEJ116gO7/5Rta8uombVxWp1F7VathW5u2rGpQNBrRCZfsr8E1+V+Jc9CgQZKk5k5DVXGPxO9vp0utO91/tuld6U+zpH1mB3q8pg5DlZWDuhMqAAAAAPSJiGHkXssyUoaWvr5Zy17boi2rGtTa1KlUsmuYkrKoLvjeDNWM9E6Gmuvb9frDq7Tq3e1qbmhXvKJEYycP0eGn7KWR46u6/2x8LFiwQDNnztRLXxiko2p7f37i5+5v1XvR/fT6m94b2Tc0NKimpkb19fWqrq7u9ZgAAAAAwK5bCaGb9taEHvjVG9q2tkm1+w/TaV+ZqlhpTh2pvSqZTGrcmFH67Eea9IuTencF046kodG/atWV11ynH/7wh573IyEEAAAAUEh5y9jiFSX6xH8drKFjBmnt4h167C+LAm1P0VdisZg+dc55uvt9Qy2dvTvBb96ShOpaEjr33HN79XEAAAAAoCfyWsKrqCrTmV8/RNNPn6gRe1Zqyxr37SUK5fLLL9f29pjOvKu915LCl9cmdMm/O3T8ccfqoIMO6pXHAAAAAIB8yFvLaH/x3HPP6dRTTtaEqqQ+e2BU5+xfosnDoop4rTAaQHvC0LOrkrpncafufC+lw2ccoYcffUyDBw/2/T1aRgEAAAAUUugSQkmaP3++fvXLX+qhh+appbVN0YhUVV7iueuEn86koeZdeypOmjhB513waX3nO9/JmgxKJIQAAAAACiuUCaGppaVFTz31lNavX6/Gxu61t5aUlGjIkCE6+OCDdfDBB+dUaSQhBAAAAFBIoU4IC42EEAAAAEAhFc++EAAAAACAPkVCCAAAAAAhRUIIAAAAACFFQggAAAAAIUVCCAAAAAAhRUIIAAAAACFFQggAAAAAIUVCCAAAAAAhRUIIAAAAACFFQggAAAAAIUVCCAAAAAAhRUIIAAAAACFFQggAAAAAIUVCCAAAAAAhRUIIAAAAACFVUugAwqS9vV3t7e3W3xsaGgoYDQAAAICwo0LYh+bOnauamhrrT21tbaFDAgAAABBiEcMwjEIHERZuFcLa2lrV19erurq6gJEBAAAACCNaRvtQPB5XPB4vdBgAAAAAIImWUQAAAAAILRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIqZJCB4AiYBhSZ0uhowAAAAD6j9JBUiRS6Ch6jISwD7W3t6u9vd36e0NDQwGjselskX48rtBRAAAAAP3HtzdIZYMLHUWP0TLah+bOnauamhrrT21tbaFDAgAAABBiEcMwjEIHERZuFcLa2lrV19erurq6cIHRMgoAAADkhpZR5Coejysejxc6jEyRyIAodwMAAADIDS2jAAAAABBSJIQAAAAAEFIkhAAAAAAQUswh7EPORWXq6+slSazrAwAAAKAQqBD2Iee2E+PHj5ckRQbA6kQAAAAA+h+2nehDbhXC8ePHF37bCQAAAAChRMtoHyrabScAAAAAhBItowAAAAAQUiSEAAAAABBSJIQAAAAAEFIkhAAAAAAQUiSEAAAAABBSJIQAAAAAEFIkhAAAAAAQUiSEAAAAABBSJIQAAAAAEFIkhAAAAAAQUiSEAAAAABBSJIQAAAAAEFIkhAAAAAAQUiSEAAAAABBSJIQAAAAAEFIkhAAAAAAQUiSEAAAAABBSJIQAAAAAEFIkhAAAAAAQUiSEAAAAABBSJIQAAAAAEFIkhAAAAAAQUiSEAAAAABBSJIQAAAAAEFIkhAAAAAAQUiSEAAAAABBSJIQAAAAAEFIkhAAAAAAQUiSEAAAAABBSJYUOIEza29vV3t5u/b2hoaGA0QAAAAAIOyqEfWju3Lmqqamx/tTW1hY6JAAAAAAhFjEMwyh0EGHhViGsra1VfX29qqurCxgZAAAAgDCiZbQPxeNxxePxQocBAAAAAJJoGQUAAACA0CIhBAAAAICQIiEEAEDSs2uf1ZIdS/IylmEYemL1E1pZvzIv46WMlB5Z+YjWN63Py3idqU49tOIhbWnZkpfx2hJtmrd8nna07cjLeM2dzZq3fJ4aOvKzGnd9e73mLZ+nls6WvIy3rXWb5i2fp45kR17G29S8Sf9Z8R8lUom8jLe2Ya0eXfWo8rVMxIq6FXpq9VN5GUuS3t/+vl5Y90Lexnt769uav3F+3sZbuGmh3tryVt7Ge3H9i1q8fXHexnt6zdNatnNZXsYyDEOPrXpMaxrW5GW8ZCqph1c8rI1NG/MyXkeyQ/OWz9O21m15Ga810ap5y+eprq0uL+MNFCSEAIDQW9+0Xlc9fZW+9fy38jLekp1LdM2z1+j6l6/Py3ivbXpN33r+W5o7f25exnt27bOa88Ic/eb13+RlvP+s+I++/eK39X/v/F9exrtryV369ovf1h2L78jLeLcsukXffvHbun/Z/XkZ7w9v/UHffvHbemzVY3kZ7+cLf67rXrhOL294OS/j3fjqjfrmc9/U21vfzst4171wnb7+7Ne1on5FXsa7+tmrdcVTV2hry9Yej2UYhr7y5Ff0lSe/oubO5h6P15po1Zef+LK+8uRXlEwlezzejrYduuKpK/S1Z77W47EkaU3DGn3tma/puheuy8t4721/T9947hv6wSs/yMt4r258Vde+cK1+uvCneRnvqTVP6dsvflu/e/N3eRnvwWUP6tsvflv/b9H/y8t4AwUJIQAg9Ha27ZQk1bXX5XU88/97PF77zrT/7/F4bXkeL8/xmVfv8zbervc1X+9vaMfLU1Wlrr1Ohoy8VICTRlKNHY3qTHXmpQLcmmhVR6pDTZ1NSho9Twgb2huUMlJ5e+167ViQ72NVvsYr8vgGChJCAEDoma1/+WrZM08k83FCKcmqVOSjYiHZ4svXeHmOL2Ek8jteivF6wnp/8/x5zsf3zR5TPuKzv2b5jM/8TPd4vN46FuT7WJWv8Yo8voGChBAAEHodqa6EsOgTuCI9Keov8eXtpDxk45nj5OuCiZXwF3EC5/zv7sp7cs7Fpp6Nl+f4BgoSQgBA6JkVwnxXuPJ2Qp7K7wm5lTDk6wS/yOPL+0ll2MYr4iTE/h3LZwIn5Sc+MyZDhlJGqsfj5b2anMfkXLLFl6/x8nwstarTeRpvoCAhBACEXmeyU1J42rryfdJW7PHl/aQ3z+PlPeEq4gQuZaRkqGv103xXCPOZwEn5OR70Vnx5T5DyfTGnSI+l+W5HHyhICAEAoWe1jBZpAlfsbVP9Jb58Vxzz3UJZ9BXbPCdI+W7xLOYETspvfKE7VhVpO/pAQUIIAAg9s2U0LG1dRT9HL89tXf2mAlek8eXz81zsLZ79Jb5ifG+l4v7sSfk/Vg0UJIQAgNAzK4RSONq6emuOY7G2deV7vP5SES3Gk/zerMCFKb6kkZRhGD0fL98LEBVxdVrK/7FqoCAhBACEnjmHUKKtq1vjFXvFLM/j9VaVJu+rguZhvJSRsqrmxd7iGbb48ppgFvuxoEjjGyhICAEAodeZ2p0Q5rMqkPcWwCI9Keqt+PK+DUORziEs5gphb23DIIWrxVPqhfjyMV5vtaMX67Eqz/ENFCSEAIDQM+cQSvk5yTcTzHy1dZnjFeuiI+Y49sS6J/LeIttLz7dY2+zyGV/eEyTbeMVYgct3fGnj5Tu+PI6Xr8+e/diXz/GKdQGngYKEEAAQevY5hHk/Sc3jeHmfR1Ps20Tku2JWrBXRIo4vbU5dPloU7RXHfFcwQzZePltQ8z4nscj3JGVRmXQkhACA0LNXCIv5JLBoFzEp8gQp74vU9Nbm23k46TUMI7/bROQ7AemliyVhHC/vi+jkedGbfCjmiyUDCQkhACD00hLCfFdBivEkq9gXgemtfQiLfE5iMVbgemvOmtQLc/QYr2fj5fHzF5aLTQMFCSEAIPTyvahM3uf52BZ+yEdbV97nrPXWxur53oewSE8q89rime+LEfmudveTClcox8vjsS/v82vzvQIvcwjTlBQ6gEJraGjQpk2b1NTU1K3fj8ViGjJkiPbYYw+VlIT+5QSAfsmeEOa7za43TgJLIj379yZsFcIwzXHM+z56+W557KU5cBLx9Xi8PMZX9KuMUiFME8oMZufOnbr77rt119136blnn1MqlerxmEOHD9W5Z5+r8847Tx//+McViUTyECkAoC/kfQ5hL29uXdLDf777zcbqeU4wi3GzbMMw8lq1sMeUl1Uyi7xFsV9tE1GMFeBeOlYZMpQyUopGetaMWOzHqoEidAnhxo0bdfSso7Vi+QpV7V+lMZ8do7IxZYpVxLo1npE0lGxOqvmDZt32wG3685//rGuvvVZz584lKQSAfiLfcwh7/aSye/9k7R4j3/v8hWiOXtp4eTipNDd9l4r0BL/IWxT7S4Lk/O/u6m8JcDTWs4Qw3/sk5rsFdaAIVUK4efNmHT3raK3fsV6TfzxZ8THxvI1dNbVKxrmGtj++XT/96U8ViUQ0d+7cvI0PAOg9vbrtRBG2dVktlPlqycz3SVtvjVeEbWy9us1BnuPL92e52BO4MBwLeju+UpX2bLw870lKhdBdqBaVufnmm7Vm3RrVfqs2r8mgKRKJaMTsERp11ij99Kc/1YYNG/L+GACA/OtM5ndRmf5y0mvIyGsVKe9z/oq0BTWf8fWrjdBDEF+xVwj7U3z53icxH/I9n3igCFVC+I9//kODDxms+Oj8J4N2w08YrkgsonvvvbdXHwcAkB/2CmEY2rp6a7PxfLdk5r0FtQjH608VwmKMjzmOPRyvN+c45vFiU8pIpbVXd3s8KoSuQpMQfvDBB1q6ZKmqp1f3+mPFBsdUuX+l7rn3nl5/LABAz9nnEIahrStt4ZE8VmmKta0r33P+zBPTUMzRC3F8xd4iW4zHqrxXlHspAWYOYbrQJISrVq2SJJWPL++TxysbX6YVK1b0yWMBAHom7xvT96O2rmKsEPbaNhH5rpjlucKV7xPoMG1zIIUvgSv6Y1URr9hMhTBdaBLChoYGSVJsUA+XZgsoVhGzHhMAUNzyvTF9WE/airWtK58LU+T9BL83K2Z5nuNYjC2KRR9fkbd49pd29LyNxz6ErkKTEBqG0fUffbQTRCQa2f2YAICilpYQ5vmkrRj3luvNzcbzedKW7zl/RXmC2p/GK8aLJcUeXz9aBTXvCWa+F13K8yI1nKfvFpqEsBi0t7eroaEh7Q8AoPCyzSHsTHVqTcOajNvWNqx1HS/bSWVHskPrGtdlvc0aI8tJW2uiVRuaNmTctrFpo+t42U7amjubtal5U9ptTR1N2ty82X28LFWLho4GbWvdlnZbfXt9xm3O+LxOKHe27dTOtp1pt21v3a66trqM+xqGkbXiuLVlqxo6GjJua+xozIwtQIVmU/MmtXS2ZL3NOYbXeBuaNqgt0ZZ22/qm9WpPtndrvLWNa9NW1vW6LWM8jwRpTcOajO+N223O8dx+bhiGVtWvSqs0u93mOp5LgmQYhlbWr0w7+TfHc0sIssWXMlIZv+t2mzVelgQumUpqdcPqtNsSqUTGbW7xub2/bselzmSwY5VbfO3J9ozjUnuyXeub1meNz208t+NSS2dLxvHGLT63z19TR5O2tGxJu62xo1FbW7a6jpetoux2XKprq9OOth2u4w0UJIQONx51Y6A/3TF37lzV1NRYf2pra/McPQCgO7LNIfzZgp/ptPtP08JNC63b5s6fq1PvP1VvbH4j4/7ZTtq++9J3dcp9p2jJjiXWbXNemKNT7jtFy3Yuyxwvy0nR1c9erVPuOyUtKfyvp/9Lp9x3iuuJVraT/C89/iWddt9paQnWFx77gk6//3TVt9fnHN+n//NpfeL+T6g10Wrddv5D5+vMB870TWrcxkqkEjrrwbN09r/Ptu7XmerUWf8+S+fMOycjacgWW0tni8544Ax95uHPWLc1dzbrtPtP0+ce+VzOz3V763adet+puvyJy63btrVu0yn3nqKvPvXVzOeTpfq7tnGtTr73ZP33c/9t3baqfpVOvvdkffO5b+Y83uLti3Xqfafq+pevt257d+u7OvW+U3Xjq5nnN9kWIHp146s67f7T9IvXfmHd9tL6l3Ta/afp16//2nc8t9fvidVP6IwHztAf3/6jddujqx7VGQ+cob+8+xf/8Vy+a/cuvVefeOAT+scH/7Buu3vJ3TrjgTN015K7co7vlkW36IwHztBDKx6ybvvLu3/RGQ+coUdWPpI5XpYLCL9987c6/f7T9fy6563bfvP6b3T6/afrpfUvZdw/2+fvR6/+SKfef6re2vKWdduNr96oU+8/Ve9ufTdzvCzHKvO4tLxuuXXbt577lk6+92Stql/lH5/LeFc9fVXGcemrT31Vp9x7imsSly2+Lzz2BZ1232lpF3QufvRinXb/aWrqaPKPz/H6GYah8x86X5984JPWvwkpI6VzHzpXn3zgk64XTAYKEkKHMyefqeljpquqrErV8WrPP90xZ84c1dfXW3/WrnW/WgMA6Ftp2064nHSsrF8pSWlXytc0dlUMVzWsyrh/tpNy80TKPt7axq5/E9wqA9lOKlfWrVTKSKVdtV/buFZJI2mNa5ftpG1F/Qp1pDq0uWV3RXBt41q1JduyJpjOpMGsdjR2NlrJpBlrQ0dDRqXPHp9bW1djR6O2t23X1tatVjLZ3NGsnW07tbllc0YlLduiI1tbt6q5szntddrWuk2tiVbX9zZbBWl90/quKo1tvI1NG5UwEtbnyGs8t/didcNqGTLSxlvX1PW5yRaf32fZPp7531njc0lA3MYzvxvdeb4r6ldkjtcQcDyX+FzH60F8rs+3wftYkC2ByzW+rMeCHOPLlgCb49mPLWZ8bseqrJ+XupVKGsm048jahrVKGAnrc502XoDXry3Zpm0tu6t6axvWqjXRmlE5zIjP8fp1pDq0vmm96jvqrQQzkUpoU/Mm7WzfqfqOzIthA0VJoQMoNncvuVunTDxFe1TuoQeWPaCHVjyU0UbSXfF4XPF47+6BCADInf3Kr2ubU7I142fmyYS96uX8mfO/rfESmeOZ/20+Vtp4Wdq62pJtmffbdeLoFl+2peHNpMrtZMx1PJ+TSnsF0G1D96yvn5FUSWT36Yo94XPbD7A10apBpYM8x3KynqvLe5ZIJZRIJVQS3f342U5Q/V47Z7Ka03gu8fVovO7E5/JZ9o0vmafnm4f43FoFs8Xn911zHc8tviwXEPoyvmzfNbf4rGOV23gux6psF8OsY6nLMSjrscoRX8pIWceXoMdSv/jSji0uC1G5xTdQUCF0+NH8H+m4u4/TLe/domNrj9UT5zyhX8z6hY4ad1ShQwMA9JJsFcL2RHvGz8yTIteWxywnvebvuJ1Em4+VNl6WBNPvJDprfI7xOlOdrnP4zBOjXMfLlsA5xzMMwzfBtJ8ku+0v6BwvW0uhOZ4hw2o39YsvW4XGSs49XjtnxTNbS6Zrsm94vxfZFuFwSxj83ttsJ/jWeC7vmdtnOVt8rif4u+7XnQTJ+q65XCzJlsD5HgtcPvPdSYDd4vNN+HsQX4+OVW7jdeNY5Rqfz+fFLz7788nHsdTtWGV/TLfxBgoqhC46U516ZOUjemTlIxo7eKzOnHymvnvEdxWLxPTJBz85oK8QAEDYJFPJtHlnrlULn5Po7py0+VZpcjypNAyjZ/E5T7JsJz2Bq0g+42VL4JzjZcwBTCUl245R9vubCYA9EchoGc2yyIWzKhCNRTMqmINLB+++T7YKTSIzQbLvg5hIJVQaK3WNz69i5paIulZ8AlbgXCsq3ah2u12MsKrTWardbvGZMQStEGZL0N0qXH7fjWwtlOZzcvtcdSc+t/e3J/G5XZDoyeuXa3x+728ylbQuvgWuKOeYwKWMlAwZ3YrP/vjWBva25+0W30BBhTCLlJGSDCmiiGKRvtnDEADQd+zVQcm/bSpolSFwVcXlJCtbS6Yzvs5Up2tlyy9p8G2bcjkpsu8vmLWl1RGfWwJnv78zPudJWpD40hK4pPd4fie8XvH5JZhBLx7kIz6v5NxZccyawLklDPlo8ezOxQO/+IJWzHrSgpolAfFr8cxXfG4JsJVgdiM+33b0LBcQfC9wuLR4usXnVwFOax93SWxzPZamfXcDHAucP+/psWogoULoojRaqhMmnKCzJp+lQ0YdoufXPa8fz/+xXlz/onXVAQAwMNhXGJX826YCz/PxOek1DMP3JDrrVgIBKnCSf1thvtum/JZyd4vPfn+/lsyg8aW1eCa8xzPbQqOR3dfDc44vWwuqz3trxVfmHl/Qio/5O4YMdaY6VRbbPWC2ClLOLXvZWgBzbX/O1uKZyGwZ7UkC59aC2pMErifHgr6Mz/X9yPFYkNY+nocE2PNYFbAlOONYkMztWNCddnS/Y8FAQkLo8J2Z39EpE7uWw71/2f361vPfUl17XaHDAgD0Evum9FIOJ/ndPCnqSHVYFxe71YYVIEHqSXxuV93T2qZ6EF+Q8TLazHziC9KC6ta2Fo1FXe8fKL6ALZ5mVTUaiQZ+vkEXHXEudGFPCLO1yPq2UCbbMhLmrIuE7BrP/j0y/9t1UY9sLZ4ui46Y43WrhdKtpTVghcu3hdJtjmOe4svHxaag8fnO0Ut4zNHrZnxuFbis8fldDMv23XXE59qO7jGe28Uwt/gGChJCh/P2PU8bmzdqXdM6HT76cB0++nDX+1397NV9HBkAoDdkqxDaV7Lrzrwcv7Ykt0Qq11VB3U6K7GPnurKg21X3tITL5STLfqLlu3Kfy0lWRsuo4yTNLz7Xti5nS6ZzPCOhUu2ew9eTlla/RVvM+0Yj0cDxBZ3z5zxJrYnXuP4scEumvYKZbFdFSYV7fN1oaXXqzRZPvwSzGFo8g74f3W1HT6QSrt9Zv9fPrx3d7VhgHzvXBNjtYk4uc/5843O5OJRzO3qWi020jA4A0eiuq10p//vNWz4vL22hRsrY/ZgAgKLlTAiDzCux3y/XhTPsJxWu83JyrMDZxzPvZxhG4JPAIPHlMucvSIXQL8F0nqT1tEKYMZ5f25nbeM74srV4OuIrjZYGrmDmOuevJ/F5jpdoS08IsyVwPvF1pjqVTCUVi8Yyfib5z1nrTgLnO17AizlZ5xD2YI6jMz7DMHy3dch1Dqb9WOR6rAqwxUvaeFmOVblebHKLL9ucP7/Pc1p8ARaYytaO7hZf2ngDeFGZ0CSE1dVdm8knW5KKDfJeHOa7L303L4+XbEmquqZ7G9gPBAf9/SA9cc4TGjN4jO/9vvPidzS+arwun3Z5H0UGAOmci8pktE15LW3ezXlXaeO5nFTmOofQLb5sc/5yjc9vHo3z933n5QSY55MxXq7zhnKNL8c5ifbft7eFdje+7uwJ6buqasAKptd4vvEFXWTFUXEcFN29L2S2OZi+1fhutHi6zkn0WUQn6BzMoHMI/Z5v2nY3bglmjov8ZGvxzHXLGM8Vh/2OVTnG5/ddyyk+l4s5GceCLO3oWccbwNtOhKaEtc8++0iSWlf0Tbm3fUW79tt3vz55rHya/a/ZmnHHDLV0tli3tSZaNfOOmZr9r9kFjKznOpId+sHLP9Ax/zxGR//zaF33wnVq7my2fv7Cuhd06n2nasYdM3TV01epvr3ec6xF2xbpU//+lKbfPl2XPHqJNjRt6IunAKAXZJtDmHWOXo57o7lVuOy/k3PbVLZ5LzmeVLpW4Hzm0WS0YfnF59aG5bPqptTztq6s8WVrO8vSghpkXlPQ+LK1FJorivpVCAO3oHqtguoXn0+F0G+OYy7xua6SaZsflvF9zdbimeM2EX7xpYxU7hXCgHPgutOOHuTiRrb4cl0QKlt8QecQ5qUd3a293e9Yle1iUzeOpQNFaBLCiRMn6uBDD1bDwoZef6zOuk41LWnS+eed3+uP1RtGDRqlp9c+bf39mTXPaOSgkQWMKD/u/OBOLd6+WP/+5L/16NmPakfrDv3fO/8nSdreul3XPn+trptxnZ47/zlVl1XrJwt+4jpOR7JDX3/m67poykV68cIXdeioQzXnhTl9+VQA5FFnMv0E03nSkdZGFLDF069q4dbmZL9frm1TWbdhcJyQ29tJnWN7xec3XkYbll98OW7r4Pb3nmwT4RpfjuNlbUHNcWGKoAlc4PGyJZhZKnq+8XVnPGfCGjQ+j9fFr6IcOD7bxZyMbTt8vhte3QJB2739vmuucwizXWzyacnMd3zmGPbjh+uxymeOo9uxNG/t6EGOVc45l7luaTOA5xCGJiGUpAvOu0BNbzepdWXvvaGGYWjrvK2KRqL65Cc/2WuP05tOmXiK/rPiP9bfH1rxkE6beJrv79y/9H6dcM8JOvauY3XPh/ek/ay+vV7fev5bmnXXLJ1878l6cNmDrmPUt9fry098WUf/82gd889j9IOXf2DN7fnS41/SA8sesO7b0tmimXfM1KbmTYGf1/qm9frYHh/TkPIhGlw6WB8f/3Etr18uSXpqzVPaf8T+OmbPY1RRUqGvTPuKHl/1uOvBc+GmhSqLlensfc5WPBbXZVMv0+Lti7WucV3gWAAUj4xFZYK2TfktdOHXNuVyUmlfDj3XtqmsLY+5tk25xJfTeH7xBRgv6xzCHmwT4Rqf23g9aUHN9zYWWaogfi2eri2PPtsSZI0v6Hg+CVy2FllrPI/PvF+C6fZemxVFt9clZaR855X5tRQGbUEN2u7drTmEfRhfXtrRXY6lad+NbO3jQb5ruXx3cz2W0jI6MHzlK1/RoYccqjW/WKPmJc0ZV4V6KtWR0qZ/btKOp3bo17/+tYYPH57X8fvKjDEztHTnUu1o26EdbTv04c4PdcS4Izzvv3TnUv104U/1q2N/pUfPflRvbn4z7edzXpijURWj9MQ5T+gPJ/xBN71xk5bsWJIxjmEYumDKBXrq3Kd07yfu1eLti3XXkrskSadPOl2PrHzEuu8za5/R/sP315jBY7SxaaOO+sdRnn82Nm2UJJ056Uy9seUNbW/drsaORj255kkdNe4oSdKK+hXaZ+g+1vh7Vu2pkmiJ1jauzYhzed3ytPtWlFRoz6o9tbxueZCXF0CRyTaH0LNtKh9tYrYTU7fHCzRetkVRAmzDkC0+3/Ecvx+orSuH8fJZgXONL8vzzbkFtQerqiaMRMa5iWtC7deSmWUOod/G5VnjCzqePT6flmC/ip5X4ui3UEjQ+bp+8fkl6EFaKJ3vn+94PWxH7058OY3XjW0YfNvR3Y5VAavJgePL5diX67F0ALeMhmZRGalrYZknH39SJ5x0ghbOXahB4wZp8GGDVTamTLGKmBTJfUwjYSjZnFTLBy1qertJidaEfvvb3+qqq67K/xPoI7FITCdOOFGPrnxUknTihBPTJsw7Pbn6SR0//nhNHTlVkvTlaV/WvBXzJEnbWrfptc2v6aaP36TSaKn2rtlbp048VU+teUr7Dts3bZwh5UN0bO2xkqSRg0bqnH3O0asbX9Vn9/+sThh/gubOn6vtrds1vGK4Hln5iE7d+1RJ0tjKsXr50y9nfV611bUaVj5Mx919nCTpqHFH6bx9zpPUVXF0LoBTWVqplkRLxjgtiRYNLh0c6L4Ail+2VUa92rp85/n4LOXu1taV7STLd2n4HrZQBmqbsic0Web85drimfPS8Dm2dfU0vpxbULPEl+0kNWWkFIvEXO+fa3x+c/68Kjm+Lag+La3dakF1jJdtI/Rc40v77no8bluiTdVl1VnvlzGeS4JpyFBHqkPxWDz38QK2ePpVWD2PVQHnEPqNF2SF4Fza0YMkcM7fDzSf2O9Y5Tz25XgsHcgto6FKCKWupPDF51/Uk08+qXvuuUf3PXCfttZt7fG4+x2wny6cc6HOPfdcTZkyJQ+RFtbpe5+uuQvmypChOTPmuP4jYNraujUtmbL/98amjWpPtmvWP2dZtyWNpE7bO7MFtbmzWT969Ueav2m+mjublTJSOnDEgZKkQaWDdPSeR+vx1Y/r1ImnasGmBfrRx36U03P6n1f/RyWREr3y6VeUMlL6wcs/0C9f/6W+Nf1bGlQ6SE2dTWn3b+ps0qCSQRnjDCoZlLYYjd99ARS/rKuMJtzbuoLO8wkyZ82v4iPlUHF0qSBl3dYhSNuUT1tX1jmEWeLLucUzxxbZbPG5Juh+J6lZ4nNriwva4mneNyZbQphjBTjrKp674jNkWCuk+sXn1wJoGEb2FlSfVVoz5uhlWSUzW3y+c/48Esxc4su2CIx5n7SEMOCcRLfW3PZke8YqtkErelaSamtHz7pFToAKa9AVbt3+7tYi67etQ9aLOS7zk/1aPHvaju6WUA8UoUsIJamsrEynnnqqTj31VN1yyy1qa2tTY2Njt8YqKSlRdXW1YjHvrSz6owNGHGCtsnngiAP19ta3Pe87smKk1jXtnj9nn9c3atAoDSoZpJcufEmRiH8J9tb3btWO9h361xn/0tDyobp7yd1pbaKn7326/vLuX1QaLdXMsTOtjXg3Nm3UmQ+e6Tnug2c+qLGVY/Xhjg/1zenftKp7n5j0Cf32zd9Kkvau2VtPrH7C+p31TeuVSCVUW1WbMd6kIZOsVlap6wCxrnGdJg2Z5Pv8ABQn56Iyvm1YHhVCwzDSjnGB59G4zctxnPDaN24OGl+PKlw9XGW0IPHlUIHzTULcTnp9VvF0G98twQza4mnetyxWtvvxsyTAfvG5bmPhOOmNxqK+8QXeCN3jIkhGS2bQBZI8Elu/+IJcfOlJfJ4JnOPzZ56fZBvPM8F0zOlM2xcy6Jw/l3b0rHP+cv2u9XaLZ0+PBdm+uzm2yLptszFQhDIhdCovL1d5eXmhwyg6vznuN4Hud8KEE/TZRz6rC7ddqMlDJutP7/zJ+tnowaM1beQ0/e7N3+myqZepNFqqD3d+qHgsnpFANXc2qyJWocqySm1o2qC7ltyV1sZx1Lij9P2Xvq/bF9+uL0/7snX72MqxWnDRgqxxHjDiAM1bMU+Hjj5UhmHooRUP6SNDPyJJOn788frN67/Ri+tf1GGjD9PNb92sk/Y6SeUlmZ+L6WOmqy3ZpvuX3q/T9j5Nf37nz9p/+P7as2rPQK8XgOKSsahMgJMO+3Lorm1i9qpAnufU+bVNdadF0XcbBrf4spxk9eU2Ea5tXXneJqIn22y4tnjmmGD2pAXVraLnXDmyVKWB4/NLfr22ncglvmwtlFnjc46XZdXNPonPb7ykR4un44KJPSH0bUd3ic8+VkeqQ8lUUrHo7iKGbzt6kV1s6nF82drRczwWDCShWlQGuZk0ZFKgqtdHhn5E3zz8m/raM1/T7Htn6+BRB6f9/CdH/0SbWzbrlHtP0ay7ZumnC37q2mL1mf0/o62tW/XROz+qq5+9WsePPz7t5yXREs3ea7Y2Nm+05hrm4prDrlFHskMn/utEnXTvSepIdugbh39DkjS8Yrh+csxP9KNXf6Rj/nmM6tvrdd2M66zf/fKTX7a2qCiLlemm427Sbe/fpqPuPEqvb35dc4+em3M8AIqDc1+zQNsw+JzoOMfwO6n0apvy23vL9yTVpYLkVwFxey6u84Z85tHk0uIZJEHqaQtqzttEZDupzEN8OS10kevCHj7xmW2hJq92y+4mmJ6LonRzDmG2RVayxpeP8XwqrL05nldi1t3n69aOLmVpCQ5wLMjluxbkWOrb3t6NY1XQarcULEH3O5YOJFQIkeaxcx5zvX3ayGmeP5Oks/c5W2fvc7b193P3Odf67yHlQzzn+9lvHzN4jG4/9Xbf+MYOHquPj/+4a+Uum+EVw/WrY3/l+fNj9jxGx+x5jOvP/njCH9P+fuCIA3XfJ+7LOQYAxSdbhdB16XWXE2N7m1iuJ6luc3fMqkC2q+7Z4vObI+X12M77+s35y6XC5TqHMMf4ct2GIWuLp0tVJegJedD4cpnjaL9vyki5bwOSY3zRWNT1sYO0oPq2UNreO7NqHo1EfSvKfhUpr33+glYwnSf8XvN/fcfzS4BdqrXO3/GreAdpH88an09FNNsqnmZ8g0q71jzI1o6ebT5swkioM9Wp0mip6+8HOlb1oB091/m6WeNza+f3OZYOJFQI0W80dzbrvmX36VMf+VShQwEwgGQsKpNjhUZKPxGzt5NKwU56fU9McmhRdIvPbBNzPqbX+Hlv68qxwtXXLbJuCXBvxpfLKqheC+50N76MikmOr5/firn2+/q2ZAatSAWsmPktouPVkhl4vG5UHHPZF9Lt4oHz+NHTiqjvBZ9cLuZ4jedzrMp5DmEyfduOrN+1Hm6509Nj1UBCQoh+4Zk1z+i4u4/TtJHTNH3M9EKHA2AAcS4qk7H0etK/zUnyPlF0+7trC2rASoTbeNnmmEnei2G4je/a4ulzktXT+HqyTYTbSZtX0uMZX44Jpl/bWTKVTGtB7um8K6/7drcF1eu++Wjx9IwvlwpcwFU8uxNfb1XgpOBzEn23ifC4OORXsfWdU2dWkwO2qbqNl6293fmY2baJcJtzan++KSOV9v3p83Z0l+eSbQXogYKWUfQLx40/LtDCMQCQK7NCGIvElDSSgdq6grRFev09yEmb135iruNlWYXSfEyzTaxbLZ6OlSbT2sR6GF+u20RkbUH1aIvsbny5JHBeLWrd3WajOwmc3+sXZA6hX3z2tlDX8dza9nyW/s9o8QywTYRfgtmdbSdySeCCVAj9FjUKcqHG7/OZ8bgB5hD6Vk1zafH0qBD6XgwLUE10S2rNVXZ72o7uXAG6pxebBvIqo1QIAQChZs4h9EpwgrRN5VIhdJ330sttXbmctAVqO/OY+yR1r60rrU2sp21dOazSat8I3Su+bAlmkEUsuhufV/XKbyGO7sTn11bq9/kL0oLqN15GsuK4EGJ+LoImwL7VeK85hDksCBVkDqFfApxre7bzPs520iDHjVza0XsaX3cSuKBtuUHva7+PuQJ0j+LzuXgwkJAQAgBCzUwIS6JdTTPdautyaTv0+rvrRugBrvR7jefa8pjDSVuQFjDf1qoetnUljaTvSXjaCZ5hZN+GIYe90bxO3v2SHr+TVK8EzreClEOLZ6AEs4fx5aMFNfA2DFnaI92qSPlYBTVoQh0kATEMI3hLa5YE2BmbM76MZD9AZ4Hv9zHLsSrIxSG/Y0G3xutufC6fZa/HDDKe23etPdmelpAPJCSEAIBQM+eseK2Ul7ZSodccQp+TtiDJXpAk1HM8t1UtA+zd5zV+oBbUHKoC2VbJzCW+RCqRXiHxaPG0Vxz9WigDJUjZ5iH5JJiBWih7Gl+2ltFc48thZcZALajOx/RroQwQXy77JAZatdRvm4ggCUjApFbyb5HtzsWcIMmU3zy+rMcqtzl1PTgWBBovl2Opy3EjyCJPQcbzPFYN0CohCSEAINTMhNCsEPa4zSmHk7buXNUuSFtXN+NzboTudoKfS3xe87Psv5PRJuZTVfGq/gVdJCRjPI/4ctkmIuf4srWMBtiTMpe92/zu6/b+ZjymYw6hPXnvaQUzSMUnY7xuVjC99iTNpUU21wpcLglSoPbxXI5V3YmvO+Pl+1gaML7OVKdrG7DfRYaBhIQQABBqzjmEvd3mlG2bA+d98tLW1dM2sQBX+k32VQKDVJC8HtNtvCAtlM77+T1fr2qdcw6h3zYiQbYw8E2QehhftgphkGTUrwLnW8H0eGzf18RnvCBzHHNZddOr0u6X8KetctmN75r9M28Yhu++izm3j2c7tgRYACunY0uOF8NyWdG3p8/X2T7enYtNaYtbBfiuOccbSEgIAQCh5pxD6DfPJ9DS686Kik87qNsJdMZ9ujGHMMjWGSb7CZRzI3SvkyK/+HKdA5cRn0/Lo1d7n28i4DcHzqM6FLT1MGh83Z6jFyC+bPsQFjy+fGyzEXDOpN+FC0OGldgHeU3cYnVLLoKuROoan1t7doDvt9ff3dpBfY8/uRyrPNrlvRbucft7oBbPgMfSzlSn64Wa7h5LPeev+hxLBxISQgBAqJnthV6LyuS89Hp32rACtG06f8f1vl5VC59VQZ3VMLfH8n1N/E7wA6ySmRFfLouidKPC6leRCjJvyC+hDnJS2Znq9J2nFihBCpB0u40XaJuIHOYQBokvl21APOPzS0JzmZOYSiplpGTIp03VZ85fkO+a38UIv899kAWcujM/uSfHqkCrlnps7eH8u2EYubfL+13cyMfFnBw/y1LmZ2qgICEEAISauTG916IyuS4kke2kzfUkyy+B8zmBTqQS6Ruh9zC+oCdFQbexCNKimBFfDidtQdq6/E7KgyRIzvv5tZ0Fjc/vJNqvahGkhdIvPq9tInzvk0NVJcg2Fn6bl3tuY+H3GfCZk+g2fzFbC6DfIjVBVvTt1S1jsiVwObaMdqeC2d34OlIdaYl4oGNBkR+rBhI2pu9D7e3tam/f/UVsaGgoYDQAACmzQhjkBKUnbV1BKoR+LZS+m7oHaOvKSBhynAOXdbwA8726W1UJetIWdJXWoAlc4BbUHOIbVDoo5/jcTnrNiwJe+2gGii9gC2V34stlGxDPbSxyWJQnZaQUi8S843OUQtLaSh1z/oIsUJJTNdnvc+XVguq3Aq/PcaOn8Tnbx7sTn+/FkgAVTN+LEXloRw9yrPI7lg4kVAj70Ny5c1VTU2P9qa2tLXRIABB6uSwq09O2LudG6J7bWHT3qnaQhSRyaXn0avH0Gy/ICX6A5ff7Ir4gq3g6n0e34gvQhuwWX9AE07clOEA7qPM+9ipbt1o87eP5bDvR3fi6PZ6R9K2qOfeY86uYmZvE+yZlPhdLnPF1p8UzyOe0uxdzvC42dbvF02v+bze3schLO3p3KoQsKoOemjNnjurr660/a9euLXRIABB6fttOeK5kl+eTIt9WrVxOivIdX4CTou6ctPV6W1e+4wu4dH23qiB+LZQBtolwxpfLNhFuFySSRtJ35c5ALZ4+FcJcqz5m4uX1mLlUlBOpRKCqmttYbuO5JZhBP8sZ9w1yLPAZL5lKprePex0LuvlZ7lZ83TlWdTe+fB8LPOIbqNtO0DLah+LxuOLxeKHDAADYOCuE9hMA50p2ga5q+5wUBVk0w3m/3j5p822b8kpofE7aCtKC2s2T/Iyl5gNsY5FLfF7vh+/m4PYKXICN2rPFFygBdomvNNb9FlRn0mgYhiKRSNb43PZxzDaHK6dFg4yklF4EzG3bBJfXL6Pq57JQjOd4QVYZ9dniJdfqai7xBWnfdcaXy/za7lwMCzRevo5VXt0gVAgBABh4nBXCniZI3amoBNmawm08z0U9cjmp7GFbV1+2eAY9SQ180tuNCqZffJ4JXIDFP/IVX7cSzG7GF3SfSXOebnfi84st63gu8fm227rMR7RfEApUIcxlI3SX+Yo5Xczxqf56fTeCXiwJevEl6HejW8eq7oyXr2OV1+vHHEIAAAYevwphXtqmclzUo0fj9bStq7fj62mFqzvj+cQXeA5hwFVQuzUnMYcWT8/nG7CK5Lbqput4AZOQoBXgnlRY3Z6rmaQ5E7aM8Vziy1jYxWfOn/25ONvHJY8W1KBz9Fyq04Zh+CeYOVw8CJLAdeu71t348t0un4cKYXeOpQO1ZZSEEAAQan6rjHq1EfkuAtPDRTOc98vlpKg78XWn5bHHbWLOk/Kgq4wGXAkwaHwZJ+U93MbCGZ9Zffat0vh9Xjziy2URoiBtcfa5Z874ckkwk6lkV1LjkyTlsshPwkhkPL60+/vhfBzneG6fl1xW3bSPl0glXBed8Wv3zqXCZY7X3QWmAld/g7ajB/2uFepiWMB9CLubYAZ5vgMJCSEAINTMCqGVEPawgtSdio9fW1Iui3B4nRSlneDn0JLp1SIb9AQ/cAUpYIUr79tEBD2p7GkVpLdPegsVn1sLpUuS1pOqj9942Sp6Gd83lzl/nalOz9fBPp4z+fUaL+hzdWs9TBr+4+WanEvdb6F0e+1yHS/QAkl+x1K/dvmACVzgY6nX9ACfY+lAQkIIAAg158b0bkuv26uH9rYu83a3k8Dorn9i3U54rfmKjrYuazyXE3zX8ZLu45n/b97uNicsyHjOk0BrPJcKZk7j+cVnOMZzSZCc1Vy/+MzHcovPPFnMFp9bFSmX+Jzvb+DXL+H++vl+/nxeP7fPc67xuVVY7fHZ43cdL8f4zJ9HFLH2F3SrEDrHs89bdBvPvM1tvKjt9Nj8LJivcUQRRSNRz/F834tdxw+3186MPWM8l5ZH671wuTjknAvtHM/tYonbeM74vMbzi8+tgpnx2evmZznju9FL3zW/Y8FAQkIIAAitlJGyTnT85hCWRcus2+xVEPN2t5OiWDSWOV4yfTznSZE1nstJket4CY/xuhuf13h9GV/KMZ7LSZs1Xir3+NxOorPG55LwW+O5VC2c8ZmPad7uVrVwi8+8n/l7zoUzXMdLOcZzWdTIGs9RYQ0SX1oFPZkZn/3nzvHs7aTO+Owbodvjs5KCSNT6HXM8+2vlHM/+nqXFt2s88/tuH89KGKK7T4/N+9sTBjMhtMdnfwyzBdf5WUkbL5l5bLHHF+SzF+i70d3vmtexqljj68axoDvHKuYQAgAwwNjnTrnOIdx10mEuwW/+3DyRMG93qyCZ1Qy3kyJrSX/HSZF5u1uFy3W8pMd4PvFZJ0UR74TG/D1nBdN1vJTPeEmP8RzxuVUtrPFcEjhrPMM9PreFPdxeP/N1zhafa0Id8T6pzBaf3+vnF1+Qz0su8ZkVK+uiSI7xuX2e7e+/8/Wz/64zPvtj2uMz35NIJKLSSHp8fuOZnwF7ImmPLxqJqiSSXuWy/yyi9G0y7NVfe4XJjM9+jDCrYc5jgT1mM760Y4stPvtrZ1YVM44tLhebzN8zZHRd8HLEl7YCr994AY9Vbu3yrt9d57HKyH6s8h3P5bPsFp/bfNggxyqvYymrjAIAMMCY8wclW4XQ1tblWiG0nbQFuartdlKUUfHxqSDlUuHyqiC5zcvJpYLpjM/1JDCHq+7O+FwT4B5UBdwW9uhRBdNlnpRbfM4KV5D48l6x9amquMVnXygll/iSqd0bodvjc6sQmklIWgLniM/+mPb47G2DzjZAezupvY3Tfp+SSHoCZ1UBI7bxEunjRRXNGM+tQmiPz15xtBJgtwphKj0+++/Z4zNfA3tSl0t12hlfoGOVS4KU9VjQzQqhcw5hTyuEXsdSt3b0IN81z2MpLaOA9NaWt/SvD/9V6DAAwFfKSOnW927Vsp3LfO9nTwjT5vI42rrsV/HtbXH2ypLVJmbOYYl4r1qa9aq7y0mW63jduOpe0PhyqGBa42WpcEkKVOFyi8+sHjjjc6vSBInPWfUJEp81RynivQ9m1oqtS4JujZelqmJ/PfwqrM747AmcPT5zvIgiu+etJdNbMt3iM2OLRqJpFRx7AudsGbW3k5ob3ztbRkuiJbt/Zm9BlW28pM94jvhKIiVpj+WWYDrjM5+ra3wR9xZU+zHHGV+Q74YzPvt7a17wyuVYEOSz5xtfPo5V3alg5vlYNVBbRkuy3wXoSgT/963/1asbX5Uknb736SovKS9wVADgblXDKv38tZ8roohOnHCivjLtK5o8dHLG/cwkLhrZXRGQuk4USlQS+Kq71HUlurSsNPOqtsuiGebvmW1dbhUkwzAUiURyqyB5zcvxmwPnU8H0rEj5zEns1Tl6ASuEbptvu8VnVg+cVYFcKnB+8QWZo9edCqFvlcY53q6/2zdCt8dnfz2c49nbSZ3x2Z+Dvbpub/F0LsRhf62c8ZnvmT2Bs1fjXcezPZa9amePL62iZySlXQXRSCSyu2XUEV8kEslYeCStZTS1+7HsiwyVREqUUGJ3y6ijmuwZn6JKKZVWISyJlCiiiAwZaku0qbqsOuO9MI8f0UjUdU6i17GqI9WheCzu3y2Qj/nE3ZlD2N05id04VvW0W2AgISGEL3siaO+BN2QUMCoAyMIw/8/QU2ue0hOrn3BNDM0KYSwSs05CpV0nCjFbxcfR1mXNy3G0iVWqMnPei9uiHjGPFtTY7pORRCqh0lip77wX53gZCUPMp8XTZU6Yc7yMtq6YzyImPnPMrPGcbVixzAQuyLwm53jWSVvM56TSZw6m9fo5T1LdxgswR8/5fmSM59YW54jPvhF6TvE5x3OZo2ePz/56OOOzt5M647MSpEhJ2mPZq2zOBM7+WnnFVxopdV3FM208x5zE6K7/ucZnn/OXSlq9cdHI7t9xxme/QOSMzzmH0GpbNRPWpEt8EZdFamzxRSIRyXAkmLueb2eqMyO+tDmJqaSisWjGZ898LOexwHwu8Vg887PsUj03fy9lpNIW4TJvb0+2W0lpxrHKbYEk57HAeazym0PodvGlB8cq15Zbx3hWguky3kBCQghPZ//7bH2480Pr7/YDxRce/ULaAQ4AionbCfzjqx/X46sf19F7HK0/nPAHSekJYdpS8462OLPKYM7ncZ70JlKJzJO2AFehzcdyXtWWuk7ISmOlvlfdnfOQnG1d9qvaVsXRr4LpqJh5XXUPPAcu6Jw/l20xcplD6EzQXRfOcFsl07HqZrfmTPq8HxltZwHis28WbyZk9vjME3Ov+Lw+L/b3LG2bCJc5f66Ltjjis28jYLU82lsoFd39fB3bOtjn/Ll+15TZQhlVNHNRGVsC52zxtMeX1iK5K8e1t4y6JZgZLajOBM4Zn1vLqCPBtM8HzPr62RNCR3z2imPCSKhUpbtbHm0XqezxxSIxK4bWRKtq4jW+3QJmfNnmOEpd711FSUWgY1WPVhntRgWz28cqZ7t3NLNFdiAhIYSnidUTtaxumQzDyKgIThoyKW2+DQAUk4aOBn1Y92HG7SXREk2snmj93dyrzDxZMnlVBZxtXWbbWUKJjJNosx0tra3LZZ6PvaoSi8Yy2sQy5r0EqUg55r0YMqw2Md95Q17zaLo7LyfgSoA9nUMYpALnOkevB6uCOufA2TdCz6Ui6vX6pbVkuszRs9/uVgVxxudsUTTjSlsV1HHSa08QMuLb9RzMz6z5XOxtl2YlxrlNhF+LZyy6u1rvbEF1JnBpLZ6ORWCsFtRIiZKyJfq7vua5tqC6zfmzx2df9MbZgmpWMM3jh/05OFtkg1RY7XMSgx6rzFVVO4wOz2OVX/XcGZ/9GNaWaOtKCAMcq7LNIexMdSqRSqgkWlLYY5Xz4lUs8+LVQMIZPTz94thfaFvrNv1t0d/0jw/+YV2ZlKTvHPEdVZRUFDhCAHC3om6Fnlj9hKSuasSg0kH6/AGf16f3+7Sqyqqs+1lzCKPR9JZRx0l5trYue5uY17yhaMxjno/9pNJWFchYuj7APBqzemSdFEXTT9rc5g0FWbXU7aq71SbmrHD5rHzoOafOrWW0J+MFnKMXdFXQXCtw+dgbzRwvokjaCaxbRc+3CmImIF6rZNoqehkJiEuF0DUBydbi6RjPXtHLGl+ABMne/ukWXyqV2v1YZoUwS3wZLagui9TY47PPScxYBTUSSTt+SLZjS8R9Gwv7++5MMF33NXTG53Gs6kh1WPE5jwXm8SMaiWZUz53xmYv/JI2kZ3xp7e2OPSut6r5jXqv5+pmdF2njBZif7LcqqN+en4H3XRygFUJ6/uBrRMUIfWP6N/T4OY/rs/t9VqXR0ozFFwCg2JiJ0ODSwbri4Cv0xDlP6PJpl6clg1J6y6i0e66P1dZlX7reoy3O66TSPs/HubKgV1tXWtuZY2XGIHt7mY9vVYlsJ9de8QWZU+escNlfG685a67jOU/aXK66d2c8Z1UgyD6OaRuhZ5vz57btRMQ9gbPHESQ+rzmEngmX25y/xO45f+aFW6/47Ktk2sezV8yc7619PKvF02wpjJS6jue3TYR9zl/Gdy3qiM85R0/+LZ5Wgp7IbEH1SuCc20SktYyaq4y6zEkMusqo/bzJ+foFSoCd8dnOwTLi8zhWpa366ojPbZ9E6/2NZT9WZWyz4fLdtdrRbdVzwzAy2qml7h2rvOb8JQzbCtB+84k95hA65zgyhxChZiaGlxx4idY0rFE8Fi90SADgqba6Vn8+8c86cMSBGUmgXWey60TBSgidbV1BqgIeJ0Vp21iYVQvHSWVGW5eiGVUBq20q6t3m5LWyoDleh9HhGZ9rW1fUo60r6tImFiC+bHPqOlIdSqaSikVj3RovyKqb5njO5NxvPKuFMsB4aRuh206IDcNwndNpsraJMMdzafG0Jyf2CwLO+OyvlVd8aXPWUv4JTdqqoM6N320to27j2b8bzvHcWjzTWjJtyaJzFU/7fd1aPF0TOHsyZptD6HUxxzc+jwTO7Vhgbye1Hz+c8bltY+HW0po2HzDHY5V9245sx6rSqPucRLdjVbvaczpW2cezdzNEo7aKY0+OVS7dEdYK0M74XC42ZRvPvgL0QEJCiJyMqBihERUjCh0GAGR15Lgjs97HmkO460TJ2dblulKhy7wcyaWty+equ7Oty69K46xI2du6XOf5uFRVOlKZ84aCrLTnnJdjX5jCa+VD1zasLBU48/kOig7yHC+Zsm2EnkNFz6tq4bqPnteqpX6rgvrMMUsY6XP+3KoMXlUL54JG5s/c5hA6T6B943N8lv22dUir6PklSC7jpX03XCpczgTJa5uI7rag2lsy0x7LtsqoZwLnEp+9Auc2xzGqqCLRrtutje7tCaajBdVtTqdrO7pLfBFltqB6HavsSbO5KE9GO7rLscqecJnzmoMmwEGq++ZjObstkkmXFtQAFT3nxSZ7kuu1AnQu49lXW+1MdaY9j4GAvj8AQGh5toy6bG5trwqkVS1yaOuy77VmfyznHEIpWFuXVwtqkJNKv5M2a9XSXW1drnvLORb2cKsKOFcqdLZhObftsI/nlSC5jeesOKa1iXm0nZmvXSwSy3gtnOP5bjvhl8ClHAlc1KVlNEt8aXPMbHP+Ior4Vgg943MkSG7V6YxVQc05cB7P1228tIsbjgTJLcH0HC9AS2tawuVskfVIMP3is4/neixwaUHN1uLpXKTGrR09aLeAWwuq27Eq13Z083fsz8HZwut2rPK72GSu8JrtWBWk28K1XT6afjHH9diX5eKVvX3cOZ5bxXEgto2SEAIAQsusEJonUEGqAkHburqzMIXvSYzL5tZecwiDVFV8F6mxXf1Oa+uKeC/970yq0jZC96jouVVYnc/XWUGyP1+vOYmSPDcHd7Y8OhMGt/Hakm3Wia3nePYWT2XOgUsbb9dztbeTOhe68Dohd33tXFYFdY7nWYEL8NlLS0Ac45lrC5ivm9scQmcCkpZwpXwSGo/5us4WVHuC6ZfABd4mQpkJXLb47HMSnRdz7Am1W3xu21j4VWzTEnSX+NzmYPrOd86ySI1rguk2nsuxypzTmrVCGCDBzKWil8uxL21BKI89Xe3fa/v9BwoSQgBAaLnNIZSytMUFTOBc287cqj5GetUno83OnPdiX2reo63LGZ/bSoXWVXfH0vD2jdA95w25zUPyOMnym6PnOl6WpfDTFs1wVIOcc4P8Xr+ME/LI7hM9r4qj2SbmO57bnLWU/7YOaXP+PLadyBjPpVrrTEAkn43kPeJLGy/pMufP47th38MzmfKvINmrN37bRGRtoQzwXXObg+ls8cxIqG2PlbEKqsc2EX7x2R/LbxXUrC2yLvFlvB8ui8o4K6zO71pai6y8E0zXY5XLNiBBj1XWz1zmO9vv63VxKJHa3QFgjmdu7+N3wcTr2Od7rPI51g8kJIQAgNAyTyrMEw6vvdGcJ22uJ73Okza3qkWWk3K/k8q0zahdqiquVQGXNjG3peENw0jfCN12FT+tauGyUqGzBdUZm+TdhuWahHgsNe9VoZHcqwLOqo/Xtg7O9zZlpKy9d+2vQ8ZKijluw2AfrzXRmlYdzCU+twpNRgulfPbR86hg+iZcARdFsceX7wqX66qg9hZP5xxHl++a5xw4l/hyTuDc3g+XFs+MBNhrDqFPBS7t2OL2/XCZQxiNRDNalv3mYLrOmXQkwM5ugSDHqrRVSw33CxxecwitFVpdKnpWfPZtOzwqys5tMVzbx81uhpT3sW8gISEEAISWcw6hs63LbV5TRoLkcVW7W21dAdqmzNvsFb20hUw8EkxrnpTLvKGUkUpbOCVj7zG/8RzVuWwn5GaMkuMk2jFekApXRguqfR6cx8IZfhU4+xw482dm8pRtoQuvE2i3OYTS7pVVTX7bTqRVfwMusuI3R891mwOf9yLotgkZq5Z6zEl0u1jiFp/nNgduCZKjBdWKz2uOntucP785ei4Vaq8WT+fFiLSWUWd8UZ92dJ8EOEgFM+P1c7a0Bk0w3Vp4fV4/57HK3j5eGil1//z5zMG0ji0+FT0rPpfxPLfIyVI9N8f0im8gISEEAISWtcqos2W0O21dOc4hdK1ayKUNy94WZ0sM7FfJA7V1eSwNb95m/tx+JdyKz+UkP9veY26rbvq2dTlbWj1OAmPRWEai7VexdT5f5wlv2ni25Ffyr9J4jedXobFXTtoSbWmP5RwvbeNy+wJEtjZO8zVvT7YrZaR8t2HwHC+VOZ5vy2i26rlLhctZTU77bgStxrss4OTX4mnfFsPeEuyXYPpuE+H1ebF/9jxWHHZL0LO1eAad0xn0WJV2bHHbZsP2mU272OTRLu86Xsrl2JJKpu0zGovGXI9VQS6GmccPMzZ7Rc+Kz+1Y5bUthsuxyvk5cjtW2Z/PQEFCCAAILa9VRrPNIfRbGS/tpNd2Up4yUlYCmpE02NuwHCtHep302q+Sey5So2jmvDWXCmEilXCtXpqP79tm5zhp81s0w4ovlTmetfBIlvFKI6UZiXaQKo3vqqD2io+taudMMO3tpEE2kncusmJu22HG51ch9Kzo2U540/ZaS7anPZa9LdQZn9eCRhmrlrrN0XOriLpUMP0+K64VLq+KaNAWT48Eyb7ojT2Bs1/4cE2QPBK4tPE8EjhnRcptDmHWOY5+czCVebEpbdXSLHMwnVvaOOdg2tvHc+lmsD+WKWEk0ipqsUjM9fPnNn/a2Y5u/o7nscWjZdmrvd2vQmhu7+P6eaZCCADAwGElhFFHy6hzpUePVUb9rmpbe4XJJYGLONq6/NrEXKoCidTukyzzdq+qj+c+iY6VBe0nRea45uMHqdJkzKlzqTBY8fm0oGas4pmlrcs8ccsYz9Gm6DXnL62FzUhv8XQmIWkJnM+cP69tGCSltVHaH8t3zp/bKqPKrDj6VZDcEmCvOXCdqc6MCwvOiqNby2hafH4JUsRlvGzbMPi8t64tsi4tmRkJnLOl1R6fowU16yqjWRJMv3Z01xZUZSYgXi2ohmG4JpjOY5WZ8DsXIXIeP+wJj187utfFJufnz/lZdqt4+1YI7XMSbfHZLx5Y8bm1o3scC/yq++b93VqqmUMIAMAAYi4qY56gOCsJ5s99510FSOCSqWT6SVaAk2i3BNN+Im9vczJjyYjPZ16O31X3tPGyxWe2ie2Kw2rrcjmBtuJzSUK85iEFTZCs8ZwJsEdVwLVCY0t+zdfcPp69xdNzvIh39df++rYl2tyryYbLNhH2io+jLdSMwx6f86Tb6/l6VX+lrpNotxZUZ0umc86a68UDtwqXTzU+bTyXje4zWlBtFVG3bTvsewq6VqddEqRcK5j274ZzgaTutHhmbUe3/U5G+7hHO7pngqn0lmB7+7jztXC9eBX0WLXr8d0umPjOIbSvWuqWYOZ4rPKdr+voZnCr2LLtBAAAA0hGy6jtpMh5kuV5UuRx0us8UbGvZOeck2VPkDKWhndrGU1lVvS82rq85g2ltYwaiYwE06vKFeQqflp8kSxtXR4LXVjzfBwnbbHo7rbLlJGytg5xxuc8yc+Y85fMHM/Z4ulsE7NXCK3xsu0r56wQ2uJzq8BlVFid1WnbCa/9dW9Ntqa9734LGrlWf23JZUZ8kSzbErhc3LC/twkjYVUdzZ85x/PcJzFgBS4jYd01nr1NN62F0q3abWsn9Urg0sYz0hN0vwTO/v20b4SecUHC3o7u0ULpnIPp2T7uuCBhv3hgvi9u8XkmXCn3CrXrfGe3Y5Xz4pVHi6fvscXlYljWY5XHfGKzu8DtWBAkvoGEhBAAEFrORWXsJ972f/Tte605qxZBTorsLZ7OBM65UIhXG5az6pNR0bO1ddmv1DvHc636uCSYrklSJLMi6jbPxxmfs63Lrw0wY6l5RwJXGt09h1DaXeV1G8/eTpoxRy/h0kLp1uJpbxm1VSOdFUwzfvtJpbPlzPy5eX+3FkDX+FzmEJr3N5f+b0+kzyEMNMfMccJrT0Ls8blV9FxbUD0qPhnxubV4erW0erSgGobhmwCb45VGS7POgQuyqqr98+fawuvTgmqvmCVSifSLTT4tstZnxRGfvR3d3kJptY9nqYgGbUd3OxYE2SYiowXV61hlBKsoZ8x39ovPpaLs1S1gvqb29nH7xavOZGd6O7rj4tVAQkIIAAgta2N6l30InRUar5O2jIU43Ko+Lm1Tbif5WdvE5J3ABV2YwnW8LG1dQRbisJ/8O+OzVxnSNkN3G89s6/LY6N5ZcbSfXGfM+bMncI45f2Zbn/ME37PFM7m7xdP+c2dFKmPREdt7Kylt7zbXjcbd5mDaK1K2CpdXfG5z1jwXlbElfWnj2V4/55y1tPE8Kpj2VUud8dlX8fTbU9NrWwfzM+72+rk9X9eLJT7zdZ0VTK/28bRVS13mmHklwGkXm6LpF5vs41lz/pKObSK85uhFXI4tRvZjldd8Z9cWT7/x3C5w+B2rPOZ0uiasubaguiSYWbsZHHMIzQuGXuMNJCXZ74J8aW9vV3v77n+4GhoaChhNuidXP6m3trxV6DAAoE+9s+0dSbtPoHI9KfJLaJytVp5tTo4qg9XW5Twpkndbl/lzZ3xubWIZVRXDo0KYbV6O35xEr9fP8K7oZSSYkWBtXfaE0NkGmNbiaWtftD+es6XQaxEYe4LktsiK20IX9gQuo2XU1uLplsClzfmzb5vgSDCtltFEq+KxuPUzZ8ujfc6fyVlRscZLdt3fdQ6cyxxM1xZUW8ttIpVIa0H1W8XTWTFz29Yh4/Xza/F0bHOgroJP+py/RKsMw/Cc42j/jDnH862YuSTo9mOB+dnrTjt62rEgSPu4oopGo67jOS9IOOfueVYwnccqjwq153znLM8344KEkf76+R2r/CqiaXMS7e38u7YUiSgiQ4bnxaaBOIeQhLAPzZ07VzfccEOhw3D16sZXddeSuwodBgAUxP7D99f0MdP1/LrntaFpgz7c8aEa2xslSRWlFZo+Zrrmb5yv1Q2rtbxuuXa27ZQk7TN0H1XHq/XQiodU116nBRsXaHvbdknSpCGTtLphtba1bdPi7YutE/eqsipNHzNdz6x5RpuaN2nJjiVq7Oh6rP2G7ae2ZJueXPOktrdu14KNC1TXXtf1WMP20Rtb3lBTZ5Pe2fqOdrTtkCQNjQ/V9DHTNW/5PO1s36n3tr9nnawfOOJArWtcpxfWv6BNzZu0YOMCNXU2Wc/5qdVPqTPVqTe3vKkPdnwgSRpZMVLTx0zXXUvuUkuiRW9vfduqpB486mCljJQWbFqg9Y3rtWDjAutkaurIqfrX0n9JkhZuXKhlO5dJksYMHqPpY6brr4v+qmQyqdc2vWa97oePOVzbWrfp7a1va3XDai3YuMA6iZ42apo13qsbXtXKupWSpNqqWk0fM13/9+7/SZJe3/S6pK6kYMbYGfpw54f6YMcHWl63XPM3zrceyxyvI9mhBRsXaF3jOknSXjV76dBRh+rWxbcqYST05pY3JUll0TJNHzNdb255UysbVurDnR+qqrRKUteJ44EjDtT9y+5Xa6JVCzYu0MamjV3ve80kTRk2RXd/eLfaEm1atHWRJGlQySBNHzNdL6x/QRuaN2jxtsVqau96L+KxuPYbtp8eXvmwGjsatWDjAm1t2SpJ2nfovhpWMUwPLn9QLZ0ten/7+2mfo8dWPaYdbTu0aOsilZeUS5IGlw7WR4Z8RE+teUo723ZqwcYF2tG6w3rfk0ZSj656VA3tDVq6Y6kkaUj5EE0fM133Lb1PzZ3NenPzm2ro6LpwXV1WrYk1E/Xi+he1tWWrFmxcYP1s6sip2tS8Sc+sfUY72nZoZX3X+zRyUNfn6LbFt6kp1aSFGxdqRf0KSdKw8mEaXzVeCzcttD6XzZ3NkqRDRx+qkmiJXt7wsra0bLFO9scMGqOZY2fqL4v+opSR0ivrX9Gq+lWSpFGDRqmqrErvbHtH6xrX6eX1L1vJzowxM9TQ3qDXt7yujU0breRmj8o9NGPsDP198d8lSS+tf8n6TIytHKu2RJuW7FyiVfWr9Mr6V6zP0RFjj9Cq+lVatH2R1jastb7ve1XvpUNHH6p/Lvnn7s9E80brM2sYhnX8MBOLeCyu6WOm662tb2lZ3TKtrF+ZdvyorarV/cvuV0NHQ9pnYmL1RO1o3WEdP7a0bLHe9+ljpuvlDS9rbeNaLd25NO34UR4r1yMrH7E+Eztbu45jk4dM1oc7P7SOH+Z3sDpereljpuuJ1U9oS8sWfbD9g7TjR0N7g55e+/Tuz0R712di32H7av6m+dbxw/xuDK8YruljpuuBZQ+ovqNei7YuSjt+DCoZpJc2vKSNzRvTPhMHDD9Aj6x8RMlkUm9sfkNLdiyx3vfpY6brH+//Q23JNr215S0r9kNGHaL2ZLte2/ya1jau1YKNC6xEzn5sWbBxgZbVdR2rxlWO6zq2vPN/ShgJ69giSdPHTNfG5o16d9u7A7JllISwD82ZM0fXXHON9feGhgbV1tYWMKLdjhx7pAaVDCp0GADQ54aWD9UFUy5QRUmFhpYPlSQtr1+ura1dJ181ZTWaPma6RlSMkCStbFhpnQzvM2wfjRk8RpLU1Nmk1za/ZiWLk4dM1mubu5KfJTuXWI83rGKYpo+ZriHlQyRJS+uWWic++w/f36qg1bXX6bXNr1nJ4pRhUzS4dLC2tm7V4u2LrRjME++qsq5k5YMdH1gnPlNHTrVu39G2Q69tfs06mTlg+AGKl8TVnGjWO9ve0drGtZK6Toanj5muipIKSdJ7296zTq4PHXWoFc+Wli16bfNrVlvVwaMOVkmkpCup2vqmVjeslrQ7gSuLlak92a63t75tvRbTx0zXhzs/lCRtaN6ghZsWWlf4Dxt1mHW/1za/pvVN6yV1JXAzx860fmaOVxIt6Uq41r0gSVrbuFZvbH7Dup85XiKV0GubX7NO1icPmazDxxxu3e+drV1V4/KSck0fM13/WfGfrve9fqVVWSiNlmrqyKmSuqoZr21+zfq87DtsXx086mBJXdXQxTsWS9p9sj5m0Bgt0iItq1tmvYcVJRXaf/j+kmyfo/auz9EBIw6wPnutiVZ9WNf1etXEd38uV9Sv0Ps73rcSwsrSSu0zbB9JUn1HfdrnaNrIadZnoKmjyUrShpd3nawPLR+qzS2b9d7296y5a0PKh2hSzSRJuz9HLZ0tXa/r6MOs97Cuvc56382T9aqyqq6LGNvesRKaERUjNKF6gqSuz9HCTQutBZ5mjJmhpo6upGN763arqjOucpxmjJ2hipIKNXc2640tb2hN4xpJ0ujBozU03vXd3di8UQs2LbDez6PGHWXFt7lls1U9G181XkeOO9K634JNC6zP2B6Ve1jfybVNa/Xalq7vcXmsXDPHzrQ+Y+ub1lsJkv1z2Znq1MJNC7W5ZbMkaUL1BOv1Wtmw0nr9K0srNX3MdP17+b8lSWsa1qQdPw4ccaD1vr+2+TUrWdx7yN5aXr9cUtfxwzx/Mz8Tw8uHdz1W/cq040dNvEaS1NjZqNc2v2Ylix8Z9hFVrd99/DCTKvMzYf7e0rqlaccPc2zzWGW+FvsN3y/t+GHGbX4mKssqpWbp/R3vW8ePaSOnWYny9tbtem3za1ayeNDIg3YfP7a9rZUNXRcdxg3uSuDKS8q7Esxti3YfP0Yfpm2t27re9+bNem3za9ax9dBRh1rv+xtb3tDahrXWZ2L6mOkqjZUqkUikHatmjJ2hlJHS6EGjdejo3b8/UJAQ9qF4PK54PF7oMFwdP+F4HT/h+EKHAQAFZW9TNE+KzJNss8KXMlIyDMO6f0Ws68THvH9Kuzd0trZiMAzrRMW8vzmeYRi7x4uWZKyGaX8ss0XLkGH93DzxMn/PMIzdm0pHSqyfW/HZNpw2x0t7vrHytPFSsi3MEo15jxcpUSwaUyLZtTegmUSar5/5Wtjn9TnjMzd9l6SyWJn13874zBbGlJHKmMNoPl4ilbDeC0kqjZWmvZ7299fZ7irtboF1Gy8Wje1+7Xbdljaey3tr3pY2nm3BG/vveMWX0u7Pnmt8RmZ81ni298M8CU/t+l/aeDGX52uLz3wM+3j2z5FzEZ+sr9+u3zHfe/vr5zperFzNnc2+8dnbTEujpWmPZV/cpzRaarW0mvv5WeO5fTccxwJzixWp67Nsvnbm800bzx6f47th/17bjx9e3zXn58V5LPA6VtnfW8l2rIqUpB0/Mo4tjvZtMz77e2uPzz5eSpmvn+t4bsdS+3i23zETO2u8qMuxxf76GenjmfN8zcf3ej+ci0gdvefROnrPozUQsagMAAC7pJ1k7TpJMOdl2ZM7+4ltvKTr584ELu0k0HZSZN4/7aTIdmJrPp7zpC0WdT/pNe/vmjREd4/XaXR6xmc/CXSeFJknUWbMnuM5khDrpC2WfhLtXOjF/nzNsezPy4rP46TNuXKgfTz7CoHmAhjm62NPMJ3L2ktKa+tzjmcmv/bnnzae7b2w/07aeEYi7UKA8/V2iy8tQYrGvMezn5C7jRfJTBis8XZ9PjtTnWmPZX9v7a9fPBZPT4DlSIB3vf9p49k/e7aLG874XMfzSKit75PSvxv2uV/2BC4jATbSE2q3BCnju+Z4P8zYMsazxZc2nnkssL1X9u+n+XhW0mx7r9wuXjmPVWnHFlt8rseqiMuxyny+LglmLBLzTDCdnxfnxSZ7Apx2rCrxOPZ5Hati/gmc81ia67HKbQ7tQDWwnx0AADlwOylyqxDaT/TMk4ikkUyrzjmrIFZC43JS5HZS6byqnVG1cCZIthMm+4mU+XOz9c6rSpNxkuU4KXLGlzGeIwE2f+6VwJkLgbid4EuOCqG8K5jOlQPt49lPXu2LqRhKTzDti0w4E0z7++F1Qm7+3Bov6p7sS0qr+th/Zk9A7HHY43NWfz3Hc6sQpjLjcx1vV5UmmUq6J5hKpZ14V5RUpCVjXhXCtPGi6RUf+/YC9iq563ixzPejJJqeoJvjWRUzewKs3b9jj8/++tmr8fbvhrMa70yQopGoyqJl1nj2ZNbtYo7VLRAgwUymkq7HAudn2fxd82euxyrzu2s/Vrm8fn4VTLdjVeCKqP1Yqsz4MsZzHKsyji1ZjlXmscDt++Y6niPBtHcQDFQkhAAA7BLkpM1+VdveliRlVml8K3AeV92dbWJu7aRuV8k927qcV/G92roCtHi6xZdWVXFruc2SwNnbxNJaPG3bN7i27Tn2AcxIuGwVGnsFzu31c6sQusaXY4unJKVS6RU914TVrwXVUSH0qsB5tijuqoiaz8vZguqsOLq2eHq0UErpFULnZy8tPsP99XO+t/btKlzH82q5DXBxw6sF1fX9cKlgZiRIRnqLp1d8Xu3ebhdz7McPe5W8M9WZ9liuxyq3BNh2/DAfz0zq0xJ+l++uWwtqWousV8uoM+H36T4I2o7u14LqViF0xmdvR0+ryPu0tFqrktouJg1UJIQAAOzi2oblaOtKGulX6u0nbc4qjW+ClCWBM2/PdlLe3QQza4XQUdFzxuds8bQnwH4ngebt5vN3q8AFis+sChiO8bLMgXOLLxqJpm3tYI/XtYIUsMXTHp/rHD2XFlTfOXo+FTivOYTOBM4zwcxSEXV7b82LFK4tqC7xpVXMXOLL1qLoN5694uP87nolIJ6vn0c13i2+oO+Hcw9F+3hpFVbb8SMaibpWRNNaZN1aRj0q1GkXr5wVZZ92dPtj2T8TafN/7fNlPVpQvS4OmY/h2dKapdvCOe/aKz7rZ14XJDyOLWGoEEYM+yuEPtXQ0KCamhrV19erurq6oLG0t7dr7ty5mjNnTtEufBMmvB/FhfejePBeFBfej+LC+1FceD+KB++FPxLCAjIMQ42NjaqqqrKWQS6UYkpOwftRbHg/igfvRXHh/SguvB/FhfejePBe+Bv4TbFFLBKJ8KEEAAAAUDDMIQQAAACAkCIhBAAAAICQIiGEJCkej+v6669nom2R4P0oLrwfxYP3orjwfhQX3o/iwvtRPHgv/LGoDAAAAACEFBVCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkJIkv73f/9Xe+21l8rLyzVz5kwtWLCg0CENeHPnztX06dNVVVWlUaNG6ZOf/KSWLFmSdp9jjz1WkUgk7c+Xv/zlAkU8sP3gBz/IeK2nTJli/bytrU1XXHGFhg8frsrKSp199tnavHlzASMe2Pbaa6+M9yMSieiKK66QxHejtz3//PM644wzNG7cOEUiET3wwANpPzcMQ9///vc1duxYVVRU6IQTTtDSpUvT7rNjxw5ddNFFqq6u1pAhQ3TppZeqqampD5/FwOD3XnR2duraa6/VQQcdpMGDB2vcuHH63Oc+pw0bNqSN4fZ9+slPftLHz2RgyPbduOSSSzJe65NPPjntPnw38ifb++H270gkEtHPf/5z6z58P0gIIemuu+7SNddco+uvv15vvPGGpk2bptmzZ2vLli2FDm1Ae+6553TFFVfo1Vdf1RNPPKHOzk6ddNJJam5uTrvfZZddpo0bN1p/fvaznxUo4oHvgAMOSHutX3zxRetnV199tebNm6d77rlHzz33nDZs2KBPfepTBYx2YFu4cGHae/HEE09Iks4991zrPnw3ek9zc7OmTZum//3f/3X9+c9+9jP99re/1R//+EfNnz9fgwcP1uzZs9XW1mbd56KLLtJ7772nJ554Qg899JCef/55felLX+qrpzBg+L0XLS0teuONN/S9731Pb7zxhu677z4tWbJEn/jEJzLue+ONN6Z9X6666qq+CH/AyfbdkKSTTz457bW+8847037OdyN/sr0f9vdh48aN+utf/6pIJKKzzz477X6h/34YCL0ZM2YYV1xxhfX3ZDJpjBs3zpg7d24BowqfLVu2GJKM5557zrpt1qxZxte+9rXCBRUi119/vTFt2jTXn9XV1RmlpaXGPffcY932/vvvG5KMV155pY8iDLevfe1rxqRJk4xUKmUYBt+NviTJuP/++62/p1IpY8yYMcbPf/5z67a6ujojHo8bd955p2EYhrF48WJDkrFw4ULrPo888ogRiUSM9evX91nsA43zvXCzYMECQ5KxevVq67YJEyYYv/71r3s3uBByez8uvvhi48wzz/T8Hb4bvSfI9+PMM880Pv7xj6fdxvfDMKgQhlxHR4def/11nXDCCdZt0WhUJ5xwgl555ZUCRhY+9fX1kqRhw4al3X7HHXdoxIgROvDAAzVnzhy1tLQUIrxQWLp0qcaNG6e9995bF110kdasWSNJev3119XZ2Zn2PZkyZYrGjx/P96QPdHR06Pbbb9cXvvAFRSIR63a+G4WxcuVKbdq0Ke37UFNTo5kzZ1rfh1deeUVDhgzR4Ycfbt3nhBNOUDQa1fz58/s85jCpr69XJBLRkCFD0m7/yU9+ouHDh+uQQw7Rz3/+cyUSicIEGALPPvusRo0apX333Vdf+cpXtH37dutnfDcKZ/PmzfrPf/6jSy+9NONnYf9+lBQ6ABTWtm3blEwmNXr06LTbR48erQ8++KBAUYVPKpXS17/+dX30ox/VgQceaN3+6U9/WhMmTNC4ceP0zjvv6Nprr9WSJUt03333FTDagWnmzJn629/+pn333VcbN27UDTfcoKOPPlqLFi3Spk2bVFZWlnGCNXr0aG3atKkwAYfIAw88oLq6Ol1yySXWbXw3Csf8zLv9u2H+bNOmTRo1alTaz0tKSjRs2DC+M72ora1N1157rS688EJVV1dbt//Xf/2XDj30UA0bNkwvv/yy5syZo40bN+pXv/pVAaMdmE4++WR96lOf0sSJE7V8+XJ9+9vf1imnnKJXXnlFsViM70YB/f3vf1dVVVXGdA++HySEQFG44oortGjRorQ5a5LS5hQcdNBBGjt2rI4//ngtX75ckyZN6uswB7RTTjnF+u+pU6dq5syZmjBhgu6++25VVFQUMDL8v//3/3TKKado3Lhx1m18N4B0nZ2dOu+882QYhm6++ea0n11zzTXWf0+dOlVlZWW6/PLLNXfuXMXj8b4OdUC74IILrP8+6KCDNHXqVE2aNEnPPvusjj/++AJGhr/+9a+66KKLVF5ennY73w8WlQm9ESNGKBaLZayWuHnzZo0ZM6ZAUYXLlVdeqYceekjPPPOM9txzT9/7zpw5U5K0bNmyvggt1IYMGaJ99tlHy5Yt05gxY9TR0aG6urq0+/A96X2rV6/Wk08+qS9+8Yu+9+O70XfMz7zfvxtjxozJWJgskUhox44dfGd6gZkMrl69Wk888URaddDNzJkzlUgktGrVqr4JMMT23ntvjRgxwjo28d0ojBdeeEFLlizJ+m+JFM7vBwlhyJWVlemwww7TU089Zd2WSqX01FNP6cgjjyxgZAOfYRi68sordf/99+vpp5/WxIkTs/7OW2+9JUkaO3ZsL0eHpqYmLV++XGPHjtVhhx2m0tLStO/JkiVLtGbNGr4nveyWW27RqFGjdNppp/nej+9G35k4caLGjBmT9n1oaGjQ/Pnzre/DkUceqbq6Or3++uvWfZ5++mmlUikreUd+mMng0qVL9eSTT2r48OFZf+ett95SNBrNaF1E/q1bt07bt2+3jk18Nwrj//2//6fDDjtM06ZNy3rfMH4/aBmFrrnmGl188cU6/PDDNWPGDP3mN79Rc3OzPv/5zxc6tAHtiiuu0D/+8Q89+OCDqqqqsuYO1NTUqKKiQsuXL9c//vEPnXrqqRo+fLjeeecdXX311TrmmGM0derUAkc/8HzjG9/QGWecoQkTJmjDhg26/vrrFYvFdOGFF6qmpkaXXnqprrnmGg0bNkzV1dW66qqrdOSRR+qII44odOgDViqV0i233KKLL75YJSW7/7niu9H7mpqa0qqtK1eu1FtvvaVhw4Zp/Pjx+vrXv67/+Z//0Uc+8hFNnDhR3/ve9zRu3Dh98pOflCTtt99+Ovnkk3XZZZfpj3/8ozo7O3XllVfqggsuSGv9RXZ+78XYsWN1zjnn6I033tBDDz2kZDJp/VsybNgwlZWV6ZVXXtH8+fN13HHHqaqqSq+88oquvvpqfeYzn9HQoUML9bT6Lb/3Y9iwYbrhhht09tlna8yYMVq+fLm+9a1vafLkyZo9e7Ykvhv5lu1YJXVdsLrnnnv0y1/+MuP3+X7sUuhlTlEcfve73xnjx483ysrKjBkzZhivvvpqoUMa8CS5/rnlllsMwzCMNWvWGMccc4wxbNgwIx6PG5MnTza++c1vGvX19YUNfIA6//zzjbFjxxplZWXGHnvsYZx//vnGsmXLrJ+3trYaX/3qV42hQ4cagwYNMs466yxj48aNBYx44HvssccMScaSJUvSbue70fueeeYZ1+PTxRdfbBhG19YT3/ve94zRo0cb8XjcOP744zPep+3btxsXXnihUVlZaVRXVxuf//znjcbGxgI8m/7N771YuXKl578lzzzzjGEYhvH6668bM2fONGpqaozy8nJjv/32M3784x8bbW1thX1i/ZTf+9HS0mKcdNJJxsiRI43S0lJjwoQJxmWXXWZs2rQpbQy+G/mT7VhlGIbxpz/9yaioqDDq6uoyfp/vR5eIYRhGr2edAAAAAICiwxxCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAAACCkSQgAAAAAIKRJCAAAAAAgpEkIAAAJ4aMVDuvKpKz1/Pvtfs/XG5jdcf7a+ab0OvvXgXooMAIDuIyEEACCA0/c+Xb8//veB7vudF7+jP739p16L5a0tb2nq36dmPMZf3v2LjvnnMfronR/Vr177lQzDsH62aNsiferfn9L026frkkcv0YamDdbP2hJtuu6F6zTzjpk68V8n6uEVD3s+dspI6acLfqqj/nGUZt01S7e+d2v+nyAAoM+QEAIA0I+kjJR+vvDnOnDEgWm3P7/uef3zg3/qjlPv0IOffFAvrH9B9y+7X5LUkezQ15/5ui6acpFevPBFHTrqUM15YY71u3946w+qa6vTU+c+pV/M+oX+Z/7/aGX9StfHv3vJ3Vq4aaHmnTVPt55yq/7+3t/16sZXe+8JAwB6FQkhACD0/vXhv/St578lSepMdWrGHTN089s3S5JW1a/S8XcfrweWPaAvPv5F63deWPeCTr3vVH30zo/q5rdutm5/cNmDenjFw/rTO3/SjDtm6MZXbkx7nOPuPk7H3nWsHlz2YLdjPWjkQZpYMzHt9oeWP6Rz9zlXtdW1GlExQhcfcLH+vfzfkqSFmxaqLFams/c5W/FYXJdNvUyLty/WusZ1kqR5K+bpS1O/pMqySk0bOU3H1R6nh1e6VwnnrZiniw+4WMMrhmtC9QSdvc/Zmrd8XreeCwCg8EoKHQAAAIV26OhD9ce3/yhJen/7+xpWPkxvbn5TkvTGljd06OhD0+6/o22HvvHcN/SzY36mo8Ydpd+9+TttbtksSTpz8plasGmBxleN1+XTLpfUNYcwaSS1rG6ZHj/7cS3ctFBff/brOmHCCRpcOlh/efcv+uu7f3WN7f+3d3cxTZ1xHMd/2BY4BaQUpKhsiZQCqy+xFJ3WF0LcDZOYEDUkGrwREl+uvPXWF9TEC0185UpRF52Zu1kWLxbnjIkaygJzwlIhJjAzxGkd5UWLsguSsx1BnIlZSM73k/Tiyf//9HlO7359zmlDvpBOrDshSYqPxtXyoEUX11/U4XuHLX3dL7pVvaDaHAdyAuqOd0/U4t0qySkxa4bTUGFWobrj3cpKzdLTkaeWeklOidoH2qfcT0+8x9IbyAnoZt/Nd320AIAZjkAIALC9ouwiJd8k1TfYp7b+Nm0u2axLnZf0+s1rRfujCuWHLP23+m4pmBtU5SeVkqSdS3eqpbPlvevsWLJDLodLkfkRGU5DvYO9KvOWqWFxgxoWN7x3/vGfj6s+WK/ZqbMn1YaTw8pMzTTHma5MDSeHJ2pjw8pwZVj6M12ZGh4b1sjYiCRZ6hmuDHPupHXGrOtM1wsAmPm4ZRQAAEmh/JDanrQp+iSqioIKBbwBdT3rUlt/m8K+sKV3YGRABRkF5thwGvKkeaZ9f0eKQ570f3rSHekfFKQ6/+zU/af3tTGwccq62+VW4lXCHCeSCbld7oma062h5JClP5FMyO10y3AakmSpDyWHzLmT1nFa15muFwAw83FCCACApPL8crX+0aquZ10K5gYVzg/r+qPrevHyhQI5AXU+6zR75xhzdPv32+Z4dGxU8Zdxc5yilA9au7mjWc2/NE+9L1+5Tn9xWq39rXr01yOt+3qdpIlA50hxqHewV/tX75c/269YPKaqT6skSQ+fP5Tf45ck+T1+Xf7tsmW/fYN98nv8yk7LVp6Rp1g8Zp6Exp7HzLlvK/IUKRaPqdRbaq5T7Cn+oOsFAMwcBEIAACSFfWGdbD+phbkL5ZrlUtgX1q4fdimUH9KsFOsNNWsK1+jg3YP6qe8nrZy7UqfaT1n+4sFrePV46PHbS7xT45JGNS5pnLZnU8kmyzOCh+4d0vzM+dq+eLskqcZfo3139ql6QbUMp6HzD85r62dbJUnLCpZp9PWorsWuaX3Rep3tOKtgblCFWYUTc4tqdKbjjI5WHlVPvEc3em/owpcXptxHTVGNzv16TpF5ESVeJXQ1dlUHVh/4z9cKAJhZuGUUAABJZd4yjY+Pm6dki/IWaezNmMrzyyf1etO9OrL2iJruNqnySqXSHenyuX1mvba4Vh0DHYpcimj/nf0fZX+G01CekWe+0hxpcjvd5vOEawvXqq60Tlu+26IN327QqnmrVFtcK0lKdaTqWNUxtXS2KPJVRNH+qJrWNJnvvXvpbmWnZqvqSpX2/LhHez/fa/6KabQ/quUXl5u9daV1qvBVqOabGtV/X69twW1aMXfFR7lGAMD/L2X8319pAgAAAABsgxNCAAAAALApAiEAAAAA2BSBEAAAAABsikAIAAAAADZFIAQAAAAAmyIQAgAAAIBNEQgBAAAAwKYIhAAAAABgUwRCAAAAALApAiEAAAAA2BSBEAAAAABsikAIAAAAADZFIAQAAAAAmyIQAgAAAIBNEQgBAAAAwKYIhAAAAABgUwRCAAAAALApAiEAAAAA2BSBEAAAAABsikAIAAAAADZFIAQAAAAAmyIQAgAAAIBNEQgBAAAAwKYIhAAAAABgUwRCAAAAALApAiEAAAAA2BSBEAAAAABsikAIAAAAADb1Nx+Ebvcq8Wb6AAAAAElFTkSuQmCC\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["rsa.jupyter_schedule()"]}, {"cell_type": "code", "execution_count": 10, "id": "13d8cdaf", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "      <th>unit</th>\n", "      <th>extra</th>\n", "      <th>quality</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>X-amp</td>\n", "      <td>0.72</td>\n", "      <td>V</td>\n", "      <td>{}</td>\n", "      <td>R²=0.956(normal)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    name  value unit extra           quality\n", "0  X-amp   0.72    V    {}  R²=0.956(normal)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(rsa.analysis.show_results())"]}, {"cell_type": "code", "execution_count": 11, "id": "af14898c", "metadata": {}, "outputs": [{"data": {"text/plain": ["R²=0.956(normal)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["rsa.analysis.quality"]}, {"cell_type": "code", "execution_count": 12, "id": "a07f7ade", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 2 Axes>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["rsa.analysis.drawer.figure"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}