# -*- coding: utf-8 -*-

# This code is part of pyqcat.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/05/18
# __author:       HanQ<PERSON> Shi

"""
Normal Acquisition class, origin collection method.
"""

import asyncio
import os
import time
import warnings
from copy import copy
from pathlib import Path
from threading import Event
from typing import TYPE_CHECKING, List, Optional, Tuple, Union

import matplotlib
import numpy as np

from ..analysis.algorithms import (
    IQdiscriminator,
    correct_fidelity,
    get_multi_bits_probability,
    get_p_labels,
    get_qubits_probability,
    post_selection,
)
from ..analysis.visualization.scatter_drawer import iq_scatter_drawer
from ..concurrent.data_client import DataClient
from ..config import VISAGE_EXIST
from ..data_transfer.state import CRASH_STATE, DataTypeEnum, TransferTaskStatusEnum
from ..errors import (
    AcquisitionDataTackleError,
    AcquisitionDataTypeError,
    AcquisitionTaskStateError,
)
from ..structures import QDict
from ..tools import VisageDynamic  # type: ignore
from .acq_tackle import (
    MultipleMeasureWrapper,
    MultipleMeasureWrapperAU,
    QubitMeasureMode,
    UnionMode,
)

if TYPE_CHECKING:
    from ..config import PyqcatConfig

backend = matplotlib.get_backend()
if "inline" in backend or "nb" in backend:
    from tqdm import tqdm_notebook as tq

    use_progress = True
else:
    from tqdm import tqdm as tq

    use_progress = False if VISAGE_EXIST else True


class DataAcquisition:
    """Standard data acquisition."""

    def __init__(
        self,
        id_: str,
        sample_channels: List[Union[int, str]],
        data_type: str = "amp_phase",
        discriminator: Optional[Union[IQdiscriminator, List[IQdiscriminator]]] = None,
        is_dynamic: Union[int, bool] = 1,
        is_amend: bool = False,
        fidelity_matrix: Optional[np.ndarray] = None,
        fidelity_correct_type: str = "inv",
        post_select_type: Optional[str] = None,
        measure_qubits: Optional[List[str]] = None,
        simulator_data_path: Optional[str] = None,
        # simulator_remote_path: Optional[str] = None,
        exp_name: str = "",
        save_path: str = "",
        is_parallel: bool = False,
        use_simulator: bool = False,
        simulator_index: int = 0,
        simulator_name: str = "",
        config: Optional["PyqcatConfig"] = None,
        plot_iq: bool = True,
        is_retry: bool = False,
        repeat: int = 1024,
        loop: int = 1,
        measure_modes: Optional[Union[QubitMeasureMode, List[QubitMeasureMode]]] = None,
        prepare_measure_bits: Optional[List[str]] = None,
        acq_index: Optional[List[int]] = None,
        union_mode: str = "union",
        **kwargs,
    ):
        """Create a new acquisition object.

        Args:
            id_: require id.
            data_type: Data collection type, usually amp phase or I-Q
            discriminator: IQ discriminator
            is_dynamic: Whether to perform dynamic drawing
            is_amend: Whether to perform read fidelity correction
            fidelity_matrix: fidelity matrix
            fidelity_correct_type: Read fidelity correction type
            post_select_type: todo
            measure_qubits: Measurement qubit name, like [q1, q3]
            simulator_data_path: simulator data path
            simulator_remote_path: simulator data remote path
            exp_name: The name of the experiment to be collected
            save_path: The target experiment result storage location of the experiment to be collected
            sample_channels: Acquisition channel number
            is_parallel: Whether it is a parallel experiment
            use_simulator: Whether to use simulator
            simulator_index: Simulation sub experiment index
            simulator_name: Simulation experiment name, used to determine simulation data.
            config: Monster system configuration object `PyqcatConfig`
            plot_iq: Whether to plot IQ data
            is_retry: Whether to retry
            repeat: The number of repeats of the experiment
            loop: The maximum number of cycles for the experiment
            **kwargs:
        """
        self._id = id_
        self._tid = id_
        self._save_path = save_path
        self._data_type: DataTypeEnum = DataTypeEnum.adapter_pre_version(data_type)  # type: ignore
        self._is_parallel = is_parallel
        self._use_simulator = use_simulator
        self._simulator_index = simulator_index
        self._simulator_name = simulator_name
        self._label = exp_name

        # acquisition data types
        self._amp = []
        self._phase = []
        self._I = []
        self._Q = []
        self._p0 = []
        self._c_p0 = []
        self._p1 = []
        self._c_p1 = []
        self._p2 = []
        self._c_p2 = []
        self._prob_list = []
        self._prob_c_list = []

        self._status = TransferTaskStatusEnum.INIT
        self._is_dynamic = is_dynamic

        self.discriminator = discriminator

        self.measure_qubits = measure_qubits
        self.fidelity_correct_type = fidelity_correct_type
        self.post_select = post_select_type

        self.sweep_points = list(range(loop))
        self.sample_channels = sample_channels

        # use dynamic plot x-axis, need to be given
        self.x_list: Union[List, np.ndarray] = []

        self._simulator_data_path = simulator_data_path
        # self._simulator_remote_path = simulator_remote_path

        self._dynamic_data = QDict(
            id=str(id_),
            dirs=self.dirs,
            exp=exp_name,
            x=[],
            y1=[],
            y2=[],
            title1="",
            title2="",
        )
        self._repeat = repeat
        self.config = config

        # use for actual dcm
        self._read_dcm_list = []
        self._read_idx_list = []
        if measure_qubits and discriminator:
            if not isinstance(discriminator, list):
                discriminator = [discriminator]
            self._read_dcm_list.clear()
            self._read_idx_list.clear()
            for q_name in measure_qubits:
                for idx, dcm in enumerate(discriminator):
                    if q_name == dcm.name:
                        self._read_dcm_list.append(dcm)
                        self._read_idx_list.append(idx)

        # multi bits, fidelity_matrix
        on_discriminator = self._read_dcm_list if self._read_dcm_list else discriminator
        self.fidelity_matrix = self.update_fidelity_matrix(
            is_amend, fidelity_matrix, on_discriminator
        )

        # is plot iq scatter
        self._plot_iq = plot_iq
        self._iq_path = str(Path(self._save_path, f"IQDATA-{id_}"))
        if (
            plot_iq
            and data_type == "I_Q"
            and discriminator
            and not os.path.exists(self._iq_path)
        ):
            os.makedirs(self._iq_path, exist_ok=True)

        # BugFixed: use matplotlib in ProcessPool, set `matplotlib.use("Agg")`.
        self._mat_backend = matplotlib.get_backend()

        self._is_retry = is_retry

        # for multiple readout
        self.prepare_measure_bits = prepare_measure_bits
        self.multiple_wrapper = None

        if measure_modes is not None and on_discriminator:
            multiple_wrapper_cls = (
                MultipleMeasureWrapperAU
                if union_mode == UnionMode.AU
                else MultipleMeasureWrapper
            )
            self.multiple_wrapper = multiple_wrapper_cls(
                repeat=repeat,
                dcms=on_discriminator,
                measure_modes=measure_modes,
                save_path=save_path,
                union_mode=union_mode,
                label=self._label,
            )
        elif self.prepare_measure_bits and measure_qubits and discriminator:
            measure_modes = []
            for qubit in measure_qubits:
                base_label_list = ["0"] if qubit in self.prepare_measure_bits else ["x"]
                measure_modes.append(
                    QubitMeasureMode(
                        name=qubit,
                        measure_total=2,
                        measure_num_list=[-1],
                        base_label_list=base_label_list,
                    )
                )
            self.multiple_wrapper = MultipleMeasureWrapper(
                repeat=repeat,
                dcms=discriminator,
                measure_modes=measure_modes,
                save_path=save_path,
                union_mode=union_mode,
                label=self._label,
            )

            if "PrepareSingleShot" in exp_name:
                self.multiple_wrapper.prepare_single_shot = True

        if self.multiple_wrapper:
            self.multiple_wrapper.plot_iq = plot_iq
            self.multiple_wrapper.iq_path = self._iq_path

        self.visage_plot = None

        if is_amend and self.multiple_wrapper:
            self.multiple_wrapper.fidelity_correct_type = fidelity_correct_type

        self.error = None

        # use for acq index
        self._acq_index = acq_index
        # pyqlog.debug(f"{exp_name} data acquisition start ...")

    @property
    def dirs(self):
        return self._save_path

    @property
    def status(self):
        return self._status

    @property
    def amp(self):
        return self._amp

    @property
    def phase(self):
        return self._phase

    @property
    def I(self):  # noqa: E743
        return self._I

    @property
    def Q(self):
        return self._Q

    @property
    def P0(self):
        return self._p0

    @property
    def CP0(self):
        return self._c_p0

    @property
    def P1(self):
        return self._p1

    @property
    def CP1(self):
        return self._c_p1

    @property
    def P2(self) -> List[float]:
        """Return self._p2"""
        return self._p2

    @property
    def CP2(self) -> List[float]:
        """Return self._c_p2"""
        return self._c_p2

    @property
    def probability_list(self):
        return self._prob_list

    @property
    def probability_c_list(self):
        return self._prob_c_list

    @property
    def repeat(self) -> int:
        """Return experiment repeat."""
        return self._repeat

    @property
    def is_parallel(self):
        return self._is_parallel

    @staticmethod
    def update_fidelity_matrix(
        is_amend: bool = False,
        fidelity_matrix: Optional[np.ndarray] = None,
        discriminator: Optional[Union[List[IQdiscriminator], IQdiscriminator]] = None,
    ) -> Union[np.ndarray, None]:
        """According to give fidelity matrix and discriminator,
        adjust updating fidelity_matrix.

        """
        new_fd_matrix = None
        if is_amend is True:
            if fidelity_matrix is None:
                if isinstance(discriminator, IQdiscriminator):
                    if hasattr(discriminator, "_fidelity_matrix"):
                        new_fd_matrix = discriminator.fidelity_matrix
                if isinstance(discriminator, list):
                    for dcm in discriminator:
                        if hasattr(dcm, "_fidelity_matrix"):
                            if new_fd_matrix is None:
                                new_fd_matrix = dcm.fidelity_matrix
                            else:
                                new_fd_matrix = np.kron(
                                    new_fd_matrix, dcm.fidelity_matrix
                                )
            else:
                new_fd_matrix = fidelity_matrix

        return new_fd_matrix

    @staticmethod
    def slice_result(measure_result: List[QDict]) -> Tuple[np.ndarray, np.ndarray]:
        """Merge collected data on various readout channels

        Args:
            measure_result (List[QDict]): Collection results on each channel of a single loop

            demo-1: The amp-phase mode collected by two readout channels
                measure_result = [
                    {
                        'amp': [0.5],
                        'phase': [0.2],
                    },
                    {
                        'amp': [0.6],
                        'phase': [0.4],
                    },
                ]
                x = [0.5, 0.6]  # amp
                y = [0.2, 0.4]  # phase


            demo-1: The amp-phase mode collected by two readout channels
                measure_result = [
                    {
                        'I': [0.5, 0.4, 0.3, ..., 0.6],  # length equal to repeat
                        'Q': [0.2, 0.2, 0.2, ..., 0.5],
                    },
                    {
                        'I': [0.5, 0.4, 0.3, ..., 0.6],
                        'Q': [0.2, 0.2, 0.2, ..., 0.5],
                    },
                ]
                x = [0.5, 0.4, 0.3, ..., 0.6, 0.5, 0.4, 0.3, ..., 0.6]  # I
                y = [0.2, 0.2, 0.2, ..., 0.5, 0.2, 0.2, 0.2, ..., 0.5]  # Q

        Returns: x/y data
        """
        x = np.empty(0)
        y = np.empty(0)
        for m_res in measure_result:
            if m_res.amp:
                x = np.hstack((x, m_res.amp))
                y = np.hstack((y, m_res.phase))
            else:
                x = np.hstack((x, m_res.I))
                y = np.hstack((y, m_res.Q))
        return x, y

    def correct_res(self):
        """Readout fidelity matrix correction."""
        if self.multiple_wrapper:
            return

        if isinstance(self.discriminator, IQdiscriminator):
            if self.fidelity_matrix is not None:
                p0, p1 = self._readout_fidelity_correct([self._p0[-1], self._p1[-1]])
                self._c_p0.append(p0)
                self._c_p1.append(p1)

        elif isinstance(self.discriminator, List):
            if self._read_dcm_list:
                qubit_num = len(self._read_dcm_list)
            else:
                qubit_num = len(self.discriminator)
            prob = self._prob_list[-1]
            index_list = [True] * qubit_num
            if self.post_select is not None:
                prob = post_selection(qubit_num, index_list, prob, self.post_select)
            if self.fidelity_matrix is not None:
                prob = correct_fidelity(
                    qubit_num,
                    index_list,
                    prob,
                    self.fidelity_matrix,
                    self.fidelity_correct_type,
                )
            self._prob_c_list.append(prob)

    def concat_array(self, measure_result: List[QDict], loop: int):
        res = self.slice_result(measure_result)
        if self._data_type == DataTypeEnum.AP:
            amp, phase = res
            # feature: 2024/09/25 amp phase only one data.
            x1, x2 = amp[0], phase[0]
            self._amp.append(x1)
            self._phase.append(x2)
        elif self._data_type == DataTypeEnum.IQ:
            data_i, data_q = res
            if self._acq_index:
                ln, rn = self._acq_index
                ln = max(0, ln)
                rn = min(rn, self.repeat)
                data_i = data_i[ln:rn]
                data_q = data_q[ln:rn]
            multiple_wrapper_result = None

            if self.multiple_wrapper:
                labels, multiple_wrapper_result = self.multiple_wrapper.tackle(
                    measure_result, loop
                )
                multiple_wrapper_result = multiple_wrapper_result[0]

                if not self.prepare_measure_bits:
                    return res

                if labels == "PrepareSingleShot":
                    data_i = multiple_wrapper_result[0]
                    data_q = multiple_wrapper_result[1]
                    self.discriminator = None

            if isinstance(self.discriminator, IQdiscriminator):
                if multiple_wrapper_result is None:
                    self.discriminator.get_probability(data_i, data_q)
                    result: List = self.discriminator.probability
                else:
                    result: List = multiple_wrapper_result
                if self.discriminator.level_str == "012":
                    self._p2.append(result[2])
                self._p0.append(result[0])
                self._p1.append(result[1])

                # plot iq data
                if self._plot_iq and not self.prepare_measure_bits:
                    save_path = str(
                        Path(self._iq_path, f"{self.discriminator.name}-loop-{loop}")
                    )
                    iq_scatter_drawer(
                        [data_i],
                        [data_q],
                        [self.discriminator],
                        loop,
                        save_path=save_path,
                    )

            elif isinstance(self.discriminator, List):
                if multiple_wrapper_result is None:
                    qubit_num = len(self.discriminator)
                    array_i = data_i.reshape(qubit_num, self.repeat)
                    array_q = data_q.reshape(qubit_num, self.repeat)

                    # plot iq data
                    if self._plot_iq and not self.prepare_measure_bits:
                        save_path = os.path.join(
                            self._iq_path,
                            f"{''.join(dcm.name for dcm in self.discriminator)}-loop-{loop}",
                        )
                        iq_scatter_drawer(
                            array_i.tolist(),
                            array_q.tolist(),
                            self.discriminator,
                            loop,
                            save_path=save_path,
                        )

                    # feature: divide IQ data by measure qubits
                    if self.measure_qubits:
                        array_i = array_i[self._read_idx_list, :]
                        array_q = array_q[self._read_idx_list, :]
                        dcms = self._read_dcm_list
                    else:
                        dcms = self.discriminator

                    prob = get_qubits_probability(
                        dcms, array_i.tolist(), array_q.tolist(), self.repeat
                    )
                else:
                    prob = multiple_wrapper_result
                self._prob_list.append(prob)

            else:
                if len(data_i) > 1:
                    self._I.append(data_i)
                    self._Q.append(data_q)
                else:
                    self._I.append(data_i[0])
                    self._Q.append(data_q[0])
        elif self._data_type == DataTypeEnum.TRACK:
            di, dq = res
            x1, x2 = (di, dq) if len(di) > 1 else (di[0], dq[0])
            self._I.append(x1)
            self._Q.append(x2)
        else:
            raise AcquisitionDataTypeError(self._data_type)
        return res

    def _init_dynamic_plot(self):
        if self.visage_plot:
            self.visage_plot.start_dynamic_plot(
                experiment_id=str(self._id),
                loop_counter=len(self.sweep_points),
                dirs=self.dirs,
                title=self._label,
            )
        self._dynamic_data.total = len(self.sweep_points)

    def _readout_fidelity_correct(self, origin_data):
        """Readout fidelity matrix correction

        Args:
            origin_data: [p0, p1]
        """
        f_matrix = self.fidelity_matrix
        if f_matrix is None:
            f_matrix = np.eye(2)
        return correct_fidelity(
            bit_num=1,
            index_list=[True],
            std_p=origin_data,
            f_matrix=f_matrix,
            method=self.fidelity_correct_type,
        )

    def _1d_dynamic_plot(self, loop):
        if loop == 0:
            return

        x_list = self.x_list[loop - 1 : loop + 1]
        draw = False
        tit1, tit2, y1_list, y2_list = None, None, [], []
        if self._data_type == DataTypeEnum.AP:
            y1_list = self._amp[loop - 1 : loop + 1]
            y2_list = self._phase[loop - 1 : loop + 1]
            if isinstance(self.amp[0], float):
                draw = True
                tit1, tit2 = ["amp", "phase"]
            else:
                # todo, multiple qubit dynamic plot
                # optimize:multiple qubit readout close raise error
                draw = False
        elif self._data_type == DataTypeEnum.IQ:
            if isinstance(self.discriminator, IQdiscriminator):
                if self.discriminator.level_str == "01":
                    draw = True
                    tit1, tit2 = ["P0", "P1"]
                    y1_list = self._p0[loop - 1 : loop + 1]
                    y2_list = self._p1[loop - 1 : loop + 1]
                elif self.discriminator.level_str == "02":
                    draw = True
                    tit1, tit2 = ["P0", "P2"]
                    y1_list = self._p0[loop - 1 : loop + 1]
                    y2_list = self._p2[loop - 1 : loop + 1]
                else:
                    # todo, `012` plot three classier P
                    draw = False
            elif isinstance(self.discriminator, list):
                # todo, multiple qubit dynamic plot
                # optimize:multiple qubit readout close raise error
                draw = False

        if draw:
            self._dynamic_data.x = [float(d) for d in x_list]
            self._dynamic_data.y1 = [float(d) for d in y1_list]
            self._dynamic_data.y2 = [float(d) for d in y2_list]
            self._dynamic_data.title1 = tit1
            self._dynamic_data.title2 = tit2

    def _check_fail(self, status=None, is_raise=True):
        status = status or self._status
        flag = status in CRASH_STATE
        if is_raise is True and flag is True:
            raise AcquisitionTaskStateError(status)
        return flag

    def _get_measure_data(self, sample_channels: List):
        client = DataClient()
        self._tid = client.query_task_uuid(self._id)
        status, measure_data = client.query_acq_data_all(
            self._id, sample_channels, self._data_type
        )
        self._status = status
        loop_total = len(self.sweep_points)

        msg = f"ACQ ID({self._id}) | {sample_channels} |len({len(measure_data)}) | loop({loop_total}) | {self._status.name}"
        if len(measure_data) != loop_total * len(sample_channels):
            raise AcquisitionDataTackleError(msg)

        for loop in range(len(self.sweep_points)):
            one_loop_measure_data = []
            for ch in range(len(sample_channels)):
                one_loop_measure_data.append(
                    QDict(**measure_data[loop + ch * loop_total])
                )
            self.concat_array(one_loop_measure_data, loop)
            self.correct_res()

    def _query_task_status(self):
        """Define a coroutine function that waits for the experiment to complete."""
        self._status = DataClient().query_task_state(self._id)

    def execute_loop(self):
        """Execute measurement by looping through multiple points."""
        try:
            # loop acq
            self._get_measure_data(self.sample_channels)

            # clear resource
            if self.multiple_wrapper:
                self.multiple_wrapper.save_record_data(self.x_list)

            if self.prepare_measure_bits:
                self.multiple_wrapper = None

        except AcquisitionTaskStateError as e:
            self.error = e
        except Exception:  # pylint: disable=broad-exception-caught
            import traceback

            self.error = AcquisitionDataTackleError(traceback.format_exc())
        finally:
            DataClient().clear_acq_data(self._id, self.sample_channels)

    async def _wait_for_acquisition_start(self):
        """
        wait for acquisition start
        """
        self._tid = DataClient().query_task_uuid(self._id)
        while 1:
            status = DataClient().wait_for_acquisition(self._id)
            self._status = status
            self._check_fail()
            if status in [TransferTaskStatusEnum.ACQ_START, TransferTaskStatusEnum.SUC]:
                return
            await asyncio.sleep(0.1)

    async def async_execute_loop(self):
        """Execute measurement by looping through multiple points."""
        try:
            # wait acquisition start
            await self._wait_for_acquisition_start()

            # use for dynamic plot
            if self.is_parallel is False:
                self.visage_plot = VisageDynamic()
                self._init_dynamic_plot()

            # progress bar
            if use_progress and self.is_parallel is False:
                progress = tq(self.sweep_points)
                progress.total = len(self.sweep_points)
                progress.miniters = 1
                progress.set_description(self._label)
            else:
                progress = self.sweep_points

            # loop acq
            for loop, _ in enumerate(progress):
                # Typically, we use this function to get the data for each loop.
                await self._async_get_measure_data(self.sample_channels, loop)

                # send dynamic plot data
                if self.visage_plot:
                    self._dynamic_data.loop = loop + 1
                    if self._is_dynamic:
                        self._1d_dynamic_plot(loop)
                    self.visage_plot.dynamic_loop(self._dynamic_data.to_dict())

            # clear resource
            if self.multiple_wrapper:
                self.multiple_wrapper.save_record_data(self.x_list)

            if self.prepare_measure_bits:
                self.multiple_wrapper = None
        except AcquisitionTaskStateError as e:
            self.error = e
        except asyncio.exceptions.CancelledError:
            self.error = "User KeyboardInterrupt"
        except Exception:
            import traceback

            self.error = AcquisitionDataTackleError(traceback.format_exc())
        finally:
            # release resource after acquisition
            if self.visage_plot:
                self.visage_plot.end_dynamic_plot(
                    experiment_id=str(self._id), status=self._status
                )
            DataClient().clear_acq_data(self._id, self.sample_channels)

    async def _async_get_measure_data(self, sample_channels: List, loop: int):
        """Get the measure data object which stores the `MeasureResultDoc` object.

        Data Struct:
            measure_data = {
                "1": ["5d8ae0b0ad7734f89d538ca0", "5d8ae08d60139bad92c10b41", ...],
                "2": ["5d8ae0b0ad7734f89d538ca2", "5d8ae08d60139bad92c10b42", ...],
            }
            Each ID corresponds to a document, such as "5d8ae0b0ad7734f89d538ca0" -> `MeasureResultDoc`.

        Notes:
            `measure_data` is a dictionary whose keys are readout channels and values are lists of
            `pyQCat.protobuf.v1.acquisition.AcquisitionPackage` objects.

            if experiment called `set_multiple_IF([600, 566, 466], channel=1)`,
            MeasureResultDoc.amp/phase may like this list object: [0.3, 0.12, -0.24].
            MeasureResultDoc./I/Q may like this list object: [0.3, 0.12, -0.24, ...].
            I/Q list length is equaled to: len([600, 566, 466]) * repeat.

        Args:
            sample_channels (List[int]): readout channel number.
            loop (int): loop number.

        Returns:

        """
        while True:
            status, measure_data = DataClient().query_acq_data_one(
                self._id, sample_channels, loop, self._data_type
            )
            self._status = status
            if measure_data:
                self.concat_array(measure_data, loop)
                self.correct_res()
                return measure_data
            else:
                self._check_fail()
                # To ensure that dynamic drawing is not inserted by asynchronous tasks
                # in non parallel mode, a synchronous acquisition interface is used when
                # dynamic drawing mode is enabled
                time.sleep(0.01)
                # await asyncio.sleep(0.01)
                continue


class SimulateAcquisition(DataAcquisition):
    """Simulate Acquisition"""

    event = Event()

    def execute_loop(self):
        """Execute measurement by looping through multiple points."""
        try:
            status = None
            dat_flag = True

            if self._use_simulator and self._simulator_data_path:
                if isinstance(self._simulator_data_path, List):
                    self._simulator_data_path = self._simulator_data_path[0]
                if self._simulator_data_path.endswith(".dat"):
                    data: np.ndarray = np.loadtxt(self._simulator_data_path)
                else:
                    data = np.load(self._simulator_data_path)
                    dat_flag = False
            else:
                warnings.warn("Get simulator data error, random generator!")
                n = 2 ** len(self.sample_channels)
                if "SingleShot" in self._label:
                    dat_flag = False
                    data = {
                        "arr_0": [
                            np.random.rand(self.repeat) for _ in range(len(self.x_list))
                        ],
                        "arr_1": [
                            np.random.rand(self.repeat) for _ in range(len(self.x_list))
                        ],
                    }
                else:
                    data = random_result_data(int(n), list(self.x_list))

            loops = 0

            if self._data_type == DataTypeEnum.IQ:
                if self.discriminator is None:
                    if dat_flag:
                        loops = len(self.sweep_points)
                        data_i_list = []
                        data_q_list = []
                        data_count = data.shape[1]
                        cur_index = 0
                        while cur_index < data_count:
                            data_i_list.append(data[:, cur_index])
                            data_q_list.append(data[:, cur_index + 1])
                            cur_index += 2
                        self._I = np.vstack(tuple(data_i_list))
                        self._Q = np.vstack(tuple(data_q_list))
                    else:
                        loops = len(self.sweep_points)
                        # data = np.load(self._simulator_data_path)
                        self._I = data["arr_0"]
                        self._Q = data["arr_1"]
                elif isinstance(self.discriminator, IQdiscriminator):
                    loops = len(data[:, 1])
                    self.x_list = data[:, 0]

                    if self.discriminator.level_str == "01":
                        self._p0 = np.array(data[:, -2])
                        self._p1 = np.array(data[:, -1])
                        self.correct_res()
                    elif self.discriminator.level_str == "02":
                        self._p0 = np.array(data[:, 1])
                        self._p2 = np.array(data[:, 2])
                        self.correct_res()
                    else:
                        self._p0 = np.array(data[:, 1])
                        self._p1 = np.array(data[:, 2])
                        self._p2 = np.array(data[:, 3])
                        self.correct_res()

                else:
                    # todo, multi dcm, while RB Experiment some questions
                    self.x_list = data[:, 0]
                    loops = len(self.x_list)
                    self._prob_list = data[:, 1:]
                    self.correct_res()

            elif self._data_type == DataTypeEnum.AP:
                loops = len(data[:, 1])
                self.x_list = data[:, 0]
                self._amp = np.array(data[:, 1])
                self._phase = np.array(data[:, 2])

            # simulator process bar
            sweep_points = range(loops)
            progress = sweep_points

            self._dynamic_data.total = len(sweep_points)
            visage_plot = None
            if not self._is_parallel:
                visage_plot = VisageDynamic()
                visage_plot.start_dynamic_plot(
                    experiment_id=str(self._id),
                    loop_counter=loops,
                    dirs=self.dirs,
                    title=self._label,
                )

            for loop, _ in enumerate(progress):
                self._dynamic_data.loop = loop + 1
                status = TransferTaskStatusEnum.SUC
                if self._mat_backend != "agg":
                    if self._is_dynamic == 1:
                        self._1d_dynamic_plot(loop)

                simulator_delay = self.config.run.get("simulator_delay")
                if simulator_delay:
                    self.event.wait(simulator_delay)

                if visage_plot:
                    visage_plot.dynamic_loop(self._dynamic_data.to_dict())

            self._status = status

        except Exception:
            import traceback

            self.error = AcquisitionDataTackleError(str(traceback.format_exc()))
            self._status = TransferTaskStatusEnum.FAIL
        finally:
            # release resource after acquisition
            if visage_plot:
                visage_plot.end_dynamic_plot(
                    experiment_id=str(self._id), status=self._status
                )

    def correct_res(self):
        if isinstance(self.discriminator, IQdiscriminator):
            if self.fidelity_matrix is not None:
                if self.discriminator.level_str == "01":
                    for i in range(len(self._p0)):
                        correct_f = self._readout_fidelity_correct(
                            [self._p0[i], self._p1[i]]
                        )
                        p0, p1 = correct_f
                        self._c_p0.append(p0)
                        self._c_p1.append(p1)
                elif self.discriminator.level_str == "02":
                    for i in range(len(self._p0)):
                        correct_f = self._readout_fidelity_correct(
                            [self._p0[i], self._p2[i]]
                        )
                        p0, p2 = correct_f
                        self._c_p0.append(p0)
                        self._c_p2.append(p2)
                else:
                    for i in range(len(self._p0)):
                        correct_f = self._readout_fidelity_correct(
                            [self._p0[i], self._p1[i], self._p2[i]]
                        )
                        p0, p1, p2 = correct_f
                        self._c_p0.append(p0)
                        self._c_p1.append(p1)
                        self._c_p2.append(p2)

        elif isinstance(self.discriminator, List):
            if self._read_dcm_list:
                qubit_num = len(self._read_dcm_list)
            else:
                qubit_num = len(self.discriminator)
            index_list = [True] * qubit_num

            prob_list = self._prob_list
            for prob in prob_list:
                # if self.measure_qubits is not None:
                #     qubit_info = self.get_experiment("qubit_info")
                #     if qubit_num == len(qubit_info):
                #         exp_bit_list = [qubit_doc.bit for qubit_doc in qubit_info]
                #         index_list = [False] * qubit_num
                #         for bit in self.measure_qubits:
                #             i = exp_bit_list.index(bit)
                #             index_list[i] = True
                #     else:
                #         # todo, when len(self.discriminator) != len(qubit_info)
                #         index_list = [True] * qubit_num
                # else:
                #     index_list = [True] * qubit_num

                if self.post_select is not None:
                    prob = post_selection(qubit_num, index_list, prob, self.post_select)
                if self.fidelity_matrix is not None:
                    prob = correct_fidelity(
                        qubit_num,
                        index_list,
                        prob,
                        self.fidelity_matrix,
                        self.fidelity_correct_type,
                    )
                self._prob_c_list.append(prob)


class WeirdoAcquisition(DataAcquisition):
    """Weirdo Acquisition"""

    def __init__(self, *args, **kwargs):
        """Initial object."""
        super().__init__(*args, **kwargs)

        # The time and space complexity of `pyQCat.analysis.algorithms.iqprobability.get_p_labels` is $O(n^2)$,
        # so the time and space it takes to execute `get_p_labels` increases exponentially with the number of qubits.
        self._prob_labels = {}
        on_discriminator = (
            self._read_dcm_list if self._read_dcm_list else self.discriminator
        )
        if on_discriminator:
            labels = get_p_labels(on_discriminator)
            for label in labels:
                self._prob_labels[label[1:]] = 0

    def concat_array(self, measure_result: List[QDict], loop: int):
        res = self.slice_result(measure_result)
        if self._data_type == DataTypeEnum.AP:
            amp, phase = res
            x1, x2 = (amp, phase) if len(amp) > 1 else (amp[0], phase[0])
            self._amp.append(x1)
            self._phase.append(x2)
        elif self._data_type == DataTypeEnum.IQ:
            data_i, data_q = res

            if isinstance(self.discriminator, IQdiscriminator):
                self.discriminator.get_probability(data_i, data_q)
                prob = self.discriminator.probability
                if self.fidelity_matrix is not None:
                    correct_prob = correct_fidelity(
                        bit_num=1,
                        index_list=[True],
                        std_p=prob,
                        f_matrix=self.fidelity_matrix,
                        method=self.fidelity_correct_type,
                    )
                    cp0, cp1, cp2 = None, None, None
                    if self.discriminator.level_str == "02":
                        cp0, cp2 = correct_prob
                    elif self.discriminator.level_str == "012":
                        cp0, cp1, cp2 = correct_prob
                    else:
                        cp0, cp1 = correct_prob

                    if cp0 is not None:
                        self._c_p0.append(cp0)

                    if cp1 is not None:
                        self._c_p1.append(cp1)

                    if cp2 is not None:
                        self._c_p2.append(cp2)

                p0, p1, p2 = None, None, None
                if self.discriminator.level_str == "02":
                    p0, p2 = prob
                elif self.discriminator.level_str == "012":
                    p0, p1, p2 = prob
                else:
                    p0, p1 = prob

                self._p0.append(p0)
                if p1 is not None:
                    self._p1.append(p1)
                if p2 is not None:
                    self._p2.append(p2)

                # plot iq data
                if self._plot_iq:
                    save_path = os.path.join(
                        self._iq_path, f"{self.discriminator.name}-loop-{loop}"
                    )
                    iq_scatter_drawer(
                        [data_i],
                        [data_q],
                        [self.discriminator],
                        loop,
                        save_path=save_path,
                    )

            elif isinstance(self.discriminator, List):
                qubit_num = len(self.discriminator)
                repeat = self.repeat
                i_array = data_i.reshape(qubit_num, repeat)
                q_array = data_q.reshape(qubit_num, repeat)
                i_list = i_array.tolist()
                q_list = q_array.tolist()

                # plot iq data
                if self._plot_iq:
                    save_path = os.path.join(
                        self._iq_path,
                        f"{''.join(dcm.name for dcm in self.discriminator)}-loop-{loop}",
                    )
                    iq_scatter_drawer(
                        i_list, q_list, self.discriminator, loop, save_path=save_path
                    )

                # feature: divide IQ data by measure qubits
                if self.measure_qubits:
                    i_list = i_array[self._read_idx_list, :].tolist()
                    q_list = q_array[self._read_idx_list, :].tolist()
                    *_, prob = get_multi_bits_probability(
                        self._read_dcm_list, i_list, q_list, copy(self._prob_labels)
                    )
                else:
                    *_, prob = get_multi_bits_probability(
                        self.discriminator, i_list, q_list, copy(self._prob_labels)
                    )

                self._prob_list.append(prob)
                if self.fidelity_matrix is not None:
                    prob = correct_fidelity(
                        bit_num=qubit_num,
                        index_list=[True] * qubit_num,
                        std_p=prob,
                        f_matrix=self.fidelity_matrix,
                        method=self.fidelity_correct_type,
                    )
                    self._prob_c_list.append(prob)
            else:
                if len(data_i) > 1:
                    self._I.append(data_i)
                    self._Q.append(data_q)
                else:
                    self._I.append(data_i[0])
                    self._Q.append(data_q[0])
        elif self._data_type == DataTypeEnum.TRACK:
            di, dq = res
            x1, x2 = (di, dq) if len(di) > 1 else (di[0], dq[0])
            self._I.append(x1)
            self._Q.append(x2)
        else:
            raise AcquisitionDataTypeError(self._data_type)

        return res


class SQMCAcquisition(DataAcquisition):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        acquisition_data = {}
        if self._data_type == DataTypeEnum.AP:
            k1, k2 = "amp", "phase"
        else:
            k1, k2 = "I", "Q"
        for channel in self.sample_channels:
            acquisition_data[f"{k1}-{channel}"] = np.zeros(len(self.x_list))
            acquisition_data[f"{k2}-{channel}"] = np.zeros(len(self.x_list))
        self.y_data = acquisition_data

    def concat_array(self, measure_result: List[QDict], loop: int):
        res = self.slice_result(measure_result)
        if self._data_type == DataTypeEnum.AP:
            amp, phase = res
            for idx, channel in enumerate(self.sample_channels):
                self.y_data[f"amp-{channel}"][loop] = amp[idx]
                self.y_data[f"phase-{channel}"][loop] = phase[idx]
        elif self._data_type == DataTypeEnum.IQ:
            data_i, data_q = res
            for idx, channel in enumerate(self.sample_channels):
                self.y_data[f"I-{channel}"][loop] = data_i[idx]
                self.y_data[f"Q-{channel}"][loop] = data_q[idx]
        else:
            raise AcquisitionDataTypeError(self._data_type)
        return res


class SimulateSQMCAcquisition(SQMCAcquisition):
    def execute_loop(self):
        """Execute measurement by looping through multiple points."""
        try:
            status = None
            loops = len(self.x_list)

            if self._data_type == DataTypeEnum.IQ:
                for channel in self.sample_channels:
                    self.y_data[f"I-{channel}"] = np.random.rand(loops, self.repeat)
                    self.y_data[f"Q-{channel}"] = np.random.rand(loops, self.repeat)

            elif self._data_type == DataTypeEnum.AP:
                for channel in self.sample_channels:
                    self.y_data[f"amp-{channel}"] = np.random.rand(loops)
                    self.y_data[f"phase-{channel}"] = np.random.rand(loops)

            # simulator process bar
            sweep_points = range(loops)
            progress = sweep_points

            self._dynamic_data.total = len(sweep_points)
            visage_plot = None
            if not self._is_parallel:
                visage_plot = VisageDynamic()
                visage_plot.start_dynamic_plot(
                    experiment_id=str(self._id),
                    loop_counter=loops,
                    dirs=self.dirs,
                    title=self._label,
                )

            for loop, _ in enumerate(progress):
                self._dynamic_data.loop = loop + 1
                status = TransferTaskStatusEnum.SUC
                if self._mat_backend != "agg":
                    if self._is_dynamic == 1:
                        self._1d_dynamic_plot(loop)

                simulator_delay = self.config.run.get("simulator_delay")
                if simulator_delay:
                    self.event.wait(simulator_delay)

                if visage_plot:
                    visage_plot.dynamic_loop(self._dynamic_data.to_dict())

            self._status = status

        except Exception:
            import traceback

            self.error = AcquisitionDataTackleError(str(traceback.format_exc()))
            self._status = TransferTaskStatusEnum.FAIL
        finally:
            # release resource after acquisition
            if visage_plot:
                visage_plot.end_dynamic_plot(
                    experiment_id=str(self._id), status=self._status
                )


def random_result_data(size: int, x_data: list):
    result = []
    loop = len(x_data)
    random_data = np.random.random((size, loop))
    for col in range(loop):
        sum_v = sum(random_data[:, col])
        normalized_column = random_data[:, col] / sum_v
        result.append(normalized_column)
    return np.vstack((np.array(x_data), np.array(result).T)).T
