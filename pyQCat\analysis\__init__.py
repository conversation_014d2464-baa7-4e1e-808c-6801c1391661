# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

"""
===========================================================
Experiment Analysis (:mod:`pyQCat.analysis`)
===========================================================

Analysis modules, experiment data analysis related modules.

Modules
=======

.. list-table::

    * - :mod:`~pyQCat.analysis.algorithms`
      - algorithms
    * - :mod:`~pyQCat.analysis.fit`
      - curve fit
    * - :mod:`~pyQCat.analysis.library`
      - Basic library for experiment data analysis
    * - :mod:`~pyQCat.analysis.visualization`
      - visualization


Base Classes
============

.. autosummary::
    :toctree: ../stubs/analysis/

    TopAnalysis

Standard Analysis Library
=========================

.. autosummary::
    :toctree: ../stubs/analysis/

    CurveAnalysis
    TomographyAnalysis
    DecayAnalysis
    OscillationAnalysis
    DumpedOscillationAnalysis
    StateTomographyAnalysis
    ProcessTomographyAnalysis

Standard Analysis Specification
===============================

.. autosummary::
    :toctree: ../stubs/analysis/

    FitModel
    FitData
    CurveAnalysisData
    SingleShotAnalysisData
    AnalysisResult
    ParameterRepr
    OptionsDict
    InitialGuesses
    Boundaries
    FitOptions
"""

from . import algorithms, fit, quality, visualization
from .curve_analysis import CurveAnalysis
from .curve_fit_analysis import CurveFitAnalysis
from .decay_analysis import DecayAnalysis
from .library import (
    ACSpectrumAnalysis,
    AmpCompositeAnalysis,
    AmpOptAnalysis,
    AnharmonicityAnalysis,
    APEAnalysis,
    APECompositeAnalysis,
    BusCavityAnalysis,
    CavityAnalysis,
    CavityPowerScanAnalysis,
    CavityShiftF012Analysis,
    CMAAnalysis,
    CoherentPumpAnalysis,
    CoherentPumpAnalysisV2,
    ConditionalPhaseAdjustAnalysis,
    ConditionalPhaseAnalysis,
    CouplerFixedPointCalibrationAnalysis,
    CouplerSweetPointCalibrationAnalysis,
    CPMGAnalysis,
    CrosstalkAnalysis,
    CrosstalkOnceAnalysis,
    CZAssistAnalysis,
    CZPhaseAnalysis,
    DCSpectrumAnalysis,
    DetuneCalibrationAnalysis,
    DistortionPhaseAnalysis,
    DistortionRBAnalysis,
    DistortionT1Analysis,
    DistortionT1CompositeAnalysis,
    DPhaseCompositeAnalysis,
    GradientCalibrationAnalysis,
    ImpaCavityAnalysis,
    ImpaGainAnalysis,
    ImpaGainAnalysisV2,
    ImpaGainStabilityAnalysis,
    ImpaOptAnalysis,
    IQTrackSingleShotAnalysis,
    LeakageAmpAnalysis,
    LeakageNumAnalysis,
    LeakageOnceAnalysis,
    LeakagePreAnalysis,
    McmQubitDePhaseAnalysis,
    McmQubitDePhaseComAnalysis,
    McmQubitPopulationAnalysis,
    McmSpectatorAnalysis,
    MulInterleavedPurityRBAnalysis,
    MulInterleavedRBAnalysis,
    NMAnalysis,
    OneStepCalibrationAnalysis,
    OptimizeFIRAnalysis,
    PopulationLossOnceAnalysis,
    PopulationLossSpectrumAnalysis,
    PurityRBAnalysis,
    QubitFreqCaliAnalysis,
    QubitSpectrum2DComAnalysis,
    QubitSpectrumAnalysis,
    QubitSpectrumPreAnalysis,
    QubitSpectrumScanPowerAnalysis,
    QubitSpectrumZAmpAnalysis,
    RabiAmpAnalysis,
    RabiAmpF12Analysis,
    RabiScanAmpPowerAnalysis,
    RabiScanAmpPowerFreqAnalysis,
    RabiWidthAnalysis,
    RamseyAnalysis,
    RamseyEGAnalysis,
    RamseySegmAnalysis,
    RamseySpectrumAnalysis,
    RamseyZampAnalysis,
    RBAnalysis,
    ReadoutFreqCaliAnalysis,
    ReadoutFreqSSCaliAnalysis,
    ReadoutPowerCaliAnalysis,
    ReadoutPowerF012CaliAnalysis,
    RoomTempDistortionAnalysis,
    SampleWidthOptimizeAnalysis,
    SingInterleavedPurityRBAnalysis,
    SingleShotAnalysis,
    SingleShotExtendAnalysis,
    SingleShotExtendCompositeAnalysis,
    SingleShotExtendVsSampleWidthAnalysis,
    SQPhaseTMSEAnalysis,
    StabilityAnalysis,
    SwapAnalysis,
    SwapOnceAnalysis,
    SweepDetuneAnalysis,
    SweetPointCalibrationAnalysis,
    T1Analysis,
    T1SpectrumAnalysis,
    T2RamseyAnalysis,
    T2SpectrumAnalysis,
    TunableAnalysis,
    XEBAnalysis,
    XEBCompositeAnalysis,
    XEBPhaseAnalysis,
    XMTimingAnalysis,
    XYCrossRabiWidthOnceAnalysis,
    XYCrossRwAnalysis,
    XYCrosstalkAnalysis,
    XYCrosstalkNpiAnalysis,
    XYCrosstalkOnceAnalysis,
    XYCrosstalkRBAnalysis,
    XYZTimingAnalysis,
    XYZTimingCompositeAnalysis,
    ZZShiftZampAnalysis,
    ZZShitAnalysis,
    ZZTimingCompositeAnalysis,
    ZZTimingOnceAnalysis,
)
from .library_v2 import (
    AmpCompositeAnalysisV2,
    AmpToPhotonAnalysis,
    APEAnalysisV2,
    APECompositeAnalysisV2,
    BusQAnalysis,
    BusS21Analysis,
    CavityAnalysisV2,
    CavityCheckAnalysis,
    CavityCheckOnceAnalysis,
    CouplerTunableAnalysis,
    CouplerTunableAnalysisV1,
    CouplerTunableAnalysisV2,
    CouplerTunableAnalysisV3,
    DePhaseRamseyAnalysis,
    DephaseRamseyCompAnalysis,
    PhotonNumMeasAnalysis,
    PhotonNumMeasVsAmpAnalysis,
    PhotonNumMeasVsFreqAnalysis,
    PhotonScanReadoutFreqAnalysis,
    PhotonScanReadoutFreqV2Analysis,
    RBAnalysisV2,
    SaturationPowerAnalysis,
    T1AnalysisV2,
    TunableAnalysisV2,
)
from .oscillation_analysis import DumpedOscillationAnalysis, OscillationAnalysis
from .specification import (
    AnalysisResult,
    Boundaries,
    CurveAnalysisData,
    FitData,
    FitModel,
    FitOptions,
    InitialGuesses,
    OptionsDict,
    ParameterRepr,
    SingleShotAnalysisData,
)
from .standard_curve_analysis import StandardCurveAnalysis
from .tomography_analysis import (
    ProcessTomographyAnalysis,
    ProcessTomographyAnalysisV2,
    StateTomographyAnalysis,
    StateTomographyAnalysisV2,
    TomographyAnalysis,
)
from .top_analysis import TopAnalysis

__all__ = [
    "ImpaGainAnalysisV2",
    "SaturationPowerAnalysis",
    "ImpaGainStabilityAnalysis",
    "QubitSpectrumZAmpAnalysis",
    "BusS21Analysis",
    "CouplerTunableAnalysisV1",
    "CouplerTunableAnalysisV2",
    "CouplerTunableAnalysisV3",
    "CouplerTunableAnalysis",
    "StandardCurveAnalysis",
    "CurveFitAnalysis",
    "DecayAnalysis",
    "algorithms",
    "fit",
    "quality",
    "visualization",
    "CurveAnalysis",
    "TopAnalysis",
    "ProcessTomographyAnalysis",
    "ProcessTomographyAnalysisV2",
    "StateTomographyAnalysis",
    "StateTomographyAnalysisV2",
    "TomographyAnalysis",
    "FitOptions",
    "Boundaries",
    "InitialGuesses",
    "OptionsDict",
    "ParameterRepr",
    "AnalysisResult",
    "SingleShotAnalysisData",
    "CurveAnalysisData",
    "FitData",
    "FitModel",
    "OscillationAnalysis",
    "DumpedOscillationAnalysis",
    "ACSpectrumAnalysis",
    "AmpOptAnalysis",
    "APEAnalysis",
    "CavityAnalysis",
    "ConditionalPhaseAnalysis",
    "ConditionalPhaseAdjustAnalysis",
    "CZPhaseAnalysis",
    "CMAAnalysis",
    "SQPhaseTMSEAnalysis",
    "CrosstalkAnalysis",
    "CZAssistAnalysis",
    "DistortionT1Analysis",
    "QubitSpectrumPreAnalysis",
    "QubitSpectrumAnalysis",
    "AnharmonicityAnalysis",
    "RabiAmpAnalysis",
    "RabiAmpF12Analysis",
    "RabiWidthAnalysis",
    "RamseyAnalysis",
    "RamseyZampAnalysis",
    "RamseySegmAnalysis",
    "T1Analysis",
    "T1AnalysisV2",
    "T2RamseyAnalysis",
    "TunableAnalysis",
    "XYZTimingAnalysis",
    "XMTimingAnalysis",
    "CoherentPumpAnalysis",
    "CoherentPumpAnalysisV2",
    "SingleShotAnalysis",
    "IQTrackSingleShotAnalysis",
    "APECompositeAnalysis",
    "DistortionT1CompositeAnalysis",
    "ReadoutFreqCaliAnalysis",
    "ReadoutPowerCaliAnalysis",
    "SampleWidthOptimizeAnalysis",
    "T1SpectrumAnalysis",
    "T2SpectrumAnalysis",
    "DCSpectrumAnalysis",
    "SwapOnceAnalysis",
    "SwapAnalysis",
    "QubitFreqCaliAnalysis",
    "DetuneCalibrationAnalysis",
    "BusCavityAnalysis",
    "CavityShiftF012Analysis",
    "ReadoutPowerF012CaliAnalysis",
    "CrosstalkOnceAnalysis",
    "LeakageOnceAnalysis",
    "LeakageAmpAnalysis",
    "LeakageNumAnalysis",
    "RoomTempDistortionAnalysis",
    "CavityPowerScanAnalysis",
    "RBAnalysis",
    "DistortionRBAnalysis",
    "MulInterleavedRBAnalysis",
    "ZZShitAnalysis",
    "ZZShiftZampAnalysis",
    "StabilityAnalysis",
    "SweepDetuneAnalysis",
    "ZZTimingOnceAnalysis",
    "ZZTimingCompositeAnalysis",
    "OneStepCalibrationAnalysis",
    "GradientCalibrationAnalysis",
    "SweetPointCalibrationAnalysis",
    "OptimizeFIRAnalysis",
    "XEBAnalysis",
    "NMAnalysis",
    "AmpCompositeAnalysis",
    "XYCrossRwAnalysis",
    "XYZTimingCompositeAnalysis",
    "XYCrosstalkOnceAnalysis",
    "XYCrosstalkAnalysis",
    "XYCrosstalkNpiAnalysis",
    "ImpaCavityAnalysis",
    "XYCrosstalkRBAnalysis",
    "LeakagePreAnalysis",
    "RamseySpectrumAnalysis",
    "PopulationLossOnceAnalysis",
    "PopulationLossSpectrumAnalysis",
    "PurityRBAnalysis",
    "SingInterleavedPurityRBAnalysis",
    "MulInterleavedPurityRBAnalysis",
    "XYCrossRabiWidthOnceAnalysis",
    "QubitSpectrum2DComAnalysis",
    "CouplerSweetPointCalibrationAnalysis",
    "CouplerFixedPointCalibrationAnalysis",
    "ImpaOptAnalysis",
    "ImpaGainAnalysis",
    "XEBCompositeAnalysis",
    "XEBPhaseAnalysis",
    "CPMGAnalysis",
    "ReadoutFreqSSCaliAnalysis",
    "RamseyEGAnalysis",
    "McmQubitDePhaseAnalysis",
    "McmQubitDePhaseComAnalysis",
    "McmQubitPopulationAnalysis",
    "McmSpectatorAnalysis",
    "AmpCompositeAnalysisV2",
    "APEAnalysisV2",
    "APECompositeAnalysisV2",
    "CavityAnalysisV2",
    "RBAnalysisV2",
    "TunableAnalysisV2",
    "SingleShotExtendAnalysis",
    "SingleShotExtendCompositeAnalysis",
    "AmpToPhotonAnalysis",
    "DePhaseRamseyAnalysis",
    "DephaseRamseyCompAnalysis",
    "PhotonNumMeasAnalysis",
    "PhotonNumMeasVsAmpAnalysis",
    "PhotonNumMeasVsFreqAnalysis",
    "PhotonScanReadoutFreqAnalysis",
    "PhotonScanReadoutFreqV2Analysis",
    "DistortionPhaseAnalysis",
    "DPhaseCompositeAnalysis",
    "RabiScanAmpPowerAnalysis",
    "RabiScanAmpPowerFreqAnalysis",
    "SingleShotExtendVsSampleWidthAnalysis",
    "QubitSpectrumScanPowerAnalysis",
    "CavityCheckAnalysis",
    "CavityCheckOnceAnalysis",
    "BusQAnalysis",
]
