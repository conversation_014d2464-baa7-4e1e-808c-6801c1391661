# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:58+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.tools.rst:2
msgid "pyQCat.tools package"
msgstr ""

#: ../../source/api/pyQCat.tools.rst:5
msgid "Module contents"
msgstr ""

#: of pyQCat.tools:3
msgid "Common Tools (:mod:`pyQCat.tools`)"
msgstr ""

#: of pyQCat.tools:5
msgid "pyQCat tools modules."
msgstr ""

#: of pyQCat.tools:8
msgid "NM_bnd Function"
msgstr ""

#: of pyQCat.tools:14:<autosummary>:1
msgid ""
":py:obj:`minimize_nelder_mead <pyQCat.tools.minimize_nelder_mead>`\\ "
"\\(func\\, x0\\[\\, args\\, step\\, ...\\]\\)"
msgstr ""

#: of pyQCat.tools:14:<autosummary>
msgid "param bounds"
msgstr ""

#: of pyQCat.tools:14:<autosummary>:1
msgid "Sequence of ``(min, max)`` pairs for each element in `x`. None"
msgstr ""

#: of pyQCat.tools:16
msgid "parse_yaml Function"
msgstr ""

#: of pyQCat.tools:23:<autosummary>:1
msgid ""
":py:obj:`parse_yaml_args <pyQCat.tools.parse_yaml_args>`\\ "
"\\(\\[yaml\\_path\\]\\)"
msgstr ""

#: of pyQCat.tools:23:<autosummary>:1
msgid "Parse yaml config info."
msgstr ""

#: of pyQCat.tools:23:<autosummary>:1
msgid ""
":py:obj:`generate_yaml_doc <pyQCat.tools.generate_yaml_doc>`\\ "
"\\(data\\[\\, yaml\\_path\\]\\)"
msgstr ""

#: of pyQCat.tools:23:<autosummary>:1
msgid "update yaml config info :param data: :param yaml_path:"
msgstr ""

#: of pyQCat.tools:25
msgid "S3 Storage Class"
msgstr ""

#: of pyQCat.tools:31:<autosummary>:1
msgid ""
":py:obj:`S3Storage <pyQCat.tools.S3Storage>`\\ \\(\\[endpoint\\_url\\, "
"access\\_key\\, ...\\]\\)"
msgstr ""

#: of pyQCat.tools:31:<autosummary>:1
msgid "s3 storage, use minio oss(object store)"
msgstr ""

#: of pyQCat.tools:33
msgid "utilities Function"
msgstr ""

#: of pyQCat.tools:61:<autosummary>:1
msgid ""
":py:obj:`sweep_combination <pyQCat.tools.sweep_combination>`\\ "
"\\(sweep\\_control\\_list\\)"
msgstr ""

#: of pyQCat.tools:61:<autosummary>:1
msgid "Sweep combination"
msgstr ""

#~ msgid ""
#~ ":py:obj:`minimize_nelder_mead "
#~ "<pyQCat.tools.minimize_nelder_mead>`\\ \\(func\\, x0\\[\\,"
#~ " args\\, step\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`parse_yaml_args <pyQCat.tools.parse_yaml_args>`\\ "
#~ "\\(\\[yaml\\_path\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`generate_yaml_doc <pyQCat.tools.generate_yaml_doc>`\\"
#~ " \\(data\\[\\, yaml\\_path\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`S3Storage <pyQCat.tools.S3Storage>`\\ "
#~ "\\(\\[endpoint\\_url\\, access\\_key\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`read_store_by_filename "
#~ "<pyQCat.tools.read_store_by_filename>`\\ \\(file\\[\\, "
#~ "username\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`update_union_readout_data "
#~ "<pyQCat.tools.update_union_readout_data>`\\ \\(qubit\\_list\\,"
#~ " ...\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`read_store_by_filename "
#~ "<pyQCat.tools.read_store_by_filename>`\\ \\(file\\[\\, "
#~ "username\\]\\)"
#~ msgstr ""

#~ msgid "Get config file data api."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`minimize_nelder_mead "
#~ "<pyQCat.tools.minimize_nelder_mead>`\\ \\(func\\, x0\\[\\,"
#~ " args\\, step\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`parse_yaml_args <pyQCat.tools.parse_yaml_args>`\\ "
#~ "\\(\\[yaml\\_path\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`generate_yaml_doc <pyQCat.tools.generate_yaml_doc>`\\ "
#~ "\\(data\\[\\, yaml\\_path\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`S3Storage <pyQCat.tools.S3Storage>`\\ "
#~ "\\(\\[endpoint\\_url\\, access\\_key\\, ...\\]\\)"
#~ msgstr ""

#~ msgid "set_config Function"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`update_union_readout_data "
#~ "<pyQCat.tools.update_union_readout_data>`\\ \\(qubit\\_list\\,"
#~ " ...\\[\\, ...\\]\\)"
#~ msgstr ""

#~ msgid "Update union readout data by origin union readout data."
#~ msgstr ""

