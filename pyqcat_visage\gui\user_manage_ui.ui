<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>940</width>
    <height>646</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>User Manager</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout" columnstretch="1,2">
    <item row="0" column="0">
     <widget class="QGroupBox" name="groupBox">
      <property name="title">
       <string>ID Card</string>
      </property>
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="0" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="2,2,6">
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string>username</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLineEdit" name="username_edit">
           <property name="enabled">
            <bool>false</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="1" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="2,2,6">
         <item>
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>group</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLineEdit" name="group_edit">
           <property name="enabled">
            <bool>false</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="2" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="2,2,6">
         <item>
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>email</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLineEdit" name="email_edit">
           <property name="enabled">
            <bool>false</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="3" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <item>
          <widget class="QCheckBox" name="is_super_check">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="text">
            <string>is_super</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="is_admin_check">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="text">
            <string>is_admin</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
    <item row="0" column="1">
     <widget class="QGroupBox" name="groupBox_2">
      <property name="title">
       <string>Group</string>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QTableViewGroupWidget" name="table_view_group"/>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="0" colspan="2">
     <widget class="QGroupBox" name="groupBox_3">
      <property name="title">
       <string>User</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QLineEdit" name="searchBar">
         <property name="placeholderText">
          <string>Search users in this group by username</string>
         </property>
         <property name="clearButtonEnabled">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTableViewUserWidget" name="table_view_user"/>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <property name="floatable">
    <bool>true</bool>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionCreate_Group"/>
   <addaction name="separator"/>
   <addaction name="actionChange_Password"/>
  </widget>
  <action name="actionAll_Groups">
   <property name="text">
    <string>All Groups</string>
   </property>
  </action>
  <action name="actionCreate_Group">
   <property name="text">
    <string>Create Group</string>
   </property>
  </action>
  <action name="actionChange_Password">
   <property name="text">
    <string>Change Password</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QTableViewGroupWidget</class>
   <extends>QTableView</extends>
   <header>.widgets.manager.table_view_groups</header>
  </customwidget>
  <customwidget>
   <class>QTableViewUserWidget</class>
   <extends>QTableView</extends>
   <header>.widgets.manager.table_view_users</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>actionAll_Groups</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>query_all_groups()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>434</x>
     <y>322</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionCreate_Group</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>create_group()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>434</x>
     <y>322</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionChange_Password</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>change_password()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>469</x>
     <y>322</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>searchBar</sender>
   <signal>textChanged(QString)</signal>
   <receiver>table_view_user</receiver>
   <slot>update_display()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>469</x>
     <y>365</y>
    </hint>
    <hint type="destinationlabel">
     <x>469</x>
     <y>494</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>query_all_groups()</slot>
  <slot>create_group()</slot>
  <slot>change_password()</slot>
 </slots>
</ui>
