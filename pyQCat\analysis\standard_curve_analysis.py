# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/07/30
# __author:       <PERSON><PERSON><PERSON>

import math

import numpy as np

from ..structures import ExperimentData, Options
from .base_analysis import BaseAnalysis
from .visualization import CurveDrawer


class StandardCurveAnalysis(BaseAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.curve_drawer = CurveDrawer()
        options.raw_data_format = "plot"
        options.child_figsize = (6, 4)
        options.merge_y_data = False
        options.plot_2d = False
        options.x_label = None
        options.y_label = None
        options.sub_title = None
        options.plot_raw_data = True

        return options

    def _initialize_canvas(self):
        # Default drawing of all y data data
        sub_axis_num = len(self.experiment_data.y_data)
        y_labels = list(self.experiment_data.y_data.keys())
        sub_titles = [f"X-{v}" for v in y_labels]

        # Determine whether to merge the drawings based on the option 'merge y_data'
        if sub_axis_num > 1 and self.options.merge_y_data is True:
            sub_axis_num += 1
            y_labels.append("All Y Data")
            sub_titles.append("Merge All Y Data")

        # Determine whether to plot a spectrum based on the option plot_2d
        if self.options.plot_2d is True and self.experiment_data.has_child:
            child_data: ExperimentData = self.experiment_data.child_data(index=0)  # type: ignore
            sub_axis_num += len(child_data.y_data)
            y_labels.extend(["Child Exp Scan Data" for _ in range(len(child_data.y_data))])
            sub_titles.extend([f"2D-{k}" for k in child_data.y_data.keys()])

        # Automatically expand canvas size
        if sub_axis_num == 1:
            subplots = (1, 1)
        elif sub_axis_num == 2:
            subplots = (2, 1)
        else:
            rs, cs = self.options.child_figsize
            row = math.ceil(sub_axis_num / 2)
            subplots = (row, 2)
            self.options.figsize = (rs * 2, row * cs)
        diff = subplots[0] * subplots[1] - sub_axis_num
        if diff > 0:
            y_labels.extend([""] * diff)
            sub_titles.extend([""] * diff)

        # Set Canvas Options
        self.drawer.set_options(
            subplots=self.options.get("subplots") or subplots,
            xlabel=self.options.x_label or self.experiment_data.scan_describe,
            ylabel=self.options.y_label or y_labels,
            sub_title=self.options.sub_title or sub_titles,
            figsize=self.options.figsize,
            raw_data_format=self.options.raw_data_format,
        )

        # CurveDrawer initialize canvas
        self.drawer.initialize_canvas()

    def _visualization(self) -> None:
        """Draw all data, including raw data and fit data and so on.
        Some composite experiments may need to override this function.
        """
        # define ax index
        ax_index = 0

        # Set plot title.
        self.drawer.set_options(title=self._description())

        # get experiment keys.
        exp_keys = list(self.experiment_data.y_data.keys())

        # plot raw data.
        for i, key in enumerate(exp_keys):
            if self.options.plot_raw_data is True:
                if self.experiment_data.replace_x_data and key in self.experiment_data.replace_x_data:
                    x_data = self.experiment_data.replace_x_data[key]
                else:
                    x_data = self.experiment_data.x_data
                raw_data = self.experiment_data.y_data[key]
                self.drawer.draw_raw_data(x_data=x_data, y_data=raw_data, ax_index=ax_index)
                
                # plot annotations.
                if self.drawer.options.text_pos and self.drawer.options.text_rp:
                    if key in self.drawer.options.text_key:
                        self.drawer.draw_text(ax_index=ax_index)
                
            ax_index += 1

        # plot all y daya in on axis
        if self.options.merge_y_data and len(exp_keys) > 1:
            x_arr = self.experiment_data.x_data
            y_data = self.experiment_data.y_data
            default_colors = self.drawer.options.default_colors
            color_len = len(default_colors)
            draw_ops = {"linewidth": 2.5, "color": None}
            for i, key in enumerate(y_data.keys()):
                y_arr = y_data.get(key)
                draw_ops.update({"label": key})
                if color_len >= ax_index:
                    draw_ops.update({"color": default_colors[i]})
                self.drawer.draw_raw_data(x_data=x_arr, y_data=y_arr, ax_index=ax_index, **draw_ops)
            ax_index += 1

        # plot 2D data
        if self.options.plot_2d is True and self.experiment_data.has_child:
            x_arr = self.experiment_data.x_data
            child_data: ExperimentData = self.experiment_data.child_data(index=0)  # type: ignore
            y_arr = child_data.x_data

            for child_data_key in list(child_data.y_data.keys()):
                child_2d_data = []
                for i, _ in enumerate(x_arr):
                    child_data: ExperimentData = self.experiment_data.child_data(index=i)  # type: ignore
                    child_2d_data.append(child_data.y_data.get(child_data_key))

                self.drawer.draw_color_map(
                    x_arr,
                    y_arr,
                    np.array(child_2d_data).T,
                    ax_index=ax_index,
                    **self.options.pcolormesh_options,
                )
                ax_index += 1

    def _format_canvas(self):
        # Finalize plot.
        self.drawer.format_canvas()


class StandardIQAnalysis(BaseAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.curve_drawer = CurveDrawer()
        options.raw_data_format = "scatter"
        options.child_figsize = (5, 4)
        options.x_label = "I"
        options.y_label = "Q"
        options.sub_title = None

        return options

    def _initialize_canvas(self):
        # Default drawing of all y data data
        sub_axis_num = len(self.experiment_data.y_data) // 2
        sub_title = list(set([v.split("-")[-1] for v in list(self.experiment_data.y_data.keys())]))
        self.options.sub_title = sub_title
        sub_titles = [f"IQ-{v}" for v in sub_title]

        # Automatically expand canvas size
        if sub_axis_num == 1:
            subplots = (1, 1)
        elif sub_axis_num == 2:
            subplots = (2, 1)
        else:
            rs, cs = self.options.child_figsize
            row = math.ceil(sub_axis_num / 2)
            subplots = (row, 2)
            self.options.figsize = (rs * 2, row * cs)
        diff = subplots[0] * subplots[1] - sub_axis_num
        if diff > 0:
            sub_titles.extend([""] * diff)

        # Set Canvas Options
        self.drawer.set_options(
            subplots=self.options.get("subplots") or subplots,
            xlabel=self.options.x_label,
            ylabel=self.options.y_label,
            sub_title=sub_titles,
            figsize=self.options.figsize,
        )

        # CurveDrawer initialize canvas
        self.drawer.initialize_canvas()

    def _visualization(self) -> None:
        """Draw all data, including raw data and fit data and so on.
        Some composite experiments may need to override this function.
        """
        # Set plot title.
        self.drawer.set_options(title=self._description())
        
        # plot raw data.
        for i, key in enumerate(self.options.sub_title):
            data_i = self.experiment_data.y_data.get(f"I-{key}")
            data_q = self.experiment_data.y_data.get(f"Q-{key}")
            self.drawer.draw_raw_data(x_data=data_i, y_data=data_q, ax_index=i)

    def _format_canvas(self):
        # Finalize plot.
        self.drawer.format_canvas()
