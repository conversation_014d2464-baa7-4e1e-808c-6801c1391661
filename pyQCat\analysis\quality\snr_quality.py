# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/09/09
# __author:       <PERSON><PERSON><PERSON> Shi


from .base_quality import BaseQuality, QualityDescribe


class SNRQuality(BaseQuality):
    def __init__(self, bound: float):
        super().__init__()

        self._bound = bound

        self._snr = None

        self.name = None
        self.eigenvalue = None

    def __repr__(self):
        return f"SNR={self._snr}({self.descriptor})"

    @property
    def value(self):
        return self._snr

    def evaluate(self, snr: float, name: str, eigenvalue: list):
        self._snr = round(snr, 3)
        self.name = name
        self.eigenvalue = eigenvalue

        if self._snr >= self._bound:
            self._quality = QualityDescribe.perfect
        else:
            self._quality = QualityDescribe.bad
            
    def to_dict(self):
        return dict(descriptor=self.descriptor, snr=self.value)
