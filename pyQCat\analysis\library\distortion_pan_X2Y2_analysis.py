# -*- coding: utf-8 -*-
from abc import ABC

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/17
# __author:       <PERSON><PERSON><PERSON>

import numpy as np
import matplotlib.pyplot as plt
from operator import itemgetter
from scipy.optimize import curve_fit, minimize
from sklearn.metrics import mean_squared_error

from pyQCat.analysis import CurveAnalysis, ParameterRepr
from pyQCat.structures import Options, QDict
from pyQCat.log import pyqlog


class DistortionPhaseTomoAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.set_validator('ana_scheme', ['pan', 'dicarlo'])

        options.subplots = (2, 2)
        options.x_label = ['<X>', 'Delay (ns)', 'Delay (ns)', 'Delay (ns)']
        # options.y_label = ['<Y>', r'<$\sigma$>', 'P1X', 'P1Y', 'phase', 'phase unwrap']
        options.y_label = ['<Y>', r'Probability', 'phase', 'phase unwrap']
        options.data_key = ['None']
        # options.correct_read = True
        options.fidelity_matrix = None
        options.result_parameters = [
            ParameterRepr(name='sigma_I', repr='sigma_I'),
            ParameterRepr(name='sigma_Q', repr='sigma_Q'),
            ParameterRepr(name='phase', repr='phase', unit='rad'),
            ParameterRepr(name='phase_unwrap', repr='phase_unwrap', unit='rad'),
            ParameterRepr(name='phase_std', repr='phase_std', unit='rad'),
            ParameterRepr(name='df_ref', repr='df', unit='GHz'),
            ParameterRepr(name='offset_ref', repr='offset', unit='rad'),
            ParameterRepr(name='phase_net', repr='phase_net', unit='rad')
        ]
        options.quality_bounds = [0.98, 0.95, 0.85]
        options.cbar = {
            'cmap': plt.cm.get_cmap("viridis")
        }
        options.avg_num = 1
        options.ana_scheme = 'pan'
        options.dicarlo_ref_time = 100
        options.base_num = 2
        return options

    def run_analysis(self):
        """Run analysis on experiment data.

        Notes:
            Sub analysis classes may need to override this method.
        """
        self._initialize()
        self._extract_result()
        if self.options.is_plot:
            self._visualization()

    def _create_analysis_data(self) -> QDict:
        base_num = self.options.base_num
        # x_data = self.experiment_data.x_data[:len(self.experiment_data.x_data) // 2]
        x_data = self.experiment_data.x_data[::base_num]
        # p0set = self.experiment_data.y_data['P0']
        p1set = self.experiment_data.y_data['P1']

        analysis_data_dict = QDict()
        analysis_data_dict['delays'] = np.array(x_data)
        analysis_data_dict['p1set'] = [p1set[idx::base_num] for idx in range(base_num)]
        return analysis_data_dict

    def _extract_result(self, data_key: str = None):
        base_num = self.options.base_num
        for p_name, result in self.results.items():
            if p_name not in ['df_ref', 'offset_ref']:
                result.extra.update({'out_flag': False})

        sigma_I_set, sigma_Q_set, phase_set, phase_unwrap, phase_std = self.phase_tomography()
        self.results.sigma_I.value = sigma_I_set
        self.results.sigma_Q.value = sigma_Q_set
        self.results.phase.value = phase_set
        self.results.phase_unwrap.value = phase_unwrap
        self.results.phase_std.value = phase_std

        if self.options.ana_scheme == 'dicarlo':
            delays = self.analysis_datas['delays']
            mask_ref = delays >= self.options.dicarlo_ref_time
            if not np.any(mask_ref):
                mask_ref[-20:] = [True] * len(mask_ref[-20:])
                pyqlog.warning(f'dicarlo_ref_time {self.options.dicarlo_ref_time} > max_delay {delays[-1]}ns, '
                               f'modify dicarlo_ref_time to {delays[mask_ref][0]}ns')

            self.results.df_ref.value = []
            self.results.offset_ref.value = []
            self.results.phase_net.value = []
            for idx in range(base_num):
                # phase_unwrap = phase_unwrap_set[idx]
                delays_ref = delays[mask_ref]
                phase_ref = phase_unwrap[mask_ref]
                f_linear = lambda x, k, b: 2*np.pi * k * x + b  # noqa
                popt, pcov = curve_fit(f_linear, delays_ref, phase_ref)
                rmse = mean_squared_error(phase_ref, f_linear(delays_ref, *popt))
                pyqlog.log('EXP', f'dicarlo scheme: df={popt[0]}GHz, rmse={rmse}')
                self.results.df_ref.value.append(popt[0])
                self.results.offset_ref.value.append(popt[1])
                self.results.phase_net.value.append(phase_unwrap - (2*np.pi * popt[0] * delays + popt[1]))

    def _visualization(self):
        delays = self.analysis_datas['delays']

        axis_legend = {
            "family": "Times New Roman",
            "weight": "normal",
            "size": 15,
        }

        self.drawer.set_options(title=self._description())
        self.drawer.set_options(raw_data_format="plot")
        self.drawer.set_options(axis_legend=axis_legend)

        base_num = self.options.base_num

        # self.options.cbar.update({'c': list(range(len(np.hstack(self.results.sigma_I.value))))})
        self.options.cbar.update({'c': np.tile(delays, base_num)})
        self.drawer.draw_xy_point(
            x_data=np.hstack(self.results.sigma_I.value),
            y_data=np.hstack(self.results.sigma_Q.value),
            ax_index=0,
            **self.options.cbar
        )
        self.drawer._axis[0].set_xlim(-1, 1)
        self.drawer._axis[0].set_ylim(-1, 1)

        colors = [c['color'] for c in plt.rcParams.get('axes.prop_cycle')]
        phases = [0, r'\pi/2', r'\pi', r'-\pi/2']
        for phase, idx in zip(phases, range(base_num)):
            self.drawer.draw_raw_data(
                x_data=delays,
                y_data=self.analysis_datas['p1set'][idx],
                label=f'$P_1^{{{phase}}}$',
                ax_index=1,
                color=colors[idx]
            )

        for idx in range(len(self.results.phase.value)):
            self.drawer.draw_raw_data(
                x_data=delays,
                y_data=self.results.phase.value[idx],
                label=f'phase{idx}',
                color=colors[idx],
                ax_index=2
            )
            if self.options.ana_scheme == 'pan':
                # self.drawer.draw_raw_data(
                #     x_data=delays,
                #     y_data=self.results.phase_unwrap.value[idx],
                #     ax_index=3
                # )
                self.drawer._axis[3].errorbar(
                    delays,
                    self.results.phase_unwrap.value,
                    self.results.phase_std.value,
                    fmt='-o',
                    capsize=3
                )
            elif self.options.ana_scheme == 'dicarlo':
                self.drawer.draw_raw_data(
                    x_data=delays,
                    y_data=self.results.phase_net.value[idx],
                    ax_index=3
                )

        self.drawer.format_canvas()

    def phase_tomography(self):
        base_num = self.options.base_num
        p1set = self.analysis_datas['p1set']
        sigma_I_set = []
        sigma_Q_set = []
        phase_set = []
        for idx in range(base_num):
            pI = p1set[idx]
            pQ = p1set[(idx+1) % base_num]
            sigma_I = 2 * pQ - 1
            sigma_Q = 1 - 2 * pI
            phase_set.append(np.angle(sigma_I + 1j * sigma_Q))
            sigma_I_set.append(sigma_I)
            sigma_Q_set.append(sigma_Q)

        # sigma_x = 1 - 2 * p1x
        # sigma_y = 1 - 2 * p1y
        # sigma_x = 1 - 2 * p1y
        # sigma_y = 2 * p1x - 1
        # phase = np.angle(sigma_x + 1j * sigma_y)

        # phase_set = np.reshape(phase, (self.options.avg_num, len(phase)//self.options.avg_num))
        phase_set = np.reshape(phase_set, (-1, len(self.analysis_datas.delays)))
        # phase_set = phase_align(phase_set)
        phase_unwrap_set = np.array([np.unwrap(phase[::-1])[::-1] for phase in phase_set])
        phase_unwrap_set = phase_align(phase_unwrap_set)
        # phase_unwrap = phase_unwrap_set.flatten()
        phase_std = np.std(phase_unwrap_set, axis=0)
        phase_unwrap = np.mean(phase_unwrap_set, axis=0)
        return sigma_I_set, sigma_Q_set, phase_set, phase_unwrap, phase_std


def phase_align(phase_set):
    def cost_fun(offsets, xarr):
        xarr = [x + offset for x, offset in zip(xarr, offsets)]
        cost = np.mean(np.std(xarr, axis=0))
        return cost

    res = minimize(
        cost_fun,
        x0=np.zeros(len(phase_set)),
        args=phase_set,
        method='Nelder-Mead'
    )

    phase_set_aligned = [phase + offset for phase, offset in zip(phase_set, res.x)]
    return phase_set_aligned
