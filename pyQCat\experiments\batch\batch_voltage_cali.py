# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/22
# __author:       <PERSON><PERSON><PERSON>

from typing import Dict, List

import numpy as np

from ...executor.batch import (
    check_coupler_drive_probe_bit,
    divide_coupler_calibration_parallel_group,
)
from ...log import pyqlog
from ...qubit import Co<PERSON>ler, Qubit
from ..batch_experiment import BatchExperiment


class BatchVoltageCalibration(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()

        options.set_validator("coupler_scheme", ["new", "old"])
        options.set_validator("qubit_scheme", ["idle", "max"])
        options.set_validator("auto_set_amp", bool)

        options.coupler_physical_units = None
        options.qubit_physical_units = None
        options.auto_set_amp = False
        options.qubit_init_amp = 0.05
        options.coupler_init_amp = 0.05

        options.coupler_scheme = "new"  # new or old
        options.coupler_new_flows = [
            "ZZShiftSweetPointCalibrationNew",
        ]
        options.coupler_old_flows = [
            "ZZShiftSweetPointCalibration",
        ]

        options.qubit_scheme = "idle"  # max or idle
        options.qubit_idle_flows = [
            "SingleShot_1",
            "VoltageDriftGradientCalibration_sw",
        ]
        options.qubit_max_flows = ["SingleShot_1", "SweetPointCalibration"]
        options.qubit_max_flows_idle = [
            "VoltageDriftGradientCalibration",
        ]

        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.amp_map = {}
        return options

    def _count_max_sweep_count(self, physical_units: List[str], init_zamp: float):
        max_count = 1
        if self.experiment_options.auto_set_amp:
            for unit in physical_units:
                zamp_list = self._get_zamp(unit, init_zamp)
                self.experiment_options.amp_map.update({unit: zamp_list})
                max_count = max(max_count, len(zamp_list))
        return max_count

    def _change_amp(
        self, i: int, physical_units: List[str], exp_name: str
    ) -> List[str]:
        if self.experiment_options.auto_set_amp:
            working_units = []
            exp_collection = self.params_manager.exp_map.get(exp_name)
            eop = exp_collection.options_for_parallel_exec.get("experiment_options", {})
            if "z_amp" not in eop:
                eop.update({"z_amp": {}})
            amp_map = self.experiment_options.amp_map
            for unit in physical_units:
                if len(amp_map.get(unit, [])) > i:
                    working_units.append(unit)
                    amp = amp_map[unit][i]
                    eop["z_amp"][unit] = amp
                    pyqlog.info(f"{unit} sweet point calibration: init z amp {amp} V")
            return working_units
        else:
            return physical_units

    def _run_batch(self):
        coupler_physical_units = self.experiment_options.coupler_physical_units
        pyqlog.info(f"start calibration coupler {coupler_physical_units}...")
        if self.experiment_options.coupler_scheme == "new":
            bad_units = self._run_coupler_new(coupler_physical_units)
        else:
            bad_units = self._run_coupler_old(coupler_physical_units)
        if bad_units:
            pyqlog.info(f"coupler failed units: {bad_units}")

        qubit_physical_units = self.experiment_options.qubit_physical_units
        pyqlog.info(f"start calibration qubit {qubit_physical_units} ...")
        if self.experiment_options.qubit_scheme == "idle":
            bad_units = self._run_qubit_idle(qubit_physical_units)
        else:
            bad_units = self._run_qubit_max(qubit_physical_units)
        if bad_units:
            pyqlog.info(f"qubit failed units: {bad_units}")

    def _run_coupler_old(self, coupler_physical_units: List[str]) -> List[str]:
        if self.experiment_options.coupler_old_flows:
            parallel_coupler_groups = divide_coupler_calibration_parallel_group(
                coupler_physical_units,
                self.context_manager.chip_data,
                **self.backend.system.parallel_divide,
            )
            for group in parallel_coupler_groups:
                sweep_count = self._count_max_sweep_count(
                    group, self.experiment_options.coupler_init_amp
                )
                for i in range(sweep_count):
                    working_units = self._change_amp(
                        i, group, exp_name="ZZShiftSweetPointCalibration"
                    )
                    if working_units:
                        pass_units = self._run_flow(
                            flows=self.experiment_options.coupler_old_flows,
                            physical_units=working_units,
                        )
                        if pass_units:
                            for unit in pass_units:
                                group.remove(unit)
                                coupler_physical_units.remove(unit)
        return coupler_physical_units

    def _run_coupler_new(self, coupler_physical_units: List[str]) -> List[str]:
        if self.experiment_options.coupler_new_flows:
            bad_units = []

            # run high freq qubit idle point calibration
            probe_qubit_units = []
            qc_qp_map = {}
            for unit in coupler_physical_units:
                qc = self.context_manager.chip_data.get_physical_unit(unit)
                probe_qubit_units.append(f"q{qc.probe_bit}")
                qc_qp_map[unit] = f"q{qc.probe_bit}"
            probe_qubit_units = list(set(probe_qubit_units))

            # qubit parallel divide
            parallel_qubit_groups = self.parallel_allocator_for_qc(probe_qubit_units)

            # run qubit idle calibration (sweet update) flow
            for gn, qubit_group in parallel_qubit_groups.items():
                cur_pass_units = self._run_flow(
                    flows=self.experiment_options.qubit_idle_flows,
                    physical_units=qubit_group,
                    name=f"{gn} qubit idle calibrate"
                )
                if cur_pass_units:
                    for unit in cur_pass_units:
                        probe_qubit_units.remove(unit)
            # remove fail coupler unit
            if probe_qubit_units:
                for coupler_unit, qp_unit in qc_qp_map.items():
                    if qp_unit in probe_qubit_units:
                        coupler_physical_units.remove(coupler_unit)
                        bad_units.append(coupler_unit)
                        pyqlog.warning(
                            f"{coupler_unit} probe qubit idle calibration fail ..."
                        )

            # run coupler sweet point calibration flow
            # coupler parallel divide group
            parallel_coupler_groups = divide_coupler_calibration_parallel_group(
                coupler_physical_units,
                self.context_manager.chip_data,
                **self.backend.system.parallel_divide,
            )
            for coupler_group in parallel_coupler_groups:
                cur_pass_units = self._run_flow(
                    flows=self.experiment_options.coupler_new_flows,
                    physical_units=coupler_group,
                )
                if cur_pass_units:
                    for unit in cur_pass_units:
                        coupler_physical_units.remove(unit)
            bad_units.extend(coupler_physical_units)
            return bad_units
        else:
            return coupler_physical_units

    def _run_qubit_idle(self, physical_units: List[str]) -> List[str]:
        if self.experiment_options.qubit_idle_flows:
            parallel_qubit_groups = self.parallel_allocator_for_qc(physical_units)
            for gn, group in parallel_qubit_groups.items():
                pass_units = self._run_flow(
                    flows=self.experiment_options.qubit_idle_flows,
                    physical_units=group,
                    name=f"{gn} qubit idle"
                )
                if pass_units:
                    for unit in pass_units:
                        physical_units.remove(unit)
        return physical_units

    def _run_qubit_max(self, physical_units: List[str]) -> List[str]:
        if self.experiment_options.qubit_max_flows:
            parallel_qubit_groups = self.parallel_allocator_for_qc(physical_units)
            for gn, group in parallel_qubit_groups.items():
                sweep_count = self._count_max_sweep_count(
                    group, self.experiment_options.qubit_init_amp
                )
                for idx in range(sweep_count):
                    working_units = self._change_amp(
                        idx, group, exp_name="SweetPointCalibration"
                    )
                    if working_units:
                        pass_units = self._run_flow(
                            flows=self.experiment_options.qubit_max_flows,
                            physical_units=working_units,
                            name=f"{gn} qubit max"
                        )
                        if pass_units:
                            pass_units = self._run_flow(
                                flows=self.experiment_options.qubit_max_flows_idle,
                                physical_units=pass_units,
                                name=f"{gn} qubit max idle"
                            )
                            if pass_units:
                                for unit in pass_units:
                                    group.remove(unit)
                                    physical_units.remove(unit)
        return physical_units

    def _get_zamp(self, physical_unit: str, init_zamp: float = 0.05):
        base_qubit = self.context_manager.chip_data.get_physical_unit(physical_unit)
        dc_max = base_qubit.dc_max
        dc_min = base_qubit.dc_min
        zamp_max = 0.47 - abs(dc_max)
        zamp_max = np.min([zamp_max, abs(dc_max - dc_min) / 3 * 2])
        zamp_list = [init_zamp, (zamp_max + abs(init_zamp)) / 2, zamp_max]
        return zamp_list

    def _get_coupler_qh(self, coupler_physical_units: List[str]) -> Dict[str, str]:
        qh_map = {}
        for coupler_name in coupler_physical_units:
            qc: Coupler = self.context_manager.chip_data.get_physical_unit(coupler_name)
            dq: Qubit = self.context_manager.chip_data.get_physical_unit(
                f"q{qc.drive_bit}"
            )
            pq: Qubit = self.context_manager.chip_data.get_physical_unit(
                f"q{qc.probe_bit}"
            )

            counts = 0
            qh = None

            if dq and dq.goodness and dq.tunable:
                qh = dq.name
                counts += 1

            if pq and pq.goodness and pq.tunable:
                qh = pq.name
                counts += 1

            if counts == 2:
                if dq.drive_freq > pq.drive_freq:
                    qh = dq.name
                else:
                    qh = pq.name
                qh_map.update({coupler_name: qh})
            elif counts == 1:
                qh_map.update({coupler_name: qh})
            else:
                pyqlog.warning(
                    f"Both probe and drive bits are not available when calibrating coupler: {coupler_name}"
                )

        return qh_map

    def _batch_up(self):
        super()._batch_up()
        check_coupler_drive_probe_bit(
            self.experiment_options.coupler_physical_units,
            self.context_manager.chip_data,
        )
