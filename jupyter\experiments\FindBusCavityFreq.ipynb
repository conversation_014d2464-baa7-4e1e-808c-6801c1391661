{"cells": [{"cell_type": "markdown", "id": "fce06e75", "metadata": {}, "source": ["# FindBusCavityFreq\n", "\n", "腔频粗扫实验"]}, {"cell_type": "code", "execution_count": 1, "id": "54284559", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\n"]}], "source": ["# 若用户未安装 pyqcat 包（monster），通过本地源码测试，需要执行下面操作\n", "# 若安装了 pyqcat 包（monster），跳过此步骤\n", "\n", "import os\n", "import sys\n", "\n", "\n", "package_root = \"../../../pyqcat-monster\"\n", "\n", "print(os.path.abspath(package_root))\n", "\n", "sys.path.insert(0, package_root)"]}, {"cell_type": "code", "execution_count": 2, "id": "4f4de072", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:09:34\u001b[0m | \u001b[1m  INFO  \u001b[0m | \u001b[1m\n", "|=============================================================|\n", "|                       Invoker <PERSON><PERSON><PERSON>                       |\n", "|=============================================================|\n", "|    invoker_addr    |        tcp://************:8088         |\n", "|    point_label     |              person_point              |\n", "|       sample       |               test_chip                |\n", "|      env_name      |                D00_1011                |\n", "|      version       |                 0.1.6                  |\n", "|=============================================================|\n", "\u001b[0m\n"]}, {"data": {"text/plain": ["{'code': 200,\n", " 'data': {'token': 'e5d450de44a02c795c4bfc63f13c860c'},\n", " 'msg': 'success'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib\n", "\n", "from pyQCat.tools import qarange\n", "from pyQCat.context import ExperimentContext\n", "from pyQCat.invoker import Invoker\n", "from pyQCat.parameters import get_parameters\n", "\n", "\n", "%matplotlib inline\n", "pd.set_option('max_colwidth', None)\n", "\n", "conf_file = r'E:\\WorkCode\\NewCodes\\newstart-monster\\pyqcat-monster\\conf\\config.conf'\n", "\n", "username = \"test_04\"\n", "password = \"123456\"\n", "\n", "context = ExperimentContext(conf_file)\n", "Invoker.verify_account(username=username, password=password)"]}, {"cell_type": "code", "execution_count": 3, "id": "c746cf3e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>experiment option</td>\n", "      <td>show_result</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_shape</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_data_path</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>experiment option</td>\n", "      <td>freq_list</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>experiment option</td>\n", "      <td>network_analyzer</td>\n", "      <td>E5071C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>experiment option</td>\n", "      <td>net_power</td>\n", "      <td>-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>experiment option</td>\n", "      <td>bus</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>experiment option</td>\n", "      <td>ATT</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>experiment option</td>\n", "      <td>net_IFBW</td>\n", "      <td>500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>analysis option</td>\n", "      <td>is_plot</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>analysis option</td>\n", "      <td>figsize</td>\n", "      <td>(12, 8)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>analysis option</td>\n", "      <td>distance</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>analysis option</td>\n", "      <td>cavity_count</td>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 type                 name    value\n", "0   experiment option          show_result     True\n", "1   experiment option      simulator_shape     None\n", "2   experiment option  simulator_data_path     None\n", "3   experiment option            freq_list     None\n", "4   experiment option     network_analyzer   E5071C\n", "5   experiment option            net_power      -30\n", "6   experiment option                  bus        0\n", "7   experiment option                  ATT        0\n", "8   experiment option             net_IFBW      500\n", "9     analysis option              is_plot     True\n", "10    analysis option              figsize  (12, 8)\n", "11    analysis option             distance       20\n", "12    analysis option         cavity_count        6"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from pyQCat.preliminary import FindBusCavityFreq\n", "\n", "fcf = FindBusCavityFreq.from_experiment_context(context)\n", "\n", "pd.DataFrame(fcf.options_table())"]}, {"cell_type": "markdown", "id": "28600b09", "metadata": {}, "source": ["**experiment options**\n", "\n", "*辅助功能选型*\n", "- show_result (bool): 是否在日志中展示分析结果\n", "- simulator_data_path (str): 模拟数据地址\n", "- simulator (bool): 是否开启模拟器\n", "\n", "*实验参数选项*\n", "- freq_list (list): 扫描读取频率列表\n", "- network_analyzer (str): 网分选择类型\n", "- bus (int): 测量BUS\n", "- ATT (float): 衰减\n", "\n", "**analysis options**\n", "- distance (float): 寻峰间距\n", "- cavity_count (int): 腔个数"]}, {"cell_type": "code", "execution_count": 4, "id": "058835a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m2022-10-14 17:09:34\u001b[0m | \u001b[34m  EXP   \u001b[0m | \u001b[34mResult path (local): D:\\test\\test_chip\\FindBusCavityFreq\\BUS0\\2022-10-14\\17.09.34\\\u001b[0m\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "079bb228a3194ce294fcfd724d51b358", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fcf.set_experiment_options(\n", "    network_analyzer='E5071C',\n", "    freq_list=np.linspace(7100, 7500, 1601),\n", "    bus=0,\n", "    simulator_data_path='../../scripts/simulator/data/FindBusCavityFreq/',\n", "    show_result=False\n", ")\n", "\n", "fcf.set_analysis_options(\n", "    cavity_count=6,\n", "    distance=20\n", ")\n", "\n", "fcf.run()"]}, {"cell_type": "code", "execution_count": 5, "id": "b4ebb7cd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "      <th>unit</th>\n", "      <th>extra</th>\n", "      <th>quality</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>fc_list</td>\n", "      <td>[7185.25, 7225.25, 7272.0, 7323.25, 7373.0, 7415.5]</td>\n", "      <td>MHz</td>\n", "      <td>{}</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      name                                                value unit extra  \\\n", "0  fc_list  [7185.25, 7225.25, 7272.0, 7323.25, 7373.0, 7415.5]  MHz    {}   \n", "\n", "  quality  \n", "0    None  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(fcf.analysis.show_results())"]}, {"cell_type": "code", "execution_count": 6, "id": "f4d4454f", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 2 Axes>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["fcf.analysis.drawer.figure"]}, {"cell_type": "code", "execution_count": 7, "id": "8368f651", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>experiment option</td>\n", "      <td>show_result</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_shape</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>experiment option</td>\n", "      <td>simulator_data_path</td>\n", "      <td>../../scripts/simulator/data/FindBusCavityFreq/FindBusCavity-good.dat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>experiment option</td>\n", "      <td>freq_list</td>\n", "      <td>[7100.0, 7100.25, 7100.5, 7100.75, 7101.0, 7101.25, 7101.5, 7101.75, 7102.0, 7102.25, 7102.5, 7102.75, 7103.0, 7103.25, 7103.5, 7103.75, 7104.0, 7104.25, 7104.5, 7104.75, 7105.0, 7105.25, 7105.5, 7105.75, 7106.0, 7106.25, 7106.5, 7106.75, 7107.0, 7107.25, 7107.5, 7107.75, 7108.0, 7108.25, 7108.5, 7108.75, 7109.0, 7109.25, 7109.5, 7109.75, 7110.0, 7110.25, 7110.5, 7110.75, 7111.0, 7111.25, 7111.5, 7111.75, 7112.0, 7112.25, 7112.5, 7112.75, 7113.0, 7113.25, 7113.5, 7113.75, 7114.0, 7114.25, 7114.5, 7114.75, 7115.0, 7115.25, 7115.5, 7115.75, 7116.0, 7116.25, 7116.5, 7116.75, 7117.0, 7117.25, 7117.5, 7117.75, 7118.0, 7118.25, 7118.5, 7118.75, 7119.0, 7119.25, 7119.5, 7119.75, 7120.0, 7120.25, 7120.5, 7120.75, 7121.0, 7121.25, 7121.5, 7121.75, 7122.0, 7122.25, 7122.5, 7122.75, 7123.0, 7123.25, 7123.5, 7123.75, 7124.0, 7124.25, 7124.5, 7124.75, ...]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>experiment option</td>\n", "      <td>network_analyzer</td>\n", "      <td>E5071C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>experiment option</td>\n", "      <td>net_power</td>\n", "      <td>-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>experiment option</td>\n", "      <td>bus</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>experiment option</td>\n", "      <td>ATT</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>experiment option</td>\n", "      <td>net_IFBW</td>\n", "      <td>500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>analysis option</td>\n", "      <td>is_plot</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>analysis option</td>\n", "      <td>figsize</td>\n", "      <td>(12, 8)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>analysis option</td>\n", "      <td>distance</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>analysis option</td>\n", "      <td>cavity_count</td>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 type                 name  \\\n", "0   experiment option          show_result   \n", "1   experiment option      simulator_shape   \n", "2   experiment option  simulator_data_path   \n", "3   experiment option            freq_list   \n", "4   experiment option     network_analyzer   \n", "5   experiment option            net_power   \n", "6   experiment option                  bus   \n", "7   experiment option                  ATT   \n", "8   experiment option             net_IFBW   \n", "9     analysis option              is_plot   \n", "10    analysis option              figsize   \n", "11    analysis option             distance   \n", "12    analysis option         cavity_count   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      value  \n", "0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     False  \n", "1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      None  \n", "2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ../../scripts/simulator/data/FindBusCavityFreq/FindBusCavity-good.dat  \n", "3   [7100.0, 7100.25, 7100.5, 7100.75, 7101.0, 7101.25, 7101.5, 7101.75, 7102.0, 7102.25, 7102.5, 7102.75, 7103.0, 7103.25, 7103.5, 7103.75, 7104.0, 7104.25, 7104.5, 7104.75, 7105.0, 7105.25, 7105.5, 7105.75, 7106.0, 7106.25, 7106.5, 7106.75, 7107.0, 7107.25, 7107.5, 7107.75, 7108.0, 7108.25, 7108.5, 7108.75, 7109.0, 7109.25, 7109.5, 7109.75, 7110.0, 7110.25, 7110.5, 7110.75, 7111.0, 7111.25, 7111.5, 7111.75, 7112.0, 7112.25, 7112.5, 7112.75, 7113.0, 7113.25, 7113.5, 7113.75, 7114.0, 7114.25, 7114.5, 7114.75, 7115.0, 7115.25, 7115.5, 7115.75, 7116.0, 7116.25, 7116.5, 7116.75, 7117.0, 7117.25, 7117.5, 7117.75, 7118.0, 7118.25, 7118.5, 7118.75, 7119.0, 7119.25, 7119.5, 7119.75, 7120.0, 7120.25, 7120.5, 7120.75, 7121.0, 7121.25, 7121.5, 7121.75, 7122.0, 7122.25, 7122.5, 7122.75, 7123.0, 7123.25, 7123.5, 7123.75, 7124.0, 7124.25, 7124.5, 7124.75, ...]  \n", "4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    E5071C  \n", "5                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       -30  \n", "6                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         0  \n", "7                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         0  \n", "8                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       500  \n", "9                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      True  \n", "10                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  (12, 8)  \n", "11                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       20  \n", "12                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        6  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(fcf.options_table())"]}, {"cell_type": "code", "execution_count": null, "id": "74d9c27d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}