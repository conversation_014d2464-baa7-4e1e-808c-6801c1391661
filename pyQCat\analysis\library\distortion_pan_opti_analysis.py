# -*- coding: utf-8 -*-
# @Time     : 2023/5/4 22:24
# <AUTHOR> WTL
# @Software : PyCharm
import numpy as np
import matplotlib.pyplot as plt

from pyQCat.analysis import CurveAnalysis, ParameterRepr
from pyQCat.structures import Options


class DistortionOptiAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.delays = None
        options.subplots = (2, 1)
        options.x_label = ['Iter Num', 'Iter Num']
        options.y_label = ['phase unwrap std', 'Delay']
        options.data_key = ['None']
        options.result_parameters = [
            ParameterRepr(name='phase_std', repr='std', unit='rad'),
            ParameterRepr(name='phase_avg', repr='avg', unit='rad')
        ]
        options.pcolormesh_options = {
            "shading": "nearest",
            "cmap": plt.cm.get_cmap('viridis')
        }
        return options

    def run_analysis(self):
        self._initialize()
        self._extract_result()
        self._visualization()

    def _extract_result(self, data_key: str = None):
        for p_name, result in self.results.items():
            if p_name == 'phase_avg':
                result.extra.update({'out_flag': False})

        self.results.phase_std.value = self.experiment_data.y_data['phase_unwrap_std']
        self.results.phase_avg.value = self.experiment_data.y_data['phase_unwrap_avg']

    def _visualization(self):
        iters = self.experiment_data.x_data
        delays = self.options.delays
        self.drawer.set_options(title=self._description())
        self.drawer.set_options(raw_data_format="plot")

        self.drawer.draw_raw_data(
            x_data=iters,
            y_data=self.results.phase_std.value,
            ax_index=0
        )
        self.drawer._axis[0].set_ylim(0, 0.1)

        self.drawer.draw_color_map(
            iters, delays, np.array(self.results.phase_avg.value).T,
            ax_index=1,
            **self.options.pcolormesh_options
        )
