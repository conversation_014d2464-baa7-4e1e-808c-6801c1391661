# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# __date:         2023/08/18
# __author:       <PERSON><PERSON><PERSON>

import json
import os
import re
from collections import defaultdict
from copy import deepcopy
from functools import cmp_to_key
from pathlib import Path
from typing import Dict, List

import numpy as np
from prettytable import PrettyTable

from ....analysis import CurveAnalysis
from ....analysis.fit import freq2amp_formula
from ....processor.utilities import sort_bit
from ....errors import ExperimentOptionsError
from ....gate import CPhaseGate, Rphi_gate
from ....log import pyqlog
from ....pulse.pulse_function import zero_pulse
from ....pulse.pulse_lib import Constant
from ....structures import Options, QDict
from ...composite_experiment import CompositeExperiment
from ...top_experiment import TopExperiment

RE_PATTERN = QDict(
    measure_qubit=r"MEASURE q[[\d]+]",
    qinit=r"QINIT [\d]+",
    creg=r"CREG [\d]+",
    rx=r"RX q[[\d]+]",
    u3=r"U3 q[[\d]+]",
    h=r"H q[[\d]+]",
    cz=r"CZ q[[\d]+]",
    cnot=r"CNOT q[[\d]+]",
    barrier=r"BARRIER q[[\d]+]",
    bit_format=r"[[](.*?)[]]",
)


def circuit_split(circuit_code: str, log_path=""):
    """Decomposing quantum circuits into combinations of Rphi and CZ.

    Args:
        circuit_code (str): Quantum circuit, for example:
            ```
                RX q[0],(-1.570796)
                RX q[1],(-1.570796)
                RX q[2],(-1.570796)
                H q[1]
                CZ q[0],q[1]
                RX q[1],(-0.235619)
                CZ q[0],q[1]
                H q[1]
                H q[2]
                CZ q[1],q[2]
                RX q[2],(-0.235619)
                CZ q[1],q[2]
                RX q[0],(-1.570796)
                RX q[1],(-1.570796)
                H q[2]
                U3 q[0],(1.570796,4.712389,1.570796)
                RX q[2],(-1.570796)

                MEASURE q[0],c[0]
                MEASURE q[1],c[1]
                MEASURE q[2],c[2]
            ```

        log_path (str): You can fill in a folder path and we will input the parsing process as JSON

    Returns:
        - Quantum Gate Identifier Dictionary;
        - Qubit set;
        - Coupler set;
        - Qubit Pair set;
        - Measure qubit set
    """

    # format circuit code
    circuit_code.strip()
    circuit_records = [
        circuit_line.strip().split(",")
        for circuit_line in circuit_code.split("\n")
        if circuit_line.strip()
    ]

    # circuit analytic
    qubit_set = set()
    coupler_set = set()
    qubit_pair_set = set()
    circuit_map = defaultdict(list)
    measure_qubit_list = []

    # cz split util
    def _cz_split(_rd):
        _qc_bit = f"q{re.findall(RE_PATTERN.bit_format, _rd[0])[0]}"
        _qt_bit = f"q{re.findall(RE_PATTERN.bit_format, _rd[1])[0]}"

        cl_qc = len(circuit_map.get(_qc_bit, []))
        cl_qt = len(circuit_map.get(_qt_bit, []))
        gap = cl_qc - cl_qt
        if abs(gap) > 0:
            if gap > 0:
                long_bit, shot_bit = _qc_bit, _qt_bit
            else:
                long_bit, shot_bit = _qt_bit, _qc_bit

            min_index = min(cl_qt, cl_qc)
            for gate in circuit_map.get(long_bit)[min_index:]:
                if gate[0] in ["CZ", "CZI"]:
                    circuit_map[shot_bit].append(["CZI", 1])
                else:
                    circuit_map[shot_bit].append(["I", 1])

        _pair_name = (
            f"{_qc_bit}{_qt_bit}"
            if int(_qc_bit[1:]) < int(_qt_bit[1:])
            else f"{_qt_bit}{_qc_bit}"
        )
        _coupler_name = (
            f"c{_qc_bit[1:]}-{_qt_bit[1:]}"
            if int(_qc_bit[1:]) < int(_qt_bit[1:])
            else f"c{_qt_bit[1:]}-{_qc_bit[1:]}"
        )

        return _qc_bit, _qt_bit, _pair_name, _coupler_name

    # gate alignment
    def _alignment(align_bits):
        max_length = 0
        _bits = []
        max_circuits = None
        for _bit in align_bits:
            circuits = circuit_map.get(_bit)
            if len(circuits) > max_length:
                max_length = len(circuits)
                max_circuits = circuits
            _bits.append(_bit)

        for _bit in align_bits:
            circuits = circuit_map.get(_bit)
            cur_len = len(circuits)
            while cur_len < max_length:
                if max_circuits[cur_len][0] in ["CZ", "CZI"]:
                    circuits.append(["CZI", 1])
                else:
                    circuits.append(["I", 1])
                cur_len += 1

    # iterate split
    for rd in circuit_records:
        for i, r in enumerate(rd):
            rd[i] = r.strip()

        if re.search(RE_PATTERN.qinit, rd[0]):
            continue

        if re.search(RE_PATTERN.creg, rd[0]):
            continue

        if re.search(RE_PATTERN.measure_qubit, rd[0]):
            bit = f"q{re.findall(RE_PATTERN.bit_format, rd[0])[0]}"
            qubit_set.add(bit)
            measure_qubit_list.append(bit)
            continue

        if re.search(RE_PATTERN.rx, rd[0]):
            bit = f"q{re.findall(RE_PATTERN.bit_format, rd[0])[0]}"
            theta = float(rd[1][1:-1])
            circuit_map[bit].extend(
                [
                    ["RZ", -np.pi / 2],
                    ["Rphi", 0],
                    ["RZ", np.pi - theta],
                    ["Rphi", 0],
                    ["RZ", -np.pi / 2],
                ]
            )
            qubit_set.add(bit)
            continue

        if re.search(RE_PATTERN.u3, rd[0]):
            bit = f"q{re.findall(RE_PATTERN.bit_format, rd[0])[0]}"
            thetas = [float(rd[1][1:]), float(rd[2]), float(rd[3][:-1])]
            circuit_map[bit].extend(
                [
                    ["RZ", thetas[2]],
                    ["Rphi", 0],
                    ["RZ", thetas[0]],
                    ["Rphi", np.pi],
                    ["RZ", thetas[1]],
                ]
            )
            qubit_set.add(bit)
            continue

        if re.search(RE_PATTERN.h, rd[0]):
            bit = f"q{re.findall(RE_PATTERN.bit_format, rd[0])[0]}"
            circuit_map[bit].extend(
                [["RZ", np.pi / 2], ["Rphi", 0], ["RZ", np.pi / 2], ["I", 1], ["I", 1]]
            )
            qubit_set.add(bit)
            continue

        if re.search(RE_PATTERN.cz, rd[0]):
            qc_bit, qt_bit, pair_name, coupler_name = _cz_split(rd)

            circuit_map[qc_bit].append(["CZ", pair_name])
            circuit_map[qt_bit].append(["CZ", pair_name])

            qubit_pair_set.add(pair_name)
            coupler_set.add(coupler_name)
            qubit_set.add(qc_bit)
            qubit_set.add(qt_bit)
            continue

        if re.search(RE_PATTERN.cnot, rd[0]):
            qc_bit, qt_bit, pair_name, coupler_name = _cz_split(rd)

            circuit_map[qc_bit].extend(
                [
                    ["I", 1],
                    ["I", 1],
                    ["I", 1],
                    ["CZ", pair_name],
                    ["I", 1],
                    ["I", 1],
                    ["I", 1],
                ]
            )
            circuit_map[qt_bit].extend(
                [
                    ["RZ", np.pi / 2],
                    ["Rphi", 0],
                    ["RZ", np.pi / 2],
                    ["CZ", pair_name],
                    ["RZ", np.pi / 2],
                    ["Rphi", 0],
                    ["RZ", np.pi / 2],
                ]
            )

            qubit_pair_set.add(pair_name)
            coupler_set.add(coupler_name)
            qubit_set.add(qc_bit)
            qubit_set.add(qt_bit)
            continue

        if re.search(RE_PATTERN.barrier, rd[0]):
            bits = []
            for r in rd:
                bit = f"q{re.findall(RE_PATTERN.bit_format, r)[0]}"
                bits.append(bit)
            _alignment(bits)
            continue

        raise NameError(f"Can't tackle {rd}")

    _alignment(list(qubit_set))

    if Path(log_path).is_dir():
        with open(
            str(Path(log_path, "s1-circuit-split.json")), mode="w", encoding="utf-8"
        ) as f:
            json.dump(circuit_map, f, indent=4, ensure_ascii=False)

    assert len(qubit_pair_set) == len(coupler_set)

    return circuit_map, qubit_set, coupler_set, qubit_pair_set, measure_qubit_list


def virtual_z(circuit_map: Dict, cz_phase: Dict = None, log_path=""):
    """Implementing virtual Z-gates for quantum circuits.

    Args:
        circuit_map (dict): Correspondence between Bits and Quantum Circuit Gates.
        cz_phase (dict): Single qubit phase from CZ-gate
        log_path (str): You can fill in a folder path, we will input the parsing process as JSON

    Returns:
        Quantum Gate Identifier Dictionary;
    """
    # virtual z
    cz_phase = cz_phase or {}
    max_length = 0
    for bit, circuits in circuit_map.items():
        sum_phase = 0
        for gate in circuits:
            if gate[0] == "RZ":
                sum_phase += gate[-1]
                # pyqlog.info(f"{bit}: {gate} + phase, sum phase({sum_phase})!")
                gate[0] = "I"
                gate[-1] = 1
            elif gate[0] == "Rphi":
                gate[-1] -= sum_phase
                # pyqlog.info(f"{bit}: {gate} - phase{sum_phase})!")
            elif gate[0] == "CZ":
                phase = cz_phase.get(gate[1], {}).get(bit, 0)
                sum_phase += phase
                # pyqlog.info(f"{bit}: {gate} + phase, sum phase({sum_phase})!")
        max_length = max(len(circuits), max_length)

    if Path(log_path).is_dir():
        with open(
            str(Path(log_path, "s2-virtual-z.json")), mode="w", encoding="utf-8"
        ) as f:
            json.dump(circuit_map, f, indent=4, ensure_ascii=False)

    return circuit_map


def eliminate_i_gate(circuit_map: Dict, log_path=""):
    """Eliminate useless I gate operations in quantum gate sequences and reduce line length.

    Args:
        circuit_map (dict): Correspondence between Bits and Quantum Circuit Gates.
        log_path (str): You can fill in a folder path, we will input the parsing process as JSON

    Returns:
        Quantum Gate Identifier Dictionary;
    """
    # Eliminate I Gate
    max_length = max([len(cr) for cr in circuit_map.values()])
    eliminate_indexes = []
    for i in range(max_length):
        all_i_gate = True
        for circuits in circuit_map.values():
            if i < len(circuits) and circuits[i][0] != "I":
                all_i_gate = False
                break
        if all_i_gate:
            eliminate_indexes.append(i)
    for bit in list(circuit_map.keys()):
        circuits = circuit_map.get(bit)
        ei_circuits = [
            cir for idx, cir in enumerate(circuits) if idx not in eliminate_indexes
        ]
        zip_ei_circuits = []
        for cir in ei_circuits:
            if (
                cir[0] in ["I", "CZI"]
                and zip_ei_circuits
                and zip_ei_circuits[-1][0] == cir[0]
            ):
                zip_ei_circuits[-1][1] += cir[1]
                continue
            else:
                zip_ei_circuits.append(cir)
        circuit_map[bit] = zip_ei_circuits

    if Path(log_path).is_dir():
        with open(
            str(Path(log_path, "s3-eliminate-i.json")), mode="w", encoding="utf-8"
        ) as f:
            json.dump(circuit_map, f, indent=4, ensure_ascii=False)
    return circuit_map


def add_clock(circuit_map: Dict, log_path=""):
    """Adding clock signals to quantum gate sequences,
    it's mainly used for comparison with parsing files executed by pyqcat-cloud

    Args:
        circuit_map (dict): Correspondence between Bits and Quantum Circuit Gates.
        log_path (str): You can fill in a folder path, we will input the parsing process as JSON

    Returns:
        no return
    """

    def _add_clock(cir_list):
        for i, gate in enumerate(cir_list):
            if gate[0] == "Rphi":
                theta = gate[1]
                gate[1] = theta * 1 / np.pi * 180

            if i == 0:
                gate.append(0)
            elif cir_list[i - 1][0] == "CZ":
                gate.append(cir_list[i - 1][-1] + 40)
            elif cir_list[i - 1][0] == "I":
                gate.append(cir_list[i - 1][-1] + 30 * cir_list[i - 1][1])
            elif cir_list[i - 1][0] == "CZI":
                gate.append(cir_list[i - 1][-1] + 40 * cir_list[i - 1][1])
            else:
                gate.append(cir_list[i - 1][-1] + 30)

    for bit, circuits in circuit_map.items():
        _add_clock(circuits)

    if Path(log_path).is_dir():
        with open(str(Path(log_path, "s4-add-clock.json")), encoding="utf-8") as f:
            json.dump(circuit_map, f, indent=4, ensure_ascii=False)


def cloud_circuit_socket(cloud_circuits, log_path=""):
    """pyqcat-cloud circuit socket utils

    Args:
        cloud_circuits: pyqcat-cloud quantum circuit, for example:
            [
                {"RPhi": [45, -269.2901637114869, 0]},
                {"RPhi": [45, -243.41581060167834, 30]},
                {"RPhi": [46, -242.36114734033825, 0]},
                {"RPhi": [46, -301.28454790989156, 30]},
                {"RPhi": [52, -66.46310423517549, 0]},
                {"RPhi": [52, -15.951565934741552, 30]},
                {"RPhi": [53, -70.47380880109125, 0]},
                {"RPhi": [53, -20.53522829578813, 30]},
                {"RPhi": [54, -221.16170892049777, 0]},
                {"RPhi": [54, -163.77467707849394, 30]},
                {"RPhi": [48, -149.54198452914486, 0]},
                {"RPhi": [48, -175.8067907762412, 30]},
                {"RPhi": [46, -668.1144152870007, 60]},
                {"CZ": [45, 46, 90]},
                {"RPhi": [46, -818.3206099401979, 130]}
            ]
        log_path (str): You can fill in a folder path, we will input the parsing process as JSON

    Returns:
        no return
    """
    circuit_map = defaultdict(list)
    for cir in cloud_circuits:
        if "RPhi" in cir:
            infos = cir.get("RPhi")
            bit = f"q{infos[0]}"
            circuit_map[bit].append(["Rphi", infos[1], infos[2]])
        elif "CZ" in cir:
            infos = cir.get("CZ")
            qc = f"q{infos[0]}"
            qt = f"q{infos[1]}"
            t = infos[2]
            circuit_map[qt].append(["CZ", f"{qc}{qt}", t])
            circuit_map[qc].append(["CZ", f"{qc}{qt}", t])

    if Path(log_path).is_dir():
        with open(str(Path(log_path, "monster_circuit_5.json")), encoding="utf-8") as f:
            json.dump(circuit_map, f, indent=4, ensure_ascii=False)


def circuit_compile(path: str, cz_phase: Dict = None, log_path=""):
    if Path(path).is_file():
        with open(path, encoding="utf-8", mode="r") as f:
            circuit_code = f.read()
    else:
        circuit_code = path

    (
        circuit_map,
        qubit_set,
        coupler_set,
        qubit_pair_set,
        measure_qubit_set,
    ) = circuit_split(circuit_code, log_path)
    circuit_map = virtual_z(circuit_map, cz_phase, log_path)
    circuit_map = eliminate_i_gate(circuit_map, log_path)

    if log_path:
        with open(str(Path(log_path, "circuit.txt")), encoding="utf-8", mode="w") as f:
            f.write(circuit_code)

        add_clock(deepcopy(circuit_map), log_path)

    return circuit_map, qubit_set, coupler_set, qubit_pair_set, measure_qubit_set


class _Execute(TopExperiment):
    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.circuit_map_list = None
        options.qubit_pairs = None
        options.width = QDict()
        options.pulse_map = defaultdict(list)
        options.unit_map = None
        options.cz_gate_map = None
        options.measure_circuit_qubits = None

        return options

    def _check_options(self):
        super()._check_options()
        rop = self.run_options

        cz_phase_map = {}
        for qubit_pair in rop.qubit_pairs:
            ql, qh = qubit_pair.ql, qubit_pair.qh
            cz_phase_map[qubit_pair.name] = {
                ql: qubit_pair.cz_value(ql, "phase"),
                qh: qubit_pair.cz_value(qh, "phase"),
            }

        for cm in rop.circuit_map_list:
            cm = virtual_z(cm, cz_phase_map)
            cm = eliminate_i_gate(cm)
            for bit, circuits in cm.items():
                bit_obj = rop.unit_map.get(bit)
                self._get_xy_pulse(bit_obj, circuits)
                self._get_z_pulse(bit_obj, circuits)
            for coupler in self.couplers:
                pq, dq = f"q{coupler.probe_bit}", f"q{coupler.drive_bit}"
                pair_name = (
                    f"{pq}{dq}"
                    if coupler.probe_bit < coupler.drive_bit
                    else f"{dq}{pq}"
                )
                pq_cir = cm.get(pq)
                new_cir = []
                for gate in pq_cir:
                    if gate[0] == "CZ":
                        if gate[1] == pair_name:
                            new_cir.append(gate)
                        else:
                            new_cir.append(["CZI", 1])
                    elif gate[0] == "CZI":
                        new_cir.append(deepcopy(gate))
                    elif new_cir and new_cir[-1][0] == "I":
                        if gate[0] == "I":
                            new_cir[-1][1] += gate[1]
                        else:
                            new_cir[-1][1] += 1
                    else:
                        if gate[0] == "I":
                            new_cir.append(deepcopy(gate))
                        else:
                            new_cir.append(["I", 1])

                self._get_z_pulse(coupler, new_cir)

        # get readout channel
        multi_readout_channels = []
        for qubit in self.run_options.measure_circuit_qubits:
            multi_readout_channels.append(qubit.readout_channel)

        self.set_experiment_options(
            data_type="I_Q", multi_readout_channels=multi_readout_channels
        )

        if len(rop.circuit_map_list) == 1:
            self.set_experiment_options(enable_one_sweep=True)

        self.set_analysis_options(is_plot=False)

    def _get_xy_pulse(self, qubit, circuits: List):
        pulse = Constant(0, 0, "XY")()
        for gate in circuits:
            if gate[0] == "Rphi":
                pulse += Rphi_gate(phase=gate[1]).to_pulse(qubit)()
            elif gate[0] == "I":
                width = self.run_options.width.gate_1q * gate[1]
                pulse += Constant(width, 0, "XY")()
            elif gate[0] == "CZI":
                width = self.run_options.width.gate_2q * gate[1]
                pulse += Constant(width, 0, "XY")()
            elif gate[0] == "CZ":
                width = self.run_options.width.gate_2q
                pulse += Constant(width, 0, "XY")()
        self.run_options.pulse_map[f"{qubit.name}-xy"].append(pulse)

    def _get_z_pulse(self, qubit, circuits: List):
        pulse = Constant(0, 0)()
        for gate in circuits:
            if gate[0] == "CZ":
                pulse += self.run_options.cz_gate_map.get(gate[1]).to_pulse(qubit)
            elif gate[0] == "I":
                tw = self.run_options.width.gate_1q * gate[1]
                pulse += Constant(tw, 0)()
            elif gate[0] == "CZI":
                tw = self.run_options.width.gate_2q * gate[1]
                pulse += Constant(tw, 0)()
            else:
                pulse += Constant(self.run_options.width.gate_1q, 0)()
        self.run_options.pulse_map[f"{qubit.name}-z"].append(pulse)

    def _set_xy_pulses(self):
        for qubit in self.qubits:
            pulse = self.run_options.pulse_map.get(f"{qubit.name}-xy")
            self.play_pulse("XY", qubit, pulse)

    def _set_z_pulses(self):
        for qubit in self.qubits:
            pulse = self.run_options.pulse_map.get(f"{qubit.name}-z")
            self.play_pulse("Z", qubit, pulse)

        for coupler in self.couplers:
            pulse = self.run_options.pulse_map.get(f"{coupler.name}-z")
            self.play_pulse("Z", coupler, pulse)

    def _set_measure_pulses(self):
        if len(self.run_options.measure_circuit_qubits) == 1:
            self._set_single_readout_pulse(self.run_options.measure_circuit_qubits[0])
        else:
            self._set_union_readout_pulse(self.run_options.measure_circuit_qubits)

    def _update_instrument(self):
        # if len(self.run_options.circuit_map_list) > 1:
        for qubit in self.run_options.measure_circuit_qubits:
            self._bind_probe_inst(qubit)

        for channel in self.experiment_options.multi_readout_channels:
            sweep_delay = self._pulse_time_list[
                : len(self.run_options.circuit_map_list)
            ]
            self.sweep_readout_trigger_delay(channel, sweep_delay)

    def _special_run_analysis(self):
        self._run_analysis(
            x_data=range(len(self.run_options.circuit_map_list)),
            analysis_class=CurveAnalysis,
        )

    def run(self):
        super().run()
        self._special_run_analysis()

        table = PrettyTable()
        table.field_names = ["Probability", "Value"]

        measure_result = []
        for k, v in self.experiment_data.y_data.items():
            v = [round(_v, 4) for _v in v]
            measure_result.append(v)
            v = str(list(v))
            if len(v) > 30:
                v = f"{v[:15]} ... {v[-15:]}"
            table.add_row([k, v])
            # print(v)

        print(table)
        measure_result = list(np.array(measure_result).T)
        return measure_result


class ExecuteQuantumCircuit(CompositeExperiment):
    _sub_experiment_class = _Execute

    @classmethod
    def from_experiment_context(cls, context):
        exp = super().from_experiment_context(context)
        exp._context = context
        return exp

    @property
    def context(self):
        return self._context

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        # options.set_validator("circuit_code", str, limit_null=True)
        options.circuit_code = None
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.circuit_code_strings = []
        options.er_prob_list = []
        return options

    def run(self):
        super().run()
        circuit_code = self.experiment_options.circuit_code
        circuit_code_strings = self.run_options.circuit_code_strings

        # load circuit code
        txt_files = []
        if isinstance(circuit_code, list):
            circuit_code_strings = circuit_code
        elif Path(circuit_code).is_dir():
            txt_files = [
                str(Path(root, file))
                for root, _, files in os.walk(circuit_code)
                for file in files
                if file.endswith(".txt")
            ]
            for fls in txt_files:
                with open(fls, encoding="utf-8", mode="r") as f:
                    circuit_code_strings.append(f.read())
        elif Path(circuit_code).is_file() and circuit_code.endswith(".txt"):
            txt_files = [circuit_code]
            with open(circuit_code, encoding="utf-8", mode="r") as f:
                circuit_code_strings.append(f.read())
        elif isinstance(circuit_code, str):
            circuit_code_strings.append(circuit_code)
        else:
            raise ExperimentOptionsError(
                self,
                msg="Circuit code only support file, dirs or str",
                key="circuit_code",
                value=circuit_code,
            )

        # collect unit
        qubit_set = set()
        coupler_set = set()
        qubit_pair_set = set()
        measure_bit_set = None
        cir_map_list = []
        for cc in circuit_code_strings:
            circuit_map, qs, cs, qps, mqs = circuit_split(cc)
            qubit_set = qubit_set.union(qs)
            coupler_set = coupler_set.union(cs)
            qubit_pair_set = qubit_pair_set.union(qps)
            if measure_bit_set is None:
                measure_bit_set = mqs
            else:
                assert measure_bit_set == mqs
            cir_map_list.append(circuit_map)

        # creat experiment
        qubit_set_list = sorted(list(qubit_set), key=cmp_to_key(sort_bit))
        qubits, couplers, qubit_pairs = [], [], []

        for qubit in self.context.qubits:
            if qubit.name in qubit_set_list:
                qubits.append(qubit)

        for coupler in self.context.couplers:
            if coupler.name in coupler_set:
                couplers.append(coupler)

        for pair in self.context.qubit_pair:
            if pair.name in qubit_pair_set:
                qubit_pairs.append(pair)

        compensates = {}
        unit_map = {}
        cz_gate_map = {}

        for qubit in qubits:
            unit_map[qubit.name] = qubit
            compensates[qubit] = self.context.compensates.get(qubit)
        for coupler in couplers:
            unit_map[coupler.name] = coupler
            compensates[coupler] = self.context.compensates.get(coupler)

        def _freq_to_amp(pair, qubit):
            freq = pair.cz_value(qubit.name, "freq")
            z_amp = pair.cz_value(qubit.name, "amp")

            # freq to z amp
            if freq:
                if qubit.idle_point > 0:
                    branch = "right"
                elif qubit.idle_point < 0:
                    branch = "left"
                else:
                    branch = pair.cz_value(qubit.name, "branch")

                ac_spec = qubit.ac_spectrum.standard
                ac_spec[-2] = 0
                z_amp = freq2amp_formula(freq, *ac_spec, branch) - qubit.idle_point

                pyqlog.debug(
                    f"Update {pair.name} - {qubit.name}'s freq={freq}MHz to amp={z_amp}V"
                )

                pair.set_cz_value(qubit.name, "amp", z_amp)

        base_physical_units = []
        base_physical_units.extend(self.qubits)
        base_physical_units.extend(self.couplers)
        for pair in qubit_pairs:
            qh, ql = None, None
            for qubit in qubits:
                if qubit.name == pair.qh:
                    qh = qubit
                elif qubit.name == pair.ql:
                    ql = qubit

            _freq_to_amp(pair, qh)
            _freq_to_amp(pair, ql)

            unit_map[pair.name] = pair
            cp = CPhaseGate()
            cp.bind_gate(pair, base_physical_units)
            cz_gate_map[pair.name] = cp

        measure_bits = list(measure_bit_set)

        dcm = []
        for d in self.context.discriminators:
            if d.name in measure_bits:
                dcm.append(d)

        if len(dcm) == 1:
            dcm = dcm[0]

        measure_qubits = []
        for mb in measure_bits:
            for qubit in qubits:
                if qubit.name == mb:
                    measure_qubits.append(qubit)
                    break

        child_exp = _Execute(
            inst=deepcopy(self.context.inst),
            qubits=qubits,
            couplers=couplers,
            compensates=compensates,
            discriminators=dcm,
            ac_bias=self.context.ac_bias,
            config=self.context.config,
        )
        child_exp.set_experiment_options(**self.child_experiment.experiment_options)

        child_exp.run_options.config = self.context.config
        child_exp.run_options.crosstalk_dict = self.context.crosstalk_dict
        child_exp.run_options.online_bits = self.context.online_bits
        child_exp.run_options.online_dcms = self.context.online_dcms
        child_exp.run_options.online_coms = self.context.online_coms
        child_exp.run_options.measure_circuit_qubits = measure_qubits
        child_exp.run_options.env_bit_dict = self.context.env_bit_dict

        child_exp.run_options.qubit_pairs = qubit_pairs
        child_exp.run_options.circuit_map_list = cir_map_list
        child_exp.run_options.width.gate_1q = zero_pulse(qubits[0]).width
        child_exp.run_options.width.gate_2q = (
            qubit_pairs[0].width() if qubit_pairs else 0
        )
        child_exp.run_options.unit_map = unit_map
        child_exp.run_options.cz_gate_map = cz_gate_map

        measure_result = child_exp.run()
        self.set_run_options(er_prob_list=measure_result)
        self._experiments.append(child_exp)

        if txt_files:
            with open(
                str(Path(child_exp.file.dirs, "circuit_files.txt")),
                mode="w",
                encoding="utf-8",
            ) as fp:
                fp.write("\n\n".join(txt_files))
