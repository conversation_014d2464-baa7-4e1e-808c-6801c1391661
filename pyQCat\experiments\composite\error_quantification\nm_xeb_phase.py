# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/05/20
# __author:       WeiXu

import json
from copy import deepcopy
from typing import Union

import numpy as np

from ....analysis import AnalysisResult
from ....log import pyqlog
from ....structures import Options
from ....tools.calculator import qubit_pair_detune_prepare
from ...single import XEBPhase
from .nm_base import NMRBMultiple


class NMXEBPhaseOpt(NMRBMultiple):
    _sub_experiment_class = XEBPhase

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.best_phase = {}
        return options

    async def _execute_exp(self, parameters) -> Union[int, float]:
        qubit_pair = self.child_experiment.qubit_pair
        ql_name = qubit_pair.metadata.std.ql
        qh_name = qubit_pair.metadata.std.qh
        qc_name = qubit_pair.metadata.std.qc
        scan_name = self.experiment_options.scan_name
        best_phase = self.run_options.best_phase
        exp = deepcopy(self.child_experiment)
        if self.run_options.count == 0:
            gate_params = qubit_pair.gate_params("cz")
            json_str = json.dumps(gate_params, ensure_ascii=False, indent=4)
            self.file.save_text(json_str, name="gate_params", prefix=".json")

        for i, key in enumerate(self.run_options.opt_keys):
            if key == "ql_amp":
                exp.qubit_pair.set_cz_value(ql_name, "amp", parameters[i])
                exp.qubit_pair.set_cz_value(ql_name, "freq", None)
            elif key == "qh_amp":
                exp.qubit_pair.set_cz_value(qh_name, "amp", parameters[i])
                exp.qubit_pair.set_cz_value(qh_name, "freq", None)
            elif key == "qc_amp":
                exp.qubit_pair.set_cz_value(qc_name, "amp", parameters[i])
                exp.qubit_pair.set_cz_value(qc_name, "freq", None)
            elif key == "qh_phase":
                exp.qubit_pair.set_cz_value(qh_name, "phase", parameters[i])
            elif key == "ql_phase":
                exp.qubit_pair.set_cz_value(ql_name, "phase", parameters[i])
            elif key == "qh_freq":
                exp.qubit_pair.set_cz_value(qh_name, "freq", parameters[i])
            elif key == "ql_freq":
                exp.qubit_pair.set_cz_value(ql_name, "freq", parameters[i])
            elif key == "detune1":
                exp.qubit_pair.set_cz_value(scan_name, "detune1", parameters[i])
            elif key == "detune2":
                exp.qubit_pair.set_cz_value(scan_name, "detune2", parameters[i])

            elif key == "detune":
                freq_map = qubit_pair_detune_prepare(
                    self.qubit_pair, self.qubits, label="cz", goal_detune=parameters[i]
                )
                msg = f"detune({parameters[i]})"
                for unit, freq in freq_map.items():
                    msg += f" {unit}({freq[0]})"
                    exp.qubit_pair.set_cz_value(unit, "freq", freq[0])
                pyqlog.info(f"NM detune: {msg}")

        await self._run_exp(exp)
        phase_ql = exp.analysis.results.phase_ql.value
        phase_qh = exp.analysis.results.phase_qh.value
        res = -float(np.mean(exp.analysis.analysis_datas.F_xeb.y))
        best_phase.update({res: [phase_ql, phase_qh]})
        self.set_run_options(best_phase=best_phase)
        return res

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        try:
            gate_params = self.qubit_pair.metadata.std.cz["params"]
            json_str = json.dumps(gate_params, ensure_ascii=False, indent=4)
            self.file.save_text(json_str, name="gate_params", prefix=".json")
        except Exception as e:
            pyqlog.warning(f"{e} error occrued when saving gate_params")

        sim = self.analysis.results.sim
        ql = self.qubit_pair.ql
        qh = self.qubit_pair.qh
        qc = self.qubit_pair.qc
        best_phase = self.run_options.best_phase
        min_res_phase = min(best_phase.items(), key=lambda x: x[0])[1]
        scan_name = self.experiment_options.scan_name
        for i, key in enumerate(self.run_options.opt_keys):
            if key == "ql_amp":
                self.analysis.results["ql_amp"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{ql}.amp",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "qh_amp":
                self.analysis.results["qh_amp"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{qh}.amp",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "qc_amp":
                self.analysis.results["qc_amp"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{qc}.amp",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "qh_phase":
                if len(min_res_phase) == 2:
                    v = min_res_phase[1]
                else:
                    v = sim.value[i]
                self.analysis.results["qh_phase"] = AnalysisResult(
                    name=key,
                    value=v,
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{qh}.phase",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "ql_phase":
                if len(min_res_phase) == 2:
                    v = min_res_phase[0]
                else:
                    v = sim.value[i]
                self.analysis.results["ql_phase"] = AnalysisResult(
                    name=key,
                    value=v,
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{ql}.phase",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "qh_freq":
                self.analysis.results["qh_freq"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{qh}.freq",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "ql_freq":
                self.analysis.results["ql_freq"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{ql}.freq",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "detune1":
                self.analysis.results["detune1"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{scan_name}.detune1",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "detune2":
                self.analysis.results["detune2"] = AnalysisResult(
                    name=key,
                    value=sim.value[i],
                    extra={
                        "path": f"QubitPair.metadata.std.cz.params.{scan_name}.detune2",
                        "name": self.qubit_pair.name,
                    },
                )
            elif key == "detune":
                freq_map = qubit_pair_detune_prepare(
                    self.qubit_pair, self.qubits, goal_detune=sim.value[i]
                )
                for unit, freq in freq_map.items():
                    self.analysis.results[unit] = AnalysisResult(
                        name=unit,
                        value=freq[0],
                        extra={
                            "path": f"QubitPair.metadata.std.cz.params.{unit}.freq",
                            "name": self.qubit_pair.name,
                        },
                    )
