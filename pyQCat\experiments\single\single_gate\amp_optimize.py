# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2020/11/28
# __author:       <PERSON>
# __corporation:  OriginQuantum

"""
AmpOptimize experiment.

Amplitude Optimize Experiment is used to calibrate the
amplitude of the single gate XY line waveform.
"""

from typing import List

import numpy as np

from ....analysis.library import AmpOptAnalysis
from ....analysis.specification import ParameterRepr
from ....errors import ExperimentOptionsError
from ....log import pyqlog
from ....pulse.pulse_function import (
    f12_pi_pulse,
    half_f12_pi_pulse,
    half_pi_pulse,
    pi_pulse,
)
from ....structures import MetaData, Options
from ...coupler_experiment_v1 import CouplerBaseExperimentV1 as CouplerBaseExperiment
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment


class AmpOptimize(TopExperiment):
    """AmpOptimize experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            amp_list (List, np.ndarray): Scan amp list.
            amp_init (float): Scan amp initial value, default none.
            threshold (Tuple): Set scan amp range.
            points (int): Set scan points number.
            theta_type (str): Support `θ` type, normal "Xpi" or "Xpi/2".
            N (int): Number of gates, π gate or π/2 gate.
        """
        options = super()._default_experiment_options()

        options.set_validator("theta_type", ["Xpi", "Xpi/2"])
        options.set_validator("amp_init", (-1, 1, 2))
        options.set_validator("amp_list", list, limit_null=True)
        options.set_validator("points", int)
        options.set_validator("N", int)
        options.set_validator("threshold_left", (0.5, 1, 2))
        options.set_validator("threshold_right", (1, 1.5, 2))
        options.set_validator("f12_opt", bool)

        options.amp_list = None
        options.amp_init = None
        options.threshold_left = 0.9
        options.threshold_right = 1.1
        options.points = 61
        options.theta_type = "Xpi"
        options.N = 7
        options.f12_opt = False

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options.

        Analysis Options:
            quality_bounds (Iterable[float]): The bounds value of the
                                              goodness of fit.
            is_plot (bool): Plot or not analysis fit photo.
            data_key (List[str]): List of mark select data.
        """
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)

        options.data_key = None
        options.result_parameters = None
        options.quality_bounds = [0.98, 0.95, 0.85]

        return options

    def _check_options(self):
        """
        When `amp_list` and `amp_init` is both none, get xpi value from qubit and assign
        it to `amp_init`, according to `theta_type` adjusts the output results and selects
        the analyzed data. When processing amplitude and phase data, both are processed;
        At the same time, we did some exception handling:
            ① when `theta_type` is not xpi and xpi/2;
            ② When n is 0;
            ③ When `theta_type` is xpi/2 and N is odd;
        """
        super()._check_options()
        amp_list = self.experiment_options.amp_list
        amp_init = self.experiment_options.amp_init
        theta_type = self.experiment_options.theta_type
        lb = self.experiment_options.threshold_left
        ub = self.experiment_options.threshold_right
        points = self.experiment_options.points
        N = self.experiment_options.N
        f12_opt = self.experiment_options.f12_opt

        # validate experiment options
        err_msg = None

        if N == 0:
            err_msg = "Gate repeat time is 0."

        if theta_type == "Xpi/2" and N % 2 != 0:
            err_msg = (
                f"When optimizing Xpi/2, N must be an even number, but input is {N}."
            )

        if err_msg:
            raise ExperimentOptionsError(self.label, err_msg)

        # validate amp list
        if amp_list is None:
            if amp_init is None:
                if f12_opt:
                    amp_init = (
                        self.qubit.f12_options.Xpi
                        if theta_type == "Xpi"
                        else self.qubit.f12_options.Xpi2
                    )
                elif self.qubit:
                    amp_init = (
                        self.qubit.XYwave.Xpi
                        if theta_type == "Xpi"
                        else self.qubit.XYwave.Xpi2
                    )
                else:
                    amp_init = (
                        self.coupler.drive_XYwave.Xpi
                        if theta_type == "Xpi"
                        else self.coupler.drive_XYwave.Xpi2
                    )

            amp_list = list(np.linspace(lb * amp_init, ub * amp_init, points))

        if f12_opt:
            path_pre = "Qubit.f12_options"
        elif self.is_coupler_exp is True:
            path_pre = "Coupler.drive_XYwave"
        elif self.coupler and self.is_coupler_exp is False:
            path_pre = "Coupler.probe_XYwave"
        else:
            path_pre = "Qubit.XYwave"

        # validate data key and result parameters
        if theta_type == "Xpi":
            result_parameters = [
                ParameterRepr(
                    name="Xpi", repr="X-amp", unit="V", param_path=f"{path_pre}.Xpi"
                ),
                ParameterRepr(name="points", repr="points", unit=None),
                ParameterRepr(name="freq", repr="freq", unit=None),
            ]
            if self.qubit:
                data_key = ["P0"] if N % 2 == 0 else ["P1"]
            else:
                data_key = ["P1"] if N % 2 == 0 else ["P0"]
        else:
            result_parameters = [
                ParameterRepr(
                    name="Xpi2", repr="X2-amp", unit="V", param_path=f"{path_pre}.Xpi2"
                ),
                ParameterRepr(name="points", repr="points", unit=None),
                ParameterRepr(name="freq", repr="freq", unit=None),
            ]
            if self.qubit:
                data_key = ["P0"] if N % 4 == 0 else ["P1"]
            else:
                data_key = ["P1"] if N % 4 == 0 else ["P0"]

        # update data keys
        if self.discriminator is None:
            data_key = ["Amp", "Phase"]
            data_type = "amp_phase"
        else:
            data_type = "I_Q"

        pyqlog.log(
            "EXP",
            f"theta_type: {self._experiment_options.theta_type}, "
            f"N: {self._experiment_options.N}",
        )

        # update experiment options
        self.set_experiment_options(
            amp_list=amp_list, amp_init=amp_init, data_type=data_type
        )

        # update analysis options
        self.set_analysis_options(
            result_parameters=result_parameters, data_key=data_key
        )

        # Shq 2024/04/10
        # for async mode.
        self.set_run_options(x_data=amp_list, analysis_class=AmpOptAnalysis)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "theta_type": self.experiment_options.theta_type,
            "N": self.experiment_options.N,
        }
        return metadata

    @staticmethod
    def get_xy_pulse(
        qubit, amp_list: List, theta: float, N: int, f12_opt: bool
    ) -> List:
        pulse_list = []
        for amp in amp_list:
            if theta == np.pi:
                opt_pulse = f12_pi_pulse(qubit) if f12_opt else pi_pulse(qubit)
            else:
                opt_pulse = (
                    half_f12_pi_pulse(qubit) if f12_opt else half_pi_pulse(qubit)
                )

            exp_pulse = opt_pulse(amp=amp) * N

            if f12_opt:
                exp_pulse = pi_pulse(qubit)() + exp_pulse + pi_pulse(qubit)()

            pulse_list.append(exp_pulse)

        return pulse_list

    @staticmethod
    def set_xy_pulses(builder):
        theta_type = builder.experiment_options.theta_type
        amp_list = builder.experiment_options.amp_list
        N = builder.experiment_options.N
        f12_opt = builder.experiment_options.f12_opt

        if theta_type == "Xpi":
            theta = np.pi
        elif theta_type == "Xpi/2":
            theta = np.pi / 2
        else:
            raise ExperimentOptionsError(f"theta_type {theta_type} not supported")

        xy_pulse_list = AmpOptimize.get_xy_pulse(
            builder.qubit, amp_list, theta, N, f12_opt
        )
        builder.play_pulse("XY", builder.qubit, xy_pulse_list)


class CouplerAmpOptimize(CouplerBaseExperiment, AmpOptimize):
    """Coupler Amp Optimize Experiment"""

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulses."""
        theta_type = self.experiment_options.theta_type
        amp_list = self.experiment_options.amp_list
        N = self.experiment_options.N

        if theta_type == "Xpi":
            theta = np.pi
        elif theta_type == "Xpi/2":
            theta = np.pi / 2
        else:
            raise ExperimentOptionsError(f"theta_type {theta_type} not supported")

        xy_pulse_list = AmpOptimize.get_xy_pulse(self.driveQ, amp_list, theta, N, False)
        self.compose_xy_pulses(xy_pulse_list)
