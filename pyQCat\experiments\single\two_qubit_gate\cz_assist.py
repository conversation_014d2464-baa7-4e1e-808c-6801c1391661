# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/27
# __author:       <PERSON>, <PERSON><PERSON><PERSON>

from copy import deepcopy

import numpy as np

from ....analysis import AnalysisResult, CZAssistAnalysis, CZPhaseAnalysis
from ....analysis.library.conditional_phase_analysis import CZPhaseAnalysisV1
from ....errors import ExperimentOptionsError
from ....gate import Rphi_gate
from ....log import pyqlog
from ....parameters import freq_list_to_amp, get_physical_bit, options_wrapper
from ....pulse.pulse_function import (
    half_pi_pulse,
    pi_pulse,
    stimulate_state_pulse,
    zero_pulse,
)
from ....pulse.pulse_lib import Constant
from ....pulse_adjust import params_to_pulse
from ....structures import Meta<PERSON><PERSON>, Options, QDict
from ....tools import cz_flow_options_adapter
from ....tools.calculator import qubit_pair_detune_prepare
from ...top_experiment_v1 import StandardContext
from ...top_experiment_v1 import TopExperimentV1 as TopExperiment
from .swap_once import (
    validate_qubit_pair_cz_std,
    validate_two_qubit_exp_read_options,
)


class CZAssist(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("cz_num", int)
        options.set_validator("add_cz", bool)
        options.set_validator("control_gate", ["I", "X"])
        options.set_validator("phase_list", list)
        options.set_validator("ramsey_bit", str)

        options.cz_num = 1
        options.add_cz = True
        options.control_gate = "I"
        options.phase_list = np.linspace(0, 2 * np.pi, 15).tolist()
        options.ramsey_bit = None
        options.readout_type = None

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.support_context = [StandardContext.CGC]

        options.ql = None
        options.qh = None
        options.control_bit = None
        options.gate_params = {}
        options.buffer = None

        options.qc = None
        options.width = None
        options.env_bits = None
        options.control_bit = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)

        options.quality_bounds = [0.98, 0.95, 0.85]
        options.data_key = None
        options.figsize = (12, 12)
        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "control_gate": self.experiment_options.control_gate,
            "ramsey_bit": self.experiment_options.ramsey_bit,
            "add_cz": self.experiment_options.add_cz,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        cz_flow_options_adapter(self)
        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)

        # check ramsey bit and control bit
        ramsey_bit = self.experiment_options.ramsey_bit
        control_gate = self.experiment_options.control_gate
        readout_type = self.experiment_options.readout_type
        data_key = None
        control_bit = None

        if ramsey_bit is None:
            ramsey_bit = self.qubit_pair.ql
            control_bit = self.qubit_pair.qh
            data_key = ["P01"]
        elif ramsey_bit == self.qubit_pair.qh:
            control_bit = self.qubit_pair.ql
            data_key = ["P10"]
        elif ramsey_bit == self.qubit_pair.ql:
            control_bit = self.qubit_pair.qh
            data_key = ["P01"]

        if ramsey_bit not in [q.name for q in self.qubits] or not control_bit:
            raise ExperimentOptionsError(
                self.label,
                key="ramsey_bit",
                value=ramsey_bit,
                msg=f"ramsey bit {ramsey_bit} not in exp qubits!",
            )
        self.set_experiment_options(ramsey_bit=ramsey_bit)
        self.set_run_options(
            control_bit=control_bit,
            x_data=self.experiment_options.phase_list,
            analysis_class=CZAssistAnalysis,
        )

        if not readout_type.startswith("union"):
            data_key = ["P1"]
        elif control_gate == "X":
            data_key = ["P11"]

        self.set_analysis_options(data_key=data_key)

        pyqlog.debug(
            f"ramsey to {ramsey_bit}, control_gate to {control_bit}, control_gate = {control_gate}"
        )

    @staticmethod
    def set_xy_pulses(builder):
        """Set experiment XY pulses."""
        cz_num = builder.experiment_options.cz_num
        control_gate = builder.experiment_options.control_gate
        cz_width = builder.run_options.width
        phase_list = builder.experiment_options.phase_list
        ramsey_bit = builder.experiment_options.ramsey_bit
        control_bit = builder.run_options.control_bit
        open_half_pi = builder.experiment_options.open_half_pi

        for qubit in builder.qubits:
            if qubit.name.startswith("c"):
                continue

            xy_pulse_list = []
            for phase in phase_list:
                pulse_2 = Constant(cz_width, 0, name="XY")
                if qubit.name == ramsey_bit:
                    pulse_1 = half_pi_pulse(qubit)
                    pulse_1t = Constant(pulse_1.width, 0, "XY") if open_half_pi is True else Constant(0, 0, "XY")
                    pulse_3 = half_pi_pulse(qubit)
                    pulse_3.phase = phase
                elif qubit.name == control_bit:
                    pulse_1 = stimulate_state_pulse(control_gate, qubit)
                    pulse_1t = Constant(0, 0, "XY")
                    pulse_3 = stimulate_state_pulse("I", qubit)
                else:
                    pulse_1 = stimulate_state_pulse("I", qubit)
                    pulse_1t = Constant(0, 0, "XY")
                    pulse_3 = stimulate_state_pulse("I", qubit)

                xy_pulse = pulse_1() + deepcopy(pulse_1t)() + pulse_2() * cz_num + deepcopy(pulse_1t)() + pulse_3()
                xy_pulse_list.append(xy_pulse)

            builder.play_pulse("XY", qubit, xy_pulse_list)

    @staticmethod
    def set_z_pulses(builder):
        cz_num = builder.experiment_options.cz_num
        cz_width = builder.run_options.width
        phase_list = builder.experiment_options.phase_list
        add_cz = builder.experiment_options.add_cz

        ql = builder.run_options.ql
        qh = builder.run_options.qh
        parking_qubits = builder.run_options.env_bits
        gate_params = builder.run_options.gate_params

        length = len(phase_list)
        qubit_list = [ql, qh]
        qubit_list.extend(parking_qubits)
        drag_time = pi_pulse(qh)().width

        for qubit in qubit_list:
            if qubit.name.startswith("q"):
                pulse_1 = zero_pulse(qubit, name="Z")
                pulse_3 = zero_pulse(qubit, name="Z")
            else:
                pulse_1 = Constant(drag_time, 0)
                pulse_3 = Constant(drag_time, 0)

            s_gate_params = deepcopy(gate_params.get(qubit.name))
            s_gate_params.update({"width": cz_width})
            if add_cz is False:
                s_gate_params.update({"amp": 0})

            pulse_2 = params_to_pulse(**s_gate_params)

            z_pulse = pulse_1() + pulse_2() * cz_num + pulse_3()
            builder.play_pulse("Z", qubit, [deepcopy(z_pulse) for _ in range(length)])


@options_wrapper
class CPhaseTMSE(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("mode", ["TM", "SE-TM"])
        options.set_validator("ramsey_bit", str)
        options.set_validator("phase_mode", ["control", "single"])
        options.set_validator("scan_name", str)
        options.set_validator("adapter_name", str)
        options.set_validator("z_amp_list", list)
        options.set_validator("adapter_amp_list", list)
        options.set_validator("k", int)
        options.set_validator("cz_num", int)
        options.set_validator("leakage_mode", ["fit", "max"])
        options.set_validator("detune_list", list)
        options.set_validator("analysis_mode", ["default", "detune_phase"])

        options.mode = "TM"
        options.phase_mode = "control"
        options.ramsey_bit = None
        options.scan_name = ""
        options.adapter_name = "qc"
        options.z_amp_list = None
        options.adapter_amp_list = None
        options.k = 1
        options.cz_num = 1
        options.readout_type = None
        options.leakage_mode = "fit"
        options.scope_detune = False
        options.detune_list = None
        options.analysis_mode = "default"

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.data_key = ["delta_phase"]
        options.adapter_amp_list = None
        options.adapter_freq_list = None
        options.point = 21
        options.quality_bounds = [0.99, 0.9, 0.8]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.ramsey_qubit = None
        options.drag_qubit = None
        options.data_acq = None

        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []

        options.use_detune = False
        options.detune_point = False
        options.support_context = [StandardContext.CGC]
        options.injection_func = [
            "_set_tm_xy_pulse",
            "_set_se_tm_xy_pulse",
            "_set_tm_z_pulse",
            "_set_se_tm_z_pulse",
        ]
        options.need_check_options = True
        options.actual_x_data = None

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "mode": self.experiment_options.mode,
            "phase_mode": self.experiment_options.phase_mode,
            "k": self.experiment_options.k,
            "cz_num": self.experiment_options.cz_num,
        }
        metadata.process_meta = {
            "scan_name": self.experiment_options.scan_name,
            "actual_x_data": self.run_options.actual_x_data,
            "scan_map": self.run_options.scan_map,
            "adapter_options": QDict(
                k=self.experiment_options.k,
                mode=self.experiment_options.mode,
                data_acq=self.run_options.data_acq,
            ),
        }
        return metadata

    def _check_options(self):
        eop = self.experiment_options
        rop = self.run_options

        # base context check: check experiment context, once call
        if self.run_options.qh is None:
            super()._check_options()
            cz_flow_options_adapter(self)
            validate_qubit_pair_cz_std(self)
            validate_two_qubit_exp_read_options(self)

            if eop.ramsey_bit == rop.qh.name:
                ramsey_qubit = rop.qh
                drag_qubit = rop.ql
            elif eop.ramsey_bit == rop.ql.name:
                ramsey_qubit = rop.ql
                drag_qubit = rop.qh
            else:
                raise ExperimentOptionsError(
                    self.label,
                    key="ramsey_bit",
                    value=eop.ramsey_bit,
                    msg=f"No find ramsey bit {eop.ramsey_bit}",
                )

            data_acq = None
            if self.experiment_options.readout_type.startswith("union"):
                if ramsey_qubit == rop.qh:
                    data_acq = [["P00", "P01"], ["P10", "P11"]]
                elif ramsey_qubit == rop.ql:
                    data_acq = [["P00", "P10"], ["P01", "P11"]]

            self.set_run_options(
                ramsey_qubit=ramsey_qubit,
                drag_qubit=drag_qubit,
                data_acq=data_acq,
                analysis_class=CZPhaseAnalysis,
            )
            self.set_analysis_options(result_name=self.qubit_pair.name)

        # function check
        if eop.phase_mode == "control" and self.run_options.need_check_options is True:
            if eop.detune_list:
                if not eop.detune_freq_list:
                    self.run_options.x_data = eop.detune_list
                self.run_options.scan_map.update(
                    {eop.scan_name: {"detune2": eop.detune_list}}
                )
                self.set_run_options(analysis_class=CZPhaseAnalysisV1)
            elif eop.detune_freq_list:
                self.run_options.x_data = eop.detune_freq_list
                self.set_run_options(analysis_class=CZPhaseAnalysisV1)
            else:
                # check leakage points
                if self.qubit_pair.metadata.std.process.min_leakage_point.fit.detune:
                    eop.leakage_mode = "fit"
                elif self.qubit_pair.metadata.std.process.min_leakage_point.max.detune:
                    eop.leakage_mode = "max"
                leakage_point = (
                    self.qubit_pair.metadata.std.process.min_leakage_point.get(
                        eop.leakage_mode
                    )
                )
                if eop.scan_name and eop.scan_name not in self.run_options.scan_map:
                    self.run_options.scan_map[eop.scan_name] = {}
                if (
                    eop.adapter_name
                    and eop.adapter_name not in self.run_options.scan_map
                ):
                    self.run_options.scan_map[eop.adapter_name] = {}

                # auto load `scan parameters`
                if not eop.z_amp_list and not eop.freq_list:
                    # adapter leakage point
                    if leakage_point.detune:
                        # load detune list
                        self.run_options.x_data = leakage_point.detune
                        self.experiment_options.scan_name = ""
                        freq_map = qubit_pair_detune_prepare(
                            self.qubit_pair, self.qubits, goal_detune=leakage_point.detune
                        )
                        self.run_options.use_detune = True
                        self.run_options.detune_point = leakage_point.detune
                        for unit, freq_list in freq_map.items():
                            physical_bit, branch = get_physical_bit(self, unit)
                            amp_list = freq_list_to_amp(freq_list, physical_bit, branch)
                            self.run_options.scan_map.update(
                                {
                                    unit: {
                                        "freq": freq_list,
                                        "amp": amp_list,
                                    }
                                }
                            )
                    else:
                        # load freq or amp list for scan name
                        al = (
                            leakage_point.qh
                            if eop.scan_name.startswith("q")
                            else leakage_point.qc
                        )
                        if al:
                            self.run_options.x_data = al
                            if al[0] > 1:
                                eop.freq_list = al
                                params = {eop.scan_name: {"freq": al}}
                            else:
                                eop.z_amp_list = al
                                params = {eop.scan_name: {"amp": al}}
                            self.run_options.scan_map.update(params)
                elif eop.freq_list:
                    self.run_options.x_data = eop.freq_list
                else:
                    self.run_options.x_data = eop.z_amp_list
                    self.run_options.scan_map[eop.scan_name].update(
                        {"amp": eop.z_amp_list}
                    )

                if not eop.adapter_amp_list and not eop.adapter_freq_list:
                    # load freq or amp list for adapter name
                    bl = (
                        leakage_point.qh
                        if eop.adapter_name.startswith("q")
                        else leakage_point.qc
                    )
                    if bl:
                        if bl[0] > 1:
                            eop.adapter_freq_list = bl
                            params = {eop.adapter_name: {"freq": bl}}
                        else:
                            eop.adapter_amp_list = bl
                            params = {eop.adapter_name: {"amp": bl}}
                        self.run_options.scan_map.update(params)
                elif eop.adapter_amp_list:
                    self.run_options.scan_map[eop.adapter_name].update(
                        {"amp": eop.adapter_amp_list}
                    )

        if self.run_options.need_check_options is True:
            if eop.adapter_amp_list:
                self.analysis_options.adapter_amp_list = eop.adapter_amp_list
            if eop.adapter_freq_list:
                self.analysis_options.adapter_freq_list = eop.adapter_freq_list

            self.run_options.need_check_options = False
            for params in self.run_options.scan_map.values():
                if "amp" not in params:
                    self.run_options.need_check_options = True

            if eop.detune_freq_list:
                self.run_options.need_check_options = True

            if eop.detune_list:
                self.run_options.need_check_options = False

            if self.run_options.need_check_options is False:
                self._set_analysis_params()

    @staticmethod
    def set_xy_pulses(self):
        if self.experiment_options.mode == "TM":
            pulse_map = self._set_tm_xy_pulse()
        else:
            pulse_map = self._set_se_tm_xy_pulse()

        for unit, pulses in pulse_map.items():
            self.play_pulse("XY", unit, pulses)

    @staticmethod
    def set_z_pulses(self):
        if self.experiment_options.mode == "TM":
            pulse_map = self._set_tm_z_pulse()
        else:
            pulse_map = self._set_se_tm_z_pulse()

        for unit, pulses in pulse_map.items():
            self.play_pulse("Z", unit, pulses)

    def _set_tm_xy_pulse(self):
        pulse_map = {}
        eop = self.experiment_options
        rop = self.run_options
        zero_cz = Constant(rop.width, 0, name="XY")() * eop.cz_num
        
        open_half_pi = eop.open_half_pi
        delay = half_pi_pulse(self.qh).width if open_half_pi else 0
        compensate = Constant(delay, 0, "XY")()

        for qubit in self.qubits:
            if qubit.name.startswith("c"):
                continue

            if qubit == rop.ramsey_qubit:
                x2 = half_pi_pulse(qubit)()
                y2 = Rphi_gate(phase=np.pi / 2).to_pulse(qubit)()
                xy_pulse_x2 = deepcopy(x2) + compensate + zero_cz + x2 + compensate
                xy_pulse_y2 = deepcopy(x2) + compensate + zero_cz + y2 + compensate
                xy_pulse_list = [
                    deepcopy(xy_pulse_x2),
                    deepcopy(xy_pulse_y2),
                    deepcopy(xy_pulse_x2),
                    deepcopy(xy_pulse_y2),
                ]
            elif qubit == rop.drag_qubit:
                x = pi_pulse(qubit)()
                zero = zero_pulse(qubit)()
                xy_pulse1 = deepcopy(zero) + zero_cz + zero
                xy_pulse2 = x + zero_cz + zero
                xy_pulse_list = [
                    deepcopy(xy_pulse1),
                    deepcopy(xy_pulse1),
                    deepcopy(xy_pulse2),
                    deepcopy(xy_pulse2),
                ]
            else:
                zero = zero_pulse(qubit)()
                xy_pulse = deepcopy(zero) + zero_cz + zero
                xy_pulse_list = [deepcopy(xy_pulse) for _ in range(4)]

            xy_pulses = []
            if self.experiment_options.phase_mode == "control":
                for _ in rop.actual_x_data:
                    xy_pulses.extend(deepcopy(xy_pulse_list))
            else:
                if qubit == rop.drag_qubit:
                    x1 = xy_pulse_list[0]
                    xy_pulse_list = [deepcopy(x1) for _ in range(4)]
                xy_pulses = deepcopy(xy_pulse_list)

            pulse_set = []
            for _ in range(eop.k):
                pulse_set.extend(deepcopy(xy_pulses))

            pulse_map[qubit] = pulse_set

        return pulse_map

    def _set_tm_z_pulse(self):
        pulse_map = {}
        eop = self.experiment_options
        rop = self.run_options

        zero_cz = Constant(rop.width, 0)() * eop.cz_num
        zero_x = zero_pulse(rop.ramsey_qubit, name="Z")()

        for qubit in rop.env_bis:
            gate_param = rop.gate_params.get(qubit.name)
            if eop.phase_mode == "single":
                s_ps = gate_param
                s_ps["time"] = rop.width
                cz_pulse = params_to_pulse(**s_ps)() * eop.cz_num
                z_pulse1 = deepcopy(zero_x) + zero_cz + zero_x
                z_pulse2 = deepcopy(zero_x) + cz_pulse + zero_x
                z_pulses = [
                    deepcopy(z_pulse1),
                    deepcopy(z_pulse1),
                    deepcopy(z_pulse2),
                    deepcopy(z_pulse2),
                ]
            else:
                z_pulses = []
                for i in range(len(self.run_options.actual_x_data)):
                    if qubit.name in rop.scan_map:
                        if eop.detune_list:
                            detune2 = rop.scan_map.get(qubit.name).get("detune2")[i]
                            gate_param["detune2"] = detune2
                        else:
                            amp = rop.scan_map.get(qubit.name).get("amp")[i]
                            gate_param["amp"] = amp
                            gate_param["freq"] = 0
                    cz_pulse = params_to_pulse(**gate_param)() * eop.cz_num
                    z_pulse = deepcopy(zero_x) + cz_pulse + zero_x
                    for _ in range(4):
                        z_pulses.append(deepcopy(z_pulse))

            pulse_set = []
            for _ in range(eop.k):
                pulse_set.extend(deepcopy(z_pulses))

            pulse_map[qubit] = pulse_set

        return pulse_map

    def _set_se_tm_xy_pulse(self):
        pulse_map = {}
        eop = self.experiment_options
        rop = self.run_options

        zero_cz = Constant(rop.width, 0, name="XY")() * eop.cz_num

        open_half_pi = eop.open_half_pi
        delay = half_pi_pulse(self.qh).width if open_half_pi else 0
        compensate = Constant(delay, 0, "XY")()

        n = (
            len(rop.actual_x_data)
            if self.experiment_options.phase_mode == "control"
            else 1
        )

        for qubit in self.qubits:
            if qubit.name.startswith("c"):
                continue

            if qubit == rop.ramsey_qubit:
                x2 = half_pi_pulse(qubit)()
                x = pi_pulse(qubit)()
                y2 = Rphi_gate(phase=np.pi / 2).to_pulse(qubit)()
                xy_pulse1 = deepcopy(x2) + compensate + zero_cz + x + zero_cz + x2 + compensate
                xy_pulse2 = deepcopy(x2) + compensate + zero_cz + x + zero_cz + y2 + compensate
                xy_pulse_once = [xy_pulse1, xy_pulse2]
            elif qubit == rop.drag_qubit:
                x = pi_pulse(qubit)()
                zero = zero_pulse(qubit)()
                if eop.phase_mode == "control":
                    xy_pulse = deepcopy(zero) + zero_cz + x + zero_cz + zero
                else:
                    xy_pulse = deepcopy(zero) + zero_cz + zero + zero_cz + zero
                xy_pulse_once = [deepcopy(xy_pulse), deepcopy(xy_pulse)]
            else:
                zero = zero_pulse(qubit)()
                xy_pulse = deepcopy(zero) + zero_cz + zero + zero_cz + zero
                xy_pulse_once = [deepcopy(xy_pulse), deepcopy(xy_pulse)]

            xy_pulses = []
            for _ in range(n):
                xy_pulses.extend(deepcopy(xy_pulse_once))

            pulse_set = []
            for _ in range(eop.k):
                pulse_set.extend(deepcopy(xy_pulses))

            pulse_map[qubit] = pulse_set
        return pulse_map

    def _set_se_tm_z_pulse(self):
        pulse_map = {}
        eop = self.experiment_options
        rop = self.run_options

        zero_cz = Constant(rop.width, 0)() * eop.cz_num
        k = self.experiment_options.k
        bit_list = []
        bit_list.extend(self.qubits)
        bit_list.extend(self.couplers)
        zero_x = zero_pulse(self.run_options.ramsey_qubit, name="Z")()

        for qubit in bit_list:
            gate_param = rop.gate_params.get(qubit.name)
            gate_param["time"] = rop.width
            if eop.phase_mode == "single":
                cz_pulse = params_to_pulse(**gate_param)() * eop.cz_num
                z_pulse = deepcopy(zero_x) + zero_cz + zero_x + cz_pulse + zero_x
                z_pulses = [deepcopy(z_pulse), deepcopy(z_pulse)]
            else:
                z_pulses = []
                for i in range(len(self.run_options.actual_x_data)):
                    if qubit.name in rop.scan_map:
                        if eop.detune_list:
                            detune2 = rop.scan_map.get(qubit.name).get("detune2")[i]
                            gate_param["detune2"] = detune2
                        else:
                            amp = rop.scan_map.get(qubit.name).get("amp")[i]
                            gate_param["amp"] = amp
                            gate_param["freq"] = 0
                    cz_pulse = params_to_pulse(**gate_param)() * eop.cz_num
                    z_pulse = deepcopy(zero_x) + cz_pulse + zero_x + cz_pulse + zero_x
                    z_pulses.extend([deepcopy(z_pulse), deepcopy(z_pulse)])

            pulse_set = []
            for _ in range(k):
                pulse_set.extend(deepcopy(z_pulses))

            pulse_map[qubit] = pulse_set
        return pulse_map

    def _set_analysis_params(self):
        if not self.run_options.actual_x_data:
            x_data = self.run_options.x_data
            new_x_data = []
            mode = self.experiment_options.mode
            phase_mode = self.experiment_options.phase_mode
            k = self.experiment_options.k

            if phase_mode == "control":
                if mode == "TM":
                    for x in x_data:
                        new_x_data.extend([x for _ in range(4)])
                else:
                    for x in x_data:
                        new_x_data.extend([x for _ in range(2)])
            else:
                x_data = [1]
                if mode == "TM":
                    new_x_data = [1, 1, 2, 2]
                else:
                    new_x_data = [1, 1]

            if k > 1:
                cur_x_data = new_x_data
                new_x_data = []
                for _ in range(k):
                    new_x_data.extend(deepcopy(cur_x_data))

            self.set_run_options(actual_x_data=x_data, x_data=new_x_data)

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        y_data = self.experiment_data.y_data
        self._save_data(y_data, "Amp-Phase", False, "%.6f")

        all_phase_data = self.experiment_data.metadata.process_meta.get(
            "all_phase_data"
        )
        if all_phase_data is not None:
            name_info = f"cz_num={self.experiment_options.cz_num}_cz_phase"
            suffix = "cz_phase"
            self.file.save_data(
                all_phase_data, name=name_info, fmt="%.6f", suffix=suffix
            )

        eop = self.experiment_options
        if eop.phase_mode == "control":
            for key in list(self.analysis.results.keys()):
                result = self.analysis.results.get(key)
                if result.value:
                    if key == "ac_scan":
                        if self.run_options.use_detune:
                            goal_detune = result.value
                            scope = {
                                "l": abs(self.run_options.detune_point[0]),
                                "r": abs(self.run_options.detune_point[-1]),
                                "p": 30,
                            }
                            freq_map = qubit_pair_detune_prepare(
                                self.qubit_pair, self.qubits, goal_detune=goal_detune, **scope
                            )
                            for unit, params in freq_map.items():
                                self.analysis.results[unit] = AnalysisResult(
                                    name=unit,
                                    value=round(params[0], 3),
                                    extra={
                                        "name": self.qubit_pair.name,
                                        "path": f"QubitPair.metadata.std.cz.params.{unit}.freq",
                                    },
                                )
                        else:
                            des = "amp"
                            if result.value > 1:
                                result.value = round(result.value, 3)
                                des = "freq"
                            result.extra["path"] = (
                                f"QubitPair.metadata.std.cz.params.{eop.scan_name}.{des}"
                            )
                    elif key == "ac_adapter":
                        des = "amp"
                        if result.value > 1:
                            result.value = round(result.value, 3)
                            des = "freq"
                        result.extra["path"] = (
                            f"QubitPair.metadata.std.cz.params.{eop.adapter_name}.{des}"
                        )
        else:
            for key, result in self.analysis.results.items():
                if key == "phase":
                    result.extra["path"] = (
                        f"QubitPair.metadata.std.cz.params.{eop.ramsey_bit}.phase"
                    )
