# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/11/10
# __author:       <PERSON><PERSON><PERSON>


from copy import deepcopy
from typing import List

from ....analysis import ParameterRepr, RabiAmpF12Analysis, RabiWidthAnalysis
from ....log import pyqlog
from ....pulse import PulseComponent
from ....pulse.pulse_function import f12_pi_pulse, half_f12_pi_pulse, pi_pulse
from ....qubit import Qubit
from ....structures import MetaData, Options
from ....tools import qarange
from .rabi import RabiScanAmp, TopExperiment


class RabiScanWidthF12(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            widths (list, np.ndarray): The list of XY pulse widths that
                                       will be scanned in the experiment.
        """
        options = super()._default_experiment_options()

        options.set_validator("widths", list, limit_null=True)
        options.set_validator("f12_xpi", (-1, 1, 3))
        options.set_validator("f12_freq", float)

        options.f12_freq = None
        options.f12_xpi = None
        options.widths = qarange(100, 300, 5)

        return options

    @staticmethod
    def set_xy_pulses(self):
        """Set RabiScanWidth experiment XY pulses."""
        pulse_list = RabiScanWidthF12.get_xy_pulse(
            self.qubit, self.experiment_options.widths
        )
        self.play_pulse("XY", self.qubit, pulse_list)

    @staticmethod
    def update_instrument(self):
        self.sweep_readout_trigger_delay(
            self.qubit.readout_channel, self._pulse_time_list
        )

    @staticmethod
    def get_xy_pulse(qubit: Qubit, widths: List) -> List[PulseComponent]:
        options = qubit.f12_options
        f01_pulse = pi_pulse(qubit)()

        qubit = deepcopy(qubit)
        f12_pulse = f12_pi_pulse(qubit)

        pyqlog.log(
            "EXP",
            f" f01={qubit.drive_freq} |"
            f" IF01={f01_pulse.freq * 1e3} |"
            f" f12={options.drive_freq} |"
            f" IF12={f12_pulse.freq * 1e3}",
        )

        xy_pulses = []
        for param in widths:
            new_f12_pulse = deepcopy(f12_pulse)
            new_f12_pulse.amp = options.Xpi
            new_f12_pulse.time = param
            pulse = deepcopy(f01_pulse) + new_f12_pulse() + deepcopy(f01_pulse)
            xy_pulses.append(pulse)

        return xy_pulses

    def _check_options(self):
        if self.discriminator is not None:
            data_type = "I_Q"
            if "2" in self.discriminator.level_str:
                self.set_run_options(acquisition_key="weirdo")
                self.set_experiment_options(is_dynamic=0)
        else:
            data_type = "amp_phase"

        super()._check_options()

        f12_xpi = self.experiment_options.f12_xpi
        f12_freq = self.experiment_options.f12_freq

        if f12_xpi is not None:
            self.qubit.f12_options.Xpi = f12_xpi

        if f12_freq is not None:
            self.qubit.f12_options.drive_freq = f12_freq

        self.set_experiment_options(data_type=data_type)
        self.set_run_options(
            x_data=self.experiment_options.widths, analysis_class=RabiWidthAnalysis
        )

    def _metadata(self) -> MetaData:
        """Set RabiScanWidth experiment metadata."""
        metadata = super()._metadata()

        metadata.draw_meta = {
            "f01": (self.qubit.drive_freq, "MHz"),
            "f12": (self.qubit.f12_options.drive_freq, "MHz"),
            "f12_xpi": (self.qubit.f12_options.Xpi, "v"),
        }

        return metadata


class RabiScanAmpF12(RabiScanAmp):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            widths (list, np.ndarray): The list of XY pulse widths that
                                       will be scanned in the experiment.
        """
        options = super()._default_experiment_options()

        options.set_validator("f12_time", float)
        options.f12_time = None
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.f12_time = None
        return options

    @staticmethod
    def set_xy_pulses(builder):
        pulse_list = RabiScanAmpF12.get_xy_pulse(
            builder.qubit,
            builder.experiment_options.name,
            builder.experiment_options.amps,
        )
        builder.play_pulse("XY", builder.qubit, pulse_list)

    @staticmethod
    def get_xy_pulse(qubit: Qubit, type_: str, amps: List):
        f01_pulse = pi_pulse(qubit)()
        f12_pulse = f12_pi_pulse(qubit)(time=qubit.f12_options.time)
        half_f12_pulse = half_f12_pi_pulse(qubit)(time=qubit.f12_options.time)
        pyqlog.log(
            "EXP",
            f" f01={qubit.drive_freq} |"
            f" IF01={f01_pulse.freq * 1e3} |"
            f" f12={qubit.f12_options.drive_freq} |"
            f" IF12={f12_pulse.freq * 1e3}",
        )

        xy_pulses = []
        for amp in amps:
            if type_ == "Xpi":
                new_f12_pulse = deepcopy(f12_pulse)
                pulse = (
                    deepcopy(f01_pulse) + new_f12_pulse(amp=amp) + deepcopy(f01_pulse)
                )
            else:
                new_half_f12_pulse = deepcopy(half_f12_pulse)
                pulse = new_half_f12_pulse(amp=amp) * 2
                pulse = deepcopy(f01_pulse) + pulse + deepcopy(f01_pulse)

            xy_pulses.append(pulse)

        return xy_pulses

    def _check_options(self):
        """Check Options."""
        if self.discriminator is not None:
            data_type = "I_Q"
            if "2" in self.discriminator.level_str:
                self.set_run_options(acquisition_key="weirdo")
        else:
            data_type = "amp_phase"

        super(RabiScanAmp, self)._check_options()

        drive_power = self.experiment_options.drive_power
        name = self.experiment_options.name

        if drive_power is None:
            drive_power = self.qubit.drive_power

        path_pre = "Qubit.f12_options"
        result_parameters = [
            ParameterRepr(name="drive_power", repr="dp", unit="dB"),
            ParameterRepr(
                name="f12_width",
                repr="f12_width",
                unit="ns",
                param_path=f"{path_pre}.time",
            ),
        ]

        if name == "Xpi":
            result_parameters.extend(
                [
                    ParameterRepr(
                        name="Xpi",
                        repr="02-X-amp",
                        unit="V",
                        param_path=f"{path_pre}.Xpi",
                    ),
                    ParameterRepr(
                        name="Xpi2",
                        repr="02-X2-amp",
                        unit="V",
                        param_path=f"{path_pre}.Xpi2",
                    ),
                ]
            )
        else:
            result_parameters.append(
                ParameterRepr(
                    name="Xpi2",
                    repr="02-X2-amp",
                    unit="V",
                    param_path=f"{path_pre}.Xpi2",
                )
            )

        if self.experiment_options.f12_time:
            self.qubit.f12_options.time = self.experiment_options.f12_time

        self.set_experiment_options(drive_power=drive_power, data_type=data_type)
        self.set_analysis_options(
            result_parameters=result_parameters, f12_time=self.qubit.f12_options.time
        )
        self.set_run_options(
            x_data=self.experiment_options.amps, analysis_class=RabiAmpF12Analysis
        )

    def _metadata(self) -> MetaData:
        """Set RabiScanAmp experiment metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "f01": (self.qubit.drive_freq, "MHZ"),
            "f12": (self.qubit.f12_options.drive_freq, "MHZ"),
            "power": (
                self.experiment_options.drive_power or self.qubit.drive_power,
                "db",
            ),
            "f12_time": (self.qubit.f12_options.time, "ns"),
        }
        return metadata
