# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:2
msgid "pyQCat.analysis.library.T1SpectrumAnalysis"
msgstr ""

#: of pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis:1
msgid "Bases: :py:class:`~pyQCat.analysis.curve_analysis.CurveAnalysis`"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:1
msgid "Initialize the analysis object."
msgstr "初始化分析对象"

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:4
msgid "pyQCat Experiments Data container class."
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:7
msgid "Whether the experiment contains sub-experiments."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`__init__ "
"<pyQCat.analysis.library.T1SpectrumAnalysis.__init__>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`from_sub_analysis "
"<pyQCat.analysis.library.T1SpectrumAnalysis.from_sub_analysis>`\\ "
"\\(x\\_data\\, sub\\_analysis\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:25:<autosummary>:1
msgid "This interface is used for composite experiment."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`run_analysis "
"<pyQCat.analysis.library.T1SpectrumAnalysis.run_analysis>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:25:<autosummary>:1
#, fuzzy
msgid "Run analysis on experiment data."
msgstr "初始化曲线分析数据"

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`set_options "
"<pyQCat.analysis.library.T1SpectrumAnalysis.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:25:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`show_results "
"<pyQCat.analysis.library.T1SpectrumAnalysis.show_results>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:25:<autosummary>:1
msgid "Show analysis results"
msgstr ""

#: ../../source/stubs/analysis/library/pyQCat.analysis.library.T1SpectrumAnalysis.rst:27
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`analysis_datas "
"<pyQCat.analysis.library.T1SpectrumAnalysis.analysis_datas>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis datas."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`data_filter "
"<pyQCat.analysis.library.T1SpectrumAnalysis.data_filter>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "The parameters provided for data filter functions."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`drawer <pyQCat.analysis.library.T1SpectrumAnalysis.drawer>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "A short-cut for curve drawer instance."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`experiment_data "
"<pyQCat.analysis.library.T1SpectrumAnalysis.experiment_data>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "Return the experiment data for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ""
":py:obj:`has_child "
"<pyQCat.analysis.library.T1SpectrumAnalysis.has_child>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis has_child value."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.library.T1SpectrumAnalysis.options>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "Return the analysis options for :meth:`run` method."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.library.T1SpectrumAnalysis.quality>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "Get the analysis data quality."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid ":py:obj:`results <pyQCat.analysis.library.T1SpectrumAnalysis.results>`\\"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1:<autosummary>:1
msgid "Get the result datas."
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:1
msgid "Create the Analysis options, and set some fields."
msgstr "创建分析选型，并设置一些属性"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:3
msgid "**Analysis Options**:"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:5
msgid ""
"**freq_list (List, array)** - List of frequencies calculated from the AC "
"spectrum, Used to plot the relationship between qubit frequency and T1."
msgstr "**freq_list (List, array)** - 从 AC 频谱计算的频率列表，用于绘制量子比特频率和 T1 之间的关系。"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:8
msgid ""
"**r_square_threshold (float)** - T1 experiment goodness-of-fit threshold,"
" used to extract abnormal sub-experiments."
msgstr "r_square_threshold (float)** - T1 实验拟合优度阈值，用于提取异常子实验。"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:11
msgid ""
"**rate_threshold (float)** - The proportion of the decoherence time `t1` "
"to the maximum scan delay `max_delay` is used to extract abnormal sub-"
"experiments."
msgstr "**rate_threshold (float)** - 退相干时间 t1 与最大扫描延迟 max_delay 的比例用于提取异常子实验。"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:14
msgid ""
"**subplots (Tuple)** - The layout of the drawing canvas of the "
"experimental results."
msgstr "**subplots (Tuple)** - 实验结果绘图画布规模"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:16
msgid ""
"**x_label (List)** - The labels of the X-axis in the resulting graph, "
"corresponding to the number in `subplots`."
msgstr "x_label (List)** - 结果图中 X 轴的标签，对应 ``subplots`` 属性中的个数"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:19
msgid ""
"**y_label (List)** - The labels of the Y-axis in the resulting graph, "
"corresponding to the number in `subplots`."
msgstr "**y_label (List)** - 结果图中 Y 轴的标签，对应 ``subplots`` 属性中的个数"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:22
msgid ""
"**pcolormesh_options (Dict)** - Parameter selection for drawing "
"chromatogram, refer to `plt.pcolormesh()` method."
msgstr "**pcolormesh_options (Dict)** - 绘制色谱图的参数选择，参考 plt.pcolormesh() 方法。"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:25
msgid "**result_parameters (List)** - Expected Extracted Experimental Results."
msgstr "**result_parameters (List)** - 期望的实验结果"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options
msgid "Return type"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:27
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options
msgid "Returns"
msgstr ""

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._default_options:28
msgid "T1Spectrum experiment analysis options."
msgstr "T1Specturm 实验结果分析选项"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._extract_result:1
msgid "Extract experiment result."
msgstr "提取实验结果"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._extract_result:3
msgid "We do as follows:"
msgstr "我们是这样做的："

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._extract_result:5
msgid "Limit not showing abnormal points in title;"
msgstr "限制异常点在标题中显示"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._extract_result:6
msgid ""
"Scan the sub-experiment results and collect the sub-experiment "
"information whose goodness of fit and T1 do not meet the conditions;"
msgstr "扫描子实验结果，收集拟合优度和T1不满足条件的子实验信息；"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._extract_result:8
msgid "You will see abnormal AC, T1 and Frequceny in the results."
msgstr "你会在结果中看到异常的 AC、T1 和频率。"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._visualization:1
msgid "Visualization of experimental results"
msgstr "实验结果可视化"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._visualization:3
msgid "We will draw four graphs:"
msgstr "我们将绘制四张图"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._visualization:5
msgid "The relationship between `AC` and `T1`;"
msgstr "`AC` 和 `T1` 之间的关系"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._visualization:6
msgid "Depth spectrogram between `AC` and `Delay`;"
msgstr "`AC` 和 `Delay` 之间的色谱图"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._visualization:7
msgid "The relationship between `Frequncy` and `T1`;"
msgstr "`Frequency` 与 `T1` 之间的关系"

#: of
#: pyQCat.analysis.library.t1_spectrum_analysis.T1SpectrumAnalysis._visualization:8
msgid "Depth spectrogram between `Frequency` and `Delay`."
msgstr "`Frequency` 与 `Delay` 之间的关系"

#~ msgid "Bases: :py:class:`~pyQCat.analysis.curve_analysis.CurveAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_analysis "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.run_analysis>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid "Run analysis."
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_options "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_datas "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`data_filter "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`drawer <pyQCat.analysis.library.T1SpectrumAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_data "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`has_child "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`options "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`quality "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`results "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "Options:"
#~ msgstr ""

#~ msgid ""
#~ "freq_list (List, array): Corresponding to "
#~ "z_amp frequency list. r_square_threshold "
#~ "(float): To extract abnormal points,"
#~ msgstr ""

#~ msgid "set threshold of the analysis quality's r_square."
#~ msgstr ""

#~ msgid "rate_threshold (float): To extract abnormal points,"
#~ msgstr ""

#~ msgid "set threshold of the analysis results' rate."
#~ msgstr ""

#~ msgid "Now no fit model."
#~ msgstr ""

#~ msgid ""
#~ "Extract cavity frequency from twice "
#~ "cavity frequency spectrum experiment data."
#~ msgstr ""

#~ msgid "The basis for selecting data."
#~ msgstr ""

#~ msgid "Quality of fit outcome."
#~ msgstr ""

#~ msgid "Plot z_amp, frequency and T1 relationship."
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.analysis.curve_analysis.CurveAnalysis`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_sub_analysis "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_analysis "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.run_analysis>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid "Run analysis"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.set_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_datas "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`data_filter "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.data_filter>`\\"
#~ msgstr ""

#~ msgid "The parameters provided for data filter funtions."
#~ msgstr ""

#~ msgid ":obj:`drawer <pyQCat.analysis.library.T1SpectrumAnalysis.drawer>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_data "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`has_child "
#~ "<pyQCat.analysis.library.T1SpectrumAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.library.T1SpectrumAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.library.T1SpectrumAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":obj:`results <pyQCat.analysis.library.T1SpectrumAnalysis.results>`\\"
#~ msgstr ""

#~ msgid "This experiment is currently not being evaluated for quality."
#~ msgstr "该实验目前尚未进行质量评估。"

#~ msgid "extract experiment results;"
#~ msgstr "提取实验结果"

#~ msgid "expeirment results visualization."
#~ msgstr "实验结果可视化"

