# Configuration file for the Sphinx documentation builder.
#
# This file only contains a selection of the most common options. For a full
# list see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

# -- Path setup --------------------------------------------------------------

# If extensions (or modules to document with autodoc) are in another directory,
# add these directories to sys.path here. If the directory is relative to the
# documentation root, use os.path.abspath to make it absolute, like shown here.

import os
import sys
sys.path.insert(
    0,
    os.path.dirname(
        os.path.dirname((os.path.dirname(os.path.abspath(__file__))))))

# -- Project information -----------------------------------------------------

project = "pyQCat-Monster"
copyright = "2022, OriginQ pyQCat Development Team"
author = "pyQCat Development Team"

# The full version, including alpha/beta/rc tags
release = "0.0.1"

# -- General configuration ---------------------------------------------------

# Add any Sphinx extension module names here, as strings. They can be
# extensions coming with Sphinx (named "sphinx.ext.*") or your custom
# ones.

# sphinx.ext.autodoc: https://www.osgeo.cn/sphinx/usage/extensions/autodoc.html
# sphinx.ext.autosummary: https://www.osgeo.cn/sphinx/usage/extensions/autosummary.html?highlight=sphinx%20ext%20autosummary#module-sphinx.ext.autosummary
# sphinx.ext.napoleon: https://www.osgeo.cn/sphinx/usage/extensions/napoleon.html#module-sphinx.ext.napoleon
# sphinx_autodoc_typehints: https://pypi.org/project/sphinx-autodoc-typehints/
# sphinx.ext.mathjax:
# sphinx.ext.extlinks: https://www.osgeo.cn/sphinx/usage/extensions/extlinks.html?highlight=sphinx%20ext%20extlinks#confval-extlinks
extensions = [
    "sphinx.ext.autodoc",
    "sphinx.ext.autosummary",
    "sphinx.ext.napoleon",
    "sphinx_autodoc_typehints",
    "sphinx.ext.mathjax",
    "sphinx.ext.extlinks",
    "sphinx.ext.githubpages",
    "sphinx.ext.viewcode",
    "recommonmark"
]

# -----------------------------------------------------------------------------
# Autosummary
# -----------------------------------------------------------------------------

autosummary_generate = True

# -----------------------------------------------------------------------------
# Autodoc
# -----------------------------------------------------------------------------

autodoc_default_options = {
    "member-order": "bysource",
    "members": True,
    "inherited-members": False,
    "show-inheritance": True,
    "private-members": True,
}

# If true, figures, tables and code-blocks are automatically numbered if they
# have a caption.
numfig = True

# A dictionary mapping 'figure', 'table', 'code-block' and 'section' to
# strings that are used for format of figure numbers. As a special character,
# %s will be replaced to figure number.
numfig_format = {"table": "Table %s"}

# autoclass_content = "both"

# A boolean that decides whether module names are prepended to all object names
# (for object types where a 鈥渕odule鈥� of some kind is defined), e.g. for
# py:function directives.
add_module_names = False

# The name of the Pygments (syntax highlighting) style to use.
pygments_style = "colorful"

# If true, sectionauthor and moduleauthor directives will be shown in the
# output. They are ignored by default. 鏄剧ず浣滆��
show_authors = True

# Add any paths that contain templates here, relative to this directory.
templates_path = ["_templates"]

# The language for content autogenerated by Sphinx. Refer to documentation
# for a list of supported languages.
#
# This is also used if you do content translation via gettext catalogs.
# Usually you set "language" from the command line for these cases.
language = "en"
locale_dirs = ["../translation/"]
gettext_compact = False

# List of patterns, relative to source directory, that match files and
# directories to ignore when looking for source files.
# This pattern also affects html_static_path and html_extra_path.
exclude_patterns = []

# -- Options for HTML output -------------------------------------------------

# The theme to use for HTML and HTML Help pages.  See the documentation for
# a list of builtin themes.
html_theme = 'sphinx_rtd_theme'

# Add any paths that contain custom static files (such as style sheets) here,
# relative to this directory. They are copied after the builtin static files,
# so a file named "default.css" will overwrite the builtin "default.css".
# html_static_path = ['_static']

# html_sidebars = {'**': ['globaltoc.html']}
# If this is not None, a 鈥楲ast updated on:鈥� timestamp is inserted at every page bottom,
# using the given strftime() format.
# The empty string is equivalent to '%b %d, %Y' (or a locale-dependent equivalent).
html_last_updated_fmt = "%Y/%m/%d"
