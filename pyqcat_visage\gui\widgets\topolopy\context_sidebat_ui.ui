<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>376</width>
    <height>378</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="windowIcon">
   <iconset theme="system-run">
    <normaloff>C:/Users/<USER>/.designer/backup</normaloff>C:/Users/<USER>/.designer/backup</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="default_tab">
      <attribute name="title">
       <string>default</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_4" stretch="3,1,1,1,1,1,5,1,3">
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>12</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout" stretch="4,1,10">
         <item>
          <widget class="QLabel" name="label_10">
           <property name="text">
            <string>context name</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QComboBox" name="default_context_com">
           <property name="editable">
            <bool>true</bool>
           </property>
           <item>
            <property name="text">
             <string>qubit_calibration</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>coupler_probe_calibration</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>coupler_calibration</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>cz_gate_calibration</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>crosstalk_measure</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>union_read_measure</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>net_tunable</string>
            </property>
           </item>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_7">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>10</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="4,1,10">
         <item>
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>readout type</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QComboBox" name="default_read_com">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="editable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_8">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>9</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="4,1,10">
         <item>
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>physical unit</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_7">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QMultiComboBox" name="default_physical_unit_com">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="editable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_3">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>12</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="8,5,2">
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="btn_default_refresh">
           <property name="text">
            <string>refresh</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_4">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>12</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="exp_tab">
      <attribute name="title">
       <string>experiment</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_3" stretch="3,1,1,1,1,1,5,1,3">
       <item>
        <spacer name="verticalSpacer_4">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>12</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="4,1,10">
         <item>
          <widget class="QLabel" name="label_9">
           <property name="text">
            <string>context name</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_8">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QComboBox" name="exp_context_com">
           <property name="editable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_9">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>10</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="4,1,10">
         <item>
          <widget class="QLabel" name="label_8">
           <property name="text">
            <string>readout type</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_9">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QComboBox" name="exp_read_com">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="editable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_10">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>9</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="4,1,10">
         <item>
          <widget class="QLabel" name="label_7">
           <property name="text">
            <string>physical unit</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_10">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QMultiComboBox" name="exp_physical_unit_com">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="editable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_5">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>12</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_16" stretch="8,5,2">
         <item>
          <spacer name="horizontalSpacer_5">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="btn_exp_save">
           <property name="text">
            <string>save</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_6">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_6">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>12</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="point_tab">
      <attribute name="title">
       <string>point</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QPointTableView" name="point_table_view"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>Global</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_6" stretch="1,5,1,5,1,5,1,5,1,5,1,5,2,3">
       <item>
        <spacer name="verticalSpacer_17">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>0</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="4,1,10">
         <item>
          <widget class="QLabel" name="label_glo_1">
           <property name="text">
            <string>working type</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_11">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QComboBox" name="com_work_type">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="editable">
            <bool>true</bool>
           </property>
           <property name="currentText">
            <string>awg_bias</string>
           </property>
           <property name="frame">
            <bool>true</bool>
           </property>
           <item>
            <property name="text">
             <string>awg_bias</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>ac</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>dc</string>
            </property>
           </item>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_11">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>1</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="4,1,10">
         <item>
          <widget class="QLabel" name="label_glo_2">
           <property name="text">
            <string>divide type</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_12">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QComboBox" name="com_divide_type">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="editable">
            <bool>true</bool>
           </property>
           <item>
            <property name="text">
             <string>character_idle_point</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>calibrate_idle_point</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>character_point</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>sweet_point</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>calibration_point</string>
            </property>
           </item>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_12">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>0</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="4,1,10">
         <item>
          <widget class="QLabel" name="label_glo_3">
           <property name="text">
            <string>max point unit</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_13">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QMultiComboBox" name="com_max_qubit">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="editable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_13">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>0</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_11" stretch="4,1,10">
         <item>
          <widget class="QLabel" name="label_glo_4">
           <property name="text">
            <string>online unit</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_14">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QMultiComboBox" name="com_online_qubit">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="editable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_14">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>0</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_12" stretch="4,1,10">
         <item>
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>env_bits</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_15">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QMultiComboBox" name="env_com">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="editable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_15">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>1</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_13" stretch="4,1,10">
         <item>
          <widget class="QLabel" name="label_glo_5">
           <property name="text">
            <string>02 opt unit</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_16">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QMultiComboBox" name="f02_opt_qubit">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="editable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_16">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>18</width>
           <height>3</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_17" stretch="20,1,2,1">
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_5">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_14" stretch="1,1">
             <item>
              <widget class="QCheckBox" name="crosstalk_check">
               <property name="enabled">
                <bool>true</bool>
               </property>
               <property name="text">
                <string>crosstalk</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="checked">
                <bool>false</bool>
               </property>
               <property name="tristate">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QCheckBox" name="online_check">
               <property name="enabled">
                <bool>true</bool>
               </property>
               <property name="text">
                <string>online</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="checked">
                <bool>false</bool>
               </property>
               <property name="tristate">
                <bool>false</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_15" stretch="1,1">
             <item>
              <widget class="QCheckBox" name="xy_crosstalk_check">
               <property name="enabled">
                <bool>true</bool>
               </property>
               <property name="text">
                <string>xy_crosstalk</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="checked">
                <bool>false</bool>
               </property>
               <property name="tristate">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QCheckBox" name="custom_point_check">
               <property name="enabled">
                <bool>true</bool>
               </property>
               <property name="text">
                <string>custom_point</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="checked">
                <bool>false</bool>
               </property>
               <property name="tristate">
                <bool>false</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="horizontalSpacer_17">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="btn_global_set">
           <property name="text">
            <string>update</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_18">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QMultiComboBox</class>
   <extends>QComboBox</extends>
   <header>pyqcat_visage.gui.widgets.combox_custom.combox_multi</header>
  </customwidget>
  <customwidget>
   <class>QPointTableView</class>
   <extends>QTableView</extends>
   <header>.table_view_point</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>default_context_com</sender>
   <signal>currentTextChanged(QString)</signal>
   <receiver>Form</receiver>
   <slot>refresh_com()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>257</x>
     <y>209</y>
    </hint>
    <hint type="destinationlabel">
     <x>183</x>
     <y>184</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>exp_context_com</sender>
   <signal>currentTextChanged(QString)</signal>
   <receiver>Form</receiver>
   <slot>refresh_com()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>82</x>
     <y>198</y>
    </hint>
    <hint type="destinationlabel">
     <x>183</x>
     <y>184</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>btn_default_refresh</sender>
   <signal>clicked()</signal>
   <receiver>Form</receiver>
   <slot>default_refresh()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>69</x>
     <y>71</y>
    </hint>
    <hint type="destinationlabel">
     <x>163</x>
     <y>134</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>btn_exp_save</sender>
   <signal>clicked()</signal>
   <receiver>Form</receiver>
   <slot>context_save()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>69</x>
     <y>71</y>
    </hint>
    <hint type="destinationlabel">
     <x>163</x>
     <y>134</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>btn_global_set</sender>
   <signal>clicked()</signal>
   <receiver>Form</receiver>
   <slot>update_global()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>266</x>
     <y>219</y>
    </hint>
    <hint type="destinationlabel">
     <x>163</x>
     <y>134</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>refresh_com()</slot>
  <slot>context_save()</slot>
  <slot>default_refresh()</slot>
  <slot>update_global()</slot>
  <slot>update_env_bits()</slot>
 </slots>
</ui>
