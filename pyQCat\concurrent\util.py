# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/10
# __author:       <PERSON>

"""
Concurrent utils.
"""

import asyncio
import os
import secrets
from datetime import datetime
from enum import Enum
from typing import Any

from loguru import logger

from pyQCat.config import VISAGE_EXIST
from pyQCat.invoker import DEFAULT_FOLDER
from pyQCat.qm_protocol import CommonMessage
from pyQCat.types import SimulatorMode

CONCURRENT_CACHE = {
    "merge_thread": None,
    "merge_service": None,
    "concurrent_flag": False,
    "temp_trans": {},
    "docs": {},
    "async_win_size": None,
    "process_broken": False,
    "stop": False,
    "main_process": False,
    "transfer_name": "",
    "open_monitor": False,
    "open_record": True,
    "special_tag": "",
    "pulse_period": None
}

# 2024/06/26: zyc: Limit the maximum length of asynchronous task data for distribution
MAX_ASYNC_TASK_UNM_LIMIT = 10


def start_transfer_server_process(log_path: str = ""):
    formatted_now = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    CONCURRENT_CACHE["main_process"] = True
    CONCURRENT_CACHE["transfer_name"] = formatted_now
    from multiprocessing import Process

    from pyQCat.data_transfer.main import main

    proc = Process(target=main, args=(formatted_now, log_path))
    proc.start()
    return formatted_now


def is_force_stop():
    """Check if the user has initiated forced termination

    Returns:
        bool:
    """
    return CONCURRENT_CACHE["stop"]


def update_async_win_size(size):
    if size and isinstance(size, int) and size != CONCURRENT_CACHE["async_win_size"]:
        CONCURRENT_CACHE["async_win_size"] = size
        c_log(f"update async_win_size to {size}!")


def to_cache_bytes(any_body) -> bytes:
    """
    any data send to merge could use this.
    """
    search_id = get_secrets_token(8)
    CONCURRENT_CACHE["temp_trans"].update({search_id: any_body})
    return search_id


def from_cache_bytes(id_bytes) -> Any:
    """
    any bytes data get by merge service could use this.
    """
    if id_bytes in CONCURRENT_CACHE["temp_trans"]:
        return CONCURRENT_CACHE["temp_trans"].pop(id_bytes)
    return None


def get_secrets_token(bit_num=16) -> bytes:
    """
    get require id by bytes.
    """
    return secrets.token_bytes(bit_num)


def reset_concurrent_module_cache():
    """
    clear concurrent module cache.
    """
    CONCURRENT_CACHE["temp_trans"].clear()


class Singleton(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls]


def c_log(msg: str):
    """
    concurrent log
    """
    logger.log("FLOW", msg)


def c_error_log(msg: str, error=None):
    """
    concurrent error log
    """
    if error:
        if isinstance(error, Exception):
            error = str(error)
        msg = msg + "\n" + error
    logger.error(msg)


class ResponseCode(int, Enum):
    SUCCESS = 200
    FAILED = 400
    REQUIRE_NOT_EXIST = 401
    REQUIRE_BODY_ERROR = 405


class Response:
    """
    merge service response struct.
    """

    __slots__ = ["code", "data", "msg"]

    def __init__(self, code, data=None, msg=None):
        self.code = code
        self.data = data
        self.msg = msg

    def __repr__(self):
        return f"{self.code}: {self.msg}"


class ParallelExpMsg:
    """
    parallel exp msg.
    """

    __slots__ = [
        "exp_name",
        "parallel_total",
        "shared_resource",
        "simulate",
        "use_allocation_cache",
    ]

    def __init__(
        self,
        exp_name: str,
        parallel_total: int,
        shared_resource: CommonMessage,
        simulate: SimulatorMode = SimulatorMode.CLOSE,
        use_allocation_cache: bool = True,
    ):
        self.exp_name = exp_name
        self.parallel_total = parallel_total
        self.shared_resource = shared_resource
        self.simulate = simulate
        self.use_allocation_cache = use_allocation_cache


class MPTask:
    """
    Merge Part Task, the msg use to merge.
    """

    __slots__ = ["token", "count", "merge_struct", "physical_units", "require_id"]

    def __init__(self, token, count, merge_struct=None, physical_units=None, require_id=None):
        self.token = token
        self.count = count
        self.merge_struct = merge_struct
        self.require_id = require_id
        self.physical_units = physical_units

    def __str__(self):
        return f"MPTask({self.physical_units}|{self.token}|{self.count})"


class CutExpMsg:
    """
    Cut and Cuts exp msg.
    """

    __slots__ = ["token", "count", "physical_unit"]

    def __init__(self, token, physical_unit, count=None):
        self.token = token
        self.physical_unit = physical_unit
        self.count = count


class SyncMergeFuture:
    """
    Sync merge result struct.
    """

    __slots__ = ["_result"]

    def __init__(self, result):
        self._result = result

    def done(self):
        return bool(self._result)

    def result(self):
        return self._result


def pop_doc_id_from_concurrent(_id):
    CONCURRENT_CACHE["docs"].pop(_id, None)


def get_async_win_size():
    return CONCURRENT_CACHE["async_win_size"] or MAX_ASYNC_TASK_UNM_LIMIT


async def monitor_async_task_queue():
    """
    Monitor the number of asynchronous tasks, Block the compilation process when the number of
    tasks exceeds the maximum limit
    """
    while True:
        if len(CONCURRENT_CACHE["docs"]) < get_async_win_size():
            return
        else:
            await asyncio.sleep(0.01)


def record_process_id():
    """Used to record process ID when starting a program."""
    if VISAGE_EXIST:
        process_record_file = os.path.join(DEFAULT_FOLDER, "process")

        with open(process_record_file, "a+") as fs:
            pid = os.getpid()
            fs.write(str(pid) + "\n")


def kill_record_process_id():
    """Used to kill process ID when starting a visage program."""
    if VISAGE_EXIST:
        process_record_file = os.path.join(DEFAULT_FOLDER, "process")

        from pyqcat_visage.tool.utilies import kill_process_by_pid  # type: ignore

        if os.path.exists(process_record_file):
            # kill history process id
            with open(process_record_file, "r") as fs:
                for pid in fs:
                    kill_process_by_pid(pid[:-1])

            # clear msg
            with open(process_record_file, "w") as fs:
                pass
