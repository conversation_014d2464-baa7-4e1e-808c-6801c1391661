# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/11
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy
from functools import partial

import numpy as np
from scipy.interpolate import interp1d
from scipy.optimize import curve_fit, root_scalar
from scipy.signal import correlate, correlate2d, find_peaks, savgol_filter

from ...log import pyqlog
from ...types import QualityDescribe
from ..algorithms.find_peak import get_peak_point
from ..curve_fit_analysis import CurveFitAnalysis
from ..fit.curve_fit import cal_rmse, curve_fitting
from ..fit.fit_models import polynomial_model
from ..quality import BaseQuality
from ..specification import (
    CurveAnalysisData,
    FitData,
    FitModel,
    FitOptions,
    GoodnessofFit,
    ParameterRepr,
    QDict,
)
from ..standard_curve_analysis import Options, StandardCurveAnalysis


class CouplerTunableAnalysis(StandardCurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.plot_2d = True
        options.x_label = "flux (v)"
        options.result_parameters = [
            ParameterRepr(name="dc_min", repr="dc_min", unit="v"),
            ParameterRepr(name="dc_max", repr="dc_max", unit="v"),
            ParameterRepr(name="fc_min", repr="fc_min", unit="MHz"),
            ParameterRepr(name="fc_max", repr="fc_max", unit="MHz"),
            ParameterRepr(name="probe_freq", repr="probe_freq", unit="MHz"),
            ParameterRepr(name="Ql_min", repr="ql_min", unit=""),
            ParameterRepr(name="Ql_max", repr="ql_max", unit=""),
            ParameterRepr(name="tunable", repr="tunable", unit=""),
            ParameterRepr(name="r2", repr="r2", unit=""),
        ]
        options.max_points = []
        options.min_points = []
        return options

    def _data_processing(self):
        fc = self.experiment_data.y_data["fc"]
        x = self.experiment_data.x_data
        error_index = check_error_point(fc)
        x = np.delete(x, error_index)
        fc = np.delete(fc, error_index)
        outlier_index = get_outlier(fc)
        x = np.delete(x, outlier_index)

        origin_filter_fc = np.delete(fc, outlier_index)
        smooth_filter_fc = savgol_filter(origin_filter_fc, window_length=6, polyorder=2)
        self.experiment_data.y_data["origin_filter_fc"] = origin_filter_fc
        self.experiment_data.y_data["smooth_filter_fc"] = smooth_filter_fc
        self.experiment_data.replace_x_data = {
            "smooth_filter_fc": x,
            "origin_filter_fc": x,
        }

        max_y = np.max(smooth_filter_fc)
        y = -smooth_filter_fc + max_y
        distance = 5
        height = np.max(y) / 2
        prominence = np.max(y) / 3
        points = get_peak_point(x, y, distance, height, prominence, width=4)
        pos_list = []
        for point in points:
            point.y = max_y - point.y
            pos_list.append((round(point.x, 2), round(point.y, 4)))
            self.options.min_points.append(pos_list[-1])
        rp_list = [f"MIN\n{pos}" for pos in pos_list]
        if len(pos_list) >= 2:
            if abs(pos_list[0][1] - pos_list[1][1]) * 1e3 > 0.5:
                self.results.tunable.value = False
            else:
                f = interp1d(x, y, kind="quadratic")
                max_px = (pos_list[0][0] + pos_list[1][0]) / 2
                max_py = round(max_y - f(max_px), 4)
                pos_list.append((max_px, max_py))
                rp_list.append(f"MAX\n{pos_list[-1]}")
                self.options.max_points.append(pos_list[-1])
                self.results.dc_max.value = max_px
                self.results.fc_max.value = round(max_py * 1e3, 3)
                self.results.dc_min.value = pos_list[0][0]
                self.results.fc_min.value = round(pos_list[0][1] * 1e3, 3)
                self.results.tunable.value = True
        else:
            self.results.tunable.value = False
        self.results.r2.value = 0
        self.drawer.set_options(
            text_pos=pos_list, text_rp=rp_list, text_key=["smooth_filter_fc"]
        )

    def _visualization(self) -> None:
        super()._visualization()

        if self.options.min_points:
            self.drawer._get_axis(4).scatter(
                [pos[0] for pos in self.options.min_points],
                [pos[1] * 1e3 for pos in self.options.min_points],
                s=200,
                color="red",
                marker="*",
            )

        if self.options.max_points:
            self.drawer._get_axis(4).scatter(
                [pos[0] for pos in self.options.max_points],
                [pos[1] * 1e3 for pos in self.options.max_points],
                s=200,
                color="blue",
                marker="o",
            )

    def _evaluate_quality(self):
        if self.results.tunable.value is True:
            self._quality = BaseQuality.instantiate(QualityDescribe.normal)
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.abnormal)


def check_error_point(data):
    outlier_indices = np.where((data < 6) | (data > 8))[0]
    return outlier_indices


def get_outlier(data):
    # 计算 IQR 范围
    q1, q3 = np.percentile(data, [30, 70])
    iqr = q3 - q1
    lower = q1 - 0.5 * iqr
    upper = q3 + 1.5 * iqr

    # 获取离群点索引
    outlier_indices = np.where((data < lower) | (data > upper))[0]

    # outlier_values = data[outlier_indices]
    # print("离群点索引:", outlier_indices)  # 输出: [36, 37]
    # print("离群点值:", outlier_values)  # 输出: [7.444319 7.441426]

    return outlier_indices


class CouplerTunableAnalysisV1(StandardCurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.plot_2d = True
        options.x_label = "flux (v)"
        options.result_parameters = [
            ParameterRepr(name="dc_min", repr="dc_min", unit="v"),
            ParameterRepr(name="dc_max", repr="dc_max", unit="v"),
            ParameterRepr(name="fc_min", repr="fc_min", unit="MHz"),
            ParameterRepr(name="fc_max", repr="fc_max", unit="MHz"),
            ParameterRepr(name="probe_freq", repr="probe_freq", unit="MHz"),
            ParameterRepr(name="Ql_min", repr="ql_min", unit=""),
            ParameterRepr(name="Ql_max", repr="ql_max", unit=""),
            ParameterRepr(name="tunable", repr="tunable", unit=""),
            ParameterRepr(name="r2", repr="r2", unit=""),
        ]
        options.diff_rate = 1.4
        options.peak_rate = 0.65
        options.peak_limit = 3
        options.retry_count = 10
        options.percent_rate = 0.2
        return options

    def _data_processing(self):
        # step1: data prepare
        self.drawer.options.marker = None
        self.experiment_data.replace_x_data = {}
        ac_list = self.experiment_data.x_data  # scan ac
        qc_list = self.experiment_data.child_data(0).x_data  # scan cavity freq
        fc_list = self.experiment_data.y_data.get("fc")  # fc result
        self.experiment_data.y_data.clear()

        for key in ["Amp", "Phase"]:
            acq_amp_arr = []
            for i in range(len(ac_list)):
                acq_amp_arr.append(self.experiment_data.child_data(i).y_data.get(key))
            acq_amp_arr = np.array(acq_amp_arr)
            mean_value = np.mean(acq_amp_arr)
            max_value = np.max(acq_amp_arr)
            min_value = np.min(acq_amp_arr)

            valid_data = QDict(
                ac_list=ac_list,
                acq_amp_arr=acq_amp_arr,
                qc_list=qc_list,
                max_value=max_value,
                min_value=min_value,
                mean_value=mean_value,
                fc_list=fc_list,
            )

            is_ok = False
            for i in range(self.options.retry_count):
                is_ok = self._try_correlate2d_index(i, deepcopy(valid_data))
                if is_ok:
                    break
            if is_ok:
                break

    def _try_correlate2d_index(self, correlate2d_index: int, valid_data: QDict):
        try:
            ac_list = valid_data.ac_list
            acq_amp_arr = valid_data.acq_amp_arr
            qc_list = valid_data.qc_list
            max_value = valid_data.max_value
            min_value = valid_data.min_value
            mean_value = valid_data.mean_value
            fc_list = valid_data.fc_list

            # step2: 对二维热图沿着频率（纵轴）方向进行阶跃卷积，并求和
            plot_meta = QDict()
            kernel_sub = [-1, 0, 1]
            kernel = np.array([kernel_sub] * len(ac_list))
            output = correlate2d(acq_amp_arr, kernel, mode="same")
            row_sums = abs(np.sum(output, axis=0))[1:-1]
            x_values = qc_list[1:-1]
            y_values = row_sums
            max_idx = np.argsort(-y_values)[correlate2d_index]
            max_x = x_values[max_idx]
            self.experiment_data.y_data["Step convolution"] = y_values
            self.experiment_data.replace_x_data["Step convolution"] = x_values
            plot_meta.probe_freq = max_x

            # step3: Autocorrelation to search period
            expect_acq_amp_arr = acq_amp_arr[:, max_idx + 1]
            if (max_value - mean_value) < (mean_value - min_value):
                expect_acq_amp_arr = -expect_acq_amp_arr
            w_normalized = expect_acq_amp_arr - np.mean(expect_acq_amp_arr)
            auto_corr = np.correlate(w_normalized, w_normalized, mode="full")
            auto_corr = auto_corr[len(auto_corr) // 2:]
            auto_corr_peaks, auto_corr_properties = find_peaks(auto_corr, height=0)

            judge3 = True
            if len(auto_corr_peaks) > 1:
                peak_heights = auto_corr_properties["peak_heights"]
                highest_peak_idx = np.argmax(peak_heights)
                period = auto_corr_peaks[highest_peak_idx]
                if peak_heights[highest_peak_idx] / max(auto_corr) <= 0.35:
                    judge3 = False
            else:
                period = len(expect_acq_amp_arr) // 2

            plot_meta.auto_corr = QDict(
                period=period, peak_x=auto_corr_peaks, peak_y=auto_corr[auto_corr_peaks]
            )
            self.experiment_data.y_data["Autocorrelation"] = auto_corr
            self.experiment_data.replace_x_data["Autocorrelation"] = np.arange(
                len(auto_corr)
            )

            # step4: find peak
            amp_peaks_all, _ = find_peaks(expect_acq_amp_arr)
            amp_peaks, _ = find_peaks(expect_acq_amp_arr, distance=period)

            judge2 = True
            new_amp_peaks = [
                expect_acq_amp_arr[i] - min(expect_acq_amp_arr) for i in amp_peaks
            ]
            if len(new_amp_peaks) in [2, 3]:
                if min(new_amp_peaks) <= self.options.peak_rate * max(new_amp_peaks):
                    judge2 = False
            if len(new_amp_peaks) <= 1:
                judge2 = False
            if len(new_amp_peaks) > self.options.peak_limit:
                judge2 = False

            self.experiment_data.y_data["Peak Amp Signal"] = expect_acq_amp_arr
            plot_meta.amp_peaks = QDict(
                peak_x=ac_list[amp_peaks],
                peak_y=expect_acq_amp_arr[amp_peaks],
                peak_all_x=ac_list[amp_peaks_all],
                peak_all_y=expect_acq_amp_arr[amp_peaks_all],
            )

            # 相邻两个峰值点之间的距离不应该大于 diff rate (1.4) 倍周期
            judge1 = True
            if len(amp_peaks) in [2, 3]:
                diff_peaks = np.diff(amp_peaks)
                for diff in diff_peaks:
                    if diff > int(self.options.diff_rate * period):
                        judge1 = False

            # 找到的峰值对应的 fc 应是所在周期前 percent_rate 20% 以内的小值
            min_points = ac_list[amp_peaks]
            half_period = period // 2
            total_percent_limit = (max(fc_list) - min(fc_list)) * 0.2
            for index, mp in enumerate(amp_peaks):
                min_fc = fc_list[mp]
                cur_fc = fc_list[
                         max(0, mp - half_period): min(mp + half_period, len(ac_list))
                         ]
                sorted_qc = sorted(cur_fc)
                percent_index = int(len(sorted_qc) * self.options.percent_rate)
                min_percent_limit = sorted_qc[percent_index - 1]
                is_in_min_20_percent = min_fc <= min_percent_limit
                if (sorted_qc[-1] - sorted_qc[0]) <= total_percent_limit:
                    is_in_min_20_percent = True
                if is_in_min_20_percent:
                    break

            plot_meta.is_in_min_20_percent = is_in_min_20_percent
            if is_in_min_20_percent and judge1 and judge2 and judge3:
                self._quality = BaseQuality.instantiate(QualityDescribe.normal)
                dc_min = min_points[index]
                if index < len(min_points) - 1:
                    dc_max = (min_points[index] + min_points[index + 1]) / 2
                else:
                    dc_max = (min_points[index] + min_points[index - 1]) / 2
                self.results.dc_min.value = dc_min
                self.results.dc_max.value = dc_max
                if dc_max == dc_min or len(auto_corr_peaks) <= 1:
                    self._quality = BaseQuality.instantiate(QualityDescribe.abnormal)
                    return False
                else:
                    self.experiment_data.metadata.process_meta["plot_meta"] = plot_meta
                    return True
            else:
                self._quality = BaseQuality.instantiate(QualityDescribe.abnormal)
                return False
        except Exception:
            import traceback
            print(traceback.format_exc())
            self._quality = BaseQuality.instantiate(QualityDescribe.abnormal)
            return False

    def _visualization(self) -> None:
        super()._visualization()

        plot_meta = self.experiment_data.metadata.process_meta.get("plot_meta")
        if not plot_meta:
            return
        for ax_index in [3, 4]:
            self.drawer.draw_axh_line(
                plot_meta.probe_freq,
                ax_index=ax_index,
                color="red",
                linewidth=3,
                linestyle="--",
            )
        self.drawer.draw_axv_line(
            plot_meta.auto_corr.period,
            ax_index=1,
            color="black",
            linewidth=3,
            linestyle="--",
        )
        self.drawer.draw_scatter_point(
            x_data=plot_meta.auto_corr.peak_x,
            y_data=plot_meta.auto_corr.peak_y,
            ax_index=1,
            color="Green",
            marker="*",
            s=200,
        )
        self.drawer.draw_scatter_point(
            x_data=plot_meta.amp_peaks.peak_all_x,
            y_data=plot_meta.amp_peaks.peak_all_y,
            ax_index=2,
            color="red",
            marker="o",
            s=50,
        )
        self.drawer.draw_scatter_point(
            x_data=plot_meta.amp_peaks.peak_x,
            y_data=plot_meta.amp_peaks.peak_y,
            ax_index=2,
            color="Green",
            marker="*",
            s=200,
        )
        if plot_meta.is_in_min_20_percent:
            for ax_index in [3, 4]:
                self.drawer.draw_axv_line(
                    self.results.dc_min.value,
                    ax_index=ax_index,
                    linewidth=3,
                    linestyle="--",
                    color="red",
                )
                self.drawer.draw_axv_line(
                    self.results.dc_max.value,
                    ax_index=ax_index,
                    linewidth=3,
                    linestyle="--",
                    color="red",
                )

    def _evaluate_quality(self):
        pass


class CouplerTunableAnalysisV2(CurveFitAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.plot_2d = True
        options.x_label = "flux (v)"
        options.result_parameters = [
            ParameterRepr(name="dc_min", repr="dc_min", unit="v"),
            ParameterRepr(name="dc_max", repr="dc_max", unit="v"),
            # ParameterRepr(name="fc_min", repr="fc_min", unit="MHz"),
            # ParameterRepr(name="fc_max", repr="fc_max", unit="MHz"),
            # ParameterRepr(name="probe_freq", repr="probe_freq", unit="MHz"),
            # ParameterRepr(name="Ql_min", repr="ql_min", unit=""),
            # ParameterRepr(name="Ql_max", repr="ql_max", unit=""),
            # ParameterRepr(name="r2", repr="r2", unit=""),
            # ParameterRepr(name="tunable", repr="tunable", unit=""),
        ]
        options.delta_y = 6 ** 2 / 300 / 1e3
        options.thod = 0.001
        options.order = [4, 6, 8, 10]
        options.data_key = ["segment1", "segment2"]
        options.best = {'order': None, 'params': None, 'mse': np.inf, 'a': None}
        # options.p0 = ["a", "d", "coeffs"]
        options.fit_model = FitModel(
            fit_func=polynomial_model)
        return options

    def _create_analysis_data(self):
        """Create Analysis data provided for detailed analyze.

        Returns:
            A QDict object, key represents data type and value is
            CurveAnalysisData object.
        """
        analysis_data_dict = QDict()
        data_key = list(
            self.options.get("data_key", None) or self._experiment_data.y_data.keys()
        )
        ac_list = self.experiment_data.x_data  # scan ac
        fc_list = self.experiment_data.y_data.get("fc")  # fc result
        y_diff = np.diff(fc_list)
        y_new_seg_index = self.extract_normal_segments(y_diff, thod=self.options.thod)
        y_new_seg_index = [i for i in y_new_seg_index if (i[1] - i[0]) > 5]  # 数据长度判断，考虑整个数据长度
        # 大半周期判断
        if len(y_new_seg_index) > 1:
            y_new_seg_index = self.find_longest_two_intervals(y_new_seg_index)
            n_y = len(fc_list)
            if (y_new_seg_index[0][1] - y_new_seg_index[0][0]) > n_y * 7 / 9:
                y_new_seg_index = y_new_seg_index[:1]
        ind = 0
        x1 = ac_list[y_new_seg_index[ind][0]:y_new_seg_index[ind][1]]
        y1 = fc_list[y_new_seg_index[ind][0]:y_new_seg_index[ind][1]]
        analysis_data = CurveAnalysisData(
            x=x1,
            y=y1,
        )
        key = data_key[ind]
        analysis_data_dict[key] = analysis_data
        if len(y_new_seg_index) > 1:
            ind = 1
            x2 = ac_list[y_new_seg_index[ind][0]:y_new_seg_index[ind][1]]
            y2 = fc_list[y_new_seg_index[ind][0]:y_new_seg_index[ind][1]]
            analysis_data = CurveAnalysisData(
                x=x2,
                y=y2,
            )
            key = data_key[ind]
            analysis_data_dict[key] = analysis_data
        return analysis_data_dict

    def _run_fitting(self) -> bool:
        """Perform curve fitting on given data collection and fit models.

        Returns:
            fitting success or failed.
        """
        fit_data = self._first_data_fit()
        res = self._second_data_fit()
        if res:
            fit_data = res

        # if all guess parameters fitting failed, return false.
        if not fit_data:
            pyqlog.warning(
                "All initial guesses and parameter boundaries failed to fit the data. "
                "Please provide better initial guesses or fit parameter boundaries.",
            )
            # at least return raw data points rather than terminating
            return False
        else:
            return True

    @staticmethod
    def extract_normal_segments(data, thod=0.003):
        normal_segments = []
        start_index = None

        # 提取导数小于某些值的区域
        for i, value in enumerate(data):
            if abs(value) < thod:
                if start_index is None:
                    start_index = i
            else:
                if start_index is not None:
                    normal_segments.append((start_index, i - 1))
                    start_index = None

        # Check if the last segment ends at the end of the list
        if start_index is not None:
            normal_segments.append((start_index, len(data) - 1))

        return normal_segments

    @staticmethod
    def find_longest_two_intervals(intervals: list):
        intervals.sort(key=lambda x: x[1] - x[0], reverse=True)
        return intervals[:2]

    def _fit_after_processing(self, analysis_data: "CurveAnalysisData"):
        x = analysis_data.x
        y = analysis_data.y
        best = self.options.best
        plot_meta = QDict()
        if best['order'] is not None:
            model_best = self.options.fit_model.fit_func(best['order']).fit_func
            x_fine = np.linspace(x.min(), x.max(), 500)
            y_fit = model_best(x_fine, *best['params'])
            plot_meta.first_fit = QDict()
            plot_meta.first_fit.x = x
            plot_meta.first_fit.y = y
            plot_meta.first_fit.replace_x = x_fine
            plot_meta.first_fit.replace_y = y_fit
            plot_meta.first_fit.a = best["a"]
            plot_meta.first_fit.target_y = model_best(best['a'], *best['params']) + self.options.delta_y
            self.experiment_data.metadata.process_meta["plot_meta"] = plot_meta

        # idle point find
        def func_model(x_val, target_y):
            return model_best(x_val, *best['params']) - target_y

        if self.options.delta_y is not None and best['order'] is not None:
            model_best = self.options.fit_model.fit_func(best['order']).fit_func
            y_min = model_best(best['a'], *best['params'])
            target_y = y_min + self.options.delta_y
            func_d = partial(func_model, target_y=target_y)

            # 左侧根
            a_min, a = x.min(), best['a']
            try:
                sol_left = root_scalar(func_d, bracket=[a_min, best['a']], method='brentq')
                x_left = sol_left.root if sol_left.converged else None
            except ValueError:
                x_left = None

            # 右侧根
            a, a_max = best['a'], x.max()
            try:
                sol_right = root_scalar(func_d, bracket=[best['a'], a_max], method='brentq')
                x_right = sol_right.root if sol_right.converged else None
            except ValueError:
                x_right = None

            # 返回更靠近x=0的解
            candidates = [val for val in [x_left, x_right] if val is not None]
            if candidates:
                x_target = min(candidates, key=lambda z: abs(z))
                best['x_target'] = x_target  # 这里不是相对于最低点的位置

        # guess dcmax
        model_best = self.options.fit_model.fit_func(best['order']).fit_func
        y_min = model_best(best['a'], *best['params'])
        x_fine = np.linspace(x.min(), x.max(), 500)
        y_fit = model_best(x_fine, *best['params'])
        y_max = max(y_fit)
        target_y = y_min + (y_max - y_min) * 40  # 经验参数here
        func_d = partial(func_model, target_y=target_y)
        # 左侧根
        a_min, a = -0.48, best['a']
        try:
            sol_left = root_scalar(func_d, bracket=[a_min, best['a']], method='brentq')
            x_left = sol_left.root if sol_left.converged else None
        except ValueError:
            x_left = None

        # 右侧根
        a, a_max = best['a'], 0.48
        try:
            sol_right = root_scalar(func_d, bracket=[best['a'], a_max], method='brentq')
            x_right = sol_right.root if sol_right.converged else None
        except ValueError:
            x_right = None

        # 返回更靠近x=0的解
        candidates = [val for val in [x_left, x_right] if val is not None]
        if candidates:
            x_target = min(candidates, key=lambda z: abs(z))
            best['dcmax_guess'] = x_target  # 这里不是相对于最低点的位置
            return True
        return False

    def _first_data_fit(self):
        analysis_data: "CurveAnalysisData" = list(self.analysis_datas.values())[0]
        fit_data = None
        for order in self.options.order:
            num_coeffs = order // 2
            p0 = [np.mean(analysis_data.x)] + [1.0] * num_coeffs + [np.min(analysis_data.y)]
            fit_func = self.options.fit_model.fit_func(order).fit_func
            try:
                params_opt, _ = curve_fit(fit_func, analysis_data.x, analysis_data.y, p0=p0, maxfev=10000)
                y_fit = fit_func(analysis_data.x, *params_opt)
                variance = cal_rmse(analysis_data.x, analysis_data.y, y_fit)
                # score = self.mse(analysis_data.y, y_fit)
                if variance < self.options.best['mse']:
                    self.options.best.update(
                        {'order': order, 'params': params_opt, 'mse': variance, 'a': params_opt[0]})
                fit_data = FitData(
                    popt=list(params_opt),
                    popt_keys=list("param"),
                    variance=variance,
                    y_fit=y_fit
                )
                fit_data.format()
            except Exception as err:
                print(err)

        # Find best value with r-square value.
        fit_data.goodness_of_fit = GoodnessofFit(
            *self.options.quality_bounds
        )
        fit_data.goodness_of_fit.evaluate(analysis_data.y, fit_data.y_fit)
        analysis_data.fit_data = fit_data
        res = self._fit_after_processing(analysis_data)
        if not res:
            fit_data.goodness_of_fit.descriptor = QualityDescribe.bad
        return fit_data


    def _second_data_fit(self):
        if len(self.analysis_datas) <= 1:
            return
        best = self.options.best
        analysis_data: "CurveAnalysisData" = list(self.analysis_datas.values())[1]
        x = analysis_data.x
        y = analysis_data.y
        fixed_params = best['params']
        fit_func = polynomial_model(best["order"]).fit_func
        def reduced_model(x, a):
            params = [a] + list(fixed_params[1:])
            return fit_func(x, *params)

        fit_model = FitModel(
            fit_func=reduced_model
        )
        fit_options = FitOptions(
            parameters=fit_model.signature,
            default_p0=self.options.p0,
            default_bounds=self.options.bounds,
            **self.options.curve_fit_extra,
        )
        fit_options.p0.set_if_empty(a=np.mean(x))
        fit_data: "FitData" = curve_fitting(analysis_data, fit_options, fit_model)
        a_fit = fit_data.popt[0]
        all_params = [a_fit] + list(fixed_params[1:])
        plot_meta = self.experiment_data.metadata.process_meta.get("plot_meta")
        x_fine = np.linspace(x.min(), x.max(), 500)
        y_fit = fit_func(x_fine, *all_params)
        plot_meta.second_fit = QDict()
        plot_meta.second_fit.x = x
        plot_meta.second_fit.y = y
        plot_meta.second_fit.replace_x = x_fine
        plot_meta.second_fit.replace_y = y_fit
        plot_meta.second_fit.a = a_fit

        fit_data.goodness_of_fit = GoodnessofFit(
            *self.options.quality_bounds
        )
        fit_data.goodness_of_fit.evaluate(analysis_data.y, fit_data.y_fit)
        analysis_data.fit_data = fit_data
        return fit_data

    def _data_processing(self):
        super()._data_processing()
        plot_meta = self.experiment_data.metadata.process_meta.get("plot_meta")
        flag = 1
        k1 = self.options.best["a"]
        dc_max_guess = self.options.best.get("dcmax_guess")
        k2 = dc_max_guess if dc_max_guess else k1
        x_target = self.options.best.get("x_target")
        idle_point = x_target if x_target else k1
        if plot_meta.get("second_fit"):
            flag = 0
            k2 = plot_meta.second_fit.a
        if flag == 0:
            min_point = k1
            max_point = (k1 + k2) / 2
        else:
            min_point = k1
            max_point = k2
        self.results.dc_min.value = min_point
        self.results.dc_max.value = max_point

        ac_list = self.experiment_data.x_data  # scan ac
        fc_list = self.experiment_data.y_data.get("fc")  # fc result
        y_diff = np.diff(fc_list)
        plot_meta.ac = QDict()
        plot_meta.ac.x = ac_list
        plot_meta.ac.y = fc_list
        plot_meta.ac.max_point = max_point
        plot_meta.ac.min_point = min_point
        plot_meta.ac.idle_point = idle_point
        plot_meta.ac.diff = y_diff

    # @staticmethod
    # def mse(y_true, y_pred):
    #     return np.mean((y_true - y_pred) ** 2)

    def _initialize_canvas(self):
        # Default drawing of all y data data
        subplots = (2, 2)
        self.options.figsize = (16, 9)
        # Set Canvas Options
        self.drawer.set_options(
            subplots=self.options.get("subplots") or subplots,
            figsize=self.options.figsize,
            raw_data_format=self.options.raw_data_format,
        )

        # CurveDrawer initialize canvas
        self.drawer.initialize_canvas()

    def _visualization(self) -> None:
        # super()._visualization()

        plot_meta = self.experiment_data.metadata.process_meta.get("plot_meta")
        if not plot_meta:
            return
        first_fit_data = plot_meta.first_fit
        ax_index = 0
        self.drawer.draw_scatter_point(
            first_fit_data.x,
            first_fit_data.y,
            ax_index=ax_index,
            s=10,
            label="data"
        )
        self.drawer.draw_plot_point(
            first_fit_data.replace_x,
            first_fit_data.replace_y,
            ax_index=ax_index,
            label=f"{self.options.best['order']} fit"
        )
        self.drawer.draw_axv_line(
            first_fit_data.a,
            ax_index=ax_index,
            color="g",
            linestyle="--",
            label="min"
        )
        self.drawer.draw_axh_line(
            first_fit_data.target_y,
            ax_index=ax_index,
            color="m",
            linestyle="-.",
            label="target_y"
        )

        second_fit_data = plot_meta.second_fit
        if second_fit_data:
            ax_index += 1
            self.drawer.draw_scatter_point(
                second_fit_data.x,
                second_fit_data.y,
                ax_index=ax_index,
                s=10,
                label="data"
            )
            self.drawer.draw_plot_point(
                second_fit_data.replace_x,
                second_fit_data.replace_y,
                ax_index=ax_index,
                label=f"{self.options.best['order']} fit"
            )
            self.drawer.draw_axv_line(
                second_fit_data.a,
                ax_index=ax_index,
                color="g",
                linestyle="--",
                label="min"
            )

        ac_data = plot_meta.ac
        ax_index += 1
        self.drawer.draw_scatter_point(
            ac_data.x,
            ac_data.y,
            ax_index=ax_index,
            s=10,
            label="data"
        )
        self.drawer.draw_axv_line(
            ac_data.max_point,
            ax_index=ax_index,
            color="darkred",
            linestyle="--",
            label=f'max={np.round(ac_data.max_point, 3)}'
        )
        self.drawer.draw_axv_line(
            ac_data.min_point,
            ax_index=ax_index,
            color="darkgreen",
            linestyle="--",
            label=f'min={np.round(ac_data.min_point, 3)}'
        )
        self.drawer.draw_axv_line(
            ac_data.idle_point,
            ax_index=ax_index,
            color="darkorange",
            linestyle="--",
            label=f'idle={np.round(ac_data.idle_point, 3)}'
        )
        ax_index += 1
        self.drawer.draw_plot_point(
            range(len(ac_data.diff)),
            ac_data.diff,
            ax_index=ax_index,
            label="diff"
        )


class CouplerTunableAnalysisV3(StandardCurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.plot_2d = True
        options.x_label = "flux (v)"
        options.result_parameters = [
            ParameterRepr(name="dc_min", repr="dc_min", unit="v"),
            ParameterRepr(name="dc_max", repr="dc_max", unit="v"),
        ]
        options.diff_rate = 1.4
        options.peak_rate = 0.65
        options.peak_limit = 3
        options.retry_count = 10
        options.percent_rate = 0.2
        return options

    def _data_processing(self):

        def get_value_label(x, y, pair):
            new_peak = (pair[0] + pair[1]) / 2
            if new_peak.is_integer():
                value = y[int(new_peak)]
                x = x[int(new_peak)]
            else:
                left = int(np.floor(new_peak))
                right = int(np.ceil(new_peak))
                value = (y[left] + y[right]) / 2
                x = (x[left] + x[right]) / 2
            local_y = y[pair[0] : pair[1]]
            percentile = np.sum(local_y <= value) / len(local_y) * 100
            if percentile <= 45:  # 最小20%
                plot_meta.dc_min = x
                return True
            elif percentile >= 65:  # 最大20%
                plot_meta.dc_max = x
                return True
            return False

        ac_list = self.experiment_data.x_data
        fc_list = self.experiment_data.y_data.get("fc")
        plot_meta = QDict(
            quality=BaseQuality.instantiate(QualityDescribe.normal),
            dc_min=None,
            dc_max=None,
            peak_voltage=None,
        )
        self.experiment_data.metadata.process_meta["plot_meta"] = plot_meta

        kernel = [-1, 0, 1]
        convolved = correlate(fc_list, kernel, mode="same")
        peaks, _ = find_peaks(
            abs(convolved[1:-1]),
            distance=4,
            height=np.max(abs(convolved[1:-1])) * 0.2,
        )
        peak_voltage = [ac_list[1:-1][i] for i in peaks]
        plot_meta.peak_voltage = peak_voltage
        if len(peak_voltage) > 4:
            plot_meta.quality = BaseQuality.instantiate(QualityDescribe.bad)
        elif len(peak_voltage) < 2:
            plot_meta.quality = BaseQuality.instantiate(QualityDescribe.bad)
        elif len(peak_voltage) == 2:
            pairs = [[peaks[0], peaks[1]]]
            if peaks[0] > (len(ac_list) - peaks[1]):
                ans = get_value_label(ac_list[1:-1], fc_list[1:-1], [1, peaks[0]])
                if ans:
                    pass
                else:
                    ans = get_value_label(ac_list[1:-1], fc_list[1:-1], [peaks[1], len(ac_list) - 1])
            else:
                ans = get_value_label(ac_list[1:-1], fc_list[1:-1], [peaks[1], len(ac_list) - 1])
                if ans:
                    pass
                else:
                    ans = get_value_label(ac_list[1:-1], fc_list[1:-1], [1, peaks[0]])
            for pair in pairs:
                get_value_label(ac_list[1:-1], fc_list[1:-1], pair)

        else:
            pairs = [[peaks[i], peaks[i + 1]] for i in range(len(peaks) - 1)]
            for pair in pairs:
                ans = get_value_label(ac_list[1:-1], fc_list[1:-1], pair)

        if plot_meta.dc_min is None or plot_meta.dc_max is None:
            plot_meta.quality = BaseQuality.instantiate(QualityDescribe.bad)

    def _evaluate_quality(self):
        plot_meta = self.experiment_data.metadata.process_meta.get("plot_meta")
        self._quality = plot_meta.quality

    def _extract_result(self):
        plot_meta = self.experiment_data.metadata.process_meta.get("plot_meta")
        self.results.dc_min.value = plot_meta.dc_min
        self.results.dc_max.value = plot_meta.dc_max

    def _visualization(self) -> None:
        super()._visualization()

        plot_meta = self.experiment_data.metadata.process_meta.get("plot_meta")

        for peak in plot_meta.peak_voltage:
            self.drawer.draw_axv_line(
                x=peak,
                ax_index=0,
                color="black",
                linestyle="--",
                alpha=0.5,
            )

        if plot_meta.dc_min is not None:
            self.drawer.draw_axv_line(
                x=plot_meta.dc_min,
                ax_index=0,
                color="green",
                linestyle="--",
                alpha=0.7,
            )

        if plot_meta.dc_max is not None:
            self.drawer.draw_axv_line(
                x=plot_meta.dc_max,
                ax_index=0,
                color="red",
                linestyle="--",
                alpha=0.7,
            )
