# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 17:45+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/fit/pyQCat.analysis.fit.amp2cavity_freq_formula.rst:2
msgid "pyQCat.analysis.fit.amp2cavity\\_freq\\_formula"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2cavity_freq_formula:1
msgid "DC modulation model."
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2cavity_freq_formula:3
msgid ""
"phi = \\pi \\ast M \\ast (x - offset)\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2cavity_freq_formula:6
msgid ""
"fr1 = (fq\\_max + fc) \\times \\sqrt{\\sqrt{1 + d^2 (\\tan (phi))^2} "
"\\times \\left | \\cos (phi) \\right | }\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2cavity_freq_formula:9
msgid ""
"delta = fr1 - fr0\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2cavity_freq_formula:12
msgid ""
"kai = \\frac{g^2}{delta\\cdot (1 - \\frac{delta}{fc} )}\n"
"\n"
msgstr ""

#: of pyQCat.analysis.fit.fit_models.amp2cavity_freq_formula:15
msgid ""
"y = fr0 - kai\n"
"\n"
msgstr ""

#~ msgid "Qubit dc modulation function."
#~ msgstr ""

