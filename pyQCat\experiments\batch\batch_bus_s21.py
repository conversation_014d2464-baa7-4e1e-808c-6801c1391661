# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/10
# __author:       <PERSON><PERSON><PERSON>

"""
节点作用：
1. 获取全部 BUS 的 4-8.5 GHz 高功率 S21 数据
2. 获取全部 BUS 的腔频范围高（-15dBm) 低（-40dBm) 功率S21数据
3. 期望的测试结果详见: BUS S21

节点流程：

1. 使用拨码开关控制网分作用的 BUS
2. 使用网络分析仪获取 S21 信号，截图保存，并保存 S2P 文件
3. 每根 BUS 重复上述流程
"""

import time
from functools import cmp_to_key

import matplotlib.pyplot as plt
import numpy as np

from ...errors import BatchExperimentError, ConfigError
from ...instrument.INSTRUMENT import INSTRUMENT
from ...processor.chip_generator import BUS_CAVITY_FREQ_SORT_DICT
from ...processor.utilities import sort_bit
from ..batch_experiment import (
    BatchExperiment,
    BatchPhysicalUnitType,
    List,
    Path,
    QDict,
    pyqlog,
)


class BatchBusBase(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.micro_switch = "MicroSwitch"
        options.physical_unit_type = BatchPhysicalUnitType.BUS
        options.affect_next_node = False
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.bus_data = QDict()
        options.micro_switch = None
        options.ppt_template.update(
            dict(
                shape=(2, 2),
                split_by_unit=False,
            )
        )
        return options

    @property
    def bus_data(self):
        return self.run_options.bus_data

    def _check_options(self):
        super()._check_options()
        config = self.context_manager.config
        micro_switch = self.experiment_options.micro_switch
        self.run_options.micro_switch = INSTRUMENT(config.system.config_path)[
            micro_switch
        ]
        if not self.run_options.bus_data:
            raise BatchExperimentError("no find bus data in config path")

    def change_bus(self, bus: int):
        time.sleep(1)
        retry = 0
        while retry < 3:
            try:
                self.run_options.micro_switch.open_bus(bus)
            except Exception as e:
                pyqlog.error(f"change micro switch bus-{bus} retry-{retry} error: {e}")
                retry += 1
                time.sleep(1)
            else:
                return True
        return False

    def collect_record_data(self):
        try:
            self.run_options.micro_switch.close_sw()
        except Exception as e:
            pyqlog.error(f"close micro switch error: {e}")
        super().collect_record_data()
        self._save_data_to_json(
            self.run_options.bus_data.to_dict(), "bus_data", mode="config"
        )


class BatchSaturationPower(BatchBusBase):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.flows = ["FindSaturationPower"]
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.saturation_power = {}
        return options

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)
        for unit in record.pass_units:
            self.run_options.saturation_power[unit] = record["analysis_data"][unit][
                "result"
            ]["saturation_power"]
        return record

    def _run_batch(self):
        pass_units = []
        for bus in self.experiment_options.physical_units:
            bus_num = int(bus.split("-")[-1])
            if not self.change_bus(bus_num):
                continue
            self.change_regular_exec_exp_options(
                self.experiment_options.flows[0], bus=bus_num
            )
            flow_pass_units = self._run_flow(
                flows=self.experiment_options.flows,
                physical_units=[bus],
                name=bus,
            )
            if flow_pass_units:
                pass_units.append(bus)
        self.bind_pass_units(pass_units)
        saturation_power = self.run_options.saturation_power
        if saturation_power:
            mean_power = int(np.mean(list(saturation_power.values())))
            for bus, bus_state in self.run_options.bus_data.bus_state.items():
                high_power = saturation_power.get(bus, mean_power)
                bus_state.high_power = high_power


class BatchBusS21(BatchBusBase):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.flows = ["BusS21Collector"]
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.s21_data = {}
        options.ppt_template.update(
            dict(
                batch_pic_count=1,
                batch_shape=(1, 1),
                shape=(2, 2),
                split_by_unit=False,
            )
        )
        return options

    def _batch_down(self):
        super()._batch_down()
        save_path = self._plot_s21()
        self.record_meta.execute_meta.result.origin_png_results = [save_path]

    def _plot_s21(self):
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle("All BUS S21", fontsize=16)
        color_cycle = plt.rcParams["axes.prop_cycle"].by_key()["color"][:8]
        style_cycle = ["-", "--", "-."]
        i = 0
        for k, v in self.run_options.s21_data.items():
            ax1.plot(
                v["hp_wide"]["freq"],
                v["hp_wide"]["amp"],
                label=k,
                color=color_cycle[i % 8],
                linestyle=style_cycle[i // 8],
            )
            ax2.plot(
                v["hp_small"]["freq"],
                v["hp_small"]["amp"],
                label=k,
                color=color_cycle[i % 8],
                linestyle=style_cycle[i // 8],
            )
            i += 1

        ax1.set_xlabel("Frequency (MHz)")
        ax1.set_ylabel("Amp")
        ax1.legend()
        ax2.set_xlabel("Frequency (MHz)")
        ax2.set_ylabel("Amp")
        ax2.legend()

        save_path = str(Path(Path(self.run_options.record_path).parent, "s21.png"))
        fig.savefig(save_path)
        plt.tight_layout()
        plt.close()
        return save_path

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)
        for unit, data in record["analysis_data"].items():
            self.run_options.bus_data.bus_state[unit].update(data["result"])

        experiment_data = exp.experiment_data
        self.run_options.s21_data[physical_units[0]] = dict(
            hp_wide=dict(
                freq=experiment_data.replace_x_data.get("hp_wide_amp"),
                amp=experiment_data.y_data.get("hp_wide_amp"),
            ),
            hp_small=dict(
                freq=experiment_data.replace_x_data.get("hp_small_amp"),
                amp=experiment_data.y_data.get("hp_small_amp"),
            ),
        )

        return record

    def _run_batch(self):
        pass_units = []
        for bus in self.experiment_options.physical_units:
            bus_num = int(bus.split("-")[-1])
            if not self.change_bus(bus_num):
                continue
            self.change_regular_exec_exp_options(
                self.experiment_options.flows[0], bus=bus_num
            )
            flow_pass_units = self._run_flow(
                flows=self.experiment_options.flows,
                physical_units=[bus],
                name=bus,
            )
            if flow_pass_units:
                pass_units.append(bus)
        self._extract_mid_chi()
        self.bind_pass_units(pass_units)

    def _extract_mid_chi(self):
        chi_list = []
        median_chi = None
        for _, data in self.run_options.bus_data.bus_state.items():
            if "normal" in data.s21_quality:
                chi_list.extend(data["cavity_shift"])
        if chi_list:
            median_chi = np.median(np.array(chi_list))
        self.run_options.bus_data.median_chi = median_chi


class BatchBusCavityQ(BatchBusBase):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.flows = ["BusQFit"]
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.ppt_template.update(
            dict(
                shape=(2, 2),
                split_by_unit=True,
            )
        )
        options.hq_pass_count = 0
        options.lq_pass_count = 0
        options.q_count = 0
        return options

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)
        q_data = exp.analysis.results.data.value
        unit = physical_units[0]
        if not q_data:
            return record
        for k, v in q_data.items():
            for p, qd in v.items():
                quality = qd.get("quality", "bad")
                if "perfect" in quality or "normal" in quality:
                    if k == "high_power":
                        self.run_options.hq_pass_count += 1
                    elif k == "low_power":
                        self.run_options.lq_pass_count += 1
        self.run_options.bus_data.bus_state[unit].q_data = q_data
        return record

    def _run_batch(self):
        pass_units = []
        self.run_options.q_count = len(self.experiment_options.physical_units) * 6
        for bus in self.experiment_options.physical_units:
            bus_num = int(bus.split("-")[-1])
            self.change_regular_exec_exp_options(
                exp_name=self.experiment_options.flows[0], bus=bus_num
            )
            if not self.change_bus(bus_num):
                continue
            flow_pass_units = self._run_flow(
                flows=self.experiment_options.flows,
                physical_units=[bus],
                name=bus,
            )
            if flow_pass_units:
                pass_units.append(bus)
        self.bind_pass_units(pass_units)
        self.bus_data.hq_pass_count = self.run_options.hq_pass_count
        self.bus_data.lq_pass_count = self.run_options.lq_pass_count
        self.bus_data.hq_pass_rate = round(
            self.run_options.hq_pass_count / self.run_options.q_count, 2
        )
        self.bus_data.lq_pass_rate = round(
            self.run_options.lq_pass_count / self.run_options.q_count, 2
        )


class BatchBusImpaCalibrate(BatchBusBase):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.flux_scan_flows = ["ImpaCavityFluxScan"]
        options.flows = ["ImpaOptiParams", "ImpaGain"]
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.ppt_template.update(
            dict(
                shape=(2, 3),
                split_by_unit=True,
            )
        )
        options.flux_points_map = {}
        options.fc_scope = {}
        return options

    def _check_options(self):
        super()._check_options()
        for bus in self.experiment_options.physical_units:
            fcs = self.bus_data.bus_state[bus].low_power_cavity
            fc_min, fc_max = min(fcs) - 50, max(fcs) + 50
            self.run_options.fc_scope[bus] = [
                fc_min * 2 * 1e-3,
                fc_max * 2 * 1e-3,
                (fc_min + fc_max) * 1e-3,
            ]
        self.run_options.ppt_template.exps = (
            self.experiment_options.flux_scan_flows + self.experiment_options.flows
        )

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "ImpaCavityFluxScan" == exp_name:
            for unit in record.pass_units:
                flux_points = record.analysis_data.get(unit).get("result").get("points")
                flux_points.append(None)
                self.run_options.flux_points_map[unit] = flux_points

        elif "ImpaOptiParams" == exp_name:
            for unit in record.pass_units:
                best_gain = (
                    record.analysis_data.get(unit).get("result").get("best_gain")
                )
                self.change_regular_exec_exp_options(
                    exp_name="ImpaGain", ffp_list=best_gain
                )
        elif "ImpaGain" == exp_name:
            impa_params = exp.experiment_options.ffp_list
            for unit in record.pass_units:
                self.bus_data.bus_state[unit].impa_params = impa_params

        return record

    def _run_batch(self):
        pass_units = []
        for bus in self.experiment_options.physical_units:
            bus_num = int(bus.split("-")[-1])
            if not self.change_bus(bus_num):
                continue
            for exp_name in self.experiment_options.flux_scan_flows:
                self.change_regular_exec_exp_options(exp_name=exp_name, bus=bus_num)
            for exp_name in self.experiment_options.flows:
                self.change_regular_exec_exp_options(exp_name=exp_name, bus=bus_num)

            self._run_flow(
                flows=self.experiment_options.flux_scan_flows,
                physical_units=[bus],
                name=bus,
            )

            flux_points = self.run_options.flux_points_map.get(bus)
            fc_list = self.run_options.fc_scope.get(bus)

            if flux_points:
                groups = [(flux, fc) for flux in flux_points for fc in fc_list]
            else:
                groups = [(None, fc) for fc in fc_list]

            for flux, fc in groups:
                if flux is None:
                    self.change_regular_exec_exp_options(
                        exp_name="ImpaOptiParams",
                        input_data=dict(
                            flux=dict(is_opt=True, init_v=-0.01, bound=[-9.9, 9.9]),
                            fp=dict(is_opt=True, init_v=fc, bound=[-0.3, 0.3]),
                            pp=dict(is_opt=True, init_v=-0.1, bound=[-15, 5]),
                        ),
                        de_opts={"NIND": 8, "MAXGEN": 60, "mutF": 0.6, "XOVR": 0.7},
                    )
                else:
                    self.change_regular_exec_exp_options(
                        exp_name="ImpaOptiParams",
                        input_data=dict(
                            flux=dict(is_opt=True, init_v=flux, bound=[-2, 2]),
                            fp=dict(is_opt=True, init_v=fc, bound=[-0.3, 0.3]),
                            pp=dict(is_opt=True, init_v=-0.1, bound=[-15, 5]),
                        ),
                        de_opts={"NIND": 8, "MAXGEN": 30, "mutF": 0.6, "XOVR": 0.7},
                    )

                pass_bus = self._run_flow(
                    flows=self.experiment_options.flows,
                    physical_units=[bus],
                    name=bus,
                )

                if pass_bus:
                    pass_units.extend(pass_bus)
                    break

        self.bind_pass_units(pass_units)


class BatchBusImpaControl(BatchBusBase):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.set_validator("mode", ["open", "close"])
        options.flows = ["ImpaGain"]
        options.mode = "open"
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.ppt_template.update(
            dict(
                shape=(2, 3),
                split_by_unit=False,
            )
        )
        return options

    def _run_batch(self):
        self.change_regular_exec_exp_options(
            self.experiment_options.flows[0], mode=self.experiment_options.mode
        )

        all_pass_bus = []

        for bus in self.experiment_options.physical_units:
            bus_num = int(bus.split("-")[-1])
            ffp_list = self.bus_data.bus_state[bus].impa_params
            if not ffp_list:
                pyqlog.warning(f"No find {bus} impa params!")
                continue

            count = 0
            while bus and count < 3:
                if not self.change_bus(bus_num):
                    continue
                self.change_regular_exec_exp_options(
                    self.experiment_options.flows[0],
                    bus=bus_num,
                    ffp_list=ffp_list,
                )
                pass_bus = self._run_flow(
                    flows=self.experiment_options.flows,
                    physical_units=[bus],
                    name=bus,
                )
                if pass_bus:
                    pyqlog.info(
                        f"{bus} {self.experiment_options.mode} Count-{count} suc!"
                    )
                    all_pass_bus.extend(pass_bus)
                    break
                count += 1
                pyqlog.warning(
                    f"{bus} {self.experiment_options.mode} Count-{count} fail, retry!"
                )

        self.bind_pass_units(all_pass_bus)


class BatchBusCavityCheck(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.flows = ["CavityCheck"]
        options.affect_next_node = True
        options.chip_type = "102bit-v2.0"
        options.hp_point_label = "high_power"
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.bus_data = QDict()
        options.bad_qubits = set()
        options.ppt_template.update(
            dict(
                shape=(2, 2),
                split_by_unit=False,
            )
        )
        return options

    def _check_options(self):
        super()._check_options()
        if not self.run_options.bus_data:
            raise BatchExperimentError("no find bus data in config path")

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "CavityCheck" == exp_name:
            for unit in record.analysis_data.keys():
                qubit_cavity_map = (
                    record.analysis_data.get(unit).get("result").get("qubit_cavity_map")
                )
                if qubit_cavity_map:
                    for qubit, cavity in qubit_cavity_map.items():
                        obj = self.backend.chip_data.cache_qubit.get(qubit)
                        obj.probe_freq = cavity
                    for qubit in exp.qubits:
                        if qubit.name not in qubit_cavity_map:
                            self.run_options.bad_qubits.add(qubit.name)
                else:
                    for qubit in exp.qubits:
                        self.run_options.bad_qubits.add(qubit.name)

        return record

    def _save_pass_qubits(self, mode: str = "low_power"):
        return update_fc_from_bus_data(
            self.run_options.bus_data,
            self.backend.chip_data,
            self.experiment_options.chip_type,
            mode,
        )

    def _cavity_check(self, bad_bus: List[str]):
        for bus in bad_bus:
            low_power_cavity = self.run_options.bus_data.bus_state[bus].low_power_cavity
            self.change_regular_exec_exp_options(
                self.experiment_options.flows[0], init_cavity=low_power_cavity
            )
            self._run_flow(
                flows=self.experiment_options.flows,
                physical_units=[bus],
                name=bus,
            )

        bad_qubits = list(self.run_options.bad_qubits)
        pass_units = [
            unit
            for unit in self.experiment_options.physical_units
            if unit not in bad_qubits
        ]
        self.bind_pass_units(pass_units)

    def _run_batch(self):
        for mode in ["low_power", "high_power"]:
            time.sleep(1)
            if mode == "high_power":
                self.backend.change_point_label(self.experiment_options.hp_point_label)
            bad_bus = self._save_pass_qubits(mode)
            pyqlog.info(f"{mode} bad bus is {bad_bus}")
            if mode == "low_power" and bad_bus:
                self._cavity_check(bad_bus)
            self.readout_baseband_freq_allocator(self.experiment_options.physical_units)
            if self.experiment_options.save_db is True:
                self.backend.save_chip_data_to_db(
                    self.experiment_options.physical_units
                )

    def _batch_down(self):
        self.backend.change_point_label()
        self.backend.refresh()
        super()._batch_down()


def update_fc_from_bus_data(
    bus_data, chip_data, chip_type: str, mode: str = "low_power"
):
    bus_cavity_freq_sort_dict = BUS_CAVITY_FREQ_SORT_DICT.get(chip_type)
    if bus_cavity_freq_sort_dict is None:
        raise ConfigError(
            msg=f"The value of `chip_type` (an experiment option) is {chip_type}. "
            f"Such chip type doesn't exist in `BUS_CAVITY_FREQ_SORT_DICT`.",
        )
    bad_bus = []
    for bus, bus_state in bus_data.bus_state.items():
        bus_num = int(bus.split("-")[-1])
        bus_cavity = getattr(bus_state, f"{mode}_cavity")
        if bus_cavity and len(bus_cavity) == 6:
            bus_sort = bus_cavity_freq_sort_dict.get(f"BUS{bus_num}")
            qubit_names = sorted(
                list(chip_data.get_qubits_from_bus(bus_num).keys()),
                key=cmp_to_key(sort_bit),
            )
            q_data = bus_state.get("q_data", {}).get(mode)
            for idx, bit in enumerate(qubit_names):
                qubit = chip_data.cache_qubit.get(bit)
                qubit.probe_freq = bus_cavity[int(bus_sort[idx]) - 1]
                pyqlog.log("UPDATE", f"set {bit} probe freq to {qubit.probe_freq} MHz")
                if q_data:
                    cf_q_data = q_data[str(qubit.probe_freq)]
                    quality = cf_q_data["quality"]
                    if "perfect" in quality or "normal" in quality:
                        qubit.q_int = cf_q_data["Qi_dia_corr"] * 1e-4
                        qubit.q_ext = cf_q_data["Qc_dia_corr"] * 1e-4
        else:
            bad_bus.append(bus)
    return bad_bus
