# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 18:03+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/analysis/algorithms/pyQCat.analysis.algorithms.judge_peak_dip.rst:2
msgid "pyQCat.analysis.algorithms.judge\\_peak\\_dip"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip:1
msgid "Evaluate whether the peaks or valleys are more obvious in the data."
msgstr "评估数据中峰更明显还是谷更明显"

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip:5
msgid "Find the mean, maximum and minimum values of the data set respectively;"
msgstr "分别求出数据集的均值，最大值，最小值；"

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip:6
msgid ""
"If the difference between the maximum value and the mean value is greater"
" than the difference between the mean value and the minimum value, the "
"peak data is considered, and the subsequent peak search operation can be "
"performed;"
msgstr ""
"如果最大值和均值之差大于均值和最小值之差，则认为峰数据，后续可进行寻峰操作"

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip:9
msgid ""
"On the contrary, the valley data is considered, and the subsequent valley"
" search operation is performed;"
msgstr "反之，则认为谷数据，后续进行寻谷操作"

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip
msgid "Parameters"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip:13
msgid "data to be evaluated."
msgstr "输入信号"

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip
msgid "Returns"
msgstr ""

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip:16
msgid "`peaks` or `dips`, `peaks` for peak data, `dips` for valley data"
msgstr "`peaks` 或 `dips`，`peaks` 表示峰数据，`dips` 表示谷数据"

#: of pyQCat.analysis.algorithms.find_peak.judge_peak_dip
msgid "Return type"
msgstr ""

#~ msgid "Notes"
#~ msgstr ""

#~ msgid "Find the mean, maximum and minimum values of the data set respectively;"
#~ msgstr ""

#~ msgid ""
#~ "2. If the difference between the "
#~ "maximum value and the mean value "
#~ "is greater than the difference between"
#~ " the mean value and the minimum "
#~ "value, the peak data is considered, "
#~ "and the subsequent peak search operation"
#~ " can be performed; 3. On the "
#~ "contrary, the valley data is considered,"
#~ " and the subsequent valley search "
#~ "operation is performed;"
#~ msgstr ""

