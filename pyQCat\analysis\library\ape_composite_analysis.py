# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/05
# __author:       ssfang

"""
APE Composite Analysis.

"""
import numpy as np

from ...structures import Options, Point
from ...types import QualityDescribe
from ..algorithms.find_peak import find_coincident_point
from ..curve_analysis import CurveAnalysis
from ..quality.base_quality import BaseQuality
from ..specification import ParameterRepr


class APECompositeAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Options:
            diff_threshold (float): Twice fine scan results difference.
        """
        options = super()._default_options()

        options.fine = True
        options.step = None
        options.diff_threshold = 0.2

        options.subplots = (1, 1)
        options.x_label = "Detune [MHz]"
        options.y_label = "P0"
        options.plot_key = "P0"

        options.result_parameters = [
            ParameterRepr(name="detune", repr="detune", unit="MHz")
        ]

        options.figsize = (12, 8)

        return options

    def _extract_result(self, data_key: str):
        """Extract detune value from points, and judge coincident or not.

        Args:
            data_key (str): The basis for selecting data.
        """
        analysis_data = self.analysis_datas[data_key]
        x = analysis_data.x
        y = analysis_data.y

        all_points = []
        points_list = []
        for ape_point in y:
            points_list.append(ape_point.points)
            all_points.extend(ape_point.points)

        self._quality = BaseQuality.instantiate(QualityDescribe.normal)

        if not self.options.fine:
            # rough scan detune
            # get coincidence, and judge coincidence
            times = len(x)
            coincident_point_list = find_coincident_point(all_points, times)

            # bugfix for zyc: 2024/03/05: Detune Coincident Point Check
            def _calculate_gap(point_list):
                new_point_list = []
                use_child_index = []
                for point in point_list:
                    for child_index, child_data in enumerate(
                        list(self.experiment_data._child_data.values())
                    ):
                        if child_index not in use_child_index:
                            x_idx = child_data.x_data.tolist().index(point.x)
                            y_data = child_data.y_data[self.options.y_label][x_idx]
                            if point.y == y_data:
                                if x_idx == 0:
                                    side_x = child_data.x_data[1]
                                    side_y = child_data.y_data[self.options.y_label][1]
                                elif x_idx == len(child_data.x_data) - 1:
                                    side_x = child_data.x_data[-2]
                                    side_y = child_data.y_data[self.options.y_label][-2]
                                else:
                                    ly = child_data.y_data[self.options.y_label][
                                        x_idx - 1
                                    ]
                                    ry = child_data.y_data[self.options.y_label][
                                        x_idx + 1
                                    ]
                                    if abs(y_data - ly) > abs(y_data - ry):
                                        side_x = child_data.x_data[x_idx + 1]
                                        side_y = ry
                                    else:
                                        side_x = child_data.x_data[x_idx - 1]
                                        side_y = ly
                                new_point_list.extend([point, Point(side_x, side_y)])
                                use_child_index.append(child_index)
                                break
                # assert len(new_point_list) == len(points_list) * 2
                order_points = sorted(new_point_list, key=lambda point: point.x)
                x_gap_list = list(
                    map(lambda a, b: b.x - a.x, order_points, order_points[1:])
                )
                y_gap_list = list(
                    map(lambda a, b: abs(b.y - a.y), order_points, order_points[1:])
                )
                return np.mean(np.array(x_gap_list)), np.mean(np.array(y_gap_list))

            best_coincident_point_list = None
            best_x_gap, best_y_gap = 100, 1
            for point_coin in coincident_point_list:
                cur_x_gap, cur_y_gap = _calculate_gap(point_coin)
                if cur_x_gap < best_x_gap:
                    best_coincident_point_list = point_coin
                    best_x_gap = cur_x_gap
                    best_y_gap = cur_y_gap
                elif cur_x_gap == best_x_gap:
                    if cur_y_gap < best_y_gap:
                        best_coincident_point_list = point_coin
                        best_x_gap = cur_x_gap
                        best_y_gap = cur_y_gap

            diff_step = self.options.step * (times - 1)
            coincident_point = self._check_points(best_coincident_point_list, diff_step)
        elif len(all_points) >= 2:
            for child_exp_data in self.experiment_data._child_data.values():
                quality = child_exp_data.metadata.process_meta.get('quality')
                if not quality or not quality.is_pass():
                    self._quality.set_bad()
                    return
            # fine scan detune
            # judge 2 times fine scan detune different
            point_1, point_2, *_ = all_points
            diff_step = self.options.diff_threshold
            coincident_point = self._check_points([point_1, point_2], diff_step)
        else:
            self._quality.set_bad()

        detune = round(coincident_point.x, 3)
        pos_list = [(detune, round(coincident_point.y, 3))]

        if self.results.detune:
            self.results.detune.value = detune
        if self.results.point:
            self.results.point.value = detune

        rp_list = [f"Coincident Point\n{pos}" for pos in pos_list]
        self.drawer.set_options(text_pos=pos_list, text_rp=rp_list)

    def _visualization(self):
        """Plot N times P0."""
        self.drawer.set_options(title=self._description())
        self.drawer.set_options(default_colors=["orangered", "blueviolet", "green"])

        if self.has_child is True:
            x_arr = None
            self.drawer.set_options(raw_data_format="plot")
            for i, n in enumerate(self.experiment_data.x_data):
                child_data = self.experiment_data.child_data(index=i)

                if x_arr is None:
                    x_arr = child_data.x_data
                y_arr = child_data.y_data.get(self.options.plot_key)
                if y_arr is None:
                    y_arr = child_data.y_data.get("Amp")

                color = self.drawer.options.default_colors[
                    i % len(self.drawer.options.default_colors)
                ]

                self.drawer.draw_raw_data(
                    x_data=x_arr, y_data=y_arr, ax_index=0, label=f"N={n}", color=color
                )

            if self.drawer.options.text_pos:
                self.drawer.draw_text(ax_index=0)

        self.drawer.format_canvas()

    def _check_points(self, coincident_point_list, diff_step):
        """Check coincident point

        If the sum of the intervals of the three coincident points
        is greater than 2 times of sweep step, the task coincidence
        point is abnormal, and the default X value uses the mean of
        the three coincidence points.
        """
        x_list = [point.x for point in coincident_point_list]
        coincident_point, *_ = coincident_point_list
        coincident_point.x = np.mean(x_list)

        # set analysis quality
        if x_list[-1] - x_list[0] <= diff_step:
            self._quality = BaseQuality.instantiate(QualityDescribe.perfect)
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)

        return coincident_point

    # shq 2024/04/30
    # fixed bug: AttributeError: 'NoneType' object has no attribute 'options'
    # due to: TopAnalysis
    #     def __reduce__(self):
    #         self.options.curve_drawer = None
    #         return super().__reduce__()
    def __reduce__(self):
        self.options.text_pos = self.options.drawer.options.text_pos
        self.options.text_rp = self.options.drawer.options.text_rp
        return super().__reduce__()
