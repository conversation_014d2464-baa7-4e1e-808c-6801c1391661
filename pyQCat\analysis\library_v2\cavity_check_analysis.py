# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/08/12
# __author:       <PERSON><PERSON><PERSON>

from collections import defaultdict

import numpy as np

from ...structures import QDict
from ..library import CavityAnalysis
from ..quality.base_quality import BaseQuality, QualityDescribe
from ..standard_curve_analysis import ExperimentData, Options, StandardCurveAnalysis


class CavityCheckOnceAnalysis(StandardCurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.x_label = "freq (MHz)"
        options.y_label = "Amp (V)"
        options.result_parameters = ["fc"]
        return options

    def _data_processing(self):
        experiment_data = self.experiment_data
        experiment_data.replace_x_data = {}
        cavity_count = experiment_data.metadata.draw_meta.get("cavity_count")
        fc_list = experiment_data._x_data
        cavity_fc_split_list = np.array_split(fc_list, cavity_count)
        amp_split_list = np.array_split(experiment_data.y_data.get("Amp"), cavity_count)

        child_ana = []
        cavity = []
        for c_index in range(cavity_count):
            fc = cavity_fc_split_list[c_index]
            amp = amp_split_list[c_index]
            epd = ExperimentData(
                experiment_id=str(c_index),
                x_data=fc,
                y_data=QDict(Amp=amp),
            )
            ana = CavityAnalysis(epd)
            ana.set_options(quality_bounds=self.options.quality_bounds)
            ana.run_analysis()
            child_ana.append(ana)
            experiment_data.y_data[f"Cavity-{c_index}"] = amp
            experiment_data.replace_x_data[f"Cavity-{c_index}"] = fc
            if ana.quality.is_pass():
                cavity.append(ana.results.fr.value)
            else:
                cavity.append(None)
        experiment_data.metadata.process_meta["child_analysis"] = child_ana
        experiment_data.y_data.pop("Amp")
        experiment_data.y_data.pop("Phase")
        self.results.fc.value = cavity

    def _visualization(self):
        super()._visualization()
        child_ana = self.experiment_data.metadata.process_meta["child_analysis"]
        for index, ana in enumerate(child_ana):
            analysis_data = ana.analysis_datas["Amp"]
            analysis_x = analysis_data.x
            analysis_fit = analysis_data.fit_data
            if analysis_fit is not None and not np.isnan(analysis_fit.y_fit).all():
                self.drawer.draw_fit_line(
                    x_data=analysis_x,
                    y_data=analysis_fit.y_fit,
                    ax_index=index,
                    color="red",
                )


class CavityCheckAnalysis(StandardCurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.is_plot = True
        options.x_label = "Init Cavity (MHz)"
        options.y_label = []
        options.result_parameters = ["qubit_cavity_map"]
        return options

    def _data_processing(self) -> None:
        """Processes quantum experiment data to map qubits to optimal cavities.

        This method performs the following key steps:
        1. Extracts qubit metadata and initial cavity configurations
        2. Processes frequency measurement data for each qubit-cavity combination
        3. Calculates variance metrics for valid measurements
        4. Assigns optimal cavities to qubits based on variance analysis

        The resulting qubit-cavity mapping is stored in:
        - experiment_data.metadata.process_meta
        - self.results.qubit_cavity_map.value

        Returns:
            None: Results are stored in class attributes
        """
        experiment_data = self.experiment_data

        # Extract metadata
        qubits = experiment_data.metadata.process_meta.get("qubits")
        init_cavity = experiment_data.metadata.process_meta.get("init_cavity")
        num_qubits = len(qubits)
        num_cavities = len(init_cavity)

        # Preprocess flux data (single shared array)
        flux_list = np.array(experiment_data.x_data)
        # Split into num_qubits segments but only use first (per original logic)
        flux_segments = np.array_split(flux_list, num_qubits)
        flux_values = flux_segments[0] if flux_segments else np.array([])

        # Process frequency measurements
        fc_measurements = np.array(experiment_data.y_data.get("fc"))
        # Split measurements per qubit [num_qubits][num_flux_points][num_cavities]
        fc_per_qubit = np.array_split(fc_measurements, num_qubits)

        # Aggregate valid measurements per qubit-cavity pair
        qubit_cavity_data = defaultdict(list)
        for q_idx, qubit in enumerate(qubits):
            # For each flux point index
            for flux_idx in range(len(flux_values)):
                # Get cavity measurements at this flux point
                cavity_measurements = fc_per_qubit[q_idx][flux_idx]
                for cavity_idx, measurement in enumerate(cavity_measurements):
                    # Only store valid measurements (non-zero/None)
                    if measurement:
                        key = f"{qubit}-c{cavity_idx}"
                        qubit_cavity_data[key].append(measurement)

        # Calculate variance metrics
        variance_data = QDict()  # Custom dictionary type
        for q_idx, qubit in enumerate(qubits):
            variance_data[qubit] = []
            for cavity_idx in range(num_cavities):
                key = f"{qubit}-c{cavity_idx}"
                measurements = qubit_cavity_data[key]
                valid_measurements = [m for m in measurements if m]

                # Require >50% valid measurements to calculate variance
                if len(valid_measurements) * 2 > len(measurements):
                    variance_data[qubit].append(np.var(valid_measurements))
                else:
                    variance_data[qubit].append(0.0)  # Insufficient data

        # Assign optimal cavities to qubits
        qubit_cavity_map = {}
        unassigned_qubits = set(qubits)
        search_data = {q: variance_data[q][:] for q in qubits}  # Shallow copy

        while unassigned_qubits:
            # Find maximum variance position for each qubit
            max_variance_positions = {}
            for qubit in unassigned_qubits:
                variances = search_data[qubit]
                max_idx = np.argmax(variances)
                max_variance_positions[qubit] = (max_idx, variances[max_idx])

            # Group qubits by their preferred cavity index
            cavity_assignments = defaultdict(list)
            for qubit, (cavity_idx, _) in max_variance_positions.items():
                cavity_assignments[cavity_idx].append(qubit)

            # Process conflicts and assignments
            for cavity_idx, candidates in cavity_assignments.items():
                if len(candidates) == 1:
                    qubit = candidates[0]
                    variance_val = max_variance_positions[qubit][1]
                    if variance_val > 0:
                        qubit_cavity_map[qubit] = (
                            init_cavity[cavity_idx],
                            variance_val,
                        )
                        # Reset this cavity for remaining qubits
                        for bit in search_data:
                            search_data[bit][cavity_idx] = 0.0
                    else:
                        qubit_cavity_map[qubit] = (None, variance_val)
                    unassigned_qubits.remove(qubit)
                else:
                    # Resolve conflict: select qubit with highest variance
                    best_qubit = max(
                        candidates, key=lambda q: max_variance_positions[q][1]
                    )
                    variance_val = max_variance_positions[best_qubit][1]

                    if variance_val > 0:
                        qubit_cavity_map[best_qubit] = (
                            init_cavity[cavity_idx],
                            variance_val,
                        )
                        # Reset this cavity for remaining qubits
                        for bit in search_data:
                            search_data[bit][cavity_idx] = 0.0
                    else:
                        qubit_cavity_map[best_qubit] = (None, variance_val)
                    unassigned_qubits.remove(best_qubit)

        # Update experiment data structure
        experiment_data._x_data = init_cavity
        experiment_data._y_data = variance_data
        experiment_data._child_data.clear()
        experiment_data.metadata.process_meta["qubit_cavity_map"] = qubit_cavity_map

        # Store simplified results (cavity only)
        self.results.qubit_cavity_map.value = {
            qubit: cavity for qubit, (cavity, _) in qubit_cavity_map.items() if cavity
        }

    def _evaluate_quality(self):
        if self.results.qubit_cavity_map.value:
            self._quality = BaseQuality.instantiate(QualityDescribe.normal)
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)

    def _visualization(self):
        super()._visualization()

        qubits = self.experiment_data.metadata.process_meta.get("qubits")
        qubit_cavity_map = self.experiment_data.metadata.process_meta.get(
            "qubit_cavity_map"
        )
        for idx, bit in enumerate(qubits):
            if qubit_cavity_map[bit][0]:
                cavity, var = qubit_cavity_map[bit]
                self.drawer.draw_scatter_point(
                    x_data=[cavity],
                    y_data=[var],
                    ax_index=idx,
                    marker="*",
                    c="Green",
                    s=500,
                )
