# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/18
# __author:       <PERSON><PERSON><PERSON>

import numpy as np
from pyQCat.experiments.batch_experiment import (
    BatchExperiment,
    BatchPhysicalUnitType,
    pyqlog,
    List,
    ParallelExperiment,
    BaseExperiment,
)


class BatchZZShift(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.qubit_check_flows = ["SingleShot"]
        options.flows = ["ZZShiftSpinEcho", "SpinEchoZZ"]
        options.physical_unit_type = BatchPhysicalUnitType.PAIR
        options.fosc_limit = 0.02
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.zc_point_map = {}
        options.pair_qubit_map = {}
        options.fidelity_record = {}
        options.fringe = None
        return options

    def _batch_up(self):
        super()._batch_up()
        self.record_meta.execute_meta.result.physical_units = (
            self.experiment_options.physical_units
        )
        for pair_name in self.experiment_options.physical_units:
            pair_obj = self.backend.chip_data.get_physical_unit(pair_name)
            self.run_options.pair_qubit_map[pair_name] = [pair_obj.qh, pair_obj.ql]

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "ZZShiftSpinEcho" == exp_name:
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    point = record.analysis_data.get(unit).get("result").get("point")
                    self.run_options.zc_point_map[unit] = point
                    self.change_parallel_exec_exp_options(
                        exp_name="SpinEchoZZ", unit=unit, z_amp=point
                    )

        elif "SpinEchoZZ" == exp_name:
            if not self.run_options.fringe and exp:
                if isinstance(exp, ParallelExperiment):
                    self.run_options.fringe = exp.first_exp.get_option_value("fringe")
                elif isinstance(exp, BaseExperiment):
                    self.run_options.fringe = exp.get_option_value("fringe")
                else:
                    return

            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    freq = record.analysis_data.get(unit).get("result").get("freq")
                    gap = abs(freq - self.run_options.fringe)
                    if gap > self.experiment_options.fosc_limit:
                        pyqlog.warning(
                            f"{unit} osc freq {freq}MHz > {self.experiment_options.fosc_limit}, remove point"
                        )
                        self.run_options.zc_point_map.pop(unit, None)

        elif "SingleShot" == exp_name:
            for unit in record.analysis_data.keys():
                fidelity = (
                    record.analysis_data.get(unit)
                    .get("result", {})
                    .get("dcm", {})
                    .get("fidelity")
                )
                if fidelity:
                    self.run_options.fidelity_record[unit] = np.mean(np.array(fidelity))

        return record

    def _check_best_readout_qubit(self, working_units: List[str], describe: str):
        pair_qubit_map = self.run_options.pair_qubit_map
        qubits = []

        for unit in working_units:
            qubits.extend(pair_qubit_map[unit])

        if qubits:
            self._run_flow(
                flows=self.experiment_options.qubit_check_flows,
                physical_units=qubits,
                name=f"{describe} Check best readout qubit",
            )

            for unit in working_units:
                qh, ql = pair_qubit_map[unit]
                f_qh, f_ql = (
                    self.run_options.fidelity_record.get(qh, 0),
                    self.run_options.fidelity_record.get(ql, 0),
                )
                readout_type = "qh-01" if f_qh > f_ql else "ql-01"
                pyqlog.log(
                    "FLOW",
                    f"{unit} qh({qh})({f_qh}) ql({ql})({f_ql}), set readout type {readout_type}",
                )
                for exp in self.experiment_options.flows:
                    self.params_manager.update_exp_context_options(
                        exp_name=exp, readout_type=readout_type
                    )
        return working_units

    def _run_batch(self):
        group_map = self.parallel_allocator_for_cgc(
            self.experiment_options.physical_units
        )

        suc_units = []
        for group_name, group in group_map.items():
            self._check_best_readout_qubit(group, group_name)

            pass_units = self._run_flow(
                self.experiment_options.flows, group, name=f"{group_name} "
            )
            suc_units.extend(pass_units)

        self.bind_pass_units(suc_units)

        if self.record_meta.execute_meta.result.fail_units:
            for unit in self.record_meta.execute_meta.result.fail_units:
                self.run_options.zc_point_map.pop(unit, None)

        self._save_data_to_json(self.run_options.zc_point_map, "zc_point")
