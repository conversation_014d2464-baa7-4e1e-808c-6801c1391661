# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/acquisition/pyQCat.acquisition.DataAcquisition.rst:2
msgid "pyQCat.acquisition.DataAcquisition"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: ../../source/stubs/acquisition/pyQCat.acquisition.DataAcquisition.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/acquisition/pyQCat.acquisition.DataAcquisition.rst:26:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.acquisition.DataAcquisition.__init__>`\\ "
"\\(id\\_\\[\\, data\\_type\\, discriminator\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/acquisition/pyQCat.acquisition.DataAcquisition.rst:26:<autosummary>:1
msgid ""
":py:obj:`excute_loop <pyQCat.acquisition.DataAcquisition.excute_loop>`\\ "
"\\(\\[func\\, args\\, analyze\\_one\\]\\)"
msgstr ""

#: ../../source/stubs/acquisition/pyQCat.acquisition.DataAcquisition.rst:26:<autosummary>:1
#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop:1
msgid "Excute measurement by looping through multiple points."
msgstr ""

#: ../../source/stubs/acquisition/pyQCat.acquisition.DataAcquisition.rst:26:<autosummary>:1
msgid ""
":py:obj:`get_experiment "
"<pyQCat.acquisition.DataAcquisition.get_experiment>`\\ \\(\\[field\\]\\)"
msgstr ""

#: ../../source/stubs/acquisition/pyQCat.acquisition.DataAcquisition.rst:26:<autosummary>:1
msgid ""
":py:obj:`loop_control "
"<pyQCat.acquisition.DataAcquisition.loop_control>`\\ "
"\\(sample\\_channels\\, loop\\)"
msgstr ""

#: ../../source/stubs/acquisition/pyQCat.acquisition.DataAcquisition.rst:26:<autosummary>:1
#: of pyQCat.acquisition.acquisition.DataAcquisition.loop_control:1
msgid "The default method for getting data for each loop."
msgstr ""

#: ../../source/stubs/acquisition/pyQCat.acquisition.DataAcquisition.rst:26:<autosummary>:1
msgid ""
":py:obj:`save_plot_data "
"<pyQCat.acquisition.DataAcquisition.save_plot_data>`\\ \\(x\\_list\\, "
"y1\\_list\\, y2\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/acquisition/pyQCat.acquisition.DataAcquisition.rst:26:<autosummary>:1
#: of pyQCat.acquisition.acquisition.DataAcquisition.save_plot_data:1
msgid "Save temp data to temp file use for gui dynamic plot."
msgstr ""

#: ../../source/stubs/acquisition/pyQCat.acquisition.DataAcquisition.rst:26:<autosummary>:1
msgid ""
":py:obj:`slice_result "
"<pyQCat.acquisition.DataAcquisition.slice_result>`\\ "
"\\(measure\\_result\\)"
msgstr ""

#: ../../source/stubs/acquisition/pyQCat.acquisition.DataAcquisition.rst:28
msgid "Attributes"
msgstr ""

#: of
#: pyQCat.acquisition.acquisition.DataAcquisition._start_info:1:<autosummary>:1
msgid ":py:obj:`I <pyQCat.acquisition.DataAcquisition.I>`\\"
msgstr ""

#: of
#: pyQCat.acquisition.acquisition.DataAcquisition._start_info:1:<autosummary>:1
msgid ":py:obj:`P0 <pyQCat.acquisition.DataAcquisition.P0>`\\"
msgstr ""

#: of
#: pyQCat.acquisition.acquisition.DataAcquisition._start_info:1:<autosummary>:1
msgid ":py:obj:`P1 <pyQCat.acquisition.DataAcquisition.P1>`\\"
msgstr ""

#: of
#: pyQCat.acquisition.acquisition.DataAcquisition._start_info:1:<autosummary>:1
msgid ":py:obj:`Q <pyQCat.acquisition.DataAcquisition.Q>`\\"
msgstr ""

#: of
#: pyQCat.acquisition.acquisition.DataAcquisition._start_info:1:<autosummary>:1
msgid ":py:obj:`amp <pyQCat.acquisition.DataAcquisition.amp>`\\"
msgstr ""

#: of
#: pyQCat.acquisition.acquisition.DataAcquisition._start_info:1:<autosummary>:1
msgid ":py:obj:`phase <pyQCat.acquisition.DataAcquisition.phase>`\\"
msgstr ""

#: of
#: pyQCat.acquisition.acquisition.DataAcquisition._start_info:1:<autosummary>:1
msgid ""
":py:obj:`probability_list "
"<pyQCat.acquisition.DataAcquisition.probability_list>`\\"
msgstr ""

#: of
#: pyQCat.acquisition.acquisition.DataAcquisition._start_info:1:<autosummary>:1
msgid ":py:obj:`status <pyQCat.acquisition.DataAcquisition.status>`\\"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition._start_info:1
msgid "Print log when experiment start."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition._stop_info:1
msgid "Print log when experiment end."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop
#: pyQCat.acquisition.acquisition.DataAcquisition.loop_control
msgid "Parameters"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.loop_control:4
msgid "readout channel number."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.loop_control:6
msgid "current loop count."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop
#: pyQCat.acquisition.acquisition.DataAcquisition.loop_control
msgid "Returns"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.loop_control:8
msgid ""
"measure result and status. status 1: run experiment with Amp/Phase type "
"data. status 2: finished. status 3: run experiment failed. status 4: run "
"experiment with I/Q type data."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop:3
msgid ""
"A callback function that, if defined, is used for a single circular fetch"
" of data and is used as an alternate interface for special cases. "
"Attention, this function must return the result and state. ex. >>>def "
"func(*args):     >>>    # do something     >>>    return res, status"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop:3
msgid ""
"A callback function that, if defined, is used for a single circular fetch"
" of data and is used as an alternate interface for special cases. "
"Attention, this function must return the result and state. ex. >>>def "
"func(*args):"
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop:9
msgid "The argument to the callback function `func`."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop:10
msgid "A callback function of the processing function to one loop."
msgstr ""

#: of pyQCat.acquisition.acquisition.DataAcquisition.excute_loop:12
msgid "None"
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.acquisition.DataAcquisition.__init__>`\\ \\(id\\_\\[\\,"
#~ " data\\_type\\, discriminator\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`excute_loop "
#~ "<pyQCat.acquisition.DataAcquisition.excute_loop>`\\ "
#~ "\\(\\[func\\, args\\, analyze\\_one\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_experiment "
#~ "<pyQCat.acquisition.DataAcquisition.get_experiment>`\\ "
#~ "\\(\\[field\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`loop_control "
#~ "<pyQCat.acquisition.DataAcquisition.loop_control>`\\ "
#~ "\\(sample\\_channels\\, loop\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`save_plot_data "
#~ "<pyQCat.acquisition.DataAcquisition.save_plot_data>`\\ "
#~ "\\(x\\_list\\, y1\\_list\\, y2\\_list\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`slice_result "
#~ "<pyQCat.acquisition.DataAcquisition.slice_result>`\\ "
#~ "\\(measure\\_result\\)"
#~ msgstr ""

#~ msgid ":py:obj:`I <pyQCat.acquisition.DataAcquisition.I>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`P0 <pyQCat.acquisition.DataAcquisition.P0>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`P1 <pyQCat.acquisition.DataAcquisition.P1>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`Q <pyQCat.acquisition.DataAcquisition.Q>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`amp <pyQCat.acquisition.DataAcquisition.amp>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`phase <pyQCat.acquisition.DataAcquisition.phase>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`probability_list "
#~ "<pyQCat.acquisition.DataAcquisition.probability_list>`\\"
#~ msgstr ""

#~ msgid ":py:obj:`status <pyQCat.acquisition.DataAcquisition.status>`\\"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.acquisition.DataAcquisition.__init__>`\\"
#~ " \\(id\\_\\[\\, data\\_type\\, discriminator\\, "
#~ "...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`excute_loop "
#~ "<pyQCat.acquisition.DataAcquisition.excute_loop>`\\ "
#~ "\\(\\[func\\, args\\, analyze\\_one\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_experiment "
#~ "<pyQCat.acquisition.DataAcquisition.get_experiment>`\\ "
#~ "\\(\\[field\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`loop_control "
#~ "<pyQCat.acquisition.DataAcquisition.loop_control>`\\ "
#~ "\\(sample\\_channels\\, loop\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`save_plot_data "
#~ "<pyQCat.acquisition.DataAcquisition.save_plot_data>`\\ "
#~ "\\(x\\_list\\, y1\\_list\\, y2\\_list\\, ...\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`slice_result "
#~ "<pyQCat.acquisition.DataAcquisition.slice_result>`\\ "
#~ "\\(measure\\_result\\)"
#~ msgstr ""

#~ msgid ":obj:`I <pyQCat.acquisition.DataAcquisition.I>`\\"
#~ msgstr ""

#~ msgid ":obj:`P0 <pyQCat.acquisition.DataAcquisition.P0>`\\"
#~ msgstr ""

#~ msgid ":obj:`P1 <pyQCat.acquisition.DataAcquisition.P1>`\\"
#~ msgstr ""

#~ msgid ":obj:`Q <pyQCat.acquisition.DataAcquisition.Q>`\\"
#~ msgstr ""

#~ msgid ":obj:`amp <pyQCat.acquisition.DataAcquisition.amp>`\\"
#~ msgstr ""

#~ msgid ":obj:`phase <pyQCat.acquisition.DataAcquisition.phase>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`probability_list "
#~ "<pyQCat.acquisition.DataAcquisition.probability_list>`\\"
#~ msgstr ""

#~ msgid ":obj:`status <pyQCat.acquisition.DataAcquisition.status>`\\"
#~ msgstr ""

