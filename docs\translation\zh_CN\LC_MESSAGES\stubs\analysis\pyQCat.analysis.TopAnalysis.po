# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:2
msgid "pyQCat.analysis.TopAnalysis"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis:1
msgid "Bases: :py:class:`~abc.ABC`"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis:1
msgid "Abstract base class for analyzing Experiment data."
msgstr "实验数据分析的抽象基类。"

#: of pyQCat.analysis.top_analysis.TopAnalysis:3
msgid ""
"The data produced by experiments are analyzed with subclasses of "
"TopAnalysis. The analysis is typically run after the data has been "
"gathered by the experiment. For example, an analysis may perform some "
"data processing of the measured data and a fit to a function to extract a"
" parameter."
msgstr "实验数据用TopAnalysis的子类进行分析。分析通常是在实验收集到数据之后进行的"

#: of pyQCat.analysis.top_analysis.TopAnalysis:9
msgid ""
"Any configurable values should be specified in the `_default_options` "
"class method. These values can be overriden by a user by calling the "
"`set_options` method."
msgstr "任何可配置的值都应该在 `_default_options` 类方法中指定"

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:1
msgid "Initialize the analysis object."
msgstr "初始化数据分析对象。"

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis
#: pyQCat.analysis.top_analysis.TopAnalysis.set_options
msgid "Parameters"
msgstr "参数"

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:4
msgid "pyQCat Experiments Data container class."
msgstr "用于描述实验数据结构的类。"

#: of pyQCat.analysis.top_analysis.TopAnalysis.__init__:7
msgid "Whether the experiment contains sub-experiments."
msgstr "是否具有子实验的标志位，默认为False。"

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:13
msgid "Methods"
msgstr "方法"

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.analysis.TopAnalysis.__init__>`\\ "
"\\(experiment\\_data\\[\\, has\\_child\\]\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`from_sub_analysis "
"<pyQCat.analysis.TopAnalysis.from_sub_analysis>`\\ \\(x\\_data\\, "
"sub\\_analysis\\_list\\, ...\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1
msgid "This interface is used for composite experiment."
msgstr "该接口用于构建复合实验的分析"

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`run_analysis <pyQCat.analysis.TopAnalysis.run_analysis>`\\ "
"\\(\\*args\\, \\*\\*kwargs\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.run_analysis:1
msgid "Run analysis on experiment data."
msgstr "执行对实验数据的分析"

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:25:<autosummary>:1
msgid ""
":py:obj:`set_options <pyQCat.analysis.TopAnalysis.set_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.set_options:1
msgid "Set the analysis options."
msgstr "设置数据分析的配置选项"

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:25:<autosummary>:1
msgid ":py:obj:`show_results <pyQCat.analysis.TopAnalysis.show_results>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:25:<autosummary>:1
#: of pyQCat.analysis.top_analysis.TopAnalysis.show_results:1
msgid "Show analysis results"
msgstr ""

#: ../../source/stubs/analysis/pyQCat.analysis.TopAnalysis.rst:27
msgid "Attributes"
msgstr "属性"

#: of
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1:<autosummary>:1
msgid ":py:obj:`analysis_datas <pyQCat.analysis.TopAnalysis.analysis_datas>`\\"
msgstr ""

#: of pyQCat.analysis.TopAnalysis.analysis_datas:1
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1:<autosummary>:1
msgid "Get the analysis datas."
msgstr "获取分析数据的数据结构，默认为QDict类型"

#: of
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1:<autosummary>:1
msgid ":py:obj:`experiment_data <pyQCat.analysis.TopAnalysis.experiment_data>`\\"
msgstr ""

#: of pyQCat.analysis.TopAnalysis.experiment_data:1
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1:<autosummary>:1
msgid "Return the experiment data for :meth:`run` method."
msgstr "返回 :meth:`run` 方法的实验数据结构"

#: of
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1:<autosummary>:1
msgid ":py:obj:`has_child <pyQCat.analysis.TopAnalysis.has_child>`\\"
msgstr ""

#: of pyQCat.analysis.TopAnalysis.has_child:1
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1:<autosummary>:1
msgid "Return the analysis has_child value."
msgstr "返回has_child的值"

#: of
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1:<autosummary>:1
msgid ":py:obj:`options <pyQCat.analysis.TopAnalysis.options>`\\"
msgstr ""

#: of pyQCat.analysis.TopAnalysis.options:1
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1:<autosummary>:1
msgid "Return the analysis options for :meth:`run` method."
msgstr "返回数据分析的选项参数"

#: of
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1:<autosummary>:1
msgid ":py:obj:`quality <pyQCat.analysis.TopAnalysis.quality>`\\"
msgstr ""

#: of pyQCat.analysis.TopAnalysis.quality:1
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1:<autosummary>:1
msgid "Get the analysis data quality."
msgstr "获取分析数据的质量评估结果"

#: of
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1:<autosummary>:1
msgid ":py:obj:`results <pyQCat.analysis.TopAnalysis.results>`\\"
msgstr ""

#: of pyQCat.analysis.TopAnalysis.results:1
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:1:<autosummary>:1
msgid "Get the result datas."
msgstr "获取结果"

#: of pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:4
msgid "experiment id"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:6
msgid "The value of the x data."
msgstr "X 数据的值集合（实验自变量，通常为扫描的对象）"

#: of pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:9
msgid "sub-analysis object."
msgstr "一系列子分析对象的列表"

#: of pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:12
msgid ""
"Some information provided by experiment which maybe used for detailed "
"analysis."
msgstr "实验提供的一些信息，可用于详细的分析。"

#: of pyQCat.analysis.TopAnalysis.analysis_datas
#: pyQCat.analysis.TopAnalysis.experiment_data
#: pyQCat.analysis.TopAnalysis.has_child pyQCat.analysis.TopAnalysis.options
#: pyQCat.analysis.TopAnalysis.results
#: pyQCat.analysis.top_analysis.TopAnalysis._create_analysis_data
#: pyQCat.analysis.top_analysis.TopAnalysis._create_analysis_result
#: pyQCat.analysis.top_analysis.TopAnalysis._default_options
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis
msgid "Return type"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:16
msgid ":py:class:`~pyQCat.analysis.top_analysis.TopAnalysis`"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis._create_analysis_result
#: pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis
msgid "Returns"
msgstr "返回"

#: of pyQCat.analysis.top_analysis.TopAnalysis.from_sub_analysis:17
msgid "A TopAnalysis object that contains the sub-analysis results."
msgstr "一个TopAnalysis对象，包含了所有子实验数据的信息。"

#: of pyQCat.analysis.TopAnalysis.has_child:3
msgid ":py:class:`bool`"
msgstr ""

#: of pyQCat.analysis.TopAnalysis.options:3
#: pyQCat.analysis.top_analysis.TopAnalysis._default_options:4
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of pyQCat.analysis.TopAnalysis.experiment_data:3
msgid ":py:class:`~pyQCat.structures.ExperimentData`"
msgstr ""

#: of pyQCat.analysis.TopAnalysis.analysis_datas:3
#: pyQCat.analysis.TopAnalysis.results:3
#: pyQCat.analysis.top_analysis.TopAnalysis._create_analysis_data:4
#: pyQCat.analysis.top_analysis.TopAnalysis._create_analysis_result:4
msgid ":py:class:`~pyQCat.structures.QDict`"
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis._default_options:1
msgid "Default analysis options common to all analyzes."
msgstr ""

#: of pyQCat.analysis.top_analysis.TopAnalysis.set_options:3
msgid "The fields to update the options"
msgstr "更新选项的字段。"

#: of pyQCat.analysis.top_analysis.TopAnalysis._create_analysis_data:1
msgid "Create Analysis data provided for detailed analyze."
msgstr "抽象方法，子类必须实现。创建分析数据，提供详细分析。"

#: of pyQCat.analysis.top_analysis.TopAnalysis._create_analysis_result:1
msgid ""
"Create Analysis result data structure to save analysis results. The "
"AnalysisResult object will get more attributes after analysis."
msgstr "抽象方法，子类必须实现。创建分析结果数据结构，保存分析结果。 **AnalysisResult** 对象在执行完分析过程后将获得更多属性。"

#: of pyQCat.analysis.top_analysis.TopAnalysis._create_analysis_result:5
msgid ""
"A QDict object, key represents result type and value is AnalysisResult "
"object."
msgstr ""

#~ msgid "Bases: :class:`abc.ABC`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.analysis.TopAnalysis.__init__>`\\ "
#~ "\\(experiment\\_data\\[\\, has\\_child\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_sub_analysis "
#~ "<pyQCat.analysis.TopAnalysis.from_sub_analysis>`\\ "
#~ "\\(x\\_data\\, sub\\_analysis\\_list\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_analysis <pyQCat.analysis.TopAnalysis.run_analysis>`\\"
#~ " \\(\\*args\\, \\*\\*kwargs\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_options <pyQCat.analysis.TopAnalysis.set_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis_datas <pyQCat.analysis.TopAnalysis.analysis_datas>`\\"
#~ msgstr ""

#~ msgid ":obj:`experiment_data <pyQCat.analysis.TopAnalysis.experiment_data>`\\"
#~ msgstr ""

#~ msgid ":obj:`has_child <pyQCat.analysis.TopAnalysis.has_child>`\\"
#~ msgstr ""

#~ msgid ":obj:`options <pyQCat.analysis.TopAnalysis.options>`\\"
#~ msgstr ""

#~ msgid ":obj:`quality <pyQCat.analysis.TopAnalysis.quality>`\\"
#~ msgstr ""

#~ msgid ":obj:`results <pyQCat.analysis.TopAnalysis.results>`\\"
#~ msgstr ""

