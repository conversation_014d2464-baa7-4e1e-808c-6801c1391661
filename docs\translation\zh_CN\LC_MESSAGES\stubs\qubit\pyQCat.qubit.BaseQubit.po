# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:2
msgid "pyQCat.qubit.BaseQubit"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit:1
msgid "BaseQubit class, depends on different chip structure."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit:4
msgid "BaseQubit use row-major ordering:"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit:4
msgid "BaseQubit(0, 0) < BaseQubit(0, 1) < BaseQubit(1, 0) < BaseQubit(1, 1)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.BaseQubit.__init__:1
msgid "Create a new BaseQubit."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.__init__
#: pyQCat.qubit.qubit.BaseQubit.ac_instead_dc
#: pyQCat.qubit.qubit.BaseQubit.from_dict
#: pyQCat.qubit.qubit.BaseQubit.from_file pyQCat.qubit.qubit.BaseQubit.to_file
msgid "Parameters"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.__init__:4
msgid "It is recommended to use q0 for Qubit and c0 for Coupler."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid ":py:obj:`__init__ <pyQCat.qubit.BaseQubit.__init__>`\\ \\(name\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid ""
":py:obj:`ac_instead_dc <pyQCat.qubit.BaseQubit.ac_instead_dc>`\\ "
"\\(value\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.BaseQubit.ac_instead_dc:1
msgid "Use AWG instead DC module to provide voltage."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid ":py:obj:`anno <pyQCat.qubit.BaseQubit.anno>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.BaseQubit.anno:1
msgid "Normal used to describe."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid ":py:obj:`from_dict <pyQCat.qubit.BaseQubit.from_dict>`\\ \\(data\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.BaseQubit.from_dict:1
msgid "Load qubit information from dict."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid ""
":py:obj:`from_file <pyQCat.qubit.BaseQubit.from_file>`\\ \\(file\\[\\, "
"fmt\\]\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.BaseQubit.from_file:1
msgid "Load qubit information from file."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid ":py:obj:`is_adjacent <pyQCat.qubit.BaseQubit.is_adjacent>`\\ \\(other\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.BaseQubit.is_adjacent:1
msgid "Determines if two qubits are adjacent qubits."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid ""
":py:obj:`put_sweet_point <pyQCat.qubit.BaseQubit.put_sweet_point>`\\ "
"\\(\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.BaseQubit.put_sweet_point:1
msgid "Put Qubit/Coupler on sweet point."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid ":py:obj:`reset <pyQCat.qubit.BaseQubit.reset>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.BaseQubit.reset:1
msgid "Reset fields."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid ":py:obj:`save_database <pyQCat.qubit.BaseQubit.save_database>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid "Save database hook."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid ""
":py:obj:`set_coords <pyQCat.qubit.BaseQubit.set_coords>`\\ \\(row\\, "
"col\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.BaseQubit.set_coords:1
msgid "Set coords."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid ":py:obj:`to_dict <pyQCat.qubit.BaseQubit.to_dict>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.BaseQubit.to_dict:1
msgid "Convert object to dict structure."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1
msgid ""
":py:obj:`to_file <pyQCat.qubit.BaseQubit.to_file>`\\ "
"\\(\\[export\\_path\\, fmt\\]\\)"
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:32:<autosummary>:1 of
#: pyQCat.qubit.qubit.BaseQubit.to_file:1
msgid "Export the object to a `yaml` or `json` file."
msgstr ""

#: ../../source/stubs/qubit/pyQCat.qubit.BaseQubit.rst:34
msgid "Attributes"
msgstr ""

#: of pyQCat.qubit.BaseQubit.row:1:<autosummary>:1
msgid ":py:obj:`col <pyQCat.qubit.BaseQubit.col>`\\"
msgstr ""

#: of pyQCat.qubit.BaseQubit.col:1 pyQCat.qubit.BaseQubit.row:1:<autosummary>:1
msgid "coordinate col."
msgstr ""

#: of pyQCat.qubit.BaseQubit.row:1:<autosummary>:1
msgid ":py:obj:`composite_attrs <pyQCat.qubit.BaseQubit.composite_attrs>`\\"
msgstr ""

#: of pyQCat.qubit.BaseQubit.row:1:<autosummary>:1
msgid ":py:obj:`options_attrs <pyQCat.qubit.BaseQubit.options_attrs>`\\"
msgstr ""

#: of pyQCat.qubit.BaseQubit.row:1:<autosummary>:1
msgid ":py:obj:`row <pyQCat.qubit.BaseQubit.row>`\\"
msgstr ""

#: of pyQCat.qubit.BaseQubit.row:1 pyQCat.qubit.BaseQubit.row:1:<autosummary>:1
msgid "coordinate row."
msgstr ""

#: of pyQCat.qubit.BaseQubit.row:1:<autosummary>:1
msgid ":py:obj:`unit_map <pyQCat.qubit.BaseQubit.unit_map>`\\"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit._comparison_key
#: pyQCat.qubit.qubit.BaseQubit.from_dict
#: pyQCat.qubit.qubit.BaseQubit.from_file
#: pyQCat.qubit.qubit.BaseQubit.is_adjacent
#: pyQCat.qubit.qubit.BaseQubit.to_dict
msgid "Return type"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.is_adjacent:4
msgid ":py:class:`bool`"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit._comparison_key:1
msgid "key to judge two qubits equal."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit._comparison_key:4
msgid ":py:data:`~typing.Any`"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.from_dict:4
msgid "Dict of qubit information."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.from_dict:8
#: pyQCat.qubit.qubit.BaseQubit.from_file:11
msgid ":py:class:`~pyQCat.qubit.qubit.BaseQubit`"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.from_file:4
msgid "From file path name."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.from_file:7
msgid "From file type, support `yaml` and `json`."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.to_dict
msgid "Returns"
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.to_dict:3
msgid "Target data."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.to_file:4
msgid "Export file path, default `conf/bit_data'."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.to_file:7
msgid "Export file type, support `yaml` and `json`."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.save_database:1
msgid "Save database hook. Push the information to database."
msgstr ""

#: of pyQCat.qubit.qubit.BaseQubit.ac_instead_dc:4
msgid "Value to provide static work point."
msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ <pyQCat.qubit.BaseQubit.__init__>`\\ "
#~ "\\(name\\[\\, username\\, label\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`anno <pyQCat.qubit.BaseQubit.anno>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`export <pyQCat.qubit.BaseQubit.export>`\\ "
#~ "\\(\\[export\\_path\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`load <pyQCat.qubit.BaseQubit.load>`\\ \\(\\[json\\_file\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`load_by_time <pyQCat.qubit.BaseQubit.load_by_time>`\\"
#~ " \\(create\\_time\\[\\, bit\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`log_info <pyQCat.qubit.BaseQubit.log_info>`\\ "
#~ "\\(msg\\[\\, log\\_path\\]\\)"
#~ msgstr ""

#~ msgid ":py:obj:`reset <pyQCat.qubit.BaseQubit.reset>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`save_by_yaml <pyQCat.qubit.BaseQubit.save_by_yaml>`\\"
#~ " \\(yaml\\_path\\)"
#~ msgstr ""

#~ msgid ":py:obj:`save_database <pyQCat.qubit.BaseQubit.save_database>`\\ \\(\\)"
#~ msgstr ""

#~ msgid "Mark description."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.qubit.BaseQubit.__init__>`\\ "
#~ "\\(name\\[\\, username\\, label\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`export <pyQCat.qubit.BaseQubit.export>`\\ "
#~ "\\(\\[export\\_path\\]\\)"
#~ msgstr ""

#~ msgid "Export qubit info to a yaml file."
#~ msgstr ""

#~ msgid ":obj:`load <pyQCat.qubit.BaseQubit.load>`\\ \\(\\[json\\_file\\]\\)"
#~ msgstr ""

#~ msgid "Load qubit information from json file or database."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`load_by_time <pyQCat.qubit.BaseQubit.load_by_time>`\\ "
#~ "\\(create\\_time\\[\\, bit\\]\\)"
#~ msgstr ""

#~ msgid "According to time get Qubit attribute value from Store."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`log_info <pyQCat.qubit.BaseQubit.log_info>`\\ "
#~ "\\(msg\\[\\, log\\_path\\]\\)"
#~ msgstr ""

#~ msgid "Log message to file, write qubit running state and some info."
#~ msgstr ""

#~ msgid "Update fields."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`save_by_yaml <pyQCat.qubit.BaseQubit.save_by_yaml>`\\ "
#~ "\\(yaml\\_path\\)"
#~ msgstr ""

#~ msgid "Save {}.yaml to MongoDB Store."
#~ msgstr ""

#~ msgid ""
#~ "Compare the current attribute value with"
#~ " the attribute value in the database."
#~ msgstr ""

#~ msgid "json file path. Defaults to None."
#~ msgstr ""

#~ msgid "str or datetime.datetime, Examples, 2021-03-26 15:23:38.442"
#~ msgstr ""

#~ msgid "qubit/coupler number, default self.bit"
#~ msgstr ""

#~ msgid "export qubit data path, default `config/qubit_data'"
#~ msgstr ""

#~ msgid "message"
#~ msgstr ""

#~ msgid "log path"
#~ msgstr ""

#~ msgid ""
#~ "Compare the current attribute value with"
#~ " the attribute value in the database."
#~ " If it is inconsistent, modify the"
#~ " corresponding attribute value in the "
#~ "database and save it to the "
#~ "database again."
#~ msgstr ""

#~ msgid "{}.yaml path, like `{path}/config/q0.yaml`"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid "Mark which sample name, relate to the parameters."
#~ msgstr ""

#~ msgid "Belong to user's username."
#~ msgstr ""

#~ msgid "Mark point description label."
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ <pyQCat.qubit.BaseQubit.__init__>`\\ "
#~ "\\(name\\[\\, sample\\, username\\, "
#~ "point\\_label\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`anno <pyQCat.qubit.BaseQubit.anno>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_file <pyQCat.qubit.BaseQubit.from_file>`\\ "
#~ "\\(file\\[\\, fmt\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`load <pyQCat.qubit.BaseQubit.load>`\\ \\(data\\)"
#~ msgstr ""

#~ msgid ":obj:`reset <pyQCat.qubit.BaseQubit.reset>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`save_database <pyQCat.qubit.BaseQubit.save_database>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ":obj:`to_dict <pyQCat.qubit.BaseQubit.to_dict>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`to_file <pyQCat.qubit.BaseQubit.to_file>`\\ "
#~ "\\(\\[export\\_path\\, fmt\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`composite_attrs <pyQCat.qubit.BaseQubit.composite_attrs>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Save database hook. Push the information"
#~ " to the process queue, and then "
#~ "save it by other processes."
#~ msgstr ""

