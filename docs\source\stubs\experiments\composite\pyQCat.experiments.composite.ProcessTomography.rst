﻿pyQCat.experiments.composite.ProcessTomography
==============================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: ProcessTomography

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~ProcessTomography.__init__
      ~ProcessTomography.component_experiment
      ~ProcessTomography.from_experiment_context
      ~ProcessTomography.get_qubit_str
      ~ProcessTomography.options_table
      ~ProcessTomography.run
      ~ProcessTomography.set_analysis_options
      ~ProcessTomography.set_experiment_options
      ~ProcessTomography.set_parent_file
      ~ProcessTomography.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~ProcessTomography.analysis
      ~ProcessTomography.analysis_options
      ~ProcessTomography.child_experiment
      ~ProcessTomography.experiment_options
      ~ProcessTomography.run_options
   
   