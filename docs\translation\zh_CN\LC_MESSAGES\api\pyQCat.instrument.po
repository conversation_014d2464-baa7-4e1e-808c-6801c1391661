# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:58+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/api/pyQCat.instrument.rst:2
msgid "pyQCat.instrument package"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:5
msgid "Subpackages"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:14
msgid "Submodules"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:17
msgid "pyQCat.instrument.APMS12G module"
msgstr ""

#: of pyQCat.instrument.APMS12G.APMS12G:1
#: pyQCat.instrument.ATTENUATOR.ATTENUATOR:1
#: pyQCat.instrument.AWG33522A.AWG33522A:1 pyQCat.instrument.AWG5208.AWG5208:1
#: pyQCat.instrument.CB2902A.CB2902A:1 pyQCat.instrument.CE8267D.CE8267D:1
#: pyQCat.instrument.DG645.DG645:1 pyQCat.instrument.DSG3000.DSG3000:1
#: pyQCat.instrument.E5071C.E5071C:1 pyQCat.instrument.HP83732A.HP83732A:1
#: pyQCat.instrument.Keithley2400.Keithley2400:1
#: pyQCat.instrument.KeithleyB2902A.KeithleyB2902A:1
#: pyQCat.instrument.KeysightMSOX6004A.KeysightMSOX6004A:1
#: pyQCat.instrument.M8190A.M8190A:1 pyQCat.instrument.N5173B.N5173B:1
#: pyQCat.instrument.N9030B.N9030B:1 pyQCat.instrument.SR830.SR830:1
msgid "Bases: :py:class:`~pyQCat.instrument.visa_inst.VisaInst`"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:25
msgid "pyQCat.instrument.ATS9360 module"
msgstr ""

#: of pyQCat.instrument.ATS9360.ATS9360:1
#: pyQCat.instrument.INSTRUMENT.INSTRUMENT:1
#: pyQCat.instrument.MIC12.ChannelSelect:1 pyQCat.instrument.MIC12.FreqPower:1
#: pyQCat.instrument.MIC12.Head:1 pyQCat.instrument.MIC12.MIC12:1
#: pyQCat.instrument.MIC12.Phase:1 pyQCat.instrument.MIC12.Switch:1
#: pyQCat.instrument.MIC12.TcpRecv:1 pyQCat.instrument.PTFS20.PTFS20:1
#: pyQCat.instrument.instrument_aio.Instrument:1
#: pyQCat.instrument.qaio.NonFeedbackAnalysis:1
#: pyQCat.instrument.qaio.Structure:1 pyQCat.instrument.qdc.DcOutput:1
#: pyQCat.instrument.qdc.DcQuery:1 pyQCat.instrument.qdc.DcRec:1
#: pyQCat.instrument.qdc.DcSet:1 pyQCat.instrument.qdc.End:1
#: pyQCat.instrument.qdc.Head:1 pyQCat.instrument.qdc.Qdc:1
#: pyQCat.instrument.virt_inst.VirtInst:1 pyQCat.instrument.visa_inst.Block:1
#: pyQCat.instrument.visa_inst.VisaInst:1
msgid "Bases: :py:class:`object`"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:33
msgid "pyQCat.instrument.ATTENUATOR module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:41
msgid "pyQCat.instrument.AWG33522A module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:49
msgid "pyQCat.instrument.AWG5208 module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:57
msgid "pyQCat.instrument.CB2902A module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:65
msgid "pyQCat.instrument.CE8267D module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:73
msgid "pyQCat.instrument.DG645 module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:81
msgid "pyQCat.instrument.DSG3000 module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:89
msgid "pyQCat.instrument.E5071C module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:97
msgid "pyQCat.instrument.HP83732A module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:105
msgid "pyQCat.instrument.INSTRUMENT module"
msgstr ""

#: of pyQCat.instrument.INSTRUMENT.INSTRUMENT:1
msgid "Instrument object to get a instrument with name in config file"
msgstr ""

#: of pyQCat.instrument.INSTRUMENT.INSTRUMENT:6
#: pyQCat.instrument.qaio.Qaio.set_multiple_index:6
msgid "Usage::"
msgstr ""

#: of pyQCat.instrument.INSTRUMENT.INSTRUMENT:4
msgid ">>>inst = INSTRUMENT() >>>inst['S0'].getAmpList()"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:113
msgid "pyQCat.instrument.Keithley2400 module"
msgstr ""

#: of pyQCat.instrument.Keithley2400:1 pyQCat.instrument.N9030B:1
msgid ""
"__date:         2018/08/30 __author:       Miracle Shih __corporation:  "
"OriginQuantum __usage:"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:121
msgid "pyQCat.instrument.KeithleyB2902A module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:129
msgid "pyQCat.instrument.KeysightMSOX6004A module"
msgstr ""

#: of
#: pyQCat.instrument.KeysightMSOX6004A.KeysightMSOX6004A.initializing_oscilloscope:1
msgid "Initializing the Interface and the Oscilloscope"
msgstr ""

#: of pyQCat.instrument.KeysightMSOX6004A.KeysightMSOX6004A.auto_scale:1
msgid ""
"The :AUToscale command performs a very useful function for unknown "
"waveforms by setting up the vertical channel, time base, and trigger "
"level of the instrument."
msgstr ""

#: of pyQCat.instrument.KeysightMSOX6004A.KeysightMSOX6004A.set_acq:1
msgid "Configure the instrument for normal acquisition."
msgstr ""

#: of pyQCat.instrument.KeysightMSOX6004A.KeysightMSOX6004A.get_image_data:1
msgid "query reads and returns screen image data"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:137
msgid "pyQCat.instrument.M8190A module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:145
msgid "pyQCat.instrument.MIC12 module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:153
msgid "pyQCat.instrument.N5173B module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:161
msgid "pyQCat.instrument.N5232B module"
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B:1
msgid "Bases: :py:class:`~pyQCat.instrument.E5071C.E5071C`"
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_trigger_mode:1
msgid "Sets the number of trigger signals the specified channel will ACCEPT."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.display_measurement
#: pyQCat.instrument.N5232B.N5232B.display_trace
#: pyQCat.instrument.N5232B.N5232B.set_cw_freq
#: pyQCat.instrument.N5232B.N5232B.set_format
#: pyQCat.instrument.N5232B.N5232B.set_ratioed_measurements
#: pyQCat.instrument.N5232B.N5232B.set_sweep_points
#: pyQCat.instrument.N5232B.N5232B.set_sweep_time
#: pyQCat.instrument.N5232B.N5232B.set_sweep_type
#: pyQCat.instrument.N5232B.N5232B.set_trigger_mode
#: pyQCat.instrument.PTFS20.PTFS20._sweep_enable
#: pyQCat.instrument.PTFS20.PTFS20.clock_mode
#: pyQCat.instrument.PTFS20.PTFS20.power_mode
#: pyQCat.instrument.PTFS20.PTFS20.pulse_output
#: pyQCat.instrument.PTFS20.PTFS20.set_freq_power
#: pyQCat.instrument.instrument_aio.Instrument.bind_sweep
#: pyQCat.instrument.instrument_aio.Instrument.from_json
#: pyQCat.instrument.instrument_aio.Instrument.get_status
#: pyQCat.instrument.instrument_aio.Instrument.get_trigger_delay
#: pyQCat.instrument.instrument_aio.Instrument.load
#: pyQCat.instrument.instrument_aio.Instrument.open_inst
#: pyQCat.instrument.instrument_aio.Instrument.output_off
#: pyQCat.instrument.instrument_aio.Instrument.output_on
#: pyQCat.instrument.instrument_aio.Instrument.select
#: pyQCat.instrument.instrument_aio.Instrument.set_control_channel
#: pyQCat.instrument.instrument_aio.Instrument.set_custom_waveform
#: pyQCat.instrument.instrument_aio.Instrument.set_dc
#: pyQCat.instrument.instrument_aio.Instrument.set_dc_all
#: pyQCat.instrument.instrument_aio.Instrument.set_intermediate_freq
#: pyQCat.instrument.instrument_aio.Instrument.set_output
#: pyQCat.instrument.instrument_aio.Instrument.set_output_freq
#: pyQCat.instrument.instrument_aio.Instrument.set_period
#: pyQCat.instrument.instrument_aio.Instrument.set_power
#: pyQCat.instrument.instrument_aio.Instrument.set_power_attenuation
#: pyQCat.instrument.instrument_aio.Instrument.set_sample_delay
#: pyQCat.instrument.instrument_aio.Instrument.set_sample_width
#: pyQCat.instrument.instrument_aio.Instrument.set_sine_waveform
#: pyQCat.instrument.instrument_aio.Instrument.set_square_waveform
#: pyQCat.instrument.instrument_aio.Instrument.set_sync_period
#: pyQCat.instrument.instrument_aio.Instrument.set_sync_trig_mode
#: pyQCat.instrument.instrument_aio.Instrument.set_trig_mode
#: pyQCat.instrument.instrument_aio.Instrument.set_trigger_delay
#: pyQCat.instrument.instrument_aio.Instrument.set_trigger_out_delay
#: pyQCat.instrument.instrument_aio.Instrument.set_trigout_pulse_width
#: pyQCat.instrument.instrument_aio.Instrument.sweep_delay
#: pyQCat.instrument.instrument_aio.Instrument.sweep_freq
#: pyQCat.instrument.instrument_aio.Instrument.sweep_power
#: pyQCat.instrument.instrument_aio.Instrument.sweep_sample_delay
#: pyQCat.instrument.instrument_aio.Instrument.sweep_voltage
#: pyQCat.instrument.instrument_aio.Instrument.sweep_wave
#: pyQCat.instrument.qaio.Qaio.AWG_output_off
#: pyQCat.instrument.qaio.Qaio.AWG_output_on
#: pyQCat.instrument.qaio.Qaio.AWG_set
#: pyQCat.instrument.qaio.Qaio.DC_output_off
#: pyQCat.instrument.qaio.Qaio.DC_output_on pyQCat.instrument.qaio.Qaio.DC_set
#: pyQCat.instrument.qaio.Qaio.RF_output_off
#: pyQCat.instrument.qaio.Qaio.RF_output_on pyQCat.instrument.qaio.Qaio.RF_set
#: pyQCat.instrument.qaio.Qaio.TO_output_off
#: pyQCat.instrument.qaio.Qaio.TO_output_on pyQCat.instrument.qaio.Qaio.TO_set
#: pyQCat.instrument.qaio.Qaio.check_mic_name
#: pyQCat.instrument.qaio.Qaio.instrument_output_off
#: pyQCat.instrument.qaio.Qaio.instrument_output_on
#: pyQCat.instrument.qaio.Qaio.set_multiple_index
msgid "Parameters"
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_trigger_mode:4
msgid ""
"Trigger mode. Choose from: HOLD - channel will not trigger CONTinuous - "
"channel triggers indefinitely GROups - channel accepts the number of "
"triggers specified with the          last SENS:SWE:GRO:COUN <num>.This is"
" one of the VNA          overlapped commands. Learn more. SINGle - "
"channel accepts ONE trigger, then goes to HOLD."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_trigger_mode:4
msgid ""
"Trigger mode. Choose from: HOLD - channel will not trigger CONTinuous - "
"channel triggers indefinitely GROups - channel accepts the number of "
"triggers specified with the"
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_trigger_mode:8
msgid ""
"last SENS:SWE:GRO:COUN <num>.This is one of the VNA overlapped commands. "
"Learn more."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_trigger_mode:10
msgid "SINGle - channel accepts ONE trigger, then goes to HOLD."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.display_measurement
#: pyQCat.instrument.N5232B.N5232B.display_trace
#: pyQCat.instrument.N5232B.N5232B.set_cw_freq
#: pyQCat.instrument.N5232B.N5232B.set_ratioed_measurements
#: pyQCat.instrument.N5232B.N5232B.set_sweep_time
#: pyQCat.instrument.N5232B.N5232B.set_trigger_mode
#: pyQCat.instrument.PTFS20.PTFS20._sweep_enable
#: pyQCat.instrument.PTFS20.PTFS20.clock_mode
#: pyQCat.instrument.PTFS20.PTFS20.freq_sweep_enable
#: pyQCat.instrument.PTFS20.PTFS20.power_mode
#: pyQCat.instrument.PTFS20.PTFS20.power_sweep_enable
#: pyQCat.instrument.PTFS20.PTFS20.pulse_output
#: pyQCat.instrument.PTFS20.PTFS20.set_freq_power
#: pyQCat.instrument.instrument_aio.Instrument.get_status
#: pyQCat.instrument.instrument_aio.Instrument.get_trigger_delay
#: pyQCat.instrument.instrument_aio.Instrument.set_power_attenuation
#: pyQCat.instrument.qaio.NonFeedbackAnalysis.get_experiment_data
#: pyQCat.instrument.qaio.Qaio.check_mic_name
#: pyQCat.instrument.qaio.Qaio.set_multiple_index
msgid "Returns"
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_format:1
msgid "Sets the display format for the measurement."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_format:4
msgid ""
"Choose from: MLINear|MLOGarithmic|PHASe|UPHase 'Unwrapped "
"phase|IMAGinary|REAL|POLar|SMITh| SADMittance 'Smith "
"Admittance|SWR|GDELay 'Group Delay|KELVin|FAHRenheit|CELSius| PPHase "
"'Positive Phase"
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_format:9
msgid ""
"Channel number of the measurement. There must be a selected measurement "
"on that channel. If unspecified, <cnum> is set to 1."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_format:12
#: pyQCat.instrument.N5232B.N5232B.set_ratioed_measurements:14
msgid ""
"Measurement number for each measurement. There must be a selected "
"measurement on the trace. If unspecified, <mnum> is set to 1."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_format
#: pyQCat.instrument.N5232B.N5232B.set_sweep_points
#: pyQCat.instrument.N5232B.N5232B.set_sweep_time
#: pyQCat.instrument.N5232B.N5232B.set_sweep_type
#: pyQCat.instrument.instrument_aio.Instrument.close_inst
#: pyQCat.instrument.instrument_aio.Instrument.get_status
#: pyQCat.instrument.instrument_aio.Instrument.get_trigger_delay
#: pyQCat.instrument.instrument_aio.Instrument.load
#: pyQCat.instrument.instrument_aio.Instrument.open_inst
#: pyQCat.instrument.instrument_aio.Instrument.set_custom_waveform
#: pyQCat.instrument.instrument_aio.Instrument.set_intermediate_freq
#: pyQCat.instrument.instrument_aio.Instrument.set_output_freq
#: pyQCat.instrument.instrument_aio.Instrument.set_period
#: pyQCat.instrument.instrument_aio.Instrument.set_power
#: pyQCat.instrument.instrument_aio.Instrument.set_sine_waveform
#: pyQCat.instrument.instrument_aio.Instrument.set_square_waveform
#: pyQCat.instrument.instrument_aio.Instrument.set_trig_mode
#: pyQCat.instrument.qaio.Qaio.AWG_output_off
#: pyQCat.instrument.qaio.Qaio.AWG_output_on
#: pyQCat.instrument.qaio.Qaio.AWG_set
#: pyQCat.instrument.qaio.Qaio.DC_output_off
#: pyQCat.instrument.qaio.Qaio.DC_output_on pyQCat.instrument.qaio.Qaio.DC_set
#: pyQCat.instrument.qaio.Qaio.RF_output_off
#: pyQCat.instrument.qaio.Qaio.RF_output_on pyQCat.instrument.qaio.Qaio.RF_set
#: pyQCat.instrument.qaio.Qaio.TO_output_off
#: pyQCat.instrument.qaio.Qaio.TO_output_on pyQCat.instrument.qaio.Qaio.TO_set
#: pyQCat.instrument.qaio.Qaio.check_mic_name
#: pyQCat.instrument.qaio.Qaio.instrument_output_off
#: pyQCat.instrument.qaio.Qaio.instrument_output_on
msgid "Return type"
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_format:16
#: pyQCat.instrument.N5232B.N5232B.set_sweep_points:11
#: pyQCat.instrument.N5232B.N5232B.set_sweep_time:14
#: pyQCat.instrument.N5232B.N5232B.set_sweep_type:10
#: pyQCat.instrument.instrument_aio.Instrument.close_inst:4
#: pyQCat.instrument.instrument_aio.Instrument.load:14
#: pyQCat.instrument.instrument_aio.Instrument.open_inst:13
#: pyQCat.instrument.instrument_aio.Instrument.set_custom_waveform:15
#: pyQCat.instrument.instrument_aio.Instrument.set_intermediate_freq:16
#: pyQCat.instrument.instrument_aio.Instrument.set_output_freq:16
#: pyQCat.instrument.instrument_aio.Instrument.set_period:15
#: pyQCat.instrument.instrument_aio.Instrument.set_power:16
#: pyQCat.instrument.instrument_aio.Instrument.set_sine_waveform:28
#: pyQCat.instrument.instrument_aio.Instrument.set_square_waveform:14
#: pyQCat.instrument.instrument_aio.Instrument.set_trig_mode:15
#: pyQCat.instrument.qaio.Qaio.AWG_output_off:6
#: pyQCat.instrument.qaio.Qaio.AWG_output_on:6
#: pyQCat.instrument.qaio.Qaio.AWG_set:22
#: pyQCat.instrument.qaio.Qaio.DC_output_off:6
#: pyQCat.instrument.qaio.Qaio.DC_output_on:6
#: pyQCat.instrument.qaio.Qaio.DC_set:11
#: pyQCat.instrument.qaio.Qaio.RF_output_off:6
#: pyQCat.instrument.qaio.Qaio.RF_output_on:6
#: pyQCat.instrument.qaio.Qaio.RF_set:31
#: pyQCat.instrument.qaio.Qaio.TO_output_off:6
#: pyQCat.instrument.qaio.Qaio.TO_output_on:6
#: pyQCat.instrument.qaio.Qaio.TO_set:20
#: pyQCat.instrument.qaio.Qaio.instrument_output_off:17
#: pyQCat.instrument.qaio.Qaio.instrument_output_on:17
msgid ":py:obj:`None`"
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_sweep_points:1
msgid "Sets the number of data points for the measurement."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_sweep_points:4
msgid ""
"Choose any number between 1 and the VNA maximum number of points. This "
"command will accept MIN or MAX instead of a numeric parameter. See SCPI "
"Syntax for more information."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_cw_freq:11
#: pyQCat.instrument.N5232B.N5232B.set_sweep_points:8
msgid "Any existing channel number. If unspecified, value is set to 1"
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_sweep_type:1
msgid ""
"Sets the type of analyzer sweep mode. First set sweep type, then set "
"sweep parameters such as frequency or power settings."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_sweep_type:5
msgid "Choose from: LINear | LOGarithmic | POWer | CW | SEGMent | PHASe"
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_sweep_time:13
#: pyQCat.instrument.N5232B.N5232B.set_sweep_type:7
msgid "Any existing channel number. If unspecified, value is set to 1."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_sweep_time:1
msgid ""
"Sets the time the analyzer takes to complete one sweep. If sweep time "
"accuracy is critical, use ONLY the values that are attained using the up "
"and down arrows next to the sweep time entry box. See Sweep Time."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_sweep_time:6
msgid ""
"Sweep time in seconds.To select the fastest sweep speed, either send MIN "
"as an argument to this command, or send SENS:SWE:TIME:AUTO 1.This command"
" will accept MIN or MAX instead of a numeric parameter. See SCPI Syntax "
"for more information.The MAX value will change based on point count, "
"IFBW, and dwell time."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_sweep_time:11
msgid "format, defualt is sec."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.display_measurement:12
#: pyQCat.instrument.N5232B.N5232B.display_trace:13
#: pyQCat.instrument.N5232B.N5232B.set_cw_freq:12
#: pyQCat.instrument.N5232B.N5232B.set_ratioed_measurements:20
#: pyQCat.instrument.N5232B.N5232B.set_sweep_time:15
msgid "None"
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_cw_freq:1
msgid ""
"Sets the Continuous Wave (or Fixed) frequency. Must also send "
"SENS:SWEEP:TYPE CW to put the analyzer into CW sweep mode."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_cw_freq:5
msgid ""
"CW frequency. Choose any number between the minimum and maximum frequency"
" limits of the analyzer. Units are Hz. This command will accept MIN or "
"MAX instead of a numeric parameter. See SCPI Syntax for more information."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_cw_freq:9
msgid "format, defualt is Hz."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.output_on_RF_power:1
msgid "Turns RF power from the source ON."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.output_off_RF_power:1
msgid "Turns RF power from the source OFF."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_ratioed_measurements:1
msgid "select the parameters and create measurement."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_ratioed_measurements:4
msgid "Select a receiver for the Numerator."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_ratioed_measurements:6
msgid "select another receiver for the Denominator."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_ratioed_measurements:8
msgid "select a source port for the measurement."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.set_ratioed_measurements:10
msgid ""
"Channel number of the measurements to be listed. There must be a selected"
" measurement on that channel. If unspecified, <cnum> is set to 1."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.display_measurement:9
#: pyQCat.instrument.N5232B.N5232B.set_ratioed_measurements:17
msgid ""
"Display the measurement in a specified Window number. The window must be "
"turned on. In addition, a window number must be specified. The range is 1"
" to 160."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.display_trace:1
msgid ""
"Turns the display of the specified trace in the specified window ON or "
"OFF. When OFF, the measurement behind the trace is still active."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.display_trace:5
msgid "ON (or 1) - turns the trace ON. OFF (or 0) - turns the trace OFF."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.display_trace:8
#: pyQCat.instrument.N5232B.N5232B.display_trace:11
msgid ""
"Any existing window number. If unspecified, value is set to 1. Use "
"Disp:Cat? to read the existing window numbers."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.display_measurement:3
msgid "This command creates a new trace in the specified window and connects the"
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.display_measurement:2
msgid ""
"trace to measurement which results in the trace displaying the data from "
"measurement."
msgstr ""

#: of pyQCat.instrument.N5232B.N5232B.display_measurement:6
msgid ""
"Measurement number for the measurement. If unspecified, <mnum> is set to "
"1."
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:169
msgid "pyQCat.instrument.N9030B module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:177
msgid "pyQCat.instrument.PTFS20 module"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20.set_freq_power:1
msgid "The unit is hz, and effective accuracy is 0.1"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20.set_freq_power:2
msgid "The unit is dBm, and effective accuracy is 0.1"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20.power_mode:1
msgid "0 or 1, 1 means ALC is on, 0 means ALC is off"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20.clock_mode:1
msgid "o or 1, 0 means external clock and 1 means internal clock"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20.freq_sweep:1
msgid ""
"Frequency sweep :param start_freq: The start value of frequency, The unit"
" is hz, and effective accuracy is 1 :param end_freq: The end value of "
"frequency, The unit is hz, and effective accuracy is 1 :param step_freq: "
"The step value of frequency, The unit is hz, and effective accuracy is 1 "
":param power: Output power, The unit is dBm, and effective accuracy is "
"0.1 :param keep_time: Output time, The unit is ns. External trigger does "
"not take effect :return:"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20.freq_sweep_enable:1
#: pyQCat.instrument.PTFS20.PTFS20.power_sweep_enable:1
msgid ""
"open and close sweep :param output: 0 or 1, o means off, 1 means on "
":param mode: The mode of sweep, 'rising' or 'falling'"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20.freq_sweep_enable:4
#: pyQCat.instrument.PTFS20.PTFS20.power_sweep_enable:4
msgid "'rising' means ascending order 'falling' means descending order"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20.power_sweep:1
msgid ""
"Power sweep :param start_power: The start value of power, The unit is "
"dbm, and effective accuracy is 0.1 :param end_power: The end value of "
"power, The unit is dbm, and effective accuracy is 0.1 :param step_power: "
"The step value of power, The unit is dbm, and effective accuracy is 0.1 "
":param freq: Output frequency, The unit is hz, and effective accuracy is "
"1 :param keep_time: Output time, The unit is ns. External trigger does "
"not take effect :return:"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20.pulse_output:1
msgid "Modulation waveform setting :param pulse_mode: 0 or 1 or 2"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20.pulse_output:3
msgid "0 means CW 1 means internal pulse 2 means external pulse"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20.pulse_output:6
#: pyQCat.instrument.PTFS20.PTFS20.pulse_output:7
msgid "The unit is ns"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20._sweep_enable:1
msgid "0 or 1. 0 means sweep is off, 1 means sweep is on"
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20._sweep_enable:2
msgid "rising or falling."
msgstr ""

#: of pyQCat.instrument.PTFS20.PTFS20._sweep_enable:3
msgid "'power' or 'freq'"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:185
msgid "pyQCat.instrument.SR830 module"
msgstr ""

#: of pyQCat.instrument.SR830:1
msgid ""
"__date:         2018/08/25 __author:       Miracle Shih __corporation:  "
"OriginQuantum __usage:"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:193
msgid "pyQCat.instrument.instrument\\_aio module"
msgstr ""

#: of pyQCat.instrument.instrument_aio:1
msgid "Quantum instrument object."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument:1
msgid "Generic quantum measurement and control instrument."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.open_inst:1
msgid "Open remote instrument OriginQAIO/MeasureAIO."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.open_inst:4
msgid "Ip address of remote device's database. Defaults to None."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.open_inst:7
msgid "Port number of remote device's database. Defaults to None."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.load
#: pyQCat.instrument.instrument_aio.Instrument.open_inst
#: pyQCat.instrument.qaio.Qaio.AWG_set pyQCat.instrument.qaio.Qaio.RF_set
msgid "Raises"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.open_inst:10
msgid "Connect to OriginQAIO fialed!"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.close_inst:1
msgid "Close remote instrument OriginQAIO/MeasureAIO."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.get_status:1
msgid "Get the status of remote instrument OriginQAIO/MeasureAIO."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.get_status:3
msgid "The database registered id number."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.get_status:5
msgid ":py:class:`int`"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.get_status:6
msgid "status 0/1/2/3/4"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.select:1
msgid "select which module and channel to output."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.select:3
msgid ""
"kwargs arguments can only be set as below: key: 8bit: Z_dc_control, "
"range: [1-8]. depends on your json file.      30bit: Z_dc_control, range:"
" [1-80]. depends on your json file. key: 8bit: Z_flux_control, range: "
"[1-8]. depends on your json file.      30bit: Z_flux_control, range: "
"[1-80]. depends on your json file. key: 8bit: XY_control, range: [5-20]. "
"depends on your json file.      30bit: XY_control, range: [9-40]. depends"
" on your json file. key: 8bit: Readout_control, range: [1-4]. depends on "
"your json file.      30bit: Readout_control, range: [1-8]. depends on "
"your json file."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.select:3
msgid ""
"kwargs arguments can only be set as below: key: 8bit: Z_dc_control, "
"range: [1-8]. depends on your json file."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.select:5
msgid "30bit: Z_dc_control, range: [1-80]. depends on your json file."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.select:6
msgid "key: 8bit: Z_flux_control, range: [1-8]. depends on your json file."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.select:7
msgid "30bit: Z_flux_control, range: [1-80]. depends on your json file."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.select:8
msgid "key: 8bit: XY_control, range: [5-20]. depends on your json file."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.select:9
msgid "30bit: XY_control, range: [9-40]. depends on your json file."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.select:10
msgid "key: 8bit: Readout_control, range: [1-4]. depends on your json file."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.select:11
msgid "30bit: Readout_control, range: [1-8]. depends on your json file."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.select:17
msgid "::usage::"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.select:13
msgid ""
">>>inst = Instrument() >>>inst.from_json(\"$dir/instrument.json\") "
">>>inst.select(Z_dc_control=[1, 2, 3, 4, 5, 6], >>>            "
"Z_flux_control=1, >>>            XY_control=5, >>>            "
"Readout_control=1)"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.load:1
msgid "Loading instrument info from data dictionary."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.load:3
msgid ""
"Convert the parameters of config file to a Document instance. After "
"loading successfully, the attribute _kernel.instrument will be a "
"MeasureAIODoc instance."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.load:8
msgid "Instrument json file load data."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.load:11
msgid "json schenma path error"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.from_json:1
msgid "Loading instrument info from json file."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.from_json:4
msgid "From json file name."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_dc_all:1
msgid "Set dc module voltages with sequence. such as, dc_seq = [1,1,1,1,1,1]."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_dc_all:3
msgid "iterable Object"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sync_period:1
msgid ""
"Set the period of  MeasureAIO AWG/RF/ADDA module. This parameter is "
"required when you use MeasureAIO non-feedback mode."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sync_period:4
msgid ""
"a number follows : Minmum value is 10. Maxmun value is 1000. Step is 5. "
"(The value you set must be exactly divisible by 5.) Unit: nanosecond(ns)"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sync_trig_mode:1
msgid "Set the trig mode of MeasureAIO AWG/RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sync_trig_mode:3
msgid "0 - 4, int type"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_period:1
msgid "Set module pulse period."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_period:4
#: pyQCat.instrument.instrument_aio.Instrument.set_trig_mode:4
#: pyQCat.instrument.instrument_aio.Instrument.set_trigger_delay:4
msgid ""
"Module name, you can choose as follows: Z_flux_control, XY_control, "
"Read_out_control, Trig_out_control."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_period:8
#: pyQCat.instrument.instrument_aio.Instrument.set_trig_mode:8
msgid "The channel number of moudle."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_period:11
msgid "[description]"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_trig_mode:1
msgid "Set module trig way."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_trig_mode:11
msgid "Trig mode, only support 0/1/2/3/4."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_dc:1
msgid "Set the DC voltage of MeasureAIO DC module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_dc:4
msgid "int type, 1 ~ 8."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_dc:5
msgid "The range is -5 ~ 5 V."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_power:1
msgid "Set the pulse power of MeasureAIO RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_intermediate_freq:4
#: pyQCat.instrument.instrument_aio.Instrument.set_output_freq:4
#: pyQCat.instrument.instrument_aio.Instrument.set_power:4
#: pyQCat.instrument.instrument_aio.Instrument.sweep_freq:4
#: pyQCat.instrument.instrument_aio.Instrument.sweep_power:4
msgid "Can be set only as 'XY_control' / 'Read_out_control'."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_intermediate_freq:7
#: pyQCat.instrument.instrument_aio.Instrument.set_output_freq:7
#: pyQCat.instrument.instrument_aio.Instrument.set_power:7
msgid ""
"The channel number. When module is 'XY_control, the range is 5 ~ 20. When"
" module is 'Read_out_control, the range is 1 ~ 4."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_power:12
msgid "The range is -40 ~ -10 dBm. step is 0.1 dBm."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_output_freq:1
msgid "Set the output frequency of MeasureAIO RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_output_freq:12
msgid "The range is 4000 ~ 8000 MHz, step is 0.001 MHz(1KHz)."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_intermediate_freq:1
msgid "Set the intermediate frequency of MeasureAIO RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_intermediate_freq:12
msgid "The range is 500 ~ 700 MHz. step is 0.001 MHz(1KHz)."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_trigger_delay:1
msgid "Set the trigger delay of MeasureAIO AWG/RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_custom_waveform:7
#: pyQCat.instrument.instrument_aio.Instrument.set_sine_waveform:8
#: pyQCat.instrument.instrument_aio.Instrument.set_trigger_delay:8
msgid ""
"The channel number of module. When module is 'XY_control, the range is 5 "
"~ 20. When module is 'Read_out_control, the range is 1 ~ 4. When module "
"is 'Z_flux_control', the range is 1~8."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_trigger_delay:13
msgid "The range is 0 ~ 1000000 ns, step is 5 ns. Max length is 5."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.get_trigger_delay:1
msgid "Get the trigger delay of MeasureAIO AWG/RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.get_trigger_delay:4
msgid ""
"Can be set only as 'Z_flux_control'/ 'XY_control' / 'Read_out_control'/ "
"'Trig_out_control'."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.get_trigger_delay:6
#: pyQCat.instrument.instrument_aio.Instrument.sweep_delay:6
#: pyQCat.instrument.instrument_aio.Instrument.sweep_wave:5
msgid ""
"When module is 'XY_control, the range is 5 ~ 20. When module is "
"'Read_out_control, the range is 1 ~ 4. When module is 'Z_flux_control', "
"the range is 1~8."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.get_trigger_delay:9
msgid ":py:class:`list`"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.get_trigger_delay:10
msgid "delay value."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_trigger_out_delay:1
msgid "Set the trigger out delay of MeasureAIO AWG/RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_trigger_out_delay:4
msgid "Can be set only as 'Z_flux_control'/ 'XY_control' ."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_trigger_out_delay:6
msgid ""
"When module is 'XY_control, the range is 5 ~ 8. When module is "
"'Z_flux_control', the range is 1 ~ 4."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_trigger_out_delay:8
msgid "The range is 0 ~ 1000000 ns. step is 5 ns. Max length is 5."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_trigout_pulse_width:1
msgid "Set the pulse width of MeasureAIO Trigout module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_trigout_pulse_width:4
msgid "1 ~ 4"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sample_width:5
#: pyQCat.instrument.instrument_aio.Instrument.set_trigout_pulse_width:6
msgid "The range is 0 ~ 5000 ns. step is 5 ns."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sample_delay:1
msgid "Set the sampling delay of MeasureAIO ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_power_attenuation:4
#: pyQCat.instrument.instrument_aio.Instrument.set_sample_delay:4
#: pyQCat.instrument.instrument_aio.Instrument.set_sample_width:4
#: pyQCat.instrument.instrument_aio.Instrument.sweep_sample_delay:4
msgid "The range is 1 ~ 4."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sample_delay:5
msgid "The range is 0 ~ 1000000 ns. step is 5 ns."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sample_width:1
msgid "Set the sampling time width of MeasureAIO ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_power_attenuation:1
msgid "Set the power attenuation of MeasureAIO ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_power_attenuation:5
msgid "The range is 0 ~ 40 dB. step is 1 dB."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sine_waveform:1
msgid "Set the sine wave of MeasureAIO AWG/RF module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sine_waveform:4
msgid ""
"Module name, you can choose as follows: Z_flux_control, XY_control, "
"Read_out_control."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sine_waveform:14
#: pyQCat.instrument.instrument_aio.Instrument.set_square_waveform:7
msgid "The range is 5 ~ 30000 ns, step is 5 ns."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sine_waveform:17
msgid ""
"When module is 'Z_flux_control', the parameter is valid, range is -1 ~ 1 "
"V."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sine_waveform:20
msgid ""
"When module is 'XY_control', the range is 500 ~ 700 MHz, step is "
"0.001MHz(1KHz). When module is 'Z_flux_control', the range is 0 ~ 200 "
"MHz, step is 0.001MHz(1KHz)."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_sine_waveform:24
msgid "The range is 0 ~ 360°, step is 0.5°."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_square_waveform:1
msgid "Set square wave of MeasureAIO AWG module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_square_waveform:4
msgid "The channel number of Z flux control, range is 1 ~ 8."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_square_waveform:10
msgid "The range is -1 ~ 1 V."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_custom_waveform:1
msgid "Set custom wave of MeasureAIO AWG/RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_custom_waveform:4
#: pyQCat.instrument.instrument_aio.Instrument.sweep_delay:4
#: pyQCat.instrument.instrument_aio.Instrument.sweep_wave:4
msgid "Can be set only as 'Z_flux_control'/ 'XY_control' / 'Read_out_control'."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_custom_waveform:12
msgid "Wave list object."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.output_on:1
msgid "Output with MeasureAIO."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.output_off:3
#: pyQCat.instrument.instrument_aio.Instrument.output_on:3
#: pyQCat.instrument.instrument_aio.Instrument.set_control_channel:3
#: pyQCat.instrument.instrument_aio.Instrument.set_output:8
msgid "Moudle name and channel."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.output_off:1
msgid "Stop output with MeasureAIO."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.clear_control_channel:1
msgid "Clear control module channel before set new values."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_output:1
msgid "Set MeasureAIO output status."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_output:4
msgid "Only supoort 0 or 4. 1: Open output. 4: Close output."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_control_channel:1
msgid "Set moudle controller channel."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_control_channel
msgid "Example"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.set_control_channel:6
msgid ""
"Z_dc_control = [1, 2, 3, 4] XY_dc_control = [1, 2, 3, 4] Z_flux_control ="
" [1, 2, 3, 4] Trig_out_control = [1, 2, 3, 4]"
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_delay:1
msgid "Sweep trigger delay parameter of MeasureAIO AWG/RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_delay:9
#: pyQCat.instrument.instrument_aio.Instrument.sweep_sample_delay:5
msgid "The start value of the sweep parameter. The range is 0 ~ 1000000 ns."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_delay:10
#: pyQCat.instrument.instrument_aio.Instrument.sweep_sample_delay:6
msgid "The end value of the sweep parameter. The range is 0 ~ 1000000 ns."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_delay:11
#: pyQCat.instrument.instrument_aio.Instrument.sweep_sample_delay:7
msgid ""
"The step value of the sweep parameter. The range is 0 ~ 1000000 ns. "
"::note: step must be multiple of 5 ns."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_delay:13
#: pyQCat.instrument.instrument_aio.Instrument.sweep_freq:12
#: pyQCat.instrument.instrument_aio.Instrument.sweep_power:12
#: pyQCat.instrument.instrument_aio.Instrument.sweep_sample_delay:9
#: pyQCat.instrument.instrument_aio.Instrument.sweep_voltage:8
msgid "Iterable object, include the all sweep points."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_delay:14
#: pyQCat.instrument.instrument_aio.Instrument.sweep_freq:13
#: pyQCat.instrument.instrument_aio.Instrument.sweep_power:13
#: pyQCat.instrument.instrument_aio.Instrument.sweep_sample_delay:10
#: pyQCat.instrument.instrument_aio.Instrument.sweep_voltage:9
#: pyQCat.instrument.instrument_aio.Instrument.sweep_wave:10
msgid ""
"The default value is 1000. The range is 0 ~ 10000 This argument will only"
" useful when excute `Not need feedback` operation."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_delay:17
msgid "The index of trigger delay list."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_delay:18
#: pyQCat.instrument.instrument_aio.Instrument.sweep_freq:16
#: pyQCat.instrument.instrument_aio.Instrument.sweep_power:16
#: pyQCat.instrument.instrument_aio.Instrument.sweep_sample_delay:14
#: pyQCat.instrument.instrument_aio.Instrument.sweep_voltage:12
msgid ":return points_ : List object, the sweep points."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_power:1
msgid "Sweep pulse power parameter of MeasureAIO RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_freq:6
#: pyQCat.instrument.instrument_aio.Instrument.sweep_power:6
msgid ""
"When module is 'XY_control, the range is 5 ~ 20. When module is "
"'Read_out_control, the range is 1 ~ 4."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_power:8
msgid "The start value of the sweep parameter. The range is -40 ~ -10 dBm."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_power:9
msgid "The end value of the sweep parameter. The range is -40 ~ -10 dBm."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_power:10
msgid ""
"The step value of the sweep parameter. The range is -40 ~ -10 dBm. "
"::note: step must be multiple of 0.1 dBm."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_freq:1
msgid "Sweep output frequency parameter of MeasureAIO RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_freq:8
msgid "The start value of the sweep parameter. The range is 4000 ~ 8000 MHz."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_freq:9
msgid "The end value of the sweep parameter. The range is 4000 ~ 8000 MHz."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_freq:10
msgid ""
"The step value of the sweep parameter. The range is 4000 ~ 8000 MHz. "
"::note: step must be multiple of 0.001 MHz(1KHz)."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_sample_delay:1
msgid "Sweep sample delay parameter of MeasureAIO RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_sample_delay:13
msgid "index of delay parameters."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_voltage:1
msgid "Sweep bias voltage parameter of MeasureAIO DC module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_voltage:4
msgid "The range is 1 ~ 8."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_voltage:5
msgid "The start value of the sweep parameter. The range is -5 ~ 5 v."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_voltage:6
msgid "The end value of the sweep parameter. The range is -5 ~ 5 v."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_voltage:7
msgid "The step value of the sweep parameter. The range is -5 ~ 5 v."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_wave:1
msgid "Sweep custom waveform of MeasureAIO AWG/RF/ADDA module."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_wave:8
msgid "List object which is used to store waveform data."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.sweep_wave:9
msgid "List object which is used to store waveform sweep datas."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.bind_sweep:1
msgid "Bind sweep paramets."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.bind_sweep:3
msgid "The method bind_sweep is designed to synchronize the sweep parameters."
msgstr ""

#: of pyQCat.instrument.instrument_aio.Instrument.bind_sweep:5
msgid "Must be a continuous array (array elements must be separated by 1)"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:201
msgid "pyQCat.instrument.qaio module"
msgstr ""

#: of pyQCat.instrument.qaio:1
msgid "QAIO instrument mode control interface."
msgstr ""

#: of pyQCat.instrument.qaio.StructureMeta:1
msgid "Bases: :py:class:`type`"
msgstr ""

#: of pyQCat.instrument.qaio.Sine:1 pyQCat.instrument.qaio.Square:1
msgid "Bases: :py:class:`~pyQCat.instrument.qaio.Structure`"
msgstr ""

#: of pyQCat.instrument.qaio.Qaio:1
msgid "Bases: :py:class:`~pyQCat.instrument.instrument_aio.Instrument`"
msgstr ""

#: of pyQCat.instrument.qaio.Qaio:1
msgid "Instrument mode QAIO."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio:3
msgid "Inherited from non-feedback mode QAIO."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.DC_set:1
msgid "Set dc."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.DC_set:4
msgid "Channel number, range 1 ~ 8."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.DC_set:7
msgid "Voltage value, range -5 ~ 5V."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.DC_output_on:1
msgid "Open z dc control module output."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.DC_output_off:3
#: pyQCat.instrument.qaio.Qaio.DC_output_on:3
msgid "The channel number of DC."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.DC_output_off:1
msgid "Close z dc control module output."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.AWG_set:1
msgid "Instrument mode AWG module set task parameters."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.AWG_output_off:3
#: pyQCat.instrument.qaio.Qaio.AWG_output_on:3
#: pyQCat.instrument.qaio.Qaio.AWG_set:4
#: pyQCat.instrument.qaio.Qaio.RF_output_off:3
#: pyQCat.instrument.qaio.Qaio.RF_output_on:3
#: pyQCat.instrument.qaio.Qaio.TO_output_off:3
#: pyQCat.instrument.qaio.Qaio.TO_output_on:3
msgid "The channel number of AWG."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.AWG_set:7
#: pyQCat.instrument.qaio.Qaio.RF_set:7 pyQCat.instrument.qaio.Qaio.TO_set:7
msgid "Pulse period."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.AWG_set:10
#: pyQCat.instrument.qaio.Qaio.RF_set:10 pyQCat.instrument.qaio.Qaio.TO_set:10
msgid "Trig mode."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.AWG_set:13
#: pyQCat.instrument.qaio.Qaio.RF_set:13 pyQCat.instrument.qaio.Qaio.TO_set:16
msgid "Trig delay."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.AWG_set:16
msgid "Output wave."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.AWG_set:19
msgid "Wavefrom must be Sine, Square or iterable!"
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.AWG_output_on:1
msgid "Open AWG(Z_flux_control) module output."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.AWG_output_off:1
msgid "Close AWG(Z_flux_control) module output."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.RF_set:1
msgid "Instrument mode RF module set task parameters."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.RF_set:4
msgid "The channel number of RF."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.RF_set:16
msgid "RF output frequency."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.RF_set:19
msgid "RF output intermediate frequency."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.RF_set:22
msgid "RF output power."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.RF_set:25
msgid "RF wave."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.RF_set:28
msgid "Wavefrom must be Sine or iterable!"
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.RF_output_on:1
msgid "Open RF(Z_flux_control/Readout_control) module output."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.RF_output_off:1
msgid "Close RF(Z_flux_control/Readout_control) module output."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.TO_set:1
msgid "Instrument mode TO module set task parameters."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.TO_set:4
msgid "The channel number of TO."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.TO_set:13
msgid "Trig width."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.TO_output_on:1
msgid "Open TO(Trigout_control) module output."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.TO_output_off:1
msgid "Close TO(Trigout_control) module output."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.instrument_output_on:1
msgid "Open DC/AWG/RF/TO module output."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.instrument_output_off:4
#: pyQCat.instrument.qaio.Qaio.instrument_output_on:4
msgid "DC module channels. Defaults to None."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.instrument_output_off:7
#: pyQCat.instrument.qaio.Qaio.instrument_output_on:7
msgid "AWG module channels. Defaults to None."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.instrument_output_off:10
#: pyQCat.instrument.qaio.Qaio.instrument_output_on:10
msgid "RF module channels. Defaults to None."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.instrument_output_off:13
#: pyQCat.instrument.qaio.Qaio.instrument_output_on:13
msgid "TO module channels. Defaults to None."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.instrument_output_off:1
msgid "Close DC/AWG/RF/TO module output."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.check_mic_name:1
msgid "Check RF module name."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.check_mic_name:4
msgid "QAIO 8:"
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.check_mic_name:4
msgid "XY_control channel range 5 ~ 20; Readout_control channel range 1 ~ 4;"
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.check_mic_name:8
msgid "QAIO 30:"
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.check_mic_name:7
msgid "XY_control channel range 9 ~ 40; Readout_control channel range 1 ~ 8;"
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.check_mic_name:11
msgid "The channel number."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.check_mic_name:14
msgid "Module name."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio._is_execute:1
msgid "Check if the task was executed successfully."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio._is_execute
msgid "Notes"
msgstr ""

#: of pyQCat.instrument.qaio.Qaio._is_execute:4
msgid "2: Task executed success. 3: Task executed fialed."
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.set_multiple_index:2
msgid "Setting the start and end values"
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.set_multiple_index:3
msgid "channel settings"
msgstr ""

#: of pyQCat.instrument.qaio.Qaio.set_multiple_index:6
msgid ""
">>>experiment = Qaio() >>>experiment.set_multiple_index([0, 1500], [100, "
"900], channel=1)"
msgstr ""

#: of pyQCat.instrument.qaio.NonFeedbackAnalysis._query_status:1
msgid "query mongo database by object id"
msgstr ""

#: of pyQCat.instrument.qaio.NonFeedbackAnalysis._get_measure_data_length:1
msgid "get the length of a list object which stores the `MeasureResultDoc` object"
msgstr ""

#: of pyQCat.instrument.qaio.NonFeedbackAnalysis._start_info:1
msgid "Print log when experiment start."
msgstr ""

#: of pyQCat.instrument.qaio.NonFeedbackAnalysis._stop_info:1
msgid "Print log when experiment end."
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:209
msgid "pyQCat.instrument.qdc module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:217
msgid "pyQCat.instrument.virt\\_inst module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:225
msgid "pyQCat.instrument.visa\\_inst module"
msgstr ""

#: ../../source/api/pyQCat.instrument.rst:233
msgid "Module contents"
msgstr ""

#: of pyQCat.instrument:3
msgid "QAIO Instrument (:mod:`pyQCat.instrument`)"
msgstr ""

#: of pyQCat.instrument:5
msgid "Instrument related modules."
msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.instrument.visa_inst.VisaInst`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`object`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.instrument.E5071C.E5071C`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`type`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.instrument.qaio.Structure`"
#~ msgstr ""

#~ msgid "Bases: :py:class:`~pyQCat.instrument.instrument_aio.Instrument`"
#~ msgstr ""

#~ msgid ""
#~ ">>>inst = Instrument() "
#~ ">>>inst.load(\"instrument.json\") "
#~ ">>>inst.select(Z_dc_control=[1, 2, 3, 4, 5,"
#~ " 6], >>>            Z_flux_control=1, >>>"
#~ "            XY_control=5, >>>            "
#~ "Readout_control=1)"
#~ msgstr ""

#~ msgid "Json file absolute path."
#~ msgstr ""

#~ msgid "Belong to some user data."
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.instrument.visa_inst.VisaInst`"
#~ msgstr ""

#~ msgid "Bases: :class:`object`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.instrument.E5071C.E5071C`"
#~ msgstr ""

#~ msgid "pyQCat.instrument.U1084A module"
#~ msgstr ""

#~ msgid "Bases: :class:`type`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.instrument.qaio.Structure`"
#~ msgstr ""

#~ msgid "Bases: :class:`pyQCat.instrument.instrument_aio.Instrument`"
#~ msgstr ""

