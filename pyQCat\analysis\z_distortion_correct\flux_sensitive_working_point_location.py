# -*- coding: UTF-8 -*-
import json

import matplotlib.pyplot as plt
import numpy as np
import time
import copy
import os
import json


def find_frequency(frequency_list, lb, ub, rb_spectrum_step):
    best_freq = []
    # frequency_list是rb谱得出的解空间，（lb, ub）是flux-sensitive的区域，适合用于畸变测试，
    # 此函数用于查找frequency_list 中 处于（lb, ub）范围内的元素，并对其进行打分，最后找到得分最高的频率返回。
    # 最多有 (ub-lb)/rb_spectrum_step + 1 个点，但由于小数点问题，一般最多只会有(ub-lb)/rb_spectrum_step个点
    # 在（lb, ub）区间范围内，ub-lb=search_range，search_range=50，rb_spectrum_step=10，所以range_in里最多会有五个元素为True。
    range_in = ((frequency_list>=lb) * (frequency_list<=ub))
    # print(np.sum(range_in))
    if np.sum(range_in) > 0:
        # 把这些在（lb,ub） 范围内的频率取出来，作为可选畸变工作点list，然后我们对其打分
        index = np.where(range_in > 0)
        frequency_candidate = frequency_list[index]
        # 对frequency_candidate里的元素打分
        score_candidate = np.zeros_like(frequency_candidate)
        for index,freq in enumerate(frequency_candidate):
            # 打分方式可以调整，尽可能使得所选工作点附近在RB谱中是连续可用的区间。
            score = frequency_list.__contains__(freq) + \
                    frequency_list.__contains__(freq + rb_spectrum_step)*3 + \
                    frequency_list.__contains__(freq - rb_spectrum_step)*3 + \
                    frequency_list.__contains__(freq + 2*rb_spectrum_step) + \
                    frequency_list.__contains__(freq - 2*rb_spectrum_step)
            score_candidate[index] = score
        # 得分一样的选择的是index更靠前的（np.argmax决定的），由于RB谱是从上往下扫，所以同等得分选择的是高频工作点。
        frequency_candidate_max_score = frequency_candidate[np.argmax(score_candidate)]
        best_freq.append(frequency_candidate_max_score)
    return best_freq

def find_distortion_working_point(filepath, filename, rb_spectrum_step=10, frequency_down=200, search_range=50):
    # rb_spectrum_data
    with open(os.path.join(filepath, filename), mode='r', encoding="utf-8") as f:
        rb_spectrum_data = json.load(f)

    # 对每个比特依次处理
    qubit_list = list(rb_spectrum_data.keys())
    result_save = []
    for qubit in qubit_list:
        frequency_list_str = list(rb_spectrum_data[qubit].keys())
        # frequency_list = np.array([float(str) for str in frequency_list_str])
        frequency_list = []
        for frequency_str in frequency_list_str:
            if rb_spectrum_data[qubit][frequency_str]["is_pass"] == True:
                try:
                    freq_max = rb_spectrum_data[qubit][frequency_str]["params"]["ac_spectrum"]["standard"][0]
                except:
                    freq_max = rb_spectrum_data[qubit][frequency_str]["params"]["ac_spectrum"]["bottom_left"][0]
                bit = rb_spectrum_data[qubit][frequency_str]["params"]["bit"]
                if rb_spectrum_data[qubit][frequency_str]['rb_fidelity'] > 0.995:
                    frequency_list.append(float(frequency_str))
        frequency_list = np.array(frequency_list)
        target_freq = freq_max - frequency_down
        # 搜索顺序依次是先找在[target_freq-search_range， target_freq],
        # [target_freq,target_freq + search_range]，打分过程建议封装

        # 按照搜索顺序依次搜索，这部分可以根据实际情况再做调整。
        lb = target_freq-search_range
        ub = target_freq
        distortion_working_point_frequency = find_frequency(frequency_list, lb, ub, rb_spectrum_step)

        # 按照搜索顺序依次搜索
        if len(distortion_working_point_frequency) == 0:
            lb = target_freq
            ub = target_freq + search_range
            distortion_working_point_frequency = find_frequency(frequency_list, lb, ub, rb_spectrum_step)
        # 按照搜索顺序依次搜索
        if len(distortion_working_point_frequency) == 0:
            lb = target_freq - search_range*2
            ub = target_freq - search_range
            distortion_working_point_frequency = find_frequency(frequency_list, lb, ub, rb_spectrum_step)

        # 总结搜索结果, else与最近的if匹配
        rb_fidelity = 1
        rb_std = 0
        if len(distortion_working_point_frequency) == 0:
            print(qubit + ':can not find suitable working point for Z distortion characterization')
            print(qubit + f" maximum frequency is {freq_max}, available frequency list:", frequency_list)
            result_freq = freq_max
        else:
            result_freq = distortion_working_point_frequency[0]
            # print(qubit + f':{result_freq} MHz is suitable to characterize Z distortion')
            # result_freq_str
            for frequency_str in frequency_list_str:
                if float(frequency_str) == result_freq:
                    rb_fidelity = rb_spectrum_data[qubit][frequency_str]['rb_fidelity']
                    rb_std = rb_spectrum_data[qubit][frequency_str]['rb_std']
                    break
        # 保存结果，（比特号，freq_max，distortion_working_point_frequency，从最高点降低的频率）
        if len(frequency_list) > 0:
            result_save.append([bit, freq_max, result_freq, freq_max - result_freq, rb_fidelity, rb_std])
    np.savetxt(filepath + filename + "_flux_sensitive_working_point.dat", result_save)
    return result_save


def plot_data(filepath, filename):
    result = np.genfromtxt(filepath + filename + "_flux_sensitive_working_point.dat")
    bit = result[:, 0]
    freq_max = result[:, 1]
    result_freq = result[:, 2]
    freq_down = freq_max - result_freq

    rb_fidelity = result[:, 4]
    rb_std = result[:, 5]

    np.savetxt(filepath + filename + "_flux_sensitive_working_point.dat",
               np.column_stack((bit, freq_max, result_freq, freq_down, rb_fidelity, rb_std)))
    # plot
    fig, ax = plt.subplots()
    ax.scatter(bit, freq_max, label='max freq')  # , s=6, c='b'
    ax.scatter(bit, result_freq, label='flux sensitive working point')
    plt.legend()

    ax.set_title("distortion working point")
    ax.set_xlabel('qubit')
    ax.set_ylabel('frequency (MHz)')
    ax.grid()
    fig.savefig(filepath + filename + "_distortion_working_point.png")
    plt.close(fig)

    fig, ax = plt.subplots()
    ax.scatter(bit, freq_down, c='r')  #
    # ax.set_ylim(150, 300)
    ax.set_xlabel('qubit')
    ax.set_ylabel('frequency down (MHz)')
    fig.savefig(filepath + filename + "_freq_down.png")
    plt.close(fig)

    ## rb_fidelity, rb_std plot
    fig, (ax0, ax1) = plt.subplots(2)
    ax0.plot(bit, rb_fidelity)
    ax0.set_ylabel("RB Fidelity")
    ax0.get_xaxis().set_ticklabels([])

    ax1.plot(bit, rb_std)
    ax1.set_xlabel('qubit')
    ax1.set_ylabel("RB STD")
    # for label in ax1.get_xticklabels(which="major"):
    #     label.set(rotation=30, horizontalalignment="right")

    fig.suptitle(f"RB result at distortion working point")
    fig.savefig(filepath + filename + "_rb result.png")
    plt.close()


if __name__ == "__main__":
    filepath = r"C:\Users\<USER>\Downloads\RB_Spectrum\second_test\\"
    filename = "chip_rb_data.json"
    result = find_distortion_working_point(filepath, filename, rb_spectrum_step=10, frequency_down=200, search_range=50)
    # plot_data(filepath, filename)


