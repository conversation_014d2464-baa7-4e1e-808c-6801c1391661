# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-23 16:12+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.9.1\n"

#: ../../source/stubs/tools/pyQCat.tools.solve_equations.rst:2
msgid "pyQCat.tools.solve\\_equations"
msgstr ""

#: of pyQCat.tools.utilities.solve_equations:1
msgid ""
"Solve the coefficients of a system of quadratic equations with one "
"variable."
msgstr ""

#: of pyQCat.tools.utilities.solve_equations
msgid "Parameters"
msgstr ""

#: of pyQCat.tools.utilities.solve_equations:4
msgid "The x value of 3 couple of coordinate."
msgstr ""

#: of pyQCat.tools.utilities.solve_equations:7
msgid "The y value of 3 couple of coordinate."
msgstr ""

#: of pyQCat.tools.utilities.solve_equations
msgid "Returns"
msgstr ""

#: of pyQCat.tools.utilities.solve_equations:10
msgid "The coefficients of quadratic equations: y = ax^2 + bx + c."
msgstr ""

#: of pyQCat.tools.utilities.solve_equations
msgid "Return type"
msgstr ""

