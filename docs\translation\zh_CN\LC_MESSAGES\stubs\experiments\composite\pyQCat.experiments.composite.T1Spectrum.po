# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2022, OriginQ pyQCat Development Team
# This file is distributed under the same license as the pyQCat-Monster
# package.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: pyQCat-Monster \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-14 10:13+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:2
msgid "pyQCat.experiments.composite.T1Spectrum"
msgstr ""

#: of pyQCat.experiments.composite.t1_spectrum.T1Spectrum:1
msgid ""
"Bases: "
":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
msgstr ""

#: of pyQCat.experiments.composite.t1_spectrum.T1Spectrum:1
msgid "T1 Spectrum Experiment."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite_experiment.CompositeExperiment.__init__:1
msgid "Initialize the experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:13
msgid "Methods"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`__init__ <pyQCat.experiments.composite.T1Spectrum.__init__>`\\ "
"\\(inst\\, qubits\\[\\, couplers\\, ...\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`component_experiment "
"<pyQCat.experiments.composite.T1Spectrum.component_experiment>`\\ "
"\\(\\[index\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid "Return the component Experiment object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`from_experiment_context "
"<pyQCat.experiments.composite.T1Spectrum.from_experiment_context>`\\ "
"\\(context\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid "Create experiment using `ExperimentContext` instance."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`get_qubit_str "
"<pyQCat.experiments.composite.T1Spectrum.get_qubit_str>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid "Get qubit str, an args qubit for SaveFile class."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`options_table "
"<pyQCat.experiments.composite.T1Spectrum.options_table>`\\ \\(\\[mode\\, "
"detail\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid ":py:obj:`run <pyQCat.experiments.composite.T1Spectrum.run>`\\ \\(\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
#: of pyQCat.experiments.composite.t1_spectrum.T1Spectrum.run:1
msgid "Run T1 Spectrum Composite Experiment."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_analysis_options "
"<pyQCat.experiments.composite.T1Spectrum.set_analysis_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid "Set the analysis options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_experiment_options "
"<pyQCat.experiments.composite.T1Spectrum.set_experiment_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid "Set the experiment options."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_parent_file "
"<pyQCat.experiments.composite.T1Spectrum.set_parent_file>`\\ "
"\\(save\\_file\\[\\, description\\]\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid "Set parent file saved object."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid ""
":py:obj:`set_run_options "
"<pyQCat.experiments.composite.T1Spectrum.set_run_options>`\\ "
"\\(\\*\\*fields\\)"
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:30:<autosummary>:1
msgid "Set options values for the experiment  :meth:`run` method."
msgstr ""

#: ../../source/stubs/experiments/composite/pyQCat.experiments.composite.T1Spectrum.rst:32
msgid "Attributes"
msgstr ""

#: of pyQCat.experiments.single.t1.T1:1:<autosummary>:1
msgid ":py:obj:`analysis <pyQCat.experiments.composite.T1Spectrum.analysis>`\\"
msgstr ""

#: of pyQCat.experiments.single.t1.T1:1:<autosummary>:1
msgid "Return the analysis instance for the experiment"
msgstr ""

#: of pyQCat.experiments.single.t1.T1:1:<autosummary>:1
msgid ""
":py:obj:`analysis_options "
"<pyQCat.experiments.composite.T1Spectrum.analysis_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.t1.T1:1:<autosummary>:1
msgid "Return the options for the experiment."
msgstr ""

#: of pyQCat.experiments.single.t1.T1:1:<autosummary>:1
msgid ""
":py:obj:`child_experiment "
"<pyQCat.experiments.composite.T1Spectrum.child_experiment>`\\"
msgstr ""

#: of pyQCat.experiments.single.t1.T1:1:<autosummary>:1
msgid "Get child experiment."
msgstr ""

#: of pyQCat.experiments.single.t1.T1:1:<autosummary>:1
msgid ""
":py:obj:`experiment_options "
"<pyQCat.experiments.composite.T1Spectrum.experiment_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.t1.T1:1:<autosummary>:1
msgid ""
":py:obj:`run_options "
"<pyQCat.experiments.composite.T1Spectrum.run_options>`\\"
msgstr ""

#: of pyQCat.experiments.single.t1.T1:1:<autosummary>:1
msgid "Return options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_experiment_options:1
msgid "Default Experiment Options"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_experiment_options:9
msgid "Experiment options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_experiment_options:4
msgid ""
"z_amp_list (List, np.ndarray): Scan Z line amp list. delay_list (List, "
"np.ndarray): T1 experiment scan delay. ac_spectrum_paras (List): The "
"target qubit ac spectrum"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_experiment_options:7
msgid "fit parameters, default None."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_experiment_options
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._metadata
msgid "Return type"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:13
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_experiment_options:11
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:12
msgid ":py:class:`~pyQCat.structures.Options`"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:1
msgid "Default analysis options for T1Spectrum experiment."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:11
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:10
msgid "Options:"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:5
msgid "freq_list (List, np.ndarray): The frequency calculate"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:5
msgid "by ac spectrum paras and z amp. If no ac spectrum paras, this is None."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:7
msgid "r_square_threshold (float): To extract abnormal points,"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:8
msgid "set threshold of the analysis quality's r_square."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:11
msgid "rate_threshold (float): To extract abnormal points,"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_analysis_options:10
msgid "set threshold of the analysis results' rate."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:1
msgid "Default options values for the experiment :meth:`run` method."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:4
msgid "tau_list (List): List of t1 value, that the T1 experiment"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:5
msgid "run analysis result."
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:10
msgid "freq_list (List, array): The frequency calculate by"
msgstr ""

#: of
#: pyQCat.experiments.composite.t1_spectrum.T1Spectrum._default_run_options:7
msgid "ac spectrum paras and z amp. If no ac spectrum paras, this is None."
msgstr ""

#: of pyQCat.experiments.composite.t1_spectrum.T1Spectrum._metadata:1
msgid "Set metadata."
msgstr ""

#: of pyQCat.experiments.composite.t1_spectrum.T1Spectrum._metadata:4
msgid ":py:class:`~pyQCat.structures.MetaData`"
msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":py:class:`~pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`__init__ "
#~ "<pyQCat.experiments.composite.T1Spectrum.__init__>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`component_experiment "
#~ "<pyQCat.experiments.composite.T1Spectrum.component_experiment>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.T1Spectrum.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.T1Spectrum.get_qubit_str>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ":py:obj:`run <pyQCat.experiments.composite.T1Spectrum.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.T1Spectrum.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.T1Spectrum.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.T1Spectrum.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`set_run_options "
#~ "<pyQCat.experiments.composite.T1Spectrum.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":py:obj:`analysis <pyQCat.experiments.composite.T1Spectrum.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`analysis_options "
#~ "<pyQCat.experiments.composite.T1Spectrum.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`child_experiment "
#~ "<pyQCat.experiments.composite.T1Spectrum.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`experiment_options "
#~ "<pyQCat.experiments.composite.T1Spectrum.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":py:obj:`run_options "
#~ "<pyQCat.experiments.composite.T1Spectrum.run_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ "Bases: "
#~ ":class:`pyQCat.experiments.composite_experiment.CompositeExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`__init__ "
#~ "<pyQCat.experiments.composite.T1Spectrum.__init__>`\\ \\(inst\\,"
#~ " qubits\\[\\, couplers\\, ...\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`component_experiment "
#~ "<pyQCat.experiments.composite.T1Spectrum.component_experiment>`\\ "
#~ "\\(\\[index\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`from_experiment_context "
#~ "<pyQCat.experiments.composite.T1Spectrum.from_experiment_context>`\\"
#~ " \\(context\\)"
#~ msgstr ""

#~ msgid "rtype"
#~ msgstr ""

#~ msgid ":py:class:`~pyQCat.experiments.base_experiment.BaseExperiment`"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`get_qubit_str "
#~ "<pyQCat.experiments.composite.T1Spectrum.get_qubit_str>`\\ "
#~ "\\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`options_table "
#~ "<pyQCat.experiments.composite.T1Spectrum.options_table>`\\ "
#~ "\\(\\[mode\\]\\)"
#~ msgstr ""

#~ msgid ":obj:`run <pyQCat.experiments.composite.T1Spectrum.run>`\\ \\(\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_analysis_options "
#~ "<pyQCat.experiments.composite.T1Spectrum.set_analysis_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_experiment_options "
#~ "<pyQCat.experiments.composite.T1Spectrum.set_experiment_options>`\\"
#~ " \\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_parent_file "
#~ "<pyQCat.experiments.composite.T1Spectrum.set_parent_file>`\\ "
#~ "\\(save\\_file\\[\\, description\\]\\)"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`set_run_options "
#~ "<pyQCat.experiments.composite.T1Spectrum.set_run_options>`\\ "
#~ "\\(\\*\\*fields\\)"
#~ msgstr ""

#~ msgid ":obj:`analysis <pyQCat.experiments.composite.T1Spectrum.analysis>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`analysis_options "
#~ "<pyQCat.experiments.composite.T1Spectrum.analysis_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`child_experiment "
#~ "<pyQCat.experiments.composite.T1Spectrum.child_experiment>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`experiment_options "
#~ "<pyQCat.experiments.composite.T1Spectrum.experiment_options>`\\"
#~ msgstr ""

#~ msgid ""
#~ ":obj:`run_options "
#~ "<pyQCat.experiments.composite.T1Spectrum.run_options>`\\"
#~ msgstr ""

